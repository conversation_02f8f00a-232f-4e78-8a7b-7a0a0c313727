import request from '@/utils/request'

// 电芯排废查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZDXBarSel',
    method: 'post',
    data
  })
}
// 电芯排废增加
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZDXBarIns',
    method: 'post',
    data
  })
}
// 电芯排废修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZDXBarUpd',
    method: 'post',
    data
  })
}
// 电芯排废删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZDXBarDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
