[[{"clientId": 1006, "clientName": "抛丸机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlBlastPlc/BlasterMachAlarmGroup/TriggerAlarm", "code": "SlBlastPlc/BlasterMachAlarmGroup/AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 3007, "clientName": "大件分拣桁架", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/LargePieceSortingTrussTriggerAlarm", "code": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/LargePieceSortingTrussAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 3007, "clientName": "小件输送", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/SmallConveYingTriggerAlarm", "code": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/SmallConveYingAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 1004, "clientName": "横移小车", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/HorizontalAlarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/HorizontalAlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G3工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G3Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G3AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G11工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G11Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G11AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G16工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G16Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G16AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}], [{"clientId": 1007, "clientName": "入库辊道线", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlRollPlc/StorageDeviceStatusAlarmGroup/Alarm", "code": "SlRollPlc/StorageDeviceStatusAlarmGroup/AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 3007, "clientName": "大件码垛桁架", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/LargePieceStackingTrussTriggerAlarm", "code": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/LargePieceStackingTrussAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 3005, "clientName": "大件校平机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLevelingMachineOpc/LeveLingMachineEquipmentAlarmGroup/TriggerAlarm", "code": "FJLevelingMachineOpc/LeveLingMachineEquipmentAlarmGroup/AlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 1004, "clientName": "总输送线", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/OverallAlarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/OverallAlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G4工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G4Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G4AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G12工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G12Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G12AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G17工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G17Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G17AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}], [{"clientId": 2010, "clientName": "喷码机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "QgInkjetPrinterPlc/EquipmentStatusInkjetPrinterGroup/TriggerAlarm", "code": "QgInkjetPrinterPlc/EquipmentStatusInkjetPrinterGroup/AlarmCode"}, "ports": {"mqtt": 8084, "cell": 8090}}, {"clientId": 3007, "clientName": "小件分拣机器人", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/SmallSortingRobotTriggerAlarm", "code": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/SmallSortingRobotAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 3006, "clientName": "大件砂光机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJSandingMachineOpc/TwoLargePieceSandingMachineAlarmGroup/LargeTriggerAlarm", "code": "FJSandingMachineOpc/TwoLargePieceSandingMachineAlarmGroup/LargeAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 1004, "clientName": "总回流线", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/flowAlarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/flowAlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G5工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G5Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G5AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G13工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G13Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G13AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}], [{"clientId": 2008, "clientName": "等离子切割机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "QgPlasCutOpcG6/OPCStatus/Alarm", "code": "QgPlasCutOpcG6/OPCStatus/AlarmList"}, "ports": {"mqtt": 8084, "cell": 8090}}, {"clientId": 3007, "clientName": "小件码垛机器人", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/SmallPalletizingRobotTriggerAlarm", "code": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/SmallPalletizingRobotAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 3006, "clientName": "1号小件砂光机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJSandingMachineOpc/TwoLargePieceSandingMachineAlarmGroup/OneTriggerAlarm", "code": "FJSandingMachineOpc/TwoLargePieceSandingMachineAlarmGroup/OneAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 1004, "clientName": "G1工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G1Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G1AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G6工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G6Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G6AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G14工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G14Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G14AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}], [{"clientId": 2008, "clientName": "激光切割机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "QgLaserCutS7G5/CutAlarmGroup/TriggerAlarm", "code": "QgLaserCutS7G5/CutAlarmGroup/AlarmCode"}, "ports": {"mqtt": 8084, "cell": 8090}}, {"clientId": 3007, "clientName": "大件辊道", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/LargeRollerConveyorTriggerAlarm", "code": "FJLargeItemSortingOpc/SortingAreaEquipmentAlarmGroup/LargeRollerConveyorAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 3006, "clientName": "2号小件砂光机", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "FJSandingMachineOpc/TwoLargePieceSandingMachineAlarmGroup/TwoTriggerAlarm", "code": "FJSandingMachineOpc/TwoLargePieceSandingMachineAlarmGroup/TwoAlarmCode"}, "ports": {"mqtt": 8085, "cell": 8091}}, {"clientId": 1004, "clientName": "G2工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G2Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G2AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G8工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G8Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G8AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"clientId": 1004, "clientName": "G15工位", "faulted": false, "faultCode": null, "faultContent": "无", "tags": {"trigger": "SlTransGSlPlc/CarDeviceAlarmGroup/G15Alarm", "code": "SlTransGSlPlc/CarDeviceAlarmGroup/G15AlarmCode"}, "ports": {"mqtt": 8083, "cell": 8089}}]]