<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="区域代码：">
                <el-input v-model="query.section_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="区域名称：">
                <el-input v-model="query.section_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="true" title="工位信息" :visible.sync="stationDrawerVisible" size="100%">
        <station v-if="stationDrawerVisible" ref="station" :section_code="section_code" :image_content="image_content" :stationId="stationId"/>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px"
              :inline="true">
              <el-form-item label="区域名称" prop="section_code">
                <!-- 区域代码： -->
                <el-input v-model="form.section_code" clearable size="small" />
              </el-form-item>
              <el-form-item label="区域名称" prop="section_name">
                <!-- 区域名称： -->
                <el-input v-model="form.section_name" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <el-dialog title="图片预览" :visible.sync="dialogVisible" width="70%" :before-close="handleClose">
            <img v-if="imageUrlIco" :src="imageUrlIco" style="width: 100%;">
          </el-dialog>
          <el-drawer append-to-body :wrapper-closable="false" :title="uploadDrawerTitle"
            :visible.sync="uploadDrawerVisbleSync" size="390px" @closed="handleUploadDrawerClose">
            <el-upload ref="upload" name="file" :multiple="false" action="" drag="" :limit="uploadLimit"
              :on-change="handleUploadOnChange" :http-request="handleUploadHttpRequest" :accept="uploadAccept"
              :auto-upload="false" :file-list="fileList">
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
            </el-upload>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="handleUploadDrawerCancel">取消</el-button>
              <el-button type="primary" size="small" icon="el-icon-check" @click="handleUploadFile">上传</el-button>
            </div>
          </el-drawer>
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small"
            :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row
            @selection-change="crud.selectionChangeHandler">
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="section_code"  align="center" label="区域代码" />
            <el-table-column :show-overflow-tooltip="true" prop="section_name"  align="center" label="区域名称" />
            <el-table-column :show-overflow-tooltip="true" label="配置" align="center">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium"
                  @click="opendStation(scope.row)">配置</el-tag>
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" >
                  <template slot="left">
                    <el-button slot="reference" type="text" size="small" @click="handleUpload(scope.row)">上传</el-button>
                    <el-button slot="reference" type="text" size="small" v-if="scope.row.image_content" @click="imgPreview(scope.row)">预览</el-button>
                </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
  
<script>
import crudMainService from '@/api/dcs/core/service/mainService'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import station from '@/views/dcs/core/service/station'
const defaultForm = {
  created_by: '',
  creation_date: '',
  id: '',
  last_update_date: '',
  last_updated_by: '',
  section_code: '',
  section_name: '',
}
export default {
  name: 'MAIN_SERVICE',
  components: { crudOperation, rrOperation, udOperation, pagination, station },
  cruds() {
    return CRUD({
      title: '主页面维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['id asc'],
      // CRUD Method
      crudMethod: { ...crudMainService },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'main_service:add'],
        edit: ['admin', 'main_service:edit'],
        del: ['admin', 'main_service:del'],
        down: ['admin', 'main_service:down']
      },
      rules: {
        recipe_id: [{ required: true, message: '请选择配方', trigger: 'blur' }],
        craft_route_main_code: [{ required: true, message: '请输入工艺路线编码', trigger: 'blur' }],
        craft_route_main_des: [{ required: true, message: '请输入工艺路线描述', trigger: 'blur' }],
      },
      stationDrawerVisible: false,
      section_code: '',
      image_content:'',
      // 文件上传
      currentFilePath: '',
      uploadDrawerTitle: '文件上传',
      uploadDrawerVisbleSync: false,
      uploadLimit: 1,
      uploadAccept: '*.*',
      fileList: [],
      stationId:'',
      obj:{
        id:''
      },
      dialogVisible:false,
      imageUrlIco:''
    }
  },

  mounted: function () {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function () {
  },
  methods: {
    opendStation(row) {
      this.section_code = row.section_code
      this.image_content = row.image_content
      this.stationId = row.id
      this.stationDrawerVisible = true
    },
    imgPreview(row){
      this.imageUrlIco = 'data:image/png;base64,' + row.image_content
      this.dialogVisible = true
    },
    handleClose(){
      this.dialogVisible = false
    },
    handleUploadDrawerClose() {
      this.fileList = []
    },
    handleUpload(row) {
      this.obj.id = row.id
      this.uploadDrawerTitle = '图片文件上传'
      this.uploadAccept = '.png'
      this.uploadDrawerVisbleSync = true
    },
    handleUploadDrawerCancel() {
      this.uploadDrawerVisbleSync = false
    },
    // 导入文件时将文件存入数组中
    handleUploadOnChange(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequest(file) {
      this.fileData.append('file', file.file)
    },
    // 处理上传文件
    handleUploadFile() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      this.fileData = new FormData()
      this.fileData.append('id', this.obj.id)
      this.fileData.append('user_name', Cookies.get('userName'))
      this.$refs.upload.submit()
      // 配置路径
      var method = 'dcs/hmi/base/upload-image'
        var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = 'http://*************:9087/' + method
      const loading = this.$loading({
        lock: true,
        text: '上传文件处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios.post(path, this.fileData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: progressEvent => {
          const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
          this.$refs.upload.onProgress({ percent: num }) // 进度条
        }
      })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            this.crud.toQuery()
            this.uploadDrawerVisbleSync = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
        .catch(er => {
          loading.close()
          this.$message({
            message: '上传文件异常：' + er,
            type: 'error'
          })
        })
    },
  }
}
</script>
  
<style scoped>
.table-descriptions-label {
  width: 150px;
}

.table-descriptions-content {
  width: 150px;
}
</style>
  