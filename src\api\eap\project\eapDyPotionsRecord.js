import request from '@/utils/request'

// 查询药水添加记录信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dayuan/EapDyPotionsRecordSel',
    method: 'post',
    data
  })
}
// 新增药水添加记录信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dayuan/EapDyPotionsRecordIns',
    method: 'post',
    data
  })
}
// 修改药水添加记录信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dayuan/EapDyPotionsRecordUpd',
    method: 'post',
    data
  })
}

// 删除药水添加记录信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dayuan/EapDyPotionsRecordDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

