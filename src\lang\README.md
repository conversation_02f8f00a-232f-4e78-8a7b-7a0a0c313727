# 国际化模块化使用指南

## 概述

为了解决国际化文件过大、难以维护的问题，我们实现了一个模块化的国际化文件结构。这个结构允许每个项目或页面拥有自己的国际化文件，同时保持与现有系统的兼容性。系统会自动加载和合并所有模块化的国际化文件，无需手动导入。

## 文件结构

```
src/
└── lang/
    ├── index.js           # 主入口文件，负责整合所有语言文件
    ├── zh.js              # 保留现有的主语言文件（向后兼容）
    ├── en.js              # 保留现有的主语言文件（向后兼容）
    ├── tw.js              # 保留现有的主语言文件（向后兼容）
    ├── th.js              # 保留现有的主语言文件（向后兼容）
    ├── modules/           # 新增的模块化目录
    │   ├── common/        # 通用翻译
    │   │   ├── zh.js
    │   │   ├── en.js
    │   │   ├── tw.js
    │   │   └── th.js
    │   ├── zhcy/          # 珠海超毅项目
    │   │   ├── zh.js
    │   │   ├── en.js
    │   │   ├── tw.js
    │   │   └── th.js
    │   ├── tlps/          # TLPS项目
    │   │   ├── zh.js
    │   │   ├── en.js
    │   │   ├── tw.js
    │   │   └── th.js
    │   └── ...            # 其他项目
```

## 如何添加新的项目国际化文件

1. 在 `src/lang/modules/` 目录下创建新的项目文件夹，例如 `myproject`
2. 在项目文件夹中创建四种语言的文件：`zh.js`, `en.js`, `tw.js`, `th.js`
3. 在每个文件中使用以下格式定义翻译：

```javascript
// src/lang/modules/myproject/zh.js
export default {
  myproject: {
    // 这里添加项目特定的翻译
    title: '我的项目',
    description: '这是一个示例项目',
    // 可以嵌套更多层级
    buttons: {
      save: '保存',
      cancel: '取消'
    }
  }
}
```

4. 系统会自动加载和合并这些文件，无需手动导入

## 如何在组件中使用

在组件中使用国际化文本的方式与之前相同，只是路径会反映模块化结构：

```vue
<template>
  <div>
    <!-- 使用通用模块的翻译 -->
    <el-button>{{ $t('common.buttons.save') }}</el-button>
    
    <!-- 使用项目特定的翻译 -->
    <h1>{{ $t('myproject.title') }}</h1>
    <p>{{ $t('myproject.description') }}</p>
    <el-button>{{ $t('myproject.buttons.save') }}</el-button>
    
    <!-- 仍然可以使用原有的翻译 -->
    <el-button>{{ $t('lang_pack.commonPage.add') }}</el-button>
  </div>
</template>
```

## 最佳实践

1. **命名空间**：为每个项目创建一个唯一的顶级命名空间，避免与其他项目冲突
2. **结构化**：在项目内部使用结构化的嵌套对象组织翻译，使其更易于维护
3. **复用**：通用的翻译放在 `common` 模块中，避免重复定义
4. **完整性**：确保每种语言都有相同的键，避免缺失翻译
5. **注释**：在翻译文件中添加注释，说明每个部分的用途

## 工作原理

1. `src/lang/index.js` 文件使用 `require.context` 自动加载 `modules` 目录下的所有语言文件
2. 根据文件路径判断语言类型（zh.js, en.js, tw.js, th.js）
3. 使用深度合并函数将不同模块的翻译合并到一起
4. 将合并后的语言包导出给 Vue I18n 使用

这种方式的优点是：
- 无需手动导入和注册新的语言文件
- 项目结构清晰，便于维护
- 与现有系统完全兼容
