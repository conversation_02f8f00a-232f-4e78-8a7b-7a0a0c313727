<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :default-time="['00:00:00', '23:59:59']"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="生产线：">
                <el-select
                  v-model="query.prod_line_code"
                  filterable
                  clearable
                  @change="prodLineChange"
                >
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_id"
                    :label="item.prod_line_code + ' ' + item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位：">
                <el-select v-model="query.station_code" filterable clearable>
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_id"
                    :label="item.station_code + ' ' + item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="订单号：">
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产品型号：">
                <el-select v-model="query.small_model_type" filterable clearable>
                  <el-option
                    v-for="item in smallModelTypeData"
                    :key="item.small_model_type"
                    :label="item.small_model_type + ' ' + item.main_material_des"
                    :value="item.small_model_type"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="合格标志：">
                <el-select v-model="query.quality_sign" clearable>
                  <el-option
                    v-for="item in dict.QUALIFIED_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="总成号：：">
                <el-input v-model="query.serial_num" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="托盘号：">
                <el-input v-model="query.container_num" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="机体编码：">
                <el-input v-model="query.print_barcode" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="启用标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in ['Y', 'N']"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button
                  class="filter-item"
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  @click="toQuery"
                >{{ $t("lang_pack.commonPage.search") }}</el-button>
                <!-- 搜索 -->
                <el-button
                  v-if="crud.optShow.reset"
                  class="filter-item"
                  size="small"
                  icon="el-icon-refresh-left"
                  @click="resetQuery()"
                >{{ $t("lang_pack.commonPage.reset") }}</el-button>
                <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="group_left">
          <el-button
            v-permission="permission.down"
            :loading="crud.downloadLoading"
            :disabled="!crud.data.length"
            size="small"
            icon="el-icon-bottom"
            @click="doExport1"
          />
          <el-button
            v-permission="permission.down"
            :loading="crud.downloadLoading"
            :disabled="!crud.data.length"
            size="small"
            icon="el-icon-download"
            @click="doExport"
          />
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_line_code"
              width="100"
              label="生产线"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              width="130"
              label="工位号"
              :sortable="true"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_des"
              width="230"
              label="工位描述"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              width="220"
              label="总成号："
              :sortable="true"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="quality_sign"
              width="100"
              label="合格标志"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.quality_sign === 'OK' ? 'success' : 'danger'"
                  effect="dark"
                  style="cursor: pointer"
                  size="medium"
                >{{ scope.row.quality_sign === "OK" ? "OK" : "NG" }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="container_num"
              width="230"
              label="托盘号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="arrive_date"
              width="150"
              label="到达时间"
              :sortable="true"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="leave_date"
              width="150"
              label="离开时间"
              :sortable="true"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cost_time"
              width="100"
              label="消耗时间(秒)"
              :sortable="true"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="make_order"
              width="150"
              label="订单号"
              :sortable="true"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="small_model_type"
              width="150"
              label="机型"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="staff_id"
              width="150"
              label="操作者"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="product_batch"
              width="150"
              label="订单批号"
              :sortable="true"
            />
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="mo_custom_code"
              width="150"
              label="订单来源客户代码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mo_custom_des"
              width="150"
              label="订单来源客户描述"
            /> -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="repair_flag"
              width="150"
              label="返修标志"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="shif_code"
              width="150"
              label="班次代码"
            />
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="shif_des"
              width="150"
              label="班次描述"
            /> -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              width="100"
              label="有效标识"
            >
              <template slot-scope="scope">
                {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px; text-align: right">
            <el-button-group>
              <el-button type="primary">总数量：{{ page.total }}</el-button>
              <el-button type="primary">当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudStationFlow from '@/api/mes/project/sh/stationFlow'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import { mesExportSelOne } from '@/api/mes/core/meExport'
import { downloadFile } from '@/utils/index'
import { fileDownload } from '@/api/core/file/file'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {}
export default {
  name: 'MES_ME_STATION_FLOW',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: '过站数据',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'station_flow_id',
      // 排序
      sort: ['station_flow_id desc'],
      // CRUD Method
      crudMethod: { ...crudStationFlow },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        tableSize: 40,
        enable_flag: 'Y'
      }
    })
  },
  // 数据字典
  dicts: ['QUALIFIED_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 390,
      permission: {
        add: ['admin', 'mes_me_station_flow:add'],
        edit: ['admin', 'mes_me_station_flow:edit'],
        del: ['admin', 'mes_me_station_flow:del'],
        down: ['admin', 'mes_me_station_flow:down']
      },
      stationData: [],
      prodLineData: [],
      smallModelTypeData: [],
      nowPageIndex: 1, // 当前页数
      pageList: [],
      exportId: '',
      timer: ''
    }
  },
  computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime =
        start.getFullYear() +
        '-' +
        (start.getMonth() + 1 >= 10
          ? start.getMonth() + 1
          : '0' + (start.getMonth() + 1)) +
        '-' +
        (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) +
        ' 00:00:00'
      const defalutEndTime =
        end.getFullYear() +
        '-' +
        (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : '0' + (end.getMonth() + 1)) +
        '-' +
        (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) +
        ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.crud.query.item_date = this.timeDefault

    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    doExport() {
      if (!this.query.item_date) {
        this.$message({
          message: '一次最多只能导出365天数据，请重选时间后再导出！',
          type: 'info'
        })
        return
      }
      let dateStr1 = this.query.item_date[0]
      dateStr1 = dateStr1.replace(/-/g, '/')
      let dateStr2 = this.query.item_date[1]
      dateStr2 = dateStr2.replace(/-/g, '/')
      var date1 = new Date(dateStr1)
      var date2 = new Date(dateStr2)
      var Difference_In_Time = date2.getTime() - date1.getTime()
      var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
      if (Difference_In_Days > 365) {
        this.$message({
          message: '一次最多只能导出365天数据，请重选时间后再导出！',
          type: 'info'
        })
        return
      }

      this.crud.downloadLoading = true
      crudStationFlow
        .exportEventInsert(this.query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.exportId = defaultQuery.result
            this.timer = setInterval(this.getFileStatus, 2000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
            this.crud.downloadLoading = false
          }
        })
        .catch(() => {
          this.$message({ message: '导出数据异常', type: 'error' })
          this.crud.downloadLoading = false
        })
    },
    doExport1() {
      if (!this.query.item_date) {
        this.$message({
          message: '一次最多只能导出365天数据，请重选时间后再导出！',
          type: 'info'
        })
        return
      }
      let dateStr1 = this.query.item_date[0]
      dateStr1 = dateStr1.replace(/-/g, '/')
      let dateStr2 = this.query.item_date[1]
      dateStr2 = dateStr2.replace(/-/g, '/')
      var date1 = new Date(dateStr1)
      var date2 = new Date(dateStr2)
      var Difference_In_Time = date2.getTime() - date1.getTime()
      var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
      if (Difference_In_Days > 365) {
        this.$message({
          message: '一次最多只能导出365天数据，请重选时间后再导出！',
          type: 'info'
        })
        return
      }

      this.crud.downloadLoading = true
      crudStationFlow
        .exportEventInsertSH(this.query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.exportId = defaultQuery.result
            this.timer = setInterval(this.getFileStatus, 2000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
            this.crud.downloadLoading = false
          }
        })
        .catch(() => {
          this.$message({ message: '导出数据异常', type: 'error' })
          this.crud.downloadLoading = false
        })
    },
    getFileStatus() {
      // 获取文件下载状态
      mesExportSelOne({ export_id: this.exportId }).then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0 && defaultQuery.data) {
          if (defaultQuery.data.finish_status === 'OK') {
            clearInterval(this.timer)
            // 文件已生成，则下载该文件
            fileDownload({ file_path: defaultQuery.data.down_url })
              .then((result) => {
                downloadFile(result, defaultQuery.data.export_name, 'csv')
                this.crud.downloadLoading = false
              })
              .catch(() => {
                this.crud.downloadLoading = false
              })
          } else if (defaultQuery.data.finish_status === 'NG') {
            this.$message({ message: defaultQuery.data.error_msg, type: 'error' })
            clearInterval(this.timer)
            this.crud.downloadLoading = false
          }
        }
      })
    },
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page =
          this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    prodLineChange(val) {
      const lineItem = this.prodLineData.find((item) => item.prod_line_code === val)
      if (lineItem) {
        this.getStationList(lineItem.prod_line_id)
        this.getSmallModel(lineItem.prod_line_id)
      }
    },
    getStationList(prod_line_id) {
      this.stationData = []
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getSmallModel(prod_line_id) {
      this.smallModelTypeData = []
      selSmallModel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.smallModelTypeData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
