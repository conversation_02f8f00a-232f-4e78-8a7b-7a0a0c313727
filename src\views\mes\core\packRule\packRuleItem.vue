<template>
  <!--PACK电芯-->
  <el-card shadow="never">
    <el-drawer
      append-to-body
      :wrapper-closable="false"
      :title="crud.status.title"
      :visible="crud.status.cu > 0"
      :before-close="crud.cancelCU"
      size="650px"
    >
      <el-form
        ref="form"
        class="el-form-wrap"
        :model="form"
        :rules="rules"
        size="small"
        label-width="100px"
        :inline="true"
      >
        <el-form-item label="模组型号" prop="mz_model_type">
          <el-select v-model="form.mz_model_type" clearable filterable>
            <el-option
              v-for="item in mzModelTypeData"
              :key="item.small_type_id"
              :label="item.small_model_type"
              :value="item.small_model_type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="电芯挡位" prop="dx_gear">
          <el-input v-model="form.dx_gear" />
        </el-form-item>
        <el-form-item label="模组数量" prop="mz_type_count">
          <el-input v-model="form.mz_type_count" />
        </el-form-item>
        <el-form-item
          v-if="mzSequenceFlag === 'Y'"
          label="模组顺序"
          prop="mz_sequence"
        >
          <el-input v-model="form.mz_sequence" />
        </el-form-item>
        <el-form-item label="有效标识">
          <fastCode
            fastcode_group_code="ENABLE_FLAG"
            :fastcode_code.sync="form.enable_flag"
            control_type="radio"
            size="mini"
          />
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button
          size="small"
          icon="el-icon-close"
          plain
          @click="crud.cancelCU"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          icon="el-icon-check"
          :loading="crud.status.cu === 2"
          @click="crud.submitCU"
          >确认</el-button
        >
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          height="478"
          max-height="478"
          highlight-current-row
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column
            v-if="1 == 0"
            width="10"
            prop="pack_mz_id"
            label="id"
          />

          <el-table-column
            :show-overflow-tooltip="true"
            prop="mz_model_type"
            label="模组型号"
          />
          <el-table-column
            :show-overflow-tooltip="true"
            prop="dx_gear"
            label="电芯档位"
          />
          <el-table-column
            :show-overflow-tooltip="true"
            prop="mz_type_count"
            label="模组数量"
          />
          <el-table-column
            v-if="mzSequenceFlag === 'Y'"
            :show-overflow-tooltip="true"
            prop="mz_sequence"
            label="模组顺序"
          />
          <el-table-column
            label="有效标识"
            align="center"
            prop="enable_flag"
            width="100"
          >
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                :disabled-dle="false"
              />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import { sel as selSysParameter } from "@/api/core/system/sysParameter";
import crudPackDRule from "@/api/mes/core/packDRule";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import udOperation from "@crud/UD.operation";
import pagination from "@crud/Pagination";
const defaultForm = {
  pack_mz_id: "",
  pack_id: "",
  mz_model_type: "",
  dx_gear: "",
  mz_type_count: "",
  enable_flag: "Y",
  attribute1: "",
  attribute2: "",
  attribute3: "",
};
export default {
  name: "PackRuleItem",
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    pack_id: {
      type: [String, Number],
      default: -1,
    },
  },
  cruds() {
    return CRUD({
      title: "PACK模组电芯",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "pack_mz_id",
      // 排序
      sort: ["pack_mz_id asc"],
      // CRUD Method
      crudMethod: { ...crudPackDRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true,
      },
      queryOnPresenterCreated: false,
    });
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ["admin", "c_mes_fmod_recipe_pack_d:add"],
        edit: ["admin", "c_mes_fmod_recipe_pack_d:edit"],
        del: ["admin", "c_mes_fmod_recipe_pack_d:del"],
        down: ["admin", "c_mes_fmod_recipe_pack_d:down"],
      },
      rules: {
        // 提交验证规则
        mz_model_type: [
          { required: true, message: "请输入模组型号", trigger: "blur" },
        ],
        dx_gear: [
          { required: true, message: "请填写电芯档位", trigger: "blur" },
        ],
        mz_type_count: [
          { required: true, message: "请填写模组数量", trigger: "blur" },
        ],
      },
      mzSequenceFlag: "",
      mzModelTypeData: [],
    };
  },
  watch: {
    pack_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.pack_id = this.pack_id;
        this.crud.toQuery();
      },
    },
  },
  mounted: function () {
    // 获取系统参数信息,判断是否是唐山国轩项目
    var queryParameter = {
      userName: Cookies.get("userName"),
      parameter_code: "ProjectCode",
      enable_flag: "Y",
    };
    selSysParameter(queryParameter)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res));
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== "") {
            if (defaultQuery.data[0].parameter_val === "TSGX")
              this.mzSequenceFlag = "Y";
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t("lang_pack.vie.queryException"),
          type: "error",
        });
      });
    this.GetMzModelType();
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200;
    };
  },
  created: function () {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.pack_id = this.pack_id;
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.pack_id = this.pack_id;
      return true;
    },
    GetMzModelType() {
      const query = {
        material_type: "MZ",
      };
      crudPackDRule
        .MesSmallModelSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length) {
              this.mzModelTypeData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t("lang_pack.vie.queryException"),
            type: "error",
          });
        });
    },
  },
};
</script>
