<template>
  <div>
    <el-input v-model="query.log_msg" :placeholder="$t('lang_pack.mainmain.logQuery')" clearable class="input-with-select" style="width: 80%">
      <el-select slot="prepend" v-model="query.log_type" clearable :placeholder="$t('lang_pack.mainmain.logType')" style="width: 100px">
        <el-option label="Info" value="INFO" />
        <el-option label="Warn" value="WARN" />
        <el-option label="Fatal" value="FATAL" />
        <el-option label="Error" value="ERROR" />
      </el-select>
      <el-button slot="append" icon="el-icon-search" @click="getStepLogData" />
    </el-input>

    <el-row :gutter="20" style="margin-top:10px;">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" size="small" :data="stepLogData" style="width: 100%" height="250px">
          <el-table-column  type="expand">
            <template slot-scope="props">
              <el-descriptions :column="2" size="small" border>
                <el-descriptions-item :label="$t('lang_pack.mainmain.logType')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.log_type }}</el-descriptions-item>
                <el-descriptions-item :label="$t('lang_pack.mainmain.logCode')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.log_code }}</el-descriptions-item>
                <el-descriptions-item :label="$t('lang_pack.mainmain.date')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.item_date }}</el-descriptions-item>
                <el-descriptions-item :label="$t('lang_pack.mainmain.logInfo')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.log_msg }}</el-descriptions-item>
              </el-descriptions>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="log_msg" :label="$t('lang_pack.mainmain.logInfo')" />
          <el-table-column  :show-overflow-tooltip="true" prop="item_date" width="150" :label="$t('lang_pack.mainmain.date')" />
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import axios from 'axios'
export default {
  name: 'RCS_FLOW_MAIN_STEP_LOG',
  props: {
    flow_main_id: {
      type: [String, Number],
      default: -1
    },
    step_mod_id: {
      type: [String, Number],
      default: -1
    },
    cel_api: {
      type: String,
      default: ''
    },
    me_flow_task_id: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      stepLogData: [],
      query: {
        log_msg: '',
        log_type: ''
      }
    }
  },
  watch: {},

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.getStepLogData()
  },
  methods: {
    getStepLogData() {
      var method = '/cell/core/flow/CoreFlowStepLogListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cel_api.split(':')[1] + method
      } else {
        path = 'http://' + this.cel_api + method
      }
      const data = {
        step_mod_id: this.step_mod_id,
        me_flow_task_id: this.me_flow_task_id,
        log_msg: this.query.log_msg,
        log_type: this.query.log_type
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            if (defaultQuery.data.count > 0) {
              this.stepLogData = defaultQuery.data.data
            } else {
              this.stepLogData = []
            }
          } else {
            this.stepLogData = []
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.stepLogData = []
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
