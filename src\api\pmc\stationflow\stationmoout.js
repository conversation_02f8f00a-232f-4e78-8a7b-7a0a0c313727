import request from '@/utils/request'

// 工位生产订单拉出查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationMoOutSel',
    method: 'post',
    data
  })
}

// 预约/直接拉出
export function setout(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationMoPreSetOut',
    method: 'post',
    data
  })
}

// 取消预约拉出/入
export function cancelout(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationMoPreSetOutInCancel',
    method: 'post',
    data
  })
}

export default { sel, setout, cancelout }
