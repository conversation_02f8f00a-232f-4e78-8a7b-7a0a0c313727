<template>
    <div class="workstationScreen">
        <el-card class="cardStyle cardheadbg">
            <el-row :gutter="24" class="elRowStyle">
                <el-col :span="24">
                    <div class="headerStyle">
                        <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
                        <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
                        <div class="headTitle">
                            <h2>{{ prod_line_des }}</h2>
                        </div>
                        <div class="headerL">
                        <p class="pTime">{{ nowDateWeek }}</p>
                        <p class="pTime">{{ nowDateTime }}</p>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </el-card>
        <el-card shadow="always">
            <el-row :gutter="24" class="elRowStyle main">
                <el-col :span="12">
                    <div class="carInfoText carInfomargin">
                        <div class="titleTip">本月 TOP</div>
                        <el-table border :data="monthData" style="width: 100%" height="360px" >
                            <el-table-column  prop="index" label="#" width="50"/>
                            <el-table-column prop="vehicleModel" :show-overflow-tooltip="true" label="车型" width="160"/>
                            <el-table-column prop="faultDesc" :show-overflow-tooltip="true" label="问题描述"/>
                            <el-table-column prop="level" :show-overflow-tooltip="true" label="等级" width="80" />
                            <el-table-column prop="frequency" :show-overflow-tooltip="true" label="频次" width="80"/>
                        </el-table>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="carInfoText carInfomargin">
                        <div class="titleTip">上周售后质量问题</div>
                        <el-table border :data="qualityProData" style="width: 100%" height="360px" >
                            <el-table-column prop="index" label="#" width="50"/>
                            <el-table-column  prop="level" :show-overflow-tooltip="true" label="等级" width="80"/>
                            <el-table-column prop="faultDesc" :show-overflow-tooltip="true" label="问题描述"/>
                            <el-table-column prop="frequency" :show-overflow-tooltip="true" label="频次" width="80"/>
                        </el-table>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="24" class="elRowStyle main">
                <el-col :span="12">
                    <div class="carInfoText carInfomargin">
                        <div class="titleTip">今日 TOP</div>
                        <el-table border :data="dayData" style="width: 100%" height="360px" >
                            <el-table-column  prop="index" label="#" width="50"/>
                            <el-table-column prop="vehicleModel" :show-overflow-tooltip="true" label="车型" width="160"/>
                            <el-table-column prop="faultDesc" :show-overflow-tooltip="true" label="问题描述"/>
                            <el-table-column prop="level" :show-overflow-tooltip="true" label="等级" width="80"/>
                            <el-table-column  prop="frequency" :show-overflow-tooltip="true" label="频次" width="80"/>
                        </el-table>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="carInfoText carInfomargin">
                        <div class="titleTip">本周 TOP</div>
                        <el-table border :data="weekData" style="width: 100%" height="360px" >
                            <el-table-column prop="index" label="#" width="50"/>
                            <el-table-column prop="vehicleModel" :show-overflow-tooltip="true" label="车型" width="160"/>
                            <el-table-column prop="faultDesc" :show-overflow-tooltip="true" label="问题描述"/>
                            <el-table-column prop="level" :show-overflow-tooltip="true" label="等级" width="80"/>
                            <el-table-column prop="frequency" :show-overflow-tooltip="true" label="频次" width="80"/>
                        </el-table>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="24" class="elRowStyle main">
                <el-col :span="12">
                    <div class="carInfoText carInfomargin">
                        <div class="titleTip">当月指标展示(DPU)</div>
                        <div id="numberOfIss" style="width: 100%;height: 330px" />
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="carInfoText carInfomargin">
                        <div class="titleTip">当月指标展示(质量问题责任部门分布)</div>
                        <div id="finalAssembly" style="width: 100%;height: 330px" />
                    </div>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
<script>
import { getStationTime,getTopMonth,getLastWeek,getDpuProData,getfinAssProData,getStationDes } from '@/api/pmc/sysworkshopQualitygate'
import headlbg from '@/assets/images/headlbg.png'
export default {
    name: 'qualitygate',
    data() {
        return {
            stationTimeTimer:null,
            nowDateTime: '', // 当前日期
            nowDateWeek: '', // 周末
            prod_line_des: '质量门',
            headlbg: headlbg,
            monthData: [],
            monthIndex:0,
            dayData:[],
            dayIndex:0,
            weekData:[],
            weekIndex:0,
            qualityProData:[],
            qualityProIndex:0,
            numberOfIss:null,
            finalAssembly:null,
            timer:null,
            timer2:null,
            count:0,
        }
    },
    created() {
        // 加载工位屏时间
        this.initstationTime()
        this.stationTimeTimer = setInterval(() => {
            this.initstationTime()
        }, 1000)
        // 获取本月数据
        this.getMonthData()
        //获取售后质量数据
        this.qualityData()
        // 获取今日数据
        this.getDayData()
        // 获取本周数据
        this.getWeekData()
        //获取DPU问题质量
        this.DpuData()
        // 获取总装下线台数
        this.finAssData()
        this.timer = setInterval(()=>{
            this.getMonthData()
            this.qualityData()
            this.getDayData()
            this.getWeekData()
            this.DpuData()
            this.finAssData()
        },1000 * 60)
    },
    mounted() {
        const that = this
        const query = {
            station: this.$route.query.station_code,
        }
        getStationDes(query).then(res=>{
            if(res.code === '200' && res.data.length > 0){
                let stationDesc = ['底涂上线']
                stationDesc.forEach(e=>{
                    if(e === res.data[0].stationDesc){
                        this.prod_line_des = '总装车间质量门'
                    }else{
                        this.prod_line_des = res.data[0].stationDesc
                    }
                })
                
            }else{
                console.log('请求数据异常')
            }
        }).catch(()=>{console.log('请求数据为空')})
        window.addEventListener('resize', function() {
            that.numberOfIss.resize()
            that.finalAssembly.resize()
      })
    },
    // 销毁定时器
    beforeDestroy() {
        if(this.timer){
            clearInterval(this.timer)
        }
        if(this.stationTimeTimer){
            clearInterval(this.stationTimeTimer)
        }
        this.timer2 && clearInterval(this.timer2)
    },
    methods: {
        // 加载工位屏时间
        initstationTime() {
            var query = {
                station_code: this.$route.query.station_code
            }
            getStationTime(query).then(res => {
                if (res.code !== 0 && res.count < 0) {
                    console.log('请求数据异常')
                    return
                }
                this.nowDateTime = res.data[0].sysdate
                this.nowDateWeek = res.data[0].sysweek
            }).catch(() => {
                    console.log('请求数据为空')
            })
        },
        getMonthData(){
            var query = {
                    type:'thisMonth',
                    station: this.$route.query.station_code,
                }
            getTopMonth(query).then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                res.data.forEach((e,index)=>{e.index = index + 1})
                this.monthData = res.data.slice(this.monthIndex,this.monthIndex+5)
                this.monthIndex += 5
                if(this.monthIndex >=res.data.length){
                    this.monthIndex = 0
                }
            }).catch(() => {
                console.log('请求数据为空')
            })
        },
        qualityData(){
            getLastWeek().then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                res.data.forEach((e,index)=>{e.index = index + 1})
                this.qualityProData = res.data.slice(this.qualityProIndex,this.qualityProIndex+5)
                this.qualityProIndex += 5
                if(this.qualityProIndex >=res.data.length){
                    this.qualityProIndex = 0
                }
            }).catch(() => {
                console.log('请求数据为空')
            })
        },
        getDayData(){
            var query = {
                type:'today',
                station: this.$route.query.station_code,
            }
            getTopMonth(query).then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                    res.data.forEach((e,index)=>{e.index = index + 1})
                    this.dayData = res.data.slice(this.dayIndex,this.dayIndex+5)
                    this.dayIndex += 5
                if(this.dayIndex >=res.data.length){
                    this.dayIndex = 0
                }
            }).catch(() => {
                console.log('请求数据为空')
            })
    },
    getWeekData(){
        var query = {
            type:'thisWeek',
            station: this.$route.query.station_code,
        }
        getTopMonth(query).then(res=>{
            if (res.code != 200) {
                console.log('请求数据异常')
                return
            }
                res.data.forEach((e,index)=>{e.index = index + 1})
                this.weekData = res.data.slice(this.weekIndex,this.weekIndex+5)
                this.weekIndex += 5
            if(this.weekIndex >=res.data.length){
                this.weekIndex = 0
            }
        }).catch(() => {
            console.log('请求数据为空')
        })
    },
    DpuData(){
        const query = {
                station: this.$route.query.station_code,
            }
            getDpuProData(query).then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                this.count ++ 
                this.numberOfIss = this.$echarts.init(document.getElementById('numberOfIss'))
                let option = {
                    legend: {
                        data: ['DPU'],
                        top: 0,
                        right: '4%',
                        itemWidth: 40,
                        itemHeight: 25, 
                        borderRadius: 4,
                        textStyle: {
                            color: '#000', 
                            fontFamily: 'Alibaba PuHuiTi', 
                            fontSize: 24, 
                            fontWeight: 400, 
                        },
                    },
                    tooltip: {
                        //   trigger: "item", //默认效果
                        //柱状图加阴影
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                            label: {
                                show: true,
                            },
                        },
                    },
                    grid: {
                        top: '25%',
                        left: '5%',
                        right: '5%',
                        bottom: '8%',
                        containLabel: true,
                    },
                    color: ['#5B9BD5'],
                    xAxis: {
                        type: 'category',
                        data: res.data.map(e=>{return e.name}),
                        axisLine:{
                            show:false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel:{
                            textStyle:{
                                fontSize:20,
                                fontWeight:700,
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine:{
                                show:false
                                },
                        axisTick: {
                            show: false
                        },
                        axisLabel:{
                            textStyle:{
                                fontSize:22,
                            }
                        }
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: 0, // 表示这个 dataZoom 组件控制 第一个 xAxis
                            startValue: 0, // 数据窗口范围的起始数值index
                            endValue: 6, // 数据窗口范围的结束数值index
                        }
                    ],
                    series: [
                        {
                            name: 'DPU',
                            data: res.data.map(e=>{return e.num}),
                            barWidth: 50,
                            type: 'bar',
                            itemStyle: {
                            normal: {
                                label: {
                                    show: true, //开启显示
                                    position: 'top', //顶部显示
                                    textStyle: {
                                        //数值样式
                                            color: 'black',
                                            fontSize: 24,
                                            fontWeight:700
                                        },
                                    },
                                },
                            },
                        },
                    ],
                };
                this.numberOfIss.setOption(option)
                if(this.count <=1){
                    this.timer2 =  setInterval(()=>{
                        console.log(option.dataZoom[0].endValue,res.data.length)
                        if(option.dataZoom[0].endValue >= res.data.length){
                            option.dataZoom[0].endValue = 6
                            option.dataZoom[0].startValue = 0
                        }else{
                            option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
                            option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1;
                        }
                        this.numberOfIss.setOption(option)
                    },2000)
                }
      }).catch(() => {
        console.log('请求数据为空')
      })
    },
    finAssData(){
      const query = {
        station: this.$route.query.station_code,
      }
      getfinAssProData(query).then(res=>{
        if (res.code != 200) {
            console.log('请求数据异常')
            return
        }
            this.finalAssembly = this.$echarts.init(document.getElementById('finalAssembly'))
            var seriesData = []
            res.data.forEach(e=>{
                seriesData.push({name:e.name,value:e.count})
            })
        let option = {
            tooltip: {
                trigger: 'item', 
                backgroundColor: '#ffffff',
                textStyle: {
                    color: '#6a6a6a', 
                    fontSize: 16, 
                },
                formatter: function (params) {
                    return (
                        '质量问题责任部门分布' +
                        '<br />' +
                        params.marker + // 数据项标记
                        '<span style="color:' +
                        params.color +
                        '">' +
                        params.data['name'] + // 数据项名称
                        '\n' +
                        '<span style="color:#666666">' +
                        params.data['value'] + // 数据项值
                        '</span></span>'
                    );
                },
            },
            color: ['#5C9EDB','#F37825','#A5A5A5'],
            // 设置图例配置
            legend: {
                align:'left',
                top: 0, 
                itemWidth: 40,
                itemHeight: 25, 
                itemGap: 15, 
                borderRadius: 4,
                textStyle: {
                    color: '#000', 
                    fontFamily: 'Alibaba PuHuiTi', 
                    fontSize: 24, 
                    fontWeight: 400, 
                },
            },
            // 设置饼图系列配置
            series: [
                {
                    name: '',
                    type: 'pie', 
                    radius: '70%', 
                    center: ['50%', '60%'], 
                    data:seriesData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 0,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0)',
                        },
                    },
                    label: {
                        alignTo: "edge",
                        formatter: function (value) {
                            let data = value.data;
                            return ("{nameSize|" +data.name +"}{valueSize|" +data.value +"}");
                        },
                        rich: {
                            nameSize: {
                                fontSize: 24,
                            },
                            valueSize: {
                                fontSize: 24,
                            },
                        },
                        },
                },
            ],
            };

          this.finalAssembly.setOption(option);
      }).catch(() => {
        console.log('请求数据为空')
      })
    }
    }
}
</script>
<style lang="less" scoped>
.workstationScreen{
    ::v-deep .el-card{
        border: 0;
    }
    ::v-deep .el-card__body {
        padding: 0px !important;
    }
    .cardStyle{
        margin: 0;
        padding: 0;
        border-radius: 0;
    }
    .main {
        padding: 0 10px;
        ::v-deep .el-table__body-wrapper .el-table__row td div,::v-deep .el-table__header-wrapper .has-gutter th div{
            line-height: 28px !important;
        }
        ::v-deep .el-table th.el-table__cell>.cell,div{
            font-size: 26px;
        }
        ::v-deep .el-table--small .el-table__cell{
            padding: 16px 0 !important;
        }
    }
    .cardheadbg{
         background-color: #031c45;
         .headerStyle{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 120px;
            .headbg{
                background-repeat: no-repeat;
                background-size: 100% 100%;
                width: 100%;
                position: absolute;
                top: 0;
                bottom: 0;
            }
            .logoStyle{
                display: flex;
                align-items: center;
                width: 20%;
                z-index: 2;
                padding-left: 4%;
                img{
                width: 180px;
                }
                span{
                font-weight: 700;
                font-size: 24px;
                }
            }
            .headerL{
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                width: 15%;
                margin-left: 5%;
                p{
                    margin: 5px 0;
                }
                .pImg{
                    width: 60px;
                    img{
                        width: 100%;
                    }
                }
                .pTime{
                    font-weight: 700;
                    color: #ffffff;
                    font-size: 26px;
                }
            }
            .headTitle{
                h2{
                margin: 0;
                font-size: 55px;
                color: #fdfd21;
                text-shadow: 3px 3px 3px #000000;
                }
            }
        }
    }
    .carInfomargin{
        .titleTip{
            font-size: 34px;
            font-weight: 700;
            display: flex;
            align-items: center;
            margin:10px 0px;
            &::before {
                content: '';
                background: #0070C0 !important;
                display: inline-block;
                width: 8px;
                height: 36px;
                border-radius: 10px;
                position: relative;
                margin-right: 2px;
            }
        }
        
    }
}
</style>