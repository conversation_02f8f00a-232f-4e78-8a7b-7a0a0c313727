<template>
  <div class="box-card">
    <el-card shadow="always" class="wrapCard">
      <el-form ref="formServer" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.progbatchupd.serve')">
                <!-- 服务： -->
                <el-select v-model="formServer.server_id" clearable size="small" @change="serverChange">
                  <el-option v-for="item in serverData" :key="item.server_id" :label="item.server_des" :value="item.server_id" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <el-button v-if="true" class="filter-item" size="small" type="primary" plain round icon="el-icon-coin" :disabled="this.formServer.server_id === ''" @click="toButInstall">{{ $t('lang_pack.progbatchupd.massUpdate') }}</el-button>
              <!-- 批量更新 -->
              <el-button v-if="true" class="filter-item" size="small" type="danger" plain round icon="el-icon-folder-checked" :disabled="this.formServer.server_id === ''" @click="toButChooseInstall">{{ $t('lang_pack.progbatchupd.selectiveupdate') }}</el-button>
              <!-- 选择更新 -->
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-drawer append-to-body title="文件上传" :visible.sync="dialogVisbleSyncFrom" size="40%" @closed="drawerClose">
      <el-form ref="formUpload" :inline="true" size="small" style="margin: 0; padding: 0" label-width="80px">
        <el-form-item :label="$t('lang_pack.progbatchupd.updateMode')">
          <!-- 更新方式 -->
          <el-select v-model="formUpload.proc_update_way" @change="procUpdateWayChange">
            <el-option v-for="item in updateWayData" :key="item.fastcode_code" :label="item.fastcode_des" :value="item.fastcode_code" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.progbatchupd.file')">
          <!-- 文件 -->
          <el-upload
            ref="upload"
            :multiple="true"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :on-change="handleImport"
            :auto-upload="false"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="toButDrawerCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" @click="toButDrawerUpload">{{ $t('lang_pack.commonPage.upload') }}</el-button>
        <!-- 上传 -->
      </div>
    </el-drawer>

    <el-drawer append-to-body title="文件列表" :visible.sync="dialogVisbleSyncFileList" size="40%" @closed="drawerFileListClose">
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" :data="procDDate" style="width: 100%" :stripe="true" max-height="350px">
        <el-table-column  :label="$t('lang_pack.progbatchupd.serialNumber')" type="index" width="120" />
        <!-- 序号 -->
        <el-table-column  :show-overflow-tooltip="true" prop="cell_container_name" :label="$t('lang_pack.progbatchupd.cell')" />
        <!-- 单元 -->
        <el-table-column  :show-overflow-tooltip="true" prop="proc_code" :label="$t('lang_pack.progbatchupd.procedure')" />
        <!-- 程序 -->
        <el-table-column  :show-overflow-tooltip="true" prop="proc_update_way" :label="$t('lang_pack.progbatchupd.updateMode')" />
        <!-- 更新方式 -->
        <el-table-column  :show-overflow-tooltip="true" prop="file_name" :label="$t('lang_pack.progbatchupd.fileName')" />
        <!-- 文件名称 -->
        <el-table-column  :show-overflow-tooltip="true" prop="file_type" :label="$t('lang_pack.progbatchupd.fileType')" />
        <!-- 文件类型 -->
        <el-table-column  :show-overflow-tooltip="true" prop="file_size" :label="$t('lang_pack.progbatchupd.fileSize')" />
        <!-- 文件大小 -->
      </el-table>
    </el-drawer>

    <el-row :gutter="20" style="margin-top: 10px">
      <el-col :span="6">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect">
            <span>{{ $t('lang_pack.progbatchupd.cell') }}</span>
            <!-- 单元 -->
          </div>
          <el-tree ref="cellTree" show-checkbox :check-on-click-node="true" :data="cellTreeData" :props="cellTreeProps" node-key="cell_id" @check-change="handleCheckChange" />
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect">
            <span>{{ cellTitle }}</span>
          </div>
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :cell-style="getCellStyle" max-height="445px" :highlight-current-row="true">
            <el-table-column  :label="$t('lang_pack.progbatchupd.serialNumber')" type="index" width="120" />
            <!-- 序号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="fastcode_des" :label="$t('lang_pack.progbatchupd.updateObject')" />
            <!-- 更新对象 -->
            <!-- <el-table-column 
              :show-overflow-tooltip="true"
              prop="proc_update_way"
              label="更新方式"
              width="150"
            /> -->

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <el-link class="linkItem" :disabled="$refs.cellTree.getCheckedNodes().length === 0" type="primary" @click="toTableButUpload(scope.row)">上传</el-link>
                <el-link class="linkItem" :disabled="$refs.cellTree.getCheckedNodes().length === 0" type="primary" @click="toTableButFileList(scope.row)">查看文件</el-link>
                <el-link class="linkItem" :disabled="$refs.cellTree.getCheckedNodes().length === 0" type="primary" @click="toTableButInstall(scope.row)">更新</el-link>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import axios from 'axios'
import { lovServer } from '@/api/core/center/server'
import { lovFastcode } from '@/api/core/system/sysFastcode'

import { querySysCoreCell, SysCoreCellUpdateProcSave, UpdateProcDSel, ServerCellProcInfoSel, ServerUpdate, ClearProcAndProcD } from '@/api/core/center/cellProgBatchUpd'

export default {
  name: 'CELLPROGBATCHUPD',
  // 数据模型
  data() {
    return {
      dialogVisbleSyncFrom: false,
      dialogVisbleSyncFileList: false,
      uploadLimit: 2, // 注意：加密程序需要传2个文件
      uploadAccept: '.jar,.tar.gz',
      // 查询条件
      // 是否有效(查询条件)
      formServer: {
        server_id: ''
      },
      formUpload: {
        proc_update_way: 'package'
      },
      // cell树结构数据
      cellTreeData: [],
      cellTreeProps: {
        children: 'children',
        label: 'cell_container_name'
      },
      cellTitle: '当前选择单元:',
      // Table
      listLoadingTable: false,
      popTableBut: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],
      selectedTable: [], // 已选择项

      serverData: [],
      updateWayData: [],
      currentCell: [],
      currentProc: [],
      // 上传
      fileList: [],
      fileData: '',
      procDDate: []
    }
  },

  mounted: function() {},
  created: function() {
    this.serverData = []
    var query = {
      enable_flag: 'Y'
    }
    lovServer(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.count > 0) {
          this.serverData = defaultQuery.data
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    // 更新方式Lov
    const query1 = {
      userID: Cookies.get('userId'),
      fastcode_group_code: 'PROC_UPDATE_WAY'
    }
    // 从后台获取到对象数组
    lovFastcode(query1)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.updateWayData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })

    this.loadTable()
  },

  methods: {
    // 关闭抽屉时
    drawerClose() {
      this.fileList = []
    },
    drawerFileListClose() {
      this.procDDate = []
    },
    getCellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec;padding:5px 0px 5px 0px'
    },
    handleCheckChange(data, checked, indeterminate) {
      var arr = []

      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        arr.push(item.cell_container_name)
      })
      this.cellTitle = '当前选择单元:' + arr.join(',')
    },
    // Server值改变时
    serverChange() {
      this.cellTreeData = []
      this.currentCell = []
      this.currentProc = []
      this.$refs.cellTree.setCheckedKeys([])
      if (this.formServer.server_id === '') {
        return
      }
      var query = {
        server_id: this.formServer.server_id
      }
      querySysCoreCell(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.cellTreeData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
    },
    procUpdateWayChange() {
      if (this.formUpload.proc_update_way === 'package') {
        this.uploadLimit = 1
        this.uploadAccept = '.jar,.tar.gz'
      } else {
        this.uploadLimit = 20
        this.uploadAccept = '.dll,.exe,.config,.json,.html'
      }
    },
    loadTable() {
      this.tableDataTable = []
      var query = {
        userID: Cookies.get('userId'),
        fastcode_group_code: 'PROC_CODE'
      }
      this.listLoadingTable = true
      lovFastcode(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            }
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
    },
    // 头部安装按钮事件
    toButInstall() {
      this.$confirm('此操作将根据服务查找已设置更新对象的单元进行更新处理, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          ServerCellProcInfoSel({
            server_id: this.formServer.server_id
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                const server_host = defaultQuery.deploy_info[0].server_host
                const server_port = defaultQuery.deploy_info[0].server_port
                var method = 'SysContainerFileDeploy'
                var path = 'http://' + server_host + ':' + server_port + '/' + method
                ServerUpdate(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '更新成功',
                        type: 'success'
                      })
                      this.updateSuccessDelete({ server_id: this.formServer.server_id }, 1)
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '更新异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可更新信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '初始化数据异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    updateSuccessDelete(parameter, type) {
      ClearProcAndProcD(parameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code == 0) {
            this.$message({
              message: '相关数据清理成功',
              type: 'success'
            })
            if (type === 1) {
              // 查询
              this.serverChange()
            } else {
              this.loadTable()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '相关数据清理异常',
            type: 'error'
          })
        })
    },
    // 头部选择安装按钮事件
    toButChooseInstall() {
      if (this.$refs.cellTree.getCheckedNodes().length === 0) {
        this.$message({
          message: '请选择需要更新的单元',
          type: 'info'
        })
        return
      }
      var arr = []
      var cell_names = []
      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        arr.push(item.cell_id)
        cell_names.push(item.cell_container_name)
      })
      this.$confirm('此操作将根据选择的' + cell_names + '进行更新程序, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          ServerCellProcInfoSel({
            server_id: this.formServer.server_id,
            cell_ids: arr.join(',')
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                const server_host = defaultQuery.deploy_info[0].server_host
                const server_port = defaultQuery.deploy_info[0].server_port
                var method = 'SysContainerFileDeploy'
                var path = 'http://' + server_host + ':' + server_port + '/' + method
                ServerUpdate(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '更新成功',
                        type: 'success'
                      })
                      this.updateSuccessDelete({ cell_ids: arr.join(',') }, 1)
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '更新异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可更新信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '初始化数据异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    // 列表上传按钮事件
    toTableButUpload(row) {
      this.currentProc = row
      this.dialogVisbleSyncFrom = true
    },
    toTableButFileList(row) {
      this.procDDate = []
      var arr = []
      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        arr.push(item.cell_id)
      })
      var query = {
        cell_ids: arr.join(','),
        proc_code: row.fastcode_code
      }
      UpdateProcDSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code == 0) {
            if (defaultQuery.data.length > 0) {
              this.procDDate = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
      this.dialogVisbleSyncFileList = true
    },
    // 列表安装按钮事件
    toTableButInstall(row) {
      var cell_names = []
      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        cell_names.push(item.cell_container_name)
      })
      this.$confirm('确定要更新' + cell_names + '的' + row.fastcode_code + '吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var arr = []
          this.$refs.cellTree.getCheckedNodes().forEach(item => {
            arr.push(item.cell_id)
          })
          ServerCellProcInfoSel({
            server_id: this.formServer.server_id,
            cell_ids: arr.join(','),
            proc_code: row.fastcode_code
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                const server_host = defaultQuery.deploy_info[0].server_host
                const server_port = defaultQuery.deploy_info[0].server_port
                var method = 'SysContainerFileDeploy'
                var path = 'http://' + server_host + ':' + server_port + '/' + method
                ServerUpdate(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '更新成功',
                        type: 'success'
                      })
                      this.updateSuccessDelete(
                        {
                          cell_ids: arr.join(','),
                          proc_code: row.fastcode_code
                        },
                        2
                      )
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '更新异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可更新信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '初始化数据异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    // 列表选择事件
    toSelectionTableChangeHandler(val) {
      // Table 选中事件
      this.selectedTable = val
    },
    // 抽屉取消按钮事件
    toButDrawerCancel() {
      this.dialogVisbleSyncFrom = false
    },
    // 抽屉上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const serverInfo = this.serverData.filter(item => item.server_id === this.formServer.server_id)[0]
      const server_code = serverInfo.server_code
      const server_host = serverInfo.server_host
      const server_port = serverInfo.server_port
      var upload_info = []

      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        var deploy_info = {}
        deploy_info.server_code = server_code
        deploy_info.cell_container_name = item.cell_container_name
        deploy_info.prod_line_code = item.prod_line_code
        deploy_info.proc_code = this.currentProc.fastcode_code
        deploy_info.proc_update_way = this.formUpload.proc_update_way
        upload_info.push(deploy_info)
      })

      this.fileData = new FormData()
      this.fileData.append('upload_info', JSON.stringify(upload_info))
      this.$refs.upload.submit()
      // 配置路径
      var method = 'SysContainerFileUploadBatch'
      var path = 'http://' + server_host + ':' + server_port + '/' + method
      this.listLoadingTable = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code == 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            this.initCellFilieUpdateProc()
            this.currentProc = []
            this.dialogVisbleSyncFrom = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
            this.listLoadingTable = false
          }
        })
    },
    initCellFilieUpdateProc() {
      var procDetailList = []
      this.fileList.forEach(item => {
        var procDetai = {}
        procDetai.file_name = item.name
        procDetai.file_type = item.raw.type
        procDetai.file_size = item.size
        procDetailList.push(procDetai)
      })
      var cell_ids = []
      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        cell_ids.push(item.cell_id)
      })
      var data = {
        userID: Cookies.get('userId'),
        cell_ids: cell_ids.join(','),
        proc_code: this.currentProc.fastcode_code,
        proc_update_way: this.formUpload.proc_update_way,
        proc_detail_list: procDetailList
      }
      SysCoreCellUpdateProcSave(data)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code == 0) {
            this.$message({
              type: 'success',
              message: '文件信息保存成功!'
            })
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
          this.listLoadingTable = false
        })
    },
    // 文件
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.append('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {}
  }
}
</script>

<style scoped lang="scss">
.box-card {
  height: calc(100vh - 55px);
  padding: 10px;
  padding-top: 20px;
  .box-card1 {
    height: calc(100vh - 150px);
    //   .el-card__header{
    //       border-bottom: 1px solid #ff5722;
    //   }
    .el-checkbox__label {
      font-size: 12px;
      color: #757575;
    }
  }
}
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-card__body {
  padding: 10px;
}
.el-table .success-row {
  background: #6cca9e;
  font-weight: 600;
}
.wrapTextSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    white-space: nowrap;
    font-size: 14px;
    color: rgb(117, 117, 117);
    font-weight: bold;
    margin-right: 10px;
  }
}
.el-tree--highlight-current {
  .el-tree-node__content {
    padding: 10px;
    height: auto;
  }
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: #e8efff;
}
::v-deep .el-tree-node__content {
  padding: 10px;
  height: auto;
}
::v-deep .el-tree-node__content:hover {
  background-color: #e8efff;
}
</style>
