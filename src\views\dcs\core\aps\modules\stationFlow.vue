<template>
    <el-card shadow="always" style="margin-top: 10px">
        <!--工具栏-->
        <crudOperation show="" :permission="permission" />
        <el-row :gutter="20">
            <el-col :span="24">
                <el-dialog title="过战信息"  width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
                    <el-table border ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%"
                        :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true"
                        @selection-change="crud.selectionChangeHandler">
                        <!-- 生产任务号 -->
                        <el-table-column  :show-overflow-tooltip="true" min-width="120" align="center" prop="task_num"
                            :label="$t('lang_pack.sortingArea.ProductionTaskNumber')" />
                        <!-- 当前工位号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="100" prop="station_code"
                            :label="$t('lang_pack.sortingArea.CurrentStationNumber')" />
                        <!-- 来源任务号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="100" prop="task_from"
                            :label="$t('lang_pack.sortingArea.SourceStaus')" />
                        <!-- 切割机 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="80" prop="cut_texture"
                            :label="$t('lang_pack.sortingArea.Cutterbar')" />
                        <!-- 到达时间 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="120" prop="arrive_date"
                            :label="$t('lang_pack.sortingArea.arriveDate')" />
                        <!-- 离开时间 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="120" prop="leave_date"
                            :label="$t('lang_pack.sortingArea.leaveDate')" />
                        <!-- 工位消耗时间 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="120" prop="cost_time"
                            :label="$t('lang_pack.sortingArea.StationConsumptionTime')" />
                        <!-- 托盘号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="80" prop="pallet_num"
                            :label="$t('lang_pack.sortingArea.PalletNumber')" />
                        <!-- 钢板型号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="80" prop="model_type"
                            :label="$t('lang_pack.cuttingZone.SteelPlateModel')" />
                        <!-- 是否报工 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="80" prop="bg_flag"
                            :label="$t('lang_pack.sortingArea.ReportWorkOrNot')" >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.bg_flag == 'Y' ? '已报工' : '未报工' }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination />
                </el-dialog>
            </el-col>
        </el-row>
    </el-card>
</template>
<script>
import crudStationFlow from '@/api/dcs/core/aps/stationFlow'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
    name: 'SORTRESULT',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '过战信息',
            // 唯一字段
            idField: 'task_num',
            // 排序
            sort: ['task_num asc'],
            // CRUD Method
            crudMethod: { ...crudStationFlow },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: false
            },
            query: {
                task_num:this.propsData.task_num
            }
        })
    },
    props:{
        task_num:{
            type:[String,Number],
            default:''
        }
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    data() {
        return {
            height: document.documentElement.clientHeight - 400,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            dialogVisible:false
        }
    },
    dicts: ['PART_TYPE'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 400
        }
    },
    methods:{
        handleClose(){
            this.dialogVisible = false
            this.$emit('ok')
        }
    }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
</style>