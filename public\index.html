<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <!--网页的兼容性模式设置优先级-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!--渲染器选择  webkit，ie-comp，ie-stand 极速模式，兼容模式，IE模式-->
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>jhong.ico">
    
    <!--Vue 中设置浏览器的 title 跟随路由的名称变化-->
    <title><%= webpackConfig.name %></title>
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <!--生成的文件将被自动注入-->
  </body>
  <script>
    window._CONFIG = {};
    window._CONFIG['websocketURL'] = 'ws://'+window.location.host+'/';//websocket地址
  </script>
</html>
