<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="5">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div style="text-align: center">
              <div class="el-upload">
                <img :src="user.avatarPath ? 'data:image/png;base64,' + user.avatarPath : Avatar" title="点击上传头像" class="avatar" @click="toggleShow">
                <myUpload ref="myUpload" v-model="show" :url="updateAvatarApi" :params="uploadParams" :no-rotate="false" @crop-upload-success="cropUploadSuccess" @crop-upload-fail="cropUploadFail" />
              </div>
            </div>
            <ul class="user-info">
              <li>
                <div style="height: 100%">
                  <svg-icon icon-class="login" /> 登录账号
                  <div class="user-right">{{ user.username }}</div>
                </div>
              </li>
              <li>
                <svg-icon icon-class="user1" /> 用户姓名
                <div class="user-right">{{ user.nickName }}</div>
              </li>
              <li>
                <svg-icon icon-class="dept" /> 所属部门
                <div class="user-right">{{ dict.label.DEPARTMENT[user.depart_code] }}</div>
              </li>
              <li>
                <svg-icon icon-class="phone" /> 手机号码
                <div class="user-right">{{ user.phone }}</div>
              </li>
              <li>
                <svg-icon icon-class="email" /> 用户邮箱
                <div class="user-right">{{ user.email }}</div>
              </li>
              <li>
                <svg-icon icon-class="anq" /> 安全设置
                <div class="user-right">
                  <a @click="$refs.pass.dialog = true">修改密码</a>
                </div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="16" :lg="18" :xl="19">
        <!--    用户资料    -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>用户资料</span>
          </div>
          <el-form ref="form" :inline="false" :model="form" :rules="rules" style="margin-top: 10px;" size="small" label-width="65px">
            <el-form-item label="姓名：" prop="staff_name" style="padding-bottom:15px;">
              <el-input v-model="form.staff_name" style="width: 35%" />
            </el-form-item>
            <el-form-item label="手机号：" prop="staff_phone" style="padding-bottom:15px;">
              <el-input v-model="form.staff_phone" style="width: 35%;" />
            </el-form-item>
            <el-form-item label="邮箱：" prop="staff_email" style="padding-bottom:15px;">
              <el-input v-model="form.staff_email" style="width: 35%;" />
            </el-form-item>
            <el-form-item label="">
              <el-button :loading="saveLoading" size="mini" type="primary" @click="doSubmit">保存配置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <updatePass ref="pass" />
  </div>
</template>

<script>
import myUpload from 'vue-image-crop-upload'
import Cookies from 'js-cookie'
import { mapGetters } from 'vuex'
import store from '@/store'
import Avatar from '@/assets/images/avatar.png'
import { isvalidPhone } from '@/utils/validate' // 数据验证
import updatePass from './updatePass'
import { updateDtl } from '@/api/core/system/sysUser'
export default {
  name: 'Center',
  components: { updatePass, myUpload },
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'))
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    return {
      show: false,
      Avatar: Avatar,
      saveLoading: false,
      headers: {
        Authorization: ''
      },
      form: {},
      rules: {
        staff_name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        staff_phone: [{ required: true, trigger: 'blur', validator: validPhone }],
        staff_email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }, { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }]
      },
      uploadParams: { user_name: Cookies.get('userName'), user_id: '' }
    }
  },
  // 数据字典
  dicts: ['DEPARTMENT'],
  computed: {
    ...mapGetters(['user', 'updateAvatarApi', 'baseApi'])
  },
  created() {
    this.form = { user_name: Cookies.get('userName'), staff_name: this.user.nickName, staff_phone: this.user.phone, staff_email: this.user.email }
  },
  methods: {
    toggleShow() {
      this.show = !this.show
    },
    cropUploadSuccess(jsonData, field) {
      store.dispatch('GetInfo').then(() => {})
      this.show = !this.show
      this.$refs.myUpload.off()
    },
    cropUploadFail(jsonData, field) {
      this.show = !this.show
      this.$refs.myUpload.off()
    },
    doSubmit() {
      if (this.$refs['form']) {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.saveLoading = true
            updateDtl(this.form)
              .then(res => {
                this.saveLoading = false
                if (res.code === 0) {
                  this.$notify({
                    title: '保存成功',
                    type: 'success'
                  })
                  store.dispatch('GetInfo').then(() => {})
                } else {
                  this.$notify({
                    title: '保存失败，' + res.msg,
                    type: 'error'
                  })
                }
              })
              .catch(() => {
                this.saveLoading = false
              })
          }
        })
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.box-card {
    height: calc(100vh - 100px);
  }
.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}
.user-info {
  padding-left: 0;
  list-style: none;
  li {
    border-bottom: 1px solid #f0f3f4;
    padding: 11px 0;
    font-size: 13px;
  }
  .user-right {
    float: right;
    a {
      color: #317ef3;
    }
  }
}
</style>
