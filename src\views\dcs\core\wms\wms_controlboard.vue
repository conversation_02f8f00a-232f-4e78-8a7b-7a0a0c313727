<template>
  <el-container style="height: 100%">
    <el-header
      :style="
        'background-image:url(' +
          headerBackground +
          ');background-size:100% 100%;width:100%;height:90px'
      "
    >
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7">
          <img
            :src="headerLogo"
            style="width: 150px; height: 30px; float: left; margin-left: 210px;margin-top: 5px;"
          >
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 25px">
          <span class="title">智能下料车间-智能行车状态监控</span>
        </el-col>
        <el-col
          :span="7"
          style="text-align: center; padding-top: 10px; padding-right: 150px"
        ><span class="time"> {{ nowDate }}{{ time }}{{ week }} </span></el-col>
      </el-row>
      <div style="display: flex;justify-content: left;align-items: center;color: #fff;">
        <img
          :src="monitorInit"
          style="width: 30px"
        >
        <span class="dataInfo">无</span>
        <img
          :src="monitorGreen"
          style="width: 30px;margin-left: 20px;"
        >
        <span class="dataInfo">正常</span>
        <img
          :src="monitorRed"
          style="width: 30px;margin-left: 20px;"
        >
        <span class="dataInfo">异常</span>
      </div>
    </el-header>
    <el-main>
      <el-row v-for="(item,index) in TagKey" :key="index" :gutter="20" :style="{'margin-top':index === 1 ? '10px' :''}">
        <el-col v-for="(itemArr,i) in item.keyValue" :key="i" :span="8">
          <el-card class="cardTextBox">
            <div slot="header" class="wrapTextSelect">
              <span>{{ itemArr.name }}</span>
            </div>
            <div :class="{ 'PlcStatusInfo' : index === 0 , 'suckerPlcAlarmInfo': index === 1}">
              <template v-for="itemKey in itemArr.key">
                <div v-if="itemKey.textValue" class="PlcStatusValue">
                  <span class="dataInfo">{{ itemKey.label }}：{{ itemKey.tagValue }}</span>
                </div>
                <div v-else class="PlcStatusValue">
                  <img
                    :id="`img_signal_${itemKey.key.split('/PlcAlarm/')[1]}`"
                    :src="(itemKey.tagValue === '1' && itemKey.showLight && itemKey.showLight === 'monitorGreen') ? monitorGreen : (itemKey.tagValue === '1' && itemKey.showLight && itemKey.showLight === 'monitorRed') ? monitorRed : monitorInit"
                    style="width: 30px"
                  >
                  <span class="dataInfo">{{ itemKey.label }}</span>
                </div>
              </template>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
  </el-container>
</template>
<script>
import { getIconPathData } from '@/components/wmsDashboard/icon'
import headerBackground from '@/assets/images/dcs/header1.png'
import headerLogo from '@/assets/images/dcs/xcmg.png'
import xcmgGroup from '@/assets/images/dcs/xcmgGroup.jpg'
import monitorInit from '@/assets/images/dcs/Init.png'
import monitorGreen from '@/assets/images/dcs/green.png'
import monitorRed from '@/assets/images/dcs/red.png'
import axios from 'axios'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
import { lovCell, selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  data() {
    return {
      imgData: getIconPathData(),
      headerBackground: headerBackground,
      headerLogo: headerLogo,
      xcmgGroup: xcmgGroup,
      monitorInit: monitorInit,
      monitorGreen: monitorGreen,
      monitorRed: monitorRed,
      diameter: 15,
      nodeForm: {},
      nowDate: '',
      time: '',
      week: '',
      timer1: null,
      timerLoad: null,
      // MQTT
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
      'ScadaWeb_' +
      Cookies.get('userId') +
      '_' +
      Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      TagKey: [
        {
          keyValue: [
            {
              name: '大车状态',
              key: [
                { label: '大车变频器故障', key: 'SlCarPlc01/PlcAlarm/Alarm001', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车激光测距故障', key: 'SlCarPlc01/PlcAlarm/Alarm004', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车主电源断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm009', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车风机断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm010', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车抱闸断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm011', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车风机接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm020', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车抱闸接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm021', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车激光数据跳变故障', key: 'SlCarPlc01/PlcAlarm/Alarm048', showLight: 'monitorRed', tagValue: '0' },
                { label: '大车东限位', key: 'SlCarPlc01/PlcAlarm/Alarm050', showLight: 'monitorGreen', tagValue: '0' },
                { label: '大车西限位', key: 'SlCarPlc01/PlcAlarm/Alarm051', showLight: 'monitorGreen', tagValue: '0' },
                { label: '大车东减速', key: 'SlCarPlc01/PlcAlarm/Alarm052', showLight: 'monitorGreen', tagValue: '0' },
                { label: '大车西减速', key: 'SlCarPlc01/PlcAlarm/Alarm053', showLight: 'monitorGreen', tagValue: '0' },
                { label: '大车变频报警码', key: 'SlCarPlc01/PlcAlarm/Alarm072', showLight: 'monitorGreen', tagValue: '0', textValue: true },
                { label: '大车变频故障码', key: 'SlCarPlc01/PlcAlarm/Alarm075', showLight: 'monitorGreen', tagValue: '0', textValue: true }
              ]
            },
            {
              name: '小车状态',
              key: [
                { label: '小车变频器故障', key: 'SlCarPlc01/PlcAlarm/Alarm002', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车激光测距故障', key: 'SlCarPlc01/PlcAlarm/Alarm005', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车主电源断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm012', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车风机断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm013', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车抱闸断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm014', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车风机接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm022', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车抱闸接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm023', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车顶起故障', key: 'SlCarPlc01/PlcAlarm/Alarm028', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车激光数据跳变故障', key: 'SlCarPlc01/PlcAlarm/Alarm049', showLight: 'monitorRed', tagValue: '0' },
                { label: '小车东限位', key: 'SlCarPlc01/PlcAlarm/Alarm054', showLight: 'monitorGreen', tagValue: '0' },
                { label: '小车北限位', key: 'SlCarPlc01/PlcAlarm/Alarm055', showLight: 'monitorGreen', tagValue: '0' },
                { label: '小车东减速', key: 'SlCarPlc01/PlcAlarm/Alarm056', showLight: 'monitorGreen', tagValue: '0' },
                { label: '小车北减速', key: 'SlCarPlc01/PlcAlarm/Alarm057', showLight: 'monitorGreen', tagValue: '0' },
                { label: '小车变频报警码', key: 'SlCarPlc01/PlcAlarm/Alarm073', showLight: 'monitorGreen', tagValue: '0', textValue: true },
                { label: '小车变频故障码', key: 'SlCarPlc01/PlcAlarm/Alarm076', showLight: 'monitorGreen', tagValue: '0', textValue: true }
              ]
            },
            {
              name: '起升状态',
              key: [
                { label: '起升变频器故障', key: 'SlCarPlc01/PlcAlarm/Alarm003', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升激光测距故障', key: 'SlCarPlc01/PlcAlarm/Alarm006', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升主电源断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm015', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升风机断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm016', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升抱闸断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm017', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升风机接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm024', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升抱闸接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm025', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升抱闸反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm026', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升抱闸磨损故障', key: 'SlCarPlc01/PlcAlarm/Alarm027', showLight: 'monitorRed', tagValue: '0' },
                { label: '起升上限位1', key: 'SlCarPlc01/PlcAlarm/Alarm058', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升上限位2', key: 'SlCarPlc01/PlcAlarm/Alarm059', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升下限位1', key: 'SlCarPlc01/PlcAlarm/Alarm060', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升下限位2', key: 'SlCarPlc01/PlcAlarm/Alarm061', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升上减速1', key: 'SlCarPlc01/PlcAlarm/Alarm062', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升下减速1', key: 'SlCarPlc01/PlcAlarm/Alarm063', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升上减速2', key: 'SlCarPlc01/PlcAlarm/Alarm064', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升下减速2', key: 'SlCarPlc01/PlcAlarm/Alarm065', showLight: 'monitorGreen', tagValue: '0' },
                { label: '起升变频报警码', key: 'SlCarPlc01/PlcAlarm/Alarm074', tagValue: '0', textValue: true },
                { label: '起升变频故障码', key: 'SlCarPlc01/PlcAlarm/Alarm077', tagValue: '0', textValue: true }
              ]
            }
          ]
        },
        {
          keyValue: [
            {
              name: '吸盘状态',
              key: [
                { label: '第1组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm036', showLight: 'monitorRed', tagValue: '0' },
                { label: '第2组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm037', showLight: 'monitorRed', tagValue: '0' },
                { label: '第3组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm038', showLight: 'monitorRed', tagValue: '0' },
                { label: '第4组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm039', showLight: 'monitorRed', tagValue: '0' },
                { label: '第5组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm040', showLight: 'monitorRed', tagValue: '0' },
                { label: '第6组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm041', showLight: 'monitorRed', tagValue: '0' },
                { label: '第7组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm042', showLight: 'monitorRed', tagValue: '0' },
                { label: '第8组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm043', showLight: 'monitorRed', tagValue: '0' },
                { label: '第9组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm044', showLight: 'monitorRed', tagValue: '0' },
                { label: '第10组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm045', showLight: 'monitorRed', tagValue: '0' },
                { label: '第11组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm046', showLight: 'monitorRed', tagValue: '0' },
                { label: '第12组吸板故障', key: 'SlCarPlc01/PlcAlarm/Alarm047', showLight: 'monitorRed', tagValue: '0' }
              ]
            },
            {
              name: '吊具状态',
              key: [
                { label: '吊具断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm018', showLight: 'monitorRed', tagValue: '0' },
                { label: '吊具通讯故障', key: 'SlCarPlc01/PlcAlarm/Alarm033', showLight: 'monitorRed', tagValue: '0' },
                { label: '吊具故障', key: 'SlCarPlc01/PlcAlarm/Alarm035', showLight: 'monitorRed', tagValue: '0' },
                { label: '吊具下降减速位1', key: 'SlCarPlc01/PlcAlarm/Alarm066', showLight: 'monitorGreen', tagValue: '0' },
                { label: '吊具下降减速位2', key: 'SlCarPlc01/PlcAlarm/Alarm067', showLight: 'monitorGreen', tagValue: '0' },
                { label: '吊具下降停止位1', key: 'SlCarPlc01/PlcAlarm/Alarm068', showLight: 'monitorGreen', tagValue: '0' },
                { label: '吊具下降停止位2', key: 'SlCarPlc01/PlcAlarm/Alarm069', showLight: 'monitorGreen', tagValue: '0' },
                { label: '吊具下降极限位1', key: 'SlCarPlc01/PlcAlarm/Alarm070', showLight: 'monitorGreen', tagValue: '0' },
                { label: '吊具下降极限位2', key: 'SlCarPlc01/PlcAlarm/Alarm071', showLight: 'monitorGreen', tagValue: '0' },
                { label: '钢板激光测距数据', key: 'SlCarPlc01/PlcAlarm/Alarm080', tagValue: '0', textValue: true }
              ]
            },
            {
              name: '其他状态',
              key: [
                { label: '钢板激光测距故障', key: 'SlCarPlc01/PlcAlarm/Alarm007', showLight: 'monitorRed', tagValue: '0' },
                { label: '主断路器闭合故障', key: 'SlCarPlc01/PlcAlarm/Alarm008', showLight: 'monitorRed', tagValue: '0' },
                { label: '主接触器反馈故障', key: 'SlCarPlc01/PlcAlarm/Alarm019', showLight: 'monitorRed', tagValue: '0' },
                { label: '相序故障', key: 'SlCarPlc01/PlcAlarm/Alarm029', showLight: 'monitorRed', tagValue: '0' },
                { label: '称重故障', key: 'SlCarPlc01/PlcAlarm/Alarm030', showLight: 'monitorRed', tagValue: '0' },
                { label: '分张故障', key: 'SlCarPlc01/PlcAlarm/Alarm031', showLight: 'monitorRed', tagValue: '0' },
                { label: '放板故障', key: 'SlCarPlc01/PlcAlarm/Alarm032', showLight: 'monitorRed', tagValue: '0' },
                { label: '机旁柜通讯故障', key: 'SlCarPlc01/PlcAlarm/Alarm034', showLight: 'monitorRed', tagValue: '0' },
                { label: '故障码', key: 'SlCarPlc01/PlcAlarm/Alarm078', tagValue: '0', textValue: true },
                { label: '设备自检结果码', key: 'SlCarPlc01/PlcAlarm/Alarm079', tagValue: '0', textValue: true }
              ]
            }
          ]
        }
      ]
    }
  },
  mounted() {
    this.currentTime()
    this.timer1 = setInterval(this.workLocation, 1000)
    this.timerLoad = setInterval(() => {
      location.reload()
    }, 1000 * 60 * 10)
    this.toStartWatch()
  },
  beforeDestory() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.timerLoad) {
      clearInterval(this.timerLoad)
    }
    // 离开此页面时销毁mqtt链接
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  created() {
  },
  methods: {
    currentTime() {
      setInterval(this.formatDate, 1000)
    },
    formatDate() {
      const date = new Date()
      const hours = date.getHours()
      const minuter =
    date.getMinutes() > 9 ? date.getMinutes() : '0' + date.getMinutes()
      const seconds =
    date.getSeconds() > 9 ? date.getSeconds() : '0' + date.getSeconds()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const d = date.getDate()
      const day = date.getDay()
      const week = [
        '星期日',
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六'
      ]

      this.nowDate = year + '-' + month + '-' + d + ' '
      this.time = hours + ':' + minuter + ':' + seconds + ' '
      this.week = week[day]
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 获取连接地址
      // 'ws://***************:8090/mqtt'
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.GetTagValue(result.ip, result.webapi_port)
          //  var connectUrl ="ws://*************:8083/mqtt";
          // connectUrl=MQTT_SERVICE;
          console.log('拼接URL：' + connectUrl)
          // mqtt连接
          // this.clientMqtt = mqtt.connect(MQTT_SERVICE, this.optionsMqtt); //开启连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', (e) => {
            this.mqttConnStatus = true
            // for (let index = 0; index < this.tagKeyList.length; index++) {
            //   this.topicSubscribe("SCADA_CHANGE/" + this.tagKeyList[index]);
            // }
            this.TagKey.forEach(item => {
              item.keyValue.forEach(subItem => {
                subItem.key.forEach(keyItem => {
                  // if(keyItem.key === tagKey){
                  //   keyItem.tagValue = tagValue
                  // }
                  this.topicSubscribe('SCADA_CHANGE/' + keyItem.key)
                })
              })
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', (error) => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })

            clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', (error) => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', (error) => {
            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          this.clientMqtt.on('close', () => {
            this.clientMqtt.end()

            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())

            const res = JSON.parse(message.toString())

            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    GetTagValue(ip, port) {
      var readTagArray = []
      this.TagKey.forEach(item => {
        item.keyValue.forEach(subItem => {
          subItem.key.forEach(keyItem => {
            readTagArray.push({ tag_key: keyItem.key })
          })
        })
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + ip + ':' + port + method
        // path = 'http://*************:8089' + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()

                  for (let i = 0; i < this.TagKey.length; i++) {
                    const item = this.TagKey[i]
                    for (let j = 0; j < item.keyValue.length; j++) {
                      const subItem = item.keyValue[j]
                      for (let k = 0; k < subItem.key.length; k++) {
                        const keyItem = subItem.key[k]
                        if (keyItem.key === tagKey) {
                          keyItem.tagValue = tagValue
                          break // 终止循环
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },

    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_STATUS/') >= 0 || channel.indexOf('SCADA_BEAT/') >= 0 || channel.indexOf('SCADA_MSG/') >= 0) {

      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue

      for (let i = 0; i < this.TagKey.length; i++) {
        const item = this.TagKey[i]
        for (let j = 0; j < item.keyValue.length; j++) {
          const subItem = item.keyValue[j]
          for (let k = 0; k < subItem.key.length; k++) {
            const keyItem = subItem.key[k]
            if (keyItem.key === TagKey) {
              keyItem.tagValue = TagNewValue
              break // 终止循环
            }
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.el-header {
background-color: #161522;
text-align: center;
}

.el-header .title {
font-size: 25px;
letter-spacing: 5px;
background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
-webkit-background-clip: text;
color: transparent;
font-weight: 600;
}
.el-header .time {
font-size: 20px;
background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
-webkit-background-clip: text;
color: transparent;
font-weight: 600;
}
.el-main {
background-color: #161522;
padding-top: 5px;
overflow: hidden;
}
::v-deep .el-main .cardTextBox{

  color:#fff;
  border:1px solid #808183;

  background: linear-gradient(to left, #b7b8bc, #b7b8bc)  left top no-repeat, linear-gradient(to bottom, #b7b8bc, #b7b8bc) left top no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) right top no-repeat, linear-gradient(to bottom, #b7b8bc, #b7b8bc) right top no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) left bottom no-repeat, linear-gradient(to bottom, #b7b8bc, #b7b8bc) left bottom no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) right bottom no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) right bottom no-repeat;
  background-size: 2px 12px, 12px 2px, 2px 12px, 12px 2px;
  background-color:#232526;
  .el-card__header{
    padding:10px !important;
    font-size:20px;
    font-weight:600;
    border-bottom:1px solid #808183;
  }
  .dataInfo {
    font-size: 20px;
    background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
    -webkit-background-clip: text;
    margin-left: 5px;
  }
  .PlcStatusInfo{
    display: flex;
    flex-wrap: wrap;
    height: 400px;
    .PlcStatusValue{
      width:50%;
      display:flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .PlcStatuspoint{
      width:50%;
      display:flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .RCSStatusValue{
      width:100%;
      display: flex;
      .dataInfoName{
        width: 50%;
        display: block;
        text-align: right;
      }

    }
  }
  .suckerPlcAlarmInfo{
      display: flex;
      overflow-x: hidden;
      flex-wrap: wrap;
      height: 240px;
      .PlcStatusValue{
          width:50%;
          display:flex;
          align-items: center;
          margin-bottom: 10px;
      }
  }
}
</style>
