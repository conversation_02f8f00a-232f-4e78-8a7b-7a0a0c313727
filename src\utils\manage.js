import request from '@/utils/request'

// 导出
export function exportGet(url,method,data) {
    return request({
      url,
      method,
      data,
      responseType: 'blob',
    })
  }

  export function exportPost(url,data) {
    return request({
      url,
      method:'post',
      data,
      responseType: 'blob',
    })
  }
  export function getAction(url,data) {
    return request({
      url,
      method:'get',
      data,
    })
  }
  export function postAction(url,data) {
    return request({
      url,
      method:'post',
      data,
    })
  }