import request from '@/utils/request'

// 配方PACK查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackSel',
    method: 'post',
    data
  })
}
// 配方PACK增加
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackIns',
    method: 'post',
    data
  })
}
// 配方PACK修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackUpd',
    method: 'post',
    data
  })
}
// 配方PACK删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
