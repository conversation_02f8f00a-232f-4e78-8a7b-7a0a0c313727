import request from '@/utils/request'

// 查询供应商
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSupplierSel',
    method: 'post',
    data
  })
}
// 新增供应商
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSupplierIns',
    method: 'post',
    data
  })
}
// 修改供应商
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSupplierUpd',
    method: 'post',
    data
  })
}
// 删除供应商
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSupplierDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
