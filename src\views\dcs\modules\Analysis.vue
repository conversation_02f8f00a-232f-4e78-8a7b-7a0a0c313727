<template>
    <el-row>
      <el-col>
        <ECharts ref="chart3" :options="orgOptions" style="height:250px;" />
      </el-col>
    </el-row>
  </template>
  <script>
  import ECharts from 'vue-echarts'
  
  export default {
    components: {
      ECharts
    },
    data() {
      return {
        orgOptions: {},
      }
    },
    created() {},
    mounted() {
      this.orgOptions = {
        backgroundColor: '',
   grid: {
      left: "5%",
      right: "5%",
      bottom: "5%",
      top: "5%",
      containLabel: true,
   },
   tooltip: {
      show: true,
      formatter: "{b}<br/>{c}",
      textStyle: {
         fontWeight: "bold",
         fontSize: 16,
      },
   },
   yAxis: [{
      type: "category",
      inverse: true,
      axisLabel: {
         show: true,
         color: "#000000",
         fontSize: 15,
      },
      splitLine: {
         show: false,
         lineStyle: {
            color: "#707070",
         },
      },
      axisLine: {
         show: true,
         lineStyle: {
            color: "#707070",
            width: 1,
         },
      },
      axisTick: {
         show: false,
      },
      data: ["故障1", "故障2", "故障3", "故障4", "故障5", "故障6", "故障7", "故障8", "故障9"]
   },],
   xAxis: [{
      type: "value",
      axisTick: {
         show: false,
      },
      max: 100,

      axisLine: {
         show: true,
         lineStyle: {
            color: "#707070",
            width: 1,
         },
      },
      splitLine: {
         show: false,
      },
      nameTextStyle: {
         color: 'red',
      },
      // nameLocation: "end",
      name: "",
      axisLabel: {
         inside: false,
         textStyle: {
            color: "#000",
            fontSize: 17,
         },
         interval: 0,
         // rotate: 25,
         formatter: '{value}'
      },
   },],
   series: [{
      name: "assist",
      type: "bar",
      stack: "1",
      itemStyle: {
         normal: {
            barBorderColor: "rgba(0,0,0,0)",
            color: "rgba(0,0,0,0)",
         },
         emphasis: {
            barBorderColor: "rgba(0,0,0,0)",
            color: "rgba(0,0,0,0)",
         },
      },
      tooltip: {
         trigger: "none",
      },
      data: [],
   }, //设置两个柱状图进行重叠，第一层柱子设置透明度,由此来实现柱子与坐标轴之间的距离  stack:''设置重叠效果
   {
      type: "bar",
      stack: "1",
      barWidth: 12,
      barBorderRadius: 30,
      itemStyle: {
         normal: {
            barBorderRadius: 20,
            color: function (params) {
               // console.log('测试',_that.checkName)
               // if (_that.platName == params.name) {
               //    return 'yellow';
               // } else {
                  return {
                     type: "linear",
                     x: 1,
                     y: 0,
                     x2: 0,
                     y2: 0,
                     colorStops: [{
                        offset: 0,
                        color: "#80BDF6", // 0% 处的颜色
                     },
                     {
                        offset: 0.9,
                        color: "#188DF0", // 100% 处的颜色
                     },
                     ],

                  // }
               }
            },
         },
      },
      // zlevel: 2,
      data: [100, 90, 86, 80, 75, 70, 60, 50, 40]
   },
   ],
      }
    },
    methods: {}
  }
  </script>
  <style lang="scss" scoped>
  .echarts{
    width: 100%;
    height: 200px !important;
  }
  </style>
  