import request from '@/utils/request'

  // 查询国际化配置
  export function sel(data) {
    return request({
      url: 'aisEsbWeb/core/system/CoreSysErrorMsgSel',
      method: 'post',
      data
    })
  }
  // 新增国际化配置
  export function add(data) {
    return request({
      url: 'aisEsbWeb/core/system/CoreSysErrorMsgIns',
      method: 'post',
      data
    })
  }
  // 修改国际化配置
  export function edit(data) {
    return request({
      url: 'aisEsbWeb/core/system/CoreSysErrorMsgUpd',
      method: 'post',
      data
    })
  }
  // 删除国际化配置
  export function del(data) {
    return request({
      url: 'aisEsbWeb/core/system/CoreSysErrorMsgDel',
      method: 'post',
      data
    })
  }

  export default { sel, add, edit, del}