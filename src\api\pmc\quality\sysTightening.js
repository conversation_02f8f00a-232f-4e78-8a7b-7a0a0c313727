import request from '@/utils/request'

// 查询中心
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/PmcBqQualityShaftSel',
    method: 'post',
    data
  })
}
// 加注导出
export function exportEventInsert(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/PmcBqQualityShaftExportEventInsert',
    method: 'post',
    data
  })
}
export default { sel, exportEventInsert }
