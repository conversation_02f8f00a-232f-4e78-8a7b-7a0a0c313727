<template>
  <div id="loadMonitor" class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <!-- 料号 -->
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <el-input v-model="query.material_num" clearable size="small" />
              </el-form-item>
            </div>
            <!-- 任务来源 -->
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.taskSource') + ':'">
                <el-select v-model="query.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_FROM"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- 任务状态 -->
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.task_status') + ':'">
                <el-select v-model="query.status" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item label="批号:">
                <el-input v-model="query.batch_num" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card
      ref="queryCard"
      shadow="never"
      class="wrapCard"
      style="margin-top: 10px"
    >
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <div class="orderInfo">
          <span>扫码获取MES任务</span>
          <div>
            <el-input v-model="currentBarcode" clearable size="small" />
          </div>
          <el-button
            :disabled="currentBarcode == null || currentBarcode === ''"
            size="small"
            type="primary"
            @click="handleScan"
          >
            <svg-icon icon-class="scan" style="margin-right: 5px" />{{
              $t("lang_pack.vie.scan")
            }}
          </el-button>
        </div>
        <!-- <el-button
          size="medium"
          type="primary"
          :icon="
            controlValue === '1' ? 'el-icon-video-play' : 'el-icon-video-pause'
          "
          class="playBtn"
          :class="{
            playStart: controlValue === '1',
            playEnd: controlValue != '1',
          }"
          @click="handleTagWrite(appStartTagKey)"
        >{{ controlValue === "1" ? "Start" : "Stop" }}</el-button> -->
      </div>
    </el-card>
    <el-row :gutter="20">
      <el-col :span="18">
        <el-card shadow="always" style="margin-top: 10px">
          <!--工具栏-->
          <crudOperation show="" :permission="permission" />
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <!-- 任务来源 -->
              <el-form-item
                :label="$t('lang_pack.vie.taskSource') + '：'"
                prop="task_from"
              >
                <el-select v-model="form.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_FROM"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 单号 -->
              <el-form-item label="单号：" prop="order_num">
                <el-input v-model="form.order_num" clearable size="small" />
              </el-form-item>
              <!-- 批号 -->
              <el-form-item label="批号：" prop="batch_num">
                <el-input v-model="form.batch_num" clearable size="small" />
              </el-form-item>
              <!-- 料号 -->
              <el-form-item
                :label="$t('lang_pack.vie.partNum') + '：'"
                prop="material_num"
              >
                <el-input v-model="form.material_num" readonly="readonly">
                  <div slot="append">
                    <el-button slot="reference" @click="handleSelect">{{
                      $t("lang_pack.vie.select")
                    }}</el-button>
                  </div>
                </el-input>
              </el-form-item>
              <!-- 计划数量 -->
              <el-form-item :label="$t('lang_pack.vie.planQuantity') + '：'" prop="batch_size">
                <el-input
                  v-model.number="form.batch_size"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <!-- 单包数量 -->
              <el-form-item :label="$t('lang_pack.vie.singlePackQua') + '：'" prop="unit_count">
                <el-input
                  v-model.number="form.unit_count"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <!-- 工单状态 -->
              <el-form-item
                :label="$t('lang_pack.vie.orderStatus') + '：'"
                prop="lot_status"
              >
                <el-select v-model="form.status" clearable filterable disabled>
                  <el-option
                    v-for="item in dict.TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 有效标识 -->
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentification')"
              >
                <el-radio-group v-model="form.enable_flag">
                  <el-radio
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="crud.cancelCU"
              >{{ $t("lang_pack.commonPage.cancel") }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="55" />

            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions
                  style="margin-right: 150px"
                  :column="4"
                  size="small"
                  border
                >
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskSource')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.TASK_FROM[props.row.task_from]
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="单号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.order_num }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('view.field.plan.lotNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.batch_num }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.partNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.material_num }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.planQuantity')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.batch_size }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.singlePackQua')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.unit_count }}</el-descriptions-item>
                  <!-- <el-descriptions-item
                    :label="$t('lang_pack.vie.orderStatus')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.TASK_STATUS[props.row.status] }}</el-descriptions-item> -->
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskStarTime')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.start_at }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskEndTime')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.end_at }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskUserTime')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.cost_time }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.commonPage.validIdentificationt')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.ENABLE_FLAG[props.row.enable_flag]
                  }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.vie.index')"
              type="index"
              width="60"
            />
            <!-- 来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.vie.taskSource')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.TASK_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="order_num"
              label="单号"
              width="120"
              align="center"
            />
            <!-- 批号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="batch_num"
              :label="$t('view.field.plan.lotNum')"
              width="120"
              align="center"
            />
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_num"
              :label="$t('lang_pack.vie.partNum')"
              width="120"
              align="center"
            />
            <!-- 计划数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="batch_size"
              :label="$t('lang_pack.vie.planQuantity')"
              width="130"
              align="center"
            />
            <!-- 单包数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="unit_count"
              :label="$t('lang_pack.vie.singlePackQua')"
              width="130"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="status"
              :label="$t('lang_pack.vie.orderStatus')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  class="statusBtn"
                  :type="scope.row.status === 'PLAN' ? '' : scope.row.status === 'WORK' ? 'warning' : scope.row.status === 'FINISH' ? 'success' : scope.row.status === 'CANCEL' ? 'info' : ''"
                >
                  {{ dict.label.TASK_STATUS[scope.row.status] }}
                </el-button>
              </template>
            </el-table-column>
            <!-- 开始时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="start_at"
              label="开始时间"
              width="140"
              align="center"
            />
            <!-- 结束时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="end_at"
              label="结束时间"
              width="130"
              align="center"
            />
            <!-- 消耗时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cost_time"
              label="消耗时间"
              width="150"
              align="center"
            />
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{
                  scope.row.enable_flag === "Y"
                    ? $t("lang_pack.vie.Yes")
                    : $t("lang_pack.vie.NO")
                }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="120"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :disabled-dle="false"
                >
                  <template slot="right">
                    <el-button
                      v-if="scope.row.lot_status === 'WORK'"
                      v-permission="permission.del"
                      :loading="crud.status.cu === 2"
                      :disabled="false"
                      size="small"
                      type="text"
                      @click="handleCloseJobOrder(scope.row)"
                    >
                      {{ $t("view.button.close") }}
                    </el-button>
                    <!-- 关闭 -->
                    <el-button
                      slot="reference"
                      :loading="crud.status.cu === 2"
                      type="text"
                      size="small"
                      @click="viewsDetails(scope.row)"
                    >
                      明细
                    </el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          shadow="always"
          style="margin-top: 10px"
          :style="{ height: height + 130 + 'px' }"
        >
          <el-form
            ref="form"
            class="el-form-column workOrder"
            :model="form"
            :rules="rules"
            size="small"
            label-width="145px"
            :inline="true"
            :style="{
              'max-height': height + 'px',
              'overflow-x': 'auto',
              'overflow-x': 'hidden',
            }"
          >
            <!-- 订单号 -->
            <el-form-item label="单号：">
              <el-input
                v-model="form.order_num"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 批号 -->
            <el-form-item label="批号：">
              <el-input
                v-model="form.batch_num"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 料号 -->
            <el-form-item label="料号：">
              <el-input
                v-model="form.material_num"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- Tray类型 -->
            <el-form-item label="Tray类型：">
              <!-- <el-input
                v-model="form.tray_type"
                disabled
                clearable
                size="small"
              /> -->
              <el-select v-model="form.tray_type" clearable filterable disabled>
                <el-option
                  v-for="(item, idx) in Object.keys(trayTypeOptionsMap)"
                  :key="item + idx"
                  :label="trayTypeOptionsMap[item]"
                  :value="item"
                >
                  <span style="float: left">{{ trayTypeOptionsMap[item] }}</span>
                  <span
                    style="float: right; color: #8492a6; font-size: 13px"
                  >{{ item }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <!-- <el-divider /> -->
          <div style="text-align: center">
            <el-button
              v-loading="IssLoadingFlag"
              type="primary"
              size="small"
              icon="el-icon-check"
              style="
                position: absolute;
                bottom: 10px;
                margin-left: -70px;
                width: 150px;
                height: 45px;
                font-size: 24px;
              "
              @click="handleOk"
            >{{ $t("lang_pack.vie.confirmIssuance") }}</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <detail v-if="orderFlag" ref="orderFlag" @ok="orderDetail" />
    <el-dialog
      :title="$t('lang_pack.vie.messageAlert')"
      :visible.sync="contentMessage.dialogVisible"
      width="650px"
      top="65px"
    >
      <span>{{ contentMessage.content }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">{{
          $t("lang_pack.vie.close")
        }}</el-button>
      </span>
    </el-dialog>
    <pileDetails
      v-if="tableDialogVisible"
      ref="tableDialog"
      :plan_id="plan_id"
      :data="selectedData"
      @ok="tableDialogVisible = false"
    />
    <el-dialog :title="jsonTitle" width="50%" :visible.sync="jsonDialogVisible">
      <el-input
        v-model="jsonStr"
        autosize
        type="textarea"
        :rows="20"
        :placeholder="$t('lang_pack.interfaceLogs.pleaseEnter')"
        style="max-height: 450px; overflow: auto"
      />
      <span style="color: red">{{ jsonErrorMsg }}</span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import planAPI from '@/api/v4/plan'
import crudPlan from '@/api/pack/core/plan'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import detail from './detail.vue'
import pileDetails from './modules/pileIndex.vue'

const defaultForm = {
  id: '',
  task_from: 'AIS',
  task_type: 'Normal',
  order_num: '',
  batch_num: '',
  material_num: '',
  status: 'PLAN',
  enable_flag: 'Y',
  userName: Cookies.get('userName')
}
export default {
  name: 'PACKTASK',
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    detail,
    pileDetails
  },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.workOrder'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...crudPlan },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 302,
      permission: {
        add: ['admin', 'plan:add'],
        edit: ['admin', 'plan:edit'],
        del: ['admin', 'plan:del']
      },
      rules: {
        task_from: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        order_num: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        batch_num: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        material_num: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ]
      },
      orderFlag: false,
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: 1
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },

      // 监控点位
      controlValue: '',
      messageList: [],
      timer: null,
      messageContent: '',
      MessageLevel: 'info',
      messageShow: false,
      contentMessage: {
        content: '',
        level: 'info',
        dialogVisible: false
      },
      timerVisible: null,
      IssLoadingFlag: false,
      currentBarcode: '',
      tableDialogVisible: false,
      plan_id: '',
      jsonStr: '',
      jsonErrorMsg: '',
      jsonTitle: '',
      jsonDialogVisible: false,
      selectedData: {},
      appStartTagKey: 'BzPlc01/PlcBase/AppStart',
      trayTypeOptionsMap: {
        '1': '薄',
        '2': '厚'
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'TASK_STATUS', 'TASK_TYPE', 'TASK_FROM', 'QR_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 302
    }
    this.getStationData()
  },
  created: function() {
    this.timer = setInterval(() => {
      this.crud.refresh()
    }, 1000 * 30)
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearTimeout(this.timerVisible)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        // cell_id: this.$route.query.cell_id
        cell_id: 1
      }
      this.getCellIp()
    },
    getCellIp() {
      // this.toStartWatch()
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    getTagValue() {
      const readTagArray = [{ tag_key: this.appStartTagKey }]
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      axios
        .post(path, readTagArray, {
          headers: { 'Content-Type': 'application/json' }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  if (tagKey === this.appStartTagKey) {
                    this.controlValue = tagValue
                  }
                }
              }
            }
          }
        })
        .catch((ex) => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    BlurText(e) {
      const boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(
          this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger')
        )
        e.target.value = ''
      }
    },
    getLotStatus(id) {
      var item = this.dict.TASK_STATUS.find((item) => item.id === id)
      if (item !== undefined) {
        return item.label
      }
      return ''
    },
    handleRowClick(row) {
      this.form = row
    },
    orderDetail(row) {
      this.form.material_num = row.model_type
      this.form.tray_type = row.tray_type || '1'
      this.orderFlag = false
    },
    handleSelect() {
      this.orderFlag = true
      this.$nextTick(() => {
        this.$refs.orderFlag.dialogVisible = true
      })
    },
    handleOk() {
      this.IssLoadingFlag = true
      const requestBody = {
        user_name: Cookies.get('userName'),
        order_num: this.form.order_num
      }
      planAPI._post(requestBody)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(this.$t('lang_pack.vie.SuccessIss'))
            // 向BzPlc01/PlcBase/AppStart写入值1
            // this.sendMessage(
            //   'SCADA_WRITE/BzPlc01',
            //   JSON.stringify({
            //     Data: [{ TagKey: this.appStartTagKey, TagValue: '1' }],
            //     ClientName: 'SCADA_WEB'
            //   })
            // )
            this.crud.toQuery()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.msg)
        })
        .finally(() => {
          this.IssLoadingFlag = false
        })
    },
    [CRUD.HOOK.beforeToCU](crud) {
      this.form = {}
      this.form.task_from = 'AIS'
      this.form.task_type = 'Normal'
      this.form.finish_ok_count = '0'
      this.form.finish_ng_count = '0'
      this.form.lot_status = 'PLAN'
      this.form.enable_flag = 'Y'
    },
    // 查询成功后的回调
    [CRUD.HOOK.afterRefresh](crud) {
      for (let i = 0; i < crud.data.length; i++) {
        const item = crud.data[i]
        if (item.materials) {
          this.$set(item, 'materialsJSON', JSON.stringify(item.materials))
        }
      }
    },
    [CRUD.HOOK.beforeSubmit](crud) {
      if (this.form.materialsJSON) {
        this.form.materials = JSON.parse(this.form.materialsJSON)
      }
      crud.form = this.form
    },
    // ----------------------------------【MQTT】----------------------------------
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://************:8083/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        // this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        this.topicSubscribe('AISWEB_MSG/BzSort')
        this.topicSubscribe('SCADA_CHANGE/' + this.appStartTagKey)
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          var TagKey = jsonData.TagKey
          var TagNewValue = jsonData.TagNewValue
          if (TagKey === this.appStartTagKey) {
            this.controlValue = TagNewValue
          } else if (
            topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0
          ) {
            this.handleMessage(jsonData)
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    handleTagWrite(tagKey) {
      // 鼠标按下写入值为1，抬起写入值为0
      this.controlValue = this.controlValue === '1' ? '2' : '1'
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: this.controlValue
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/BzPlc01'
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        }
      })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'pack_task') return
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      }
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      this.contentMessage.content = json.msg
      this.contentMessage.level = 'info'
      if (json.msg_level === 'INFO') {
        this.contentMessage.level = 'info'
      } else if (json.msg_level === 'ERROR') {
        this.contentMessage.level = 'error'
      } else if (json.msg_level === 'WARN') {
        this.contentMessage.level = 'warning'
      }
      this.contentMessage.dialogVisible = true
      if (json.dlg_second > 0) {
        this.timerVisible = setTimeout(() => {
          clearTimeout(this.timerVisible)
          this.contentMessage.dialogVisible = false
        }, json.dlg_second)
      }
    },
    handleClose() {
      this.timerVisible && clearTimeout(this.timerVisible)
      this.contentMessage.dialogVisible = false
    },
    handleCloseJobOrder(data) {
      this.$confirm(
        this.$t('view.dialog.reconfirmedToClose'),
        this.$t('view.dialog.hint'),
        {
          confirmButtonText: this.$t('view.button.confirm'),
          cancelButtonText: this.$t('view.button.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          const requestBody = {
            user_name: Cookies.get('userName'),
            order_num: data.order_num,
            action: 'close'
          }
          planAPI._post(requestBody)
            .then((res) => {
              if (res.code === 0) {
                this.$message.success(this.$t('view.dialog.operationSucceed'))
                // 向BzPlc01/PlcBase/AppStart写入值2
                // this.sendMessage(
                //   'SCADA_WRITE/BzPlc01',
                //   JSON.stringify({
                //     Data: [{ TagKey: this.appStartTagKey, TagValue: '2' }],
                //     ClientName: 'SCADA_WEB'
                //   })
                // )
                this.crud.toQuery()
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch((err) => {
              this.$message({
                message: this.$t('view.dialog.operationFailed') + ': ' + err,
                type: 'error'
              })
            })
          // closePlan({
          //   user_name: Cookies.get('userName'),
          //   plan_id: data.id
          // })
          //   .then((res) => {
          //     if (res.code === 0) {
          //       this.$message({
          //         message: this.$t('view.dialog.operationSucceed'),
          //         type: 'success'
          //       })
          //       // 向BzPlc01/PlcBase/AppStart写入值2
          //       this.sendMessage(
          //         'SCADA_WRITE/BzPlc01',
          //         JSON.stringify({
          //           Data: [{ TagKey: this.appStartTagKey, TagValue: '2' }],
          //           ClientName: 'SCADA_WEB'
          //         })
          //       )
          //     } else {
          //       this.$message({
          //         message:
          //           this.$t('view.dialog.operationFailed') + ': ' + res.msg,
          //         type: 'error'
          //       })
          //     }
          //     this.crud.toQuery()
          //   })
          //   .catch((ex) => {
          //     this.$message({
          //       message: this.$t('view.dialog.operationFailed') + ': ' + ex,
          //       type: 'error'
          //     })
          //   })
        })
        .catch(() => {
          this.$message({ message: this.$t('view.dialog.operationCanceled') })
        })
    },
    handleScan() {
      this.$confirm(
        this.$t('view.dialog.confirmedToSend'),
        this.$t('view.dialog.hint'),
        {
          confirmButtonText: this.$t('view.button.confirm'),
          cancelButtonText: this.$t('view.button.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.sendMessage(
            'SCADA_WRITE/AIS',
            JSON.stringify({
              Data: [
                { TagKey: 'AIS/Plan/OrderNum', TagValue: this.currentBarcode }
              ],
              ClientName: 'SCADA_WEB'
            })
          )
          this.$message({
            message: this.$t('view.dialog.operationSucceed'),
            type: 'success'
          })
          // getPackProductInfoByLot(this.currentBarcode).then(res => {
          //   if (res.code === 0) {
          //     this.$message({ message: this.$t('view.dialog.operationSucceed'), type: 'success' })
          //   } else {
          //     this.$message({ message: this.$t('view.dialog.operationFailed') + ': ' + res.msg, type: 'error' })
          //   }
          //   this.crud.toQuery()
          // }).catch(ex => {
          //   this.$message({ message: ex, type: 'error' })
          // })
        })
        .catch(() => {
          this.$message({ message: this.$t('view.dialog.operationCanceled') })
        })
    },
    viewsDetails(row) {
      this.plan_id = row.id
      this.selectedData = row
      this.tableDialogVisible = true
      this.$nextTick(() => {
        this.$refs.tableDialog.dialogVisible = true
      })
    },
    viewData(row, dataKey, title) {
      this.jsonTitle = title
      this.jsonStr = JSON.stringify(row[dataKey], null, 4)
      this.jsonErrorMsg = ''
      this.jsonDialogVisible = true
    }
    // doPrint(row) {
    //   this.$confirm('确认打印', this.$t('view.dialog.hint'), {
    //     confirmButtonText: this.$t('view.button.confirm'),
    //     cancelButtonText: this.$t('view.button.cancel'),
    //     type: 'warning'
    //   })
    //     .then(() => {
    //       requestPrint({ plan_id: row.id })
    //         .then(res => {
    //           if (res.code === 0) {
    //             this.$message({ message: this.$t('view.dialog.operationSucceed'), type: 'success' })
    //           } else {
    //             this.$message({ message: this.$t('view.dialog.operationFailed') + ': ' + res.msg, type: 'error' })
    //           }
    //           this.crud.toQuery()
    //         })
    //         .catch(ex => {
    //           this.$message({ message: ex, type: 'error' })
    //         })
    //     })
    //     .catch(() => {
    //       this.$message({ message: this.$t('view.dialog.operationCanceled') })
    //     })
    // }
  }
}
</script>
<style scope lang="less">
.el-pagination {
  text-align: right;
  float: none !important;
}

.statusBtn {
  width: 70px !important;
}

.orderInfo {
  margin-left: 35px;
  font-size: 13px;
  display: flex;
  align-items: center;

  div {
    width: 960px;
    height: 30px;
    // background-color: #FFFF00;
    // border: 1px solid #cdcccc;
    // margin: 0 5px;
  }
}

#loadMonitor .message {
  color: #000;
  font-weight: 600;
  padding: 5px 10px 5px 10px;
  border-radius: 10px;
}

#loadMonitor .message-info {
  background-color: #7aa1ef;
}

#loadMonitor .message-warning {
  background-color: #fbb85a;
}

#loadMonitor .message-error {
  background-color: #f56c6c;
}

#loadMonitor {
  .playBtn {
    font-size: 24px;
    border: none;
    width: 150px;
    height: 45px;
    margin-top: -10px;
  }

  .playStart {
    background-color: #0cd80c;
  }

  .playEnd {
    background-color: #ff0000;
  }
}
</style>
