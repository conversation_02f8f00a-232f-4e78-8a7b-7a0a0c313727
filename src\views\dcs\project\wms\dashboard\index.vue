<template>
  <el-container id="bigScreen" :style="'background-image:url(' +mainBackground +')'">
    <el-header :style="'background-image:url(' + headerBackground +');background-size:100% 100%;width:100%;height:80px'">
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7" style="display: flex;align-items: center;">
          <img
            :src="headerLogo"
            style="width: 500px; height: 70px; float: left; ;margin-top: 10px;margin-left: -20px;"
          >
          <!-- <span class="logoTitle">中冶(上海)钢结构科技有限公司</span> -->
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 5px">
          <span class="title">中冶钢构库区智能调度可视化看板</span>
        </el-col>
        <el-col :span="7" style="text-align: right; padding-top: 20px; padding-right: 10px"><span class="time"> {{ DateTime[0] + ' ' + DateTime[1] }} </span><span class="time time1"> {{ DateTime[2] }} </span></el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20">
        <el-col :span="18" style="width: 77.1666666667%;">
          <MainChart
            ref="chart"
            v-loading="loading"
            :nodes="nodes"
            :connections="connections"
            :width="'1470'"
            :height="'570'"
            :readonly="false"
            element-loading-text="拼命绘制流程图中"
          />
          <div class="tb tb_left">
            <table class="table1">
              <tr class="tb_title"><td>报警时间</td><td>报警代码</td><td>报警设备</td><td>报警内容</td></tr>
              <tr v-for="(item,index) in alarmData" :key="item.alarm_tagKey" class="tb_content">
                <td>{{ item.create_date }}</td><td>{{ item.alarm_code }}</td><td> {{ item.alarm_content }}</td><td>{{ item.alarm_desc }}</td>
              </tr>
            </table>
          </div>
        </el-col>
        <el-col :span="6" style="width: 22.8333333333%;">
          <el-card class="cardTextBox taskInfo">
            <div slot="header" class="wrapTextSelect">
              <span>当前任务信息</span>
            </div>
            <div v-for="(item,index) in configData.taskPoint" :key="index" class="card_info">
              <img :src="require(`@/assets/images/dcs/${item.icon}.png`)" alt="">
              <span>{{ item.tags.tag_des + ":" }}</span>
              <span class="font">{{ item.tags.tag_value }}</span>
            </div>
          </el-card>
          <el-card class="cardTextBox domain" style="margin-top: 10px;">
            <div slot="header" class="wrapTextSelect">
              <span>功能按钮区域</span>
            </div>
            <div class="btn-crown-block">
              <span v-for="(item,index) in taskButton" :key="item.id + 'process'" :class="{'lu-btn-3d-screen':clientHeight > 1000,'lu-btn-3d':clientHeight < 1000}" :style="{'margin-left':index % 2 === 0 ? '' : '15px'}" @click="handleOpen(item)">{{ item.label }}</span>
              <span
                v-for="(item,index) in configData.writePoint"
                :key="index"
                :style="{'margin-left':index % 2 !== 0 ? '' : '15px'}"
                :class="{'lu-btn-3d-active': item.readReserve ? configData.reservePonit[0].tags.tag_value === '1' : item.tags.tag_value === '1','lu-btn-3d-screen':clientHeight > 1000,'lu-btn-3d':clientHeight < 1000}"
                @click="handleTagWrite(item)"
              >{{ item.tags.tag_des }}</span>
              <span :class="{'lu-btn-3d-screen':clientHeight > 1000,'lu-btn-3d':clientHeight < 1000}" style="margin-left: 15px;" @click="viewDetails()">行车实时监控</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :append-to-body="true" :modal-append-to-body="false" width="90%" :before-close="handleClose">
      <interfLog v-if="jkrzcx" ref="jkrzcx" />
      <wmsMapMeStock v-if="wmskccx" ref="wmskccx" />
      <wmsFjTask v-if="fjrkrw" ref="fjrkrw" />
      <wmsCarTask v-if="rkrwcx" ref="rkrwcx" />
      <wmsAlarmHistory v-if="sbbjcx" ref="sbbjcx" />
    </el-dialog>
  </el-container>
</template>
<script>
import Paho from 'paho-mqtt'
import Cookies from 'js-cookie'
import wmsFjTask from '../wmsFjTask'
import wmsCarTask from '../wmsCarTask'
import wmsMapMeStock from '../wmsMapMeStock'
import wmsAlarmHistory from '../wmsAlarmHistory'
import interfLog from '../../../../core/center/interf/interfLog.vue'
import { getFormatDate } from '@/utils/index.js'
import { selCellIP } from '@/api/core/center/cell'
import { saveScadaEvent, sel as alarmSel, add as alarmAdd, edit as alarmEdit } from '@/api/dcs/project/wms/wmsDashboard'
import { CoreScadaReadTag } from '@/api/hmi/mainIndex'
import MainChart from './wmsDashboard/index'
import headerBackground from '@/assets/images/dcs/wms_header.png'
import mainBackground from '@/assets/images/dcs/wms_backGround.png'
import headerLogo from '@/assets/images/dcs/wms_logo.png'
import configData from '@/api/dcs/project/wms/index.json'
import crownBlock from '@/api/dcs/project/wms/crownBlock.json'
import { sel as stockGroupSel } from '@/api/dcs/project/wms/wmsMapStockGroup'
import { sel as stockSel, maxLocaltionXSel } from '@/api/dcs/project/wms/wmsMapMeStock'
export default {
  name: 'INDEX_DASHBOARD',
  components: { MainChart, interfLog, wmsFjTask, wmsMapMeStock, wmsAlarmHistory, wmsCarTask },
  data() {
    return {
      headerBackground,
      mainBackground,
      headerLogo,
      timer: null,
      timerGroup: null,
      alarmTimer: null,
      configData,
      nodes: crownBlock,
      loading: false,
      connections: [],
      DateTime: [],
      tagKeysMap: {},
      scadaClient: {},
      clients: {},
      triggerMonitors: {},
      alarmData: [],
      taskButton: [
        { label: '分拣入库任务', id: 1, type: 'fjrkrw', height: 500 },
        { label: '中控出库任务', id: 2, type: 'rkrwcx', height: 530 },
        { label: 'WMS库存查询', id: 3, type: 'wmskccx', height: 500 },
        { label: '接口日志查询', id: 4, type: 'jkrzcx', height: 530 },
        { label: '设备报警查询', id: 5, type: 'sbbjcx', height: 530 }
      ],
      clientHeight: document.documentElement.clientHeight,
      dialogTitle: '',
      dialogVisible: false,
      jkrzcx: false,
      fjrkrw: false,
      wmskccx: false,
      rkrwcx: false,
      sbbjcx: false,
      x: '',
      y: '',
      px: 0.2325, // 1mm = 0.2325px
      carVrituWorkRegionWidth: 1230, // 天车网页活动范围X长度:1450是实际活动范围
      carVrituWorkRegionHeight: 530, // 天车网页活动范围Y长度，高度570，减去上下边距20 530
      carAuctalWorkRegionWidth: 0, // 天车的x的活动范围 b_dcs_wms_fmod_stock表中最大的location_x的值 - A区的start_position_x起始位置
      carAuctalWorkRegionHeight: 0,
      big_Min_Start_X: 0, // 大车起始位置
      big_Max_End_X: 0, // 大车终点位置
      small_Min_Start_Y: 0, // 小车起始位置
      small_Max_End_Y: 0 // 小车重点位置
    }
  },
  created() {
    this.init()
    this.DateTime = getFormatDate().split(' ')
    this.timer = setInterval(() => {
      this.DateTime = getFormatDate().split(' ')
    }, 1000)
    this.alarmHistory()
    this.alarmTimer = setInterval(() => {
      this.alarmHistory()
    }, 5000)
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
    this.timerGroup && clearInterval(this.timerGroup)
    this.alarmTimer && clearInterval(this.alarmTimer)
    for (const key in this.clients) {
      this.clients[key] && this.clients[key].disconnect()
    }
  },
  mounted() {
    var that = this
    window.onresize = function temp() {
      that.clientHeight = document.documentElement.clientHeight
    }
  },
  methods: {
    init() {
      setTimeout(() => {
        this.getCellIp()
      }, 500)
      this.getStockGroupData() // 获取库区位置
      this.timerGroup = setInterval(() => {
        this.getStockGroupData()
      }, 1000 * 60 * 3)
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.getScadaPointdata(ipInfo)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    getStockGroupData() {
      const query = {
        page: 1,
        size: 10,
        sort: 'stock_group_id desc',
        userName: Cookies.get('userName')
      }
      stockGroupSel(query).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          // 获取大车最小的起始位置
          this.big_Min_Start_X = Math.min(...res.data.map(item => item.start_position_x))
          // 获取小车的起始位置
          this.small_Min_Start_Y = Math.min(...res.data.map(item => item.end_position_y))
          // 获取小车的终点位置
          this.small_Max_End_Y = Math.max(...res.data.map(item => item.start_position_y))
          this.carAuctalWorkRegionHeight = this.small_Max_End_Y - this.small_Min_Start_Y // 小车的实际范围
          // 获取天车的实际范围
          this.getCarRaneData(res.data)
          // 获取库位
          this.getStockData()
        }
      })
    },
    getCarRaneData(data) {
      maxLocaltionXSel({}).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.big_Max_End_X = Math.max(...res.data.map(item => item.location_x))
          this.carAuctalWorkRegionWidth = this.big_Max_End_X - this.big_Min_Start_X // 大车的实际范围
          data.map(item => {
            this.nodes.map(temp => {
              if (item.stock_group_code === temp.type) {
                temp.height = updateWidth(item.start_position_y - item.end_position_y) + temp.heightBlack // 实际库位的高度不用加
                temp.width = updateHeight(item.end_position_x - item.start_position_x) + temp.widthBlack // 实际库位的宽度不用加
                temp.x = updateX(item.start_position_x) + temp.xBlack// 实际库位的x坐标不用加
                temp.y = updateY(item.end_position_y) + temp.yBlack // 实际库位的y坐标不用加
              }
            })
          })
        }
      })
      const updateWidth = (height) => {
        const height_ = Math.floor((parseFloat(height) / this.small_Max_End_Y) * this.carVrituWorkRegionHeight)
        return height_
      }
      const updateHeight = (width) => {
        const width_ = Math.floor((parseFloat(width) / this.carAuctalWorkRegionWidth) * this.carVrituWorkRegionWidth)
        return width_
      }
      const updateX = (x) => {
        if (!x) return
        var car1LocationX = (parseFloat(x) - this.big_Min_Start_X) * this.px
        var rate1 = +((this.carAuctalWorkRegionWidth * this.px) / this.carVrituWorkRegionWidth).toFixed(2)
        var position_x = +(car1LocationX / rate1).toFixed(2)
        return position_x
      }
      const updateY = (y) => {
        if (!y && y !== 0) return
        const car1LocationY = (parseFloat(y) - this.small_Min_Start_Y) * this.px
        const rate2 = +((this.carAuctalWorkRegionHeight * this.px) / this.carVrituWorkRegionHeight).toFixed(2)
        const position_y = +(car1LocationY / rate2).toFixed(2)
        return position_y
      }
    },
    // 获取库位
    getStockData() {
      const query = {
        page: 1,
        size: 100,
        sort: 'stock_id desc',
        userName: Cookies.get('userName')
      }
      stockSel(query).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          const arr = res.data.map(item => {
            return {
              height: updateWidth(item.m_width), // 按照页面高度 495px,初始纵向12个库位，一共是 23100 mm, 每个库位大约1660mm（算上 上下库位的间距11400-8300 = 3100 和库位之间的边距）得出 495 / 12 = 41.25去掉边距，大概每个库位35px高度，那么就得出 35 / 1660 = 0.021  1mm =0.021px的像素
              width: updateHeight(item.m_length), // 按照页面宽度 700px,700px的宽度只能放下两个，去掉左右的边距，那就是350px一个库位的宽度，按照A区库位范围在30000 - 47000 可以得出 350 / 8500 = 0.04
              x: updateX(item.position_x),
              y: updateY(item.position_y),
              type: 'kw',
              borderColor: '#FF0000',
              color: '#083388',
              describe: '库位',
              stock_code: item.stock_code,
              m_length: item.m_length,
              m_width: item.m_width,
              material_code: item.material_code,
              lot_num: item.lot_num,
              m_thickness: item.m_thickness,
              stock_id: item.stock_id
            }
          })
          this.nodes = [...arr, ...crownBlock]
        }
      })
      const updateWidth = (height) => {
        const height_ = Math.floor((parseFloat(height) / this.small_Max_End_Y) * this.carVrituWorkRegionHeight)
        return height_
      }
      const updateHeight = (width) => {
        const width_ = Math.floor((parseFloat(width) / this.carAuctalWorkRegionWidth) * this.carVrituWorkRegionWidth)
        return width_
      }
      const updateX = (x) => {
        if (!x) return
        var car1LocationX = (parseFloat(x) - this.big_Min_Start_X) * this.px
        var rate1 = +((this.carAuctalWorkRegionWidth * this.px) / this.carVrituWorkRegionWidth).toFixed(2)
        var position_x = +(car1LocationX / rate1).toFixed(2)
        return position_x
      }
      const updateY = (y) => {
        if (!y && y !== 0) return
        const car1LocationY = (parseFloat(y) - this.small_Min_Start_Y) * this.px
        const rate2 = +((this.carAuctalWorkRegionHeight * this.px) / this.carVrituWorkRegionHeight).toFixed(2)
        const position_y = +(car1LocationY / rate2).toFixed(2)
        return position_y
      }
    },
    viewDetails() {
      this.$router.replace('/alarmDetails')
    },
    handleClose() {
      this.dialogVisible = false
      this.jkrzcx = false
      this.fjrkrw = false
      this.wmskccx = false
      this.rkrwcx = false
      this.sbbjcx = false
    },
    handleOpen(item) {
      this.dialogTitle = item.label
      this.dialogVisible = true
      this[item.type] = true
      this.$nextTick(() => {
        this.$refs[item.type].height = document.documentElement.clientHeight - item.height
      })
    },
    handleTagWrite(item) {
      const h = this.$createElement
      let value = item.tags.tag_value === '1' ? '0' : '1'
      let content = `确认将【${item.tags.tag_des}】点位值写入${value}?`
      // 这个目的是区分点位写入1后，是否自动写入0
      if (item.scadaType && item.scadaType === '1') {
        value = '1'
        content = `确认将【${item.tags.tag_des}】点位值先写入1后自动写入0?`
      }
      this.$msgbox({
        title: `${item.tags.tag_des}`,
        message: h('p', null, [
          h('span', null, content)
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            setTimeout(() => {
              write(item.tags.tag_key, value)
              instance.confirmButtonLoading = false
              setTimeout(() => {
                done()
              }, 300)
            }, 1000)
          } else {
            done()
          }
        }
      }).then(action => {
        if (item.scadaType && item.scadaType === '1') { // 这个值代表不同的按钮
          setTimeout(() => {
            write(item.tags.tag_key, '0')
            this.$message({ type: 'success', message: `${item.tags.tag_des}复位成功` })
          }, 500)
        }
      })
      const write = (tag_key, tag_value) => {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: tag_key,
          TagValue: tag_value
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/' + tag_key.split('/')[0]
        this.mqttClient.send(topic, sendStr)
        saveScadaEvent({
          event_desc: item.tags.tag_des,
          event_val: tag_value
        }).catch(err => {
          console.error('Save scada event fail:', err)
        })
      }
    },
    getScadaPointdata(ipInfo) {
      Object.keys(this.configData).forEach(temp => {
        this.configData[temp].forEach(item => {
          if (temp === 'alarmPonit') {
            this.triggerMonitors[item.tags.tag_key] = item
          }
          const scadaTopic = item.scadaClient
          const cellPort = 8089
          if (this.scadaClient[scadaTopic] == null) {
            this.scadaClient[scadaTopic] = []
          }
          if (this.tagKeysMap[cellPort] == null) {
            this.tagKeysMap[cellPort] = []
          }
          this.scadaClient[scadaTopic].push({ 'tag_key': item.tags.tag_key, 'client': item.scadaClient })
          this.tagKeysMap[cellPort].push({ 'tag_key': item.tags.tag_key })
        })
      })
      for (const key in this.tagKeysMap) {
        const cellUrl = `http://${ipInfo.ip}:${ipInfo.webapi_port}/`
        CoreScadaReadTag(cellUrl, this.tagKeysMap[key])
          .then(res => {
            if (res.code === 0 && res.data) {
              for (const i in res.data) {
                const item = res.data[i]
                const k = item.tag_key
                const v = item.tag_value
                this.setContent(k, v)
              }
            }
          })
      }
      for (const key in this.scadaClient) {
        const port = parseInt(ipInfo.mqtt_port)
        this.connectMQTT(ipInfo.ip, port, (c) => {
          this.clients[key] = c
          // 发布订阅
          this.scadaClient[key].forEach(item => {
            c.subscribe(`${item.client}/${item.tag_key}`, {
              onSuccess: () => {
                console.log('订阅成功：', item.client + '/' + item.tag_key)
              },
              onFailure: (responseObject) => {
                console.log('订阅失败：', item.client + '/' + item.tag_key, responseObject.errorMessage)
              }
            })
          })
        })
      }
    },
    alarmHistory() {
      alarmSel({ enable_flag: 'Y', page: 1, size: 50 }).then(res => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.alarmData = res.data
            return
          }
          this.alarmData = []
        }
      }).catch(error => {
        this.alarmData = []
        console.warn('sel is faild:' + error.message)
      })
    },
    setContent(k, v) {
      if (v === undefined || v === null) return
      Object.keys(this.configData).forEach(temp => {
        this.configData[temp].forEach(item => {
          if (item.tags.tag_key === k) {
            item.tags.tag_value = v
          }
          if (k === 'SlCarPlc01/PlcStatus/Tag023') {
            this.x = v
            this.updateCarLocation()
          }
          if (k === 'SlCarPlc01/PlcStatus/Tag022') {
            this.y = v
            this.updateCarLocation()
          }
        })
      })
      if (this.triggerMonitors[k]) {
        const client = this.triggerMonitors[k]
        const alarm_desc = client.tags.tag_des
        const alarm_code = client.alarmCode
        const alarm_tagKey = client.tags.tag_key
        if (v === client.alarmValue) {
          alarmAdd({
            alarm_code,
            alarm_desc,
            alarm_tagKey
          }).catch(error => {
            console.warn('add is faild：' + error.message)
          })
        }
        if (v === client.alarmRestoreValue) {
          alarmEdit({
            alarm_code,
            alarm_desc,
            alarm_tagKey
          }).catch(error => {
            console.warn('update is faild：' + error.message)
          })
        }
      }
    },
    updateCarLocation() {
      if (this.x === '' || this.y === '') {
        return
      }
      var car1LocationX = (parseFloat(this.x) - this.big_Min_Start_X) * this.px
      var car1LocationY = (parseFloat(this.y) - this.small_Min_Start_Y) * this.px
      var rate1 = +(
        (this.carAuctalWorkRegionWidth * this.px) /
        this.carVrituWorkRegionWidth
      ).toFixed(2)
      var rate2 = +(
        (this.carAuctalWorkRegionHeight * this.px) /
        this.carVrituWorkRegionHeight
      ).toFixed(2)
      var x = +(car1LocationX / rate1).toFixed(2)
      var y = +(car1LocationY / rate2).toFixed(2)
      const bigCarInfo = this.nodes.filter((item) => item.type === 'bigCar')[0]
      const smallCarInfo = this.nodes.filter((item) => item.type === 'smallCar')[0]
      if (x < 0) {
        bigCarInfo.x = 0
        smallCarInfo.x = 0
      } else if (x > 1230) {
        bigCarInfo.x = 1230
        smallCarInfo.x = 1230
      } else {
        bigCarInfo.x = x
        smallCarInfo.x = x
      }
      if (y > 530) {
        smallCarInfo.y = 530
      } else if (y < 0) {
        smallCarInfo.y = 0
      } else {
        smallCarInfo.y = y
      }
    },
    connectMQTT(host, port, onConnected) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      this.mqttClient = mqttClient
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          this.setContent(data.TagKey, data.TagNewValue)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }
}
</script>
<style lang="less" scoped>
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('../../../../../assets/fonts/YouSheBiaoTiHei.ttf');
}
@font-face {
  font-family: light;
  src: url('../../../../../assets/fonts/LCD2B___.TTF');
}
#bigScreen{
    background-size:100% 100%;width:100%;height:100%;
    .el-header{
        .logoTitle{
          font-family: YouSheBiaoTiHei;
            color: #fff;
            font-size: 30px;
            font-weight: 600;
        }
        .title{
            font-size: 32px;
            font-weight: 600;
            letter-spacing: 5px;
            background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
            -webkit-background-clip: text;
            color: transparent;
            // color: #fff;
            // font-family: YouSheBiaoTiHei;
        }
        .time{
            color: #fff;
            font-size: 30px;
            font-family: light;
        }
        .time1{
            color: #fff;
            font-size: 36px;
            font-family: YouSheBiaoTiHei;
        }
    }
    .el-main{
        padding: 10px;
        // .taskInfo{
        //   height: calc(100vh - 500px) !important;
        // }
        .domain{
          height: calc(100vh - 520px) !important;
        }
        .cardTextBox{
            color:#fff;
            border:1px solid #0a348d;
            background: linear-gradient(to left, #00ffff, #00ffff)  left top no-repeat, linear-gradient(to bottom, #00ffff, #00ffff) left top no-repeat, linear-gradient(to left, #00ffff, #00ffff) right top no-repeat, linear-gradient(to bottom, #00ffff, #00ffff) right top no-repeat, linear-gradient(to left, #00ffff, #00ffff) left bottom no-repeat, linear-gradient(to bottom, #00ffff, #00ffff) left bottom no-repeat, linear-gradient(to left, #00ffff, #00ffff) right bottom no-repeat, linear-gradient(to left, #00ffff, #00ffff) right bottom no-repeat;
            background-size: 2px 12px, 12px 2px, 2px 12px, 12px 2px;
            box-shadow: #7b85c6 0px 0px 10px inset;
            background-color:transparent;
            .wrapTextSelect{
                span{font-size: 24px;font-weight: 600;}
            }
            ::v-deep .el-card__header{
                padding: 10px;
                border-bottom: 1px solid #0a348d;
            }
            ::v-deep .el-card__body{
                padding: 10px !important;
                .card_info{
                    // background: linear-gradient(to bottom, #0b4c8c 0%,#052444 50%, #0B0F2A 100%);
                    background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
  -webkit-background-clip: text;
  color: transparent;
                    font-size: 26px;
                    margin: 5px 0;
                    font-family: YouSheBiaoTiHei;
                    img{
                        width: 25px;
                        height: 25px;
                    }
                    .font{
                      font-family: light;
                    }
                }
            }
            .btn-crown-block {
                display: flex;
                overflow-x: hidden;
                flex-wrap: wrap;
                .lu-btn-3d {
                    display: block;
                    width: 180px;
                    height: 42px;
                    line-height: 42px;
                    background: linear-gradient(to bottom, #0b4c8c 0%,#052444 50%, #0B0F2A 100%);
                    border: none;
                    border-radius: 5px;
                    color: #d6dce5;
                    font-weight: 600;
                    font-family: YouSheBiaoTiHei;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
                    font-size: 24px;
                    text-align: center;
                    box-shadow: 0px 3px 0px rgba(0, 0, 0, 0.2);
                    cursor: pointer;
                    margin: 5px 0;
                    letter-spacing: 2px;
                }
                .lu-btn-3d-active {
                  color: #fff;
                  background: linear-gradient(to bottom, #13e913 0%, #13e913 100%);
                }
                .lu-btn-3d-screen{
                  margin-left: 0 !important;
                  display: block;
                    width: 100%;
                    height: 42px;
                    line-height: 42px;
                    background: linear-gradient(to bottom, #0b4c8c 0%,#052444 50%, #0B0F2A 100%);
                    border: none;
                    border-radius: 5px;
                    color: #d6dce5;
                    font-weight: 600;
                    font-family: YouSheBiaoTiHei;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
                    font-size: 24px;
                    text-align: center;
                    box-shadow: 0px 3px 0px rgba(0, 0, 0, 0.2);
                    cursor: pointer;
                    margin: 3px 0;
                    letter-spacing: 2px;
                }
            }
        }
        .tb{
          width: 100%;
          height: 225px;
          overflow: auto;
          display: flex;
          justify-content: center;
        /*  background:#0c0f2b url(../img/tb_bg.png) left center no-repeat;*/
          background: linear-gradient(to left, #00ffff, #00ffff) left top no-repeat,
                      linear-gradient(to bottom, #00ffff, #00ffff) left top no-repeat,
                      linear-gradient(to left, #00ffff, #00ffff) right top no-repeat,
                      linear-gradient(to bottom, #00ffff, #00ffff) right top no-repeat,
                      linear-gradient(to left, #00ffff, #00ffff) left bottom no-repeat,
                      linear-gradient(to bottom, #00ffff, #00ffff) left bottom no-repeat,
                      linear-gradient(to left, #00ffff, #00ffff) right bottom no-repeat,
                      linear-gradient(to left, #00ffff, #00ffff) right bottom no-repeat;
          background-size: 2px 10px, 10px 2px, 2px 10px, 10px 2px;
          background-color: #0B0F2A;
          margin-top: 12px;
          table{
            width: 98%;
            color: #fff;
            font-weight: 600;
            text-align: center;
            border: none;
            border-spacing: 0;
            height: 35px;
            tr:nth-child(odd) {
              background: #1f223c;
            }
              .tb_title{
                position: sticky;
                top: 0;
                background: #1f223c;
                color: #fff;
                font-weight: 600;
                height: 35px;
                line-height: 35px;
                font-size: 16px;
              }
              .tb_content{
                  height: 35px;
                  line-height: 35px;
                  font-size: 16px;
              }
              tr:hover{
                color: #dfbb83;
                background: linear-gradient(to right, #3c3c3c,#424040, #453c3e,#424040,#3c3c3c);
              }
            }
      }
      .tb ::-webkit-scrollbar {
          width: 0 !important;
        }
      .tb::-webkit-scrollbar {
          width: 0 !important;height: 0;
        }
    }
}
::v-deep .el-dialog{
  margin-top: 5vh !important;
}
@media screen and (max-height: 950px) {
  .el-main{
    .tb {
      height: 225px !important;
    }
  }
  // .btn-crown-block {
  //       display: flex;
  //       overflow-x: hidden;
  //       flex-wrap: wrap;
  //       .lu-btn-3d {
  //           display: block;
  //           width: 180px;
  //           height: 42px;
  //           line-height: 42px;
  //           background: linear-gradient(to bottom, #0b4c8c 0%,#052444 50%, #0B0F2A 100%);
  //           border: none;
  //           border-radius: 5px;
  //           color: #d6dce5;
  //           font-weight: 600;
  //           font-family: YouSheBiaoTiHei;
  //           text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
  //           font-size: 24px;
  //           text-align: center;
  //           box-shadow: 0px 3px 0px rgba(0, 0, 0, 0.2);
  //           cursor: pointer;
  //           margin: 5px 0;
  //           letter-spacing: 2px;
  //       }
  //       .lu-btn-3d-active {
  //         color: #fff;
  //         background: linear-gradient(to bottom, #13e913 0%, #13e913 100%);
  //       }
  //   }
}
@media screen and (min-height: 1000px) {
  .el-main{
    .tb {
      height: 395px !important;
    }
  }
  // .btn-crown-block {
  //       .lu-btn-3d {
  //           display: block;
  //           width: 100%;
  //           height: 42px;
  //           line-height: 42px;
  //           background: linear-gradient(to bottom, #0b4c8c 0%,#052444 50%, #0B0F2A 100%);
  //           border: none;
  //           border-radius: 5px;
  //           color: #d6dce5;
  //           font-weight: 600;
  //           font-family: YouSheBiaoTiHei;
  //           text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
  //           font-size: 24px;
  //           text-align: center;
  //           box-shadow: 0px 3px 0px rgba(0, 0, 0, 0.2);
  //           cursor: pointer;
  //           margin: 3px 0;
  //           letter-spacing: 2px;
  //       }
  //       .lu-btn-3d-active {
  //         color: #fff;
  //         background: linear-gradient(to bottom, #13e913 0%, #13e913 100%);
  //       }
  //   }
}
</style>
