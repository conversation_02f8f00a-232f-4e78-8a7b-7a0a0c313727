<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          工单号
        </template>
        <el-input ref="webLotNum" v-model="webLotNum" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          生产端口
        </template>
        <el-radio v-for="(item,index) in protDataList" :key="index" v-model="webLotPortCode" :label="item.port_index" style="margin-left:0px;" border>{{ item.port_des }}</el-radio>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { selQcBeatTime } from '@/api/mes/project/mesQcMaterialWarehouseBoard'
import Cookies from 'js-cookie'
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      portList: [{ port_index: '1', port_des: '端口1' }, { port_index: '2', port_des: '端口2' }, { port_index: '3', port_des: '端口3' }, { port_index: '4', port_des: '端口4' }],
      protDataList:[],
      webLotNum: '',
      webLotPortCode: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.initQcBeatTime()
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webLotNum.focus()
    })
  },
  methods: {
    handleSendInfo() {
      console.log(this.tag_key_list)
      if (this.webLotNum === '') {
        this.$message({ message: '请输入工单号', type: 'info' })
        return
      }
      if (this.webLotPortCode === '') {
        this.$message({ message: '请选择生产端口', type: 'info' })
        return
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebLotNum,
        TagValue: this.webLotNum
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebLotPortCode,
        TagValue: this.webLotPortCode
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebLotRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebLotNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    },
    initQcBeatTime(){
      // 获取系统参数信息
      var queryParameter = {
        userName: Cookies.get('userName'),
        parameter_code: 'prot_index',
        enable_flag: 'Y'
      }
      selQcBeatTime(queryParameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            var indexArray = defaultQuery.data[0].parameter_val.split(',')
            this.protDataList = this.portList.filter(item => indexArray.includes(item.port_index));
          }
        }).catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
