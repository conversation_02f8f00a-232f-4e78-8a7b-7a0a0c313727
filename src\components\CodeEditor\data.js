const data = [
  { displayText: 'abstract', displayInfo: '', text: 'abstract' },
  { displayText: 'as', displayInfo: '', text: 'as' },
  { displayText: 'async', displayInfo: '', text: 'async' },
  { displayText: 'await', displayInfo: '', text: 'await' },
  { displayText: 'base', displayInfo: '', text: 'base' },
  { displayText: 'break', displayInfo: '', text: 'break' },
  { displayText: 'case', displayInfo: '', text: 'case' },
  { displayText: 'catch', displayInfo: '', text: 'catch' },
  { displayText: 'checked', displayInfo: '', text: 'checked' },
  { displayText: 'class', displayInfo: '', text: 'class' },
  { displayText: 'const', displayInfo: '', text: 'const' },
  { displayText: 'continue', displayInfo: '', text: 'continue' },
  { displayText: 'default', displayInfo: '', text: 'default' },
  { displayText: 'delegate', displayInfo: '', text: 'delegate' },
  { displayText: 'do', displayInfo: '', text: 'do' },
  { displayText: 'else', displayInfo: '', text: 'else' },
  { displayText: 'enum', displayInfo: '', text: 'enum' },
  { displayText: 'event', displayInfo: '', text: 'event' },
  { displayText: 'explicit', displayInfo: '', text: 'explicit' },
  { displayText: 'extern', displayInfo: '', text: 'extern' },
  { displayText: 'finally', displayInfo: '', text: 'finally' },
  { displayText: 'fixed', displayInfo: '', text: 'fixed' },
  { displayText: 'for', displayInfo: '', text: 'for' },
  { displayText: 'foreach', displayInfo: '', text: 'foreach' },
  { displayText: 'goto', displayInfo: '', text: 'goto' },
  { displayText: 'if', displayInfo: '', text: 'if' },
  { displayText: 'implicit', displayInfo: '', text: 'implicit' },
  { displayText: 'in', displayInfo: '', text: 'in' },
  { displayText: 'init', displayInfo: '', text: 'init' },
  { displayText: 'interface', displayInfo: '', text: 'interface' },
  { displayText: 'internal', displayInfo: '', text: 'internal' },
  { displayText: 'is', displayInfo: '', text: 'is' },
  { displayText: 'lock', displayInfo: '', text: 'lock' },
  { displayText: 'namespace', displayInfo: '', text: 'namespace' },
  { displayText: 'new', displayInfo: '', text: 'new' },
  { displayText: 'operator', displayInfo: '', text: 'operator' },
  { displayText: 'out', displayInfo: '', text: 'out' },
  { displayText: 'override', displayInfo: '', text: 'override' },
  { displayText: 'params', displayInfo: '', text: 'params' },
  { displayText: 'private', displayInfo: '', text: 'private' },
  { displayText: 'protected', displayInfo: '', text: 'protected' },
  { displayText: 'public', displayInfo: '', text: 'public' },
  { displayText: 'readonly', displayInfo: '', text: 'readonly' },
  { displayText: 'record', displayInfo: '', text: 'record' },
  { displayText: 'ref', displayInfo: '', text: 'ref' },
  { displayText: 'required', displayInfo: '', text: 'required' },
  { displayText: 'return', displayInfo: '', text: 'return' },
  { displayText: 'sealed', displayInfo: '', text: 'sealed' },
  { displayText: 'sizeof', displayInfo: '', text: 'sizeof' },
  { displayText: 'stackalloc', displayInfo: '', text: 'stackalloc' },
  { displayText: 'static', displayInfo: '', text: 'static' },
  { displayText: 'struct', displayInfo: '', text: 'struct' },
  { displayText: 'switch', displayInfo: '', text: 'switch' },
  { displayText: 'this', displayInfo: '', text: 'this' },
  { displayText: 'throw', displayInfo: '', text: 'throw' },
  { displayText: 'try', displayInfo: '', text: 'try' },
  { displayText: 'typeof', displayInfo: '', text: 'typeof' },
  { displayText: 'unchecked', displayInfo: '', text: 'unchecked' },
  { displayText: 'unsafe', displayInfo: '', text: 'unsafe' },
  { displayText: 'using', displayInfo: '', text: 'using' },
  { displayText: 'virtual', displayInfo: '', text: 'virtual' },
  { displayText: 'void', displayInfo: '', text: 'void' },
  { displayText: 'volatile', displayInfo: '', text: 'volatile' },
  { displayText: 'while', displayInfo: '', text: 'while' },
  { displayText: 'add', displayInfo: '', text: 'add' },
  { displayText: 'alias', displayInfo: '', text: 'alias' },
  { displayText: 'ascending', displayInfo: '', text: 'ascending' },
  { displayText: 'descending', displayInfo: '', text: 'descending' },
  { displayText: 'dynamic', displayInfo: '', text: 'dynamic' },
  { displayText: 'from', displayInfo: '', text: 'from' },
  { displayText: 'get', displayInfo: '', text: 'get' },
  { displayText: 'global', displayInfo: '', text: 'global' },
  { displayText: 'group', displayInfo: '', text: 'group' },
  { displayText: 'into', displayInfo: '', text: 'into' },
  { displayText: 'join', displayInfo: '', text: 'join' },
  { displayText: 'let', displayInfo: '', text: 'let' },
  { displayText: 'orderby', displayInfo: '', text: 'orderby' },
  { displayText: 'partial', displayInfo: '', text: 'partial' },
  { displayText: 'remove', displayInfo: '', text: 'remove' },
  { displayText: 'select', displayInfo: '', text: 'select' },
  { displayText: 'set', displayInfo: '', text: 'set' },
  { displayText: 'value', displayInfo: '', text: 'value' },
  { displayText: 'var', displayInfo: '', text: 'var' },
  { displayText: 'yield', displayInfo: '', text: 'yield' },
  { displayText: 'Action', displayInfo: '', text: 'Action' },
  { displayText: 'Boolean', displayInfo: '', text: 'Boolean' },
  { displayText: 'Byte', displayInfo: '', text: 'Byte' },
  { displayText: 'Char', displayInfo: '', text: 'Char' },
  { displayText: 'DateTime', displayInfo: '', text: 'DateTime' },
  { displayText: 'DateTimeOffset', displayInfo: '', text: 'DateTimeOffset' },
  { displayText: 'Decimal', displayInfo: '', text: 'Decimal' },
  { displayText: 'Double', displayInfo: '', text: 'Double' },
  { displayText: 'Func', displayInfo: '', text: 'Func' },
  { displayText: 'Guid', displayInfo: '', text: 'Guid' },
  { displayText: 'Int16', displayInfo: '', text: 'Int16' },
  { displayText: 'Int32', displayInfo: '', text: 'Int32' },
  { displayText: 'Int64', displayInfo: '', text: 'Int64' },
  { displayText: 'Object', displayInfo: '', text: 'Object' },
  { displayText: 'SByte', displayInfo: '', text: 'SByte' },
  { displayText: 'Single', displayInfo: '', text: 'Single' },
  { displayText: 'String', displayInfo: '', text: 'String' },
  { displayText: 'Task', displayInfo: '', text: 'Task' },
  { displayText: 'TimeSpan', displayInfo: '', text: 'TimeSpan' },
  { displayText: 'UInt16', displayInfo: '', text: 'UInt16' },
  { displayText: 'UInt32', displayInfo: '', text: 'UInt32' },
  { displayText: 'UInt64', displayInfo: '', text: 'UInt64' },
  { displayText: 'bool', displayInfo: '', text: 'bool' },
  { displayText: 'byte', displayInfo: '', text: 'byte' },
  { displayText: 'char', displayInfo: '', text: 'char' },
  { displayText: 'decimal', displayInfo: '', text: 'decimal' },
  { displayText: 'double', displayInfo: '', text: 'double' },
  { displayText: 'short', displayInfo: '', text: 'short' },
  { displayText: 'int', displayInfo: '', text: 'int' },
  { displayText: 'long', displayInfo: '', text: 'long' },
  { displayText: 'object', displayInfo: '', text: 'object' },
  { displayText: 'sbyte', displayInfo: '', text: 'sbyte' },
  { displayText: 'float', displayInfo: '', text: 'float' },
  { displayText: 'string', displayInfo: '', text: 'string' },
  { displayText: 'ushort', displayInfo: '', text: 'ushort' },
  { displayText: 'uint', displayInfo: '', text: 'uint' },
  { displayText: 'ulong', displayInfo: '', text: 'ulong' },
  { displayText: 'catch', displayInfo: '', text: 'catch' },
  { displayText: 'class', displayInfo: '', text: 'class' },
  { displayText: 'do', displayInfo: '', text: 'do' },
  { displayText: 'else', displayInfo: '', text: 'else' },
  { displayText: 'finally', displayInfo: '', text: 'finally' },
  { displayText: 'for', displayInfo: '', text: 'for' },
  { displayText: 'foreach', displayInfo: '', text: 'foreach' },
  { displayText: 'if', displayInfo: '', text: 'if' },
  { displayText: 'struct', displayInfo: '', text: 'struct' },
  { displayText: 'switch', displayInfo: '', text: 'switch' },
  { displayText: 'try', displayInfo: '', text: 'try' },
  { displayText: 'while', displayInfo: '', text: 'while' },
  { displayText: 'class', displayInfo: '', text: 'class' },
  { displayText: 'interface', displayInfo: '', text: 'interface' },
  { displayText: 'namespace', displayInfo: '', text: 'namespace' },
  { displayText: 'record', displayInfo: '', text: 'record' },
  { displayText: 'struct', displayInfo: '', text: 'struct' },
  { displayText: 'var', displayInfo: '', text: 'var' },
  { displayText: 'true', displayInfo: '', text: 'true' },
  { displayText: 'false', displayInfo: '', text: 'false' },
  { displayText: 'null', displayInfo: '', text: 'null' },
  {
    displayText: 'SendScadaWriteMsg',
    displayInfo: '批量写入点位',
    text: 'List<ScadaWriteFormat> lstWrite = new List<ScadaWriteFormat>();\n' +
                    '【tabSize】ScadaWriteFormat scadaWriteFormat = new ScadaWriteFormat()\n' +
                    '【tabSize】{\n' +
                    '【tabSize】   TagKey = "",\n' +
                    '【tabSize】   TagValue = ""\n' +
                    '【tabSize】};\n' +
                    '【tabSize】lstWrite.Add(scadaWriteFormat);\n' +
                    '【tabSize】if (!rcsMqttClient.SendScadaWriteMsg(clientCode, lstWrite))\n' +
                    '【tabSize】{\n' +
                    '【tabSize】   errorMsg = "写入信息失败";\n' +
                    '【tabSize】   aisStepStatusEnum = AisStepStatusEnum.NG;\n' +
                    '【tabSize】   return;\n' +
                    '【tabSize】}'
  }
]
export function getHintList(start, str, thi) {
  var tabSize = ''
  for (var j = 0; j < start * 2; j++) {
    tabSize = tabSize + ' '
  }
  var found = []
  for (var i = 0; i < data.length; i++) {
    var word = data[i].displayText
    if (word.slice(0, str.length) === str) {
      const item = {
        text: data[i].text.replace(/【tabSize】/g, tabSize),
        displayText: data[i].displayText,
        displayInfo: data[i].displayInfo,
        render: thi.hintRender
      }
      found.push(item)
    }
  }
  return found
}
export default { getHintList }

