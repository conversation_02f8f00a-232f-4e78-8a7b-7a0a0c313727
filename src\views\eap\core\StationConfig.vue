<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="设备类型：">
                <el-select v-model="query.device_type" clearable>
                  <el-option v-for="item in deviceTypeData" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="配置描述：">
                <el-input v-model="query.config_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="130px" :inline="true">
          <el-form-item label="设备类型" prop="device_type">
            <el-select v-model="form.device_type" clearable>
              <el-option v-for="item in deviceTypeData" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="配置描述" prop="config_des">
            <el-input v-model="form.config_des" />
          </el-form-item>
          <el-form-item label="实例编码" prop="client_code">
            <el-input v-model="form.client_code" />
          </el-form-item>
          <el-form-item label="标签组编码" prop="group_code">
            <el-input v-model="form.group_code" />
          </el-form-item>
          <el-form-item label="标签编码" prop="tag_code">
            <el-input v-model="form.tag_code" />
          </el-form-item>
          <el-form-item label="数据类型" prop="data_type">
            <el-select v-model="form.data_type" clearable>
              <el-option v-for="item in dataTypeData" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>

                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.config_id }}</el-descriptions-item>
                  <el-descriptions-item label="设备类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.device_type }}</el-descriptions-item>
                  <el-descriptions-item label="标签组编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.group_code }}</el-descriptions-item>
                  <el-descriptions-item label="标签编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_code }}</el-descriptions-item>
                  <el-descriptions-item label="实例编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.client_code }}</el-descriptions-item>
                  <el-descriptions-item label="配置描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.config_des }}</el-descriptions-item>
                  <el-descriptions-item label="数据类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.data_type }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="device_type" min-width="100" label="设备类型">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ deviceTypeData.find(item=>item.value===scope.row.device_type).label }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="group_code" min-width="100" label="标签组编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_code" min-width="100" label="标签编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="client_code" min-width="100" label="实例编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="config_des" min-width="100" label="配置描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="data_type" min-width="100" label="数据类型" />
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudStationConfig from '@/api/eap/eapFmodConfig'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  config_id: '',
  device_type: 'Load',
  group_code: '',
  tag_code: '',
  client_code: '',
  config_des: '',
  data_type: 'Bool',
  enable_flag: 'Y'
}
export default {
  name: 'EAP_STATION_CONFIG',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '工位配置维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'config_id',
      // 排序
      sort: ['config_id asc'],
      // CRUD Method
      crudMethod: { ...crudStationConfig },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'eap_station_config:add'],
        edit: ['admin', 'eap_station_config:edit'],
        del: ['admin', 'eap_station_config:del'],
        down: ['admin', 'eap_station_config:down']
      },
      rules: {
        config_des: [{ required: true, message: '请输入配置描述', trigger: 'blur' }],
        client_code: [{ required: true, message: '请输入实例编码', trigger: 'blur' }],
        group_code: [{ required: true, message: '请输入标签组编码', trigger: 'blur' }],
        tag_code: [{ required: true, message: '请输入标签编码', trigger: 'blur' }]
      },
      deviceTypeData: [{ value: 'Load', label: '投板机' }, { value: 'UnLoad', label: '收板机' }],
      dataTypeData: ['Bool', 'String', 'Json', 'Interf']
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将配置为【' + data.config_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudStationConfig
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              station_port_id: data.station_port_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
