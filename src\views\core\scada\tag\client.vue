<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-drawer append-to-body :wrapper-closable="false" :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="650px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <!--作用：区分修改、新增-->
          <el-form-item v-if="1 == 0" :label="$t('lang_pack.tagsDefined.productionLineId')" prop="prod_line_id">
            <!-- 产线ID -->
            <el-input v-model="form.prod_line_id" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.tagsDefined.exampleCode')" prop="client_code">
            <!-- 实例编码 -->
            <el-input v-model="form.client_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.tagsDefined.exampleDescription')" prop="client_des">
            <!-- 实例描述 -->
            <el-input v-model="form.client_des" />
          </el-form-item>
          <!--快速编码：CONTROL_DRIVER-->
          <el-form-item :label="$t('lang_pack.tagsDefined.driveProgram')" prop="client_driver">
            <!-- 驱动程序 -->
            <el-select v-model="form.client_driver" clearable filterable style="width:100%" @change="changeClientDriver">
              <el-option v-for="item in dict.CONTROL_DRIVER" :key="item.id" :label="'【'+item.value+'】 '+ item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!--查询表：scada_driver_model-->
          <el-form-item :label="$t('lang_pack.tagsDefined.type')" prop="client_model">
            <!-- 型号 -->
            <el-select v-model="form.client_model" clearable filterable>
              <el-option v-for="item in clientModelData" :key="item.model_name" :label="item.model_name" :value="item.model_name" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.tagsDefined.exampleAttribute')" prop="client_attr">
            <!-- 实例属性 -->
            <el-input v-model="form.client_attr" />
          </el-form-item>
          <!--查询表：sys_fmod_station-->
          <el-form-item :label="$t('lang_pack.tagsDefined.stationCode')" prop="station_code">
            <!-- 工位号 -->
            <el-select v-model="form.station_code" clearable filterable>
              <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_code" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.tagsDefined.simulation')">
            <!-- 是否模拟 -->
            <el-radio-group v-model="form.simulated_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" size="small" :data="tableDataTable" style="width: 100%" :height="height" highlight-current-row @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="client_code" :label="$t('lang_pack.tagsDefined.exampleCode')" sortable="custom" />
        <!-- 实例编码 -->
        <el-table-column  :show-overflow-tooltip="true" prop="client_des" :label="$t('lang_pack.tagsDefined.exampleDescription')" sortable="custom" />
        <!-- 实例描述 -->
        <el-table-column  :label="$t('lang_pack.tagsDefined.driveProgram')" align="center" prop="client_driver" width="100">
          <!-- 驱动程序 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.CONTROL_DRIVER[scope.row.client_driver] }}
          </template>
        </el-table-column>

        <el-table-column  :show-overflow-tooltip="true" prop="client_model" :label="$t('lang_pack.tagsDefined.type')" sortable="custom" />
        <!-- 型号 -->
        <el-table-column  :show-overflow-tooltip="true" prop="client_attr" :label="$t('lang_pack.tagsDefined.exampleAttribute')" sortable="custom" />
        <!-- 实例属性 -->
        <el-table-column  :show-overflow-tooltip="true" prop="station_code" :label="$t('lang_pack.tagsDefined.stationCode')">
          <!-- 工位号 -->
          <template slot-scope="scope">
            {{ getStationDes(scope.row.station_code) }}
          </template>
        </el-table-column>

        <el-table-column  :label="$t('lang_pack.tagsDefined.simulation')" align="center" prop="simulated_flag" sortable="custom">
          <!-- 是否模拟 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.simulated_flag] }}
          </template>
        </el-table-column>
        <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag">
          <!-- 有效标识 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import { lovModel } from '@/api/core/scada/model'
import { selScadaClient, insScadaClient, updScadaClient, delScadaClient } from '@/api/core/scada/client'

export default {
  name: 'CLIENT',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 230,
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      // Table
      listLoadingTable: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],

      form: {
        client_id: '',
        prod_line_id: '',
        client_code: '',
        client_des: '',
        client_driver: '',
        client_model: '',
        client_attr: '',
        station_code: '',
        simulated_flag: 'Y',
        enable_flag: 'Y'
      },
      rules: {
        // 提交验证规则
        client_code: [{ required: true, message: '请输入实例编码', trigger: 'blur' }],
        client_des: [{ required: true, message: '请输入实例描述', trigger: 'blur' }],
        station_code: [{ required: true, message: '请输入工位号', trigger: 'blur' }]
      },
      clientDriverData: [],
      clientModelData: [],
      stationData: [],
      clientCodeDes: '',

      query: {
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'client_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WHETHER_FLAG', 'CONTROL_DRIVER'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 230
    }
  },
  created: function() {
    // 查询
    this.toButQuery()
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 获取工位的中文描述
    getStationDes(station_code) {
      var item = this.stationData.find(item => item.station_code === station_code)
      if (item !== undefined) {
        return item.station_des
      }
      return station_code
    },

    // 查询按钮：
    toButQuery(prodLineId, queryContent, stationData) {
      if (prodLineId === undefined) {
        this.form.prod_line_id = this.$parent.$attrs.prodlineid
        this.stationData = this.$parent.$attrs.stationdata
      } else {
        this.form.prod_line_id = prodLineId
        this.stationData = stationData
      }

      if (queryContent !== undefined) {
        this.clientCodeDes = queryContent
      }
      this.listLoadingTable = true
      const query = {
        user_name: Cookies.get('userName'),
        prod_line_id: this.form.prod_line_id,
        clientCodeDes: this.clientCodeDes,
        enable_flag: '',
        sort: 'client_id',
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      selScadaClient(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 新增按钮：
    toButAdd() {
      this.form.client_id = ''
      this.form.client_code = ''
      this.form.client_des = ''
      this.form.client_driver = ''
      this.form.client_model = ''
      this.form.client_attr = ''
      this.form.station_code = ''
      this.form.simulated_flag = 'Y'
      this.form.enable_flag = 'Y'

      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增实例'
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    // 编辑按钮：
    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.client_id = data.client_id
      this.form.prod_line_id = data.prod_line_id
      this.form.client_code = data.client_code
      this.form.client_des = data.client_des
      this.form.client_driver = data.client_driver
      this.changeClientDriver()
      this.form.client_model = data.client_model
      this.form.client_attr = data.client_attr
      this.form.station_code = data.station_code
      this.form.simulated_flag = data.simulated_flag
      this.form.enable_flag = data.enable_flag
      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改实例'
    },
    // 驱动程序 LOV
    changeClientDriver() {
      this.form.client_model = ''
      this.clientModelData = []
      const query = {
        user_name: Cookies.get('userName'),
        client_driver: this.form.client_driver
      }
      // 从后台获取到对象数组
      lovModel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.clientModelData = defaultQuery.data
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    // 内按钮：确定
    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            client_id: this.form.client_id,
            prod_line_id: this.form.prod_line_id,
            client_code: this.form.client_code,
            client_des: this.form.client_des,
            client_driver: this.form.client_driver,
            client_model: this.form.client_model,
            client_attr: this.form.client_attr,
            station_code: this.form.station_code,
            simulated_flag: this.form.simulated_flag,
            enable_flag: this.form.enable_flag
          }
          const that = this
          // 新增
          if (this.form.client_id === undefined || this.form.client_id.length <= 0) {
            insScadaClient(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  that.$emit('AddTreeNode', defaultQuery.result, this.form.client_code, this.form.client_des)
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updScadaClient(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  that.$emit('EditTreeNode', this.form.client_id, this.form.client_code, this.form.client_des)
                  // 查询
                  this.toButQuery()
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除实例${data.client_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            client_id: data.client_id
          }
          const that = this
          delScadaClient(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                that.$emit('DelTreeNode', data.client_id)
                // 查询
                this.toButQuery()
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'client_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
</style>
