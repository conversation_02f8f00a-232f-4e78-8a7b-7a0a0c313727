<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.screenSetType')">
                <!-- 大屏类型： -->
                <el-select v-model="query.screen_set_type" clearable filterable>
                  <el-option v-for="item in dict.SCREEN_SET_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.enableFlag')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
              <el-form-item :label="$t('lang_pack.screenset.screenSetType')">
                <!-- 大屏类型 -->
                <el-select v-model="form.screen_set_type" clearable filterable>
                  <el-option v-for="item in dict.SCREEN_SET_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.screenset.screenSetCode')" prop="screen_set_code">
                <!-- 大屏编码 -->
                <el-input v-model="form.screen_set_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screenset.screenSetDes')" prop="screen_set_des">
                <!-- 大屏描述 -->
                <el-input v-model="form.screen_set_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screenset.screenSetUrl')" prop="screen_set_url">
                <!-- 页面路由 -->
                <el-input v-model="form.screen_set_url" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screenset.screenSetUrlDes')" prop="screen_set_url_des">
                <!-- 页面路由描述 -->
                <el-input v-model="form.screen_set_url_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screenset.orderNum')" prop="order_num">
                <!-- 顺序号 -->
                <el-input v-model="form.order_num" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screenset.switchTime')" prop="switch_time">
                <!-- 切换时长 -->
                <el-input v-model="form.switch_time" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.screenset.enableFlag')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" :label="$t('lang_pack.screenset.creationDate')" />
            <!-- 创建时间 -->

            <el-table-column  :label="$t('lang_pack.screenset.screenSetType')" align="center" prop="screen_set_type">
              <!-- 大屏类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.SCREEN_SET_TYPE[scope.row.screen_set_type] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="screen_set_code" :label="$t('lang_pack.screenset.screenSetCode')" />
            <!-- 大屏编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="screen_set_des" :label="$t('lang_pack.screenset.screenSetDes')" />
            <!-- 大屏描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="screen_set_url" :label="$t('lang_pack.screenset.screenSetUrl')" />
            <!-- 页面路由 -->
            <el-table-column  :show-overflow-tooltip="true" prop="screen_set_url_des" :label="$t('lang_pack.screenset.screenSetUrlDes')" />
            <!-- 页面路由描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="order_num" :label="$t('lang_pack.screenset.orderNum')" />
            <!-- 顺序号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="switch_time" :label="$t('lang_pack.screenset.switchTime')" />
            <!-- 切换时长 -->

            <el-table-column  :label="$t('lang_pack.screenset.enableFlag')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudScreenSet from '@/api/pmc/factory/screenset'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  screen_set_id: '',
  screen_set_type: '',
  screen_set_code: '',
  screen_set_des: '',
  screen_set_url: '',
  screen_set_url_des: '',
  order_num: '0',
  switch_time: '0',
  enable_flag: 'Y'
}
export default {
  name: 'SCREENSET',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '大屏基础',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'screen_set_id',
      // 排序
      sort: ['screen_set_id asc'],
      // CRUD Method
      crudMethod: { ...crudScreenSet },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'fmod_screen_set:add'],
        edit: ['admin', 'fmod_screen_set:edit'],
        del: ['admin', 'fmod_screen_set:del']
      },
      rules: {
        screen_set_type: [{ required: true, message: '请选择大屏类型', trigger: 'blur' }]
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'SCREEN_SET_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {}
}
</script>
