<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务号:">
                                <!-- 任务号 -->
                                <el-input v-model="query.task_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="零件条码:">
                                <!-- 零件条码 -->
                                <el-input v-model="query.part_barcode" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="零件编码:">
                                <!-- 零件编码 -->
                                <el-input v-model="query.part_code" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="零件类型:">
                                <!-- 零件类型 -->
                                <el-select v-model="query.part_type" clearable filterable>
                                    <el-option v-for="item in dict.PART_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="码垛开始时间:">
                                <!-- 码垛开始时间 -->
                                <el-date-picker
                                    v-model="query.fj_start_time"
                                    type="datetimerange"
                                    size="small"
                                    align="right"
                                    unlink-panels
                                    range-separator="~"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                    :picker-options="pickerOptions"
                                />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="码垛结束时间:">
                                <!-- 码垛结束时间 -->
                                <el-date-picker
                                    v-model="query.fj_end_time"
                                    type="datetimerange"
                                    size="small"
                                    align="right"
                                    unlink-panels
                                    range-separator="~"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                    :picker-options="pickerOptions"
                                />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.materialFramePosition')" prop="box_location">
                                <!-- 料框位置 -->
                                <el-input v-model="form.box_location" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.materialFrameBarcode')" prop="box_barcode">
                                <!-- 料框条码 -->
                                <el-input v-model="form.box_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.taskNumber')" prop="task_num">
                                <!-- 任务号 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.partBarcodeNumber')" prop="part_barcode">
                                <!-- 零件条码 -->
                                <el-input v-model="form.part_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.partCode')" prop="part_code">
                                <!-- 零件编码 -->
                                <el-input v-model="form.part_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.partType')" prop="part_type">
                                <!-- 零件类型 -->
                                <el-input v-model="form.part_type" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingBoxResults.resultCode')" prop="fj_code">
                                <!-- 结果代码 -->
                                <el-input v-model="form.fj_code" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border  ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" />
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.sort_box_id }}</el-descriptions-item>
                                <el-descriptions-item label="料框位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_location }}</el-descriptions-item>
                                <el-descriptions-item label="料框条码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_barcode }}</el-descriptions-item>
                                <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                                <el-descriptions-item label="零件条码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.part_barcode }}</el-descriptions-item>
                                <el-descriptions-item label="零件编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.part_code }}</el-descriptions-item>
                                <el-descriptions-item label="零件类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.PART_TYPE[props.row.part_type] }}</el-descriptions-item>
                                <el-descriptions-item label="结果代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fj_code == '0' ? '正常' : '异常' }}</el-descriptions-item>
                                <el-descriptions-item label="消息/异常信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fj_msg }}</el-descriptions-item>
                                <el-descriptions-item label="码垛开始时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fj_start_time }}</el-descriptions-item>
                                <el-descriptions-item label="码垛结束时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fj_end_time }}</el-descriptions-item>
                            </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 料框位置 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="box_location"
                            :label="$t('lang_pack.sortingBoxResults.materialFramePosition')" min-width="80" align='center' />
                        <!-- 料框条码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="box_barcode"
                            :label="$t('lang_pack.sortingBoxResults.materialFrameBarcode')" min-width="80" align='center' />
                        <!-- 任务号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_num"
                            :label="$t('lang_pack.sortingBoxResults.taskNumber')"  min-width="80" align='center'/>
                        <!-- 零件条码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="part_barcode"
                            :label="$t('lang_pack.sortingBoxResults.partBarcodeNumber')" min-width="80" align='center' />
                        <!-- 零件编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="part_code"
                            :label="$t('lang_pack.sortingBoxResults.partCode')" min-width="80" align='center' />
                        <!-- 零件类型 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="part_type"
                            :label="$t('lang_pack.sortingBoxResults.partType')" min-width="80" align='center' >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.PART_TYPE[scope.row.part_type] }}
                            </template>
                        </el-table-column>
                        <!-- 结果代码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="fj_code"
                            :label="$t('lang_pack.sortingBoxResults.resultCode')" min-width="80" align='center' />
                        <!-- 码垛消息/异常 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="fj_msg"
                            :label="$t('lang_pack.sortingBoxResults.stackingMessage')" min-width="80" align='center' />
                        <!-- 码垛开始时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="fj_start_time"
                            :label="$t('lang_pack.sortingBoxResults.stackingStartTime')" min-width="80" align='center' />
                        <!-- 码垛结束时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="fj_end_time"
                            :label="$t('lang_pack.sortingBoxResults.stackingEndTime')" min-width="80" align='center' />
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import crudSortBox from '@/api/dcs/core/me/sortBox'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'
const defaultForm = {
    sort_box_id:'',
    box_location:'',
    box_barcode:'',
    task_num:'',
    part_barcode:'',
    part_code:'',
    part_type:'',
    fj_code:'',
    fj_msg:'',
    fj_start_time:'',
    fj_end_time:'',
}
export default {
    name: 'WEB_SORT_BOX',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '二次分拣料框结果',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'sort_box_id',
            // 排序
            sort: ['sort_box_id asc'],
            // CRUD Method
            crudMethod: { ...crudSortBox },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 310,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
            },
            pickerOptions: {}
        }
    },
    dicts: ['PART_TYPE'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 310
        }
    },
    created: function () {
        // 初始化日期选择器快捷选项
        this.pickerOptions = createDatePickerShortcuts(this.$i18n)
    },
    methods: {
    }
}
</script>
