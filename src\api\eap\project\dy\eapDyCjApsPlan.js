import request from '@/utils/request'

// 载具上报
export function carrierIDReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/cj/interf/send/CarrierIDReportOfWeb',
    method: 'post',
    data
  })
}

// Ping
export function eapPing(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/interf/send/EapPing',
    method: 'post',
    data
  })
}

// Mode切换
export function cimModeChangeReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/cj/interf/send/CimModeChangeReport',
    method: 'post',
    data
  })
}

// 取消任务
export function eapApsPlanCancel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/cj/plan/EapApsPlanCancel',
    method: 'post',
    data
  })
}

// 切换模式时判断
export function eapApsJudgeIsCanAbort(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/cj/plan/EapApsJudgeIsCanAbort',
    method: 'post',
    data
  })
}

// 本地任务存储
export function eapManualPlanSave(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/cj/plan/EapManualPlanSave',
    method: 'post',
    data
  })
}

export default { carrierIDReport, eapPing, cimModeChangeReport, eapApsPlanCancel, eapApsJudgeIsCanAbort,eapManualPlanSave }
