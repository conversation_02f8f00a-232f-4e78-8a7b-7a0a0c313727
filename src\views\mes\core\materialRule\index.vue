<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="版本号：">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="规则代码/描述：">
                <el-input v-model="query.materialRuleCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="success" icon="el-icon-document" plain round @click="recipeDrawerVisible = true">
            配方
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="150px" :inline="true">
          <el-form-item label="配方" prop="recipe_id">
            <el-select v-model="form.recipe_id" filterable clearable>
              <el-option v-for="item in recipeData" :key="item.recipe_id" :label="item.recipe_name + ' ' + item.recipe_version" :value="item.recipe_id" />
            </el-select>
          </el-form-item>

          <!--快速编码：MARERIAL_RULE_TYPE-->
          <el-form-item label="物料规则类型" prop="material_rule_type">
            <fastCode fastcode_group_code="MARERIAL_RULE_TYPE" :fastcode_code.sync="form.material_rule_type" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="物料规则编码" prop="material_rule_code">
            <el-input v-model="form.material_rule_code" />
          </el-form-item>
          <el-form-item label="物料规则描述" prop="material_rule_des">
            <el-input v-model="form.material_rule_des" />
          </el-form-item>
          <el-form-item label="是否定制规则" prop="special_flag">
            <el-select v-model="form.special_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="定制方法名称" prop="special_func_name">
            <el-input v-model="form.special_func_name" />
          </el-form-item>
          <el-form-item label="检查条码长度" prop="check_len_flag">
            <el-select v-model="form.check_len_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="条码长度值" prop="len_value">
            <el-input v-model.number="form.len_value" />
          </el-form-item>
          <el-form-item label="检查物料号" prop="check_code_flag">
            <el-select v-model="form.check_code_flag" style="width: 200px">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>

          <!--快速编码：CHECK_CODE_WAY-->
          <el-form-item label="检查物料号方式" prop="check_code_way">
            <fastCode fastcode_group_code="CHECK_CODE_WAY" :fastcode_code.sync="form.check_code_way" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="物料号开始位" prop="code_start_index">
            <el-input v-model.number="form.code_start_index" />
          </el-form-item>
          <el-form-item label="物料号结束位" prop="code_end_index">
            <el-input v-model.number="form.code_end_index" />
          </el-form-item>
          <el-form-item label="是否检查数量" prop="check_count_flag">
            <el-select v-model="form.check_count_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>

          <!--快速编码：CHECK_COUNT_WAY-->
          <el-form-item label="获取物料数量方式" prop="check_count_way">
            <fastCode fastcode_group_code="CHECK_COUNT_WAY" :fastcode_code.sync="form.check_count_way" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="数量开始位" prop="count_start_index">
            <el-input v-model.number="form.count_start_index" />
          </el-form-item>
          <el-form-item label="数量结束位" prop="count_end_index">
            <el-input v-model.number="form.count_end_index" />
          </el-form-item>
          <el-form-item label="是否检查批次号" prop="check_batch_flag">
            <el-select v-model="form.check_batch_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>

          <!--快速编码：CHECK_BATCH_WAY-->
          <el-form-item label="检查批次号方式" prop="check_batch_way">
            <fastCode fastcode_group_code="CHECK_BATCH_WAY" :fastcode_code.sync="form.check_batch_way" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="批次号开始位置" prop="batch_start_index">
            <el-input v-model.number="form.batch_start_index" />
          </el-form-item>
          <el-form-item label="批次号结束位置" prop="batch_end_index">
            <el-input v-model.number="form.batch_end_index" />
          </el-form-item>
          <el-form-item label="是否检查供应商" prop="check_supplier_flag">
            <el-select v-model="form.check_supplier_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>

          <!--快速编码：CHECK_SUPPLIER_WAY-->
          <el-form-item label="检查供应商方式" prop="check_supplier_way">
            <fastCode fastcode_group_code="CHECK_SUPPLIER_WAY" :fastcode_code.sync="form.check_supplier_way" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="供应商开始位置" prop="supplier_start_index">
            <el-input v-model.number="form.supplier_start_index" />
          </el-form-item>
          <el-form-item label="供应商结束位置" prop="supplier_end_index">
            <el-input v-model.number="form.supplier_end_index" />
          </el-form-item>
          <el-form-item label="是否检查机型" prop="check_model_flag">
            <el-select v-model="form.check_model_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>

          <!--快速编码：CHECK_MODEL_WAY-->
          <el-form-item label="检查机型方式" prop="check_model_way">
            <fastCode fastcode_group_code="CHECK_MODEL_WAY" :fastcode_code.sync="form.check_model_way" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="机型开始位置" prop="model_start_index">
            <el-input v-model.number="form.model_start_index" />
          </el-form-item>
          <el-form-item label="机型结束位置" prop="model_end_index">
            <el-input v-model.number="form.model_end_index" />
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>

          <!--快速编码：ENABLE_FLAG-->
          <el-form-item label="有效标识">
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>

        <el-divider />
        <div class="ruleBottom">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="false" title="配方信息" :visible.sync="recipeDrawerVisible" size="100%" @closed="getRecipeData()">
        <recipe v-if="recipeDrawerVisible" ref="recipe" />
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="料规则ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_id }}</el-descriptions-item>
                  <el-descriptions-item label="物料规则类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_type }}</el-descriptions-item>
                  <el-descriptions-item label="物料规则编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_code }}</el-descriptions-item>
                  <el-descriptions-item label="物料规则描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_des }}</el-descriptions-item>
                  <el-descriptions-item label="是否定制规则" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.special_flag == 'N' ? '否' : '是' }}</el-descriptions-item>
                  <el-descriptions-item label="定制方法名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.special_func_name }}</el-descriptions-item>
                  <el-descriptions-item label="检查条码长度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_len_flag == 'N' ? '否' : '是' }}</el-descriptions-item>
                  <el-descriptions-item label="条码长度值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.len_value }}</el-descriptions-item>
                  <el-descriptions-item label="检查物料号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_code_way }}</el-descriptions-item>
                  <el-descriptions-item label="检查物料号方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_code_way }}</el-descriptions-item>
                  <el-descriptions-item label="物料号开始位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.code_start_index }}</el-descriptions-item>
                  <el-descriptions-item label="物料号结束位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.code_end_index }}</el-descriptions-item>
                  <el-descriptions-item label="是否检查数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_count_flag == 'N' ? '否' : '是' }}</el-descriptions-item>
                  <el-descriptions-item label="获取物料数量方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_count_way }}</el-descriptions-item>
                  <el-descriptions-item label="数量开始位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.count_start_index }}</el-descriptions-item>
                  <el-descriptions-item label="数量结束位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.count_end_index }}</el-descriptions-item>
                  <el-descriptions-item label="是否检查批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_batch_flag == 'N' ? '否' : '是' }}</el-descriptions-item>
                  <el-descriptions-item label="检查批次号方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_batch_way }}</el-descriptions-item>
                  <el-descriptions-item label="批次号开始位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.batch_start_index }}</el-descriptions-item>
                  <el-descriptions-item label="批次号结束位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.batch_end_index }}</el-descriptions-item>
                  <el-descriptions-item label="是否检查供应商" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_supplier_flag == 'N' ? '否' : '是' }}</el-descriptions-item>
                  <el-descriptions-item label="检查供应商方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_supplier_way }}</el-descriptions-item>
                  <el-descriptions-item label="供应商开始位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.supplier_start_index }}</el-descriptions-item>
                  <el-descriptions-item label="供应商结束位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.supplier_end_index }}</el-descriptions-item>
                  <el-descriptions-item label="是否检查机型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_flag == 'N' ? '否' : '是' }}</el-descriptions-item>
                  <el-descriptions-item label="检查机型方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_way }}</el-descriptions-item>
                  <el-descriptions-item label="机型开始位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_start_index }}</el-descriptions-item>
                  <el-descriptions-item label="机型结束位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_end_index }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag == 'N' ? '无效' : '有效' }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="material_rule_id" label="id" />

            <el-table-column  :show-overflow-tooltip="true" prop="recipe_type" label="配方类型" />
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_name" label="配方描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_version" label="版本号" />
            <el-table-column  :show-overflow-tooltip="true" prop="liable_person" label="责任人" />
            <el-table-column  :show-overflow-tooltip="true" prop="update_remark" label="更新说明" />

            <el-table-column  :show-overflow-tooltip="true" prop="material_rule_type" min-width="120" label="物料规则类型" sortable="custom" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_rule_code" min-width="120" label="物料规则编码" sortable="custom" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_rule_des" min-width="120" label="物料规则描述" sortable="custom" />
            <el-table-column  :show-overflow-tooltip="true" prop="special_flag" width="120" label="定制化规则" sortable="custom">
              <template slot-scope="scope">
                {{ scope.row.special_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="special_func_name" width="170" label="定制化方法名称" sortable="custom" />
            <el-table-column  :show-overflow-tooltip="true" prop="check_len_flag" width="120" label="检查条码长度" sortable="custom">
              <template slot-scope="scope">
                {{ scope.row.check_len_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="len_value" width="120" label="条码长度值" sortable="custom" />
            <el-table-column  label="有效标识" align="center" prop="enable_flag" width="120">
              <template slot-scope="scope">
                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMaterialRule from '@/api/mes/core/materialRule'
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import recipe from '@/views/mes/core/recipe/recipe'
const defaultForm = {
  material_rule_id: '',
  recipe_id: '',
  material_rule_type: '',
  material_rule_code: '',
  material_rule_des: '',
  special_flag: 'N',
  special_func_name: '',
  check_len_flag: 'N',
  len_value: '0',
  check_code_flag: 'N',
  check_code_way: '',
  code_start_index: '0',
  code_end_index: '0',
  check_count_flag: 'N',
  check_count_way: '',
  count_start_index: '0',
  count_end_index: '0',
  check_batch_flag: 'N',
  check_batch_way: '',
  batch_start_index: '0',
  batch_end_index: '0',
  check_supplier_flag: 'N',
  check_supplier_way: '',
  supplier_start_index: '0',
  supplier_end_index: '0',
  check_model_flag: 'N',
  check_model_way: '',
  model_start_index: '0',
  model_end_index: '0',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MaterialRule',
  components: { crudOperation, rrOperation, udOperation, pagination, recipe },
  cruds() {
    return CRUD({
      title: '物料扫描规则',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_rule_id',
      // 排序
      sort: ['material_rule_id asc'],
      // CRUD Method
      crudMethod: { ...crudMaterialRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_material_rule:add'],
        edit: ['admin', 'c_mes_fmod_recipe_material_rule:edit'],
        del: ['admin', 'c_mes_fmod_recipe_material_rule:del'],
        down: ['admin', 'c_mes_fmod_recipe_material_rule:down']
      },
      rules: {
        // 提交验证规则
        recipe_version: [{ required: true, message: '请输入配方版本号', trigger: 'blur' }],
        material_rule_code: [{ required: true, message: '请输入物料扫描规则编码', trigger: 'blur' }],
        material_rule_des: [{ required: true, message: '请输入物料扫描规则值', trigger: 'blur' }]
      },
      recipeDrawerVisible: false,
      recipeData: []
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.getRecipeData()
  },

  methods: {
    getRecipeData() {
      this.recipeData = []
      crudRecipe
        .sel({
          user_name: Cookies.get('userName'),
          recipe_type: 'SCANRULE',
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.recipeData = defaultQuery.data
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  min-height: calc(100vh - 30px);
  .el-card__header {
    padding: 0px 20px;
    height: 40px;
    line-height: 40px;
  }
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}

.el-drawer {
  overflow-y: scroll;
}
.ruleBottom{
  text-align: center;
  margin: 40px 0;
}
</style>
