#chart{
    background-size: 20px 20px, 20px 20px, 10px 10px, 10px 10px;
    background-image: linear-gradient(to right, #dadada 1px, transparent 1px),
    linear-gradient(to bottom, #dadada 1px, transparent 1px),
    linear-gradient(to right, #dadada 1px, transparent 1px),
    linear-gradient(to bottom, #dadada 1px, transparent 1px);
    background-position: left -1px top -1px, left -1px top -1px, left -1px top -1px, left -1px top -1px;
}
#chart:hover{
    cursor: grab;
    filter: brightness(100%);
}
#chart:active {
    cursor: grabbing;
}
#svg {
    height: 400%;
    width: 400%;
    overflow:auto;
}

#chart {
    position: relative;
    width: 800px;
    height: 600px;
    border: 0px solid #dfdfdf;
    overflow: auto;
}
#chart::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
  }
  #chart::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f6f9ff;
    cursor: pointer !important;
  }
#position {
    position: absolute;
    right: 4px;
}

.unselectable {
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color:#dfdfdf;
}

.connector {
    cursor: crosshair;
    opacity: 0;
}

.connector.active {
    opacity: 1;
    fill: white;
    stroke: #bbbbbb;
    stroke-width: 1px;
}

.connector:hover {
    stroke: red;
}
.tool_btn {
    cursor:pointer;
    opacity: 0;
}

.tool_btn.active {
    opacity: 1;
    fill: white;
    stroke: #bbbbbb;
    stroke-width: 1px;
}

#svg .selection {
    stroke: lightblue;
    fill: lightblue;
    fill-opacity: 0.8;
    display: none;
}

#svg .selection.active {
    display: block;
}
.el-drawer__body{
    overflow: visible !important;
  }
.mini-map-container {
    position: absolute;
    z-index: 999;
    width: 300px;
    height: 200px;
    bottom: 30px;
    right: 40px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    background-size: 20px 20px, 20px 20px, 10px 10px, 10px 10px;
    background-image: linear-gradient(to right, #dadada 1px, transparent 1px),
    linear-gradient(to bottom, #dadada 1px, transparent 1px),
    linear-gradient(to right, #dadada 1px, transparent 1px),
    linear-gradient(to bottom, #dadada 1px, transparent 1px);
    background-position: left -1px top -1px, left -1px top -1px, left -1px top -1px, left -1px top -1px;
    background-color: #ffffff;
  }
  .mini-map-container .move{
    position:absolute;
    top:0px;
    left:0px;
    display:none;
    width:100px;
    height:100px;
    border: 2px solid red;
  }
  .mini-map-container:hover .move{
    display: block;
  }