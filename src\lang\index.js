// 导入现有的主语言文件（向后兼容）
import * as zh from './zh'
import * as en from './en'
import * as tw from './tw'
import * as th from './th'

// 深度合并对象
function deepMerge(target, ...sources) {
  if (!sources.length) return target
  const source = sources.shift()

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }

  return deepMerge(target, ...sources)
}

function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item))
}

// 自动加载模块化的语言文件
function loadLanguageModules() {
  // 使用 require.context 自动加载 modules 目录下的所有语言文件
  const moduleFiles = require.context('./modules', true, /\.(js|ts)$/)
  
  // 初始化语言对象
  const zhModules = {}
  const enModules = {}
  const twModules = {}
  const thModules = {}

  // 遍历所有模块文件
  moduleFiles.keys().forEach(modulePath => {
    try {
      // 获取模块内容
      const module = moduleFiles(modulePath)
      
      // 根据文件路径判断语言类型
      if (modulePath.includes('/zh.js')) {
        deepMerge(zhModules, module.default || {})
      } else if (modulePath.includes('/en.js')) {
        deepMerge(enModules, module.default || {})
      } else if (modulePath.includes('/tw.js')) {
        deepMerge(twModules, module.default || {})
      } else if (modulePath.includes('/th.js')) {
        deepMerge(thModules, module.default || {})
      }
    } catch (error) {
      console.error(`Error loading language module: ${modulePath}`, error)
    }
  })

  return { zhModules, enModules, twModules, thModules }
}

// 加载所有模块化的语言文件
const { zhModules, enModules, twModules, thModules } = loadLanguageModules()

// 合并主语言文件和模块化语言文件
const zhLang = deepMerge({}, zh, zhModules)
const enLang = deepMerge({}, en, enModules)
const twLang = deepMerge({}, tw, twModules)
const thLang = deepMerge({}, th, thModules)

// 导出合并后的语言包
export default {
  'zh-CN': zhLang,
  'en-US': enLang,
  'zh-TW': twLang,
  'th': thLang
}
