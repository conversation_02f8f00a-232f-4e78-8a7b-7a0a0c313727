import request from '@/utils/request'

// 查询工位当前任务计划
export function stationTaskSel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanStationTaskSelect',
    method: 'post',
    data
  })
}
// 查询工位当前任务计划
export function stationTaskPbjSel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanStationTaskSelectPbj',
    method: 'post',
    data
  })
}

// 查询放板机最近任务信息
export function loadCurrentTaskSel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanLoadCurrentTaskSelect',
    method: 'post',
    data
  })
}

// 查询收板机最近任务信息
export function unLoadCurrentTaskSel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanUnLoadCurrentTaskSelect',
    method: 'post',
    data
  })
}

// 放板机查询收板机设备状态
export function unLoadStatusSel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanUnLoadStatusSelectByLoad',
    method: 'post',
    data
  })
}

// 广合放板机查询收板机设备状态
export function unLoadStatusSelCore4(data) {
  return request({
    url: 'aisEsbWeb/eap/core4/EapApsPlanUnLoadStatusSelectByLoad',
    method: 'post',
    data
  })
}

// 放板机手工输入条码验证
export function loadPlanPanelCheck(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapCoreLoadPlanPanelCheck',
    method: 'post',
    data
  })
}

// CIM消息查询
export function eapCimMsgShow(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeHmiShowDlgSelect',
    method: 'post',
    data
  })
}

// 取消任务
export function eapApsPlanCancel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanCancel',
    method: 'post',
    data
  })
}

// 切换模式时判断
export function eapApsJudgeIsCanAbort(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsJudgeIsCanAbort',
    method: 'post',
    data
  })
}

export default { stationTaskSel, stationTaskPbjSel, loadCurrentTaskSel, unLoadCurrentTaskSel, unLoadStatusSel, unLoadStatusSelCore4, loadPlanPanelCheck, eapCimMsgShow, eapApsPlanCancel, eapApsJudgeIsCanAbort }
