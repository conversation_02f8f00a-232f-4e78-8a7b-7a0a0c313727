import request from '@/utils/request'

// 查询工位端口信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapStationPortSel',
    method: 'post',
    data
  })
}
// 新增工位端口信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapStationPortIns',
    method: 'post',
    data
  })
}
// 修改工位端口信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapStationPortUpd',
    method: 'post',
    data
  })
}
// 修改工位端口信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapStationPortEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除工位端口信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapStationPortDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

