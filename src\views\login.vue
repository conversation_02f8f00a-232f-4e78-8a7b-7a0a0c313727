<template>
  <div class="login" :style="'background-image:url(' + Background + ');'">
    <div class="cloud-bubble-one" :style="'background-image:url(' + cloudBackBule1 + ');'" />
    <div class="cloud-bubble-two" :style="'background-image:url(' + cloudBackBule2 + ');'" />
    <div class="cloud-bubble-three" :style="'background-image:url(' + cloudBackBule3 + ');'" />
    <div class="u-f u-f-ac wrapeldropdown">
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          {{ availableLanguages.length > 0 ? getCurrentLanguageLabel() : '语言选择' }}
          <i class="el-icon-arrow-down el-icon--right" />
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="lang in availableLanguages"
            :key="lang.value"
            :command="lang.value">{{ lang.label }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <img class="imgLeft" :src="LoginLeftImage">
        <!--form 表单-->
        <!--rules表单验证-->
        <el-form id="loginForm" ref="loginForm" class="loginForm" :model="loginForm" :rules="loginRules" label-position="left">
          <h3 class="title">{{ $t('lang_pack.SystemName') }}</h3>
          <!--绑定prop属性-->
          <el-form-item prop="username" style="margin-top:30px;">
            <span>{{ $t('lang_pack.UserName') }}：</span>
            <el-input v-model="loginForm.username" type="text" auto-complete="off" />
          </el-form-item>
          <el-form-item class="passwordStyle" prop="password">
            <span>{{ $t('lang_pack.Password') }}：</span>
            <el-input v-model="loginForm.password" type="password" auto-complete="off" @keyup.enter.native="handleLogin" />
          </el-form-item>
          <el-form-item prop="code">
            <span>{{ $t('lang_pack.Code') }}：</span>
            <el-input v-model="loginForm.code" auto-complete="off" @keyup.enter.native="handleLogin" />
            <!--验证码-->
            <div class="login-code">
              <img :src="codeUrl" @click="getCode">
            </div>
          </el-form-item>
          <!-- <el-form-item class="passwordStyle">
            <span>系统语言：</span>
            <div class="u-f u-f-ac wrapeldropdown">
              <el-dropdown @command="handleCommand">
                <span class="el-dropdown-link"> {{ $t('lang_pack.locale') }}<i class="el-icon-arrow-down el-icon--right" /> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
                  <el-dropdown-item command="zh-TW">繁体</el-dropdown-item>
                  <el-dropdown-item command="en-US">English</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </el-form-item> -->
          <!-- <el-checkbox
            v-model="loginForm.rememberMe"
            style="margin: 0 0 25px 0"
          >
            <span style="color: #707070; font-size: 13px">记住我</span>
          </el-checkbox> -->
          <el-form-item style="width: 100%;text-align:center;margin-top:40px;">

            <el-button :loading="loading" size="medium" type="primary" class="login_btn" @click.native.prevent="handleLogin">
              <span v-if="!loading">{{ $t('lang_pack.SignOn') }}</span>
              <span v-else>{{ $t('lang_pack.LoggingIn') }}</span>
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <!--  底部  -->
    <!--是否显示设置的底部信息-->
    <div v-if="$store.state.settings.showFooter" id="el-login-footer" style="color: #FFFFFF">
      <!--底部文字，支持html语法-->
      <span v-html="$store.state.settings.footerTxt" />
      <!-- <span> ⋅ </span> -->
      <!--备案号-->
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{ $store.state.settings.caseNumber }}</a>
    </div>
    <!-- <div id="particles-js">
      <canvas class="particles-js-canvas-el" width="1536" height="658" style="width: 100%; height: 100%;" />
    </div> -->
    <updatePass ref="pass" />
  </div>
</template>

<script>
import Config from '@/settings'
import Cookies from 'js-cookie'
import Background from '@/assets/images/loginbj1.jpg'
import cloudBackBule1 from '@/assets/images/cloud-back-bule1.png'
import cloudBackBule2 from '@/assets/images/cloud-back-bule2.png'
import cloudBackBule3 from '@/assets/images/cloud-back-bule3.png'
import LoginLeftImage from '@/assets/images/loginleft.gif'
import { getSysCodeImg } from '@/api/login'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'

// import { encrypt } from '@/utils/rsaEncrypt'
import updatePass from './core/system/user/updatePass'

export default {
  name: 'Login',
  components: {
    updatePass
  },
  data() {
    return {
      cloudBackBule1: cloudBackBule1,
      cloudBackBule2: cloudBackBule2,
      cloudBackBule3: cloudBackBule3,
      Background: Background,
      LoginLeftImage: LoginLeftImage,
      codeUrl: '',
      // 判断 密码是否和上次输入密码一致
      cookiePass: '',
      // 隐藏的语言列表
      hiddenLanguages: [],
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: this.$t('lang_pack.header.usernameRequire') }],
        password: [{ required: true, trigger: 'blur', message: this.$t('lang_pack.header.passwordRequire') }],
        code: [{ required: true, trigger: 'change', message: this.$t('lang_pack.header.codeRequire') }]
      },
      loading: false,
      redirect: undefined
    }
  },
  // watch：它可以用来监测(相应数据的变化)Vue实例上的数据变动
  // immediate：在最初绑定值的时候也执行函数
  // handler：

  // $route：
  // $route.path 字符串，等于当前路由对象的路径，会被解析为绝对路径，如 "/home/<USER>" 。
  // $route.params 对象，包含路由中的动态片段和全匹配片段的键值对
  // $route.query 对象，包含路由中查询参数的键值对。例如，对于 /home/<USER>/detail/01?favorite=yes ，会得到$route.query.favorite == 'yes' 。
  // $route.router 路由规则所属的路由器（以及其所属的组件）。
  // $route.matched 数组，包含当前匹配的路径中所包含的所有片段所对应的配置参数对象。
  // $route.name 当前路径的名字，如果没有使用具名路径，则名字为空。

  // $router
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  computed: {
    // 可用的语言列表
    availableLanguages() {
      const allLanguages = [
        { value: 'zh-CN', label: '中文' },
        { value: 'zh-TW', label: '繁体' },
        { value: 'th', label: 'Thai' },
        { value: 'en-US', label: 'English' }
      ]

      // 如果有隐藏的语言，则过滤掉
      if (this.hiddenLanguages && this.hiddenLanguages.length > 0) {
        return allLanguages.filter(lang => !this.hiddenLanguages.includes(lang.value))
      }

      return allLanguages
    }
  },
  // created：生命周期钩子函数，就是一个vue实例被生成后调用这个函数
  created() {
    // 获取验证码
    this.getCode()
    // 获取用户名、密码等Cookie
    this.getCookie()
    // token 过期提示
    this.point()
    // 获取隐藏的语言列表
    this.getHiddenLanguageList()
  },
  methods: {
    // 获取验证码
    getCode() {
      getSysCodeImg().then(res => {
        this.codeUrl = res.img
        this.loginForm.uuid = res.uuid
        // 记录验证码(失效时间为1分钟)
        var millisecond = new Date().getTime()
        var expiresTime = new Date(millisecond + 60 * 1000 * 1)
        Cookies.set('code', res.code, { expires: expiresTime })
      })
    },
    // 获取用户名、密码等Cookie
    getCookie() {
      // const username = Cookies.get('username')
      // let password = Cookies.get('password')
      // const rememberMe = Cookies.get('rememberMe') //记住我
      // // 保存cookie里面的加密后的密码
      // this.cookiePass = password === undefined ? '' : password
      // password = password === undefined ? this.loginForm.password : password
      // this.loginForm = {
      //  username: username === undefined ? this.loginForm.username : username,
      //  password: password,
      //  rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      //  code: ''
      // }
    },
    // 登录
    handleLogin() {
      // $refs 是一个对象，持有已注册过 ref 的所有的子组件
      // 表单验证validate
      this.$refs.loginForm.validate(valid => {
        const user = {
          username: this.loginForm.username,
          password: this.loginForm.password,
          rememberMe: this.loginForm.rememberMe,
          code: this.loginForm.code,
          uuid: this.loginForm.uuid
        }

        // 验证码校验
        const code = Cookies.get('code')
        if (code === undefined || code.toUpperCase() !== this.loginForm.code.toUpperCase()) {
          this.$notify({
            title: this.$t('lang_pack.Prompt'),
            message: this.$t('lang_pack.codeError'),
            type: 'warning',
            duration: 5000
          })
          // 异步请求失败，获取新的验证码重新登录
          this.loading = false
          this.getCode()
          return false
        }

        // 密码加密
        // if (user.password !== this.cookiePass) {
        //  user.password = encrypt(user.password)
        // }

        if (valid) {
          this.loading = true
          // 记住我
          if (user.rememberMe) {
            // Cookies.set('username', user.username, { expires: Config.passCookieExpires })
            // Cookies.set('password', user.password, { expires: Config.passCookieExpires })
            Cookies.set('rememberMe', user.rememberMe, {
              expires: Config.passCookieExpires
            })
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('code')
            Cookies.remove('rememberMe')
          }
          // 异步请求：store->user.js
          this.$store
            .dispatch('Login', user)
            .then(() => {
              this.loading = false
              this.$router.push({ path: this.redirect || '/' })
              // this.$router.push({ path: '/home' })
            })
            .catch(() => {
              // 异步请求失败，获取新的验证码重新登录
              this.loading = false
              this.getCode()
            })
        } else {
          return false
        }
      })
    },
    point() {
      // 登录过期校验
      const point = Cookies.get('point') !== undefined
      if (point) {
        this.$notify({
          title: this.$t('lang_pack.Prompt'),
          message: '当前登录状态已过期，请重新登录！',
          type: 'warning',
          duration: 5000
        })
        Cookies.remove('point')
      }
    },
    handleCommand(command) {
      this.$i18n.locale = command
      localStorage.setItem('language', this.$i18n.locale)
    },

    // 获取当前语言的标签
    getCurrentLanguageLabel() {
      // 如果没有可用的语言选项，返回默认文本
      if (this.availableLanguages.length === 0) {
        return '语言选择'
      }

      const currentLocale = this.$i18n.locale || localStorage.getItem('language') || 'zh-CN'
      const lang = this.availableLanguages.find(item => item.value === currentLocale)

      // 如果当前语言不在可用的语言选项中，默认选择第一个语言
      if (!lang) {
        // 自动切换到第一个可用的语言
        const firstLang = this.availableLanguages[0]
        this.$i18n.locale = firstLang.value
        localStorage.setItem('language', firstLang.value)
        return firstLang.label
      }

      return lang.label
    },

    // 获取隐藏的语言列表
    getHiddenLanguageList() {
      const queryParameter = {
        userName: Cookies.get('userName'),
        parameter_code: 'HIDDEN_LANG_LIST',
        enable_flag: 'Y'
      }

      selSysParameter(queryParameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0 && defaultQuery.data && defaultQuery.data.length > 0) {
            const hiddenLangStr = defaultQuery.data[0].parameter_val
            if (hiddenLangStr) {
              // 将逗号分隔的字符串转换为数组
              this.hiddenLanguages = hiddenLangStr.split(',').map(item => item.trim())
            }
          }
        })
        .catch(() => {
          console.error('获取隐藏语言列表失败')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-size: cover;
  .el-input--small .el-input__inner {
    height: 45px;
    line-height: 45px;
    border-radius: 5px;
    background-color: #e8f0fe;
    border: 0px;
    font-size: 12px;
    color: #333333;
  }
}
.title {
  margin: 0 auto 10px auto;
  text-align: center;
  color: #18245e;
  font-size: 24px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  min-width: 50%;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  margin-left: 10px;
  img {
    cursor: pointer;
    vertical-align: middle;
    width: 110px;
  }
}
.login_btn {
  background-color: #6b9ef9;
  width: 100%;
  font-size: 16px;
  border-radius: 10px;
  padding: 12px;
}
.login_btn:hover {
  background-color: #558beb;
}
.el-form-item--small .el-form-item__content {
  display: flex;
  white-space: nowrap;
  align-items: center;
  span {
    font-size: 14px;
    text-align: right;
    min-width: 65px;
  }
}
.imgLeft {
  width: 40%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 13;
}
.loginForm {
  position: fixed;
  bottom: 15%;
  right: 10%;
  border-radius: 10px;
  background: #ffffff;
  padding: 30px 25px;
  width: 380px;
  z-index: 13;
  ::v-deep .el-form-item__error{
    top: 108%;
    left: 20%;
  }

}
.passwordStyle {
  margin: 23px 0 !important;
}
.el-form-item__error {
  top: 110%;
  left: 42px;
}
#el-login-footer {
  bottom: 10px;
}
.wrapeldropdown{
position: fixed;
  top: 20px;
  right: 2%;
  display: flex;
  align-items: center;
  span{
    color: #333333;
    font-size: 13px;
  }
  .el-dropdown{
  color: #333333;
  font-size: 13px;
  }
}
.spanStyle{
  min-width: 70px;
}
.cloud-bubble-one {
  position: absolute;
  top: -120px;
  left: 50%;
  z-index: 10;
  margin-left: -120px;
  width: 240px;
  height: 240px;
  background-repeat: no-repeat;
  animation: bubble-animate-1 linear 10s infinite;
}

.cloud-bubble-two {
  position: absolute;
  top: 50px;
  left: 24%;
  z-index: 11;
  width: 360px;
  height: 360px;
  background-repeat: no-repeat;
  animation: bubble-animate-2 linear 12s infinite;
}

.cloud-bubble-three {
  position: absolute;
  top: 50px;
  left: 48%;
  z-index: 12;
  width: 300px;
  height: 300px;
  background-repeat: no-repeat;
  animation: bubble-animate-3 linear 11s infinite;
}

@keyframes bubble-animate-1 {
  from {
    top: -120px;
  }

  50% {
    top: -180px;
  }

  to {
    top: -120px;
  }
}

@keyframes bubble-animate-2 {
  from {
    top: 50px;
    left: 34%;
  }

  50% {
    top: 80px;
    left: 24%;
  }

  to {
    top: 50px;
    left: 34%;
  }
}

@keyframes bubble-animate-3 {
  from {
    top: 50px;
    left: 48%;
  }

  50% {
    top: 80px;
    left: 58%;
  }

  to {
    top: 50px;
    left: 48%;
  }
}
</style>

<style>

#loginForm> .el-form-item--small .el-form-item__content span {
    font-size: 12px !important;
  }
 #loginForm>  .el-form-item--small .el-form-item__content {
    display: flex;
    align-items: center;
  }
</style>
