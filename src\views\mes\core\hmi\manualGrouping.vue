<template>
  <div class="orderinfo-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="12">
          <el-card class="cardFirst">
            <div class="orderrecipe">
              <div class="orderButton">
                <el-button class="buttonone" size="large" type="primary" @click="getOrder">选择订单</el-button>
              </div>
              <div class="wrapDes">
                <el-descriptions :column="2">
                  <el-descriptions-item label="订单号">{{ currentOrderInfo.make_order }}</el-descriptions-item>
                  <el-descriptions-item label="计划数量">{{ currentOrderInfo.mo_plan_count }}</el-descriptions-item>
                  <el-descriptions-item label="机型">{{ currentOrderInfo.small_model_type }}</el-descriptions-item>
                  <el-descriptions-item label="完成数量">{{ currentOrderInfo.mo_finish_count }}</el-descriptions-item>
                  <el-descriptions-item label="模组配方">{{ currentOrderInfo.recipe }}</el-descriptions-item>
                  <el-descriptions-item label="电芯排废率">{{ currentOrderInfo.mo_scrap_rate }}</el-descriptions-item>
                  <el-descriptions-item label="开始时间">{{ currentOrderInfo.plan_start_time }}</el-descriptions-item>
                  <el-descriptions-item label="结束时间">{{ currentOrderInfo.plan_end_time }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
          <el-card>
            <el-tabs type="border-card">
              <el-tab-pane label="电芯配方">
                <el-table border height="360" :data="batteriesTableData" style="width: 100%" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
                  <el-table-column label="电芯序号" prop="dx_num" />
                  <el-table-column label="线体选择" prop="line_num" />
                  <el-table-column label="电芯方向" prop="dx_direct" />
                  <el-table-column label="电芯档位" prop="dx_gear" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="数据范围">
                <el-table border height="360" :data="limitList" style="width: 100%" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
                  <el-table-column label="范围值约束编码" prop="mz_limit_code" />
                  <el-table-column label="范围值约束描述" prop="mz_limit_des" />
                  <el-table-column label="上限" prop="upper_limit" />
                  <el-table-column label="下限" prop="down_limit" />
                  <el-table-column label="排废槽号" prop="ng_rack_code" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="条码NG规则">
                <el-table border height="360" :data="dxbarList" style="width: 100%" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
                  <el-table-column label="电芯条码排废名称" prop="dxbar_ng_name" width="120" />
                  <el-table-column label="条码起始位置" prop="start_index" />
                  <el-table-column label="条码结束位置" prop="end_index" />
                  <el-table-column label="NG方式" prop="ng_way" />
                  <el-table-column label="值集合" prop="value_list" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="cardFirst">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="orderButton">
                  <el-button :class="'btnRequestPallet'+requestPallet" size="large" type="primary" @click="handleRequestPallet">请求一个空托盘</el-button>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="orderButton">
                  <el-button size="large" type="primary" @click="repairRequest">返修上线</el-button>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statuStyle">
                  <p><span :class="allowScan=='1'?'commonsatu statuOne':'commonsatu statuZero'" /><span>允许扫描电芯</span></p>
                </div>
              </el-col>
              <el-col :span="6">
                <span>电芯完成/计划：</span><span style="font-size:20px;font-weight:700">{{ finishCountAndPlanCount }}</span>
                <el-tag v-if="dxScanFinish" type="success">已全部完成</el-tag>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top:25px;">
              <el-col :span="24">
                <div class="scanStyle">
                  <span>模组码：</span>
                  <span>
                    {{ mzBarcode }}
                  </span>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-card>
            <div class="scanStyle">
              <span>电芯码：</span>
              <div class="wrapimin">
                <el-input v-model="scantwo" type="text" size="large" placeholder="请输入内容" @blur="handleBlur" @input="handleInput" />
                <img :src="keyboard" @click="showKeyboard">
              </div>
              <el-button class="scanBtn" size="large" type="primary" @click="ManualDxScan">扫描</el-button>
            </div>
            <el-table border :data="dxData" style="width: 100%" height="460" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
              <el-table-column label="替换电芯" prop="checked">
                <template slot-scope="scope">
                  <el-checkbox v-model="checked" :true-label="scope.row.dx_index" @change="handleUpdDx" />
                </template>
              </el-table-column>
              <el-table-column label="序号" prop="dx_index" />
              <el-table-column label="串数" prop="mk_num" />
              <el-table-column label="NG标识" prop="check_ng_flag">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.check_ng_flag === 'Y' ? 'danger' : 'success'" disable-transitions>
                    {{ scope.row.check_ng_flag }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="NG描述" width="200" prop="check_ng_msg" />
              <el-table-column label="电芯码" prop="dx_barcode" />
              <el-table-column label="时间" prop="creation_date" />
              <el-table-column label="档位" prop="dx_gear" />
              <el-table-column label="批次号" prop="dx_ori_batch" />
              <el-table-column label="容量" prop="dx_ori_capacity" />
              <el-table-column label="电压" prop="dx_ori_ocv4_pressure" />
              <el-table-column label="内阻" prop="dx_ori_ocr4_air" />
              <el-table-column label="K值" prop="dx_ori_k_value" />
              <el-table-column label="Dcir内阻" prop="dx_ori_dcir" />
              <el-table-column label="厚度" prop="dx_ori_thickness" />
              <el-table-column label="ocv4时间" prop="dx_ori_ocv4_time" />
            </el-table>
          </el-card>
          <!-- <el-card class="box-card1" /> -->
        </el-col>
      </el-row>
    </el-card>
    <el-drawer title="选择订单" :visible.sync="chooseOrder" direction="rtl" size="85%">
      <el-table border :data="radioArr" style="width: 100%" height="550" highlight-current-row @header-dragend="crud.tableHeaderDragend()" @row-click="singleElection">
        <el-table-column align="center" width="55" label="选择">
          <template slot-scope="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio v-model="templateSelection" class="radio" :label="scope.row.mo_id">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="mo_from" label="订单来源" width="80" />
        <el-table-column align="center" prop="make_order" label="订单号" width="150" />
        <el-table-column align="center" prop="product_batch" label="订单批号" width="80" />
        <el-table-column align="center" prop="small_model_type" label="产品型号" width="250" />
        <el-table-column align="center" prop="mo_plan_count" label="订单计划数量" width="80" />
        <el-table-column align="center" prop="mo_finish_count" label="实际完成数量" width="80" />
        <el-table-column align="center" prop="mo_order_by" label="订单排序" width="80" />
        <el-table-column align="center" prop="plan_start_time" label="计划开始时间" :formatter="formatDate" width="80" />
        <el-table-column align="center" prop="plan_end_time" label="计划结束时间" :formatter="formatDate" width="80" />
        <el-table-column align="center" prop="start_date" label="订单启动时间" :formatter="formatDate" width="80" />
        <el-table-column align="center" prop="finish_date" label="订单完成时间" :formatter="formatDate" width="80" />
        <el-table-column align="center" prop="mo_custom_des" label="订单来源客户描述" width="80" />
      </el-table>
    </el-drawer>
    <el-drawer title="订单明细" :visible.sync="orderDetail" direction="rtl" size="50%" />

    <!-- 返修弹窗 -->
    <el-dialog title="返修上线" :visible.sync="repairDialogVisible" width="650px" top="65px" :close-on-click-modal="false" @close="handleCloseDialog">
      <manualRepair v-if="manualRepairShow" ref="manualRepair" @repairConfirm="repairConfirm" />
    </el-dialog>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import { chooseOrderInfo, getOrderInfo, showOrderInfo, mesGxMzManualRepairSelect } from '@/api/hmi/orderinfo.js'
import { mesGxMzManualMzInfoSelect, mesGxMzManualSelect, mesGxMzManualDxCheck } from '@/api/mes/project/manualGroup.js'
import { selCellIP } from '@/api/core/center/cell'

import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
import manualRepair from '@/views/mes/core/hmi/manualRepair'

export default {
  name: 'orderInfo',
  components: {
    SimpleKeyboard, manualRepair
  },
  data() {
    return {
      currentOrderInfo: {
        make_order: '',
        mo_plan_count: '',
        small_model_type: '',
        mo_finish_count: '',
        recipe: '',
        mo_scrap_rate: '',
        plan_start_time: '',
        plan_end_time: ''
      },
      checked: false,
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      // scanone: '',
      scantwo: '',
      chooseOrder: false,
      orderDetail: false,
      choose_make_order: '',
      choose_mo_id: '',
      iframeStaionId: '',
      iframeprodLineId: '',
      iframeStaionCode: '',
      allowScan: 0, // 允许扫描电芯
      finishCountAndPlanCount: '0/0', // 电芯完成数量/电芯计划数量

      requestPallet: '0', // 请求一个空托盘
      mzBarcode: '', // 模组码
      plcRequestFlag: 'N', // PLC触发请求
      dxIndex: '',
      dxScanFinish: false,
      radioArr: [],
      // 当前选择的行的id
      templateSelection: '',
      recipeTableData: [],
      batteriesTableData: [],
      dxbarList: [],
      limitList: [],
      dxData: [],

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      connectUrl: '', // MQTT连接地址
      tagOnlyKey: '',
      repairDialogVisible: false,
      manualRepairShow: false
    }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  created() { },
  mounted() {
    this.iframeprodLineId = this.$route.query.prod_line_id
    this.iframeStaionId = this.$route.query.station_id
    this.iframeStaionCode = this.$route.query.station_code
    // console.log(this.iframeprodLineId, this.iframeStaionId, this.iframeStaionCode)
    this.dxDataSelect()
    // 启动监控
    this.toStartWatch()
    this.initOrderDes()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    handleInput(val) {
      console.log('input', val)
    },
    handleBlur(val) {
      console.log('blur', val)
    },
    // 关闭窗口
    handleCloseDialog() {
      this.repairDialogVisible = false
      this.manualRepairShow = false
    },
    // 打开返修上线弹窗
    repairRequest() {
      this.manualRepairShow = true
      this.repairDialogVisible = true
    },
    // 确定返修
    repairConfirm(repairBarCode, stationIndex) {
      var sendJson = {}
      var rowJson = []
      // 模组码
      var newRow = {
        TagKey: 'OP1070/MesRecipe/MesR_ModularBlockCode',
        TagValue: repairBarCode
      }
      rowJson.push(newRow)
      // 返修开始位置
      newRow = {
        TagKey: 'OP1070/MesRecipe/MesR_ModuleStationRecord1',
        TagValue: stationIndex
      }
      rowJson.push(newRow)
      // 返修标识
      newRow = {
        TagKey: 'OP1070/MesRecipe/MesR_Module_RepairState',
        TagValue: '2'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/OP1070'
      this.sendMessage(topic, sendStr)
      this.$message({
        message: '写入到PLC成功!!!',
        type: 'success'
      })
      this.repairDialogVisible = false
      this.manualRepairShow = false
    },
    // 接受到返修扫描条码
    repairScan(repairBarCode) {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_code: this.iframeStaionCode,
        repair_barcode: repairBarCode
      }
      mesGxMzManualRepairSelect(query)
        .then(res => {
          if (res.code !== 0 && res.count < 0) {
            this.$message({
              message: res.error,
              type: 'error'
            })
            this.$refs.manualRepair.repairScan(repairBarCode, '', false)
            return
          }
          var mz_barcode = res.data[0].mz_barcode
          this.$refs.manualRepair.repairScan(repairBarCode, mz_barcode, true)
          this.$message({
            message: '返修查询成功!!!',
            type: 'success'
          })
        })
        .catch(() => {
          this.$message({
            message: '根据返修条码查询异常',
            type: 'error'
          })
          this.$refs.manualRepair.repairScan(repairBarCode, '', false)
        })
    },

    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellId = result.cell_id
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          if (process.env.NODE_ENV === 'development') {
            this.connectUrl = 'ws://127.0.0.1:8083/mqtt'
          } else {
            this.connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          }
          console.log('拼接URL：' + this.connectUrl)

          // Tag点位集合
          var newClientTagGroupList = []
          var newClientTagList = ['OP1070/MesStatus/MesS_PalletRequest', 'OP1070/PlcStatus/PlcS_EmptyRequestSignal', 'OP1071/BarStatus/BarS_GetBarCodeResult']

          // mqtt连接
          this.clientMqtt = mqtt.connect(this.connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // var topic_clientStatus = 'SCADA_STATUS/OP1010'
            // 获取Tag值
            if (newClientTagList.length > 0) {
              this.GetTagValue(newClientTagList)
              // MesSleep(100);
            }
            // 订阅Tag组
            if (newClientTagList.length > 0) {
              for (var i = 0; i < newClientTagList.length; i++) {
                var tagTopicArray = newClientTagList[i].toString().split('/')
                var client_code = tagTopicArray[0]
                var tag_group_code = tagTopicArray[1]

                var clientGroupMultyKey = 'SCADA_CHANGE/' + client_code + '/' + tag_group_code
                if (newClientTagGroupList.indexOf(clientGroupMultyKey) < 0) {
                  newClientTagGroupList.push(clientGroupMultyKey)
                  this.topicSubscribe(clientGroupMultyKey)
                }
              }
            }

            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())
            // const res = JSON.parse(message.toString())
            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, error => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // this.clientMqtt.disconnect()
      // this.clientMqtt = null
      this.clientMqtt.end()
      this.mqttConnStatus = false
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < newTagList.length; i++) {
        var readTag = {}
        readTag.tag_key = newTagList[i].toString()
        readTagArray.push(readTag)
      }
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
      }
      console.log('环境process.env.NODE_ENV：' + process.env.NODE_ENV)
      console.log('调用接口：' + path)

      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');

              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()

                  if (tagKey === 'OP1070/PlcStatus/PlcS_EmptyRequestSignal') {
                    this.allowScan = tagValue
                    if (tagValue === '1') {
                      this.plcRequestFlag = 'N'
                      this.getFinishAndPlanCount()
                      this.dxDataSelect()
                    }
                  } else if (tagKey === 'OP1071/BarStatus/BarS_GetBarCodeResult') {
                    this.scantwo = tagValue
                  } else if (tagKey === 'OP1070/MesStatus/MesS_PalletRequest') {
                    this.requestPallet = tagValue
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      console.log('MQTT收到来自', channel, '的消息', message.toString())
      // var jsonData = JSON.parse(message)
      // var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        var TagKey = jsonData.TagKey
        // var TagCode = jsonData.TagCode
        // var TagOldValue = jsonData.TagOldValue
        var TagNewValue = jsonData.TagNewValue
        if (TagKey === 'OP1070/PlcStatus/PlcS_EmptyRequestSignal') {
          this.allowScan = TagNewValue
          if (TagNewValue === '1') {
            this.plcRequestFlag = 'Y'
            this.getFinishAndPlanCount()
            this.dxDataSelect()
          }
        } else if (TagKey === 'OP1071/BarStatus/BarS_GetBarCodeResult') {
          if (this.manualRepairShow) {
            this.repairScan(TagNewValue)
          } else {
            this.plcRequestFlag = 'N'
            this.scantwo = TagNewValue
            this.toGxDxOnlineCheck()
          }
        } else if (TagKey === 'OP1070/MesStatus/MesS_PalletRequest') {
          this.requestPallet = TagNewValue
        }
      }
    },

    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate() + ' ' + dt.getHours() + ':' + dt.getMinutes() + ':' + dt.getSeconds()
      }
    },
    // handleClick(tab, event) {
    //         console.log(tab, event);
    //       },
    initOrderDes() {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_id: this.iframeStaionId
      }
      chooseOrderInfo(query)
        .then(res => {
          console.log(res)
          console.log(res.data[0].mo_flag)

          if (res.data[0].mo_flag > 0) {
            this.templateSelection = res.data[0].mo_id
            var initshowQuery = {
              userName: Cookies.get('userName'),
              prod_line_id: this.iframeprodLineId,
              station_id: this.iframeStaionId
            }
            showOrderInfo(initshowQuery)
              .then(res => {
                const result = JSON.parse(res.result)
                console.log(result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
                }
                // this.recipeTableData = result.mz_list
                this.batteriesTableData = result.mzd_list
                this.dxbarList = result.dxbar_list
                this.limitList = result.limit_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          } else {
            var initshowQueryone = {
              userName: Cookies.get('userName'),
              prod_line_id: this.iframeprodLineId,
              station_id: this.iframeStaionId
            }
            showOrderInfo(initshowQueryone)
              .then(res => {
                const result = JSON.parse(res.result)
                console.log(result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
                }
                this.batteriesTableData = result.mzd_list
                this.dxbarList = result.dxbar_list
                this.limitList = result.limit_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          }
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    getOrder() {
      // var $this = this
      this.chooseOrder = true
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_id: this.iframeStaionId
      }
      chooseOrderInfo(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            this.$message({
              message: '选择订单异常',
              type: 'error'
            })
            return
          }
          this.radioArr = res.data
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    // 单选---选择订单
    singleElection(row) {
      console.log(row)
      this.chooseOrder = false
      this.templateSelection = row.mo_id
      console.log(this.templateSelection)
      this.choose_make_order = row.make_order
      this.choose_mo_id = row.mo_id

      console.log(this.choose_make_order)
      var getQuery = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId,
        make_order: this.choose_make_order,
        station_code: this.iframeStaionCode,
        mo_id: this.choose_mo_id
      }
      getOrderInfo(getQuery)
        .then(() => {
          var showQuery = {
            userName: Cookies.get('userName'),
            station_id: this.iframeStaionId
          }
          showOrderInfo(showQuery)
            .then(res => {
              const result = JSON.parse(res.result)
              console.log(result)
              if (result.mo_list.length === 0) {
                this.$message({
                  message: '当前订单数据为空',
                  type: 'warning'
                })
                this.currentOrderInfo.make_order = ''
                this.currentOrderInfo.mo_plan_count = ''
                this.currentOrderInfo.small_model_type = ''
                this.currentOrderInfo.mo_finish_count = ''
                this.currentOrderInfo.recipe = ''
                this.currentOrderInfo.mo_scrap_rate = ''
                this.currentOrderInfo.plan_start_time = ''
                this.currentOrderInfo.plan_end_time = ''
              } else {
                this.currentOrderInfo.make_order = result.mo_list[0].make_order
                this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                this.currentOrderInfo.recipe = result.mo_list[0].recipe
                this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
              }
              this.batteriesTableData = result.mzd_list
              this.dxbarList = result.dxbar_list
              this.limitList = result.limit_list
            })
            .catch(() => {
              this.$message({
                message: '展示信息查询异常',
                type: 'error'
              })
            })
        })
        .catch(() => {
          this.$message({
            message: '获取信息查询异常',
            type: 'error'
          })
        })
    },
    toGxDxOnlineCheck() {
      this.plcRequestFlag = 'N'
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_code: this.iframeStaionCode,
        dx_barcode: this.scantwo,
        dx_index: this.dxIndex
      }
      console.log(this.iframeprodLineId, '11111111111')
      mesGxMzManualDxCheck(query)
        .then(res => {
          this.dxIndex = ''
          this.checked = false
          this.dxScanFinish = false
          console.log(res)
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            if (res.result === 'Y') {
              // 扫描完成
              this.dxScanFinish = true
              this.$message({
                message: '电芯已全部扫描完成',
                type: 'success',
                duration: 5000
              })
            }
          }
          this.dxDataSelect()
          this.getFinishAndPlanCount()
        })
        .catch(() => {
          this.$message({
            message: '电芯上线校验异常',
            type: 'error'
          })
        })
    },
    dxDataSelect() {
      this.dxData = []
      var query = {
        prod_line_id: this.iframeprodLineId,
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        plc_request_flag: this.plcRequestFlag
      }
      mesGxMzManualSelect(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
          if (res.count > 0) {
            this.dxData = res.data
          }
        })
        .catch(() => {
          this.$message({
            message: '查询电芯数据异常',
            type: 'error'
          })
        })
    },
    ManualDxScan() {
      if (this.scantwo === '') {
        this.$message({
          message: '输入电芯条码不能为空',
          type: 'warning'
        })
        return
      }
      this.toGxDxOnlineCheck()
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
            // console.log(inputDom.type, i);
          }
        }
      }
    },
    // 请求一个空托盘
    handleRequestPallet() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'OP1070/MesStatus/MesS_PalletRequest',
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/OP1070'
      this.sendMessage(topic, sendStr)
    },
    // 获取完成数量与计划数量
    getFinishAndPlanCount() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        plc_request_flag: this.plcRequestFlag
      }
      mesGxMzManualMzInfoSelect(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
          const resultInfo = res.result.split(',')
          this.mzBarcode = resultInfo[0]
          this.finishCountAndPlanCount = resultInfo[1]
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    handleUpdDx(val) {
      this.dxIndex = val
      if (!val) {
        this.dxIndex = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  height: calc(100vh - 80px);
}
.orderButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 15px;
  button {
    margin-left: 0 !important;
  }
  .buttonone {
    width: 90px;
    height: 30px;
    margin-top: -10px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonone:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .buttonTwo {
    margin-top: 5px !important;
    width: 90px;
    height: 30px;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonTwo:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .btnRequestPallet1{
    width: 100px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #57EF99, #13ce66);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #57EF99, -1px 1px 1px 1px #13ce66, -1px 3px 18px 0px #13ce66;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .btnRequestPallet1:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #57EF99, -1px 1px 1px 0.5px #13ce66, -1px 4px 10px 1px #13ce66;
  }
  .btnRequestPallet0{
    width: 100px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .btnRequestPallet0:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #AAAAAA, -1px 1px 1px 0.5px #AAAAAA, -1px 4px 10px 1px #AAAAAA;
  }
}
// .wrapDes {
//   margin-top: 30px;
// }
.stepStyle {
  padding: 10px 0;
  padding-bottom: 30px;
}
.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
}
.el-menu {
  display: flex;
  justify-content: space-between;
  overflow: auto;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: #ffffff !important;
}
::v-deep .el-menu--horizontal > .el-menu-item {
  flex: 1;
  text-align: center;
  margin: 0 5px;
}
.flagActive {
  background-color: #e8efff;
}
.orderrecipe {
  display: flex;
  align-items: center;
}
.cardFirst {
  margin-bottom: 10px;
}
.statuStyle {

  justify-content: space-around;
  p {
    display: flex;
    align-items: center;
    color: #333333;
    margin: 0;
    margin-bottom: 10px;
    span {
      display: flex;
      align-items: center;
    }
    .commonsatu {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: block;
    }
    .statuOne {
      background-color: #13ce66;
    }
    .statuZero {
      background-color: #cccccc;
    }
    .statuTwo {
      background-color: #ff4949;
    }
    .statuSecond {
      background-color: #cccccc;
    }
  }
}
::v-deep .el-card__header {
  text-align: center;
  background: #79a0f1;
  color: #ffffff;
  padding: 10px 0;
}
::v-deep .el-tabs--border-card > .el-tabs__header {
  background-color: #ffffff;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333 !important;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #ffffff !important;
    background: #79a0f1;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #333333;
}
::v-deep .el-table th {
  background-color: #e8efff !important;
}
::v-deep .el-table__body tr.current-row > td {
  background-color: #79a0f1 !important;
  color: #ffffff;
}
::v-deep .el-input__inner{
  padding-right: 40px;
}
.keyboard-mask{
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 99;
    left: 0;
    right: 0;
}
.wrapimin{
  position: relative;
  width: 100%;
  img{
    position: absolute;
    right: 7px;
    top: -3px;
    width: 45px;
    z-index: 2;
  }
}
</style>
