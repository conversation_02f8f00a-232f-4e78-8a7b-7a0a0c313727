<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="警报代码">
                <el-input v-model="query.alarm_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="警报代码" width="300" align="center" prop="code" />
            <el-table-column :show-overflow-tooltip="true" label="警报描述" width="300" align="center" prop="desc" />
            <el-table-column :show-overflow-tooltip="true" label="所属设备" width="300" align="center" prop="clientId">
              <template slot-scope="scope">
                {{ getClientDesc(scope.row.clientId) }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
        <el-form-item label="警报代码" prop="code" display:none>
          <el-input v-model="form.code" />
        </el-form-item>
        <el-form-item label="警报描述" prop="desc" display:none>
          <el-input v-model="form.desc" />
        </el-form-item>
        <el-form-item label="所属设备" prop="clientId" display:none>
          <el-select v-model="form.clientId" clearable filterable>
            <el-option v-for="(item, i) in clients" :key="i" :label="item.desc" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import api from '@/api/dcs/project/xg/alarm'
import scadaClientApi from '@/api/core/scada/client/index'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  id: '',
  code: '',
  desc: '',
  clientId: null,
  enable_flag: 'Y'
}
export default {
  name: 'DCS_ALARM',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.dcsAlarm'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'rcs_flow_main:add'],
        edit: ['admin', 'rcs_flow_main:edit'],
        del: ['admin', 'rcs_flow_main:del'],
        down: ['admin', 'rcs_flow_main:down']
      },
      clients: [],
      rules: {
        code: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        desc: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // clientId: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
    this.loadClients()
  },
  methods: {
    loadClients() {
      scadaClientApi.sel().then(res => {
        if (res.code === 0) {
          this.clients = res.data
        }
      })
    },
    getClientDesc(id) {
      const c = this.clients.find(item => item.id === id)
      return c ? c.desc : ''
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              last_update_by: Cookies.get('userName'),
              id: data.id,
              clientId: data.clientId,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
