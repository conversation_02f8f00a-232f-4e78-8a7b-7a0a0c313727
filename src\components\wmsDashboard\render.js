
import { getIconPathData } from "@/components/wmsDashboard/icon";
import { roundTo20 } from "@/utils/math";
function render(g, node, isSelected,that) {
  if(node.type === 'kw'){
    let body = g.append("rect").attr("class", "body").attr("rx", 2);
        body
        .style("width", node.width + "px")
        .style("stroke-width", (node.stock_count && node.alarm_flag == 'N') ? (node.strokeWidth + "px") : '0px')
        .attr("x", node.x)
        .attr("y", node.y)
        //.style("fill", node.color)
        .style("height", roundTo20(node.height) + "px")
        .attr("stroke", node.borderColor);
        let iconInfo = getIconPathData().filter((item) => item.id === "kuwei")[0];
        g.append("image")
          .attr("x", node.x + 2)
          .attr("preserveAspectRatio", "none")
          .attr("y", node.y + 2)
          .style("height", node.height - 4 + "px")
          .style("width", node.width - 4 + "px")
          .attr("href", iconInfo.path);
        //库位号
        g.append("text")
          .attr("x", node.x + 5)
          .attr("y", node.y + 33)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => node.stock_code)
          .style("font-size", "20px")
          .style("font-weight", "bold")
        //库存
        g.append("text")
          .attr("x", node.x + 45)
          .attr("y", node.y + 20)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => "库存:")
          .style("font-size", "12px")
          .style("font-weight", "bold")
        g.append("text")
          .attr("x", node.x + 78)
          .attr("y", node.y + 20)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => node.stock_count)
          .style("font-size", "12px")
          .style("font-weight", "bold")
          // 材质
        g.append("text")
          .attr("x", node.x + 100)
          .attr("y", node.y + 20)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => "材质:")
          .style("font-size", "12px")
          .style("font-weight", "bold")
        g.append("text")
          .attr("x", node.x + 133)
          .attr("y", node.y + 20)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => node.m_texture)
          .style("font-size", "12px")
          .style("font-weight", "bold")
        //型号
        // g.append("text")
        //   .attr("x", node.x + 55)
        //   .attr("y", node.y + 40)
        //   .attr("class", "unselectable")
        //   .attr("fill", "#FFFFFF")
        //   .text(() => "型号:")
        //   .style("font-size", "12px")
        //   .style("font-weight", "bold");
        // g.append("text")
        //   .attr("x", node.x + 88)
        //   .attr("y", node.y + 40)
        //   .attr("class", "unselectable")
        //   .attr("fill", "#FFFFFF")
        //   .text(() => node.model_type)
        //   .style("font-size", "12px")
        //   .style("font-weight", "bold");
        // 板厚
        g.append("text")
          .attr("x", node.x + 45)
          .attr("y", node.y + 40)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => "板厚:")
          .style("font-size", "12px")
          .style("font-weight", "bold")
        g.append("text")
          .attr("x", node.x + 78)
          .attr("y", node.y + 40)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => node.m_height)
          .style("font-size", "12px")
          .style("font-weight", "bold")
        // 尺寸
        g.append("text")
          .attr("x", node.x + 100)
          .attr("y", node.y + 40)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => "尺寸:")
          .style("font-size", "12px")
          .style("font-weight", "bold")
        g.append("text")
          .attr("x", node.x + 133)
          .attr("y", node.y + 40)
          .attr("class", "unselectable")
          .attr("fill", (node.stock_count && node.alarm_flag == 'N') ? '#FF0000' : '#FFFFFF')
          .text(() => (node.m_length && node.m_width) ? node.m_length + "x" + node.m_width : '' )
          .style("font-size", "12px")
          .style("font-weight", "bold");

  }else if (node.type == "rgkw") {
    let body = g.append("rect").attr("class", "body").attr("rx", 2);
        body
        .style("width", node.width + "px")
        .style("stroke-width", node.strokeWidth + "px")
        .attr("x", node.x)
        .attr("y", node.y)
        //.style("fill", node.color)
        .style("height", roundTo20(node.height) + "px")
        .attr("stroke", node.borderColor);
        let iconInfo = getIconPathData().filter((item) => item.id === "kuwei")[0];
        g.append("image")
          .attr("x", node.x + 2)
          .attr("preserveAspectRatio", "none")
          .attr("y", node.y + 2)
          .style("height", node.height - 4 + "px")
          .style("width", node.width - 4 + "px")
          .attr("href", iconInfo.path);
        //人工库位号
        g.append("text")
          .attr("x", node.x + 10)
          .attr("y", node.y + 33)
          .attr("class", "unselectable")
          .attr("fill", "#FFFFFF")
          .text(() => node.stock_code)
          .style("font-size", "20px")
          .style("font-weight", "bold");
        g.append("text")
          .attr("x", node.x + 68)
          .attr("y", node.y + 32)
          .attr("class", "unselectable")
          .attr("fill", "#FFFFFF")
          .text(() => node.describe)
          .style("font-size", "20px")
          .style("font-weight", "bold");
  }else if (node.type == "rkgd") {
    let body = g.append("rect").attr("class", "body").attr("rx", 2);
    body
      .style("width", node.width + "px")
      .style("stroke-width", node.strokeWidth + "px")
      .attr("x", node.x)
      .attr("y", node.y)
      .style("fill", "#7F756B")
      .style("height", roundTo20(node.height) + "px")
      .attr("stroke", node.borderColor);
    let iconInfo = getIconPathData().filter((item) => item.id === "gundao")[0];
    g.append("image")
      .attr("x", node.x)
      .attr("preserveAspectRatio", "none")
      .attr("y", node.y - 15)
      .style("height", node.height + 30 + "px")
      .style("width", node.width + "px")
      .attr("href", iconInfo.path);
  }else if (node.type == "bigCar") {
    let iconInfo = getIconPathData().filter((item) => item.id === "bigCar")[0];
    g.append("image")
      .attr("x", node.x)
      .attr("preserveAspectRatio", "none")
      .attr("y", node.y)
      .style("height", node.height + "px")
      .style("width", node.width + "px")
      .attr("href", iconInfo.path);
  } else if (node.type == "smallCar") {
    let iconInfo = getIconPathData().filter((item) => item.id === "smallCar")[0];
    g.append("image")
      .attr("x", node.x)
      .attr("y", node.y)
      .style("height", node.height + "px")
      .style("width", node.width + "px")
      .attr("href", iconInfo.path);
  }else if(node.type === "paowanji"){
    let iconInfo = getIconPathData().filter((item) => item.id === "paowanji")[0];
    g.append("image")
      .attr("x", node.x)
      .attr("y", node.y)
      .style("height", node.height + "px")
      .style("width", node.width + "px")
      .attr("href", iconInfo.path);
  }
}
export default render
