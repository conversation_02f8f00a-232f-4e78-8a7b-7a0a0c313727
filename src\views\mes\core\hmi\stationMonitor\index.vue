<template>
  <div class="mesContainer">
    <el-card>
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <table class="table3 headTable" style="margin-top: 0">
            <tbody>
              <tr class="trtitle">
                <td class="tdName"><span>工作编号</span></td>
                <td style="width: 220px">
                  <span>{{ stationMis.serial_num }}</span>
                </td>
                <!-- <td class="tdName"><span>订单号</span></td> -->
                <td :class="[orderBtnStatus ? 'scanGray' : 'scan']">
                  <el-button
                    type="warning"
                    :disabled="orderBtnStatus"
                    @click="getOrder"
                  >订 单 选 择</el-button>
                </td>
                <td style="width: 140px">
                  <span> {{ stationMis.make_order }}</span>
                </td>
                <td class="tdName"><span>订单完工/计划</span></td>
                <td>
                  <span>{{ stationMis.mo_finish_count }}/{{
                    stationMis.mo_plan_count
                  }}</span>
                </td>
                <td class="tdName"><span>机型代码</span></td>
                <td style="width: 74px">
                  <span>{{ stationMis.model_code }}</span>
                </td>
                <td class="tdName"><span>工作标识</span></td>
                <td style="width: 64px">
                  <span style="white-space: nowrap">{{
                    stationMis.serial_status
                  }}</span>
                </td>
              </tr>
              <tr class="trtitle">
                <td class="tdName"><span>上线时间</span></td>
                <td>
                  <span>{{ stationMis.online_time }}</span>
                </td>
                <td class="tdName"><span>机型</span></td>
                <td style="width: 200px">
                  <span>{{ stationMis.small_model_type }}</span>
                </td>
                <td class="tdName"><span>在制时间</span></td>
                <td>
                  <span>{{ stationMis.serial_stay_time }}</span>
                </td>
                <td class="tdName"><span>实际/理想节拍</span></td>
                <td>
                  <span>{{ stationMis.actual_beats }}/{{
                    stationMis.ideal_beats
                  }}</span>
                </td>
                <td class="tdName"><span>当前节拍</span></td>
                <td>
                  <span>{{ stationMis.current_beats }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </el-col>
      </el-row>
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="18" style="padding-right: 2px">
          <table class="table3" style="margin-top: 0">
            <tbody>
              <tr class="trtitle">
                <td class="tdName"><span>流程步骤</span></td>
                <td style="width: 42.6%">
                  <span>{{ flowInfo.step }}</span>
                </td>
                <td class="tdName"><span>步骤信息</span></td>
                <td style="width: 42.6%">
                  <span>{{ flowInfo.msg }}</span>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="itemtableTwo">
            <table class="table3 nomarginTop tablemarginR">
              <tbody>
                <tr class="trtitle">
                  <td>
                    <div class="tdScan">
                      <div
                        v-for="item in pdureList"
                        :key="item.proceduce_id"
                        class="wrappstyle"
                      >
                        <p>
                          <span
                            :class="proceduceStatus(item)"
                          /><span>{{ item.proceduce_des }}</span>
                        </p>
                      </div>
                    </div>
                  </td>
                  <!-- <td style="width:350px"><el-input v-model="barCode" /></td>
                  <td class="scan"><el-button type="warning" @click="handleScan">扫描</el-button></td> -->
                </tr>
              </tbody>
            </table>
            <table class="table3 nomarginTop">
              <tbody>
                <tr class="trtitle">
                  <td><el-input v-model="barCode" /></td>
                  <td class="scan">
                    <el-button
                      type="warning"
                      @click="handleScan"
                    >扫 描</el-button>
                  </td>
                  <td v-if="gxCode === 'SH'" class="scan">
                    <el-button
                      type="warning"
                      @click="handleOffline"
                    >强制下线</el-button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="tableImg">
            <div :class="backgroundClass">
              <sop
                ref="sop"
                :proceduce_id="currentProceduceId"
                :quality_data="qualityData"
                :gx-code="gxCode"
              />
            </div>
            <div class="wraptable">
              <el-table
                border
                :data="materialData"
                style="width: 100%"
                height="164"
                :cell-style="materialTableRowClassName"
              >
                <el-table-column
                  prop="material_code"
                  label="物料号"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column prop="material_des" label="物料描述" width="70" />
                <el-table-column prop="usage" label="用量" width="60" />
                <el-table-column
                  prop="exact_barcode"
                  label="物料条码"
                  width="270"
                  show-overflow-tooltip
                />
                <el-table-column prop="lable_barcode" label="批次" width="60" />
                <el-table-column prop="supplier_des" label="供应商" />
              </el-table>
              <table class="table3">
                <tbody>
                  <tr class="trtitle">
                    <td class="orderwd">
                      <div>
                        <span>当前序号：{{ currentQuality.tag_col_order }}</span>
                        <span>套简号：{{ currentQuality.bolt_code }}</span>
                        <span>程序号：{{ currentQuality.progm_num }}</span>
                      </div>
                    </td>
                    <td class="scan scannoborder">
                      <el-button
                        type="warning"
                        @click="openManualPage"
                      >手 工 画 面</el-button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <el-table
                :data="qualityData"
                style="width: 100%"
                height="162"
                :cell-style="qualityTableRowClassName"
              >
                <el-table-column prop="tag_col_order" width="60" label="序号" />
                <el-table-column prop="quality_for" label="测量对象" />
                <el-table-column prop="tag_des" label="项目" />
                <el-table-column prop="tag_value" label="采集值" />
                <el-table-column prop="tag_uom" label="单位" width="60" />
                <el-table-column prop="down_limit" label="上下限">
                  <template slot-scope="scope">
                    {{ scope.row.down_limit }}/{{ scope.row.upper_limit }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <table class="table3 daynoborder">
            <tbody>
              <tr class="trtitle">
                <td class="tdName riyueactive"><span>日累计</span></td>
                <td class="tdName"><span>0点-1点</span></td>
                <td class="tdName"><span>1点-2点</span></td>
                <td class="tdName"><span>2点-3点</span></td>
                <td class="tdName"><span>3点-4点</span></td>
                <td class="tdName"><span>4点-5点</span></td>
                <td class="tdName"><span>5点-6点</span></td>
                <td class="tdName"><span>6点-7点</span></td>
                <td class="tdName"><span>7点-8点</span></td>
                <td class="tdName"><span>8点-9点</span></td>
                <td class="tdName"><span>9点-10点</span></td>
                <td class="tdName"><span>10点-11点</span></td>
                <td class="tdName tdEnd"><span>11点-12点</span></td>
              </tr>
              <tr>
                <td>
                  <span>{{ productionStatistics.dayTotal || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count00 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count01 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count02 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count03 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count04 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count05 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count06 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count07 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count08 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count09 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count10 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count11 || 0 }}</span>
                </td>
              </tr>
            </tbody>
          </table>
          <table class="table3 daynoborder">
            <tbody>
              <tr class="trtitle">
                <td class="tdName riyueactive"><span>月累计</span></td>
                <td class="tdName"><span>12点-13点</span></td>
                <td class="tdName"><span>13点-14点</span></td>
                <td class="tdName"><span>14点-15点</span></td>
                <td class="tdName"><span>15点-16点</span></td>
                <td class="tdName"><span>16点-17点</span></td>
                <td class="tdName"><span>17点-18点</span></td>
                <td class="tdName"><span>18点-19点</span></td>
                <td class="tdName"><span>19点-20点</span></td>
                <td class="tdName"><span>20点-21点</span></td>
                <td class="tdName"><span>21点-22点</span></td>
                <td class="tdName"><span>22点-23点</span></td>
                <td class="tdName tdEnd"><span>23点-24点</span></td>
              </tr>
              <tr>
                <td>
                  <span>{{ productionStatistics.monthTotal || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count12 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count13 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count14 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count15 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count16 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count17 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count18 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count19 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count20 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count21 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count22 || 0 }}</span>
                </td>
                <td>
                  <span>{{ productionStatistics.count23 || 0 }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </el-col>
        <el-col :span="6" style="padding-left: 2px">
          <div class="wrapOkstyle">
            <div class="wrapOkbtn">
              <template
                v-if="
                  stationFlowData.length && stationFlowData.filter((item) => item.quality_sign === 'NG')
                    .length > 0
                "
              >
                <div class="ng">NG</div>
              </template>
              <template v-else>
                <div class="ok">OK</div>
              </template>
            </div>
            <el-table
              :data="stationFlowData"
              style="width: 100%; margin-top: 5px"
              height="277"
              :cell-style="tableRowClassName"
            >
              <el-table-column prop="station_code" width="80" label="工位号" />
              <el-table-column
                prop="station_des"
                label="工位描述"
                show-overflow-tooltip
              />
              <el-table-column label="追溯" width="80">
                <template slot-scope="scope">
                  <el-button
                    class="zhuisubtn"
                    type="primary"
                    @click="openDetail(scope.row.station_flow_id)"
                  >查看</el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-dialog
              title="过站质量与物料数据"
              :visible.sync="stationFlowDialogVisible"
              width="80%"
              top="10px"
              :close-on-click-modal="false"
              @opened="handleOpenedDialog"
            >
              <el-table
                :data="stationFlowMaterialData"
                style="width: 100%; margin-top: 5px"
                height="300"
              >
                <el-table-column
                  prop="serial_num"
                  width="80"
                  label="工件编号"
                />
                <el-table-column
                  prop="material_code"
                  min-width="80"
                  label="物料编码"
                />
                <el-table-column
                  prop="material_des"
                  min-width="80"
                  label="物料描述"
                />
                <el-table-column prop="use_count" width="80" label="使用数量" />
                <el-table-column prop="material_uom" width="80" label="单位" />
                <el-table-column
                  prop="material_attr"
                  width="80"
                  label="物料属性"
                />
                <el-table-column
                  prop="material_batch"
                  width="80"
                  label="批次号"
                />
                <el-table-column
                  prop="material_supplier"
                  width="80"
                  label="供应商代码"
                />
                <el-table-column
                  prop="supplier_des"
                  width="80"
                  label="供应商描述"
                />
                <el-table-column
                  prop="lable_barcode"
                  width="80"
                  label="标签条码"
                />
                <el-table-column
                  prop="exact_barcode"
                  width="80"
                  label="精准二维码"
                />
                <el-table-column
                  prop="main_material_flag"
                  width="80"
                  label="是否主物料"
                />
                <el-table-column
                  prop="verify_flag"
                  width="100"
                  label="精准追溯验证"
                />
                <el-table-column
                  prop="batch_flag"
                  width="80"
                  label="批次验证"
                />
                <el-table-column
                  prop="bom_version"
                  width="80"
                  label="物料版本号"
                />
                <el-table-column
                  prop="trace_d_time"
                  width="80"
                  label="追溯时间"
                />
              </el-table>
              <el-table
                :data="stationFlowQualityData"
                style="width: 100%; margin-top: 5px"
                height="300"
              >
                <el-table-column
                  prop="serial_num"
                  width="80"
                  label="工件编号"
                />
                <el-table-column
                  prop="group_order"
                  min-width="80"
                  label="组号"
                />
                <el-table-column
                  prop="group_name"
                  min-width="80"
                  label="组描述"
                />
                <el-table-column
                  prop="tag_col_order"
                  width="80"
                  label="列位置"
                />
                <el-table-column prop="tag_id" width="80" label="Tag Id" />
                <el-table-column
                  prop="tag_col_inner_order"
                  width="100"
                  label="列位置内排序"
                />
                <el-table-column
                  prop="quality_for"
                  width="80"
                  label="测量对象"
                />
                <el-table-column
                  prop="tag_des"
                  width="100"
                  label="采集项目名称"
                />
                <el-table-column
                  prop="tag_uom"
                  width="100"
                  label="采集项目单位"
                />
                <el-table-column
                  prop="theory_value"
                  width="80"
                  label="标准值"
                />
                <el-table-column prop="down_limit" width="80" label="下限值" />
                <el-table-column prop="upper_limit" width="80" label="上限值" />
                <el-table-column prop="bolt_code" width="80" label="套筒号" />
                <el-table-column prop="progm_num" width="80" label="程序号" />
                <el-table-column prop="tag_value" width="80" label="采集值" />
                <el-table-column
                  prop="quality_d_sign"
                  width="120"
                  label="子合格标志(设备)"
                />
                <el-table-column
                  prop="mes_quality_d_sign"
                  width="120"
                  label="子合格标志(MES)"
                />
                <el-table-column
                  prop="trace_d_time"
                  width="80"
                  label="追溯时间"
                />
              </el-table>
            </el-dialog>
            <div class="wrapWhole">
              <div class="wrapWholeone">
                <div
                  v-for="(item, index) in stationStatusList"
                  :key="index"
                  class="wrappstyle marginten"
                >
                  <template v-if="item.tag_value === item.green_value">
                    <p>
                      <span class="wholeline wholelinenormal" /><span>{{
                        item.tag_des
                      }}</span>
                    </p>
                  </template>
                  <template v-else-if="item.tag_value === item.red_value">
                    <p>
                      <span class="wholeline wholelineerror" /><span>{{
                        item.tag_des
                      }}</span>
                    </p>
                  </template>
                  <template v-else>
                    <p>
                      <span class="wholeline wholelinegray" /><span>{{
                        item.tag_des
                      }}</span>
                    </p>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-drawer
      title="选择订单"
      :visible.sync="chooseOrder"
      direction="rtl"
      size="95%"
    >
      <el-table
        border
        :data="radioArr"
        style="width: 100%"
        height="500"
        highlight-current-row
        :row-class-name="tableRowClassName1"
        @header-dragend="crud.tableHeaderDragend()"
        @row-click="singleElection"
      >
        <el-table-column align="center" width="55" label="选择">
          <template slot-scope="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio
              v-model="templateSelection"
              class="radio"
              :label="scope.row.mo_id"
            >&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="mo_from"
          label="订单来源"
          width="80"
        />
        <el-table-column
          align="center"
          prop="make_order"
          label="订单号"
          width="100"
        />
        <el-table-column
          align="center"
          prop="product_batch"
          label="订单批号"
          width="80"
        />
        <el-table-column
          align="center"
          prop="small_model_type"
          label="产品型号"
          width="100"
        />
        <el-table-column
          align="center"
          prop="mo_plan_count"
          label="订单计划数量"
        />
        <el-table-column
          align="center"
          prop="mo_finish_count"
          label="实际完成数量"
        />
        <el-table-column
          align="center"
          prop="mo_online_count"
          label="实际上线数量"
        />
        <!-- <el-table-column  align="center" prop="mo_status" label="订单状态">
          <template slot-scope="scope">
            {{ dict.label.PLAN_MO_STATUS[scope.row.mo_status] }}
          </template>
        </el-table-column> -->
        <el-table-column
          align="center"
          prop="mo_order_by"
          label="订单排序"
          width="80"
        />
        <el-table-column
          align="center"
          prop="plan_start_time"
          label="计划开始时间"
          :formatter="formatDate"
        />
        <el-table-column
          align="center"
          prop="plan_end_time"
          label="计划结束时间"
          :formatter="formatDate"
        />
        <el-table-column
          align="center"
          prop="start_date"
          label="订单启动时间"
          :formatter="formatDate"
        />
        <el-table-column
          align="center"
          prop="finish_date"
          label="订单完成时间"
          :formatter="formatDate"
        />
        <el-table-column
          align="center"
          prop="mo_custom_code"
          label="总成编号"
        />
        <!-- Table单条操作-->
        <el-table-column
          v-if="isCanClose"
          label="操作"
          align="right"
          width="70px"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click.stop="closeOrder(scope.row)"
            >关闭订单</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
    <el-dialog
      title="手动画面"
      :visible.sync="manualDialogVisible"
      width="750px"
      top="65px"
    >
      <div style="margin-bottom: 10px">
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('shaft_manual_judge')"
        >拧紧结果人工判定</el-button>
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('shaft_manual_repeated_code')"
        >是否重复定扭判定</el-button>
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('manual_ng_pass')"
        >一键NG</el-button>
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('shaft_ok_finish')"
        >拧紧数据人工确认OK完成</el-button>
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('shaft_ng_finish')"
        >拧紧数据人工确认NG完成</el-button>
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('hlp_ok_go')"
        >汇流排强制OK放行</el-button>
        <el-button
          type="primary"
          style="line-height: 30px; font-size: 24px"
          plain
          @click="handleManualEvent('hlp_ng_go')"
        >汇流排强制NG放行</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="msgDialogTitle"
      :visible.sync="mgsDialogVisible"
      width="650px"
      top="65px"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
    >
      <shaftManual
        v-if="shaftManualShow"
        ref="shaftManual"
        @mesShaftManualJudge="mesShaftManualJudge"
      />
    </el-dialog>

    <el-dialog
      :title="msgDialogTitle"
      :visible.sync="mgsDialogVisible2"
      width="650px"
      top="65px"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
    >
      <shaftManualRepeatedCode
        v-if="shaftManualRepeatedCodeShow"
        ref="shaftManualRepeatedCode"
        :tag_key_list="tagKeyList"
        @mesShaftManualRepeatedCode="mesShaftManualRepeatedCode"
      />
    </el-dialog>
    <el-dialog
      :title="msgDialogTitle"
      :visible.sync="mgsDialogVisible3"
      width="650px"
      top="65px"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
    >
      <manualNgPass
        v-if="manualNgPassShow"
        ref="manualNgPass"
        :tag_key_list="tagKeyList"
        @manualNgPassJudge="manualNgPassJudge"
      />
    </el-dialog>

    <!-- .触发人工替换电芯时，页面需要弹框提示电芯NG需要人工替换电芯。然后页面上得显示替换电芯的贴胶类型，和电芯方向，指导人工操作 -->
    <el-dialog title="提示" :visible.sync="PlcS_RequestSignal1Flag" width="30%">
      <span style="font-size: 20px;">电芯NG，请人工替换电芯</span><br>
      <span style="font-size: 20px;">电芯方向：{{ monitorData.PlcR_DX_direction1.value === '1' ? '正极电芯' : monitorData.PlcR_DX_direction1.value === '0' ? '初始' : '负极电芯' }}</span><br>
      <span style="font-size: 20px;">贴胶类型：{{ monitorData.PlcR_DX_Tape_type1.value === '1' ? '气凝胶' : monitorData.PlcR_DX_Tape_type1.value === '0' ? '不贴胶' : '隔片' }}</span><br>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleDxOk('OP1051/MesStatus/MesS_Reserved_1','0','PlcS_RequestSignal1Flag',false)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 弹框提示人工撕胶 -->
    <el-dialog title="提示" :visible.sync="PlcS_Reserved_1Flag" width="30%">
      <span style="font-size: 20px;">请人工撕胶</span><br>
      <span style="font-size: 20px;">电芯方向：{{ monitorData.PlcR_DX_direction1.value === '1' ? '正极电芯' : monitorData.PlcR_DX_direction1.value === '0' ? '初始' : '负极电芯' }}</span><br>
      <span style="font-size: 20px;">贴胶类型：{{ monitorData.PlcR_DX_Tape_type1.value === '1' ? '气凝胶' : monitorData.PlcR_DX_Tape_type1.value === '0' ? '不贴胶' : '隔片' }}</span><br>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleDxOk('OP1051/MesStatus/MesS_Reserved_1','0','PlcS_Reserved_1Flag',false)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderInfo } from '@/api/hmi/orderinfo'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import stationMonitor from '@/api/mes/core/hmi/stationMonitor'
import sop from '@/views/mes/core/hmi/stationMonitor/sop'
import shaftManual from '@/views/mes/core/hmi/stationMonitor/shaftManual'
import shaftManualRepeatedCode from '@/views/mes/core/hmi/stationMonitor/shaftManualRepeatedCode'
import manualNgPass from '@/views/mes/core/hmi/stationMonitor/manualNgPass'
import { editMoStatus } from '@/api/mes/core/apsPlanMo'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'MES_STATION_MONITOR',
  components: { sop, shaftManual, shaftManualRepeatedCode, manualNgPass },
  // 数据字典
  // dicts: ['PLAN_MO_STATUS'],
  // 数据模型
  data() {
    return {
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: '',
      timer5: '',
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      imageClass: 'showImg', // 初始图片URL
      shaftStatus: '',
      PlcS_RequestSignal1Flag: false,
      PlcS_RequestSignal2Flag: false,
      PlcS_Reserved_1Flag: false,
      PlcS_Reserved_2Flag: false,
      stationFlowData: [],
      stationFlowId: 0,
      stationFlowDialogVisible: false,
      stationFlowMaterialData: [],
      stationFlowQualityData: [],
      stationMis: {},
      flowInfo: { step: '', msg: '', code: '' },
      pdureList: [],
      currentProceduceId: '0',
      barCode: '',
      barCodeTagKeyList: [],
      tagKeyList: {},
      materialData: [],
      currentQuality: {},
      qualityData: [],
      productionStatistics: {},
      stationStatusList: [],
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      chooseOrder: false,
      radioArr: [],
      templateSelection: '',
      orderBtnStatus: true,
      manualDialogVisible: false,
      // 监控弹窗相关
      msgDialogTitle: '提示',
      mgsDialogVisible: false,
      mgsDialogVisible2: false,
      mgsDialogVisible3: false,
      shaftManualShow: false,
      shaftManualRepeatedCodeShow: false,
      manualNgPassShow: false,
      isCanClose: false, // 是否有关闭订单权限
      // 监听数据
      monitorData: {
        PlcS_RequestSignal1: { client_code: 'OP1051', group_code: 'PlcStatus', tag_code: 'PlcS_RequestSignal', tag_des: '请求信号', value: '' },
        PlcR_DX_direction1: { client_code: 'OP1051', group_code: 'PlcQuality', tag_code: 'PlcR_DX_direction', tag_des: '当前电芯方向', value: '' },
        PlcR_DX_Tape_type1: { client_code: 'OP1051', group_code: 'PlcQuality', tag_code: 'PlcR_DX_Tape_type', tag_des: '当前电芯贴胶类型', value: '' },
        MesS_Reserved_1: { client_code: 'OP1051', group_code: 'PlcQuality', tag_code: 'MesS_Reserved_1', tag_des: 'MES反馈提示已触发', value: '' },
        PlcS_Reserved_1: { client_code: 'OP1051', group_code: 'PlcStatus', tag_code: 'PlcS_Reserved_2', tag_des: '触发MES替换胶片', value: '' },
        PlcS_RequestSignal2: { client_code: 'OP1052', group_code: 'PlcStatus', tag_code: 'PlcS_RequestSignal', tag_des: '请求信号', value: '' },
        PlcR_DX_direction2: { client_code: 'OP1052', group_code: 'PlcQuality', tag_code: 'PlcR_DX_direction', tag_des: '当前电芯方向', value: '' },
        PlcR_DX_Tape_type2: { client_code: 'OP1052', group_code: 'MesStatus', tag_code: 'PlcR_DX_Tape_type', tag_des: '当前电芯贴胶类型', value: '' },
        MesS_Reserved_2: { client_code: 'OP1052', group_code: 'PlcQuality', tag_code: 'MesS_Reserved_1', tag_des: 'MES反馈提示已触发', value: '' },
        PlcS_Reserved_2: { client_code: 'OP1052', group_code: 'PlcStatus', tag_code: 'PlcS_Reserved_2', tag_des: '触发MES替换胶片', value: '' }
      },
      gxCode: '',
      orderSelectFlag: true
    }
  },
  computed: {
    backgroundClass() {
      return this.imageClass // 返回当前的类名。
    }
  },
  mounted: function() {
    this.timer1 = setInterval(this.getStationMis, 5000)
    this.timer2 = setInterval(this.getStationFlow, 5000)
    this.timer3 = setInterval(this.getPdureMaterialAndQuality, 3000)
    this.timer4 = setInterval(this.getProductionStatistic, 10000)
    this.timer5 = setInterval(this.getFlowTaskData, 3000)
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
    clearInterval(this.timer5)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  created: function() {
    this.currentStation = {
      prod_line_id: this.$route.query.prod_line_id,
      prod_line_code: '',
      prod_line_des: '',
      station_id: this.$route.query.station_id,
      station_code: this.$route.query.station_code,
      station_des: '',
      cell_id: this.$route.query.cell_id
    }
    this.getCellIp()
    this.getStationMis()
    this.getProductionStatistic()
    this.setSelectOrderButtonStatus()
    this.getOrderCloseStation()
  },
  methods: {
    proceduceStatus(item) {
      if (item.proceduce_des === '拧紧螺丝') {
        this.shaftStatus = item.proceduce_status
      }
      return item.proceduce_status === 'FINISH' ? 'wholeline wholelinenormal' : 'wholeline wholelinegray'
    },
    tableRowClassName1({ row, column, rowIndex, columnIndex }) {
      if (row.mo_plan_count == row.mo_online_count) {
        return 'red-row'
      }
    },
    materialTableRowClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'exact_barcode') {
        if (row.verify_finish_flag === 'N') {
          return { background: '#FFE83D', color: '#000', fontWeight: 700 }
        } else {
          return { background: '#50d475', color: '#000', fontWeight: 700 }
        }
      } else if (column.property === 'lable_barcode') {
        if (row.batch_finish_flag === 'N') {
          return { background: '#FFE83D', color: '#000', fontWeight: 700 }
        } else {
          return { background: '#50d475', color: '#000', fontWeight: 700 }
        }
      } else {
        return {}
      }
    },
    qualityTableRowClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'tag_value') {
        if (row.quality_status === 'WAIT') {
          return {}
        } else if (row.quality_status === 'PLAN') {
          return { background: '#FFE83D', color: '#000', fontWeight: 700 }
        } else if (row.quality_status === 'OK') {
          return { background: '#50d475', color: '#000', fontWeight: 700 }
        } else {
          return { background: '#f00', color: '#000', fontWeight: 700 }
        }
      } else {
        return {}
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.quality_sign === 'OK') {
        if (this.shaftStatus === 'FINISH') {
          this.imageClass = 'showOKImg'
        } else {
          this.imageClass = 'showImg'
        }
        return { background: '#50d475', color: '#000', fontWeight: 700 }
      } else if (row.quality_sign === 'NG' || row.quality_sign === 'NG_PASS') {
        if (this.shaftStatus === 'FINISH') {
          this.imageClass = 'showNGImg'
        } else {
          this.imageClass = 'showImg'
        }
        return { background: '#f00', color: '#000', fontWeight: 700 }
      } else {
        return { background: '#FFE83D', color: '#000', fontWeight: 700 }
      }
    },
    // 扫描
    handleScan() {
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code,
        barcode: this.barCode
      }
      // 唐山国轩接口用的是带2的，其他的不带
      var type = ''
      if (this.gxCode === 'TSGX' || this.gxCode === 'JZGX') type = '2'
      if (this.gxCode === 'SH') type = '3'
      stationMonitor
        .mesHmiStationMaterialScan(query, type)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '扫描与验证成功', type: 'success' })
            this.getPdureMaterialAndQuality()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '扫描物料异常', type: 'error' })
        })
      // this.stationFlowData.push({ station_code: 'OP010', station_des: 'xxxxxx', quality_sign: 'NG' })
    },
    handleOffline() {
      if (!this.materialData.some(e => e.batch_finish_flag === 'Y')) {
        this.$message({ type: 'error', message: '请至少扫描一项' })
        return
      }
      this.$confirm(`确定强制下线吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(res => {
        const query = {
          user_name: Cookies.get('userName'),
          station_code: this.currentStation.station_code,
          proceduce_id: +this.currentProceduceId
        }
        console.log(query)
        stationMonitor.MesMeMaterialOffline(query).then(res => {
          if (res.code === '200') {
            console.log(res)
          }
        })
      }).catch(() => {})
    },
    openDetail(stationFlowId) {
      this.stationFlowId = stationFlowId
      this.stationFlowDialogVisible = true
    },
    // 弹窗打开完成后查询数据
    handleOpenedDialog() {
      this.getStationFlowQuality()
      this.getStationFlowMaterial()
    },

    // 打开手动操作页面
    openManualPage() {
      this.manualDialogVisible = true
    },
    // 处理关闭弹窗
    handleCloseDialog() {
      this.manualDialogVisible = false
      this.shaftManualShow = false
      this.shaftManualRepeatedCodeShow = false
      this.manualNgPassShow = false
    },
    // 手工操作画面
    handleManualEvent(type) {
      if (type === 'shaft_manual_judge') {
        this.msgDialogTitle = '重新拧紧或者NG越过'
        this.shaftManualShow = true
        this.mgsDialogVisible = true
      }
      if (type === 'shaft_manual_repeated_code') {
        this.msgDialogTitle = '人工确认是否重复定扭'
        this.shaftManualRepeatedCodeShow = true
        this.mgsDialogVisible2 = true
      }
      if (type === 'manual_ng_pass') {
        this.msgDialogTitle = '人工NG放行'
        this.manualNgPassShow = true
        this.mgsDialogVisible3 = true
      }
      if (type === 'shaft_ok_finish') {
        // 人工确认拧紧数据OK
        this.shaftManualConfirmData('1')
        this.handleCloseDialog()
        return
      }
      if (type === 'shaft_ng_finish') {
        // 人工确认拧紧数据NG
        this.shaftManualConfirmData('2')
        this.handleCloseDialog()
        return
      }
      if (type === 'hlp_ok_go') {
        // 汇流排强制OK放行
        this.hlpConfireGo('1')
        this.handleCloseDialog()
        return
      }
      if (type === 'hlp_ng_go') {
        // 汇流排强制NG放行
        this.hlpConfireGo('2')
        this.handleCloseDialog()
        return
      }
    },
    // 汇流排OK强制放行
    hlpConfireGo(confirm_code) {
      if (this.currentStation.station_code === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code,
        confirm_code: confirm_code
      }
      stationMonitor
        .mesInterfGxConfirmGo(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '操作成功!!!', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '操作汇流排强制放行异常', type: 'error' })
        })
    },
    // 人工模拟拧紧枪拧紧OK完成或者NG完成
    shaftManualConfirmData(confirm_code) {
      if (this.currentStation.station_code === '') {
        return
      }
      var station_code = this.currentStation.station_code
      var clientCode = station_code
      if (station_code.indexOf('0-1') >= 0) {
        clientCode = station_code.replace('0-1', '1')
      }
      if (station_code.indexOf('0-2') >= 0) {
        clientCode = station_code.replace('0-2', '2')
      }
      var ShaftQ_FinalStatus_tag =
        clientCode + '/ShaftStatus/ShaftQ_FinalStatus'
      var ShaftQ_AllowRead_tag = clientCode + '/ShaftStatus/ShaftQ_AllowRead'
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: ShaftQ_FinalStatus_tag,
        TagValue: confirm_code
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: ShaftQ_AllowRead_tag,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + clientCode
      this.sendMessage(topic, sendStr)
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getStationStatus()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询selCellIP异常', type: 'error' })
        })
    },
    // 查询当前工位作业信息
    getStationMis() {
      if (this.currentStation.station_code === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code
      }
      stationMonitor
        .mesHmiStationMisSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const result = JSON.parse(defaultQuery.result)
            this.stationMis = result.stationInfo
            this.pdureList = result.pdureList
            if (result.currentProceduceId !== '0') {
              this.currentProceduceId = result.currentProceduceId
            }
            // 全柴订单选择后，当实际上线数量等于订单计划数量的时候让弹窗,其他项目逻辑不变
            if (this.gxCode === 'QC' && !this.orderBtnStatus) {
              this.getOrderList()
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationMisSel异常',
            type: 'error'
          })
        })
    },
    // 查询流程任务信息
    getFlowTaskData() {
      if (this.cellIp === '' || this.webapiPort === '') {
        return
      }
      this.flowInfo = { step: '', msg: '', code: '' }
      var method = '/cell/core/flow/CoreFlowTaskListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      const data = {
        station_id: this.currentStation.station_id
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            const flowTaskList = defaultQuery.data.data || []
            if (flowTaskList.length > 0) {
              this.flowInfo.step = flowTaskList[0].step_mod_des
              this.flowInfo.msg = flowTaskList[0].log_msg
              this.flowInfo.code = flowTaskList[0].log_code
            }
          }
        })
        .catch((ex) => {
          this.$message({
            message: '查询getFlowTaskData异常：' + ex,
            type: 'error'
          })
        })
    },
    // 查询工序物料，质量信息
    getPdureMaterialAndQuality() {
      if (this.currentProceduceId === '' || this.currentProceduceId === '0') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        proceduce_id: this.currentProceduceId
      }
      // 唐山国轩接口用的是带2的，其他的不带
      const type = (this.gxCode === 'TSGX' || this.gxCode === 'JZGX') ? '02' : ''
      stationMonitor
        .mesHmiPdureMaterialAndQualitySel(query, type)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const result = JSON.parse(defaultQuery.result)
            // 拧紧数据接口带返会的有qualityList1(代表第一把拧紧枪)、qualityList2(代表第二把拧紧枪)
            // 国轩和金寨返回的是 qualityList1
            // 其他项目返回的是 qualityList
            this.qualityData = result[`qualityList${type !== '' ? '1' : ''}`] || []
            this.materialData = result.bomList || []
            const qualityInfo = this.qualityData.filter(
              (item) => item.quality_status === 'PLAN'
            )
            this.currentQuality = {}
            if (qualityInfo.length > 0) {
              this.currentQuality.tag_col_order = qualityInfo[0].tag_col_order
              this.currentQuality.bolt_code = qualityInfo[0].bolt_code
              this.currentQuality.progm_num = qualityInfo[0].progm_num
            }

            for (var i = qualityInfo.length - 1; i >= 0; i--) {
              const index = this.qualityData.findIndex(
                (b) => b.quality_id === qualityInfo[i].quality_id
              )
              this.qualityData.splice(index, 1)
              this.qualityData.unshift(qualityInfo[i])
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiPdureMaterialAndQualitySel异常',
            type: 'error'
          })
        })
    },
    // 查询工位过站日、月、小时统计数量
    getProductionStatistic() {
      if (this.currentStation.station_code === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code
      }
      stationMonitor
        .mesHmiProductionStatisticSel(query)
        .then((res) => {
          this.productionStatistics = res
          Object.keys(res.hourTotal).forEach((key) => {
            this.productionStatistics['count' + key] = res.hourTotal[key]
          })
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiProductionStatisticSel异常',
            type: 'error'
          })
        })
    },
    // 查询过站列表
    getStationFlow() {
      if (
        this.stationMis.serial_num === undefined ||
        this.stationMis.serial_num === ''
      ) {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        serial_num: this.stationMis.serial_num
      }
      stationMonitor
        .mesHmiStationFlowSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.stationFlowData = defaultQuery.data || []
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationFlowSel异常',
            type: 'error'
          })
        })
    },
    // 查询当前工位作业状态监控配置
    getStationStatus() {
      if (this.currentStation.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      stationMonitor
        .mesHmiStationStatusSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              this.stationStatusList = defaultQuery.data
            }
            this.barCodeTagKeyList = defaultQuery.result.split(',')
            this.getTagValue()
            this.toStartWatch()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationStatusSel异常',
            type: 'error'
          })
        })
    },
    // 查询工位过站质量信息
    getStationFlowQuality() {
      const query = {
        user_name: Cookies.get('userName'),
        station_flow_id: this.stationFlowId
      }
      stationMonitor
        .mesHmiStationFlowQualitySel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.count > 0) {
              this.stationFlowQualityData = defaultQuery.data
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationFlowQualitySel异常',
            type: 'error'
          })
        })
    },
    // 查询工位过站物料信息
    getStationFlowMaterial() {
      const query = {
        user_name: Cookies.get('userName'),
        station_flow_id: this.stationFlowId
      }
      stationMonitor
        .mesHmiStationFlowMaterialSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.count > 0) {
              this.stationFlowMaterialData = defaultQuery.data
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationFlowMaterialSel异常',
            type: 'error'
          })
        })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      this.stationStatusList.forEach((item) => {
        if (item.tag_key !== '') {
          var readTag = {}
          readTag.tag_key = item.tag_key
          readTagArray.push(readTag)
        }
      })

      this.barCodeTagKeyList.forEach((item) => {
        var readTag = {}
        readTag.tag_key = item
        readTagArray.push(readTag)
      })
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach((item) => {
                if (item.tag_value !== undefined) {
                  if (this.barCodeTagKeyList.filter((item1) => item1 === item.tag_key).length > 0) {
                    this.barCode = item.tag_value
                  } else {
                    this.stationStatusList.length > 0 && (this.stationStatusList.filter((item1) => item1.tag_key === item.tag_key)[0].tag_value = item.tag_value)
                  }
                }
              })
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter(item => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({
            message: '查询getTagValue异常：' + ex,
            type: 'error'
          })
        })
    },
    handleDxOk(key, value, attr, flag) {
      this[attr] = flag
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: key,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        this.stationStatusList.forEach((item) => {
          if (item.tag_key !== '') {
            this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
          }
        })

        Object.keys(this.monitorData).forEach(key => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
        })
        this.barCodeTagKeyList.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item)
        })
        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        if (topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0) {
          this.handleMessage(jsonData)
        } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          if (this.barCodeTagKeyList.filter((item1) => item1 === jsonData.TagKey).length > 0) {
            this.barCode = jsonData.TagNewValue
            this.handleScan()
          } else {
            this.stationStatusList.length && (this.stationStatusList.filter((item1) => item1.tag_key === jsonData.TagKey)[0].tag_value = jsonData.TagNewValue)
          }
          Object.keys(this.monitorData).forEach(key => {
            var client_code = this.monitorData[key].client_code
            var group_code = this.monitorData[key].group_code
            var tag_code = this.monitorData[key].tag_code
            var tag_key = client_code + '/' + group_code + '/' + tag_code
            if (tag_key === jsonData.TagKey) {
              console.log(tag_key, jsonData.TagKey)
              this.monitorData[key].value = jsonData.TagNewValue
            }
          })
          if (this.monitorData.PlcS_RequestSignal1.value === '1') {
            this.handleDxOk('OP1051/MesStatus/MesS_Reserved_1', '1', 'PlcS_RequestSignal1Flag', true)
          }
          if (this.monitorData.PlcS_Reserved_1.value === '1') {
            this.handleDxOk('OP1051/MesStatus/MesS_Reserved_1', '1', 'PlcS_Reserved_1Flag', true)
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'mes_monitor') return
      this.msgDialogTitle = json.func_des
      this.tagKeyList = json.func_paras
      if (json.func_code === 'shaft_manual_judge') {
        this.shaftManualShow = true
        this.mgsDialogVisible = true
      }
      if (json.func_code === 'shaft_manual_repeated_code') {
        this.shaftManualRepeatedCodeShow = true
        this.mgsDialogVisible2 = true
      }
      if (json.func_code === 'manual_ng_pass') {
        this.manualNgPassShow = true
        this.mgsDialogVisible3 = true
      } else {
        this.mgsDialogVisible = true
      }
    },
    // 调用接口请求重新拧紧或者NG越过
    mesShaftManualJudge(order_status) {
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code,
        order_status: order_status
      }
      stationMonitor
        .mesHmiStationShaftManual(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '操作成功', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: 'NG拧紧人工确认异常', type: 'error' })
        })
      this.shaftManualShow = false
      this.mgsDialogVisible = false
    },

    // 调用接口请求重新定扭
    mesShaftManualRepeatedCode(topic, sendStr) {
      this.sendMessage(topic, sendStr)
      this.shaftManualRepeatedCodeShow = false
      this.mgsDialogVisible2 = false
      this.mgsDialogVisible = false
      this.mgsDialogVisible3 = false
    },
    // NG_PASS
    manualNgPassJudge(order_status) {
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code,
        order_status: order_status
      }
      stationMonitor
        .mesHmiStationManualNgPass(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '操作成功', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: 'NG拧紧人工确认异常', type: 'error' })
        })
      this.manualNgPassShow = false
      this.mgsDialogVisible3 = false
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          this.$message({ message: '操作成功！', type: 'success' })
        } else {
          this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    },
    getOrder() {
      // var $this = this
      this.chooseOrder = true
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.currentStation.prod_line_id,
        station_id: this.currentStation.station_id,
        sort: 'make_order desc',
        page: 1,
        size: 1000
      }
      stationMonitor
        .mesHmiStationMoSel(query)
        .then((res) => {
          if (res.code !== 0 && res.count < 0) {
            this.$message({
              message: '选择订单异常',
              type: 'error'
            })
            return
          }
          this.radioArr = res.data
          const seletedOrder = res.data.find((e) => e.mo_flag > 0)
          if (seletedOrder) {
            this.templateSelection = seletedOrder.mo_id
            console.log(this.templateSelection)
          }
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    getOrderList() {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.currentStation.prod_line_id,
        station_id: this.currentStation.station_id,
        sort: 'make_order desc',
        page: 1,
        size: 1000
      }
      stationMonitor
        .mesHmiStationMoSel(query)
        .then((res) => {
          if (res.code === 0 && res.data.length > 0) {
            const seletedOrder = res.data.find((e) => e.mo_flag > 0)
            if (seletedOrder) {
              const temp = res.data.find(e => e.mo_id === seletedOrder.mo_id)
              if (temp.mo_online_count === temp.mo_plan_count) {
                this.$message({
                  type: 'error',
                  message: `当前订单【${temp.small_model_type}】计划上线数量已完工 请人工切换下一个订单！！！`
                })
              }
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '订单异常',
            type: 'error'
          })
        })
    },
    // 单选---选择订单
    singleElection(row) {
      if (!this.orderSelectFlag) {
        this.orderSelectFlag = true
        return
      }
      this.orderSelectFlag = false
      this.chooseOrder = false
      this.templateSelection = row.mo_id
      const choose_make_order = row.make_order
      const choose_mo_id = row.mo_id
      var getQuery = {
        userName: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        make_order: choose_make_order,
        station_code: this.currentStation.station_code,
        mo_id: choose_mo_id
      }
      getOrderInfo(getQuery)
        .then(() => {
          this.$message({ message: '操作成功！', type: 'success' })
        })
        .catch(() => {
          this.$message({
            message: '获取订单信息查询异常',
            type: 'error'
          })
        })
    },
    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return (
          dt.getFullYear() +
          '-' +
          (dt.getMonth() + 1) +
          '-' +
          dt.getDate() +
          ' ' +
          dt.getHours() +
          ':' +
          dt.getMinutes() +
          ':' +
          dt.getSeconds()
        )
      }
    },
    setSelectOrderButtonStatus() {
      const query = {
        enable_flag: 'Y',
        prod_line_id: this.currentStation.prod_line_id,
        stationCodeDes: this.currentStation.station_code
      }
      selStation(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0 && defaultQuery.data.length > 0) {
            const stationData = defaultQuery.data[0]
            if (stationData.select_order_flag === 'Y') {
              this.orderBtnStatus = false
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    closeOrder(row) {
      this.$confirm(`是否确认关闭【${row.make_order}】订单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          editMoStatus({
            userName: Cookies.get('userName'),
            mo_status: 'SHUT_DOWN',
            mo_id: row.mo_id
          })
            .then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.getOrder()
              } else {
                this.$message({
                  message: '操作异常' + defaultQuery.msg,
                  type: 'error'
                })
              }
            })
            .catch((ex) => {
              this.$message({
                message: '操作异常' + ex,
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    getOrderCloseStation() {
      // 获取系统参数信息
      var queryParameter = {
        userName: Cookies.get('userName'),
        parameter_code: 'HMI_ORDER_CLOSE_STATION',
        enable_flag: 'Y'
      }
      selSysParameter(queryParameter)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (
              defaultQuery.data !== '' &&
              defaultQuery.data[0].parameter_val.indexOf(
                this.currentStation.station_code
              ) > -1
            ) {
              this.isCanClose = true
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
        // 获取系统参数信息
      var query = {
        userName: Cookies.get('userName'),
        parameter_code: 'ProjectCode',
        enable_flag: 'Y'
      }
      selSysParameter(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.gxCode = defaultQuery.data[0].parameter_val
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-card__body {
  padding: 5px !important;
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  height: 30px !important;
  width: 30px !important;
}
.el-table .warning-row {
  background: rgb(114, 83, 25);
}
::v-deep .el-table .red-row {
  background: rgb(135, 20, 20) !important;
  color: #fff;
}

.el-table .success-row {
  background: #f0f9eb;
}
table.table3 {
  text-align: center;
  border-collapse: collapse;
  width: 100%;
  margin: 5px 0;
}
.table3 tbody td {
  font-size: 12px;
  border: 1px solid #9596a5;
  height: 22px;
  background-color: #ffffff;
}
.tdName {
  width: 55px;
  white-space: nowrap;
  background-color: #f2f2f2 !important;
  color: #000000 !important;
}
.loginStyle {
  width: 150px;
  button {
    width: 162px;
    height: 40px;
    font-weight: 700;
    font-size: 16px;
    background-color: #229f99;
    border: 0;
    border-radius: 0px;
  }
  button:active {
    background-color: #0a8c86;
  }
}
.normal {
  color: #67c23a;
  font-weight: 700;
}
::v-deep .el-input--small .el-input__inner {
  height: 22px;
  line-height: 22px;
}

.showImg {
  background: #ffffff;
  color: #333333;
  width: 49.7%;
  height: 360px !important;
  text-align: center;
  border: 2px solid #ffc107;
  background-size: 60%;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url('./default.jpg');
}

.showOKImg {
  width: 49.7%;
  height: 360px !important;
  text-align: center;
  border: 2px solid #ffc107;
  position: relative;
}

.showOKImg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('./OK.png');
  background-size: 40%;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.8;
  z-index: 1;
}

.showNGImg {
  width: 49.7%;
  height: 360px !important;
  text-align: center;
  border: 2px solid #ffc107;
  position: relative;
}

.showNGImg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('./NG.png');
  background-size: 40%;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.8;
  z-index: 1;
}

.qianzhi {
  button {
    background: #3d98cf;
    color: #ffffff;
    font-size: 18px;
    width: 150px;
    height: 127px;
    border-radius: 0;
    border: 0;
  }
  button:active {
    background-color: #096ca8;
  }
}
.wraptable {
  width: 49.7%;
  // margin-left: 5px;
}
.jianju {
  width: 45px;
  display: inline-block;
}
::v-deep .el-table {
  border: 1px solid #9596a5;
}
::v-deep .el-table th {
  font-size: 12px !important;
  color: #000000;
  font-weight: normal !important;
  height: 22px;
  padding: 0;
  background-color: #f2f2f2 !important;
}
::v-deep .el-table th.el-table__cell.is-leaf,
.el-table td.el-table__cell {
  border-bottom: 1px solid #9596a5 !important;
}
::v-deep .el-table .cell {
  text-align: center;
}
::v-deep .el-table td.el-table__cell div {
  font-size: 12px !important;
  font-weight: normal;
}
::v-deep .el-table__empty-block {
  background: #ffffff;
}
.wrapWhole {
  min-height: 210px;
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  padding-top: 10px;
  border: 1px solid #9596a5;
  background: #ffffff !important;
  .marginten {
    margin-bottom: 10px !important;
  }
}
.wrapWholeone {
  display: flex;
  flex-wrap: wrap;
  height: max-content;
}
.wrappstyle {
  width: 50%;
  margin-bottom: 10px;
  font-weight: normal;
  font-size: 13px;
  p {
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    span {
      margin: 0 5px;
    }
  }
}
.wholeline {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  flex-shrink: 0;
}
.wholelinenormal {
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #00b050;
  box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  background-image: radial-gradient(
      0.3em 0.25em at 50% 25%,
      rgb(255, 255, 255) 25%,
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      0.25em 0.25em at 30% 75%,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      0.3em 0.3em at 60% 80%,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      100% 100%,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.3) 40%,
      rgba(0, 0, 0, 0.5) 50%
    );
}
.wholelineerror {
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #f00;
  box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  background-image: radial-gradient(
      0.3em 0.25em at 50% 25%,
      rgb(255, 255, 255) 25%,
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      0.25em 0.25em at 30% 75%,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      0.3em 0.3em at 60% 80%,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      100% 100%,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.3) 40%,
      rgba(0, 0, 0, 0.5) 50%
    );
}
.wholelinegray {
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #606266;
  box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  background-image: radial-gradient(
      0.3em 0.25em at 50% 25%,
      rgb(255, 255, 255) 25%,
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      0.25em 0.25em at 30% 75%,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      0.3em 0.3em at 60% 80%,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0)
    ),
    radial-gradient(
      100% 100%,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.3) 40%,
      rgba(0, 0, 0, 0.5) 50%
    );
}
.scan {
  width: 70px;
  button {
    background: #3398cb;
    color: #ffffff;
    font-size: 12px;
    width: 70px;
    height: 22px;
    padding: 0;
    border-radius: 0;
    border: 0;
  }
  button:active {
    box-shadow: inset 2px 2px 2px 0px rgba(42, 170, 244, 0.8),
      inset -7px -7px 10px 0px rgba(0, 0, 0, 0.1),
      7px 7px 20px 0px rgba(0, 0, 0, 0.1), 4px 4px 5px 0px rgba(0, 0, 0, 0.1);
  }
}
.scannoborder {
  border: 0 !important;
}
.scanGray {
  width: 75px;
  button {
    background: #909399;
    color: #ffffff;
    font-size: 12px;
    width: 75px;
    height: 22px;
    padding: 0;
    border-radius: 0;
    border: 0;
  }
}
.zhuisubtn {
  background: #3d98cf;
  color: #ffffff;
  font-size: 12px;
  border-radius: 0;
  border: 0;
  box-shadow: inset 2px 2px 2px 0px rgba(255, 255, 255, 0.5),
    inset -7px -7px 10px 0px rgba(0, 0, 0, 0.1),
    7px 7px 20px 0px rgba(0, 0, 0, 0.1), 4px 4px 5px 0px rgba(0, 0, 0, 0.1);
}
.zhuisubtn:active {
  box-shadow: inset 2px 2px 2px 0px rgba(42, 170, 244, 0.8),
    inset -7px -7px 10px 0px rgba(0, 0, 0, 0.1),
    7px 7px 20px 0px rgba(0, 0, 0, 0.1), 4px 4px 5px 0px rgba(0, 0, 0, 0.1);
}
.wholetd {
  width: 32.8%;
}
.tableImg {
  display: flex;
  justify-content: space-between;
}
.wrapOkbtn {
  .ok {
    width: 100%;
    height: 22px;
    line-height: 22px;
    padding: 0;
    font-size: 18px;
    font-weight: 700;
    background-color: #00b050;
    color: #ffffff;
    text-align: center;
  }
  .ng {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0;
    font-size: 16px;
    font-weight: 700;
    background-color: #f00;
    color: #ffffff;
    text-align: center;
    box-shadow: inset 2px 2px 2px 0px rgba(255, 255, 255, 0.5),
      inset -7px -7px 10px 0px rgba(0, 0, 0, 0.1),
      7px 7px 20px 0px rgba(0, 0, 0, 0.1), 4px 4px 5px 0px rgba(0, 0, 0, 0.1);
  }
}
.workpiece {
  border: 1px solid #e7dfdf;
  .wrappstyle {
    justify-content: center;
    p {
      width: 112px;
      white-space: nowrap;
    }
  }
}
.workpiece tr td {
  height: 48.3px;
  border: 0;
}
.orderwd {
  text-align: left;
  padding-left: 15px;
  border: 0 !important;
}
.riyueactive {
  color: #ffffff !important;
  background-color: #3d97ce !important;
}
// .daynoborder tr td{
//   border: 0;
// }
.daynoborder {
  .tdName {
    border-top: 1px solid #9596a5;
    border-bottom: 1px solid #9596a5;
    border-right: 1px solid #9596a5;
  }
  .tdEnd {
    border-right: 1px solid #9596a5;
  }
}
.tdScan {
  display: flex;
  .wrappstyle {
    margin-bottom: 0;
  }
}
::v-deep .el-input__inner {
  border: 0;
}
.nomargin {
  margin: 0 !important;
}
.itemtableTwo {
  display: flex;
  .tablemarginR {
    margin-right: 5px;
  }
  .nomarginTop {
    margin-top: 0;
  }
}
.w150 {
  width: 150px;
}
</style>
