<template>
  <el-drawer :title="nodeForm.flow_mod_sub_des" :wrapper-closable="true" :visible.sync="drawerVisible" direction="rtl" size="650px" append-to-body style="overflow-y: auto" @closed="drawerClose">
    <div id="scrollbar" :style="'height:' + height + 'px'">
      <el-scrollbar style="height: 100%">
        <el-form ref="nodeForm" class="el-form-wrap" :inline="true" :model="nodeForm" size="small" label-width="180px" :rules="rules" :disabled="readonly">
          <el-form-item :label="$t('lang_pack.mainmain.subDes')" prop="flow_mod_sub_des">
            <el-input v-model="nodeForm.flow_mod_sub_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.sort')" prop="flow_mod_sub_index">
            <el-input v-model.number="nodeForm.flow_mod_sub_index" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.processType')" prop="flow_sub_attr">
            <el-select v-model="nodeForm.flow_sub_attr">
              <el-option v-for="item in [{ label: $t('lang_pack.mainmain.startSub'), id: 'FIRST' }, { label: $t('lang_pack.mainmain.endSub'), id: 'LAST' }, { label: $t('lang_pack.mainmain.regularSub'), id: 'NORMAL' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.nextSub')" prop="next_flow_mod_sub_id">
            <el-select v-model="nodeForm.next_flow_mod_sub_id" clearable>
              <el-option v-for="item in subData" :key="item.subId_stepId" :label="item.describe" :value="item.subId_stepId" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.subEnd')">
            <el-select v-model="nodeForm.finish_all_flag">
              <el-option v-for="item in [{ label: $t('lang_pack.mainmain.afterEnd'), id: 'Y' }, { label: $t('lang_pack.vie.NO'), id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.subFunctionDll')" prop="sub_mod_function_dll">
            <el-input v-model="nodeForm.sub_mod_function_dll" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.subAttr')" prop="sub_mod_function_attr">
            <el-input v-model="nodeForm.sub_mod_function_attr" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.controlIcon')">
            <el-input v-model="nodeForm.control_ico" />
          </el-form-item>

          <el-form-item :label="$t('lang_pack.mainmain.controlWidth')">
            <el-input v-model="nodeForm.width" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.controlHeight')">
            <el-input v-model="nodeForm.height" />
          </el-form-item>

          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <el-select v-model="nodeForm.enable_flag">
              <el-option v-for="item in [{ label: $t('lang_pack.vie.effective'), id: 'Y' }, { label: $t('lang_pack.vie.invalid'), id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.colorStateOK')">
            <el-color-picker v-model="nodeForm.ok_control_color" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.colorStateNG')">
            <el-color-picker v-model="nodeForm.ng_control_color" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.colorStateWait')">
            <el-color-picker v-model="nodeForm.wait_control_color" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.colorStateRetry')">
            <el-color-picker v-model="nodeForm.retry_control_color" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.colorStateCancel')">
            <el-color-picker v-model="nodeForm.abort_control_color" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.initColor')">
            <el-color-picker v-model="nodeForm.color" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div v-if="!readonly" style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="drawerClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleClickSaveNode">{{ $t('lang_pack.vie.save') }}</el-button>
        </div>
      </el-scrollbar>
    </div>
  </el-drawer>
</template>
<script>
import { sel, add, edit } from '@/api/core/flow/rcsFlowModSub'
import Cookies from 'js-cookie'
import Vue from 'vue'
const defaultForm = {
  name: null,
  id: null,
  subId_stepId: null,
  width: null,
  height: null,
  x: null,
  y: null,
  type: null,
  imgId: null,
  color: null,
  describe: null,
  strokeWidth: null,
  flow_mod_sub_id: 0,
  flow_mod_main_id: 0,
  flow_mod_sub_code: '',
  flow_mod_sub_des: '',
  flow_mod_sub_index: 1,
  flow_sub_attr: 'NORMAL',
  next_flow_mod_sub_id: '',
  control_ico: '',
  control_type: '-1',
  control_location_x: 0,
  control_location_y: 0,
  ok_control_color: '#37ED13',
  ng_control_color: '#EA1F1F',
  abort_control_color: '#F1930F',
  wait_control_color: '#EEF91B',
  retry_control_color: '#15E4DD',
  finish_all_flag: 'N',
  sub_mod_function_dll: '',
  sub_mod_function_attr: '',
  enable_flag: 'Y'
}
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data: function() {
    // 自定义验证
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error(this.$t('lang_pack.mainmain.cannotBeEmpty')))
      }
      if (!Number.isInteger(value)) {
        callback(new Error(this.$t('lang_pack.mainmain.enterNumber')))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 100,
      nodeForm: {},
      thisH: null,
      rules: {
        // 提交验证规则
        flow_mod_sub_code: [{ required: true, message: this.$t('lang_pack.mainmain.enterSub'), trigger: 'blur' }],
        flow_mod_sub_des: [{ required: true, message: this.$t('lang_pack.mainmain.enterDes'), trigger: 'blur' }],
        next_flow_mod_sub_id: [{ required: true, message: this.$t('lang_pack.mainmain.nextSelect'), trigger: 'blur' }],
        sub_mod_function_dll: [
          {
            required: true,
            message: this.$t('lang_pack.mainmain.enterFunction'),
            trigger: 'blur'
          }
        ],
        flow_mod_sub_index: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      subData: [],
      drawerVisible: false
    }
  },
  watch: {
    visible: {
      immediate: true,
      deep: true,
      handler() {
        this.drawerVisible = this.visible
      }
    }
  },
  mounted() {
    window.addEventListener('resize', () => (this.height = document.documentElement.clientHeight - 100), false)
  },
  methods: {
    getClientHeight() {
      this.height = document.documentElement.clientHeight - 100
      console.log(this.height)
    },
    chooseIcon(iconId) {
      this.nodeForm.imgId = iconId
      this.nodeForm.control_ico = iconId
      this.customPopover = false
    },
    drawerClose() {
      // this.$emit("updateVisible");
      this.$emit('update:visible', false)
    },
    editNode(node, thisH, flow_mod_main_id) {
      this.thisH = thisH
      this.subData = thisH.internalNodes.filter(item => item.type === 'sub' && item.id !== node.id && item.subId_stepId !== 0)
      this.subData.push({ subId_stepId: 0, describe: this.$t('lang_pack.mainmain.notHave') })
      this.subData.push({ subId_stepId: -1, describe: this.$t('lang_pack.mainmain.endAll') })
      for (const key in defaultForm) {
        if (this.nodeForm.hasOwnProperty(key)) {
          this.nodeForm[key] = defaultForm[key]
        } else {
          Vue.set(this.nodeForm, key, defaultForm[key])
        }
      }
      this.nodeForm.id = node.id
      this.nodeForm.subId_stepId = node.subId_stepId
      this.nodeForm.flow_sub_id = node.subId_stepId
      this.nodeForm.flow_mod_main_id = flow_mod_main_id
      this.nodeForm.flow_sub_index = parseInt(node.name)
      this.nodeForm.type = node.type
      this.nodeForm.width = node.width
      this.nodeForm.height = node.height
      this.nodeForm.control_type = node.type
      this.nodeForm.control_location_x = parseInt(node.x)
      this.nodeForm.control_location_y = parseInt(node.y)
      this.nodeForm.control_ico = node.imgId
      this.nodeForm.color = node.color
      this.nodeForm.flow_mod_sub_des = node.describe
      this.nodeForm.strokeWidth = node.strokeWidth
      if (node.subId_stepId !== undefined && node.subId_stepId !== 0) {
        const query = {
          flow_mod_sub_id: node.subId_stepId
        }
        sel(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.nodeForm.flow_mod_sub_id = defaultQuery.data[0].flow_mod_sub_id
                this.nodeForm.flow_mod_main_id = defaultQuery.data[0].flow_mod_main_id
                this.nodeForm.flow_mod_sub_des = defaultQuery.data[0].flow_mod_sub_des
                this.nodeForm.flow_mod_sub_index = defaultQuery.data[0].flow_mod_sub_index
                this.nodeForm.flow_sub_attr = defaultQuery.data[0].flow_sub_attr
                this.nodeForm.next_flow_mod_sub_id = defaultQuery.data[0].next_flow_mod_sub_id
                this.nodeForm.control_ico = defaultQuery.data[0].control_ico
                this.nodeForm.control_type = defaultQuery.data[0].control_type

                this.nodeForm.ok_control_color = defaultQuery.data[0].ok_control_color
                this.nodeForm.ng_control_color = defaultQuery.data[0].ng_control_color
                this.nodeForm.abort_control_color = defaultQuery.data[0].abort_control_color
                this.nodeForm.wait_control_color = defaultQuery.data[0].wait_control_color
                this.nodeForm.retry_control_color = defaultQuery.data[0].retry_control_color
                this.nodeForm.finish_all_flag = defaultQuery.data[0].finish_all_flag
                this.nodeForm.sub_mod_function_dll = defaultQuery.data[0].sub_mod_function_dll
                this.nodeForm.sub_mod_function_attr = defaultQuery.data[0].sub_mod_function_attr
                this.nodeForm.enable_flag = defaultQuery.data[0].enable_flag
              }
            }
          })
          .catch(() => {
            this.$message({
              message: this.$t('lang_pack.vie.queryException'),
              type: 'error'
            })
          })
      }
    },
    handleClickSaveNode() {
      // 确定(修改)
      this.$refs['nodeForm'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            ...this.nodeForm
          }
          const that = this
          // 新增
          if (this.nodeForm.flow_mod_sub_id === undefined || this.nodeForm.flow_mod_sub_id.length <= 0 || this.nodeForm.flow_mod_sub_id === 0) {
            add(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  that.$message({ message: this.$t('lang_pack.mainmain.addSub') ,type: 'success' })
                  that.$emit(
                    'refreshChart',
                    {
                      id: that.nodeForm.id,
                      subId_stepId: parseInt(defaultQuery.result),
                      name: that.nodeForm.flow_mod_sub_index,
                      type: that.nodeForm.type,
                      width: that.nodeForm.width,
                      height: that.nodeForm.height,
                      x: that.nodeForm.control_location_x,
                      y: that.nodeForm.control_location_y,
                      imgId: that.nodeForm.control_ico,
                      color: that.nodeForm.color,
                      describe: that.nodeForm.flow_mod_sub_des,
                      strokeWidth: that.nodeForm.strokeWidth
                    },
                    that.thisH
                  )
                  that.$emit('update:visible', false)
                } else if (defaultQuery.code === -1) {
                  that.$message({ message: defaultQuery.msg, type: 'warning' })
                }
              })
              .catch(() => {
                that.$message({ message: this.$t('lang_pack.mainmain.addError'), type: 'error' })
              })
          } else {
            // 修改
            edit(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  that.$emit(
                    'refreshChart',
                    {
                      id: that.nodeForm.id,
                      subId_stepId: that.nodeForm.subId_stepId,
                      name: that.nodeForm.flow_mod_sub_index,
                      type: that.nodeForm.type,
                      width: that.nodeForm.width,
                      height: that.nodeForm.height,
                      x: that.nodeForm.control_location_x,
                      y: that.nodeForm.control_location_y,
                      imgId: that.nodeForm.control_ico,
                      color: that.nodeForm.color,
                      describe: that.nodeForm.flow_mod_sub_des,
                      strokeWidth: that.nodeForm.strokeWidth
                    },
                    that.thisH
                  )
                  that.$emit('update:visible', false)
                  that.$message({ message: this.$t('lang_pack.mainmain.editSuccess'), type: 'success' })
                } else if (defaultQuery.code === -1) {
                  that.$message({ message: defaultQuery.msg, type: 'warning' })
                }
              })
              .catch(() => {
                that.$message({
                  message: this.$t('lang_pack.mainmain.editError'),
                  type: 'error'
                })
              })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss">
#scrollbar {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>
