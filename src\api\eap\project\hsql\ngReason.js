import request from '@/utils/request'

/**
 * 手动放行 - NG原因保存
 * 根据工号、当前作业工单批号、读码信息、读码面次、维护NG原因、机台编号进行手动放行
 * @param {Object} data 请求参数
 * @param {string} data.EmpID 工号
 * @param {string} data.LotNum 工单批号
 * @param {string} data.ReadInfo 读码信息
 * @param {string} data.ReadMC 读码面次
 * @param {string} data.NGReason 维护NG原因
 * @param {string} data.StationCode 机台编号
 * @returns {Promise} 返回处理结果
 */
export function ngReasonSaved(data) {
  return request({
    url: '/aisEsbApi/eap/project/hsql/index/NGReasonSaved',
    method: 'post',
    data
  })
}

/**
 * 手动放行 - NG原因保存 (GET方式)
 * @param {Object} params 请求参数
 * @returns {Promise} 返回处理结果
 */
export function ngReasonSavedGet(params) {
  return request({
    url: '/aisEsbApi/eap/project/hsql/index/NGReasonSaved',
    method: 'get',
    params
  })
}
