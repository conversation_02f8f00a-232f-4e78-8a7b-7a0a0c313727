<template>
  <!--子菜单明细-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
        <el-form-item :label="$t('lang_pack.maintenanceMenu.submenuCoding')" prop="menu_item_code" display:none>
          <!-- 子菜单编码 -->
          <el-input v-model="form.menu_item_code" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.maintenanceMenu.submenuCodingDescription')" prop="menu_item_des">
          <!-- 子菜单描述 -->
          <el-input v-model="form.menu_item_des" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.maintenanceMenu.order')" prop="item_order_by">
          <!-- 顺序 -->
          <el-input v-model="form.item_order_by" />
        </el-form-item>

        <!--表：sys_function-->
        <el-form-item :label="$t('lang_pack.maintenanceMenu.procedure')">
          <!-- 程序 -->
          <el-select v-model="form.function_id" filterable>
            <el-option v-for="item in functionDataTable" :key="item.function_code" :label="item.function_des" :value="item.function_id">
              <span style="float: left">{{ item.function_code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.function_des }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.maintenanceMenu.icon')" prop="menu_item_ico" class="iconItem">
          <!-- 图标 -->
          <el-popover v-model="customPopover" placement="top">
            <sysIcon @chooseIcon="chooseIcon" />
            <el-input slot="reference" v-model="form.menu_item_ico" />
          </el-popover>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <!-- 有效标识 -->
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确认 -->
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%;"  :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="menu_item_id" label="id" />
          <el-table-column  v-if="1 == 0" width="10" prop="menu_group_id" label="menu_group_id" />
          <el-table-column  :show-overflow-tooltip="true" prop="menu_item_code" width="200" :label="$t('lang_pack.maintenanceMenu.submenuCoding')" />
          <!-- 子菜单编码 -->
          <el-table-column  :show-overflow-tooltip="true" prop="menu_item_des"  width="140" :label="$t('lang_pack.maintenanceMenu.submenuCodingDescription')" />
          <!-- 子菜单描述 -->
          <el-table-column  :show-overflow-tooltip="true" prop="item_order_by"  width="50" :label="$t('lang_pack.maintenanceMenu.order')" />
          <!-- 顺序 -->
          <el-table-column  :show-overflow-tooltip="true" prop="function_id"    width="570" :label="$t('lang_pack.maintenanceMenu.procedure')" />
          <!-- 程序 -->
          <el-table-column  :show-overflow-tooltip="true" prop="menu_item_ico"  width="40" :label="$t('lang_pack.maintenanceMenu.icon')" />
          <!-- 图标 -->
          <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" width="120" align="center" prop="enable_flag">
            <!-- 有效标识 -->
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" width="120" fixed="right">
            <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import sysIcon from '@/components/SvgIcon/chooseIcon' // 图标
import crudMenuItem from '@/api/core/system/sysMenuItem'
import { sel } from '@/api/core/system/sysFunction'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  menu_item_id: '',
  menu_group_id: '',
  menu_item_code: '',
  menu_item_des: '',
  item_order_by: '',
  function_id: '',
  menu_item_ico: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYSMENUItem',
  components: { crudOperation, rrOperation, udOperation, pagination, sysIcon },
  props: {
    menu_group_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '菜单明细',
      // 登录用户
      userName: Cookies.get('userName'),
      // 菜单组ID
      query: { menu_group_id: '' },
      // 唯一字段
      idField: 'menu_item_id',
      // 排序
      sort: ['menu_item_id asc'],
      // CRUD Method
      crudMethod: { ...crudMenuItem },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      // 设置默认分页大小为20条每页
      props: {
        pageSize: 20
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_menu:add'],
        edit: ['admin', 'sys_menu:edit'],
        del: ['admin', 'sys_menu:del'],
        down: ['admin', 'sys_menu:down']
      },
      rules: {
        // 提交验证规则
        menu_item_code: [{ required: true, message: '请输入子菜单编码', trigger: 'blur' }],
        item_order_by: [{ required: true, message: '请输入顺序', trigger: 'blur' }],
        function_id: [{ required: true, message: '请输选择程序', trigger: 'blur' }]
      },
      // LOV数据
      functionDataTable: [],
      customPopover: false
    }
  },
  watch: {
    menu_group_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.menu_group_id = this.menu_group_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userId')
      // menu_type_code: this.select_menu_type_code,
    }
    // 从后台获取到对象数组
    sel(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.functionDataTable = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    chooseIcon(iconId) {
      this.form.menu_item_ico = iconId
      this.customPopover = false
    },

    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.menu_group_id = this.menu_group_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.menu_group_id = this.menu_group_id
      return true
    }
  }
}
</script>
<style lang="less" scoped>
.el-table {
  border-radius: 10px;
}
.el-card {
  border: 0 !important;
  overflow: inherit;
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
