
function render(g, node, isSelected) {
  if(node.type === 'bigCar'){
    g.append('image')
    .attr('x', node.x)
    .attr('y', node.y)
    .attr('preserveAspectRatio', 'none')
    .attr('vector-effect', 'non-scaling-stroke')
    .style('height', '290px')
    .attr('href', require('@/assets/images/lateralCar.png'))
  }else if(node.type === 'smallCar'){
    g.append('image')
    .attr('x', node.x + 14)
    .attr('y', node.y + 60)
    .attr('preserveAspectRatio', 'none')
    .attr('vector-effect', 'non-scaling-stroke')
    .style('width', '100px')
    .attr('href', require('@/assets/images/verticalCar.png'))
  }else{
    const borderColor = isSelected ? '#445ddb' : 'none'
    const borderWidth = isSelected ? 5 : node.strokeWidth
       g.append('rect')
        .style('width', '305px')
        .style('height', '22px')
        .style('fill', 'white')
        .style('stroke-width', borderWidth + 'px')
        .attr('x', node.x)
        .attr('y', node.y)
        .style('fill', '#79a0f1')
        .attr('stroke', '#79a0f1')
        // 库位号 
        g.append('text')
        .attr('x', node.x + 20)
        .attr('y', node.y + 15)
        .attr('fill', '#FFFFFF')
        .attr('class', 'unselectable')
        .attr('text-anchor', 'middle')
        .text(() => '库位:')
        .style('font-size', '14px')
        .style('font-weight', 'bold')
        g.append("text")
          .attr("x", node.x + 40)
          .attr("y", node.y + 15)
          .attr("class", "unselectable")
          .attr("fill", "#FFFFFF")
          .text(() => node.stock_code)
          .style("font-size", "14px")
          .style("font-weight", "bold");
          // 库存
          g.append('text')
          .attr('x', node.x + 85)
          .attr('y', node.y + 15)
          .attr('fill', '#FFFFFF')
          .attr('class', 'unselectable')
          .attr('text-anchor', 'middle')
          .text(() => '库存:')
          .style('font-size', '14px')
          .style('font-weight', 'bold')
          g.append("text")
            .attr("x", node.x + 105)
            .attr("y", node.y + 15)
            .attr("class", "unselectable")
            .attr("fill", "#FFFFFF")
            .text(() => node.stock_count)
            .style("font-size", "14px")
            .style("font-weight", "bold");
          // 型号
          g.append('text')
          .attr('x', node.x + 145)
          .attr('y', node.y + 15)
          .attr('fill', '#FFFFFF')
          .attr('text-anchor', 'middle')
          .text(() => "型号:")
          .style('font-size', '14px')
          .style('font-weight', 'bold')
          g.append("text")
            .attr("x", node.x + 165)
            .attr("y", node.y + 15)
            .attr("class", "unselectable")
            .attr("fill", "#FFFFFF")
            .text(() => node.model_type)
            .style("font-size", "14px")
            .style("font-weight", "bold");
  }
  
}

export default render
