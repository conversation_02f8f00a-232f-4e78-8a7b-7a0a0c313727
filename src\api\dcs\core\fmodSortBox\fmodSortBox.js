import request from '@/utils/request'

// 查询分拣料箱基础
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodSortBoxSelect',
    method: 'post',
    data
  })
}
// 新增分拣料箱基础
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodSortBoxInsert',
    method: 'post',
    data
  })
}
// 修改分拣料箱基础
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodSortBoxUpdate',
    method: 'post',
    data
  })
}
// 删除分拣料箱基础
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodSortBoxDelete',
    method: 'post',
    data
  })
}
// 修改分拣料箱基础--修改有效标识
export function editEnableFlag(data) {
  return request({
      url: 'aisEsbWeb/dcs/core/DcsApsFmodSortBoxEnableFlagUpdate',
      method: 'post',
      data
  })
}

export default { sel, add, edit, del ,editEnableFlag}

