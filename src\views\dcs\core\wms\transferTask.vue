<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="传输任务号:">
                <!-- 传输任务号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号:">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="型号:">
                <el-select v-model="query.model_type" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_type"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识:">
                <el-select v-model="query.enable_flag" clearable filterable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="到达时间:">
                <!-- 到达时间 -->
                <el-date-picker
                  v-model="query.arrive_date"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="离开时间:">
                <!-- 离开时间 -->
                <el-date-picker
                  v-model="query.leave_date"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item label="序列号" prop="serial_num">
                <el-input v-model="form.serial_num" clearable size="small" />
              </el-form-item>
              <el-form-item label="批次号" prop="lot_num">
                <el-input v-model="form.lot_num" clearable size="small" />
              </el-form-item>
              <el-form-item label="托盘号" prop="pallet_num">
                <el-input v-model="form.pallet_num" clearable size="small" />
              </el-form-item>
              <el-form-item label="当前位置点" prop="location_code">
                <el-input v-model="form.location_code" clearable size="small" />
              </el-form-item>
              <el-form-item label="来源位置点" prop="from_location_code">
                <el-input v-model="form.from_location_code" clearable size="small" />
              </el-form-item>
              <el-form-item label="计划任务数量" prop="plan_task_count">
                <el-input v-model.number="form.plan_task_count" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item label="型号" prop="model_type">
                <el-select v-model="form.model_type" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_type"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="是否完成寻中" prop="finish_xz_flag">
                <el-radio-group v-model="form.finish_xz_flag">
                  <el-radio v-for="item in [{id:0,label:'是',value:'Y'},{id:1,label:'否',value:'N'}]" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否允许放行" prop="allow_pass">
                <el-radio-group v-model="form.allow_pass">
                  <el-radio v-for="item in [{id:0,label:'是',value:'Y'},{id:1,label:'否',value:'N'}]" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="有效标识" prop="enable_flag">
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" prop="transfer_task_id" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.transfer_task_id }}</el-descriptions-item>
                  <el-descriptions-item label="传输任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                  <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                  <el-descriptions-item label="批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content"> {{ props.row.lot_num }}</el-descriptions-item>
                  <el-descriptions-item label="托盘号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_num }}</el-descriptions-item>
                  <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_code }}</el-descriptions-item>
                  <el-descriptions-item label="当前位置点" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.from_location_code }}</el-descriptions-item>
                  <el-descriptions-item label="来源位置点" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="任务执行状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.EXECUTE_STATUS[props.row.execute_status] }}</el-descriptions-item>
                  <el-descriptions-item label="任务结束后下一步位置点" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.next_location_code }}</el-descriptions-item>
                  <el-descriptions-item label="任务代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_code }}</el-descriptions-item>
                  <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="计划任务数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plan_task_count }}</el-descriptions-item>
                  <el-descriptions-item label="剩余任务数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.left_task_count }}</el-descriptions-item>
                  <el-descriptions-item label="到达时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.arrive_date }}</el-descriptions-item>
                  <el-descriptions-item label="离开时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.leave_date }}</el-descriptions-item>
                  <el-descriptions-item label="停留时间(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cost_time }}</el-descriptions-item>
                  <el-descriptions-item label="寻中获取Y轴值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.xz_location_y }}</el-descriptions-item>
                  <el-descriptions-item label="是否完成寻中" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_xz_flag == 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="是否允许放行" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.allow_pass == 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="允许放行时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.all_pass_date }}</el-descriptions-item>
                  <el-descriptions-item label="允许放行者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.all_pass_by }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 传输任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              label="传输任务号"
              width="110"
              align="center"
            />
            <!-- 序列号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              label="序列号"
              width="110"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              label="批次号"
              width="110"
              align="center"
            />
            <!-- 当前位置点 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_code"
              label="当前位置点"
              width="110"
              align="center"
            />
            <!-- 来源位置点 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_location_code"
              label="来源位置点"
              width="110"
              align="center"
            />
            <!-- 任务执行状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="execute_status"
              label="任务执行状态"
              width="110"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.EXECUTE_STATUS[scope.row.execute_status] }}
              </template>
            </el-table-column>
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              label="型号"
              width="130"
              align="center"
            />
            <!-- 计划任务数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_task_count"
              label="计划任务数量"
              width="110"
              align="center"
            />
            <!-- 剩余任务数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="left_task_count"
              label="剩余任务数量"
              width="110"
              align="center"
            />
            <!-- 到达时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="arrive_date"
              label="到达时间"
              width="130"
              align="center"
            />
            <!-- 离开时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="leave_date"
              label="离开时间"
              width="130"
              align="center"
            />
            <!-- 停留时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cost_time"
              label="停留时间"
              width="130"
              align="center"
            />
            <!-- 是否完成寻中 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_xz_flag"
              label="是否完成寻中"
              width="110"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.finish_xz_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否允许放行 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="allow_pass"
              label="是否允许放行"
              width="110"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.allow_pass == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 允许放行时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="all_pass_date"
              label="允许放行时间"
              width="130"
              align="center"
            />
            <!-- 允许放行者 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="all_pass_by"
              label="允许放行者"
              width="130"
              align="center"
            />
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              label="有效标识"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column
              prop="button"
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudTransferTask from '@/api/dcs/core/wms/transferTask'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  transfer_task_id: '',
  task_num: '',
  serial_num: '',
  lot_num: '',
  pallet_num: '',
  location_code: '',
  from_location_code: '',
  execute_status: '',
  next_location_code: '',
  task_code: '',
  model_type: '',
  plan_task_count: '',
  left_task_count: '',
  arrive_date: '',
  leave_date: '',
  cost_time: '',
  xz_location_y: '',
  finish_xz_flag: 'Y',
  allow_pass: 'Y',
  all_pass_date: '',
  all_pass_by: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'CARTASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: 'WMS辊道任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'transfer_task_id',
      // 排序
      sort: ['transfer_task_id asc'],
      // CRUD Method
      crudMethod: { ...crudTransferTask },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        lot_num: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
        model_type: [{ required: true, message: '请选择型号', trigger: 'blur' }],
        plan_task_count: [{ required: true, message: '请选择计划任务数量', trigger: 'blur' }],
        allow_pass: [{ required: true, message: '请选择是否允许放行', trigger: 'blur' }],
        finish_xz_flag: [{ required: true, message: '请选择是否完成寻中', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      },
      modelList: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EXECUTE_STATUS'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    crudFmodModel.sel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data.length > 0) {
          this.modelList = defaultQuery.data
        }
      }
    })
      .catch(() => {
        this.$message({
          message: '型号查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    BlurText(e) {
      const boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning('计划任务数量不能为空或正整数')
        e.target.value = ''
      }
    },
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudTransferTask
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              transfer_task_id: data.transfer_task_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
    // 开始 "新建/编辑" - 之前
    // [CRUD.HOOK.beforeToCU](crud) {

    //     return true
    // },
  }
}
</script>
