import request from '@/utils/request'

// PACK详细生成规则查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDSel',
    method: 'post',
    data
  })
}
// PACK详细生成规则增加
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDIns',
    method: 'post',
    data
  })
}
// PACK详细生成规则修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDUpd',
    method: 'post',
    data
  })
}
// PACK详细生成规则删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDDel',
    method: 'post',
    data
  })
}
// pack详细生成规则型号查询
export function MesSmallModelSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelSel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, MesSmallModelSel }
