import request from '@/utils/request'

// 查询工位屏时间
export function getStationTime(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqSysDataSel',
    method: 'post',
    data
  })
}
// 派工大屏
export function getSelectVehicleDispatch(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-om/api/v1/pickCarRecord/selectVehicleDispatch?${data}`,
    method: 'get',
  })
}
// 派工大屏蓝色底图数据
export function getSelectTodayPlanCount() {
  return request({
    url: 'http://10.140.4.11:9999/baic-mom-om/api/v1/pickCarRecord/selectTodayPlanCount',
    method: 'get',
  })
}
export default { getStationTime,getSelectVehicleDispatch,getSelectTodayPlanCount }
