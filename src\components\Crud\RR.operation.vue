<!--搜索与重置-->
<template>
  <span class="wrapRRItem">
    <!--左侧-->
    <slot name="left" />
    <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="crud.toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
    <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="crud.resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
    <!--右侧-->
    <slot name="right" />
  </span>
</template>
<script>
import { crud } from '@crud/crud'
export default {
  mixins: [crud()],
  props: {
    itemClass: {
      type: String,
      required: false,
      default: ''
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRRItem {
  display: flex;
  align-items: center;
}
</style>
