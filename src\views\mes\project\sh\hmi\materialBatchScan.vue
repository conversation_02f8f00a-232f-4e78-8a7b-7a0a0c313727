<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码:">
                <!-- 物料编码 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识:">
                <!-- 有效标识 -->
                <el-select v-model="query.enable_flag" clearable filterable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="400px"
          >
            <el-form
              ref="form"
              class="el-form-wrap el-form-column"
              :model="form"
              :rules="rules"
              size="small"
              label-width="160px"
              :inline="true"
            >
              <el-form-item label="物料编码" prop="material_code">
                <el-input v-model="form.material_code" disabled clearable size="small" />
              </el-form-item>
              <el-form-item label="物料名称" prop="material_name">
                <el-input v-model="form.material_name" disabled clearable size="small" />
              </el-form-item>
              <el-form-item label="工位编码" prop="station_code">
                <el-input v-model="form.station_code" disabled clearable size="small" />
              </el-form-item>
              <el-form-item label="批次号" prop="batch_code">
                <el-input v-model="form.batch_code" disabled clearable size="small" />
              </el-form-item>
              <el-form-item label="批次上线顺序" prop="batch_order">
                <el-input v-model="form.batch_order" disabled clearable size="small" />
              </el-form-item>
              <el-form-item label="批次物料数量" prop="batch_count">
                <el-input v-model.number="form.batch_count" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item label="切换批次预留量" prop="switch_count" @blur="BlurText($event)">
                <el-input v-model.number="form.switch_count" clearable size="small" />
              </el-form-item>
              <el-form-item label="有效标识" prop="enable_flag">
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="已使用数量" prop="used_count">
                <el-input v-model="form.used_count" disabled clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <div class="scanStyle">
            <span>物料批次条码：</span>
            <div class="wrapimin">
              <el-input v-model="materialBatchCode" type="text" size="large" placeholder="请输入内容" />
            </div>
            <el-button class="scanBtn" size="large" type="primary" @click="ManualMaterialScan">扫描</el-button>
          </div>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              width="200"
              label="物料编码"
              align="center"
            />
            <!-- 物料名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_name"
              width="200"
              label="物料名称"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="batch_code"
              label="批次号"
              width="320"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="batch_order"
              label="批次上线顺序"
              width="240"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="batch_count"
              label="批次物料数量"
              width="240"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="used_count"
              label="已使用数量"
              width="240"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="switch_count"
              label="切换批次预留量"
              width="200"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              label="有效标识"
              width="200"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.enable_flag == 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="130" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMaterialBatch from '@/api/mes/project/sh/shMaterialBatch'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
import mqtt from 'mqtt'
import stationMonitor from '@/api/mes/core/hmi/stationMonitor'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
  batch_id: '',
  line_id: '',
  material_code: '',
  material_name: '',
  station_code: '',
  batch_code: '',
  batch_order: '',
  batch_count: '',
  switch_count: '',
  enable_flag: '',
  used_count: ''
}
export default {
  name: 'WEB_APS_TASK_EVENT',
  components: { crudOperation, udOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '物料批次扫描',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'batch_id',
      // 排序
      sort: ['batch_id asc'],
      // CRUD Method
      crudMethod: { ...crudMaterialBatch },
      query: {
        station_id: ''
      },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'material_batch_sacn:add'],
        edit: ['admin', 'material_batch_sacn:edit'],
        del: ['admin', 'material_batch_sacn:del']
      },
      projectCode: '',
      materialBatchCode: '',
      materialBatchList: [],
      rules: {
        batch_count: [{ required: true, message: '批次物料数量是为必填项', trigger: 'blur' }],
        switch_count: [{ required: true, message: '必填项', trigger: 'blur' }]
      },
      timer: null,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    this.query.station_id = +this.$route.query.station_id
    this.$nextTick(() => {
      this.crud.toQuery()
    })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    console.log(this.$route.query.station_id)
    const query = {
      user_name: Cookies.get('userName'),
      cell_id: this.$route.query.cell_id,
      current_ip: window.location.hostname
    }
    selCellIP(query)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          const ipInfo = JSON.parse(defaultQuery.result)
          this.cellIp = ipInfo.ip
          this.webapiPort = ipInfo.webapi_port
          this.mqttPort = ipInfo.mqtt_port
          this.getStationStatus()
        } else {
          this.$message({ message: defaultQuery.msg, type: 'error' })
        }
      })
      .catch(() => {
        this.$message({ message: '查询selCellIP异常', type: 'error' })
      })
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
    this.timer && clearInterval(this.timer)
  },
  methods: {
    [CRUD.HOOK.beforeRefresh](crud) {
      this.query.station_id = this.$route.query.station_id
    },
    [CRUD.HOOK.afterRefresh](crud) {
      this.query.station_id = this.$route.query.station_id
    },
    ManualMaterialScan() {
      console.log(this.$route.query.station_id)
      if (this.materialBatchCode === '') {
        this.$message({
          message: '请输入物料条码',
          type: 'warning'
        })
        return
      }
      this.handleMaterialScan()
    },
    BlurText(e) {
      const boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning('不能为空且为正整数')
        e.target.value = ''
      }
    },
    handleMaterialScan() {
      var query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        material_batch_code: this.materialBatchCode
      }
      crudMaterialBatch.MesMeMaterialBatchScan(query)
        .then(res => {
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
            return
          }
          // 扫描完成
          this.$message({
            message: '扫描成功',
            type: 'success',
            duration: 5000
          })
          this.crud.toQuery()
        })
        .catch((ex) => {
          this.$message({
            message: ex.msg + '异常',
            type: 'error'
          })
        })
    },
    // 查询当前工位作业状态监控配置
    getStationStatus() {
      if (this.$route.query.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      }
      stationMonitor
        .mesHmiStationStatusSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.materialBatchList = defaultQuery.result.split(',')
            this.getTagValue()
            this.toStartWatch()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationStatusSel异常',
            type: 'error'
          })
        })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      this.materialBatchList.forEach((item) => {
        var readTag = {}
        readTag.tag_key = item
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach((item) => {
                if (item.tag_value !== undefined) {
                  if (this.materialBatchList.filter((item1) => item1 === item.tag_key).length > 0) {
                    this.materialBatchCode = item.tag_value
                    this.materialBatchCode && this.handleMaterialScan()
                  }
                }
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({
            message: '查询getTagValue异常：' + ex,
            type: 'error'
          })
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        this.materialBatchList.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item)
        })
        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          if (this.materialBatchList.filter((item1) => item1 === jsonData.TagKey).length > 0) {
            this.materialBatchCode = jsonData.TagNewValue
            this.materialBatchCode && this.handleMaterialScan()
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.scanStyle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
}
.wrapimin {
  width: 100%;
}
</style>
