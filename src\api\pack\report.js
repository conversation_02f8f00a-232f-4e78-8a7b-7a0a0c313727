import request from '@/utils/request'
// 查询包装set记录表
export function packIndex(data) {
    return request({
        url: 'aisEsbWeb/pack/core/packArrayIndex',
        method: 'post',
        data
    })
}
export function sel(data) {
    return request({
        url: 'aisEsbWeb/pack/core/packArraySelect',
        method: 'post',
        data
    })
}
// 导出
export function down(data) {
    return request({
        url: 'aisEsbWeb/pack/core/packArrayExport',
        method: 'post',
        data,
        responseType: 'blob',
    })
}
export default {sel,packIndex,down }