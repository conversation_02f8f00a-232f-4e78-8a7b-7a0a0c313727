import request from '@/utils/request'

// 查询工位设备对应关系
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationDeviceSel',
    method: 'post',
    data
  })
}
// 新增工位设备对应关系
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationDeviceIns',
    method: 'post',
    data
  })
}
// 修改工位设备对应关系
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationDeviceUpd',
    method: 'post',
    data
  })
}
// 删除工位设备对应关系
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationDeviceDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

