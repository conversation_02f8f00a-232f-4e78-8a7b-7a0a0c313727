import Vue from 'vue'
import Router from 'vue-router'
import Layout from '../layout/index'

Vue.use(Router)

export const constantRouterMap = [{
  path: '/obdDevice',
  meta: { title: 'OBD设备', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/obdDevice.vue'], resolve),
  hidden: true
},
{
  path: '/videoTest',
  meta: { title: '视频声音测试', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/videoTest.vue'], resolve),
  hidden: true
},
{
  path: '/fill',
  meta: { title: '加注', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/quality/fill.vue'], resolve),
  hidden: true
},
{
  path: '/tightening',
  meta: { title: '拧紧', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/quality/tightening.vue'], resolve),
  hidden: true
},
{
  path: '/playTime',
  meta: { title: '打刻标识', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/quality/playTime.vue'], resolve),
  hidden: true
},
{
  path: '/testingResult',
  meta: { title: '检测结果', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/quality/testingResult.vue'], resolve),
  hidden: true
},
{
  path: '/testingLet',
  meta: { title: '检测排放', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/quality/testingLet.vue'], resolve),
  hidden: true
},
{
  path: '/glassGlue',
  meta: { title: '玻璃涂胶', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/glassGlue.vue'], resolve),
  hidden: true
},
{
  path: '/qualitygate',
  meta: { title: '质量门', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/qualitygate.vue'], resolve),
  hidden: true
},
{
  path: '/iframe',
  meta: { title: '嵌套', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/iframe.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreen',
  prop: true,
  meta: { title: '总装工位屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreen.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenVoice',
  prop: true,
  meta: { title: '总装工位屏有声音', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenVoice.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenZh',
  prop: true,
  meta: { title: '总装工位屏综合线', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenZh.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenNs',
  prop: true,
  meta: { title: '总装工位屏内饰线', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenNs.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenYz',
  prop: true,
  meta: { title: '总装工位工艺文件', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenYz.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenImg',
  prop: true,
  meta: { title: '总装工位屏预装线', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenImg.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenLine',
  prop: true,
  meta: { title: '总装工位屏趋势图', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenLine.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workstationScreenNSLine',
  prop: true,
  meta: { title: '总装工位屏内饰线', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workstationScreenNSLine.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workshopScreenone',
  meta: { title: '总装安灯大屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workshopScreenone.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/workshopScreencar',
  meta: { title: '总装车辆过站大屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/workshopScreencar.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/partWireOn',
  prop: true,
  meta: { title: '总装零件屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/partWireOn.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/tireLineOn',
  prop: true,
  meta: { title: '轮胎线大屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/tireLineOn.vue'], resolve),
  hidden: true
},
{
  path: '/HA/workstationScreen',
  prop: true,
  meta: { title: '焊装工位屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/HA/workstationScreen.vue'], resolve),
  hidden: true
},
{
  path: '/HA/workshopScreenone',
  meta: { title: '焊装安灯大屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/HA/workshopScreenone.vue'], resolve),
  hidden: true
},
{
  path: '/HA/workshopScreencar',
  meta: { title: '焊装车辆过站大屏', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/HA/workshopScreencar.vue'], resolve),
  hidden: true
},
{
  path: '/CA/pressline',
  meta: { title: '冲压线', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/CA/pressline.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/zzqueue',
  meta: { title: '总装生产队列', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/zzqueue.vue'], resolve),
  hidden: true
},
{
  path: '/ZA/dpqueue',
  meta: { title: '底盘生产队列', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/ZA/dpqueue.vue'], resolve),
  hidden: true
},
{
  path: '/welcome',
  meta: { title: '欢迎', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/welcome.vue'], resolve),
  hidden: true
},
{
  path: '/hotwelcome',
  meta: { title: '热烈欢迎', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/common/hotwelcome.vue'], resolve),
  hidden: true
},
{
  path: '/lockScreen',
  meta: { title: '锁屏', noCache: true },
  component: (resolve) => require(['@/views/core/hmi/lockScreen.vue'], resolve),
  hidden: true
},
{
  path: '/whiteLogin',
  meta: { title: '白名单登录', noCache: true },
  component: (resolve) => require(['@/views/core/hmi/hmi_login'], resolve),
  hidden: true
},
{
  path: '/whiteLoginWx',
  meta: { title: '白名单登录', noCache: true },
  component: (resolve) => require(['@/views/core/hmi/hmi_login_wx'], resolve),
  hidden: true
},
{
  path: '/login',
  meta: { title: '登录', noCache: true },
  component: (resolve) => require(['@/views/login'], resolve),
  hidden: true
},
{
  path: '/whiteLoginOfVerify',
  meta: { title: '白名单登录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/hmi/login'], resolve),
  hidden: true
},
{
  path: '/loginOfVerify',
  meta: { title: '登录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/login'], resolve),
  hidden: true
},
{
  path: '/404',
  component: (resolve) => require(['@/views/core/features/404'], resolve),
  hidden: true
},
{
  path: '/401',
  component: (resolve) => require(['@/views/core/features/401'], resolve),
  hidden: true
},
{
  path: '/redirect',
  component: Layout,
  hidden: true,
  children: [{
    path: '/redirect/:path*',
    component: (resolve) => require(['@/views/core/features/redirect'], resolve)
  }]
},
{
  path: '/',
  component: Layout,
  redirect: '/dashboard',
  hidden: true,
  children: [{
    path: 'dashboard',
    component: (resolve) => require(['@/views/home'], resolve),
    name: 'Dashboard',
    meta: { title: '首页', icon: 'index', affix: true, noCache: true }
  }]
},
{
  path: '/user',
  component: Layout,
  hidden: true,
  redirect: 'noredirect',
  children: [{
    path: 'center',
    component: (resolve) => require(['@/views/core/system/user/center'], resolve),
    name: '个人信息',
    meta: { title: '个人信息' }
  }]
},
{
  path: '/hmi_main',
  meta: { title: 'HMI', noCache: true },
  component: (resolve) => require(['@/views/core/hmi/main.vue'], resolve),
  hidden: true
},
{
  path: '/hmi_main_wx',
  meta: { title: 'HMI', noCache: true },
  component: (resolve) => require(['@/views/core/hmi/main_wx.vue'], resolve),
  hidden: true
},
// {
//   path: '/hmi_main',
//   meta: { title: 'HMI', noCache: true },
//   component: (resolve) => require(['@/views/core/hmiMain/main.vue'], resolve),
//   hidden: true
// },
// {
//   path: '/hmi_main',
//   meta: { title: 'HMI', noCache: true },
//   component: (resolve) => require(['@/views/core/hmiMain/home_page.vue'], resolve),
//   hidden: true
// },
{
  path: '/orderInfo',
  meta: { title: 'orderInfo', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/orderInfo'], resolve),
  hidden: true
},
{
  path: '/mainInterface',
  meta: { title: 'mainInterface', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/mainInterface'], resolve),
  hidden: true
},
{
  path: '/manualPack',
  meta: { title: 'PACK模组绑定', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/manualPack'], resolve),
  hidden: true
},
{
  path: '/manualGrouping',
  meta: { title: '人工配组', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/manualGrouping'], resolve),
  hidden: true
},
{
  path: '/manualModule',
  meta: { title: '模组中段上线', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/manualModule'], resolve),
  hidden: true
},
{
  path: '/manualPackOnline',
  meta: { title: 'PACK箱体上线', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/manualPackOnline'], resolve),
  hidden: true
},
{
  path: '/manualMzReplaceCell',
  meta: { title: '模组NG替换电芯', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/manualMzReplaceCell'], resolve),
  hidden: true
},
{
  path: '/manualMzPrint',
  meta: { title: '模组查询打印', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/manualMzPrint'], resolve),
  hidden: true
},
{
  path: '/materialScan',
  meta: { title: '物料扫描', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/materialScan'], resolve),
  hidden: true
},
{
  path: '/materialScanQc',
  meta: { title: '物料扫描', noCache: true },
  component: (resolve) => require(['@/views/mes/project/qc/hmi/materialScan'], resolve),
  hidden: true
},
{
  path: '/taskflow',
  meta: { title: '任务流程图', noCache: true },
  component: (resolve) => require(['@/views/core/flow/task/task'], resolve),
  hidden: true
},
{
  path: '/taskrecord',
  meta: { title: '任务流程记录列表', noCache: true },
  component: (resolve) => require(['@/views/core/flow/task/taskrecord'], resolve),
  hidden: true
},
{
  path: '/confirm-dialog',
  meta: { title: '人工确认', noCache: true },
  component: (resolve) => require(['@/views/core/flow/task/confirm-dialog'], resolve),
  hidden: true
},
{
  path: '/barcodeRule',
  meta: { title: '条码生成规则维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/barcodeRule/index'], resolve),
  hidden: true
},
{
  path: '/craftRoute',
  meta: { title: '工艺路线维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/craftRoute/index'], resolve),
  hidden: true
},
{
  path: '/materialRule',
  meta: { title: '物料扫描规则维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/materialRule/index'], resolve),
  hidden: true
},
{
  path: '/mzRule',
  meta: { title: '模组规则维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/mzRule/index'], resolve),
  hidden: true
},
{
  path: '/packRule',
  meta: { title: 'PACK规则维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/packRule/index'], resolve),
  hidden: true
},
// 维信主设备
{
  path: '/recipe',
  meta: { title: '配方维护', noCache: true },
  component: (resolve) => require(['@/views/eap/core/recipe/index'], resolve),
  hidden: true
},
{
  path: '/WXrecipe',
  meta: { title: '配方维护', noCache: true },
  component: (resolve) => require(['@/views/wx/recipe/index'], resolve),
  hidden: true
},
{
  path: '/wxRecipeMain',
  meta: { title: '配方维护', noCache: true },
  component: (resolve) => require(['@/views/wx/recipe/project/recipe/index'], resolve),
  hidden: true
},
{
  path: '/tlwxMainMain',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/tlwx/index'], resolve),
  hidden: true
},
{
  path: '/tlwxRecipeMain',
  meta: { title: '配方维护', noCache: true },
  component: (resolve) => require(['@/views/eap/project/tlwx/recipe/index'], resolve),
  hidden: true
},
//
{
  path: '/screenConfig',
  meta: { title: '大屏配置', noCache: true },
  component: (resolve) => require(['@/views/eap/core/screenConfig/index'], resolve),
  hidden: true
},
{
  path: '/positonsLists',
  meta: { title: '药水工单记录列表', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dayuan/positonsLists/index'], resolve),
  hidden: true
},
{
  path: '/frontDisposeScreen',
  meta: { title: '药水工单记录列表', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dayuan/frontDisposeScreen.vue'], resolve),
  hidden: true
},
{
  path: '/shift',
  meta: { title: '班次维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/shift/index'], resolve),
  hidden: true
},
{
  path: '/smallModel',
  meta: { title: '机型维护', noCache: true },
  component: (resolve) => require(['@/views/mes/core/smallModel/index'], resolve),
  hidden: true
},
{
  path: '/planMo',
  meta: { title: '订单信息', noCache: true },
  component: (resolve) => require(['@/views/mes/core/planMo/index'], resolve),
  hidden: true
},
{
  path: '/plcRecipe',
  meta: { title: 'plc小配方', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/plcRecipe/index'], resolve),
  hidden: true
},
{
  path: '/fmodEquip',
  meta: { title: '设备台账', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/fmodEquip/index'], resolve),
  hidden: true
},
{
  path: '/bgScreen',
  meta: { title: '物料看板大屏', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/bgScreen/index'], resolve),
  hidden: true
},
{
  path: '/meDxQuality',
  meta: { title: '电芯质量数据', noCache: true },
  component: (resolve) => require(['@/views/mes/core/meDxQuality/index'], resolve),
  hidden: true
},
{
  path: '/meMzQuality',
  meta: { title: '模组质量数据', noCache: true },
  component: (resolve) => require(['@/views/mes/core/meMzQuality/index'], resolve),
  hidden: true
},
{
  path: '/StationPort',
  meta: { title: '工位端口维护', noCache: true },
  component: (resolve) => require(['@/views/eap/core/StationPort'], resolve),
  hidden: true
},
{
  path: '/StationConfig',
  meta: { title: '工位配置维护', noCache: true },
  component: (resolve) => require(['@/views/eap/core/StationConfig'], resolve),
  hidden: true
},
{
  path: '/stationSet',
  meta: { title: '工位设置', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/stationSet'], resolve),
  hidden: true
},
{
  path: '/stationSet2',
  meta: { title: '工位设置', noCache: true },
  component: (resolve) => require(['@/views/eap/core/StationSet'], resolve),
  hidden: true
},
{
  path: '/stationMonitor',
  meta: { title: '放板机工位监控', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/stationMonitor/index'], resolve),
  hidden: true
},
{
  path: '/stationMonitorWxjd',
  meta: { title: '放板机工位监控', noCache: true },
  component: (resolve) => require(['@/views/eap/project/wxjd/hmi/stationMonitorWxjd/index'], resolve),
  hidden: true
},
{
  path: '/stationMonitor2',
  meta: { title: '收板机工位监控', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/stationMonitor2/index'], resolve),
  hidden: true
},
{
  path: '/stationMonitor3',
  meta: { title: 'Mes界面', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/stationMonitor3/index'], resolve),
  hidden: true
},
{
  path: '/plateReceiver',
  meta: { title: '收板机', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/plateReceiver/index'], resolve),
  hidden: true
},
// 珠海深联看板
{
  path: '/plateReceiverZH',
  meta: { title: '收板机', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/plateReceiverZH/index'], resolve),
  hidden: true
}, {
  path: '/stationAndon',
  meta: { title: '工位安灯', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/stationAndon'], resolve),
  hidden: true
},
{
  path: '/stationFlow',
  meta: { title: '工位流程监控', noCache: true },
  component: (resolve) => require(['@/views/core/hmi/stationFlow'], resolve),
  hidden: true
},
{
  path: '/scadaMonitor',
  meta: { title: '点位监控', noCache: true },
  component: (resolve) => require(['@/views/core/scada/monitor/index'], resolve),
  hidden: true
},
{
  path: '/taskRecord',
  meta: { title: '历史流程查询', noCache: true },
  component: (resolve) => require(['@/views/core/flow/task/taskrecord'], resolve),
  hidden: true
},
{
  path: '/PlanReport',
  meta: { title: 'PlanReport', noCache: true },
  component: (resolve) => require(['@/views/eap/core/plan/PlanReport'], resolve),
  hidden: true
}, {
  path: '/interfLog',
  meta: { title: '接口日志查询', noCache: true },
  component: (resolve) => require(['@/views/core/center/interf/interfLog'], resolve),
  hidden: true
}, {
  path: '/interfLogWx',
  meta: { title: '接口日志查询', noCache: true },
  component: (resolve) => require(['@/views/core/center/interf/interfLogWx'], resolve),
  hidden: true
},
{
  path: '/sop',
  meta: { title: 'sop', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/stationMonitor/sop'], resolve),
  hidden: true
}, {
  path: '/stationMonitorMes',
  meta: { title: '工位监控', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/stationMonitor/index'], resolve),
  hidden: true
},
{
  path: '/stationMonitorMesIndexGuide',
  meta: { title: '工位监控', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/stationMonitor/indexGuide'], resolve), // 金寨项目工位2
  hidden: true
},
{
  path: '/stationMonitorMesTsGx',
  meta: { title: '作业指导', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/stationMonitor/index_ts_gx'], resolve), // 唐山国轩作业指导
  hidden: true
},
{
  path: '/dx_original_sel',
  meta: { title: '电芯原始数据', noCache: true },
  component: (resolve) => require(['@/views/mes/core/hmi/dxOrginalSel'], resolve), // 唐山国轩电芯原始数据查询
  hidden: true
},
{
  path: '/stationMonitorMes2',
  meta: { title: '工位监控', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/hmi/stationMonitor/index'], resolve),
  hidden: true
}, {
  path: '/eapPlanReport',
  meta: { title: 'EAP生产任务报表', noCache: true },
  component: (resolve) => require(['@/views/eap/core/plan/PlanReport'], resolve),
  hidden: true
}, {
  path: '/eapStationFlownReport',
  meta: { title: 'EAP板件履历报表', noCache: true },
  component: (resolve) => require(['@/views/eap/core/stationFlow/index'], resolve),
  hidden: true
}, {
  path: '/smallModelBom',
  meta: { title: 'smallModelBom报表', noCache: true },
  component: (resolve) => require(['@/views/mes/core/smallModelBom/index'], resolve),
  hidden: true
}, {
  path: '/scadaLink',
  meta: { title: 'SCADA通讯诊断', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/report_scada_link'], resolve),
  hidden: true
}, {
  path: '/scadaTagRead',
  meta: { title: 'SCADA读取超时诊断', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/report_scada_tagread'], resolve),
  hidden: true
}, {
  path: '/scadaTagChange',
  meta: { title: 'SCADA点位变化分析', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/report_scada_datachange'], resolve),
  hidden: true
}, {
  path: '/scadaFastPic',
  meta: { title: 'SCADA快照查询', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/report_scada_fastpic'], resolve),
  hidden: true
}, {
  path: '/scadaTagWrite',
  meta: { title: 'SCADA点位写入记录', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/report_scada_tagwrite'], resolve),
  hidden: true
}, {
  path: '/reportScadaAlarm',
  meta: { title: '报警信息查询', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/report_scada_alarm'], resolve),
  hidden: true
}, {
  path: '/setinout',
  meta: { title: '拉入拉出', noCache: true },
  component: (resolve) => require(['@/views/pmc/core/stationflow/inout/index'], resolve),
  hidden: true
}, {
  path: '/materialDel',
  meta: { title: '解绑物料', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/materialDel'], resolve),
  hidden: true
},
{
  path: '/andonScreen',
  meta: { title: '安灯大屏', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/andonScreen/index'], resolve),
  hidden: true
},
{
  path: '/productScreen',
  meta: { title: '生产线大屏', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/productScreen/index'], resolve),
  hidden: true
},
{
  path: '/equipStatuScreen',
  meta: { title: '设备状态大屏', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/equipStatuScreen/index'], resolve),
  hidden: true
},
{
  path: '/qciframe',
  meta: { title: '设备状态大屏', noCache: true },
  component: (resolve) => require(['@/views/mes/project/quanchai/qciframe'], resolve),
  hidden: true
},
{
  path: '/SCADA_SECE_LOG',
  meta: { title: 'SECS日志', noCache: true },
  component: (resolve) => require(['@/views/core/scada/report/scada_secs_log'], resolve),
  hidden: true
},
{
  path: '/dyStationMonitor',
  meta: { title: '定颖放扳机主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/stationMonitor/index'], resolve),
  hidden: true
},
{
  path: '/dyStationMonitor2',
  meta: { title: '定颖收扳机主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/stationMonitor2/index'], resolve),
  hidden: true
},
{
  path: '/dyStationMonitor3',
  meta: { title: '驰久主设备主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/cjMonitor/index'], resolve),
  hidden: true
},
{
  path: '/aoiStationMonitor',
  meta: { title: '快捷AOI放扳机主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/fp/hmi/stationMonitor/index'], resolve),
  hidden: true
},
{
  path: '/jxStationMonitor',
  meta: { title: '金像放扳机主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/jx/hmi/stationMonitor/index'], resolve),
  hidden: true
},
{
  path: '/dyProdOrder',
  meta: { title: '定颖工单查询', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/prodOrder/index'], resolve),
  hidden: true
},
{
  path: '/dyWipManual',
  meta: { title: '定颖WIP补报', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/wip/index'], resolve),
  hidden: true
},
{
  path: '/eapHmiShow',
  meta: { title: 'CIM消息报表', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmiShow/index'], resolve),
  hidden: true
},
{
  path: '/eapStatUtility',
  meta: { title: '三率报表', noCache: true },
  component: (resolve) => require(['@/views/eap/core/stat/utility'], resolve),
  hidden: true
},
{
  path: '/eapMainPage',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/cjMonitor/index'], resolve),
  hidden: true
},
{
  path: '/recipeAndon',
  meta: { title: '配方安东', noCache: true },
  component: (resolve) => require(['@/views/eap/project/sfjd/andon/index'], resolve),
  hidden: true
},
{
  path: '/DummyReport',
  meta: { title: 'DummyReport', noCache: true },
  component: (resolve) => require(['@/views/eap/project/gx/DummyReport'], resolve),
  hidden: true
},
{
  path: '/ZjReport',
  meta: { title: 'ZjReport', noCache: true },
  component: (resolve) => require(['@/views/eap/project/gx/ZjReport'], resolve),
  hidden: true
},
// 徐工页面路径
{
  path: '/mainPage',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/mainPage'], resolve),
  hidden: true
},
{
  path: '/carMainPage',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/carMainPage'], resolve),
  hidden: true
},
{
  path: '/loadAreaOper',
  meta: { title: '上料区作业', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/loadAreaOper'], resolve),
  hidden: true
},
{
  path: '/loadAreaOperList',
  meta: { title: '任务管理', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/loadAreaOperList'], resolve),
  hidden: true
},
{
  path: '/cuttingZone',
  meta: { title: '切割区', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/cuttingZone'], resolve),
  hidden: true
},
{
  path: '/sortingArea',
  meta: { title: '分拣区', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/sortingArea'], resolve),
  hidden: true
},
{
  path: '/equipmentUnitMonitoring',
  meta: { title: '设备单元监控', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/equipmentUnitMonitoring'], resolve),
  hidden: true
},
{
  path: '/equipmentMaintenance',
  meta: { title: '设备维护', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/hmi/equipmentMaintenance'], resolve),
  hidden: true
},
// {
//   path: '/fmodSortBox',
//   meta: { title: '分拣料箱基础', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/fmodSortBox/fmodSortBox'], resolve),
//   hidden: true
// },
// {
//   path: '/fmodQuality',
//   meta: { title: '质量数据采集', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/fmodQuality/fmodQuality'], resolve),
//   hidden: true
// },
// {
//   path: '/meStationMis',
//   meta: { title: '工位MIS实时状态表', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/meStationMis/meStationMis'], resolve),
//   hidden: true
// },
// {
//   path: '/carTask',
//   meta: { title: 'WMS天车任务', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/wms/carTask'], resolve),
//   hidden: true
// },
// {
//   path: '/transferTask',
//   meta: { title: 'WMS天车任务', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/wms/transferTask'], resolve),
//   hidden: true
// },
// {
//   path: '/wmsFmodStock',
//   meta: { title: 'WMS天车库位表', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/wmsCbTable/wmsFmodStock'], resolve),
//   hidden: true
// },
// {
//   path: '/wmsMeStock',
//   meta: { title: 'WMS天车库存表', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/wmsCbTable/wmsMeStock'], resolve),
//   hidden: true
// },
// {
//   path: '/wmsLockCarTask',
//   meta: { title: 'WMS调度任务表', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/wmsCbTable/wmsLockCarTask'], resolve),
//   hidden: true
// },
// {
//   path: '/wmsLockCarRoute',
//   meta: { title: 'WMS调度任务表路线表', noCache: true },
//   component: (resolve) => require(['@/views/dcs/core/wmsCbTable/wmsLockCarRoute'], resolve),
//   hidden: true
// },
// {
//  path: '/user',
//  component: Layout,
//  hidden: true,
//  redirect: 'noredirect',
//  children: [
//    {
//      path: 'center',
//      component: (resolve) => require(['@/views/system/user/center'], resolve),
//      name: '个人中心',
//      meta: { title: '个人中心' }
//    }
//  ]
// }
{
  path: '/dcs/alarm',
  meta: { title: '设备报警代码维护', noCache: true },
  component: (resolve) => require(['@/views/dcs/project/xg/alarm/index'], resolve), // 徐工项目
  hidden: true
},
{
  path: '/dcs/alarm/dashboard',
  meta: { title: '智能下料产线设备报警信息看板', noCache: true },
  component: (resolve) => require(['@/views/dcs/project/xg/alarm/dashboard/index'], resolve), // 徐工项目
  hidden: true
},
{
  path: '/wms_dashboard',
  meta: { title: '徐工集团', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/wms/wms_dashboard'], resolve),
  hidden: true
},
{
  path: '/wms_Intelboard',
  meta: { title: '智能下料车间-智能行车', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/wms/wms_Intelboard'], resolve),
  hidden: true
},
{
  path: '/wms_controlboard',
  meta: { title: '智能下料车间-智能行车故障监控', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/wms/wms_controlboard'], resolve),
  hidden: true
},
{
  path: '/wms_screen',
  meta: { title: '智能下料车间-智能行车', noCache: true },
  component: (resolve) => require(['@/views/dcs/core/wms/wms_screen'], resolve),
  hidden: true
},
// 包装项目
// {
//   path: '/packCoreRecipe',
//   meta: { title: '配方管理', noCache: true },
//   component: (resolve) => require(['@/views/pack/core/recipe/index'], resolve),
//   hidden: true
// },
{
  path: '/pack/core/recipe',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/pack/core/recipe/index'], resolve), // 通用
  hidden: true
},
{
  path: '/pack/core/sort/split-rule',
  meta: { title: '分选截取比对规则设定', noCache: true },
  component: (resolve) => require(['@/views/pack/core/sort/split-rule/index'], resolve), // 通用
  hidden: true
},
{
  path: '/cdpackTaskManager',
  meta: { title: '工单管理', noCache: true },
  component: (resolve) => require(['@/views/eap/project/zhcd/index'], resolve), // 崇达项目
  hidden: true
},
{
  path: '/pdpackTaskManager',
  meta: { title: '工单管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/task/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pdpackCoreRecipe',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/recipe/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/packXNPSort',
  meta: { title: 'X数/位分选模式记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/sort/xnp/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pdPackSortRule',
  meta: { title: '分选规则设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/sort/rule/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pdPackSortSplitRule',
  meta: { title: '分选截取比对规则设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/sort/split-rule/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/jsPackSortSplitRule',
  meta: { title: '分选截取比对规则设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/sort/index'], resolve), // 景硕项目
  hidden: true
},
{
  path: '/packSortChoice',
  meta: { title: '分选条件设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/sort/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/packPdMonitor',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/monitor/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pdpackStationMonitor',
  meta: { title: '出货地校验配置', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/shipaddress/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pdpackStationSetReport',
  meta: { title: '包装SET记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/report/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pdpackStationPileReport',
  meta: { title: '包装Pile记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/report/pileIndex'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/packCoreRecipe',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/recipe/index'], resolve), // 越南健鼎项目
  hidden: true
},
{
  path: '/packTaskManager',
  meta: { title: '工单管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/task/index'], resolve), // 越南健鼎项目
  hidden: true
},
{
  path: '/jdPackSortChoice',
  meta: { title: '分选条件设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/sort/index'], resolve), // 健鼎项目
  hidden: true
},
{
  path: '/pack/project/tripod/sort/hb',
  meta: { title: '分选条件设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/sort/hb/index'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/pack/project/tripod/sort/yn',
  meta: { title: '分选条件设定', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/sort/yn/index'], resolve), // 越南健鼎项目
  hidden: true
},
{
  path: '/packStationMonitor',
  meta: { title: '工位监控', noCache: true },
  component: (resolve) => require(['@/views/pack/core/monitor/index'], resolve),
  hidden: true
},
{
  path: '/packStationSetReport',
  meta: { title: '包装SET记录', noCache: true },
  component: (resolve) => require(['@/views/pack/core/report/index'], resolve),
  hidden: true
},
{
  path: '/pack/project/tripod/report/hb',
  meta: { title: '包装SET记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/report/hb/index'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/packStationPileReport',
  meta: { title: '包装Pile记录', noCache: true },
  component: (resolve) => require(['@/views/pack/core/report/pileIndex'], resolve),
  hidden: true
},
{
  path: '/pack/project/tripod/report/hb/pileIndex',
  meta: { title: '包装Pile记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/report/hb/pileIndex'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/pack/ccd/mapping',
  meta: { title: '2D下载Mapping信息', noCache: true },
  component: (resolve) => require(['@/views/pack/project/avary/ccd/mapping/index'], resolve), // 鹏鼎项目
  hidden: true
},
{
  path: '/pack/project/kinsus/monitor',
  meta: { title: '工位监控', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/monitor/index'], resolve), // 台湾景硕项目
  hidden: true
},
{
  path: '/pack/project/kinsus/task',
  meta: { title: '工单管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/task/index'], resolve), // 台湾景硕项目
  hidden: true
},
{
  path: '/pack/project/kinsus/recipe',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/recipe/index'], resolve), // 台湾景硕项目
  hidden: true
},
{
  path: '/pack/project/kinsus/tray-recipe',
  meta: { title: 'TRAY配方管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/tray-recipe/index'], resolve), // 台湾景硕项目
  hidden: true
},
{
  path: '/pack/project/kinsus/report/station-flow',
  meta: { title: '过站记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/report/station-flow/index'], resolve), // 台湾景硕项目
  hidden: true
},
{
  path: '/pack/project/kinsus/report/station-quality',
  meta: { title: '质量记录', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/report/station-quality/index'], resolve), // 台湾景硕项目
  hidden: true
},
{
  path: '/pack/project/kinsus/report/ccd-validation-result',
  meta: { title: 'CCD校验结果', noCache: true },
  component: (resolve) => require(['@/views/pack/project/kinsus/report/ccd-validation-result/index'], resolve), // 台湾景硕项目
  hidden: true
},

{
  path: '/recipeManage',
  meta: { title: '配方维护', noCache: true },
  component: (resolve) => require(['@/views/wx/recipe/index'], resolve), // 维信项目
  hidden: true
},
{
  path: '/ghStationMonitorPbj',
  meta: { title: '广合配板机', noCache: true },
  component: (resolve) => require(['@/views/eap/core4/hmi/stationMonitorPbj/index'], resolve),
  hidden: true
},
{
  path: '/ghStationMonitor',
  meta: { title: '广合放扳机主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/core4/hmi/stationMonitor/index'], resolve),
  hidden: true
},
{
  path: '/ghStationMonitor2',
  meta: { title: '广合收扳机主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/core4/hmi/stationMonitor2/index'], resolve),
  hidden: true
},
{
  path: '/stationMonitor4',
  meta: { title: '放板机工位监控', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/stationMonitor4/index'], resolve),
  hidden: true
},
{
  path: '/stationMonitor5',
  meta: { title: '放板机工位监控', noCache: true },
  component: (resolve) => require(['@/views/eap/core/hmi/stationMonitor5/index'], resolve),
  hidden: true
},
{
  path: '/recipeManage2',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/eap/project/sh/index'], resolve), // 胜宏项目
  hidden: true
},
{
  path: '/interfManage',
  meta: { title: '接口管理', noCache: true },
  component: (resolve) => require(['@/views/core/center/interf/index'], resolve), // 胜宏项目
  hidden: true
},
{
  path: '/recipeManage3',
  meta: { title: '配方历史管理', noCache: true },
  component: (resolve) => require(['@/views/eap/project/sh/history/index'], resolve), // 胜宏项目
  hidden: true
},
{
  path: '/recipe&andon',
  meta: { title: '配方&ANDON', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/recipe/recipe&andon'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/pack/project/tripod/task/hb',
  meta: { title: '工单管理', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/task/hb/index'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/pack/project/tripod/extra',
  meta: { title: '额外参数', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/extra/index'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/productInfoIndex',
  meta: { title: '包装线生产信息', noCache: true },
  component: (resolve) => require(['@/views/pack/project/tripod/productInfo/index'], resolve), // 湖北健鼎项目
  hidden: true
},
{
  path: '/materialBatchScan',
  meta: { title: '物料批次扫描', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/materialBatchScan'], resolve), // 双环项目
  hidden: true
},
{
  path: '/productionBoard',
  meta: { title: '生产数据看板', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/productionBoard'], resolve), // 双环项目
  hidden: true
},
{
  path: '/shOeeBoard1',
  meta: { title: '产线当月统计报表', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/oeeBoard1'], resolve), // 双环项目
  hidden: true
},
{
  path: '/shOeeBoard2',
  meta: { title: '产线当日统计报表', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/oeeBoard2'], resolve), // 双环项目
  hidden: true
},
{
  path: '/shOeeBoard3',
  meta: { title: '产线当月统计报表-查询', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/oeeBoard3'], resolve), // 双环项目
  hidden: true
},
{
  path: '/sh/alarm/dashboard',
  meta: { title: '设备状态看板', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/index'], resolve), // 双环项目
  hidden: true
},
{
  path: '/sh/alarm/dashboard1',
  meta: { title: '设备状态看板', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/index1'], resolve), // 双环项目
  hidden: true
},
{
  path: '/sh/alarm/dashboard2',
  meta: { title: '设备状态看板', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/index2'], resolve), // 双环项目
  hidden: true
},
{
  path: '/repairStationIndex',
  meta: { title: '返修工位上线', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/repairStationIndex'], resolve), // 双环项目
  hidden: true
},
{
  path: '/materialUnbind',
  meta: { title: '物料解绑', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/materialUnbind'], resolve), // 双环项目
  hidden: true
},
{
  path: '/materialUnbindcopy',
  meta: { title: '物料解绑', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/materialUnbindcopy'], resolve), // 双环项目
  hidden: true
},
{
  path: '/repairrecord2',
  meta: { title: '返修历史查询', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/repairrecord2'], resolve), // 双环项目
  hidden: true
},
{
  path: '/repairStationIndex2',
  meta: { title: '返修工位上线', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/repairStationIndex2'], resolve), // 双环项目
  hidden: true
},
{
  path: '/stationQuality',
  meta: { title: '质量数据查询', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/hmi/stationQuality'], resolve), // 双环项目
  hidden: true
},
{
  path: '/mes/project/sh/workpiece',
  meta: { title: '工件追溯查询', noCache: true },
  component: (resolve) => require(['@/views/mes/project/sh/workpiece/index'], resolve), // 双环项目
  hidden: true
},
{
  path: '/krsCycleTask',
  meta: { title: '科瑞斯循环任务写入', noCache: true },
  component: (resolve) => require(['@/views/eap/project/krs/hmi/cycleTask/index'], resolve),
  hidden: true
},
{
  path: '/eachPosition',
  meta: { title: '暂存机信息查询', noCache: true },
  component: (resolve) => require(['@/views/eap/project/dy/hmi/eachPosition/index'], resolve),
  hidden: true
},
// 泰国护士**************************/
{
  path: '/hsRecipeMain',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/tlhs/index'], resolve), // 主页面
  hidden: true
},
// 晟丰主设备**************************/
{
  path: '/sfRecipeMain',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/sfjd/index'], resolve), // 主页面
  hidden: true
},
{
  path: '/sfRecipeMager',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/eap/project/sfjd/recipe/index'], resolve), // 配方管理
  hidden: true
},
{
  path: '/sfMaterialMaintenance',
  meta: { title: '物料维护', noCache: true },
  component: (resolve) => require(['@/views/eap/project/sfjd/material/index'], resolve), // 物料维护
  hidden: true
},
// 泰国鹏晟-主界面-下发功能国际化
{
  path: '/tlpsStationMonitor',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/tlps/index'], resolve), // 主页面
  hidden: true
},
// 淮安庆鼎、在主界面增加配方下发工作
{
  path: '/haqdRecipeMain',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/haqd/index'], resolve), // 主页面
  hidden: true
},
// 黄石群立 -- 塞孔
{
  path: '/sfHsqlRecipeMain',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/hsql/index'], resolve), // 主页面
  hidden: true
},
{
  path: '/sfHsqlRecipeMager',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) => require(['@/views/eap/project/hsql/recipe/index'], resolve), // 配方管理
  hidden: true
},
{
  path: '/sfHsqlMaterialMaintenance',
  meta: { title: '物料维护', noCache: true },
  component: (resolve) => require(['@/views/eap/project/hsql/material/index'], resolve), // 物料维护
  hidden: true
},

// 珠海超毅主界面
{
  path: '/zhcyStationMonitor',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/eap/project/zhcy/index'], resolve), // 珠海超毅主页面，志圣合作
  hidden: true
},
// 上海WMS* ***********************************/
{
  path: '/dcs/project/wms/dashboard/index',
  meta: { title: '中冶钢构库区智能调度可视化看板', noCache: true },
  component: (resolve) => require(['@/views/dcs/project/wms/dashboard/index'], resolve), // 上海WMS库区智能调度可视化看板
  hidden: true
},
{
  path: '/alarmDetails',
  meta: { title: '程控行车设备状态看板', noCache: true },
  component: (resolve) => require(['@/views/dcs/project/wms/dashboard/alarmDetails'], resolve), // 上海WMS程控行车设备状态看板
  hidden: true
},
//* ***********************************/
// 中铝瑞闽
{
  path: '/fjrm/dashboard/index',
  meta: { title: '中铝瑞闽废料二期立体智能仓库', noCache: true },
  component: (resolve) => require(['@/views/dcs/project/fjrm/dashboard/index'], resolve), // 中铝瑞闽wms
  hidden: true
},
// 威海wms
{
  path: '/wh/dashboard/index',
  meta: { title: '智能堆场可视化看板', noCache: true },
  component: (resolve) => require(['@/views/dcs/project/whzsj/dashboard/index'], resolve), // 威海wms
  hidden: true
},
/* 奥芯包装机 */
{
  path: '/pack/project/amt/main',
  meta: { title: '主页面', noCache: true },
  component: (resolve) => require(['@/views/pack/project/amt/main/index'], resolve),
  hidden: true
},
{
  path: '/pack/project/amt/plan',
  meta: { title: '工单管理', noCache: true },
  component: (resolve) =>
    require(['@/views/pack/project/amt/plan/index'], resolve),
  hidden: true
},
{
  path: '/pack/project/amt/recipe',
  meta: { title: '配方管理', noCache: true },
  component: (resolve) =>
    require(['@/views/pack/project/amt/recipe/index'], resolve),
  hidden: true
},
{
  path: '/secsaisRecipe',
  meta: { title: 'SECS/AIS配方管理', noCache: true },
  component: (resolve) => require(['@/views/eap/core/secsais/index'], resolve),
  hidden: true
}
]

export default new Router({
  // mode: 'hash',
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
