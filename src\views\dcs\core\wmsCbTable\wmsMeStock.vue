<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务号:">
                                <!-- 任务号 -->
                                <el-input v-model="query.task_num" clearable size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormFirst col-md-8 col-12" v-if="false">
                        <div class="formChild col-md-3 col-12">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.warehousingMethod')">
                                <!-- 入库方式 -->
                                <el-select v-model="query.task_way" clearable filterable>
                                    <el-option v-for="item in dict.TASK_WAY" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.taskType')">
                                <!-- 任务类型 -->
                                <el-select v-model="query.task_type" clearable filterable>
                                    <el-option v-for="item in dict.TASK_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.warehousingTime')">
                                <!-- 存放天数 -->
                                <!-- <el-date-picker
                                    v-model="query.stock_d_time"
                                    type="date"
                                    size="small"
                                    align="right"
                                    unlink-panels
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                /> -->
                                <el-input v-model="query.stock_d_time" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockCode')">
                                <!-- 天车编码 -->
                                <el-input v-model="query.car_code" clearable size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.locationID')" prop="stock_id">
                                <!-- 库位ID -->
                                <el-input type="number" v-model="form.stock_id" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.taskSource')" prop="task_from">
                                <!-- 任务来源 -->
                                <el-input v-model="form.task_from" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.taskNumber')" prop="task_num">
                                <!-- 任务号 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.serialNumber')" prop="serial_num">
                                <!-- 序列号 -->
                                <el-input v-model="form.serial_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.batchNumber')" prop="lot_num">
                                <!-- 批次号 -->
                                <el-input v-model="form.lot_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.inventoryLocationSorting')" prop="stock_index">
                                <!-- 库位排序 -->
                                <el-input v-model="form.stock_index" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.ZAxisCoordinates')" prop="location_z">
                                <!-- Z轴坐标 -->
                                <el-input v-model="form.location_z" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.isInventoryLocked')" prop="lock_flag">
                                <!-- 库存是否锁定 -->
                                <el-radio-group v-model="form.lock_flag">
                                    <el-radio label="0">是</el-radio>
                                    <el-radio label="1">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.warehousingMethod')" prop="task_way">
                                <!-- 入库方式 -->
                                <el-select v-model="query.task_way" clearable filterable>
                                    <el-option v-for="item in dict.TASK_WAY" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.taskType')" prop="task_type">
                                <!-- 任务类型 -->
                                <el-select v-model="query.task_type" clearable filterable>
                                    <el-option v-for="item in dict.TASK_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.findTheYCoordinate')" prop="xz_location_y">
                                <!-- 寻中Y坐标 -->
                                <el-input type="number" v-model="form.xz_location_y" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.isTheSearchCompleted')" prop="xz_finish_flag">
                                <!-- 是否完成寻中 -->
                                <el-radio-group v-model="form.xz_finish_flag">
                                    <el-radio label="0">是</el-radio>
                                    <el-radio label="1">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.warehousingTime')" prop="stock_d_time">
                                <!-- 存放天数 -->
                                <el-input v-model="form.stock_d_time" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.isInventoryAlarm')" prop="alarm_flag">
                                <!-- 是否库存超期报警 -->
                                <el-radio-group v-model="form.alarm_flag">
                                    <el-radio label="0">是</el-radio>
                                    <el-radio label="1">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockCode')" prop="car_code">
                                <!-- 天车编码 -->
                                <el-input v-model="form.car_code" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" fixed prop="stock_d_id"/>
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                                <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                                <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                                <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_id }}</el-descriptions-item>
                                <el-descriptions-item label="库位ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_id }}</el-descriptions-item>
                                <el-descriptions-item label="任务来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_from }}</el-descriptions-item>
                                <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                                <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                                <el-descriptions-item label="批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_index }}</el-descriptions-item>
                                <el-descriptions-item label="Z轴坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_z }}</el-descriptions-item>
                                <el-descriptions-item label="库存是否锁定" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lock_flag == 'Y' ? '是' : '否' }}</el-descriptions-item>
                                <el-descriptions-item label="入库方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{  dict.label.TASK_WAY[props.row.task_way]}}</el-descriptions-item>
                                <el-descriptions-item label="任务类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.TASK_TYPE[props.row.task_type] }}</el-descriptions-item>
                                <el-descriptions-item label="寻中Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.xz_location_y }}</el-descriptions-item>
                                <el-descriptions-item label="是否完成寻中" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.xz_finish_flag == 'Y' ? '是' : '否'}}</el-descriptions-item>
                                <el-descriptions-item label="存放天数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_d_time }}</el-descriptions-item>
                                <el-descriptions-item label="是否库存超期报警" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.alarm_flag == 'Y' ? '是' : '否'}}</el-descriptions-item>
                                <el-descriptions-item label="天车编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_code }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <!-- 任务来源 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_from"
                            :label="$t('lang_pack.wmsCbTable.taskSource')" width="130" align='center'/>
                        <!-- 任务号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_num"
                            :label="$t('lang_pack.wmsCbTable.taskNumber')" width="130" align='center'/>
                        <!-- 序列号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="serial_num"
                            :label="$t('lang_pack.wmsCbTable.serialNumber')" width="100" align='center'/>
                        <!-- 批次号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="lot_num"
                            :label="$t('lang_pack.wmsCbTable.batchNumber')" width="100" align='center'/>
                        <!-- 库位排序 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="stock_index"
                            :label="$t('lang_pack.wmsCbTable.inventoryLocationSorting')" width="100" align='center'/>
                        <!-- Z轴坐标 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="location_z"
                            :label="$t('lang_pack.wmsCbTable.ZAxisCoordinates')" width="100" align='center'/>
                        <!-- 库存是否锁定 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="lock_flag"
                            :label="$t('lang_pack.wmsCbTable.isInventoryLocked')" width="120" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.lock_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <!-- 入库方式 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_way"
                            :label="$t('lang_pack.wmsCbTable.warehousingMethod')" width="100" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.TASK_WAY[scope.row.task_way] }}
                            </template>
                        </el-table-column>
                        <!-- 任务类型 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_type"
                            :label="$t('lang_pack.wmsCbTable.taskType')" width="100" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
                            </template>
                        </el-table-column>
                        <!-- 天车编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_code"
                            :label="$t('lang_pack.wmsCbTable.crownBlockCode')" width="130" align='center'/>
                        <!-- 寻中Y坐标 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="xz_location_y"
                            :label="$t('lang_pack.wmsCbTable.findTheYCoordinate')" width="130" align='center'/>
                        <!-- 是否完成寻中 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="xz_finish_flag"
                            :label="$t('lang_pack.wmsCbTable.isTheSearchCompleted')" width="120" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.xz_finish_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <!-- 是否库存超期报警 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="alarm_flag"
                            :label="$t('lang_pack.wmsCbTable.isInventoryAlarm')" width="160" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.alarm_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudWmsMeStock from '@/api/dcs/core/wmsCbTable/wmsMeStock'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    stock_d_id: '',
    stock_id: '',
    task_from: '',
    task_num: '',
    serial_num: '',
    lot_num: '',
    stock_index: '',
    location_z: '',
    lock_flag: '0',
    task_way: '',
    task_type: '',
    xz_location_y: '0',
    xz_finish_flag: '0',
    stock_d_time: '',
    alarm_flag: '0',
    car_code:''

}
export default {
    name: 'WMSMESTOCK',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: 'WMS天车库存表',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'stock_d_id',
            // 排序
            sort: ['stock_d_id asc'],
            // CRUD Method
            crudMethod: { ...crudWmsMeStock },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                stock_d_id: [{ required: true, message: '请选择库存表ID', trigger: 'blur' }],
                stock_id: [{ required: true, message: '请选择库位ID', trigger: 'blur' }],
                task_from: [{ required: true, message: '请选择任务来源', trigger: 'blur' }],
                task_num: [{ required: true, message: '请选择任务号', trigger: 'blur' }],
                serial_num: [{ required: true, message: '请选择序列号', trigger: 'blur' }],
                stock_index: [{ required: true, message: '请选择库位排序', trigger: 'blur' }],
                location_z: [{ required: true, message: '请选择Z轴坐标', trigger: 'blur' }],
                lock_flag: [{ required: true, message: '请选择库存是否锁定', trigger: 'blur' }],
                task_way: [{ required: true, message: '请选择入库方式', trigger: 'blur' }],
                task_type: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
                xz_location_y: [{ required: true, message: '请选择寻中Y坐标', trigger: 'blur' }],
                stock_d_time: [{ required: true, message: '请选择存放天数', trigger: 'blur' }],
                alarm_flag: [{ required: true, message: '请选择是否库存超期报警', trigger: 'blur' }],
            },
        }
    },
    dicts: ['TASK_WAY','TASK_TYPE'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {}
}
</script>
  