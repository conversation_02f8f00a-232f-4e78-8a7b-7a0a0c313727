import request from '@/utils/request'

const context = 'aisEsbWeb/dcs/project/xg/alarm-histories'

export function sel(params) {
  return request({
    url: context,
    method: 'get',
    params
  })
}

export function edit(data) {
  return request({
    url: context + '/' + data.id,
    method: 'patch',
    data
  })
}

export function add(data) {
  return request({
    url: context,
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: context + '/' + (data.id || data.ids),
    method: 'delete',
    data
  })
}

export default { add, del, edit, sel }
