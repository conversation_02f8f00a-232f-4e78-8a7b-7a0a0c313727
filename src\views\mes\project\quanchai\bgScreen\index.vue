<template>
  <div class="screen_container">
    <div class="screen_head">
      <h2>装三车间集配区物料数字看板</h2>
      <div class="headLogo">
        <img class="logo" src="@/assets/images/qcLogo.png">
        <div class="imgtime"><img src="@/assets/images/time.png"><span class="showTime">{{ gettime }}</span><span class="showTime timeMargin">{{ week }}</span></div>
      </div>
    </div>
    <div class="mainbox">
      <div class="column">
        <div class="columnFirst">
          <div class="panel panelOne">
            <div class="panelHeight panelbgone">
              <p>物料总呼叫次数：<span>{{ callNum }}</span></p>
            </div>
            <div class="panelbgone">
              <div id="indicatorr" class="indicatorrone" />
            </div>
          </div>
          <div class="panel panelTwo panelbgtwo" @mouseover="enter" @mouseout="out">
            <el-table
              ref="table"
              :data="materialTableData"
              height="740"
            >
              <el-table-column 
                prop="prod_line_code"
                label="生产线"
              />
              <el-table-column 
                prop="station_des"
                label="工位描述"
              />
              <el-table-column 
                prop="andon_type_des"
                label="呼叫类型"
              />
              <el-table-column 
                prop="andon_station_type_i_des"
                label="Andon明细"
              />
              <el-table-column 
                prop="material_code"
                label="物料编码"
              />
              <el-table-column 
                prop="material_des"
                label="物料描述"
              />
              <el-table-column 
                prop="happen_date"
                label="发生时间"
              />
              <el-table-column 
                prop="happen_user"
                label="呼叫人"
              />
              <el-table-column  prop="finish_flag" label="状态">
                <template slot-scope="scope">
                  <span>{{ scope.row.finish_flag === 'N' ? '处理中' : '已处理' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { selMaterialWare } from '@/api/mes/project/mesQcMaterialWarehouseBoard'
import tooltipShow from 'roc-tooltip-show'
export default {
  name: 'MES_QC_BG_SCREEN',
  data() {
    return {
      indicatorrOption: {
        color: ['#24e42f', '#03a9f4', '#ffc107'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          data: ['Andon总数量', '处理中', '处理完成'],
          textStyle: {
            color: '#ffffff',
            fontSize: 22,
            fontWeight: 700
          },
          top: '10%'
        },
        series: [
          {
            name: '数据状态',
            type: 'pie',
            radius: '70%',
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold',
                color: '#ffffff'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: '', name: 'Andon总数量' },
              { value: '', name: '处理中' },
              { value: '', name: '处理完成' }
            ],
            top: '10%'
          }
        ]
      },
      indicatorr: null,
      materialTableData: [],
      timer: '',
      timer1: '',
      timer2: '',
      gettime: '',
      week: '',
      callNum: ''
    }
  },
  created: function() {
    this.getTime()
    this.timer1 = setInterval(() => {
      this.getTime()
    }, 1000)
  },
  mounted() {
    // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果(此配置为每100毫秒移动1像素)
    this.timer = setInterval(() => {
      this.move()
    }, 30)
    this.echartsInit()
    this.initMaterialWare()
    this.timer2 = setInterval(() => {
      this.initMaterialWare()
    }, 10000)
  },
  // 离开此页面时销毁定时器
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
    clearInterval(this.timer2)
  },
  methods: {
    // 加载工位屏计划数/完成数据
    initMaterialWare() {
      selMaterialWare()
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data === null || res.data.length === 0) {
            this.today_plan = ''
            this.current_offline = ''
          }
          this.callNum = res.data.callNum
          this.indicatorrOption.series[0].data[0].value = res.data.callNum
          this.indicatorrOption.series[0].data[1].value = res.data.notCompletedNum
          this.indicatorrOption.series[0].data[2].value = res.data.completedNum
          this.indicatorr.setOption(this.indicatorrOption)
          this.materialTableData = res.data.andonList
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    enter() {
      clearInterval(this.timer)
    },
    out() {
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        this.move()
      }, 30)
    },
    move() {
      // 拿到表格挂载后的真实DOM
      const table = this.$refs.table
      // 拿到表格中承载数据的div元素
      const divData = table.bodyWrapper
      // 元素自增距离顶部1像素
      divData.scrollTop += 1
      // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
      const divDataHeight = Math.ceil(divData.clientHeight + divData.scrollTop) + 3
      console.log(divData.clientHeight, divData.scrollTop, divData.scrollHeight, divDataHeight)
      if (divDataHeight >= divData.scrollHeight) {
        // 重置table距离顶部距离
        divData.scrollTop = 0
      }
    },
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    },
    // 初始化echarts
    echartsInit() {
      this.indicatorr = this.$echarts.init(document.getElementById('indicatorr'))
      // 用法示例
      const options = {
        interval: 2000,
        loopSeries: false,
        seriesIndex: 0,
        updateData: null
      }
      tooltipShow(this.indicatorr, this.indicatorrOption, options)
      window.addEventListener('resize', () => {
        this.indicatorr.resize()
      })
      this.indicatorr.setOption(this.indicatorrOption)
    }
  }
}
</script>

<style lang="less" scoped>
.screen_container{
  background: url('~@/assets/images/wcbg.jpg') no-repeat #000;
  background-size: cover;
  height:calc(100vh);
  .screen_head{
    position: relative;
    background: url('~@/assets/images/headBg.png') no-repeat;
    background-size: 100% 100%;
    height: 140px;
    h2{
        margin: 0;
        height: 140px;
        line-height: 100px;
        text-align: center;
        color: #ffffff;
        font-weight: 700;
        font-size: 40px;
    }
    .headLogo{
      width: 100%;
      position: absolute;
      top: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      .logo{
       width: 100px;
       margin-left: 152px;
      }
      .imgtime{
        display: flex;
        align-items: center;
        img{
          width: 20px;
          margin-right: 5px;
        }
        .showTime{
          color: #00e2fd;
          font-size: 30px;
          font-weight: 700;
        }
        .timeMargin{
          margin-left: 10px;
        }
      }
    }
  }
  .mainbox{
    // min-width: 1024px;
    // max-width: 1920px;
    padding: 0.125rem 0.125rem 0;
    display: flex;
    .column{
      width: 100%;
      .columnFirst{
        display: flex;
        .panel{
          padding: 0 0.1875rem 0.1875rem;
          margin-bottom: 0.1875rem;
        }
        .panelOne{
          width: 30%;
          margin-right: 0.1875rem;
          .panelHeight{
            height: 220px;
            margin-bottom: 10px;
          }
          .panelbgone{
            background: url('~@/assets/images/bordercenter.png') no-repeat;
            background-size: 100% 100%;
              p{
              font-size: 30px;
              color: #ffffff;
              font-weight: 700;
              margin: 0;
              height: 220px;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-wrap: wrap;
                span{
                  color: #00ed1f;
                  font-size: 60px;
                }
            }
          }
          .indicatorrone{
            width: 100%;
            height: 600px;
          }
        }
        .panelTwo{
          width: 70%;
        }
        .panelbgtwo{
          background: url('~@/assets/images/bordercenter.png') no-repeat;
            background-size: 100% 100%;
        }
      }
    }
  }
}
// 细化滚动条
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 0 !important;
  height: 0 !important;
}
:deep(.el-table){
  background: none !important;
  margin-top: 30px !important;
  th{
    background: #1daad6 !important;
    color: #fff !important;
    font-size: 16px !important;
  }
  tr{
    background: none;
    color: #fff !important;
    font-size: 28px !important;
  }
  .cell{
    line-height: 26px;
  }
}
:deep(.el-table__body tr.current-row > td){
    background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table__row:hover){
     background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background: none !important;
}
</style>
