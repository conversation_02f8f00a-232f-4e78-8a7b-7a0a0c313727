<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="药水名称：">
                <el-input v-model="query.potions_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="操作时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            height="448"
            max-height="448"
            highlight-current-row
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="potions_record_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="potions_name" label="药水名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="dosage" label="用量" />
            <el-table-column  :show-overflow-tooltip="true" prop="unit" label="单位" />
            <el-table-column  :show-overflow-tooltip="true" prop="add_time" label="添加时间" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import eapDyPotionsRecord from '@/api/eap/project/dayuan/eapDyPotionsRecord'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  potions_record_id: '',
  potions_name: '',
  item_date: ''
}
export default {
  name: 'EAP_DY_POTIONS_RECORD',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {},
  cruds() {
    return CRUD({
      title: '药水详情',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'potions_record_id',
      // 排序
      sort: ['potions_record_id asc'],
      // CRUD Method
      crudMethod: { ...eapDyPotionsRecord },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        search: false,
        refresh: false,
        grid: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'eap_dy_potions_record:add'],
        down: ['admin', 'eap_dy_potions_record:down']
      }
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
</style>
