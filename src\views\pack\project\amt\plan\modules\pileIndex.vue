<template>
  <el-card shadow="always" style="margin-top: 10px">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog
          title="明细"
          width="80%"
          :before-close="handleClose"
          :visible.sync="dialogVisible"
        >
          <el-card ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-8 col-12">
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="标签ID:">
                      <el-input v-model="query.pile_barcode" clearable size="small" />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormSecond formChild col-md-2 col-12">
                  <el-form-item>
                    <rrOperation>
                      <template slot="left">
                        <el-button class="filter-item" size="small" type="primary" icon="el-icon-plus" @click="crud.toAdd">{{ $t('lang_pack.commonPage.add') }}</el-button>
                      </template>
                    </rrOperation>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </el-card>
          <div class="crud-opts" />
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
            <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
              <el-form-item label="Lot No" prop="lot_num">
                <el-input v-model="form.lot_num" disabled />
              </el-form-item>
              <el-form-item label="料号" prop="model_type">
                <el-input v-model="form.model_type" disabled />
              </el-form-item>
              <el-form-item label="序号" prop="pile_index">
                <el-input v-model="form.pile_index" disabled />
              </el-form-item>
              <el-form-item label="条码" prop="pile_barcode">
                <el-input v-model="form.pile_barcode" />
              </el-form-item>
              <el-form-item label="数量" prop="array_count">
                <el-input-number v-model="form.array_count" :min="0" :max="99999" :step="1" clearable />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.IsItATrunk')">
                <el-radio-group v-model="form.trunk_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.value === "Y" ? $t('lang_pack.vie.Yes') : $t('lang_pack.vie.NO')
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="'是否重工'">
                <el-radio-group v-model="form.retry_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.value === "Y" ? $t('lang_pack.vie.Yes') : $t('lang_pack.vie.NO')
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
            </div>
          </el-drawer>
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
          >
            <!-- 时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_date"
              :label="$t('lang_pack.vie.time')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              label="批号"
              width="120"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pile_barcode"
              label="标签ID"
              width="150"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pile_index"
              :label="$t('lang_pack.vie.pileIndex')"
              width="130"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_count"
              :label="$t('view.table.totalQuantity')"
              width="120"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="130"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pile_status"
              :label="$t('lang_pack.vie.pileStatus')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  :class="scope.row.pile_status == 'OK' ? 'btnGreen' : 'btnRed'"
                >
                  {{ scope.row.pile_status }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pile_ng_msg"
              :label="$t('lang_pack.vie.pile_ng_msg')"
              width="120"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="trunk_flag"
              :label="$t('lang_pack.vie.IsItATrunk')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.trunk_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="retry_flag"
              label="是否重工"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.retry_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="left">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.enable_flag === 'N'"
                  slot="reference"
                  type="text"
                  size="small"
                  @click="doEnable(scope.row)"
                >{{
                  $t('lang_pack.hmiMain.enable')
                }}</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  @click="doDelete(scope.row)"
                >{{
                  $t('lang_pack.commonPage.remove')
                }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination />
        </el-dialog>
        <el-dialog :title="jsonTitle" width="50%" :visible.sync="jsonDialogVisible">
          <el-input v-model="jsonStr" autosize type="textarea" :rows="20" :placeholder="$t('lang_pack.interfaceLogs.pleaseEnter')" style="max-height: 450px;overflow: auto;" />
          <span style="color:red;">{{ jsonErrorMsg }}</span>
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import Cookies from 'js-cookie'
import crudPile from '@/api/pack/core/pile'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
const defaultForm = {
  lot_num: '',
  model_type: '',
  pile_index: 0,
  pile_barcode: '',
  array_count: 0
}
export default {
  name: 'PILE_DETAILS',
  components: { pagination, rrOperation },
  cruds() {
    return CRUD({
      title: '',
      // 唯一字段
      idField: 'pile_index',
      // 排序
      sort: ['pile_index asc'],
      // CRUD Method
      crudMethod: { ...crudPile },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        planId: this.propsData.plan_id
      }
    })
  },
  props: {
    plan_id: {
      type: String
    },
    data: {
      type: Object
    }
  },
  dicts: ['TASK_TYPE', 'QR_TYPE', 'DEPOSIT_POSITION', 'ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {},
      dialogVisible: false,
      jsonStr: '',
      jsonErrorMsg: '',
      jsonTitle: '',
      jsonDialogVisible: false,
      tableDialogVisible: false
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    },
    doEnable(row) {
      this.$confirm('是否重启该数据?', this.$t('lang_pack.vie.prompt'),
        {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
          type: 'warning'
        })
        .then(() => {
          row.enable_flag = 'Y'
          crudPile
            .edit(row)
            .then(res => {
              this.$message.success(this.$t('lang_pack.commonPage.operationSuccessful'))
              this.crud.toQuery()
            })
            .catch(ex => {
              this.$message.error(this.$t('lang_pack.commonPage.operationfailure') + ex)
            })
        })
        .catch(() => {
        })
    },
    doDelete(row) {
      this.$confirm('是否删除该数据?', this.$t('lang_pack.vie.prompt'),
        {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
          type: 'warning'
        })
        .then(() => {
          crudPile
            .del({
              user_name: Cookies.get('userName'),
              id: row.id
            })
            .then(res => {
              this.$message.success(this.$t('lang_pack.commonPage.deleteSuccesful'))
              this.crud.toQuery()
            })
            .catch(ex => {
              this.$message.error(this.$t('lang_pack.commonPage.operationfailure') + ex)
            })
        })
        .catch(() => {
        })
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      let pileIndex = 0
      if (crud.data != null && crud.data.length > 0) {
        pileIndex = crud.data[crud.data.length - 1].pile_index + 1
      }
      this.form.plan_id = this.data.plan_id || this.plan_id
      this.form.lot_num = this.data.lot_num
      this.form.model_type = this.data.model_type
      this.form.pile_index = pileIndex
      this.form.pile_status = 'OK'
      return true
    }
  }
}
</script>
<style scoped lang="less">
.crud-opts {
  padding: 4px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.crud-opts .crud-opts-right {
  margin-left: auto;
}
.crud-opts .crud-opts-right span {
  float: left;
}
.el-pagination {
    float: none !important;
    text-align: right;
}

.btnGreen {
    background-color: #0d0 !important;
    border: none !important;
}

.btnRed {
    background-color: #ee1216 !important;
    border: none !important;
}
</style>
