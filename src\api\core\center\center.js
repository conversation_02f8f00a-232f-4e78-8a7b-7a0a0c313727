import request from '@/utils/request'

// 查询中心
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCenterSel',
    method: 'post',
    data
  })
}
// 新增中心
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCenterIns',
    method: 'post',
    data
  })
}
// 修改中心
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCenterUpd',
    method: 'post',
    data
  })
}
// 删除中心
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCenterDel',
    method: 'post',
    data
  })
}

// 查询中心LOV
export function lovCenter(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCenterLov',
    method: 'post',
    data
  })
}

// 部署服务
export function deploySysCoreCenter(data) {
  return request({
    url: 'aisEsbWeb/core/center/SysCoreCenterDeploy',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, lovCenter, deploySysCoreCenter }

