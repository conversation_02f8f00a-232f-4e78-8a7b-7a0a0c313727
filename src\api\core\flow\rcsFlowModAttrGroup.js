import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModAttrGroupSel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModAttrGroupIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModAttrGroupUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModAttrGroupDel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del }

