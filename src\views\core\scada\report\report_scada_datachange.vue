<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <!--查询条件-->
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
          <el-form ref="query" :inline="true" size="mini" style="margin: 0; padding: 0">
            <el-form-item :label="$t('view.form.instance') + ': '" style="margin: 0px 0px 5px 0px">
              <el-select
                v-model="query.client_code"
                filterable
                size="mini"
                style="width: 200px"
                :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                @change="clientCodeSelect"
              >
                <el-option v-for="(item,index) in scadaClientLov" :key="index" :label="item.client_des" :value="item.client_code">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.client_code }}</span>
                  <span style="float: right">{{ item.client_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('view.form.tagGroup') + ': '" style="margin: 0px 0px 5px 0px">
              <el-select
                v-model="query.tag_group_code"
                clearable
                size="mini"
                style="width: 200px"
                :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                @change="tagGroupSelect"
              >
                <el-option v-for="item in scadaTagGroupLov" :key="item.tag_group_code" :label="item.tag_group_des" :value="item.tag_group_code">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.tag_group_code }}</span>
                  <span style="float: right">{{ item.tag_group_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('view.form.tag') + ': '" style="margin: 0px 0px 5px 0px">
              <el-select
                v-model="query.tag_id"
                clearable
                size="mini"
                style="width: 200px"
                :placeholder="$t('view.enum.placeholder.pleaseChosen')"
              >
                <el-option v-for="item in scadaTagLov" :key="item.tag_id" :label="item.tag_des" :value="item.tag_id">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.tag_id }}</span>
                  <span style="float: right">{{ item.tag_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :label="$t('view.form.processWarning') + ': '"
              style="margin: 0px 0px 5px 0px"
            >
              <el-select
                v-model="query.tag_alarm_flag"
                clearable
                size="mini"
                style="width: 100px"
                :placeholder="$t('view.enum.placeholder.pleaseChosen')"
              >
                <el-option v-for="item in tagAlarmFlagLov" :key="item.tag_alarm_flag" :label="item.tag_alarm_flag_des" :value="item.tag_alarm_flag" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('view.form.timePeriod') + ': '" style="margin: 0px 0px 5px 0px">
              <el-date-picker
                ref="timepicker"
                v-model="query.create_time"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="~"
                :start-placeholder="$t('view.form.timePeriodStart')"
                :end-placeholder="$t('view.form.timePeriodEnd')"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                align="right"
                :picker-options="pickerOptions"
              />
            </el-form-item>
            <el-form-item style="margin: 0;float:right">
              <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="btnQuery('search','','')">
                {{ $t('view.button.search') }}
              </el-button>
              <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="btnQuery('reset','','')">
                {{ $t('view.button.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!--查询结果-->
        <el-card shadow="always" style="margin-top: 10px">
          <el-tabs v-model="tabValue" :tab-position="tabPosition" style="height:500px;" @tab-click="tabSelect">
            <el-tab-pane name="tableTab">
              <span slot="label">
                <i class="el-icon-s-grid" />
                {{ $t('view.table.tableForm') }}
              </span>
              <el-card>
                <el-table
                  ref="tableReport"
                  v-loading="loadingReport"
                  :data="dataReport"
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F4F7', color: '#757575' }"
                  border
                  height="450px"
                  :highlight-current-row="true"
                >
                  <el-table-column v-if="1 == 1" width="200" prop="id" label="id" />
                  <el-table-column :show-overflow-tooltip="true" width="200" prop="create_time" :label="$t('view.table.time')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="tag_id" :label="$t('view.table.tagID')" />
                  <el-table-column :show-overflow-tooltip="true" width="150" prop="tag_des" :label="$t('view.table.tagDescription')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="150" prop="new_val" :label="$t('view.table.collectionValue')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="pre_alarm_flag" :label="$t('view.table.isWarningSet')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="tag_alarm_flag" :label="$t('view.table.processWarning')">
                    <template slot-scope="scope">
                      <el-tag :type="scope.row.tag_alarm_flag === 'Y' ? 'danger' : 'success'" disable-transitions>
                        {{ scope.row.tag_alarm_flag }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="down_limit" :label="$t('view.table.lowerLimitValue')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="upper_limit" :label="$t('view.table.upperLimitValue')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="150" prop="old_val" :label="$t('view.table.oldValue')" />
                  <el-table-column :show-overflow-tooltip="true" width="200" prop="tag_only_key" :label="$t('view.table.tagUniqueKey')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="tag_attr" :label="$t('view.table.tagAttribute')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="data_type" :label="$t('view.table.dataType')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="block_name" :label="$t('view.table.area')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="block_addr" :label="$t('view.table.areaAddress')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="start_address" :label="$t('view.table.startAddress')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="data_length" :label="$t('view.table.dataLength')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="data_bit" :label="$t('view.table.bit')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="opc_addr" :label="$t('view.table.OPCAddress')" />
                  <el-table-column :show-overflow-tooltip="true" align="center" width="100" prop="simulated_flag" :label="$t('view.table.simulationFlag')" />
                </el-table>
                <el-row :gutter="20">
                  <el-col :span="4" :offset="10">
                    <div style="margin-top: 5px">
                      <el-button-group>
                        <el-button type="primary" icon="el-icon-arrow-left" @click="pageQuery('pre')">
                          {{ $t('view.pagination.previous') }}
                        </el-button>
                        <el-button type="primary" @click="pageQuery('next')">
                          {{ $t('view.pagination.next') }}
                          <i class="el-icon-arrow-right el-icon--right" />
                        </el-button>
                      </el-button-group>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-tab-pane>
            <el-tab-pane name="chartTab">
              <span slot="label">
                <i class="el-icon-s-data" />
                {{ $t('view.table.curveForm') }}
              </span>
              <el-card>
                <ECharts ref="chartReport" v-loading="loadingChartReport" :options="chartOptions" style="width: 1200px;height:450px;" />
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<!--JS-->
<script>
// 导入
import { selCellIP } from '@/api/core/center/cell'
import { InitTimePickRange, ScadaClientSelect, ScadaTagGroupSelect, ScadaTagSelect } from '@/api/core/scada/report'
import ECharts from 'vue-echarts'
import Cookies from 'js-cookie'
import axios from 'axios'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'
// 执行
export default {
  name: 'REPORT_SCADA_DATACHANGE',
  // 1.初始化组件
  components: {
    ECharts
  },
  // 2.初始化参数设置
  data() {
    return {
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      clientInfo: {},
      query: {
        client_code: '',
        tag_group_code: '',
        tag_id: '',
        tag_alarm_flag: '',
        create_time: null
      },
      scadaClientLov: [],
      scadaTagGroupLov: [],
      scadaTagLov: [],
      tagAlarmFlagLov: [
        { tag_alarm_flag: 'N', tag_alarm_flag_des: this.$t('view.table.normal') },
        { tag_alarm_flag: 'Y', tag_alarm_flag_des: this.$t('view.table.alarm') }
      ],
      tabPosition: 'left', // Tab方向
      tabValue: 'tableTab', // 当前Tab选择
      dataReport: [], // 表格数据
      loadingReport: false, // 表格是否正在加载
      loadingChartReport: false,
      tableSize: 100, // 默认表格单次查询100行数据
      chartOptions: {},
      pickerOptions: {}
    }
  },
  // 3.页面创建时加载
  created: function() {
    // 初始化日期选择器快捷选项
    this.pickerOptions = createDatePickerShortcuts(this.$i18n)

    // Scada实例查询
    ScadaClientSelect().then((res) => {
      var result = JSON.parse(JSON.stringify(res))
      if (result.hasOwnProperty('error')) {
        this.$message({
          message: this.$t('view.dialog.scadaInstanceBaseDataQueryFailed') + result.error,
          type: 'error'
        })
        this.scadaClientLov = []
        return
      }
      this.scadaClientLov = result.data
    }).catch(() => {
      this.$message({
        message: this.$t('view.dialog.scadaInstanceBaseDataQueryTimeoutOrSQLError'),
        type: 'error'
      })
    })
    // 自动加载开始时间结束时间(默认1天以内)
    this.query.create_time = InitTimePickRange(1)
  },
  // 5.页面渲染
  mounted() {
    // 散点图
    this.chartOptions = {
      title: {
        text: this.$t('view.table.tagDataScatterPlotAnalysis'),
        subtext: '',
        left: 'center'
      },
      legend: {
        data: [this.$t('view.table.dataAlarm'), this.$t('view.table.dataNormal')],
        left: 'right'
      },
      dataZoom: [{
        type: 'inside'
      }, {
        type: 'slider'
      }],
      tooltip: {
        showDelay: 0,
        formatter: function(params) {
          if (params.value.length >= 5) {
            return this.$t('view.table.serialNumber') + ': ' + params.value[0] + '<br/>' +
                                this.$t('view.table.collectionValue') + ': ' + params.value[1] + '<br/>' +
                                this.$t('view.table.time') + ': ' + params.value[2] + '<br/>' +
                                this.$t('view.table.isWarningSet') + ': ' + params.value[3] + '<br/>' +
                                this.$t('view.table.processWarningSign') + ': ' + params.value[4]
          }
        },
        axisPointer: {
          show: true,
          type: 'cross',
          lineStyle: {
            type: 'dashed',
            width: 1
          }
        }
      },
      xAxis: {
        name: this.$t('view.table.sampleQuantity')
      },
      yAxis: {
        name: this.$t('view.table.collectionValue')
      },
      series: [{
        name: this.$t('view.table.dataNormal'),
        type: 'scatter',
        symbolSize: 10,
        data: [],
        itemStyle: {
          normal: {
            color: 'green'
          }
        }
      },
      {
        name: this.$t('view.table.dataAlarm'),
        type: 'scatter',
        symbolSize: 10,
        data: [],
        itemStyle: {
          normal: {
            color: 'red'
          }
        }
      }
      ]
    }
  },
  // 4.页面执行方法事件
  methods: {
    // select change方法
    // 0.1 实例选择
    clientCodeSelect() {
      this.query.tag_group_code = ''
      this.query.tag_id = ''
      this.scadaTagGroupLov = []
      this.scadaTagLov = []
      if (this.query.client_code === '') {
        return
      }
      ScadaTagGroupSelect(this.query.client_code).then((res) => {
        var result = JSON.parse(JSON.stringify(res))
        if (result.hasOwnProperty('error')) {
          this.$message({
            message: this.$t('view.dialog.scadaInstanceTagGroupDataQueryFailed') + ': ' + result.error,
            type: 'error'
          })
          return
        }
        this.scadaTagGroupLov = result.data
      }).catch(() => {
        this.$message({
          message: this.$t('view.dialog.scadaInstanceTagGroupDataQueryTimeoutOrSQLError'),
          type: 'error'
        })
      })
      this.getCellIp()
    },
    getCellIp() {
      const cell_id = this.scadaClientLov.filter(a => a.client_code === this.query.client_code)[0].cell_id
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('view.dialog.querySelCellIPException'), type: 'error' })
        })
    },
    // 0.2 标签组选择
    tagGroupSelect() {
      this.query.tag_id = ''
      this.scadaTagLov = []
      if (this.query.client_code === '' || this.query.tag_group_code === '') {
        return
      }
      ScadaTagSelect(this.query.client_code, this.query.tag_group_code).then((res) => {
        var result = JSON.parse(JSON.stringify(res))
        if (result.hasOwnProperty('error')) {
          this.$message({
            message: this.$t('view.dialog.scadaInstanceTagDataQueryFailed') + ': ' + res.error,
            type: 'error'
          })
          return
        }
        this.scadaTagLov = result.data
      }).catch(() => {
        this.$message({
          message: this.$t('view.dialog.scadaInstanceTagDataQueryTimeoutOrSQLError'),
          type: 'error'
        })
      })
    },
    // 4.1 查询方法
    btnQuery(code, pageDirct, pageId) {
      if (code === 'reset') { // 重置
        this.query.client_code = ''
        this.query.tag_group_code = ''
        this.query.tag_id = ''
        this.query.tag_alarm_flag = ''
        this.scadaTagGroupLov = []
        this.scadaTagLov = []
        this.query.create_time = InitTimePickRange(1)
      } else {
        if (this.query.client_code === '') {
          this.$message({
            message: this.$t('view.dialog.selectInstance'),
            type: 'info'
          })
          return
        }
        if (this.query.tag_group_code === '') {
          this.$message({
            message: this.$t('view.dialog.pleaseSelectTagGroup'),
            type: 'info'
          })
          return
        }
        if (this.query.tag_id === '') {
          this.$message({
            message: this.$t('view.dialog.pleaseSelectTag'),
            type: 'info'
          })
          return
        }
        var start_time = this.query.create_time === null ? '' : this.query.create_time[0]
        var end_time = this.query.create_time === null ? '' : this.query.create_time[1]
        this.loadingReport = true
        if (this.cellIp === '' || this.webapiPort === '') {
          this.$message({
            message: this.$t('view.dialog.noUnitIPAndPortNumberObtained'),
            type: 'info'
          })
          return
        }
        var method = '/cell/core/scada/CoreScadaTagChangeReportSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        // A:表格刷新
        const data = {
          device_code: this.query.client_code,
          tag_id: this.query.tag_id,
          tag_alarm_flag: this.query.tag_alarm_flag,
          start_time: start_time,
          end_time: end_time,
          tableSize: this.tableSize,
          page_dirct: pageDirct,
          page_id: pageId
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0) {
              if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                if (pageId !== '') {
                  this.$message({
                    message: this.$t('view.dialog.bottom'),
                    type: 'info'
                  })
                } else {
                  this.dataReport = []
                }
                this.loadingReport = false
                return
              }
              this.dataReport = defaultQuery.data.data
              this.loadingReport = false
            } else {
              this.$message({
                message: this.$t('view.dialog.scadaReadDataChangeReportQueryFailed') + ': ' + res.error,
                type: 'error'
              })
              this.dataReport = []
              this.loadingReport = false
            }
          })
          .catch(ex => {
            this.loadingReport = false
            this.$message({
              message: this.$t('view.dialog.scadaReadDataChangeReportQueryTimeoutOrSQLError'),
              type: 'error'
            })
          })
        // B:曲线刷新
        if (code === 'search') {
          this.loadingChartReport = true
          if (this.query.tag_group_code === '' || this.query.tag_id === '') {
            this.chartOptions = {}
            this.loadingChartReport = false
            return
          }
          const data1 = {
            device_code: this.query.client_code,
            tag_id: this.query.tag_id,
            tag_alarm_flag: this.query.tag_alarm_flag,
            start_time: start_time,
            end_time: end_time,
            tableSize: '',
            page_dirct: '',
            page_id: ''
          }
          axios
            .post(path, data1, {
              headers: {
                'Content-Type': 'application/json'
              }
            })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.data.code === 0) {
                if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                  this.chartOptions = {}
                  this.loadingChartReport = false
                  return
                }
                var arrayData = defaultQuery.data.data
                var okList = []
                var ngList = []
                var upperLimit = -1
                var downLimit = -1
                var tagDes = ''
                for (var i = 0; i < arrayData.length; i++) {
                  var new_val = arrayData[i].new_val
                  var pre_alarm_flag = arrayData[i].pre_alarm_flag
                  var tag_alarm_flag = arrayData[i].tag_alarm_flag
                  var create_time = arrayData[i].create_time
                  var upper_limit = arrayData[i].upper_limit
                  var down_limit = arrayData[i].down_limit
                  var tValue = parseFloat(new_val)
                  if (!isNaN(tValue)) {
                    var item = []
                    item.push(i + 1)
                    item.push(tValue)
                    item.push(create_time)
                    item.push(pre_alarm_flag)
                    item.push(tag_alarm_flag)
                    if (tag_alarm_flag === 'Y') ngList.push(item)
                    else okList.push(item)
                    if (tagDes === '') {
                      tagDes = arrayData[i].tag_des
                      upperLimit = upper_limit
                      downLimit = down_limit
                    }
                  }
                }
                // 散点图
                this.chartOptions = {
                  title: {
                    text: this.$t('view.table.tagDataScatterPlotAnalysis'),
                    subtext: tagDes,
                    left: 'center'
                  },
                  legend: {
                    data: [this.$t('view.table.dataAlarm'), this.$t('view.table.dataNormal')],
                    left: 'right'
                  },
                  dataZoom: [{
                    type: 'inside'
                  }, {
                    type: 'slider'
                  }],
                  tooltip: {
                    showDelay: 0,
                    formatter: function(params) {
                      if (params.value.length >= 5) {
                        return this.$t('view.table.serialNumber') + ': ' + params.value[0] + '<br/>' +
                                this.$t('view.table.collectionValue') + ': ' + params.value[1] + '<br/>' +
                                this.$t('view.table.time') + ': ' + params.value[2] + '<br/>' +
                                this.$t('view.table.isWarningSet') + ': ' + params.value[3] + '<br/>' +
                                this.$t('view.table.processWarningSign') + ': ' + params.value[4]
                      }
                    },
                    axisPointer: {
                      show: true,
                      type: 'cross',
                      lineStyle: {
                        type: 'dashed',
                        width: 1
                      }
                    }
                  },
                  xAxis: {
                    name: this.$t('view.table.sampleQuantity')
                  },
                  yAxis: {
                    name: this.$t('view.table.collectionValue')
                  },
                  series: [{
                    name: this.$t('view.table.dataNormal'),
                    type: 'scatter',
                    symbolSize: 10,
                    data: okList,
                    itemStyle: {
                      normal: {
                        color: 'green'
                      }
                    },
                    markLine: {
                      silent: true,
                      lineStyle: {
                        color: '#FC7D02'
                      },
                      data: [{
                        yAxis: downLimit
                      },
                      {
                        yAxis: upperLimit
                      }
                      ]
                    }
                  },
                  {
                    name: this.$t('view.table.dataAlarm'),
                    type: 'scatter',
                    symbolSize: 10,
                    data: ngList,
                    itemStyle: {
                      normal: {
                        color: 'red'
                      }
                    },
                    markPoint: {
                      data: [
                        { type: 'max', name: 'Max' },
                        { type: 'min', name: 'Min' }
                      ]
                    }
                  }
                  ]
                }
                this.loadingChartReport = false
              } else {
                this.$message({
                  message: 'Scada数据变化报表查询失败:' + defaultQuery.data.msg,
                  type: 'error'
                })
                this.chartOptions = {}
                this.loadingChartReport = false
              }
            }).catch(() => {
              this.loadingChartReport = false
              this.$message({
                message: 'Scada数据快照报表查询超时或者SQL错误',
                type: 'error'
              })
            })
        }
      }
    },
    // 4.2 Tab切换
    tabSelect(tab, event) {},
    // 4.3 上一页或者下一页翻页
    pageQuery(pageDirct) {
      if (this.dataReport == null || this.dataReport.length <= 0) {
        this.$message({
          message: this.$t('view.dialog.noData'),
          type: 'info'
        })
        return
      }
      if (pageDirct === 'pre') {
        this.btnQuery('tablePage', pageDirct, this.dataReport[0].id)
      } else {
        this.btnQuery('tablePage', pageDirct, this.dataReport[this.dataReport.length - 1].id)
      }
    }
  }
}
</script>
<!--CSS-->
<style lang="scss" >
.box-card {min-height: calc(100vh);padding: 10px;}
:focus {outline: 0;}
.el-card__body {padding: 10px;}
</style>
