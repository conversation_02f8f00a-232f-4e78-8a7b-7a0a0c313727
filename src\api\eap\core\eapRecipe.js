import request from '@/utils/request'

// 查询配方维护信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeSel',
    method: 'post',
    data
  })
}
// 新增配方维护信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeIns',
    method: 'post',
    data
  })
}
// 修改配方维护信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeUpd',
    method: 'post',
    data  
  })
}
// 删除配方维护信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

