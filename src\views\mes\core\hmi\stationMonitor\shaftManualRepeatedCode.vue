<template>
  <div>
    <div style="margin-top: 10px; text-align: right">
      <el-button type="primary" @click="handleSendInfo()">重新定扭</el-button>
      <roleCheck
        v-if="roleCheckShow"
        ref="roleCheck"
        :role_user_id="userId"
        :role_func_code="role_func_code"
        :paramsCode="paramsCode"
        @roleCheck="roleCheck"
      />
    </div>
  </div>
</template>

<script>
import roleCheck from "@/views/core/hmi/roleCheck";
export default {
  components: { roleCheck },
  props: {
    tag_key_list: {
      type: Object,
      default: null,
    },
  },
  // 数据模型
  data() {
    return {
      roleCheckShow: false,
      userId: "",
      role_func_code: "",
      paramsCode: "Hmi_ShaftManualRepeatedCheckPwd",
    };
  },
  mounted: function () {},
  created: function () {},
  methods: {
    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      debugger
      this.roleCheckShow = false;
      if (roleFuncCode === "shaftManualRepeatedCode") {
        if (status === "OK") {
          var sendJson = {};
          var rowJson = [];
          var newRow = {
            TagKey: this.tag_key_list.StartShaftFlowFlag,
            TagValue: this.tag_key_list.StartShaftFlowValue,
          };
          rowJson.push(newRow);
          sendJson.Data = rowJson;
          sendJson.ClientName = "SCADA_WEB";
          var sendStr = JSON.stringify(sendJson);
          var topic =
            "SCADA_WRITE/" + this.tag_key_list.StartShaftFlowFlag.split("/")[0];
          this.$emit("mesShaftManualRepeatedCode", topic, sendStr);
        }
      }
    },

    handleSendInfo() {
      this.roleCheckShow = true;
      this.role_func_code = "shaftManualRepeatedCode";
      return;
    },
  },
};
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
