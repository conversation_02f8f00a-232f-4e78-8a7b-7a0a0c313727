<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          Panel条码
        </template>
        <el-input ref="webPanelNum" v-model="webPanelNum" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo('manual')">手动输入</el-button>
      <el-button type="primary" @click="handleSendInfo('move')">移走确认</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      webPanelNum: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webPanelNum.focus()
    })
  },
  methods: {
    handleSendInfo(type) {
      var webPanelletConfirmModel = '0'
      var checkFlag = false
      var panelBarCode = ''
      if (type === 'manual') {
        if (this.webPanelNum === '') {
          this.$message({ message: '请输入Panel条码', type: 'info' })
          return
        }
        webPanelletConfirmModel = '3'
        checkFlag = true
        panelBarCode = this.webPanelNum
      } else if (type === 'move') {
        webPanelletConfirmModel = '4'
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebPanelNum,
        TagValue: this.webPanelNum
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPanelletConfirmModel,
        TagValue: webPanelletConfirmModel
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPanelInfoRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebPanelNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr, checkFlag, panelBarCode)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
