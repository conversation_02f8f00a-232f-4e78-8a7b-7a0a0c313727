<template>
  <div id="bigScreen" class="bigScreen">
    <div class="main-wraper">
      <div class="header">
        智能下料产线生产数据看板
      </div>
      <div class="imgtime"><img src="@/assets/images/time.png"><span class="showTime">{{ time }}</span></div>
      <div class="week"><span class="showTime timeMargin">{{ week }}</span></div>
      <div class="main">
        <div class="task_info">
          <div v-for="(item, index) in taskCountData" :key="index" class="task_info_detail">
            <span class="task_info_name"><img :src="item.url" alt="">{{ item.name }}</span>
            <span class="task_info_val" :style="{ color: item.color }">{{ item.val }}</span>
          </div>
        </div>
        <!-- 中间 -->
        <div ref="wrapTextSelect" class="main-middle">
          <div class="wrapTextSelect">
            <div />
            <div class="elliptic"><img src="@/assets/images/dcs/taskInfoImg.png" alt="">任务列表状态(周)</div>
            <el-button type="primary" style="z-index: 99;" @click="moreData">更多</el-button>
          </div>
          <el-table
            ref="table1"
            v-loading="loading"
            border
            size="small"
            :data="tableData "
            :highlight-current-row="highlightCurrentRow"
            :height="height"
            @mouseenter.native="autoScroll(true,'table1')"
            @mouseleave.native="autoScroll(false,'table1')"
          >
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="task_num"
              :label="$t('lang_pack.cuttingZone.TaskNumber')"
            />
            <!-- 钢板型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="model_type"
              :label="$t('lang_pack.cuttingZone.SteelPlateModel')"
            />
            <!-- 切割文件名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="nc_name"
              label="切割文件名称"
              align="center"
            />
            <!-- 切割类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="cut_type"
              :label="$t('lang_pack.taskList.CutType')"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.CUT_TYPE[scope.row.cut_type] }}
              </template>
            </el-table-column>
            <!-- 目标切割机 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="cut_code"
              :label="$t('lang_pack.taskList.TargetCuttingMachine')"
            />
            <!-- 任务状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="task_status"
              :label="$t('lang_pack.taskList.TaskStatus')"
            >

              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <!-- 当前工位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="now_station_code"
              :label="$t('lang_pack.cuttingZone.CurrentWorkstation')"
            />
          </el-table>
          <img src="@/assets/images/dcs/borderTaskLeftBottom.png" alt="" class="leftBottom">
          <img src="@/assets/images/dcs/borderTaskLeftTop.png" alt="" class="leftTop">
          <img src="@/assets/images/dcs/borderTaskRightBottom.png" alt="" class="rightBottom">
          <img src="@/assets/images/dcs/borderTaskRightTop.png" alt="" class="rightTop">
        </div>
        <!-- 底部 -->
        <div class="main-bottom">
          <div class="car">
            <div class="wrapTextheader">
              <img src="@/assets/images/dcs/carRun.png" alt="">
              <span>天车</span>
            </div>
            <div id="outInStockChart" />
            <img src="@/assets/images/dcs/borderTaskLeftBottom.png" alt="" class="leftBottom">
            <img src="@/assets/images/dcs/borderTaskLeftTop.png" alt="" class="leftTop">
            <img src="@/assets/images/dcs/borderTaskRightBottom.png" alt="" class="rightBottom">
            <img src="@/assets/images/dcs/borderTaskRightTop.png" alt="" class="rightTop">
          </div>
          <div class="device">
            <div class="wrapTextheader">
              <img src="@/assets/images/dcs/locator.png" alt="">
              <span>库位管理</span>
            </div>
            <el-table
              ref="table2"
              v-loading="loading"
              border
              :data="deviceData"
              :row-key="row => row.id"
              :height="deviceHeight"
              @mouseenter.native="autoScroll(true,'table2')"
              @mouseleave.native="autoScroll(false,'table2')"
            >
              <!-- 库存明细 -->
              <el-table-column :show-overflow-tooltip="true" align="center" label="库存明细">

                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    type="text"
                    style="font-size: 18px;"
                    size="medium"
                    icon="el-icon-share"
                    @click="handleMudules(scope.row)"
                  >库存明细</el-button>
                </template>
              </el-table-column>
              <!-- 库位号 -->
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="stock_code"
                :label="$t('lang_pack.cuttingZone.StockCode')"
              />
              <!-- 物料型号 -->
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="model_type"
                :label="$t('lang_pack.cuttingZone.MaterialModel')"
              />
              <!-- 库存数量 -->
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="stock_count"
                :label="$t('lang_pack.cuttingZone.StockCount')"
              />
              <!-- 手动操作 -->
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                width="260"
                :label="$t('lang_pack.cuttingZone.ManualOperation')"
              >

                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    type="text"
                    size="medium"
                    style="font-size: 18px;"
                    icon="el-icon-share"
                    @click="handMove('手动入库', scope.row, '1')"
                  >手动入库</el-button>
                  <el-button
                    slot="reference"
                    type="text"
                    size="medium"
                    style="font-size: 18px;"
                    icon="el-icon-share"
                    @click="handMove('手动出库', scope.row, '2')"
                  >手动出库</el-button>
                </template>
              </el-table-column>
              <!-- 有效标识 -->
              <el-table-column :show-overflow-tooltip="true" align="center" label="有效标识">

                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.enable_flag"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    active-value="Y"
                    inactive-value="N"
                    @change="changeEnabled(scope.row, scope.row.enable_flag)"
                  />
                </template>
              </el-table-column>
            </el-table>
            <img src="@/assets/images/dcs/borderTaskLeftBottom.png" alt="" class="leftBottom">
            <img src="@/assets/images/dcs/borderTaskLeftTop.png" alt="" class="leftTop">
            <img src="@/assets/images/dcs/borderTaskRightBottom.png" alt="" class="rightBottom">
            <img src="@/assets/images/dcs/borderTaskRightTop.png" alt="" class="rightTop">
          </div>
          <div class="sort">
            <div class="wrapTextheader">
              <img src="@/assets/images/dcs/sorting1.png" alt="">
              <span>分拣</span>
            </div>
            <div class="sorting">
              <div v-for="(item, index) in sortNumber" :key="index">
                <div class="wrappstyle">
                  <div class="name">
                    <div />
                    <div><span class="finish">总数量</span><span class="smallPiece">{{
                      item.total }}</span></div>
                  </div>
                </div>
                <div class="wrappstyle">
                  <div class="name">
                    <div />
                    <div><span class="finish">等待数量</span><span class="smallPiece">{{
                      item.wait_count }}</span></div>
                  </div>
                </div>
                <div class="wrappstyle">
                  <div class="name">
                    <div><img :src="imgUrl[index]" alt=""><span class="smallPiece">{{
                      item.station_name }}</span>
                    </div>
                    <div><span class="finish">完成数量</span><span class="smallPiece">{{
                      item.complete_count }}</span>
                    </div>
                  </div>
                </div>
                <div class="wrappstyle" :style="{ 'margin-bottom': index == 1 ? '0px' : '' }">
                  <div class="name">
                    <div />
                    <div><span class="finish">完成率</span><span class="smallPiece">{{
                      item.complete_rate }}%</span>
                    </div>
                  </div>
                </div>
                <el-divider v-if="index == 0" />
              </div>
            </div>
            <img src="@/assets/images/dcs/borderTaskLeftBottom.png" alt="" class="leftBottom">
            <img src="@/assets/images/dcs/borderTaskLeftTop.png" alt="" class="leftTop">
            <img src="@/assets/images/dcs/borderTaskRightBottom.png" alt="" class="rightBottom">
            <img src="@/assets/images/dcs/borderTaskRightTop.png" alt="" class="rightTop">
          </div>

        </div>
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
      <el-form
        ref="ruleForm"
        class="el-form-wrap"
        :model="formRules"
        :rules="rules"
        size="small"
        label-width="100px"
        :inline="true"
      >
        <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationNumber')">
          <!-- 库位号 -->
          <el-input v-model="formRules.stock_code" disabled clearable size="small" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.wmsCbTable.inventory')">
          <!-- 库位量 -->
          <el-input v-model.number="formRules.stock_count" type="number" disabled clearable size="small" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.wmsCbTable.maximumInventory')" prop="max_count">
          <!-- 最大库存 -->
          <el-input v-model="formRules.max_count" disabled clearable size="small" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.taskList.SteelPlateModel')" prop="model_id">
          <!-- 钢板型号 -->
          <el-select
            v-model="formRules.model_id"
            :disabled="formRules.model_flag === 'Y' || formRules.stock_count > 0"
            clearable
            filterable
          >
            <el-option
              v-for="item in dict.MODAL_LIST"
              :key="item.model_id"
              :label="item.model_type"
              :value="item.model_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.sortingArea.Number')" prop="count">
          <!-- 数量 -->
          <el-input
            v-model.number="formRules.count"
            type="number"
            clearable
            size="small"
            @blur="BlurText($event)"
          />
        </el-form-item>
        <el-form-item v-if="dialogType === '1'" :label="$t('lang_pack.wmsCbTable.batchNumber')" prop="lot_num">
          <!-- 批次号 -->
          <el-input v-model="formRules.lot_num" clearable size="small" />
        </el-form-item>
        <el-divider />
        <div style="text-align: center;width: 100%;">
          <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{
            $t('lang_pack.commonPage.cancel')
          }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="dialogLoading"
            @click="handleOk"
          >{{
            $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-form>
    </el-dialog>
    <selectModal ref="selectModal" :dict="dict" />
    <inventory v-if="inventoryFlag" ref="inventory" :stock_id="stock_id" @ok="inventoryFlag = false" />
  </div>
</template>

<script>
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import autofit from 'autofit.js'
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
import selectModal from '@/components/selectModal'
import crudWmsFmodStock from '@/api/dcs/core/wmsCbTable/wmsFmodStock'
import inventory from '../../modules/inventoryDetailsModel'// 库存明细
import Cookies from 'js-cookie'
import { autoScroll } from '@/utils/autoScroll'
export default {
  name: 'wms_screen',
  components: { selectModal, inventory },
  data() {
    return {
      taskCountData: [
        { name: '任务数量', key: 'taskCount', val: 0, color: '#f12112', url: require('@/assets/images/dcs/taskNumber.png') },
        { name: '待切割', key: 'waitCutCount', val: 0, color: '#f1c412', url: require('@/assets/images/dcs/toBeCut.png') },
        { name: '切割中', key: 'cuttingCount', val: -1, color: '#f17612', url: require('@/assets/images/dcs/cutting.png') },
        { name: '待分拣', key: 'waitSortCount', val: 0, color: '#85f112', url: require('@/assets/images/dcs/toBeSorted.png') },
        { name: '分拣中', key: 'sortingCount', val: -1, color: '#12f17a', url: require('@/assets/images/dcs/Sorting.png') },
        { name: '分拣完成', key: 'finishCount', val: 0, color: '#1294f1', url: require('@/assets/images/dcs/sortingCompletd.png') }
      ],
      tableData: [],
      height: 370,
      deviceHeight: 340,
      loading: false,
      highlightCurrentRow: false,
      outInStockChart: null,
      sortNumber: [],
      imgUrl: [require('@/assets/images/dcs/smallPiece.png'), require('@/assets/images/dcs/bigPiece.png')],
      time: '',
      timer: '',
      week: '',
      deviceData: [],
      inventoryFlag: false,
      stock_id: '',
      dialogType: '',
      dialogTitle: '',
      dialogVisible: false,
      form: {},
      formRules: {
        stock_group_code: '',
        stock_count: '',
        max_count: '',
        min_count: 0,
        stock_code: '',
        model_id: '',
        count: '',
        lot_num: '',
        lot_number: '',
        model_flag: ''
      },
      dialogLoading: false,
      rules: {
        lot_num: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
        model_type: [{ required: true, message: '请选择钢板型号', trigger: 'blur' }],
        count: [{ required: true, message: '请选择数量', trigger: 'blur' }]
      },
      // modelList: [],
      timer2: null,
      tableRefs: ['table1', 'table2']
    }
  },
  dicts: ['CUT_TYPE', 'EXECUTE_STATUS', 'PROD_TASK_STATUS'],
  mounted() {
    this.tableRefs.forEach(tableRef => {
      this.autoScroll(false, tableRef)
    })
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出

    this.getTaskInfo() // 头部的任务信息
    this.getTaskStatus() // 获取任务列表状态
    this.getOutInStock() // 获取天车出/入库数量
    this.getSortNumber() // 获取天车显示分拣数量
    this.getStockSel() // 查询上料区库位List
    this.getTime() // 获取时间
    this.getModelType() // 获取型号
    this.timer = setInterval(() => {
      this.getTime()
    }, 1000)
    this.timer2 = setInterval(() => {
      this.getTaskInfo() // 头部的任务信息
      this.getTaskStatus() // 获取任务列表状态
      this.getOutInStock() // 获取天车出/入库数量
      this.getSortNumber() // 获取天车显示分拣数量
      this.getStockSel() // 查询上料区库位List
    }, 1000 * 60)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      clearInterval(this.timer2)
    }
    autofit.off()
    this.tableRefs.forEach(tableRef => {
      clearInterval(this[tableRef])
    })
  },

  methods: {
    // 设置自动滚动
    autoScroll,
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.time = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    },
    getTaskInfo() {
      crudMainPage.taskCount({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.length > 0) {
          const data = defaultQuery.data[0]
          this.taskCountData.forEach(item => {
            if (data.hasOwnProperty(item.key)) {
              item.val = data[item.key]
            }
          })
        } else {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    getTaskStatus() {
      crudMainPage.sel({ onlyToday: true }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.tableData = defaultQuery.data || []
          }
        } else {
          this.tableData = []
          this.$message({
            message: '任务状态查询异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.tableData = []
          this.$message({
            message: '任务状态查询异常',
            type: 'error'
          })
        })
    },
    getOutInStock() {
      crudMainPage.outInStock({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            const result = defaultQuery.data[0]
            this.outInStockChart = this.$echarts.init(document.getElementById('outInStockChart'))
            const data = []
            for (const key in result) {
              data.push({ name: key, value: result[key] })
            }
            const options = {
              color: ['#3DFFDC', '#5A3FFF'],
              tooltip: {
                trigger: 'item'
              },
              legend: {
                orient: 'horizontal',
                bottom: 20,
                right: 20,
                textStyle: {
                  color: '#fff'
                }
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                top: '0%'
              },
              series: [
                {
                  name: '天车',
                  type: 'pie',
                  radius: '75%',
                  itemStyle: {
                    borderWidth: 3
                  },
                  data,
                  // emphasis: {
                  //     itemStyle: {
                  //         shadowBlur: 10,
                  //         shadowOffsetX: 0,
                  //         shadowColor: 'rgba(0, 0, 0, 0.5)'
                  //     }
                  // }
                  itemStyle: {
                    normal: {
                      label: {
                        show: true,
                        formatter: '{b} : {c}',
                        position: 'inside',
                        color: '#fff',
                        fontSize: 16
                      },
                      labelLine: { show: false }
                    }
                  }
                }
              ]
            }
            window.addEventListener('resize', () => {
              this.outInStockChart.resize()
            })
            this.outInStockChart.setOption(options)
          }
        } else {
          this.$message({
            message: '获取天车出/入库异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.$message({
            message: '获取天车出/入库异常',
            type: 'error'
          })
        })
    },
    // 获取型号方法
    getModelType() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.dict['MODAL_LIST'] = defaultQuery.data
            // this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    },
    getSortNumber() {
      crudMainPage.sortNumber({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.sortNumber = defaultQuery.data
          }
        } else {
          this.$message({
            message: '获取分拣数量异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.$message({
            message: '获取分拣数量异常',
            type: 'error'
          })
        })
    },
    handleMudules(row) {
      this.stock_id = row.stock_id
      this.inventoryFlag = true
      this.$nextTick(() => {
        this.$refs.inventory.dialogVisible = true
      })
    },
    getStockSel() {
      crudLoadAreaOper.stockSel({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.deviceData = defaultQuery.data || []
          }
        } else {
          this.deviceData = []
          this.$message({
            message: '库位查询异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.deviceData = []
          this.$message({
            message: '库位查询异常',
            type: 'error'
          })
        })
    },
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudWmsFmodStock.editEnableFlag({
            user_name: Cookies.get('userName'),
            stock_id: data.stock_id,
            enable_flag: val
          })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    handMove(title, row, flag) {
      this.dialogTitle = title
      this.dialogType = flag
      this.formRules.stock_code = row.stock_code
      this.formRules.stock_id = row.stock_id
      this.formRules.stock_group_code = row.stock_group_code,
      this.formRules.stock_count = row.stock_count,
      this.formRules.max_count = row.max_count,
      this.formRules.min_count = row.min_count,
      this.formRules.model_id = row.model_id,
      this.formRules.model_flag = row.model_flag
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    handleOk() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.dialogType === '1') { // 入库
            if (this.formRules.count + this.formRules.stock_count > this.formRules.max_count) {
              this.$message.warning(`入库数量已超额且最多入库数量为${this.formRules.max_count - this.formRules.stock_count}`)
              this.dialogLoading = false
              return false
            }
            const data = {
              stock_code: this.formRules.stock_code,
              stock_id: this.formRules.stock_id,
              count: this.formRules.count,
              lot_num: this.formRules.lot_num,
              model_id: this.formRules.model_id
            }
            crudWmsFmodStock.inStock(data).then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.handleClose()
                this.getStockSel()
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'warning' })
              }
            }).catch(err => {
              this.dialogLoading = false
              this.$message({ message: '手动录入异常：' + err, type: 'error' })
            })
          }
          // 手动出库的传数量和stock_id
          if (this.dialogType === '2') { // 出库
            if (this.formRules.count > (this.formRules.stock_count - this.formRules.min_count)) {
              this.$message.warning(`当前出库数量最多为${this.formRules.stock_count - this.formRules.min_count}`)
              this.dialogLoading = false
              return false
            }
            const data = {
              stock_code: this.formRules.stock_code,
              stock_id: this.formRules.stock_id,
              count: this.formRules.count,
              lot_num: this.formRules.lot_num,
              model_id: this.formRules.model_id
            }
            crudWmsFmodStock.outStock(data).then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.handleClose()
                this.getStockSel()
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'warning' })
              }
            }).catch(err => {
              this.dialogLoading = false
              this.$message({ message: '手动出库异常：' + err, type: 'error' })
            })
          }
        } else {
          return false
        }
      })
    },
    handleClose() {
      this.$refs.ruleForm.resetFields()
      this.dialogLoading = false
      this.dialogVisible = false
    },
    moreData() {
      this.$refs.selectModal.open({
        type: 'rwzt',
        checkType: '',
        search: {
          onlyToday: false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
body,
html {
    overflow: hidden;
    position: relative;
    padding: 0;
    margin: 0;
    height: 100%;
    width: 100%;
    /* background:red; */
}

.bigScreen {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transform-origin: 0 0;
    background-image: url('~@/assets/images/dcs/bgScreen.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;

    .main-wraper {
        width: 100%;

        .header {
            margin: 0 auto;
            width: 99%;
            height: 90px;
            background: url('~@/assets/images/dcs/topHeader.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            color: #23CEFD;
            line-height: 90px;
            letter-spacing: 5px;
        }

        .imgtime {
            position: absolute;
            top: 3%;
            left: 2%;
            display: flex;
            align-items: center;

            img {
                width: 22px;
            }

            .showTime {
                color: #23CEFD;
                font-size: 22px;
                margin-left: 10px;
                font-weight: 550;
            }
        }

        .week {
            position: absolute;
            top: 3%;
            right: 7%;

            .showTime {
                color: #23CEFD;
                font-size: 22px;
                margin-left: 10px;
                font-weight: 550;
            }
        }

        .main {
            margin: 10px;

            .task_info {
                width: 100%;
                height: 80px;
                display: flex;
                justify-content: space-between;

                .task_info_detail {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border: 1px solid #0d5d5d;
                    width: 15%;
                    background: url(~@/assets/images/dcs/borderLeftBottomImg.png) no-repeat -1.5px 102.5%,
                        url(~@/assets/images/dcs/borderLeftTopImg.png) no-repeat -2px -2%,
                        url(~@/assets/images/dcs/borderRightBottomImg.png) no-repeat 100.5% 101%,
                        url(~@/assets/images/dcs/borderRightTopImg.png) no-repeat 100.5% -2px;

                    .task_info_name {
                        display: flex;
                        align-items: center;
                        color: #23CEFD;
                        font-size: 20px;
                        font-weight: 550;
                        margin-left: 30px;

                        img {
                            width: 10px;
                            margin-right: 5px;
                            margin-bottom: 10px;
                        }
                    }

                    .task_info_val {
                        margin-right: 60px;
                        font-size: 40px;
                    }
                }
            }

            .main-middle {
                width: 99.8%;
                margin: 20px 0;
                padding: 15px;
                border: 1px solid #20BCFC;
                position: relative;
            }

            .main-bottom {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .car {
                    padding: 10px;
                    width: 25%;
                    height: 400px;
                    position: relative;
                    border: 1px solid #0d5d5d;

                    #outInStockChart {
                        width: 100%;
                        height: 350px;
                    }
                }

                .sort {
                    padding: 10px;
                    width: 25%;
                    height: 400px;
                    position: relative;
                    border: 1px solid #0d5d5d;
                }

                .device {
                    padding: 10px;
                    width: 47%;
                    height: 400px;
                    position: relative;
                    border: 1px solid #0d5d5d;
                }
            }
        }

        .rightTop {
            position: absolute;
            top: -1px;
            right: -3px;
        }

        .rightBottom {
            position: absolute;
            bottom: -3px;
            right: -8px;
        }

        .leftTop {
            position: absolute;
            top: -1px;
            left: -3px;
        }

        .leftBottom {
            position: absolute;
            bottom: -3px;
            left: -5px;
        }

        .wrapTextSelect {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .elliptic {
                display: flex;
                align-items: center;
                font-size: 20px;
                color: #23CEFD;
                font-weight: 550;

                img {
                    margin: 0 10px;
                    width: 26px;
                }
            }
        }

        .wrapTextheader {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;

            img {
                width: 26px;
                margin: 0 10px;
            }

            span {
                font-size: 20px;
                color: #23CEFD;
                font-weight: 550;
            }
        }

        .wrappstyle {
            width: 90%;
            margin: 10px auto;

            .name {
                display: flex;
                justify-content: space-between;
                width: 100%;

                img {
                    width: 24px;
                    height: 24px;
                }

                div {
                    width: 50%;
                    display: flex;
                    align-items: center;

                    .smallPiece {
                        margin: 0 0 0 5px;
                        font-size: 24px;
                        color: #23CEFD;
                    }

                    .finish {
                        display: block;
                        width: 70%;
                        text-align: center;
                        font-size: 24px;
                        color: #23CEFD;
                    }
                }
            }

        }

        ::v-deep .el-table .cell {
            font-size: 22px;
        }

        ::v-deep .el-table th {
            background-color: #1F3858 !important;
            color: #23CEFD;
            border-right: none;
        }

        ::v-deep .el-table__body-wrapper {
            background-color: #183163;

            .el-table__empty-text {
                color: #fff;
            }
        }

        ::v-deep .el-table {
            color: #72BCB7;
        }

        ::v-deep .el-table tr {
            background: #0F2043 !important;
        }

        ::v-deep .el-table tr:nth-child(even) {
            background: #13274B !important;
        }

        ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
            width: 3px;
            height: 3px;
            background-color: #0F2043;
            cursor: pointer !important;
        }

        ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(15, 32, 67, 0.3);
            -webkit-box-shadow: inset 0 0 6px rgba(15, 32, 67, 0.3);
            background-color: #66ffff;
            cursor: pointer !important;
        }

        ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            background: rgba(15, 32, 67, 1);
            cursor: pointer !important;
        }
         ::v-deep .el-table__body tr:hover > td {
            background-color: #00ffff !important;
        }
    }
}

::v-deep .v-model {
    display: none;
}
</style>
