<template>
    <el-dialog :title="obj.title" :visible.sync="dialogVisible" :width="width" :before-close="handleClose">
        <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-8 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="描述">
                                <!-- 主流程编码： -->
                                <el-input v-model="tableTagQuery.client_des" placeholder="实例描述" class="filter-item" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-4 col-12">
                        <el-form-item>
                            <!-- <rrOperation /> -->
                            <el-button class="filter-item" size="small" type="primary" icon="el-icon-search"
                                @click="toButtableTagQuery(1)">{{ $t('lang_pack.commonPage.search') }}</el-button>
                            <!-- 搜索 -->
                            <el-button class="filter-item" size="small" icon="el-icon-refresh-left"
                                @click="toButResetQuery()">{{
                                    $t('lang_pack.commonPage.reset') }}</el-button> <!-- 重置 -->
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>
        <el-card shadow="never" class="wrapCard">
            <el-table border @header-dragend="crud.tableHeaderDragend()" v-loading="tableTagLoading" ref="multipleTable" :row-key="getRowKeys" :data="tagData" height="400px"
                max-height="400px" tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column  type="selection" width="55" :reserve-selection="true">
                </el-table-column>
                <el-table-column  prop="client_des" align="center" label="描述">
                </el-table-column>
            </el-table>
            <el-pagination :page-size.sync="tagTablePage.size" :total="tagTablePage.total"
                :current-page.sync="tagTablePage.page" :page-sizes="[10, 30, 50]"
                style="margin: 10px 0 10px 0; float: right" layout="total, prev, pager, next, sizes"
                @size-change="sizeChangeHandler($event)" @current-change="currentChangeHandler" />
        </el-card>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleOk">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script>
import { selScadaClient } from '@/api/core/scada/client'
import Cookies from 'js-cookie'
export default {
    name: 'popupDialog',
    props: {
        obj: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            dialogVisible: false,
            width: '50%',
            query: {
                user_name: Cookies.get('userName'),
                enable_flag: 'Y'
            },
            tagTablePage: {
                // 页码
                page: 1,
                // 每页数据条数
                size: 50,
                // 总数据条数
                total: 0
            },
            tableTagQuery: {
                client_des: '',
                tag_group_des: '',
                tag_id: '',
                tag_des: '',
                tableOrder: '', // 排序方式asc,desc
                tableOrderField: '' // 排序字段
            },
            tagData: [],
            selectList: [],
            valList:[],
            tableTagLoading: false,
            tagConfigVisible: false
        }
    },
    created() {
    },
    methods: {
        getRowKeys(row) {
            return row.client_id
        },
        async open(val) {
            this.dialogVisible = true
            this.valList = val == '' ? [] : val
            if (this.valList.constructor !== Array) {
                this.valList = this.valList.split(',')
            }
            this.toButtableTagQuery(1)
        },
        handleClose() {
            this.dialogVisible = false
        },
        toButResetQuery() {
            this.tableTagQuery.client_des = ''
            this.tagTablePage.page = 1
            this.toButtableTagQuery(1)
        },
        handleSelectionChange(val) {
            this.selectList = val || []
        },
        toButtableTagQuery(page) {
            var query = {
                clientIds: [],
                client_des: this.tableTagQuery.client_des,
                sort: 'client_id',
                page: page, // 当前页
                size: this.tagTablePage.size // 每页数据条数
            }
            this.tableTagLoading = true
            selScadaClient(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.count > 0) {
                        this.tagData = defaultQuery.data
                        this.$refs.multipleTable.clearSelection() //默认先清空选中项
                        this.tagData.forEach(e=>{
                                this.valList.forEach((item)=>{
                                    if(e.client_id == item){
                                        this.$refs.multipleTable.toggleRowSelection(e, true) //默认选中
                                    }
                                })
                            })
                    } else {
                        this.tagData = []
                    }
                    this.tagTablePage.total = defaultQuery.count
                    this.tableTagLoading = false
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                    this.tagData = []
                    this.tableTagLoading = false
                })
        },
        // Page:分页
        sizeChangeHandler(size) {
            // 查询
            this.tagTablePage.size = size
            // 当出现  当前页  *  条数 > 总条数   的情况时不调用接口
            if (this.tagTablePage.page * size > this.tagTablePage.total) return
            this.toButtableTagQuery(this.tagTablePage.page)
        },
        currentChangeHandler(page) {
            // 查询
            this.toButtableTagQuery(page)
        },
        handleOk() {
            this.dialogVisible = false
            this.$emit('updateAdd', this.selectList)
        }
    }
}
</script>