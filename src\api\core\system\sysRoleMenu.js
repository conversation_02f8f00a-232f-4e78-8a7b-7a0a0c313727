import request from '@/utils/request'

//查询角色菜单(树 全部-有/无权限)
export function queryRoleMenuTree(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleMenuTreeSel',
    method: 'post',
    data
  })
}

//查询角色菜单
export function queryRoleMenu(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleMenuSel',
    method: 'post',
    data
  })
}
//新增角色菜单
export function insRoleMenu(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleMenuIns',
    method: 'post',
    data
  })
}

export default { queryRoleMenuTree, queryRoleMenu, insRoleMenu }