// 核心模块的中文翻译

export default {
  // 这里可以添加核心模块特定的翻译
  core: {
    // SECS/AIS配方管理
    secsais: {
      // 页面标题和通用文本
      maintenance: 'SECS/AIS配方管理',
      detail: '配方详情',
      noRecipeSelected: '未选择配方或配方详情加载中...',
      clientList: '实例列表',
      modelList: '配方列表',

      // 表单字段
      clientDes: '客户端描述',
      tagGroupCode: '标签组编码',
      modelName: '模型名称',
      tagGroupId: '标签组ID',
      tagCodePrefix: '标签编码前缀',
      clientId: '客户端ID',
      clientCode: '客户端编码',
      parameterCode: '参数编码',
      parameterDes: '参数描述',
      parameterVal: '参数值',
      enableFlag: '有效标识',
      valid: '有效',
      invalid: '无效',

      // 操作相关
      modifyParameter: '是否修改参数',
      addParameter: '新增参数',
      confirmDelete: '确认删除选中的{0}条数据?',
      confirmDistribute: '确认下发该配方吗?',
      confirmChangeEnableFlag: '确定要将有效标识修改为【{value}】吗？',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败',
      modifySuccess: '修改成功',
      modifyFailed: '修改失败',
      emptyValueSet: '空值已设置',
      distributeSuccess: '下发成功',
      distributeFailed: '下发失败',
      mqttConnectionFailed: 'MQTT连接失败',
      mqttNotConnected: '请先连接MQTT服务',
      mqttConnectSuccess: 'MQTT连接成功',
      mqttUpdateTimeout: 'MQTT更新超时，请检查连接',
      serviceCell: '无法获取单元服务器信息',
      getCellIPFailed: '获取单元IP失败',

      // 参数相关
      parameterCode: '参数编码',
      parameterDes: '参数描述',
      parameterVal: '当前值',
      parameterUnit: '单位',
      parameterLimit: '上下限',
      parameterInput: '输入值',
      modifyParameter: '是否修改参数',
      noRecipeSelected: '请选择配方',
      fetchRecipeDataFailed: '获取配方数据失败',
      valueTooLow: '输入值低于下限',
      valueTooHigh: '输入值高于上限',

      // 搜索和提示
      search: '搜索',
      reset: '重置',
      confirm: '确认',
      cancel: '取消',
      prompt: '提示',
      required: '必填项',
      fetchModelDataFailed: '获取配方模型数据失败',
      fetchRecipeDataFailed: '获取配方详情失败',
      maintenance: 'SECS/AIS配方维护',
      confirmDelete: '确认删除{0}条数据?',

      // 状态和操作提示
      refreshInstanceList: '刷新实例列表',
      refreshingInstanceList: '正在刷新实例列表...',
      online: '在线',
      offline: '离线',
      stationCodeMissing: '工站编码缺失，无法查询配方模型数据',
      stationCodeMissingMqtt: '工站编码缺失，无法连接MQTT',
      cancelDelete: '取消删除',
      unknownMqttFormat: '未知格式的MQTT消息',

      // 配方参数相关
      recipeList: '配方列表',
      recipeDetails: '配方详情',
      modelList: '配方列表',
      modifyParameter: '修改参数',
      parameterCode: '参数代码',
      parameterDes: '参数描述',
      parameterVal: '参数值',
      parameterInput: '输入值',
      parameterUnit: '单位',
      parameterLimit: '参数范围',
      noRecipeSelected: '请选择一个配方',
      valueTooLow: '输入值低于下限',
      valueTooHigh: '输入值高于上限',
      modifySuccess: '修改成功',
      modifyFailed: '修改失败',

      // MQTT相关
      mqttNotConnected: 'MQTT未连接',
      mqttConnectSuccess: 'MQTT连接成功',
      mqttConnectionFailed: 'MQTT连接失败',
      serviceCell: '获取服务单元信息失败',
      getCellIPFailed: '获取单元IP失败'
    }
  }
}
