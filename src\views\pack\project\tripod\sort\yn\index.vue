<template>
  <div class="app-container">
    <el-card ref="queryCard" style="width:60%">
      <table border class="wraptable">
        <tr v-if="StationConfigData.length>0" class="tableTbody">
          <td colspan="2" style="width: 5%;text-align: center;">
            {{ $t('lang_pack.vie.index') }}
          </td>
          <th colspan="8" style="text-align: center;">
            {{ $t('lang_pack.vie.Features') }}
          </th>
        </tr>
        <tbody v-for="(item, index) in StationConfigData" :key="index" class="tableTbody">
          <tr class="trtitle">
            <td colspan="2" style="width: 5%;text-align: center;">
              {{ index + 1 }}
            </td>
            <td colspan="2" style="text-align: center;">
              <el-switch
                v-model="item.sort_flag"
                active-value="Y"
                inactive-value="N"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </td>
            <th class="tdName" colspan="2"><span>{{ item.sort_name }}</span></th>
            <!-- <td  colspan="2"><span>{{ item.sort_index }}</span></td> -->
            <td v-if="item.sort_fastgroup" colspan="2">
              <el-select v-model="item.sort_value" clearable filterable :multiple="isMultipleMap[item.sort_fastgroup]">
                <el-option v-for="item1 in dict[item.sort_fastgroup]" :key="item1.id" :label="item1.label" :value="item1.value">
                  <span style="float: left">{{ item1.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item1.value }}</span>
                </el-option>
              </el-select>
            </td>
            <td v-else-if="item.data_type === 'Bool'" colspan="2">
              <el-switch
                v-model="item.sort_value_flag"
                active-value="Y"
                inactive-value="N"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </td>
            <td v-else-if="item.sort_code === 'XoutNPSort'" colspan="2">
              <el-select v-model="item.sort_value" clearable filterable>
                <el-option v-for="item1 in xnpSortList" :key="item1.sortId" :label="item1.sortName" :value="item1.sortValue">
                  <!-- <span style="float: left">{{ item1.sortName }}</span> -->
                  {{ item1.sortName }}
                </el-option>
              </el-select>
            </td>
            <td v-else-if="item.data_type === 'JSON' && item.sort_code !== 'XoutNPSort'" colspan="2">
              <el-button type="primary" @click="handleSetting(index, item)">{{ $t('view.button.setting') }}</el-button>
            </td>
            <td v-else-if="item.data_type !== 'Bool' && item.data_type !== 'JSON' && item.sort_code !== 'XoutNPSort'" colspan="2">
              <el-input v-model="item.sort_value" :disabled="item.sort_value_flag == 'N'" clearable size="medium" style="width: 100%" />
            </td>
          </tr>
        </tbody>
      </table>
      <div style="margin-top: 10px;text-align: center;">
        <el-button type="primary" @click="() => getStationData(handleSave)">{{ $t('lang_pack.vie.save') }}</el-button>
      </div>
    </el-card>
    <roleCheck v-if="roleCheckShow" ref="roleCheck" :role_func_code="role_func_code" :params-code="'Sort_CheckPwd'" @roleCheck="roleCheck" />
  </div>
</template>
<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import roleCheck from '@/views/core/hmi/roleCheck'
import { sortSel, SortUpd } from '@/api/pack/pack'
import { selCellIP } from '@/api/core/center/cell'
export default {
  name: 'SORT_SWITCH',
  components: { roleCheck },
  data() {
    return {
      StationConfigData: [],
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: 1
      },
      content_style: {
        'font-size': '18px'
      },
      label_style: {
        'font-size': '18px'
      },
      fastCode: [],
      isMultipleMap: {
        undefined: false,
        null: false,
        '': false,
        'PANEL_TYPE': true
      },
      dictData: {},
      xnpSortList: [],
      controlValue: '',
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      roleCheckShow: false,
      role_func_code: '',
      attrCall: ''
    }
  },
  dicts: ['QR_LEVEL', 'XOUT_TYPE', 'PANEL_TYPE'],
  mounted() {
    const query = {
      sort: 'sort_index asc'
    }
    sortSel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data.length > 0) {
          defaultQuery.data.map(e => {
            if (this.isMultipleMap[e.sort_fastgroup] && typeof e.sort_value === 'string') {
              e.sort_value = e.sort_value !== '' ? e.sort_value.split(',') : []
            }
          })
          this.StationConfigData = defaultQuery.data
        } else {
          this.StationConfigData = []
        }
      }
    }).catch(err => {
      this.$message.error(err.msg)
    })
  },
  methods: {
    handleSave() {
      if (this.controlValue !== '1') {
        this.$message({ message: 'PLC不允许PC下发信息', type: 'error' })
        return
      }
      const newData = JSON.parse(JSON.stringify(this.StationConfigData))
      newData.map(e => {
        if (this.isMultipleMap[e.sort_fastgroup] && Array.isArray(e.sort_value)) {
          if (e.sort_value.length === 0) {
            e.sort_value = ''
          } else {
            e.sort_value = e.sort_value.join(',')
          }
        }
      })
      const query = {
        params: newData
      }
      SortUpd(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success(this.$t('lang_pack.commonPage.operationSuccessful'))
        } else {
          this.$message({ message: defaultQuery.msg, type: 'error' })
        }
      }).catch(ex => {
        this.$message({ message: ex.msg, type: 'error' })
      })
    },
    handleSetting(index, data) {
      if (index == null) {
        return
      }
      this.dialog.form.index = index
      this.dialog.title = this.$t('view.title.setting') + data.sort_name
      this.dialog.visible = true
      if (data.data_type === 'JSON') {
        this.$set(this.dialog.form, 'data', JSON.parse(data.sort_value))
      }
    },
    getStationData(call) {
      this.roleCheckShow = true
      this.attrCall = call
    },
    roleCheck(roleFuncCode, status) {
      if (status === 'OK') {
        this.currentStation = {
          prod_line_id: this.$route.query.prod_line_id,
          prod_line_code: '',
          prod_line_des: '',
          station_id: this.$route.query.station_id,
          station_code: this.$route.query.station_code,
          station_des: '',
          cell_id: this.$route.query.cell_id || 1
        }
        this.getCellIp(this.attrCall)
      }
      this.roleCheckShow = false
    },
    getCellIp(call) {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getTagValue(call)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    getTagValue(call) {
      const readTagArray = [
        { tag_key: 'SortPlc/PlcBase/TaskAssignmentSign' }
      ]
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  if (tagKey === 'SortPlc/PlcBase/TaskAssignmentSign') {
                    this.controlValue = tagValue
                  }
                }
              }
            }
          }
          call && call()
        })
        .catch(ex => {
          this.$message({ message: ex.msg, type: 'error' })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.app-container{
    display:flex;
    justify-content:center;
}
::v-deep.el-switch.is-checked .el-switch__core{
    border-color: #13ce66 !important;
    background-color: #13ce66 !important;
}
.wraptable{
    width:100%;
    border-collapse: collapse;
    // display: flex;
    .tableTbody{
        width: 50% !important;
        td,th {
            width:20%;
            border: 1px solid #e6ebf5;
            padding:10px;
        }
        .tdName{
            width:60%;
            background-color:#fafafa;
            color:#909399;
        }
    }
}
</style>
