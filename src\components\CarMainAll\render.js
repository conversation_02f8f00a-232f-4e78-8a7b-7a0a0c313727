
function render(g, node, isSelected, that) {
  if (node.type === 'rect') {
    g.append('rect')
      .style('width', node.width + 65)
      .style('height', node.width + 15)
      .style('fill', node.bgColor)
      .attr('stroke', node.bgColor)
      .attr('x', node.x + 100)
      .attr('y', node.y)
      .attr('rx', 7)
      .attr('ry', 7)
      .style('filter', 'drop-shadow(4px 4px 4px rgba(0,0,0,0.4))') // 设置阴影
    g.append('text')
      .attr('x', node.x + 40)
      .attr('y', node.y + 20)
      .style('fill', '#000')
      .attr('class', 'unselectable')
      .attr('text-anchor', 'middle')
      .text(() => node.title)
      .style('font-size', '14px')
      .style('font-weight', 'bold')
  } else if (node.type === 'squ') {
    g.append('rect')
      .style('width', node.width)
      .style('height', node.height)
      .style('fill', node.bgColor)
      .attr('stroke', node.bgColor)
      .attr('x', node.x)
      .attr('y', node.y)
      .style('filter', 'drop-shadow(4px 4px 4px rgba(0,0,0,0.3))') // 设置阴影
  } else if (node.type === 'icon') {
    g.append('image')
      .attr('x', node.key === 'tray' ? node.x + 100 : node.x + 120)
      .attr('y', node.key === 'tray' ? node.y - 15 : node.y)
      .attr('preserveAspectRatio', 'none')
      .attr('vector-effect', 'non-scaling-stroke')
      .style('width', node.width)
      .style('height', node.height)
      .attr('href', node.href)
      .style('filter', node.key === 'shadow' ? 'drop-shadow(4px 4px 4px rgba(0,0,0,0.4))' : '') // 设置阴影
    g.append('text')
      .attr('x', node.x + 40)
      .attr('y', node.y + 20)
      .style('fill', '#000')
      .attr('class', 'unselectable')
      .attr('text-anchor', 'middle')
      .text(() => node.title)
      .style('font-size', '14px')
      .style('font-weight', 'bold')
  } else if (node.type === 'code') {
    // g.append('rect')
    //   .style('width', node.width)
    //   .style('height', node.height)
    //   .style('fill', '#fff')
    //   .attr('x', node.x)
    //   .attr('y', node.y)
    //   .style('filter', 'drop-shadow(4px 4px 4px rgba(0,0,0,0.4))') // 设置阴影
    g.append('rect')
      .style('width', '50px')
      .style('height', '20px')
      .style('fill', node.status === 1 ? '#00479D' : '#6E9ACF')
      .attr('stroke', node.status === 1 ? '#00479D' : '#6E9ACF')
      .attr('x', node.x + 55)
      .attr('y', node.y + 50)
      .attr('rx', 7)
      .attr('ry', 7)
    g.append('image')
      .attr('x', node.x)
      .attr('y', node.y)
      .attr('preserveAspectRatio', 'none')
      .attr('vector-effect', 'non-scaling-stroke')
      .style('width', '160px')
      .style('height', '80px')
      .attr('href', node.href)
    if (node.title !== 'H1') {
      g.append('image') // G5 G6 G7 G8 的顺序是（到位、减速）（占位）
        .attr('x', (node.direction && node.direction === 'left') ? node.x + 140 : node.x + 30)
        .attr('y', node.y + 10)
        .style('width', '7px')
        .style('height', '7px')
        .attr('href', (node.SeizeSeat && node.SeizeSeat === '1') ? require('@/assets/images/dcs/green.png') : require('@/assets/images/dcs/Init.png'))
      g.append('image') // G5 G6 G7 G8 的顺序是（到位、减速）（占位）
        .attr('x', (node.direction && node.direction === 'left') ? node.x + 30 : node.x + 125)
        .attr('y', node.y + 10)
        .style('width', '7px')
        .style('height', '7px')
        .attr('href', (node.Deceleration && node.Deceleration === '1') ? require('@/assets/images/dcs/green.png') : require('@/assets/images/dcs/Init.png'))
      g.append('image') // G5 G6 G7 G8 的顺序是（到位、减速）（占位）
        .attr('x', (node.direction && node.direction === 'left') ? node.x + 15 : node.x + 140)
        .attr('y', node.y + 10)
        .style('width', '7px')
        .style('height', '7px')
        .attr('href', (node.InPlace && node.InPlace === '1') ? require('@/assets/images/dcs/green.png') : require('@/assets/images/dcs/Init.png'))
    }
    g.append('text')
      .attr('x', node.x + 80)
      .attr('y', node.y + 67)
      .style('fill', '#ffffff')
      .attr('class', 'unselectable')
      .attr('text-anchor', 'middle')
      .text(() => node.title)
      .style('font-size', '16px')
      .style('font-weight', 'bold')
    // g.append('title')
    // .text(()=>`任务号:${node.task_num ? node.task_num :'暂无数据'}\n托盘号:${node.pallet_num ? node.pallet_num : '暂无数据'}\n钢板型号:${node.model_type ? node.model_type : '暂无数据'}`)
    if (node.manual_Auto_Flag) {
      g.append('image')
        .attr('x', node.x + 70)
        .attr('y', node.y - 20)
        .attr('preserveAspectRatio', 'none')
        .attr('vector-effect', 'non-scaling-stroke')
        .style('width', '25px')
        .style('height', '25px')
        .attr('href', node.manual_Auto_Flag === '1' ? require('@/assets/images/dcs/dcs_automatic.png') : require('@/assets/images/dcs/dcs_anual.png'))
    }
    if (node.pallet_Flag === '1') {
      g.append('image')
        .attr('x', node.x + 10)
        .attr('y', node.title === 'H1' ? node.y - 15 : node.y)
        .attr('preserveAspectRatio', 'none')
        .attr('vector-effect', 'non-scaling-stroke')
        .style('width', '140px')
        .style('height', '90px')
        .attr('href', node.pallet_Flag === '1' ? require('@/assets/images/dcs/dcs_tray.png') : '')
      g.append('title')
        .text(() => `托盘号:${node.pallet_Num ? node.pallet_Num : '暂无数据'}`)
    }
    g.on('click', () => {
      that.open(node)
    })
  }
  // if (node.type === 'image') {
  //   g.append('image')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //     .attr('preserveAspectRatio', 'none')
  //     .attr('vector-effect', 'non-scaling-stroke')
  //     .style('width', node.width + 'px')
  //     .style('height', node.height + 'px')
  //     .attr('href', 'data:image/png;base64,' + node.href)
  // } else {
  //   const borderColor = isSelected ? '#445ddb' : node.stroke
  //   const borderWidth = isSelected ? '3px' : node.strokeWidth
  //   g.append('circle')
  //     .attr('cx', node.x)
  //     .attr('cy', node.y)
  //     .attr('r', node.size)
  //     .attr('stroke', borderColor)
  //     .style('stroke-width', borderWidth)
  //     .attr('fill', node.status === 'LOCKSTATUS' ? '#21f121' :node.ok_color)
  //   g.append('text')
  //     .attr('x', node.x)
  //     .attr('y', node.y + 6 || 0)
  //     .style('fill', '#2D333D')
  //     .attr('class', 'unselectable')
  //     .attr('text-anchor', 'middle')
  //     .text(() => node.point_id)
  //     .style('font-size', '14px')
  //     .style('font-weight', 'bold')
  //   g.append('title')
  //     .text(()=>`任务号:${node.task_num ? node.task_num :'暂无数据'}\n托盘号:${node.pallet_num ? node.pallet_num : '暂无数据'}\n钢板型号:${node.model_type ? node.model_type : '暂无数据'}`)
  //   g.on('click',()=>{
  //     that.jumpCut(node)
  //   })
  // }
}

export default render
