<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('view.table.name') + ':'">
                <!-- 名称： -->
                <el-input v-model="query.name" clearable size="mini" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.andonbtn.btnLevel')+ ':'">
                <!-- 优先级： -->
                <el-input v-model="query.priority" clearable size="mini" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.andonbtn.action')+ ':'">
                <!-- 动作： -->
                <el-input v-model="query.action" clearable size="mini" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('view.table.name')" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.andonbtn.btnLevel')" prop="priority">
            <el-input v-model.number="form.priority" type="number" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.andonbtn.action')" prop="action">
            <el-input v-model="form.action" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.eventRecordForm.condition')" prop="conditions">
            <el-input v-model="form.conditions" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.eventRecordForm.timeoutLimit')">
            <el-input v-model.number="form.timeoutLimit" type="number" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              :height="height"
              :highlight-current-row="true"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              @header-dragend="crud.tableHeaderDragend()"
              @selection-change="crud.selectionChangeHandler"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="menu_group_id" label="id" />
              <el-table-column :show-overflow-tooltip="true" prop="name" :label="$t('view.table.name')" />
              <!-- 名称 -->
              <el-table-column :show-overflow-tooltip="true" prop="priority" :label="$t('lang_pack.andonbtn.btnLevel')" />
              <!-- 优先级 -->
              <el-table-column :show-overflow-tooltip="true" prop="action" :label="$t('lang_pack.andonbtn.action')" />
              <!-- 动作 -->
              <el-table-column :show-overflow-tooltip="true" prop="conditions" :label="$t('lang_pack.eventRecordForm.condition')" />
              <!-- 条件 -->
              <el-table-column :show-overflow-tooltip="true" prop="timeoutLimit" :label="$t('lang_pack.eventRecordForm.timeoutLimit')" />
              <!-- 超时限制（ms） -->
              <el-table-column :show-overflow-tooltip="true" prop="createdDate" :label="$t('lang_pack.flowonline.creationDate')" />
              <!-- 创建时间 -->
              <el-table-column :show-overflow-tooltip="true" prop="lastModifiedDate" :label="$t('lang_pack.eventRecordForm.lastUpdateTime')" />
              <!-- 最后更新时间 -->
              <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
                <!-- 操作 -->
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>
<script>
import crudEventTemplate from '@/api/core/eventTemplate/eventTemplate'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  name: '',
  priority: '',
  action: '',
  conditions: '',
  timeoutLimit: ''
}
export default {
  name: 'EVENT-TEMPLATE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '事件模板',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'originId',
      // 排序
      sort: ['id asc'],
      // CRUD Method
      crudMethod: { ...crudEventTemplate },
      query: {
        _matchMode: 'like'
      },
      vResource: 'event-templates',
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'sys_menu:add'],
        edit: ['admin', 'sys_menu:edit'],
        del: ['admin', 'sys_menu:del'],
        down: ['admin', 'sys_menu:down']
      },
      rules: {
        menu_group_code: [{ required: true, message: '请输入菜单组编码', trigger: 'blur' }],
        group_order_by: [{ required: true, message: '请输入顺序', trigger: 'blur' }]
      }
    }
  }
}
</script>
