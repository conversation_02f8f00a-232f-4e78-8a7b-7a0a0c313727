// TLPS项目的英文翻译

export default {
  // 这里可以添加TLPS项目特定的翻译
  tlps: {
    // 配方管理
    recipe: {
      // 页面标题和通用文本
      maintenance: 'Recipe Maintenance',
      detail: 'Recipe Detail Maintenance',
      recipeDetail: 'Recipe Details',
      noRecipeSelected: 'No recipe selected or recipe details loading...',

      // 表单字段
      name: 'Recipe Description',
      version: 'Version',
      type: 'Recipe Type',
      deviceCode: 'Device Code',
      deviceDes: 'Device Description',
      materialCode: 'Material Code',
      materialDes: 'Material Description',
      parameterCode: 'Parameter Code',
      parameterDes: 'Parameter Description',
      parameterVal: 'Parameter Value',
      enableFlag: 'Enable Flag',
      valid: 'Valid',
      invalid: 'Invalid',

      // 操作相关
      scanDownload: 'Scan to Download Recipe',
      modifyParameter: 'Modify Parameters',
      addParameter: 'Add Parameter',
      confirmDelete: 'Confirm to delete {0} selected items?',
      confirmDistribute: 'Confirm to distribute this recipe?',
      confirmChangeEnableFlag: 'Are you sure to change the enable flag to [{value}]?',
      deleteSuccess: 'Delete Successful',
      deleteFailed: 'Delete Failed',
      modifySuccess: 'Modify Successful',
      modifyFailed: 'Modify Failed',
      distributeSuccess: 'Distribution Successful',

      // 按钮和交互元素
      triggerPoint: 'Trigger Point',
      selectTriggerPoint: 'Select',
      cancel: 'Cancel',
      confirm: 'Confirm',
      download: 'Download',
      upload: 'Upload',
      distribute: 'Distribute',
      delete: 'Delete',
      downloadSuccess: 'Recipe downloading',
      downloadFailed: 'Recipe download exception',

      // 提示信息
      startMonitoring: 'Please start monitoring',
      connectionFailed: 'Connection failed',
      reconnecting: 'Connection lost, reconnecting...',
      connectionDisconnected: 'Service connection disconnected',
      connectionClosed: 'Service connection closed',
      subscribeSuccess: 'MQTT subscription successful: {0}',
      subscribeFailed: 'MQTT subscription failed: {0}',
      unsubscribeSuccess: 'MQTT unsubscription successful: {0}',
      unsubscribeFailed: 'MQTT unsubscription failed: {0}',
      operationException: 'Operation exception',
      operationFailed: 'Operation failed',
      queryException: 'Query exception',
      uploadMesSuccess: 'Upload to MES successful',
      uploadSuccess: 'Upload successful',
      uploadTaskExists: 'Upload task already exists, please wait for completion',
      recipeDownloading: 'Initiating Scada recipe request, recipe downloading',
      recipeDownloadException: 'Scada recipe request exception occurred',

      // 验证规则提示
      selectRecipeType: 'Please select recipe type',
      fillVersion: 'Please fill in version',
      fillRecipeName: 'Please fill in recipe description',
      fillMaterialCode: 'Please fill in material code',
      fillMaterialDes: 'Please fill in material description',
      fillDeviceCode: 'Please fill in device code',
      fillDeviceDes: 'Please fill in device description',
      selectEnableFlag: 'Please select enable flag',
      selectTriggerTag: 'Please select collection project tag'
    },

    // 主页面
    index: {
      title: 'TLPS System',
      recipeManagement: 'Recipe Management',
      deviceStatus: 'Device Status',
      alarmInfo: 'Alarm Information',

      // 操作模式
      offlineMode: 'Offline Mode',
      onlineLocal: 'Online/Local',
      onlineRemote: 'Online/Remote',

      // 设备状态
      deviceStatusLabel: 'Device Status',
      deviceStatus0: 'Unknown',
      deviceStatus1: 'Initializing',
      deviceStatus2: 'Standby',
      deviceStatus3: 'Alarm',
      deviceStatus4: 'Stopped',
      deviceStatus5: 'Running',
      deviceStatus6: 'PM',

      // 状态指示
      alarmLight: 'Alarm Light',
      plcHeartbeat: 'PLC Heartbeat',

      // 表单字段
      batchNumber: 'Batch Number',
      materialCode: 'Material Code',
      recipe: 'Recipe',
      selectRecipe: 'Please Select Recipe',
      processingQuantity: 'Processing Quantity',
      distributePreview: 'Distribute Preview',

      // 生产信息
      productionInfo: 'Production Information',

      // 报警信息
      alarmInfo: 'Alarm Information',
      station: 'Station',
      instanceCode: 'Instance Code',
      instanceDescription: 'Instance Description',
      alarmCode: 'Alarm Code',
      alarmLevel: 'Alarm Level',
      alarmDescription: 'Alarm Description',
      alarmTime: 'Alarm Time',
      resetTime: 'Reset Time',
      isReset: 'Is Reset',
      resetYes: 'Reset',
      resetNo: 'Not Reset',
      isSimulated: 'Is Simulated',
      yes: 'Yes',
      no: 'No',

      // 图表
      capacity: 'Capacity',
      oee: 'OEE',
      readRate: 'Read Rate',

      // 表格列
      projectName: 'Project Name',
      currentValue: 'Current Value',
      unit: 'Unit',
      upperLimit: 'Upper Limit',
      lowerLimit: 'Lower Limit',
      status: 'Status',

      // 参数字段
      parameterCode: 'Parameter Code',
      parameterDes: 'Parameter Description',
      parameterVal: 'Parameter Value',
      enableFlag: 'Enable Flag',
      valid: 'Valid',
      invalid: 'Invalid',

      // 配方对话框
      modifyRecipe: 'Modify Recipe',
      modifyParameter: 'Modify Parameters',
      cancel: 'Cancel',
      confirmDistribute: 'Confirm Distribution',

      // CIM消息对话框
      cimMessage: 'CIM Message',
      code: 'Code',
      message: 'Message',
      confirm: 'Confirm',

      // 配方确认对话框
      recipeConfirmTitle: 'Confirm Recipe Distribution',
      recipeInfo: 'Recipe Information',
      recipeName: 'Recipe Name',
      stationCode: 'Station Code',
      batchNumber: 'Batch Number',
      materialCode: 'Material Code',
      quantity: 'Quantity',
      employeeInfo: 'Employee Information',
      recipeDetails: 'Recipe Details',

      // 验证消息
      pleaseSelectRecipe: 'Please select a recipe first',
      pleaseEnterBatchNumber: 'Please enter batch number',
      pleaseEnterMaterialCode: 'Please enter material code',
      deviceMustBeStandby: 'Device must be in standby status to distribute recipe',
      deviceRunning: 'Device is running, recipe distribution not allowed',
      quantityMustBePositive: 'Quantity must be a non-negative integer',
      recipeConfigIncomplete: 'Recipe parameter configuration incomplete, cannot distribute recipe. Please contact administrator to complete block_addr configuration in scada_tag table',
      getRecipeDetailFailed: 'Failed to get recipe details, cannot distribute recipe',
      getRecipeDetailFailedWithMsg: 'Failed to get recipe details: {msg}',
      recipeBaseInfoNotFound: 'Selected recipe basic information not found',
      missingBlockAddrConfig: 'The following parameters are missing correct block_addr configuration, cannot distribute recipe. Please contact engineer to complete:',
      noMaterialFound: 'No material found, please maintain material first',
      queryFailed: 'Query failed',
      queryException: 'Query exception',
      queryExceptionWithMsg: 'Query exception: {msg}',
      duplicateRecipeCode: 'Found duplicate recipe code: {codes}, please contact administrator to modify recipe configuration',
      duplicateRecipeCodeMark: 'Duplicate recipe code!',
      recipeListRefreshSuccess: 'Recipe list refreshed successfully, {count} items in total',
      noRecipeData: 'No recipe data found',
      refreshRecipeListFailed: 'Failed to refresh recipe list: {msg}',
      refreshingRecipeList: 'Refreshing recipe list...',
      pleaseStartMonitoring: 'Please start monitoring',
      operationFailed: 'Operation failed!',

      // 成功消息
      distributeSuccess: 'Recipe distribution successful',
      recipeDistributeSuccess: 'Recipe distribution successful',
      connectionSuccess: 'Connection successful',
      connectionFailed: 'Connection failed',
      connectionDisconnected: 'Connection disconnected, reconnecting...',

      // 配方详情对话框
      recipeInfo: 'Recipe Information',
      recipeDetails: 'Recipe Details',
      recipeName: 'Recipe Name',
      stationCode: 'Station Code',
      batchNumber: 'Batch Number',
      materialCode: 'Material Code',
      quantity: 'Quantity',
      employeeInfo: 'Employee Info',
      parameterCode: 'Parameter Code',
      parameterDescription: 'Parameter Description',
      parameterValue: 'Parameter Value',
      enableFlag: 'Enable Flag',
      valid: 'Valid',
      invalid: 'Invalid',
      noRecipeSelectedOrLoading: 'No recipe selected or recipe details loading...',
      loadingRecipeDetails: 'Loading recipe details...',
      cancel: 'Cancel',
      confirmDistribute: 'Confirm Distribution',

      // 报警信息
      alarmCode: 'Alarm Code',
      alarmLevel: 'Alarm Level',
      alarmDescription: 'Alarm Description',
      alarmTime: 'Alarm Time',
      resetTime: 'Reset Time',
      isReset: 'Reset Status',
      resetted: 'Reset',
      waitingReset: 'Waiting Reset',
      isSimulated: 'Is Simulated',
      yes: 'Yes',
      no: 'No',

      // 生产信息
      itemName: 'Item Name',
      currentValue: 'Current Value',
      unit: 'Unit',
      upperLimit: 'Upper Limit',
      lowerLimit: 'Lower Limit',
      status: 'Status',

      // CIM消息
      cimMessage: 'CIM Message',
      code: 'Code',
      message: 'Message',
      confirm: 'Confirm',

      // 配方下发确认
      recipeDistributeConfirm: 'Recipe Distribution Confirmation',

      // 设备状态
      deviceStatusLabel: 'Device Status',
      deviceStatus0: 'Unknown',
      deviceStatus1: 'Initializing',
      deviceStatus2: 'Standby',
      deviceStatus3: 'Alarm',
      deviceStatus4: 'Stopped',
      deviceStatus5: 'Running',
      deviceStatus6: 'PM',

      // 其他UI元素
      alarmLight: 'Tri-color Light',
      plcHeartbeat: 'PLC Heartbeat',
      recipe: 'Recipe',
      selectRecipe: 'Select Recipe',
      refreshRecipeList: 'Refresh Recipe List',
      processingQuantity: 'Processing Quantity',
      distributePreview: 'Distribution Preview',
      productionInfo: 'Production Information',
      alarmInfo: 'Alarm Information',
      station: 'Station',
      instanceCode: 'Instance Code',
      instanceDescription: 'Instance Description',
      modifyRecipe: 'Modify Recipe',
      modifyParameter: 'Modify Parameter',
      parameterDes: 'Parameter Description',
      parameterVal: 'Parameter Value',

      // 图表标题
      productionCapacity: 'Production Capacity',
      readRate: 'Read Rate'
    },

    // 确认对话框
    confirm: {
      title: 'Prompt',
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel'
    }
  }
}
