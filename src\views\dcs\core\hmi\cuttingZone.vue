<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-card >
                    <div slot="header" class="wrapTextSelect" ref="aref">
                        <span>缓存管理</span>
                    </div>
                    <el-table border ref="table" @header-dragend="tableHeaderDragend" style="width: 100%;" v-loading="loading"
                        :data="tableData" :row-key="row => row.station_code" :highlight-current-row="highlightCurrentRow"
                        :height="height">
                        <el-table-column  :show-overflow-tooltip="true" min-width="80" align="center" prop="station_code"
                            :label="$t('lang_pack.cuttingZone.CacheBit')" />
                        <!-- 任务号 -->
                        <el-table-column :show-overflow-tooltip="true" min-width="80" align="center" prop="task_num"
                            :label="$t('lang_pack.cuttingZone.TaskNumber')" />
                        <!-- 钢板型号 -->
                        <el-table-column :show-overflow-tooltip="true" min-width="80" align="center" prop="model_type"
                            :label="$t('lang_pack.cuttingZone.SteelPlateModel')" />
                        <!-- 数据来源 -->
                        <el-table-column :show-overflow-tooltip="true" min-width="80" align="center" prop="data_from"
                            :label="$t('lang_pack.mfTable.dataSources')" />
                        <!-- 缓存状态-->
                        <el-table-column :show-overflow-tooltip="true" min-width="80" align="center" prop="station_status"
                            :label="$t('lang_pack.cuttingZone.CacheStatus')" >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CACHE_STATUS[scope.row.station_status] }}
                            </template>
                        </el-table-column>
                        <!-- 到缓时间-->
                        <el-table-column :show-overflow-tooltip="true" min-width="80" align="center" prop="arrive_date"
                            :label="$t('lang_pack.cuttingZone.DelayTime')" />
                        <!-- 手动操作 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="120" :label="$t('lang_pack.commonPage.operate')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" :disabled="!scope.row.task_num" size="small" @click="transferHandle(scope.row, 'Cutting')">到切割区</el-button>
                                <el-button slot="reference" type="text" :disabled="!scope.row.task_num" size="small" @click="transferHandle(scope.row, 'SortingArea')">到分拣区</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                <el-card  style="height: 250px;margin: 10px 0;">
                    <div slot="header" class="wrapTextSelect">
                        <span>流程管理</span>
                    </div>
                    <div class="wrapRowss">
                        <el-row :gutter="12" class="wrapRowItem row" :style="{overflowX:'auto'}">
                            <el-col  :span="24" class=" nomargin">
                                <div class="flow_main" v-for="item in flowTaskData" :key="item.me_flow_task_id">
                                    <div class="flow_title">{{ item.flow_main_des }}</div>
                                    <el-dropdown trigger="click" placement="bottom" class="flow_menu">
                                        <span type="text" class="text">...</span>
                                        <el-dropdown-menu slot="dropdown">
                                            <span>
                                                <el-dropdown-item>取消流程</el-dropdown-item>
                                            </span>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                    <div class="flow_info overflowText">时间：{{ item.start_date }}</div>
                                    <el-tooltip class="item" effect="dark" :content="item.task_info" placement="top">
                                        <div class="flow_info overflowText">信息：{{ item.task_info }}</div>
                                    </el-tooltip>
                                    <div class="flow_info">步骤：{{ item.step_mod_des }}&nbsp;&nbsp;<el-button type="text"
                                            class="flow_button" @click="opendFlowTask(item)">查看</el-button></div>
                                    <el-tooltip class="item" effect="dark" :content="item.log_msg" placement="top">
                                        <div class="flow_info overflowText">日志：{{ item.log_msg }}</div>
                                    </el-tooltip>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card >
                    <div slot="header" class="wrapTextSelect" ref="aref">
                        <span>切割管理</span>
                    </div>
                    <el-table border ref="table1" @header-dragend="tableHeaderDragend" v-loading="loading"
                        :data="cutData" :row-key="row => row.station_code" :highlight-current-row="highlightCurrentRow"
                        :height="ordHeight">
                        <!-- 切割位-->
                        <el-table-column  :show-overflow-tooltip="true"  width="80" align="center" prop="station_code"
                            :label="$t('lang_pack.cuttingZone.CuttingPosition')" />
                        <!-- 任务号 -->
                        <el-table-column :show-overflow-tooltip="true" width="80" align="center" prop="task_num"
                            :label="$t('lang_pack.cuttingZone.TaskNumber')" />
                        <!-- 钢板型号 -->
                        <el-table-column :show-overflow-tooltip="true" width="80" align="center" prop="model_type"
                            :label="$t('lang_pack.cuttingZone.SteelPlateModel')" />
                        <!-- 批次号 -->
                        <el-table-column :show-overflow-tooltip="true" width="80" align="center" prop="lot_num"
                            :label="$t('lang_pack.meStationFlow.batchNumber')" />
                        <!-- 任务来源-->
                        <el-table-column :show-overflow-tooltip="true" width="80" align="center" prop="task_from"
                            :label="$t('lang_pack.taskTable.taskSource')" />
                         <!-- 缓存状态-->
                         <el-table-column :show-overflow-tooltip="true" width="80" align="center" prop="station_status"
                            :label="$t('lang_pack.cuttingZone.CacheStatus')" >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CACHE_STATUS[scope.row.station_status] }}
                            </template>
                        </el-table-column>
                        <!-- 手动操作 -->
                        <el-table-column :show-overflow-tooltip="true" width="300" align="center" fixed="right"
                            :label="$t('lang_pack.cuttingZone.ManualOperation')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small" @click="handleDialog(scope.row, '2')">查看程序</el-button>
                                <el-button slot="reference" type="text" size="small" :disabled="!!scope.row.task_num" @click="disableCut(scope.row)">禁用切割机</el-button>
                                <el-button slot="reference" :disabled="!scope.row.station_code || !scope.row.task_num" type="text" size="small" @click="reissueTask(scope.row)">重新下发任务</el-button>
                                <el-dropdown >
                                    <span class="el-button el-button--text el-button--small">更多<i class="el-icon-arrow-down el-icon--right"></i></span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item>模拟切割信号</el-dropdown-item>
                                        <el-dropdown-item>手动切割完成</el-dropdown-item>
                                        <el-dropdown-item>手动报工</el-dropdown-item>
                                        <el-dropdown-item @click.native="cutHistory(scope.row)">切割历史</el-dropdown-item>
                                        <el-dropdown-item @click.native="transferHandle(scope.row, 'GoCacheRegion')">转移到缓存位</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12" class="warehouse">
                <el-card :style="{'height':abnormalHeight + 63 + 'px'}">
                    <div slot="header" class="wrapTextSelect">
                        <span>手动控制</span>
                    </div>
                    <el-col :span="12">
                        <el-descriptions :column="1">
                            <el-descriptions-item label="当前喷码任务编号">{{ objData.task_num || '暂无数据'}}</el-descriptions-item>
                            <el-descriptions-item label="当前喷码钢板型号">{{ objData.model_type || '暂无数据'}}</el-descriptions-item>
                        </el-descriptions>
                        <div class="information" @click="viewCode">查看喷码信息</div>
                    </el-col>
                    <el-col :span="12">
                        <div class="btn">
                            <el-button type="primary" @click="Reissue">重读RFID</el-button>
                            <el-button type="primary" @click="changeCut">更换切割机</el-button>
                        </div>
                        <div class="btn">
                            <el-button type="primary" @click="cancelTask">重下喷码任务</el-button>
                            <el-button type="primary" @click="cancelSetting">缓存优先出</el-button>
                        </div>
                    </el-col>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card>
                    <div slot="header" class="wrapTextSelect">
                        <span>设备异常信息</span>
                    </div>
                    <el-table border ref="table" v-loading="loading" :data="abnormalData" :row-key="row => row.id"
                        :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                        <!-- <el-table-column  align="center" v-for="(column, index) in abnormalColumns"
                            :key="index" :prop="column.prop" :label="column.label">
                        </el-table-column> -->
                        <!-- 设备名称 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="sbmc"
                            :label="$t('lang_pack.cuttingZone.schedule')" />
                        <!-- 报警分类 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjfl"
                            :label="$t('lang_pack.cuttingZone.AlarmClassification')" />
                        <!-- 报警级别 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjjb"
                            :label="$t('lang_pack.cuttingZone.AlarmLevel')" />
                        <!-- 报警信息 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjxx"
                            :label="$t('lang_pack.cuttingZone.AlarmMessage')" />
                        <!-- 报警发生时间 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjfssj"
                            :label="$t('lang_pack.cuttingZone.AlarmOccurrenceTime')" />
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
        <el-dialog :title="title" :visible.sync="dialogVisible" :width="width" :before-close="handleClose">
            <el-table v-if="type == 1" border ref="table" v-loading="loading" :data="historyData" :row-key="row => row.id"
                :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                <!-- 切割位 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="qgw"
                    :label="$t('lang_pack.cuttingZone.CuttingPosition')" />
                <!-- 任务号 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="rwh"
                    :label="$t('lang_pack.cuttingZone.TaskNumber')" />
                <!-- 开始时间 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="kssj"
                    :label="$t('lang_pack.cuttingZone.startTime')" />
                <!-- 结束时间 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="jssj"
                    :label="$t('lang_pack.cuttingZone.endTime')" />
                <!-- 任务状态 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="rwzt"
                    :label="$t('lang_pack.taskList.TaskStatus')" />
            </el-table>
            <el-table v-if="type == 2" border ref="table" v-loading="loading" :data="historyData" :row-key="row => row.id"
                :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                <!-- 切割位 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="qgw"
                    :label="$t('lang_pack.cuttingZone.CuttingPosition')" />
                <!-- 程序号 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="cxh"
                    :label="$t('lang_pack.cuttingZone.PROGRAMNO')" />
                <!-- 计划切割时间 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="jhqgsj"
                    :label="$t('lang_pack.cuttingZone.PlannedCuttingTime')" />
                <!-- 程序上传时间 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="cxscsj"
                    :label="$t('lang_pack.cuttingZone.ProgramUploadTime')" />
            </el-table>
        </el-dialog>
        <el-dialog :title="tableTitle" :visible.sync="visible" width="30%" :before-close="handleClose">
            <el-form ref="query" :model="form" :inline="true" size="small">
                <el-form-item :label="$t('lang_pack.cuttingZone.CurrentWorkstation')">
                    <!-- 当前工位： -->
                    <el-input class="filter-item" v-model="form.station_code" disabled />
                </el-form-item>
                <br>
                <el-form-item :label="$t('lang_pack.cuttingZone.TargetWorkstation')">
                    <!-- 目标工位 -->
                    <el-select v-model="form.to_station_code" size="small" placeholder="请选择工位">
                        <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des"
                            :value="item.station_code" />
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="handleOk">确 定</el-button>
            </span>
        </el-dialog>
        <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="flowTaskDialogVisible" direction="rtl" size="100%">
            <flowTask v-if="flowTaskDialogVisible" ref="flowTask" :flow_task="currentFlowTask" :cel_api="cellIp + ':' + webapiPort" :mqtt_url="cellIp + ':' + mqttPort" @closeDrawer="flowTaskDialogVisible = false" />
        </el-drawer>
        <selectModal ref="selectModal" :dict="dict"></selectModal>
        <selectModal ref="cutSelectModal" :dict="dict"></selectModal>
    </div>
</template>
<script>
import crudCuttingZone from '@/api/dcs/core/hmi/cuttingZone'
import Cookies from 'js-cookie'
import crudStation from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import flowTask from '@/views/core/flow/task/task'
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import selectModal from '@/components/selectModal'
export default {
    name: 'CUTTING_ZONE',
    components: {flowTask,selectModal},
    data() {
        return {
            height: 0,
            ordHeight: 0,
            abnormalHeight: 0,
            loading: false,
            highlightCurrentRow: false,
            form:{
                station_code:'',
                to_station_code:'',
                task_num:'',
                url:''
            },
            objData:{
                task_num:'',
                model_type:'',
                cut_code:''
            },
            // 工位数据
            stationData: [],
            flowTaskData: [],
            tableData: [],//缓存管理data
            cutData: [], //切割管理data
            abnormalData: [
                { id: 1, sbmc: '切割机G1', bjfl: '一级报警', bjjb: '二级', bjxx: '右边启动超时', bjfssj: '2023-05-16 14:38:35', },
                { id: 2, sbmc: '喷码机', bjfl: '二级报警', bjjb: '一级', bjxx: 'z轴失速', bjfssj: '2023-05-16 14:37:35' },
                { id: 3, sbmc: '切割机G2', bjfl: '三级报警', bjjb: '二级', bjxx: '左边启动超时', bjfssj: '2023-05-16 14:39:32' },
                { id: 4, sbmc: '切割机G3', bjfl: '一级报警', bjjb: '一级', bjxx: 'x轴失速', bjfssj: '2023-05-16 14:28:45' },
                { id: 5, sbmc: '喷码机', bjfl: '二级报警', bjjb: '二级', bjxx: '左边启动超时', bjfssj: '2023-05-16 14:18:35' },
                { id: 6, sbmc: '切割机G4', bjfl: '二级报警', bjjb: '一级', bjxx: 'y轴失速', bjfssj: '2023-05-16 14:48:35' },
                { id: 7, sbmc: '喷码机', bjfl: '三级报警', bjjb: '二级', bjxx: '右边启动超时', bjfssj: '2023-05-16 14:37:33' },
            ],
            historyData: [],
            dialogVisible: false,
            width: '1000',
            title: '',
            type: '',
            tableTitle: '',
            visible: false,
            cellIp: '', // 单元IP
            webapiPort: '', // 单元API端口号
            mqttPort: '', // MQTT端口号
            flowTaskDialogVisible: false,
            currentFlowTask: {},
            timer:null,
        }
    },
    created() {
        this.getPower() //调整表格低分辨率下的几种情况
        this.cacheAreList() //缓存区
        this.getCuttingList() //获取切割管理list
        this.getAllData()// 统一获取
        this.getCellIp() //获取流程管理
    },
    // 数据字典
    dicts: ['CACHE_STATUS','PART_TYPE'],
    mounted: function () {
        const that = this
        
        this.timer = setInterval(function () {
            // that.getFlowTaskData()
        }, 5000)
        window.onresize = function temp() {
            that.getPower() //调整表格低分辨率下的几种情况
        }
    },
    beforeDestroy(){
        if(this.timer){
            clearInterval(this.timer)
        }
    },
    methods: {
        getPower(){
            const height = document.documentElement.clientHeight;
            const conditions = [
                { max: 620, height: 430, ordHeight: 170, abnormalHeight: 430 },
                { max: 700, height: 450, ordHeight: 190, abnormalHeight: 480 },
                { max: 800, height: 480, ordHeight: 225, abnormalHeight: 540 },
                { max: 900, height: 530, ordHeight: 270, abnormalHeight: 590 },
            ];
            const condition = conditions.find((condition) => height <= condition.max);
            if (condition) {
                this.height = height - condition.height;
                this.ordHeight = height - condition.ordHeight;
                this.abnormalHeight = height - condition.abnormalHeight;
            } else {
                this.height = height - 700;
                this.ordHeight = height - 440;
                this.abnormalHeight = height - 700;
            }
        },
        tableHeaderDragend(){
            this.$refs.table.doLayout()
            this.$refs.table1.doLayout()
        },
        cacheAreList(){
            crudCuttingZone.cacheArea({}).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.tableData = defaultQuery.data
                    }
                } else {
                    this.tableData = []
                    this.$message({
                        message: '缓存查询异常',
                        type: 'error'
                    })
                }
            })
            .catch(() => {
                this.tableData = []
                this.$message({
                    message: '缓存查询异常',
                    type: 'error'
                })
            })
        },
        getStation(type){
             const query = {
                    line_section_code:type === 'Cutting' ? 'CUT' :type === 'SortingArea' ? 'SORT' :'CACHE'
                }
                crudCuttingZone.cuttingCode(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        this.stationData = defaultQuery.data || []
                    }else{
                        this.stationData =[]
                        this.$message({
                            message: '工位查询异常',
                            type: 'error'
                        })
                    }
                })
                .catch(() => {
                    this.stationData =[]
                    this.$message({
                        message: '工位查询异常',
                        type: 'error'
                    })
            })
        },
        viewCode(){
            this.$refs.selectModal.open({
                type: 'pmxx',
                checkType: '',
                search: {
                    task_num:this.objData.task_num
                }
            })
        },
        getAllData(){
            crudCuttingZone.sprayInfo({}).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if(defaultQuery.code === 0 ){
                    if(defaultQuery.data.length >0){
                        this.objData = defaultQuery.data[0]
                    }
                }else{
                        this.$message({
                            message: defaultQuery.msg,
                            type: 'error'
                        })
                    }
                })
                .catch((err) => {
                    this.$message({
                        message: err.msg,
                        type: 'error'
                    })
                })
        },
        cutHistory(row){
            this.$refs.cutSelectModal.open({
                type: 'qgls',
                checkType: '',
                search: {
                    station_code:row.station_code
                }
            })
        },
        handleDialog(record, type) {
            this.historyData = []
            if (type == 2) {
                this.title = '查看程序'
                this.historyData = record.info2 || []
            }
            this.type = type
            this.dialogVisible = true
        },
        cancelTask() {
            this.$confirm(`确认重下喷码任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            .then(() => {

            })
            .catch(() => { })
        },
        Reissue() {
            this.$confirm(`确认重新下发当前任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            .then(() => {

            })
            .catch(() => { })
        },
        reissueTask(row){
            this.$confirm(`确认重新下发任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            .then(() => {
                const data = {
                    interfaceCode:'cut_issue_task',
                    task_num:row.task_num,
                    station_code:row.station_code
                }
                crudCuttingZone.issueTask(data).then(res=>{
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        this.getCuttingList()
                    }else{
                        this.$message({
                            message: defaultQuery.msg,
                            type: 'error'
                        })
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '下发任务异常',
                        type: 'error'
                    })
                })
            })
            .catch(() => { })
        },
        cancelSetting(){
            this.$confirm(`确认重新缓存优先设?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            .then(() => {

            })
            .catch(() => { })
        },
        changeCut(){
            if(!this.objData.cut_code && !this.objData.task_num){
                this.$message.warning('未获取到切割机编号或任务编号')
                return false
            }
            this.$confirm(`确认更换切割机?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            .then(() => {
                const data = {
                    task_num:'',
                    cut_code:'',
                }
                crudCuttingZone.ChangeCut(data).then(res=>{
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        this.$message({message: '更换成功',type: 'success'})
                    }else{
                        this.$message({message: defaultQuery.msg,type: 'error'})
                    }
                })
                .catch((err) => { this.$message({message: err.msg,type: 'error'}) })
            })
            .catch(() => { })
        },
        handleClose() {
            this.dialogVisible = false
            this.visible = false
            this.form.to_station_code = null
        },
        transferHandle(record, type) {
            this.form.url = type
            this.form.station_code = record.station_code
            this.form.task_num = record.task_num
            if(type === 'Cutting'){
                this.tableTitle = '缓存位到切割区'
            }else if(type === 'SortingArea'){
                this.tableTitle = '缓存位到分拣区'
            }else{
                this.tableTitle = '转移到缓存位'
            }
            this.getStation(type) //获取工位
            this.visible = true
        },
        handleOk() {
            const query = {
                station_code:this.form.station_code,
                to_station_code:this.form.to_station_code,
                task_num:this.form.task_num
            }
            crudCuttingZone.toCuttingArea(query,this.form.url).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    })
                    this.getCuttingList()
                    this.cacheAreList()
                   this.handleClose()
                } else {
                    this.$message({
                        message: `${this.tableTitle}异常`,
                        type: 'error'
                    })
                }
            })
            .catch(() => {
                this.$message({
                    message: `${this.tableTitle}异常`,
                    type: 'error'
                })
            })
        },
        getCuttingList(){
            crudCuttingZone.cuttingList({}).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    this.cutData = defaultQuery.data
                } else {
                    this.cutData = []
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                }
            })
            .catch((err) => {
                this.cutData = []
                this.$message({
                    message: '查询异常',
                    type: 'error'
                })
            })
        },
        getCellIp() {
            const query = {
                user_name: Cookies.get('userName'),
                cell_id: this.cellId,
                current_ip: window.location.hostname
            }
            selCellIP(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        const ipInfo = JSON.parse(defaultQuery.result)
                        this.cellIp = ipInfo.ip
                        this.webapiPort = ipInfo.webapi_port
                        this.mqttPort = ipInfo.mqtt_port
                        this.getFlowTaskData()
                    } else {
                        this.$message({ message: defaultQuery.msg, type: 'error' })
                    }
                })
                .catch(() => {
                    this.$message({ message: '查询异常', type: 'error' })
                })
        },
        getFlowTaskData() {
            const data = {
                line_section_code: 'CUT',
            }
            crudLoadAreaOper.flowTask(data)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        this.flowTaskData = defaultQuery.data || []
                    } else {
                        this.flowTaskData = []
                        this.$message({ message: defaultQuery.data.msg, type: 'warning' })
                    }
                })
                .catch(ex => {
                    this.flowTaskData = []
                    this.$message({ message: '查询异常：' + ex, type: 'error' })
                })
        },
        disableCut(row){
            this.$confirm(`确认禁用切割机?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            .then(() => {
                const data = {
                    user_name: Cookies.get('userName'),
                    station_code:row.station_code
                }
                crudCuttingZone.forBiddenCut(data).then(res=>{
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.code === 0) {
                            this.getCuttingList()
                            this.$message({ message: '操作成功', type: 'success' })
                        } else {
                            this.$message({ message: defaultQuery.msg, type: 'warning' })
                        }
                })
                .catch(ex => {
                    this.$message({ message: ex.msg, type: 'error' })
                })
            })
            .catch(() => { })
        },
        opendFlowTask(item) {
            this.currentFlowTask = item
            this.flowTaskDialogVisible = true
        }
    }

}
</script>
<style scoped lang="less">
.app-container {
    ::v-deep .el-card__header,
    ::v-deep .el-card__body {
        padding: 10px !important;
    }

    // .cuting {
    //     display: flex;

    //     p {
    //         width: 80px;
    //         background: #79a0f1;
    //         margin: 2px 3px;
    //         color: #fff;
    //         cursor: pointer;
    //         border: 1px solid #fff;
    //         border-radius: 3px;
    //     }
    // }

    .wrapTextSelect {
        display: flex;
        justify-content: space-between;
        align-items: center;

    }

    .information {
        cursor:pointer;
        width: 80%;
        background-color: #0dade8;
        height: 30px;
        margin: 15px 0;
        font-size: 20px;
        text-align: center;
        color: #fff;
        border-radius: 10px;
        line-height: 30px;
    }

    .warehouse {
        ::v-deep .el-descriptions-item__cell {
            padding-bottom: 27px;

            .el-descriptions-item__label,
            .el-descriptions-item__content {
                color: #28b5ff;
                font-size: 20px;
            }
        }

        .btn {
            width: 100%;
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            button{
                width: 104px;
            }
        }

    }
    .flow_main {
        background-color: #f1f3ff;
        padding-top: 50px;
        border-radius: 5px;
        border-color: rgba(0, 0, 0, 0.09);
        box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
        transition: 0.2s;
    }

    .flow_main:hover {
        border-color: rgba(0, 0, 0, 0.09);
        box-shadow: 0 2px 8px rgb(191 200 220);
    }

    .flow_title {
        font-weight: bold;
        color: #ffffff;
        margin-top: -35px;
        //   position: fixed;
        cursor: pointer;
        background-image: url('~@/assets/images/label_bg.png');
        background-position: 0;
        background-repeat: no-repeat;
        height: 35px;
        line-height: 35px;
        padding-left: 28px;
        font-size: 13px;
        margin-left: -8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .flow_info {
        line-height: 25px;
        color: #333;
        font-size: 12px;
        padding: 0 10px;
    }

    .overflowText {
        // /* 1.溢出隐藏 */
        // overflow: hidden;
        // /* 2.用省略号来代替超出文本 */
        // text-overflow: ellipsis;
        // /* 3.设置盒子属性为-webkit-box  必须的 */
        // display: -webkit-box;
        // /* 4.-webkit-line-clamp 设置为2，表示超出2行的部分显示省略号，如果设置为3，那么就是超出3行部分显示省略号 */
        // -webkit-line-clamp: 2;
        // /* 5.字面意思：单词破坏：破坏英文单词的整体性，在英文单词还没有在一行完全展示时就换行  即一个单词可能会被分成两行展示 */
        // word-break: break-all;
        // /* 6.盒子实现多行显示的必要条件，文字是垂直展示，即文字是多行展示的情况下使用 */
        // -webkit-box-orient: vertical;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        width: 320px;
    }

    .flow_button {
        padding: 0;
        font-weight: 600;
        color: #79a0f1;
    }

    .flow_menu {
        float: right;
        margin-top: -55px;
        margin-right: 10px;
    }

    .flow_menu .text {
        color: #838585;
        font-weight: 600;
        font-size: 20px;
        height: 20px;
        cursor: pointer;
    }

    .wrapRowss {
        background: #ffffff;
        border-radius: 4px;

        .wrapRowItem {
            .nomargin {
                margin-bottom: 0;
                padding: 6px;
                white-space:nowrap;
                .flow_main{
                    display:inline-block;
                    width:330px;
                    margin-left:10px;
                    overflow:hidden;
                }
            }
        }
        .wrapRowItem::-webkit-scrollbar {
            width: 10px;
            height: 10px;
            background-color: #ebeef5;
            cursor: pointer !important;
        }
        .wrapRowItem::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(255,255,255,.3);
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
            background-color: #f6f9ff;
            cursor: pointer !important;
        }
        .wrapRowItem::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,.2);
            box-shadow: inset 0 0 5px rgba(0,0,0,.2);
            border-radius: 3px;
            background: #fff;
            cursor: pointer !important;
        }
        .wrapRowItem:first-child {
            margin-top: 0;
        }
    }

    .hover-row{
        td{
            .el-dropdown{
                .el-button--text{
                    color:#fff;
                }
            }
            .inbOutNum{
                color:#fff;
            }
        }
    }
    .current-row{
        td{
            .el-dropdown{
                .el-button--text{
                    color:#fff;
                }
            }
            .inbOutNum{
                color:#fff;
            }
        }
    }

}
@media screen and (max-height: 768px) {
    .app-container{
        .warehouse{
            ::v-deep .el-descriptions-item__cell {
                .el-descriptions-item__label,
                .el-descriptions-item__content {
                    font-size: 16px !important;
                }
            }
        }
    }
}
</style>