<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="andonlist">
            <div class="carInfoTable">
              <table class="table3 tableheight">
                <tbody>
                  <tr>
                    <td>当日计划</td>
                    <td colspan="8">
                      <table class="table3">
                        <tbody>
                          <tr class="trtitle">
                            <td class="nobordertl"><p><span>当班计划产量</span><span>(件)</span></p></td>
                            <td class="nobordert"><p><span>理论完成</span><span>(件)</span></p></td>
                            <td class="nobordert"><p><span>实际完成</span><span>(件)</span></p></td>
                            <td class="nobordert"><p><span>累计停线时间</span><span>(min)</span></p></td>
                            <td class="nobordert"><p><span>设备开动率</span><span>(%)</span></p></td>
                            <td class="nobordertr"><p><span>整线状态</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td class="noborderl"><p><span>300</span></p></td>
                            <td><p><span>{{ llwc }}</span></p></td>
                            <td><p><span>{{ sjwc }}</span></p></td>
                            <td><p><span>15</span></p></td>
                            <td><p><span>85%</span></p></td>
                            <td class="noborderr"><p><span class="wholeline wholelinenormal" /></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="3" class="noborderl"><p><span>当前生产零件</span></p></td>
                            <td><p><span>计划产量</span><span>(件)</span></p></td>
                            <td><p><span>理论完成</span><span>(件)</span></p></td>
                            <td class="noborderr"><p><span>实际产量</span><span>(件)</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="3" class="noborderl"><p><span>H6500000000276右侧围外板后部</span></p></td>
                            <td><p><span>300</span></p></td>
                            <td><p><span>{{ llwc }}</span></p></td>
                            <td class="noborderr"><p><span>{{ sjwc }}</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="2" class="noborderl"><p><span>目标SPM</span></p></td>
                            <td colspan="2"><p><span>实际SPM</span></p></td>
                            <td colspan="2" class="noborderr"><p><span>模具切换时间(min)</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="2" class="noborderlb"><p><span>8</span></p></td>
                            <td colspan="2" class="noborderb"><p><span>8</span></p></td>
                            <td colspan="2" class="noborderbr"><p><span>3</span></p></td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
                <tbody>
                  <tr>
                    <td rowspan="2">下批次生产</td>
                    <td colspan="8">
                      <table class="table3">
                        <tbody>
                          <tr class="trtitle">
                            <td class="nobordertl"><p><span>下批次准备</span></p></td>
                            <td colspan="2" class="nobordert"><p><span>H6500000000275左侧围外板后部</span></p></td>
                            <td class="nobordert"><p><span>计划产量</span></p></td>
                            <td class="nobordert"><p><span>300</span></p></td>
                            <td class="nobordert"><p><span>计划生产时间</span><span>(min)</span></p></td>
                            <td class="nobordertr"><p><span>45</span></p></td>
                          </tr>
                          <tr>
                            <td class="noborderl"><p><span>模具准备</span></p></td>
                            <td colspan="12" class="noborderr">
                              <div class="wrappstyle">
                                <p><span>OP10</span><span class="wholeline wholelinenormal" /></p>
                                <p><span>OP20</span><span class="wholeline wholelinenormal" /></p>
                                <p><span>OP30</span><span class="wholeline wholelinenormal" /></p>
                                <p><span>OP40</span><span class="wholeline wholelinenormal" /></p>
                                <p><span>OP50</span><span class="wholeline wholelinenormal" /></p>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbgone">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="wrapmarquee">
            <marquee loop="infinite" scrollamount="15">
              <div class="wraptext">
                <div v-if="andon_event_list.length !==0" class="wrapandonlist">
                  <p v-for="(item,index) in andon_event_list" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                </div>
                <p v-else class="redactive">{{ below_show }}</p>
              </div>
            </marquee>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { getStationTime } from '@/api/pmc/sysTime'
import { getAndonContent } from '@/api/pmc/sysAndonContent'
import { getAndonEvent } from '@/api/pmc/sysworkshopScreenone'

import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'

import headlbg from '@/assets/images/headlbg.png'
import qyc from '@/assets/images/qyc2.jpg'
export default {
  name: 'pressline',
  components: {
  },
  data() {
    return {
      prod_line_des: '冲压A线',
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      stationCarArr: [],
      rownum: '',
      totalnum: '',
      headlbg: headlbg,
      qyc: qyc,
      below_show: '',
      andon_event_list: [],
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaPmc_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      connectUrl: '', // MQTT连接地址
      tagOnlyKey: '',
      llwc: 90,
      sjwc: 80
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    setInterval(() => {
      this.initstationTime()
    }, 1000)
    // 加载工位屏标语
    this.initAndonContent()
    setInterval(() => {
      this.initAndonContent()
    }, 5000)
    // 加载工位屏andon事件
    this.initAndonEvent()
    this.andonEventTimer = setInterval(() => {
      this.initAndonEvent()
    }, 2000)
  },
  mounted() {
    // 启动监控
    this.toStartWatch()
  },
  methods: {
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')
      console.log(this.$route)

      // 获取连接地址
      // 'ws://***************:8090/mqtt'
      this.connectUrl = 'ws://***********:8077/mqtt'

      console.log('拼接URL：' + this.connectUrl)

      // 监控Tag
      this.tagOnlyKey = 'CA_Device01/CAAstatus/Scheduled Production,CA_Device01/CAAstatus/Act Production'
      console.log('tagOnlyKey值：' + this.tagOnlyKey)
      // Tag点位集合
      var newClientTagGroupList = []
      var newClientTagList = this.tagOnlyKey.split(',')

      // mqtt连接
      this.clientMqtt = mqtt.connect(this.connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        // 订阅主题
        // var topic_clientStatus = 'SCADA_STATUS/OP1010'
        // 获取Tag值
        if (newClientTagList.length > 0) {
          this.GetTagValue(newClientTagList)
          // MesSleep(100);
        }
        // 订阅Tag组
        if (newClientTagList.length > 0) {
          for (var i = 0; i < newClientTagList.length; i++) {
            var tagTopicArray = newClientTagList[i].toString().split('/')
            var client_code = tagTopicArray[0]
            var tag_group_code = tagTopicArray[1]

            var clientGroupMultyKey = 'SCADA_CHANGE/' + client_code + '/' + tag_group_code
            if (newClientTagGroupList.indexOf(clientGroupMultyKey) < 0) {
              newClientTagGroupList.push(clientGroupMultyKey)
              this.topicSubscribe(clientGroupMultyKey)
            }
          }
        }

        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        // const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, error => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // this.clientMqtt.disconnect()
      // this.clientMqtt = null
      this.clientMqtt.end()
      this.mqttConnStatus = false
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < newTagList.length; i++) {
        var readTag = {}
        readTag.tag_key = newTagList[i].toString()
        readTagArray.push(readTag)
      }
      // 配置路径
      var path = 'http://***********:8094/cell/core/scada/CoreScadaReadTag'
      console.log('调用接口：' + path)

      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');

              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()

                  if (tagKey === 'CA_Device01/CAAstatus/Act Production') {
                    this.sjwc = tagValue
                  } else if (tagKey === 'CA_Device01/CAAstatus/Scheduled Production') {
                    this.llwc = tagValue
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      console.log('MQTT收到来自', channel, '的消息', message.toString())
      // var jsonData = JSON.parse(message)
      // var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        var jsonData = JSON.parse(message)
        console.log(jsonData)
        var tagKey = jsonData.TagKey
        var TagNewValue = jsonData.TagNewValue
        console.log(tagKey)
        console.log(TagNewValue)
        if (jsonData != null) {
          if (tagKey === 'CA_Device01/CAAstatus/Act Production') {
            console.log(tagKey)
            console.log(TagNewValue)
            this.sjwc = TagNewValue
          } else if (tagKey === 'CA_Device01/CAAstatus/Scheduled Production') {
            console.log(tagKey)
            console.log(TagNewValue)
            this.llwc = TagNewValue
          }
        }
      }
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏标语
    initAndonContent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code
      }
      getAndonContent(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data === null || res.data.length === 0) {
            this.below_show = ''
          }
          this.below_show = res.data[0].below_show
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏andon事件
    initAndonEvent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        prod_line_type: this.$route.query.prod_line_type,
        work_center_code: this.$route.query.work_center_code
      }
      getAndonEvent(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data[0].andon_event_list === null || res.data[0].andon_event_list.length === 0) {
            this.andon_event_list = []
          }
          this.andon_event_list = res.data[0].andon_event_list
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.marginT{
  margin-top: 10px;
}
.slideT{
  margin-top: 20px;
}
.cardheadbg{
  background-color: #031c45;
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  // margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .andonlist{
  height: 545px;
  ul{
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    margin-right: -17px;
        margin-left: 10px;
    li{
      list-style: none;
      width: 13%;
      height: 99px;
      text-align: center;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 5px;
      padding: 5px;
      position: relative;
      border-radius: 5px;
      margin-right: 8px;
      span{
        font-size: 30px;
        font-weight: 700;
        z-index: 2;
      }
      .carorder{
        font-size: 16px;
      }
      img{
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
    }
  }
}
.cardheadbgone{
  background-color: #0070c2;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  .wrapandonlist{
    display: flex;
    align-items: center;
    p{
      margin-right: 50px;
    }
  }
  p{
  color: #fbff03;
  font-size: 60px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  height: 80px;
  word-spacing: 150px;
  }
}
::v-deep .commonstatus{
  background-color: #ffffff;
}
::v-deep .nomalstatus{
  background-color: #00a047;
  span{
    color: #ffffff;
  }
}
::v-deep .errorstatus{
  background-color: #ff0000;
}
::v-deep .warningstatus{
  background-color: #ffd600;
}
::v-deep .el-card{
  border-radius: 0;
}
.redactive{
  color: red !important;
}

/* Table 3 Style */
table.table3{
    font-size: 18px;
    text-align:center;
    border-collapse:collapse;
    width: 100%;
}
.table3 tbody td{
    background-color: #1d3a6a;
    color: #fff;
    border: 1px solid #ffffff;
    font-size: 26px;
    font-weight: bold;
    text-shadow: 2px 2px 4px #000;
}
.trtitle td p{
  margin: 10px 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}
.wholeline{
  width:30px;
  height: 30px;
  border-radius: 50%;
}
.wholelinenormal{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.wholelineerror{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #f00;
  box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgb(0 0 0 / 30%);
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.noborder{
  border: 0 !important;
}
.wrappstyle{
  display: flex;
  justify-content: space-around;
  p{
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    span{
      margin: 0 5px;
    }
  }
}
.nobordertl{
  border-top: 0 !important;
  border-left: 0 !important;
}
.nobordertr{
  border-top: 0 !important;
  border-right: 0 !important;
}
.noborderbr{
  border-bottom: 0 !important;
  border-right: 0 !important;
}
.nobordert{
  border-top: 0 !important;
}
.noborderr{
  border-right: 0 !important;
}
.noborderb{
  border-bottom: 0 !important;
}
.noborderl{
  border-left: 0 !important;
}
.noborderlb{
  border-left: 0 !important;
  border-bottom: 0 !important;
}
@-webkit-keyframes heart {
  0% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  30% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  50% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  75% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  80% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
}

@keyframes heart {
  0% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  30% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  50% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  75% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  80% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
}
</style>
