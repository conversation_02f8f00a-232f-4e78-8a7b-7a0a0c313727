<template>
  <div class="orderinfo-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="12">
          <el-card class="cardFirst">
            <div class="orderrecipe">
              <div class="orderButton">
                <el-button class="buttonone" size="large" type="primary" @click="getOrder">选择订单</el-button>
              </div>
              <div class="wrapDes">
                <el-descriptions :column="2">
                  <el-descriptions-item label="订单号">{{ currentOrderInfo.make_order }}</el-descriptions-item>
                  <el-descriptions-item label="计划数量">{{ currentOrderInfo.mo_plan_count }}</el-descriptions-item>
                  <el-descriptions-item label="机型">{{ currentOrderInfo.small_model_type }}</el-descriptions-item>
                  <el-descriptions-item label="完成数量">{{ currentOrderInfo.mo_finish_count }}</el-descriptions-item>
                  <el-descriptions-item label="模组配方">{{ currentOrderInfo.recipe }}</el-descriptions-item>
                  <el-descriptions-item label="电芯排废率">{{ currentOrderInfo.mo_scrap_rate }}</el-descriptions-item>
                  <el-descriptions-item label="开始时间">{{ currentOrderInfo.plan_start_time }}</el-descriptions-item>
                  <el-descriptions-item label="结束时间">{{ currentOrderInfo.plan_end_time }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
          <el-card>
            <el-tabs type="border-card">
              <el-tab-pane label="电芯配方">
                <el-table border @header-dragend="crud.tableHeaderDragend()" height="460" :data="batteriesTableData" style="width: 100%" :highlight-current-row="true">
                  <el-table-column  label="电芯序号" prop="dx_num" />
                  <el-table-column  label="线体选择" prop="line_num" />
                  <el-table-column  label="电芯方向" prop="dx_direct" />
                  <el-table-column  label="电芯档位" prop="dx_gear" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="数据范围">
                <el-table border @header-dragend="crud.tableHeaderDragend()" height="460" :data="limitList" style="width: 100%" :highlight-current-row="true">
                  <el-table-column  label="范围值约束编码" prop="mz_limit_code" />
                  <el-table-column  label="范围值约束描述" prop="mz_limit_des" />
                  <el-table-column  label="上限" prop="upper_limit" />
                  <el-table-column  label="下限" prop="down_limit" />
                  <el-table-column  label="排废槽号" prop="ng_rack_code" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="条码NG规则">
                <el-table border @header-dragend="crud.tableHeaderDragend()" height="460" :data="dxbarList" style="width: 100%" :highlight-current-row="true">
                  <el-table-column  label="电芯条码排废名称" prop="dxbar_ng_name" width="120" />
                  <el-table-column  label="条码起始位置" prop="start_index" />
                  <el-table-column  label="条码结束位置" prop="end_index" />
                  <el-table-column  label="NG方式" prop="ng_way" />
                  <el-table-column  label="值集合" prop="value_list" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="cardFirst">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="orderButton">
                  <el-button size="large" type="primary" @click="handleRequestPallet">开始扫描电芯</el-button>
                </div>
              </el-col>
              <el-col :span="6">
                <span>电芯完成/计划：</span><span style="font-size:20px;font-weight:700">{{ finishCountAndPlanCount }}</span>
                <el-tag v-if="dxScanFinish" type="success">已全部完成</el-tag>
              </el-col>
              <el-col :span="6">
                <div class="orderButton">
                  <el-button size="large" type="primary" @click="MaunalSubmitDxData">重提离线数据</el-button>
                </div>
              </el-col>

            </el-row>
            <el-row :gutter="20" style="margin-top:10px;">
              <el-col :span="24">
                <div class="scanStyle">
                  <span>客户端IP：</span>
                  <span>
                    {{ iframeStaionCode }}
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top:10px;">
              <el-col :span="24">
                <div class="scanStyle">
                  <span>模组码：</span>
                  <span>
                    {{ mzBarcode }}
                  </span>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-card>
            <div class="scanStyle">
              <span>电芯码：</span>
              <div class="wrapimin">
                <el-input ref="scantwo" v-model="scantwo" type="text" size="large" placeholder="请扫描电芯" @keyup.enter.native="keyupClick" />
                <img :src="keyboard" @click="showKeyboard">
              </div>
              <el-button class="scanBtn" size="large" type="primary" @click="ManualDxScan">扫描</el-button>
            </div>
            <el-table border @header-dragend="crud.tableHeaderDragend()" :data="dxData" style="width: 100%" height="430" :highlight-current-row="true">
              <el-table-column  label="替换电芯" prop="checked">
                <template slot-scope="scope">
                  <el-checkbox v-model="checked" :true-label="scope.row.dx_index" @change="handleUpdDx" />
                </template>
              </el-table-column>
              <el-table-column  label="序号" prop="dx_index" />
              <el-table-column  label="串数" prop="mk_num" />
              <el-table-column  label="NG标识" prop="check_ng_flag">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.check_ng_flag === 'Y' ? 'danger' : 'success'" disable-transitions>
                    {{ scope.row.check_ng_flag }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column  label="NG描述" width="120" prop="check_ng_msg" />
              <el-table-column  label="电芯码" width="200" prop="dx_barcode" />
              <el-table-column  label="时间" prop="creation_date" :formatter="formatDate" />
              <el-table-column  label="档位" prop="dx_gear" />
              <el-table-column  label="批次号" prop="dx_ori_batch" />
              <el-table-column  label="容量" prop="dx_ori_capacity" />
              <el-table-column  label="电压" prop="dx_ori_ocv4_pressure" />
              <el-table-column  label="内阻" prop="dx_ori_ocr4_air" />
              <el-table-column  label="K值" prop="dx_ori_k_value" />
              <el-table-column  label="Dcir内阻" prop="dx_ori_dcir" />
              <el-table-column  label="厚度" prop="dx_ori_thickness" />
              <el-table-column  label="ocv4时间" prop="dx_ori_ocv4_time" />
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
    <el-drawer title="选择订单" :visible.sync="chooseOrder" direction="rtl" size="85%">
      <el-table border @header-dragend="crud.tableHeaderDragend()" :data="radioArr" style="width: 100%" height="550" highlight-current-row @row-click="singleElection">
        <el-table-column  align="center" width="55" label="选择">
          <template slot-scope="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio v-model="templateSelection" class="radio" :label="scope.row.mo_id">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column  align="center" prop="mo_from" label="订单来源" width="80" />
        <el-table-column  align="center" prop="make_order" label="订单号" width="150" />
        <el-table-column  align="center" prop="product_batch" label="订单批号" width="80" />
        <el-table-column  align="center" prop="small_model_type" label="产品型号" width="250" />
        <el-table-column  align="center" prop="mo_plan_count" label="订单计划数量" width="80" />
        <el-table-column  align="center" prop="mo_finish_count" label="实际完成数量" width="80" />
        <el-table-column  align="center" prop="mo_order_by" label="订单排序" width="80" />
        <el-table-column  align="center" prop="plan_start_time" label="计划开始时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="plan_end_time" label="计划结束时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="start_date" label="订单启动时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="finish_date" label="订单完成时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="mo_custom_des" label="订单来源客户描述" width="80" />
      </el-table>
    </el-drawer>
    <el-drawer title="订单明细" :visible.sync="orderDetail" direction="rtl" size="50%" />

    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { chooseOrderInfo, getOrderInfo, showOrderInfo, mesGxMzManualMzInfoSelect,
  mesGxMzManualSelect, mesGxMzOffLineDxCheck, mesGxMzOffLineIpSelect, mesGxMzOffLineStart, mesGxMzOffLineUpData } from '@/api/mes/project/manualGroup.js'
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'

export default {
  name: 'mzOffLineGroup',
  components: {
    SimpleKeyboard
  },
  data() {
    return {
      currentOrderInfo: {
        make_order: '',
        mo_plan_count: '',
        small_model_type: '',
        mo_finish_count: '',
        recipe: '',
        mo_scrap_rate: '',
        plan_start_time: '',
        plan_end_time: ''
      },
      checked: false,
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      scantwo: '',
      chooseOrder: false,
      orderDetail: false,
      choose_make_order: '',
      choose_mo_id: '',
      iframeStaionCode: '',
      allowScan: 0, // 允许扫描电芯
      finishCountAndPlanCount: '0/0', // 电芯完成数量/电芯计划数量

      requestPallet: '0', // 请求一个空托盘
      mzBarcode: '', // 模组码
      plcRequestFlag: 'N', // PLC触发请求
      dxIndex: '',
      dxScanFinish: false,
      radioArr: [],
      // 当前选择的行的id
      templateSelection: '',
      recipeTableData: [],
      batteriesTableData: [],
      dxbarList: [],
      limitList: [],
      dxData: []
    }
  },
  beforeUpdate() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.scantwo.focus()
    })
  },
  created() { },
  mounted() {
    this.initStationCode()
  },
  beforeDestroy() {
  },
  methods: {
    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate() + ' ' + dt.getHours() + ':' + dt.getMinutes() + ':' + dt.getSeconds()
      }
    },
    // 扫描回车键
    keyupClick() {
      this.toGxDxOnlineCheck()
    },
    // 初始化加载当前工位
    initStationCode() {
      var query = {
        userName: Cookies.get('userName')
      }
      mesGxMzOffLineIpSelect(query)
        .then(res => {
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            this.iframeStaionCode = res.result
            this.dxDataSelect()
            this.initOrderDes()
          }
        })
        .catch(() => {
          this.$message({
            message: '获取当前电脑客户端IP异常',
            type: 'error'
          })
        })
    },
    // 初始化订单信息
    initOrderDes() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode
      }
      chooseOrderInfo(query)
        .then(res => {
          if (res.data[0].mo_flag > 0) {
            this.templateSelection = res.data[0].mo_id
            var initshowQuery = {
              userName: Cookies.get('userName'),
              station_code: this.iframeStaionCode
            }
            showOrderInfo(initshowQuery)
              .then(res => {
                const result = JSON.parse(res.result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
                }
                this.batteriesTableData = result.mzd_list
                this.dxbarList = result.dxbar_list
                this.limitList = result.limit_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          } else {
            var initshowQueryone = {
              userName: Cookies.get('userName'),
              station_code: this.iframeStaionCode
            }
            showOrderInfo(initshowQueryone)
              .then(res => {
                const result = JSON.parse(res.result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
                }
                this.batteriesTableData = result.mzd_list
                this.dxbarList = result.dxbar_list
                this.limitList = result.limit_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          }
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    getOrder() {
      this.chooseOrder = true
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode
      }
      chooseOrderInfo(query)
        .then(res => {
          if (res.code !== 0 && res.count < 0) {
            this.$message({
              message: '选择订单异常',
              type: 'error'
            })
            return
          }
          this.radioArr = res.data
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    // 单选---选择订单
    singleElection(row) {
      this.chooseOrder = false
      this.templateSelection = row.mo_id
      this.choose_make_order = row.make_order
      this.choose_mo_id = row.mo_id
      var getQuery = {
        userName: Cookies.get('userName'),
        make_order: this.choose_make_order,
        station_code: this.iframeStaionCode,
        mo_id: this.choose_mo_id
      }
      getOrderInfo(getQuery)
        .then(() => {
          var showQuery = {
            userName: Cookies.get('userName'),
            station_code: this.iframeStaionCode
          }
          showOrderInfo(showQuery)
            .then(res => {
              const result = JSON.parse(res.result)
              if (result.mo_list.length === 0) {
                this.$message({
                  message: '当前订单数据为空',
                  type: 'warning'
                })
                this.currentOrderInfo.make_order = ''
                this.currentOrderInfo.mo_plan_count = ''
                this.currentOrderInfo.small_model_type = ''
                this.currentOrderInfo.mo_finish_count = ''
                this.currentOrderInfo.recipe = ''
                this.currentOrderInfo.mo_scrap_rate = ''
                this.currentOrderInfo.plan_start_time = ''
                this.currentOrderInfo.plan_end_time = ''
              } else {
                this.currentOrderInfo.make_order = result.mo_list[0].make_order
                this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                this.currentOrderInfo.recipe = result.mo_list[0].recipe
                this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
              }
              this.batteriesTableData = result.mzd_list
              this.dxbarList = result.dxbar_list
              this.limitList = result.limit_list
            })
            .catch(() => {
              this.$message({
                message: '展示信息查询异常',
                type: 'error'
              })
            })
        })
        .catch(() => {
          this.$message({
            message: '获取信息查询异常',
            type: 'error'
          })
        })
    },
    toGxDxOnlineCheck() {
      this.plcRequestFlag = 'N'
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        dx_barcode: this.scantwo,
        dx_index: this.dxIndex
      }
      mesGxMzOffLineDxCheck(query)
        .then(res => {
          this.dxIndex = ''
          this.checked = false
          this.dxScanFinish = false
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            if (res.result === 'Y') {
              // 扫描完成
              this.dxScanFinish = true
              this.$message({
                message: '电芯已全部扫描完成',
                type: 'info',
                duration: 5000
              })
              this.MaunalSubmitDxData()
            }
          }
          this.dxDataSelect()
          this.getFinishAndPlanCount()
          this.scantwo = ''
          this.$refs.scantwo.focus()
        })
        .catch(() => {
          this.$message({
            message: '电芯上线校验异常',
            type: 'error'
          })
          this.scantwo = ''
          this.$refs.scantwo.focus()
        })
    },
    dxDataSelect() {
      this.dxData = []
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        plc_request_flag: this.plcRequestFlag
      }
      mesGxMzManualSelect(query)
        .then(res => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
          if (res.count > 0) {
            this.dxData = res.data
          }
        })
        .catch(() => {
          this.$message({
            message: '查询电芯数据异常',
            type: 'error'
          })
        })
    },
    ManualDxScan() {
      if (this.scantwo === '') {
        this.$message({
          message: '输入电芯条码不能为空',
          type: 'warning'
        })
        return
      }
      this.toGxDxOnlineCheck()
    },
    // 提交离线数据
    MaunalSubmitDxData() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode
      }
      mesGxMzOffLineUpData(query)
        .then(res => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          } else {
            this.$message({
              message: '提交扫描数据到正式表成功',
              type: 'success'
            })
            this.$refs.scantwo.focus()
          }
        })
        .catch(() => {
          this.$message({
            message: '提交离线数据异常',
            type: 'error'
          })
        })
    },

    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
          }
        }
      }
    },
    // 请求是否进行开始
    handleRequestPallet() {
      this.$confirm('是否开始电芯扫描？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.dxScanFinish = false
          var query = {
            userName: Cookies.get('userName'),
            station_code: this.iframeStaionCode
          }
          mesGxMzOffLineStart(query)
            .then(res => {
              if (res.code !== 0) {
                this.$message({
                  message: res.msg,
                  type: 'error'
                })
              } else {
                this.$message({
                  message: '初始化成功,请依次开始扫描电芯',
                  type: 'success'
                })
                this.$refs.scantwo.focus()
              }
            })
            .catch(() => {
              this.$message({
                message: '请求是否进行开始异常',
                type: 'error'
              })
            })
        })
        .catch(() => {
          this.$message({
            message: '请求是否进行开始异常',
            type: 'error'
          })
        })
    },
    // 获取完成数量与计划数量
    getFinishAndPlanCount() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        plc_request_flag: this.plcRequestFlag
      }
      mesGxMzManualMzInfoSelect(query)
        .then(res => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
          const resultInfo = res.result.split(',')
          this.mzBarcode = resultInfo[0]
          this.finishCountAndPlanCount = resultInfo[1]
        })
        .catch(() => {
          this.$message({
            message: '获取完成数量与计划数量异常',
            type: 'error'
          })
        })
    },
    handleUpdDx(val) {
      this.dxIndex = val
      if (!val) {
        this.dxIndex = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  height: calc(100vh - 80px);
}
.orderButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 15px;
  button {
    margin-left: 0 !important;
  }
  .buttonone {
    width: 90px;
    height: 30px;
    margin-top: -10px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonone:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .buttonTwo {
    margin-top: 5px !important;
    width: 90px;
    height: 30px;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonTwo:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .btnRequestPallet1{
    width: 100px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #57EF99, #13ce66);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #57EF99, -1px 1px 1px 1px #13ce66, -1px 3px 18px 0px #13ce66;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .btnRequestPallet1:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #57EF99, -1px 1px 1px 0.5px #13ce66, -1px 4px 10px 1px #13ce66;
  }
  .btnRequestPallet0{
    width: 100px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .btnRequestPallet0:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #AAAAAA, -1px 1px 1px 0.5px #AAAAAA, -1px 4px 10px 1px #AAAAAA;
  }
}
// .wrapDes {
//   margin-top: 30px;
// }
.stepStyle {
  padding: 10px 0;
  padding-bottom: 30px;
}
.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
}
.el-menu {
  display: flex;
  justify-content: space-between;
  overflow: auto;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: #ffffff !important;
}
::v-deep .el-menu--horizontal > .el-menu-item {
  flex: 1;
  text-align: center;
  margin: 0 5px;
}
.flagActive {
  background-color: #e8efff;
}
.orderrecipe {
  display: flex;
  align-items: center;
}
.cardFirst {
  margin-bottom: 10px;
}
.statuStyle {

  justify-content: space-around;
  p {
    display: flex;
    align-items: center;
    color: #333333;
    margin: 0;
    margin-bottom: 10px;
    span {
      display: flex;
      align-items: center;
    }
    .commonsatu {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: block;
    }
    .statuOne {
      background-color: #13ce66;
    }
    .statuZero {
      background-color: #cccccc;
    }
    .statuTwo {
      background-color: #ff4949;
    }
    .statuSecond {
      background-color: #cccccc;
    }
  }
}
::v-deep .el-card__header {
  text-align: center;
  background: #79a0f1;
  color: #ffffff;
  padding: 10px 0;
}
::v-deep .el-tabs--border-card > .el-tabs__header {
  background-color: #ffffff;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333 !important;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #ffffff !important;
    background: #79a0f1;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #333333;
}
::v-deep .el-table th {
  background-color: #e8efff !important;
}
::v-deep .el-table__body tr.current-row > td {
  background-color: #79a0f1 !important;
  color: #ffffff;
}
::v-deep .el-input__inner{
  padding-right: 40px;
}
.keyboard-mask{
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 99;
    left: 0;
    right: 0;
}
.wrapimin{
  position: relative;
  width: 100%;
  img{
    position: absolute;
    right: 7px;
    top: -3px;
    width: 45px;
    z-index: 2;
  }
}
</style>
