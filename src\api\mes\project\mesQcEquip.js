import request from '@/utils/request'

// 查询PLC配方
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipSel',
    method: 'post',
    data
  })
}
// 新增PLC配方
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipIns',
    method: 'post',
    data
  })
}
// 修改PLC配方
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipUpd',
    method: 'post',
    data
  })
}
// 删除PLC配方
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

