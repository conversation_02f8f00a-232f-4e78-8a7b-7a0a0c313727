<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方类型：">
                <el-select v-model="query.recipe_type" clearable>
                  <el-option v-for="item in dict.RECIPE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="版本号：">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="130px" :inline="true">
          <el-form-item label="配方类型" prop="recipe_type">
            <el-select v-model="form.recipe_type" clearable>
              <el-option v-for="item in dict.RECIPE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="配方描述" prop="recipe_name">
            <el-input v-model="form.recipe_name" />
          </el-form-item>
          <el-form-item label="版本号" prop="recipe_version">
            <el-input v-model="form.recipe_version" />
          </el-form-item>
          <el-form-item label="责任人" prop="liable_person">
            <el-input v-model="form.liable_person" />
          </el-form-item>
          <el-form-item label="更新说明" prop="update_remark">
            <el-input v-model="form.update_remark" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            height="450px"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_id }}</el-descriptions-item>
                  <el-descriptions-item label="配方类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_type }}</el-descriptions-item>
                  <el-descriptions-item label="配方描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_name }}</el-descriptions-item>
                  <el-descriptions-item label="版本号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_version }}</el-descriptions-item>
                  <el-descriptions-item label="责任人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.liable_person }}</el-descriptions-item>
                  <el-descriptions-item label="更新说明" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.update_remark }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="recipe_type" min-width="100" label="配方类型" />
            <el-table-column :show-overflow-tooltip="true" prop="recipe_name" min-width="100" label="配方描述" />
            <el-table-column :show-overflow-tooltip="true" prop="recipe_version" min-width="100" label="版本号" />
            <el-table-column :show-overflow-tooltip="true" prop="liable_person" min-width="100" label="责任人" />
            <el-table-column :show-overflow-tooltip="true" prop="update_remark" min-width="100" label="更新说明" />
            <el-table-column :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  recipe_id: '',
  recipe_type: '',
  recipe_name: '',
  recipe_version: '',
  liable_person: '',
  update_remark: '',
  enable_flag: 'Y'
}
export default {
  name: 'MES_RECIPE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '配方信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id asc'],
      // CRUD Method
      crudMethod: { ...crudRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'RECIPE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe:add'],
        edit: ['admin', 'mes_recipe:edit'],
        del: ['admin', 'mes_recipe:del'],
        down: ['admin', 'mes_recipe:down']
      },
      rules: {
        recipe_type: [{ required: true, message: '请选择配方类型', trigger: 'blur' }],
        recipe_name: [{ required: true, message: '请输入配方描述', trigger: 'blur' }],
        recipe_version: [{ required: true, message: '请输入版本号', trigger: 'blur' }]
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将配方为【' + data.recipe_name + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudRecipe
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              recipe_id: data.recipe_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
