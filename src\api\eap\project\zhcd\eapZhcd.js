import request from '@/utils/request'

// 1.查询配扳机任务接口
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjApsPlanSel',
    method: 'post',
    data
  })
}
// 2.扫描订单号查询
export function EapPbjApsPlanAddByScan(data) {
  return request({
    url: 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjApsPlanAddByScan',
    method: 'post',
    data
  })
}

// 3.任务下发
export function EapPbjApsPlanDownTask(data) {
  return request({
    url: 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjApsPlanDownTask',
    method: 'post',
    data
  })
}

// 4.删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjApsPlanDel',
    method: 'post',
    data
  })
}
// 5.取消任务按钮
export function EapPbjApsPlanUpdLotStatus(data) {
  return request({
    url: 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjApsPlanUpdLotStatus',
    method: 'post',
    data
  })
}
export default { sel, del, EapPbjApsPlanAddByScan, EapPbjApsPlanDownTask, EapPbjApsPlanUpdLotStatus }
