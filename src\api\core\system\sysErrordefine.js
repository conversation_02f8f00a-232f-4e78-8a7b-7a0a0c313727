import request from '@/utils/request'

//查询 错误消息定义
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysErrorDefineSel',
    method: 'post',
    data
  })
}
//新增 错误消息定义
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysErrorDefineIns',
    method: 'post',
    data
  })
}
//修改 错误消息定义
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysErrorDefineUpd',
    method: 'post',
    data
  })
}
//删除 错误消息定义
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysErrorDefineDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
