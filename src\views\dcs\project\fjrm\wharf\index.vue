<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="码头编码:">
                <!-- 码头编码 -->
                <el-input v-model="query.wharf_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.fjrm.taskFrom')" prop="ware_house">
                <!-- 库区域 -->
                <el-select v-model="form.ware_house" clearable filterable>
                  <el-option
                    v-for="item in dict.WARE_HOUSE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.wharfCode')" prop="wharf_code">
                <!-- 码头编码 -->
                <el-input v-model="form.wharf_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.wharfDes')" prop="wharf_des">
                <!-- 码头描述 -->
                <el-input v-model="form.wharf_des" clearable size="small" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.wharfType')" prop="wharf_type">
                <!-- 码头类型 -->
                <el-select v-model="form.wharf_type" clearable filterable>
                  <el-option
                    v-for="item in dict.WHARF_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.wharfOrder')" prop="wharf_order">
                <!-- 码头排序 -->
                <el-input v-model.number="form.wharf_order" type="number" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.locationX')" prop="location_x">
                <!-- X坐标 -->
                <el-input v-model="form.location_x" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.locationY')" prop="location_y">
                <!-- Y坐标 -->
                <el-input v-model="form.location_y" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.locationZ')" prop="location_z">
                <!-- Z坐标 -->
                <el-input v-model="form.location_z" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.rgvCode')" prop="rgv_code">
                <!-- RGV编码 -->
                <el-input v-model="form.rgv_code" clearable size="small" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.enableFlag')" prop="enable_flag">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" prop="wharf_id" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->

            <!-- 库区域 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ware_house"
              :label="$t('lang_pack.fjrm.wareHouse')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
              </template>
            </el-table-column>
            <!-- 码头编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wharf_code"
              :label="$t('lang_pack.fjrm.wharfCode')"
              width="140"
              align="center"
            />
            <!-- 码头描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wharf_des"
              :label="$t('lang_pack.fjrm.wharfDes')"
              width="140"
              align="center"
            />
            <!-- 码头类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wharf_type"
              :label="$t('lang_pack.fjrm.wharfType')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHARF_TYPE[scope.row.wharf_type] }}
              </template>
            </el-table-column>
            <!-- 码头排序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wharf_order"
              :label="$t('lang_pack.fjrm.wharfOrder')"
              width="80"
              align="center"
            />

            <!-- X坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_x"
              :label="$t('lang_pack.fjrm.locationX')"
              width="140"
              align="center"
            />
            <!-- Y坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_y"
              :label="$t('lang_pack.fjrm.locationY')"
              width="140"
              align="center"
            />
            <!-- Z坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_z"
              :label="$t('lang_pack.fjrm.locationZ')"
              width="140"
              align="center"
            />

            <!-- RGV编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="rgv_code"
              :label="$t('lang_pack.fjrm.rgvCode')"
              width="80"
              align="center"
            />
            <!-- 任务号(执行) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              :label="$t('lang_pack.fjrm.taskNum')"
              width="80"
              align="center"
            />

            <el-table-column
              :label="$t('lang_pack.fjrm.lockFlag')"
              align="center"
              width="120"
              prop="lock_flag"
            >
              <!-- 是否锁定 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.lock_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" />
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('lang_pack.fjrm.enableFlag')"
              align="center"
              width="120"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              prop="button"
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWharf from '@/api/dcs/project/fjrm/wharf/wharf'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  wharf_id: '',
  ware_house: '',
  wharf_code: '',
  wharf_des: '',
  wharf_type: '',
  wharf_order: '',
  location_x: '',
  location_y: '',
  location_z: '',
  rgv_code: '',
  task_num: '',
  lock_flag: 'N',
  enable_flag: 'Y'
}
export default {
  name: 'WHARF',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '码头基础表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'wharf_id',
      // 排序
      sort: ['wharf_id asc'],
      // CRUD Method
      crudMethod: { ...crudWharf },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'dcs_fmod_wharf:add'],
        edit: ['admin', 'dcs_fmod_wharf:edit'],
        del: ['admin', 'dcs_fmod_wharf:del']
      },
      rules: {
        wharf_code: [{ required: true, message: '请选择码头编码', trigger: 'blur' }],
        wharf_des: [{ required: true, message: '请选择码头描述', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG', 'WARE_HOUSE', 'WHARF_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudWharf
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              wharf_id: data.wharf_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
