<template>
  <div>
    <el-card class="box-card2" shadow="always">
      <div
        slot="header"
        class="clearfix"
        style="
          font-size: 14px;
          color: #757575;
          font-weight: 700;
          text-align: right;
        "
      >
        <span style="float: left">安灯大类型工位维护</span>

        <el-button
          v-if="true"
          class="filter-item"
          size="mini"
          type="primary"
          icon="el-icon-plus"
          style="margin-top: 3px; margin-left: 5px"
          plain
          @click="handleAdd"
        >
          新增
        </el-button>
      </div>
      <el-table
        ref="table"
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        :cell-style="crud.cellStyle"
        :stripe="true"
        height="250px"
        :highlight-current-row="true"
        @sort-change="sortChage"
        @row-click="handleRowClick"
      >
        <el-table-column 
          :show-overflow-tooltip="true"
          prop="station_code"
          min-width="100"
          label="工位编码"
          sortable="custom"
        />
        <el-table-column 
          :show-overflow-tooltip="true"
          prop="station_des"
          min-width="100"
          label="工位描述"
          sortable="custom"
        />
        <el-table-column 
          :show-overflow-tooltip="true"
          prop="station_andon_order"
          min-width="100"
          label="排序"
          sortable="custom"
        />

        <el-table-column 
          label="有效标识"
          align="center"
          prop="enable_flag"
          width="100"
        >
          <template
            slot-scope="scope"
          ><!--取到当前单元格-->
            {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
          </template>
        </el-table-column>
        <!-- Table单条操作-->
        <el-table-column 
          label="操作"
          width="125"
          align="center"
          fixed="right"
          prop="tableButton"
        >
          <template slot-scope="scope">
            <el-link
              icon="el-icon-edit"
              type="primary"
              style="color: #0072d6; font-size: 12px"
              @click="handleTableEdit(scope.row)"
            >编辑</el-link>
            &nbsp;&nbsp;
            <el-link
              icon="el-icon-delete"
              type="primary"
              style="color: #0072d6; font-size: 12px"
              @click="handleTableDel(scope.row)"
            >删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <!--<pagination />-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px; float: right"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />

      <el-drawer
        append-to-body
        :title="dialogTitle"
        :visible.sync="dialogVisbleSync"
        size="450px"
        @closed="drawerClosed"
      >
        <!--<el-form ref="form" :inline="true" :model="form" :rules="rules" size="small" label-width="100px">-->
        <el-form
          ref="form"
          :inline="false"
          :model="form"
          :rules="rules"
          size="small"
          label-width="110px"
        >
          <el-form-item label="工位代码" prop="station_code">
            <el-popover v-model="customPopover" placement="top" width="550">
              <el-input
                v-model="stationTableQuery.station_code_des"
                clearable
                size="mini"
                placeholder="工位编码/描述"
                style="width: 150px"
                class="filter-item"
              />
              <el-button
                class="filter-item"
                size="mini"
                type="success"
                icon="el-icon-search"
                @click="handleStationQuery"
              >搜索</el-button>
              <el-table
                ref="tableTag"
                v-loading="stationTableLoading"
                :data="stationData"
                style="width: 100%; height: 250px; margin-top: 5px"
                :header-cell-style="{ padding: '10' }"
                :cell-style="{ padding: '0' }"
                max-height="230"
              >
                <el-table-column  label="操作" width="50" align="center">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="primary"
                      icon="el-icon-plus"
                      circle
                      @click="handleChooseStation(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column 
                  :show-overflow-tooltip="true"
                  prop="station_code"
                  min-width="100"
                  label="工位编码"
                />
                <el-table-column 
                  :show-overflow-tooltip="true"
                  prop="station_des"
                  min-width="100"
                  label="工位描述"
                />
              </el-table>
              <el-input
                slot="reference"
                v-model="form.station_code"
                readonly="readonly"
                style="width: 90%"
                :disabled="true"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleStationQuery" />
              </el-input>
            </el-popover>
          </el-form-item>
          <el-form-item label="工位名称" prop="station_des">
            <el-input v-model="form.station_des" style="width: 90%" :disabled="true" />
          </el-form-item>
          <el-form-item label="排序" prop="station_andon_order">
            <el-input
              v-model.number="form.station_andon_order"
              style="width: 90%"
            />
          </el-form-item>
          <el-form-item label="有效标识">
            <el-radio-group
              v-model="form.enable_flag"
              :disabled="false"
              style="width: 90%"
            >
              <el-radio label="Y">有效</el-radio>
              <el-radio label="N">失效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button
            size="small"
            icon="el-icon-close"
            plain
            @click="handleFromCancel"
          >取消</el-button>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="handleFormSubmit('form')"
          >确认</el-button>
        </div>
        <!--</el-dialog>-->
      </el-drawer>
    </el-card>
    <MesAndonStationTypeI ref="MesAndonStationTypeI" />
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import {
  queryAllMesAndonStationType,
  insMesAndonStationType,
  updMesAndonStationType,
  delMesAndonStationType
} from '@/api/mes/core/andonStationType'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import MesAndonStationTypeI from '@/views/mes/core/stationAndon/andonStationTypeI'
import { sel as selStation } from '@/api/core/factory/sysStation'

export default {
  name: 'MES_ANDON_STATION_TYPE',
  components: { MesAndonStationTypeI },
  cruds() {
    return CRUD({ title: 'Tag', queryOnPresenterCreated: false })
  },
  mixins: [presenter(), header(), form(null), crud()],

  // Table 内按钮状态(组/明细)
  props: {},

  // 数据模型
  data() {
    var checkStationAndonOrder = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('请输入排序'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }

    return {
      height: document.documentElement.clientHeight - 180 + 'px;',
      andonTypeId: 0,
      tableLoading: false,
      tableData: [],
      selectedTable: [], // 已选择项
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      query: {
        station: '',
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'andon_station_type_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      dialogVisbleSync: false,
      dialogTitle: '安灯大类型工位维护',
      form: {
        andon_station_type_id: '',
        andon_type_id: '',
        station_id: '',
        station_code: '',
        station_des: '',
        station_andon_order: '',
        enable_flag: ''
      },
      rules: {
        // 提交验证规则
        station_code: [
          { required: true, message: '请选择工位', trigger: 'blur' }
        ],
        station_andon_order: [
          {
            required: true,
            validator: checkStationAndonOrder,
            trigger: 'blur'
          }
        ]
      },
      // 选择工位
      customPopover: false,
      stationTableQuery: {
        station_code_des: ''
      },
      stationData: [],
      stationTableLoading: false
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  created: function() {
    // 查询
    this.handleQuery()
  },

  methods: {
    handleAdd() {
      this.form.andon_station_type_id = ''
      this.form.andon_type_id = this.andonTypeId
      this.form.station_id = ''
      this.form.station_andon_order = ''
      this.form.enable_flag = 'Y'
      this.dialogTitle = '新增安灯大类型工位'
      this.dialogVisbleSync = true
    },
    parentQuery(andonTypeId) {
      this.andonTypeId = andonTypeId
      this.handleQuery()
    },
    handleQuery() {
      const query = {
        andon_type_id: this.andonTypeId,
        station: this.query.station,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }

      // this.tableLoading = true;
      queryAllMesAndonStationType(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableData = defaultQuery.data
            } else {
              this.tableData = []
            }
            this.pageTable.total = defaultQuery.count
            this.tableLoading = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })

      if (this.$refs.MesAndonStationTypeI !== undefined) {
        this.$refs.MesAndonStationTypeI.parentQuery(0, 0)
      }
    },
    handleTableEdit(data) {
      this.form.andon_station_type_id = data.andon_station_type_id
      this.form.andon_type_id = data.andon_type_id
      this.form.station_id = data.station_id
      this.form.station_andon_order = data.station_andon_order
      this.form.enable_flag = data.enable_flag
      this.form.station_code = data.station_code
      this.form.station_des = data.station_des
      this.dialogTitle = '编辑安灯大类型工位'
      this.dialogVisbleSync = true
    },
    handleTableDel(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除${data.station_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            id: data.andon_station_type_id
          }

          delMesAndonStationType(del)
            .then((res) => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                // 查询
                this.handleQuery()
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: 'error'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    handleFormSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            andon_station_type_id: this.form.andon_station_type_id,
            andon_type_id: this.form.andon_type_id,
            station_id: this.form.station_id,
            station_andon_order: this.form.station_andon_order,
            enable_flag: this.form.enable_flag
          }
          // 新增
          if (
            this.form.andon_station_type_id === undefined ||
            this.form.andon_station_type_id.length <= 0
          ) {
            insMesAndonStationType(save)
              .then((res) => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  this.dialogVisbleSync = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })

                  // 查询
                  this.handleQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updMesAndonStationType(save)
              .then((res) => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  this.dialogVisbleSync = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })

                  // 查询
                  this.handleQuery()
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    handleFromCancel() {
      // 取消
      this.dialogVisbleSync = false // 弹出框隐藏
    },
    drawerClosed() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val

      // 查询
      this.handleQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val

      // 查询
      this.handleQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'andon_station_type_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.handleQuery()
    },
    handleRowClick(row, column, event) {
      if (column.property !== 'tableButton') {
        this.$refs.MesAndonStationTypeI.parentQuery(row.andon_station_type_id)
      }
    },
    // 工位选择
    handleStationQuery() {
      this.stationTableLoading = true
      const query = {
        enable_flag: 'Y',
        prod_line_id: this.prodLineId,
        stationCodeDes: this.stationTableQuery.station_code_des
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0 && defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
          this.stationTableLoading = false
        })
        .catch(() => {
          this.stationTableLoading = false
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleChooseStation(data) {
      this.form.station_id = data.station_id
      this.form.station_code = data.station_code
      this.form.station_des = data.station_des
      this.customPopover = false
    }
  }
}
</script>
