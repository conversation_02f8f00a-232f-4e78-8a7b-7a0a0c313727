<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!-- 料号 -->
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <!-- 版本 -->
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.version') + ':'">
                <el-input v-model="query.model_version" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title "
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="265px"
              :inline="true"
            >
              <!-- 料号 -->
              <el-form-item :label="$t('lang_pack.vie.partNum')" prop="model_type">
                <el-input v-model="form.model_type" clearable size="small" />
              </el-form-item>
              <!-- 版本 -->
              <el-form-item :label="$t('lang_pack.vie.version')" prop="model_version">
                <el-input v-model="form.model_version" clearable size="small" />
              </el-form-item>
              <!-- 重 -->
              <el-form-item label="板重(g)" prop="pcs_weight">
                <el-input v-model="form.pcs_weight" type="number" clearable size="small" @input="handleInput('pcs_weight')" />
              </el-form-item>
              <!-- Overlay高度 -->
              <el-form-item :label="'Overlay高度(um)'" prop="overlay_height">
                <el-input v-model="form.overlay_height" type="number" clearable size="small" @input="handleInput('overlay_height')" />
              </el-form-item>
              <!-- Overlay重量 -->
              <el-form-item :label="'Overlay重量(g)'" prop="overlay_weight">
                <el-input v-model="form.overlay_weight" type="number" clearable size="small" @input="handleInput('overlay_weight')" />
              </el-form-item>
              <!-- 重量偏差 -->
              <el-form-item :label="$t('view.field.recipe.weight_error')" prop="weight_error">
                <el-input v-model="form.weight_error" clearable size="small" />
              </el-form-item>
              <!-- 指定束带方式(1、2、3) -->
              <el-form-item :label="'束带方式'" prop="bd_way">
                <el-input v-model="form.bd_way" clearable size="small" @input="handleInput('bd_way')" />
              </el-form-item>
              <!-- 喷墨模板 -->
              <el-form-item :label="$t('view.field.recipe.inkjet_tpl')" prop="inkjet_tpl">
                <el-input v-model="form.inkjet_tpl" clearable size="small" />
              </el-form-item>
              <!-- 截取规则设定 -->
              <!-- <el-form-item :label="$t('lang_pack.vie.SortingComparisonRuleSetting') + '：'" prop="sorting_comparison_rule_setting">
                <el-input v-model="form.sorting_comparison_rule_setting" readonly="readonly">
                  <div slot="append">
                    <el-button slot="reference" @click="handleSelectTrayRecipe">{{ $t('lang_pack.vie.select') }}</el-button>
                  </div>
                </el-input>
              </el-form-item> -->
              <!-- 有效标识 -->
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <!-- 注释内容 -->
            <p>
              <span style="color: red">注：Overlay高度是指最上方空的tray盘+2片瓦楞板的高度</span><br>
              <span style="color: red">注：Overlay重量是指最上方空的tray盘+2片瓦楞板+束带+干燥剂+包装袋+湿度卡+2张隔纸的重量</span><br>
              <span style="color: red">注：诺最大包装数为9+2其Overlay重量为2盘空tray; 诺最大包装数为10+1其Overlay重量为1盘空tray </span><br>
              <span style="color: red">注：喷墨模板的样式可在喷墨机上做修改 </span><br>
              <span style="color: red">注：束带方式（1代表3+1；2代表2+1）</span><br>
              <span style="color: red">注：重量誤差係指最終秤重實際值與配方內各項數值總和之誤差</span><br>
              <span style="color: red">注：噴碼模板 設1表（原廠設定）、設2表（進行噴墨）、設3表（不進行噴墨）</span><br>
            </p>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item :label="$t('lang_pack.vie.partNum')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.version')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_version }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateLen')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_length }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateWid')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_width }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateThi')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_thickness }}</el-descriptions-item>
                  <el-descriptions-item label="板重(g)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="'Tray高度(um)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tray_height }}</el-descriptions-item>
                  <el-descriptions-item :label="'Tray重量(g)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tray_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="'Overlay高度(um)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.overlay_height }}</el-descriptions-item>
                  <el-descriptions-item :label="'Overlay重量(g)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.overlay_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.field.recipe.weight_error')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.weight_error }}</el-descriptions-item>
                  <el-descriptions-item :label="'束带方式'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bd_way }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.field.recipe.inkjet_tpl')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.inkjet_tpl }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.commonPage.validIdentificationt')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="130"
              align="center"
            />
            <!-- 版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_version"
              :label="$t('lang_pack.vie.version')"
              width="120"
              align="center"
            />
            <!-- 板重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pcs_weight"
              label="板重(g)"
              width="130"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.pcs_weight / 1000 }}
            </template>
            </el-table-column>
            <!-- Overlay高度 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="overlay_height"
              label="Overlay高度(um)"
              width="130"
              align="center"
            />
            <!-- Overlay重量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="overlay_weight"
              label="Overlay重量(g)"
              width="130"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.overlay_weight / 1000 }}
            </template>
            </el-table-column>
            <!-- 重量偏差 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="weight_error"
              :label="$t('view.field.recipe.weight_error')"
              width="130"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.weight_error / 1000 }}
            </template>
            </el-table-column>
            <!-- 束带方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_way"
              label="束带方式"
              width="130"
              align="center"
            />
            <!-- 喷墨模板 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="inkjet_tpl"
              :label="$t('view.field.recipe.inkjet_tpl')"
              width="130"
              align="center"
            />
            <!-- 分选对比规则设定 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="sorting_comparison_rule_setting"
              :label="$t('lang_pack.vie.SortingComparisonRuleSetting')"
              width="130"
              align="center"
            /> -->
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <trayRecipe v-if="trayRecipeFlag" ref="trayRecipe" @ok="callbackTrayRecipe" />
  </div>
</template>

<script>
import api from '@/api/pack/project/kinsus/meRecipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import trayRecipe from './tray-recipe.vue'
const defaultForm = {
  created_by: '',
  creation_date: '',
  last_updated_by: '',
  last_update_date: '',
  recipe_id: '',
  model_type: '',
  model_version: '',
  pcs_length: '',
  pcs_width: '',
  pcs_thickness: '',
  pcs_weight: '',
  bd_index_incre: '',
  enable_flag: 'Y',
  overlay_height: '',
  overlay_weight: '',
  weight_error: '',
  bd_way1: '',
  bd_way3: '',
  inkjet_tpl: ''
}
export default {
  name: 'CARTASK',
  components: { crudOperation, rrOperation, udOperation, pagination, trayRecipe },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.formulaMainten'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG', 'QR_TYPE', 'QR_CASE', 'QR_DIRECT'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        model_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        model_version: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // pcs_length: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // pcs_width: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // pcs_thickness: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        pcs_weight: [
          { required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('必须大于0'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        tray_height: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        tray_weight: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // tray_volume: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        overlay_height: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        overlay_weight: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        weight_error: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // bd_way1: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // bd_way3: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        bd_way: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        inkjet_tpl: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      trayRecipeFlag: false
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    handleInput(value) {
      const reg = /^\d+(\.\d{0,2})?$/ // 验证数字及一位小数的正则表达式
      if (!reg.test(this.form[value])) {
        // 输入不符合要求，清空输入框
        this.form[value] = ''
      }
    },
    BlurText(e) {
      const boolean = new RegExp('^[0-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger'))
        e.target.value = ''
      }
    },
    handleSelectTrayRecipe() {
      this.trayRecipeFlag = true
      this.$nextTick(() => {
        this.$refs.trayRecipe.dialogVisible = true
      })
    },
    callbackTrayRecipe(row) {
      console.log(row)
      this.form.sorting_comparison_rule_setting = row.name
      // this.form.model_type = row.model_type
      // this.form.m_length = row.m_length
      // this.form.m_width = row.m_width
      // this.form.m_tickness = row.m_tickness
      // this.form.m_weight = row.m_weight
      // this.form.array_type = row.array_type
      // this.form.bd_type = row.bd_type
      this.trayRecipeFlag = false
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.pcs_weight = crud.form.pcs_weight ? (crud.form.pcs_weight / 1000) : ''
      crud.form.overlay_weight = crud.form.overlay_weight ? (crud.form.overlay_weight / 1000) : ''
      crud.form.weight_error = crud.form.weight_error ? (crud.form.weight_error / 1000) : ''
      // crud.form.client_id_list = crud.form.client_id_list === '' ? '' : (crud.form.client_id_list.split(',')).toString()
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      delete crud.form.item_date
      crud.form.pcs_weight = crud.form.pcs_weight * 1000
      crud.form.weight_error = crud.form.weight_error * 1000
      crud.form.overlay_weight = crud.form.overlay_weight * 1000
      crud.form.overlay_height = parseFloat(crud.form.overlay_height)
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              user_name: Cookies.get('userName'),
              recipe_id: data.recipe_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}

</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
