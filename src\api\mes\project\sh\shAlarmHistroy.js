import request from '@/utils/request'
// 查询工位报警信息信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShAlarmHistorySelect',
    method: 'post',
    data
  })
}
// 新增工位报警信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShAlarmHistoryInsert',
    method: 'post',
    data
  })
}
// 编辑工位报警信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShAlarmHistoryUpdate',
    method: 'post',
    data
  })
}
export default { sel, add, edit }
