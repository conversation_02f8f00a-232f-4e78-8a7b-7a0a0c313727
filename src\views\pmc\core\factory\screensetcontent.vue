<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workCenterCode')">
                <!-- 车间： -->
                <el-select v-model="query.work_center_code" clearable filterable @change="changeWorkCenterCode">
                  <el-option v-for="item in dict.WORK_CENTER_CODE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.prodLineCode')">
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_code">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.enableFlag')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
              <el-form-item :label="$t('lang_pack.stationdevice.workCenterCode')" prop="work_center_code">
                <!-- 车间 -->
                <el-select v-model="form.work_center_code" clearable filterable @change="changeWorkCenterCode">
                  <el-option v-for="item in dict.WORK_CENTER_CODE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <!--表：sys_fmod_prod_line-->
              <el-form-item :label="$t('lang_pack.screensetcontent.prodLineCode')" prop="prod_line_code">
                <!-- 产线 -->
                <el-select v-model="form.prod_line_code" size="small" placeholder="请选择产线">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_code" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.screensetcontent.todayPlan')" prop="today_plan">
                <!-- 今日计划 -->
                <el-input v-model="form.today_plan" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.currentOffline')" prop="current_offline">
                <!-- 当前下线 -->
                <el-input v-model="form.current_offline" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.finishRate')" prop="finish_rate">
                <!-- 完成率 -->
                <el-input v-model="form.finish_rate" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.jphActual')" prop="jph_actual">
                <!-- JPH实绩 -->
                <el-input v-model="form.jph_actual" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.deviceMobility')" prop="device_mobility">
                <!-- 设备可动率 -->
                <el-input v-model="form.device_mobility" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.todayStop')" prop="today_stop">
                <!-- 今日停线 -->
                <el-input v-model="form.today_stop" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.aboveShow')" prop="above_show">
                <!-- 上面显示 -->
                <el-input v-model="form.above_show" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.belowShow')" prop="below_show">
                <!-- 下面显示 -->
                <el-input v-model="form.below_show" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.screensetcontent.backgroundImage')" prop="background_image">
                <!-- 背景图片 -->
                <el-input v-model="form.background_image" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.screensetcontent.enableFlag')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" :label="$t('lang_pack.modownstatus.creationDate')" />
            <!-- 创建时间 -->

            <el-table-column  :label="$t('lang_pack.screensetcontent.prodLineCode')" align="center" prop="prod_line_code" width="100">
              <!-- 生产线 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getProdLineDes(scope.row.prod_line_code) }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="today_plan" :label="$t('lang_pack.screensetcontent.todayPlan')" />
            <!-- 今日计划 -->
            <el-table-column  :show-overflow-tooltip="true" prop="current_offline" :label="$t('lang_pack.screensetcontent.currentOffline')" />
            <!-- 当前下线 -->
            <el-table-column  :show-overflow-tooltip="true" prop="finish_rate" :label="$t('lang_pack.screensetcontent.finishRate')" />
            <!-- 完成率 -->
            <el-table-column  :show-overflow-tooltip="true" prop="jph_actual" :label="$t('lang_pack.screensetcontent.jphActual')" />
            <!-- JPH实绩 -->
            <el-table-column  :show-overflow-tooltip="true" prop="device_mobility" :label="$t('lang_pack.screensetcontent.deviceMobility')" />
            <!-- 设备可动率 -->
            <el-table-column  :show-overflow-tooltip="true" prop="today_stop" :label="$t('lang_pack.screensetcontent.todayStop')" />
            <!-- 今日停线 -->
            <el-table-column  :show-overflow-tooltip="true" prop="above_show" :label="$t('lang_pack.screensetcontent.aboveShow')" />
            <!-- 上面显示 -->
            <el-table-column  :show-overflow-tooltip="true" prop="below_show" :label="$t('lang_pack.screensetcontent.belowShow')" />
            <!-- 下面显示 -->
            <el-table-column  :show-overflow-tooltip="true" prop="background_image" :label="$t('lang_pack.screensetcontent.backgroundImage')" />
            <!-- 背景图片 -->

            <el-table-column  :label="$t('lang_pack.screensetcontent.enableFlag')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import crudScreenSetContent from '@/api/pmc/factory/screensetcontent'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  prod_line_code: '',
  today_plan: '',
  current_offline: '',
  finish_rate: '',
  jph_actual: '',
  device_mobility: '',
  today_stop: '',
  above_show: '',
  below_show: '',
  background_image: '',
  enable_flag: 'Y'
}
export default {
  name: 'SCREENSETCONTENT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '大屏内容',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'prod_line_code',
      // 排序
      sort: ['prod_line_code asc'],
      // CRUD Method
      crudMethod: { ...crudScreenSetContent },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'fmod_screen_set_content:add'],
        edit: ['admin', 'fmod_screen_set_content:edit'],
        del: ['admin', 'fmod_screen_set_content:del']
      },
      rules: {
        prod_line_code: [{ required: true, message: '请选择产线', trigger: 'blur' }]
      },
      // 车间数据
      currentWorkCenterCode: '', // 当前车间(单笔)
      // 产线数据
      currentProdLineCode: '', // 当前产线(单笔)
      prodLineData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WORK_CENTER_CODE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 获取产线的中文描述
    getProdLineDes(prod_line_code) {
      var item = this.prodLineData.find(item => item.prod_line_code === prod_line_code)
      if (item !== undefined) {
        return item.prod_line_des
      }
      return prod_line_code
    },

    // 更改车间
    changeWorkCenterCode(val) {
      this.currentWorkCenterCode = val // 当前车间
      // 加载 产线LOV
      this.queryProdLine()
    },
    // 产线LOV
    queryProdLine() {
      const query = {
        work_center_code: this.currentWorkCenterCode,
        userID: Cookies.get('userName')
      }
      lovProdLine(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.prodLineData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
