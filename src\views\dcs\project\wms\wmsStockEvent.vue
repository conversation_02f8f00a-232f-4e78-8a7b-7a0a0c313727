<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务类型:">
                <!-- 任务类型 -->
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option v-for="item in dict.TASK_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号:">
                <!-- 任务号 -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号:">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料号:">
                <!-- 物料号 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="事件状态:">
                <!-- 事件状态 -->
                <el-select v-model="query.event_status" clearable filterable>
                  <el-option v-for="item in dict.EXECUTE_STATUS" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.sortingResults.stationCode')" prop="station_code">
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partBarcodeNumber')" prop="part_barcode">
                <!-- 零件条码 -->
                <el-input v-model="form.part_barcode" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partCode')" prop="part_code">
                <!-- 零件编码 -->
                <el-input v-model="form.part_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.PartType')" prop="part_type">
                <!-- 零件类型 -->
                <el-input v-model="form.part_type" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <!-- 库位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_code"
              label="库位号"
              min-width="80"
              align="center"
            />
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              label="任务类型"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              label="任务号"
              min-width="100"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              label="批次号"
              min-width="100"
              align="center"
            />
            <!-- 物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              label="物料号"
              min-width="130"
              align="center"
            />
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              label="长"
              min-width="130"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              label="宽"
              min-width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              label="厚"
              min-width="100"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              label="重"
              min-width="100"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              label="材质"
              min-width="130"
              align="center"
            />
            <!-- 起始库位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_stock_code"
              label="起始库位"
              min-width="100"
              align="center"
            />
            <!-- 起始库位坐标X -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_location_x"
              label="起始库位坐标X"
              min-width="130"
              align="center"
            />
            <!-- 起始库位坐标Y -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_location_y"
              label="起始库位坐标Y"
              min-width="130"
              align="center"
            />
            <!-- 起始库位坐标Z -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_location_z"
              label="起始库位坐标Z"
              min-width="130"
              align="center"
            />
            <!-- 目标库位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_stock_code"
              label="目标库位"
              min-width="130"
              align="center"
            />
            <!-- 目标库位坐标X -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_location_x"
              label="目标库位坐标X"
              min-width="130"
              align="center"
            />
            <!-- 目标库位坐标Y -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_location_y"
              label="目标库位坐标Y"
              min-width="130"
              align="center"
            />
            <!-- 目标库位坐标Z -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_location_z"
              label="目标库位坐标Z"
              min-width="130"
              align="center"
            />
            <!-- 事件状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="event_status"
              label="事件状态"
              min-width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.EXECUTE_STATUS[scope.row.execute_status] }}
              </template>
            </el-table-column>
            <!-- 是否上传 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="up_flag"
              label="是否上传"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.up_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMeStockEvent from '@/api/dcs/core/wms/meStockEvent'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  stock_event_id: '',
  stock_id: '',
  car_code: '',
  stock_code: '',
  stock_way: '',
  task_way: '',
  task_type: '',
  task_from: '',
  task_num: '',
  serial_num: '',
  lot_num: '',
  model_type: '',
  material_code: '',
  material_des: '',
  material_draw: '',
  m_length: '',
  m_width: '',
  m_height: '',
  m_weight: '',
  m_texture: '',
  from_stock_code: '',
  from_location_x: '',
  from_location_y: '',
  from_location_z: '',
  to_stock_code: '',
  to_location_x: '',
  to_location_y: '',
  to_location_z: '',
  event_status: '',
  up_flag: 'Y',
  up_code: '',
  up_msg: ''
}
export default {
  name: 'MESTOCKEVENT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: 'WMS库存事件',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'transfer_task_id1',
      // 排序
      sort: ['transfer_task_id asc'],
      // CRUD Method
      crudMethod: { ...crudMeStockEvent },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 315,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
      },
      modelList: []
    }
  },
  // 数据字典
  dicts: ['STOCK_WAY', 'TASK_WAY', 'TASK_TYPE', 'EXECUTE_STATUS', 'DATA_SOURCES'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 315
    }
  },
  created: function() {
    this.getModelType()
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudSortResult
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              mo_id: data.mo_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    getModelType() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
