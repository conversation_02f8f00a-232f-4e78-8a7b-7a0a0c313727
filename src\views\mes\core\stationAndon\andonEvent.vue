<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="生产线：" label-width="140px">
                <el-select v-model="query.prod_line_code" filterable clearable size="small">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位编码/描述：" label-width="140px">
                <el-input v-model="query.station_code_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="类型编码/描述：" label-width="140px">
                <el-input v-model="query.andon_type_code_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="明细编码/描述：" label-width="140px">
                <el-input v-model="query.andon_station_type_i_code_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="安灯明细类型：" label-width="140px">
                <el-input v-model="query.andon_i_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码/描述：" label-width="140px">
                <el-input v-model="query.material_code_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：" label-width="140px">
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column 
              :show-overflow-tooltip="true"
              prop="prod_line_code"
              width="150"
              label="生产线编码"
            >
              <template
                slot-scope="scope"
              ><!--取到当前单元格-->
                {{ handleValueToLabel(scope.row.prod_line_code) }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="station_code" width="100" label="工位号" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_des" width="100" label="工位描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="andon_type_code" width="150" label="安灯大类型编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="andon_type_des" width="150" label="安灯大类型描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="andon_station_type_i_code" width="100" label="安灯明细编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="andon_station_type_i_des" width="100" label="安灯明细描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="andon_i_type" width="100" label="安灯明细类型" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_code" width="100" label="物料编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_des" width="100" label="物料描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="happen_date" width="150" label="发生时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="respon_date" width="150" label="响应时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="reset_date" width="150" label="复位时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="happen_user" width="100" label="发生人" />
            <el-table-column  :show-overflow-tooltip="true" prop="respon_user" width="100" label="响应人" />
            <el-table-column  :show-overflow-tooltip="true" prop="reset_user" width="100" label="复位人" />
            <el-table-column  :show-overflow-tooltip="true" prop="cost_times" width="100" label="消耗时间(秒)" />
            <el-table-column  :show-overflow-tooltip="true" prop="andon_event_remarks" width="100" label="事件说明" />
            <el-table-column 
              :show-overflow-tooltip="true"
              prop="finish_flag"
              width="100"
              label="处理完成"
              sortable="custom"
            >
              <template
                slot-scope="scope"
              ><!--取到当前单元格-->
                {{ scope.row.finish_flag === "Y" ? "已完成" : "未完成" }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="created_by" width="100" label="创建人" />
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column 
              :show-overflow-tooltip="true"
              prop="enable_flag"
              width="100"
              label="有效标识"
            >
              <template
                slot-scope="scope"
              ><!--取到当前单元格-->
                {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudProdLine from '@/api/core/factory/sysProdLine'
import crudAndonEvent from '@/api/mes/core/andonEvent'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  prod_line_code: '',
  station_code_des: '',
  andon_type_code_des: '',
  andon_station_type_i_code_des: '',
  andon_i_type: '',
  material_code_des: '',
  enable_flag: ''
}
export default {
  name: 'MES_ANDON_EVENT',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '安灯事件信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'andon_event_id',
      // 排序
      sort: ['andon_event_id desc'],
      // CRUD Method
      crudMethod: { ...crudAndonEvent },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_andon_event:add'],
        edit: ['admin', 'mes_andon_event:edit'],
        del: ['admin', 'mes_andon_event:del'],
        down: ['admin', 'mes_andon_event:down']
      },
      prodLineData: []
    }
  },
  watch: {
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    crudProdLine
      .sel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y'
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    handleValueToLabel(prod_line_code) {
      var item = this.prodLineData.filter(
        (item) => item.prod_line_code === prod_line_code
      )
      if (item.length > 0) {
        return item[0].prod_line_code + ' ' + item[0].prod_line_des
      }
      return ''
    }
  }
}
</script>

<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
.ruleBottom {
  text-align: center;
  margin: 40px 0;
}
</style>
