<template>
  <div
    id="chartSop"
    tabindex="0"
    :style="{
      width: isNaN(width) ? width : width + 'px',
      height: isNaN(height) ? height : height + 'px',
      cursor: cursor
    }"
    @mousemove="handleChartMouseMove"
    @mouseup="handleChartMouseUp"
    @dblclick="handleChartDblClick($event)"
    @mousewheel="handleChartMouseWheel"
    @mousedown="handleChartMouseDown($event)"
  >
    <svg id="svg">
      <rect class="selection" height="0" width="0" />
    </svg>
  </div>
</template>
<style src="./index.css" scoped></style>
<script>
import { line2, lineTo } from '@/utils/svg'
import * as d3 from 'd3'
import { between, distanceOfPointToLine, getEdgeOfPoints, pointRectangleIntersection } from '@/utils/math'
import render from '@/components/MainChart/render'

export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    nodes: {
      type: Array,
      default: () => [{ id: 1, x: 140, y: 270, name: 'Start', type: 'start' }, { id: 2, x: 540, y: 270, name: 'End', type: 'end' }]
    },
    connections: {
      type: Array,
      default: () => [
        {
          source: { id: 1, position: 'right' },
          destination: { id: 2, position: 'left' },
          id: 1,
          type: 'pass'
        }
      ]
    },
    width: {
      type: [String, Number],
      default: 800
    },
    height: {
      type: [String, Number],
      default: 600
    },
    readonly: {
      type: Boolean,
      default: false
    },
    render: {
      type: Function,
      default: render
    }
  },
  data() {
    return {
      internalNodes: [],
      internalConnections: [],
      connectingInfo: {
        source: null,
        sourcePosition: null
      },
      selectionInfo: null,
      currentNodes: [],
      currentConnections: [],
      /**
       * Mouse position(relative to chart div)
       */
      cursorToChartOffset: { x: 0, y: 0 },
      clickedOnce: false,
      pathClickedOnce: false,
      dragEnd: false,
      /**
       * lines of all internalConnections
       */
      lines: [],
      zoom: 1,
      fastLinkNodes: []
    }
  },
  computed: {
    hoveredConnector() {
      for (const node of this.internalNodes) {
        const connectorPosition = this.getConnectorPosition(node)
        for (const prop in connectorPosition) {
          const entry = connectorPosition[prop]
          if (Math.hypot(entry.x - this.cursorToChartOffset.x / this.zoom, entry.y - this.cursorToChartOffset.y / this.zoom) < 10) {
            return { position: prop, node: node }
          }
        }
      }
      return null
    },
    hoveredConnection() {
      for (const line of this.lines) {
        const distance = distanceOfPointToLine(line.sourceX, line.sourceY, line.destinationX, line.destinationY, this.cursorToChartOffset.x, this.cursorToChartOffset.y)
        if (distance < 5 && between(line.sourceX - 2, line.destinationX + 2, this.cursorToChartOffset.x) && between(line.sourceY - 2, line.destinationY + 2, this.cursorToChartOffset.y)) {
          const connections = this.internalConnections.filter(item => item.id === line.id)
          return connections.length > 0 ? connections[0] : null
        }
      }
      return null
    },
    cursor() {
      // if (this.connectingInfo.source || this.hoveredConnector) {
      //   return "crosshair";
      // }
      if (this.hoveredConnection != null) {
        return 'pointer'
      }
      return null
    }
  },
  watch: {
    internalNodes: {
      immediate: true,
      deep: true,
      handler() {
        this.renderNodes()
        this.renderConnections()
      }
    },
    internalConnections: {
      immediate: true,
      deep: true,
      handler() {
        this.renderConnections()
      }
    },
    selectionInfo: {
      immediate: true,
      deep: true,
      handler() {
        this.renderSelection()
      }
    },
    currentNodes: {
      immediate: true,
      deep: true,
      handler() {
        this.renderNodes()
      }
    },
    currentConnections: {
      immediate: true,
      deep: true,
      handler() {
        this.renderConnections()
      }
    },
    cursorToChartOffset: {
      immediate: true,
      deep: true,
      handler() {
        if (this.selectionInfo) {
          this.renderSelection()
        }
      }
    },
    connectingInfo: {
      immediate: true,
      deep: true,
      handler() {
        this.renderConnections()
      }
    },
    nodes: {
      immediate: true,
      deep: true,
      handler() {
        this.init()
      }
    },
    connections: {
      immediate: true,
      deep: true,
      handler() {
        this.init()
      }
    }
  },
  mounted() {
    const that = this
    that.init()
    document.onkeydown = function(event) {
      console.log(event.keyCode)
      switch (event.keyCode) {
        case 37:
          that.moveCurrentNode(-10, 0)
          break
        case 38:
          that.moveCurrentNode(0, -10)
          break
        case 39:
          that.moveCurrentNode(10, 0)
          break
        case 40:
          that.moveCurrentNode(0, 10)
          break
        case 27:
          that.currentNodes.splice(0, that.currentNodes.length)
          that.currentConnections.splice(0, that.currentConnections.length)
          break
        case 65:
          if (document.activeElement === document.getElementById('chartSop')) {
            that.currentNodes.splice(0, that.currentNodes.length)
            that.currentConnections.splice(0, that.currentConnections.length)
            that.currentNodes.push(...that.internalNodes)
            that.currentConnections.push(...that.internalConnections)
            event.preventDefault()
          }
          break
        case 46:
          that.remove()
          break
        default:
          break
      }
    }
  },
  created() {},
  methods: {
    add(node) {
      if (this.readonly) {
        return
      }
      node.x = node.x / this.zoom
      node.y = node.y / this.zoom
      this.internalNodes.push(node)
      console.log(this.internalNodes)
    },
    editCurrent() {
      if (this.currentNodes.length === 1) {
        this.editNode(this.currentNodes[0])
      } else if (this.currentConnections.length === 1) {
        this.editConnection(this.currentConnections[0])
      }
    },
    editNode(node) {
      // if (this.readonly) {
      //   return;
      // }
      this.$emit('editnode', this.internalNodes.filter(item => item.point_id === node.point_id)[0], this)
    },
    editConnection(connection) {
      if (this.readonly) {
        return
      }
      this.$emit('editconnection', connection)
    },
    handleZoomIn() {
      const svg = document.getElementById('svg')
      const zoom = parseFloat(svg.style.zoom || 1)
      this.zoom = zoom + 0.1
      svg.style.zoom = this.zoom
    },
    handleZoomOut() {
      const svg = document.getElementById('svg')
      const zoom = parseFloat(svg.style.zoom || 1)
      if (zoom === 0.1) return
      this.zoom = zoom - 0.1
      svg.style.zoom = this.zoom
    },
    handleChartMouseWheel(event) {
      event.stopPropagation()
      event.preventDefault()
      if (event.ctrlKey) {
        const svg = document.getElementById('svg')
        let zoom = parseFloat(svg.style.zoom || 1)
        if (event.deltaY > 0 && zoom === 0.1) {
          return
        }
        zoom -= event.deltaY / 100 / 10
        console.log(zoom)
        this.zoom = zoom
        svg.style.zoom = zoom
      }
    },
    async handleChartMouseUp() {
      if (this.connectingInfo.source) {
        if (this.hoveredConnector) {
          if (this.connectingInfo.source.id !== this.hoveredConnector.node.id) {
            // Node can't connect to itself
            const tempId = +new Date()
            const conn = {
              source: {
                id: this.connectingInfo.source.id,
                position: this.connectingInfo.sourcePosition
              },
              destination: {
                id: this.hoveredConnector.node.id,
                position: this.hoveredConnector.position
              },
              id: tempId,
              type: 'pass',
              name: 'Pass',
              color: ''
            }
            this.internalConnections.push(conn)
          }
        }
        this.connectingInfo.source = null
        this.connectingInfo.sourcePosition = null
      }
      if (this.selectionInfo) {
        this.selectionInfo = null
      }
    },
    async handleChartMouseMove(event) {
      // calc offset of cursor to chart
      const boundingClientRect = event.currentTarget.getBoundingClientRect()
      const actualX = event.pageX - boundingClientRect.left - window.scrollX
      this.cursorToChartOffset.x = Math.trunc(actualX)
      const actualY = event.pageY - boundingClientRect.top - window.scrollY
      this.cursorToChartOffset.y = Math.trunc(actualY)

      if (this.connectingInfo.source) {
        await this.renderConnections()

        d3.selectAll('#svg .connector').classed('active', true)
        const sourceOffset = this.getNodeConnectorOffset(this.connectingInfo.source.id, this.connectingInfo.sourcePosition)
        const destinationPosition = this.hoveredConnector ? this.hoveredConnector.position : null

        this.arrowTo(sourceOffset.x, sourceOffset.y, this.cursorToChartOffset.x / this.zoom, this.cursorToChartOffset.y / this.zoom, this.connectingInfo.sourcePosition, destinationPosition)
      }
    },
    handleChartDblClick(event) {
      if (this.readonly) {
        return
      }
      this.$emit('dblclick', { x: event.offsetX, y: event.offsetY })
    },
    handleChartMouseDown(event) {
      if (event.ctrlKey || event.toElement.id === 'zoomIn' || event.toElement.id === 'zoomOut') {
        return
      }
      this.selectionInfo = { x: event.offsetX, y: event.offsetY }
    },
    getConnectorPosition(node) {
      if (node === undefined) return
      const pianyi = 10 // 这里可以设置节点四周的小圆点位置偏移
      if (node.type === 'judge') {
        const leftX = node.x - 5
        const leftY = node.y
        const topX = node.x + 75
        const topY = node.y - 55
        const rightX = node.x + 155
        const rightY = node.y
        const bottomX = node.x + 75
        const bottomY = node.y + 55
        const top = { x: topX, y: topY }
        const left = { x: leftX, y: leftY }
        const bottom = {
          x: bottomX,
          y: bottomY
        }
        const right = {
          x: rightX,
          y: rightY
        }
        return { left, right, top, bottom }
      } else {
        const halfWidth = node.width / 2
        const halfHeight = node.height / 2
        const top = { x: node.x + halfWidth, y: node.y + pianyi - 5 }
        const left = { x: node.x - 5, y: node.y + halfHeight + pianyi + 5 }
        let bottom = {
          x: node.x + halfWidth,
          y: node.y + node.height + pianyi + 10
        }
        if (node.type === 'step') {
          bottom = {
            x: node.x + halfWidth,
            y: node.y + node.height + pianyi + 18
          }
        }
        const right = {
          x: node.x + node.width + 5,
          y: node.y + halfHeight + pianyi + 5
        }
        return { left, right, top, bottom }
      }
    },
    // 画布选择矩形
    renderSelection() {
      const that = this
      // render selection rectangle
      if (that.selectionInfo) {
        const edge = getEdgeOfPoints([
          {
            x: that.selectionInfo.x / this.zoom,
            y: that.selectionInfo.y / this.zoom
          },
          {
            x: that.cursorToChartOffset.x / this.zoom,
            y: that.cursorToChartOffset.y / this.zoom
          }
        ])
        that.currentNodes.splice(0, that.currentNodes.length)
        that.currentConnections.splice(0, that.currentConnections.length)
        const svg = d3.select('#svg')
        const rect = svg.select('.selection').classed('active', true)

        rect
          .attr('x', edge.start.x)
          .attr('y', edge.start.y)
          .attr('width', edge.end.x - edge.start.x)
          .attr('height', edge.end.y - edge.start.y)

        that.internalNodes.forEach(item => {
          const points = [{ x: item.x, y: item.y }, { x: item.x, y: item.y + item.height }, { x: item.x + item.width, y: item.y }, { x: item.x + item.width, y: item.y + item.height }]
          if (points.every(point => pointRectangleIntersection(point, edge))) {
            that.currentNodes.push(item)
          }
        })
        that.lines.forEach(line => {
          const points = [{ x: line.sourceX, y: line.sourceY }, { x: line.destinationX, y: line.destinationY }]
          if (points.every(point => pointRectangleIntersection(point, edge)) && that.currentConnections.every(item => item.id !== line.id)) {
            const connection = that.internalConnections.filter(conn => conn.id === line.id)[0]
            that.currentConnections.push(connection)
          }
        })
      } else {
        d3.selectAll('#svg > .selection').classed('active', false)
      }
    },
    renderConnections() {
      const that = this
      return new Promise(function(resolve) {
        that.$nextTick(function() {
          d3.selectAll('#svg > g.connection').remove()
          // render lines
          that.lines = []
          that.internalConnections.forEach(conn => {
            const sourcePosition = that.getNodeConnectorOffset(conn.source.id, conn.source.position)
            const destinationPosition = that.getNodeConnectorOffset(conn.destination.id, conn.destination.position)
            let colors = conn.color
            if (that.currentConnections.filter(item => item === conn).length > 0) {
              colors = '#13ce66'
            }
            const result = that.arrowTo(sourcePosition.x, sourcePosition.y, destinationPosition.x, destinationPosition.y, conn.source.position, conn.destination.position, colors)
            for (const path of result.paths) {
              path.on('mousedown', function() {
                d3.event.stopPropagation()
                if (that.pathClickedOnce) {
                  that.editConnection(conn)
                } else {
                  const timer = setTimeout(function() {
                    that.pathClickedOnce = false
                    clearTimeout(timer)
                  }, 300)
                  that.pathClickedOnce = true
                }
                that.currentNodes.splice(0, that.currentNodes.length)
                that.currentConnections.splice(0, that.currentConnections.length)
                that.currentConnections.push(conn)
              })
            }
            for (const line of result.lines) {
              that.lines.push({
                sourceX: line.sourceX,
                sourceY: line.sourceY,
                destinationX: line.destinationX,
                destinationY: line.destinationY,
                id: conn.id
              })
            }
          })
          resolve()
        })
      })
    },
    renderNodes() {
      const that = this
      return new Promise(function(resolve) {
        d3.selectAll('#svg > g.node').remove()

        // render nodes
        that.internalNodes.forEach(node => {
          that.renderNode(node, that.currentNodes.filter(item => item === node).length > 0)
        })

        resolve()
      })
    },
    getNodeConnectorOffset(nodeId, connectorPosition) {
      const node = this.internalNodes.filter(item => item.id === nodeId)[0]
      return this.getConnectorPosition(node)[connectorPosition]
    },
    append(element) {
      const svg = d3.select('#svg')
      return svg.insert(element, '.selection')
    },
    guideLineTo(x1, y1, x2, y2) {
      const g = this.append('g')
      g.classed('guideline', true)
      lineTo(g, x1, y1, x2, y2, 1, '#a3a3a3', [5, 3])
    },
    arrowTo(x1, y1, x2, y2, startPosition, endPosition, color) {
      const g = this.append('g')
      g.classed('connection', true)
      line2(g, x1, y1, x2, y2, startPosition, endPosition, 2, color || '#2E4AD5', true)
      // a 5px cover to make mouse operation conveniently
      return line2(g, x1, y1, x2, y2, startPosition, endPosition, 5, 'transparent', false)
    },
    renderNode(node, isSelected) {
      const that = this
      const g = that
        .append('g')
        .attr('cursor', 'move')
        .classed('node', true)

      node.render = that.render
      node.render(g, node, isSelected)

      const drag = d3
        .drag()
        .on('start', function() {
          // handle mousedown
          const isNotCurrentNode = that.currentNodes.filter(item => item === node).length === 0
          if (isNotCurrentNode) {
            that.currentConnections.splice(0, that.currentConnections.length)
            that.currentNodes.splice(0, that.currentNodes.length)
            that.currentNodes.push(node)
          }
          // if (that.clickedOnce) {
          //   that.currentNodes.splice(0, that.currentNodes.length)
          //   that.editNode(node)
          // } else {
          //   const timer = setTimeout(function() {
          //     that.clickedOnce = false
          //     clearTimeout(timer)
          //   }, 300)
          //   that.clickedOnce = true
          // }
        })
        .on('drag', async function() {
          if (!that.readonly) {
            for (const currentNode of that.currentNodes) {
              if (currentNode.type === 'image') {
                continue
              }
              const x = d3.event.dx // / zoom;
              // if (currentNode.x + x < 0) {
              //   x = -currentNode.x
              // }
              currentNode.x += x
              const y = d3.event.dy // / zoom;
              // if (currentNode.y + y < 0) {
              //   y = -currentNode.y
              // }
              currentNode.y += y
            }
          }

          d3.selectAll('#svg > g.guideline').remove()
          const edge = that.getCurrentNodesEdge()
          const expectX = Math.round(Math.round(edge.start.x) / 10) * 10
          const expectY = Math.round(Math.round(edge.start.y) / 10) * 10
          that.internalNodes.forEach(item => {
            if (item.type !== 'image') {
              if (that.currentNodes.filter(currentNode => currentNode === item).length === 0) {
                if (item.x === expectX) {
                // vertical guideline
                  if (item.y < expectY) {
                    that.guideLineTo(item.x, item.y + item.height, expectX, expectY)
                  } else {
                    that.guideLineTo(expectX, expectY + item.height, item.x, item.y)
                  }
                }
                if (item.y === expectY) {
                // horizontal guideline
                  if (item.x < expectX) {
                    that.guideLineTo(item.x + item.width, item.y, expectX, expectY)
                  } else {
                    that.guideLineTo(expectX + item.width, expectY, item.x, item.y)
                  }
                }
              }
            }
          })
        })
        .on('end', function() {
          d3.selectAll('#svg > g.guideline').remove()
          that.dragEnd = false
          if (node.type !== 'image') {
            that.editNode(node)
          }
          // 这里因为节点与节点之间的线条不直所以注释了
          // for (const currentNode of that.currentNodes) {
          //   currentNode.x = Math.round(Math.round(currentNode.x) / 10) * 10 + 4
          //   currentNode.y = Math.round(Math.round(currentNode.y) / 10) * 10
          // }
        })
      g.call(drag)
      g.on('mousedown', function() {
        // handle ctrl+mousedown
        if (!d3.event.ctrlKey) {
          return
        }

        const isNotCurrentNode = that.currentNodes.filter(item => item === node).length === 0
        if (isNotCurrentNode) {
          that.currentNodes.push(node)
        } else {
          that.currentNodes.splice(that.currentNodes.indexOf(node), 1)
        }
      })
    },
    getCurrentNodesEdge() {
      const points = this.currentNodes.map(node => ({
        x: node.x,
        y: node.y
      }))
      points.push(
        ...this.currentNodes.map(node => ({
          x: node.x + node.width,
          y: node.y + node.height
        }))
      )
      return getEdgeOfPoints(points)
    },
    save() {
      if (this.readonly) {
        return
      }
      // d3.select('#svg').attr('transform', 'translate(500, 500)')
      this.$emit('save', this.internalNodes, this.internalConnections)
    },
    async remove() {
      if (this.readonly) {
        return
      }
      if (this.currentConnections.length > 0) {
        for (const conn of this.currentConnections) {
          this.removeConnection(conn)
        }
        this.currentConnections.splice(0, this.currentConnections.length)
      }
      if (this.currentNodes.length > 0) {
        for (const node of this.currentNodes) {
          if (node.type !== 'image') {
            this.removeNode(node)
          }
        }
        this.currentNodes.splice(0, this.currentNodes.length)
      }
    },
    removeNode(node) {
      this.$emit('delnode', node.type, node.subId_stepId)

      const connections = this.internalConnections.filter(item => item.source.id === node.id || item.destination.id === node.id)
      for (const connection of connections) {
        this.internalConnections.splice(this.internalConnections.indexOf(connection), 1)
      }
      this.internalNodes.splice(this.internalNodes.indexOf(node), 1)
      this.save()
    },
    removeConnection(conn) {
      const index = this.internalConnections.indexOf(conn)
      this.internalConnections.splice(index, 1)
    },
    moveCurrentNode(x, y) {
      if (this.currentNodes.length > 0 && !this.readonly) {
        for (const node of this.currentNodes) {
          if (node.x + x < 0) {
            x = -node.x
          }
          node.x += x
          if (node.y + y < 0) {
            y = -node.y
          }
          node.y += y
        }
      }
    },
    init(node1, thisH) {
      const that = this
      if (node1 !== undefined && thisH !== undefined) {
        thisH.internalNodes.forEach(node => {
          // 当个node编辑时，更新数据触发重新渲染图像界面
          if (node1 !== undefined && node1.id === node.id) {
            node.width = parseFloat(node1.width)
            node.height = parseFloat(node1.height)
            node.x = parseFloat(node1.x)
            node.y = parseFloat(node1.y)
            node.name = node1.name
            node.type = node1.type
            node.imgId = node1.imgId
            node.color = node1.color
            node.describe = node1.describe
            node.subId_stepId = node1.subId_stepId
            node.strokeWidth = parseFloat(node1.strokeWidth)
          }
        })
      } else {
        that.internalNodes.splice(0, that.internalNodes.length)
        that.internalConnections.splice(0, that.internalConnections.length)

        that.nodes.forEach(node => {
          const newNode = Object.assign({}, node)
          newNode.width = newNode.width || 120
          newNode.height = newNode.height || 60
          // 当个node编辑时，更新数据触发重新渲染图像界面
          if (node !== undefined && node.id === newNode.id) {
            newNode.width = parseFloat(node.width)
            newNode.height = parseFloat(node.height)
            newNode.x = parseFloat(node.x)
            newNode.y = parseFloat(node.y)
            newNode.name = node.name
            newNode.type = node.type
            newNode.imgId = node.imgId
            newNode.color = node.color
            newNode.describe = node.describe
            newNode.subId_stepId = node.subId_stepId
            newNode.strokeWidth = parseFloat(node.strokeWidth)
          }
          that.internalNodes.push(newNode)
        })
        that.connections.forEach(connection => {
          that.internalConnections.push(JSON.parse(JSON.stringify(connection)))
        })
      }
    }
  }
}
</script>
