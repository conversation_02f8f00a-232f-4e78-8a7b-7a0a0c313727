import request from '@/utils/request'

// 查询驱动区域
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaBlockSel',
    method: 'post',
    data
  })
}
// 新增驱动区域
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaBlockIns',
    method: 'post',
    data
  })
}
// 修改驱动区域
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaBlockUpd',
    method: 'post',
    data
  })
}
// 删除驱动区域
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaBlockDel',
    method: 'post',
    data
  })
}

// 驱动区域Lov
export function lovBlock(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaBlockLov',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, lovBlock }
