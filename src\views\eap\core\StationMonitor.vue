<template>
  <!-- <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
       <div class="h5-form-item fn-clear">
        <el-button type="primary" @click="onSnap" plain>抓图</el-button>
        <el-button type="primary"  @click="onStartRecord" plain>开始录像</el-button>
        <el-button type="primary"  @click="onStopRecord" plain>停止录像</el-button>
        <el-button type="primary"  @click="onStartEnlarge" plain>开启电子放大</el-button>
        <el-button type="primary"  @click="onStopEnlarge" plain>变比电子放大</el-button>
        <el-button type="primary"  @click="onSetFull" plain>全屏</el-button>
       </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
     
      </el-col>
    </el-row>
  </div> -->
  <div>
    <iframe allowfullscreen="true" frameborder="0" name="showHere" scrolling="auto" src="http://localhost:80/play.html" style="width: 100%;height: 900px;"></iframe>
  </div>
  </template>

<script>
// import crudStationConfig from '@/api/eap/eapFmodConfig'
// import Cookies from 'js-cookie'
// import CRUD, { presenter, header, form, crud } from '@crud/crud'
// // import play from '../../../utils/play.js'
// import Player from '../../../utils/module/PlayerControl.js'
// import $ from 'jquery'
// import rrOperation from '@crud/RR.operation'
// import crudOperation from '@crud/CRUD.operation'
// import udOperation from '@crud/UD.operation'
// import pagination from '@crud/Pagination'
// const defaultForm = {
//   config_id: '',
//   device_type: 'Load',
//   group_code: '',
//   tag_code: '',
//   client_code: '',
//   config_des: '',
//   data_type: 'Bool',
//   enable_flag: 'Y'
// }
// export default {
//   name: 'EAP_STATION_CONFIG',
//   components: { crudOperation, rrOperation, udOperation, pagination },
//   cruds() {
//     return CRUD({
//       title: '工位配置维护',
//       // 登录用户
//       userName: Cookies.get('userName'),
//       // 唯一字段
//       idField: 'config_id',
//       // 排序
//       sort: ['config_id asc'],
//       // CRUD Method
//       crudMethod: { ...crudStationConfig },
//       // 按钮显示
//       optShow: {
//         add: true,
//         edit: true,
//         del: true,
//         down: true,
//         reset: true
//       }
//     })
//   },
//   mixins: [presenter(), header(), form(defaultForm), crud()],
//   // 数据模型
//   data() {
//     return {
//       height: document.documentElement.clientHeight - 270,
//       permission: {
//         add: ['admin', 'eap_station_config:add'],
//         edit: ['admin', 'eap_station_config:edit'],
//         del: ['admin', 'eap_station_config:del'],
//         down: ['admin', 'eap_station_config:down']
//       },
//       rules: {
//         config_des: [{ required: true, message: '请输入配置描述', trigger: 'blur' }],
//         client_code: [{ required: true, message: '请输入实例编码', trigger: 'blur' }],
//         group_code: [{ required: true, message: '请输入标签组编码', trigger: 'blur' }],
//         tag_code: [{ required: true, message: '请输入标签编码', trigger: 'blur' }]
//       },
//       deviceTypeData: [{ value: 'Load', label: '投板机' }, { value: 'UnLoad', label: '收板机' }],
//       dataTypeData: ['Bool', 'String', 'Json', 'Interf'],
//       _doc: document.getElementById('device_ipc_content'),
//       deviceObject: {
//         loginState: [],
//         session: []
//       }, curDeviceIndex: 0,
//       $stream: undefined,
//       $volume: undefined,
//       $video_wrap: undefined,
//       WndIndex: 0,
//       $canvas: undefined, //canvas播放视频DOM
//       $video: undefined, //video播放视频DOM
//       $canvas_ivs: undefined, //canvas绘图DOM
//       $videoLoading: undefined,  //“加载中...”提示
//       ivsInstance: [],
//       recordInstance: [],
//       curEnlargeWnd: 0,
//     }
//   },
//   // 数据字典
//   dicts: ['ENABLE_FLAG'],
//   mounted: function () {
//     let videoStr = '';
//     for (var i = 0; i < 16; i++) {
//       videoStr += '<div wnd-index="' + i + '" style="float: left; background-color: #000; position: relative; width: 100%; height: 100%;overflow:hidden;border:1px solid rgb(125,125,125)"><canvas id="h5_canvas_' + i + '" style="width:100%;height:100%;"></canvas><p id="h5_loading_' + i + '" class="video_loading"  style="display:none">加载中...</p><video id="h5_video_' + i + '" style="display: none;width:100%;height:100%;position:absolute;top:0;left:0"></video><canvas id="h5_ivs_' + i + '" style="position: absolute;left: 0;top: 0;width: 100%;height: 100%;" width="500" height="300"></canvas></div>';
//     }
//     document.getElementById('device_ipc_content').querySelector('.h5-play-wrap').innerHTML = videoStr;
//     let WndIndex = this.WndIndex;
//     let curDeviceIndex = this.curDeviceIndex;
//     let that = this;
//     document.getElementById('device_ipc_content').querySelectorAll('.h5-play-wrap div').forEach((item, index) => {
//       item.addEventListener('click', function (event) {
//         let _wndIndex = event.target.parentNode.getAttribute('wnd-index') - 0;
//         document.getElementById('device_ipc_content').querySelectorAll('.h5-play-wrap div').forEach(function (item, index) {
//           if (index === _wndIndex) {
//             item.style.borderColor = 'rgb(255, 204, 0)';
//             WndIndex = _wndIndex;
//             curDeviceIndex = item.getAttribute('deviceindex') - 0;
//             console.info(this)
//             that.setVideoDom();
//           } else {
//             item.style.borderColor = 'rgb(125, 125, 125)';
//           }
//         });
//       })
//       this.WndIndex = WndIndex;
//       this.curDeviceIndex = curDeviceIndex;
//     });
//     let deviceDom = document.getElementById('device_ipc_content').querySelectorAll('.h5-menu-list li');
//     this.deviceObject.loginState = new Array(deviceDom.length).fill(false);
//     this.deviceObject.session = new Array(deviceDom.length).fill(0);
//     deviceDom.forEach(function (item, index) {
//       item.addEventListener('click', async () => {
//         await onLogout();
//         this.curDeviceIndex = index;
//         this.onLogin();
//       });
//     });
//     this.onLogin();
//     this.$stream = document.getElementById('h5_stream');

//     this.$volume = document.getElementById('h5_volume');
//     this.$video_wrap = document.getElementById('device_ipc_content').querySelector('.h5-play-wrap');
//     this.setVideoDom();

//     let inputArr = document.getElementById('device_ipc_content').querySelectorAll('input[btn-for]');
//     for (let node of inputArr) {
//       node.addEventListener('click', this.bindClickEvent);
//     }

//     let selArr = document.getElementById('device_ipc_content').querySelectorAll('select[sel-for]');
//     for (let node of selArr) {
//       node.addEventListener('change', this.bindChangeEvent);
//     }

//     this.$volume.addEventListener('input', function (event) {
//       let vol = event.target.value - 0;
//       document.getElementById('h5_volume_value').innerText = vol;
//     });
//     this.$volume.addEventListener('change', function (event) {
//       let vol = event.target.value - 0;
//       if (playerInstance[this.WndIndex]) {
//         playerInstance[this.WndIndex].setAudioVolume(vol);
//       }
//     });
//     document.getElementById('h5_first').addEventListener('click', function () {
//       if (curPage != 1) {
//         curPage = 1;
//         updateTable();
//       }
//     });
//     document.getElementById('h5_pre').addEventListener('click', function () {
//       if (curPage > 1) {
//         curPage = curPage - 1;
//         updateTable();
//       }
//     });
//     document.getElementById('h5_next').addEventListener('click', function () {
//       if (curPage < totalPage) {
//         curPage = curPage + 1;
//         updateTable();
//       }
//     });
//     document.getElementById('h5_last').addEventListener('click', function () {
//       if (curPage != totalPage) {
//         curPage = totalPage;
//         updateTable();
//       }
//     });
//     document.getElementById('h5_goPage').addEventListener('click', function () {
//       let val = document.getElementById('h5_goNumber').value - 0;
//       if (curPage != val) {
//         curPage = val;
//         updateTable();
//       }
//     });
//     ['fullscreenchange', 'webkitfullscreenchange', 'mozfullscreenchange', 'msfullscreenchange'].forEach((item, index) => {
//       document.addEventListener(item, this.fullscreenchange, false);
//     });
//     this.onChangeWdnNum();
//   },
//   created: function () { },
//   methods: {
//     /**
//      * @description 抓图
//      */
//     onSnap() {
//       if (playerInstance[this.WndIndex]) {
//         playerInstance[this.WndIndex].capture('test');
//       }
//     },
//     bindClickEvent(event) {
//       let $el = event.target,
//         method = $el.getAttribute('btn-for'),
//         disabled = $el.getAttribute('disabled');
//       if (!disabled) {
//         eval(method + "()");
//       }
//     },
//     bindChangeEvent(event) {
//       let $el = event.target,
//         method = $el.getAttribute('sel-for'),
//         disabled = $el.getAttribute('disabled');
//       if (!disabled) {
//         eval(method + "()");
//       }
//     },
//     onLogin() {
//       let _dom = document.getElementById('device_ipc_content').querySelector('.h5-menu-list li:nth-child(' + (this.curDeviceIndex + 1) + ')');
//       let ip = _dom.getAttribute('data-ip');
//       let port = _dom.getAttribute('data-port');
//       let name = _dom.getAttribute('data-user');
//       let pswd = _dom.getAttribute('data-pswd');
//       let target = ip + ':' + port;
//       setIP(target);
//       RPC.login(name, pswd, true).then((res) => {
//         setCookie('DWebClientSessionID', '', -1);
//         setCookie('DhWebClientSessionID', '', -1);
//         /**
//          * RPC.keepAlive 保活
//          */
//         console.info(target)
//         RPC.keepAlive(300, 60000, this._getSession(), target, this.WndIndex);
//         afterLogin();
//         onRealPreview(ip, port, name, pswd);//登录后拉流
//       }).catch((err) => {
//         console.log(err);
//         loginError(err);
//         $videoLoading.style.display = '';
//         $videoLoading.innerText = '打开失败！';
//         let curWndType = document.getElementById('device_ipc_content').querySelector('[sel-for=onChangeWdnNum]').value - 0;
//         let _newDom = document.getElementById('device_ipc_content').querySelector('.h5-menu-list li:nth-child(' + (this.curDeviceIndex + 2) + ')');
//         if (curWndType !== 1 && _newDom != null && isStartAll) {
//           clickNextWnd();
//           _newDom.click();
//         };
//       });
//     }, fullscreenchange() {
//       if (getFull()) {
//         return;
//       } else {
//         exitfullScreen();
//       }
//     },
//     afterLogin() {
//       deviceObject.loginState[curDeviceIndex] = true;
//       deviceObject.session[curDeviceIndex] = _getSession();
//       $stream.options.length = 0;
//       $('#h5_playback_channel').options.length = 0;
//       $('#h5_channel').options.length = 0;
//       /**
//        * RPC.MagicBox.getProductDefinition 获取产品定义
//        * @param {string}  'MaxExtraStream' 定义名称
//        * @returns {Promise}
//        */
//       RPC.MagicBox.getProductDefinition('MaxExtraStream').then(function (params) {
//         let maxExtra = params.definition;
//         $stream.options.add(new Option('主码流', 0));
//         if (maxExtra > 1) {
//           for (let i = 1; i <= maxExtra; i++) {
//             $stream.options.add(new Option('辅码流' + i, i));
//           }
//         } else {
//           $stream.options.add(new Option('辅码流', 1));
//         }
//       });
//       RPC.DevVideoInput.getCollect().then(function (params) {
//         let chnls = params.channels;
//         for (let i = 0; i < chnls; i++) {
//           $('#h5_playback_channel').options.add(new Option(i + 1, i));
//           $('#h5_channel').options.add(new Option(i + 1, i));
//         }
//       });
//       let curDate = new Date();
//       let dateString = curDate.toLocaleDateString();
//       let dateSplit = dateString.split('/');
//       let month = dateSplit[1] - 0;
//       if (month < 10) {
//         dateSplit[1] = '0' + month;
//       }
//       let day = dateSplit[2] - 0;
//       if (day < 10) {
//         dateSplit[2] = '0' + day;
//       }
//       let date = dateSplit.join('-');
//       $('#h5_startTime').value = date + 'T' + '00:00';
//       $('#h5_endTime').value = date + 'T' + '23:59:59';
//     },
//     /**
//      * @description 针对直播的，开始本地下载
//      */
//     onStartRecord() {
//       let _realDevice = document.getElementById('device_ipc_content').querySelector('div[wnd-index="' + this.WndIndex + '"]').getAttribute('deviceindex');
//       if (_realDevice === null) {
//         return;
//       }
//       let recordPlayer = null;
//       let _dom = document.getElementById('device_ipc_content').querySelector('.h5-menu-list li:nth-child(' + (this.curDeviceIndex + 1) + ')');
//       let curChannel = channel + 1; //无插件通道号从1开始
//       let ip = document.getElementById('device_ipc_content').getAttribute('data-ip');
//       let port = Number(document.getElementById('device_ipc_content').getAttribute('data-port'));
//       let username = document.getElementById('device_ipc_content').getAttribute('data-user');
//       let password = document.getElementById('device_ipc_content').getAttribute('data-pswd');
//       let stream = $stream.value - 0;
//       let rtspURL = 'rtsp://' + ip + ':' + port + '/cam/realmonitor?channel=' + curChannel + '&subtype=' + stream + '&proto=Private3';
//       let optionsRecord = {
//         wsURL: 'ws://' + ip + ':' + port + '/rtspoverwebsocket',
//         rtspURL: rtspURL,
//         username: username,
//         password: password
//       }
//       recordPlayer = new PlayerControl(optionsRecord);
//       recordPlayer.startRecord(true);
//       recordInstance[this.WndIndex] = recordPlayer;
//       this.$videoLoading.style.display = '';
//       this.$videoLoading.innerText = '录像中！';
//     },
//     /**
//      * @description 针对直播的，停止本地下载
//      */
//     onStopRecord() {
//       if (recordInstance[this.WndIndex]) {
//         recordInstance[this.WndIndex].startRecord(false);
//         recordInstance[this.WndIndex] = null;
//         this.$videoLoading.style.display = 'none';
//       }
//     },
//     /**
//      * @description 针对回放视频的，视频裁剪
//      */
//     setVideoDom() {
//       this.$canvas = document.getElementById('#h5_canvas_' + this.WndIndex);
//       this.$video = $('#h5_video_' + this.WndIndex);
//       this.$canvas_ivs = $('#h5_ivs_' + this.WndIndex);
//       this.$videoLoading = $('#h5_loading_' + this.WndIndex);
//       let _session = this.deviceObject.session[this.curDeviceIndex];
//       let _dom = document.getElementById('device_ipc_content').querySelector('.h5-menu-list li:nth-child(' + (this.curDeviceIndex + 1) + ')');
//       let ip = document.getElementById('device_ipc_content').getAttribute('data-ip');
//       let port = document.getElementById('device_ipc_content').getAttribute('data-port');
//       setIP(ip + ':' + port);
//       if (_session !== 0) {
//         _setSession(_session);
//       }
//     },
//     onStartCut() {
//       let _cutIndex = this._doc.querySelector('[btn-for=onStartCut]').getAttribute('cutIndex') - 0;
//       let cutPlayer = null;
//       let ip = playbackOptions.ip;
//       let port = playbackOptions.port;
//       let username = playbackOptions.name;
//       let password = playbackOptions.pswd;
//       let url = recordArr[_cutIndex].FilePath;
//       let _rtspURL = 'rtsp://' + ip + ':' + port + '/' + url;
//       let cutStartTime = $('#h5_cutStartTime').value;
//       let s = new Date(cutStartTime.replace('T', ' ')).getTime();
//       let startTime = new Date(recordArr[_cutIndex].StartTime).getTime();
//       let range1 = (s - startTime) / 1000;
//       let optionsRecord = {
//         wsURL: 'ws://' + ip + ':' + port + '/rtspoverwebsocket',
//         rtspURL: _rtspURL,
//         username: username,
//         password: password,
//         isPrivateProtocol: false, //是否私有协议，默认false
//         realm: RPC.realm, //登录返回的设备Realm值
//         speed: 16, //倍速拉流，16倍速
//         playback: true, //是否是回放
//         range: range1 //视频裁剪时间与视频的StartTime时间差值
//       }
//       cutPlayer = new PlayerControl(optionsRecord);
//       cutPlayer.on('WorkerReady', function () {
//         cutPlayer.connect();
//       });
//       cutPlayer.on('FileOver', function () {
//         console.log('File Over');
//         cutPlayer.startCut(false);
//         isCuting = false;
//         $('#h5_cut_process').innerText = '100%';
//       });
//       cutPlayer.on('UpdateTimeStamp', function (e) {
//         let cutStartTime1 = $('#h5_cutStartTime').value;
//         let cutEndTime1 = $('#h5_cutEndTime').value;
//         let s1 = new Date(cutStartTime1.replace('T', ' ')).getTime() / 1000;
//         let e1 = new Date(cutEndTime1.replace('T', ' ')).getTime() / 1000;
//         let process = parseInt((1 - (e1 - e.timestamp) / (e1 - s1)) * 100);
//         // console.log(new Date(e.timestamp * 1000));
//         $('#h5_cut_process').innerText = (process > 100 ? 100 : process) + '%';
//         if ((e.timestamp >= s1) && !isCuting) {
//           cutPlayer.startCut(true);
//           isCuting = true;
//         }
//         if ((e.timestamp >= e1) && isCuting) {
//           cutPlayer.startCut(false);
//           isCuting = false;
//           $('#h5_cut_process').innerText = '100%';
//         }
//       });
//       cutPlayer.init($canvas, $video);
//       cutInstance[this.WndIndex] = cutPlayer;
//     },
//     /**
//      * @description 开启电子放大
//      */
//     onStartEnlarge() {
//       if (this.ivsInstance[this.WndIndex]) {
//         this.ivsInstance[this.WndIndex].setRegionNum('rect', 1);
//         let param = { ...canvasParam };
//         this.ivsInstance[this.WndIndex].drawStart('rect', param);
//         curEnlargeWnd = this.WndIndex;
//       }
//     },
//     /**
//      * @description 开启区域放大
//      */
//     onStartGridEnlarge() {
//       document.getElementById('device_ipc_content').querySelectorAll('[wnd-index]').forEach((item, index) => {
//         if (index === this.WndIndex) {
//           this._doc.querySelector('[wnd-index="' + this.WndIndex + '"]').style.width = '100%';
//           this._doc.querySelector('[wnd-index="' + this.WndIndex + '"]').style.height = '100%';
//           this._doc.querySelector('#h5_ivs_' + this.WndIndex).setAttribute('height', 300);
//           this._doc.querySelector('#h5_ivs_' + this.WndIndex).setAttribute('width', 500);
//           ivsInstance[this.WndIndex] && ivsInstance[this.WndIndex].resize();
//         } else {
//           item.style.display = 'none';
//         }
//       });
//     },
//     /**
//      * @description 关闭区域放大
//      */
//     onCloseGridEnlarge() {
//       this._doc.querySelectorAll('[wnd-index]').forEach((item, index) => {
//         item.style.display = '';
//       });
//       this.onChangeWdnNum();
//     },
//     /**
//      * @description 关闭电子放大
//      */
//     onStopEnlarge() {
//       let dom = $canvas;
//       if (dom.style.display === 'none') {
//         dom = $video;
//       }
//       dom.style.width = '100%';
//       dom.style.height = '100%';
//       dom.style.left = 0;
//       dom.style.top = 0;
//       dom.style.position = 'static';
//     },
//     onChangeWdnNum() {
//       let val = document.getElementById('device_ipc_content').querySelector('[sel-for=onChangeWdnNum]').value;
//       let ivsDom = document.getElementById('device_ipc_content').querySelectorAll('[id^=h5_ivs_]');
//       let divDom = document.getElementById('device_ipc_content').querySelectorAll('.h5-play-wrap div');
//       if (val === '1') {
//         divDom.forEach(item => {
//           item.style.width = '100%';
//           item.style.height = '100%';
//           item.style.borderColor = '#000';
//         });
//       } else if (val === '2') {
//         divDom.forEach((item, index) => {
//           item.style.width = 'calc(50% - 2px)';
//           item.style.height = 'calc(50% - 2px)';
//         });
//       } else if (val === '3') {
//         divDom.forEach((item, index) => {
//           item.style.height = 'calc(33.333% - 2px)';
//           item.style.width = 'calc(33.333% - 2px)';
//         });
//       } else if (val === '4') {
//         divDom.forEach((item, index) => {
//           item.style.width = 'calc(25% - 2px)';
//           item.style.height = 'calc(25% - 2px)';
//         });
//       }
//       ivsDom.forEach(item => {
//         item.setAttribute('width', `${item.parentNode.clientWidth}`);
//         item.setAttribute('height', `${item.parentNode.clientHeight}`);
//       });
//       this.ivsInstance.forEach(item => {
//         item && item.resize();
//       });
//       document.getElementById('device_ipc_content').querySelector('#h5_ivs_0').click();
//     },
//     /**
//      * @description 绘制电子放大后的回调函数
//      * @param {object} data 矩形框的坐标信息
//      */
//     rebackActivateLocalEnlarging(data) {
//       if (curEnlargeWnd != this.WndIndex) return;
//       let pos = data.data;
//       let newData;
//       if (pos[0][0] === pos[1][0]) {
//         // return false;
//       } else {
//         newData = {
//           left: pos[0][0],
//           top: pos[0][1],
//           right: pos[1][0],
//           bottom: pos[1][1]
//         }
//       }
//       let dom = $canvas;
//       if (dom.style.display === 'none') {
//         dom = $video;
//       }
//       // 倒着画
//       if (newData.right < newData.left) {
//         let tmp = newData.left;
//         newData.left = newData.right;
//         newData.right = tmp;
//       }

//       if (newData.bottom < newData.top) {
//         let tmp = newData.top;
//         newData.top = newData.bottom;
//         newData.bottom = tmp;
//       }

//       let scaleW = $video_wrap.childNodes[this.WndIndex].clientWidth / 8191;
//       let scaleH = $video_wrap.childNodes[this.WndIndex].clientHeight / 8191;

//       let result = zoomArea(newData.left * scaleW, newData.top * scaleH, newData.right * scaleW, newData.bottom * scaleH, $video_wrap.childNodes[this.WndIndex].clientWidth, $video_wrap.childNodes[WndIndex].clientHeight);
//       dom.style.width = result.width + 'px';
//       dom.style.height = result.height + 'px';
//       dom.style.left = result.left + 'px';
//       dom.style.top = result.top + 'px';
//       dom.style.position = 'absolute';
//       ivsInstance[this.WndIndex].removeShapeDrawEvent();
//     },
//     /**
//      * @description 设置全屏
//      */
//     onSetFull() {
//       if (this.getFull()) {
//         this.exitfullScreen();
//       } else {
//         this.setfullScreen();
//       }
//     }
//     , getFull() {
//       return window.top.document.mozFullScreen || window.top.document.webkitIsFullScreen || window.top.document.msFullscreenElement;
//     }, exitfullScreen() {
//       let docElm = window.top.document.documentElement;
//       if (docElm.exitFullscreen) {
//         docElm.exitFullscreen();
//       } else if (docElm.mozCancelFullScreen) {
//         docElm.mozCancelFullScreen();
//       } else if (docElm.webkitCancelFullScreen) {
//         docElm.webkitCancelFullScreen();
//       } else if (docElm.msExitFullscreen) {
//         docElm.msExitFullscreen();
//       }
//       handleFullscreen(false);
//     }, setfullScreen() {
//       let docElm = window.top.document.documentElement;
//       if (docElm.requestFullScreen) {
//         docElm.requestFullScreen();
//       } else if (docElm.mozRequestFullScreen) {
//         docElm.mozRequestFullScreen();
//       } else if (docElm.webkitRequestFullScreen) {
//         docElm.webkitRequestFullScreen();
//       } else if (docElm.msRequestFullscreen) {
//         docElm.msRequestFullscreen();
//       }
//       handleFullscreen(true);
//     }
//   }
// }
</script>

<style>
.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
