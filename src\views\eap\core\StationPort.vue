<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产线：">
                <el-select v-model="query.prod_line_id" filterable clearable size="small">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位：">
                <el-select v-model="query.station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="端口号：">
                <el-input v-model="query.port_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="AGV地标码：">
                <el-input v-model="query.agv_position" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="130px" :inline="true">
          <el-form-item label="产线" prop="prod_line_id">
            <el-select v-model="form.prod_line_id" filterable clearable size="small">
              <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="工位" prop="station_id">
            <el-select v-model="form.station_id" filterable clearable>
              <el-option v-for="item in stationData1" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="组号" prop="group_num">
            <el-input v-model="form.group_num" />
          </el-form-item>
          <el-form-item label="端口号" prop="port_code">
            <el-input v-model="form.port_code" />
          </el-form-item>
          <el-form-item label="端口排序" prop="port_index">
            <el-input v-model="form.port_index" />
          </el-form-item>
          <el-form-item label="端口标识" prop="port_sign">
            <el-input v-model="form.port_sign" />
          </el-form-item>
          <el-form-item label="AGV地标码" prop="agv_position">
            <el-input v-model="form.agv_position" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_port_id }}</el-descriptions-item>
                  <el-descriptions-item label="工位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_code+' '+props.row.station_des }}</el-descriptions-item>
                  <el-descriptions-item label="组号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.group_num }}</el-descriptions-item>
                  <el-descriptions-item label="端口号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.port_code }}</el-descriptions-item>
                  <el-descriptions-item label="端口排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.port_index }}</el-descriptions-item>
                  <el-descriptions-item label="端口标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.port_sign }}</el-descriptions-item>
                  <el-descriptions-item label="AGV地标码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.agv_position }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="station_id" min-width="100" label="工位">
              <template slot-scope="scope">
                {{ scope.row.station_code+' '+scope.row.station_des }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="group_num" min-width="100" label="组号" />
            <el-table-column  :show-overflow-tooltip="true" prop="port_code" min-width="100" label="端口号" />
            <el-table-column  :show-overflow-tooltip="true" prop="port_index" min-width="100" label="端口排序" />
            <el-table-column  :show-overflow-tooltip="true" prop="port_sign" min-width="100" label="端口标识" />
            <el-table-column  :show-overflow-tooltip="true" prop="agv_position" min-width="100" label="AGV地标码" />
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import crudStationPort from '@/api/eap/eapFmodStationPort'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  station_port_id: '',
  prod_line_id: '',
  station_id: '',
  group_num: '',
  port_code: '',
  port_index: '',
  port_sign: '',
  agv_position: '',
  enable_flag: 'Y'
}

const queryDefault = {
  prod_line_id: '',
  station_id: '',
  port_code: '',
  agv_position: '',
  enable_flag: '',
  sort: '',
  page: 0,
  size: 10
}
export default {
  name: 'EAP_STATION_PORT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '工位端口维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'station_port_id',
      // 排序
      sort: ['station_port_id asc'],
      // CRUD Method
      crudMethod: { ...crudStationPort },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      query: { ...queryDefault },
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'eap_station_port:add'],
        edit: ['admin', 'eap_station_port:edit'],
        del: ['admin', 'eap_station_port:del'],
        down: ['admin', 'eap_station_port:down']
      },
      rules: {
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        group_num: [{ required: true, message: '请输入组号', trigger: 'blur' }],
        port_code: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
        port_index: [{ required: true, message: '请输入端口排序', trigger: 'blur' }],
        port_sign: [{ required: true, message: '请输入端口标识', trigger: 'blur' }],
        agv_position: [{ required: true, message: '请输入AGV地标码', trigger: 'blur' }]
      },
      prodLineData: [],
      stationData: [],
      stationData1: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  watch: {
    'query.prod_line_id': {
      handler() {
        this.getStationData('query')
      }
    },
    'form.prod_line_id': {
      handler() {
        this.getStationData('form')
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将端口号为【' + data.port_code + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudStationPort
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              station_port_id: data.station_port_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    getStationData(type) {
      var prodLineId = ''
      if (type === 'query') {
        prodLineId = this.query.prod_line_id
        this.query.station_id = ''
        this.stationData = []
      } else {
        prodLineId = this.form.prod_line_id
        this.form.station_id = ''
        this.stationData1 = []
      }
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prodLineId
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              if (type === 'query') {
                this.stationData = defaultQuery.data
              } else {
                this.stationData1 = defaultQuery.data
              }
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
