import request from '@/utils/request'

// 查询主页面任务列表状态
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/task-status',
    method: 'post',
    data: data
  })
}
// 获取主页面天车出/入库数量
export function outInStock(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/car-out-in-stock-number',
    method: 'get',
    params: data
  })
}
// 获取主页面天车显示分拣数量
export function sortNumber(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/sort-number',
    method: 'get',
    params: data
  })
}

// 获取主页面切割机
export function cutTask(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/cut-task',
    method: 'get',
    params: data
  })
}

// 获取主页面流程管理list
export function flowTask(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/working-flow',
    method: 'post',
    data: data
  })
}

// 获取主页面所有工位状态
export function allCellStatus(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/all-cell-status',
    method: 'get',
    params: data
  })
}

// 获取主页面所有库存
export function allInventory(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/all-inventory',
    method: 'get',
    params: data
  })
}

// 获取主页面分区工位状态
export function sectionCellStatus(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/section-cell-status',
    method: 'get',
    params: data
  })
}

// 获取主页面任务数量
export function taskCount(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/task-count',
    method: 'get',
    params: data
  })
}

// 获取主页面点位
export function sectionPoint(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/section-point?sectionCode=MAIN',
    method: 'post',
    data: data
  })
}

// 重写获取实时状态查询
export function stationMinStatus(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMainStationMisStatus',
    method: 'post',
    data: data
  })
}

export default { sel, outInStock, sortNumber, cutTask, flowTask, allCellStatus, allInventory, sectionCellStatus, taskCount, sectionPoint, stationMinStatus }

