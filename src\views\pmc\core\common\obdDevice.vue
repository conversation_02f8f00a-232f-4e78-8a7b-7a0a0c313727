<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-4 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="车辆VIN：">  <!-- 车辆VIN： -->
                <el-input v-model="query.carvin" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="col-md-4 col-12">
            <el-form-item class="labelIline" label="创建时期：">  <!-- 车辆VIN： -->
              <el-date-picker
                v-model="createTime"
                type="daterange"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="”yyyy-MM-dd”"
                @change="filterData"
              />
            </el-form-item>

          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column  :show-overflow-tooltip="true" prop="VIN" label="车辆VIN" />  <!-- 车辆VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="VID" label="写入的VIN" />  <!-- 写入的VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="CALID" label="CALID" />  <!-- CALID -->
            <el-table-column  :show-overflow-tooltip="true" prop="CVN" label="CVN" />  <!-- CVN -->
            <el-table-column  :show-overflow-tooltip="true" prop="TestResult" label="检测结果" /> <!-- 检测结果 -->
            <el-table-column  :show-overflow-tooltip="true" prop="FailReason" label="不合格原因" />  <!-- 不合格原因 -->
            <el-table-column  :show-overflow-tooltip="true" prop="TestTime" label="检测日期" />  <!-- 检测日期 -->
            <el-table-column  :show-overflow-tooltip="true" prop="CALID1" label="CALID1" />  <!-- CALID1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="CVN1" label="CVN1" />  <!-- CVN1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="CALID2" label="CALID2" />  <!-- CALID2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="CVN2" label="CVN2" />  <!-- CVN2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="OBDType" label="OBD类型" />  <!-- OBD类型 -->
            <el-table-column  :show-overflow-tooltip="true" prop="EngineID" label="EngineID" />  <!-- EngineID -->
            <el-table-column  :show-overflow-tooltip="true" prop="RearID" label="RearID" />  <!-- RearID -->
            <el-table-column  :show-overflow-tooltip="true" prop="OtherID" label="OtherID" />  <!-- OtherID -->
            <el-table-column  prop="ConnectResult" label="通讯是否成功" width="110">  <!-- 通讯是否成功 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.ConnectResult === "Y" ? "是" : "否" }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import sysObdDevice from '@/api/pmc/sysObdDevice'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
export default {
  name: 'sysObdDevice',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: 'OBD设备',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'function_id',
      // // 排序
      sort: ['function_id asc'],
      // CRUD Method
      crudMethod: { ...sysObdDevice },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  // 数据模型
  data() {
    return {
      createTime: '',
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {
    filterData() {
      console.log(this.createTime)
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
    color: #fff;
    background-color: #1473c5;
    border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
    background: #438fd1;
    border-color: #438fd1;
    color: #fff;
}
.labelIline{
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label{
    white-space: nowrap;
  }
}
</style>
