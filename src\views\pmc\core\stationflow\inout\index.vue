<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      class="wrapCard"
      shadow="never"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.stationCode')">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_code">
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_code"
                    :label="item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workStatus')">
                <!-- 生产状态： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.work_status">
                  <el-option
                    v-for="item in workStatusData"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.makeOrder')">
                <!-- 生产订单： -->
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.vin')">
                <!-- VIN码： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <!--预约拉出-->
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="dialogTitlePreSetOut"
        :visible.sync="dialogVisbleSyncPreSetOut"
        :before-close="toBeforeClosePreSetOut"
        size="650px"
      >
        <el-form
          ref="formPreSetOut"
          class="el-form-wrap"
          :model="formPreSetOut"
          size="small"
          label-width="100px"
          :inline="true"
        >
          <el-form-item :label="$t('lang_pack.stationmo.makeOrder')" prop="make_order">
            <!-- 订单号 -->
            <el-input v-model="formPreSetOut.make_order" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.stationmo.dms')" prop="dms">
            <!-- DMS号 -->
            <el-input v-model="formPreSetOut.dms" />
          </el-form-item>
          <!-- <el-form-item
            :label="$t('lang_pack.stationmo.itemProject')"
            prop="item_project"
          > -->
            <!-- 行项目 -->
            <!-- <el-input v-model="formPreSetOut.item_project" />
          </el-form-item> -->
          <el-form-item :label="$t('lang_pack.flowonline.vin')" prop="vin">
            <!-- VIN -->
            <el-input v-model="formSetOut.vin" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.stationmo.setMarks')" prop="set_marks">
            <!-- 拉入拉出备注 -->
            <el-input v-model="formPreSetOut.set_marks" />
          </el-form-item>
          <el-form-item label="拉出方式">
            <!-- 拉出方式 -->
            <el-radio-group v-model="formPreSetOut.type">
              <el-radio :label="0">区域拉出</el-radio>
              <el-radio :label="1">整体拉出</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="toPreSetOutCancel">{{
            $t("lang_pack.commonPage.cancel")
          }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="toPreSetOutSubmit('formPreSetOut')"
            >{{ $t("lang_pack.commonPage.confirm") }}</el-button
          >
          <!-- 确认 -->
        </div>
      </el-drawer>

      <!--直接拉出-->
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="dialogTitleSetOut"
        :visible.sync="dialogVisbleSyncSetOut"
        :before-close="toBeforeCloseSetOut"
        size="650px"
      >
        <el-form
          ref="formSetOut"
          class="el-form-wrap"
          :model="formSetOut"
          :rules="rulesSetOut"
          size="small"
          label-width="100px"
          :inline="true"
        >
          <el-form-item :label="$t('lang_pack.stationmo.makeOrder')" prop="make_order">
            <!-- 订单号 -->
            <el-input v-model="formSetOut.make_order" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.stationmo.dms')" prop="dms">
            <!-- DMS号 -->
            <el-input v-model="formSetOut.dms" />
          </el-form-item>
          <!-- <el-form-item
            :label="$t('lang_pack.stationmo.itemProject')"
            prop="item_project"
          >
            行项目
            <el-input v-model="formSetOut.item_project" />
          </el-form-item> -->
          <el-form-item :label="$t('lang_pack.flowonline.vin')" prop="vin">
            <!-- VIN -->
            <el-input v-model="formSetOut.vin" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.stationmo.setMarks')" prop="set_marks">
            <!-- 拉入拉出备注 -->
            <el-input v-model="formSetOut.set_marks" />
          </el-form-item>
          <el-form-item label="拉出方式">
            <!-- 拉出方式 -->
            <el-radio-group v-model="formSetOut.type">
              <el-radio :label="0">区域拉出</el-radio>
              <el-radio :label="1">整体拉出</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="toSetOutCancel">{{
            $t("lang_pack.commonPage.cancel")
          }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="toSetOutSubmit('formSetOut') "
            >{{ $t("lang_pack.commonPage.confirm") }}</el-button
          >
          <!-- 确认 -->
        </div>
      </el-drawer>

      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              border
              @header-dragend="crud.tableHeaderDragend()"
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              class="box-card1"
              min-height="523px"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="station_mo_id" label="id" />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="make_order"
                :label="$t('lang_pack.stationmo.makeOrder')"
              />
              <!-- 订单号 -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="dms"
                :label="$t('lang_pack.stationmo.dms')"
                width="140"
              />
              <!-- DMS号 -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="vin"
                :label="$t('lang_pack.flowonline.vin')"
              />
              <!-- vin -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="mo_work_order"
                :label="$t('lang_pack.stationmo.moWorkOrder')"
              />
              <!-- 生产订单顺序 -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="work_status"
                :label="$t('lang_pack.stationmo.workStatus')"
              />
              <!-- 生产状态 -->

              <el-table-column
                :label="$t('lang_pack.commonPage.operate')"
                align="center"
                fixed="right"
              >
                <!-- 操作 -->
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.set_sign === 'NONE'"
                    :disabled="scope.row.work_status === 'FINISH'"
                    slot="reference"
                    type="text"
                    size="small"
                    @click="toTableButPreSetOut(scope.row)"
                    >{{ $t("lang_pack.stationmo.preSetOutBut") }}</el-button
                  >
                  <!-- 预约拉出 -->
                  <el-button
                    v-if="scope.row.set_sign === 'PRE_SET_OUT'"
                    slot="reference"
                    type="text"
                    size="small"
                    @click="toTableButCancelPreSetOut(scope.row)"
                    >{{ $t("lang_pack.stationmo.cancelPreSetOutBut") }}</el-button
                  >
                  <!-- 取消预约拉出 -->
                  <el-button
                    v-if="scope.row.set_sign === 'NONE'"
                    slot="reference"
                    type="text"
                    size="small"
                    @click="toTableButSetOut(scope.row)"
                    >{{ $t("lang_pack.stationmo.setOutBut") }}</el-button
                  >
                  <!-- 直接拉出 -->
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>

        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <inoutItem
              ref="inoutItem"
              class="tableFirst box-card1"
              :station_code="currentStationCode"
              @toOutQuery="toOutQuery"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudStationMoOut from "@/api/pmc/stationflow/stationmoout";
import crudStation from "@/api/core/factory/sysStation";
import inoutItem from "./inoutItem";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import udOperation from "@crud/UD.operation";
import pagination from "@crud/Pagination";
const defaultForm = {};
export default {
  name: "StationMoOut",
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    inoutItem,
  },
  props: {},
  cruds() {
    return CRUD({
      title: "拉出",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "station_mo_id",
      // 排序
      sort: ["mo_work_order asc"],
      // CRUD Method
      crudMethod: { ...crudStationMoOut },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true,
      },
    });
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {},

      currentStationCode: "",

      // 工位数据
      stationData: [],
      //工作状态
      workStatusData: ["PLAN", "FINISH"],

      // 弹窗(预约拉出)
      dialogTitlePreSetOut: "",
      dialogVisbleSyncPreSetOut: false,
      formPreSetOut: {
        station_mo_id: "",
        station_code: "",
        make_order: "",
        dms: "",
        vin:"",
        item_project: "",
        set_date: "",
        set_marks: "",
        type: 0,
      },
      rulesPreSetOut: {
        set_date: [{ required: true, message: "请选择日期", trigger: "blur" }],
      },

      // 弹窗(直接拉出)
      dialogTitleSetOut: "",
      dialogVisbleSyncSetOut: false,
      formSetOut: {
        station_mo_id: "",
        station_code: "",
        make_order: "",
        dms: "",
        vin:"",
        item_project: "",
        set_marks: "",
        type: 0,
      },
      rulesSetOut: {},
    };
  },
  // 数据字典
  mounted: function () {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200;
    };
  },
  created: function () {
    this.queryStation();
  },
  methods: {
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get("userName"),
        station_attr: "Y",
        enable_flag: "Y",
        setinout_flag: "Y",
      };
      crudStation
        .lovStation(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },

    // 主页 查询
    toOutQuery() {
      this.crud.toQuery();
      this.$refs.inoutItem.crud.toQuery();
    },

    // 预约拉出:
    toBeforeClosePreSetOut(done) {
      // 新增弹出框(关闭前的回调)
      this.dialogVisbleSyncPreSetOut = false; // 弹出框隐藏
    },
    toTableButPreSetOut(data) {
      // Table编辑(单笔)
      this.formPreSetOut.station_mo_id = data.station_mo_id;
      this.formPreSetOut.station_code = data.station_code;
      this.formPreSetOut.make_order = data.make_order;
      this.formPreSetOut.dms = data.dms;
      this.formSetOut.vin = data.vin;
      this.formPreSetOut.item_project = data.item_project;
      this.formPreSetOut.set_date = "";
      this.formPreSetOut.set_marks = "";
      this.formPreSetOut.type = 0;

      this.dialogTitlePreSetOut = "预约拉出";
      this.dialogVisbleSyncPreSetOut = true; // 预约拉出弹出框
    },
    toTableButCancelPreSetOut(data) {
      this.$confirm(`该订单${data.make_order}将取消预约拉出，是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const save = {
            user_name: Cookies.get("userName"),
            station_mo_id: data.station_mo_id,
            set_sign: "NONE",
          };
          crudStationMoOut
            .cancelout(save)
            .then((res) => {
              const defaultDel = JSON.parse(JSON.stringify(res));
              if (defaultDel.code === 0) {
                this.$message({
                  message: "取消预约拉出成功",
                  type: "success",
                });

                // 查询
                this.crud.toQuery();
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: "info",
                });
              }
            })
            .catch(() => {
              this.$message({
                message: "取消预约拉出异常",
                type: "error",
              });
            });
        })
        .catch(() => {});
    },
    toPreSetOutCancel() {
      // 取消
      this.dialogVisbleSyncPreSetOut = false; // 预约拉出弹出框隐藏
    },
    toPreSetOutSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get("userName"),
            station_mo_id: this.formPreSetOut.station_mo_id,
            station_code: this.formPreSetOut.station_code,
            make_order: this.formPreSetOut.make_order,
            set_sign: "PRE_SET_OUT",
            set_date: this.formPreSetOut.set_date,
            set_marks: this.formPreSetOut.set_marks,
            type: this.formPreSetOut.type,
          };
          crudStationMoOut
            .setout(save)
            .then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res));
              if (defaultQuery.code === 0) {
                this.dialogVisbleSyncPreSetOut = false; // 预约拉出弹出框隐藏

                this.$message({
                  message: "预约拉出成功",
                  type: "success",
                });
                // 查询
                this.crud.toQuery();
                this.$refs.inoutItem.crud.toQuery();
              } else {
                this.$message({
                  message: defaultQuery.msg,
                  type: "info",
                });
              }
            })
            .catch(() => {
              this.$message({
                message: "预约拉出异常",
                type: "error",
              });
            });
        }
      });
    },

    // 直接拉出:
    toBeforeCloseSetOut(done) {
      // 新增弹出框(关闭前的回调)
      this.dialogVisbleSyncPreSetOut = false; // 弹出框隐藏
    },
    toTableButSetOut(data) {
      // Table编辑(单笔)
      this.formSetOut.station_mo_id = data.station_mo_id;
      this.formSetOut.station_code = data.station_code;
      this.formSetOut.make_order = data.make_order;
      this.formSetOut.dms = data.dms;
      this.formSetOut.vin = data.vin;
      this.formSetOut.item_project = data.item_project;
      this.formSetOut.set_marks = "";
      this.formSetOut.type = 0;

      this.dialogTitleSetOut = "直接拉出";
      this.dialogVisbleSyncSetOut = true; // 直接拉出弹出框
    },
    toSetOutCancel() {
      // 取消
      this.dialogVisbleSyncSetOut = false; // 直接拉出弹出框隐藏
    },
    toSetOutSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get("userName"),
            station_mo_id: this.formSetOut.station_mo_id,
            station_code: this.formSetOut.station_code,
            make_order: this.formSetOut.make_order,
            set_sign: "SET_OUT",
            set_date: "",
            set_marks: this.formSetOut.set_marks,
            type: this.formSetOut.type,
          };
          // 修改
          crudStationMoOut
            .setout(save)
            .then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res));
              if (defaultQuery.code === 0) {
                this.dialogVisbleSyncSetOut = false; // 直接拉出弹出框隐藏

                this.$message({
                  message: "直接拉出成功",
                  type: "success",
                });
                // 查询
                this.crud.toQuery();
                this.$refs.inoutItem.crud.toQuery();
              } else {
                this.$message({
                  message: defaultQuery.msg,
                  type: "info",
                });
              }
            })
            .catch(() => {
              this.$message({
                message: "直接拉出异常",
                type: "error",
              });
            });
        }
      });
    },

    [CRUD.HOOK.afterRefresh](crud) {
      this.currentStationCode = crud.query.station_code;
    },
  },
};
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
