<template>
  <el-card v-if="dialogVisible" shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="任务列表状态" :visible.sync="dialogVisible" :width="width" :before-close="handleClose">
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="145px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-10 col-12">
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="任务号:">
                      <el-input v-model="query.task_num" class="filter-item" />
                    </el-form-item>
                  </div>
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="钢板型号:">
                      <el-select v-model="query.model_type" clearable filterable>
                        <el-option
                          v-for="item in modelList"
                          :key="item.model_id"
                          :label="item.model_type"
                          :value="item.model_type"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="目标切割机:">
                      <el-input v-model="query.cut_code" class="filter-item" />
                    </el-form-item>
                  </div>
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="切割类型:">
                      <el-select v-model="query.cut_type" clearable filterable>
                        <el-option v-for="item in dict.CUT_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="任务状态:">
                      <el-select v-model="query.task_status" clearable filterable>
                        <el-option v-for="item in dict.PROD_TASK_STATUS" :key="item.id" :label="item.label" :value="item.value">
                          <span style="float: left">{{ item.label }}</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="当前位置:">
                      <el-input v-model="query.station_code" class="filter-item" />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormSecond formChild col-md-2 col-12">
                  <el-form-item>
                    <rrOperation />
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </el-card>
          <el-card shadow="never" class="wrapCard" style="margin-top: 10px;">
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              :height="height"
              :highlight-current-row="true"
              @selection-change="crud.selectionChangeHandler"
            >
              <!-- 任务号 -->
              <el-table-column
                :show-overflow-tooltip="true"
                min-width="80"
                align="center"
                prop="task_num"
                :label="$t('lang_pack.cuttingZone.TaskNumber')"
              />
              <!-- 钢板型号 -->
              <el-table-column
                :show-overflow-tooltip="true"
                min-width="80"
                align="center"
                prop="model_type"
                :label="$t('lang_pack.cuttingZone.SteelPlateModel')"
              />
              <!-- 切割类型 -->
              <el-table-column
                :show-overflow-tooltip="true"
                min-width="80"
                align="center"
                prop="cut_type"
                :label="$t('lang_pack.taskList.CutType')"
              >
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.CUT_TYPE[scope.row.cut_type] }}
                </template>
              </el-table-column>
              <!-- 目标切割机 -->
              <el-table-column
                :show-overflow-tooltip="true"
                min-width="80"
                align="center"
                prop="cut_code"
                :label="$t('lang_pack.taskList.TargetCuttingMachine')"
              />
              <!-- 任务状态 -->
              <el-table-column
                :show-overflow-tooltip="true"
                min-width="80"
                align="center"
                prop="task_status"
                :label="$t('lang_pack.taskList.TaskStatus')"
              >
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
                </template>
              </el-table-column>
              <!-- 当前工位 -->
              <el-table-column
                :show-overflow-tooltip="true"
                min-width="80"
                align="center"
                prop="now_station_code"
                :label="$t('lang_pack.cuttingZone.CurrentWorkstation')"
              />
            </el-table>
            <pagination />
          </el-card>
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'MAINPAGEMADAL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '任务列表状态',
      // 唯一字段
      idField: 'task_order',
      // 排序
      sort: ['task_order asc'],
      // CRUD Method
      crudMethod: { ...crudMainPage },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      },
      query: {
        onlyToday: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['CUT_TYPE', 'EXECUTE_STATUS', 'TASK_STATUS', 'PROD_TASK_STATUS'],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false,
      width: '80%',
      modelList: []
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  created() {
    this.getModelType()
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    },
    getModelType() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style scoped lang="less">
::v-deep .el-dialog {
    margin-top: 3vh !important;
}

.el-pagination {
    float: none !important;
    text-align: right;
}
</style>
