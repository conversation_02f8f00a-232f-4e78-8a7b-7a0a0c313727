
//导出execl格式
export function handleExport(data) {
  if (!data) {
    this.$message.warning("文件下载失败")
    return
  }
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }),)
  } else {
    let url = window.URL.createObjectURL(new Blob([data], { type: 'application/vnd.ms-excel' }))
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', '报警数据.xls')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link); //下载完成移除元素
    window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}
//导出json格式
export function handleJson(data) {

  if (!data) {
    this.$message.warning("文件下载失败")
    return
  }
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(new Blob([data]))
  } else {
    let url = window.URL.createObjectURL(new Blob([data]))
    // var blob = new Blob([data])
    // var reader = new FileReader();
    // reader.readAsText(blob, 'utf8');
    // reader.onload = function () {
    //   var content = JSON.parse(this.result)[0].flow_mod_main_des || '导出数据'; //这个就是解析出来的数据
      
    // }
    let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '主流程图模板维护数据导出.json')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}
export function format(shijianchuo){
  //shijianchuo是整数，否则要parseInt转换
  var time = new Date(shijianchuo);
  var y = time.getFullYear();
  var m = time.getMonth()+1;
  var d = time.getDate();
  var h = time.getHours();
  var mm = time.getMinutes();
  var s = time.getSeconds();
  return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
export function add0(m){return m<10?'0'+m:m }

