<template>
  <div :style="'height:'+ height + 'px'">
    <iframe id="iframeCardView" class="implantIframe" :src="src" frameborder="no" style="width: 100%;height: 100%" scrolling="no" border="0" />
  </div>
</template>
<script>
export default {
  props: {
    src: {
      type: String,
      required: true
    },
    height: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true
    }
  },
  mounted: function() {
    setTimeout(() => {
      this.loading = false
    }, 230)

    window.addEventListener('message', function(event) {
      // ?????????iframe?
    }, false)
  }
}
</script>
<style>
.implantIframe .fab-menu{
  display: none;
}
</style>
