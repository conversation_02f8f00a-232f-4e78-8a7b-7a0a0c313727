<template>
  <el-drawer id="stepDrawer" :title="nodeForm.step_mod_des" :modal="false" :append-to-body="false"
    :modal-append-to-body="false" :wrapper-closable="false" :visible.sync="drawerVisible" direction="rtl" size="650px"
    @closed="drawerClose">
    <el-tabs v-model="activeName">
      <el-tab-pane label="基础属性" name="first">
        <div id="scrollbar" :style="'height:' + height + 'px'">
          <el-scrollbar style="height: 100%">
            <el-form ref="nodeForm" class="el-form-wrap" :inline="true" :model="nodeForm" size="small"
              label-width="180px" :rules="rules" :disabled="readonly">
              <el-form-item v-if="1 == 0" display:none>
                <el-input v-model="nodeForm.id" display:none />
              </el-form-item>
              <el-form-item label="所属子流程" prop="flow_mod_sub_id">
                <el-select v-model="nodeForm.flow_mod_sub_id" filterable>
                  <el-option v-for="item in subData" :key="item.subId_stepId" :label="item.describe"
                    :value="item.subId_stepId" />
                </el-select>
              </el-form-item>
              <el-form-item label="步骤描述" prop="step_mod_des">
                <el-input v-model="nodeForm.step_mod_des" />
              </el-form-item>
              <el-form-item label="排序" prop="step_mod_index">
                <el-input v-model.number="nodeForm.step_mod_index" />
              </el-form-item>
              <el-form-item label="步骤类型">
                <el-select v-model="nodeForm.step_mod_attr">
                  <el-option
                    v-for="item in [{ label: '开始步骤', id: 'FIRST' }, { label: '结束步骤', id: 'LAST' }, { label: '常规步骤', id: 'NORMAL' }]"
                    :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="方法">
                <el-input v-model="nodeForm.step_mod_function_dll" />
              </el-form-item>
              <el-form-item v-if="atoUrl !== ''" label="方法[ATO]">
                <el-input v-model="nodeForm.step_mod_function_js"><el-button slot="append" icon="el-icon-edit-outline"
                    @click="() => openCodeEditor && openCodeEditor('node')" /></el-input>
              </el-form-item>
              <el-form-item label="OK时下一个步骤">
                <el-select v-model="nodeForm.ok_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="NG对应的步骤">
                <el-select v-model="nodeForm.ng_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="循环结束后下一步" prop="cycle_next_step_id_list">
                <el-select v-model="nodeForm.cycle_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>

              <el-form-item label="选择1下一步步骤" prop="choice1_next_step_id_list">
                <el-select v-model="nodeForm.choice1_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择2下一步步骤" prop="choice2_next_step_id_list">
                <el-select v-model="nodeForm.choice2_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择3下一步步骤" prop="choice3_next_step_id_list">
                <el-select v-model="nodeForm.choice3_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择4下一步步骤" prop="choice4_next_step_id_list">
                <el-select v-model="nodeForm.choice4_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择5下一步步骤" prop="choice5_next_step_id_list">
                <el-select v-model="nodeForm.choice5_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="取消下一步步骤">
                <el-select v-model="nodeForm.abort_next_step_id_list" filterable multiple>
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''"
                    :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item label="步骤执行驱动属性">
                <el-input v-model="nodeForm.step_mod_function_attr" />
              </el-form-item>
              <el-form-item label="重试次数" prop="ng_retry_count">
                <el-input v-model.number="nodeForm.ng_retry_count" />
              </el-form-item>
              <el-form-item label="限制时间(毫秒)" prop="limit_time">
                <el-input v-model.number="nodeForm.limit_time" />
              </el-form-item>
              <el-form-item label="控件X坐标">
                <el-input v-model="nodeForm.control_location_x" />
              </el-form-item>
              <el-form-item label="控件Y坐标">
                <el-input v-model="nodeForm.control_location_y" />
              </el-form-item>

              <el-form-item label="有效标识">
                <el-select v-model="nodeForm.enable_flag">
                  <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id"
                    :label="item.label" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="OK状态时颜色">
                <el-color-picker v-model="nodeForm.ok_control_color" />
              </el-form-item>
              <el-form-item label="NG状态时颜色">
                <el-color-picker v-model="nodeForm.ng_control_color" />
              </el-form-item>
              <el-form-item label="取消状态时颜色">
                <el-color-picker v-model="nodeForm.abort_control_color" />
              </el-form-item>
              <el-form-item label="重试状态时颜色">
                <el-color-picker v-model="nodeForm.retry_control_color" />
              </el-form-item>
              <el-form-item label="初始颜色">
                <el-color-picker v-model="nodeForm.color" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div v-if="!readonly" style="text-align: center">
              <el-button type="primary" plain @click="drawerClose">取消</el-button>
              <el-button type="primary" :loading="btnLoading" @click="handleClickSaveNode">保存</el-button>
            </div>
          </el-scrollbar>
        </div>
      </el-tab-pane>
      <el-tab-pane label="属性组" name="second" :disabled="this.nodeForm.subId_stepId === 0">
        <div id="scrollbar1" :style="'height:' + height + 'px'">
          <el-scrollbar style="height: 100%">
            <attrGroup v-if="attrGroupShow" ref="attrGroup" :client_id_list="client_id_list" :readonly="readonly"
              :step_mod_id="nodeForm.step_mod_id" :flow_main_id="flow_main_id" />
          </el-scrollbar>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="readonly" label="条件组" name="fourth">
        <conditionGroup v-if="conditionGroupShow" ref="conditionGroup" :flow_main_id="flow_main_id"
          :step_mod_id="nodeForm.step_mod_id" />
      </el-tab-pane>
    </el-tabs>

    <el-dialog append-to-body title="程序设计" :fullscreen="true" custom-class="code-dialog-height"
      :visible.sync="codeEditDialogVisible">
      <div slot="title">
        {{ flow_mod_main_des + "[" + nodeForm.step_mod_des + "]" }}
      </div>
      <div class="code-dialog-content">
        <elFrame v-if="codeEditDialogVisible" ref="codeEditor" :src="iframeSrc" />
      </div>
      <div class="code-dialog-footer">
        <el-button @click="codeEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleClickSaveNodeCode">保存</el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>
<script>
import Cookies from 'js-cookie'
import Vue from 'vue'

import { sel, add, edit } from '@/api/core/flow/rcsFlowModSubStep'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import attrGroup from '@/views/core/flow/mod/attr-group'
import elFrame from '@/components/Iframe'
import conditionGroup from '@/views/core/flow/main/condition-group'

const defaultForm = {
  name: null,
  id: null,
  subId_stepId: null,
  width: null,
  height: null,
  x: null,
  y: null,
  type: null,
  imgId: null,
  color: null,
  describe: null,
  strokeWidth: null,
  step_mod_id: 0,
  flow_mod_sub_id: 0,
  step_mod_code: '',
  step_mod_des: '',
  step_mod_index: 1,
  step_mod_attr: 'NORMAL',
  step_mod_way: 'AUTO',
  step_mod_function_dll: '',
  step_mod_function_js: '',
  step_mod_function_attr: '',
  ok_next_step_id_list: '',
  ok_wait_manual_flag: 'N',
  ok_manual_step_url: '',
  ok_manual_step_para: '',
  ng_retry_count: -1,
  ng_next_step_id_list: '',
  ng_wait_manual_flag: 'N',
  ng_manual_step_url: '',
  ng_manual_step_para: '',
  allow_abort_flag: 'N',
  allow_pass_flag: 'N',
  abort_next_step_id_list: '',
  control_ico: '',
  control_type: '-1',
  control_location_x: 0,
  control_location_y: 0,
  ok_control_color: '#37ED13',
  ok_wait_control_color: '#0E9A23',
  ng_control_color: '#EA1F1F',
  ng_wait_control_color: '#B91212',
  abort_control_color: '#F1930F',
  retry_control_color: '#15E4DD',
  limit_time: 0,
  cycle_next_step_id_list: '',
  enable_flag: 'Y',
  choice1_next_step_id_list: '',
  choice2_next_step_id_list: '',
  choice3_next_step_id_list: '',
  choice4_next_step_id_list: '',
  choice5_next_step_id_list: ''
}
export default {
  components: {
    attrGroup,
    conditionGroup,
    elFrame
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    flow_main_id: {
      type: [String, Number],
      default: -1
    },
    client_id_list: {
      type: [String, Number],
      default: ''
    },
    flow_mod_main_id: {
      type: [String, Number],
      default: -1
    },
    flow_mod_main_des: {
      type: String,
      default: ''
    }
  },
  data: function () {
    // 自定义验证
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    var checkOKWaitManualFlag = (rule, value, callback) => {
      if (this.nodeForm.ok_wait_manual_flag === 'Y') {
        if (value.length === 0) {
          return callback(new Error('不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    var checkNGWaitManualFlag = (rule, value, callback) => {
      if (this.nodeForm.ng_wait_manual_flag === 'Y') {
        if (value.length === 0) {
          return callback(new Error('不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      btnLoading: false,
      height: document.documentElement.clientHeight - 150,
      attrGroupShow: false,
      conditionGroupShow: false,
      codeEditorShow: false,
      customPopover: false,
      activeName: 'first',
      nodeForm: {},
      thisH: null,
      rules: {
        // 提交验证规则
        flow_mod_sub_id: [{ required: true, message: '请输选择所属子流程', trigger: 'blur' }],
        step_mod_des: [{ required: true, message: '请输入步骤描述', trigger: 'blur' }],
        step_mod_index: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        ok_manual_step_url: [{ required: true, validator: checkOKWaitManualFlag, trigger: 'blur' }],
        ng_manual_step_url: [{ required: true, validator: checkNGWaitManualFlag, trigger: 'blur' }],
        ng_retry_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        limit_time: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      subData: [],
      stepData: [],
      drawerVisible: false,
      codeEditDialogVisible: false,
      atoUrl: '',
      iframeSrc: ''
    }
  },
  // 数据字典
  watch: {
    visible: {
      immediate: true,
      deep: true,
      handler() {
        this.drawerVisible = this.visible
      }
    }
  },
  mounted() {
    window.addEventListener('resize', () => (this.height = document.documentElement.clientHeight - 150), false)
    window.addEventListener('message', this.handleIframeMessage, false)
    var queryParameter = {
      parameter_code: 'ATO_URL',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then((res) => {
        const resData = JSON.parse(JSON.stringify(res))
        if (resData.code === 0) {
          if (resData.data !== '') {
            this.atoUrl = resData.data[0].parameter_val
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleIframeMessage)
  },
  methods: {
    drawerClose() {
      this.attrGroupShow = false
      this.conditionGroupShow = false
      this.codeEditorShow = false
      this.$emit('update:visible', false)
    },
    editNode(node, thisH, flow_mod_main_id) {
      this.attrGroupShow = false
      this.conditionGroupShow = false
      this.codeEditorShow = false
      this.thisH = thisH
      this.subData = thisH.internalNodes.filter(item => item.type === 'sub' && item.subId_stepId !== 0)
      this.subData.push({ subId_stepId: 0, describe: '无' })
      this.subData.push({ subId_stepId: -1, describe: '结束全部' })
      this.stepData = thisH.internalNodes.filter(item => (item.type === 'step' || item.type === 'judge') && item.subId_stepId !== 0)
      this.stepData.push({ subId_stepId: 0, describe: '无', name: '' })
      this.stepData.push({ subId_stepId: -1, describe: '结束全部', name: '' })
      for (const key in defaultForm) {
        if (this.nodeForm.hasOwnProperty(key)) {
          this.nodeForm[key] = defaultForm[key]
        } else {
          Vue.set(this.nodeForm, key, defaultForm[key])
        }
      }
      this.nodeForm.id = node.id
      this.nodeForm.subId_stepId = node.subId_stepId
      this.nodeForm.step_mod_id = node.subId_stepId
      this.nodeForm.step_mod_index = parseInt(node.name.split('-')[1])
      this.nodeForm.type = node.type
      this.nodeForm.width = node.width
      this.nodeForm.height = node.height
      this.nodeForm.control_type = node.type
      this.nodeForm.control_location_x = parseInt(node.x)
      this.nodeForm.control_location_y = parseInt(node.y)
      this.nodeForm.control_ico = node.imgId
      this.nodeForm.color = node.color
      this.nodeForm.step_mod_des = node.describe
      this.nodeForm.strokeWidth = node.strokeWidth
      if (node.subId_stepId !== undefined && node.subId_stepId !== 0) {
        const query = {
          step_mod_id: node.subId_stepId
        }
        sel(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.nodeForm.step_mod_id = defaultQuery.data[0].step_mod_id
                this.nodeForm.flow_mod_sub_id = defaultQuery.data[0].flow_mod_sub_id
                this.nodeForm.step_mod_des = defaultQuery.data[0].step_mod_des
                this.nodeForm.step_mod_index = defaultQuery.data[0].step_mod_index
                this.nodeForm.step_mod_attr = defaultQuery.data[0].step_mod_attr
                this.nodeForm.step_mod_function_dll = defaultQuery.data[0].step_mod_function_dll
                this.nodeForm.step_mod_function_js = defaultQuery.data[0].step_mod_function_js
                this.nodeForm.step_mod_function_attr = defaultQuery.data[0].step_mod_function_attr
                this.nodeForm.ok_next_step_id_list = defaultQuery.data[0].ok_next_step_id_list === '' ? '' : defaultQuery.data[0].ok_next_step_id_list.split(',')
                this.nodeForm.ng_retry_count = defaultQuery.data[0].ng_retry_count
                this.nodeForm.ng_next_step_id_list = defaultQuery.data[0].ng_next_step_id_list === '' ? '' : defaultQuery.data[0].ng_next_step_id_list.split(',')
                this.nodeForm.abort_next_step_id_list = defaultQuery.data[0].abort_next_step_id_list === '' ? '' : defaultQuery.data[0].abort_next_step_id_list.split(',')
                this.nodeForm.ok_control_color = defaultQuery.data[0].ok_control_color
                this.nodeForm.ng_control_color = defaultQuery.data[0].ng_control_color
                this.nodeForm.abort_control_color = defaultQuery.data[0].abort_control_color

                this.nodeForm.retry_control_color = defaultQuery.data[0].retry_control_color
                this.nodeForm.limit_time = defaultQuery.data[0].limit_time
                this.nodeForm.cycle_next_step_id_list = defaultQuery.data[0].cycle_next_step_id_list === '' ? '' : defaultQuery.data[0].cycle_next_step_id_list.split(',')
                this.nodeForm.enable_flag = defaultQuery.data[0].enable_flag

                this.nodeForm.choice1_next_step_id_list = defaultQuery.data[0].choice1_next_step_id_list === '' ? '' : defaultQuery.data[0].choice1_next_step_id_list.split(',')
                this.nodeForm.choice2_next_step_id_list = defaultQuery.data[0].choice2_next_step_id_list === '' ? '' : defaultQuery.data[0].choice2_next_step_id_list.split(',')
                this.nodeForm.choice3_next_step_id_list = defaultQuery.data[0].choice3_next_step_id_list === '' ? '' : defaultQuery.data[0].choice3_next_step_id_list.split(',')
                this.nodeForm.choice4_next_step_id_list = defaultQuery.data[0].choice4_next_step_id_list === '' ? '' : defaultQuery.data[0].choice4_next_step_id_list.split(',')
                this.nodeForm.choice5_next_step_id_list = defaultQuery.data[0].choice5_next_step_id_list === '' ? '' : defaultQuery.data[0].choice5_next_step_id_list.split(',')
              }
            }
          })
          .catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
      }
      this.$nextTick(() => {
        this.attrGroupShow = true
        this.conditionGroupShow = true
        this.codeEditorShow = true
      })
    },
    handleClickSaveNode() {
      // 确定(修改)
      this.$refs['nodeForm'].validate(valid => {
        if (valid) {
          const subInfo = this.thisH.internalNodes.filter(item => item.type === 'sub' && item.subId_stepId === this.nodeForm.flow_mod_sub_id)
          console.log(subInfo, this.thisH.internalNodes)
          let subName = '1'
          if (subInfo !== undefined && subInfo.length > 0) {
            subName = subInfo[0].name
          }
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            ...this.nodeForm
          }
          save.ok_next_step_id_list = this.nodeForm.ok_next_step_id_list === '' ? '0' : this.nodeForm.ok_next_step_id_list.join(',')
          save.ng_next_step_id_list = this.nodeForm.ng_next_step_id_list === '' ? '0' : this.nodeForm.ng_next_step_id_list.join(',')
          save.abort_next_step_id_list = this.nodeForm.abort_next_step_id_list === '' ? '0' : this.nodeForm.abort_next_step_id_list.join(',')
          save.cycle_next_step_id_list = this.nodeForm.cycle_next_step_id_list === '' ? '0' : this.nodeForm.cycle_next_step_id_list.join(',')
          save.choice1_next_step_id_list = this.nodeForm.choice1_next_step_id_list === '' ? '0' : this.nodeForm.choice1_next_step_id_list.join(',')
          save.choice2_next_step_id_list = this.nodeForm.choice2_next_step_id_list === '' ? '0' : this.nodeForm.choice2_next_step_id_list.join(',')
          save.choice3_next_step_id_list = this.nodeForm.choice3_next_step_id_list === '' ? '0' : this.nodeForm.choice3_next_step_id_list.join(',')
          save.choice4_next_step_id_list = this.nodeForm.choice4_next_step_id_list === '' ? '0' : this.nodeForm.choice4_next_step_id_list.join(',')
          save.choice5_next_step_id_list = this.nodeForm.choice5_next_step_id_list === '' ? '0' : this.nodeForm.choice5_next_step_id_list.join(',')
          const that = this
          this.btnLoading = true
          // 新增
          if (this.nodeForm.step_mod_id === undefined || this.nodeForm.step_mod_id.length <= 0 || this.nodeForm.step_mod_id === 0) {
            add(save)
              .then(res => {
                this.btnLoading = false
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  that.$message({ message: '步骤新增成功', type: 'success' })
                  that.$emit(
                    'refreshChart',
                    {
                      id: that.nodeForm.id,
                      subId_stepId: parseInt(defaultQuery.result),
                      name: subName + '-' + that.nodeForm.step_mod_index,
                      type: that.nodeForm.type,
                      width: that.nodeForm.width,
                      height: that.nodeForm.height,
                      x: that.nodeForm.control_location_x,
                      y: that.nodeForm.control_location_y,
                      imgId: that.nodeForm.control_ico,
                      color: that.nodeForm.color,
                      describe: that.nodeForm.step_mod_des,
                      strokeWidth: that.nodeForm.strokeWidth
                    },
                    that.thisH
                  )
                  that.$emit('update:visible', false)
                } else if (defaultQuery.code === -1) {
                  that.$message({ message: defaultQuery.msg, type: 'info' })
                }
              })
              .catch(() => {
                this.btnLoading = false
                that.$message({ message: '步骤新增异常', type: 'error' })
              })
          } else {
            // 修改
            edit(save)
              .then(res => {
                this.btnLoading = false
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  that.$emit(
                    'refreshChart',
                    {
                      id: that.nodeForm.id,
                      subId_stepId: that.nodeForm.subId_stepId,
                      name: subName + '-' + that.nodeForm.step_mod_index,
                      type: that.nodeForm.type,
                      width: that.nodeForm.width,
                      height: that.nodeForm.height,
                      x: that.nodeForm.control_location_x,
                      y: that.nodeForm.control_location_y,
                      imgId: that.nodeForm.control_ico,
                      color: that.nodeForm.color,
                      describe: that.nodeForm.step_mod_des,
                      strokeWidth: that.nodeForm.strokeWidth
                    },
                    that.thisH
                  )
                  that.$emit('update:visible', false)
                  that.$message({ message: '步骤修改成功', type: 'success' })
                } else if (defaultQuery.code === -1) {
                  that.$message({ message: defaultQuery.msg, type: 'info' })
                }
              })
              .catch(() => {
                this.btnLoading = false
                that.$message({
                  message: '步骤修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    openCodeEditor(type) {
      if (!this.nodeForm.step_mod_function_js || this.nodeForm.step_mod_function_js === '') {
        this.$message.warning('请先输入方法[ATO]代码')
        return
      }
      this.iframeSrc = `${this.atoUrl}?id=${this.nodeForm.step_mod_id}&idx=${this.nodeForm.step_mod_index}&type=${type}&code=${this.nodeForm.step_mod_function_js}&name=${encodeURIComponent(this.nodeForm.step_mod_des)}`
      if (type === 'node') {
        this.iframeSrc += `&parentIds=${this.flow_mod_main_id},${this.nodeForm.flow_mod_sub_id}`
      } else if (type === 'branch') {
        this.iframeSrc += `&parentIds=${this.nodeForm.flow_mod_main_id}`
      }
      console.log(this.iframeSrc)
      this.codeEditDialogVisible = true
    },
    handleClickSaveNodeCode() {
      const iframe = this.$refs.codeEditor.$refs.iframe
      console.log(this.$refs, iframe)
      this.$nextTick(() => {
        if (iframe && iframe.contentWindow) {
          iframe.contentWindow.postMessage({ type: 'save' }, '*')
        } else {
          console.error('Iframe is not loaded or not available')
        }
      })
    },
    handleIframeMessage(event) {
      console.log('Received event message from iframe:', event)
      const data = event.data
      if (data.type === 'save') {
        console.log('Received event data message from iframe:', data)
        if (data.error) {
          this.$message.error(data.error)
        } else {
          this.codeEditDialogVisible = false
        }
      }
    }
  }
}
</script>
<style scoped>
::v-deep #el-drawer__title {
  padding: 20px 20px 0 20px !important;
}

::v-deep .el-drawer__body {
  padding: 0 20px !important;
}
</style>
<style lang="scss" scoped>
#scrollbar,
#scrollbar1,
#scrollbar2 {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;

    .el-scrollbar__view {
      height: 100%;
    }
  }
}

.el-form-wrap {
  // max-height: 460px;
  height: calc(100% - 100px);
  overflow: auto;
}

.el-form-wrap::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #ebeef5;
  cursor: pointer !important;
}

//设置div滑动条的样式
.el-form-wrap::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);
  background-color: #f6f9ff;
  cursor: pointer !important;
}

::v-deep .el-drawer__header {
  .el-icon-close:before {
    font-weight: bold;
    font-size: 15px;
    border: 2px solid #606266;
    border-radius: 50%;
    padding: 6px;
  }

}

.code-dialog-height .el-dialog__body {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-y: auto;
}

.code-dialog-content {
  top: 60px;
  right: 5px;
  bottom: 60px;
  left: 5px;
  overflow-y: auto;
  position: absolute;
}

.code-dialog-footer {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: auto;
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
}
</style>
