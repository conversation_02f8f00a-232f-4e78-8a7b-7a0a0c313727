<template>
    <div class="app-container">
        <el-row :gutter="20" >
            <el-col :span="12">
                <el-card >
                    <div slot="header" class="wrapTextSelect">
                        <span>库位管理</span>
                        <div class="wrapSearch">
                            <div class="inputSearch">
                                <el-button type="primary" icon="el-icon-upload2">盘库上传</el-button>
                            </div>
                        </div>
                    </div>
                    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
                        <el-form ref="ruleForm" class="el-form-wrap" :model="formRules" :rules="rules" size="small"
                            label-width="100px" :inline="true">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationNumber')">
                                <!-- 库位号 -->
                                <el-input v-model="formRules.stock_code" disabled clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.inventory')">
                                <!-- 库位量 -->
                                <el-input type="number" v-model.number="formRules.stock_count" disabled clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.maximumInventory')" prop="max_count">
                                <!-- 最大库存 -->
                                <el-input v-model="formRules.max_count" disabled clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskList.SteelPlateModel')" prop="model_id">
                                <!-- 钢板型号 -->
                                <el-select v-model="formRules.model_id" :disabled="formRules.model_flag === 'Y' || formRules.stock_count>0" clearable filterable>
                                    <el-option v-for="item in modelList" :key="item.model_id" :label="item.model_type"
                                        :value="item.model_id" />
                                    </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.sortingArea.Number')" prop="count">
                                <!-- 数量 -->
                                <el-input type="number" v-model.number="formRules.count"  @blur="BlurText($event)" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.batchNumber')"  v-if="dialogType==='1'">
                                <!-- 批次号 -->
                                <el-input v-model="formRules.lot_num"  clearable size="small" />
                            </el-form-item>
                            <el-divider />
                            <div style="text-align: center;width: 100%;">
                                <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
                                <!-- 取消 -->
                                <el-button type="primary" size="small" icon="el-icon-check" :loading="dialogLoading" @click="handleOk">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                                <!-- 确认 -->
                            </div>
                        </el-form>
                    </el-dialog>
                    <el-table border ref="table" v-loading="loading" :data="tableData" :row-key="row => row.id"
                        :highlight-current-row="highlightCurrentRow" :height="height">
                        <!-- 库存明细 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" label="库存明细">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small" icon="el-icon-share"
                                    @click="handleMudules(scope.row)">库存明细</el-button>
                            </template>
                        </el-table-column>
                        <!-- 库位号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="stock_code"
                            :label="$t('lang_pack.cuttingZone.StockCode')" />
                        <!-- 物料型号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="model_type"
                            :label="$t('lang_pack.cuttingZone.MaterialModel')" />
                        <!-- 库存数量 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="stock_count"
                            :label="$t('lang_pack.cuttingZone.StockCount')" />
                        <!-- 手动操作 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" min-width="150"
                            :label="$t('lang_pack.cuttingZone.ManualOperation')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small" icon="el-icon-share"
                                    @click="handMove('手动入库', scope.row, '1')">手动入库</el-button>
                                <el-button slot="reference" type="text" size="small" icon="el-icon-share"
                                    @click="handMove('手动出库', scope.row, '2')">手动出库</el-button>
                            </template>
                        </el-table-column>
                        <!-- 有效标识 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" label="有效标识">
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949"
                                    active-value="Y" inactive-value="N"
                                    @change="changeEnabled(scope.row, scope.row.enable_flag)" />
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                <el-card  style="height: 250px;margin: 10px 0;">
                    <div slot="header" class="wrapTextSelect">
                        <span>流程管理</span>
                    </div>
                    <div class="wrapRowss">
                        <el-row :gutter="12" class="wrapRowItem row" :style="{overflowX:'auto'}">
                            <el-col :span="24" class=" nomargin">
                                <div class="flow_main" v-for="item in flowTaskData" :key="item.me_flow_task_id">
                                    <div class="flow_title">{{ item.flow_main_des }}</div>
                                    <el-dropdown trigger="click" placement="bottom" class="flow_menu">
                                        <span type="text" class="text">...</span>
                                        <el-dropdown-menu slot="dropdown">
                                            <span>
                                                <el-dropdown-item>取消流程</el-dropdown-item>
                                            </span>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                    <div class="flow_info overflowText">时间：{{ item.start_date }}</div>
                                    <el-tooltip class="item" effect="dark" :content="item.task_info" placement="top">
                                        <div class="flow_info overflowText">信息：{{ item.task_info }}</div>
                                    </el-tooltip>
                                    <div class="flow_info">步骤：{{ item.step_mod_des }}&nbsp;&nbsp;<el-button type="text"
                                            class="flow_button" @click="opendFlowTask(item)">查看</el-button></div>
                                    <el-tooltip class="item" effect="dark" :content="item.log_msg" placement="top">
                                        <div class="flow_info overflowText">日志：{{ item.log_msg }}</div>
                                    </el-tooltip>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card >
                    <div slot="header" class="wrapTextSelect">
                        <span>任务管理</span>
                    </div>
                    <el-table border ref="table" v-loading="loading" :data="planTaskData" :row-key="row => row.id"
                        :highlight-current-row="highlightCurrentRow" :height="ordHeight">
                        <!-- 图纸信息 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" label="图纸信息">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small"
                                    @click="handleDialog(scope.row, '3')">图纸信息</el-button>
                            </template>
                        </el-table-column>
                        <!-- 任务号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="task_num"
                            :label="$t('lang_pack.cuttingZone.TaskNumber')" />
                        <!-- 执行顺序 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="task_order"
                            :label="$t('lang_pack.cuttingZone.ExecutionOrder')" />
                        <!-- 任务状态 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="task_status"
                            :label="$t('lang_pack.taskList.TaskStatus')" />
                        <!-- 钢板型号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="model_type"
                            :label="$t('lang_pack.taskList.SteelPlateModel')" />
                        <!-- 切割类型 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="cut_type"
                            :label="$t('lang_pack.taskList.CutType')">
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CUT_TYPE[scope.row.cut_type] }}
                            </template>
                        </el-table-column>
                        <!-- 调整顺序 -->
                        <el-table-column :show-overflow-tooltip="true" min-width="120" align="center"
                            :label="$t('lang_pack.cuttingZone.AdjustTheOrder')">
                            <template slot-scope="scope">
                                <el-button v-if="scope.$index !== 0" size="small" icon="el-icon-top" circle
                                    @click="handleUp(scope.$index, scope.row.mo_id)" />
                                <el-button v-if="scope.$index !== planTaskData.length - 1" size="small"
                                    icon="el-icon-bottom" circle @click="handleDown(scope.$index, scope.row.mo_id)" />
                            </template>
                        </el-table-column>
                        <!-- 过站信息 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" label="过站信息">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small"
                                    @click="handleDialog(scope.row, '1')">过站信息</el-button>
                            </template>
                        </el-table-column>
                        <!-- 是否有效 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" label="是否有效">
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949"
                                    active-value="Y" inactive-value="N"
                                    @change="changeEnabled2(scope.row, scope.row.enable_flag)" />
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12" class="warehouse">
                <el-card :style="{ 'height': abnormalHeight + 60 + 'px' }">
                    <div slot="header" class="wrapTextSelect">
                        <span>手动控制</span>
                    </div>
                    <el-col :span="24">
                        <el-descriptions :column="1">
                            <el-descriptions-item label="当前进行中的任务">{{ objData.task_num || '暂无数据'}}</el-descriptions-item>
                            <el-descriptions-item label="当前任务钢板型号">{{ objData.model_type || '暂无数据'}}</el-descriptions-item>
                            <el-descriptions-item label="当前任务类型">{{
                                dict.label.TASK_TYPE[objData.task_type] }}</el-descriptions-item>
                        </el-descriptions>
                        <div class="btn">
                            <el-button type="primary" style="height: 44px;" @click="reversePark">倒库</el-button>
                            <el-button type="primary" @click="Reissue">重新下发任务</el-button>
                            <el-button type="primary" @click="reRead">重新读取与<br>写入RFID</el-button>
                            <el-button type="primary" @click="cancelTask">取消当前任务</el-button>
                            <el-button type="primary" style="height: 44px;" @click="semiOperate">半自动操作</el-button>
                        </div>
                    </el-col>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card>
                    <div slot="header" class="wrapTextSelect">
                        <span>异常信息</span>
                    </div>
                    <el-table border ref="table" v-loading="loading" :data="abnormalData" :row-key="row => row.id"
                        :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                        <el-table-column align="center" v-for="(column, index) in abnormalColumns" :key="index"
                            :prop="column.prop" :label="column.label">
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
        <loadAreaOperModal v-if="loadAreaOperModalFlag" :mo_id="moId" ref="loadAreaOperModal"
            @ok="loadAreaOperModalFlag = false"></loadAreaOperModal>
        <inventory v-if="inventoryFlag" ref="inventory" :stock_id="stock_id" @ok="inventoryFlag = false" />
        <transit v-if="transitFlag" ref="transit" :task_num="taskNumber" @ok="transitFlag = false"></transit>
        <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="flowTaskDialogVisible" direction="rtl" size="100%">
            <flowTask v-if="flowTaskDialogVisible" ref="flowTask" :flow_task="currentFlowTask" :cel_api="cellIp + ':' + webapiPort" :mqtt_url="cellIp + ':' + mqttPort" @closeDrawer="flowTaskDialogVisible = false" />
        </el-drawer>
        <reverseModal v-if="reverseModal" ref="reverseModalFlag" @ok="reverseModal = false,getStockSel()"></reverseModal>
    </div>
</template>
<script>
import loadAreaOperModal from '../../modules/loadAreaOperModal.vue';
import crudWmsFmodStock from '@/api/dcs/core/wmsCbTable/wmsFmodStock'
import inventory from '../../modules/inventoryDetailsModel'//库存明细
import transit from '../../modules/transit'//过站明细
import reverseModal from '../../modules/reverseModal'//倒库
import flowTask from '@/views/core/flow/task/task'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import { selCellIP } from '@/api/core/center/cell'
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import Cookies from 'js-cookie'
import axios from 'axios'
export default {
    name: 'loadAreaOper',
    components: { loadAreaOperModal, inventory, transit ,flowTask,reverseModal},
    data() {
        return {
            height: 0,
            ordHeight: 0,
            abnormalHeight: 0,
            loading: false,
            highlightCurrentRow: false,
            rules: {
                lot_num: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
                model_type: [{ required: true, message: '请选择钢板型号', trigger: 'blur' }],
                count: [{ required: true, message: '请选择数量', trigger: 'blur' }]
            },
            objData: {
                task_num: '',
                model_type: '',
                task_type: ''
            },
            flowTaskData: [],
            tableData: [],
            orderData: [
                { id: 11, kcmx: 'MO20230506001', kwh: 'Q345B65001000', wlxh: '激光', kcsl: 'G6', sdcz: '已下达', sfyx: 'G6', state: 0, },
                { id: 2, kcmx: 'MO20230506002', kwh: 'Q345B65001001', wlxh: '等离子', kcsl: 'G12', sdcz: '已完成', sfyx: 'G12', state: 0 },
                { id: 3, kcmx: 'MO20230506003', kwh: 'Q345B65001002', wlxh: '激光', kcsl: 'G8', sdcz: '已下达', sfyx: 'G8', state: 1 },
                { id: 4, kcmx: 'MO20230506004', kwh: 'Q345B65001003', wlxh: '等离子', kcsl: 'G5', sdcz: '已完成', sfyx: 'G5', state: 0 },
                { id: 5, kcmx: 'MO20230506005', kwh: 'Q345B65001004', wlxh: '激光', kcsl: 'G10', sdcz: '已下达', sfyx: '96', state: 1 },
                { id: 6, kcmx: 'MO20230506006', kwh: 'Q345B65001005', wlxh: '等离子', kcsl: 'G17', sdcz: '已完成', sfyx: 'G15', state: 0 },
                { id: 7, kcmx: 'MO20230506007', kwh: 'Q345B65001006', wlxh: '等离子', kcsl: 'G12', sdcz: '已完成', sfyx: 'G12', state: 1 },
                { id: 8, kcmx: 'MO20230506008', kwh: 'Q345B65001007', wlxh: '激光', kcsl: 'G8', sdcz: '已下达', sfyx: 'G8', state: 1 },
                { id: 9, kcmx: 'MO20230506009', kwh: 'Q345B65001008', wlxh: '等离子', kcsl: 'G5', sdcz: '已完成', sfyx: 'G5', state: 0 },
                { id: 10, kcmx: 'MO202305060010', kwh: 'Q345B65001009', wlxh: '激光', kcsl: 'G10', sdcz: '已下达', sfyx: '96', state: 0 },
            ],
            abnormalData: [
                { id: 1, sbbh: 'TC1', bjjb: '一级报警', bjwb: '智能行车急停按钮按下', bjsj: '2023-05-16 14:38:35', },
                { id: 2, sbbh: 'TC2', bjjb: '二级报警', bjwb: 'R1工位网络通讯异常', bjsj: '2023-05-16 14:37:35' },
                { id: 3, sbbh: 'TC3', bjjb: '三级报警', bjwb: 'R2辊电机异常', bjsj: '2023-05-16 14:39:32' },
                { id: 4, sbbh: 'TC4', bjjb: '一级报警', bjwb: '智能行车急停按钮按下', bjsj: '2023-05-16 14:28:45' },
                { id: 5, sbbh: 'TC5', bjjb: '二级报警', bjwb: 'R2辊电机异常', bjsj: '2023-05-16 14:18:35' },
                { id: 6, sbbh: 'TC6', bjjb: '二级报警', bjwb: 'R1工位网络通讯异常', bjsj: '2023-05-16 14:48:35' },
                { id: 7, sbbh: 'TC7', bjjb: '三级报警', bjwb: 'R2辊电机异常', bjsj: '2023-05-16 14:37:33' },
            ],
            abnormalColumns: [
                { label: '设备编号', prop: 'sbbh' },
                { label: '报警级别', prop: 'bjjb' },
                { label: '报警文本', prop: 'bjwb' },
                { label: '报警时间', prop: 'bjsj' },
            ],
            modelList: [],
            inventoryFlag: false,
            stock_id: '',
            dialogType: '',
            dialogTitle: '',
            dialogVisible: false,
            form: {},
            formRules:{
                stock_group_code:'',
                stock_count:'',
                max_count:'',
                min_count:0,
                stock_code:'',
                model_id:'',
                count:'',
                lot_num:'',
                lot_number:'',
                model_flag:''
            },
            dialogLoading:false,
            rules:{
                lot_num:[{ required: true, message: '请选择批次号', trigger: 'blur' }],
                model_id:[{ required: true, message: '请选择钢板型号', trigger: 'blur' }],
                count:[{ required: true, message: '请选择数量', trigger: 'blur' }]
            },
            planTaskData: [],
            transitFlag: false,
            taskNumber: '',
            moId: '',
            loadAreaOperModalFlag: false,
            cellIp: '', // 单元IP
            webapiPort: '', // 单元API端口号
            mqttPort: '', // MQTT端口号
            flowTaskDialogVisible: false,
            currentFlowTask: {},
            timer:null,
            reverseModal:false,
        }
    },
    created() {
        this.getPower() //调整表格低分辨率下的几种情况
        this.getStockSel() //查询上料区库位List
        this.getModelType() //获取型号
        this.getPlanTask() //获取任务管理list
        this.getFeedCon() //获取手动控制接口
        this.getCellIp() //获取流程管理
    },
    // 数据字典
    dicts: ['CUT_TYPE', 'TASK_TYPE'],
    mounted: function () {
        const that = this
        this.timer = setInterval(function () {
            // that.getFlowTaskData()
        }, 5000)
        window.onresize = function temp() {
            that.getPower() //调整表格低分辨率下的几种情况
        }
    },
    beforeDestroy(){
        if(this.timer){
            clearInterval(this.timer)
        }
    },
    methods: {
        getPower(){
            const height = document.documentElement.clientHeight;
            const conditions = [
                { max: 620, height: 430, ordHeight: 155, abnormalHeight: 400 },
                { max: 700, height: 450, ordHeight: 175, abnormalHeight: 480 },
                { max: 800, height: 480, ordHeight: 205, abnormalHeight: 540 },
                { max: 900, height: 530, ordHeight: 255, abnormalHeight: 590 },
            ];
            const condition = conditions.find((condition) => height <= condition.max);
            if (condition) {
                this.height = height - condition.height;
                this.ordHeight = height - condition.ordHeight;
                this.abnormalHeight = height - condition.abnormalHeight;
            } else {
                this.height = height - 700;
                this.ordHeight = height - 425;
                this.abnormalHeight = height - 700;
            }
        },
        changeEnabled(data, val) {
            this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    crudWmsFmodStock.editEnableFlag({
                        user_name: Cookies.get('userName'),
                        stock_id: data.stock_id,
                        enable_flag: val
                    })
                        .then(res => {
                            if (res.code === 0) {
                                this.$message({ message: '修改成功', type: 'success' })
                            } else {
                                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                            }
                        })
                        .catch(ex => {
                            this.$message({ message: '操作失败：' + ex, type: 'error' })
                            data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                        })
                })
                .catch(() => {
                    data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                })
        },
        changeEnabled2(data, val) {
            this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    crudLoadAreaOper.editEnableFlag({
                        user_name: Cookies.get('userName'),
                        mo_id: data.mo_id,
                        enable_flag: val
                    })
                        .then(res => {
                            if (res.code === 0) {
                                this.$message({ message: '修改成功', type: 'success' })
                            } else {
                                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                            }
                        })
                        .catch(ex => {
                            this.$message({ message: '操作失败：' + ex, type: 'error' })
                            data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                        })
                })
                .catch(() => {
                    data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                })
        },
        getCellIp() {
            const query = {
                user_name: Cookies.get('userName'),
                cell_id: this.cellId,
                current_ip: window.location.hostname
            }
            selCellIP(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        const ipInfo = JSON.parse(defaultQuery.result)
                        this.cellIp = ipInfo.ip
                        this.webapiPort = ipInfo.webapi_port
                        this.mqttPort = ipInfo.mqtt_port
                        this.getFlowTaskData()
                    } else {
                        this.$message({ message: defaultQuery.msg, type: 'error' })
                    }
                })
                .catch(() => {
                    this.$message({ message: '查询异常', type: 'error' })
                })
        },
        getFlowTaskData() {
            const data = {
                line_section_code: 'FEED',
            }
            crudLoadAreaOper.flowTask(data)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        this.flowTaskData = defaultQuery.data || []
                    } else {
                        this.flowTaskData = []
                        this.$message({ message: defaultQuery.data.msg, type: 'warning' })
                    }
                })
                .catch(ex => {
                    this.flowTaskData = []
                    this.$message({ message: '查询异常：' + ex, type: 'error' })
                })
        },
        BlurText(e) {
            let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value);
            if (!boolean) {
                this.$message.warning("数量不能为空或正整数");
                e.target.value = "";
            };
        },
        handleDialog(row, type) {
            if (type === '1') {
                this.taskNumber = row.task_num
                this.transitFlag = true
                this.$nextTick(() => {
                    this.$refs.transit.dialogVisible = true
                })
            }
            if (type === '3') {
                this.moId = row.mo_id
                console.log(this.moId)
                this.loadAreaOperModalFlag = true
                this.$nextTick(() => {
                    this.$refs.loadAreaOperModal.title = '图纸信息'
                    this.$refs.loadAreaOperModal.type = '3'
                    this.$refs.loadAreaOperModal.dialogVisible = true
                })
            }

        },
        cancelTask() {
            this.$confirm(`确认取消当前任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {

                })
                .catch(() => { })
        },
        reversePark(){
            this.reverseModal = true
            this.$nextTick(()=>{
                this.$refs.reverseModalFlag.dialogVisible = true
            })
        },
        Reissue() {
            this.$confirm(`确认重新下发当前任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {

                })
                .catch(() => { })
        },
        reRead() {
            this.$confirm(`确认重新读取且写入RFID?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {

                })
                .catch(() => { })
        },
        semiOperate() {
            this.$confirm(`确认要启用半自动操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {

                })
                .catch(() => { })
        },
        handleMudules(row) {
            this.stock_id = row.stock_id
            this.inventoryFlag = true
            this.$nextTick(() => {
                this.$refs.inventory.dialogVisible = true
            })
        },
        handMove(title, row, flag) {
            console.log(row)
            this.dialogTitle = title
            this.dialogType = flag
            this.formRules.stock_code = row.stock_code
            this.formRules.stock_id = row.stock_id
            this.formRules.stock_group_code= row.stock_group_code,
            this.formRules.stock_count= row.stock_count,
            this.formRules.max_count= row.max_count,
            this.formRules.min_count= row.min_count,
            this.formRules.model_id= row.model_id,
            this.formRules.model_flag = row.model_flag
            this.$nextTick(()=>{
                this.dialogVisible = true
            })
        },
        handleClose() {
            this.$refs.ruleForm.resetFields()
            this.dialogLoading = false
            this.dialogVisible = false
        },
        // 获取型号方法
        getModelType() {
            const query = {
                userID: Cookies.get('userName')
            }
            crudFmodModel.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.modelList = defaultQuery.data
                    }
                }
            })
                .catch(() => {
                    this.$message({
                        message: '型号查询异常',
                        type: 'error'
                    })
                })
        },
        getStockSel() {
            crudLoadAreaOper.stockSel({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.tableData = defaultQuery.data || []
                    }
                } else {
                    this.tableData = []
                    this.$message({
                        message: '库位查询异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.tableData = []
                    this.$message({
                        message: '库位查询异常',
                        type: 'error'
                    })
                })
        },
        handleOk() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.dialogLoading = true
                    if(this.dialogType === '1'){//入库
                        if(this.formRules.count + this.formRules.stock_count > this.formRules.max_count){
                            this.$message.warning(`入库数量已超额且最多入库数量为${this.formRules.max_count - this.formRules.stock_count}`)
                            this.dialogLoading = false
                            return false
                        }
                        const data = {
                            stock_code:this.formRules.stock_code,
                            stock_id:this.formRules.stock_id,
                            count:this.formRules.count,
                            lot_num:this.formRules.lot_num,
                            model_id:this.formRules.model_id
                        }
                        crudWmsFmodStock.inStock(data).then((res)=>{
                            const defaultQuery = JSON.parse(JSON.stringify(res))
                            if (defaultQuery.code === 0) {
                                this.handleClose()
                                this.getStockSel()
                                this.$message({ message: '操作成功', type: 'success' })
                            } else {
                                this.$message({ message: defaultQuery.msg, type: 'warning' })
                            }
                        }).catch(err => {
                            this.dialogLoading = false
                            this.$message({ message: '手动录入异常：' + err, type: 'error' })
                        })
                    }
                    // 手动出库的传数量和stock_id 
                    if(this.dialogType === '2'){//出库
                        if(this.formRules.count >  (this.formRules.stock_count - this.formRules.min_count)){
                            this.$message.warning(`当前出库数量最多为${this.formRules.stock_count - this.formRules.min_count}`)
                            this.dialogLoading = false
                            return false
                        }
                        const data = {
                            stock_code:this.formRules.stock_code,
                            stock_id:this.formRules.stock_id,
                            count:this.formRules.count,
                            lot_num:this.formRules.lot_num,
                            model_id:this.formRules.model_id
                        }
                        crudWmsFmodStock.outStock(data).then((res)=>{
                            const defaultQuery = JSON.parse(JSON.stringify(res))
                            if (defaultQuery.code === 0) {
                                this.handleClose()
                                this.getStockSel()
                                this.$message({ message: '操作成功', type: 'success' })
                            } else {
                                this.$message({ message: defaultQuery.msg, type: 'warning' })
                            }
                        }).catch(err => {
                            this.dialogLoading = false
                            this.$message({ message: '手动出库异常：' + err, type: 'error' })
                        })
                    }
                } else {
                    return false;
                }
            });
        },
        getPlanTask() {
            crudLoadAreaOper.planTask({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.planTaskData = defaultQuery.data
                    }
                } else {
                    this.planTaskData = []
                    this.$message({
                        message: '任务状态查询异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.planTaskData = []
                    this.$message({
                        message: '任务状态查询异常',
                        type: 'error'
                    })
                })
        },
        // 上移
        handleUp(i, id) {
            crudLoadAreaOper.taskUp({ mo_id: id }).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    this.getPlanTask()
                }
            })
                .catch(() => {
                    this.$message({
                        message: '上移异常',
                        type: 'error'
                    })
                })
        },
        // 下移
        handleDown(i, id) {
            crudLoadAreaOper.taskDown({ mo_id: id }).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    this.getPlanTask()
                }
            })
                .catch(() => {
                    this.$message({
                        message: '下移异常',
                        type: 'error'
                    })
                })
        },
        getFeedCon() {
            crudLoadAreaOper.feedCon({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0 && defaultQuery.data.length > 0) {
                    this.objData = defaultQuery.data[0]
                }
            })
                .catch((err) => {
                    this.$message({
                        message: err.msg,
                        type: 'error'
                    })
                })
        },
        opendFlowTask(item) {
            this.currentFlowTask = item
            this.flowTaskDialogVisible = true
        }
    }

}
</script>
<style scoped lang="less">
.app-container {
    ::v-deep .el-card__header,
    ::v-deep .el-card__body {
        padding: 10px !important;
    }

    .inbOutNum {
        margin: 0 5px;
        color: #409eff;
        cursor: pointer;
    }

    .wrapTextSelect {
        display: flex;
        justify-content: space-between;
        align-items: center;

    }

    .flow_main {
        background-color: #f1f3ff;
        padding-top: 50px;
        border-radius: 5px;
        border-color: rgba(0, 0, 0, 0.09);
        box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
        transition: 0.2s;
    }

    .flow_main:hover {
        border-color: rgba(0, 0, 0, 0.09);
        box-shadow: 0 2px 8px rgb(191 200 220);
    }

    .flow_title {
        font-weight: bold;
        color: #ffffff;
        margin-top: -35px;
        //   position: fixed;
        cursor: pointer;
        background-image: url('~@/assets/images/label_bg.png');
        background-position: 0;
        background-repeat: no-repeat;
        height: 35px;
        line-height: 35px;
        padding-left: 28px;
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .flow_info {
        line-height: 25px;
        color: #333;
        font-size: 12px;
        padding: 0 10px;
    }

    .overflowText {
        // /* 1.溢出隐藏 */
        // overflow: hidden;
        // /* 2.用省略号来代替超出文本 */
        // text-overflow: ellipsis;
        // /* 3.设置盒子属性为-webkit-box  必须的 */
        // display: -webkit-box;
        // /* 4.-webkit-line-clamp 设置为2，表示超出2行的部分显示省略号，如果设置为3，那么就是超出3行部分显示省略号 */
        // -webkit-line-clamp: 2;
        // /* 5.字面意思：单词破坏：破坏英文单词的整体性，在英文单词还没有在一行完全展示时就换行  即一个单词可能会被分成两行展示 */
        // word-break: break-all;
        // /* 6.盒子实现多行显示的必要条件，文字是垂直展示，即文字是多行展示的情况下使用 */
        // -webkit-box-orient: vertical;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        width: 320px;
    }

    .flow_button {
        padding: 0;
        font-weight: 600;
        color: #00479d;
    }

    .flow_menu {
        float: right;
        margin-top: -55px;
        margin-right: 10px;
    }

    .flow_menu .text {
        color: #838585;
        font-weight: 600;
        font-size: 20px;
        height: 20px;
        cursor: pointer;
    }

    .wrapRowss {
        background: #ffffff;
        border-radius: 4px;

        .wrapRowItem {
            .nomargin {
                margin-bottom: 0;
                padding: 6px;
                white-space:nowrap;
                .flow_main{
                    display:inline-block;
                    width:330px;
                    margin-left:10px;
                    overflow:hidden;
                }
            }
        }
        .wrapRowItem::-webkit-scrollbar {
            width: 10px;
            height: 10px;
            background-color: #ebeef5;
            cursor: pointer !important;
        }
        .wrapRowItem::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(255,255,255,.3);
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
            background-color: #f6f9ff;
            cursor: pointer !important;
        }
        .wrapRowItem::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,.2);
            box-shadow: inset 0 0 5px rgba(0,0,0,.2);
            border-radius: 3px;
            background: #fff;
            cursor: pointer !important;
        }
        .wrapRowItem:first-child {
            margin-top: 0;
        }
    }

    .warehouse {
        ::v-deep .el-descriptions-item__cell {
            padding-bottom: 27px;

            .el-descriptions-item__label,
            .el-descriptions-item__content {
                color: #28b5ff;
                font-size: 20px;
            }
        }

        .btn {
            width: 100%;
            display: flex;
            justify-content: space-around;
            .el-button{
                width: 104px;
            }
        }

    }
}

@media screen and (max-height: 768px) {
    .app-container {
        .warehouse {
            ::v-deep .el-descriptions-item__cell {

                .el-descriptions-item__label,
                .el-descriptions-item__content {
                    font-size: 16px !important;
                }
            }
        }
    }
}</style>