<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-drawer append-to-body :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="650px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <!--表：rcs_logic_attr_item 属性-->
          <el-form-item :label="$t('lang_pack.logicfunc.subproject')" prop="logic_func_item_id">
            <!-- 子项目 -->
            <el-select v-model="form.logic_func_item_id" clearable filterable>
              <el-option v-for="item in logicAttrItemData" :key="item.logic_attr_item_id" :label="item.logic_attr_item_des" :value="item.logic_attr_item_id">
                <span>{{ item.logic_attr_item_code }}</span>
                <span>{{ item.logic_attr_item_des }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.value')" prop="logic_func_i_value">
            <!-- 值 -->
            <el-popover v-model="customPopover" placement="left" width="350">
              <div id="customPopoverDiv" style="height: 250px">
                <el-scrollbar style="height: 100%">
                  <el-tree :data="treeData" :props="defaultProps" :highlight-current="true" :render-content="renderContent"> ></el-tree>
                </el-scrollbar>
              </div>
              <el-input slot="reference" v-model="form.logic_func_i_value" />
            </el-popover>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.attribute1')" prop="attribute1">
            <!-- 属性1 -->
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.attribute2')" prop="attribute2">
            <!-- 属性2 -->
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.attribute3')" prop="attribute3">
            <!-- 属性3 -->
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.attribute4')" prop="attribute4">
            <!-- 属性4 -->
            <el-input v-model="form.attribute4" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.attribute5')" prop="attribute5">
            <!-- 属性5 -->
            <el-input v-model="form.attribute5" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :stripe="true" height="400px" :highlight-current-row="true" @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="logic_func_i_code" :label="$t('lang_pack.logicfunc.subproject')" sortable="custom" />
        <!-- 子项目 -->
        <el-table-column  :show-overflow-tooltip="true" prop="logic_func_i_des" :label="$t('lang_pack.logicfunc.subprojectDescription')" sortable="custom" />
        <!-- 子项目描述 -->
        <el-table-column  :show-overflow-tooltip="true" prop="logic_func_i_value" :label="$t('lang_pack.logicfunc.value')" />
        <!-- 值 -->
        <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
          <!-- 有效标识 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import { scadaClientTree } from '@/api/core/scada/client'
import { logicAttrItemLov } from '@/api/core/logic/logicAttrItem'

import { selLogicFuncItem, insLogicFuncItem, updLogicFuncItem, delLogicFuncItem } from '@/api/core/logic/logicFuncI'

export default {
  name: 'FUNCI',
  // 数据模型
  data() {
    return {
      customPopover: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      height: document.documentElement.clientHeight - 270,
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      // Table
      listLoadingTable: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],
      form: {
        logic_func_i_id: '',
        logic_func_g_id: '',
        logic_func_item_id: '',
        logic_func_i_value: '',
        attribute1: '',
        attribute2: '',
        attribute3: '',
        attribute4: '',
        attribute5: '',
        enable_flag: 'Y'
      },
      rules: {
        logic_func_item_id: [{ required: true, message: '请选择子项目', trigger: 'blur' }]
      },
      logicAttrItemData: [],
      logic_attr_group_id: '0',

      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      query: {
        logicFuncICodeDes: '',
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'logic_func_i_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  created: function() {
    this.toButQuery()
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 按钮：
    toButQuery(logic_func_g_id, logic_attr_group_id, queryContent) {
      if (logic_func_g_id === undefined) {
        this.form.logic_func_g_id = this.$parent.$attrs.logicfuncgid
      } else {
        this.form.logic_func_g_id = logic_func_g_id
      }
      if (logic_attr_group_id === undefined) {
        this.logic_attr_group_id = this.$parent.$attrs.logicattrgroupid
      } else {
        this.logic_attr_group_id = logic_attr_group_id
      }
      if (queryContent === undefined) {
        this.query.logicFuncICodeDes = ''
      } else {
        this.query.logicFuncICodeDes = queryContent
      }
      const query = {
        user_name: Cookies.get('userName'),
        logicFuncICodeDes: this.query.logicFuncICodeDes,
        logic_func_g_id: this.form.logic_func_g_id,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      this.listLoadingTable = true
      selLogicFuncItem(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.count > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
      this.queryLogicAttrItem()
      this.queryTag()
    },
    toButAdd() {
      this.form.logic_func_i_id = ''
      this.form.logic_func_item_id = ''
      this.form.logic_func_i_value = ''
      this.form.attribute1 = ''
      this.form.attribute2 = ''
      this.form.attribute3 = ''
      this.form.attribute4 = ''
      this.form.attribute5 = ''
      this.form.enable_flag = 'Y'

      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增属性'
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.logic_func_i_id = data.logic_func_i_id
      this.form.logic_func_g_id = data.logic_func_g_id
      this.form.logic_func_item_id = data.logic_func_item_id
      this.form.logic_func_i_value = data.logic_func_i_value
      this.form.attribute1 = data.attribute1
      this.form.attribute2 = data.attribute2
      this.form.attribute3 = data.attribute3
      this.form.attribute4 = data.attribute4
      this.form.attribute5 = data.attribute5
      this.form.enable_flag = data.enable_flag

      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改属性'
    },

    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            logic_func_i_id: this.form.logic_func_i_id,
            logic_func_g_id: this.form.logic_func_g_id,
            logic_func_item_id: this.form.logic_func_item_id,
            logic_func_i_value: this.form.logic_func_i_value,
            attribute1: this.form.attribute1,
            attribute2: this.form.attribute2,
            attribute3: this.form.attribute3,
            attribute4: this.form.attribute4,
            attribute5: this.form.attribute5,
            enable_flag: this.form.enable_flag
          }
          //  var logic_attr_group_des =this.logicAttrGroupData.filter(
          //         (item) => item.logic_attr_group_id + "" === this.form.logic_attr_group_id + ""
          //       )[0].logic_attr_group_des
          // 新增
          if (this.form.logic_func_i_id === undefined || this.form.logic_func_i_id.length <= 0) {
            insLogicFuncItem(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updLogicFuncItem(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除${data.logic_func_i_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            logic_func_i_id: data.logic_func_i_id
          }

          const that = this
          delLogicFuncItem(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                that.$emit('DelTreeNode', data.logic_func_id)
                // 查询
                this.toButQuery()
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'logic_func_i_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    },

    // 逻辑属性LOV
    queryLogicAttrItem() {
      const query = {
        user_name: Cookies.get('userName'),
        logic_attr_group_id: this.logic_attr_group_id
      }
      this.logicAttrItemData = []
      // 从后台获取到对象数组
      logicAttrItemLov(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.logicAttrItemData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    chooseTag(data) {
      this.form.logic_func_i_value = data.tag_id
      this.customPopover = false
    },
    renderContent(h, { node, data, store }) {
      if (data.level === 4) {
        return (
          <span class='custom-tree-node'>
            <span>{node.label}</span>
            <span>
              <el-button size='mini' type='text' on-click={() => this.chooseTag(data)}>
                选择
              </el-button>
            </span>
          </span>
        )
      } else {
        return (
          <span class='custom-tree-node'>
            <span>{node.label}</span>
          </span>
        )
      }
    },
    queryTag() {
      scadaClientTree({ logic_func_g_id: this.form.logic_func_g_id, isShowTag: 'Y' })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.length > 0) {
            this.treeData = []
            var a = {}
            a.level = 1
            a.label = '实例'
            a.children = defaultQuery.data
            this.treeData.push(a)
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}

.el-drawer {
  overflow-y: scroll;
}

#customPopoverDiv {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
}
.el-form-item--small .el-form-item__content > span:first-child {
  width: 100%;
}
</style>
