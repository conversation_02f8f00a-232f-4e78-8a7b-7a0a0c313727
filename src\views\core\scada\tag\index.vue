<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect">
            <span>标签定义</span>
            <!--表：sys_fmod_prod_line-->
            <el-select v-model="prod_line_id" size="small" @change="changeProdLine">
              <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
          </div>
          <div id="selectTree">
            <el-tree :data="treeData" :props="defaultProps" :highlight-current="true" @node-click="handleNodeClick" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="18" style="padding-left:0px">
        <el-card class="box-card1" :prodlineid="currentProdLineId" :stationdata="stationData" :clientid="currentClientId" :taggroupid="currenttagGroupId">
          <div slot="header" class="wrapTextSelect">
            <span>{{ rightHeaderTitle }}</span>
            <div class="wrapSearch">
              <div class="inputSearch">
                <el-input v-model="queryHeader.content" :placeholder="contentPlaceholder" class="filter-item inputItem" size="small" />
                <el-button class="filter-item buttonItem" size="small" type="primary" @click="handleQuery">搜索</el-button>
              </div>
              <el-button v-if="true" class="filter-item" size="small" type="primary" icon="el-icon-plus" plain @click="handleAddTag">新增</el-button>
            </div>
          </div>
          <scadaClient v-if="scadaClientShow" ref="scadaClient" @AddTreeNode="handleScadaClientAddTreeNode" @EditTreeNode="handleScadaClientEditTreeNode" @DelTreeNode="handleScadaClientDelTreeNode" />
          <scadaTagGroup v-if="scadaTagGroupShow" ref="scadaTagGroup" @AddTreeNode="handleScadaTagGroupAddTreeNode" @EditTreeNode="handleScadaTagGroupEditTreeNode" @DelTreeNode="handleScadaTagGroupDelTreeNode" />
          <scadaTag v-if="scadaTagShow" ref="scadaTag" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import { lovStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import scadaClient from '@/views/core/scada/tag/client'
import scadaTagGroup from '@/views/core/scada/tag/tagGroup'
import scadaTag from '@/views/core/scada/tag/tag'

import { scadaClientTree } from '@/api/core/scada/client'

export default {
  name: 'SCADA_CLIENT_TAG',
  components: { scadaClient, scadaTagGroup, scadaTag },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 600,
      // 产线数据
      prod_line_id: '',
      prodLineData: [],
      // 工位数据
      stationData: [],

      // 树
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentNode: [],

      // 右边 查询
      contentPlaceholder: '实例编码/描述',
      rightHeaderTitle: '实例列表',
      queryHeader: {
        content: ''
      },

      scadaClientShow: true,
      scadaTagGroupShow: false,
      scadaTagShow: false,
      currentProdLineId: '0',
      currentClientId: '0',
      currenttagGroupId: '0'
    }
  },

  mounted: function() {
    const that = this
    var dom = document.getElementById('selectTree')
    dom.style.height = (window.innerHeight - 150) + 'px'
    window.onresize = function temp() {
      dom.style.height = (window.innerHeight - 150) + 'px'
      that.height = document.documentElement.clientHeight - 600
    }
  },
  created: function() {
    // 加载 产线LOV
    const query = {
      userID: Cookies.get('userName')
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    // 更改产线
    changeProdLine(val) {
      this.currentProdLineId = val

      // 查询工位
      this.toLovStationQuery()

      // 加载 产线LOV
      const query = {
        userID: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId
      }
      scadaClientTree(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          this.treeData = []
          var a = {}
          a.level = 1
          a.label = '实例'
          a.children = defaultQuery.data
          this.treeData.push(a)
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 查询工位：
    toLovStationQuery() {
      // 加载 实例LOV
      this.stationData = []
      const query = {
        userID: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId
      }
      lovStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    // 树点击事件
    handleNodeClick(data) {
      console.log(data)
      this.currentNode = data
      this.queryHeader.content = ''

      if (data.level === 1) {
        this.scadaTagGroupShow = false
        this.scadaTagShow = false
        if (this.scadaClientShow) {
          this.$refs['scadaClient'].toButQuery(this.currentProdLineId, '', this.stationData)
        } else {
          this.scadaClientShow = true
        }
        this.rightHeaderTitle = '实例列表'
        this.contentPlaceholder = '实例编码/描述'
      } else if (data.level === 2) {
        this.scadaClientShow = false
        this.scadaTagShow = false
        this.currentClientId = data.client_id
        if (this.scadaTagGroupShow) {
          this.$refs['scadaTagGroup'].toButQuery(data.client_id, '')
        } else {
          this.scadaTagGroupShow = true
        }
        this.rightHeaderTitle = '标签组列表'
        this.contentPlaceholder = '组编码/描述'
      } else if (data.level === 3) {
        this.scadaClientShow = false
        this.scadaTagGroupShow = false
        this.currenttagGroupId = data.tag_group_id
        if (this.scadaTagShow) {
          this.$refs['scadaTag'].toButQuery(data.tag_group_id, '')
        } else {
          this.scadaTagShow = true
        }
        this.rightHeaderTitle = '标签列表'
        this.contentPlaceholder = '标签代码/描述'
      }
    },

    // 实例
    handleScadaClientAddTreeNode(client_id, client_code, client_des) {
      const item = this.treeData.filter(item => item.level === 1)[0]
      if (!item.children) {
        this.$set(item, 'children', [])
      }
      item.children.push({
        level: 2,
        client_id: client_id,
        client_code: client_code,
        client_des: client_des,
        label: client_des
      })
    },
    handleScadaClientEditTreeNode(client_id, client_code, client_des) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const items = children.filter(item => item.client_id + '' === client_id + '')[0]
      items.client_code = client_code
      items.client_des = client_des
      items.label = client_des
    },
    handleScadaClientDelTreeNode(client_id) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const index = children.findIndex(d => d.client_id + '' === client_id + '')
      children.splice(index, 1)
    },

    // Tag组
    handleScadaTagGroupAddTreeNode(client_id, tag_group_id, tag_group_code, tag_group_des) {
      if (!this.currentNode.children) {
        this.$set(this.currentNode, 'children', [])
      }
      this.currentNode.children.push({
        level: 3,
        client_id: client_id,
        tag_group_id: tag_group_id,
        tag_group_code: tag_group_code,
        tag_group_des: tag_group_des,
        label: tag_group_des
      })
    },
    handleScadaTagGroupEditTreeNode(client_id, tag_group_id, tag_group_code, tag_group_des) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const children1 = children.filter(item => item.client_id + '' === client_id + '')[0].children
      const items = children1.filter(item => item.tag_group_id + '' === tag_group_id + '')[0]
      items.tag_group_code = tag_group_code
      items.tag_group_des = tag_group_des
      items.label = tag_group_des
    },
    handleScadaTagGroupDelTreeNode(client_id, tag_group_id) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const children1 = children.filter(item => item.client_id + '' === client_id + '')[0].children
      const index = children1.findIndex(d => d.tag_group_id + '' === tag_group_id + '')
      children1.splice(index, 1)
    },

    // 搜索 按钮
    handleQuery() {
      if (this.scadaClientShow) {
        this.$refs['scadaClient'].toButQuery(this.currentProdLineId, this.queryHeader.content, this.stationData)
      } else if (this.scadaTagGroupShow) {
        this.$refs['scadaTagGroup'].toButQuery(this.currentClientId, this.queryHeader.content)
      } else if (this.scadaTagShow) {
        this.$refs['scadaTag'].toButQuery(this.currenttagGroupId, this.queryHeader.content)
      }
    },
    // 新增 按钮
    handleAddTag() {
      if (this.scadaClientShow) {
        this.$refs['scadaClient'].toButAdd()
      } else if (this.scadaTagGroupShow) {
        this.$refs['scadaTagGroup'].toButAdd()
      } else if (this.scadaTagShow) {
        this.$refs['scadaTag'].toButAdd()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card1 {
  min-height: calc(100vh - 70px);
  .el-card__header {
    padding: 8px;
    height: auto;
  }
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}
.wrapTextSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    white-space: nowrap;
    font-size: 14px;
    color: rgb(117, 117, 117);
    font-weight: bold;
    margin-right: 10px;
  }
}
.wrapSearch {
  display: flex;
  align-items: center;
  .inputSearch {
    display: flex;
    align-items: center;
    margin-right: 15px;
    .inputItem {
      .el-input__inner {
        width: 150px;
        border-radius: 4px 0 0 4px !important;
      }
    }
    .buttonItem {
      border-radius: 0 0.25rem 0.25rem 0;
      margin-left: -5px;
    }
  }
}
.el-tree--highlight-current {
  .el-tree-node__content {
    padding: 10px;
    height: auto;
  }
}
.el-tree-node__expand-icon {
  color: #79a0f1;
}
.el-tree-node__label {
  font-size: 12px;
}
.el-tree-node__content:hover {
  background-color: #e8efff;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e8efff;
}
//创建一个div来设置可以滚动
#selectTree{
    overflow-y: auto;
}
//修改div滚动条的宽高
#selectTree::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
}
//设置div滑动条的样式
#selectTree::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);
    background-color: #f6f9ff;
    cursor: pointer !important;
}
</style>
