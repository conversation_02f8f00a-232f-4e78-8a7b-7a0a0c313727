<template>
  <el-drawer :title="nodeForm.flow_mod_sub_des" :modal="false" :append-to-body="false" :modal-append-to-body="false" :wrapper-closable="false" :visible.sync="drawerVisible" direction="rtl" size="650px" style="overflow-y: auto" @closed="drawerClose">
    <div id="scrollbar" :style="'height:' + height + 'px'">
      <el-scrollbar style="height: 100%">
        <el-form ref="nodeForm" class="el-form-wrap" :inline="true" :model="nodeForm" size="small" label-width="180px" :rules="rules" :disabled="readonly">
          <el-form-item label="子流程描述" prop="flow_mod_sub_des">
            <el-input v-model="nodeForm.flow_mod_sub_des" />
          </el-form-item>
          <el-form-item label="排序" prop="flow_mod_sub_index">
            <el-input v-model.number="nodeForm.flow_mod_sub_index" />
          </el-form-item>
          <el-form-item label="流程类型" prop="flow_sub_attr">
            <el-select v-model="nodeForm.flow_sub_attr">
              <el-option v-for="item in [{ label: '开始子流程', id: 'FIRST' }, { label: '结束子流程', id: 'LAST' }, { label: '常规子流程', id: 'NORMAL' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="下一个子流程" prop="next_flow_mod_sub_id">
            <el-select v-model="nodeForm.next_flow_mod_sub_id" clearable>
              <el-option v-for="item in subData" :key="item.subId_stepId" :label="item.describe" :value="item.subId_stepId" />
            </el-select>
          </el-form-item>
          <el-form-item label="子流程结束">
            <el-select v-model="nodeForm.finish_all_flag">
              <el-option v-for="item in [{ label: '当前子流程结束后结束整个流程', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="子流程FunctionDll" prop="sub_mod_function_dll">
            <el-input v-model="nodeForm.sub_mod_function_dll" />
          </el-form-item>
          <el-form-item label="子流程程序属性" prop="sub_mod_function_attr">
            <el-input v-model="nodeForm.sub_mod_function_attr" />
          </el-form-item>

          <el-form-item label="有效标识">
            <el-select v-model="nodeForm.enable_flag">
              <el-option v-for="item in [{ label: '有效', id: 'Y' }, { label: '无效', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="控件X坐标">
            <el-input v-model="nodeForm.control_location_x" />
          </el-form-item>
          <el-form-item label="控件Y坐标">
            <el-input v-model="nodeForm.control_location_y" />
          </el-form-item>
          <el-form-item label="OK状态颜色">
            <el-color-picker v-model="nodeForm.ok_control_color" />
          </el-form-item>
          <el-form-item label="NG状态颜色">
            <el-color-picker v-model="nodeForm.ng_control_color" />
          </el-form-item>
          <el-form-item label="等待状态颜色">
            <el-color-picker v-model="nodeForm.wait_control_color" />
          </el-form-item>
          <el-form-item label="重试状态颜色">
            <el-color-picker v-model="nodeForm.retry_control_color" />
          </el-form-item>
          <el-form-item label="取消状态颜色">
            <el-color-picker v-model="nodeForm.abort_control_color" />
          </el-form-item>
          <el-form-item label="初始颜色">
            <el-color-picker v-model="nodeForm.color" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div v-if="!readonly" style="text-align: center">
          <el-button size="small" plain @click="drawerClose">取消</el-button>
          <el-button type="primary" size="small" :loading="btnLoading" @click="handleClickSaveNode">保存</el-button>
        </div>
      </el-scrollbar>
    </div>
  </el-drawer>
</template>
<script>
import { sel, add, edit } from '@/api/core/flow/rcsFlowModSub'
import Cookies from 'js-cookie'
import Vue from 'vue'
const defaultForm = {
  name: null,
  id: null,
  subId_stepId: null,
  width: null,
  height: null,
  x: null,
  y: null,
  type: null,
  imgId: null,
  color: null,
  describe: null,
  strokeWidth: null,
  flow_mod_sub_id: 0,
  flow_mod_main_id: 0,
  flow_mod_sub_des: '',
  flow_mod_sub_index: 1,
  flow_sub_attr: 'NORMAL',
  next_flow_mod_sub_id: '',
  control_location_x: 0,
  control_location_y: 0,
  ok_control_color: '#37ED13',
  ng_control_color: '#EA1F1F',
  abort_control_color: '#F1930F',
  wait_control_color: '#EEF91B',
  retry_control_color: '#15E4DD',
  finish_all_flag: 'N',
  sub_mod_function_dll: '',
  sub_mod_function_attr: '',
  enable_flag: 'Y'
}
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data: function() {
    // 自定义验证
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      btnLoading: false,
      height: document.documentElement.clientHeight - 100,
      nodeForm: {},
      thisH: null,
      rules: {
        // 提交验证规则
        flow_mod_sub_des: [{ required: true, message: '请输入子流程描述', trigger: 'blur' }],
        next_flow_mod_sub_id: [{ required: true, message: '请选择下一个子流程', trigger: 'blur' }],
        sub_mod_function_dll: [
          {
            required: true,
            message: '请输入子流程FunctionDll',
            trigger: 'blur'
          }
        ],
        flow_mod_sub_index: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      subData: [],
      drawerVisible: false
    }
  },
  watch: {
    visible: {
      immediate: true,
      deep: true,
      handler() {
        this.drawerVisible = this.visible
      }
    }
  },
  mounted() {
    window.addEventListener('resize', () => (this.height = document.documentElement.clientHeight - 100), false)
  },
  methods: {
    getClientHeight() {
      this.height = document.documentElement.clientHeight - 100
      console.log(this.height)
    },
    drawerClose() {
      // this.$emit("updateVisible");
      this.$emit('update:visible', false)
    },
    editNode(node, thisH, flow_mod_main_id) {
      this.thisH = thisH
      this.subData = thisH.internalNodes.filter(item => item.type === 'sub' && item.id !== node.id && item.subId_stepId !== 0)
      this.subData.push({ subId_stepId: 0, describe: '无' })
      this.subData.push({ subId_stepId: -1, describe: '结束全部' })
      for (const key in defaultForm) {
        if (this.nodeForm.hasOwnProperty(key)) {
          this.nodeForm[key] = defaultForm[key]
        } else {
          Vue.set(this.nodeForm, key, defaultForm[key])
        }
      }
      this.nodeForm.id = node.id
      this.nodeForm.subId_stepId = node.subId_stepId
      this.nodeForm.flow_sub_id = node.subId_stepId
      this.nodeForm.flow_mod_main_id = flow_mod_main_id
      this.nodeForm.flow_sub_index = parseInt(node.name)
      this.nodeForm.type = node.type
      this.nodeForm.width = node.width
      this.nodeForm.height = node.height
      this.nodeForm.control_location_x = parseInt(node.x)
      this.nodeForm.control_location_y = parseInt(node.y)
      this.nodeForm.color = node.color
      this.nodeForm.flow_mod_sub_des = node.describe
      this.nodeForm.strokeWidth = node.strokeWidth
      if (node.subId_stepId !== undefined && node.subId_stepId !== 0) {
        const query = {
          flow_mod_sub_id: node.subId_stepId
        }
        sel(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.nodeForm.flow_mod_sub_id = defaultQuery.data[0].flow_mod_sub_id
                this.nodeForm.flow_mod_main_id = defaultQuery.data[0].flow_mod_main_id
                this.nodeForm.flow_mod_sub_des = defaultQuery.data[0].flow_mod_sub_des
                this.nodeForm.flow_mod_sub_index = defaultQuery.data[0].flow_mod_sub_index
                this.nodeForm.flow_sub_attr = defaultQuery.data[0].flow_sub_attr
                this.nodeForm.next_flow_mod_sub_id = defaultQuery.data[0].next_flow_mod_sub_id

                this.nodeForm.ok_control_color = defaultQuery.data[0].ok_control_color
                this.nodeForm.ng_control_color = defaultQuery.data[0].ng_control_color
                this.nodeForm.abort_control_color = defaultQuery.data[0].abort_control_color
                this.nodeForm.wait_control_color = defaultQuery.data[0].wait_control_color
                this.nodeForm.retry_control_color = defaultQuery.data[0].retry_control_color
                this.nodeForm.finish_all_flag = defaultQuery.data[0].finish_all_flag
                this.nodeForm.sub_mod_function_dll = defaultQuery.data[0].sub_mod_function_dll
                this.nodeForm.sub_mod_function_attr = defaultQuery.data[0].sub_mod_function_attr
                this.nodeForm.enable_flag = defaultQuery.data[0].enable_flag
              }
            }
          })
          .catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
      }
    },
    handleClickSaveNode() {
      // 确定(修改)
      this.$refs['nodeForm'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            ...this.nodeForm
          }
          const that = this
          this.btnLoading = true
          // 新增
          if (this.nodeForm.flow_mod_sub_id === undefined || this.nodeForm.flow_mod_sub_id.length <= 0 || this.nodeForm.flow_mod_sub_id === 0) {
            add(save)
              .then(res => {
                this.btnLoading = false
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  that.$message({ message: '子流程新增成功', type: 'success' })
                  that.$emit(
                    'refreshChart',
                    {
                      id: that.nodeForm.id,
                      subId_stepId: parseInt(defaultQuery.result),
                      name: that.nodeForm.flow_mod_sub_index,
                      type: that.nodeForm.type,
                      width: that.nodeForm.width,
                      height: that.nodeForm.height,
                      x: that.nodeForm.control_location_x,
                      y: that.nodeForm.control_location_y,
                      imgId: '',
                      color: that.nodeForm.color,
                      describe: that.nodeForm.flow_mod_sub_des,
                      strokeWidth: that.nodeForm.strokeWidth
                    },
                    that.thisH
                  )
                  that.$emit('update:visible', false)
                } else if (defaultQuery.code === -1) {
                  that.$message({ message: defaultQuery.msg, type: 'warning' })
                }
              })
              .catch(() => {
                this.btnLoading = false
                that.$message({ message: '子流程新增异常', type: 'error' })
              })
          } else {
            // 修改
            edit(save)
              .then(res => {
                this.btnLoading = false
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  that.$emit(
                    'refreshChart',
                    {
                      id: that.nodeForm.id,
                      subId_stepId: that.nodeForm.subId_stepId,
                      name: that.nodeForm.flow_mod_sub_index,
                      type: that.nodeForm.type,
                      width: that.nodeForm.width,
                      height: that.nodeForm.height,
                      x: that.nodeForm.control_location_x,
                      y: that.nodeForm.control_location_y,
                      imgId: '',
                      color: that.nodeForm.color,
                      describe: that.nodeForm.flow_mod_sub_des,
                      strokeWidth: that.nodeForm.strokeWidth
                    },
                    that.thisH
                  )
                  that.$emit('update:visible', false)
                  that.$message({ message: '子流程修改成功', type: 'success' })
                } else if (defaultQuery.code === -1) {
                  that.$message({ message: defaultQuery.msg, type: 'warning' })
                }
              })
              .catch(() => {
                this.btnLoading = false
                that.$message({
                  message: '子流程修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    }
  }
}
</script>
<style scoped>
 ::v-deep #el-drawer__title{
  padding: 20px 20px 0 20px !important;
 }
 ::v-deep .el-drawer__body{
  padding:0 20px !important;
 }
</style>
<style lang="scss" scoped>
#scrollbar {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
    .el-scrollbar__view{
      height: 100%;
    }
  }
}
.el-form-wrap{
    // max-height: 460px;
    height: calc(100% -  100px);
    overflow: auto;
}
.el-form-wrap::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
}
//设置div滑动条的样式
.el-form-wrap::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);
    background-color: #f6f9ff;
    cursor: pointer !important;
}
</style>
