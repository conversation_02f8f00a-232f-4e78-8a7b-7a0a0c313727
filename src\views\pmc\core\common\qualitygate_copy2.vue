<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow>
      <el-row :gutter="24" class="elRowStyle" style="height: 320px;">
        <el-col :span="12">
          <el-card shadow="never">
            <div class="carInfoText carInfomargin">
              <div class="titleTip"><h4 class="el-faultDescs__title grade">本月 TOP</h4></div>
              <el-table
                :data="monthData"
                style="width: 100%"
                height="260px"
              >
                <el-table-column  type="index" label="#" width="60"/>
                <el-table-column 
                  prop="vehicleModel"
                  label="车型"
                  width="140"
                />
                <el-table-column prop="faultDesc" label="问题描述">
                  <template slot-scope="scope">
                    <div class="text-over">{{ scope.row.faultDesc }}</div>
                  </template>
                </el-table-column >
                <el-table-column 
                  prop="level"
                  label="等级"
                  width="60"
                />
                <el-table-column 
                  prop="frequency"
                  label="频次"
                  width="60"
                />
              </el-table>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card  shadow="never">
            <div class="carInfoText carInfomargin">
              <div class="titleTip"><h4 class="el-faultDescs__title grade">上周售后质量问题</h4></div>
              <el-table
                :data="qualityProData"
                style="width: 100%"
                height="260px"
              >
                <el-table-column type="index" label="#" width="40"/>
                <el-table-column 
                  prop="level"
                  label="等级"
                  width="60"
                />
                <el-table-column prop="faultDesc" label="问题描述">
                  <template slot-scope="scope">
                    <div class="text-over2">{{ scope.row.faultDesc }}</div>
                  </template>
                </el-table-column >
               
                <el-table-column 
                  prop="frequency"
                  label="频次"
                  width="60"
                />
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="24" class="elRowStyle" style="height: 320px;">
        <el-col :span="16">
          <el-card class="wraptop" shadow="never">
            <div class="carInfoQuestion">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-faultDescs__title grade">今日 TOP</h4></div>
                  <el-table
                    :data="dayData"
                    style="width: 100%"
                    height="260px"
                  >
                    <el-table-column  type="index" label="#" width="40"/>
                    <el-table-column 
                      prop="vehicleModel"
                      label="车型"
                      width="140"
                    />
                    <el-table-column prop="faultDesc" label="问题描述">
                      <template slot-scope="scope">
                        <div class="text-over3">{{ scope.row.faultDesc }}</div>
                      </template>
                    </el-table-column >
                    <el-table-column 
                      prop="level"
                      label="等级"
                      width="60"
                    />
                    <el-table-column 
                      prop="frequency"
                      label="频次"
                      width="60"
                    />
                  </el-table>
              </div>
            </div>
            <div class="carInfoQuestion">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-faultDescs__title grade">本周 TOP</h4></div>
                  <el-table
                    :data="weekData"
                    style="width: 100%"
                    height="260px"
                  >
                    <el-table-column 
                      type="index"
                      label="#"
                      width="40"
                    />
                    <el-table-column 
                      prop="vehicleModel"
                      label="车型"
                      width="140"
                    />
                    <el-table-column prop="faultDesc" label="问题描述">
                      <template slot-scope="scope">
                        <div class="text-over4">{{ scope.row.faultDesc }}</div>
                      </template>
                    </el-table-column >
                    <el-table-column 
                      prop="level"
                      label="等级"
                      width="60"
                    />
                    <el-table-column 
                      prop="frequency"
                      label="频次"
                      width="60"
                    />
                  </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-faultDescs__title grade">当月指标展示</h4></div>
              </div>
              <div style="width:100%;height:280px;display: flex;">
                <div id="numberOfIss" style="width: 50%;height: 280px" />
                <div id="finalAssembly" style="width: 50%;height: 280px" />
              </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getStationTime,getTopMonth,getLastWeek,getDpuProData,getfinAssProData } from '@/api/pmc/sysworkshopQualitygate'
import headlbg from '@/assets/images/headlbg.png'
export default {
  name: 'qualitygate',
  components: {

  },
  data() {
    return {
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      prod_line_des: '质量门',
      headlbg: headlbg,
      todayTopData: [],
      monthData: [],
      monthIndex:0,
      dayData:[],
      dayIndex:0,
      weekData:[],
      weekIndex:0,
      qualityProData:[],
      qualityProIndex:0,
      numberOfIss:null,
      finalAssembly:null
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.stationTimeTimer = setInterval(() => {
      this.initstationTime()
    }, 1000)

    // 获取本月数据
    this.getMonthData()
    //获取售后质量数据
    this.qualityData()
    // 获取今日数据
    this.getDayData()
    // 获取本周数据
    this.getWeekData()
    //获取DPU问题质量
    this.DpuData()
    // 获取总装下线台数
    this.finAssData()
    this.timer = setInterval(()=>{
      this.getMonthData()
      this.qualityData()
      this.getDayData()
      this.getWeekData()
      this.DpuData()
      this.finAssData()
    },25000)
  },
  mounted() {
    // 在通过mounted调用即可
    this.echartsInit()
  },
  // 销毁定时器
  beforeDestroy() {
    if (this.formatDate) {
      clearInterval(this.formatDate) // 在Vue实例销毁前，清除时间定时器
    }
    if(this.timer){
      clearInterval(this.timer)
    }
  },
  methods: {
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 初始化echarts
    echartsInit() {
      // var that = this
      // this.topcomparison = this.$echarts.init(document.getElementById('topcomparison'))
      // this.indicatorl = this.$echarts.init(document.getElementById('indicatorl'))
      // this.indicatorr = this.$echarts.init(document.getElementById('indicatorr'))
      // window.addEventListener('resize', function() {
      //   that.topcomparison.resize()
      //   that.indicatorl.resize()
      //   that.indicatorr.resize()
      // })
    },
    getMonthData(){
      var query = {
        type:'thisMonth',
        station: this.$route.query.station_code,
      }
      getTopMonth(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '获取本月Top异常',})
            return
        }
        res.data = [{
          vehicleModel: '牵引7',
          faultDesc: '放水阀>装配缺陷>装配不当>11111111>222222222',
          level: 'C',
          frequency: '16'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '左前组合灯总成>左下>装配缺陷>松动',
          level: 'C',
          frequency: '14'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '后围下部加强横梁左段>左上>连接缺陷>错边',
          level: 'B',
          frequency: '11'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
        },{
          vehicleModel: '牵引7',
          faultDesc: '放水阀>装配缺陷>装配不当',
          level: 'C',
          frequency: '16'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '左前组合灯总成>左下>装配缺陷>松动',
          level: 'C',
          frequency: '14'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '后围下部加强横梁左段>左上>连接缺陷>错边>4444444444444>55555555555555',
          level: 'B',
          frequency: '11'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙>111111111111>2222222222222',
          level: 'C',
          frequency: '10'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
      }]
        this.monthData = res.data.slice(this.monthIndex,this.monthIndex+5)
        this.monthIndex += 5
        if(this.monthIndex >=res.data.length){
          this.monthIndex = 0
        }
        this.$nextTick(()=>{
          const itemTextElements = document.querySelectorAll('.carInfoText .text-over');
          itemTextElements.forEach((element) => {
            element.classList.toggle('scrolling-text', element.scrollWidth > element.offsetWidth);
          });
        })
      }).catch(() => {
        this.$message({type:'error',message: '获取本月Top异常',})
      })
    },
    qualityData(){
      getLastWeek().then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '获取售后质量问题异常',})
            return
        }
        res.data = [
          {"faultDesc": "柴油机总成>标准件缺陷>螺栓/螺母滑牙>1111111111111>2222222222222222","frequency": "10", "level": "A"},
          {"faultDesc": "柴油机总成>标准件缺陷>螺栓/螺母滑牙","frequency": "9", "level": "B"},
          {"faultDesc": "柴油机总成>标准件缺陷>螺栓/螺母滑牙","frequency": "8", "level": "C"},
          {"faultDesc": "雨刮器及洗涤器安装图>噪音缺陷>异响","frequency": "7", "level": "D"},
          {"faultDesc": "柴油机总成>标准件缺陷>螺栓/螺母滑牙","frequency": "6", "level": "A"},
          {"faultDesc": "雨刮器及洗涤器安装图>噪音缺陷>异响","frequency": "5", "level": "B"},
          {"faultDesc": "柴油机总成>标准件缺陷>螺栓/螺母滑牙>33333333333333333>444444444444444444","frequency": "4", "level": "C"},
          {"faultDesc": "雨刮器及洗涤器安装图>噪音缺陷>异响","frequency": "3", "level": "D"},
          {"faultDesc": "后围下部加强横梁左段>左上>连接缺陷>错边>","frequency": "2", "level": "A"},
          {"faultDesc": "后围下部加强横梁左段>左上>连接缺陷>错边>","frequency": "1", "level": "B"},
        ]
        this.qualityProData = res.data.slice(this.qualityProIndex,this.qualityProIndex+5)
        this.qualityProIndex += 5
        if(this.qualityProIndex >=res.data.length){
          this.qualityProIndex = 0
        }
        this.$nextTick(()=>{
          const itemTextElements = document.querySelectorAll('.carInfoText .text-over2');
          itemTextElements.forEach((element) => {
            element.classList.toggle('scrolling-text', element.scrollWidth > element.offsetWidth);
          });
        })
      }).catch(() => {
        this.$message({type:'error',message: '获取售后质量问题异常',})
      })
    },
    getDayData(){
      var query = {
        type:'today',
        station: this.$route.query.station_code,
      }
      getTopMonth(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '获取今日数据异常',})
            return
        }
        res.data = [{
          vehicleModel: '牵引7',
          faultDesc: '放水阀>装配缺陷>装配不当>11111111>222222222',
          level: 'C',
          frequency: '16'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '左前组合灯总成>左下>装配缺陷>松动',
          level: 'C',
          frequency: '14'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '后围下部加强横梁左段>左上>连接缺陷>错边',
          level: 'B',
          frequency: '11'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
        },{
          vehicleModel: '牵引7',
          faultDesc: '放水阀>装配缺陷>装配不当',
          level: 'C',
          frequency: '16'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '左前组合灯总成>左下>装配缺陷>松动',
          level: 'C',
          frequency: '14'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '后围下部加强横梁左段>左上>连接缺陷>错边>4444444444444>55555555555555',
          level: 'B',
          frequency: '11'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙>111111111111>2222222222222',
          level: 'C',
          frequency: '10'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
      }]
        this.dayData = res.data.slice(this.dayIndex,this.dayIndex+5)
        this.dayIndex += 5
        if(this.dayIndex >=res.data.length){
          this.dayIndex = 0
        }
        this.$nextTick(()=>{
          const itemTextElements = document.querySelectorAll('.carInfoText .text-over3');
          itemTextElements.forEach((element) => {
            element.classList.toggle('scrolling-text2', element.scrollWidth > element.offsetWidth);
          });
        })
      }).catch(() => {
        this.$message({type:'error',message: '获取今日数据异常',})
      })
    },
    getWeekData(){
      var query = {
        type:'thisWeek',
        station: this.$route.query.station_code,
      }
      getTopMonth(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '获取本周数据异常',})
            return
        }
        res.data = [{
          vehicleModel: '牵引7',
          faultDesc: '放水阀>装配缺陷>装配不当>11111111>222222222猜>5544856244>',
          level: 'C',
          frequency: '16'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '左前组合灯总成>左下>装配缺陷>松动',
          level: 'C',
          frequency: '14'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '后围下部加强横梁左段>左上>连接缺陷>错边',
          level: 'B',
          frequency: '11'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
        },{
          vehicleModel: '牵引7',
          faultDesc: '放水阀>装配缺陷>装配不当',
          level: 'C',
          frequency: '16'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '左前组合灯总成>左下>装配缺陷>松动',
          level: 'C',
          frequency: '14'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '后围下部加强横梁左段>左上>连接缺陷>错边>4444444444444>55555555555555',
          level: 'B',
          frequency: '11'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙>111111111111>2222222222222',
          level: 'C',
          frequency: '10'
        }, {
          vehicleModel: '牵引7',
          faultDesc: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
          level: 'C',
          frequency: '10'
      }]
        this.weekData = res.data.slice(this.weekIndex,this.weekIndex+5)
        this.weekIndex += 5
        if(this.weekIndex >=res.data.length){
          this.weekIndex = 0
        }
        this.$nextTick(()=>{
          const itemTextElements = document.querySelectorAll('.carInfoText .text-over4');
          itemTextElements.forEach((element) => {
            element.classList.toggle('scrolling-text2', element.scrollWidth > element.offsetWidth);
          });
        })
      }).catch(() => {
        this.$message({type:'error',message: '获取本周数据异常',})
      })
    },
    DpuData(){
      const query = {
        station: this.$route.query.station_code,
      }
      getDpuProData(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '获取DPU数据异常',})
            return
        }
        res.data = [
            {"code": "T1H4461210","num": "100","name": "牵引6号车"},
            {"code": "T1H4461220","num": "99","name": "牵引7号车"},
            {"code": "T1H4461230","num": "98","name": "牵引8号车"},
            {"code": "T1H4461240","num": "97","name": "牵引9号车"},
            {"code": "T1H4461250","num": "96","name": "牵引10号车"},
        ]
        this.numberOfIss = this.$echarts.init(document.getElementById('numberOfIss'))
        let option = {
            legend: {
                data: ['DPU'],
                top: 20,
                right: '4%',
                textStyle: {
                    color: '#747474',
                },
            },
            tooltip: {
                //   trigger: "item", //默认效果
                //柱状图加阴影
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                    label: {
                        show: true,
                    },
                },
            },
            grid: {
                top: '30%',
                left: '5%',
                right: '5%',
                bottom: '8%',
                containLabel: true,
            },
            color: ['#5B9BD5'],
            xAxis: {
                type: 'category',
                data: res.data.map(e=>{return e.name.split('号')[0]}),
                axisLine:{
				           show:false
				        },
                axisTick: {
                  show: false
                }
            },
            yAxis: {
                type: 'value',
                axisLine:{
				           show:false
				        },
                axisTick: {
                  show: false
                }
            },
            series: [
                {
                    name: 'DPU',
                    data: res.data.map(e=>{return e.num}),
                    barWidth: 15,
                    type: 'bar',
                    itemStyle: {
                      normal: {
                          label: {
                              show: true, //开启显示
                              position: 'top', //顶部显示
                              textStyle: {
                                  //数值样式
                                  color: 'black',
                                  fontSize: 12,
                              },
                            },
                        },
                    },
                },
            ],
        };
        this.numberOfIss.setOption(option)
      }).catch(() => {
        this.$message({type:'error',message: '获取DPU数据异常'})
      })
    },
    finAssData(){
      const query = {
        station: this.$route.query.station_code,
      }
      getfinAssProData(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '获取总装下线异常'})
            return
        }
        res.data = [
            {"code": "101","name": "设计","count": "30"},
            {"code": "101","name": "零部件","count": "18"},
            {"code": "101","name": "制造","count": "10"},
        ]
        this.finalAssembly = this.$echarts.init(document.getElementById('finalAssembly'))
        var seriesData = []
        res.data.forEach(e=>{
          seriesData.push({name:e.name,value:e.count})
        })
        var option = {
              color: ['#5C9EDB','#F37825','#A5A5A5'],
              legend: {
                  top: 20,
                  align: 'left',
                  data: res.data.map(e=>{return e.name}),
              },
              series: [{
                  name: '需求类型占比',
                  type: 'pie',
                  center: ['50%', '55%'],
                  radius: '60%',
                  label: {
                      normal: {
                          show: false,
                      },
                  },
                  labelLine: {
                      show: false,
                      length: 0,
                      length2: 0,
                  },
                  label: {
                      normal: {
                          show: true,
                          position: 'inside',
                          formatter: '{value|{c}}',
                          rich: {
                              value: {
                                  fontSize: 20,
                                  color:'#ffffff',
                              },
                          },
                      }
                  },
                  data: seriesData,
              }],
          };
          this.finalAssembly.setOption(option);
      }).catch(() => {
        this.$message({type:'error',message: '获取总装下线异常'})
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .m-slide{
  background: none !important;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.wraptext p::before{
    content: '';
    width: 10px;
    height: 10px;
    background-color: #ff0000;
    display: block;
    border-radius: 50%;
    margin-right: 10px;
}
.cardStyle{
    margin: 0;
    padding: 0;
    border-radius: 0;
}
.cardheadbg{
  background-color: #031c45;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
.carInfo{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .carInfoText{
    flex: 1;
  }
  .carInfoImg{
    width: 120px;
    img{
      width: 100%;
    }
  }
}
::v-deep .carInfoTable{
  .el-table--small .el-table__cell {
    padding: 5px 0 !important;
}
}
.carInfoVideo{
height: 264px;
    background-color: #333333;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
  img{
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.dialogVideo{
    position: fixed;
    z-index: 999;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
.carInfodialogVideo{
  // height: 265px;
  background-color: #333333;
      background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}
// ::v-deep .el-faultDescs__title::before{
//   content: '';
//   width: 10px;
//   height: 10px;
//   background-color: #ffffff;
//   display: inline-block;
//   margin-right: 8px;
//   border-radius: 25px;
//       margin-left: 8px;
// }
::v-deep .el-faultDescs__title{
  display: flex;
    align-items: center;
    font-size: 20px;
    color: #ffffff;
}
::v-deep .el-table th {
    background-color: #0070c0 !important;
    color: #ffffff;
    font-weight: 700;
    font-size: 19px;
}
::v-deep .el-table__row td,::v-deep .el-table__row td{
    font-size: 12px;
    font-weight: 700;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background:#6b9ef9 !important;
  color: #ffffff !important;
}
::v-deep .el-faultDescs-item__container .el-faultDescs-item__label,::v-deep .el-faultDescs-item__container .el-faultDescs-item__content{
  font-size: 16px;
    color: #333333;
    font-weight: 700;
}
::v-deep .my-label,.labelNum{
  color: #ff0000 !important;
}
::v-deep .el-card__body {
    padding: 0px !important;
}
.carInfoQuestion{
  width: 50%;
}
.fullwdith{
  width: 100% !important;
}
img[src=""] {
    opacity: 0;
  }
.wraptop{
  ::v-deep .el-card__body{
    display: flex;
    justify-content: space-between;
  }
}
.titleTip{
  display: flex;
  justify-content: center;
  margin: 10px 0;
  .grade{
    margin: 0;
    padding: 10px;
    background-color: #0070c0;
    border-radius: 6px;
    min-width: 180px;
    display: flex;
    justify-content: center;
}
}

::v-deep .el-card{
  border: 0;
}
::v-deep .el-faultDescs__header{
  background-color: #6b9ef9;
  padding: 10px 0;
  border-radius: 0 25px 25px 0 !important;
}
.carInfomargin{
  margin: 0 5px;
}
:deep(.el-table td.el-table__cell div){
  font-size: 28px;
  line-height: 28px;
}


::v-deep .el-table__body-wrapper{
  .el-table__row td .cell {
    overflow: hidden;
    white-space: nowrap;
    .text-over,.text-over2,.text-over3,.text-over4{
      width: 98%;
    }
  }
  .scrolling-text {
    animation: scroll-text 15s linear infinite;
    animation-delay: 3s;
  }
  .scrolling-text2 {
    animation: scroll-text2 5s linear infinite;
    animation-delay: 1s;
  }
 }
@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes scroll-text2 {
  0% {
    transform: translateX(0);
  }
  
  100% {
    transform: translateX(-240%);
  }
}
</style>
