<template>
  <div>
    <div style="margin-top: 10px; text-align: right">
      <el-button type="primary" @click="handleSendInfo('PLAN')"
        >重新拧紧</el-button
      >
      <el-button type="primary" @click="handleSendInfo('NG_PASS')"
        >NG越过</el-button
      >
      <roleCheck
        v-if="roleCheckShow"
        ref="roleCheck"
        :role_user_id="userId"
        :role_func_code="role_func_code"
        :paramsCode="paramsCode"
        @roleCheck="roleCheck"
      />
    </div>
  </div>
</template>

<script>
import roleCheck from "@/views/core/hmi/roleCheck";
export default {
  components: { roleCheck },
  props: {},
  // 数据模型
  data() {
    return {
      order_status: "",
      roleCheckShow: false,
      userId: "",
      role_func_code: "",
      paramsCode: "Hmi_ShaftManualCheckPwd",
    };
  },
  mounted: function () {},
  created: function () {},
  methods: {
    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      this.roleCheckShow = false;
      if (roleFuncCode === "shaftManual") {
        if (status === "OK") {
          this.$emit("mesShaftManualJudge", "NG_PASS");
        }
      }
    },
    handleSendInfo(order_status) {
      if (order_status === "NG_PASS") {
        this.roleCheckShow = true;
        this.role_func_code = "shaftManual";
        return;
      } else {
        this.$emit('mesShaftManualJudge', order_status)
      }
    }
  },
};
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
