<template>
  <el-container>
    <el-aside width="70px" style="background-color: #faf8f9; padding: 0px">
      <div class="tool tool_return" @click="$emit('closeDrawer')">
        <div class="label">
          {{ $t('lang_pack.commonPage.back') }}
          <!-- 返回 -->
        </div>
      </div>
      <!-- <div class="tool tool_save" @click="$refs.chart.save()">
        <div class="label">保存</div>
      </div>
      <div class="tool tool_copy">
        <div class="label">复制</div>
      </div>
      <div class="tool tool_sub" draggable @dragend="handleToolDragEnd($event, 'sub')">
        <div class="label">流程</div>
      </div>
      <div class="tool tool_step" draggable @dragend="handleToolDragEnd($event, 'step')">
        <div class="label">步骤</div>
      </div>
      <div class="tool tool_judge" draggable @dragend="handleToolDragEnd($event, 'judge')">
        <div class="label">条件</div>
      </div>
      <div class="tool tool_label" draggable @dragend="handleToolDragEnd($event, 'label')">
        <div class="label">标签</div>
      </div> -->
    </el-aside>
    <el-container style="border-left: 2px solid #e7e7e7">
      <el-header>
        {{ flow_mod_main_des }}
      </el-header>
      <el-main>
        <el-scrollbar ref="scrollbar" :width="'100%'" :height="height">
          <Flowchart
            ref="chart"
            v-loading="loading"
            :nodes="nodes"
            :connections="connections"
            :width="'100%'"
            :height="height"
            :readonly="true"
            element-loading-text="拼命绘制流程图中"
            @editnode="handleEditNode"
            @dblclick="handleDblClick"
            @editconnection="handleEditConnection"
            @save="handleChartSave"
            @delnode="handleDelNode"
          />
        </el-scrollbar>
        <div style="position: absolute;height:100%;top:0;right:0" :style="{width:rcsFlowSubVisible ? '650px':''}">
          <RcsFlowSub
            ref="rcsFlowSub"
            :readonly="true"
            :visible.sync="rcsFlowSubVisible"
            :node-form.sync="nodeForm.target"
            @refreshChart="handleRefreshChart"
          />
        </div>
        <div style="position: fixed;width:650px;height:100%;top:0;right:0" :style="{width:rcsFlowSubStepVisible? '650px':''}">
          <RcsFlowStep
            ref="rcsFlowStep"
            :client_id_list="client_id_list"
            :flow_main_id="flow_main_id"
            :readonly="true"
            :visible.sync="rcsFlowSubStepVisible"
            :node-form.sync="nodeForm.target"
            @refreshChart="handleRefreshChart"
          />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
/* eslint-disable no-unused-vars */

import Cookies from 'js-cookie'
import Flowchart from '@/components/FlowChart/index'
import RcsFlowSub from '@/views/core/flow/mod/sub'
import RcsFlowStep from '@/views/core/flow/mod/step'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
import { sel, saveChart } from '@/api/core/flow/rcsFlowModMain'
import { del as delSub } from '@/api/core/flow/rcsFlowModSub'
import { del as delStep } from '@/api/core/flow/rcsFlowModSubStep'
export default {
  components: {
    Flowchart,
    RcsFlowSub,
    RcsFlowStep
  },
  props: {
    flow_main_id: {
      type: [String, Number],
      default: -1
    },
    client_id_list: {
      type: [String, Number],
      default: ''
    },
    flow_mod_main_id: {
      type: [String, Number],
      default: -1
    },
    flow_mod_main_des: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 60,
      nodes: [],
      connections: [],
      nodeForm: { target: null },
      connectionForm: { target: null, operation: null },
      loading: false,
      dialogLabel: false,
      flow_label: '',
      flowLabelPlaceholder: '',
      rcsFlowSubVisible: false,
      rcsFlowSubStepVisible: false,
      scrollLeft: 0,
      scrollTop: 0
    }
  },
  watch: {
    flow_mod_main_id: {
      immediate: true,
      deep: true,
      handler() { }
    },
    flow_mod_main_des: {
      immediate: true,
      deep: true,
      handler() { }
    }
  },
  mounted() {
    const that = this
    this.handleScroll()
    window.onresize = () => {
      return (() => {
        that.height = document.documentElement.clientHeight - 60
      })()
    }
  },
  created() {
    this.LoadFlowChart()
  },
  methods: {
    handleScroll() {
      const scrollbarEl = this.$refs.scrollbar.wrap
      scrollbarEl.onscroll = () => {
        this.scrollLeft = scrollbarEl.scrollLeft
        this.scrollTop = scrollbarEl.scrollTop
      }
    },
    handleDblClick(position) { },
    handleDelNode(type, id) {
      if (type === 'sub' && id !== 0) {
        delSub({
          flow_mod_sub_id: id
        })
          .then(res => {
            const defaultDel = JSON.parse(JSON.stringify(res))
            if (defaultDel.code === 0) {
              this.$message({
                message: '删除子流程成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: defaultDel.msg,
                type: 'error'
              })
            }
          })
          .catch(() => {
            this.$message({
              message: '删除子流程异常',
              type: 'error'
            })
          })
      } else if (type === 'step' && id !== 0) {
        delStep({ step_mod_id: id })
          .then(res => {
            const defaultDel = JSON.parse(JSON.stringify(res))
            if (defaultDel.code === 0) {
              this.$message({
                message: '删除流程步骤成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: defaultDel.msg,
                type: 'error'
              })
            }
          })
          .catch(() => {
            this.$message({
              message: '删除流程步骤异常',
              type: 'error'
            })
          })
      }
    },
    async handleChartSave(nodes, connections) {
      const save = {
        user_name: Cookies.get('userName'),
        flow_mod_main_id: this.flow_mod_main_id,
        flow_mod_chart: '{"nodes":' + JSON.stringify(nodes) + ',"connections":' + JSON.stringify(connections) + '}'
      }
      // 修改
      saveChart(save)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '流程图保存成功', type: 'success' })
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'warning' })
          }
        })
        .catch(() => {
          this.$message({
            message: '保存异常',
            type: 'error'
          })
        })
    },
    handleRefreshChart(node, thisH) {
      this.$refs.chart.init(node, thisH)
      this.$refs.chart.save()
    },
    handleEditNode(node, thisH) {
      this.rcsFlowSubStepVisible = false
      this.rcsFlowSubVisible = false
      this.nodeForm.target = node
      if (node.type === 'sub') {
        this.rcsFlowSubVisible = true
        this.$nextTick(() => {
          this.$refs.rcsFlowSub.editNode(node, thisH, this.flow_mod_main_id)
        })
      } else if (node.type === 'step' || node.type === 'judge') {
        this.rcsFlowSubStepVisible = true
        this.$nextTick(() => {
          this.$refs.rcsFlowStep.editNode(node, thisH, this.flow_mod_main_id)
        })
      } else if (node.type === 'label') {
        this.flow_label = node.describe === '标签' ? '' : node.describe
        this.flowLabelPlaceholder = node.describe
        this.$nextTick(() => {
          this.dialogLabel = true
        })
      }
    },
    handleEditConnection(connection) {
      // this.connectionForm.target = connection;
      // this.connectionDialogVisible = true;
    },
    LoadFlowChart() {
      this.loading = true
      sel({
        user_name: Cookies.get('userName'),
        flow_mod_main_id: this.flow_mod_main_id
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              if (defaultQuery.data[0].flow_mod_chart !== '') {
                const flow_chart = JSON.parse(defaultQuery.data[0].flow_mod_chart)
                this.nodes = flow_chart.nodes
                this.connections = flow_chart.connections
              } else {
                this.nodes = []
                this.connections = []
              }
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
          this.nodes = []
          this.connections = []
          this.loading = false
        })
    },
    handleToolDragEnd(event, type) {
      if (type === 'sub') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          x: event.clientX - 100 + this.scrollLeft,
          y: event.clientY - 90 + this.scrollTop,
          name: '1',
          type: 'sub',
          width: 170,
          height: 85,
          imgId: '108',
          color: '#FFFFFF',
          describe: '子流程',
          strokeWidth: 1
        })
      } else if (type === 'step') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          x: event.clientX - 100,
          y: event.clientY - 90,
          name: '1-1',
          type: 'step',
          width: 150,
          height: 70,
          imgId: '105',
          color: '#FFFFFF',
          describe: '步骤',
          strokeWidth: 1
        })
      } else if (type === 'judge') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          x: event.clientX - 100,
          y: event.clientY - 60,
          name: '1-1',
          type: 'judge',
          width: 150,
          height: 70,
          imgId: '105',
          color: '#FFFFFF',
          describe: '条件',
          strokeWidth: 1
        })
      } else if (type === 'label') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          x: event.clientX - 70,
          y: event.clientY - 60,
          name: '1',
          type: 'label',
          width: 10,
          height: 10,
          imgId: '105',
          color: '#FFFFFF',
          describe: '标签',
          strokeWidth: 1
        })
      }
    }
  }
}
</script>
<style scoped>
.el-drawer__wrapper {
  position: absolute !important;
}
.tool {
  width: 70px;
  height: 50px;
  text-align: center;
  margin-top: 5px;
  border: 1px solid #ffffff;
  color: #7c7c7c;
  border-radius: 5px;
  cursor: pointer;
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: 23px 5px;
  -moz-user-select: none;
  /*火狐*/

  -webkit-user-select: none;
  /*webkit浏览器*/

  -ms-user-select: none;
  /*IE10*/

  -khtml-user-select: none;
  /*早期浏览器*/

  user-select: none;
}

.tool .label {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-top: 25px;
}

.tool_return {
  background-image: url('~@/assets/images/flow/return_grey.png');
}

.tool_return:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/return_white.png');
}

.tool_save {
  background-image: url('~@/assets/images/flow/save_grey.png');
}

.tool_save:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/save_white.png');
}

.tool_copy {
  background-size: 18px;
  background-position: 25px 5px;
  background-image: url('~@/assets/images/flow/copy_grey.png');
}

.tool_copy:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/copy_white.png');
}

.tool_sub {
  background-image: url('~@/assets/images/flow/sub_grey.png');
}

.tool_sub:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/sub_white.png');
}

.tool_step {
  background-image: url('~@/assets/images/flow/step_grey.png');
}

.tool_step:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/step_white.png');
}

.tool_judge {
  background-size: 15px;
  background-position: 25px 3px;
  background-image: url('~@/assets/images/flow/judge_grey.png');
}

.tool_judge:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/judge_white.png');
}

.tool_label {
  background-image: url('~@/assets/images/flow/label_grey.png');
}

.tool_label:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/label_white.png');
}

.title {
  margin-top: 10px;
  margin-bottom: 0;
}

.subtitle {
  margin-bottom: 10px;
}

.el-header {
  background-color: #f6f6f6;
  color: #333;
  font-weight: 600;
  font-size: 30px;
  text-align: center;
  line-height: 60px;
}

.el-main {
  background-color: #f1f1f1;
  color: #333;
  padding: 0px;
}</style>
