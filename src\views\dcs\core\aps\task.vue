<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号：">
                <!-- 任务号： -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="序列号:">
                <!-- 序列号 -->
                <el-input v-model="query.serial_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码:">
                <!-- 物料编码 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务类型:">
                <!-- 任务类型 -->
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.APS_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- <div class="formChild col-md-4 col-12">
              <el-form-item label="零件图号:">
                零件图号
                <el-input v-model="query.material_draw" clearable size="small" />
              </el-form-item>
            </div> -->
            <div class="formChild col-md-4 col-12">
              <el-form-item label="型号:">
                <!-- 型号 -->
                <el-select v-model="query.model_type" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_type"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="切割机类型:">
                <!-- 切割机类型 -->
                <el-select v-model="query.cut_type" clearable filterable>
                  <el-option
                    v-for="item in dict.CUT_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="生产任务状态:">
                <!-- 生产任务状态 -->
                <el-select v-model="query.task_status" clearable filterable>
                  <el-option
                    v-for="item in dict.PROD_TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <!-- <template slot="left">
                    <el-button size="small" type="primary" icon="el-icon-search" plain round>
                        查询计划任务
                    </el-button>
                    <el-button size="small" type="primary" icon="el-icon-search" plain round>
                        查询执行任务
                    </el-button>
                    <el-button size="small" type="primary" icon="el-icon-search" plain round>
                        查询历史任务
                    </el-button>
                </template> -->
        <template slot="right">
          <el-button
            size="small"
            type="warning"
            icon="el-icon-download"
            plain
            round
            @click="importDialogVisible = true"
          >
            导入任务
          </el-button>
          <el-button size="small" type="success" icon="el-icon-upload2" plain round @click="crud.doExport">
            下载模板
          </el-button>
          <el-button size="small" type="success" icon="el-icon-sort" plain round>
            同步任务
          </el-button>
          <el-button size="small" type="warning" icon="el-icon-check" plain round>
            一键排产
          </el-button>
          <el-button size="small" type="warning" icon="el-icon-upload2" plain round @click="exportForWork">
            导出报工文件
          </el-button>
          <el-button size="small" type="primary" icon="el-icon-tickets" @click="releaseOrder">
            手动发布订单
          </el-button>
          <el-button size="small" type="primary" icon="el-icon-s-order" @click="batchResolve">
            批量解析文件
          </el-button>
          <el-badge :value="rcsEventtableData.length" class="item">
            <el-button size="small" type="primary" icon="el-icon-warning" @click="rcsEventDialogtVisible = true">
              报警信息查询
            </el-button>
          </el-badge>
          <!-- <el-button
            size="small"
            type="primary"
            @click="manualRelease()"
          >放行
          </el-button> -->
        </template>
      </crudOperation>
      <!--导入BOM-->
      <el-dialog
        :fullscreen="false"
        :show-close="true"
        :close-on-press-escape="false"
        :modal-append-to-body="false"
        title="导入订单"
        width="400px"
        :visible.sync="importDialogVisible"
      >
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit1"
            :accept="uploadAccept1"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept1 }}文件</div>
          </el-upload>
          <el-input v-if="isUpLoadError" v-model="errorMsg" type="textarea" :rows="5" />
          <div style="text-align: center;margin-top:10px">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-check"
              :loading="upLoading"
              @click="toButDrawerUpload"
            >导入</el-button>
          </div>
        </div>
      </el-dialog>
      <el-dialog
        :fullscreen="false"
        :show-close="true"
        :close-on-press-escape="false"
        :modal-append-to-body="false"
        title="手动发布"
        width="1200px"
        :visible.sync="releaseDialogVisible"
      >
        <el-button size="small" type="primary" style="float:right;margin-bottom:10px;" @click="confirmRelease">
          确认发布
        </el-button>
        <el-table
          ref="table"
          v-loading="releaseLoading"
          border
          size="small"
          :data="tableData"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :height="height"
          :highlight-current-row="true"
          @selection-change="releaseSelectionChangeHandler"
        >
          <el-table-column type="selection" width="55" fixed />
          <!-- 任务号 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="task_num"
            :label="$t('lang_pack.taskTable.taskNumber')"
            align="center"
          />
          <!-- 型号 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="model_type"
            :label="$t('lang_pack.taskList.SteelPlateModel')"
            align="center"
          />
          <!-- 分配切割机 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="cut_code"
            :label="$t('lang_pack.taskTable.cutterbar')"
            align="center"
          >
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ getProdLineDes(scope.row.cut_code) }}
            </template>
          </el-table-column>
          <!-- JSON文件名称 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="json_name"
            :label="$t('lang_pack.taskTable.JSONFileName')"
            width="110"
            align="center"
          />
          <!-- NC文件名称 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="nc_name"
            :label="$t('lang_pack.taskTable.NCFileName')"
            width="100"
            align="center"
          />
          <!-- 排序 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="task_order"
            :label="$t('lang_pack.taskTable.sort')"
            width="80"
            align="center"
          />
          <!-- 调整顺序 -->
          <el-table-column
            :show-overflow-tooltip="true"
            align="center"
            width="100"
            :label="$t('lang_pack.cuttingZone.AdjustTheOrder')"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.$index !== 0"
                size="small"
                icon="el-icon-top"
                circle
                @click="handleUp(scope.$index, scope.row.mo_id, 'order')"
              />
              <el-button
                v-if="scope.$index != tableData.length - 1"
                size="small"
                icon="el-icon-bottom"
                circle
                @click="handleDown(scope.$index, scope.row.mo_id, 'order')"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <el-dialog
        :fullscreen="false"
        :show-close="true"
        :close-on-press-escape="false"
        :modal-append-to-body="false"
        title="批量解析文件"
        width="1200px"
        :visible.sync="fjPUBLISHDialogVisible"
      >
        <el-button size="small" type="primary" style="float:right;margin-bottom:10px;" @click="resolution">
          解析
        </el-button>
        <el-table
          ref="table"
          v-loading="fjLoading"
          border
          size="small"
          :data="fjtableData"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :height="height"
          :highlight-current-row="true"
          @selection-change="fjSelectionChangeHandler"
        >
          <el-table-column type="selection" width="55" fixed />
          <!-- 任务号 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="task_num"
            :label="$t('lang_pack.taskTable.taskNumber')"
            align="center"
          />
          <!-- 型号 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="model_type"
            :label="$t('lang_pack.taskList.SteelPlateModel')"
            align="center"
          />
          <!-- 分配切割机 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="cut_code"
            :label="$t('lang_pack.taskTable.cutterbar')"
            align="center"
          >
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ getProdLineDes(scope.row.cut_code) }}
            </template>
          </el-table-column>
          <!-- JSON文件名称 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="json_name"
            :label="$t('lang_pack.taskTable.JSONFileName')"
            width="110"
            align="center"
          />
          <!-- NC文件名称 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="nc_name"
            :label="$t('lang_pack.taskTable.NCFileName')"
            width="100"
            align="center"
          />
          <!-- 排序 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="task_order"
            :label="$t('lang_pack.taskTable.sort')"
            width="80"
            align="center"
          />
          <!-- 调整顺序 -->
          <el-table-column
            :show-overflow-tooltip="true"
            align="center"
            width="100"
            :label="$t('lang_pack.cuttingZone.AdjustTheOrder')"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.$index !== 0"
                size="small"
                icon="el-icon-top"
                circle
                @click="handleUp(scope.$index, scope.row.mo_id, 'resolut')"
              />
              <el-button
                v-if="scope.$index != fjtableData.length - 1"
                size="small"
                icon="el-icon-bottom"
                circle
                @click="handleDown(scope.$index, scope.row.mo_id, 'resolut')"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <el-dialog
        :fullscreen="false"
        :show-close="true"
        :close-on-press-escape="false"
        :modal-append-to-body="false"
        title="报警信息查询"
        width="1200px"
        :visible.sync="rcsEventDialogtVisible"
        :before-close="handleClose"
      >
        <el-table
          ref="table"
          v-loading="rcsEventLoading"
          border
          size="small"
          :data="rcsEventtableData"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :height="height"
          :highlight-current-row="true"
        >
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="id" label="id" />
          <!-- 日期-->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="item_date"
            :label="$t('lang_pack.commonPage.date')"
          />
          <!-- 消息 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="event_msg"
            :label="$t('lang_pack.commonPage.msg')"
            width="660px"
          />
          <!-- 操作 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="cut_code"
            :label="$t('lang_pack.commonPage.operate')"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.event_data !== 'G2'"
                size="small"
                type="success"
                :data="scope.row"
                @click="editHandleFlag(scope.row)"
              >确认维修
              </el-button>
              <el-button
                v-if="scope.row.event_data === 'G2'"
                size="small"
                type="primary"
                :data="scope.row"
                @click="manualRelease(scope.row)"
              >放行
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.taskTable.taskSource')" prop="task_from">
                <!-- 任务来源 -->
                <el-select v-model="form.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.DATA_SOURCES"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.serialNumber')" prop="serial_num">
                <!-- 序列号 -->
                <el-input v-model="form.serial_num" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.batchNumber')" prop="lot_num">
                <!-- 批次号 -->
                <el-input v-model="form.lot_num" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.taskType')" prop="task_type">
                <!-- 任务类型 -->
                <el-select v-model="form.task_type" clearable filterable @change="getModelType">
                  <el-option
                    v-for="item in dict.APS_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.materialCode')" prop="material_code">
                <!-- 物料编码 -->
                <el-input v-model="form.material_code" disabled />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.modelY')" prop="model_type">
                <!-- 型号 -->
                <el-select v-model="form.model_type" clearable filterable :disabled="form.task_type===''" @change="getEcho">
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_type"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.length')" prop="m_length">
                <!-- 长 -->
                <el-input v-model="form.m_length" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.width')" prop="m_width">
                <!-- 宽 -->
                <el-input v-model="form.m_width" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.thick')" prop="m_height">
                <!-- 厚 -->
                <el-input v-model="form.m_height" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.weight')" prop="m_weight">
                <!-- 重 -->
                <el-input v-model="form.m_weight" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.materialQuality')" prop="m_texture">
                <!-- 材质 -->
                <el-input v-model="form.m_texture" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.cuttingMachine')" prop="cut_texture">
                <!-- 材质(切割机) -->
                <el-select v-model="form.cut_texture" clearable filterable>
                  <el-option
                    v-for="item in dict.CUTTERBAR_TEXTURE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingArea.Cutterbar')" prop="cut_code">
                <!-- 切割机 -->
                <!-- <el-input disabled v-model="form.cut_code" clearable size="small" /> -->
                <el-select v-model="form.cut_code" clearable filterable>
                  <el-option
                    v-for="item in stationDataTable"
                    :key="item.station_code"
                    :label="item.label"
                    :value="item.station_code"
                  >
                    <span style="float: left">{{ item.station_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_code
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.cutterbarType')" prop="cut_texture">
                <!-- 切割机类型 -->
                <el-select v-model="form.cut_type" clearable filterable>
                  <el-option
                    v-for="item in dict.CUT_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.taskTable.xCoordinate')" prop="npa_startx">
                                x坐标
                                <el-input v-model="form.npa_startx" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskTable.yCoordinate')" prop="npa_starty">
                                y坐标
                                <el-input v-model="form.npa_starty" />
                            </el-form-item> -->
              <!-- <el-form-item :label="$t('lang_pack.taskTable.DXFFileName')" prop="dxf_name">
                                DXF文件名称
                                <el-input v-model="form.dxf_name" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskTable.DXFFileURLPath')" prop="dxf_url">
                                DXF文件URL路径
                                <el-input v-model="form.dxf_url" readonly="readonly">
                                    <el-button slot="append" icon="el-icon-upload" @click="handleUpload('dxf_url')" />
                                </el-input>
                            </el-form-item> -->
              <el-form-item :label="$t('lang_pack.taskTable.JSONFileName')" prop="json_name">
                <!-- JSON文件名称 -->
                <el-input v-model="form.json_name" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.JSONFileURLPath')" prop="json_url">
                <!-- JSON文件URL路径 -->
                <el-input v-model="form.json_url" readonly="readonly">
                  <el-button slot="append" icon="el-icon-upload" @click="handleUpload('json_url')" />
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.NCFileName')" prop="nc_name">
                <!-- NC文件名称 -->
                <el-input v-model="form.nc_name" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.NCFileURLPath')" prop="nc_url">
                <!-- NC文件URL路径 -->
                <el-input v-model="form.nc_url" readonly="readonly">
                  <el-button slot="append" icon="el-icon-upload" @click="handleUpload('nc_url')" />
                </el-input>
              </el-form-item>
              <el-form-item label="任务数量" prop="task_number">
                <!-- 任务数量 -->
                <el-input v-model.number="form.task_number" type="number" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.shotBlastingOrNot')">
                <!-- 是否抛丸 -->
                <el-radio-group v-model="form.is_auto_blast">
                  <el-radio
                    v-for="item in [{ id: 0, label: '是', value: 'Y' }, { id: 1, label: '否', value: 'N' }]"
                    :key="item.id"
                    :label="item.value"
                  >{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.whetherToAutotheboard')">
                <!-- 天车状态 -->
                <el-radio-group v-model="form.is_auto_car">
                  <el-radio
                    v-for="item in [{ id: 0, label: '自动', value: 'Y' }, { id: 1, label: '手动', value: 'N' }]"
                    :key="item.id"
                    :label="item.value"
                  >{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.whetherToSprayCode')">
                <!-- 是否喷码 -->
                <el-radio-group v-model="form.is_auto_print">
                  <el-radio
                    v-for="item in [{ id: 0, label: '自动', value: 'Y' }, { id: 1, label: '手动', value: 'N' }]"
                    :key="item.id"
                    :label="item.value"
                  >{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.whetherToAutomaticallyCut')">
                <!-- 是否切割 -->
                <el-radio-group v-model="form.is_auto_cut">
                  <el-radio
                    v-for="item in [{ id: 0, label: '自动', value: 'Y' }, { id: 1, label: '手动', value: 'N' }]"
                    :key="item.id"
                    :label="item.value"
                  >{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskTable.automaticSortingOrNot')">
                <!-- 是否分拣 -->
                <el-radio-group v-model="form.is_auto_sort">
                  <el-radio
                    v-for="item in [{ id: 0, label: '分拣', value: 'Y' }, { id: 1, label: '不分拣', value: 'N' }]"
                    :key="item.id"
                    :label="item.value"
                  >{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.taskTable.isTheCarCentered')">
                                横移小车对中
                                <el-radio-group v-model="form.is_center">
                                    <el-radio v-for="item in [{id:0,label:'启用',value:'Y'},{id:1,label:'禁用',value:'N'}]" :key="item.id" :label="item.value" >{{
                                        item.label
                                    }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskTable.onSiteSiteMaterials')">
                                场内/外物料
                                <el-radio-group v-model="form.is_materialt">
                                    <el-radio v-for="item in [{id:0,label:'场内',value:'Y'},{id:1,label:'场外',value:'N'}]" :key="item.id" :label="item.value" >{{
                                        item.label
                                    }}</el-radio>
                                </el-radio-group>
                            </el-form-item> -->
              <el-form-item :label="$t('lang_pack.taskTable.reportWorkOrNot')">
                <!-- 是否完成报工 -->
                <el-radio-group v-model="form.bg_flag">
                  <el-radio
                    v-for="item in [{ id: 0, label: '是 ', value: 'Y' }, { id: 1, label: '否', value: 'N' }]"
                    :key="item.id"
                    :label="item.value"
                  >{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="uploadDrawerTitle"
            :visible.sync="uploadDrawerVisbleSync"
            size="390px"
            @closed="handleUploadDrawerClose"
          >
            <el-upload
              ref="upload"
              name="file"
              :multiple="false"
              action=""
              drag=""
              :limit="uploadLimit"
              :on-change="handleUploadOnChange"
              :http-request="handleUploadHttpRequest"
              :accept="uploadAccept"
              :auto-upload="false"
              :file-list="fileList"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
            </el-upload>
            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="handleUploadDrawerCancel"
              >取消</el-button>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                @click="handleUploadFile"
              >上传</el-button>
            </div>
          </el-drawer>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item
                    label="ID"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.mo_id
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="任务号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_num
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="任务来源"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_from
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="序列号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.serial_num
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="批次号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.lot_num
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="任务类型"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ dict.label.APS_TASK_TYPE[props.row.task_type]
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="物料编码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.material_code
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="物料描述"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.material_des
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件图号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.material_draw
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="型号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.model_type
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="长"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_length
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="宽"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_width
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="厚"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_height
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="重"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_weight
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="材质"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_texture
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="材质(切割)"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ dict.label.CUTTERBAR_TEXTURE[props.row.cut_texture] }}</el-descriptions-item>
                  <!-- <el-descriptions-item label="切割寻边开始X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.npa_startx }}</el-descriptions-item> -->
                  <!-- <el-descriptions-item label="切割寻边开始Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.npa_starty }}</el-descriptions-item> -->
                  <!-- <el-descriptions-item label="DXF文件名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dxf_name }}</el-descriptions-item> -->
                  <!-- <el-descriptions-item label="DXF文件URL路径" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dxf_url }}</el-descriptions-item> -->
                  <el-descriptions-item
                    label="JSON文件名称"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.json_name
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="JSON文件URL路径"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.json_url
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="JSON数据"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.json_data
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="NC文件名称"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.nc_name
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="NC文件URL路径"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.nc_url
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="计划时间"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.plan_date
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="排序"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_order
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="任务状态"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.PROD_TASK_STATUS[props.row.task_status] }}</el-descriptions-item>
                  <el-descriptions-item
                    label="任务消息"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_msg
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分配切割机"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    getProdLineDes(props.row.cut_code) }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分配切割机类型"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.CUT_TYPE[props.row.cut_type] }}</el-descriptions-item>
                  <el-descriptions-item
                    label="切割计划时间"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.cut_plan_minutes
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="是否抛丸"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.is_auto_blast ===
                    'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item
                    label="是否选择自动天车"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.is_auto_car === 'Y'
                    ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item
                    label="是否选择自动喷码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.is_auto_print ===
                    'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item
                    label="是否选择自动切割"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.is_auto_cut === 'Y'
                    ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item
                    label="是否选择自动分拣"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.is_auto_sort === 'Y'
                    ? '是' : '否' }}</el-descriptions-item>
                  <!-- <el-descriptions-item label="横移小车是否对中" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.is_center === 'Y' ? '是' : '否'}}</el-descriptions-item> -->
                  <el-descriptions-item
                    label="场内/外物料"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.is_materialt ===
                    'Y' ? '场内' : '场外' }}</el-descriptions-item>
                  <el-descriptions-item
                    label="当前任务所在工位位置"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.now_station_code
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="是否完成报工"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.bg_flag === 'Y' ?
                    '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item
                    label="有效标识"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item
                    label="预留属性1"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.attribute1
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="预留属性2"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.attribute2
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="预留属性3"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.attribute3
                  }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column
              label=""
              align="center"
              width="80"
              fixed="left"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" :disabled="scope.row.task_status !== 'PLAN'" type="text" size="small" @click="withdraw(scope)">任务撤回</el-button>
              </template>
            </el-table-column>
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              :label="$t('lang_pack.taskTable.taskNumber')"
              width="180"
              align="center"
            />
            <!-- 工位位置 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="now_station_code"
              :label="$t('lang_pack.taskTable.stationPosition')"
              width="100"
              align="center"
            />
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.taskList.SteelPlateModel')"
              width="130"
              align="center"
            />
            <!-- 任务状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_status"
              :label="$t('lang_pack.taskTable.taskStatus')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <!-- 切割机类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_type"
              :label="$t('lang_pack.taskTable.cutterbarType')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.CUT_TYPE[scope.row.cut_type] }}
              </template>
            </el-table-column>
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.mfTable.length')"
              width="80"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.mfTable.width')"
              width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              :label="$t('lang_pack.mfTable.thick')"
              width="80"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.mfTable.weight')"
              width="80"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              :label="$t('lang_pack.mfTable.materialQuality')"
              width="80"
              align="center"
            />
            <!-- JSON文件名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="json_name"
              :label="$t('lang_pack.taskTable.JSONFileName')"
              width="110"
              align="center"
            />
            <!-- NC文件名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="nc_name"
              :label="$t('lang_pack.taskTable.NCFileName')"
              width="100"
              align="center"
            />
            <!-- 任务消息 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_msg"
              :label="$t('lang_pack.taskTable.taskMessage')"
              width="100"
              align="center"
            />
            <!-- JSON路径 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="json_url"
              :label="$t('lang_pack.taskTable.JSONFileURLPath')"
              width="110"
              align="center"
            />
            <!-- JSON文本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="json_data"
              :label="$t('lang_pack.taskTable.JSONFileURLTxt')"
              width="110"
              align="center"
            />
            <!-- NC路径 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="nc_url"
              :label="$t('lang_pack.taskTable.NCFileURLPath')"
              width="100"
              align="center"
            />
            <!-- 排序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_order"
              :label="$t('lang_pack.taskTable.sort')"
              width="80"
              align="center"
            />
            <!-- 分配切割机 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_code"
              :label="$t('lang_pack.taskTable.cutterbar')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getProdLineDes(scope.row.cut_code) }}
              </template>
            </el-table-column>
            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.taskTable.taskSource')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              :label="$t('lang_pack.taskTable.taskType')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.APS_TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.taskTable.materialCode')"
              width="80"
              align="center"
            />

            <!-- 材质(切割机) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_texture"
              :label="$t('lang_pack.mfTable.cuttingMachine')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.CUTTERBAR_TEXTURE[scope.row.cut_texture] }}
              </template>
            </el-table-column>
            <!-- DXF文件名称
                         <el-table-column  :show-overflow-tooltip="true" prop="dxf_name"
                            :label="$t('lang_pack.taskTable.DXFFileName')" width="100" align='center'/>
                        DXF文件路径
                        <el-table-column  :show-overflow-tooltip="true" prop="dxf_url"
                            :label="$t('lang_pack.taskTable.DXFFileURLPath')" width="100" align='center'/> -->
            <!-- 计划时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_date"
              :label="$t('lang_pack.taskTable.scheduledTime')"
              width="100"
              align="center"
            />
            <!-- 序列号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              :label="$t('lang_pack.taskTable.serialNumber')"
              width="80"
              align="center"
            />
            <!-- 物料描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_des"
              :label="$t('lang_pack.taskTable.materialDescription')"
              width="80"
              align="center"
            />
            <!-- 零件图号 -->
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="material_draw"
                            :label="$t('lang_pack.taskTable.partDrawingNumber')" width="80" align='center'/>
                        x坐标
                        <el-table-column  :show-overflow-tooltip="true" prop="npa_startx"
                            :label="$t('lang_pack.taskTable.xCoordinate')" width="80" align='center'/>
                        y坐标
                        <el-table-column  :show-overflow-tooltip="true" prop="npa_starty"
                            :label="$t('lang_pack.taskTable.yCoordinate')" width="80" align='center'/> -->

            <!-- 是否抛丸 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="is_auto_blast"
              :label="$t('lang_pack.taskTable.shotBlastingOrNot')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.is_auto_blast == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否自动天车 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="is_auto_car"
              :label="$t('lang_pack.taskTable.whetherToAutotheboard')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.is_auto_car == 'Y' ? '自动' : '手动' }}
              </template>
            </el-table-column>
            <!-- 是否选择自动喷码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="is_auto_print"
              :label="$t('lang_pack.taskTable.whetherToSprayCode')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.is_auto_print == 'Y' ? '自动' : '手动' }}
              </template>
            </el-table-column>
            <!-- 是否自动切割 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="is_auto_cut"
              :label="$t('lang_pack.taskTable.whetherToAutomaticallyCut')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.is_auto_cut == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否自动分拣 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="is_auto_sort"
              :label="$t('lang_pack.taskTable.automaticSortingOrNot')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.is_auto_sort == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否报工 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bg_flag"
              :label="$t('lang_pack.sortingArea.ReportWorkOrNot')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.bg_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 小车是否对中 -->
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="is_center"
                            :label="$t('lang_pack.taskTable.isTheCarCentered')" width="100" align='center'>
                            <template slot-scope="scope">
                                取到当前单元格
                                {{ scope.row.is_center == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column> -->
            <el-table-column
              :label="$t('lang_pack.recipequality.enableFlag')"
              prop="enable_flag"
              width="80"
              align="center"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch
                  v-model="scope.row.enable_flag"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-value="Y"
                  inactive-value="N"
                  @change="changeEnabled(scope.row, scope.row.enable_flag)"
                />
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="240"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                  <template slot="right">
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      :disabled="scope.row.task_status != 'WAIT' && scope.row.task_status != 'PLAN' && scope.row.task_status != 'WAIT_PUBLISH'"
                      @click="resolutionColum(scope.row.mo_id)"
                    >解析任务</el-button>
                    <el-dropdown>
                      <span class="el-button el-button--text el-button--small">更多<i
                        class="el-icon-arrow-down el-icon--right"
                      /></span>
                      <el-dropdown-menu slot="dropdown">
                        <!-- <el-dropdown-item :disabled="scope.row.bg_flag === 'Y'" @click.native="handleWork(scope.row.task_num)">手动报工</el-dropdown-item> -->
                        <el-dropdown-item
                          @click.native="taskResolve(scope.row.mo_id)"
                        >解析结果</el-dropdown-item>
                        <el-dropdown-item
                          @click.native="sortResult(scope.row.mo_id)"
                        >分拣结果</el-dropdown-item>
                        <el-dropdown-item
                          @click.native="stationFlow(scope.row.task_num)"
                        >过站履历</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
            <!-- 调整顺序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              width="100"
              :label="$t('lang_pack.cuttingZone.AdjustTheOrder')"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.$index !== 0 || crud.page.page!=1"
                  size="small"
                  icon="el-icon-top"
                  circle
                  @click="handleUp(scope.$index,scope.row.mo_id,'query')"
                />
                <el-button
                  v-if="(crud.page.page - 1) * crud.page.size + scope.$index + 1 !== crud.page.total"
                  size="small"
                  icon="el-icon-bottom"
                  circle
                  @click="handleDown(scope.$index,scope.row.mo_id,'query')"
                />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <sortResult v-if="dialogVisible" ref="sortResultFlag" :mo_id="mo_id" @ok="dialogVisible = false" />
    <taskResolve v-if="dialogVisible1" ref="taskResolveFlag" :mo_id="mo_id" @ok="dialogVisible1 = false" />
    <stationFlow v-if="dialogVisible2" ref="stationFlowFlag" :task_num="task_num" @ok="dialogVisible2 = false" />
  </div>
</template>

<script>
import { downloadFile } from '@/utils/index'
import crudTask from '@/api/dcs/core/aps/task'
import axios from 'axios'
import Cookies from 'js-cookie'
import { selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import sortResult from './modules/sortResult.vue'
import taskResolve from './modules/taskResolve.vue'
import stationFlow from './modules/stationFlow.vue'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { data } from 'jquery'
const defaultForm = {
  item_date: '',
  item_date_val: '',
  mo_id: '',
  task_num: '',
  task_from: '',
  serial_num: '',
  lot_num: '',
  task_type: '',
  material_code: '',
  material_des: '',
  material_draw: '',
  model_type: '',
  m_length: '',
  m_width: '',
  m_height: '',
  m_weight: '',
  m_texture: '',
  cut_texture: 'MS',
  npa_startx: '',
  npa_starty: '',
  dxf_name: '',
  dxf_url: '',
  json_name: '',
  json_url: '',
  json_data: '',
  nc_name: '',
  nc_url: '',
  plan_date: '',
  task_order: '',
  task_status: '',
  task_msg: '',
  cut_code: '',
  cut_type: '',
  cut_plan_minutes: '',
  task_number: 1,
  is_auto_blast: 'Y',
  is_auto_car: 'Y',
  is_auto_print: 'Y',
  is_auto_cut: 'Y',
  is_auto_sort: 'Y',
  is_center: 'Y',
  is_materialt: 'Y',
  now_station_code: '',
  bg_flag: 'N',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: '',
  id: '',
  event_msg: '',
  handle_flag: ''
}
export default {
  name: 'WEB_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination, sortResult, taskResolve, stationFlow },
  cruds() {
    return CRUD({
      title: '生产任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mo_id',
      // 排序
      sort: ['mo_id asc'],
      // CRUD Method
      crudMethod: { ...crudTask },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'task:add'],
        edit: ['admin', 'task:edit'],
        del: ['admin', 'task:del']
      },
      rules: {
        lot_num: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
        // material_code: [{ required: true, message: '请选择物料编码', trigger: 'blur' }],
        material_draw: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
        // material_code: [{ required: true, message: '请选择零件图号', trigger: 'blur' }],
        model_type: [{ required: true, message: '请选择型号', trigger: 'blur' }],
        dxf_url: [{ required: true, message: '请选择DXF文件URL路径', trigger: 'blur' }],
        json_url: [{ required: true, message: '请选择JSON文件URL路径', trigger: 'blur' }],
        nc_url: [{ required: true, message: '请选择NC文件URL路径', trigger: 'blur' }],
        task_number: [{ required: true, message: '请选择任务数量', trigger: 'blur' }]
      },
      uploadLimit: 1, // 注意：加密程序需要传2个文件
      uploadAccept: '*',
      uploadDrawerTitle: '文件上传',
      currentUploadType: '',
      currentFilePath: '',
      uploadDrawerVisbleSync: false,
      fileList: [],
      modelList: [],
      importDialogVisible: false,
      uploadLimit1: 1,
      uploadAccept1: '.xls,.xlsx',
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: '',
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      mo_id: '',
      task_num: '',
      downloadLoading: false,
      stationDataTable: [],
      releaseDialogVisible: false,
      releaseLoading: true,
      id: '',
      event_msg: '',
      handle_flag: '',
      rcsEventDialogtVisible: false,
      rcsEventLoading: true,
      rcsEventtableData: [],
      tableData: [],
      selectionData: [],
      fjPUBLISHDialogVisible: false,
      fjLoading: true,
      fjtableData: [],
      fjselectionData: [],
      modelData: [],
      // MQTT
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userId') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      rcsEventFlag: true
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'APS_TASK_TYPE', 'CUT_TYPE', 'DATA_SOURCES', 'CUTTERBAR_TEXTURE', 'PROD_TASK_STATUS'],
  mounted: function() {
    const that = this
    that.crud.props.searchToggle = false // 默认先隐藏
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 300
    }
    const query = {
      task_type: '',
      enable_flag: 'Y',
      userName: Cookies.get('userName')
    }
    crudTask.getFmodModel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        this.modelData = defaultQuery.data || []
      }
    }).catch(() => {
      this.modelData = []
      this.$message({
        message: '型号查询异常',
        type: 'error'
      })
    })
    this.rcsEventSel()
    // 说明：事件报警信息，第一次刷新如果有数据就弹框，如果没有就不弹
    setInterval(() => {
      this.rcsEventSel()
      this.rcsEventFlag = false
    }, 1000 * 10)
  },
  created: function() {
    // this.getModelType()
    this.getSelStation()
    this.toStartWatch()
  },
  beforeDestroy() {
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    handleClose() {
      this.rcsEventDialogtVisible = false
    },
    rcsEventSel() {
      const query = {
        userName: Cookies.get('userName')
      }
      // this.rcsEventDialogtVisible = true
      crudTask.getRcsEvent(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        this.rcsEventLoading = false
        if (defaultQuery.code === 0) {
          this.rcsEventtableData = defaultQuery.data || []
          if (this.rcsEventFlag) {
            this.rcsEventtableData.length > 0 && (this.rcsEventDialogtVisible = true)
          }
        } else {
          this.rcsEventtableData = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      }).catch(() => {
        this.rcsEventtableData = []
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    releaseOrder() {
      const query = {
        page: 1,
        size: 20,
        task_status: 'WAIT_PUBLISH'
      }
      this.releaseDialogVisible = true
      crudTask.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        this.releaseLoading = false
        if (defaultQuery.code === 0) {
          this.tableData = defaultQuery.data || []
        } else {
          this.tableData = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      }).catch(() => {
        this.tableData = []
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    releaseSelectionChangeHandler(val) {
      this.selectionData = val
    },
    fjSelectionChangeHandler(val) {
      this.fjselectionData = val
    },
    withdraw(row) {
      this.$confirm('确定撤回这项任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const query = {
          task_num: [row.row.task_num],
          user_name: Cookies.get('userName')
        }
        this.taskRecall(query)
      }).catch(() => {
        this.$message({
          message: '操作异常',
          type: 'error'
        })
      })
    },
    taskRecall(query) {
      crudTask.taskWithdraw(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('任务撤回成功')
          this.crud.toQuery()
        } else {
          this.$message({
            message: defaultQuery.msg || '任务撤回失败',
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '任务撤回失败',
          type: 'error'
        })
      })
    },
    confirmRelease() {
      if (!this.selectionData.length) {
        this.$message.warning('请至少选择一项')
        return false
      }
      this.$confirm(`确定要发布所选${this.selectionData.length}项内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var ids = this.selectionData.map(e => e.mo_id).join(',')
          crudTask.taskPublish({ ids }).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('操作成功')
              this.releaseDialogVisible = false
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '发布失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
        })
        .catch(() => {

        })
    },
    resolution() {
      if (!this.fjselectionData.length) {
        this.$message.warning('请至少选择一项')
        return false
      }
      this.$confirm(`确定要解析所选${this.fjselectionData.length}项内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var ids = this.fjselectionData.map(e => e.mo_id).join(',')
          const query = {
            mo_id_list: ids,
            user_name: Cookies.get('userName')
          }
          crudTask.FjSendTask(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('操作成功')
              this.fjPUBLISHDialogVisible = false
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '解析失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '解析失败',
              type: 'error'
            })
          })
        })
        .catch(() => {

        })
    },
    resolutionColum(mo_id) {
      this.$confirm(`确定要解析这项内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            mo_id_list: mo_id,
            user_name: Cookies.get('userName')
          }
          crudTask.FjSendTask(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('操作成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '解析失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '解析失败',
              type: 'error'
            })
          })
        })
        .catch(() => {

        })
    },
    getProdLineDes(station_code) {
      var item = this.stationDataTable.find(item => item.station_code === station_code)
      if (item !== undefined) {
        return item.station_des
      }
      return station_code
    },
    getSelStation() {
      const query = {
        line_section_code: 'CUT',
        user_name: Cookies.get('userName')
      }
      crudTask.selStation(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.count === 0) {
            this.stationDataTable = []
          } else {
            this.stationDataTable = defaultQuery.data
          }
        } else {
          this.stationDataTable = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudTask
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              mo_id: data.mo_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    sortResult(id) {
      if (!id) {
        this.$message.error('当前没有生产任务id')
        return
      }
      this.mo_id = id
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.sortResultFlag.dialogVisible = true
      })
    },
    taskResolve(id) {
      if (!id) {
        this.$message.error('当前没有生产任务id')
        return
      }
      this.mo_id = id
      this.dialogVisible1 = true
      this.$nextTick(() => {
        this.$refs.taskResolveFlag.dialogVisible = true
      })
    },
    stationFlow(task_num) {
      if (!task_num) {
        this.$message.error('当前没有生产任务号')
        return
      }
      this.task_num = task_num
      this.dialogVisible2 = true
      this.$nextTick(() => {
        this.$refs.stationFlowFlag.dialogVisible = true
      })
    },
    batchResolve() {
      const query = {
        page: 1,
        size: 20,
        task_status: 'WAIT,WAIT_PUBLISH'
      }
      this.fjPUBLISHDialogVisible = true
      crudTask.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        this.fjLoading = false
        if (defaultQuery.code === 0) {
          this.fjtableData = defaultQuery.data || []
        } else {
          this.fjtableData = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      }).catch(() => {
        this.fjtableData = []
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    handleWork(task_num) {
      this.$confirm('确定要执行手动报工吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudTask.operationBg({ task_num }).then((res) => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message({ message: defaultQuery.msg || '报工完成', type: 'success' })
              this.crud.toQuery()
            } else {
              this.$message({ message: defaultQuery.msg || '手动报工异常', type: 'error' })
            }
          })
            .catch((err) => {
              this.$message({ message: err.msg || '手动报工异常', type: 'error' })
            })
        })
        .catch(() => {

        })
    },
    exportForWork() {
      const task_num = this.crud.selections.map(e => e.task_num)
      for (let i = 0; i < this.crud.selections.length; i++) {
        if (this.crud.selections[i].bg_flag === 'N') {
          this.$message({ message: '未报工信息不可导出', type: 'error' })
          return
        }
      }
      this.$confirm('确定要导出报工文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downloadLoading = true
          crudTask.exportBg({ task_num }).then(result => {
            downloadFile(result, '导出报工数据', 'xlsx')
            this.downloadLoading = false
          }).catch(() => {
            this.downloadLoading = false
          })
        })
        .catch(() => {
        })
    },
    getEcho(e) {
      let obj = {}
      obj = this.modelList.filter(item => { return item.model_type === e })[0] || {}
      if (Object.keys(obj).length > 0) {
        this.form.m_length = obj.m_length
        this.form.m_width = obj.m_width
        this.form.m_height = obj.m_height
        this.form.m_weight = obj.m_weight
        this.form.m_texture = obj.m_texture
        this.form.material_code = obj.material_code
        this.form.material_des = obj.material_des
      }
    },
    getModelType(e) {
      const Arr = ['m_length', 'm_width', 'm_height', 'm_weight', 'm_texture', 'material_code', 'material_des', 'model_type']
      Arr.forEach(item => {
        this.form[item] = ''
      })
      const query = {
        userID: Cookies.get('userName'),
        task_type: e
      }
      crudTask.getFmodModel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    },
    handleUploadDrawerClose() {
      this.fileList = []
    },
    handleUpload(type) {
      this.currentUploadType = type
      if (type === 'dxf_url') {
        this.uploadDrawerTitle = 'DXF文件'
        this.currentFilePath = 'DXF'
        this.uploadAccept = '.dxf'
      } else if (type === 'json_url') {
        this.uploadDrawerTitle = 'JSON文件'
        this.currentFilePath = 'JSON'
        this.uploadAccept = '.json'
      } else if (type === 'nc_url') {
        this.uploadDrawerTitle = 'NC文件'
        this.currentFilePath = 'NC'
        this.uploadAccept = '.gnc,.txt,.mpf,.cnc'
      }
      this.uploadDrawerVisbleSync = true
    },
    handleUploadDrawerCancel() {
      this.uploadDrawerVisbleSync = false
    },
    // 导入文件时将文件存入数组中
    handleUploadOnChange(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequest(file) {
      this.fileData.append('file', file.file)
    },
    // 数组项 互换位置
    // swapArray(arr, index1, index2) {
    //     arr[index1] = arr.splice(index2, 1, arr[index1])[0];
    //     return arr;
    // },]

    // 修改报警信息的处理标识
    editHandleFlag(data) {
      const query = {
        id: data.id,
        event_msg: data.event_msg,
        user_name: Cookies.get('userName')
      }
      crudTask.editHandleFlag(query).then(res => {
        if (res.code === 0) {
          this.$message({
            message: '清除成功',
            type: 'success'
          })
          this.rcsEventSel()
        }
      }).catch((e) => {
        this.$message({
          message: '清除失败',
          type: 'error'
        })
      })
    },
    // 上移
    handleUp(i, id, type) {
      crudTask.taskUp({ mo_id: id }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (type === 'query') {
            this.crud.toQuery()
          } else if (type === 'order') {
            this.releaseOrder()
          } else if (type === 'resolut') {
            this.batchResolve()
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '上移异常',
            type: 'error'
          })
        })
    },
    // 下移
    handleDown(i, id, type) {
      crudTask.taskDown({ mo_id: id }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (type === 'query') {
            this.crud.toQuery()
          } else if (type === 'order') {
            this.releaseOrder()
          } else if (type === 'resolut') {
            this.batchResolve()
          }
        } else {
          this.$message({
            message: defaultQuery.msg || '下移异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.$message({
            message: '下移异常',
            type: 'error'
          })
        })
      // this.swapArray(this.crud.data, i, i + 1);
    },
    // 处理上传文件
    handleUploadFile() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      this.fileData = new FormData()
      this.fileData.append('path', this.currentFilePath)
      this.$refs.upload.submit()

      // 配置路径
      var method = 'dcs/core/FileSave'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = 'http://*************:9090/aisEsbWeb/' + method

      const loading = this.$loading({
        lock: true,
        text: '上传文件处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            const fileInfo = JSON.parse(defaultQuery.data.result)
            console.log(fileInfo)
            if (this.currentUploadType === 'dxf_url') {
              this.form.dxf_name = fileInfo.file_name
              this.form.dxf_url = fileInfo.file_dir_path
            } else if (this.currentUploadType === 'json_url') {
              this.form.json_name = fileInfo.file_name
              this.form.json_url = fileInfo.file_dir_path
              const file = this.fileList[0].raw
              const reader = new FileReader()
              reader.onload = () => {
                // const data = JSON.parse(reader.result)
                this.form.json_data = reader.result
              }
              reader.readAsText(file)
            } else if (this.currentUploadType === 'nc_url') {
              this.form.nc_name = fileInfo.file_name
              this.form.nc_url = fileInfo.file_dir_path
            }
            this.uploadDrawerVisbleSync = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
        .catch(er => {
          loading.close()
          this.$message({
            message: '上传文件异常：' + er,
            type: 'error'
          })
        })
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) { },
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = 'dcs/core/DcsApsIntefProducTaskImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    },
    manualRelease(data) {
      this.$confirm('确定要放行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudTask.updateEventStatus({ id: data.id }).then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            var sendJson = {}
            var rowJson = []
            var newRow = {
              TagKey: 'SlAisSimPlc/GStatus/ManualLoadingFinish',
              TagValue: '1'
            }
            rowJson.push(newRow)
            sendJson.Data = rowJson
            sendJson.ClientName = 'SCADA_WEB'
            var sendStr = JSON.stringify(sendJson)
            var topic = 'SCADA_WRITE/SlAisSimPlc'
            this.sendMessage(topic, sendStr)
            this.rcsEventSel()
          }
        }).catch(e => {
          this.$message({
            type: 'error',
            message: e.msg || '放行失败'
          })
        })
      }).catch(() => {
      })
    },
    toStartWatch() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query).then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res.result))
        const result = JSON.parse(defaultQuery)
        if (result === '' || result === undefined || result == null) {
          this.$message({
            message: '请先维护服务、单元信息',
            type: 'error'
          })
          return
        }
        var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
        this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
        this.clientMqtt.on('connect', (e) => {
          this.mqttConnStatus = true
          this.topicSubscribe('SCADA_CHANGE/SlAisSimPlc/GStatus/ManualLoadingFinish')
        })
        // MQTT连接失败
        this.clientMqtt.on('error', (error) => {
          console.log(error)
          this.$message({
            message: '连接失败',
            type: 'error'
          })
          this.clientMqtt.end()
        })
        // 断开发起重连(异常)
        this.clientMqtt.on('reconnect', (error) => {
          console.log(error)
          this.$message({
            message: '连接断开，正在重连。。。',
            type: 'error'
          })
        })
        this.clientMqtt.on('disconnect', (error) => {
          console.log(error)
          this.$message({
            message: '服务连接断开',
            type: 'error'
          })
        })
        this.clientMqtt.on('close', () => {
          this.clientMqtt.end()

          this.$message({
            message: '服务连接断开',
            type: 'error'
          })
        })
        // 接收消息处理
        this.clientMqtt.on('message', (topic, message) => {
        })
      })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
.app-container {
    .inbOutNum {
        margin: 0 5px;
        color: #409eff;
        cursor: pointer;
    }

    .hover-row {
        td {
            .el-dropdown {
                .el-button--text {
                    color: #fff;
                }
            }

            .inbOutNum {
                color: #fff;
            }
        }
    }

    .current-row {
        td {
            .el-dropdown {
                .el-button--text {
                    color: #fff;
                }
            }

            .inbOutNum {
                color: #fff;
            }
        }
    }

    ::v-deep .el-dialog {
        margin-top: 10vh !important;
    }
}
</style>
