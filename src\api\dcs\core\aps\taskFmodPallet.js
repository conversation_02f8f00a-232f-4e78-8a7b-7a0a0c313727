import request from '@/utils/request'

// 查询托盘管理
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodPalletSelect',
    method: 'post',
    data
  })
}
// 新增托盘管理
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodPalletInsert',
    method: 'post',
    data
  })
}
// 修改托盘管理
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodPalletUpdate',
    method: 'post',
    data
  })
}
// 删除托盘管理
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodPalletDelete',
    method: 'post',
    data
  })
}
export default { sel ,add ,edit ,del}

