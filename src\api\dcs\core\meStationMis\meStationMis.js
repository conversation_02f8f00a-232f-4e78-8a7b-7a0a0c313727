import request from '@/utils/request'

// 查询工位MIS实时状态表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMeStationMisSelAll',
    method: 'post',
    data
  })
}
// 新增工位MIS实时状态表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarInsert',
    method: 'post',
    data
  })
}
// 修改工位MIS实时状态表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarUpdate',
    method: 'post',
    data
  })
}
// 删除工位MIS实时状态表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarDelete',
    method: 'post',
    data
  })
}
// 自走棋表格
export function StationMisSel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMeStationMisSel',
    method: 'post',
    data
  })
}
// 自走棋
export function mainChess(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMainChess',
    method: 'post',
    data
  })
}
export function clearRollerTask(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsClearRollerTask',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, mainChess, StationMisSel, clearRollerTask }

