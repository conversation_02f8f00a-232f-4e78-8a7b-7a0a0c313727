<template>
  <div id="#bigScreen" class="mesContainer">
    <div class="header">
      <img src="@/assets/images/sh/headerLeft.png" alt="">
      <span>RAL-01</span>
      <img src="@/assets/images/sh/headerRight.png" alt="">
    </div>
    <div style="margin-top: 20px;display: flex;">
      <div class="productPlan">
        <div v-for="(item,index) in productionData" :key="index" class="box">
          <div class="name">{{ item.name }}:</div>
          <div class="value"><span>{{ item.value }}</span></div>
        </div>
      </div>
      <div class="time">
        <div>{{ date }}</div>
        <div>{{ week }}</div>
        <div class="timedata">{{ time }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { sel } from '@/api/mes/project/sh/shProductionScreen'
import autofit from 'autofit.js'
export default {
  name: 'productionBoard',
  data() {
    return {
      productionData: [
        { name: '生产机型', value: '', key: 'small_model_type' },
        { name: '计划数', value: 0, key: 'mo_plan_count' },
        { name: '基准数', value: 0, key: 'start_date' },
        { name: '上线数', value: 0, key: 'mo_online_count' },
        { name: '下线数', value: 0, key: 'mo_offline_count' }
      ],
      date: '',
      time: '',
      week: '',
      timer: null
    }
  },
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    this.getProductionData()
    this.getTime() // 获取时间
    this.timer = setInterval(() => {
      this.getTime()
    }, 1000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    getProductionData() {
      const query = {}
      sel(query).then(res => {
        if (res.code === 0 && res.data !== '') {
          const data = res.data[0]
          for (const val in data) {
            const obj = this.productionData.find(e => e.key === val)
            if (Object.keys(obj).length > 0) {
              obj.value = data[val]
            }
          }
        }
      })
    },
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.date = yy + '-' + mm + '-' + dd
      this.time = hh + ' ' + ':' + ' ' + mf + ' ' + ':' + ' ' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    }
  }
}
</script>
<style lang="less" scoped>
.mesContainer{
    background: #2B304D;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transform-origin: 0 0;
    padding: 0 15px;
    .header{
        width: 100%;
        height: 105px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
            font-size: 48px;
            color: #fff;
            font-weight: 600;
        }
    }
    .productPlan{
        width: 50%;
        .box{
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            .name{
                border: 1px solid #fff;
                border-radius: 8px;
                height: 170px;
                width: 750px;
                font-size: 130px;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
            }
            .value{
                width: 30%;
                height: 100%;
                font-size: 130px;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
            }
        }
    }
    .time{
        width: 50%;
        display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        div{
            font-size: 130px;
            color: #fff;
            font-weight: 600;
        }
        .timedata{
            font-size: 200px;
        }
    }
}
</style>
