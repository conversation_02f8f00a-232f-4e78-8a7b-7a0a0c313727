import request from '@/utils/request'

// 查询项目
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pm/core/SysPmPageList',
    method: 'post',
    data
  })
}
// 新增项目
export function add(data) {
  return request({
    url: 'aisEsbWeb/pm/core/SysPmDataInsert',
    method: 'post',
    data
  })
}
// 修改项目
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pm/core/SysPmUpdate',
    method: 'post',
    data
  })
}
// 删除项目
export function del(data) {
  return request({
    url: 'aisEsbWeb/pm/core/SysPmDataDel',
    method: 'post',
    data
  })
}
// 导出项目
export function down(data) {
  return request({
    url: 'aisEsbWeb/pm/core/SysPmExport',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 修改生产任务--修改有效标识
export function editEnableFlag(data) {
  return request({
      url: 'aisEsbWeb/pm/core/SysPmIsEnableFlag',
      method: 'post',
      data
  })
}
export default { sel,add,edit,del,down,editEnableFlag}