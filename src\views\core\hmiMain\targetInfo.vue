<template>
    <div>
        <div class="info">
            <div class="header">
                <img src="@/assets/images/alarm.png" alt="">
                <span>{{ '板件CCD' }}</span>
                <img src="@/assets/images/close.png" alt="">
            </div>
            <div class="line"></div>
            <p>板件码:<span>0333330001</span></p>
            <p>识别结果:<span>NG|混板</span></p>
            <p>板件属性:<span>正常板</span></p>
            <p>面次：<span>L</span>&nbsp;&nbsp;&nbsp;&nbsp;旋转：<span>0度</span></p>
            <p>读码时间:<span>19:51:01</span></p>
            <p>耗时:<span>200ms</span></p>
        </div>
        <!-- <div class="info">

        </div>
        <div class="info">

        </div> -->
    </div>
</template>
<script>
export default {
    data() {
        return {

        }
    }
}
</script>
<style lang="less" scoped>
    .info{
        position: absolute;
        top: 20px;
        left: 20px;
        width: 200px;
        background: var(--el-dialog-bgColor);
        animation: fadeio 4s infinite;
        animation-iteration-count: 1;
        opacity: 0.8;
        border-radius: 5px;
        padding: 5px;
        border: 1px solid #fff;
        .header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            img{
                width: 25px;
            }
            span{
                font-weight: 600;
                font-size: 18px;
                color: var(--el-dialog-textColor);
            }
        }
        .line{
            width: 100%;
            height: 1px;
            background: #fff;
        }
        p{
            color: var(--el-dialog-textColor);
            span{
                font-weight: 600;
            }
        }
    }
    @keyframes fadeio {
    /*设置内容由显示变为隐藏*/
    0% {
        opacity: 0;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
</style>
