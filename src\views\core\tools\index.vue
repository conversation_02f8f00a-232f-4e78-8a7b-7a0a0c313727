<template>
  <elFrame :src="toolsSrc" />
</template>
<script>

//https://blog.csdn.net/qq_28200505/article/details/105629605 VUE+ElementUI 实现左侧菜单栏+Tab页访问本地页面和远程页面，非iFrame方式
//https://blog.csdn.net/wade3po/article/details/101361941 vue-cli3创建多页面项目
//https://www.cnblogs.com/zhaoxiaoying/p/10647545.html vue-cli3 创建多页面应用项目
import { mapGetters } from 'vuex'
import elFrame from '@/components/Iframe/index'
import { selIframe } from '@/api/core/system/sysFunction'

export default {
  name: 'PageModel',
  components: { elFrame },
    computed: {
      ...mapGetters([
        'toolsSrc'
    ])
  },
  data () {
    return {
      toolsSrc: ''  //显示界面
    }
  },
  computed: {
      theme() {
        return this.$store.getters.THEME_COLOR
      }
  },
  mounted: function() {
      console.log("【tools.index.vue】this.$route.path："+this.$route.path)
      //数据格式：/5/menu1
      console.log(this.$route.path.split('/')[2])

      this.toQuery(this.$route.path.split('/')[2])
  },
  methods:{
    //查询
    toQuery(function_refer){
      //格式化查询条件
      const query = {
          userID: '-1',
          function_refer: function_refer
      }
      selIframe(query).then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code == 0) {
            if(defaultQuery.data.length > 0){
               this.toolsSrc = defaultQuery.data[0].function_attr
            }
          }
        }).catch(() => {
        })
    }
  }
}
</script>
