<template>
  <div>
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位编号/描述：">
                <el-input v-model="query.station" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="155px"
          :inline="true">
          <el-form-item label="工位" prop="station_id">
            <el-select v-model="form.station_id" filterable clearable @change="stationChage">
              <el-option v-for="item in stationData" :key="item.station_id"
                :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="工位名称" prop="station_des">
            <el-input v-model="form.station_des" />
          </el-form-item>
          <el-form-item label="子排序" prop="craft_route_order">
            <el-input v-model.number="form.craft_route_order" />
          </el-form-item>
          <el-form-item label="节拍(单位秒)" prop="beat_times">
            <el-input v-model="form.beat_times" />
          </el-form-item>
          <el-form-item label="路线相关属性" prop="craft_route_attr">
            <el-input v-model="form.craft_route_attr" />
          </el-form-item>
          <el-form-item label="程序号" prop="progm_num">
            <el-input v-model.number="form.progm_num" />
          </el-form-item>
          <el-form-item label="工艺指导文件名称" prop="craf_file_name">
            <el-input v-model="form.craf_file_name" />
          </el-form-item>
          <el-form-item label="工艺指导文件路径" prop="craf_file_path">
            <el-input v-model="form.craf_file_path" readonly="readonly">
              <el-button slot="append" icon="el-icon-upload" @click="handleUpload('craf_file_path')" />
            </el-input>
          </el-form-item>
          <el-form-item label="操作指导文件名称" prop="opera_file_name">
            <el-input v-model="form.opera_file_name" />
          </el-form-item>
          <el-form-item label="操作指导文件路径" prop="opera_file_path">
            <el-input v-model="form.opera_file_path" readonly="readonly">
              <el-button slot="append" icon="el-icon-upload" @click="handleUpload('opera_file_path')" />
            </el-input>
          </el-form-item>
          <el-form-item label="视频文件名称" prop="vidio_file_name">
            <el-input v-model="form.vidio_file_name" />
          </el-form-item>
          <el-form-item label="视频文件路径" prop="vidio_file_path">
            <el-input v-model="form.vidio_file_path" readonly="readonly">
              <el-button slot="append" icon="el-icon-upload" @click="handleUpload('vidio_file_path')" />
            </el-input>
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
            @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="false" :title="uploadDrawerTitle"
        :visible.sync="uploadDrawerVisbleSync" size="390px" @closed="handleUploadDrawerClose">
        <el-upload ref="upload" name="file" :multiple="false" action="" drag="" :limit="uploadLimit"
          :on-change="handleUploadOnChange" :http-request="handleUploadHttpRequest" :accept="uploadAccept"
          :auto-upload="false" :file-list="fileList">
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
        </el-upload>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="handleUploadDrawerCancel">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleUploadFile">上传</el-button>
        </div>
      </el-drawer>
      <el-dialog append-to-body :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false"
        :modal-append-to-body="false" title="导入质量数据" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload ref="upload" :multiple="false" class="upload-demo" action="" drag="" :limit="uploadLimit"
            :accept="uploadAccept" :auto-upload="false" :on-change="handleImport" :http-request="uploadFile"
            :on-progress="progressA" :file-list="fileList" name="file">
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input v-if="isUpLoadError" v-model="errorMsg" type="textarea" :rows="5" />
          <p>注：</p>
          <p>1、确认导入数据表格是从当前页面操作导出的工艺路线质量数据</p>
          <p>2、导入数据中A至F列为数据识别列，用于匹配是否是当前导入工艺路线数据，不可编辑</p>
          <div style="text-align: center;margin-top:10px">
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading"
              @click="toButDrawerUpload">导入</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small"
            :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="250px" highlight-current-row
            @selection-change="crud.selectionChangeHandler" @row-click="handleRowClick">
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.craft_route_id }}</el-descriptions-item>
                  <el-descriptions-item label="工位ID" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.station_id }}</el-descriptions-item>
                  <el-descriptions-item label="工位名称" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.station_des }}</el-descriptions-item>
                  <el-descriptions-item label="子排序" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.craft_route_order
                    }}</el-descriptions-item>
                  <el-descriptions-item label="节拍(秒)" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.beat_times }}</el-descriptions-item>
                  <el-descriptions-item label="路线相关属性" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.craft_route_attr
                    }}</el-descriptions-item>
                  <el-descriptions-item label="程序号" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.progm_num }}</el-descriptions-item>
                  <el-descriptions-item label="工艺指导文件名称" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.craf_file_name }}</el-descriptions-item>
                  <el-descriptions-item label="工艺指导文件路径" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.craf_file_path }}</el-descriptions-item>
                  <el-descriptions-item label="操作指导文件名称" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.opera_file_name }}</el-descriptions-item>
                  <el-descriptions-item label="操作指导文件路径" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.opera_file_path }}</el-descriptions-item>
                  <el-descriptions-item label="视频文件名称" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.vidio_file_name }}</el-descriptions-item>
                  <el-descriptions-item label="视频文件路径" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.vidio_file_path }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag]
                    }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content">{{ props.row.last_update_date
                    }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="station_code" min-width="100"
              label="工位编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_des" min-width="100"
              label="工位名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="craft_route_order" min-width="100"
              label="子排序" />
            <el-table-column  :show-overflow-tooltip="true" prop="beat_times" min-width="100"
              label="节拍(单位秒)" />
            <el-table-column  :show-overflow-tooltip="true" prop="craft_route_attr" min-width="100"
              label="路线相关属性" />
            <el-table-column  :show-overflow-tooltip="true" prop="progm_num" min-width="100"
              label="程序号" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949"
                  active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" prop="tableButton" width="150" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission">
                  <template slot="right">
                    <el-button slot="reference" type="text" size="small" style="margin-left:0px;"
                      @click="handlerExportQuality(scope.row)">导出</el-button>
                    <el-button slot="reference" type="text" size="small" style="margin-left:0px;"
                      @click="handlerImportQuality(scope.row)">导入</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <pdure ref="pdure" :craft_route_id="currentId" />
  </div>
</template>

<script>
import axios from 'axios'
import crudRecipeCrMain from '@/api/mes/core/recipeCrMain'
import { downloadFile } from '@/utils/index'
import crudRecipeCrStation from '@/api/mes/core/recipeCrStation'
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import pdure from '@/views/mes/core/craftRoute/pdure'
const defaultForm = {
  craft_route_id: '',
  craft_route_main_id: '',
  station_id: '',
  station_des: '',
  craft_route_order: '',
  beat_times: '',
  craft_route_attr: '',
  progm_num: '',
  craf_file_name: '',
  craf_file_path: '',
  opera_file_name: '',
  opera_file_path: '',
  vidio_file_name: '',
  vidio_file_path: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MES_RECIPE_CR_STATION',
  components: { crudOperation, rrOperation, udOperation, pagination, pdure },
  cruds() {
    return CRUD({
      title: '工位信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'craft_route_id',
      // 排序
      sort: ['craft_route_id asc'],
      // CRUD Method
      crudMethod: { ...crudRecipeCrStation },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    craft_route_main_id: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_cr_station:add'],
        edit: ['admin', 'mes_recipe_cr_station:edit'],
        del: ['admin', 'mes_recipe_cr_station:del'],
        down: ['admin', 'mes_recipe_cr_station:down']
      },
      rules: {
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        station_des: [{ required: true, message: '请输入工位名称', trigger: 'blur' }],
        craft_route_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        progm_num: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      currentId: 0,
      stationId: 0,
      stationData: [],
      // 文件上传
      currentUploadType: '',
      currentFilePath: '',
      uploadDrawerTitle: '文件上传',
      uploadDrawerVisbleSync: false,
      uploadLimit: 1,
      uploadAccept: '*.*',
      fileList: [],

      importDialogVisible: false,
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: ''
    }
  },
  watch: {
    craft_route_main_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.craft_route_main_id = this.craft_route_main_id
        defaultForm.craft_route_main_id = this.craft_route_main_id
      }
    }
  },

  mounted: function () {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function () {
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.craft_route_main_id = this.craft_route_main_id
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将工位为【' + data.station_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudRecipeCrStation
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              craft_route_id: data.craft_route_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    handleRowClick(row, column, event) {
      if (column.property !== 'tableButton') {
        this.currentId = row.craft_route_id
        this.$nextTick(() => {
          this.$refs.pdure.crud.toQuery()
        })
      }
    },
    stationChage(val) {
      const item = this.stationData.find(item => item.station_id === val)
      if (item !== undefined) {
        this.form.station_des = item.station_des
      } else {
        this.form.station_des = ''
      }
    },
    handleUploadDrawerClose() {
      this.fileList = []
    },
    handleUpload(type) {
      this.currentUploadType = type
      if (type === 'craf_file_path') {
        this.uploadDrawerTitle = '工艺指导文件上传'
        this.currentFilePath = 'craf_file'
        this.uploadAccept = '.pdf'
      } else if (type === 'opera_file_path') {
        this.uploadDrawerTitle = '操作指导文件上传'
        this.currentFilePath = 'opera_file'
        this.uploadAccept = '.pdf'
      } else if (type === 'vidio_file_path') {
        this.uploadDrawerTitle = '视频文件上传'
        this.currentFilePath = 'vidio_file'
        this.uploadAccept = '.mp4'
      }
      this.uploadDrawerVisbleSync = true
    },
    handleUploadDrawerCancel() {
      this.uploadDrawerVisbleSync = false
    },
    // 导入文件时将文件存入数组中
    handleUploadOnChange(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequest(file) {
      this.fileData.append('file', file.file)
    },
    // 处理上传文件
    handleUploadFile() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      this.fileData = new FormData()
      this.fileData.append('file_path', this.currentFilePath)
      this.$refs.upload.submit()

      // 配置路径
      var method = 'core/file/CoreFileUpload'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/cellApi/') + method

      const loading = this.$loading({
        lock: true,
        text: '上传文件处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            const fileInfo = JSON.parse(defaultQuery.data.result)
            if (this.currentUploadType === 'craf_file_path') {
              this.form.craf_file_name = fileInfo.file_name
              this.form.craf_file_path = fileInfo.file_dir_path
            } else if (this.currentUploadType === 'opera_file_path') {
              this.form.opera_file_name = fileInfo.file_name
              this.form.opera_file_path = fileInfo.file_dir_path
            } else if (this.currentUploadType === 'vidio_file_path') {
              this.form.vidio_file_name = fileInfo.file_name
              this.form.vidio_file_path = fileInfo.file_dir_path
            }
            this.uploadDrawerVisbleSync = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
        .catch(er => {
          loading.close()
          this.$message({
            message: '上传文件异常：' + er,
            type: 'error'
          })
        })
    },
    handlerExportQuality(row) {
      const loading = this.$loading({
        lock: true,
        text: '数据处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      crudRecipeCrMain.exportQualityData({
        craft_route_main_id: row.craft_route_main_id,
        station_id: row.station_id
      }).then(result => {
        loading.close()
        downloadFile(result, '工艺路线信息-' + row.station_des + '-质量数据数据', 'xlsx')
      }).catch(() => {
        loading.close()
      })
    },
    handlerImportQuality(row) {
      this.currentId = row.craft_route_main_id
      this.stationId=row.station_id
      this.errorMsg = ''
      this.isUpLoadError = false
      this.importDialogVisible = true
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function (file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) { },
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.fileData.set('craftRouteMainId', this.currentId.toString())
      this.fileData.set('stationId', this.stationId.toString())
      this.$refs.upload.submit()

      // 配置路径
      var method = '/mes/core/MesRecipeCrMainQualityImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      const loading = this.$loading({
        lock: true,
        text: '数据处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })

            this.fileList = []
            this.isUpLoadError = false
            loading.close()
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          loading.close()
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}

.table-descriptions-content {
  width: 150px;
}
</style>
