<template>
  <el-card shadow="always" style="margin-top: 10px">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :height="height"
          :highlight-current-row="true"
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column :show-overflow-tooltip="true" label="报警时间" align="center" prop="item_date" />
          <el-table-column :show-overflow-tooltip="true" label="警报代码" align="center" prop="code" />
          <el-table-column :show-overflow-tooltip="true" label="警报描述" align="center" prop="desc" />
          <el-table-column :show-overflow-tooltip="true" label="所属设备" align="center">{{ clientName }}</el-table-column>
        </el-table>
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import api from '@/api/dcs/project/xg/alarmHistory'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'

const defaultForm = {
  id: '',
  code: '',
  desc: '',
  client_id: null,
  enable_flag: 'Y'
}
export default {
  name: 'DCS_ALARM_HISTORY',
  components: { pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.dcsAlarm'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: [],
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      clientId: null,
      clientName: ''
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  methods: {
    load(clientId, clientName) {
      this.clientId = clientId
      this.clientName = clientName
      this.crud.query = {
        client_id: clientId,
        client_name: clientName
      }
      this.crud.toQuery()
    }
  }
}
</script>
