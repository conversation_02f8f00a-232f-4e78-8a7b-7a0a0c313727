<template>
  <div>
    <!--工具栏-->
    <el-button class="filter-item" size="small" type="primary" icon="el-icon-plus" style="margin-bottom: 10px" :disabled="step_condition_g_id == 0" plain round @click="crud.toAdd">
      {{ $t('lang_pack.commonPage.add') }}  <!-- 新增 -->
    </el-button>
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
        <el-form-item :label="$t('lang_pack.mainmain.conditionsDescribed')" prop="step_condition_i_des">  <!-- 条件描述 -->
          <el-input v-model="form.step_condition_i_des" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.mainmain.monitoringPoint')" prop="tag_id">  <!-- 监控点位 -->
          <el-input v-model.number="form.tag_id" readonly="readonly">
            <div slot="append">
              <el-popover v-model="customPopover" placement="left" width="650">
                <tagSelect ref="tagSelect" client-id-list="" :tag-id="form.tag_id" @chooseTag="handleChooseTag" />
                <el-button slot="reference">选择</el-button>
              </el-popover>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.mainmain.setUpInstructions')" prop="ok_remarks">  <!-- 成立说明 -->
          <el-input v-model="form.ok_remarks" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">  <!-- 有效标识 -->
          <el-radio-group v-model="form.enable_flag">
            <el-radio label="Y">有效</el-radio>
            <el-radio label="N">失效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button> <!-- 确认 -->
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="250px" @header-dragend="crud.tableHeaderDragend()" @selection-change="crud.selectionChangeHandler">
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="step_condition_i_id" label="id" />
          <el-table-column :show-overflow-tooltip="true" prop="step_condition_i_des" :label="$t('lang_pack.mainmain.conditionsDescribed')" />  <!-- 条件描述 -->
          <el-table-column :show-overflow-tooltip="true" prop="tag_id" :label="$t('lang_pack.mainmain.monitoringPoint')" />  <!-- 监控点位 -->
          <el-table-column :show-overflow-tooltip="true" prop="ok_remarks" :label="$t('lang_pack.mainmain.setUpInstructions')" />  <!-- 成立说明 -->
          <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">  <!-- 有效标识 -->
            <template slot-scope="scope">
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">  <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudFlowMainConditionI from '@/api/core/flow/rcsFlowMainConditionI'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
// import crudOperation from "@crud/CRUD.operation";
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  step_condition_i_id: 0,
  step_condition_g_id: 0,
  step_condition_i_des: '',
  tag_id: '',
  ok_remarks: '',
  enable_flag: 'Y'
}
export default {
  name: 'RCSFLOWMAINCONDITIONI',
  components: { udOperation, pagination },
  props: {
    step_condition_g_id: {
      type: [String, Number],
      default: 0
    }
  },
  cruds() {
    return CRUD({
      title: '属性',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'step_condition_i_id',
      // 排序
      sort: ['step_condition_i_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowMainConditionI },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'rcs_flow_main_condition_i:add'],
        edit: ['admin', 'rcs_flow_main_condition_i:edit'],
        del: ['admin', 'rcs_flow_main_condition_i:del']
      },
      rules: {
        step_condition_i_des: [{ required: true, message: '请输入条件描述', trigger: 'blur' }],
        tag_id: [{ required: true, message: '请选择监控点位', trigger: 'blur' }],
        ok_remarks: [{ required: true, message: '请输入成立说明', trigger: 'blur' }]
      },
      customPopover: false
    }
  },
  watch: {
    step_condition_g_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.step_condition_g_id = this.step_condition_g_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    handleChooseTag(tagId) {
      this.form.tag_id = tagId
      this.customPopover = false
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.step_condition_g_id = this.step_condition_g_id
      return true
    }
  }
}
</script>
