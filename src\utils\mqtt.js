import Paho from 'paho-mqtt'

class MQTT {
    clients;

    constructor() {
      this.clients = {}
    }

    init(host, port, onConnected, onMessage) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      const onSuccess = () => {
        console.debug(`ws://${host}:${port}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://${host}:${port}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: `连接服务器[${host}:${port}]失败：${responseObject.errorMessage}`, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: `与服务器[${host}:${port}]断开连接，5s后将会自动重连...`, type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        console.log('onMessageArrived:', message)
        const topic = message.destinationName
        const payload = message.payloadString
        onMessage && onMessage({ topic, payload })
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
}

export default new MQTT()
