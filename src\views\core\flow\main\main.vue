<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.mainmain.mainProcessCode')">
                <!-- 主流程编码： -->
                <el-input v-model="query.flow_main_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12" style="margin-left: 10px">
              <el-form-item :label="$t('lang_pack.mainmain.mainProcessDescription')">
                <!-- 主流程描述： -->
                <el-input v-model="query.flow_main_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('lang_pack.mainmain.basicAttribute')" name="first">
            <!-- 基础属性 -->
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="140px" :inline="true">
              <el-form-item :label="$t('lang_pack.mainmain.processTemplate')" prop="flow_mod_main_id">
                <!-- 流程模板 -->
                <el-select v-model="form.flow_mod_main_id" filterable clearable>
                  <el-option v-for="item in flowModMainData" :key="item.flow_mod_main_id" :label="item.flow_mod_main_code + ' ' + item.flow_mod_main_des" :value="item.flow_mod_main_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.station')" prop="station_id">
                <!-- 工位 -->
                <el-select v-model="form.station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.mainProcessCodet')" prop="flow_main_code">
                <!-- 主流程编码 -->
                <el-input v-model="form.flow_main_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.mainProcessDescriptiont')" prop="flow_main_des">
                <!-- 主流程描述 -->
                <el-input v-model="form.flow_main_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.taskNumberPrefix')" prop="flow_task_head">
                <!-- 任务编号前缀 -->
                <el-input v-model="form.flow_task_head" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.programProperties')" prop="function_attr">
                <!-- 程序属性 -->
                <el-input v-model="form.function_attr" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.processFlow')" prop="flow_main_ico">
                <!-- 流程图标 -->
                <el-input v-model="form.flow_main_ico" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.examplesCollections')" prop="client_id_list">
                <!-- 实例集合 -->
                <!-- <el-select v-model="form.client_id_list" filterable clearable multiple>
                  <el-option v-for="item in clientData" :key="item.client_id" :label="item.client_des" :value="item.client_id + ''" />
                </el-select> -->
                <!-- @focus="$refs.popupDialog.open()" -->
                <el-input v-model="form.client_id_list" readonly="readonly">
                  <div slot="append">
                    <el-button slot="reference" @click="$refs.popupDialog.open(form.client_id_list)">选择</el-button>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.triggerPoint')" prop="create_task_tag_id">
                <!-- 触发点位 -->
                <el-input v-model.number="form.create_task_tag_id" readonly="readonly">
                  <div slot="append">
                    <el-popover v-model="customPopover" placement="left" width="650">
                      <tagSelect ref="tagSelect" :client-id-list="form.client_id_list" :tag-id="form.create_task_tag_id" @chooseTag="handleChooseTag" />
                      <el-button slot="reference">选择</el-button>
                    </el-popover>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.triggerPointValue')" prop="create_task_tag_value">
                <!-- 触发点位值 -->
                <el-input v-model="form.create_task_tag_value" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.taskTriggeringMode')" prop="create_task_tag_way">
                <!-- 触发任务方式 -->
                <el-select v-model="form.create_task_tag_way" clearable>
                  <el-option v-for="item in [{ value: 'EQUAL', label: '值相等' }, { value: 'CHANGE', label: '值变化' }, { value: 'LIST', label: '值集合' }, { value: 'CHANGE_LIMIT_LEN', label: '固定长度值变化' }, { value: 'INITIAL_VALUE', label: '初始值' }]" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="Secs Code" prop="secs_code">
                <el-input v-model="form.secs_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.pollingTime')" prop="cycle_time">
                <!-- 轮询时间 -->
                <el-input v-model="form.cycle_time" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>

            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('lang_pack.mainmain.conditionsSet')" name="second" :disabled="form.flow_main_id === 0">
            <!-- 条件组 -->
            <conditionGroup v-if="crud.status.cu > 0" ref="conditionGroup" :flow_main_id="form.flow_main_id" :step_mod_id="0" />
          </el-tab-pane>
        </el-tabs>
      </el-drawer>
      <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="editFlowChart" direction="rtl" size="100%">
        <flowChart
          v-if="mainchartShow"
          ref="flowChart"
          :flow_main_id="currentRow.flow_main_id"
          :flow_mod_main_id="currentRow.flow_mod_main_id"
          :flow_mod_main_des="currentRow.flow_main_des"
          :client_id_list="currentRow.client_id_list"
          @closeDrawer="
            editFlowChart = false
            mainchartShow = false
          "
        />
      </el-drawer>
      <el-dialog :fullscreen="false" top="10px" :show-close="true" :close-on-click-modal="false" title="步骤属性设置" custom-class="step-attr-dialog" width="90%" :visible.sync="stepAttrDialogVisible">
        <stepAttr v-if="stepAttrDialogVisible" ref="stepAttr" :flow_main_id="currentRow.flow_main_id" :flow_mod_main_id="currentRow.flow_mod_main_id" :client_id_list="currentRow.client_id_list" />
        <div slot="footer" class="dialog-footer">
          <el-button @click="stepAttrDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAutoMatchAttr">快速匹配</el-button>
          <el-button type="primary" @click="handleSaveAttrData">保存属性</el-button>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="flow_main_id" label="id" />
            <el-table-column :show-overflow-tooltip="true" prop="station_id" :label="$t('lang_pack.mainmain.station')" width="280">
              <!-- 工位 -->
              <template slot-scope="scope">
                {{ getStationDes(scope.row.station_id) }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="flow_main_code" :label="$t('lang_pack.mainmain.mainProcessCodet')" width="280" />
            <!-- 主流程编码 -->
            <el-table-column :show-overflow-tooltip="true" prop="flow_main_des" :label="$t('lang_pack.mainmain.mainProcessDescriptiont')" width="280" />
            <!-- 主流程描述 -->
            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="属性" align="center">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendAttr(scope.row)">属性</el-tag>
              </template>
            </el-table-column>

            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                  <template slot="right">
                    <el-button size="small" type="text" @click="openFlowChart(scope.row)">设计</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
          <!-- 实例弹窗的公用组件 -->
          <popupDialog ref="popupDialog" :obj.sync="exampleObj" @updateAdd="updateAdd" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { sel as selFlowModMain } from '@/api/core/flow/rcsFlowModMain'
import crudFlowMain from '@/api/core/flow/rcsFlowMain'
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import flowChart from '@/views/core/flow/main/flow-chart'
import stepAttr from '@/views/core/flow/main/attr'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import conditionGroup from '@/views/core/flow/main/condition-group'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import popupDialog from '@/components/popupDialog/index'
const defaultForm = {
  flow_main_id: '',
  flow_mod_main_id: '',
  station_id: '',
  flow_main_code: '',
  flow_main_des: '',
  flow_task_head: '',
  function_attr: '',
  flow_main_ico: '',
  client_id_list: '',
  create_task_tag_id: '',
  create_task_tag_value: '',
  create_task_tag_way: '',
  secs_code: '',
  cycle_time: '',
  enable_flag: 'Y'
}
export default {
  name: 'RCS_FLOW_MAIN',
  components: {
    conditionGroup,
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    flowChart,
    stepAttr,
    popupDialog
  },
  cruds() {
    return CRUD({
      title: '主流程',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'flow_main_id',
      // 排序
      sort: ['flow_main_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowMain },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      activeName: 'first',
      permission: {
        add: ['admin', 'rcs_flow_main:add'],
        edit: ['admin', 'rcs_flow_main:edit'],
        del: ['admin', 'rcs_flow_main:del'],
        down: ['admin', 'rcs_flow_main:down']
      },
      rules: {
        flow_mod_main_id: [{ required: true, message: '请选择流程模板', trigger: 'blur' }],
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        flow_main_code: [{ required: true, message: '请输入主流程编码', trigger: 'blur' }],
        flow_main_des: [{ required: true, message: '请输入主流程描述', trigger: 'blur' }],
        flow_task_head: [{ required: true, message: '请输入任务编号前缀', trigger: 'blur' }],
        client_id_list: [{ required: true, message: '请选择实例集合', trigger: 'blur' }],
        create_task_tag_id: [{ required: true, message: '请选择触发点位', trigger: 'blur' }],
        cycle_time: [{ required: true, message: '请输入轮询时间', trigger: 'blur' }]
      },
      customPopover: false,
      clientData: [],
      stationData: [],
      currentRow: {},
      mainchartShow: false,
      editFlowChart: false,
      flowModMainData: [],
      stepAttrDialogVisible: false,
      exampleObj: {
        title: '实例集合',
        tableLable: [
          { prop: 'client_des', label: '描述' }
        ]
      }
    }
  },

  mounted: function() {
    const that = this
    that.$refs.table.doLayout()
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      user_name: Cookies.get('userName'),
      sort: 'client_id',
      enable_flag: 'Y'
    }
    // selScadaClient(query)
    //   .then(res => {
    //     const defaultQuery = JSON.parse(JSON.stringify(res))
    //     if (defaultQuery.code === 0) {
    //       if (defaultQuery.data.length > 0) {
    //         this.clientData = defaultQuery.data
    //       }
    //     }
    //   })
    //   .catch(() => {
    //     this.$message({
    //       message: '查询异常',
    //       type: 'error'
    //     })
    //   })
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    selFlowModMain(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.flowModMainData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    opendAttr(row) {
      this.currentRow = row
      this.stepAttrDialogVisible = true
    },
    handleChooseTag(tagId) {
      this.form.create_task_tag_id = tagId
      this.customPopover = false
    },
    // 获取角色的中文描述
    getStationDes(station_id) {
      var item = this.stationData.find(item => item.station_id === station_id)
      if (item !== undefined) {
        return item.station_code + ' ' + item.station_des
      }
      return station_id
    },
    openFlowChart(row) {
      this.currentRow = row
      this.mainchartShow = true
      this.editFlowChart = true
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.client_id_list = crud.form.client_id_list === '' ? '' : crud.form.client_id_list.split(',')
      this.activeName = 'first'
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.client_id_list = this.form.client_id_list === '' ? '' : this.form.client_id_list.join(',')
      return true
    },
    handleSaveAttrData() {
      this.$refs.stepAttr.saveAttrData()
    },
    handleAutoMatchAttr() {
      this.$refs.stepAttr.autoMatchAttr()
    },
    updateAdd(value) {
      const list = []
      if (value.length > 0) {
        value.forEach((e) => {
          list.push(e.client_id)
        })
      }
      this.form.client_id_list = list
    }
  }
}
</script>
<style lang="scss">
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px;
}
.step-attr-dialog .el-dialog__body {
  padding: 0px;
  overflow-y: auto;
}
</style>
