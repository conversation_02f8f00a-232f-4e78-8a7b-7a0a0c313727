<template>
  <div>
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
      <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
        <el-form-item label="token" prop="token">
          <el-input v-model="form.token" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.interf.remark')" prop="remarks">
          <!-- 备注 -->
          <el-input v-model="form.remarks" />
        </el-form-item>

        <el-form-item :label="$t('lang_pack.interf.cell')" prop="cell_id">
          <!-- 单元 -->
          <el-select v-model="form.cell_id" filterable>
            <el-option v-for="item in cellData" :key="item.cell_id" :label="item.cell_container_name + ' ' + item.cell_container_des" :value="item.cell_id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.interf.parameterValue')" prop="paras_list">
          <!-- 传参值 -->
          <el-input v-model="form.paras_list" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <!-- 有效标识 -->
          <el-radio-group v-model="form.enable_flag">
            <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确认 -->
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"  @selection-change="crud.selectionChangeHandler">
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="esb_interf_d_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="token" label="token" />
          <el-table-column  :show-overflow-tooltip="true" prop="remarks" :label="$t('lang_pack.interf.remark')" />
          <!-- 备注 -->
          <el-table-column  :show-overflow-tooltip="true" prop="cell_id" :label="$t('lang_pack.interf.cell')">
            <!-- 单元 -->
            <template slot-scope="scope">
              {{ getCelDes(scope.row.cell_id) }}
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="paras_list" :label="$t('lang_pack.interf.parameterValue')" width="150"/>
          <!-- 传参值 -->
          <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
            <!-- 有效标识 -->
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
            <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudInterfD from '@/api/core/center/interfItem'
import { sel as selCell } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
// import crudOperation from "@crud/CRUD.operation";
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  esb_interf_d_id: '',
  esb_interf_id: '',
  token: '',
  remarks: '',
  cell_id: 0,
  paras_list: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYSINTERFD',
  components: { udOperation, pagination },
  props: {
    esb_interf_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '接口明细维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'esb_interf_d_id',
      // 排序
      sort: ['esb_interf_d_id asc'],
      // CRUD Method
      crudMethod: { ...crudInterfD },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_interf_d:add'],
        edit: ['admin', 'sys_interf_d:edit'],
        del: ['admin', 'sys_interf_d:del']
      },
      rules: {
        token: [{ required: true, message: '请输入token', trigger: 'blur' }]
      },
      cellData: []
    }
  },
  watch: {
    esb_interf_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.esb_interf_id = this.esb_interf_id
        this.crud.toQuery()
      }
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {
    const query = {
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    selCell(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.cellData = defaultQuery.data
            this.cellData.push({ cell_id: 0, cell_container_name: '', cell_container_des: '通用参数' })
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.esb_interf_id = this.esb_interf_id
      return true
    },
    // 获取单元的中文描述
    getCelDes(cell_id) {
      var item = this.cellData.find(item => item.cell_id === cell_id)
      if (item !== undefined) {
        return item.cell_container_name + ' ' + item.cell_container_des
      }
      return cell_id
    }
  }
}
</script>
