*{
    margin: 0;
    padding: 0;
  }
  @font-face {
    font-family: YouSheBiaoTiHei;
    src: url('~@/assets/fonts/YouSheBiaoTiHei.ttf');
  }
  @font-face {
    font-family: light;
    src: url('~@/assets/fonts/LCD2B___.TTF');
  }
  .container-zlrm{
    background: radial-gradient(circle, #0a3179, #030d2a);
    .el-header{
      width: 100%;
      height: 105px !important;
      border-bottom: 1px solid #0d425c;
        .logoTitle{
          font-family: YouSheBiaoTiHei;
            color: #fff;
            font-size: 30px;
            font-weight: 600;
        }
        .title{
          color: #4ABBF5;
          font-size: 36px;
          letter-spacing: 5px;
          font-weight: 600;
        }
        .time{
          color: #fff;
          font-weight: 600;
            font-size: 30px;
        }
        .time1{
          color: #fff;
          font-weight: 600;
            font-size: 36px;
        }
      }
    .el-main{
      width: 100%;
      overflow: hidden;
      height: 595px!important;
      border: 1px solid #0d425c;
      .el-row{
        height: 100%;
        width: 100%;
        .el-col{
          height: 100%;
          width: 100%;
          padding-right: 0px   !important;
          .car-box{
            display: flex;
            justify-content: space-between;
            height: 90%;
            margin: 5px 0 0 5px;
            .car-box-left{
               width: 1.5%;   
               height: 100%;
               background-color: #0a275d;
               box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
               border-radius: 10px;
               p{
                text-align: right;
                font-size: 18px;
                color: #fff;
                height: 16.6%;
                display: flex;
                align-items: center;
                justify-content: center;
               }
            }
            .car-box-right{
              width: 98.2%; 
           }
          }
          .car-gra{
            display: flex;
            justify-content: space-between;
            margin: 5px 0 0 5px;
            width: 100%;
            height: 8%;
            background-color: #0a275d;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            .car-gra-left{
              width: 1.5%;
              height: 100%; 
            }
            .car-gra-right{
              width: 98.2%; 
              height: 100%;
              display: flex;
              justify-content: space-between;
              align-items: end;
              span{
                display: block;
                text-align: center;
                width: 68px;
                font-size: 16px;
                color: #fff;
                font-weight: 600;
              }
           }
          }
        }
      }
    }
    .footer{
      width: 100%;
      height: calc(100vh - 705px);
      display: flex;
      margin-top: 5px;
      justify-content: space-around;
      .box{
        height: 98%;
        background-color: rgba(3, 23, 81, 0.8); /* 设置背景颜色 */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 添加阴影 */
        position: relative;
        .image{
          display: flex;
          justify-content: right;
          img{
            width: 60%;
            height: 30px;
          }
        }
      }
      .footer-left{
        width: 28%;
        border-radius: 5px; /* 圆角 */
        padding: 0 5px;
        overflow: hidden; /* 超出内容时出现滚动条 */
        .header {
              padding: 5px; /* 边距 */
              position: relative;
              color: #00ffff;
              font-size: 22px;
              font-weight: 500;
              font-family: YouSheBiaoTiHei;
              svg{
                margin-right: 10px;
                filter: drop-shadow(0 0 5px #ffffff);
              }
          }
          table {
              width: 100%;
              border-collapse: collapse;
          }
          th, td {
            color: transparent; /* 关键： 初始颜色设置为透明 */
            background: linear-gradient(to right, #C1FFE7, #80FFAD); /* 设置背景渐变 */
            -webkit-background-clip: text; /* 裁剪背景到文字 */
            background-clip: text; /* 标准语法，覆盖旧的 */
            text-align: center;
            padding: 5px;
          }
      }
      .footer-centet{
        width: 42%;
        border-radius: 5px; /* 圆角 */
        padding: 0 5px;
        overflow: hidden; /* 超出内容时出现滚动条 */
        .header {
          padding: 5px; /* 边距 */
              position: relative;
              color: #00ffff;
              font-size: 22px;
              font-weight: 500;
              font-family: YouSheBiaoTiHei;
              svg{
                margin-right: 10px;
                filter: drop-shadow(0 0 5px #ffffff);
              }
        }
        table {
              width: 100%;
              border-collapse: collapse;
          }
          th, td {
            color: transparent; /* 关键： 初始颜色设置为透明 */
            background: linear-gradient(to right, #C1FFE7, #80FFAD); /* 设置背景渐变 */
            -webkit-background-clip: text; /* 裁剪背景到文字 */
            background-clip: text; /* 标准语法，覆盖旧的 */
            text-align: center;
            padding: 5px;
          }
      }
      .footer-right{
        width: 28%;
        border-radius: 5px; /* 圆角 */
        padding: 0 5px;
        overflow: hidden;
        .header {
          padding: 5px; /* 边距 */
              position: relative;
              color: #00ffff;
              font-size: 22px;
              font-weight: 500;
              font-family: YouSheBiaoTiHei;
              svg{
                margin-right: 10px;
                filter: drop-shadow(0 0 5px #ffffff);
              }
        }
        table {
              width: 100%;
              border-collapse: collapse;
          }
          th, td {
            color: transparent; /* 关键： 初始颜色设置为透明 */
            background: linear-gradient(to right, #C1FFE7, #80FFAD); /* 设置背景渐变 */
            -webkit-background-clip: text; /* 裁剪背景到文字 */
            background-clip: text; /* 标准语法，覆盖旧的 */
            text-align: center;
            padding: 5px;
          }
      }
    }
  }
  
  .table-wrapper{
    overflow-y: auto;
    max-height: calc(100vh - 753px); /* 可以根据需求调整最大高度 */
    .event {
      background-color: rgba(25, 68, 138, 0.5);
      td,th{
        color: #68D7EE !important;
      }
    }
  }
  .table-wrapper::-webkit-scrollbar {
      width: 3px;
      height: 3px;
      background-color: #122450;
      cursor: pointer !important;
    }
  
  .table-wrapper::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: aqua;
    cursor: pointer !important;
  }
  .fjTable{
    ::v-deep .el-dialog{
      .el-dialog__title{
        font-family: YouSheBiaoTiHei;
        color: #00ffff;
        font-size: 22px;
        font-weight: 600;
      }
        .el-dialog__header,.el-dialog__body{
            background-color: rgba(0, 26, 105, 0.9) !important;
        }
        .el-card__body{
            padding: 0!important;
            .el-table__header-wrapper{
              .el-table th{
                background-color: #00ffff;
              }
            }
        }
        .el-dialog__body{
            padding: 10px!important;
        }
    }
  }
