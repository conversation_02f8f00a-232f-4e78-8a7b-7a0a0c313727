<template>
  <div>
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码/描述：">
                <el-input v-model="query.material" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="195px" :inline="true">
          <el-form-item label="物料编码" prop="material_code">
            <el-input v-model="form.material_code" />
          </el-form-item>
          <el-form-item label="物料描述" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item>
          <el-form-item label="零件图号" prop="draw_code">
            <el-input v-model="form.draw_code" />
          </el-form-item>
          <el-form-item label="配套用量" prop="usage">
            <el-input v-model.number="form.usage" />
          </el-form-item>
          <el-form-item label="单位" prop="material_uom">
            <el-input v-model="form.material_uom" />
          </el-form-item>
          <el-form-item label="物料属性" prop="material_attr">
            <el-select v-model="query.material_attr" clearable>
              <el-option v-for="item in dict.MATERIAL_ATTR" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="物料单位成本" prop="material_cost">
            <el-input v-model="form.material_cost" />
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="form.remarks" />
          </el-form-item>
          <el-form-item label="最大数量" prop="max_count">
            <el-input v-model.number="form.max_count" />
          </el-form-item>
          <el-form-item label="安全库存" prop="min_count">
            <el-input v-model.number="form.min_count" />
          </el-form-item>
          <el-form-item label="料架编码" prop="rack_code">
            <el-input v-model="form.rack_code" />
          </el-form-item>
          <el-form-item label="料架最大承载容器数" prop="rack_max_count">
            <el-input v-model.number="form.rack_max_count" />
          </el-form-item>
          <el-form-item label="料架安全承载容器数" prop="rack_min_count">
            <el-input v-model.number="form.rack_min_count" />
          </el-form-item>
          <el-form-item label="标准容器规格" prop="box_type">
            <el-input v-model="form.box_type" />
          </el-form-item>
          <el-form-item label="标准容器承载数量" prop="box_set">
            <el-input v-model.number="form.box_set" />
          </el-form-item>
          <el-form-item label="零件标号" prop="material_index">
            <el-input v-model.number="form.material_index" />
          </el-form-item>
          <el-form-item label="零件规格" prop="material_gg">
            <el-input v-model="form.material_gg" />
          </el-form-item>
          <el-form-item label="是否主物料" prop="main_material_flag">
            <el-select v-model="form.main_material_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否精准追溯验证" prop="verify_flag">
            <el-select v-model="form.verify_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否批次验证" prop="batch_flag">
            <el-select v-model="form.batch_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="精准追溯物料验证规则" prop="material_rule_exact">
            <el-input v-model="form.material_rule_exact" />
          </el-form-item>
          <el-form-item label="批次追溯物料验证规则" prop="material_rule_card">
            <el-input v-model="form.material_rule_card" />
          </el-form-item>
          <el-form-item label="是否库存管理" prop="stock_flag">
            <el-select v-model="form.stock_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="物料版本号" prop="bom_version">
            <el-input v-model="form.bom_version" />
          </el-form-item>
          <el-form-item label="是否防重" prop="fchong_flag">
            <el-select v-model="form.fchong_flag">
              <el-option v-for="item in [{ label: '否', value: 'N' }, { label: '是', value: 'Y' }]" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;margin-bottom:10px;">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="物料ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_id }}</el-descriptions-item>
                  <el-descriptions-item label="物料编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                  <el-descriptions-item label="物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_des }}</el-descriptions-item>
                  <el-descriptions-item label="零件图号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.draw_code }}</el-descriptions-item>
                  <el-descriptions-item label="配套用量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.usage }}</el-descriptions-item>
                  <el-descriptions-item label="单位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_uom }}</el-descriptions-item>
                  <el-descriptions-item label="物料属性" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.MATERIAL_ATTR[props.row.material_attr] }}</el-descriptions-item>
                  <el-descriptions-item label="物料单位成本" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_cost }}</el-descriptions-item>
                  <el-descriptions-item label="备注" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.remarks }}</el-descriptions-item>
                  <el-descriptions-item label="最大数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.max_count }}</el-descriptions-item>
                  <el-descriptions-item label="安全库存" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.min_count }}</el-descriptions-item>
                  <el-descriptions-item label="料架编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rack_code }}</el-descriptions-item>
                  <el-descriptions-item label="料架最大承载容器数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rack_max_count }}</el-descriptions-item>
                  <el-descriptions-item label="料架安全承载容器数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rack_min_count }}</el-descriptions-item>
                  <el-descriptions-item label="标准容器规格" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_type }}</el-descriptions-item>
                  <el-descriptions-item label="标准容器承载数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_set }}</el-descriptions-item>
                  <el-descriptions-item label="零件标号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_index }}</el-descriptions-item>
                  <el-descriptions-item label="零件规格" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_gg }}</el-descriptions-item>
                  <el-descriptions-item label="是否主物料" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.main_material_flag }}</el-descriptions-item>
                  <el-descriptions-item label="是否精准追溯验证" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.verify_flag }}</el-descriptions-item>
                  <el-descriptions-item label="是否批次验证" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.batch_flag }}</el-descriptions-item>
                  <el-descriptions-item label="精准追溯物料验证规则" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_exact }}</el-descriptions-item>
                  <el-descriptions-item label="批次追溯物料验证规则" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_card }}</el-descriptions-item>
                  <el-descriptions-item label="是否库存管理" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_flag }}</el-descriptions-item>
                  <el-descriptions-item label="物料版本号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bom_version }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="material_code" min-width="100" label="物料编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_des" min-width="100" label="物料描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="draw_code" min-width="100" label="零件图号" />
            <el-table-column  :show-overflow-tooltip="true" prop="usage" min-width="100" label="配套用量" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_uom" min-width="100" label="单位" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_attr" min-width="100" label="物料属性" />
            <el-table-column  :show-overflow-tooltip="true" prop="bom_version" min-width="100" label="物料版本号" />
            <el-table-column  :show-overflow-tooltip="true" prop="fchong_flag" min-width="100" label="是否防重">
              <template slot-scope="scope">
                {{ scope.row.fchong_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudrecipeCrPdureBom from '@/api/mes/core/recipeCrPdureBom'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  material_id: '',
  proceduce_id: '',
  material_code: '',
  material_des: '',
  draw_code: '',
  usage: '',
  material_uom: '',
  material_attr: '',
  material_cost: '',
  remarks: '',
  max_count: '',
  min_count: '',
  rack_code: '',
  rack_max_count: '',
  rack_min_count: '',
  box_type: '',
  box_set: '',
  material_index: '',
  material_gg: '',
  main_material_flag: 'N',
  verify_flag: 'N',
  batch_flag: 'N',
  material_rule_exact: '',
  material_rule_card: '',
  stock_flag: 'N',
  bom_version: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MES_RECIPE_CR_PDURE_BOM',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '物料信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_id',
      // 排序
      sort: ['material_id asc'],
      // CRUD Method
      crudMethod: { ...crudrecipeCrPdureBom },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'MATERIAL_ATTR'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    proceduce_id: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback()
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    var checkUsage = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('请输入配套用量'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_cr_pdure_bom:add'],
        edit: ['admin', 'mes_recipe_cr_pdure_bom:edit'],
        del: ['admin', 'mes_recipe_cr_pdure_bom:del'],
        down: ['admin', 'mes_recipe_cr_pdure_bom:down']
      },
      rules: {
        material_code: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
        material_des: [{ required: true, message: '请输入物料描述', trigger: 'blur' }],
        usage: [{ required: true, validator: checkUsage, trigger: 'blur' }],
        material_cost: [
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,7})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
            message: '请输入正确格式,可保留七位小数'
          }
        ],
        max_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        min_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        rack_max_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        rack_min_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        box_set: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        material_index: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      }
    }
  },
  watch: {
    proceduce_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.proceduce_id = this.proceduce_id
        defaultForm.proceduce_id = this.proceduce_id
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.proceduce_id = this.proceduce_id
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将物料为【' + data.material_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudrecipeCrPdureBom
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              material_id: data.material_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
