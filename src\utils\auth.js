// Token操作
import Cookies from 'js-cookie'
import Config from '@/settings'

const TokenKey = Config.TokenKey

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token, rememberMe) {
  if (rememberMe) {
    return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: 1 })// 记住密码状态下的token在Cookie中存储的天数，默认1天
  } else return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
