<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位号:">
                <!-- 工位号 -->
                <el-select v-model="query.station_code">
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_id"
                    :label="item.station_code"
                    :value="item.station_code"
                  >
                    <span style="float: left">{{ item.station_code }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.station_des }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="机器人编码:">
                <!-- 机器人编码 -->
                <el-input v-model="query.robot_id" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件条码:">
                <!-- 零件条码 -->
                <el-input v-model="query.part_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件编码:">
                <!-- 零件编码 -->
                <el-input v-model="query.part_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件类型:">
                <!-- 零件类型 -->
                <el-select v-model="query.part_type" clearable filterable>
                  <el-option
                    v-for="item in dict.PART_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料号:">
                <!-- 零件条码 -->
                <el-input v-model="query.material_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="分拣开始时间:">
                <!-- 分拣开始时间 -->
                <el-date-picker
                  v-model="query.fj_start_time"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="分拣结束时间:">
                <!-- 分拣结束时间 -->
                <el-date-picker
                  v-model="query.fj_end_time"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item
                :label="$t('lang_pack.sortingResults.stationCode')"
                prop="station_code"
              >
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.partBarcodeNumber')"
                prop="part_barcode"
              >
                <!-- 零件条码 -->
                <el-input v-model="form.part_barcode" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.partCode')"
                prop="part_code"
              >
                <!-- 零件编码 -->
                <el-input v-model="form.part_code" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.PartType')"
                prop="part_type"
              >
                <!-- 零件类型 -->
                <el-input v-model="form.part_type" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.partMaterialNumber')"
                prop="material_num"
              >
                <!-- 零件图号 -->
                <el-input v-model="form.material_num" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="crud.cancelCU"
              >{{ $t("lang_pack.commonPage.cancel") }}</el-button>

              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions
                  style="margin-right: 150px"
                  :column="4"
                  size="small"
                  border
                >
                  <el-descriptions-item
                    label="ID"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.mo_resolve_id }}</el-descriptions-item>
                  <el-descriptions-item
                    label="主任务ID"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.mo_id }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件分拣所在工位号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.station_code }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分拣机器人编码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.robot_id }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件条码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_barcode }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件编码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_code }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件图号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_draw }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分拣顺序号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_index }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件类型"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.PART_TYPE[props.row.part_type]
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分拣结果代码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    props.row.fj_code == "0" ? "正常" : "异常"
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="消息/异常信息"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.fj_msg }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分拣开始时间"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.fj_start_time }}</el-descriptions-item>
                  <el-descriptions-item
                    label="分拣结束时间"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.fj_end_time }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 工位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              :label="$t('lang_pack.sortingResults.stationCode')"
              min-width="80"
              align="center"
            />
            <!-- 机器人编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="robot_id"
              :label="$t('lang_pack.sortingResults.robotCoding')"
              min-width="80"
              align="center"
            />
            <!-- 零件条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_barcode"
              :label="$t('lang_pack.sortingResults.partBarcodeNumber')"
              min-width="80"
              align="center"
            />
            <!-- 零件编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_code"
              :label="$t('lang_pack.sortingResults.partCode')"
              min-width="80"
              align="center"
            />
            <!-- 零件图号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_draw"
              :label="$t('lang_pack.sortingResults.partDrawingNumber')"
              min-width="80"
              align="center"
            />
            <!-- 分拣顺序号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_index"
              :label="$t('lang_pack.sortingResults.sortSequenceNumber')"
              min-width="80"
              align="center"
            />
            <!-- 零件类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_type"
              :label="$t('lang_pack.sortingResults.PartType')"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PART_TYPE[scope.row.part_type] }}
              </template>
            </el-table-column>
            <!-- 物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_num"
              :label="$t('lang_pack.sortingResults.partMaterialNumber')"
              width="120"
              align="center"
            />
            <!-- NC文件名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="nc_name"
              :label="$t('lang_pack.sortingResults.NCFileName')"
              width="120"
              align="center"
            />
            <!-- 分拣结果代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="fj_code"
              :label="$t('lang_pack.sortingResults.sortResultCode')"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.fj_code == 0 ? "正常" : "异常" }}
              </template>
            </el-table-column>
            <!-- 消息/异常信息 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="print_top_x"
              :label="$t('lang_pack.sortingResults.exceptionInformation')"
              min-width="80"
              align="center"
            />
            <!-- 分拣开始时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="fj_start_time"
              :label="$t('lang_pack.sortingResults.sortStartTime')"
              min-width="130"
              align="center"
            />
            <!-- 分拣结束时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="fj_end_time"
              :label="$t('lang_pack.sortingResults.sortEndTime')"
              min-width="130"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudSortResult from '@/api/dcs/core/me/sortResult'
import crudStation from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  sort_result_id: '',
  mo_id: '',
  station_code: '',
  robot_id: '',
  part_barcode: '',
  part_code: '',
  part_index: '',
  part_type: '',
  fj_code: '',
  fj_msg: '',
  fj_start_time: '',
  fj_end_time: ''
}
export default {
  name: 'WEB_SORT_RESULT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '生产任务分拣结果',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'sort_result_id',
      // 排序
      sort: ['sort_result_id asc'],
      // CRUD Method
      crudMethod: { ...crudSortResult },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 350,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_from: [
          { required: true, message: '请选择工位', trigger: 'blur' }
        ]
      },
      stationData: []
    }
  },
  dicts: ['PART_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 350
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    crudStation
      .sel(query)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data || []
          }
        } else {
          this.stationData = []
          this.$message({
            message: '工位查询异常',
            type: 'error'
          })
        }
      })
      .catch(() => {
        this.stationData = []
        this.$message({
          message: '工位查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm(
        '确定要将有效标识修改为【' +
          (data.enable_flag === 'Y' ? '有效' : '无效') +
          '】吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          crudSortResult
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              mo_id: data.mo_id,
              enable_flag: val
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({
                  message: '操作失败：' + res.msg,
                  type: 'error'
                })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch((ex) => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
