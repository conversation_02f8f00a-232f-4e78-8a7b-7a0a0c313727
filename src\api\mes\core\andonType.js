import request from '@/utils/request'

// 产线-安灯大类型查询,树形结构
export function queryAllMesAndonTypeTree(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonTypeTreeSelect',
    method: 'post',
    data
  })
}
// 安灯大类型查询
export function queryAllMesAndonType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonTypeSelect',
    method: 'post',
    data
  })
}

// 安灯大类型增加
export function insMesAndonType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonTypeInsert',
    method: 'post',
    data
  })
}

// 安灯大类型修改
export function updMesAndonType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonTypeUpdate',
    method: 'post',
    data
  })
}

// 安灯大类型删除
export function delMesAndonType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonTypeDelete',
    method: 'post',
    data
  })
}

export default { queryAllMesAndonTypeTree, queryAllMesAndonType, insMesAndonType, updMesAndonType, delMesAndonType }
