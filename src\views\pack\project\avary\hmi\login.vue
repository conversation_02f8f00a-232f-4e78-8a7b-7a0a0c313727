<template>
  <div class="login" :style="'background-image:url(' + Background + ');'">
    <div class="cloud-bubble-one" :style="'background-image:url(' + cloudBackBule1 + ');'" />
    <div class="cloud-bubble-two" :style="'background-image:url(' + cloudBackBule2 + ');'" />
    <div class="cloud-bubble-three" :style="'background-image:url(' + cloudBackBule3 + ');'" />
    <div class="u-f u-f-ac wrapeldropdown">
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link"> {{ $t('lang_pack.locale') }}<i class="el-icon-arrow-down el-icon--right" /> </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
          <el-dropdown-item command="zh-TW">繁体</el-dropdown-item>
          <el-dropdown-item command="th">Thai</el-dropdown-item>
          <el-dropdown-item command="en-US">English</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <img class="imgLeft" :src="LoginLeftImage">
        <!--form 表单-->
        <!--rules表单验证-->
        <el-form ref="loginForm" class="loginForm" :model="loginForm" :rules="loginRules" label-position="left">
          <h3 class="title">{{ $t('lang_pack.SystemName') }}</h3>
          <!--绑定prop属性-->
          <el-form-item prop="username" style="margin-top:30px;">
            <span>{{ $t('lang_pack.UserName') }}：</span>
            <el-input ref="username" v-model="loginForm.username" type="text" auto-complete="off" />
          </el-form-item>
          <el-form-item class="passwordStyle" prop="password">
            <span>{{ $t('lang_pack.Password') }}：</span>
            <el-input ref="password" v-model="loginForm.password" type="password" auto-complete="off" @keyup.enter.native="handleLogin" />
          </el-form-item>

          <!-- <el-form-item class="passwordStyle">
            <span>系统语言：</span>
            <div class="u-f u-f-ac wrapeldropdown">
              <el-dropdown @command="handleCommand">
                <span class="el-dropdown-link"> {{ $t('lang_pack.locale') }}<i class="el-icon-arrow-down el-icon--right" /> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
                  <el-dropdown-item command="zh-TW">繁体</el-dropdown-item>
                  <el-dropdown-item command="en-US">English</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </el-form-item> -->
          <!-- <el-checkbox
            v-model="loginForm.rememberMe"
            style="margin: 0 0 25px 0"
          >
            <span style="color: #707070; font-size: 13px">记住我</span>
          </el-checkbox> -->
          <el-form-item style="width: 100%;text-align:center;margin-top:40px;">
            <span class="jianpan" @click="showKeyboard">
              <img :src="keyboard">
            </span>
            <el-button :loading="loading" size="medium" type="primary" class="login_btn" @click.native.prevent="handleLogin">
              <span v-if="!loading">{{ $t('lang_pack.SignOn') }}</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <!--  底部  -->
    <!--是否显示设置的底部信息-->
    <div v-if="$store.state.settings.showFooter" id="el-login-footer" style="color: #FFFFFF">
      <!--底部文字，支持html语法-->
      <span v-html="$store.state.settings.footerTxt" />
      <!-- <span> ⋅ </span> -->
      <!--备案号-->
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{ $store.state.settings.caseNumber }}</a>
    </div>
    <!-- <div id="particles-js">
      <canvas class="particles-js-canvas-el" width="1536" height="658" style="width: 100%; height: 100%;" />
    </div> -->
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
    <updatePass ref="pass" />
  </div>
</template>

<script>
import Config from '@/settings'
import Cookies from 'js-cookie'
import Background from '@/assets/images/loginbj1.jpg'
import keyboard from '@/assets/images/keyboard.png'
import cloudBackBule1 from '@/assets/images/cloud-back-bule1.png'
import cloudBackBule2 from '@/assets/images/cloud-back-bule2.png'
import cloudBackBule3 from '@/assets/images/cloud-back-bule3.png'
import LoginLeftImage from '@/assets/images/hmiLoginLeft.gif'
import { getLoginLog } from '@/api/login'
import updatePass from '@/views/core/system/user/updatePass'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
// import { encrypt } from '@/utils/rsaEncrypt'

import { sel } from '@/api/core/system/sysParameter'
import mqtt from '@/utils/mqtt'

export default {
  name: 'whiteLogin',
  components: {
    SimpleKeyboard,
    updatePass
  },
  data() {
    return {
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      cloudBackBule1: cloudBackBule1,
      cloudBackBule2: cloudBackBule2,
      cloudBackBule3: cloudBackBule3,
      Background: Background,
      LoginLeftImage: LoginLeftImage,
      // codeUrl: '',
      // 判断 密码是否和上次输入密码一致
      cookiePass: '',
      loginForm: {
        username: '',
        password: '',
        // rememberMe: false,
        // code: '',
        uuid: ''
      },
      getLog: {
        userCode: '',
        stationCode: '',
        terminalType: 'IN'
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: this.$t('lang_pack.header.usernameRequire') }],
        password: [{ required: true, trigger: 'blur', message: this.$t('lang_pack.header.passwordRequire') }]
      },
      loading: false,
      redirect: undefined,
      action: ''
    }
  },
  // watch：它可以用来监测(相应数据的变化)Vue实例上的数据变动
  // immediate：在最初绑定值的时候也执行函数
  // handler：

  // $route：
  // $route.path 字符串，等于当前路由对象的路径，会被解析为绝对路径，如 "/home/<USER>" 。
  // $route.params 对象，包含路由中的动态片段和全匹配片段的键值对
  // $route.query 对象，包含路由中查询参数的键值对。例如，对于 /home/<USER>/detail/01?favorite=yes ，会得到$route.query.favorite == 'yes' 。
  // $route.router 路由规则所属的路由器（以及其所属的组件）。
  // $route.matched 数组，包含当前匹配的路径中所包含的所有片段所对应的配置参数对象。
  // $route.name 当前路径的名字，如果没有使用具名路径，则名字为空。

  // $router
  // watch: {
  //   $route: {
  //     handler: function(route) {
  //       this.redirect = route.query && route.query.redirect
  //     },
  //     immediate: true
  //   }
  // },
  // created：生命周期钩子函数，就是一个vue实例被生成后调用这个函数
  created() {
    // // 获取验证码
    // this.getCode()
    // 获取用户名、密码等Cookie
    this.getCookie()
    // token 过期提示
    this.point()
    // 查询系统参数
    const query = {
      user_name: Cookies.get('userName'),
      parameter_code: 'SYS_AUTH_DEVICE',
      enable_flag: 'Y'
    }
    sel(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            const parasInfo = defaultQuery.data[0]
            const parasInfoVal = parasInfo['parameter_val'] && JSON.parse(parasInfo['parameter_val'])
            const mqttSettings = parasInfoVal && parasInfoVal['mqtt']
            if (mqttSettings) {
              const tag = parasInfoVal['tag']
              const host = mqttSettings['host']
              const port = mqttSettings['port']
              if (host && port && tag) {
                mqtt.init(
                  host,
                  port,
                  (c) => {
                    if (c.isConnected()) {
                      c.subscribe(`SCADA_CHANGE/${tag}`, {
                        onSuccess: () => {
                          console.log('subscribe success.')
                        },
                        onFailure: (responseObject) => {
                          console.error('subscribe fail:', responseObject.errorMessage)
                        }
                      })
                    }
                  },
                  (data) => {
                    const { payload } = data
                    const result = JSON.parse(payload)
                    const resultTagValue = result && result['TagNewValue'] && JSON.parse(result['TagNewValue'])
                    const log = {
                      user_code: this.getLog.userCode,
                      station_code: this.getLog.stationCode,
                      terminal_type: this.getLog.terminalType
                    }
                    this.$store
                      .dispatch('mqttLogin', resultTagValue)
                      .then(() => {
                        this.loading = false
                        this.$router.push('/hmi_main')
                        // this.$router.push({ path: '/home' })
                        function getTimer() {
                          var time = new Date()
                          var year = time.getFullYear()
                          var month = time.getMonth() + 1
                          month = month < 10 ? '0' + month : month
                          var dates = time.getDate()
                          dates = dates < 10 ? '0' + dates : dates
                          var h = time.getHours()
                          h = h < 10 ? '0' + h : h
                          var m = time.getMinutes()
                          m = m < 10 ? '0' + m : m
                          var s = time.getSeconds()
                          s = s < 10 ? '0' + s : s
                          return year + '-' + month + '-' + dates + ' ' + h + ':' + m + ':' + s
                        }
                        getLoginLog(log)
                        // console.log(getTimer())
                        Cookies.set('OnlineTime', getTimer())
                      })
                      .catch(() => {
                        // 异步请求失败，获取新的验证码重新登录
                        this.loading = false
                        // this.getCode()
                      })
                  }
                )
              }
            }
          }
        }
      })
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  methods: {
    // 获取验证码
    // getCode() {
    //   getSysCodeImg().then(res => {
    //     this.codeUrl = res.img
    //     this.loginForm.uuid = res.uuid
    //     // 记录验证码(失效时间为1分钟)
    //     var millisecond = new Date().getTime()
    //     var expiresTime = new Date(millisecond + 60 * 1000 * 1)
    //     Cookies.set('code', res.code, { expires: expiresTime })
    //   })
    // },
    // 获取用户名、密码等Cookie
    getCookie() {
      // const username = Cookies.get('username')
      // let password = Cookies.get('password')
      // const rememberMe = Cookies.get('rememberMe') //记住我
      // // 保存cookie里面的加密后的密码
      // this.cookiePass = password === undefined ? '' : password
      // password = password === undefined ? this.loginForm.password : password
      // this.loginForm = {
      //  username: username === undefined ? this.loginForm.username : username,
      //  password: password,
      //  rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      //  code: ''
      // }
    },
    // 登录
    handleLogin() {
      // $refs 是一个对象，持有已注册过 ref 的所有的子组件
      // 表单验证validate
      this.$refs.loginForm.validate(valid => {
        const user = {
          username: this.loginForm.username,
          password: this.loginForm.password,
          // rememberMe: this.loginForm.rememberMe,
          // code: this.loginForm.code,
          uuid: this.loginForm.uuid
        }
        const log = {
          user_code: this.getLog.userCode,
          station_code: this.getLog.stationCode,
          terminal_type: this.getLog.terminalType
        }

        // 验证码校验
        // const code = Cookies.get('code')
        // if (code === undefined || code.toUpperCase() !== this.loginForm.code.toUpperCase()) {
        //   this.$notify({
        //     title: '提示',
        //     message: '验证码不存在或已过期！',
        //     type: 'warning',
        //     duration: 5000
        //   })
        //   // 异步请求失败，获取新的验证码重新登录
        //   this.loading = false
        //   this.getCode()
        //   return false
        // }

        // 密码加密
        // if (user.password !== this.cookiePass) {
        //  user.password = encrypt(user.password)
        // }

        if (valid) {
          this.loading = true
          // 记住我
          if (user.rememberMe) {
            // Cookies.set('username', user.username, { expires: Config.passCookieExpires })
            // Cookies.set('password', user.password, { expires: Config.passCookieExpires })
            Cookies.set('rememberMe', user.rememberMe, {
              expires: Config.passCookieExpires
            })
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('code')
            Cookies.remove('rememberMe')
          }
          // 异步请求：store->user.js
          this.$store
            .dispatch('whiteLoginOfVerify', user)
            .then(() => {
              this.loading = false
              this.$router.push('/hmi_main')
              // this.$router.push({ path: '/home' })
              function getTimer() {
                var time = new Date()
                var year = time.getFullYear()
                var month = time.getMonth() + 1
                month = month < 10 ? '0' + month : month
                var dates = time.getDate()
                dates = dates < 10 ? '0' + dates : dates
                var h = time.getHours()
                h = h < 10 ? '0' + h : h
                var m = time.getMinutes()
                m = m < 10 ? '0' + m : m
                var s = time.getSeconds()
                s = s < 10 ? '0' + s : s
                return year + '-' + month + '-' + dates + ' ' + h + ':' + m + ':' + s
              }
              getLoginLog(log)
              // console.log(getTimer())
              Cookies.set('OnlineTime', getTimer())
            })
            .catch(() => {
              // 异步请求失败，获取新的验证码重新登录
              this.loading = false
              // this.getCode()
            })
        } else {
          return false
        }
      })
    },
    point() {
      // 登录过期校验
      const point = Cookies.get('point') !== undefined
      if (point) {
        this.$notify({
          title: '提示',
          message: '当前登录状态已过期，请重新登录！',
          type: 'warning',
          duration: 5000
        })
        Cookies.remove('point')
      }
    },
    handleCommand(command) {
      this.$i18n.locale = command
      localStorage.setItem('language', this.$i18n.locale)
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
            // console.log(inputDom.type, i);
          }
        }
      }
    }

  }
}
</script>

<style lang="less" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-size: cover;
  .el-input--small .el-input__inner {
    height: 45px;
    line-height: 45px;
    border-radius: 5px;
    background-color: #e8f0fe;
    border: 0px;
    font-size: 12px;
    color: #333333;
  }
}
.title {
  margin: 0 auto 10px auto;
  text-align: center;
  color: #18245e;
  font-size: 24px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  min-width: 50%;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  margin-left: 10px;
  img {
    cursor: pointer;
    vertical-align: middle;
    width: 100%;
  }
}
.login_btn {
  background-color: #6b9ef9;
  width: 100%;
  font-size: 16px;
  border-radius: 10px;
  padding: 12px;
}
.login_btn:hover {
  background-color: #558beb;
}
.el-form-item--small .el-form-item__content {
  display: flex;
  white-space: nowrap;
  align-items: center;
  span {
    font-size: 14px;
    text-align: right;
    min-width: 65px;
  }
}
.imgLeft {
  width: 40%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 13;
}
.loginForm {
  position: fixed;
  bottom: 15%;
  right: 10%;
  border-radius: 10px;
  background: #ffffff;
  padding: 30px 25px;
  width: 380px;
  z-index: 13;
  ::v-deep .el-form-item__error{
    top: 108%;
    left: 20%;
  }
}
.passwordStyle {
  margin: 23px 0 !important;
}
.el-form-item__error {
  top: 110%;
  left: 42px;
}
#el-login-footer {
  bottom: 10px;
}
.wrapeldropdown{
position: fixed;
  top: 20px;
  right: 2%;
  display: flex;
  align-items: center;
  span{
    color: #333333;
    font-size: 13px;
  }
  .el-dropdown{
  color: #333333;
  font-size: 13px;
  }
}
.spanStyle{
  min-width: 70px;
}
.cloud-bubble-one {
  position: absolute;
  top: -120px;
  left: 50%;
  z-index: 10;
  margin-left: -120px;
  width: 240px;
  height: 240px;
  background-repeat: no-repeat;
  animation: bubble-animate-1 linear 10s infinite;
}

.cloud-bubble-two {
  position: absolute;
  top: 50px;
  left: 24%;
  z-index: 11;
  width: 360px;
  height: 360px;
  background-repeat: no-repeat;
  animation: bubble-animate-2 linear 12s infinite;
}

.cloud-bubble-three {
  position: absolute;
  top: 50px;
  left: 48%;
  z-index: 12;
  width: 300px;
  height: 300px;
  background-repeat: no-repeat;
  animation: bubble-animate-3 linear 11s infinite;
}

@keyframes bubble-animate-1 {
  from {
    top: -120px;
  }

  50% {
    top: -180px;
  }

  to {
    top: -120px;
  }
}

@keyframes bubble-animate-2 {
  from {
    top: 50px;
    left: 34%;
  }

  50% {
    top: 80px;
    left: 24%;
  }

  to {
    top: 50px;
    left: 34%;
  }
}

@keyframes bubble-animate-3 {
  from {
    top: 50px;
    left: 48%;
  }

  50% {
    top: 80px;
    left: 58%;
  }

  to {
    top: 50px;
    left: 48%;
  }
}
.keyboard-mask{
    position: fixed;
    bottom: 15%;
    left: 5%;
    z-index: 999;
}
::v-deep .hg-theme-default .hg-button.hg-standardBtn {
    width: 24px !important;
    // height: 51px;
}
.jianpan{
  display: flex;
  justify-content: flex-end;
  text-align: center !important;
  cursor: pointer;
  img{
    width: 35px;
    margin-right: 15px;
  }
}
</style>
