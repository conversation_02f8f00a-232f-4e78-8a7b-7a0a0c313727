<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.hmiMain.fmale') + '：'">
                <el-input v-model="query.group_lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.subBatchNo')+'：'">
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.subSimplifiedCode')+'：'">
                <el-input v-model="query.lot_short_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.materialNo')+'：'">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.hmiMain.vehicle')+'：'">
                <el-input v-model="query.pallet_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.taskSource')+'：'">
                <el-select v-model="query.task_from" clearable>
                  <el-option v-for="item in ['AIS','EAP','MES']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.station')+'：'">
                <el-select v-model="query.station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.portNo')+'：'">
                <el-input v-model="query.port_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.isFirstCheck')+'：'">
                <el-select v-model="query.inspect_flag" clearable>
                  <el-option v-for="item in ['N','Y']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.orientation')+'：'">
                <el-select v-model="query.face_code" clearable>
                  <el-option
                    v-for="item in [{ 'label': $t('view.form.orientationToFront'), 'value': 0 },
                                    { 'label': $t('view.form.orientationToBack'), 'value': 1 }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('lang_pack.hmiMain.production')+'：'">
                <el-select v-model="query.pdb_rule" clearable>
                  <el-option
                    v-for="item in [{ 'label': $t('view.enum.boardErrorCode.normal'), 'value': 0 },
                                    { 'label': $t('lang_pack.hmiMain.dummy'), 'value': 1 }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('lang_pack.productionTasks.masterStatus')+'：'">
                <el-select v-model="query.group_lot_status" clearable>
                  <el-option v-for="item in groupLotStatus" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('lang_pack.productionTasks.subbatchState')+'：'">
                <el-select v-model="query.lot_status" clearable>
                  <el-option v-for="item in ['FINISH','PLAN','WORK']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('lang_pack.productionTasks.methodCompletion')+'：'">
                <el-select v-model="query.task_error_code" clearable>
                  <el-option
                    v-for="item in [{ 'label': $t('lang_pack.productionTasks.normalFinish'), 'value': 1 },
                                    { 'label': $t('lang_pack.productionTasks.lessBoard'), 'value': 2 },
                                    { 'label': $t('lang_pack.productionTasks.boardsFinish'), 'value': 3 },
                                    { 'label': $t('lang_pack.productionTasks.ForcedFinish'), 'value': 4 }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('view.form.time')+'：'">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:100%"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <!--PNL列表-->
    <el-drawer append-to-body :wrapper-closable="false" :title="$t('lang_pack.productionTasks.PNLlist')" :visible.sync="pnlDrawerVisible" size="40%">
      <planDReport v-if="pnlDrawerVisible" ref="planDReport" :plan_id="plan_id" />
    </el-drawer>
    <!--任务过站信息-->
    <el-drawer append-to-body :wrapper-closable="false" :title="$t('view.title.boardPractice')" :visible.sync="flowDrawerVisible" size="80%">
      <planStationFlow v-if="flowDrawerVisible" ref="flowReport" :plan_id="plan_id" />
    </el-drawer>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions :column="4" size="small" border>
                  <!-- 工位号 -->
                  <el-descriptions-item :label="$t('lang_pack.tagsDefined.stationCode')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_code }}</el-descriptions-item>
                  <!-- 任务来源 -->
                  <el-descriptions-item :label="$t('view.form.taskSource')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_from }}</el-descriptions-item>
                  <!-- 计划数量 -->
                  <el-descriptions-item :label="$t('lang_pack.vie.planQuantity')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plan_lot_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.targetQuantity')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.target_lot_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.completed')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.quantity')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_ok_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.NGQuantity')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_ng_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.form.subSimplifiedCode')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_short_num }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.subTaskSorting')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_index }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.form.materialNo')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.hmiMain.vehicle')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_num }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.carrierType')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_type }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.layer')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_level }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.boardLength')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.panel_length }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.boardWidth')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.panel_width }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.boardThickness')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.panel_tickness }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.table.isPanelMode')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.panel_model }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.checkplans')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.inspect_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.checksCompleted')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.inspect_finish_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.hmiMain.production')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pdb_rule }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.platingplates')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pdb_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plates')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fp_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.area')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.face_code }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.MaxNumber')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_use_count }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.UserName')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.user_name }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.orderParameters')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.item_info }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.haCarTypeRoute.attribute1')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.haCarTypeRoute.attribute2')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.haCarTypeRoute.attribute3')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="prod_line_des" :label="$t('lang_pack.andonevent.prodLineCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="station_des" :label="$t('lang_pack.andonevent.stationName')" />
            <el-table-column :show-overflow-tooltip="true" prop="group_lot_num" :label=" projectCode === 'gxCode' ? '读码校验工单' : $t('lang_pack.hmiMain.fmale')" />
            <!-- 判断是不是广芯 如果是广芯，显示拆卡工单ID -->
            <el-table-column v-if="projectCode === 'gxCode'" :show-overflow-tooltip="true" prop="" :label="$t('view.form.cardWorkOrder')" width="120">
              <template slot-scope="scope">
                {{ scope.row.other_attribute && (JSON.parse(scope.row.other_attribute).OldLotNum || '') }}
              </template>
            </el-table-column>
            <!-- 判断是否为广芯项目，如果是广芯项目则显示，如果不是则不显示 -->
            <el-table-column :show-overflow-tooltip="true" prop="lot_num" :label="$t('view.form.subBatchNo')" width="100" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_num" :label="$t('lang_pack.hmiMain.vehicle')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_status" :label="$t('lang_pack.productionTasks.subbatchState')" />
            <el-table-column :show-overflow-tooltip="true" prop="task_error_code" :label="$t('lang_pack.productionTasks.FinishedCondition')" width="100">
              <template slot-scope="scope">
                <el-tag :type="((scope.row.task_error_code == '[0]计划中' || scope.row.task_error_code == '[0]生产中') ? '' : (scope.row.task_error_code == '[1]正常完板' ? 'success' : 'danger'))">
                  {{ scope.row.task_error_code }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="plan_lot_count" :label="$t('lang_pack.vie.planQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="finish_count" :label="$t('lang_pack.vie.completed')" />
            <el-table-column :show-overflow-tooltip="true" prop="target_lot_count" :label="$t('lang_pack.vie.targetQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="group_lot_status" :label="$t('lang_pack.vie.masterLot')" />
            <el-table-column :show-overflow-tooltip="true" prop="task_start_time" :label="$t('lang_pack.hmiMain.startTime')" width="140" />
            <el-table-column :show-overflow-tooltip="true" prop="task_end_time" :label="$t('lang_pack.hmiMain.endTime')" width="140" />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" width="220" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="showPnlList(scope.row)">{{ $t('lang_pack.productionTasks.viewPNL') }}</el-button>
                <el-button type="text" size="small" @click="showFlowList(scope.row)">{{ $t('view.title.boardPractice') }}</el-button>
                <!-- 广芯要求删除任务的时候不需要删除这条数据，需要把母批状态改成delete，方便查询 -->
                <el-button v-if="scope.row.group_lot_status !=='DELETE' " type="text" size="small" @click="deleteTask(scope.row)">{{ $t('lang_pack.productionTasks.deleteTask') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >{{ $t('view.pagination.total') + '：' }}{{ page.total }}</el-button>
              <el-button
                type="primary"
              >{{ $t('view.pagination.current') }}{{ nowPageIndex }}{{ $t('view.pagination.unit') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;{{ $t('view.pagination.previous') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >{{ $t('view.pagination.next') }}&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import { sel as selStation } from '@/api/core/factory/sysStation'
import planDReport from '@/views/eap/core/plan/PlanDReport'
import planStationFlow from '@/views/eap/core/plan/PlanStationFlow'
import crudEapApsPlanReport from '@/api/eap/eapApsPlanReport'
const defaultForm = {

}
export default {
  name: 'planreport',
  components: { crudOperation, planDReport, planStationFlow },
  cruds() {
    return CRUD({
      title: '任务报表',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'group_lot_num',
      // // 排序
      sort: ['group_lot_num asc'],
      // CRUD Method
      crudMethod: { ...crudEapApsPlanReport },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      pnlDrawerVisible: false,
      flowDrawerVisible: false,
      stationData: [],
      projectCode: '',
      groupLotStatus: ['FINISH', 'PLAN', 'WORK'],
      plan_id: '',
      height: document.documentElement.clientHeight - 330,
      permission: {
        add: ['admin', 'plan_report:add'],
        edit: ['admin', 'plan_report:edit'],
        del: ['admin', 'plan_report:del'],
        down: ['admin', 'plan_report:down']
      }
    }
  },
  created() {
    this.getStationList()
  },
  mounted: function() {
    // 获取系统参数信息
    var queryParameter = {
      userName: Cookies.get('userName'),
      parameter_code: 'ProjectCode',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0 && defaultQuery.data !== '') {
          this.projectCode = defaultQuery.data[0].parameter_val
          if (this.projectCode === 'gxCode') {
            this.groupLotStatus.push('DELETE')
          }
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 330
    }
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('view.dialog.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('view.dialog.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    getStationList() {
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y'
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('view.dialog.queryException'),
            type: 'error'
          })
        })
    },
    showPnlList(row) {
      this.plan_id = row.plan_id
      this.pnlDrawerVisible = true
    },
    showFlowList(row) {
      this.plan_id = row.plan_id
      this.flowDrawerVisible = true
    },
    deleteTask(row) {
      this.$confirm(this.$t('lang_pack.vie.selectedTaskData'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          crudEapApsPlanReport.del({
            plan_id: row.plan_id
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.deleteSuccesful'), type: 'success' })
                this.toQuery()
              }
            })
            .catch((ex) => {
              this.$message({
                message: this.$t('lang_pack.vie.operationException') + ex,
                type: 'error'
              })
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
  color: #fff;
  background-color: #1473c5;
  border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
  background: #438fd1;
  border-color: #438fd1;
  color: #fff;
}
.labelIline {
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label {
    white-space: nowrap;
  }
}
::v-deep .wrapElFormFirst {
  .el-form-item__label {
    white-space: nowrap;
    margin: 0;
    width: 80px;
  }
}
::v-deep .el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 235px;
}
::v-deep .el-date-editor .el-range-separator {
  width: 12%;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  width: 130px;
}
</style>
