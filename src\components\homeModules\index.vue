<template>
    <vue-seamless-scroll :data="list" :class-option="optionSingleHeight1" class="seamless-warp">
        <div class="home-message" v-for="item in list" :key="item.id">
            <div class="home-message-item">
                <span >{{ item.messageInfo }}</span>
            </div>
            <div class="wrapTime">
                {{ item.createTime }}
            </div>
        </div>
    </vue-seamless-scroll>
</template>
<script>
import vueSeamlessScroll from 'vue-seamless-scroll' //轮播无线滚动
export default {
    name: 'homeScroll',
    components: {
        vueSeamlessScroll
    },
    computed: {
        optionSingleHeight1() {
            return {
                // limitMoveNum: this.$store.getters.patrolResults.length,
                limitMoveNum: 5,
                // singleHeight: this.midHeight / 8,
                step: 0.1,
                openTouch: false,
                hoverStop: true,
                autoPlay: true
            }
        }
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        }
    },
    data() {
        return {}
    }
}
</script>
