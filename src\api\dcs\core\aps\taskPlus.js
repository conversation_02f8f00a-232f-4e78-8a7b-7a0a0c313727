import request from '@/utils/request'

// 查询生产任务余料
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskPlusSelect',
    method: 'post',
    data
  })
}
// 新增生产任务余料
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskPlusInsert',
    method: 'post',
    data
  })
}
// 修改生产任务余料
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskPlusUpdate',
    method: 'post',
    data
  })
}
// 删除生产任务余料
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskPlusDelete',
    method: 'post',
    data
  })
}
export default { sel ,add ,edit ,del}

