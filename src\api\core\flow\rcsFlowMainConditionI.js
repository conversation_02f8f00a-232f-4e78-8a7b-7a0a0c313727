import request from '@/utils/request'

//查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepConditionISel',
    method: 'post',
    data
  })
}
//新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepConditionIIns',
    method: 'post',
    data
  })
}
//修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepConditionIUpd',
    method: 'post',
    data
  })
}
//删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepConditionIDel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del }

