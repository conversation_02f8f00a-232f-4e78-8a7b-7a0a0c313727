import request from '@/utils/request'

// 工位安灯大类型查询
export function queryAllMesAndonStationType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeSelect',
    method: 'post',
    data
  })
}

// 工位安灯大类型增加
export function insMesAndonStationType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeInsert',
    method: 'post',
    data
  })
}

// 工位安灯大类型修改
export function updMesAndonStationType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeUpdate',
    method: 'post',
    data
  })
}

// 工位安灯大类型删除
export function delMesAndonStationType(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeDelete',
    method: 'post',
    data
  })
}
// 工位查询
export function queryAllMesFmodStationForm(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesFmodStationFormSelect',
    method: 'post',
    data
  })
}

export default { queryAllMesAndonStationType, insMesAndonStationType, updMesAndonStationType, delMesAndonStationType, queryAllMesFmodStationForm }
