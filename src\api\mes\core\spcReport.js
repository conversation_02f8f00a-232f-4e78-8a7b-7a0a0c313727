import request from '@/utils/request'

// 查询过站质量信息
export function mesQualitySpcAnalyze(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesQualitySpcAnalyze',
    method: 'post',
    data
  })
}

// 查询采集项目信息
export function mesQualitySpcTagList(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesQualitySpcTagList',
    method: 'post',
    data
  })
}

// 样本趋势图
export function sampleTrend(data) {
  return request({
      url: 'aisEsbWeb/mes/report/sampleTrend',
      method: 'post',
      data
  })
}
// 样本趋势图导出
export function sampleTrendExport(data) {
  return request({
      url: 'aisEsbWeb/mes/report/sampleTrendExport',
      method: 'post',
      data,
      responseType: 'blob',
  })
}
// 基本趋势图
export function basicTrend(data) {
  return request({
      url: 'aisEsbWeb/mes/report/basicTrend',
      method: 'post',
      data
  })
}
// 基本趋势图导出
export function basicTrendExport(data) {
  return request({
      url: 'aisEsbWeb/mes/report/basicTrendExport',
      method: 'post',
      data,
      responseType: 'blob',
  })
}
// 直方图
export function histogram(data) {
  return request({
      url: 'aisEsbWeb/mes/report/histogram',
      method: 'post',
      data
  })
}
// 直方图导出
export function histogramExport(data) {
  return request({
      url: 'aisEsbWeb/mes/report/histogramExport',
      method: 'post',
      data,
      responseType: 'blob',
  })
}
// 均值方差图
export function meanVariance(data) {
  return request({
      url: 'aisEsbWeb/mes/report/meanVariance',
      method: 'post',
      data
  })
}
// 均值标准差图
export function meanStandardDeviation(data) {
  return request({
      url: 'aisEsbWeb/mes/report/meanStandardDeviation',
      method: 'post',
      data
  })
}
// 工序能力分析
export function processCapability(data) {
  return request({
      url: 'aisEsbWeb/mes/report/processCapability',
      method: 'post',
      data,
  })
}
// 工序能力分析导出
export function processCapabilityExport(data) {
  return request({
      url: 'aisEsbWeb/mes/report/processCapabilityExport',
      method: 'post',
      data,
      responseType: 'blob',
  })
}
// 良品率
export function yieldData(data) {
  return request({
      url: 'aisEsbWeb/mes/report/yield',
      method: 'post',
      data,
  })
}
// 良品率导出
export function yieldDataExport(data) {
  return request({
      url: 'aisEsbWeb/mes/report/yieldExport',
      method: 'post',
      data,
      responseType: 'blob',
  })
}
// oee分析图
export function oee(data) {
  return request({
      url: 'aisEsbWeb/mes/report/oee',
      method: 'post',
      data
  })
}
// oee分析图导出
export function oeeExport(data) {
  return request({
      url: 'aisEsbWeb/mes/report/oeeExport',
      method: 'post',
      data,
      responseType: 'blob',
  })
}

export default { 
  mesQualitySpcAnalyze, mesQualitySpcTagList,
  sampleTrend,sampleTrendExport,
  basicTrend,basicTrendExport,
  histogram,histogramExport,
  meanVariance,
  meanStandardDeviation,
  processCapability,processCapabilityExport,
  yieldData,yieldDataExport,
  oee, oeeExport,
}
