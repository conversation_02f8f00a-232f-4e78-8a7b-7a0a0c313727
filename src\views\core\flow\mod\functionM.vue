<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modfunctionm.functionCode')">
                <!-- 函数编码： -->
                <el-input v-model="query.function_m_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modfunctionm.funcname')">
                <!-- 函数名： -->
                <el-input v-model="query.function_m_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.modfunctionm.functionCodet')" prop="function_m_code">
            <!-- 函数编码 -->
            <el-input v-model="form.function_m_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modfunctionm.funcnamet')" prop="function_m_name">
            <!-- 函数名 -->
            <el-input v-model="form.function_m_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modfunctionm.functionDescription')" prop="function_m_des">
            <!-- 函数描述 -->
            <el-input v-model="form.function_m_des" />
          </el-form-item>
        </el-form>
        <div style="text-align: center" class="wrapButtonM">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-dialog title="代码设计" :fullscreen="true" :show-close="false" custom-class="code-dialog-height" :visible.sync="codeEditDialogVisible">
        <div slot="title">
          代码设计
          <el-button size="mini" style="float:right" @click="codeEditDialogVisible = false">关闭</el-button>
          <el-button type="success" size="mini" style="float:right;margin-right:10px;" @click="handleCodeSave">保存</el-button>
        </div>
        <div class="code-dialog-content">
          <codeEditor ref="codeEditor" :code_content="currentRow.function_m_content" @change="handleCodeChange" />
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="function_m_id" label="id" />
            <el-table-column :show-overflow-tooltip="true" prop="function_m_code" :label="$t('lang_pack.modfunctionm.functionCodet')" />
            <!-- 函数编码 -->
            <el-table-column :show-overflow-tooltip="true" prop="function_m_name" :label="$t('lang_pack.modfunctionm.funcnamet')" />
            <!-- 函数名 -->
            <el-table-column :show-overflow-tooltip="true" prop="function_m_des" :label="$t('lang_pack.modfunctionm.functionDescription')" />
            <!-- 函数描述 -->
            <el-table-column :show-overflow-tooltip="true" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendCodeEdit(scope.row)">代码设计</el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import codeEditor from '@/components/CodeEditor/CodeEditor'
import crudFlowModFunctionM from '@/api/core/flow/rcsFlowModFunctionM'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  function_m_id: 0,
  function_m_code: '',
  function_m_name: '',
  function_m_des: '',
  function_m_content: ''
}
export default {
  name: 'RCS_FLOW_MOD_FUNCTION',
  components: { crudOperation, rrOperation, udOperation, pagination, codeEditor },
  cruds() {
    return CRUD({
      title: '函数',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'function_m_id',
      // 排序
      sort: ['function_m_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowModFunctionM },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'rcs_flow_mod_function:add'],
        edit: ['admin', 'rcs_flow_mod_function:edit'],
        del: ['admin', 'rcs_flow_mod_function:del'],
        down: ['admin', 'rcs_flow_mod_function:down']
      },
      rules: {
        function_m_code: [{ required: true, message: '请输入函数编码', trigger: 'blur' }],
        function_m_name: [{ required: true, message: '请输入函数名', trigger: 'blur' }],
        function_m_des: [{ required: true, message: '请输入函数描述', trigger: 'blur' }]
      },
      code_content: '',
      currentRow: [],
      codeEditDialogVisible: false
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    handleCodeChange(content) {
      this.code_content = content
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.function_m_content = this.code_content
      return true
    },
    opendCodeEdit(row) {
      this.currentRow = row
      this.codeEditDialogVisible = true
    },
    handleCodeSave() {
      this.currentRow.user_name = Cookies.get('userName')
      this.currentRow.function_m_content = this.code_content
      crudFlowModFunctionM
        .edit(this.currentRow)
        .then(response => {
          if (response.code === 0) {
            this.$message({ message: '保存成功', type: 'success' })
            this.currentRow = []
            this.codeEditDialogVisible = false
          } else {
            this.$message({ message: '保存失败：' + response.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '保存异常', type: 'error' })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.wrapButtonM {
  margin: 40px 0;
}
::v-deep .CodeMirror-vscrollbar {
  overflow-x: inherit !important;
  overflow-y: inherit !important;
}
.CodeMirror-vscrollbar::-webkit-scrollbar {
  width: 0px;
  height: 4px;
  background-color: #ebeef5;
}
.CodeMirror-vscrollbar::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f6f9ff;
}
.CodeMirror-vscrollbar::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}
.lzg-dialog-height {
  width: 70%;
  top: 7%;
  right: 15%;
  bottom: 7%;
  left: 15%;
  transform: translate(0%, 0%);
}
.code-dialog-height .el-dialog__body {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-y: auto;
}
.code-dialog-content {
  top: 60px;
  right: 5px;
  bottom: 5px;
  left: 5px;
  overflow-y: auto;
  position: absolute;
}
</style>
