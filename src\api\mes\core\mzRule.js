import request from '@/utils/request'

// 配方模组查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZSel',
    method: 'post',
    data
  })
}
// 配方模组增加
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZIns',
    method: 'post',
    data
  })
}
// 配方模组修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZUpd',
    method: 'post',
    data
  })
}
// 配方模组删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMZDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
