<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码：">
                <!-- 物料编码： -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件图号:">
                <!-- 零件图号 -->
                <el-input v-model="query.material_draw" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="型号:">
                <!-- 型号 -->
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="长:">
                <!-- 长 -->
                <el-input v-model="query.m_length" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="宽:">
                <!-- 宽 -->
                <el-input v-model="query.m_width" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button size="small" type="warning" icon="el-icon-upload2" plain round @click="crud.doExport">
            导出模板
          </el-button>
          <el-button size="small" type="primary" icon="el-icon-download" plain round @click="importDialogVisible = true">
            导入
          </el-button>
        </template>
      </crudOperation>
      <!--导入BOM-->
      <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="导入文件" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input
            v-if="isUpLoadError"
            v-model="errorMsg"
            type="textarea"
            :rows="5"
          />
          <div style="text-align: center;margin-top:10px">
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" @click="toButDrawerUpload">导入</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.mfTable.mainMaterialCode')" prop="material_code">
                <!-- 物料编码： -->
                <el-input v-model="form.material_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.mainMaterialDescription')" prop="material_des">
                <!-- 物料描述： -->
                <el-input v-model="form.material_des" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.partDrawingNumber')">
                <!-- 零件图号 -->
                <el-input v-model="form.material_draw" clearable size="small" />
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.mfTable.model_type')" prop="model_type">
                型号
                <el-input v-model="form.model_type" clearable size="small" />
              </el-form-item> -->
              <el-form-item :label="$t('lang_pack.mfTable.length')" prop="m_length">
                <!-- 长 -->
                <el-input v-model="form.m_length" type="number" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.width')" prop="m_width">
                <!-- 宽 -->
                <el-input v-model="form.m_width" type="number" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.thick')" prop="m_height">
                <!-- 厚 -->
                <el-input v-model="form.m_height" type="number" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.materialQuality')" prop="m_texture">
                <!-- 材质 -->
                <el-input v-model="form.m_texture" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.cuttingMachine')" prop="cut_texture">
                <!-- 材质(切割机) -->
                <fastCode fastcode_group_code="CUTTERBAR_TEXTURE" :fastcode_code.sync="form.cut_texture" control_type="select" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mfTable.dataSources')" prop="data_from">
                <!-- 数据来源 -->
                <fastCode fastcode_group_code="DATA_SOURCES" :fastcode_code.sync="form.data_from" control_type="select" size="mini" />
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.mfTable.cuttingXCoordinate')" prop="npa_startx">
                                切割x坐标
                                <el-input v-model="form.npa_startx" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.mfTable.cuttingyCoordinate')" prop="npa_starty">
                                切割y坐标
                                <el-input v-model="form.npa_starty" clearable size="small" />
                            </el-form-item> -->
              <el-form-item :label="$t('lang_pack.recipequality.enableFlag')" prop="enable_flag">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" prop="model_id" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_id }}</el-descriptions-item>
                  <el-descriptions-item label="物料编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                  <el-descriptions-item label="物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_des }}</el-descriptions-item>
                  <el-descriptions-item label="零件图号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_draw }}</el-descriptions-item>
                  <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="长" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_length }}</el-descriptions-item>
                  <el-descriptions-item label="宽" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_width }}</el-descriptions-item>
                  <el-descriptions-item label="厚" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_height }}</el-descriptions-item>
                  <el-descriptions-item label="重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_weight }}</el-descriptions-item>
                  <el-descriptions-item label="材质" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_texture }}</el-descriptions-item>
                  <el-descriptions-item label="材质(切割机)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.CUTTERBAR_TEXTURE[props.row.cut_texture] }}</el-descriptions-item>
                  <el-descriptions-item label="数据来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.DATA_SOURCES[props.row.data_from] }}</el-descriptions-item>
                  <el-descriptions-item label="X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.npa_startx }}</el-descriptions-item>
                  <el-descriptions-item label="Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.npa_starty }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 时间 -->
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="last_update_date"
                            :label="$t('lang_pack.mfTable.time')" width="100" align='center' /> -->
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.mfTable.mainMaterialCode')"
              width="120"
              align="center"
            />
            <!-- 物料描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_des"
              :label="$t('lang_pack.mfTable.mainMaterialDescription')"
              width="100"
              align="center"
            />
            <!-- 零件图号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_draw"
              :label="$t('lang_pack.mfTable.partDrawingNumber')"
              width="100"
              align="center"
            />
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.mfTable.model_type')"
              width="140"
              align="center"
            />
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.mfTable.length')"
              width="100"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.mfTable.width')"
              width="100"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              :label="$t('lang_pack.mfTable.thick')"
              width="100"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.mfTable.weight')"
              width="100"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              :label="$t('lang_pack.mfTable.materialQuality')"
              width="120"
              align="center"
            />
            <!-- 材质(切割机) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_texture"
              :label="$t('lang_pack.mfTable.cuttingMachine')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.CUTTERBAR_TEXTURE[scope.row.cut_texture] }}
              </template>
            </el-table-column>
            <!-- 数据来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="data_from"
              :label="$t('lang_pack.mfTable.dataSources')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_SOURCES[scope.row.data_from] }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.recipequality.enableFlag')"
              align="center"
              width="100"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  model_id: '',
  material_code: '',
  material_des: '',
  material_draw: '',
  model_type: '',
  m_length: '',
  m_width: '',
  m_height: '',
  m_weight: '',
  m_texture: '',
  cut_texture: 'MS',
  data_from: 'OWN',
  npa_startx: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_FMOD_MODEL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '机型基础',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'model_id',
      // 排序
      sort: ['model_id asc'],
      // CRUD Method
      crudMethod: { ...crudFmodModel },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 310,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        material_code: [{ required: true, message: '请选择机型id', trigger: 'blur' }],
        model_id: [{ required: true, message: '请选择物料编码', trigger: 'blur' }],
        material_des: [{ required: true, message: '请选择物料描述', trigger: 'blur' }],
        material_draw: [{ required: true, message: '请选择零件图号', trigger: 'blur' }],
        model_type: [{ required: true, message: '请选择型号', trigger: 'blur' }],
        m_length: [{ required: true, message: '请选择长', trigger: 'blur' }],
        m_width: [{ required: true, message: '请选择宽', trigger: 'blur' }],
        m_height: [{ required: true, message: '请选择厚', trigger: 'blur' }],
        m_weight: [{ required: true, message: '请选择重', trigger: 'blur' }],
        data_from: [{ required: true, message: '请选择数据来源', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      },
      importDialogVisible: false,
      uploadLimit: 1,
      uploadAccept: '.xls,.xlsx',
      fileList: [],
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: ''
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'DATA_SOURCES', 'CUTTERBAR_TEXTURE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 310
    }
  },
  created: function() {
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudFmodModel
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              model_id: data.model_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = 'dcs/core/DcsApsFmodModelImport'

      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method

      console.log(process.env, process.env.VUE_APP_BASE_API)
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>
