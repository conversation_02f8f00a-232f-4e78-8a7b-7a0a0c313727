<template>
  <div class="app-contanier" :style="'background-image:url(' + Background + ');'">
    <div class="cloud-bubble-one" :style="'background-image:url(' + cloudBackBule1 + ');'" />
    <div class="cloud-bubble-two" :style="'background-image:url(' + cloudBackBule2 + ');'" />
    <div class="cloud-bubble-three" :style="'background-image:url(' + cloudBackBule3 + ');'" />
    <img class="imgLeft" :src="LoginLeftImage">
    <el-form class="userInfo">
      <h3 class="title">AIS流程设计与作业系统</h3>
      <el-form-item>
        <el-input
          v-model="userForm.newPw"
          placeholder="请输入登陆密码"
          type="password"
          auto-complete="off"
          show-password
        >
          <div slot="prefix" style="margin-left: 3px">
            <i class="el-icon-lock" /></div></el-input>
        <span class="jianpan" @click="showKeyboard">
          <img :src="keyboard">
        </span>
      </el-form-item>
      <el-form-item class="wrapbtn">
        <!-- <el-button
          size="medium"
          type="warning"
          @click="logout"
        ><i class="el-icon-unlock" />退屏重新登录</el-button> -->
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click="unLock"
        ><i class="el-icon-unlock" />解锁</el-button>
      </el-form-item>
    </el-form>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>

<script>
import md5 from 'js-md5'
import keyboard from '@/assets/images/keyboard.png'
import Background from '@/assets/images/loginbj1.jpg'
import cloudBackBule1 from '@/assets/images/cloud-back-bule1.png'
import cloudBackBule2 from '@/assets/images/cloud-back-bule2.png'
import cloudBackBule3 from '@/assets/images/cloud-back-bule3.png'
import LoginLeftImage from '@/assets/images/hmiLoginLeft.gif'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
export default {
  name: 'lockScreen',
  components: {
    SimpleKeyboard
  },
  data() {
    return {
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      cloudBackBule1: cloudBackBule1,
      cloudBackBule2: cloudBackBule2,
      cloudBackBule3: cloudBackBule3,
      Background: Background,
      LoginLeftImage: LoginLeftImage,
      userForm: {
        newPw: '',
        user: '',
        isCover: true
        // isLock:this.$store.state.user.isLock,
      },
      loading: false
    }
  },
  mounted() {
    // this.unLock()
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  methods: {
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
            // console.log(inputDom.type, i);
          }
        }
      }
    },
    unLock() {
      const oldAuct = sessionStorage.getItem('lockPassword')
      sessionStorage.setItem('newlockPassword', md5(this.userForm.newPw))
      console.log(oldAuct, sessionStorage.getItem('newlockPassword'), '999990')
      if (this.userForm.newPw === '' || this.userForm.newPw === undefined) {
        this.$notify.error({
          title: '错误',
          message: '请输入登陆密码解锁',
          duration: 1500
        })
        return
      } else if (md5(this.userForm.newPw) !== oldAuct) {
        this.userForm.newPw = ''
        this.$notify.error({
          title: '错误',
          message: '解锁密码错误，请输入登陆密码解锁',
          duration: 1500
        })
        return
      } else {
        setTimeout(() => {
          this.$notify.success({
            title: '解锁成功',
            duration: 1500
          })
          this.$router.push('/hmi_main')
          this.userForm.newPw = ''
        }, 500)
      }
    }
    // async logout() {
    //   this.$confirm('确定注销并退出系统吗？', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     const password = sessionStorage.getItem('lockPassword')
    //     sessionStorage.setItem('newlockPassword', password)
    //     this.$store.dispatch('LogOut').then(() => {
    //       location.href = '/whiteLogin'
    //     })
    //   })
    // }
  }
}
</script>

<style lang="less" scoped>
.app-contanier {
  // background-image: url("../../assets/images/back.png");
  background-size: 100%;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-color: skyblue;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
  .userInfo{
        position: fixed;
    bottom: 15%;
    right: 10%;
    border-radius: 10px;
    background: #ffffff;
    padding: 30px 25px;
    width: 380px;
    z-index: 13;
    .title {
  margin: 0 auto 40px auto;
  text-align: center;
  color: #18245e;
  font-size: 24px;
}
  }
  .wrapbtn{
    margin-top: 20px;
  }
}
.imgLeft {
  width: 40%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 13;
}
.cloud-bubble-one {
  position: absolute;
  top: -120px;
  left: 50%;
  z-index: 10;
  margin-left: -120px;
  width: 240px;
  height: 240px;
  background-repeat: no-repeat;
  animation: bubble-animate-1 linear 10s infinite;
}

.cloud-bubble-two {
  position: absolute;
  top: 50px;
  left: 24%;
  z-index: 11;
  width: 360px;
  height: 360px;
  background-repeat: no-repeat;
  animation: bubble-animate-2 linear 12s infinite;
}

.cloud-bubble-three {
  position: absolute;
  top: 50px;
  left: 48%;
  z-index: 12;
  width: 300px;
  height: 300px;
  background-repeat: no-repeat;
  animation: bubble-animate-3 linear 11s infinite;
}

@keyframes bubble-animate-1 {
  from {
    top: -120px;
  }

  50% {
    top: -180px;
  }

  to {
    top: -120px;
  }
}

@keyframes bubble-animate-2 {
  from {
    top: 50px;
    left: 34%;
  }

  50% {
    top: 80px;
    left: 24%;
  }

  to {
    top: 50px;
    left: 34%;
  }
}

@keyframes bubble-animate-3 {
  from {
    top: 50px;
    left: 48%;
  }

  50% {
    top: 80px;
    left: 58%;
  }

  to {
    top: 50px;
    left: 48%;
  }
}
.keyboard-mask{
    position: fixed;
    bottom: 15%;
    left: 5%;
    z-index: 999;
}
::v-deep .hg-theme-default .hg-button.hg-standardBtn {
    width: 24px !important;
    // height: 51px;
}
.jianpan img{
      width: 35px;
    margin-top: 10px;
    margin-left: 10px;
    cursor: pointer;
}
</style>

