<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          生产端口
        </template>
        <el-radio v-for="(item,index) in portList" :key="index" v-model="webLotPortCode" :label="item.port_index" style="margin-left:0px;" border>{{ item.port_des }}</el-radio>
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          工单
        </template>
        <el-input ref="webPalletNum" v-model="webPalletNum" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo">上报</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      portList: [{ port_index: '1', port_des: '端口1' }, { port_index: '2', port_des: '端口2' }],
      webPalletNum: '',
      webLotPortCode: '',
      isFirst: 'Y'
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webPalletNum.focus()
    })
    this.webLotPortCode = this.tag_key_list.PortCode
  },
  methods: {
    handleSendInfo() {
      if (this.webLotPortCode === '') {
        this.$message({ message: '请选择端口号', type: 'info' })
        return
      }
      if (this.webPalletNum === '') {
        this.$message({ message: '请扫描工单', type: 'info' })
        return
      }
      // 提交
      var sendJson = {}
      var rowJson = []
      var newRow2 = {
        TagKey: this.tag_key_list.WebLotNum,
        TagValue: this.webPalletNum
      }
      rowJson.push(newRow2)
      var newRow3 = {
        TagKey: this.tag_key_list.WebLotPortCode,
        TagValue: this.webLotPortCode
      }
      rowJson.push(newRow3)
      var newRow = {
        TagKey: this.tag_key_list.WebLotRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebLotNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr, this.webPalletNum, this.webPalletNum, this.webLotPortCode)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
