<template>
    <div class="app-container">
        <el-row :gutter="20">
        <el-col :span="18">
            <el-descriptions :column="1" border style="margin-top:10px;">
            <el-descriptions-item>
                <template slot="label">
                <i class="el-icon-tickets" />
                默认直径
                </template>
                <el-input v-model.number="diameter" style="width:100px;" />
                <el-button-group style="float:right;">
                <el-button type="primary" icon="el-icon-plus" @click="add()">添加</el-button>
                <el-button type="primary" icon="el-icon-edit" @click="save()">保存</el-button>
                </el-button-group>
            </el-descriptions-item>
            <el-descriptions-item>
                <template slot="label">
                <i class="el-icon-tickets" />
                编号
                </template>
                {{ nodeForm.point_id }}
            </el-descriptions-item>
            <el-descriptions-item>
                <template slot="label">
                <i class="el-icon-tickets" />
                X轴
                </template>
                <el-input v-model.number="nodeForm.x" />
            </el-descriptions-item>
            <el-descriptions-item>
                <template slot="label">
                <i class="el-icon-tickets" />
                Y轴
                </template>
                <el-input v-model.number="nodeForm.y" />
            </el-descriptions-item>
            <el-descriptions-item>
                <template slot="label">
                <i class="el-icon-tickets" />
                直径
                </template>
                <el-input v-model.number="nodeForm.size" />
            </el-descriptions-item>
            <el-descriptions-item>
                <template slot="label">
                <i class="el-icon-tickets" />
                工位
                </template>
                <el-input v-model="nodeForm.station_code" />
            </el-descriptions-item>
            </el-descriptions>
        </el-col>
        </el-row>
        <!-- <el-card shadow="never" style="margin-top: 10px">
            <div slot="header" class="wrapTextSelect" ref="aref">
                <span>全局详情图</span>
            </div>
            <div class="dcs-main">
                <img src="@/assets/images/dcs/dcs-back-main.png" id="imgUrl" alt="" :style="{ height: imageHeight + 'px' }"/>
            </div>
        </el-card> -->
        <el-col :span="24">
          <MainChart
            ref="chart"
            v-loading="loading"
            :nodes="nodes"
            :connections="connections"
            :width="'1856'"
            :height="'266'"
            :readonly="false"
            element-loading-text="拼命绘制流程图中"
            @editnode="handleEditNode"
            @dblclick="handleDblClick"
            @editconnection="handleEditConnection"
            @save="handleChartSave"
            @delnode="handleDelNode"
          />
        </el-col>
    </div>
  </template>
  <script>
  /* eslint-disable no-unused-vars */
  
  import crudMainService from '@/api/dcs/core/service/mainService'
  import MainChart from '@/components/MainChart/index'
  import axios from 'axios'
  import Cookies from 'js-cookie'
  import * as d3 from 'd3'
  import { roundTo20 } from '@/utils/math'
  export default {
    props:{
        section_code:{
            type: [String, Number],
            default: -1
        },
        image_content:{
          type: [String, Number],
          default: ''
        },
        stationId:{
          type: [String, Number],
          default: ''
        }
    },
    components: {
      MainChart
    },
    data() {
      return {
        height: document.documentElement.clientHeight - 60,
        diameter: 15,
        nodes: [],
        connections: [],
        nodeForm: { },
        thisH: null,
        loading: false,
        imageHeight: document.documentElement.clientHeight - 550,
      }
    },
    mounted() {
    },
    created() {
        const query = {
            user_name: Cookies.get('userName'),
            section_code:this.section_code
        }
        crudMainService.queryPonit(query).then(res=>{
            const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  // var obj =
                  // {"result":"","msg":"","code":0,"data":[{"allow_jump_flag":"N","enable_flag":"Y","proceduce_code":"OP3030-1-1","pic_path":"/u01/ais/upload/pdure/sop.png","creation_date":"2023-01-31 15:21:14","proceduce_attr":"","created_by":"admin","proceduce_des":"拧紧螺丝","pic_name":"sop.png","last_updated_by":"admin","pdure_chart":"{\"nodes\":[{\"id\":1673920322915,\"x\":0,\"y\":0,\"type\":\"image\",\"width\":370,\"height\":360,\"describe\":\"作业指导图片\",\"strokeWidth\":1,\"href\":211},{\"id\":1673603105641,\"x\":50,\"y\":50,\"type\":\"circle\",\"index\":1,\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673603105965,\"x\":100,\"y\":100,\"type\":\"circle\",\"index\":\"6\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673603106281,\"x\":50,\"y\":100,\"type\":\"circle\",\"index\":\"5\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673603106631,\"x\":200,\"y\":50,\"type\":\"circle\",\"index\":\"4\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673603106941,\"x\":150,\"y\":50,\"type\":\"circle\",\"index\":\"3\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673603107286,\"x\":100,\"y\":50,\"type\":\"circle\",\"index\":\"2\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673920326764,\"x\":200,\"y\":100,\"type\":\"circle\",\"index\":\"8\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"},{\"id\":1673920327545,\"x\":150,\"y\":100,\"type\":\"circle\",\"index\":\"7\",\"width\":15,\"height\":15,\"color\":\"#C8CACC\",\"describe\":\"圆圈\",\"stroke\":\"#FFFFFF\",\"strokeWidth\":2,\"status\":\"WAIT\"}],\"connections\":[]}","proceduce_id":211,"attribute1":"","work_order_by":1,"attribute3":"","craft_route_id":111,"attribute2":"","proceduce_type":"SHAFT_QUALITY","data_move_flag":"N","last_update_date":"2023-01-31 15:21:14"}],"count":1}
                  // if (obj.data[0].pdure_chart !== '') {
                  //   const flow_chart = JSON.parse(obj.data[0].pdure_chart)
                  //   this.nodes = flow_chart.nodes
                  //   this.nodes.map(item=>{
                  //     if(item.type === 'image'){
                  //       item.id = +new Date()
                  //       item.width = 1856
                  //       item.height = 266
                  //     }
                  //   })
                  //   this.connections = flow_chart.connections
                  // } else {
                  //   this.nodes = [{
                  //     id: +new Date(),
                  //     x: 0,
                  //     y: 0,
                  //     type: 'image',
                  //     width: 1856,
                  //     height: 266,
                  //     describe: '作业指导图片',
                  //     strokeWidth: 1,
                  //     href: this.proceduce_id
                  //   }]
                  //   this.connections = []
                  // }
                  // this.nodes = defaultQuery.data

                  if(defaultQuery.data.length > 0){
                    this.nodes = defaultQuery.data
                    this.nodes.map(item=>{
                      item.x = item.x_coordinate
                      item.y = item.y_coordinate
                      item.station_code = item.station_code ? item.station_code :''
                      // item.width = 15
                      // item.height = 15
                      item.status = 'WAIT'
                      item.type = 'circle'
                    })
                    this.nodes.unshift({
                      id: +new Date(),
                      x: 0,
                      y: 0,
                      type: 'image',
                      width: 1856,
                      height: 266,
                      describe: '作业指导图片',
                      strokeWidth: 1,
                      href: this.image_content
                    })
                    this.connections = []
                  }else{
                      this.nodes = [{
                        id: +new Date(),
                        x: 0,
                        y: 0,
                        type: 'image',
                        width: 1856,
                        height: 266,
                        describe: '作业指导图片',
                        strokeWidth: 1,
                        href: this.image_content
                      }]
                    this.connections = []
                  }
                }
                this.loading = false
            })
            .catch(() => {
                this.$message({
                    message: '获取点位异常',
                    type: 'error'
                })
                this.nodes = [{
                  id: +new Date(),
                  x: 0,
                  y: 0,
                  type: 'image',
                  width: 1856,
                  height: 266,
                  describe: '作业指导图片',
                  strokeWidth: 1,
                  href: this.image_content
                }]
                this.connections = []
                this.loading = false
              })
    },
    methods: {
    batchAdd() {
    },
    add() {
      const arr = this.$refs.chart.internalNodes.filter(item =>item.type !='image')
      const idSet = new Set();
      
      // 遍历数组，将所有id值存储到 Set 中
      arr.forEach(item => {
        idSet.add(parseInt(item.point_id));
      });
      
      // 判断1~8是否在 Set 中存在，如果不存在则打印对应的数字
      for (let i = 1; i <= 50; i++) {
        if (!idSet.has(i)) {
          this.$refs.chart.add({
              id: +new Date(),
              x: 50 * this.$refs.chart.internalNodes.length,
              y: 50,
              ng_color: "RED",
              ok_color: "GREEN",
              section_code: "CUT",
              type: 'circle',
              point_id: i,
              size: this.diameter,
              describe: '圆圈',
              stroke: '#FFFFFF',
              strokeWidth: '2px',
              status: 'WAIT'
          })
          break;
        }
      }
      
    },
    save() {
        this.$refs.chart.save()
    },
    handleRefreshChart() {
        if (this.$refs.chart !== undefined) {
          this.$refs.chart.init(this.nodeForm, this.thisH)
        }
    },
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
    handleEditConnection(connection) {
      // this.connectionForm.target = connection;
      // this.connectionDialogVisible = true;
    },
    handleDblClick(position) {},
    handleDelNode(type, id) {},
    async handleChartSave(nodes, connections) {
      let pointList = JSON.parse(JSON.stringify(nodes))
      pointList = pointList.filter(item=>item.type !='image')
      pointList.map((item)=>{
          item.x_coordinate = item.x
          item.y_coordinate = item.y
          delete item.color
          delete item.x
          delete item.y
          delete item.created_by
          delete item.creation_date
          delete item.describe
          delete item.last_update_date
          delete item.last_updated_by
          delete item.name
          delete item.render
          delete item.status
          delete item.strokeWidth
          delete item.subId_stepId
          delete item.type
          delete item.width
          delete item.height
      })
      const save = {
        section_code:this.section_code,
        user_name: Cookies.get('userName'),
        pointList,
      }
      // 修改
      crudMainService.savePonit(save)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '保存成功', type: 'success' })
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'warning' })
          }
        })
        .catch(() => {
          this.$message({
            message: '保存异常',
            type: 'error'
          })
        })
    },
    }
  }
  </script>
<style lang="less" scoped>
    .dcs-main {
            margin: 10px 0;
            width: 100%;
            position: relative;
            img{
                background-size: 100% 100%;
                width: 100%;
                height: 100%;
            }
        }
</style>
  