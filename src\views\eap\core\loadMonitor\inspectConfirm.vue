<template>
  <div>
    <div style="margin-top:10px;text-align:center">
      <el-button type="primary" @click="handleSendInfo('OK')">继续量产</el-button>
      <el-button type="primary" @click="handleSendInfo('NG')">强制退载具</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型  合格/不合格
  data() {
    return {

    }
  },
  mounted: function() {

  },
  created: function() {
  },
  methods: {
    handleSendInfo(type) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebInspectConfirmStatus,
        TagValue: type === 'OK' ? '1' : '2'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebInspectConfirmStatus.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
