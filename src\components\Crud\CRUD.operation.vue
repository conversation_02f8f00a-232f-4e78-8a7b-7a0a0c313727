<template>
  <div class="crud-opts">
    <span class="crud-opts-left">
      <!--左侧插槽-->
      <slot name="left" />
      <el-button v-if="crud.optShow.add" v-permission="permission.add" class="filter-item" size="small" type="primary" icon="el-icon-plus" plain round @click="crud.toAdd">
        {{ $t('lang_pack.commonPage.add') }}
      </el-button>  <!-- 新增 -->
      <el-button v-if="crud.optShow.edit" v-permission="permission.edit" class="filter-item" size="small" type="warning" icon="el-icon-edit" plain round :disabled="crud.selections.length !== 1" @click="crud.toEdit(crud.selections[0])">
        {{ $t('lang_pack.commonPage.edit') }}
      </el-button>  <!-- 修改 -->
      <el-button
        v-if="crud.optShow.del"
        slot="reference"
        v-permission="permission.del"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        plain
        round
        size="small"
        :loading="crud.delAllLoading"
        :disabled="crud.selections.length === 0"
        @click="toDelete(crud.selections)"
      >
        {{ $t('lang_pack.commonPage.remove') }}
      </el-button>  <!-- 删除 -->
      <!--右侧-->
      <slot name="right" />
    </span>
    <el-button-group v-if="crud.optShow.buttonGroup == undefined ? true : crud.optShow.buttonGroup" class="crud-opts-right">
      <!--左侧插槽-->
      <slot name="group_left" />
      <el-button v-if="updateOperation(crud.optShow.search)" size="small" icon="el-icon-search" @click="toggleSearch()" />
      <el-button v-if="updateOperation(crud.optShow.refresh)" size="small" icon="el-icon-refresh" @click="crud.refresh()" />
      <el-button v-if="updateOperation(crud.optShow.down)" v-permission="permission.down" :loading="crud.downloadLoading" :disabled="!crud.data.length" size="small" icon="el-icon-download" @click="crud.doExport" />
      <el-popover placement="bottom-end" width="150" trigger="click">
        <el-button v-if="updateOperation(crud.optShow.grid)" slot="reference" size="small" icon="el-icon-s-grid">
          <i class="fa fa-caret-down" aria-hidden="true" />
        </el-button>
        <el-checkbox v-model="allColumnsSelected" :indeterminate="allColumnsSelectedIndeterminate" @change="handleCheckAllChange">
          {{ $t('lang_pack.commonPage.checkAll') }}
        </el-checkbox>
        <el-checkbox v-for="item in tableColumns" :key="item.property" v-model="item.visible" @change="handleCheckedTableColumnsChange(item)">
          {{ item.label }}
        </el-checkbox>
      </el-popover>
      <!--右侧-->
      <slot name="group_right" />
    </el-button-group>
  </div>
</template>
<script>
import CRUD, { crud } from '@crud/crud'

function sortWithRef(src, ref) {
  const result = Object.assign([], ref)
  let cursor = -1
  src.forEach(e => {
    const idx = result.indexOf(e)
    if (idx === -1) {
      cursor += 1
      result.splice(cursor, 0, e)
    } else {
      cursor = idx
    }
  })
  return result
}

export default {
  mixins: [crud()],
  props: {
    permission: {
      type: Object,
      default: () => {
        return {}
      }
    },
    hiddenColumns: {
      type: Array,
      default: () => {
        return []
      }
    },
    ignoreColumns: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableColumns: [],
      allColumnsSelected: true,
      allColumnsSelectedIndeterminate: false,
      tableUnwatcher: null,
      // 忽略下次表格列变动
      ignoreNextTableColumnsChange: false
    }
  },
  watch: {
    'crud.props.table'() {
      this.updateTableColumns()
      this.tableColumns.forEach(column => {
        if (this.hiddenColumns.indexOf(column.property) !== -1) {
          column.visible = false
          this.updateColumnVisible(column)
        }
      })
    },
    'crud.props.table.store.states.columns'() {
      this.updateTableColumns()
    }
  },
  created() {
    this.crud.updateProp('searchToggle', true)
  },
  methods: {
    updateOperation(flag) {
      if (flag || flag === null || flag === undefined) return true
      return false
    },
    updateTableColumns() {
      const table = this.crud.getTable()
      if (!table) {
        this.tableColumns = []
        return
      }
      let cols = null
      const columnFilter = e => e && e.type === 'default' && e.property && this.ignoreColumns.indexOf(e.property) === -1
      const refCols = table.columns.filter(columnFilter)
      if (this.ignoreNextTableColumnsChange) {
        this.ignoreNextTableColumnsChange = false
        return
      }
      this.ignoreNextTableColumnsChange = false
      const columns = []
      const fullTableColumns = table.$children.map(e => e.columnConfig).filter(columnFilter)
      cols = sortWithRef(fullTableColumns, refCols)
      cols.forEach(config => {
        const column = {
          property: config.property,
          label: config.label,
          visible: refCols.indexOf(config) !== -1
        }
        columns.push(column)
      })
      this.tableColumns = columns
    },
    toDelete(datas) {
      this.$confirm(`${this.$t('lang_pack.vie.conSel')} ${datas.length} ${this.$t('lang_pack.vie.Piece')}`, this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          this.crud.delAllLoading = true
          this.crud.doDelete(datas)
        })
        .catch(() => {})
    },
    handleCheckAllChange(val) {
      if (val === false) {
        this.allColumnsSelected = true
        return
      }
      this.tableColumns.forEach(column => {
        if (!column.visible) {
          column.visible = true
          this.updateColumnVisible(column)
        }
      })
      this.allColumnsSelected = val
      this.allColumnsSelectedIndeterminate = false
      // 表格动态高度时fixed列会出现错位，需要重新渲染表格
      this.crud.getTable().doLayout()
    },
    handleCheckedTableColumnsChange(item) {
      let totalCount = 0
      let selectedCount = 0
      this.tableColumns.forEach(column => {
        ++totalCount
        selectedCount += column.visible ? 1 : 0
      })
      if (selectedCount === 0) {
        this.crud.notify('请至少选择一列', CRUD.NOTIFICATION_TYPE.WARNING)
        this.$nextTick(function() {
          item.visible = true
        })
        return
      }
      this.allColumnsSelected = selectedCount === totalCount
      this.allColumnsSelectedIndeterminate = selectedCount !== totalCount && selectedCount !== 0
      this.updateColumnVisible(item)
      // 表格动态高度时fixed列会出现错位，需要重新渲染表格
      this.crud.getTable().doLayout()
    },
    updateColumnVisible(item) {
      const table = this.crud.props.table
      const vm = table.$children.find(e => e.prop === item.property)
      const columnConfig = vm.columnConfig
      if (item.visible) {
        // 找出合适的插入点
        const columnIndex = this.tableColumns.indexOf(item)
        vm.owner.store.commit('insertColumn', columnConfig, columnIndex + 1, null)
      } else {
        vm.owner.store.commit('removeColumn', columnConfig, null)
      }
      this.ignoreNextTableColumnsChange = true
    },
    toggleSearch() {
      this.crud.props.searchToggle = !this.crud.props.searchToggle
    }
  }
}
</script>

<style>
.crud-opts {
  padding: 4px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.crud-opts .crud-opts-right {
  margin-left: auto;
}
.crud-opts .crud-opts-right span {
  float: left;
}
</style>
