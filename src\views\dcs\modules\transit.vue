<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="过站信息" width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 工位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="station_code"
              :label="$t('lang_pack.sortingArea.CurrentStationNumber')"
            />
            <!-- 工位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="station_des"
              :label="$t('lang_pack.stationflow.stationDes')"
            />
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="task_num"
              :label="$t('lang_pack.taskList.TaskNumber')"
            />
            <!-- 到达时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="arrive_date"
              :label="$t('lang_pack.sortingArea.arriveDate')"
            />
            <!-- 离开时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="leave_date"
              :label="$t('lang_pack.sortingArea.leaveDate')"
            />
            <!-- 消耗时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="cost_time"
              :label="$t('lang_pack.sortingArea.StationConsumptionTime')"
            />
          </el-table>
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'TRANSIT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '过站信息',
      // 唯一字段
      idField: 'task_num',
      // 排序
      sort: ['task_num asc'],
      // CRUD Method
      crudMethod: { ...crudLoadAreaOper },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        task_num: this.propsData.task_num
      }
    })
  },
  props: {
    task_num: {
      type: String
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
</style>
