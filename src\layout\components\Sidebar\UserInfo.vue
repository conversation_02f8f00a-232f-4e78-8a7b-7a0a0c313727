<template>
  <div v-if="getUserValue() === '0'" class="sidebar-user-container">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" :to="$route.fullPath">
        <el-dropdown ref="messageDrop" size="medium" trigger="click" style="float: right; margin-right: 15px">
          <div class="sidebar-userinfo-collapse">
            <img :src="user.avatarPath ? 'data:image/png;base64,' + user.avatarPath : Avatar" style="width:45px;height:45px;border:1px solid #FFFFFF;border-radius: 50%;">
          </div>
          <el-dropdown-menu slot="dropdown">
            <span @click="$router.push({ path: '/user/center' })">
              <el-dropdown-item icon="el-icon-user">个人信息</el-dropdown-item>
            </span>
            <!-- <el-dropdown-item icon="el-icon-edit">修改密码</el-dropdown-item> -->
            <span style="display:block;" @click="open">
              <el-dropdown-item icon="el-icon-back" divided>退出系统</el-dropdown-item>
            </span>
          </el-dropdown-menu>
        </el-dropdown>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" :to="$route.fullPath">
        <div v-if="getWarningValue()" class="warningInfo" @click="openInfo()">
          <el-badge :value="total" :max="99">
            <i class="el-icon-bell" />
          </el-badge>
        </div>
        <el-dropdown ref="messageDrop" size="medium" trigger="click" style="float: right; margin-right: 15px">
          <div class="sidebar-userinfo">
            <div class="wrapImg">
              <img :src="user.avatarPath ? 'data:image/png;base64,' + user.avatarPath : Avatar" title="点击查看个人信息">
              <span>{{ user.nickName }}</span>
              <i ref="arrowRef" class="el-icon-more arrowStyle" />
            </div>
          </div>
          <el-dropdown-menu slot="dropdown">
            <span @click="show = true">
              <el-dropdown-item icon="el-icon-s-data">布局设置</el-dropdown-item>
            </span>
            <span @click="openPeople">
              <el-dropdown-item icon="el-icon-user">个人信息</el-dropdown-item>
            </span>
            <!-- <el-dropdown-item icon="el-icon-edit">修改密码</el-dropdown-item> -->
            <span style="display:block;" @click="open">
              <el-dropdown-item icon="el-icon-back" divided>退出系统</el-dropdown-item>
            </span>
          </el-dropdown-menu>
        </el-dropdown>
      </router-link>

    </transition>
    <AlarmInfo ref="AlarmInfo" @ok="alarmNum" />
  </div>
</template>

<script>
import Avatar from '@/assets/images/avatar.png'
import AlarmInfo from '@/components/AlarmInfo'
import { mapGetters } from 'vuex'
export default {
  name: 'SidebarUserInfo',
  components: { AlarmInfo },
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      Avatar: Avatar,
      total: 0
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'device', 'user', 'baseApi']),
    show: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    showTheme: {
      get() {
        return this.$store.state.settings.themeStyle
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'themeStyle',
          value: val
        })
      }
    }
  },
  mounted() {
  },
  methods: {
    openInfo() {
      this.$refs.AlarmInfo.dialogVisible = true
    },
    alarmNum(val) {
      this.total = val
    },
    open() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.logout()
      })
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload()
      })
    },
    openPeople() {
      this.$router.push({ path: '/user/center' })
    },
    getWarningValue() {
      // const arr = Cookies.get('userCode') ? JSON.parse(Cookies.get('userCode')) : Cookies.get('userCode')
      // if(!arr.length){
      //   return true
      // }
      // const value = Boolean(JSON.parse(Cookies.get('userCode'))[0].parameter_val.split(',')[JSON.parse(Cookies.get('userCode'))[0].parameter_val.split(',').length - 2])
      // const cookieValue = value ? value : true
      // this.$store.state.settings.warningValue = cookieValue
      return this.$store.state.settings.warningValue
    },
    getUserValue() {
      // const arr = Cookies.get('userCode') ? JSON.parse(Cookies.get('userCode')) : Cookies.get('userCode')
      // if(!arr.length){
      //   return '0'
      // }
      // const value = JSON.parse(Cookies.get('userCode'))[0].parameter_val.split(',')[JSON.parse(Cookies.get('userCode'))[0].parameter_val.split(',').length - 1]
      // const cookieValue = value ? value : '0'
      // this.$store.state.settings.userValue = cookieValue
      return this.$store.state.settings.userValue
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/variables.scss';
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-user-container {
  position: absolute;
  width: 100%;
  height: 65px;
  line-height: 50px;
  overflow: hidden;
  bottom: 0;
  background: var(--el-menuBg--color);
  padding: 10px 0;
  border-top: 1px solid #bcd0fc;
  .el-dropdown {
    display: flex;
    justify-content: center;
    margin-right: 0 !important;
    float: inherit !important;
  }

  .sidebar-userinfo {
    width: 170px;
    height: 45px;
    line-height: 45px;
    border-radius: 35px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    // background-color: #263bb0;
    .wrapImg{
      display: flex;
      align-items: center;
      img{
            width: 45px;
            height: 45px;
            border: 1px solid #ffffff;
            border-radius: 50%;
            margin-right: 20px;
          }
    }
    span{
      color: #ffffff;
      font-size: 13px;
    }
  }
  .sidebar-userinfo-collapse {
    width: 45px;
    height: 45px;
    line-height: 45px;
    border-radius: 35px;
    background-color: #79a0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 24px !important;
      height: 24px !important;
      display: block;
      line-height: 24px;
      text-align: center;
    }
    i {
      color: rgb(106, 155, 255);
      font-size: 16px;
    }
  }
}
.arrowStyle{
  color: #ffffff;
  transform: rotate(90deg);
  padding-bottom: 5px;
}
</style>
<style lang="less" scoped>
::v-deep .warningInfo{
            width:30px;
            height:50px;
            text-align:center;
            line-height:40px;
            .el-badge__content{
              top:10px !important;
            }
            .el-badge{
              height:20px;
              .el-icon-bell{
                font-size:18px;
                color:#fff;
              }
            }
        }
</style>
