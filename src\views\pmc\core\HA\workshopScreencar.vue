<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="andonlist">
            <ul ref="andon">
              <li v-for="(item , index) in stationCarArr" :key="index" :class="item.make_order !== '' ? 'nomalstatus':'commonstatus'"><span>{{ item.station_code }}</span><span class="carorder">{{ item.make_order }}</span></li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbgone">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="wrapmarquee">
            <marquee loop="infinite" scrollamount="15">
              <div class="wraptext">
                <p>{{ below_show }}</p>
              </div>
            </marquee>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getStationTime } from '@/api/pmc/sysTime'
import { getScreencar } from '@/api/pmc/sysworkshopScreencar'
import { getAndonContent } from '@/api/pmc/sysAndonContent'
import headlbg from '@/assets/images/headlbg.png'
import qyc from '@/assets/images/qyc2.jpg'
export default {
  name: 'workshopScreencar',
  components: {
  },
  data() {
    return {
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      stationCarArr: [],
      rownum: '',
      totalnum: '',
      headlbg: headlbg,
      qyc: qyc,
      below_show: '',
      prod_line_des: '',
      timer1: '',
      timer2: '',
      timer3: ''
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.timer1 = setInterval(() => {
      this.initstationTime()
    }, 1000)
    // 加载工位屏车辆状态
    this.initScreencar()
    this.timer2 = setInterval(() => {
      this.initScreencar()
    }, 1000)
    // 加载工位屏标语
    this.initAndonContent()
    this.timer3 = setInterval(() => {
      this.initAndonContent()
    }, 5000)
  },
  mounted() {
    this.setAndonStyle()
  },
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
  },
  methods: {
    setAndonStyle() {
      switch (this.$route.query.prod_line_code) {
        case 'MB':
          this.$refs.andon.classList.add('otherliMB')
          break
        case 'UB':
          this.$refs.andon.classList.add('otherliUB')
          break
        case 'HAMF':
          this.$refs.andon.classList.add('otherliHAMF')
          break
        case 'RF':
          this.$refs.andon.classList.add('otherliHAMF')
          break
        case 'FE':
          this.$refs.andon.classList.add('otherliFE')
          break
        case 'FD':
          this.$refs.andon.classList.add('otherliFD')
          break
        case 'FL':
          this.$refs.andon.classList.add('otherliFL')
          break
        case 'WBS':
          this.$refs.andon.classList.add('otherli')
          break
        case 'BS':
          this.$refs.andon.classList.add('otherliBS')
          break
        case 'RE':
          this.$refs.andon.classList.add('otherliRE')
          break
      }
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏车辆状态
    initScreencar() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        prod_line_type: this.$route.query.prod_line_type,
        work_center_code: this.$route.query.work_center_code
      }
      getScreencar(query)
        .then(res => {
          console.log(res)
          this.stationCarArr = res.data[0].make_order_list
          this.prod_line_des = res.data[0].prod_line_des
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏标语
    initAndonContent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code
      }
      getAndonContent(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data === null || res.data.length === 0) {
            this.below_show = ''
          }
          this.below_show = res.data[0].below_show
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.carInfoTable{
  min-height: calc(100vh - 50px);
}
.tableheight{
  min-height: calc(100vh - 70px);
}
.marginT{
  margin-top: 10px;
}
.slideT{
  margin-top: 20px;
}
.cardheadbg{
  background-color: #031c45;
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  // margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .andonlist{
  height: 545px;
  ul{
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    li{
      list-style: none;
      width: 11.2%;
      height: 99px;
      text-align: center;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 5px;
      padding: 5px;
      position: relative;
      border-radius: 5px;
      span{
        font-size: 30px;
        font-weight: 700;
        z-index: 2;
      }
      .carorder{
        font-size: 24px;
      }
      img{
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
    }
  }
}
.cardheadbgone{
  background-color: #0070c2;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ff0000;
  font-size: 60px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  height: 80px;
  word-spacing: 150px;
  }
}
::v-deep .commonstatus{
  background-color: #ffffff;
}
::v-deep .nomalstatus{
  background-color: #00a047;
  span{
    color: #ffffff;
  }
}
::v-deep .errorstatus{
  background-color: #ff0000;
}
::v-deep .warningstatus{
  background-color: #ffd600;
}
::v-deep .el-card{
  border-radius: 0;
}
::v-deep .otherliMB{
  li{
      width: 19.14% !important;
      height: 126px !important;
      span{
        font-size: 32px !important;
      }
      .carorder{
        font-size: 26px !important;
      }
  }
}
::v-deep .otherliUB{
  li{
        width: 19.14% !important;
        height: 171px !important;
      span{
        font-size: 32px !important;
      }
      .carorder{
        font-size: 26px !important;
      }
  }
}
::v-deep .otherliHAMF{
  li{
        width: 32.49% !important;
        height: 172px !important;
      span{
        font-size: 54px !important;
      }
      .carorder{
        font-size: 32px !important;
      }
  }
}
::v-deep .otherliFE{
  li{
        width: 32.48% !important;
        height: 262px !important;
      span{
        font-size: 45px !important;
      }
      .carorder{
        font-size: 36px !important;
      }
  }
}
::v-deep .otherliFD{
  li{
        width: 15.83% !important;
        height: 126px !important;
      span{
        font-size: 26px !important;
      }
      .carorder{
        font-size: 22px !important;
      }
  }
}
::v-deep .otherliFL{
  li{
        width: 15.83% !important;
        height: 172px !important;
      span{
        font-size: 28px !important;
      }
      .carorder{
        font-size: 24px !important;
      }
  }
}
::v-deep .otherliBS{
  li{
        width: 24.15% !important;
        height: 172px !important;
      span{
        font-size: 36px !important;
      }
      .carorder{
        font-size: 30px !important;
      }
  }
}
::v-deep .otherliRE{
  li{
        width: 24.14% !important;
        height: 262px !important;
      span{
        font-size: 36px !important;
      }
      .carorder{
        font-size: 32px !important;
      }
  }
}
</style>
