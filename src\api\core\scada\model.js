import request from '@/utils/request'

// 查询驱动型号
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaModelSel',
    method: 'post',
    data
  })
}
// 新增驱动型号
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaModelIns',
    method: 'post',
    data
  })
}
// 修改驱动型号
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaModelUpd',
    method: 'post',
    data
  })
}
// 删除驱动型号
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaModelDel',
    method: 'post',
    data
  })
}

// 驱动型号Lov
export function lovModel(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaModelLov',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, lovModel }
