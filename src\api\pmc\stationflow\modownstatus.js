import request from '@/utils/request'

// 查询下发订单状态
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeMoDownStatusSel',
    method: 'post',
    data
  })
}
// 新增下发订单状态
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeMoDownStatusIns',
    method: 'post',
    data
  })
}
// 修改下发订单状态
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeMoDownStatusUpd',
    method: 'post',
    data
  })
}
// 删除下发订单状态
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeMoDownStatusDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

