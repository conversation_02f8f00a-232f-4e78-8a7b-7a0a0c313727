import request from '@/utils/request'

// 查询库位表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodStockSelect',
    method: 'post',
    data
  })
}

// 新增库位表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodStockInsert',
    method: 'post',
    data
  })
}
// 修改库位表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodStockUpdate',
    method: 'post',
    data
  })
}
// 删除库位表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodStockDelete',
    method: 'post',
    data
  })
}

// 修改库位表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodStockEnableFlagUpd',
    method: 'post',
    data
  })
}

// 手动入库WMS天车库位表
export function inStock(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsManualInStock',
    method: 'post',
    data
  })
}

// 手动出库WMS天车库位表
export function outStock(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsManualOutStock',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag, inStock, outStock }

