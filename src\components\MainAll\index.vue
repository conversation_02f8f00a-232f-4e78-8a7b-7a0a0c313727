<template>
  <div
    id="chartSop"
    tabindex="0"
    :style="{
      width: isNaN(width) ? width : width + 'px',
      height: isNaN(height) ? height : height + 'px',
      cursor: cursor
    }"
    @mousemove="handleChartMouseMove"
    @mouseup="handleChartMouseUp"
    @dblclick="handleChartDblClick($event)"
    @mousewheel="handleChartMouseWheel"
    @mousedown="handleChartMouseDown($event)"
  >
    <svg id="svg">
      <!-- <rect class="selection" height="0" width="0" /> -->
    </svg>
    <el-dialog title="详细信息" width="65%" :before-close="handleClose" :visible.sync="dialogVisible">
      <p class="title">当前所在工位:{{ dataObj.title }}</p>
      <p class="title">当前工位任务信息</p>
      <el-table
        v-loading="loading"
        border
        style="width: 100%;"
        :data="tableData"
        :row-key="row => row.id"
      >
        <!-- 任务号 -->
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="task_num"
          :label="$t('lang_pack.cuttingZone.TaskNumber')"
        />
        <!-- 钢板型号 -->
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="model_type"
          :label="$t('lang_pack.cuttingZone.SteelPlateModel')"
        />
        <!-- 切割文件名称 -->
        <el-table-column
          :show-overflow-tooltip="true"
          prop="nc_name"
          label="切割文件名称"
          align="center"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="pallet_num"
          label="托盘号"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="arrive_date"
          label="任务到达时间"
        />
      </el-table>
      <el-divider />
      <p class="title">自走棋</p>
      <template>
        <el-button v-for="(item,index) in dataObj.goStationCode" :key="index" type="primary" @click="handleTagWrite(dataObj.title,item)">{{ dataObj.title }}到 {{ item }} (手动)</el-button>
      </template>
      <el-divider />
      <span
        class="stress-btn-3d"
        @mouseup="stressRFID('0')"
        @mousedown="stressRFID('1')"
      >重读RFID</span>
    </el-dialog>
  </div>
</template>
<style src="./index.css" scoped></style>
<script>
import { line2, lineTo } from '@/utils/svg'
import * as d3 from 'd3'
import { between, distanceOfPointToLine, getEdgeOfPoints, pointRectangleIntersection } from '@/utils/math'
import render from '@/components/MainAll/render'
import crudMeStationMis from '@/api/dcs/core/meStationMis/meStationMis'

export default {
  name: 'MainChart',
  props: {
    nodes: {
      type: Array,
      default: () => [{ id: 1, x: 140, y: 270, name: 'Start', type: 'start' }, { id: 2, x: 540, y: 270, name: 'End', type: 'end' }]
    },
    connections: {
      type: Array,
      default: () => [
        {
          source: { id: 1, position: 'right' },
          destination: { id: 2, position: 'left' },
          id: 1,
          type: 'pass'
        }
      ]
    },
    width: {
      type: [String, Number],
      default: 800
    },
    height: {
      type: [String, Number],
      default: 600
    },
    readonly: {
      type: Boolean,
      default: false
    },
    render: {
      type: Function,
      default: render
    }
  },
  data() {
    return {
      internalNodes: [],
      internalConnections: [],
      connectingInfo: {
        source: null,
        sourcePosition: null
      },
      selectionInfo: null,
      currentNodes: [],
      currentConnections: [],
      /**
       * Mouse position(relative to chart div)
       */
      cursorToChartOffset: { x: 0, y: 0 },
      clickedOnce: false,
      pathClickedOnce: false,
      dragEnd: false,
      /**
       * lines of all internalConnections
       */
      lines: [],
      zoom: 1,
      fastLinkNodes: [],
      showPopover: false,
      popoverContent: '123456789',
      dialogVisible: false,
      tableData: [],
      loading: false,
      dataObj: {},
      // 重读RFID
      stressKeyList: [
        { 'key': 'SlTransGSlPlc/DcsStatusG2/DcsS_Spare_4', 'mqtt_port': '8083' }, // G2
        { 'key': 'QgTransGPmPlc/DcsStatusG3/DcsS_Spare_4', 'mqtt_port': '8084' }, // G3
        { 'key': 'QgTransGHcPlc/DcsStatusG4/DcsS_Spare_4', 'mqtt_port': '8084' }, // G4
        { 'key': 'QgTransGCutPlc01/DcsStatusG5/DcsS_Spare_4', 'mqtt_port': '8084' }, // G5
        { 'key': 'QgTransGCutPlc01/DcsStatusG6/DcsS_Spare_4', 'mqtt_port': '8084' }, // G6
        { 'key': 'QgTransGCutPlc02/DcsStatusG7/DcsS_Spare_4', 'mqtt_port': '8084' }, // G7
        { 'key': 'QgTransGHcPlc/DcsStatusG8/DcsS_Spare_4', 'mqtt_port': '8084' }, // G8
        { 'key': 'QgTransGCutPlc02/DcsStatusG9/DcsS_Spare_4', 'mqtt_port': '8084' }, // G9
        { 'key': 'QgTransGCutPlc02/DcsStatusG10/DcsS_Spare_4', 'mqtt_port': '8084' }, // G10
        { 'key': 'QgTransGHcPlc/DcsStatusG11/DcsS_Spare_4', 'mqtt_port': '8084' }, // G11
        { 'key': 'FjTransGPlc01/DcsStatusG12/DcsS_Spare_4', 'mqtt_port': '8085' }, // G12
        { 'key': 'FjTransGPlc01/DcsStatusG13/DcsS_Spare_4', 'mqtt_port': '8085' }, // G13
        { 'key': 'FjTransGPlc01/DcsStatusG14/DcsS_Spare_4', 'mqtt_port': '8085' }, // G14
        { 'key': 'FjTransGPlc01/DcsStatusG15/DcsS_Spare_4', 'mqtt_port': '8085' }, // G15
        { 'key': 'FjTransGPlc02/DcsStatusG16/DcsS_Spare_4', 'mqtt_port': '8085' }// G16
      ],
      // 目标工位
      tagKeyList: [
        { 'key': 'SlTransGSlPlc/DcsStatusG2/DcsS_PurposeStation', 'mqtt_port': '8083' }, // G2
        { 'key': 'QgTransGPmPlc/DcsStatusG3/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G3
        { 'key': 'QgTransGHcPlc/DcsStatusG4/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G4
        { 'key': 'QgTransGCutPlc01/DcsStatusG5/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G5
        { 'key': 'QgTransGCutPlc01/DcsStatusG6/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G6
        { 'key': 'QgTransGCutPlc02/DcsStatusG7/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G7
        { 'key': 'QgTransGHcPlc/DcsStatusG8/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G8
        { 'key': 'QgTransGCutPlc02/DcsStatusG9/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G9
        { 'key': 'QgTransGCutPlc02/DcsStatusG10/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G10
        { 'key': 'QgTransGHcPlc/DcsStatusG11/DcsS_PurposeStation', 'mqtt_port': '8084' }, // G11
        { 'key': 'FjTransGPlc01/DcsStatusG12/DcsS_PurposeStation', 'mqtt_port': '8085' }, // G12
        { 'key': 'FjTransGPlc01/DcsStatusG13/DcsS_PurposeStation', 'mqtt_port': '8085' }, // G13
        { 'key': 'FjTransGPlc01/DcsStatusG14/DcsS_PurposeStation', 'mqtt_port': '8085' }, // G14
        { 'key': 'FjTransGPlc01/DcsStatusG15/DcsS_PurposeStation', 'mqtt_port': '8085' }, // G15
        { 'key': 'FjTransGPlc02/DcsStatusG16/DcsS_PurposeStation', 'mqtt_port': '8085' }// G16
      ],
      // 请求放行
      requestKeyList: [
        'SlTransGSlPlc/DcsStatusG2/DcsS_ReleaseReq', // G2
        'QgTransGPmPlc/DcsStatusG3/DcsS_ReleaseReq', // G3
        'QgTransGHcPlc/DcsStatusG4/DcsS_ReleaseReq', // G4
        'QgTransGCutPlc01/DcsStatusG5/DcsS_ReleaseReq', // G5
        'QgTransGCutPlc01/DcsStatusG6/DcsS_ReleaseReq', // G6
        'QgTransGCutPlc02/DcsStatusG7/DcsS_ReleaseReq', // G7
        'QgTransGHcPlc/DcsStatusG8/DcsS_ReleaseReq', // G8
        'QgTransGCutPlc02/DcsStatusG9/DcsS_ReleaseReq', // G9
        'FjTransGPlc01/DcsStatusG13/DcsS_ReleaseReq', // G13
        'QgTransGCutPlc02/DcsStatusG10/DcsS_ReleaseReq', // G10
        'QgTransGHcPlc/DcsStatusG11/DcsS_ReleaseReq', // G11
        'FjTransGPlc01/DcsStatusG12/DcsS_ReleaseReq', // G12
        'FjTransGPlc01/DcsStatusG14/DcsS_ReleaseReq', // G14
        'FjTransGPlc01/DcsStatusG15/DcsS_ReleaseReq', // G15
        'FjTransGPlc02/DcsStatusG16/DcsS_ReleaseReq'// G16
      ],
      from_station_code: '',
      to_station_code: ''
    }
  },
  computed: {
    hoveredConnector() {
      for (const node of this.internalNodes) {
        const connectorPosition = this.getConnectorPosition(node)
        for (const prop in connectorPosition) {
          const entry = connectorPosition[prop]
          if (Math.hypot(entry.x - this.cursorToChartOffset.x / this.zoom, entry.y - this.cursorToChartOffset.y / this.zoom) < 10) {
            return { position: prop, node: node }
          }
        }
      }
      return null
    },
    hoveredConnection() {
      for (const line of this.lines) {
        const distance = distanceOfPointToLine(line.sourceX, line.sourceY, line.destinationX, line.destinationY, this.cursorToChartOffset.x, this.cursorToChartOffset.y)
        if (distance < 5 && between(line.sourceX - 2, line.destinationX + 2, this.cursorToChartOffset.x) && between(line.sourceY - 2, line.destinationY + 2, this.cursorToChartOffset.y)) {
          const connections = this.internalConnections.filter(item => item.id === line.id)
          return connections.length > 0 ? connections[0] : null
        }
      }
      return null
    },
    cursor() {
      // if (this.connectingInfo.source || this.hoveredConnector) {
      //   return "crosshair";
      // }
      if (this.hoveredConnection != null) {
        return 'pointer'
      }
      return null
    }
  },
  watch: {
    internalNodes: {
      immediate: true,
      deep: true,
      handler() {
        this.renderNodes()
        this.renderConnections()
      }
    },
    internalConnections: {
      immediate: true,
      deep: true,
      handler() {
        this.renderConnections()
      }
    },
    selectionInfo: {
      immediate: true,
      deep: true,
      handler() {
        this.renderSelection()
      }
    },
    currentNodes: {
      immediate: true,
      deep: true,
      handler() {
        this.renderNodes()
      }
    },
    currentConnections: {
      immediate: true,
      deep: true,
      handler() {
        this.renderConnections()
      }
    },
    cursorToChartOffset: {
      immediate: true,
      deep: true,
      handler() {
        if (this.selectionInfo) {
          this.renderSelection()
        }
      }
    },
    connectingInfo: {
      immediate: true,
      deep: true,
      handler() {
        this.renderConnections()
      }
    },
    nodes: {
      immediate: true,
      deep: true,
      handler() {
        this.init()
      }
    },
    connections: {
      immediate: true,
      deep: true,
      handler() {
        this.init()
      }
    }
  },
  mounted() {
    const that = this
    that.init()
    // document.onkeydown = function(event) {
    //   console.log(event.keyCode)
    //   switch (event.keyCode) {
    //     case 37:
    //       that.moveCurrentNode(-10, 0)
    //       break
    //     case 38:
    //       that.moveCurrentNode(0, -10)
    //       break
    //     case 39:
    //       that.moveCurrentNode(10, 0)
    //       break
    //     case 40:
    //       that.moveCurrentNode(0, 10)
    //       break
    //     case 27:
    //       that.currentNodes.splice(0, that.currentNodes.length)
    //       that.currentConnections.splice(0, that.currentConnections.length)
    //       break
    //     case 65:
    //       if (document.activeElement === document.getElementById('chartSop')) {
    //         that.currentNodes.splice(0, that.currentNodes.length)
    //         that.currentConnections.splice(0, that.currentConnections.length)
    //         that.currentNodes.push(...that.internalNodes)
    //         that.currentConnections.push(...that.internalConnections)
    //         event.preventDefault()
    //       }
    //       break
    //     case 46:
    //       return
    //       that.remove()
    //       break
    //     default:
    //       break
    //   }
    // }
  },
  created() {},
  methods: {
    jumpCut(data) {
      this.$emit('ok', data)
    },
    handleClose() {
      this.dialogVisible = false
    },
    // nextStep(from_code,to_code){
    // this.from_station_code = from_code
    // this.to_station_code = to_code
    // this.handleTagWrite(from_station_code,to_station_code)
    // if(!this.tableData.map(item=>item.task_num)[0]){
    //   this.$message({message:'该工位没有任务,无法跳转',type:'error'})
    //   return
    // }
    // const obj = this.nodes.find(e=>e.title === to_code)
    // this.loading = false
    // const query = {
    //   from_station_code:from_code,
    //   to_station_code:to_code
    // }
    // crudMeStationMis.mainChess(query).then(res=>{
    //   const defaultQuery = JSON.parse(JSON.stringify(res))
    //   if(defaultQuery.code === 0){
    //     this.$message({message:'工位移动成功',type:'success'})
    //     this.$nextTick(()=>{
    //       this.open(obj)
    //     })
    //   }else{
    //     this.$message({message:defaultQuery.msg || '工位移动异常',type:'error'})
    //   }
    // }).catch((err) => {
    //   this.$message({
    //       message: err.msg,
    //       type: 'error'
    //   })
    // })
    // },
    // 先写入目标工位 再写入请求放行
    handleTagWrite(from_station_code, to_station_code) {
      for (let i = 0; i < this.tagKeyList.length; i++) {
        const key = this.tagKeyList[i].key.split('/')[1].split('DcsStatus')[1]
        if (from_station_code === key) {
          var sendJson = {}
          var rowJson = []
          var newRow = {
            TagKey: this.tagKeyList[i].key,
            TagValue: to_station_code
          }
          rowJson.push(newRow)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + this.tagKeyList[i].key.split('/')[0]
          this.sendMessage('目标工位', topic, sendStr, this.tagKeyList[i].mqtt_port)
          // 写请求放行
          setTimeout(() => {
            this.handleTagWritRequest(from_station_code, this.tagKeyList[i].mqtt_port, '1')
          }, 1000)
          // 10s后 写请求放行 0
          setTimeout(() => {
            this.handleTagWritRequest(from_station_code, this.tagKeyList[i].mqtt_port, '0')
          }, 10000)
          break
        }
      }
    },
    handleTagWritRequest(from_station_code, mqtt_port, ReleaseVal) {
      for (let i = 0; i < this.requestKeyList.length; i++) {
        const key = this.requestKeyList[i].split('/')[1].split('DcsStatus')[1]
        if (from_station_code === key) {
          var sendJson = {}
          var rowJson = []
          var newRow = {
            TagKey: this.requestKeyList[i],
            TagValue: ReleaseVal
          }
          rowJson.push(newRow)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + this.requestKeyList[i].split('/')[0]
          this.sendMessage('请求放行', topic, sendStr, mqtt_port)
          break
        }
      }
    },
    stressRFID(val) {
      for (let i = 0; i < this.stressKeyList.length; i++) {
        const key = this.stressKeyList[i].key.split('/')[1].split('DcsStatus')[1]
        if (this.dataObj.title === key) {
          var sendJson = {}
          var rowJson = []
          var newRow = {
            TagKey: this.stressKeyList[i].key,
            TagValue: val
          }
          rowJson.push(newRow)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + this.stressKeyList[i].key.split('/')[0]
          this.sendMessage('重读RFID', topic, sendStr, this.stressKeyList[i].mqtt_port)
          const clickedSpan = event.target
          if (val === '1') {
            clickedSpan.classList.add('stress-btn-3d-active')
          } else {
            clickedSpan.classList.remove('stress-btn-3d-active')
          }
          break
        }
      }
    },
    sendMessage(val, topic, msg, mqtt_port) {
      if (!this.$parent.$parent.$parent[`mqttConnStatus_${mqtt_port}`]) {
        this.$message({
          message: '该mqtt端口未启用',
          type: 'error'
        })
        return
      }
      console.log(msg)
      // qos消息发布服务质量
      this.$parent.$parent.$parent[`clientMqtt_${mqtt_port}`].publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
          this.$message.success(`${val}` + '写入成功')
        } else {
          this.$message.error(`${val}` + '写入失败')
          // 写入失败是mqtt断开了  需要重新刷新页面
          location.reload()
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    open(obj) {
      if (obj.goStationCode && typeof (obj.goStationCode) === 'string') {
        obj.goStationCode = obj.goStationCode.split(',')
      }
      this.dataObj = obj
      this.loading = true
      this.dialogVisible = true
      crudMeStationMis.StationMisSel({ station_code: obj.title }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.tableData = defaultQuery.data
          } else {
            this.tableData = []
          }
          this.loading = false
        }
      })
        .catch((err) => {
          this.tableData = []
          this.loading = false
          this.$message({
            message: err.msg,
            type: 'error'
          })
        })
    },
    add(node) {
      if (this.readonly) {
        return
      }
      node.x = node.x / this.zoom
      node.y = node.y / this.zoom
      this.internalNodes.push(node)
      console.log(this.internalNodes)
    },
    editCurrent() {
      if (this.currentNodes.length === 1) {
        this.editNode(this.currentNodes[0])
      } else if (this.currentConnections.length === 1) {
        this.editConnection(this.currentConnections[0])
      }
    },
    editNode(node) {
      // if (this.readonly) {
      //   return;
      // }
      this.$emit('editnode', this.internalNodes.filter(item => item.point_id === node.point_id)[0], this)
    },
    editConnection(connection) {
      if (this.readonly) {
        return
      }
      this.$emit('editconnection', connection)
    },
    handleZoomIn() {
      const svg = document.getElementById('svg')
      const zoom = parseFloat(svg.style.zoom || 1)
      this.zoom = zoom + 0.1
      svg.style.zoom = this.zoom
    },
    handleZoomOut() {
      const svg = document.getElementById('svg')
      const zoom = parseFloat(svg.style.zoom || 1)
      if (zoom === 0.1) return
      this.zoom = zoom - 0.1
      svg.style.zoom = this.zoom
    },
    handleChartMouseWheel(event) {
      event.stopPropagation()
      event.preventDefault()
      if (event.ctrlKey) {
        const svg = document.getElementById('svg')
        let zoom = parseFloat(svg.style.zoom || 1)
        if (event.deltaY > 0 && zoom === 0.1) {
          return
        }
        zoom -= event.deltaY / 100 / 10
        console.log(zoom)
        this.zoom = zoom
        svg.style.zoom = zoom
      }
    },
    async handleChartMouseUp() {
      if (this.connectingInfo.source) {
        if (this.hoveredConnector) {
          if (this.connectingInfo.source.id !== this.hoveredConnector.node.id) {
            // Node can't connect to itself
            const tempId = +new Date()
            const conn = {
              source: {
                id: this.connectingInfo.source.id,
                position: this.connectingInfo.sourcePosition
              },
              destination: {
                id: this.hoveredConnector.node.id,
                position: this.hoveredConnector.position
              },
              id: tempId,
              type: 'pass',
              name: 'Pass',
              color: ''
            }
            this.internalConnections.push(conn)
          }
        }
        this.connectingInfo.source = null
        this.connectingInfo.sourcePosition = null
      }
      if (this.selectionInfo) {
        this.selectionInfo = null
      }
    },
    async handleChartMouseMove(event) {
      // calc offset of cursor to chart
      const boundingClientRect = event.currentTarget.getBoundingClientRect()
      const actualX = event.pageX - boundingClientRect.left - window.scrollX
      this.cursorToChartOffset.x = Math.trunc(actualX)
      const actualY = event.pageY - boundingClientRect.top - window.scrollY
      this.cursorToChartOffset.y = Math.trunc(actualY)

      if (this.connectingInfo.source) {
        await this.renderConnections()

        d3.selectAll('#svg .connector').classed('active', true)
        const sourceOffset = this.getNodeConnectorOffset(this.connectingInfo.source.id, this.connectingInfo.sourcePosition)
        const destinationPosition = this.hoveredConnector ? this.hoveredConnector.position : null

        this.arrowTo(sourceOffset.x, sourceOffset.y, this.cursorToChartOffset.x / this.zoom, this.cursorToChartOffset.y / this.zoom, this.connectingInfo.sourcePosition, destinationPosition)
      }
    },
    handleChartDblClick(event) {
      if (this.readonly) {
        return
      }
      this.$emit('dblclick', { x: event.offsetX, y: event.offsetY })
    },
    handleChartMouseDown(event) {
      if (event.ctrlKey || event.toElement.id === 'zoomIn' || event.toElement.id === 'zoomOut') {
        return
      }
      this.selectionInfo = { x: event.offsetX, y: event.offsetY }
    },
    getConnectorPosition(node) {
      if (node === undefined) return
      const pianyi = 10 // 这里可以设置节点四周的小圆点位置偏移
      if (node.type === 'judge') {
        const leftX = node.x - 5
        const leftY = node.y
        const topX = node.x + 75
        const topY = node.y - 55
        const rightX = node.x + 155
        const rightY = node.y
        const bottomX = node.x + 75
        const bottomY = node.y + 55
        const top = { x: topX, y: topY }
        const left = { x: leftX, y: leftY }
        const bottom = {
          x: bottomX,
          y: bottomY
        }
        const right = {
          x: rightX,
          y: rightY
        }
        return { left, right, top, bottom }
      } else {
        const halfWidth = node.width / 2
        const halfHeight = node.height / 2
        const top = { x: node.x + halfWidth, y: node.y + pianyi - 5 }
        const left = { x: node.x - 5, y: node.y + halfHeight + pianyi + 5 }
        let bottom = {
          x: node.x + halfWidth,
          y: node.y + node.height + pianyi + 10
        }
        if (node.type === 'step') {
          bottom = {
            x: node.x + halfWidth,
            y: node.y + node.height + pianyi + 18
          }
        }
        const right = {
          x: node.x + node.width + 5,
          y: node.y + halfHeight + pianyi + 5
        }
        return { left, right, top, bottom }
      }
    },
    // 画布选择矩形
    renderSelection() {
      const that = this
      // render selection rectangle
      if (that.selectionInfo) {
        const edge = getEdgeOfPoints([
          {
            x: that.selectionInfo.x / this.zoom,
            y: that.selectionInfo.y / this.zoom
          },
          {
            x: that.cursorToChartOffset.x / this.zoom,
            y: that.cursorToChartOffset.y / this.zoom
          }
        ])
        that.currentNodes.splice(0, that.currentNodes.length)
        that.currentConnections.splice(0, that.currentConnections.length)
        const svg = d3.select('#svg')
        const rect = svg.select('.selection').classed('active', true)

        rect
          .attr('x', edge.start.x)
          .attr('y', edge.start.y)
          .attr('width', edge.end.x - edge.start.x)
          .attr('height', edge.end.y - edge.start.y)

        that.internalNodes.forEach(item => {
          const points = [{ x: item.x, y: item.y }, { x: item.x, y: item.y + item.height }, { x: item.x + item.width, y: item.y }, { x: item.x + item.width, y: item.y + item.height }]
          if (points.every(point => pointRectangleIntersection(point, edge))) {
            that.currentNodes.push(item)
          }
        })
        that.lines.forEach(line => {
          const points = [{ x: line.sourceX, y: line.sourceY }, { x: line.destinationX, y: line.destinationY }]
          if (points.every(point => pointRectangleIntersection(point, edge)) && that.currentConnections.every(item => item.id !== line.id)) {
            const connection = that.internalConnections.filter(conn => conn.id === line.id)[0]
            that.currentConnections.push(connection)
          }
        })
      } else {
        d3.selectAll('#svg > .selection').classed('active', false)
      }
    },
    renderConnections() {
      const that = this
      return new Promise(function(resolve) {
        that.$nextTick(function() {
          d3.selectAll('#svg > g.connection').remove()
          // render lines
          that.lines = []
          that.internalConnections.forEach(conn => {
            const sourcePosition = that.getNodeConnectorOffset(conn.source.id, conn.source.position)
            const destinationPosition = that.getNodeConnectorOffset(conn.destination.id, conn.destination.position)
            let colors = conn.color
            if (that.currentConnections.filter(item => item === conn).length > 0) {
              colors = '#13ce66'
            }
            const result = that.arrowTo(sourcePosition.x, sourcePosition.y, destinationPosition.x, destinationPosition.y, conn.source.position, conn.destination.position, colors)
            for (const path of result.paths) {
              path.on('mousedown', function() {
                d3.event.stopPropagation()
                if (that.pathClickedOnce) {
                  that.editConnection(conn)
                } else {
                  const timer = setTimeout(function() {
                    that.pathClickedOnce = false
                    clearTimeout(timer)
                  }, 300)
                  that.pathClickedOnce = true
                }
                that.currentNodes.splice(0, that.currentNodes.length)
                that.currentConnections.splice(0, that.currentConnections.length)
                that.currentConnections.push(conn)
              })
            }
            for (const line of result.lines) {
              that.lines.push({
                sourceX: line.sourceX,
                sourceY: line.sourceY,
                destinationX: line.destinationX,
                destinationY: line.destinationY,
                id: conn.id
              })
            }
          })
          resolve()
        })
      })
    },
    renderNodes() {
      const that = this
      return new Promise(function(resolve) {
        d3.selectAll('#svg > g.node').remove()
        // console.log(that.internalNodes)
        // render nodes
        that.internalNodes.forEach(node => {
          that.renderNode(node, that.currentNodes.filter(item => item === node).length > 0)
        })

        resolve()
      })
    },
    getNodeConnectorOffset(nodeId, connectorPosition) {
      const node = this.internalNodes.filter(item => item.id === nodeId)[0]
      return this.getConnectorPosition(node)[connectorPosition]
    },
    append(element) {
      const svg = d3.select('#svg')
      return svg.insert(element, '.selection')
    },
    guideLineTo(x1, y1, x2, y2) {
      const g = this.append('g')
      g.classed('guideline', true)
      lineTo(g, x1, y1, x2, y2, 1, '#a3a3a3', [5, 3])
    },
    arrowTo(x1, y1, x2, y2, startPosition, endPosition, color) {
      const g = this.append('g')
      g.classed('connection', true)
      line2(g, x1, y1, x2, y2, startPosition, endPosition, 2, color || '#2E4AD5', true)
      // a 5px cover to make mouse operation conveniently
      return line2(g, x1, y1, x2, y2, startPosition, endPosition, 5, 'transparent', false)
    },
    renderNode(node, isSelected) {
      const that = this
      const g = that
        .append('g')
        .attr('cursor', 'move')
        .classed('node', true)

      node.render = that.render
      node.render(g, node, isSelected, that)

      const drag = d3
        .drag()
        .on('start', function() {
          return
          // handle mousedown
          const isNotCurrentNode = that.currentNodes.filter(item => item === node).length === 0
          if (isNotCurrentNode) {
            that.currentConnections.splice(0, that.currentConnections.length)
            that.currentNodes.splice(0, that.currentNodes.length)
            that.currentNodes.push(node)
          }
          // if (that.clickedOnce) {
          //   that.currentNodes.splice(0, that.currentNodes.length)
          //   that.editNode(node)
          // } else {
          //   const timer = setTimeout(function() {
          //     that.clickedOnce = false
          //     clearTimeout(timer)
          //   }, 300)
          //   that.clickedOnce = true
          // }
        })
        .on('drag', async function() {
          return
          if (!that.readonly) {
            for (const currentNode of that.currentNodes) {
              if (currentNode.type === 'image') {
                continue
              }
              const x = d3.event.dx // / zoom;
              // if (currentNode.x + x < 0) {
              //   x = -currentNode.x
              // }
              currentNode.x += x
              const y = d3.event.dy // / zoom;
              // if (currentNode.y + y < 0) {
              //   y = -currentNode.y
              // }
              currentNode.y += y
            }
          }

          d3.selectAll('#svg > g.guideline').remove()
          const edge = that.getCurrentNodesEdge()
          const expectX = Math.round(Math.round(edge.start.x) / 10) * 10
          const expectY = Math.round(Math.round(edge.start.y) / 10) * 10
          that.internalNodes.forEach(item => {
            if (item.type !== 'image') {
              if (that.currentNodes.filter(currentNode => currentNode === item).length === 0) {
                if (item.x === expectX) {
                // vertical guideline
                  if (item.y < expectY) {
                    that.guideLineTo(item.x, item.y + item.height, expectX, expectY)
                  } else {
                    that.guideLineTo(expectX, expectY + item.height, item.x, item.y)
                  }
                }
                if (item.y === expectY) {
                // horizontal guideline
                  if (item.x < expectX) {
                    that.guideLineTo(item.x + item.width, item.y, expectX, expectY)
                  } else {
                    that.guideLineTo(expectX + item.width, expectY, item.x, item.y)
                  }
                }
              }
            }
          })
        })
        .on('end', function() {
          return
          d3.selectAll('#svg > g.guideline').remove()
          that.dragEnd = false
          if (node.type !== 'image') {
            that.editNode(node)
          }
          // 这里因为节点与节点之间的线条不直所以注释了
          // for (const currentNode of that.currentNodes) {
          //   currentNode.x = Math.round(Math.round(currentNode.x) / 10) * 10 + 4
          //   currentNode.y = Math.round(Math.round(currentNode.y) / 10) * 10
          // }
        })
      g.call(drag)
      g.on('mousedown', function() {
        // handle ctrl+mousedown
        if (!d3.event.ctrlKey) {
          return
        }

        const isNotCurrentNode = that.currentNodes.filter(item => item === node).length === 0
        if (isNotCurrentNode) {
          that.currentNodes.push(node)
        } else {
          that.currentNodes.splice(that.currentNodes.indexOf(node), 1)
        }
      })
    },
    getCurrentNodesEdge() {
      const points = this.currentNodes.map(node => ({
        x: node.x,
        y: node.y
      }))
      points.push(
        ...this.currentNodes.map(node => ({
          x: node.x + node.width,
          y: node.y + node.height
        }))
      )
      return getEdgeOfPoints(points)
    },
    save() {
      if (this.readonly) {
        return
      }
      // d3.select('#svg').attr('transform', 'translate(500, 500)')
      this.$emit('save', this.internalNodes, this.internalConnections)
    },
    async remove() {
      if (this.readonly) {
        return
      }
      if (this.currentConnections.length > 0) {
        for (const conn of this.currentConnections) {
          this.removeConnection(conn)
        }
        this.currentConnections.splice(0, this.currentConnections.length)
      }
      if (this.currentNodes.length > 0) {
        for (const node of this.currentNodes) {
          if (node.type !== 'image') {
            this.removeNode(node)
          }
        }
        this.currentNodes.splice(0, this.currentNodes.length)
      }
    },
    removeNode(node) {
      this.$emit('delnode', node.type, node.subId_stepId)

      const connections = this.internalConnections.filter(item => item.source.id === node.id || item.destination.id === node.id)
      for (const connection of connections) {
        this.internalConnections.splice(this.internalConnections.indexOf(connection), 1)
      }
      this.internalNodes.splice(this.internalNodes.indexOf(node), 1)
      this.save()
    },
    removeConnection(conn) {
      const index = this.internalConnections.indexOf(conn)
      this.internalConnections.splice(index, 1)
    },
    moveCurrentNode(x, y) {
      if (this.currentNodes.length > 0 && !this.readonly) {
        for (const node of this.currentNodes) {
          if (node.x + x < 0) {
            x = -node.x
          }
          node.x += x
          if (node.y + y < 0) {
            y = -node.y
          }
          node.y += y
        }
      }
    },
    init(node1, thisH) {
      const that = this
      if (node1 !== undefined && thisH !== undefined) {
        thisH.internalNodes.forEach(node => {
          // 当个node编辑时，更新数据触发重新渲染图像界面
          if (node1 !== undefined && node1.id === node.id) {
            node.width = parseFloat(node1.width)
            node.height = parseFloat(node1.height)
            node.x = parseFloat(node1.x)
            node.y = parseFloat(node1.y)
            node.name = node1.name
            node.type = node1.type
            node.imgId = node1.imgId
            node.color = node1.color
            node.describe = node1.describe
            node.subId_stepId = node1.subId_stepId
            node.strokeWidth = parseFloat(node1.strokeWidth)
          }
        })
      } else {
        that.internalNodes.splice(0, that.internalNodes.length)
        that.internalConnections.splice(0, that.internalConnections.length)

        that.nodes.forEach(node => {
          const newNode = Object.assign({}, node)
          newNode.width = newNode.width || 120
          newNode.height = newNode.height || 60
          // 当个node编辑时，更新数据触发重新渲染图像界面
          if (node !== undefined && node.id === newNode.id) {
            newNode.width = parseFloat(node.width)
            newNode.height = parseFloat(node.height)
            newNode.x = parseFloat(node.x)
            newNode.y = parseFloat(node.y)
            newNode.name = node.name
            newNode.type = node.type
            newNode.imgId = node.imgId
            newNode.color = node.color
            newNode.describe = node.describe
            newNode.subId_stepId = node.subId_stepId
            newNode.strokeWidth = parseFloat(node.strokeWidth)
          }
          that.internalNodes.push(newNode)
        })
        that.connections.forEach(connection => {
          that.internalConnections.push(JSON.parse(JSON.stringify(connection)))
        })
      }
    }
  }
}
</script>
<style scoped lang="less">
.stress-btn-3d{
  display:block;
  width:100px;
  height:30px;
  line-height:30px;
  text-align:center;
  color: #fff;
  cursor: pointer;
  background-color: #00479d;
  border-color: #00479d;
  border-radius: 0.25rem;
}
.stress-btn-3d-active {
  background: linear-gradient(to bottom, #096609 0%, #13e913 100%);
}
</style>
