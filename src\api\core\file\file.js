import request from '@/utils/request'

//查询 文件夹(树)
export function dirFileTree(data) {
  return request({
    url: 'aisEsbWeb/core/file/CoreDirFileSel',
    method: 'post',
    data
  })
}
//下载文件
export function fileDownload(data) {
  return request({
    url: 'aisEsbWeb/core/file/CoreFileDownload',
    method: 'post',
    data,
    responseType: "blob",
    timeout:60 * 1000 * 30
  })
}
//删除文件
export function fileDel(data) {
  return request({
    url: 'aisEsbWeb/core/file/CoreFileDel',
    method: 'post',
    data
  })
}

export default { dirFileTree, fileDownload, fileDel }
