<template>
  <div class="app-container">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
      <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
        <el-form-item :label="$t('lang_pack.mainmain.conditionGroupDescription')" prop="step_condition_g_des">  <!-- 条件组描述 -->
          <el-input v-model="form.step_condition_g_des" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">  <!-- 有效标识 -->
          <el-radio-group v-model="form.enable_flag">
            <el-radio label="Y">有效</el-radio>
            <el-radio label="N">失效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确认 -->
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          height="250px"
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
          @row-click="handleRowClick"
        >
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="step_condition_g_id" label="id" />
          <el-table-column :show-overflow-tooltip="true" prop="step_condition_g_des" :label="$t('lang_pack.mainmain.conditionGroupDescription')" />  <!-- 条件组描述 -->
          <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">  <!-- 有效标识 -->
            <template slot-scope="scope">
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">  <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
    <conditionItem ref="conditionItem" :step_condition_g_id="currentGroupId" />
  </div>
</template>

<script>
import crudFlowMainConditionG from '@/api/core/flow/rcsFlowMainConditionG'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import conditionItem from '@/views/core/flow/main/condition-item'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  step_condition_g_id: 0,
  flow_main_id: 0,
  step_mod_id: 0,
  step_condition_g_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'RCSFLOWMAINCONDITIONG',
  components: { conditionItem, crudOperation, udOperation, pagination },
  props: {
    flow_main_id: {
      type: [String, Number],
      default: 0
    },
    step_mod_id: {
      type: [String, Number],
      default: 0
    }
  },
  cruds() {
    return CRUD({
      title: '条件组',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'step_condition_g_id',
      // 排序
      sort: ['step_condition_g_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowMainConditionG },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      conditionItemShow: false,
      currentGroupId: 0,
      permission: {
        add: ['admin', 'rcs_flow_main_condition_g:add'],
        edit: ['admin', 'rcs_flow_main_condition_g:edit'],
        del: ['admin', 'rcs_flow_main_condition_g:del']
      },
      rules: {
        step_condition_g_des: [{ required: true, message: '请输入条件组描述', trigger: 'blur' }]
      }
    }
  },
  watch: {
    flow_main_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.flow_main_id = this.flow_main_id
      }
    },
    step_mod_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.step_mod_id = this.step_mod_id
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    handleRowClick(row, column, event) {
      if (column.property !== 'button') {
        this.currentGroupId = row.step_condition_g_id
      }
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.flow_main_id = this.flow_main_id
      crud.form.step_mod_id = this.step_mod_id
      return true
    }
  }
}
</script>
