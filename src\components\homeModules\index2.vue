<template>
    <div class="container1">
        <!-- <div class="table-head">
            <div v-if="deviceFlag" :style="{'width':deviceFlag ? '80%' :''}">
                <span style="width: 35%">设备名称</span>
                <span style="width: 65%">报警描述</span>
            </div>
            <div v-if="!deviceFlag" :style="{'width':!deviceFlag ? '60%' :''}">
                <span style="width: 100%">日志</span>
            </div>
            <span :style="{'width': deviceFlag ? '20%' : '40%'}">发生时间</span>
        </div>
        <vue-seamless-scroll :data="deviceData" :class-option="optionSingleHeight1" class="seamless-warp">
            <div class="home-message" v-for="item in deviceData" :key="item.id">
                <div class="home-message-item" :style="{'width':deviceFlag ? '80%' :''}" v-if="deviceFlag">
                    <el-tooltip class="item" effect="dark" :content="item.client_des" placement="top">
                        <span style="width: 35%;text-align: center;">{{ item.client_des }}</span>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" :content="item.alarm_des" placement="top">
                        <span style="width: 65%;text-align: center;">{{ item.alarm_des }}</span>
                    </el-tooltip>
                </div>
                <div class="home-message-item" :style="{'width':!deviceFlag ? '60%' :''}" v-if="!deviceFlag">
                    <el-tooltip class="item" effect="dark" :content="item.log_msg" placement="top">
                        <span style="width: 100%;text-align: center;">{{ item.log_msg }}</span>
                    </el-tooltip>
                </div>
                <div class="wrapTime" :style="{'width': deviceFlag ? '20%' : '40%'}">
                    <span>{{ item.item_date }}</span>
                </div>
            </div>
        </vue-seamless-scroll> -->
        <el-table border  ref="table"  size="small" :data="deviceData" height="350" v-loadmore="handleScroll"  highlight-current-row  v-if="deviceFlag">
            <!-- 设备名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="client_des" label="设备名称"  />
            <!-- 报警描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="alarm_des" label="报警描述" />
            <!-- 发生时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="log_create_date" label="发生时间"  />
        </el-table>
        <el-table border   ref="table"  size="small" :data="deviceData" height="350"  highlight-current-row v-if="!deviceFlag">
            <el-table-column  :show-overflow-tooltip="true" prop="flow_main_des" label="产线"  >
            <!-- 产线 -->
                <template slot-scope="scope">
                    {{ getProdLineDes(scope.row.prod_line_id) }}
                </template>
            </el-table-column>
             <el-table-column  :show-overflow-tooltip="true" prop="station_id" :label="$t('lang_pack.mainmain.station')">
              <!-- 工位 -->
              <template slot-scope="scope">
                {{ getStationDes(scope.row.station_id) }}
              </template>
            </el-table-column>
              <!-- 流程描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="flow_main_des" label="流程描述"  />
             <!-- 步骤描述 -->
             <el-table-column  :show-overflow-tooltip="true" prop="step_mod_des" label="步骤描述"  />
            <!-- 日志 -->
            <el-table-column  :show-overflow-tooltip="true" prop="log_msg" label="日志"  />
            <!-- 发生时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="log_create_date" label="发生时间"  width="140"/>
            <el-table-column :label="$t('lang_pack.commonPage.operate')"  width="80">
                <!-- 操作 -->
                <template slot-scope="scope">
                    <el-button size="small" type="text" @click="openFlowChart(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="editFlowChart" direction="rtl" size="100%">
        <flowChart
          v-if="mainchartShow"
          ref="flowChart"
          :flow_main_id="currentRow.flow_main_id"
          :flow_mod_main_id="currentRow.flow_mod_main_id"
          :flow_mod_main_des="currentRow.flow_main_des"
          :client_id_list="currentRow.client_id_list"
          @closeDrawer="
            editFlowChart = false
            mainchartShow = false
          "
        />
      </el-drawer>
    </div>
</template>
<script>
// import vueSeamlessScroll from 'vue-seamless-scroll' //轮播无线滚动
import flowChart from '@/views/core/flow/main/flow-chart'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selProdLine } from "@/api/core/factory/sysProdLine";
import Cookies from 'js-cookie'
export default {
    name: 'homeScroll',
    components: {
        // vueSeamlessScroll
        flowChart
    },
    computed: {
        optionSingleHeight1() {
            return {
                // limitMoveNum: this.$store.getters.patrolResults.length,
                // limitMoveNum: 5,
                // singleHeight: this.midHeight / 8,
                // step: 0.1,
                // openTouch: false,
                // hoverStop: true,
                // autoPlay: true
            }
        }
    },
    data() {
        return {
            deviceData:[],//表格数据
            currentPage: 1,
            pageSize: 10,
            currentRow: {},
            mainchartShow:true,
            mainchartShow: false,
            editFlowChart: false,
            stationData:[],
            prodLineData:[],
        }
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        deviceFlag:{
            type:Boolean,
            default: () => true,
        }
    },
    // 监听表格滚动
    directives: {
        loadmore: {
            bind(el, binding) {
                const selectWrap = el.querySelector(".el-table__body-wrapper");
                selectWrap.addEventListener("scroll", function () {
                    const scrollDistance =
                        this.scrollHeight - this.scrollTop - this.clientHeight;
                    if (scrollDistance <= 0.5) {
                      binding.value()//执行在使用时绑定的函数，在这里即loadMorePerson方法
                    }
                });
            },
        },
    },
    created(){
        this.$nextTick(()=>{
            this.$refs.table.doLayout()
        })
        const query = {
            user_name: Cookies.get('userName'),
            enable_flag: 'Y'
        }
        selStation(query)
        .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
                if (defaultQuery.data.length > 0) {
                    this.stationData = defaultQuery.data
                }
            }
        })
        .catch(() => {
            this.$message({
            message: '查询异常',
            type: 'error'
            })
        })
        // 产线LOV
        selProdLine({
            user_name: Cookies.get("userName"),
            enable_flag: "Y",
        })
        .then((res) => {
            const defaultQuery = JSON.parse(JSON.stringify(res));
            if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
                this.prodLineData = defaultQuery.data;
            }
            }
        })
        .catch(() => {
            this.$message({
            message: "初始化模式数据异常",
            type: "error",
            });
        });
    },
    watch:{
        list:{
            immediate: true,
            deep: true,
            handler() {
                this.fetchData()
            }
        }
    },
    methods:{
        getProdLineDes(prod_line_id) {
            var item = this.prodLineData.find(item => item.prod_line_id === prod_line_id)
            if (item !== undefined) {
                return item.prod_line_code + ' ' + item.prod_line_des
            }
            return prod_line_id
        },
        getStationDes(station_id) {
            var item = this.stationData.find(item => item.station_id === station_id)
            if (item !== undefined) {
                return item.station_code + ' ' + item.station_des
            }
            return station_id
        },
        openFlowChart(row) {
            this.currentRow = row
            this.mainchartShow = true
            this.editFlowChart = true
        },
        fetchData(){
            setTimeout(()=>{
                for (let i = 0; i < 10; i++) {
                    if (this.currentPage * this.pageSize + i < this.list.length) {
                        this.deviceData.push(this.list[this.currentPage * this.pageSize + i]);
                    }else if(i < this.list.length && this.list.length < 10){
                        this.deviceData.push(this.list[i]);
                    }
                }
                    this.currentPage++;
            },1000)
        },
        handleScroll(){
            // const tableEl = event.target;
            if(this.currentPage * this.pageSize >= this.list.length){
                this.$message.info('数据到底啦！！！')
                return
            }
            this.fetchData()
        }
    }
}
</script>
<style scoped lang="less">
 ::v-deep .el-table__body td{
    border: 0 !important;
}
.container1{
    box-sizing: border-box;
    position: relative;
    .seamless-warp{
        overflow: hidden;
    }
    .table-head{
        width: 100%;
        height: 35px;
        background-color: #fff;
        // position: absolute;
        // z-index: 999;
        display:flex;
        justify-content: space-between;
        div{
            display: flex;
        }
        span {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            text-align: center;
            font-size: 20px;
            font-weight: 550;
        }
    }
    .home-message {
        width: 100%;
        height: 35px;
        line-height: 35px;
        color: #333333;
        border-radius: 5px;
        margin-bottom: 5px;
        font-size: 13px;
        display: flex;
    }
    .home-message-item {
        display: flex;
        align-items: center;
        font-size: 16px;
        span{
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
    .home-message-item-icon {
        color: #79a0f1;
        font-size: 20px;
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        margin-left: 5px;
    }
    .wrapTime {
        text-align: center;
        span{
            font-size: 16px;
            text-align: center;
            white-space: nowrap;
        }
    }
}

</style>
<style >
.el-tooltip__popper{
    max-width: 500px !important;
}
</style>