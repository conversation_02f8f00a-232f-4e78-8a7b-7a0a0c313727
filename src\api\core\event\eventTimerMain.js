import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventTimerMainSel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventTimerMainIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventTimerMainUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventTimerMainDel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del }

