<template>
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
        <el-form-item label="名称" prop="split_name" display:none>
          <!-- 名称 -->
          <el-input v-model="form.split_name" />
        </el-form-item>
        <el-form-item :label="$t('view.form.isReversed')">
          <!-- 是否反转 -->
          <fastCode fastcode_group_code="WHETHER_FLAG" :fastcode_code.sync="form.reversed_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item>
        <el-form-item label="截取起始" prop="split_index">
          <!-- 截取起始 -->
          <el-input v-model="form.split_index" />
        </el-form-item>
        <el-form-item label="截取长度" prop="split_length">
          <!-- 截取长度 -->
          <el-input v-model="form.split_length" />
        </el-form-item>
        <el-form-item label="比对函数" prop="split_compare_func">
          <!-- 比对函数 -->
          <el-select v-model="form.split_compare_func" clearable filterable>
            <el-option v-for="item in dict.SPLIT_COMPARE_FUNC" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="接口来源" prop="mes_from">
          <!-- 接口来源 -->
          <el-select v-model="form.mes_from" clearable filterable>
            <el-option v-for="item in dict.MES_FROM" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="字段名称" prop="mes_field">
          <!-- 字段名称 -->
          <el-select v-model="form.mes_field" clearable filterable>
            <el-option v-for="item in dict.MES_FIELD" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="截取起始" prop="mes_split_index">
          <!-- 截取起始 -->
          <el-input v-model="form.mes_split_index" />
        </el-form-item>
        <el-form-item label="截取长度" prop="mes_split_length">
          <!-- 截取长度 -->
          <el-input v-model="form.mes_split_length" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <!-- 有效标识 -->
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确认 -->
      </div>
    </el-drawer>
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :header-cell-style="headerCellStyle"
          :height="height"
          :highlight-current-row="true"
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
            <!-- 有效标识 -->
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
            </template>
          </el-table-column>
          <el-table-column label="序号" align="center" prop="index" />
          <!-- 名称 -->
          <el-table-column label="名称" align="center" prop="split_name" />
          <el-table-column :label="$t('view.form.reversedFlag')" align="center" prop="reversed_flag" width="100">
            <!-- 反转标识 -->
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ dict.label.WHETHER_FLAG[scope.row.reversed_flag] || '否' }}
            </template>
          </el-table-column>
          <el-table-column label="截取起始" width="90" align="center" prop="split_index" />
          <el-table-column label="截取长度" width="90" align="center" prop="split_length" />
          <el-table-column label="比对函数" align="center">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ dict.label.SPLIT_COMPARE_FUNC[scope.row.split_compare_func] }}
            </template>
          </el-table-column>
          <el-table-column label="接口来源" align="center">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ dict.label.MES_FROM[scope.row.mes_from] }}
            </template>
          </el-table-column>
          <el-table-column label="字段名称" align="center">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ dict.label.MES_FIELD[scope.row.mes_field] }}
            </template>
          </el-table-column>
          <el-table-column label="截取起始" width="90" align="center" prop="mes_split_index" />
          <el-table-column label="截取长度" width="90" align="center" prop="mes_split_length" />
          <el-table-column label="操作" align="center" fixed="right">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <!-- <pagination /> -->
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudMenuItem from '@/api/pack/menuItem'
import udOperation from '@crud/UD.operation'
const defaultForm = {
  index: '',
  mes_field: '',
  mes_from: '',
  mes_split_index: '',
  mes_split_length: '',
  shipaddress_detail_id: '',
  shipaddress_id: 0,
  split_compare_func: '',
  split_index: '',
  split_length: '',
  split_name: '',
  enable_flag: 'Y',
  reversed_flag: 'N'
}
export default {
  name: 'MENUITEM',
  cruds() {
    return CRUD({
      title: '配方设定',
      // 登录用户
      userName: Cookies.get('userName'),
      // 菜单组ID
      query: { shipaddress_detail_id: '' },
      // 唯一字段
      idField: 'shipaddress_detail_id',
      // 排序
      sort: ['index asc'],
      // CRUD Method
      crudMethod: { ...crudMenuItem },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  components: { udOperation },
  props: {
    shipaddress_id: {
      type: [String, Number],
      default: -1
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'sys_menu:add'],
        edit: ['admin', 'sys_menu:edit'],
        del: ['admin', 'sys_menu:del'],
        down: ['admin', 'sys_menu:down']
      },
      rules: {
        // 提交验证规则
        split_name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        split_index: [{ required: true, message: '请输入截取起始', trigger: 'blur' }],
        split_length: [{ required: true, message: '请输入截取长度', trigger: 'blur' }],
        split_compare_func: [{ required: true, message: '请输入比对函数', trigger: 'blur' }],
        mes_from: [{ required: true, message: '请输入接口来源', trigger: 'blur' }],
        mes_field: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
        mes_split_index: [{ required: true, message: '请输入截取起始', trigger: 'blur' }],
        mes_split_length: [{ required: true, message: '请输入截取长度', trigger: 'blur' }]
      }
    }
  },
  watch: {
    shipaddress_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.shipaddress_id = this.shipaddress_id
        this.crud.toQuery()
      }
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['SPLIT_COMPARE_FUNC', 'MES_FROM', 'MES_FIELD', 'ENABLE_FLAG', 'WHETHER_FLAG'],
  methods: {
    [CRUD.HOOK.beforeSubmit](crud) {
      // crud.form.index = crud.data.length + 1
      crud.form.shipaddress_id = this.shipaddress_id
      return true
    },
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      return (columnIndex >= 3 && columnIndex <= 6) ? 'background:#FFC000 !important' : (columnIndex >= 7 && columnIndex <= 10) ? 'background:#00B0F0 !important' : ''
    }
  }
}
</script>
