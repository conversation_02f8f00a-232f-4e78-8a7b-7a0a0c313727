<template>
  <el-card shadow="always" style="margin-top: 10px">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="倒库详情" width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-form
            ref="ruleForm"
            class="el-form-wrap"
            :model="form"
            :rules="rules"
            size="small"
            label-width="100px"
            :inline="true"
          >
            <el-col :span="8">
              <el-form-item label="当前库位" prop="from_stock_code">
                <el-select v-model="form.from_stock_code" :clearable="true" filterable @change="houseSelect">
                  <el-option v-for="item in currentData" :key="item.stock_code" :label="item.stock_code" :value="item.stock_code">
                    <span style="float: left">{{ item.stock_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="钢板型号" prop="model_type">
                <el-input v-model="form.model_type" disabled clearable size="small" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="库存量" prop="from_stock_count">
                <el-input v-model="form.from_stock_count" disabled clearable size="small" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="目标库位" prop="to_stock_code">
                <el-select v-model="form.to_stock_code" clearable filterable @change="toHouseSelect">
                  <el-option v-for="item in targetData" :key="item.stock_code" :label="item.stock_code" :value="item.stock_code">
                    <span style="float: left">{{ item.stock_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="剩余库存" prop="residue">
                <el-input v-model="form.residue" disabled clearable size="small" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="倒库数量" prop="inverted_count">
                <el-input v-model.number="form.inverted_count" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
                        <el-form-item label="天车" prop="car_code">
                            <el-select v-model="form.car_code" clearable filterable >
                                <el-option v-for="item in carData" :key="item.stock_code" :label="item.car_des" :value="item.car_code" >
                                    <span style="float: left">{{ item.car_des }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.car_code }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
            <el-divider />
            <div style="text-align: center;width: 100%;">
              <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="dialogLoading" @click="handleOk">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-form>
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import Cookies from 'js-cookie'
export default {
  name: 'REVSERMODAL',
  data() {
    return {
      modelList: [],
      form: {
        from_stock_code: '',
        model_type: '',
        from_stock_count: '',
        to_stock_code: '',
        residue: '',
        inverted_count: '',
        from_stock_id: '',
        to_stock_id: '',
        to_stock_count: ''
        // car_code:''
      },
      rules: {
        from_stock_code: [{ required: true, message: '请选择当前库位', trigger: 'blur' }],
        model_type: [{ required: true, message: '请选择钢板型号', trigger: 'blur' }],
        from_stock_count: [{ required: true, message: '请选择库存量', trigger: 'blur' }],
        to_stock_code: [{ required: true, message: '请选择目标库位', trigger: 'blur' }],
        residue: [{ required: true, message: '请选择剩余库存', trigger: 'blur' }],
        inverted_count: [{ required: true, message: '请选择倒库数量', trigger: 'blur' }]
        // car_code:[{ required: true, message: '请选择天车', trigger: 'blur' }],
      },
      dialogLoading: false,
      currentData: [],
      targetData: [],
      carData: [],
      dialogVisible: false
    }
  },
  created() {
    // 获取当前工位信息
    crudLoadAreaOper.stockSelect({}).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.count > 0) {
          this.currentData = defaultQuery.data
        }
      } else {
        this.this.currentData = []
        this.$message({ message: defaultQuery.msg, type: 'error' })
      }
    })
      .catch(() => {
        this.this.currentData = []
        this.$message({ message: '查询异常', type: 'error' })
      })
    // 获取天车信息
    crudLoadAreaOper.carSelect({}).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.count > 0) {
          this.carData = defaultQuery.data
        }
      } else {
        this.carData = []
        this.$message({ message: defaultQuery.msg, type: 'error' })
      }
    })
      .catch(() => {
        this.carData = []
        this.$message({ message: '查询异常', type: 'error' })
      })
  },
  methods: {
    BlurText(e) {
      const boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning('数量不能为空或正整数')
        e.target.value = ''
      }
    },
    houseSelect(e) {
      let obj = {}
      obj = this.currentData.filter(item => { return item.stock_code == e })[0] || {}
      if (Object.keys(obj).length > 0) {
        this.form.model_type = obj.model_type
        this.form.from_stock_count = obj.stock_count
        this.form.from_stock_id = obj.stock_id
      }
      this.form.to_stock_code = ''
      this.form.residue = ''
      this.form.inverted_count = ''
      this.getReposData(obj)
    },
    getReposData(obj) {
      // 获取目标库位信息
      const query = {
        from_stock_code: obj.stock_code,
        model_type: obj.model_type
      }
      crudLoadAreaOper.repository(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.count > 0) {
            this.targetData = defaultQuery.data
          }
        } else {
          this.targetData = []
          this.$message({ message: defaultQuery.msg, type: 'error' })
        }
      })
        .catch(() => {
          this.targetData = []
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    toHouseSelect(e) {
      let obj = {}
      obj = this.targetData.filter(item => { return item.stock_code == e })[0] || {}
      console.log(obj)
      if (Object.keys(obj).length > 0) {
        this.form.residue = obj.residue
        this.form.to_stock_id = obj.stock_id
        this.form.to_stock_count = obj.stock_count
      }
      console.log(this.form.to_stock_count)
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    },
    handleOk() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.form.inverted_count <= 0 || this.form.inverted_count > this.form.from_stock_count || this.form.inverted_count > this.form.residue) {
            this.$message.error('倒库数量必须大于0且不能超过库存量且不能超过剩余库存')
            this.dialogLoading = false
            return false
          }
          crudLoadAreaOper.storeHouse(this.form).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.handleClose()
              this.$message({ message: defaultQuery.msg || '倒库成功', type: 'success' })
            } else {
              this.$message({ message: defaultQuery.msg, type: 'error' })
            }
          })
            .catch(() => {
              this.$message({ message: '查询异常', type: 'error' })
            })
            .finally(() => {
              this.dialogLoading = false
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
.el-form-wrap .el-form-item{
    width: 100% !important;
}
</style>
