<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <!-- <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :default-time="['00:00:00', '23:59:59']"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div> -->
            <div class="formChild col-md-4 col-12">
              <el-form-item label="生产线：">
                <el-select
                  v-model="query.prod_line_code"
                  filterable
                  clearable
                  @change="prodLineChange"
                >
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_id"
                    :label="item.prod_line_code + ' ' + item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="订单号：">
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="合格标志：">
                <el-select v-model="query.quality_sign" clearable>
                  <el-option
                    v-for="item in dict.QUALIFIED_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="总成号：">
                <el-input v-model="query.serial_num" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="启用标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in ['Y', 'N']"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button
                  class="filter-item"
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  @click="toQuery"
                >{{ $t("lang_pack.commonPage.search") }}</el-button>
                <!-- 搜索 -->
                <el-button
                  v-if="crud.optShow.reset"
                  class="filter-item"
                  size="small"
                  icon="el-icon-refresh-left"
                  @click="resetQuery()"
                >{{ $t("lang_pack.commonPage.reset") }}</el-button>
                <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button
            v-loading="downloadLoading"
            :disabled="downloadLoading"
            type="primary"
            icon="el-icon-upload2"
            @click="exportExecl"
          >{{ $t('lang_pack.vie.export') }}</el-button>
          <el-date-picker
            v-model="exportDatePeriod"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('view.form.timePeriodStart')"
            :end-placeholder="$t('view.form.timePeriodEnd')"
            value-format="yyyy-MM-dd"
          />
        </template>
        <!-- <template slot="group_left"> -->
        <!-- <el-button
            v-permission="permission.down"
            :loading="crud.downloadLoading"
            :disabled="!crud.data.length"
            size="small"
            icon="el-icon-bottom"
            @click="doExport1"
          /> -->
        <!-- <el-button
            v-permission="permission.down"
            :loading="crud.downloadLoading"
            :disabled="!crud.data.length"
            size="small"
            icon="el-icon-download"
            @click="doExport"
          /> -->
        <!-- </template> -->
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_line_code"
              width="100"
              label="生产线"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              min-width="220"
              label="总成号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="quality_sign"
              width="100"
              label="合格标志"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.quality_sign === 'OK' ? 'success' : 'danger'"
                  effect="dark"
                  style="cursor: pointer"
                  size="medium"
                >{{ scope.row.quality_sign === "OK" ? "OK" : "NG" }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="make_order"
              min-width="150"
              label="订单号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="records"
              width="100"
              label="过站记录"
            >
              <template slot-scope="scope">
                <el-tag
                  style="cursor:pointer"
                  :disabled="!scope.row.records && scope.row.records.length === 0"
                  @click="viewData(scope.row.records, '过站记录')"
                >查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="materials"
              width="100"
              label="物料信息"
            >
              <template slot-scope="scope">
                <el-tag
                  style="cursor:pointer"
                  :disabled="!scope.row.materials && scope.row.materials.length === 0"
                  @click="viewData(scope.row.materials, '物料信息')"
                >查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="qualitys"
              width="100"
              label="质量数据"
            >
              <template slot-scope="scope">
                <el-tag
                  style="cursor:pointer"
                  :disabled="!scope.row.qualities && scope.row.qualities.length === 0"
                  @click="viewData(scope.row.qualities, '质量数据')"
                >查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="arrive_date"
              width="150"
              label="到达时间"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="leave_date"
              width="150"
              label="离开时间"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cost_time"
              width="100"
              label="消耗时间(ms)"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              width="100"
              label="有效标识"
            >
              <template slot-scope="scope">
                {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
      <!-- <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px; text-align: right">
            <el-button-group>
              <el-button type="primary">总数量：{{ page.total }}</el-button>
              <el-button type="primary">当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row> -->
    </el-card>
    <el-dialog :title="rDialog.title" :visible.sync="rDialog.visible" width="80%" top="10px" :close-on-click-modal="false">
      <el-table
        :data="rDialog.data"
        style="width: 100%;margin-top:5px;"
        height="300"
      >
        <el-table-column prop="serial_num" min-width="150" label="工件编号" />
        <template v-if="rDialog.title === '过站记录'">
          <el-table-column prop="station_code" width="150" label="站点编码" />
          <el-table-column prop="station_des" min-width="200" label="站点描述" />
          <el-table-column prop="make_order" width="150" label="订单号" />
          <el-table-column prop="arrive_date" width="150" label="到达时间" />
          <el-table-column prop="leave_date" width="150" label="离开时间" />
          <el-table-column prop="cost_time" width="150" label="消耗时间(ms)" />
          <el-table-column prop="container_num" width="150" label="载具条码" />
          <el-table-column prop="small_model_type" width="150" label="机型" />
          <el-table-column
            :show-overflow-tooltip="true"
            prop="quality_sign"
            width="100"
            label="合格标志"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.quality_sign === 'OK' ? 'success' : 'danger'"
                effect="dark"
                style="cursor: pointer"
                size="medium"
              >{{ scope.row.quality_sign === "OK" ? "OK" : "NG" }}</el-tag>
            </template>
          </el-table-column>
        </template>
        <template v-else-if="rDialog.title === '物料信息'">
          <el-table-column prop="material_code" min-width="150" label="物料编码" />
          <el-table-column prop="material_des" min-width="150" label="物料描述" />
          <el-table-column prop="use_count" width="100" label="使用数量" />
          <el-table-column :show-overflow-tooltip="true" prop="exact_barcode" min-width="150" label="精准二维码">
            <template slot-scope="scope">
              {{ scope.row.exact_barcode && scope.row.exact_barcode !== '' ? scope.row.exact_barcode : scope.row.material_batch }}
            </template>
          </el-table-column>
          <el-table-column prop="trace_d_time" width="150" label="追溯时间" />
        </template>
        <template v-else-if="rDialog.title === '质量数据'">
          <el-table-column prop="quality_for" min-width="80" label="测量对象" />
          <el-table-column prop="tag_des" min-width="100" label="采集项目名称" />
          <el-table-column prop="tag_value" min-width="80" label="采集值" />
          <el-table-column prop="quality_d_sign" width="120" label="子合格标志(设备)" />
          <el-table-column prop="mes_quality_d_sign" width="120" label="子合格标志(MES)" />
          <el-table-column prop="trace_d_time" width="150" label="追溯时间" />
        </template>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api/mes/core/workpiece'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
// import { mesExportSelOne } from '@/api/mes/core/meExport'
import { downloadFile } from '@/utils/index'
// import { fileDownload } from '@/api/core/file/file'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'MES_ME_WORKPIECE',
  components: { crudOperation, pagination },
  cruds() {
    return CRUD({
      title: '工件资料',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        enable_flag: 'Y'
      }
    })
  },
  // 数据字典
  dicts: ['QUALIFIED_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 390,
      permission: {
        add: ['admin', 'mes_me_station_flow:add'],
        edit: ['admin', 'mes_me_station_flow:edit'],
        del: ['admin', 'mes_me_station_flow:del'],
        down: ['admin', 'mes_me_station_flow:down']
      },
      stationData: [],
      prodLineData: [],
      smallModelTypeData: [],
      nowPageIndex: 1, // 当前页数
      pageList: [],
      exportId: '',
      timer: '',
      rDialog: {
        title: '过站记录',
        visible: false,
        data: []
      },
      downloadLoading: false,
      exportDatePeriod: []
    }
  },
  computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime =
        start.getFullYear() +
        '-' +
        (start.getMonth() + 1 >= 10
          ? start.getMonth() + 1
          : '0' + (start.getMonth() + 1)) +
        '-' +
        (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) +
        ' 00:00:00'
      const defalutEndTime =
        end.getFullYear() +
        '-' +
        (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : '0' + (end.getMonth() + 1)) +
        '-' +
        (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) +
        ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // this.crud.query.item_date = this.timeDefault

    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    // doExport() {
    //   if (!this.query.item_date) {
    //     this.$message({
    //       message: '一次最多只能导出365天数据，请重选时间后再导出！',
    //       type: 'info'
    //     })
    //     return
    //   }
    //   let dateStr1 = this.query.item_date[0]
    //   dateStr1 = dateStr1.replace(/-/g, '/')
    //   let dateStr2 = this.query.item_date[1]
    //   dateStr2 = dateStr2.replace(/-/g, '/')
    //   var date1 = new Date(dateStr1)
    //   var date2 = new Date(dateStr2)
    //   var Difference_In_Time = date2.getTime() - date1.getTime()
    //   var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
    //   if (Difference_In_Days > 365) {
    //     this.$message({
    //       message: '一次最多只能导出365天数据，请重选时间后再导出！',
    //       type: 'info'
    //     })
    //     return
    //   }

    //   this.crud.downloadLoading = true
    //   crudStationFlow
    //     .exportEventInsert(this.query)
    //     .then((res) => {
    //       const defaultQuery = JSON.parse(JSON.stringify(res))
    //       if (defaultQuery.code === 0) {
    //         this.exportId = defaultQuery.result
    //         this.timer = setInterval(this.getFileStatus, 2000)
    //       } else {
    //         this.$message({ message: defaultQuery.msg, type: 'error' })
    //         this.crud.downloadLoading = false
    //       }
    //     })
    //     .catch(() => {
    //       this.$message({ message: '导出数据异常', type: 'error' })
    //       this.crud.downloadLoading = false
    //     })
    // },
    // doExport1() {
    //   if (!this.query.item_date) {
    //     this.$message({
    //       message: '一次最多只能导出365天数据，请重选时间后再导出！',
    //       type: 'info'
    //     })
    //     return
    //   }
    //   let dateStr1 = this.query.item_date[0]
    //   dateStr1 = dateStr1.replace(/-/g, '/')
    //   let dateStr2 = this.query.item_date[1]
    //   dateStr2 = dateStr2.replace(/-/g, '/')
    //   var date1 = new Date(dateStr1)
    //   var date2 = new Date(dateStr2)
    //   var Difference_In_Time = date2.getTime() - date1.getTime()
    //   var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
    //   if (Difference_In_Days > 365) {
    //     this.$message({
    //       message: '一次最多只能导出365天数据，请重选时间后再导出！',
    //       type: 'info'
    //     })
    //     return
    //   }

    //   this.crud.downloadLoading = true
    //   crudStationFlow
    //     .exportEventInsertSH(this.query)
    //     .then((res) => {
    //       const defaultQuery = JSON.parse(JSON.stringify(res))
    //       if (defaultQuery.code === 0) {
    //         this.exportId = defaultQuery.result
    //         this.timer = setInterval(this.getFileStatus, 2000)
    //       } else {
    //         this.$message({ message: defaultQuery.msg, type: 'error' })
    //         this.crud.downloadLoading = false
    //       }
    //     })
    //     .catch(() => {
    //       this.$message({ message: '导出数据异常', type: 'error' })
    //       this.crud.downloadLoading = false
    //     })
    // },
    // getFileStatus() {
    //   // 获取文件下载状态
    //   mesExportSelOne({ export_id: this.exportId }).then((res) => {
    //     const defaultQuery = JSON.parse(JSON.stringify(res))
    //     if (defaultQuery.code === 0 && defaultQuery.data) {
    //       if (defaultQuery.data.finish_status === 'OK') {
    //         clearInterval(this.timer)
    //         // 文件已生成，则下载该文件
    //         fileDownload({ file_path: defaultQuery.data.down_url })
    //           .then((result) => {
    //             downloadFile(result, defaultQuery.data.export_name, 'csv')
    //             this.crud.downloadLoading = false
    //           })
    //           .catch(() => {
    //             this.crud.downloadLoading = false
    //           })
    //       } else if (defaultQuery.data.finish_status === 'NG') {
    //         this.$message({ message: defaultQuery.data.error_msg, type: 'error' })
    //         clearInterval(this.timer)
    //         this.crud.downloadLoading = false
    //       }
    //     }
    //   })
    // },
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    // resetQuery() {
    //   this.nowPageIndex = 1
    //   this.pageList = []
    //   this.query.page_dirct = ''
    //   this.query.page_id = ''
    //   this.crud.resetQuery()
    // },
    // pageQuery(pageDirct) {
    //   if (pageDirct === 'pre') {
    //     if (this.nowPageIndex <= 1) {
    //       this.nowPageIndex = 1
    //       this.$message({
    //         message: '已置顶',
    //         type: 'info'
    //       })
    //       return
    //     }
    //     this.nowPageIndex = this.nowPageIndex - 1
    //     const preId = this.pageList[this.nowPageIndex]
    //     this.query.page_dirct = pageDirct
    //     this.query.page_id = preId
    //     this.crud.toQuery()
    //   } else {
    //     console.log(this.page)
    //     const total_page =
    //       this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
    //     if (total_page === 1 || this.nowPageIndex >= total_page) {
    //       this.$message({
    //         message: '已置底',
    //         type: 'info'
    //       })
    //       return
    //     }
    //     const preId = this.crud.data[0].id
    //     this.pageList[this.nowPageIndex] = preId
    //     this.nowPageIndex = this.nowPageIndex + 1
    //     this.query.page_dirct = pageDirct
    //     this.query.page_id = this.crud.data[this.crud.data.length - 1].id
    //     this.crud.toQuery()
    //   }
    // },
    prodLineChange(val) {
      const lineItem = this.prodLineData.find((item) => item.prod_line_code === val)
      if (lineItem) {
        this.getStationList(lineItem.prod_line_id)
        this.getSmallModel(lineItem.prod_line_id)
      }
    },
    getStationList(prod_line_id) {
      this.stationData = []
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getSmallModel(prod_line_id) {
      this.smallModelTypeData = []
      selSmallModel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.smallModelTypeData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    viewData(data, title) {
      this.rDialog.title = title
      this.rDialog.data = data
      this.rDialog.visible = true
    },
    exportExecl() {
      if (!this.exportDatePeriod || this.exportDatePeriod.length === 0) {
        this.$message.error('请选择导出时间段（下载时间）')
        return
      }
      this.downloadLoading = true
      const name = '工件追溯记录'
      const bodyData = {
        name: name,
        columns: {},
        date_period: this.exportDatePeriod
      }
      let columnIndex = 1
      const columns = Object.assign({}, this.$t('view.field.workpiece'))
      for (const key in columns) {
        columns[key] = columnIndex.toString().padStart(3, '0') + '-' + columns[key]
        columnIndex++
      }
      bodyData.columns = columns
      // 如果入参的条件里面没有值，那么就把入参条件给删掉，传空对象
      Object.keys(bodyData).forEach((key) => {
        if (
          bodyData[key] === '' ||
          (Array.isArray(bodyData[key]) && bodyData[key].length === 0)
        ) {
          delete bodyData[key]
        }
      })
      api.exportExcel(bodyData)
        .then((res) => {
          if (res.type && res.type.indexOf('application/json') > -1) {
            new Blob([res]).text().then((text) => {
              const resJson = JSON.parse(text)
              this.$message.error(resJson.msg)
            })
            this.downloadLoading = false
            return
          }
          downloadFile(res, name, 'xlsx')
          this.downloadLoading = false
        })
        .catch(() => {
          this.downloadLoading = false
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
