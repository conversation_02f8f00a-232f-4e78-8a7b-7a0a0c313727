<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="24">
                <MainChart
                    ref="chart"
                    v-loading="loading"
                    :nodes="nodes"
                    :connections="connections"
                    :width="'1856'"
                    :height="'266'"
                    :readonly="false"
                    element-loading-text="拼命绘制流程图中"
                    @editnode="handleEditNode"
                />
            </el-col>
        </el-row>
    </div>
</template>
<script>
/* eslint-disable no-unused-vars */
import MainChart from '@/components/MainAll/index'
import axios from 'axios'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
export default {
  components: {
    MainChart
  },
  props:{
    task_num:{
      type:[String,Number],
      default:''
    }
  },
  data() {
    return {
      diameter: 15,
      nodes: [],
      connections: [],
      thisH: null,
      loading: false,
      nodeForm:{},
    }
  },
  watch:{
    task_num:{
      immediate: true,
      deep: true,
      handler() {
        this.nodes.map(item=>{
          this.getHighlight(item)
        })
      }
    }
  },
  mounted() {
  },
  created() {
      axios.get('aisEsbWeb/dcs/hmi/section-point?sectionCode=CUT').then(res=>{
          const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.data.code === 0) {
                if(defaultQuery.data.data.length >0){
                    this.nodes = defaultQuery.data.data[0].point
                    this.nodes.map(item=>{
                      this.getHighlight(item)
                      item.x = item.x_coordinate
                      item.y = item.y_coordinate
                    })
                    this.nodes.unshift({
                        x: 0,
                        y: 0,
                        type: 'image',
                        width: 1856,
                        height: 266,
                        describe: '作业指导图片',
                        strokeWidth: 1,
                        href: defaultQuery.data.data[0].image_content
                    })
                    this.connections = []
                }
              }
          })
          .catch(() => {
              this.$message({
                  message: '获取点位异常',
                  type: 'error'
              })
            })
  },
  methods: {
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
    getHighlight(item){
      if(this.task_num && this.task_num === item.task_num){
        item.status = 'LOCKSTATUS'
        this.$refs.chart.renderNode(item,false)
      }else{
        item.status = ''
        this.$refs.chart.renderNode(item,false)
      }
    }
  },
}
</script>

<style lange="less" scoped>
.app-container{
    width: 1856px;
    height: 266px;
}
</style>