<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.stationCode')">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_code" filterable>
                  <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="维修保养内容">
                <el-input v-model="query.maintain_content" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="780px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="240px" :inline="true">
          <el-form-item label="设备编号" prop="equip_code">
            <el-input v-model="form.equip_code" />
          </el-form-item>
          <el-form-item label="设备名称" prop="equip_des">
            <el-input v-model.number="form.equip_des" />
          </el-form-item>
          <el-form-item label="所属工位" prop="station_code">
            <el-select v-model="form.station_code">
              <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_code" />
            </el-select>
          </el-form-item>
          <el-form-item label="维修保养内容" prop="maintain_content">
            <el-input v-model="form.maintain_content" />
          </el-form-item>
          <el-form-item label="计划维修保养时间" prop="plan_maintain_date">
            <el-date-picker
              v-model="form.plan_maintain_date"
              type="datetime"
              format="yyyy-MM-dd HH"
              value-format="yyyy-MM-dd HH:mm:ss"
              popper-class="tpc"
            />
          </el-form-item>
          <el-form-item label="间隔提醒时间" prop="interval_hour">
            <el-input v-model.number="form.interval_hour">
              <template slot="append">(小时)</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.triggerPoint')" prop="tag_id">
            <!-- 触发点位 -->
            <el-input v-model.number="form.tag_id" readonly="readonly">
              <div slot="append">
                <el-popover v-model="customPopover" placement="left" width="650">
                  <tagSelect ref="tagSelect" :client-id-list="form.client_id_list" :tag-id="form.tag_id" @chooseTag="handleChooseTag" />
                  <el-button slot="reference">选择</el-button>
                </el-popover>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="责任人" prop="maintain_user">
            <el-input v-model="form.maintain_user" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  v-if="1 == 0" width="10" prop="equip_maintain_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_code" label="设备编号" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_des" label="设备名称" />
            <el-table-column  label="所属工位" align="center" prop="station_code">
              <!-- 工位号 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStationDes(scope.row.station_code) }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="maintain_content" label="维修保养内容" />
            <el-table-column  :show-overflow-tooltip="true" prop="plan_maintain_date" label="计划维修保养时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="interval_hour" label="间隔提醒时间(小时)" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_id" label="触发点位" />
            <el-table-column  :show-overflow-tooltip="true" prop="maintain_user" label="责任人" />
            <!-- Table单条操作-->
            <el-table-column  label="操作" width="115" align="center" fixed="right">  <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudStation from '@/api/core/factory/sysStation'
import crudMesQcEquipMaintain from '@/api/mes/project/mesQcEquipMaintain'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  equip_maintain_id: '',
  equip_code: '',
  equip_des: '',
  station_code: '',
  maintain_content: '',
  plan_maintain_date: '',
  interval_hour: '',
  tag_id: '',
  maintain_user: ''

}
export default {
  name: 'MES_QC_EQUIP_MAINTAIN',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '设备维修',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段:'
      idField: 'equip_maintain_id',
      // 排序
      sort: ['equip_maintain_id desc'],
      // CRUD Method
      crudMethod: { ...crudMesQcEquipMaintain },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_qc_equip_maintain:add'],
        edit: ['admin', 'mes_qc_equip_maintain:edit'],
        del: ['admin', 'mes_qc_equip_maintain:del'],
        down: ['admin', 'mes_qc_equip_maintain:down']
      },
      rules: {
        equip_code: [{ required: true, message: '请输入工位设备名称', trigger: 'blur' }],
        station_code: [{ required: true, message: '请选择所属工位', trigger: 'blur' }],
        equip_des: [{ required: true, message: '请输入工位设备名称', trigger: 'blur' }],
        maintain_content: [{ required: true, message: '请输入维修保养内容', trigger: 'blur' }],
        plan_maintain_date: [{ required: true, message: '请选择计划维修保养时间', trigger: 'blur' }],
        interval_hour: [{ required: true, message: '请输入间隔提醒时间', trigger: 'blur' }, { type: 'number', message: '间隔提醒时间必须为数字值' }],
        tag_id: [{ required: true, message: '请选择触发点位', trigger: 'blur' }],
        maintain_user: [{ required: true, message: '请输入责任人', trigger: 'blur' }]
      },
      // 工位数据
      stationData: [],
      customPopover: false,
      tag_id: ''
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.queryStation()
  },
  methods: {
    // 获取工位的中文描述
    getStationDes(station_code) {
      var item = this.stationData.find(item => item.station_code === station_code)
      if (item !== undefined) {
        return item.station_des
      }
      return station_code
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudStation
        .lovStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 触发点位
    handleChooseTag(tagId) {
      this.form.tag_id = tagId
      this.customPopover = false
    }
  }
}
</script>

<style lang="less">
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
.tpc .el-time-spinner__wrapper {
  width: 100% !important;
}

.tpc .el-scrollbar:nth-of-type(2) {
  display: none;
}
</style>
