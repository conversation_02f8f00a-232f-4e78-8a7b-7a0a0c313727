import request from '@/utils/request'

// 代码检查
export function codeEditorCheck(data) {
  return request({
    url: 'aisEsbWeb/core/codeeditor/CoreCodeEditorCheck',
    method: 'post',
    data
  })
}
// 代码保存
export function codeEditorSave(data) {
  return request({
    url: 'aisEsbWeb/core/codeeditor/CoreCodeEditorSave',
    method: 'post',
    data
  })
}
// 代码发布
export function codeEditorRelease(data) {
  return request({
    url: 'aisEsbWeb/core/codeeditor/CoreCodeEditorRelease',
    method: 'post',
    data
  })
}
export default { codeEditorCheck, codeEditorSave, codeEditorRelease }
