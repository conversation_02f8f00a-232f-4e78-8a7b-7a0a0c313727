<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="modalTitle" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <el-card v-if="configRow.search.length > 0" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div v-for="(item , index) in configRow.search" :key="index" class="formChild col-md-4 col-12">
              <!-- input 普通输入框 -->
              <el-form-item v-if="item.inputType === 'input'" :label="item.label">
                <el-input v-model="query[item.key]" class="filter-item" />
              </el-form-item>
              <!-- selectInter 代表不是查字典，掉接口使用 -->
              <el-form-item v-else-if="item.inputType === 'selectInter'" :label="item.label">
                <el-select v-model="query[item.key]" clearable filterable>
                  <el-option
                    v-for="elValue in dict[item.type]"
                    :key="elValue.model_id"
                    :label="elValue.model_type"
                    :value="elValue.model_type"
                  />
                </el-select>
              </el-form-item>
              <!-- select 代表是查字典 -->
              <el-form-item v-else-if="item.inputType === 'select'" :label="item.label">
                <el-select v-model="query[item.key]" clearable filterable>
                  <el-option v-for="elValue in dict[item.type]" :key="elValue.id" :label="elValue.label" :value="elValue.value">
                    <span style="float: left">{{ elValue.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ elValue.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- selectManuallyAdd 代表输入框是手动添加的数组-->
              <el-form-item v-else-if="item.inputType === 'selectManuallyAdd'" :label="item.label">
                <el-select v-model="query[item.key]" clearable filterable>
                  <el-option v-for="elValue in item.selectData" :key="elValue.id" :label="elValue.label" :value="elValue.value">
                    <span style="float: left">{{ elValue.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ elValue.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">查询</el-button>
              <el-button class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" class="wrapCard" style="margin-top: 10px;">
      <el-table
        ref="table"
        v-loading="loading"
        border
        size="small"
        :data="tableData"
        style="width: 100%"
        :height="height"
        :highlight-current-row="true"
      >
        <div v-for="(item,index) in columns" :key="index">
          <el-table-column
            v-if="item.model === 'dict'"
            :show-overflow-tooltip="true"
            :width="item.width"
            :align="item.align"
            :prop="item.prop"
            :label="item.label"
          >
            <template slot-scope="scope">
              {{ dict.label[item.type][scope.row[item.prop]] }}
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="item.model === 'boole'"
            :show-overflow-tooltip="true"
            :width="item.width"
            :align="item.align"
            :prop="item.prop"
            :label="item.label"
          >
            <template slot-scope="scope">
              {{ scope.row[item.prop] === 'Y' ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :show-overflow-tooltip="true"
            :align="item.align"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </div>
      </el-table>
      <el-pagination
        v-if="footer"
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px; float: right"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-card>
  </el-dialog>
</template>
<script>
import { postAction } from '@/api/manage'
import { getSelectData } from '@/components/selectModalConfig'
export default {
  name: 'SELECTMODAL',
  props: {
    dict: {
      type: [Object, Array],
      default: () => ({})
    },
    footer: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      loading: false,
      tableData: [],
      query: {},
      configJson: {
        search: []
      },
      configRow: {
        search: []
      },
      columns: [],
      apiUrl: '',
      modalTitle: '查看明细',
      modalWidth: '60%',
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    }
  },
  watch: {
    dict: {
      handler(newVal, oldVal) {
        this.dict = newVal
      },
      deep: true
    }
  },
  created() {
  },
  methods: {
    checkConfig() {
      if (!this.configJson || (!this.configJson.type && this.configJson.type !== 0)) {
        this.$message.warning('请传入唯一标识type')
        return false
      }
    },
    open(json) {
      this.configJson = json
      this.checkConfig()
      const selectData = getSelectData(this)
      for (const key in selectData) {
        if (key === json.type) {
          this.configRow = selectData[key]
        }
      }
      if (Object.keys(this.configRow).length === 0) {
        this.$message.warning('未匹配到唯一标识符，请检查配置')
        return false
      }
      if (!this.configRow.api) {
        this.$message.warning('配置文件未配置列表请求接口，请检查配置')
        return false
      }
      if (this.configRow.title) {
        this.modalTitle = this.configRow.title
      }
      if (this.configRow.width) {
        this.modalWidth = this.configRow.width
      }
      this.apiUrl = json.addRess ? (json.addRess + this.configRow.api) : this.configRow.api
      this.columns = this.configRow.columns
      this.dialogVisible = true
      this.getTableData()
    },
    toQuery() {
      this.pageTable.page = 1
      this.pageTable.size = 20
      this.getTableData()
    },
    resetQuery() {
      for (const key in this.query) {
        this.query[key] = ''
      }
      this.toQuery()
    },
    getTableData() {
      let params = Object.assign({
        page: this.pageTable.page,
        size: this.pageTable.size
      }, this.query)
      this.loading = true
      if (this.configJson.search) {
        for (const key in this.configJson.search) {
          params[key] = this.configJson.search[key]
        }
      }
      params = JSON.parse(JSON.stringify(params))
      postAction(this.apiUrl, params).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.tableData = defaultQuery.data || []
          this.pageTable.total = defaultQuery.count || 0
        } else {
          this.tableData = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      })
        .catch((err) => {
          this.tableData = []
          this.$message({
            message: err.msg,
            type: 'error'
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.pageTable.size = val
      // 查询
      this.getTableData()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.pageTable.page = val
      // 查询
      this.getTableData()
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    }
  }
}
</script>
<style scoped lang="less">
::v-deep .el-dialog {
    margin-top: 3vh !important;
}

.el-pagination {
    float: none !important;
    text-align: right;
}
</style>
