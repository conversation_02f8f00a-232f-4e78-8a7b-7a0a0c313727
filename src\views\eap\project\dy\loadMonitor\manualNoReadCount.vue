<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          工单号
        </template>
        <el-input
          ref="webWipLotNum"
          v-model="webWipLotNum"
          :readonly="true"
          clearable
          size="mini"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          计划数量
        </template>
        <el-input
          ref="webWipPlanCount"
          v-model="webWipPlanCount"
          :readonly="true"
          clearable
          size="mini"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          OK数量
        </template>
        <el-input
          ref="webWipOKCount"
          v-model="webWipOKCount"
          :readonly="true"
          clearable
          size="mini"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          少片数量
        </template>
        <el-input
          ref="webWipShortCount"
          v-model="webWipShortCount"
          clearable
          size="mini"
          @keydown.native="handleKeydown"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          NoRead数量
        </template>
        <el-input
          ref="webWipNoReadCount"
          v-model="webWipNoReadCount"
          clearable
          size="mini"
          @keydown.native="handleKeydown"
        />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top: 10px; text-align: right">
      <el-button type="primary" @click="handleSendInfo">确 定</el-button>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  components: {},
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      webWipLotNum: '',
      webWipPlanCount: '',
      webWipOKCount: '',
      webWipShortCount: '',
      webWipNoReadCount: ''
    }
  },
  mounted: function() {
  },
  created: function() {
    var WebWipInfo=this.tag_key_list.WebWipPlanCount
    if(WebWipInfo && WebWipInfo!==''){
      var lstParas=WebWipInfo.split(',')
      if(lstParas){
        var lot_num=lstParas[0]
        var first_plan_count=lstParas[1]
        var ok_count=lstParas[2]
        var noReadCount=lstParas[3]
        var short_count=lstParas[4]
        this.webWipLotNum = lot_num
        this.webWipPlanCount = first_plan_count
        this.webWipOKCount = ok_count
        this.webWipNoReadCount = noReadCount
        this.webWipShortCount = short_count
      }
    }
    this.$nextTick((x) => {
      // 正确写法
      this.$refs.webWipShortCount.focus()
    })
  },
  methods: {
    handleKeydown(e) {
      // 允许输入数字、Backspace、Delete、左箭头、右箭头
      if (
        !(
          (e.keyCode >= 48 && e.keyCode <= 57) || // 数字
          e.keyCode === 8 || // Backspace
          e.keyCode === 46 || // Delete
          (e.keyCode >= 37 && e.keyCode <= 40)
        ) // 方向键
      ) {
        e.preventDefault()
      }
    },
    handleSendInfo() {
      console.log(this.tag_key_list)
      if (this.webWipShortCount === '') {
        this.$message({ message: '请输入少板数量', type: 'info' })
        return
      }
      if (this.webWipNoReadCount === '') {
        this.$message({ message: '请输入NoRead数量', type: 'info' })
        return
      }
      if (this.webWipPlanCount === '') {
        this.$message({ message: '请正确操作,需通过后台弹窗', type: 'info' })
        return
      }
      if (this.webWipOKCount === '') {
        this.$message({ message: '请正确操作,需通过后台弹窗', type: 'info' })
        return
      }
      var first_plan_count=parseInt(this.webWipPlanCount)
      var ok_count=parseInt(this.webWipOKCount)
      var noReadCount=parseInt(this.webWipNoReadCount)
      var short_count=parseInt(this.webWipShortCount)
      if((ok_count+noReadCount+short_count)!==first_plan_count){
        this.$message({ message: 'Ok+NoRead+Short数量需要等于计划数量', type: 'info' })
        return
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebWipShortCount,
        TagValue: this.webWipShortCount
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebWipNoReadCount,
        TagValue: this.webWipNoReadCount
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebWipManual,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic =
        'SCADA_WRITE/' + this.tag_key_list.WebWipShortCount.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
