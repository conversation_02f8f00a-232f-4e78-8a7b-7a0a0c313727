<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!-- 料号 -->
            <!-- <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div> -->
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 序号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <!-- 名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="sort_name"
              :label="$t('view.table.name')"
              width="130"
              align="center"
            />
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-dialog
      :title="dialog.title"
      :visible="crud.status.cu > 0"
      :before-close="crud.cancelCU"
      width="60%"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
      >
        <el-form-item
          label="注意"
        >
          <el-col :span="22">
            <span style="color: red;">1.设定X数判定时，请将X位值设置为空；2.设定X位判定时，请将X数值设置为-1。</span>
          </el-col>
        </el-form-item>
        <el-form-item
          label="名称"
          prop="sort_name"
        >
          <el-col :span="8">
            <el-input v-model="form.sort_name" clearable size="small" />
          </el-col>
        </el-form-item>
        <el-form-item
          v-for="(p, i) in form.sortValueData"
          :key="'_' + i"
          label="仓位"
          required
        >
          <el-col :span="6">
            <el-form-item :prop="`data.${i}.key`">
              <el-input
                v-model="form.sortValueData[i].key"
                :disabled="i < 2"
                type="number"
                clearable
              >
                <template slot="prepend">工位</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :prop="`data.${i}.pos`">
              <el-input
                v-model="form.sortValueData[i].pos"
                :disabled="i < 2"
                clearable
                placeholder="请输入，例如：1,2,3"
              >
                <template slot="prepend">X位</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :prop="`data.${i}.num`">
              <el-input
                v-model="form.sortValueData[i].num"
                :disabled="i < 2"
                type="number"
                clearable
              >
                <template slot="prepend">X数</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button
              :disabled="i < 2"
              @click.prevent="dialog.action.removePosition(i)"
            >
              删除
            </el-button>
          </el-col>
        </el-form-item>
        <el-divider />
        <div style="text-align: center;width: 100%;">
          <!-- 取消 -->
          <el-button size="small" icon="el-icon-close" plain @click="dialog.action.close">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 新增 -->
          <el-button size="small" icon="el-icon-plus" @click="dialog.action.addPosition">{{ $t('view.button.addPosition') }}</el-button>
          <!-- 确认 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >{{ $t('lang_pack.commonPage.confirm') }}
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api/pack/core/xnpSort'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  sort_id: '',
  sort_name: '',
  sort_value: '[{"key":"1","num":0,"pos":""},{"key":"2","num":-1,"pos":"-"},{"key":"11","num":-1,"pos":"1"},{"key":"12","num":-1,"pos":"2"},{"key":"13","num":-1,"pos":"3"},{"key":"14","num":-1,"pos":"4"}]',
  sortValueData: [
    {
      key: '1',
      num: 0,
      pos: ''
    },
    {
      key: '2',
      num: -1,
      pos: '-'
    },
    {
      key: '11',
      num: -1,
      pos: '1'
    },
    {
      key: '12',
      num: -1,
      pos: '2'
    },
    {
      key: '13',
      num: -1,
      pos: '3'
    },
    {
      key: '14',
      num: -1,
      pos: '4'
    }
  ]
}
export default {
  name: 'PACK_SORT_XNP',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.parkXNPSort'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'sort_id',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      form: {},
      rules: {
        sort_name: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      dialog: {
        title: '',
        visible: false,
        loading: false,
        action: {
          close: () => {
            this.dialog.visible = false
            this.crud.cancelCU()
          },
          confirm: () => {
            this.dialog.loading = true
            this.$refs.form.validate(valid => {
              if (valid) {
                this.dialog.loading = false
                this.dialog.visible = false
                this.form.sort_value = JSON.stringify(this.form.sortValueData)
                this.crud.submitCU()
              } else {
                this.dialog.loading = false
              }
            })
          },
          removePosition: index => {
            this.form.sortValueData.splice(index, 1)
          },
          addPosition: () => {
            const i = this.form.sortValueData.length + 1
            this.form.sortValueData.push({
              key: parseInt(this.form.sortValueData[i - 2].key) + 1,
              num: -1,
              pos: ''
            })
            this.addRule(i)
          }
        }
      }
    }
  },
  watch: {
    'form.sortValueData': {
      handler(val) {
        for (let i = 0; i < val.length; i++) {
          this.addRule(i)
        }
      },
      deep: true
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              last_update_by: Cookies.get('userName'),
              sort_id: data.sort_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    addRule(i) {
      // 不能为空且只能是数字，不能小于0
      this.$set(this.dialog.rules, `data.${i}.key`, [
        { required: true, message: '请输入工位', trigger: 'blur' },
        { validator: (rule, value, callback) => {
          try {
            const p = Number(value)
            if (isNaN(p)) {
              throw new Error('工位必须为数字')
            }
            if (Number.isInteger(p) === false) {
              throw new Error('工位必须为整数')
            }
            if (p < 0) {
              throw new Error('工位不能小于0')
            }
            for (let j = 0; j < this.form.sortValueData.length; j++) {
              if (j !== i && this.form.sortValueData[j].key === value) {
                throw new Error('工位不能重复')
              }
            }
            callback()
          } catch (error) {
            callback(error)
          }
        }, trigger: 'blur' }
      ])
      // 不能为空且只能是整数，不能小于-1
      this.$set(this.dialog.rules, `data.${i}.num`, [
        { required: true, message: '请输入X数', trigger: 'blur' },
        { validator: (rule, value, callback) => {
          try {
            const p = Number(value)
            if (isNaN(p)) {
              throw new Error('X数必须为数字')
            }
            if (Number.isInteger(p) === false) {
              throw new Error('X数必须为整数')
            }
            if (p < -1) {
              throw new Error('X数不能小于-1')
            }
            callback()
          } catch (error) {
            callback(error)
          }
        }, trigger: 'blur' }
      ])
    },
    // 查询成功后的回调
    [CRUD.HOOK.afterRefresh](crud) {
      for (let i = 0; i < crud.data.length; i++) {
        const item = crud.data[i]
        if (item.sort_value) {
          this.$set(item, 'sortValueData', JSON.parse(item.sort_value))
        }
      }
    },
    [CRUD.HOOK.beforeToAdd](crud) {
      this.dialog.title = '新增'
      this.dialog.visible = true
      this.form = defaultForm
    },
    // 开始 "编辑" - 之前
    [CRUD.HOOK.beforeToEdit](crud, data) {
      this.dialog.title = '编辑'
      this.dialog.visible = true
      this.form = data
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud, data) {
      this.form.sort_value = JSON.stringify(this.form.sortValueData)
      crud.form = this.form
      return true
    },
    [CRUD.HOOK.afterAddCancel](crud) {
      this.dialog.title = ''
      this.dialog.visible = false
      this.form = defaultForm
    },
    [CRUD.HOOK.afterEditCancel](crud) {
      this.dialog.title = ''
      this.dialog.visible = false
      this.form = defaultForm
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
