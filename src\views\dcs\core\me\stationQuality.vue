<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="工位号:">
                                <!-- 工位号 -->
                                <el-select v-model="query.station_code">
                                    <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code" >
                                        <span style="float: left">{{ item.station_code }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务号:">
                                <!-- 任务号 -->
                                <el-input v-model="query.task_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="子合格标志:">
                                <!-- 子合格标志 -->
                                <el-input v-model="query.quality_d_sign" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="追溯时间:">
                                <!-- 追溯时间 -->
                                <el-date-picker v-model="query.trace_d_time" type="datetimerange" size="small" align="right"
                                    unlink-panels range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission">
                <template slot="right">
                    <el-button size="small" type="warning" icon="el-icon-upload2" plain round @click="crud.doExport">
                        导出
                    </el-button>
                </template>
            </crudOperation>

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.meStationQuality.eventType')" prop="event_type">
                                <!-- 事件类型 -->
                                <el-input v-model="form.mo_id" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationQuality.eventName')" prop="event_name">
                                <!-- 事件名称 -->
                                <el-input v-model="form.event_name" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationQuality.messageCODE')" prop="event_code">
                                <!-- 消息CODE -->
                                <el-input v-model="form.event_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationQuality.eventStatus')" prop="event_status">
                                <!-- 事件状态 -->
                                <el-input v-model="form.event_status" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%"
                        :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true"
                        @selection-change="crud.selectionChangeHandler">
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                    <el-descriptions-item label="ID" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.quality_trace_id
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="过站ID" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.station_flow_id
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="工位号" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{props.row.station_code
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="任务号" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.task_num
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="序列号" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.serial_num
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="组号" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.group_order
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="组描述" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.group_name
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="列位置num" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.tag_col_order
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="采集项目" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.tag_id
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="列位置内排序" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{
                                            props.row.tag_col_inner_order }}</el-descriptions-item>
                                    <el-descriptions-item label="测量对象" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.quality_for
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="采集项目名称" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.tag_des
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="采集项目单位" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.tag_uom
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="标准值" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.theory_value
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="下限值" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.down_limit
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="上限值" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.upper_limit
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="采集值" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.tag_value
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="子合格标志" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.quality_d_sign
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="追溯时间" label-class-name="table-descriptions-label"
                                        content-class-name="table-descriptions-content">{{ props.row.trace_d_time
                                        }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 工位号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="station_code"
                            :label="$t('lang_pack.meStationQuality.stationCode')" min-width="80" align='center' />
                        <!-- 任务号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="task_num"
                            :label="$t('lang_pack.meStationQuality.taskNumber')" min-width="80" align='center' />
                        <!-- 序列号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="serial_num"
                            :label="$t('lang_pack.meStationQuality.serialNumber')" min-width="80" align='center' />
                        <!-- 组号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="group_order"
                            :label="$t('lang_pack.meStationQuality.groupNumber')" min-width="80" align='center' />
                        <!-- 组描述 -->
                        <el-table-column :show-overflow-tooltip="true" prop="group_name"
                            :label="$t('lang_pack.meStationQuality.groupDescription')" min-width="80" align='center' />
                        <!-- 列位置 -->
                        <el-table-column :show-overflow-tooltip="true" prop="tag_col_order"
                            :label="$t('lang_pack.meStationQuality.positionOfColumn')" min-width="80" align='center' />
                        <!-- 采集项目 -->
                        <el-table-column :show-overflow-tooltip="true" prop="tag_id"
                            :label="$t('lang_pack.meStationQuality.collectionItems')" min-width="80" align='center' />
                        <!-- 列排序 -->
                        <el-table-column :show-overflow-tooltip="true" prop="tag_col_inner_order"
                            :label="$t('lang_pack.meStationQuality.columnSorting')" min-width="80" align='center' />
                        <!-- 测量对象 -->
                        <el-table-column :show-overflow-tooltip="true" prop="quality_for"
                            :label="$t('lang_pack.meStationQuality.measurementObject')" min-width="120" align='center' />
                        <!-- 采集项目名称 -->
                        <el-table-column :show-overflow-tooltip="true" prop="tag_des"
                            :label="$t('lang_pack.meStationQuality.entryName')" min-width="120" align='center' />
                        <!-- 采集项目单位 -->
                        <el-table-column :show-overflow-tooltip="true" prop="tag_uom"
                            :label="$t('lang_pack.meStationQuality.collectionProjectUnit')" min-width="120"
                            align='center' />
                        <!-- 标准值 -->
                        <el-table-column :show-overflow-tooltip="true" prop="theory_value"
                            :label="$t('lang_pack.meStationQuality.standardValue')" min-width="80" align='center' />
                        <!-- 下限值 -->
                        <el-table-column :show-overflow-tooltip="true" prop="down_limit"
                            :label="$t('lang_pack.meStationQuality.lowerLimitingValue')" min-width="80" align='center' />
                        <!-- 上限值 -->
                        <el-table-column :show-overflow-tooltip="true" prop="upper_limit"
                            :label="$t('lang_pack.meStationQuality.upperLimitValue')" min-width="80" align='center' />
                        <!-- 子合格标志 -->
                        <el-table-column :show-overflow-tooltip="true" prop="quality_d_sign"
                            :label="$t('lang_pack.meStationQuality.collectValues')" min-width="120" align='center' />
                        <!-- 追溯时间 -->
                        <el-table-column :show-overflow-tooltip="true" prop="trace_d_time"
                            :label="$t('lang_pack.meStationQuality.retrospectiveTime')" min-width="120" align='center' />
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import crudStationQuality from '@/api/dcs/core/me/stationQuality'
import crudStation from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    quality_trace_id: '',
    station_flow_id: '',
    station_code: '',
    task_num: '',
    serial_num: '',
    group_order: '',
    group_name: '',
    tag_col_order: '',
    tag_id: '',
    tag_col_inner_order: '',
    quality_for: '',
    tag_des: '',
    tag_uom: '',
    theory_value: '',
    down_limit: '',
    upper_limit: '',
    tag_value: '',
    quality_d_sign: '',
    trace_d_time: '',
}
export default {
    name: 'STATIONQUALITY',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '质量追溯',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'quality_trace_id',
            // 排序
            sort: ['quality_trace_id asc'],
            // CRUD Method
            crudMethod: { ...crudStationQuality },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
            },
            // 工位数据
            stationData: []
        }
    },
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
        this.getStation()
    },
    methods: {
        getStation() {
            const query = {
                userID: Cookies.get('userName')
            }
            crudStation.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.stationData = defaultQuery.data || []
                    }
                } else {
                    this.stationData = []
                    this.$message({
                        message: '工位查询异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.stationData = []
                    this.$message({
                        message: '工位查询异常',
                        type: 'error'
                    })
                })
        },
    }
}
</script>
