import request from '@/utils/request'
import { data } from 'jquery'

// 查询工位屏时间
export function getStationTime(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqSysDataSel',
    method: 'post',
    data
  })
}
// 查询工位屏当前工位订单
export function getStationOrder(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqStationScreenMakeOrder',
    method: 'post',
    data
  })
}
// 查询工位屏订单队列
export function getStationOrderQueue(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqStationScreenQueue',
    method: 'post',
    data
  })
}
// 查询工位屏订单队列
export function getStationOrderQueueZZ(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqZaStationScreenOrderQueue',
    method: 'post',
    data
  })
}
// 查询工位屏BOM作业三单关键特性
export function getStationBZG(data) {
  return request({
    url: 'aisEsbOra/pmc/project/bq/PmcBqStationScreenOrderInfo',
    method: 'post',
    data
  })
}
// 查询工位屏紧急事件
export function getStationEmergency(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqStationScreenEmergency',
    method: 'post',
    data
  })
}
// 查询工位屏工艺质量
export function getStationQuality(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqStationScreenQuality',
    method: 'post',
    data
  })
}
// 查询工位屏缺陷TOP
export function getStationTop(data) {
  return request({
    url: 'aisEsbOra/pmc/project/bq/PmcBqStationScreenDefectInfo',
    method: 'post',
    data
  })
}
// 查询工位生成订单状态
export function getHaStationScreenQueue(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqHaStationScreenQueue',
    method: 'post',
    data
  })
}
// 查询工位生成订单状态
export function getPmcBqPartStation(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqPartStationScreenData',
    method: 'post',
    data
  })
}
// 查询轮胎线左
export function getPmcBqTyreStationLeft(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqTyreStationScreenLeftData',
    method: 'post',
    data
  })
}
// 查询轮胎线左
export function getPmcBqTyreStationRight(data) {
  return request({
    url: 'aisEsbApi/pmc/project/bq/PmcBqTyreStationScreenRightData',
    method: 'post',
    data
  })
}
// 质量信息查询
export function getAqmisDefectInfo(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/aqmisDefectInfo?${data}`,
    method: 'get',
  })
}

// 加注停线列
export function getOilStop(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-om/api/v1/oilStop?${data}`,
    method: 'get',
  })
}

// 问题项趋势图
export function getProblemTrendsChart(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getProblemTrendsChart?station=${data.station}`,
    method: 'get',
  })
}

// AB类问题柱状图
export function getABClassifiy(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getABClassificationProblemBarChart?station=${data.station}`,
    method: 'get',
  })
}

// 一次交检合格率影响值
export function getFirstPassYield(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getFirstPassYieldImpactValue?station=${data.station}`,
    method: 'get',
  })
}
// 大屏底部文字
export function getBigScreenFooter(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreenFooter?queryMode=list&${data}`,
    method: 'get',
  })
}
// 获取问题关闭率趋势图
export function getIssueClosureRate(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getIssueClosureRate?station=${data.station}`,
    method: 'get',
  })
}

// 获取重复问题发生项数
export function getNumberofDuplicateIssueOccurrences(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getNumberofDuplicateIssueOccurrences?station=${data.station}`,
    method: 'get',
  })
}
// 获取质量奖激励明细
export function qualityAwardIncentiveDetails(data) {
  return request({
    url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/qualityAwardIncentiveDetails?${data}`,
    method: 'get',
  })
}



export default { getStationTime, getStationOrder, getStationOrderQueue, getStationBZG, getStationEmergency, getStationQuality, getStationTop, getHaStationScreenQueue, getPmcBqPartStation, getPmcBqTyreStationLeft
  , getPmcBqTyreStationRight,getAqmisDefectInfo,getOilStop,getProblemTrendsChart,getABClassifiy,getFirstPassYield,getBigScreenFooter
,getIssueClosureRate,getNumberofDuplicateIssueOccurrences,qualityAwardIncentiveDetails
}
