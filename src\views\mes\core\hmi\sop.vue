<template>
  <SopChart
    ref="chart"
    v-loading="loading"
    :nodes="nodes"
    :connections="connections"
    :width="'500'"
    :height="'300'"
    :readonly="true"
    element-loading-text="拼命绘制流程图中"
    @editnode="handleEditNode"
    @dblclick="handleDblClick"
    @editconnection="handleEditConnection"
    @save="handleChartSave"
    @delnode="handleDelNode"
  />
</template>
<script>
/* eslint-disable no-unused-vars */

import Cookies from 'js-cookie'
import SopChart from '@/components/SopChart/index'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
export default {
  components: {
    SopChart
  },
  props: {

  },
  data() {
    return {
      height: document.documentElement.clientHeight - 60,
      nodes: [
        {
          id: 1,
          subId_stepId: 0,
          x: 0,
          y: 0,
          name: '1-1',
          type: 'image',
          width: 500,
          height: 300,
          imgId: '105',
          color: '#FFFFFF',
          describe: '步骤',
          strokeWidth: 1
        },
        {
          id: 2,
          x: 90,
          y: 125,
          type: 'circle',
          width: 10,
          height: 50,
          color: '#C8CACC',
          describe: '圆圈',
          stroke: '#FFFFFF',
          strokeWidth: '2px'
        },
        {
          id: 3,
          x: 120,
          y: 110,
          type: 'circle',
          width: 10,
          height: 50,
          color: '#F9F500',
          describe: '圆圈',
          stroke: '#FFFFFF',
          strokeWidth: '2px'
        },
        {
          id: 4,
          x: 150,
          y: 95,
          type: 'circle',
          width: 10,
          height: 50,
          color: '#0EAD00',
          describe: '圆圈',
          stroke: '#FFFFFF',
          strokeWidth: '2px'
        },
        {
          id: 5,
          x: 190,
          y: 80,
          type: 'circle',
          width: 10,
          height: 50,
          color: '#FF1418',
          describe: '圆圈',
          stroke: '#FFFFFF',
          strokeWidth: '2px',
          status: 'Work'
        }
      ],
      connections: [],
      nodeForm: { target: null },
      connectionForm: { target: null, operation: null },
      loading: false,
      dialogLabel: false,
      flow_label: '',
      flowLabelPlaceholder: '',
      rcsFlowSubVisible: false,
      rcsFlowSubStepVisible: false,
      scrollLeft: 0,
      scrollTop: 0
    }
  },
  watch: {

  },
  mounted() {

  },
  created() {
  },
  methods: {
    handleDblClick(position) {},
    handleDelNode(type, id) {

    },
    async handleChartSave(nodes, connections) {

    },
    handleRefreshChart(node, thisH) {
      this.$refs.chart.init(node, thisH)
      this.$refs.chart.save()
    },
    handleEditNode(node, thisH) {

    },
    handleEditConnection(connection) {
      // this.connectionForm.target = connection;
      // this.connectionDialogVisible = true;
    }
  }
}
</script>
