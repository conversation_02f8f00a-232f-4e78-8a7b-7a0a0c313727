<template>
    <div class="box-card">
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
            <el-form ref="query" :inline="true" size="mini" style="margin-top: 15px; padding: 0" label-width="70px">
                <el-row>
                    <el-form-item label="产线" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.prod_line_code" clearable style="width: 150px" value-key="prod_line_id"
                            @change="handleProdLineChange">
                            <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_des"
                                :value="item">
                                <span style="float: left">{{ item.prod_line_des }}</span>
                                <span style="
                                    float: right;
                                    margin-left: 20px;
                                    color: #8492a6;
                                    font-size: 13px;
                                ">{{ item.prod_line_code }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="机型" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.small_model_type" filterable clearable style="width: 130px">
                            <el-option v-for="item in smallModelTypeData" :key="item.small_type_id"
                                :label="item.small_model_type" :value="item.small_model_type">
                            </el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="工位号" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.station_code" filterable clearable style="width: 150px"
                            value-key="station_code" @change="getQulityforData">
                            <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_code"
                                :value="item">
                                <span style="float: left">{{ item.station_code }}</span>
                                <span style="
                            float: right;
                            margin-left: 20px;
                            color: #8492a6;
                            font-size: 13px;
                        ">{{ item.station_des }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="测量项目" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.quality_for" filterable clearable style="width: 220px">
                            <el-option v-for="item in tagList" :key="item.tag_des" :label="item.tag_des"
                                :value="item.tag_des" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="查询时间" style="margin: 0px 0px 5px 0px">
                        <el-date-picker v-model="query.date" type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="~" start-placeholder="开始日期"
                            end-placeholder="结束日期" style="width: 350px" align="right"  @input="$forceUpdate()"/>
                    </el-form-item>
                    <el-button style="margin: 0px 0px 5px 0px" class="filter-item" size="mini" type="primary"
                        icon="el-icon-search" @click="handleQuery()">查询</el-button>
                    <el-button style="margin: 0px 0px 5px 0px" class="filter-item" size="mini" type="primary"
                        icon="el-icon-upload2" :disabled="!tableData.length" :loading="downloadLoading" @click="exportExcel()">导出</el-button>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <el-table ref="table" v-loading="tableLoading" :data="tableData" style="width: 100%"
                :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" :height="height"
                :highlight-current-row="true">
                <el-table-column :show-overflow-tooltip="true" prop="prod_line_code" width="120" label="产线编码" />
                <!-- <el-table-column :show-overflow-tooltip="true" prop="prod_line_des" width="240" label="产线描述" /> -->
                <el-table-column :show-overflow-tooltip="true" prop="station_code" label="工位编号" />
                <!-- <el-table-column :show-overflow-tooltip="true" prop="station_des" width="300" label="工位描述" /> -->
                <!-- <el-table-column :show-overflow-tooltip="true" prop="small_model_type" label="机型" /> -->
                <el-table-column :show-overflow-tooltip="true" prop="quality_for" width="130" label="测量项目" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_value" label="质量值" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_des" label="质量描述" />
                <el-table-column :show-overflow-tooltip="true" prop="upper_limit" label="标准上限" />
                <el-table-column :show-overflow-tooltip="true" prop="down_limit" label="标准下限" />
                <el-table-column :show-overflow-tooltip="true" prop="theory_value" label="标准值" />
                <el-table-column :show-overflow-tooltip="true" prop="date" width="180" label="时间" />
            </el-table>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <el-row :gutter="20">
                <el-col :span="15">
                    <el-button @click="getSampleTrend('0')" :disabled="!query.station_code">小时</el-button>
                    <el-button @click="getSampleTrend('1')" :disabled="!query.station_code">日</el-button>
                    <el-button @click="getSampleTrend('2')" :disabled="!query.station_code">周</el-button>
                    <el-button @click="getSampleTrend('3')" :disabled="!query.station_code">月</el-button>
                    <div id="cpk_chart" style="width: 100%;height: calc(100vh - 590px)">
                    </div>
                </el-col>
                <el-col :span="9">
                    <div class="sampleStyle">
                        <div class="sampleOperate" v-for="(item,index) in sampledData" :key="index">
                            <el-checkbox v-model="item.checkBox"
                                @change="handleCheckedChange(item.type)">{{ item.label }}</el-checkbox>
                            <div v-for="(value,i) in item.children" :key="i">
                                <span>{{ value.label }}</span>
                                <el-input-number v-model="value.val" clearable size="mini" :min="1" :max="99999"
                                    style="width: 100px" />
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>

        </el-card>
    </div>
</template>
<script>
import { mesQualitySpcAnalyze, mesQualitySpcTagList, sampleTrend,sampleTrendExport } from '@/api/mes/core/spcReport'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import { downloadFile } from '@/utils/index'
import Cookies from 'js-cookie'
import ECharts from 'vue-echarts'
import ExcelJS from 'exceljs';    // 引入exceljs, 用于生成excel文件
import { saveAs } from 'file-saver' // 引入file-saver, 用于保存文件
export default {
    name: 'MES_REPORT_SAMPLE',
    components: {
        ECharts
    },
    data() {
        return {
            height: document.documentElement.clientHeight - 550,
            orgOptions2: {},
            // 样本个数
            samples: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            // 样本取值
            values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            // 上限值
            upper_limit: 0,
            // 下限值
            down_limit: 0,
            tag_uom: '',
            prodLineData: [],
            smallModelTypeData: [],
            stationData: [],
            tableData: [],
            tableDataCatch: [],
            tagList: [],
            tableLoading: false,
            option1: true,
            option2: false,
            option3: false,
            option4: false,
            option5: false,
            customPopover1: false,
            query: {
                station_code: '',
                small_model_type: '',
                quality_for: '',
                selectedoption: 'option1',
                attr1: 1,
                attr2: 1,
                attr3: 1,
                date: null
            },
            // 时间选择器
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            sampleVal: '',
            sampleDom: null,
            downloadLoading:false,
            sampledData:[
                {type:'FIXED_SAMPLING',checkBox:true,label:'每日随机取样设定',children:[{label:'取样条数',val:1}]},
                {type:'AGO_SAMPLING',checkBox:false,label:'每日取样前N条设定',children:[{label:'取样条数',val:1}]},
                {type:'FINAL_SAMPLING',checkBox:false,label:'每日取样后N条设定',children:[{label:'取样条数',val:1}]},
                // 开始条数，就是传index
                {type:'INDEX_SAMPLING',checkBox:false,label:'每日取样开始位置设定',children:[{label:'取样条数',val:1},{label:'开始条数',val:1},]},
            ]
        }
    },
    computed: {
    // 默认时间
    timeDefault() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      // 月，日 不够10补0
      const defalutStartTime =
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1 >= 10
          ? start.getMonth() + 1
          : "0" + (start.getMonth() + 1)) +
        "-" +
        (start.getDate() >= 10 ? start.getDate() : "0" + start.getDate()) +
        " 00:00:00";
      const defalutEndTime =
        end.getFullYear() +
        "-" +
        (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : "0" + (end.getMonth() + 1)) +
        "-" +
        (end.getDate() >= 10 ? end.getDate() : "0" + end.getDate()) +
        " 23:59:59";
      return [defalutStartTime, defalutEndTime];
    },
  },
    created: function () {
        this.query.date = this.timeDefault;
        selProdLine({
            user_name: Cookies.get('userName'),
            enable_flag: 'Y'
        })
            .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.prodLineData = defaultQuery.data
                    }
                }
            })
            .catch(() => {
                this.$message({
                    message: '初始化模式数据异常',
                    type: 'error'
                })
            })
    },
    mounted() {
        if(sessionStorage.getItem("sampledData")){
            const data = JSON.parse(sessionStorage.getItem("sampledData"))
            this.sampledData = data.nodes
        }
    },
    methods: {
        handleProdLineChange(data) {
            const query = {
                userID: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            }
            selStation(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.stationData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                })
                return
            selSmallModel({
                user_name: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            })
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.smallModelTypeData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '初始化模式数据异常',
                        type: 'error'
                    })
                })
        },
        handleQuery() {
            if (this.query.prod_line_code === '') {
                this.$message({
                    message: '请选择产线',
                    type: 'info'
                })
                return
            }
            if (this.query.station_code === '') {
                this.$message({
                    message: '请选择工位号',
                    type: 'info'
                })
                return
            }
            if (this.query.quality_for === '') {
                this.$message({
                    message: '请选择测量项目',
                    type: 'info'
                })
                return
            }
            if (!this.query.date) {
                this.$message({
                    message: '请选择时间',
                    type: 'info'
                })
                return
            }
            this.tableData = []
            this.shifts = []
            this.reality_Beat = []
            this.standard_Beat = []
            this.samples = []
            this.values = []
            this.getSampleTrend('0')
            // const query = {
            //     prod_line_code: this.query.prod_line_code,
            //     station_code: this.query.station_code,
            //     item_date: this.query.date
            // }

            this.tableLoading = true
            return
            mesQualitySpcAnalyze(query)
                .then((res) => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code == 0) {
                        if (defaultQuery.data.length > 0) {
                            this.tableDataCatch = defaultQuery.data
                            if (this.query.selectedoption === 'option1') {
                                var i = 0 // 当前序号
                                var j = 0 // 取值次数
                                var lastTime = new Date()
                                this.tableDataCatch.forEach((item) => {
                                    if (j < this.query.attr2) {
                                        var time = item.trace_d_time
                                        var trace_d_time = new Date(time)
                                        if (i === 0) {
                                            lastTime = trace_d_time
                                        }
                                        var newdate = new Date(
                                            lastTime + this.query.attr1 * i * 60 * 1000
                                        )
                                        if (trace_d_time.getTime() === newdate.getTime()) {
                                            this.tableData.push(item)
                                            j += 1
                                        }
                                    }
                                    i += 1
                                })
                            } else if (this.query.selectedoption === 'option2') {
                                var i = 0 // 当前序号
                                var j = 0 // 取值次数
                                this.tableDataCatch.forEach((item) => {
                                    if (j < this.query.attr2) {
                                        if (i % (this.query.attr1 + 1) === 0) {
                                            this.tableData.push(item)
                                            j += 1
                                        }
                                    }
                                    i += 1
                                })
                            } else if (this.query.selectedoption === 'option3') {
                                var i = 0 // 当前序号
                                var j = 0 // 取值次数
                                var k = this.query.attr1 - this.query.attr2
                                if (k > 0) {
                                    this.tableDataCatch.forEach((item) => {
                                        if (j < this.query.attr2) {
                                            if (i >= k && i < this.query.attr1) {
                                                this.tableData.push(item)
                                                j += 1
                                            }
                                        }
                                        i += 1
                                    })
                                } else {
                                    this.$message({
                                        message: '参数设置异常',
                                        type: 'error'
                                    })
                                }
                            } else if (this.query.selectedoption === 'option4') {
                                var i = 0 // 当前序号
                                var j = 0 // 取值次数
                                this.tableDataCatch.forEach((item) => {
                                    if (j < this.query.attr2) {
                                        if (i > this.query.attr1) {
                                            this.tableData.push(item)
                                            j += 1
                                        }
                                    }
                                    i += 1
                                })
                            } else if (this.query.selectedoption === 'option5') {
                                var i = 0 // 当前序号
                                var j = 0 // 取值次数
                                this.tableDataCatch.forEach((item) => {
                                    if (j < this.query.attr3) {
                                        if (
                                            i >= this.query.attr1 &&
                                            i % (this.query.attr2 + 1) === 0
                                        ) {
                                            this.tableData.push(item)
                                            j += 1
                                        }
                                    }
                                    i += 1
                                })
                            }
                            this.tableData = defaultQuery.data;
                            var SUM = 0.0 // 采集值的和
                            var S = 0.0 // 方差和
                            var i = 0
                            this.tableData.forEach((item) => {
                                this.tag_uom = item.tag_uom
                                this.result.USL = parseFloat(item.upper_limit)
                                this.result.LSL = parseFloat(item.down_limit)
                                SUM += parseFloat(item.tag_value)
                                i += 1
                                this.samples.push(i)
                                this.values.push(item.tag_value)
                            })
                            this.result.C = ((this.result.USL + this.result.LSL) / 2).toFixed(
                                2
                            )
                            this.result.X = (SUM / defaultQuery.data.length).toFixed(2)
                            this.result.T = (this.result.USL - this.result.LSL).toFixed(2)
                            this.tableData.forEach((item) => {
                                S += Math.pow(parseFloat(item.tag_value) - this.result.X, 2)
                            })
                            this.result.δ = Math.sqrt(
                                S / (defaultQuery.data.length - 1)
                            ).toFixed(2)
                            this.result.Ca = (
                                ((this.result.X - this.result.C) * 2) /
                                this.result.T
                            ).toFixed(2)
                            this.result.Cp = (
                                (this.result.USL - this.result.LSL) /
                                (this.result.δ * 6)
                            ).toFixed(2)
                            this.result.Cpk = (
                                this.result.Cp * Math.abs(1 - this.result.Ca)
                            ).toFixed(2)
                            this.orgOptions2.xAxis.data = this.samples
                            this.orgOptions2.series[0].data = this.values
                            this.orgOptions2.series[0].markLine.data[0].yAxis = this.result.X
                            this.orgOptions2.series[0].markLine.data[1].yAxis =
                                this.result.USL
                            this.orgOptions2.series[0].markLine.data[2].yAxis =
                                this.result.LSL
                            this.orgOptions2.yAxis.name = '样本采集值(' + this.tag_uom + ')'
                        } else {
                            this.tableData = []
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                })
        },
        getQulityforData(data) {
            this.tagList = []
            this.query.quality_for = ''
            mesQualitySpcTagList({
                enable_flag: 'Y',
                station_id: data.station_id
            })
                .then(res => {
                    // console.log(res)
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.tagList = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询采集项目异常',
                        type: 'error'
                    })
                })
        },
        handleCheckedChange(type) {
            this.sampledData.forEach(item=>item.checkBox = item.type === type)
            const sampleItem = this.sampledData.find(item=>item.type === type)
            const quantity = sampleItem.children[0].val
            const query = {type,quantity}
            if(type === 'INDEX_SAMPLING'){
                query.index = sampleItem.children[1].val
            }
            sampleTrend(query).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    sessionStorage.setItem("sampledData",'{"nodes":' + JSON.stringify(this.sampledData) + '}');
                    this.$message({type:'success',message:'操作成功'})
                }
            }).catch(ex=>{
                this.$message({type:'error',message:'操作失败'+ex.msg})
            })
        },
        getSampleTrend(val) {
            if (val) {
                this.sampleVal = val
            }
            const sampleItem = this.sampledData.find(item=>item.checkBox === true)
            const query = {
                prod_line_code: this.query.prod_line_code.prod_line_code,
                station_code: this.query.station_code.station_code,
                item_date: this.query.date,
                quality_for:this.query.quality_for,
                timeDimension: this.sampleVal,
                type:sampleItem.type,
                quantity:sampleItem.children[0].val
            }
            if(sampleItem.type === 'INDEX_SAMPLING'){
                query.index = sampleItem.children[1].val
            }
            this.sampleDom = this.$echarts.init(document.getElementById('cpk_chart'))
            sampleTrend(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        this.tableLoading = false
                        const data = this.sortObjectByTimestamp(defaultQuery.data.stationQualityInfo)
                        this.tableData = defaultQuery.data.infos
                        const xAxisData = []
                        const seriesData = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key].length)
                        }
                        const option = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow',
                                    label: {
                                        show: true,
                                    },
                                },
                            },
                            xAxis: {
                                type: 'category',
                                data: xAxisData
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [
                                {
                                    data: seriesData,
                                    type: 'line',
                                    itemStyle: {
                                        normal: {
                                            label: {
                                                show: true, //开启显示
                                                position: 'top', //顶部显示
                                                textStyle: {
                                                    //数值样式
                                                    color: 'black',
                                                    fontSize: 16,
                                                },
                                            },
                                            color: '#4472C4',
                                        },
                                    },
                                }
                            ]
                        }
                        this.sampleDom.setOption(option)
                    }
                } else {
                    this.tableLoading = false
                    this.sampleDom = null
                    this.tableData = []
                    this.$message({
                        type: 'warning',
                        message: defaultQuery.msg
                    })
                }
            }).catch((ex) => {
                this.tableLoading = false
                this.sampleDom = null
                this.tableData = []
                this.$message({
                    type: 'warning',
                    message: ex.msg
                })
            })

        },
        exportExcel() {
            // const workbook = new ExcelJS.Workbook(); // 创建工作簿
            // const worksheet = workbook.addWorksheet('Sheet1'); // 添加工作表
            // worksheet.addRow(['产线编码', '产线描述', '工位编号', '工位描述', '质量值', '标准上限', '标准下限', '标准值', '时间']);
            // tableData.forEach(item=>{
            //     worksheet.addRow(['prod_line_code', 'prod_line_des', 'station_code', 'station_des', 'tag_value', 'upper_limit', 'down_limit', 'theory_value', 'date']);
            // })
            // const chart = this.$echarts.init(document.getElementById('myChart')) // 获取图表实例
            // const base64Image = chart.getDataURL({
            //     pixelRatio: 2, // 导出图片的分辨率比例，默认为1，即图片的分辨率为屏幕分辨率的一倍
            //     backgroundColor: '#fff' // 导出图片的背景色
            // })
            // let image = workbook.addImage({ // 添加图片
            //     base64: base64Image, // 图片的base64编码
            //     extension: 'png'  // 图片的扩展名
            // });
            // worksheet.addImage(image, 'M1:U20'); // 将图片添加到工作表中
            // workbook.xlsx.writeBuffer().then(function (buffer) { // 生成excel文件的二进制数据
            //     saveAs(new Blob([buffer], {  // 生成Blob对象
            //         type: 'application/octet-stream'  // 指定文件的MIME类型
            //     }), 'xchart.xlsx');  // 指定文件名
            // });
            const chart = this.$echarts.init(document.getElementById('cpk_chart'))
            const base64 = chart.getDataURL({
                pixelRatio: 4, // 可选，设置像素比例
                backgroundColor: '#fff' // 可选，设置背景颜色
            });
            // 这句代码是因为入参不需要"data:image/png;base64"
            const base = base64.split(',')[1]
            this.downloadLoading = true
            if(!base){
                this.downloadLoading = false
                return
            }
            const sampleItem = this.sampledData.find(item=>item.checkBox === true)
            const query = {
                file:base,
                prod_line_code: this.query.prod_line_code.prod_line_code,
                station_code: this.query.station_code.station_code,
                quality_for:this.query.quality_for,
                item_date: this.query.date,
                timeDimension: this.sampleVal,
                type:sampleItem.type,
                quantity:sampleItem.children[0].val
            }
            if(sampleItem.type === 'INDEX_SAMPLING'){
                query.index = sampleItem.children[1].val
            }
            sampleTrendExport(query).then(res=>{
                downloadFile(res, '样本趋势图导出', 'xlsx')
                this.downloadLoading = false
            }).catch((ex)=>{
                this.downloadLoading = false
                this.$message({type:'error',msg:ex.msg})
            })

        },
        // 字符串转时间戳并且支持排序的方法
        sortObjectByTimestamp(obj) {
            var sortedArray = Object.entries(obj).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            });
            var sortedObj = {};
            for (var i = 0; i < sortedArray.length; i++) {
                var key = sortedArray[i][0];
                var value = sortedArray[i][1];
                sortedObj[key] = value;
            }
            return sortedObj;
        }
    }
}
</script>
<style scoped lang="less">
.sampleStyle {
    display: flex;
    overflow-x: hidden; //超出隐藏
    flex-wrap: wrap; //超出自动换行
    .sampleOperate {
        margin:10px;
        padding:10px;
        width: 40%;
        text-align: center;
        border:1px solid #eee;
        border-radius:5px;
        ::v-deep .el-checkbox__label{
            font-size:16px !important;
        }
        div {
            margin-top: 5px;
            text-align: right;
            span {
                margin-right: 5px;
            }
        }
    }
}
</style>
