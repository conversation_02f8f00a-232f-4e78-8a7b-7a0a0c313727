<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-drawer append-to-body :wrapper-closable="false" :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="650px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="150px" :inline="true">
          <el-form-item label="标签代码" prop="tag_code" display:none>
            <el-input v-model="form.tag_code" />
          </el-form-item>
          <el-form-item label="标签描述" prop="tag_des">
            <el-input v-model="form.tag_des" />
          </el-form-item>
          <!--表：scada_driver_block-->
          <el-form-item label="区域名称" prop="block_name">
            <el-select v-model="form.block_name" clearable filterable>
              <el-option v-for="item in blockNameData" :key="item.block_name" :label="item.block_name" :value="item.block_name" />
            </el-select>
          </el-form-item>
          <el-form-item label="区域编号" prop="block_addr">
            <el-input v-model="form.block_addr" />
          </el-form-item>
          <!--快速编码：CONTROL_DATA_TYPE-->
          <el-form-item label="数据类型" prop="data_type">
            <el-select v-model="form.data_type" clearable filterable>
              <el-option v-for="item in dict.CONTROL_DATA_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="起始地址" prop="start_addr">
            <el-input v-model="form.start_addr" />
          </el-form-item>
          <el-form-item label="数据长度" prop="data_length">
            <el-input v-model="form.data_length" />
          </el-form-item>
          <el-form-item label="位" prop="data_bit">
            <el-input v-model="form.data_bit" />
          </el-form-item>
          <el-form-item label="OPC真实地址" prop="opc_addr">
            <el-input v-model="form.opc_addr" />
          </el-form-item>
          <el-form-item label="OPC模拟地址" prop="opc_demo_addr">
            <el-input v-model="form.opc_demo_addr" />
          </el-form-item>
          <!--快速编码：DATA_ACCESS-->
          <el-form-item label="数据权限" prop="data_access">
            <el-select v-model="form.data_access" clearable filterable>
              <el-option v-for="item in dict.DATA_ACCESS" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="其他属性" prop="tag_attr_else">
            <el-input v-model="form.tag_attr_else" />
          </el-form-item>
          <!--快速编码：DATA_FORMAT-->
          <el-form-item label="转换格式" prop="data_format">
            <el-select v-model="form.data_format" clearable filterable>
              <el-option v-for="item in dict.DATA_FORMAT" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="区分数据类型" prop="tag_attr">
            <el-input v-model="form.tag_attr" />
          </el-form-item>
          <el-form-item label="预警上限" prop="upper_limit">
            <el-input v-model="form.upper_limit" />
          </el-form-item>
          <el-form-item label="预警下限" prop="down_limit">
            <el-input v-model="form.down_limit" />
          </el-form-item>
          <el-form-item label="变化推送">
            <el-radio-group v-model="form.pub_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="快照存储">
            <el-radio-group v-model="form.fastpic_save_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="变化保存">
            <el-radio-group v-model="form.change_save_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="预警处理">
            <el-radio-group v-model="form.pre_alarm_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="开放OPCUA点位">
            <el-radio-group v-model="form.opc_ua_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="有效标识">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">确认</el-button>
        </div>
      </el-drawer>

      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" class="tableOverflow" size="small" :data="tableDataTable" style="width: 100%" :height="height" highlight-current-row @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" width="150px" prop="tag_code" label="标签代码" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" width="250px" prop="tag_des" label="标签描述" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="block_name" label="区域名称" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="block_addr" label="区域编码" sortable="custom" />
        <el-table-column  label="数据类型" align="center" prop="data_type" sortable="custom">
          <!-- 数据类型 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.CONTROL_DATA_TYPE[scope.row.data_type] }}
          </template>
        </el-table-column>

        <el-table-column  :show-overflow-tooltip="true" prop="start_addr" label="起始地址" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="data_length" label="数据长度" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="data_bit" label="位" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="opc_addr" label="OPC真实地址" sortable="custom" width="100px" />
        <el-table-column  :show-overflow-tooltip="true" prop="opc_demo_addr" label="OPC模拟地址" sortable="custom" width="100px" />
        <el-table-column  label="数据权限" align="center" prop="data_access" sortable="custom">
          <!-- 数据权限 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.DATA_ACCESS[scope.row.data_access] }}
          </template>
        </el-table-column>

        <el-table-column  label="变化推送" align="center" prop="pub_flag" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.pub_flag] }}
          </template>
        </el-table-column>
        <el-table-column  label="快照存储" align="center" prop="fastpic_save_flag" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.fastpic_save_flag] }}
          </template>
        </el-table-column>

        <el-table-column  :show-overflow-tooltip="true" prop="tag_attr_else" label="其他属性" sortable="custom" />
        <el-table-column  label="转换格式" align="center" prop="data_format" sortable="custom">
          <!-- 转换格式 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.DATA_FORMAT[scope.row.data_format] }}
          </template>
        </el-table-column>

        <el-table-column  label="变化保存" align="center" prop="change_save_flag" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.change_save_flag] }}
          </template>
        </el-table-column>
        <el-table-column  label="预警处理" align="center" prop="pre_alarm_flag" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.pre_alarm_flag] }}
          </template>
        </el-table-column>
        <el-table-column  :show-overflow-tooltip="true" prop="upper_limit" label="预警上限" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="down_limit" label="预警下限" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="tag_attr" label="区分数据类型" sortable="custom" />

        <el-table-column  label="开放OPCUA点位" align="center" prop="opc_ua_flag" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.opc_ua_flag] }}
          </template>
        </el-table-column>
        <el-table-column  label="有效标识" align="center" prop="enable_flag" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column  label="操作" align="center" fixed="right">
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import { lovBlock } from '@/api/core/scada/block'
import { selScadaTag, insScadaTag, updScadaTag, delScadaTag } from '@/api/core/scada/tag'

export default {
  name: 'TAG',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 230,
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      // Table
      listLoadingTable: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],

      form: {
        tag_id: '',
        tag_group_id: '',
        tag_code: '',
        tag_des: '',
        block_name: '',
        block_addr: '',
        data_type: '',
        start_addr: '',
        data_length: '',
        data_bit: '',
        opc_addr: '',
        opc_demo_addr: '',
        data_access: '',
        pub_flag: 'Y',
        fastpic_save_flag: 'N',
        tag_attr_else: '',
        data_format: '',
        change_save_flag: 'N',
        pre_alarm_flag: 'N',
        upper_limit: '-1',
        down_limit: '-1',
        tag_attr: '',
        opc_ua_flag: 'N',
        enable_flag: 'Y'
      },
      rules: {
        // 提交验证规则
        tag_code: [{ required: true, message: '请输入标签代码', trigger: 'blur' }]
      },
      dataTypeData: [],
      dataAccessData: [],
      dataFormatData: [],
      blockNameData: [],
      tag_code_des: '',

      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      query: {
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'tag_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WHETHER_FLAG', 'CONTROL_DATA_TYPE', 'DATA_ACCESS', 'DATA_FORMAT'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 230
    }
  },
  created: function() {
    // 查询
    this.toButQuery()

    const query = {
      user_name: Cookies.get('userName'),
      tag_group_id: this.form.tag_group_id
    }
    // 从后台获取到对象数组
    lovBlock(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.blockNameData = defaultQuery.data
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 按钮：
    toButQuery(tagGroupId, queryContent) {
      if (tagGroupId === undefined) {
        this.form.tag_group_id = this.$parent.$attrs.taggroupid
      } else {
        this.form.tag_group_id = tagGroupId
      }
      if (queryContent !== undefined) {
        this.tag_code_des = queryContent
      }
      this.listLoadingTable = true
      const query = {
        user_name: Cookies.get('userName'),
        tag_group_id: this.form.tag_group_id,
        tag_code_des: this.tag_code_des,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      selScadaTag(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toButAdd() {
      this.form.tag_id = ''
      this.form.tag_code = ''
      this.form.tag_des = ''
      this.form.block_name = ''
      this.form.block_addr = ''
      this.form.data_type = ''
      this.form.start_addr = ''
      this.form.data_length = ''
      this.form.data_bit = ''
      this.form.opc_addr = ''
      this.form.opc_demo_addr = ''
      this.form.data_access = ''
      this.form.pub_flag = 'Y'
      this.form.fastpic_save_flag = 'N'
      this.form.tag_attr_else = ''
      this.form.data_format = ''
      this.form.change_save_flag = 'N'
      this.form.pre_alarm_flag = 'N'
      this.form.upper_limit = '-1'
      this.form.down_limit = '-1'
      this.form.tag_attr = ''
      this.form.opc_ua_flag = 'N'
      this.form.enable_flag = 'Y'

      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增标签'
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.tag_id = data.tag_id
      this.form.tag_group_id = data.tag_group_id
      this.form.tag_code = data.tag_code
      this.form.tag_des = data.tag_des
      this.form.block_name = data.block_name
      this.form.block_addr = data.block_addr
      this.form.data_type = data.data_type
      this.form.start_addr = data.start_addr
      this.form.data_length = data.data_length
      this.form.data_bit = data.data_bit
      this.form.opc_addr = data.opc_addr
      this.form.opc_demo_addr = data.opc_demo_addr
      this.form.data_access = data.data_access
      this.form.pub_flag = data.pub_flag
      this.form.fastpic_save_flag = data.fastpic_save_flag
      this.form.tag_attr_else = data.tag_attr_else
      this.form.data_format = data.data_format
      this.form.change_save_flag = data.change_save_flag
      this.form.pre_alarm_flag = data.pre_alarm_flag
      this.form.upper_limit = data.upper_limit
      this.form.down_limit = data.down_limit
      this.form.tag_attr = data.tag_attr
      this.form.opc_ua_flag = data.opc_ua_flag
      this.form.enable_flag = data.enable_flag
      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改标签'
    },
    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            tag_id: this.form.tag_id,
            tag_group_id: this.form.tag_group_id,
            tag_code: this.form.tag_code,
            tag_des: this.form.tag_des,
            block_name: this.form.block_name,
            block_addr: this.form.block_addr,
            data_type: this.form.data_type,
            start_addr: this.form.start_addr,
            data_length: this.form.data_length,
            data_bit: this.form.data_bit,
            opc_addr: this.form.opc_addr,
            opc_demo_addr: this.form.opc_demo_addr,
            data_access: this.form.data_access,
            pub_flag: this.form.pub_flag,
            fastpic_save_flag: this.form.fastpic_save_flag,
            tag_attr_else: this.form.tag_attr_else,
            data_format: this.form.data_format,
            change_save_flag: this.form.change_save_flag,
            pre_alarm_flag: this.form.pre_alarm_flag,
            upper_limit: this.form.upper_limit,
            down_limit: this.form.down_limit,
            tag_attr: this.form.tag_attr,
            opc_ua_flag: this.form.opc_ua_flag,
            enable_flag: this.form.enable_flag
          }

          // 新增
          if (this.form.tag_id === undefined || this.form.tag_id.length <= 0) {
            insScadaTag(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  this.dialogVisbleSyncFrom = false
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updScadaTag(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    toTableButDelete(data) {
      this.$confirm(`此操作将永久删除标签${data.tag_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            tag_id: data.tag_id
          }
          delScadaTag(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                // 查询
                this.toButQuery()
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'tag_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.tableOverflow {
  .cell {
    white-space: nowrap;
    text-overflow: initial;
  }
}
</style>
