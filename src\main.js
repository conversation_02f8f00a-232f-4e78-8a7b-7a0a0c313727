// import { createApp } from "vue";
// import App from "./App.vue";
// import router from "./router";
// import store from "./store";

// createApp(App)
//  .use(store)
//  .use(router)
//  .mount("#app");

import Vue from 'vue' // 语句相当于引入vue（这一般是路径）然后给它起个名字叫做Vue

import Cookies from 'js-cookie' // 轻量级的 cookie 的 API : js-cookie

import './assets/styles/bootstrap.min.css'

// Normalize.css只是一个很小的css文件，但它在磨人的HTML元素样式上提供了跨浏览器的高度一致性。
// 相比于传统的CSS reset,Normalize.css是一种现代的、为HTML5准备的优质替代方案。
// 总之，Normalize.css是一种CSS reset的替代方案。
import 'normalize.css/normalize.css'

// mavon-editor是一款基于vue的markdown编辑器，比较适合博客系统。
// import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

// ui框架--elementui
import Element from 'element-ui'
import './assets/styles/element-variables.scss'
import './assets/styles/element-ui.scss'

// 徐工公共样式
import './assets/styles/dcs_hmi.less'

// 数据字典
import dict from './components/Dict'
// 系统参数
import sysParameter from './components/SysParameter'
// 权限指令
import permission from './components/Permission'

// global css
import './assets/styles/index.scss'

// 代码高亮
import VueHighlightJS from 'vue-highlightjs'
import 'highlight.js/styles/atom-one-dark.css'

import App from './App'
import store from './store'
import router from './router/routers'

import './assets/icons' // icon
import './router/index' // permission control
import 'echarts-gl'

import JsonExcel from 'vue-json-excel' // 纯前端导出
Vue.component('downloadExcel', JsonExcel)

import './components/Upload/directives'
import uploader from 'vue-simple-uploader'
// 快速编码组件
import fastCode from '@/views/components/core/fastcode/index'
Vue.component('fastCode', fastCode)
// scada标签选择
import tagSelect from '@/views/components/core/tag/index'
Vue.component('tagSelect', tagSelect)

import splitPane from 'vue-splitpane'
Vue.component('split-pane', splitPane)

import vueAwesomeSwiper from 'vue-awesome-swiper'

import 'swiper/swiper-bundle.css'
Vue.use(vueAwesomeSwiper)
import VideoPlayer from 'vue-video-player'
import 'vue-video-player/src/custom-theme.css'
import 'video.js/dist/video-js.css'

Vue.use(VideoPlayer)

import vPlayBack from 'v-playback'
Vue.use(vPlayBack)

// 全局引入echarts
import * as echarts from 'echarts'
// 需要挂载到Vue原型上
Vue.prototype.$echarts = echarts

Vue.prototype.$EventBus = new Vue()

// 引入中英文切换插件vue-i18n
import VueI18n from 'vue-i18n'

Vue.use(VueI18n) // 挂载

import dayjs from 'dayjs'
// 时间过滤器
Vue.filter('dateFormat', (time) => {
  const dtStr = dayjs(time).format('YYYY-MM-DD HH:mm:ss')
  return dtStr
})
const i18n = new VueI18n({
  locale: localStorage.getItem('language') || 'zh-CN', // 语言标识
  messages: require('./lang/index').default, // 使用模块化的语言文件
  // 加上这个，否则会报警告[vue-i18n] Value of key xxx is not a string or function !
  silentTranslationWarn: true
})
// 大屏使用
// import AsyncComputed from 'vue-async-computed'

// // iView
// import iView from 'iview';
// // import 'iview/dist/styles/iview.css';

// //执行：npm install less-loader@4.1.0 --save
// import './assets/theme/index.less';

// import 'animate.css'

// // JQuery
// import $ from 'jquery'
// require('webpack-jquery-ui');
// require('webpack-jquery-ui/css');

// // 引入Echart4.x组件
// import echarts from 'echarts'

// // 引入UEditor组件
// import VueUeditorWrap from 'vue-ueditor-wrap'

// // 自动扫描组件
// import './components/index.js'

// // Axios封装类
// import AxiosPlugin from './utils/AxiosPlugin'

// // 全局工具类
// import PnUtil from './utils/PnUtil'
// import PnDict from './utils/PnDict'
// import PnChartDict from './utils/PnChartDict'
// import PnDesigner from './utils/PnDesigner'
// import EventBus from './utils/EventBus'

// import PnApi from './api/PnApi'

// // 引入全局样式表
// import './assets/css/pnStyle.css'

// // 引入自定义图标库
// import './assets/iconfont/iconfont.css'

// // 引入自定义指令
// import './directives/directives'

// Vue.use(iView, {
//  transfer: true
// });
// Vue.use(AxiosPlugin);
// Vue.use(AsyncComputed);

// Vue.component('vue-ueditor-wrap', VueUeditorWrap);

// // 注册全局变量
// Vue.prototype.$PnUtil = PnUtil;
// Vue.prototype.$PnDict = PnDict;
// Vue.prototype.$PnChartDict = PnChartDict;
// Vue.prototype.$PnDesigner = PnDesigner;
// Vue.prototype.$EventBus = EventBus;
// Vue.prototype.$PnApi = PnApi;
// Vue.prototype.$Echarts = echarts;
// Vue.prototype.$ = $;

// // 给数组注册去重插入函数
// Array.prototype.pushNoRepeat = function () {
//  for(let i=0; i < arguments.length; i++) {
//    let ele = arguments[i];
//    if(this.indexOf(ele) == -1){
//      this.push(ele);
//    }
//  }
// };

Vue.use(uploader)

// 全局方法定义。也就是说，定义以后，你可以在这个Vue项目的任意地方使用该组件
Vue.use(VueHighlightJS)
// Vue.use(mavonEditor)
Vue.use(permission)
Vue.use(dict)
Vue.use(sysParameter)
Vue.use(Element, {
  size: Cookies.get('size') || 'small', // set element-ui default size
  i18n: function(key, value) {
    if (localStorage.getItem('language') !== 'en-US') {
      return
    }
    const val = i18n.t(key, value)
    if (val !== null && val !== undefined) {
      //  return val.match(/\.(\w+)$/)[1]
      const match = val.match(/\.(\w+)$/)
      if (match) {
        const lastPart = match[1]
        if (value) {
          return lastPart + ' ' + value.total
        } else {
          return lastPart
        }
      }
    }
    return ''
  }
})

// 阻止启动生产消息，常用作指令
Vue.config.productionTip = false
// 全局变量获取存储在浏览器中语言方式
// Vue.prototype.LANGUAGE = localStorage.getItem('language') && localStorage.getItem('language').split('-')[1] || 'zh-CN'

new Vue({
  el: '#app', // 这个和index.html中的相挂钩
  router,
  store,
  render: h => h(App),
  i18n
})
