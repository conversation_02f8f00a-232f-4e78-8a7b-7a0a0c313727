<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产线：">
                <!-- 产线： -->
                <el-select v-model="prod_line_id" filterable clearable size="small">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位：">
                <!-- 工位： -->
                <el-select v-model="station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id+''" />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
      <el-descriptions class="margin-top" :column="1" border>
        <el-descriptions-item v-for="(item,index) in StationConfigData" :key="index" label-class-name="table-descriptions-label">
          <template slot="label">
            <i class="el-icon-tickets" />
            {{ item.config_des }}
          </template>
          <template v-if="item.data_type==='Bool'">
            <el-switch v-model="item.tag_value" active-value="1" inactive-value="0" active-color="#13ce66" inactive-color="#ff4949" @change="handleBoolWrite(item)" />
          </template>
          <template v-else>
            <el-button size="small" type="primary" plain @click="openTagWrite(item)">写入</el-button>
          </template>
          &nbsp;&nbsp;&nbsp;&nbsp;{{ item.tag_value }}
        </el-descriptions-item>
      </el-descriptions>
      <el-dialog :title="currentRow.config_des" width="450px" :visible.sync="tagWriteDialogVisible" append-to-body>
        <el-input ref="tagWriteValue" v-model="tagWriteValue" clearable size="medium" style="width: 100%" />
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible = false">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite('String')">写入</el-button>
        </div>
      </el-dialog>
      <el-dialog :title="currentRow.config_des" width="550px" :visible.sync="tagWriteDialogVisible1" append-to-body>
        <template v-for="(item,index) in data1">
          <div :key="index" style="margin-bottom:5px;">
            <el-input v-model="item.key" clearable size="medium" style="width: 170px">
              <template slot="prepend">Key</template>
            </el-input>
            <el-input v-model="item.value" clearable size="medium" style="width: 170px">
              <template slot="prepend">Value</template>
            </el-input>
            <el-button-group>
              <el-button type="primary" icon="el-icon-edit" @click="handleAddData1()">新增</el-button>
              <el-button type="primary" icon="el-icon-delete" @click="handleDelData1(item.id)">删除</el-button>
            </el-button-group>
          </div>
        </template>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible1 = false">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite('Json')">写入</el-button>
        </div>
      </el-dialog>
      <el-dialog :title="currentRow.config_des" width="700px" :visible.sync="tagWriteDialogVisible2" append-to-body>
        <el-table border @header-dragend="crud.tableHeaderDragend()" :data="data2" style="width: 100%" height="350px" border :stripe="true" :header-cell-style="{ background: '#F1F4F7', color: '#757575' }">
          <el-table-column  label="操作" width="100" align="center">
            <template slot-scope="scope">
              <div>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-plus"
                  circle
                  @click="handleAddData2"
                />
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="small"
                  circle
                  @click="handleDelData2(scope.row.id)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column  label="接口编码">
            <template slot-scope="scope">
              <el-input v-model="scope.row.func_code" clearable size="mini" />
            </template>
          </el-table-column>
          <el-table-column  label="接口描述">
            <template slot-scope="scope">
              <el-input v-model="scope.row.func_des" clearable size="mini" />
            </template>
          </el-table-column>
          <el-table-column  label="NG次数">
            <template slot-scope="scope">
              <el-input v-model="scope.row.func_ng_count" clearable size="mini" />
            </template>
          </el-table-column>
          <el-table-column  label="同步" width="70">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.func_sync" active-value="Y" inactive-value="N" active-color="#13ce66" inactive-color="#ff4949" />
            </template>
          </el-table-column>
          <el-table-column  label="续传" width="70">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.func_continue" active-value="Y" inactive-value="N" active-color="#13ce66" inactive-color="#ff4949" />
            </template>
          </el-table-column>
          <el-table-column  label="启用" width="70">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.enable_flag" active-value="Y" inactive-value="N" active-color="#13ce66" inactive-color="#ff4949" />
            </template>
          </el-table-column>
        </el-table>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible2 = false">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite('Interf')">写入</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { selStationConfig } from '@/api/eap/eapFmodConfig'
import crudProdLine from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'STATION_SET',
  components: { },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      prod_line_id: '',
      station_id: '',
      prodLineData: [],
      stationData: [],
      StationConfigData: [
        // { config_des: 'Panel模式(1有/0无)', client_code: '', group_code: '', tag_code: '', data_type: 'Bool', tag_key: 'A/B/C', tag_value: '0' },
        // { config_des: '设备模式', client_code: '', group_code: '', tag_code: '', data_type: 'String', tag_key: 'A/B/C', tag_value: '123' },
        // { config_des: '下发参数', client_code: '', group_code: '', tag_code: '', data_type: 'Json', tag_key: 'A/B/C', tag_value: '{"a":"123","b":"33"}' }
        // { config_des: '接口配置', client_code: '', group_code: '', tag_code: '', data_type: 'Interf', tag_key: 'A/B/C', tag_value: '[]' }
      ],
      data1: [],
      data2: [],
      currentRow: {},
      tagWriteDialogVisible: false,
      tagWriteValue: '',
      tagWriteDialogVisible1: false,
      tagWriteDialogVisible2: false,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      }
    }
  },
  watch: {
    prod_line_id: {
      handler() {
        this.getStationData()
      }
    },
    station_id: {
      handler() {
        this.getCellIp()
      }
    }
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {
    crudProdLine
      .sel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y'
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    getStationData() {
      this.station_id = ''
      this.stationData = []
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: this.prod_line_id
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getCellIp() {
      var cellId = 0
      const stationInfo = this.stationData.filter(item => item.station_id === parseInt(this.station_id))
      if (stationInfo.length > 0) {
        cellId = stationInfo[0].cell_id
      }
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cellId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getStationConfigData()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getStationConfigData() {
      this.StationConfigData = []
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.station_id
      }
      selStationConfig(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.StationConfigData = JSON.parse(defaultQuery.result)
            this.getTagValue()
            this.toStartWatch()
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getTagValue() {
      var readTagArray = []
      for (var i = 0; i < this.StationConfigData.length; i++) {
        var readTag = {}
        readTag.tag_key = this.StationConfigData[i].tag_key
        readTagArray.push(readTag)
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              for (var i = 0; i < result.length; i++) {
                var tag_key = result[i].tag_key
                var tag_value = result[i].tag_value === undefined ? '' : result[i].tag_value
                this.StationConfigData.filter(item => item.tag_key === tag_key)[0].tag_value = tag_value
              }
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    openTagWrite(item) {
      this.currentRow = item
      if (item.data_type === 'String') {
        this.tagWriteValue = ''
        this.$nextTick(x => {
          this.$refs.tagWriteValue.focus()
        })
        this.tagWriteDialogVisible = true
      } else if (item.data_type === 'Json') {
        if (item.tag_value === '') {
          this.data1 = [{ id: +new Date(), key: '', value: '' }]
        } else {
          this.data1 = []
          const data = JSON.parse(item.tag_value)
          var index = 0
          Object.keys(data).forEach(key => {
            index++
            this.data1.push({ id: +new Date() + index, key: key, value: data[key] })
            console.log(this.data1)
          })
        }
        this.tagWriteDialogVisible1 = true
      } else if (item.data_type === 'Interf') {
        if (item.tag_value === '') {
          this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
        } else {
          this.data2 = []
          const data = JSON.parse(item.tag_value)
          if (data.length === 0) {
            this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
          } else {
            this.data2 = data
          }
        }
        this.tagWriteDialogVisible2 = true
      }
    },
    handleAddData1() {
      this.data1.push({ id: +new Date(), key: '', value: '' })
    },
    handleDelData1(id) {
      if (this.data1.length === 1) {
        this.data1.push({ id: +new Date(), key: '', value: '' })
      }
      const index = this.data1.findIndex(item => item.id === id)
      this.data1.splice(index, 1)
    },
    handleAddData2() {
      this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
    },
    handleDelData2(id) {
      if (this.data2.length === 1) {
        this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
      }
      const index = this.data2.findIndex(item => item.id === id)
      this.data2.splice(index, 1)
    },

    handleTagWrite(type) {
      var sendJson = {}
      var rowJson = []
      if (type === 'String') {
        var newRow = {
          TagKey: this.currentRow.tag_key,
          TagValue: this.tagWriteValue
        }
        rowJson.push(newRow)
      } else if (type === 'Json') {
        var data = {}
        this.data1.forEach(item => {
          if (item.key !== '') {
            data[item.key] = item.value
          }
        })
        rowJson.push({
          TagKey: this.currentRow.tag_key,
          TagValue: JSON.stringify(data)
        })
      } else if (type === 'Interf') {
        console.log(JSON.stringify(this.data2))
        rowJson.push({
          TagKey: this.currentRow.tag_key,
          TagValue: JSON.stringify(this.data2)
        })
      }
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.currentRow.tag_key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },
    handleBoolWrite(item) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: item.tag_key,
        TagValue: item.tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + item.tag_key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }

      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        for (var i = 0; i < this.StationConfigData.length; i++) {
          // 订阅主题
          this.topicSubscribe('SCADA_CHANGE/' + this.StationConfigData[i].tag_key)
        }
        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          var TagKey = jsonData.TagKey
          var TagNewValue = jsonData.TagNewValue
          const tagInfo = this.StationConfigData.filter(item => item.tag_key === TagKey)
          if (tagInfo.length > 0) {
            tagInfo[0].tag_value = TagNewValue
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 550px;
}
</style>
