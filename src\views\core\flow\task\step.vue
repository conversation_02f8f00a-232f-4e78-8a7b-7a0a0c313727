<template>
  <el-drawer id="stepDrawer" :title="nodeForm.step_mod_des" :wrapper-closable="true" :visible.sync="drawerVisible" direction="rtl" size="750px" append-to-body @closed="drawerClose">
    <el-tabs v-model="activeName" @tab-click="handletabsclick" class="elTabs">
      <el-tab-pane :label="$t('lang_pack.mainmain.log')" name="log">
        <log v-if="drawerVisible" ref="log" :readonly="readonly" :step_mod_id="nodeForm.step_mod_id" :cel_api="cel_api" :me_flow_task_id="me_flow_task_id" />
      </el-tab-pane>
      <el-tab-pane :label="$t('lang_pack.mainmain.attr')" name="step">
        <div id="scrollbar" :style="'height:' + height + 'px'">
          <el-scrollbar style="height: 100%">
            <el-form ref="nodeForm" class="el-form-wrap" :inline="true" :model="nodeForm" size="small" label-width="180px">
              <el-form-item v-if="1 == 0" display:none>
                <el-input v-model="nodeForm.id" display:none />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.inputParams')">
                <el-input v-model="nodeForm.in_parameter" :disabled="readonly">
                  <el-button slot="append" icon="el-icon-edit" @click="showParameter('in')" />
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.outParams')">
                <el-input v-model="nodeForm.out_parameter" :disabled="readonly">
                  <el-button slot="append" icon="el-icon-edit" @click="showParameter('out')" />
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.subprocess')" prop="flow_mod_sub_id">
                <el-select v-model="nodeForm.flow_mod_sub_id" filterable :disabled="readonly">
                  <el-option v-for="item in subData" :key="item.subId_stepId" :label="item.describe" :value="item.subId_stepId" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.stepDes')" prop="step_mod_des">
                <el-input v-model="nodeForm.step_mod_des" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.sort')" prop="step_mod_index">
                <el-input v-model.number="nodeForm.step_mod_index" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.stepType')">
                <el-select v-model="nodeForm.step_mod_attr" :disabled="readonly">
                  <el-option v-for="item in [{ label: $t('lang_pack.mainmain.startStep'), id: 'FIRST' }, { label: $t('lang_pack.mainmain.endStep'), id: 'LAST' }, { label: $t('lang_pack.mainmain.generalSteps'), id: 'NORMAL' }]" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.methodName')">
                <el-input v-model="nodeForm.step_mod_function_dll" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.nextOK')">
                <el-select v-model="nodeForm.ok_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.stepToNg')">
                <el-select v-model="nodeForm.ng_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.afterLoop')" prop="cycle_next_step_id_list">
                <el-select v-model="nodeForm.cycle_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.mainmain.selectNext1')" prop="choice1_next_step_id_list">
                <el-select v-model="nodeForm.choice1_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.selectNext2')" prop="choice2_next_step_id_list">
                <el-select v-model="nodeForm.choice2_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.selectNext3')" prop="choice3_next_step_id_list">
                <el-select v-model="nodeForm.choice3_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.selectNext4')" prop="choice4_next_step_id_list">
                <el-select v-model="nodeForm.choice4_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.selectNext5')"prop="choice5_next_step_id_list">
                <el-select v-model="nodeForm.choice5_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.cancelNext')">
                <el-select v-model="nodeForm.abort_next_step_id_list" filterable multiple :disabled="readonly">
                  <el-option v-for="item in stepData" :key="item.subId_stepId + ''" :label="item.name + ' ' + item.describe" :value="item.subId_stepId + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.retryCount')" prop="ng_retry_count">
                <el-input v-model.number="nodeForm.ng_retry_count" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.limiTime')" prop="limit_time">
                <el-input v-model.number="nodeForm.limit_time" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <el-select v-model="nodeForm.enable_flag" :disabled="readonly">
                  <el-option v-for="item in [{ label: $t('lang_pack.vie.Yes'), id: 'Y' }, { label: $t('lang_pack.vie.NO'), id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.colorStateOK')">
                <el-color-picker v-model="nodeForm.ok_control_color" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.colorStateNG')">
                <el-color-picker v-model="nodeForm.ng_control_color" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.colorStateCancel')">
                <el-color-picker v-model="nodeForm.abort_control_color" :disabled="readonly" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.mainmain.colorStateRetry')">
                <el-color-picker v-model="nodeForm.retry_control_color" :disabled="readonly" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.initColor')">
                <el-color-picker v-model="nodeForm.color" :disabled="readonly" />
              </el-form-item>
            </el-form>
            <el-dialog append-to-body :title="parameterTitle" width="50%" top="20px" :visible.sync="parameterDialogVisible" :close-on-click-modal="true">
              <el-input v-model="jsonParameter" :readonly="true" type="textarea" :rows="20" />
              <span style="color:red;">{{ jsonErrorMsg }}</span>
            </el-dialog>
            <el-divider />
          </el-scrollbar>
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="属性组" name="attr" :disabled="this.nodeForm.subId_stepId === 0">
        <div id="scrollbar1" :style="'height:' + height + 'px'">
          <el-scrollbar style="height: 100%">
            <attrGroup v-if="attrGroupShow" ref="attrGroup" :readonly="readonly" :step_mod_id="nodeForm.step_mod_id" :flow_main_id="flow_main_id" />
          </el-scrollbar>
        </div>
      </el-tab-pane> -->
      <el-tab-pane :label="$t('lang_pack.mainmain.code')" name="code" :disabled="this.nodeForm.subId_stepId === 0" class="elTabPane">
        <functionI ref="functionI" :readonly="readonly" :step_mod_id="nodeForm.step_mod_id" />
      </el-tab-pane>
      <el-tab-pane :label="$t('lang_pack.mainmain.conditionGroup')" name="condition">
        <conditionGroup v-if="drawerVisible" ref="conditionGroup" :flow_main_id="flow_main_id" :step_mod_id="nodeForm.step_mod_id" />
      </el-tab-pane>
      <el-tab-pane :label="$t('lang_pack.commonPage.operate')" name="operate">
        <operate ref="operate"  :readonly="readonly" :flow_mod_main_id="flow_mod_main_id"
         :me_flow_task_id="me_flow_task_id" :flow_main_id="flow_main_id" 
         :subId_stepId="nodeForm.flowPathId" 
         :nodes="nodes"
         :step_mod_id="nodeForm.step_mod_id" @operateFlow="drawerVisible = false" />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script>
import axios from 'axios'
import { sel } from '@/api/core/flow/rcsFlowModSubStep'
import attrGroup from '@/views/core/flow/task/attr'
import functionI from '@/views/core/flow/task/function'
import conditionGroup from '@/views/core/flow/task/condition'
import log from '@/views/core/flow/task/log'
import operate from '@/views/core/flow/task/operate'
// import Cookies from 'js-cookie'
import Vue from 'vue'
const defaultForm = {
  name: null,
  id: null,
  subId_stepId: null,
  width: null,
  height: null,
  x: null,
  y: null,
  type: null,
  imgId: null,
  color: null,
  describe: null,
  strokeWidth: null,
  step_mod_id: 0,
  flow_mod_sub_id: 0,
  step_mod_code: '',
  step_mod_des: '',
  step_mod_index: 1,
  step_mod_attr: 'NORMAL',
  step_mod_way: 'AUTO',
  step_mod_function_dll: '',
  step_mod_function_attr: '',
  ok_next_step_id_list: '',
  ok_wait_manual_flag: 'N',
  ok_manual_step_url: '',
  ok_manual_step_para: '',
  ng_retry_count: -1,
  ng_next_step_id_list: '',
  ng_wait_manual_flag: 'N',
  ng_manual_step_url: '',
  ng_manual_step_para: '',
  allow_abort_flag: 'N',
  allow_pass_flag: 'N',
  abort_next_step_id_list: '',
  control_ico: '',
  control_type: '-1',
  control_location_x: 0,
  control_location_y: 0,
  ok_control_color: '#37ED13',
  ok_wait_control_color: '#0E9A23',
  ng_control_color: '#EA1F1F',
  ng_wait_control_color: '#B91212',
  abort_control_color: '#F1930F',
  retry_control_color: '#15E4DD',
  limit_time: 0,
  cycle_next_step_id_list: '',
  enable_flag: 'Y',
  choice1_next_step_id_list: '',
  choice2_next_step_id_list: '',
  choice3_next_step_id_list: '',
  choice4_next_step_id_list: '',
  choice5_next_step_id_list: '',
  in_parameter: '',
  out_parameter: ''
}
export default {
  components: {
    attrGroup,
    functionI,
    conditionGroup,
    log,
    operate
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    flow_main_id: {
      type: [String, Number],
      default: -1
    },
    cel_api: {
      type: String,
      default: ''
    },
    flow_mod_main_id:{
      type:[String,Number],
      default:'',
    },
    me_flow_task_id: {
      type: String,
      default: ''
    },
    nodes:{
      type:Array,
      default:[]
    }
  },
  data: function() {
    return {
      height: document.documentElement.clientHeight - 150,
      attrGroupShow: false,
      codeEditorShow: false,
      customPopover: false,
      activeName: 'log',
      nodeForm: {},
      thisH: null,
      subData: [],
      stepData: [],
      drawerVisible: false,
      parameterDialogVisible: false,
      parameterTitle: '',
      jsonParameter: '',
      jsonErrorMsg: '',
      flowTaskStepInfo: {}
    }
  },
  watch: {
    visible: {
      immediate: true,
      deep: true,
      handler() {
        this.drawerVisible = this.visible
      }
    }
  },
  mounted() {
    console.log(this.cel_api)
    window.addEventListener('resize', () => (this.height = document.documentElement.clientHeight - 150), false)
  },
  methods: {
    handletabsclick() {
      this.$refs.functionI.$refs.codeEditor._refresh()
    },
    drawerClose() {
      this.attrGroupShow = false
      this.codeEditorShow = false
      this.$emit('update:visible', false)
    },
    editNode(node, thisH, flow_mod_main_id,flowPathId) {
      this.thisH = thisH
      this.subData = thisH.internalNodes.filter(item => item.type === 'sub' && item.subId_stepId !== 0)
      this.subData.push({ subId_stepId: 0, describe: this.$t('lang_pack.mainmain.notHave') })
      this.subData.push({ subId_stepId: -1, describe: this.$t('lang_pack.mainmain.endAll')})
      this.stepData = thisH.internalNodes.filter(item => (item.type === 'step' || item.type === 'judge') && item.subId_stepId !== 0)
      this.stepData.push({ subId_stepId: 0, describe: this.$t('lang_pack.mainmain.notHave'), name: '' })
      this.stepData.push({ subId_stepId: -1, describe: this.$t('lang_pack.mainmain.endAll'), name: '' })
      for (const key in defaultForm) {
        if (this.nodeForm.hasOwnProperty(key)) {
          this.nodeForm[key] = defaultForm[key]
        } else {
          Vue.set(this.nodeForm, key, defaultForm[key])
        }
      }
      this.nodeForm.flowPathId = flowPathId
      this.nodeForm.id = node.id
      this.nodeForm.subId_stepId = node.subId_stepId
      this.nodeForm.step_mod_id = node.subId_stepId
      this.nodeForm.step_mod_index = parseInt(node.name.split('-')[1])
      this.nodeForm.type = node.type
      this.nodeForm.width = node.width
      this.nodeForm.height = node.height
      this.nodeForm.control_type = node.type
      this.nodeForm.control_location_x = parseInt(node.x)
      this.nodeForm.control_location_y = parseInt(node.y)
      this.nodeForm.control_ico = node.imgId
      this.nodeForm.color = node.color
      this.nodeForm.step_mod_des = node.describe
      this.nodeForm.strokeWidth = node.strokeWidth
      if (node.subId_stepId !== undefined && node.subId_stepId !== 0) {
        const query = {
          step_mod_id: node.subId_stepId
        }
        sel(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.nodeForm.step_mod_id = defaultQuery.data[0].step_mod_id
                this.nodeForm.flow_mod_sub_id = defaultQuery.data[0].flow_mod_sub_id
                this.nodeForm.step_mod_des = defaultQuery.data[0].step_mod_des
                this.nodeForm.step_mod_index = defaultQuery.data[0].step_mod_index
                this.nodeForm.step_mod_attr = defaultQuery.data[0].step_mod_attr
                this.nodeForm.step_mod_function_dll = defaultQuery.data[0].step_mod_function_dll
                this.nodeForm.step_mod_function_attr = defaultQuery.data[0].step_mod_function_attr
                this.nodeForm.ok_next_step_id_list = defaultQuery.data[0].ok_next_step_id_list === '' ? '' : defaultQuery.data[0].ok_next_step_id_list.split(',')
                this.nodeForm.ng_retry_count = defaultQuery.data[0].ng_retry_count
                this.nodeForm.ng_next_step_id_list = defaultQuery.data[0].ng_next_step_id_list === '' ? '' : defaultQuery.data[0].ng_next_step_id_list.split(',')
                this.nodeForm.abort_next_step_id_list = defaultQuery.data[0].abort_next_step_id_list === '' ? '' : defaultQuery.data[0].abort_next_step_id_list.split(',')
                this.nodeForm.ok_control_color = defaultQuery.data[0].ok_control_color
                this.nodeForm.ng_control_color = defaultQuery.data[0].ng_control_color
                this.nodeForm.abort_control_color = defaultQuery.data[0].abort_control_color

                this.nodeForm.retry_control_color = defaultQuery.data[0].retry_control_color
                this.nodeForm.limit_time = defaultQuery.data[0].limit_time
                this.nodeForm.cycle_next_step_id_list = defaultQuery.data[0].cycle_next_step_id_list === '' ? '' : defaultQuery.data[0].cycle_next_step_id_list.split(',')
                this.nodeForm.enable_flag = defaultQuery.data[0].enable_flag

                this.nodeForm.choice1_next_step_id_list = defaultQuery.data[0].choice1_next_step_id_list === '' ? '' : defaultQuery.data[0].choice1_next_step_id_list.split(',')
                this.nodeForm.choice2_next_step_id_list = defaultQuery.data[0].choice2_next_step_id_list === '' ? '' : defaultQuery.data[0].choice2_next_step_id_list.split(',')
                this.nodeForm.choice3_next_step_id_list = defaultQuery.data[0].choice3_next_step_id_list === '' ? '' : defaultQuery.data[0].choice3_next_step_id_list.split(',')
                this.nodeForm.choice4_next_step_id_list = defaultQuery.data[0].choice4_next_step_id_list === '' ? '' : defaultQuery.data[0].choice4_next_step_id_list.split(',')
                this.nodeForm.choice5_next_step_id_list = defaultQuery.data[0].choice5_next_step_id_list === '' ? '' : defaultQuery.data[0].choice5_next_step_id_list.split(',')
                this.getFlowTaskStepInfo()
              }
            }
          })
          .catch(() => {
            this.$message({
              message: this.$t('lang_pack.vie.queryException'),
              type: 'error'
            })
          })
      }
      console.log(this.nodeForm.subId_stepId)
      this.attrGroupShow = true
      this.codeEditorShow = true
    },
    showParameter(type) {
      this.jsonParameter = '{}'
      this.jsonErrorMsg = ''
      if (type === 'in') {
        this.parameterTitle = this.$t('lang_pack.mainmain.inputParams')
        this.checkJsonParameter(this.nodeForm.in_parameter)
      } else {
        this.parameterTitle = this.$t('lang_pack.mainmain.outParams')
        this.checkJsonParameter(this.nodeForm.out_parameter)
      }
      this.parameterDialogVisible = true
    },
    checkJsonParameter(text) {
      if (text !== '') {
        try {
          var _json = JSON.parse(text)

          if (typeof _json === 'object' && _json) {
            this.jsonParameter = JSON.stringify(_json, null, 4)
          }
        } catch (ex) {
          this.jsonErrorMsg = ex
          this.jsonParameter = text
        }
      }
    },
    getFlowTaskStepInfo() {
      var method = '/cell/core/flow/CoreFlowStepInfoSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cel_api.split(':')[1] + method
      } else {
        path = 'http://' + this.cel_api + method
      }
      const data = {
        step_mod_id: this.nodeForm.step_mod_id,
        me_flow_task_id: this.me_flow_task_id
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            if (defaultQuery.data.count > 0) {
              this.flowTaskStepInfo = defaultQuery.data.data[0]
              this.nodeForm.in_parameter = this.flowTaskStepInfo.in_paras
              this.nodeForm.out_parameter = this.flowTaskStepInfo.out_paras
            }
          } else {
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    }
  }
}
</script>
<style lang="less" scoped>
#scrollbar,
#scrollbar1 {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
::v-deep .el-textarea__inner {
  color: #f8c555;
  background-color: #2d2d2d;
}
::v-deep .elTabs{
  height: calc(100% - 50px);
  .el-tabs__content{
    ::-webkit-scrollbar {
    /*隐藏滚轮*/
    display: none;
  }
  }
}

</style>
