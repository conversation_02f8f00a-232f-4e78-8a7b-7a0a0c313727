<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24" class="elnopadding">
          <div class="carInfoTable">
            <el-table
              :data="queue"
              style="width: 100%"
            >
              <el-table-column
                prop="vin"
                label="VIN"
                width="300"
              />
              <el-table-column
                prop="pro_order"
                label="生产订单号"
                width="240"
              />
              <el-table-column
                prop="rfid"
                label="RFID条码"
                width="260"
              />
              <el-table-column
                prop="status"
                label="状态"
                width="140"
              />
              <el-table-column
                prop="material"
                label="物料号"
              />
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getStationTime } from '@/api/pmc/sysTime'
import headlbg from '@/assets/images/headlbg.png'
export default {
  name: 'DPqueue',
  data() {
    return {
      prod_line_des: '底盘喷漆生产队列',
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      headlbg: headlbg,
      queue: [{
        vin: 'HBCE4CDG2N0003001',
        pro_order: '15000000000',
        rfid: '00013122081200100110',
        status: '已完成',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDG2N0003002',
        pro_order: '15000000008',
        rfid: '00013122081200100310',
        status: '已完成',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDG4N0003003',
        pro_order: '15000000006',
        rfid: '00013122081200200210',
        status: '已完成',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDG6N0003004',
        pro_order: '15000000007',
        rfid: '00013122081200200110',
        status: '已完成',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDG8N0003005',
        pro_order: '15000000004',
        rfid: '00013122081100100110',
        status: '已完成',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDGXN0003006',
        pro_order: '15000000005',
        rfid: '00013122081100100210',
        status: '待生产',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDG1N0003007',
        pro_order: '15000000009',
        rfid: '00013122081200100210',
        status: '待生产',
        material: 'T1H4461210-A21A001'
      }, {
        vin: 'HBCE4CDG3N0003008',
        pro_order: '15000000003',
        rfid: '00013122081100100310',
        status: '待生产',
        material: 'T1H4461210-A21A001'
      }]
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    setInterval(() => {
      this.initstationTime()
    }, 1000)
  },
  mounted() {
  },
  methods: {
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
body{
  background: #1d3a6a !important;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.wraptext p::before{
    content: '';
    width: 10px;
    height: 10px;
    background-color: #ff0000;
    display: block;
    border-radius: 50%;
    margin-right: 10px;
}
.cardheadbg{
  background-color: #031c45;
      // background: -webkit-gradient( linear, left bottom, left top, color-stop(0.02, #031c45), color-stop(0.51, #194998), color-stop(0.87, #031c45) );
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .el-card{
  border-radius: 0;
}
::v-deep .el-table th{
    background-color: #6b88b8 !important;
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px #000;
}
::v-deep .el-table--enable-row-transition .el-table__body td{
  background-color: #1d3a6a !important;
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px #000;
    text-align: center;
}
::v-deep .el-table th.el-table__cell>.cell{
  text-align: center;
}
::v-deep .el-table__body{
  min-height: calc(100vh - 70px) !important;
  padding-bottom: 20px;
    background: #1d3a6a;
}
</style>
