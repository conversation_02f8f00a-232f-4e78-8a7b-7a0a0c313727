<template>
  <textarea ref="textarea" v-model="codeContent" />
</template>

<script type="text/ecmascript-6">
import { getHintList } from '@/components/CodeEditor/data'
// 引入全局实例
import _CodeMirror from 'codemirror'
// 核心样式
import 'codemirror/lib/codemirror.css'
// 引入 主题 后还需要在 options 中指定主题才会生效
import 'codemirror/theme/cobalt.css'
import 'codemirror/theme/idea.css'
// 需要引入具体的语法高亮库才会有对应的语法高亮效果
// codemirror 官方其实支持通过 /addon/mode/loadmode.js 和 /mode/meta.js 来实现动态加载对应语法高亮库
// 但 vue 貌似没有无法在实例初始化后再动态加载对应 JS ，所以此处才把对应的 JS 提前引入
import 'codemirror/mode/clike/clike.js' // C#
// import 'codemirror/mode/javascript/javascript.js'
// import 'codemirror/mode/css/css.js'
// import 'codemirror/mode/xml/xml.js'
// import 'codemirror/mode/markdown/markdown.js'
// import 'codemirror/mode/python/python.js'
// import 'codemirror/mode/r/r.js'
// import 'codemirror/mode/shell/shell.js'
// import 'codemirror/mode/sql/sql.js'
// import 'codemirror/mode/swift/swift.js'
// import 'codemirror/mode/vue/vue.js'
import 'codemirror/addon/edit/closebrackets.js'
import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/addon/hint/javascript-hint'
import 'codemirror/addon/hint/xml-hint'
import 'codemirror/addon/hint/sql-hint'
import 'codemirror/addon/hint/anyword-hint'

// 代码折叠功能
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/foldgutter.css'

import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/comment-fold'
import 'codemirror/addon/fold/xml-fold'
import 'codemirror/addon/fold/indent-fold'
import 'codemirror/addon/fold/markdown-fold'
import 'codemirror/addon/fold/comment-fold'

// 选中单词后，其他相同单词也高亮
import 'codemirror/addon/search/match-highlighter'
// 高亮光标行
import 'codemirror/addon/selection/active-line'

// 尝试获取全局实例
const CodeMirror = window.CodeMirror || _CodeMirror

// 分割窗口
// import splitPane from 'vue-splitpane'
// import { Splitpanes, Pane } from 'splitpanes'
// import 'splitpanes/dist/splitpanes.css'

export default {
  name: 'CodeMirror',
  components: { },
  // 父组件可以使用 props 把数据传给子组件
  props: {
    // 外部传入的内容，用于实现双向绑定
    value: {
      type: String,
      default: ''
    },
    // 外部传入的语法类型
    language: {
      type: String,
      default: null
    },
    code_content: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      codeContent: '',
      codeTemplate: '',
      // 编辑器实例
      coder: null,
      // 默认配置
      options: {
        // 缩进格式
        tabSize: 2,
        mode: 'text/x-csharp',
        indentUnit: 4,
        indentWithTabs: true,
        // 主题，对应主题库 JS 需要提前引入
        // theme: 'cobalt',
        // 代码折叠
        gutters: ['CodeMirror-lint-markers', 'CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        foldGutter: true, // 启用行槽中的代码折叠
        matchBrackets: true, // 在光标点击紧挨{、]括号左、右侧时，自动突出显示匹配的括号 }、]
        highlightSelectionMatches: {
          minChars: 2,
          trim: true,
          style: 'matchhighlight',
          showToken: false
        }, // 选中单词后，其他相同单词也高亮
        lineNumbers: true, // 显示行号
        lineWrapping: false, // 自动换行
        extraKeys: { 'Ctrl': 'autocomplete' }, // 按键配置
        hintOptions: {
          completeSingle: false,
          hint: this.handleShowHint
        },
        lineWiseCopyCut: true,
        showCursorWhenSelecting: true,
        readOnly: false, // 只读 nocursor
        line: true,
        styleActiveLine: true, // 高亮光标行
        autofocus: true,
        autoCloseBrackets: true // 自动补全括号
        // electricChars: false
      }
    }
  },
  watch: {
    code_content: {
      immediate: true,
      deep: true,
      handler() {
        if (this.coder === null) {
          this.$nextTick(() => {
            if (this.code_content.length === 0) {
              this.coder.setValue(this.codeTemplate)
            } else {
              this.coder.setValue(this.code_content)
            }
          })
        } else {
          if (this.code_content.length === 0) {
            this.coder.setValue(this.codeTemplate)
          } else {
            this.coder.setValue(this.code_content)
          }
        }
      }
    }
  },
  mounted() {
    // 初始化
    this._initialize()
    // 默认C#语法
    // this.coder.setOption('mode', 'text/x-csharp')
    // this.coder.setOption('lineWrapping', false)// 设置自动换行

    // this.coder.setSize('auto', 'auto')// 设置自适应高度
  },
  methods: {
    _refresh() {
      this.$nextTick(() => {
        this.coder.refresh()
      })
    },
    // 初始化
    _initialize() {
      console.log('[_initialize]')

      // 初始化编辑器实例，传入需要被实例化的文本域对象和默认配置
      this.coder = CodeMirror.fromTextArea(this.$refs.textarea, this.options)
      this.coder.on('change', (coder) => {
        this.$emit('change', coder.getValue())
      })
      const that = this
      // 代码自动提示功能
      this.coder.on('inputRead', function() {
        that.coder.showHint()
      })
    },
    handleShowHint(cmInstance, hintOptions) {
      const cursor = cmInstance.getCursor()

      const token = cmInstance.getTokenAt(cursor)
      var found = []
      if (token.string !== '') {
        found = getHintList(token.start, token.string, this)
      }
      return {
        list: found,
        from: {
          ch: token.start, line: cursor.line
        },
        to: {
          ch: token.end, line: cursor.line
        }
      }
    },
    // 提示框显示及样式
    hintRender(element, self, data) {
      const div = document.createElement('div')
      div.setAttribute('class', 'autocomplete-div')

      // 添加弹出框表/字段图标
      let divIcon = ''
      if (data.type === 'table') {
        divIcon = document.createElement('i')
        divIcon.setAttribute('class', 'el-icon-date')
        divIcon.style.color = 'blue'
      } else if (data.type === 'field') {
        divIcon = document.createElement('i')
        divIcon.setAttribute('class', 'el-icon-film')
        divIcon.style.color = 'blue'
      } else {
        divIcon = document.createElement('i')
      }

      const divText = document.createElement('span')
      divText.setAttribute('class', 'autocomplete-name')
      divText.innerText = data.displayText
      divText.style.display = 'inline-block'
      divText.style.overflow = 'hidden'
      divText.style.whiteSpace = 'nowrap'
      divText.style.textOverflow = 'ellipsis'
      divText.style.marginRight = '10px'
      divText.style.lineHeight = '20px'
      divText.style.height = '20px'
      divText.style.fontSize = '14px'

      const divInfo = document.createElement('span')
      divInfo.setAttribute('class', 'autocomplete-hint')
      divInfo.innerText = data.displayInfo
      divInfo.style.display = 'inline-block'
      divInfo.style.float = 'right'
      divInfo.style.color = '#1F8942'
      divInfo.style.maxWidth = '150px'
      divInfo.style.overflow = 'hidden'
      divInfo.style.whiteSpace = 'nowrap'
      divInfo.style.textOverflow = 'ellipsis'
      divInfo.style.marginTop = '5px'

      div.appendChild(divIcon)
      div.appendChild(divText)
      div.appendChild(divInfo)
      element.appendChild(div)
    },
    words(str) {
      var words = str.split(' ')
      for (var i = 0; i < words.length; ++i) console.log({ displayText: words[i], displayInfo: '', text: words[i] })
    }
  }
}
</script>

<style lang="css">
.CodeMirror-code {
	line-height: 19px;
	text-align: left;
}
.code-mode-select {
	position: absolute;
	z-index: 2;
	right: 10px;
	top: 10px;
	max-width: 130px;
}
.CodeMirror{
  height: calc(100vh - 150px) !important;
  overflow-y: auto;
  
}
::v-deep.CodeMirror::-webkit-scrollbar {
    display: block !important;
  }
.cm-matchhighlight {
  background: #9BFF9B !important
}
.CodeMirror-hints {
  z-index: 9999;
}

.cm-s-default .cm-keyword {color: #0000FF !important;}
.cm-s-default .cm-atom {color: #0000FF !important;}
.cm-s-default .cm-number {color: #164 !important;}
.cm-s-default .cm-def {color: #2B91AF !important;}
.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {}
.cm-s-default .cm-variable-2 {color: #05a !important;}
.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #0000FF !important;}
.cm-s-default .cm-comment {color: #008000 !important;}
.cm-s-default .cm-string {color: #a11 !important;}
.cm-s-default .cm-string-2 {color: #f50 !important;}
.cm-s-default .cm-meta {color: #555 !important;}
.cm-s-default .cm-qualifier {color: #555 !important;}
.cm-s-default .cm-builtin {color: #30a !important;}
.cm-s-default .cm-bracket {color: #997 !important;}
.cm-s-default .cm-tag {color: #170 !important;}
.cm-s-default .cm-attribute {color: #00c !important;}
.cm-s-default .cm-hr {color: #999 !important;}
.cm-s-default .cm-link {color: #00c !important;}

.cm-s-default .cm-error {color: #f00 !important;}
</style>
