<template>
  <el-row :gutter="12">
    <el-col :span="monitorSpan">
      <el-table border @header-dragend="crud.tableHeaderDragend()" :data="tableData" style="width: 100%;" height="450px" default-expand-all row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
        <el-table-column  prop="name" :label="$t('lang_pack.logicfunc.attributeGroup')" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            {{ scope.row.name }}
            <el-tooltip v-if="scope.row.remark.length > 0" :content="scope.row.remark" placement="bottom" effect="light">
              <i class="el-icon-info" style="font-size:16px;cursor: pointer;" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column  v-if="monitorSpan === 24" prop="value" :label="$t('lang_pack.logicfunc.value')">
          <template slot-scope="scope">
            {{ getTagValue(scope.row.tag_key) }}
          </template>
        </el-table-column>
        <el-table-column  :label="$t('lang_pack.commonPage.operate')" width="120" align="center" fixed="right">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-button v-if="scope.row.value_type === 'TAG_ID'" size="small" type="text" @click="openTagWrite(scope.row)">{{ $t('lang_pack.monitor.write') }}</el-button>
            <el-button v-if="scope.row.value_type === 'TAG_ID'" size="small" type="text" @click="openTagInfo(scope.row)">{{ monitorSpan === 12 && scope.row.id === currentRow.id ?  $t('lang_pack.commonPage.close') : $t('lang_pack.monitor.attr') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog :title="currentRow.name" width="450px" :visible.sync="tagWriteDialogVisible" append-to-body>
        <el-input ref="tagWriteValue" v-model="tagWriteValue" clearable size="medium" style="width: 100%" />
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible = false">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite">{{ $t('lang_pack.monitor.write') }}</el-button>
        </div>
      </el-dialog>
    </el-col>
    <el-col :span="tagInfoSpan">
      <transition name="el-zoom-in-center">
        <el-descriptions v-if="tagInfoSpan === 12" class="margin-top" :column="2" size="mini" border>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.attr') }}
            </template>
            {{ currentRow.name }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.tagsDefined.exampleCode') }}
            </template>
            {{ currentRow.client_code }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.tagsDefined.exampleDescription') }}
            </template>
            {{ currentRow.client_des }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.tagsDefined.exampleGroupCode') }}
            </template>
            {{ currentRow.tag_group_code }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.tagsDefined.exampleGroupDes') }}
            </template>
            {{ currentRow.tag_group_des }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.labelCode') }}
            </template>
            {{ currentRow.tag_code }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.labelDescription') }}
            </template>
            {{ currentRow.tag_des }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.regionName') }}
            </template>
            {{ currentRow.block_name }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.areaNumber') }}
            </template>
            {{ currentRow.block_addr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.dataType') }}
            </template>
            {{ currentRow.data_type }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.initialAddress') }}
            </template>
            {{ currentRow.start_addr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.dataLength') }}
            </template>
            {{ currentRow.data_length }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.bit') }}
            </template>
            {{ currentRow.data_bit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.opcRealAddress') }}
            </template>
            {{ currentRow.opc_addr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.opcVirtualAddress') }}
            </template>
            {{ currentRow.opc_demo_addr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.dataPermissions') }}
            </template>
            {{ currentRow.data_access }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.changePush') }}
            </template>
            {{ currentRow.pub_flag }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.snapshotStorage') }}
            </template>
            {{ currentRow.fastpic_save_flag }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.otherAttributes') }}
            </template>
            {{ currentRow.tag_attr_else }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.convertFormat') }}
            </template>
            {{ currentRow.data_format }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.saveOnChange') }}
            </template>
            {{ currentRow.change_save_flag }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.warningProcessing') }}
            </template>
            {{ currentRow.pre_alarm_flag }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.warningUpperLimit') }}
            </template>
            {{ currentRow.upper_limit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.lowerWarningLimit') }}
            </template>
            {{ currentRow.down_limit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.disDataTypes') }}
            </template>
            {{ currentRow.tag_attr }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('lang_pack.monitor.openOPCUA') }}
            </template>
            {{ currentRow.opc_ua_flag }}
          </el-descriptions-item>
        </el-descriptions>
      </transition>
    </el-col>
  </el-row>
</template>

<script>
import { selAttrMonitor } from '@/api/core/flow/rcsFlowMainStepAttr'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  props: {
    cel_api: {
      type: String,
      default: ''
    },
    mqtt_url: {
      type: String,
      default: ''
    },
    step_mod_id: {
      type: [String, Number],
      default: ''
    },
    flow_main_id: {
      type: [String, Number],
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      tableData: [],
      currentRow: {},
      tagWriteDialogVisible: false,
      tagWriteValue: '',
      tagValueList: [],
      monitorSpan: 24,
      tagInfoSpan: 0,
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      }
    }
  },
  watch: {},
  mounted: function() {},
  created: function() {
    this.toQuery()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    toQuery() {
      const query = {
        user_name: Cookies.get('userName'),
        step_mod_id: this.step_mod_id,
        flow_main_id: this.flow_main_id
      }
      selAttrMonitor(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const resultData = JSON.parse(defaultQuery.result)
            if (resultData.length > 0) {
              this.tableData = resultData
              this.readTagValue()
              this.toStartWatch()
            } else {
              this.tableData = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    readTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < this.tableData.length; i++) {
        const _children = this.tableData[i].children
        for (var j = 0; j < _children.length; j++) {
          if (_children[j].value_type === 'TAG_ID') {
            var readTag = {}
            readTag.tag_key = _children[j].tag_key
            readTagArray.push(readTag)
            this.tagValueList.push({ tag_key: _children[j].tag_key, tag_value: '' })
          }
        }
      }
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cel_api.split(':')[1] + method
      } else {
        path = 'http://' + this.cel_api + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var i = 0; i < result.length; i++) {
                  var tag_key = result[i].tag_key
                  var tag_value = result[i].tag_value === undefined ? '' : result[i].tag_value
                  this.tagValueList.filter(item => item.tag_key === tag_key)[0].tag_value = tag_value
                }
              }
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    },
    getTagValue(tag_key) {
      const tagInfo = this.tagValueList.filter(item => item.tag_key === tag_key)
      if (tagInfo.length > 0) {
        return tagInfo[0].tag_value
      }
      return ''
    },
    openTagWrite(row) {
      this.tagWriteValue = ''
      this.currentRow = row
      this.$nextTick(x => {
        this.$refs.tagWriteValue.focus()
      })
      this.tagWriteDialogVisible = true
    },
    handleTagWrite() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.currentRow.tag_key,
        TagValue: this.tagWriteValue
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.currentRow.tag_key.split('/')[0]
      this.sendMessage(topic, sendStr)
      this.tagWriteDialogVisible = false
    },
    openTagInfo(row) {
      if (this.monitorSpan === 12 && row.id === this.currentRow.id) {
        this.currentRow = {}
        this.monitorSpan = 24
        this.tagInfoSpan = 0
      } else {
        this.currentRow = row
        this.monitorSpan = 12
        this.tagInfoSpan = 12
      }
    },

    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      if (this.tagValueList.length > 0) {
        var connectUrl = 'ws://' + this.mqtt_url + '/mqtt'
        console.log('拼接URL：' + connectUrl)
        // mqtt连接
        this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
        this.clientMqtt.on('connect', e => {
          this.mqttConnStatus = true
          // 当MQTT连接成功后，注册CLIENT相关TOPIC
          for (var i = 0; i < this.tagValueList.length; i++) {
            // 订阅主题
            this.topicSubscribe('SCADA_CHANGE/' + this.tagValueList[i].tag_key)
          }
          this.$message({
            message: this.$t('lang_pack.vie.cnneSuccess'),
            type: 'success'
          })
        })

        // MQTT连接失败
        this.clientMqtt.on('error', () => {
          this.$message({
            message: this.$t('lang_pack.vie.cnneFailed'),
            type: 'error'
          })
          this.clientMqtt.end()
        })
        // 断开发起重连(异常)
        this.clientMqtt.on('reconnect', () => {
          this.$message({
            message: this.$t('lang_pack.vie.connDisconRecon'),
            type: 'error'
          })
        })
        this.clientMqtt.on('disconnect', () => {
          // this.$message({
          //  message: '服务连接断开',
          //  type: 'error'
          // })
        })
        this.clientMqtt.on('close', () => {
          // this.clientMqtt.end()
          // this.$message({
          //  message: '服务连接断开',
          //  type: 'error'
          // })
        })
        // 接收消息处理
        this.clientMqtt.on('message', (topic, message) => {
          // 解析传过来的数据
          if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            var jsonData = JSON.parse(message)
            if (jsonData == null) return
            var TagKey = jsonData.TagKey
            var TagNewValue = jsonData.TagNewValue
            const tagInfo = this.tagValueList.filter(item => item.tag_key === TagKey)
            if (tagInfo.length > 0) {
              tagInfo[0].tag_value = TagNewValue
            }
          }
        })
      }
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    }
  }
}
</script>
