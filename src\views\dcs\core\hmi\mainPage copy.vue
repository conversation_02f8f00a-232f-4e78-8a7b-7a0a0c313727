<template>
    <div class="mainPage-container">
        <el-card class="wrapCard CardTwo">
            <el-row :gutter="24" >
                <el-col :span="24">
                    <el-card class="cardFirst">
                        <mainIndex  @ok="handleCutNode"></mainIndex>
                        <!-- <el-tabs v-model="activeName">
                            <el-tab-pane label="全局" name="main">
                                <mainIndex v-if="activeName==='main'" :task_num="task_num" @ok="handleCutNode"></mainIndex>
                            </el-tab-pane>
                            <el-tab-pane label="上料区" name="loading">
                                <mainArea v-if="activeName==='loading'"></mainArea>
                            </el-tab-pane>
                            <el-tab-pane label="切割区" name="slicing">
                                <div class="dcs-cut">
                                    <mainCut v-if="activeName==='slicing'" :task_num="task_num"></mainCut>
                                </div>
                            </el-tab-pane>
                            <el-tab-pane label="分拣区" name="sorting">
                                <mainSort v-if="activeName==='sorting'" :task_num="task_num"></mainSort>
                            </el-tab-pane>
                        </el-tabs>
                        <el-col class="search" :span="4">
                            <div>
                                <el-input placeholder="请输入任务号" style="margin-right: 20px;" v-model="task_num" clearable size="small" />
                            </div>
                        </el-col> -->
                    </el-card>
                </el-col>
                
                <el-col :span="10" :lg="10" class="borderStyle">
                    <el-card>
                        <div class="wrapTextSelect">
                            <div class="elliptic">
                                <div></div>
                                <span>任务列表状态(天) </span>
                            </div>
                            <el-button type="primary" @click="moreData">更多>></el-button>
                        </div>
                        <el-table border ref="table" v-loading="loading" size="small" :data="tableData"
                            :highlight-current-row="highlightCurrentRow" :height="height">
                            <!-- 任务号 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_num"
                                :label="$t('lang_pack.cuttingZone.TaskNumber')" />
                            <!-- 钢板型号 -->
                            <el-table-column :show-overflow-tooltip="true" align="center" prop="model_type"
                                :label="$t('lang_pack.cuttingZone.SteelPlateModel')" />
                            <!-- 切割类型 -->
                            <el-table-column :show-overflow-tooltip="true" align="center" prop="cut_type"
                                :label="$t('lang_pack.taskList.CutType')">
                                <template slot-scope="scope">
                                    <!--取到当前单元格-->
                                    {{ dict.label.CUT_TYPE[scope.row.cut_type] }}
                                </template>
                            </el-table-column>
                            <!-- 目标切割机 -->
                            <el-table-column :show-overflow-tooltip="true" align="center" prop="cut_code"
                                :label="$t('lang_pack.taskList.TargetCuttingMachine')" />
                            <!-- 任务状态 -->
                            <el-table-column :show-overflow-tooltip="true" align="center" prop="task_status"
                                :label="$t('lang_pack.taskList.TaskStatus')">
                                <template slot-scope="scope">
                                    <!--取到当前单元格-->
                                    {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
                                </template>
                            </el-table-column>
                            <!-- 当前工位 -->
                            <el-table-column :show-overflow-tooltip="true" align="center" prop="now_station_code"
                                :label="$t('lang_pack.cuttingZone.CurrentWorkstation')" />
                        </el-table>
                    </el-card>
                </el-col>
                <el-col :span="4" :lg="5" class="borderStyle">
                    <el-card :style="{'height':height + 65 + 'px'}">
                        <div class="title">
                            <div></div>
                            <span>天车</span>
                        </div>
                        <div class="Inb-out-bound" :style="{'height':height < 768 ? '13vh' : '10vh'}">
                            <div v-for="(item, key) in outInStock" :key="key">
                                <div class="wrappstyle" v-for="(value, index) in item" :key="index">
                                    <div class="name">
                                        <div><span class="wholeline"></span></span></div>
                                        <div><span class="finish">{{index}}</span><span class="smallPiece">{{value}}</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="title">
                            <div></div>
                            <span>分拣</span>
                        </div>
                        <div class="sorting">
                            <div v-for="(item,index) in sortNumber">
                                <div class="wrappstyle">
                                    <div class="name">
                                        <div></div>
                                        <div><span class="finish">等待数量</span><span class="smallPiece">{{ item.wait_count }}</span></div>
                                    </div>
                                </div>
                                <div class="wrappstyle">
                                    <div class="name">
                                        <div><span class="wholeline"></span><span class="smallPiece">{{ item.station_name }}</span></div>
                                        <div><span class="finish">完成数量</span><span class="smallPiece">{{item.complete_rate}}</span></div>
                                    </div>
                                </div>
                                <div class="wrappstyle" :style="{ 'margin-bottom':index ==1 ? '0px' :'' }">
                                    <div class="name">
                                        <div></div>
                                        <div><span class="finish">完成率</span><span class="smallPiece">{{ item.complete_rate }}%</span></div>
                                    </div>
                                </div>
                                <el-divider v-if="index==0"/>
                            </div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="10" :lg="9" class="borderStyle">
                    <el-card class="slicingNum">
                        <div class="slicingName">
                            <span>任务数量</span>
                            <span>待切割</span>
                            <span>切割中</span>
                            <span>待分拣</span>
                            <span>分拣中</span>
                            <span>分拣完成</span>
                        </div>
                        <div class="slicingValue">
                            <span>{{taskCountData.length && taskCountData[0].taskCount || 0 }}</span>
                            <span>{{taskCountData.length && taskCountData[0].waitCutCount || 0}}</span>
                            <span>{{taskCountData.length && taskCountData[0].cuttingCount || 0}}</span>
                            <span>{{taskCountData.length && taskCountData[0].waitSortCount || 0}}</span>
                            <span>{{taskCountData.length && taskCountData[0].sortingCount || 0}}</span>
                            <span>{{taskCountData.length && taskCountData[0].finishCount || 0}}</span>
                        </div>
                    </el-card>
                    <el-card class="slicing">
                        <div class="daynoborder">
                            <div class='left'>
                                <span>切割机</span>
                                <span>下达数量</span>
                                <span>完成率</span>
                            </div>
                            <div class="right">
                                <div v-for="(item,index) in cutTask" :key="index">
                                    <span>{{ item.stationCode }}</span>
                                    <span>{{ item.taskCount }}</span>
                                    <span>{{ item.completeRate }}%</span>
                                </div>
                            </div>
                        </div>
                    </el-card>
                    <el-card class="device">
                        <el-tabs v-model="deviceName">
                            <el-tab-pane label="流程管理" name="first">
                                <el-table border ref="table" v-loading="loading" :data="flowData" 
                                    :height="deviceHeight">
                                    <!-- 工位号 -->
                                    <el-table-column  :show-overflow-tooltip="true" align="center" prop="station_code"
                                        :label="$t('lang_pack.meStationQuality.stationCode')" />
                                    <!-- 流程名称 -->
                                    <el-table-column :show-overflow-tooltip="true" align="center" prop="flow_main_des"
                                        :label="$t('lang_pack.wmsCbTable.processName')" />
                                    <!-- 子流程 -->
                                    <el-table-column :show-overflow-tooltip="true" align="center" prop="flow_mod_sub_des"
                                        :label="$t('lang_pack.wmsCbTable.subProcess')" />
                                    <!-- 步骤 -->
                                    <el-table-column :show-overflow-tooltip="true" align="center" prop="step_mod_des"
                                        :label="$t('lang_pack.wmsCbTable.stepName')" />
                                    <!-- 异常 -->
                                    <el-table-column :show-overflow-tooltip="true" align="center" prop="log_msg"
                                        :label="$t('lang_pack.sortingArea.AbnormalWorkReporting')" />
                                    <!-- 时间 -->
                                    <el-table-column :show-overflow-tooltip="true" align="center" prop="start_date"
                                        :label="$t('lang_pack.mfTable.time')" />
                                </el-table>
                            </el-tab-pane>
                            <el-tab-pane label="设备异常" name="second">
                                <el-table border ref="table" v-loading="loading" :data="deviceData" :row-key="row => row.id"
                                    :height="deviceHeight">
                                    <el-table-column  align="center"
                                        v-for="(column, index) in deviceColumns" :key="index" :prop="column.prop"
                                        :label="column.label">
                                    </el-table-column>
                                </el-table>
                            </el-tab-pane>
                        </el-tabs>
                    </el-card>
                </el-col>
            </el-row>
        </el-card>
        <!-- <mainPageModal v-if="mainPageModalFlag" ref="mainPageModal" @ok="mainPageModalFlag = false"></mainPageModal> -->
        <selectModal ref="selectModal" :dict="dict"></selectModal>
    </div>
</template>
<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import mainIndex from './mainIndex.vue' //主页面
import mainArea from './mainArea.vue' //上料区
import mainCut from './mainCut.vue' //切割区
import mainSort from './mainSort.vue' //分拣区
// import mainPageModal from '../../modules/mainPageModal.vue'
import selectModal from '@/components/selectModal'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
export default {
    name: 'mainPage',
    components: {mainIndex,mainArea,mainCut,mainSort,selectModal},
    data() {
        return {
            loading: false,
            height: 0,
            deviceHeight: 0,
            imageHeight: document.documentElement.clientHeight - 550,
            highlightCurrentRow: false,
            activeName: 'main',
            deviceName: 'first',
            mainPageModalFlag: false,
            outInStock: [],
            sortNumber:[],
            cutTask:[],
            pointList:[],
            inventoryObj:{},
            cellStatusData:[],
            taskCountData:[],
            sortStatusData:[],
            tableData: [],
            flowData: [],
            task_num:'',
            deviceData: [
                { id: 1, sbyc: '智能行车', sbbm: 'TC1', bjjb: '一级', bjfl: '安全报警', bjbm: '18:00:01', bjms: '紧急停止' },
                { id: 2, sbyc: '智能行车', sbbm: 'TC1', bjjb: '一级', bjfl: '安全报警', bjbm: '18:00:01', bjms: '设备异常' },
                { id: 3, sbyc: '智能行车', sbbm: 'TC2', bjjb: '三级', bjfl: '安全报警', bjbm: '18:00:01', bjms: '紧急停止' },
                { id: 4, sbyc: '智能行车', sbbm: 'TC1', bjjb: '一级', bjfl: '安全报警', bjbm: '18:00:01', bjms: '设备异常' },
                { id: 5, sbyc: '智能行车', sbbm: 'TC2', bjjb: '二级', bjfl: '安全报警', bjbm: '18:00:01', bjms: '设备异常' },
            ],
            deviceColumns: [
                { label: '设备异常', prop: 'sbyc' },
                { label: '设备编码', prop: 'sbbm' },
                { label: '报警级别', prop: 'bjjb' },
                { label: '报警分类', prop: 'bjfl' },
                { label: '报警编码', prop: 'bjbm' },
                { label: '报警描述', prop: 'bjms' },
            ],
        }
    },
    created() {
        this.getPower() //调整表格低分辨率下的几种情况
        this.getTaskStatus() //获取任务列表状态
        this.getOutInStock() //获取天车出/入库数量
        this.getSortNumber() //获取天车显示分拣数量
        this.getCutTask() //获取切割机
        this.getFlowTask() //获取流程管理
        this.getModelType() //获取型号
        // this.getAllCellStatus() //获取工位状态
        this.getAll()
    },
    dicts: ['CUT_TYPE', 'EXECUTE_STATUS','PROD_TASK_STATUS'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.getPower() //调整表格低分辨率下的几种情况
        }
    },
    methods: {
        getPower(){
            const height = document.documentElement.clientHeight;
            const conditions = [
                { max: 620, height: 180, deviceHeight: 415},
                { max: 700, height: 240, deviceHeight: 460},
                { max: 800, height: 240, deviceHeight: 440},
                { max: 900, height: 330, deviceHeight: 530},
            ];
            const condition = conditions.find((condition) => height <= condition.max);
            if (condition) {
                this.height = height - condition.height;
                this.deviceHeight = height - condition.deviceHeight;
            } else {
                this.height = height - 500;
                this.deviceHeight = height - 700;
            }
        },
        getModelType(){
            const query = {
                userID: Cookies.get('userName')
            }
            crudFmodModel.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if(defaultQuery.data.length > 0){
                            this.dict.MODAL_LIST = []
                            for(let i=0;i<defaultQuery.data.length;i++){
                                this.dict.MODAL_LIST.push({
                                    id:defaultQuery.data[i].model_id,
                                    label:'',
                                    value:defaultQuery.data[i].model_type
                                })
                            }
                        }else{
                            this.dict.MODAL_LIST = []
                            this.$message({
                                message: defaultQuery.msg || '型号查询异常',
                                type: 'error'
                            })
                        }
                    }
                })
                .catch((err) => {
                    this.dict.modelList = []
                    this.$message({
                        message: err.msg || '型号查询异常',
                        type: 'error'
                    })
            })
        },
        handleCutNode(data){
            this.activeName = 'slicing'
        },
        moreData() {
            // this.mainPageModalFlag = true
            // this.$nextTick(() => {
            //     this.$refs.mainPageModal.dialogVisible = true
            // })
            this.$refs.selectModal.open({
                type: 'rwzt',
                checkType: '',
                search: {
                    onlyToday:false
                },
            })
        },
        getTaskStatus() {
            crudMainPage.sel({onlyToday: true}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.tableData = defaultQuery.data || []
                    }
                } else {
                    this.tableData = []
                    this.$message({
                        message: '任务状态查询异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.tableData = []
                    this.$message({
                        message: '任务状态查询异常',
                        type: 'error'
                    })
                })
        },
        getOutInStock() {
            crudMainPage.outInStock({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.outInStock = defaultQuery.data
                    }
                } else {
                    this.$message({
                        message: '获取天车出/入库异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.$message({
                        message: '获取天车出/入库异常',
                        type: 'error'
                    })
                })
        },
        getSortNumber() {
            crudMainPage.sortNumber({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.sortNumber = defaultQuery.data
                    }
                } else {
                    this.$message({
                        message: '获取分拣数量异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.$message({
                        message: '获取分拣数量异常',
                        type: 'error'
                    })
                })
        },
        getCutTask(){
            crudMainPage.cutTask({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.cutTask = defaultQuery.data
                    }
                } else {
                    this.$message({
                        message: '获取切割机异常',
                        type: 'error'
                    })
                }
            })
            .catch(() => {
                this.$message({
                    message: '获取切割机异常',
                    type: 'error'
                })
            })
        },
        getFlowTask(){
            crudMainPage.flowTask({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.flowData = defaultQuery.data
                    }
                } else {
                    this.flowData = []
                    this.$message({
                        message: '获取流程管理异常',
                        type: 'error'
                    })
                }
            })
            .catch(() => {
                this.flowData = []
                this.$message({
                    message: '获取流程管理异常',
                    type: 'error'
                })
            })
        },
        getAllCellStatus(){
            let arr = [{code:'R1',x:5.5,y:64},{code:'R2',x:11.5,y:64},{code:'R3',x:2,y:77},{code:'R4',x:11.5,y:77},{code:'G1',x:21.5,y:33}
            ,{code:'G2',x:27,y:33},{code:'G3',x:33,y:33},{code:'G4',x:39,y:18},{code:'G5',x:39,y:33},{code:'G6',x:39,y:51},{code:'G7',x:39,y:68}
            ,{code:'G8',x:39,y:84},{code:'G9',x:51,y:18},{code:'G10',x:51,y:33},{code:'G11',x:51,y:51},{code:'G12',x:51,y:65},{code:'G13',x:51,y:78}
            ,{code:'G14',x:59,y:33},{code:'G15',x:66,y:33},{code:'G16',x:72,y:33},{code:'G17',x:78,y:33}
        ]
            crudMainPage.allCellStatus({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.pointList = defaultQuery.data
                        this.pointList.map(e=>{
                            arr.map(item=>{
                                if(e.station_code === item.code){
                                    e = Object.assign(e,item)
                                    delete e.code
                                }
                            })
                        })
                    }
                } else {
                    this.$message({
                        message: '获取异常',
                        type: 'error'
                    })
                }
            })
            .catch(() => {
                this.$message({
                    message: '获取异常',
                    type: 'error'
                })
            })
        },
        getAll(){
            let arr = [
                ['L01','L02','L03','L04'],
                ['P33','P05','P04','P03','P02','P01'],
                ['P31','P32','P34','P12','P11','P10','P09','P08','P07','P06'],
                ['P30'],
                ['P18','P17','P16','P15','P14','P13'],
                ['P24','P23','P22','P21','P20','P19'],
                ['P29','P28','P27','P26','P25']
            ]
            Promise.all([
                // crudMainPage.allInventory({}),//所有库存
                // axios.get('aisEsbWeb/dcs/hmi/section-point?sectionCode=CUT'),//切割区
                crudMainPage.taskCount({}),//任务数量
                // crudMainPage.sectionCellStatus({sectionCodes:'SORT'}),//分拣区
            ]).then(responses=>{
                if(responses.length >0){
                    const [taskCount] = responses
                    // this.mergeDataFn(arr,inventory.data) //获取到库位后进行一个排序
                    // this.getCellStatus(cellStatus.data.data)
                    this.taskCountData = taskCount.data
                    // this.getSortStatus(sortStatus.data)
                }
            }).catch(error=>{
                this.$message({
                    message:  error || '获取异常',
                    type: 'error'
                })
            })
        },
        getCellStatus(data){
            let arr = [
                {code:'G3',x:6,y:27},{code:'G4',x:16,y:8},{code:'G5',x:20,y:27},{code:'G6',x:16,y:45},{code:'G7',x:16,y:67},{code:'G8',x:19,y:85},
                {code:'G9',x:78,y:9},{code:'G10',x:78,y:27},{code:'G11',x:78,y:44},{code:'G12',x:78,y:60},{code:'G13',x:78,y:76}
            ]
            data.map(e=>{
                arr.map(item=>{
                    if(e.station_code === item.code){
                        e = Object.assign(e,item)
                    }
                })
            })
            this.cellStatusData = data
        },
        getSortStatus(data){
            let arr = [
                {code:'G14',x:5,y:32},{code:'G15',x:22,y:32},{code:'G16',x:38,y:32},{code:'G17',x:53,y:32}
            ]
            data.map(e=>{
                arr.map(item=>{
                    if(e.station_code === item.code){
                        e = Object.assign(e,item)
                    }
                })
            })
            this.sortStatusData = data
        },
        mergeDataFn(arr,res){
            let i = 0;
            let k = 0;
            while (i < arr.length && k < res.length) {
                if (arr[i].includes(res[k].stock_code)) {
                    let index = arr[i].indexOf(res[k].stock_code)
                    !this.inventoryObj[`arr${i}`] && (this.inventoryObj[`arr${i}`] = [])
                    this.inventoryObj[`arr${i}`][index] = res[k]
                    k++
                    i = 0
                } else {
                    i++
                }
            }
        },
    }
}
</script>
<style lang="less" scoped>
.mainPage-container {
    ::v-deep .borderStyle .el-card.is-always-shadow{
        border: 1px solid #00479d;
    }
    ::v-deep .el-tabs__header {
        margin: 0;
    }

    ::v-deep .el-tabs {
        margin: 0 !important;
    }

    .cardFirst {
        margin-bottom: 10px;
        height: 365px;
        background-color: #99c1ff;
        ::v-deep .el-card__body{
            width: 100%;
            height: 100%;
            padding: 0 !important;
        }
        .search {
            position: absolute;
            top: 10px;
            right: 20px;

            div {
                text-align: center;
                display: flex;
            }
        }

        .dcs-main {
            margin: 10px 0;
            width: 100%;
            position: relative;
            img{
                background-size: 100% 100%;
                width: 100%;
                height: 100%;
            }
            .point{
                position:absolute;
                width: 20px;
                height: 20px;
                border-radius: 50%;
            }
            .active{
                background-color: #21f121;
                cursor: pointer;
            }
        }
        .dcs-cut{
            position: relative;
            img{
                background-size: 100% 100%;
                width: 100%;
                height: 100%;
            }
            .point{
                position:absolute;
                width: 20px;
                height: 20px;
                border-radius: 50%;
            }
            .active{
                background-color: #21f121;
                cursor: pointer;
            }
        }
        .loadingArea {
            width: 50%;
            display: flex;
            justify-content: space-between;
            .loadingAreaLeft {
                width: 65%;
                .area {
                    display: flex;
                    justify-content: space-between;

                    .left {
                        img {
                            height: 60px;
                            width: 100%;
                        }
                        div{
                            display: flex;
                            justify-content: space-around;
                            .stockNum{
                                width:63px;
                                text-align: center;
                            }
                            .modelType{
                                width: 160px;
                                text-align: center;
                            }
                        }
                    }
                }

                .feed {
                    margin: 5px 0;
                    float: right;
                    height: 45px;
                    width: 60%;
                }

                .blasting {
                    width: 100%;
                    height: 45px;
                }
            }

            .loadingAreaMain {
                width: 35%;
            }

        }

        .loadingAreaRight {
            width: 50%;

            .loadingAreaRightBottom {
                margin: 5px 0 0 10px;
                display: flex;
                justify-content: space-between;
            }
        }
    }

    .wrapTextSelect {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .elliptic {
            display: flex;
            justify-content: space-between;
            align-items: center;

            div {
                background-color: #00479d;
                width: 6px;
                height: 24px;
                border-radius: 10px;
                margin-right: 5px;
            }
        }
    }

    .title {
        width: 100%;
        height: 40px;
        background-color: #E8EFFF;
        display: flex;
        align-items: center;

        div {
            background-color: #00479d;
            width: 6px;
            height: 24px;
            border-radius: 10px;
            margin: 0 5px;
        }
    }

    .Inb-out-bound {
        flex-direction: column;

        .wrappstyle {
            width: 90%;
            margin: 15px auto;

            .name {
                display: flex;
                justify-content:
                    space-between;
                width: 100%;

                div {
                    width: 50%;
                    display: flex;
                    align-items: center;

                    .smallPiece {
                        margin: 0 0 0 5px;
                    }

                    .finish {
                        display: block;
                        width: 70%;
                        text-align: center;
                    }
                }
            }
        }
    }
    .wholeline {
        display: block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #21f121;
    }
    .sorting {
        height: 27vh;

        .wrappstyle {
            width: 90%;
            margin: 15px auto;

            .name {
                display: flex;
                justify-content:
                    space-between;
                width: 100%;

                div {
                    width: 50%;
                    display: flex;
                    align-items: center;

                    .smallPiece {
                        margin: 0 0 0 5px;
                    }

                    .finish {
                        display: block;
                        width: 70%;
                        text-align: center;
                    }
                }
            }


        }

        ::v-deep .el-divider--horizontal {
            margin: 0;
        }
    }

    .device {
        ::v-deep .el-card__body {
            padding: 10px !important;
        }

        ::v-deep .el-tabs__nav-wrap {
            margin-bottom: 10px;
        }
    }

    .slicing {
        margin: 5px 0;

        .daynoborder {
            width: 100%;
            display:flex;
            .left{
                width:15%;
                span{
                    display:block;
                    padding: 7px 0;
                    width:100%;
                    text-align:center;
                    font-weight:500;
                    color: #00479d;
                    margin:1px 0;
                }
            }
            .right{
                width:85%;
                display:flex;
                div{
                    width:100px;
                    span{
                        display:block;
                        padding: 7px 0;
                        width:100%;
                        text-align:center;
                        font-weight:500;
                        margin:1px 0;
                    }
                }
                
            }
            // .trtitle {
            //     display: flex;
            //     text-align: center;

            //     // justify-content: space-around;
            //     .riyueactive {
            //         width: 16%;
            //         font-weight: 500;
            //         color: #00479d;
            //     }

            //     .tdName {
            //         width: 14%;
            //     }
            // }
        }
    }

    .slicingNum {
        width: 100%;

        .slicingName {
            display: flex;
            justify-content: space-around;
            text-align: center;

            span {
                width: 16.5%;
                display: block;

            }

        }

        .slicingValue {
            display: flex;
            justify-content: space-around;
            text-align: center;

            span {
                width: 16.5%;
                display: block;

            }
        }
    }
}

.left {
    div {
        width: 280px;
        border: 1px solid gray;
        margin-top: 5px;
        display: flex;
        justify-content: center;

        span {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

@media screen and (max-height: 768px) {
    .mainPage-container {
        .dcs-main {
            height: 150px !important;
            .point{
                width: 10px !important;
                height: 10px !important;
            }
        }
    }

    .left {
        div {
            width: 205px;
            .stockNum{
                transform: scale(0.7);
                margin-left: -10px;
            }
            .modelType{
                transform: scale(0.7);
                margin-left: -15px;
                width: 165px !important;
            }
            span{
                text-overflow:clip;
            }
        }
    }
}
@media screen and (max-height: 1080px) {
    .sorting {

    }
    .Inb-out-bound {
        .wrappstyle {
            margin: 25px auto !important;
        }
    }
}
</style>
