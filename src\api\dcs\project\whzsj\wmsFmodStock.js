import request from '@/utils/request'

// 查询WMS天车库位表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWhzsjFmodStockSelect',
    method: 'post',
    data
  })
}
// 新增WMS天车库位表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWhzsjFmodStockInsert',
    method: 'post',
    data
  })
}
// 修改WMS天车库位表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWhzsjFmodStockUpdate',
    method: 'post',
    data
  })
}
// 删除WMS天车库位表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWhzsjFmodStockDelete',
    method: 'post',
    data
  })
}

// 修改WMS天车库位表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWhzsjFmodStockEnableFlagUpdate',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

