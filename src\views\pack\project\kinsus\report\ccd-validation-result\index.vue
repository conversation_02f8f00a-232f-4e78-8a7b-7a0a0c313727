<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="站点编码:">
                <el-input v-model="query.station_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="标签ID:">
                <el-input v-model="query.serial_num" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation
        show=""
        :permission="permission"
      >
        <template slot="left">
          <el-button
            v-loading="downloadLoading"
            :disabled="downloadLoading"
            type="primary"
            icon="el-icon-upload2"
            @click="exportExecl"
          >{{ $t('lang_pack.vie.export') }}</el-button>
          <el-date-picker
            v-model="exportDatePeriod"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('view.form.timePeriodStart')"
            :end-placeholder="$t('view.form.timePeriodEnd')"
            value-format="yyyy-MM-dd"
          />
        </template>
      </crudOperation>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title "
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="265px"
              :inline="true"
            >
              <el-form-item label="站点编码" prop="model_type">
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="站点编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_code }}</el-descriptions-item>
                  <el-descriptions-item label="站点描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_desc }}</el-descriptions-item>
                  <el-descriptions-item label="标签ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                  <el-descriptions-item label="校验结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.validate_result }}</el-descriptions-item>
                  <el-descriptions-item label="校验信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.validate_msg }}</el-descriptions-item>
                  <el-descriptions-item label="校验时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.validate_time | dateFormat }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              label="站点编码"
              width="200"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_desc"
              label="站点描述"
              width="200"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              label="标签ID"
              width="200"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="validate_result"
              label="校验结果"
              width="130"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="validate_msg"
              label="校验信息"
              width="220"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="validate_time"
              label="校验时间"
              width="200"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.validate_time | dateFormat }}</span>
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import api from '@/api/core/biz'
import apiExportExcel from '@/api/core/biz/exportExcel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { downloadFile } from '@/utils/index'
const defaultForm = {
  created_by: '',
  creation_date: '',
  last_updated_by: '',
  last_update_date: '',
  enable_flag: 'Y'
}
export default {
  name: 'CCD_VALIDATION_RESULT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.formulaMainten'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      },
      params: {
        conn: 'a_pack_ccd_v_res'
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      downloadLoading: false,
      exportDatePeriod: []
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    handleInput(value) {
      const reg = /^\d+(\.\d{0,1})?$/ // 验证数字及一位小数的正则表达式
      if (!reg.test(this.form[value])) {
        // 输入不符合要求，清空输入框
        this.form[value] = ''
      }
    },
    BlurText(e) {
      const boolean = new RegExp('^[0-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger'))
        e.target.value = ''
      }
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              user_name: Cookies.get('userName'),
              recipe_id: data.recipe_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    exportExecl() {
      if (!this.exportDatePeriod || this.exportDatePeriod.length === 0) {
        this.$message.error('请选择导出时间段（下载时间）')
        return
      }
      this.downloadLoading = true
      const name = 'CCD校验结果'
      const bodyData = {
        name: name,
        conn: 'a_pack_ccd_v_res',
        columns: {},
        date_period: this.exportDatePeriod
      }
      let columnIndex = 1
      const columns = Object.assign({}, this.$t('view.field.ccdValidationResult'))
      for (const key in columns) {
        columns[key] = columnIndex.toString().padStart(3, '0') + '-' + columns[key]
        columnIndex++
      }
      bodyData.columns = columns
      // 如果入参的条件里面没有值，那么就把入参条件给删掉，传空对象
      Object.keys(bodyData).forEach((key) => {
        if (
          bodyData[key] === '' ||
          (Array.isArray(bodyData[key]) && bodyData[key].length === 0)
        ) {
          delete bodyData[key]
        }
      })
      apiExportExcel(bodyData)
        .then((res) => {
          if (res.type && res.type.indexOf('application/json') > -1) {
            new Blob([res]).text().then((text) => {
              const resJson = JSON.parse(text)
              this.$message.error(resJson.msg)
            })
            this.downloadLoading = false
            return
          }
          downloadFile(res, name, 'xlsx')
          this.downloadLoading = false
        })
        .catch((err) => {
          this.downloadLoading = false
        })
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
