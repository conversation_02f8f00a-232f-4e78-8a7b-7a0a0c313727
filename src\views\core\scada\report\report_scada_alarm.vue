<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">

            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.prodLine') + '：'">
                <el-select v-model="query.prod_line_id" filterable clearable size="small">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_id"
                    :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.station') + '：'">
                <el-select v-model="query.station_code" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id"
                    :label="item.station_code + ' ' + item.station_des" :value="item.station_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.instanceCode') + '：'">
                <el-input v-model="query.client_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.alarmCode') + '：'">
                <el-input v-model="query.alarm_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.alarmDesc') + '：'">
                <el-input v-model="query.alarm_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.resetFlag') + '：'">
                <el-select v-model="query.reset_flag" clearable>
                  <el-option v-for="item in [
                    { value: 'Y', label: $t('lang_pack.scadaAlarmReport.resetYes') },
                    { value: 'N', label: $t('lang_pack.scadaAlarmReport.resetNo') }
                  ]"
                    :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.scadaAlarmReport.time') + '：'">
                <div class="block">
                  <el-date-picker v-model="query.item_date" type="datetimerange" size="small" align="right" unlink-panels
                    range-separator="~" :start-placeholder="$t('lang_pack.scadaAlarmReport.startTime')" :end-placeholder="$t('lang_pack.scadaAlarmReport.endTime')"
                    value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions" />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-download" @click="exportExecl"
                    :disabled="tableData.length <= 0">{{ $t('lang_pack.scadaAlarmReport.export') }}</el-button>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">
                  {{ $t('lang_pack.scadaAlarmReport.search') }}</el-button>
                <el-button class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">
                  {{ $t('lang_pack.scadaAlarmReport.reset') }}</el-button>
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%"
            :cell-style="cellStyle" highlight-current-row :height="height">
            <el-table-column :show-overflow-tooltip="true" prop="station_code" :label="$t('lang_pack.scadaAlarmReport.station')" />
            <el-table-column :show-overflow-tooltip="true" prop="client_code" :label="$t('lang_pack.scadaAlarmReport.instanceCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="client_des" :label="$t('lang_pack.scadaAlarmReport.instanceDesc')" />
            <el-table-column :show-overflow-tooltip="true" prop="alarm_code" :label="$t('lang_pack.scadaAlarmReport.alarmCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="alarm_level" :label="$t('lang_pack.scadaAlarmReport.alarmLevel')" />
            <el-table-column :show-overflow-tooltip="true" prop="alarm_des" :label="$t('lang_pack.scadaAlarmReport.alarmDesc')" />
            <el-table-column :show-overflow-tooltip="true" prop="item_date" :label="$t('lang_pack.scadaAlarmReport.alarmTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="reset_date" :label="$t('lang_pack.scadaAlarmReport.resetTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="reset_flag" :label="$t('lang_pack.scadaAlarmReport.resetFlag')">
              <template slot-scope="scope">
                {{ scope.row.reset_flag === "Y" ? $t('lang_pack.scadaAlarmReport.resetYes') : $t('lang_pack.scadaAlarmReport.resetNo') }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="simulated_flag" :label="$t('lang_pack.scadaAlarmReport.isSimulated')">
              <template slot-scope="scope">
                {{ scope.row.simulated_flag === "Y" ? $t('lang_pack.scadaAlarmReport.simulatedYes') : $t('lang_pack.scadaAlarmReport.simulatedNo') }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="tag_id" :label="$t('lang_pack.scadaAlarmReport.tag')" />
          </el-table>

        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button type="primary">{{ $t('lang_pack.scadaAlarmReport.totalCount') }}：{{ tableTotal }}</el-button>
              <el-button type="primary">{{ $t('lang_pack.scadaAlarmReport.currentPage') }}{{ nowPageIndex }}{{ $t('lang_pack.scadaAlarmReport.page') }}</el-button>
              <el-button type="primary" @click="pageQuery('pre')">&lt;&nbsp;{{ $t('lang_pack.scadaAlarmReport.previousPage') }}</el-button>
              <el-button type="primary" @click="pageQuery('next')">{{ $t('lang_pack.scadaAlarmReport.nextPage') }}&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <download-excel class="export-excel-wrapper" v-if="downloadFlag" :data="json_data" :fields="json_fields" :name="$t('lang_pack.scadaAlarmReport.title')"></download-excel>
  </div>
</template>
<script>

import exportExecl from '@/utils/manage'
import crudProdLine from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import axios from 'axios'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'
const queryDefault = {
  prod_line_id: '',
  station_code: '',
  item_date: null,
  client_code: '',
  client_des: '',
  alarm_code: '',
  alarm_des: '',
  reset_flag: '',
  page_dirct: '',
  page_id: '',
  tableSize: 20,
  tablePage: 1
}
export default {
  name: 'REPORT_SCADA_ALARM',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      query: { ...queryDefault },
      tableLoading: false,
      tableData: [],
      tableTotal: 0,
      nowPageIndex: 1, // 当前页数
      pageList: [],
      prodLineData: [],
      stationData: [],
      currentRow: {},
      mainchartShow: false,
      editFlowChart: false,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      json_fields: null,
      json_data: [],
      downloadFlag:false,
      pickerOptions: {},
    }
  },
  watch: {
    'query.prod_line_id': {
      handler() {
        this.getStationData()
      }
    },
    'query.station_code': {
      handler() {
        this.getCellIp()
      }
    }
  },

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {
    // 初始化日期选择器快捷选项
    this.pickerOptions = createDatePickerShortcuts(this.$i18n)

    // Initialize json_fields for export
    this.json_fields = {
      [this.$t('lang_pack.scadaAlarmReport.serialNumber')]: 'type',
      [this.$t('lang_pack.scadaAlarmReport.station')]: 'station_code',
      [this.$t('lang_pack.scadaAlarmReport.instanceCode')]: 'client_code',
      [this.$t('lang_pack.scadaAlarmReport.instanceDesc')]: 'client_des',
      [this.$t('lang_pack.scadaAlarmReport.alarmCode')]: 'alarm_code',
      [this.$t('lang_pack.scadaAlarmReport.alarmLevel')]: 'alarm_level',
      [this.$t('lang_pack.scadaAlarmReport.alarmDesc')]: 'alarm_des',
      [this.$t('lang_pack.scadaAlarmReport.alarmTime')]: 'item_date',
      [this.$t('lang_pack.scadaAlarmReport.resetTime')]: 'reset_date',
      [this.$t('lang_pack.scadaAlarmReport.resetFlag')]: 'reset_flag',
      [this.$t('lang_pack.scadaAlarmReport.isSimulated')]: 'simulated_flag',
    }

    crudProdLine
      .sel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y'
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.scadaAlarmReport.queryException'),
          type: 'error'
        })
      })
  },
  methods: {
    cellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec'
    },
    getStationData() {
      this.query.station_code = ''
      this.stationData = []
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: this.query.prod_line_id
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getCellIp() {
      var cellId = 0
      const stationInfo = this.stationData.filter(item => item.station_code === this.query.station_code)
      if (stationInfo.length > 0) {
        cellId = stationInfo[0].cell_id
      }
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cellId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    resetQuery() {
      this.query = { ...queryDefault }
      this.nowPageIndex = 1
      this.pageList = []
      this.cellIp = ''
      this.webapiPort = ''
      this.tableData = []
    },
    toQuery() {
      if (this.query.prod_line_id === '' || this.query.station_code === '') {
        this.$message({ message: this.$t('lang_pack.scadaAlarmReport.selectProdLineAndStation'), type: 'info' })
        return
      }
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      }
      this.tableLoading = true
      this.tableData = []
      const data = {
        station_code: this.query.station_code,
        item_date: this.query.item_date,
        client_code: this.query.client_code,
        alarm_code: this.query.alarm_code,
        alarm_des: this.query.alarm_des,
        reset_flag: this.query.reset_flag,
        tableSize: this.query.tableSize,
        tablePage: this.nowPageIndex,
        page_dirct: this.query.page_dirct,
        page_id: this.query.page_id
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          this.tableLoading = false
          const defaultQuery = JSON.parse(JSON.stringify(res))
          console.log(defaultQuery)
          if (defaultQuery.data.code === 0) {
            if (defaultQuery.data.count > 0) {
              this.tableData = defaultQuery.data.data
            }
            this.tableTotal = defaultQuery.data.count
          } else {
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.tableLoading = false
          this.$message({ message: this.$t('lang_pack.scadaAlarmReport.queryException') + '：' + ex, type: 'error' })
        })
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('lang_pack.scadaAlarmReport.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.toQuery()
      } else {
        const total_page = this.tableTotal === 0 ? 1 : Math.ceil(this.tableTotal / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('lang_pack.scadaAlarmReport.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.tableData[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.tableData[this.tableData.length - 1].id
        this.toQuery()
      }
    },
    // 数据导出
     exportExecl() {
      var method = '/cell/core/scada/CoreScadaAlarmEExportExcel'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      }
      const data = {
        station_code: this.query.station_code,
        item_date: this.query.item_date,
        client_code: this.query.client_code,
        alarm_code: this.query.alarm_code,
        alarm_des: this.query.alarm_des,
        reset_flag: this.query.reset_flag,
        page_dirct: this.query.page_dirct,
        page_id: this.query.page_id
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then( res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
              this.json_data = defaultQuery.data.data || []
              this.downloadFlag = true
              this.$nextTick(()=>{
                const dom = document.querySelector('.export-excel-wrapper')
                dom.click();
              })

          }else{
            this.$message.warning(this.$t('lang_pack.scadaAlarmReport.exportFailed') + ': ' + err)
          }
        })
        .catch((err) => {
          this.$message.warning(this.$t('lang_pack.scadaAlarmReport.exportFailed') + ': ' + err)
        })
    }
  }
}
</script>

<style scoped>
.export-excel-wrapper{
  display: none;
}
</style>
