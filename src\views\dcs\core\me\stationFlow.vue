<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="工位:">
                                <!-- 工位 -->
                                <el-select v-model="query.station_code">
                                    <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code" >
                                        <span style="float: left">{{ item.station_code }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务号:">
                                <!-- 任务号 -->
                                <el-input v-model="query.task_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="批次号:">
                                <!-- 批次号 -->
                                <el-input v-model="query.lot_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="物料编码:">
                                <!-- 物料编码 -->
                                <el-input v-model="query.material_code" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="物料描述:">
                                <!-- 物料描述 -->
                                <el-input v-model="query.material_des" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="型号:">
                                <!-- 型号 -->
                                <el-select v-model="query.model_type" clearable filterable>
                                    <el-option v-for="item in modelList" :key="item.model_id" :label="item.model_type"
                                        :value="item.model_type" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="放行时间:">
                                <!-- 放行时间 -->
                                <el-date-picker
                                    v-model="timeRange"
                                    type="datetimerange"
                                    size="small"
                                    align="right"
                                    unlink-panels
                                    range-separator="~"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                    @change="passDate"
                                />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="是否完成报工:">
                                <!-- 是否完成报工 -->
                                <el-select v-model="query.bg_flag" clearable>
                                    <el-option v-for="item in [{id:1,lable:'是',value:'Y'},{id:2,lable:'否',value:'N'}]" :key="item.id" :label="item.lable" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="上传标识:">
                                <!-- 上传标识 -->
                                <el-select v-model="query.up_flag" clearable>
                                    <el-option v-for="item in [{id:1,lable:'是',value:'Y'},{id:2,lable:'否',value:'N'}]" :key="item.id" :label="item.lable" :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation>
                                <template slot="right">
                                    <el-button class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetData">{{ $t('lang_pack.commonPage.reset') }}</el-button>
                                </template>
                            </rrOperation>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" >
                <template slot="right">
                    <el-button size="small" type="warning" icon="el-icon-upload2" plain round @click="crud.doExport">
                        导出
                    </el-button>
                </template>
            </crudOperation>
            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.meStationFlow.stationCode')" prop="box_location">
                                <!-- 工位 -->
                                <el-input v-model="form.box_location" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskList.TaskNumber')" prop="box_barcode">
                                <!-- 工位描述 -->
                                <el-input v-model="form.box_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.taskNumber')" prop="task_num">
                                <!-- 任务号 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partBarcodeNumber')" prop="part_barcode">
                                <!-- 任务来源 -->
                                <el-input v-model="form.part_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partCode')" prop="part_code">
                                <!-- 批次号 -->
                                <el-input v-model="form.part_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partType')" prop="part_type">
                                <!-- 托盘号 -->
                                <el-input v-model="form.part_type" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.resultCode')" prop="fj_code">
                                <!-- 物料编码 -->
                                <el-input v-model="form.fj_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskList.TaskNumber')" prop="box_barcode">
                                <!-- 零件图号 -->
                                <el-input v-model="form.box_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.taskNumber')" prop="task_num">
                                <!-- 长 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partBarcodeNumber')" prop="part_barcode">
                                <!-- 宽 -->
                                <el-input v-model="form.part_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partCode')" prop="part_code">
                                <!-- 厚 -->
                                <el-input v-model="form.part_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partType')" prop="part_type">
                                <!-- 重 -->
                                <el-input v-model="form.part_type" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.resultCode')" prop="fj_code">
                                <!-- 材质 -->
                                <el-input v-model="form.fj_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskList.TaskNumber')" prop="box_barcode">
                                <!-- 材质(切割机) -->
                                <el-input v-model="form.box_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.taskNumber')" prop="task_num">
                                <!-- 放行方式 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partBarcodeNumber')" prop="part_barcode">
                                <!-- 放行时间 -->
                                <el-input v-model="form.part_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partCode')" prop="part_code">
                                <!-- 到达时间 -->
                                <el-input v-model="form.part_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partType')" prop="part_type">
                                <!-- 离开时间 -->
                                <el-input v-model="form.part_type" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.resultCode')" prop="fj_code">
                                <!-- 消耗时间 -->
                                <el-input v-model="form.fj_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskList.TaskNumber')" prop="box_barcode">
                                <!-- 班次代码 -->
                                <el-input v-model="form.box_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.taskNumber')" prop="task_num">
                                <!-- 是否完成报工 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partBarcodeNumber')" prop="part_barcode">
                                <!-- 报工检查信息 -->
                                <el-input v-model="form.part_barcode" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partCode')" prop="part_code">
                                <!-- 上传标识 -->
                                <el-input v-model="form.part_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.meStationFlow.partType')" prop="part_type">
                                <!-- 上传错误代码 -->
                                <el-input v-model="form.part_type" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>
                    <!--表格渲染-->
                    <el-table border  ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler" >
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_flow_id }}</el-descriptions-item>
                                <el-descriptions-item label="工位号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_code }}</el-descriptions-item>
                                <el-descriptions-item label="工位描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_des }}</el-descriptions-item>
                                <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                                <el-descriptions-item label="任务来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_from }}</el-descriptions-item>
                                <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                                <el-descriptions-item label="批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_num }}</el-descriptions-item>
                                <el-descriptions-item label="托盘号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_num }}</el-descriptions-item>
                                <el-descriptions-item label="物料编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                                <el-descriptions-item label="物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_des }}</el-descriptions-item>
                                <el-descriptions-item label="零件图号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_draw }}</el-descriptions-item>
                                <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                                <el-descriptions-item label="长" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_length }}</el-descriptions-item>
                                <el-descriptions-item label="宽" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_width }}</el-descriptions-item>
                                <el-descriptions-item label="厚" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_height }}</el-descriptions-item>
                                <el-descriptions-item label="重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_weight }}</el-descriptions-item>
                                <el-descriptions-item label="材质" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_texture }}</el-descriptions-item>
                                <el-descriptions-item label="材质(切割机)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.CUTTERBAR_TEXTURE[props.row.cut_texture] }}</el-descriptions-item>
                                <el-descriptions-item label="放行方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.PASS_WAY[props.row.pass_way] }}</el-descriptions-item>
                                <el-descriptions-item label="放行人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pass_by }}</el-descriptions-item>
                                <el-descriptions-item label="放行时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pass_date }}</el-descriptions-item>
                                <el-descriptions-item label="放行说明" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pass_remarks }}</el-descriptions-item>
                                <el-descriptions-item label="到达时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.arrive_date }}</el-descriptions-item>
                                <el-descriptions-item label="离开时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.leave_date }}</el-descriptions-item>
                                <el-descriptions-item label="消耗时间(单位秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cost_time }}</el-descriptions-item>
                                <el-descriptions-item label="班次代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.shif_code }}</el-descriptions-item>
                                <el-descriptions-item label="班次描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.shif_des }}</el-descriptions-item>
                                <el-descriptions-item label="上线工位标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.online_flag }}</el-descriptions-item>
                                <el-descriptions-item label="下线工位标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.offline_flag }}</el-descriptions-item>
                                <el-descriptions-item label="合格标志" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.quality_sign }}</el-descriptions-item>
                                <el-descriptions-item label="是否完成报工" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bg_flag }}</el-descriptions-item>
                                <el-descriptions-item label="报工检查信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bg_check_msg }}</el-descriptions-item>
                                <el-descriptions-item label="上传标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.up_flag }}</el-descriptions-item>
                                <el-descriptions-item label="上传错误代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.up_ng_code }}</el-descriptions-item>
                                <el-descriptions-item label="上传错误消息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.up_ng_msg }}</el-descriptions-item>
                                <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                            </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 工位号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="station_code"
                            :label="$t('lang_pack.meStationFlow.stationCode')" width="120" align='center' />
                        <!-- 任务号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_num"
                            :label="$t('lang_pack.meStationFlow.taskNumber')" width="120" align='center' />
                        <!-- 任务来源 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_from"
                            :label="$t('lang_pack.meStationFlow.taskSource')" width="80" align='center' >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
                            </template>
                        </el-table-column>
                        <!-- 序列号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="serial_num"
                            :label="$t('lang_pack.meStationFlow.serialNumber')" width="80" align='center' />
                        <!-- 批次号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="lot_num"
                            :label="$t('lang_pack.meStationFlow.batchNumber')" width="80" align='center' />
                        <!-- 物料编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="material_code"
                            :label="$t('lang_pack.meStationFlow.mainMaterialCode')" width="80" align='center' />
                        <!-- 物料描述 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="material_des"
                            :label="$t('lang_pack.meStationFlow.mainMaterialDescription')" width="80" align='center' />
                        <!-- 型号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="model_type"
                            :label="$t('lang_pack.meStationFlow.model_type')" width="120" align='center' />
                        <!-- 长 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="m_length"
                            :label="$t('lang_pack.meStationFlow.length')" width="80" align='center' />
                        <!-- 宽 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="m_width"
                            :label="$t('lang_pack.meStationFlow.width')"  width="80" align='center'/>
                        <!-- 厚 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="m_height"
                            :label="$t('lang_pack.meStationFlow.thick')" width="80" align='center' />
                        <!-- 重 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="m_weight"
                            :label="$t('lang_pack.meStationFlow.weight')" width="80" align='center' />
                        <!-- 材质 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="m_texture"
                            :label="$t('lang_pack.meStationFlow.materialQuality')" width="80" align='center' />
                        <!-- 材质(切割机) -->
                        <el-table-column  :show-overflow-tooltip="true" prop="cut_texture"
                            :label="$t('lang_pack.meStationFlow.cuttingMachine')" width="100" align='center' >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CUTTERBAR_TEXTURE[scope.row.cut_texture] }}
                            </template>
                        </el-table-column>
                        <!-- 放行方式 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="pass_way"
                            :label="$t('lang_pack.meStationFlow.releaseMethod')" width="80" align='center' >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.PASS_WAY[scope.row.pass_way] }}
                            </template>
                        </el-table-column>
                        <!-- 放行时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="pass_date"
                            :label="$t('lang_pack.meStationFlow.releaseTime')" width="130" align='center' />
                         <!-- 到达时间 -->
                         <el-table-column :show-overflow-tooltip="true" prop="arrive_date"
                            :label="$t('lang_pack.meStationFlow.arriveDate')" width="130" align='center' />
                        <!-- 离开时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="leave_date"
                            :label="$t('lang_pack.meStationFlow.leaveDate')" width="130" align='center' />
                        <!-- 消耗时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="cost_time"
                            :label="$t('lang_pack.meStationFlow.costTime')"  width="130" align='center'/>
                        <!-- 班次代码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="shif_code"
                            :label="$t('lang_pack.meStationFlow.shifCode')" width="80" align='center' />
                        <!-- 班次描述 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="shif_des"
                            :label="$t('lang_pack.meStationFlow.shifDes')" width="80" align='center' />
                        <!-- 合格标志 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="quality_sign"
                            :label="$t('lang_pack.meStationFlow.tagQualitySignId')" width="80" align='center' />
                        <!-- 是否完成报工 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="bg_flag"
                            :label="$t('lang_pack.meStationFlow.reportWorkOrNot')" width="110" align='center' >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.bg_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <!-- 上传标识 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="up_flag"
                            :label="$t('lang_pack.meStationFlow.upFlag')" width="80" align='center' >
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.up_flag==='Y'?'success':'warning'" effect="dark" style="cursor: pointer;" size="medium">{{ scope.row.up_flag }}</el-tag>
                            </template>
                            </el-table-column>
                        <!-- 上传错误代码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="up_ng_code"
                            :label="$t('lang_pack.meStationFlow.upNgCode')" width="110" align='center' />
                        <!-- 上传错误消息 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="up_ng_msg"
                            :label="$t('lang_pack.meStationFlow.upNgMsg')" width="130" align='center' />
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import crudStationFlow from '@/api/dcs/core/me/stationFlow'
import crudStation from '@/api/core/factory/sysStation'
import { InitTimePickRange, ScadaClientSelect } from '@/api/core/scada/report'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import {format} from '@/utils/manageFunction'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    station_flow_id:'',
    station_code:'',
    station_des:'',
    task_num:'',
    task_from:'',
    serial_num:'',
    lot_num:'',
    pallet_num:'',
    material_code:'',
    material_des:'',
    material_draw:'',
    model_type:'',
    m_length:'',
    m_width:'',
    m_height:'',
    m_weight:'',
    m_texture:'',
    cut_texture:'',
    pass_way:'',
    pass_by:'',
    pass_date:null,
    pass_remarks:'',
    arrive_date:'',
    leave_date:'',
    cost_time:'',
    shif_code:'',
    shif_des:'',
    online_flag:'',
    offline_flag:'',
    quality_sign:'',
    bg_flag:'Y',
    bg_check_msg:'',
    up_flag:'Y',
    up_ng_code:'',
    up_ng_msg:'',
    enable_flag:'Y',
}
export default {
    name: 'WEB_STATION_FLOW',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '过站信息',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'station_flow_id',
            // 排序
            sort: ['station_flow_id asc'],
            // CRUD Method
            crudMethod: { ...crudStationFlow },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: false
            },
            query:{
                pass_date:[format(new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * 7)),format(new Date())]
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 350,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
            },
            timeRange:[],
            // 工位数据
            stationData: [],
            modelList:[]
        }
    },
    dicts: ['ENABLE_FLAG','PART_TYPE','PASS_WAY','DATA_SOURCES','CUTTERBAR_TEXTURE'],
    mounted: function () {
        const date = new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * 7)
        this.timeRange = [format(date),format(new Date())]
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 350
        }
    },
    created: function () {
        this.getStation()
        this.getModelType()
    },
    methods: {
        resetData(){
            this.timeRange = this.crud.defaultQuery.pass_date = InitTimePickRange(7)
            this.crud.resetQuery()
        },
        passDate(e){
            this.query.pass_date = e
        },
        getStation() {
            const query = {
                userID: Cookies.get('userName')
            }
            crudStation.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.stationData = defaultQuery.data || []
                    }
                } else {
                    this.stationData = []
                    this.$message({
                        message: '工位查询异常',
                        type: 'error'
                    })
                }
            })
                .catch(() => {
                    this.stationData = []
                    this.$message({
                        message: '工位查询异常',
                        type: 'error'
                    })
                })
        },
        getModelType(){
            const query = {
                userID: Cookies.get('userName')
            }
            crudFmodModel.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if(defaultQuery.data.length > 0){
                            this.modelList = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '型号查询异常',
                        type: 'error'
                    })
            })
        },
    }
}
</script>
<!-- <style scoped lang="less">
 ::v-deep .el-table td .cell {
  white-space: normal;
} -->
</style>
