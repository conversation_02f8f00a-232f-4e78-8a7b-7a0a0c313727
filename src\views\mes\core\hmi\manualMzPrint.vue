<template>
  <el-card class="wrapCard">
    <el-row :gutter="20" class="elRowStyle">
      <el-form ref="form" class="el-form-wrap el-form-column" :model="form" size="small" label-width="100px" :inline="true">
        <el-form-item label="电芯码1:">
          <el-input ref="dxCode" v-model="form.dxCode" clearable @input="handleScan" @focus="inputBlurScan('dxCode')" />
        </el-form-item>
        <el-form-item label="电芯码2:">
          <el-input ref="dxCodeCell" v-model="form.dxCodeCell" clearable @input="handleScanCode" @focus="inputBlurScan('dxCodeCell')" />
        </el-form-item>
        <el-form-item label="模组码:">
          <el-input v-model="form.mzCode" disabled clearable />
        </el-form-item>
      </el-form>
      <div style="text-align: center" class="wrapButtonM">
        <el-button size="small" type="primary" @click="printMzCode">重新打印</el-button>
      </div>
    </el-row>
  </el-card>
</template>
<script>
import Cookies from 'js-cookie'
import { sel } from '@/api/mes/project/manualMzPrint.js'
import { selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'HMI_MANUAL_MZ_PRINT',
  data() {
    return {
      form: {
        dxCode: '',
        dxCodeCell: '',
        mzCode: ''
      },
      timer: null,
      timer2: null,
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
                'ScadaHmi_' +
                Cookies.get('userName') +
                '_' +
                Math.random()
                  .toString(16)
                  .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      monitorData: {
        PrintS_StartPrint: { client_code: 'OP1202', group_code: 'PrintStatus', tag_code: 'PrintS_StartPrint', tag_des: 'Print启动条码打印', value: '' },
        BarS_ScanBarCodeOrde: { client_code: 'OP1201', group_code: 'BarStatus', tag_code: 'BarS_ScanBarCodeOrde', tag_des: 'Bar获取扫描需求指令', value: '' },
        BarS_GetBarCodeResult: { client_code: 'OP1201', group_code: 'BarStatus', tag_code: 'BarS_GetBarCodeResult', tag_des: 'Bar扫描条码结果', value: '' }
      },
      inputCodeValue: 'dxCode'
    }
  },
  mounted() {
    this.toStartWatch()
    this.$nextTick(() => {
      this.$refs.dxCode.focus()
    })
  },
  methods: {
    inputBlurScan(value) {
      // 判断光标在哪个输入框
      this.inputCodeValue = value
    },
    handleScan(e) {
      if (!e) return
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$nextTick(() => {
          this.$refs.dxCodeCell.focus()
        })
      }, 1000)
    },
    handleScanCode(e) {
      if (!e) return
      if (!this.form.dxCode) {
        this.form.dxCodeCell = ''
        this.$message({
          type: 'error',
          message: '请先扫描电芯码1'
        })
        this.inputCodeValue = 'dxCode'
        this.$nextTick(() => {
          this.$refs.dxCode.focus()
        })
        return
      }
      this.timer2 && clearTimeout(this.timer2)
      this.timer2 = setTimeout(() => {
        const query = {
          dxCodes: this.form.dxCode + ',' + this.form.dxCodeCell
        }
        sel(query).then(res => {
          if (res.code === 0) {
            this.form.mzCode = res.result
            this.printMzCode()
            return
          }
          this.$message({ type: 'error', message: res.msg || '未找到符合的模组' })

          this.form.dxCode = ''
          this.form.dxCodeCell = ''
          this.form.mzCode = ''
          this.$nextTick(() => {
            this.$refs.dxCode.focus()
          })
        }).catch(ex => {
          this.$message({ type: 'error', message: ex.msg || '未找到符合的模组' })
          this.form.dxCode = ''
          this.form.dxCodeCell = ''
          this.form.mzCode = ''
          this.$nextTick(() => {
            this.$refs.dxCode.focus()
          })
        })
      }, 1000)
    },
    printMzCode() {
      if (this.form.dxCode === '' || this.form.dxCodeCell === '') return
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'OP1202/PrintStatus/PrintS_StartPrint',
        TagValue: 'Print.Module.Gx.Mz01@1@' + this.form.mzCode
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/OP1202'
      setTimeout(() => {
        this.form.dxCode = ''
        this.form.dxCodeCell = ''
        this.form.mzCode = ''
        this.$nextTick(() => {
          this.$refs.dxCode.focus()
        })
      }, 1500)
      this.sendMessage(topic, sendStr)
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          // var connectUrl = 'ws://**********:8083' + '/mqtt'
          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })
            // 先把指令置为0
            this.resetPoint('OP1201/BarStatus/BarS_ScanBarCodeOrde', '0', 'OP1201')
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            var jsonData = JSON.parse(message)
            console.log(jsonData)
            if (jsonData == null) return
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              // 首先要清空，电芯码1与电芯码2都是用的一个点位，不然会有缓存
              // this.monitorData.BarS_GetBarCodeResult.value = ''
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
              if (this.monitorData.BarS_ScanBarCodeOrde.value === '1' && this.monitorData.BarS_GetBarCodeResult.value) {
                if (this.inputCodeValue === 'dxCode') {
                  this.form.dxCode = this.monitorData.BarS_GetBarCodeResult.value
                  this.$nextTick(() => {
                    this.$refs.dxCodeCell.focus()
                  })
                }
                if (this.inputCodeValue === 'dxCodeCell') {
                  // 如果光标在电芯码2的位置上且电芯码1为空，我就检验，不录入
                  if (!this.form.dxCode) {
                    this.$message({
                      type: 'error',
                      message: '请先扫描电芯码1'
                    })
                    this.inputCodeValue = 'dxCode'
                    this.$nextTick(() => {
                      this.$refs.dxCode.focus()
                    })
                  } else {
                    this.form.dxCodeCell = this.monitorData.BarS_GetBarCodeResult.value
                    // 这里判断是扫码枪扫入
                    this.handleScanCode('1')
                  }
                }
                this.monitorData.BarS_GetBarCodeResult.value = ''
                this.resetPoint('OP1201/BarStatus/BarS_ScanBarCodeOrde', '0', 'OP1201')
              }
            }
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    resetPoint(tag_key, tag_value, topicValue) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + topicValue
      this.sendMessage(topic, sendStr)

      this.writeDialogVisbleSyncFrom = false
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>
