<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.errorMessage.errorMessageIDDescription')">
                <!-- 错误消息ID/描述： -->
                <el-input v-model="query.errorDefineIdDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
            <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
              <!--作用：区分修改、新增-->
              <el-form-item v-if="1 == 0" label="id" prop="id" display:none> <el-input v-model="form.id" />id </el-form-item>
              <el-form-item :label="$t('lang_pack.errorMessage.errorMessageDescription')" prop="error_define_des">
                <!-- 错误消息描述 -->
                <el-input v-model="form.error_define_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>

            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="error_define_id" :label="$t('lang_pack.errorMessage.errorMessageID')" />
            <!-- 错误消息ID -->
            <el-table-column  :show-overflow-tooltip="true" prop="error_define_des" :label="$t('lang_pack.errorMessage.errorMessageDescription')" />
            <!-- 错误消息描述 -->
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope"
                ><!--取到当前单元格-->
                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudErrordefine from '@/api/core/system/sysErrordefine'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  error_define_id: '',
  error_define_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_ERROR_DEFINE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '错误消息定义',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'error_define_id',
      // 排序
      sort: ['error_define_id asc'],
      // CRUD Method
      crudMethod: { ...crudErrordefine },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_error_define:add'],
        edit: ['admin', 'sys_error_define:edit'],
        del: ['admin', 'sys_error_define:del']
      },
      rules: {
        error_define_des: [{ required: true, message: '请输入错误消息描述', trigger: 'blur' }]
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {}
}
</script>
