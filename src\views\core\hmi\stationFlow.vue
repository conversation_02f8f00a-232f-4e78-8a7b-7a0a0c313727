<template>
  <div class="app-container">
    <el-row :gutter="12">
      <el-col :span="24">
        <el-card :body-style="{ padding: '10px' }" shadow="never" class="wrapCard">
          <el-form ref="query" :inline="true" size="small">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-8 col-12">
                <div class="formChild col-md-6 col-12 formChildFirst">
                  <el-form-item :label="$t('lang_pack.proMonitor.processTasks')">
                    <el-select v-model="flowMainId" clearable size="small" class="filter-item">
                      <el-option v-for="item in flowMainList" :key="item.flow_main_id" :label="item.flow_main_des" :value="item.flow_main_id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="getFlowTaskData">{{ $t('lang_pack.commonPage.search') }}</el-button>
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-4 col-12 ElFormSecondStyle">
                <el-form-item>
                  <span>{{ $t('lang_pack.proMonitor.total') }}</span> <span>{{ flowStatisticalData.yearCount }}</span>
                </el-form-item>
                <el-form-item>
                  <span>{{ $t('lang_pack.proMonitor.dayQuantity') }}</span> <span>{{ flowStatisticalData.dayCount }}</span>
                </el-form-item>
                <el-form-item>
                  <span>{{ $t('lang_pack.proMonitor.dayNormal') }}</span> <span class="normal">{{ flowStatisticalData.dayCountOk }}</span>
                </el-form-item>
                <el-form-item>
                  <span>{{ $t('lang_pack.proMonitor.dayAbnormal') }}</span> <span class="error">{{ flowStatisticalData.dayCountNg }}</span>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <div class="wrapRowss">
      <el-row :gutter="12" class="wrapRowItem row">
        <el-col v-for="item in flowTaskData" :key="item.me_flow_task_id" :span="6" class="col-12 col-sm-6 col-md-4 col-lg-3 nomargin">
          <div class="flow_main">
            <div class="flow_title">{{ item.flow_main_des }}</div>
            <el-dropdown trigger="click" placement="bottom" class="flow_menu">
              <span type="text" class="text">...</span>
              <el-dropdown-menu slot="dropdown">
                <span @click="cancelFlowChart(item)">
                  <el-dropdown-item>{{ $t('lang_pack.proMonitor.cancel') }}</el-dropdown-item>
                </span>
                <!-- <el-dropdown-item>重启流程</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
            <div class="flow_info overflowText">{{ $t('lang_pack.proMonitor.time') }}{{ item.start_date }}</div>
            <el-tooltip class="item" effect="dark" :content="item.task_info" placement="top">
              <div class="flow_info overflowText">{{ $t('lang_pack.proMonitor.info') }}{{ item.task_info }}</div>
            </el-tooltip>
            <div class="flow_info">{{ $t('lang_pack.proMonitor.step') }}{{ item.step_mod_des }}&nbsp;&nbsp;<el-button type="text" class="flow_button" @click="opendFlowTask(item)">{{ $t('lang_pack.proMonitor.view') }}</el-button></div>
            <el-tooltip class="item" effect="dark" :content="item.log_msg" placement="top">
              <div class="flow_info overflowText">{{ $t('lang_pack.proMonitor.log') }}{{ item.log_msg }}</div>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
      <!-- <el-dialog :fullscreen="true" :show-close="false" custom-class="flow-task-dialog" :visible.sync="flowTaskDialogVisible"> -->
      <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="flowTaskDialogVisible" direction="rtl" size="100%">
        <flowTask v-if="flowTaskDialogVisible" ref="flowTask" :flow_task="currentFlowTask" :cel_api="cellIp + ':' + webapiPort" :mqtt_url="cellIp + ':' + mqttPort" @closeDrawer="flowTaskDialogVisible = false" />
      </el-drawer>
      <!-- </el-dialog> -->
      <roleCheck v-if="roleCheckShow" ref="roleCheck"  :paramsCode="paramsCode"  @roleCheck="roleCheck" />
    </div>
  </div>
</template>
<script>
import roleCheck from '@/views/core/hmi/roleCheck'
import crudFlowMain from '@/api/core/flow/rcsFlowMain'
import { mapGetters } from 'vuex'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import flowTask from '@/views/core/flow/task/task'
import axios from 'axios'

export default {
  name: 'STATION_FLOW',
  components: { flowTask,roleCheck },
  data() {
    return {
      height: document.documentElement.clientHeight - 40,
      stepDes: '通知EAP任务已接收',
      query: {
        line_code: '',
        device_code_des: '',
        flow_main_code_des: '',
        enable_flag: '',
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'flow_main_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      cellId: '', // 单元ID
      stationId: '', // 工位ID
      stationCode: '', // 工位编码
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      flowMainId: '',
      flowMainList: [],
      flowTaskData: [],
      flowStatisticalData: [],
      flowTaskDialogVisible: false,
      timer: '',
      currentFlowTask: {},
      flowChartObj:{},
      roleCheckShow: false,
      paramsCode:'Hmi_FlowchartCheckPwd'
    }
  },
  computed: {
    ...mapGetters(['permission_routers'])
  },
  mounted() {
    const that = this
    this.timer = setInterval(function() {
      that.getFlowTaskData()
      that.getFlowStatisticalData()
    }, 5000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  created() {
    this.stationId = this.$route.query.station_id
    this.stationCode = this.$route.query.station_code
    this.getCellIp()
    this.getFlowMainList()
  },
  methods: {
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.cellId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getFlowTaskData()
            this.getFlowStatisticalData()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.proMonitor.queryException'), type: 'error' })
        })
    },
    getFlowMainList() {
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        station_id: this.stationId
      }
      crudFlowMain
        .sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.flowMainList = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.proMonitor.queryException'),
            type: 'error'
          })
        })
    },
    getFlowTaskData() {
      var method = '/cell/core/flow/CoreFlowTaskListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' +8089  + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' +8089  + method
      }
      const data = {
        station_id: this.stationId,
        flow_main_id: this.flowMainId
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            this.flowTaskData = defaultQuery.data.data
          } else {
            this.flowTaskData = []
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.flowTaskData = []
          this.$message({ message: this.$t('lang_pack.proMonitor.queryException') + ':' + ex, type: 'error' })
        })
    },
    getFlowStatisticalData() {
      var method = '/cell/core/flow/CoreFlowGetStatisticalData'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' +8089  + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' +8089  + method
      }
      const data = {
        station_id: this.stationId
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0 && defaultQuery.data.result !== '') {
            this.flowStatisticalData = JSON.parse(defaultQuery.data.result)
          } else {
            this.flowStatisticalData = []
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.flowStatisticalData = []
          this.$message({ message: this.$t('lang_pack.proMonitor.queryException') + ':' + ex, type: 'error' })
        })
    },
    cancelFlowChart(item){
      this.flowChartObj = item
      this.roleCheckShow = true
    },
    getCancelProcess(){
      var method = '/cell/core/flow/CoreFlowCancel';
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' + 8089  + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' + 8089  + method
      }
      const data = {
          me_flow_task_id:this.flowChartObj.me_flow_task_id
        }
        axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        }).then(res=>{
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            this.$message({ message: this.$t('lang_pack.proMonitor.cancelSucess'), type: 'success' })
          }
        })
         .catch((err) => {
          this.$message({ message: this.$t('lang_pack.proMonitor.abnormal')+ err, type: 'error' })
        })
      // this.$confirm('此操作会取消当前流程, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(()=>{
       
        
      // })
      // .catch((err) => {
      //   this.$message({ message: '查询异常：' + err, type: 'error' })
      // })
    },
    opendFlowTask(item) {
      this.currentFlowTask = item
      this.flowTaskDialogVisible = true
    },
    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      this.roleCheckShow = false
        if (status === 'OK') {
          this.getCancelProcess()
        }
    },
  }
}
</script>
<style lang="scss">
.flow_main {
  background-color: #f1f3ff;
  padding-top: 50px;
  border-radius: 5px;
  border-color: rgba(0, 0, 0, 0.09);
  box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  transition: 0.2s;
}
.flow_main:hover {
  border-color: rgba(0, 0, 0, 0.09);
  box-shadow: 0 2px 8px rgb(191 200 220);
}
.flow_title {
  font-weight: bold;
  color: #ffffff;
  margin-top: -35px;
  //   position: fixed;
  cursor: pointer;
  background-image: url('~@/assets/images/label_bg.png');
  background-position: 0;
  background-repeat: no-repeat;
  height: 35px;
  line-height: 35px;
  padding-left: 28px;
  font-size: 13px;
  margin-left: -8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.flow_info {
  line-height: 25px;
  color: #333;
  font-size: 12px;
  padding: 0 10px;
}
.overflowText{
  /* 1.溢出隐藏 */
  overflow: hidden;
  /* 2.用省略号来代替超出文本 */
  text-overflow: ellipsis;
  /* 3.设置盒子属性为-webkit-box  必须的 */
  display: -webkit-box;
  /* 4.-webkit-line-clamp 设置为2，表示超出2行的部分显示省略号，如果设置为3，那么就是超出3行部分显示省略号 */
  -webkit-line-clamp: 2;
  /* 5.字面意思：单词破坏：破坏英文单词的整体性，在英文单词还没有在一行完全展示时就换行  即一个单词可能会被分成两行展示 */
  word-break: break-all;
  /* 6.盒子实现多行显示的必要条件，文字是垂直展示，即文字是多行展示的情况下使用 */
  -webkit-box-orient: vertical;
}
.flow_button {
  padding: 0;
  font-weight: 600;
  color: #79a0f1;
}
.flow_menu {
  float: right;
  margin-top: -55px;
  margin-right: 10px;
}
.flow_menu .text {
  color: #838585;
  font-weight: 600;
  font-size: 20px;
  height: 20px;
  cursor: pointer;
}
.wrapRowss {
  background: #ffffff;
  padding: 10px 15px;
  border-radius: 4px;
  margin-top: 10px;
  .wrapRowItem {
    // margin-top: 12px;
    .nomargin {
      margin-bottom: 0;
      padding: 6px;
    }
  }
  .wrapRowItem:first-child {
    margin-top: 0;
  }
}
.formChildFirst {
  display: flex;
  align-items: center;
  button {
    margin-left: 10px;
  }
}
.ElFormSecondStyle {
  .el-form-item {
    margin: 0 10px;
    .el-form-item__content {
      display: flex;
      align-items: center;
    }
    .normal {
      color: #79a0f1;
    }
    .error {
      color: red;
    }
    span {
      white-space: nowrap;
      color: #333;
    }
    span:last-child {
      font-weight: bold;
      font-size: 20px;
    }
  }
  .el-form-item:last-child {
    margin-right: 0;
  }
}
.flow-task-dialog .el-dialog__body {
  padding: 0px;
  overflow-y: auto;
}
.flow-task-dialog .el-dialog__header {
  padding: 0px;
}
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px;
}
</style>
