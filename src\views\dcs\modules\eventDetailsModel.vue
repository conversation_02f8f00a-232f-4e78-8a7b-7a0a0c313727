<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="事件明细" width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 天车编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="car_code"
              label="天车编码"
              min-width="80"
              align="center"
            />
            <!-- 库位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_code"
              label="库位号"
              min-width="80"
              align="center"
            />
            <!-- 库存方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_way"
              label="库存方式"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.STOCK_WAY[scope.row.stock_way] }}
              </template>
            </el-table-column>
            <!-- 任务方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_way"
              label="任务方式"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_WAY[scope.row.task_way] }}
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              label="任务类型"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              label="任务来源"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              label="任务号"
              min-width="100"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              label="批次号"
              min-width="100"
              align="center"
            />
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              label="型号"
              min-width="100"
              align="center"
            />
            <!-- 物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              label="物料号"
              min-width="130"
              align="center"
            />
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              label="长"
              min-width="130"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              label="宽"
              min-width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              label="厚"
              min-width="100"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              label="重"
              min-width="100"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              label="材质"
              min-width="130"
              align="center"
            />
            <!-- 起始库位 -->
          </el-table>
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudMeStockEvent from '@/api/dcs/core/wms/meStockEvent'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'EVENTDETAILSMODEL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '事件明细',
      // 唯一字段
      idField: 'transfer_task_id',
      // 排序
      sort: ['transfer_task_id asc'],
      // CRUD Method
      crudMethod: { ...crudMeStockEvent },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        stock_id: this.propsData.stock_id
      }
    })
  },
  props: {
    stock_id: {
      type: Number
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false
    }
  },
  dicts: ['STOCK_WAY', 'TASK_WAY', 'TASK_TYPE', 'EXECUTE_STATUS', 'DATA_SOURCES'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
</style>
