<template>
  <div class="app-container">
    <!--条码NG规则-->
    <el-card shadow="never">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
          <el-form-item label="条码排废名称" prop="dxbar_ng_name">
            <el-input v-model.number="form.dxbar_ng_name" />
          </el-form-item>
          <el-form-item label="条码起始位置" prop="start_index">
            <el-input v-model="form.start_index" />
          </el-form-item>
          <el-form-item label="条码结束位置" prop="end_index">
            <el-input v-model="form.end_index" />
          </el-form-item>
          <el-form-item label="值集合" prop="value_list">
            <el-input v-model="form.value_list" />
          </el-form-item>

          <!--快速编码：NG_WAY-->
          <el-form-item label="值集合排废" prop="ng_way">
            <fastCode fastcode_group_code="NG_WAY" :fastcode_code.sync="form.ng_way" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="排废分配槽位" prop="ng_rack_num">
            <el-input v-model="form.ng_rack_num" />
          </el-form-item>

          <el-form-item label="有效标识">
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24" class="elTableItem">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="478" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="mz_dxbar_ng_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="dxbar_ng_name" label="条码排废名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="start_index" label="条码起始位置" />
            <el-table-column  :show-overflow-tooltip="true" prop="end_index" label="条码结束位置" />
            <el-table-column  :show-overflow-tooltip="true" prop="value_list" label="值集合" />

            <el-table-column  label="值集合排废" align="center" prop="ng_way" width="100">
              <!-- 零件类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.NG_WAY[scope.row.ng_way] }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="ng_rack_num" label="排废分配槽位" />
            <el-table-column  :label="有效标识" align="center" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMzDxBarRule from '@/api/mes/core/mzDxBarRule'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  mz_dxbar_ng_id: '',
  mz_id: '',
  dxbar_ng_name: '',
  start_index: '0',
  end_index: '0',
  value_list: '',
  ng_way: '',
  ng_rack_num: '0',
  enable_flag: 'Y'
}
export default {
  name: 'MzDxBarRule',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    mz_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '条码NG规则',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mz_dxbar_ng_id',
      // 排序
      sort: ['mz_dxbar_ng_id asc'],
      // CRUD Method
      crudMethod: { ...crudMzDxBarRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_mz_dxbar:add'],
        edit: ['admin', 'c_mes_fmod_recipe_mz_dxbar:edit'],
        del: ['admin', 'c_mes_fmod_recipe_mz_dxbar:del'],
        down: ['admin', 'c_mes_fmod_recipe_mz_dxbar:down']
      },
      rules: {
        // 提交验证规则
        dxbar_ng_name: [{ required: true, message: '请输入电芯条码排废名称', trigger: 'blur' }]
      }
    }
  },
  watch: {
    mz_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.mz_id = this.mz_id
        this.crud.toQuery()
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'NG_WAY'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.mz_id = this.mz_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.mz_id = this.mz_id
      return true
    }
  }
}
</script>
