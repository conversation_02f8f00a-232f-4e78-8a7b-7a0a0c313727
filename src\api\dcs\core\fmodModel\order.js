import request from '@/utils/request'
// 查询送货单号
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/RetrieveWarehouseToDoDetails',
    method: 'post',
    data
  })
}
export function dcsCalculate(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsCalculateWeightAllocation',
    method: 'post',
    data
  })
}
export function dcsSave(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsSaveWeightAllocation',
    method: 'post',
    data
  })
}

export default { sel, dcsCalculate, dcsSave }
