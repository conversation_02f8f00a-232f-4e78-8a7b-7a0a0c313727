<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="天车编码:">
                <!-- 天车编码 -->
                <el-input v-model="query.car_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="库区域:">
                <!-- 库区域 -->
                <el-select v-model="query.ware_house" clearable filterable>
                  <el-option v-for="item in dict.WARE_HOUSE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务类型:">
                <!-- 任务类型 -->
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option v-for="item in dict.TASK_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号:">
                <!-- 任务号 -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号:">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="型号:">
                <!-- 型号 -->
                <el-select v-model="query.model_type" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_type"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料号:">
                <!-- 物料号 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务开始时间:">
                <!-- 任务开始时间 -->
                <el-date-picker
                  v-model="query.task_start_date"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务结束时间:">
                <!-- 任务结束时间 -->
                <el-date-picker
                  v-model="query.task_end_date"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.sortingResults.stationCode')" prop="station_code">
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partBarcodeNumber')" prop="part_barcode">
                <!-- 零件条码 -->
                <el-input v-model="form.part_barcode" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partCode')" prop="part_code">
                <!-- 零件编码 -->
                <el-input v-model="form.part_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.PartType')" prop="part_type">
                <!-- 零件类型 -->
                <el-input v-model="form.part_type" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_task_id }}</el-descriptions-item>
                  <el-descriptions-item label="调度任务循环代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cycle_code }}</el-descriptions-item>
                  <el-descriptions-item label="天车编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_code }}</el-descriptions-item>
                  <el-descriptions-item label="库区域" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.WARE_HOUSE[props.row.ware_house] }}</el-descriptions-item>
                  <el-descriptions-item label="任务来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content"> {{ dict.label.DATA_SOURCES[props.row.task_from] }}</el-descriptions-item>
                  <el-descriptions-item label="任务方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.TASK_WAY[props.row.task_way] }}</el-descriptions-item>
                  <el-descriptions-item label="任务类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.TASK_TYPE[props.row.task_type] }}</el-descriptions-item>
                  <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                  <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                  <el-descriptions-item label="批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_num }}</el-descriptions-item>
                  <el-descriptions-item label="物料号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                  <el-descriptions-item label="物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_des }}</el-descriptions-item>
                  <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="长" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_length }}</el-descriptions-item>
                  <el-descriptions-item label="宽" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_width }}</el-descriptions-item>
                  <el-descriptions-item label="厚" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_height }}</el-descriptions-item>
                  <el-descriptions-item label="重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_weight }}</el-descriptions-item>
                  <el-descriptions-item label="材质" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_texture }}</el-descriptions-item>
                  <el-descriptions-item label="起始库位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.from_stock_code }}</el-descriptions-item>
                  <el-descriptions-item label="目标库位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.to_stock_code }}</el-descriptions-item>
                  <el-descriptions-item label="任务开始时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_start_date }}</el-descriptions-item>
                  <el-descriptions-item label="任务结束时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_end_date }}</el-descriptions-item>
                  <el-descriptions-item label="消耗时间(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cost_time }}</el-descriptions-item>
                  <el-descriptions-item label="是否检查了辊道型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_check_gd_flag == 'Y' ? '是':'否' }}</el-descriptions-item>
                  <el-descriptions-item label="检查辊道时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_check_gd_date }}</el-descriptions-item>
                  <el-descriptions-item label="是否检查了钢板型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_flag == 'Y' ? '是':'否' }}</el-descriptions-item>
                  <el-descriptions-item label="检查型号结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_result }}</el-descriptions-item>
                  <el-descriptions-item label="检查型号时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之启动时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_start_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之取消时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_cancel_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之暂停时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_stop_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之启动者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_start_by }}</el-descriptions-item>
                  <el-descriptions-item label="告之取消者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_cancel_by }}</el-descriptions-item>
                  <el-descriptions-item label="告之暂停者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_stop_by }}</el-descriptions-item>
                  <el-descriptions-item label="任务执行状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.EXECUTE_STATUS[props.row.execute_status] }}</el-descriptions-item>
                  <el-descriptions-item label="任务执行错误信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.execute_error_msg }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 天车编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="car_code"
              label="天车编码"
              width="80"
              align="center"
            />
            <!-- 库区域 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ware_house"
              label="库区域"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
              </template>
            </el-table-column>
            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              label="任务来源"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 任务方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_way"
              label="任务方式"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_WAY[scope.row.task_way] }}
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              label="任务类型"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              label="任务号"
              width="80"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              label="批次号"
              width="100"
              align="center"
            />
            <!-- 物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              label="物料号"
              width="100"
              align="center"
            />
            <!-- 物料描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_des"
              label="物料描述"
              width="100"
              align="center"
            />
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              label="型号"
              width="100"
              align="center"
            />
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              label="长"
              width="130"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              label="宽"
              width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              label="厚"
              width="100"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              label="重"
              width="100"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              label="材质"
              width="130"
              align="center"
            />
            <!-- 起始库位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_stock_code"
              label="起始库位"
              width="100"
              align="center"
            />
            <!-- 目标库位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_stock_code"
              label="目标库位"
              width="130"
              align="center"
            />
            <!-- 任务开始时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_start_date"
              label="任务开始时间"
              width="130"
              align="center"
            />
            <!-- 任务结束时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_end_date"
              label="任务结束时间"
              width="130"
              align="center"
            />
            <!-- 消耗时间(秒) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cost_time"
              label="消耗时间(秒)"
              width="130"
              align="center"
            />
            <!-- 是否检查了辊道 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_check_gd_flag"
              label="是否检查了辊道"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.finish_check_gd_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 检查辊道时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_check_gd_date"
              label="检查辊道时间"
              width="130"
              align="center"
            />
            <!-- 是否检查型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="check_model_flag"
              label="是否检查型号"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.check_model_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 检查型号时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="check_model_date"
              label="检查型号时间"
              width="130"
              align="center"
            />
            <!-- 检查型号结果 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="check_model_result"
              label="检查型号结果"
              width="130"
              align="center"
            />
            <!-- 任务执行状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="execute_status"
              label="任务执行状态"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.EXECUTE_STATUS[scope.row.execute_status] }}
              </template>
            </el-table-column>
            <!-- 任务执行错误信息 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="execute_error_msg"
              label="任务执行错误信息"
              width="130"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMeCarTaskHis from '@/api/dcs/core/wms/meCarTaskHis'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  car_task_id: '',
  cycle_code: '',
  car_code: '',
  ware_house: '',
  task_from: '',
  task_way: '',
  task_type: '',
  task_num: '',
  serial_num: '',
  lot_num: '',
  material_code: '',
  material_des: '',
  model_type: '',
  m_length: '',
  m_width: '',
  m_height: '',
  m_weight: '',
  m_texture: '',
  from_stock_code: '',
  to_stock_code: '',
  task_start_date: '',
  task_end_date: '',
  cost_time: '',
  finish_check_gd_flag: 'Y',
  finish_check_gd_date: '',
  check_model_flag: 'Y',
  check_model_date: '',
  check_model_result: '',
  tell_start_date: '',
  tell_cancel_date: '',
  tell_stop_date: '',
  tell_start_by: '',
  tell_cancel_by: '',
  tell_stop_by: '',
  execute_status: '',
  execute_error_msg: ''
}
export default {
  name: 'MECARTASKHIS',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: 'WMS天车调度任务历史表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'car_task_id',
      // 排序
      sort: ['car_task_id asc'],
      // CRUD Method
      crudMethod: { ...crudMeCarTaskHis },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 360,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
      },
      modelList: []
    }
  },
  // 数据字典
  dicts: ['STOCK_WAY', 'TASK_WAY', 'TASK_TYPE', 'EXECUTE_STATUS', 'DATA_SOURCES', 'WARE_HOUSE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 360
    }
  },
  created: function() {
    this.getModelType()
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudSortResult
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              mo_id: data.mo_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    getModelType() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
