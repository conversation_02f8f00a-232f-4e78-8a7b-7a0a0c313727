
/* selectModalConfig.js搭配selectModal.vue配置
* <AUTHOR>
* @param {} options <br>
* @date 2023-07-05 ~ 2023-07-06 编写
* @return crud instance.
* @example
*/
// eslint-disable-next-line no-unused-vars
function getSelectData(v) {
  var selectData = {
    // 喷码信息
    pmxx: {
      'api': 'aisEsbWeb/dcs/hmi/DcsApsGetSprayInfo',
      'title': '喷码信息',
      'width': '70%',
      'columns': [{
        'label': '重',
        'align': 'center',
        'prop': 'part_weight',
        'width': '120'
      }, {
        'label': '工位号',
        'align': 'center',
        'prop': 'station_code',
        'width': '120'
      }, {
        'label': '零件条码',
        'align': 'center',
        'prop': 'part_barcode',
        'width': '120'
      }, {
        'label': '零件图号',
        'align': 'center',
        'prop': 'part_draw',
        'width': '120'
      }, {
        'label': '切割时间',
        'align': 'center',
        'prop': 'cut_datetime',
        'width': '120'
      }, {
        'label': '工艺路线编码',
        'align': 'center',
        'prop': 'craft_path_code',
        'width': '120'
      }, {
        'label': 'X坐标',
        'align': 'center',
        'prop': 'print_top_x',
        'width': '120'
      }, {
        'label': 'X坐标',
        'align': 'center',
        'prop': 'print_top_x',
        'width': '120'
      }, {
        'label': 'Y坐标',
        'align': 'center',
        'prop': 'print_top_y',
        'width': '120'
      }, {
        'label': '零件类型',
        'align': 'center',
        'prop': 'part_type',
        'width': '120',
        'model': 'dict',
        'type': 'PART_TYPE'
      }, {
        'label': '长',
        'align': 'center',
        'prop': 'part_length',
        'width': '120'
      }, {
        'label': '宽',
        'align': 'center',
        'prop': 'part_width',
        'width': '120'
      }, {
        'label': '高',
        'align': 'center',
        'prop': 'part_thickness',
        'width': '120'
      }],
      'checkType': '',
      search: []
    },
    // 任务状态
    rwzt: {
      'api': 'aisEsbWeb/dcs/hmi/task-status',
      'title': '任务列表状态',
      'width': '80%',
      'columns': [{
        'label': '当前工位',
        'align': 'center',
        'prop': 'now_station_code'
      }, {
        'label': '任务号',
        'align': 'center',
        'prop': 'task_num'
      }, {
        'label': '钢板型号',
        'align': 'center',
        'prop': 'model_type'
      }, {
        'label': '切割文件名称',
        'align': 'center',
        'prop': 'nc_name'
      }, {
        'label': '切割类型',
        'align': 'center',
        'prop': 'cut_type',
        'model': 'dict',
        'type': 'CUT_TYPE'// 代表要查字典
      }, {
        'label': '目标切割机',
        'align': 'center',
        'prop': 'cut_code'
      }, {
        'label': '任务状态',
        'align': 'center',
        'prop': 'task_status',
        'model': 'dict',
        'type': 'PROD_TASK_STATUS'
      }],
      'checkType': '',
      search: [
        {
          show: true,
          key: 'task_num',
          label: '任务号',
          inputType: 'input',
          mode: '',
          selectDataSource: '',
          selectData: []
        },
        {
          show: true,
          key: 'model_type',
          label: '钢板型号',
          inputType: 'selectInter',
          mode: '',
          selectDataSource: '',
          selectData: [],
          type: 'MODAL_LIST'
        },
        {
          show: true,
          key: 'cut_code',
          label: '目标切割机',
          inputType: 'input',
          mode: '',
          selectDataSource: '',
          selectData: []
        },
        {
          show: true,
          key: 'cut_type',
          label: '切割类型',
          inputType: 'select',
          mode: '',
          selectDataSource: '',
          selectData: [],
          type: 'CUT_TYPE'
        },
        {
          show: true,
          key: 'task_status',
          label: '任务状态',
          inputType: 'select',
          mode: '',
          selectDataSource: '',
          selectData: [],
          type: 'PROD_TASK_STATUS'
        },
        {
          show: true,
          key: 'station_code',
          label: '当前位置',
          inputType: 'input',
          mode: '',
          selectDataSource: '',
          selectData: []
        }
      ]
    },
    // 切割历史
    qgls: {
      'api': 'aisEsbWeb/dcs/hmi/DcsApsCuttingHistory',
      'title': '切割历史记录',
      'width': '80%',
      'columns': [{
        'label': '上传标识',
        'align': 'center',
        'prop': 'up_flag',
        'model': 'boole',
        'width': '120'
      }, {
        'label': '工位号',
        'align': 'center',
        'prop': 'station_code',
        'width': '120'
      }, {
        'label': '工位描述',
        'align': 'center',
        'prop': 'station_des',
        'width': '120'
      }, {
        'label': '任务号',
        'align': 'center',
        'prop': 'task_num',
        'width': '120'
      }, {
        'label': '批次号',
        'align': 'center',
        'prop': 'lot_num',
        'width': '120'
      }, {
        'label': '物料编码',
        'align': 'center',
        'prop': 'material_code',
        'width': '120'
      }, {
        'label': '物料描述',
        'align': 'center',
        'prop': 'material_des',
        'width': '120'
      }, {
        'label': '零件图号',
        'align': 'center',
        'prop': 'material_draw',
        'width': '120'
      }, {
        'label': '型号',
        'align': 'center',
        'prop': 'model_type',
        'width': '120'
      }, {
        'label': '长',
        'align': 'center',
        'prop': 'm_length',
        'width': '120'
      }, {
        'label': '宽',
        'align': 'center',
        'prop': 'm_width',
        'width': '120'
      }, {
        'label': '厚',
        'align': 'center',
        'prop': 'm_height',
        'width': '120'
      }, {
        'label': '重',
        'align': 'center',
        'prop': 'm_weight',
        'width': '120'
      }, {
        'label': '放行时间',
        'align': 'center',
        'prop': 'pass_date',
        'width': '120'
      }, {
        'label': '是否完成报工',
        'align': 'center',
        'prop': 'bg_flag',
        'model': 'boole',
        'width': '120'
      }, {
        'label': '有效标识',
        'align': 'center',
        'prop': 'enable_flag',
        'model': 'boole',
        'width': '120'
      }],
      'checkType': '',
      search: []
    },
    // 查看零件明细
    ckljmx: {
      'api': 'aisEsbWeb/dcs/core/DcsApsTaskResolveSelect',
      'title': '查看零件明细',
      'width': '80%',
      'columns': [{
        'label': '计划花费时间/s',
        'align': 'center',
        'prop': 'plan_time',
        'width': '120'
      }, {
        'label': '工位号',
        'align': 'center',
        'prop': 'station_code',
        'width': '120'
      }, {
        'label': '零件条码',
        'align': 'center',
        'prop': 'part_code',
        'width': '120'
      }, {
        'label': '零件图号',
        'align': 'center',
        'prop': 'part_draw',
        'width': '120'
      }, {
        'label': '零件类型',
        'align': 'center',
        'prop': 'part_type',
        'width': '120',
        'model': 'dict',
        'type': 'PART_TYPE'// 代表要查字典
      }, {
        'label': '零件长',
        'align': 'center',
        'prop': 'part_length',
        'width': '120'
      }, {
        'label': '零件宽',
        'align': 'center',
        'prop': 'part_width',
        'width': '120'
      }, {
        'label': '零件重',
        'align': 'center',
        'prop': 'part_weight',
        'width': '120'
      }],
      'checkType': '',
      search: []
    },
    // 查看过站明细
    ckgzmx: {
      'api': 'aisEsbWeb/dcs/hmi/DcsApsMeStationMisRecordDetail',
      'title': '查看过站明细',
      'width': '80%',
      'columns': [{
        'label': '是否报工',
        'align': 'center',
        'prop': 'bg_flag',
        'width': '120',
        'model': 'boole'
      }, {
        'label': '生产任务号',
        'align': 'center',
        'prop': 'task_num',
        'width': '120'
      }, {
        'label': '当前工位号',
        'align': 'center',
        'prop': 'station_code',
        'width': '120'
      }, {
        'label': '来源任务号',
        'align': 'center',
        'prop': 'task_from',
        'width': '120'
      }, {
        'label': '切割机(材质)',
        'align': 'center',
        'prop': 'cut_texture',
        'width': '120'
      }, {
        'label': '到达时间',
        'align': 'center',
        'prop': 'arrive_date',
        'width': '120'
      }, {
        'label': '离开时间',
        'align': 'center',
        'prop': 'leave_date',
        'width': '120'
      }, {
        'label': '工位消耗时间',
        'align': 'center',
        'prop': 'cost_time',
        'width': '120'
      }, {
        'label': '托盘号',
        'align': 'center',
        'prop': 'pallet_num',
        'width': '120'
      }, {
        'label': '钢板型号',
        'align': 'center',
        'prop': 'model_type',
        'width': '120'
      }],
      'checkType': '',
      search: []
    },
    // 查看料框明细
    cklkmx: {
      'api': '',
      'title': '查看料框明细',
      'width': '80%',
      'columns': [{
        'label': '零件类型',
        'align': 'center',
        'prop': 'cost_time',
        'width': '120'
      }, {
        'label': '料框任务号',
        'align': 'center',
        'prop': 'task_num',
        'width': '120'
      }, {
        'label': '料框号',
        'align': 'center',
        'prop': 'station_code',
        'width': '120'
      }, {
        'label': '料框零件',
        'align': 'center',
        'prop': 'task_from',
        'width': '120'
      }, {
        'label': '零件长',
        'align': 'center',
        'prop': 'cut_texture',
        'width': '120'
      }, {
        'label': '零件宽',
        'align': 'center',
        'prop': 'arrive_date',
        'width': '120'
      }, {
        'label': '零件厚',
        'align': 'center',
        'prop': 'leave_date',
        'width': '120'
      }],
      'checkType': '',
      search: []
    },
    // 查看库存明细
    ckkcmx: {
      'api': 'aisEsbWeb/dcs/project/wms/DcsWmsMapMeStockDetail',
      'title': '库存明细',
      'width': '70%',
      'columns': [
        {
          'label': '任务类型',
          'align': 'center',
          'prop': 'task_type',
          'width': '80',
          'model': 'dict',
          'type': 'TASK_TYPE'// 代表要查字典
        },
        {
          'label': '入库时间',
          'align': 'center',
          'prop': 'stock_d_time',
          'width': '160'
        }, {
          'label': '任务来源',
          'align': 'center',
          'prop': 'task_from',
          'width': '70'
        }, {
          'label': '任务号',
          'align': 'center',
          'prop': 'task_num',
          'width': '180'
        }, {
          'label': '零件编号',
          'align': 'center',
          'prop': 'material_code',
          'width': '80'
        }, {
          'label': '项目编码',
          'align': 'center',
          'prop': 'lot_num',
          'width': '100'
        }, {
          'label': '零件层次',
          'align': 'center',
          'prop': 'stock_index',
          'width': '80'
        }, {
          'label': '焊接任务号',
          'align': 'center',
          'prop': 'kit_task_num',
          'width': '140'
        }, {
          'label': '组号',
          'align': 'center',
          'prop': 'kit_structure_no',
          'width': '80'
        }, {
          'label': '零件类型',
          'align': 'center',
          'prop': 'kit_material_type',
          'width': '80',
          'model': 'dict',
          'type': 'KIT_MATERIAL_TYPE'// 代表要查字典
        }, {
          'label': '是否成套',
          'align': 'center',
          'prop': 'kit_flag',
          'width': '80',
          'model': 'dict',
          'type': 'KIT_FLAG'// 代表要查字典
        }, {
          'label': '零件Z坐标',
          'align': 'center',
          'prop': 'location_z',
          'width': '80'
        }, {
          'label': '库存是否锁定',
          'align': 'center',
          'prop': 'lock_flag',
          'width': '130',
          'model': 'dict',
          'type': 'LOCK_FLAG'// 代表要查字典
        }, {
          'label': '入库方式',
          'align': 'center',
          'prop': 'task_way',
          'width': '110',
          'model': 'dict',
          'type': 'TASK_WAY'// 代表要查字典
        }],
      'checkType': '',
      search: []
    },
    // 分拣入库任务
    fjrkrw: {
      'api': 'aisEsbWeb/dcs/project/wms/DcsWmsFjTaskSelect',
      'title': '分拣入库任务',
      'width': '70%',
      'columns': [
        {
          'label': '零件放置结束单元格',
          'align': 'center',
          'prop': 'end_cell_col',
          'width': '160'
        },
        {
          'label': '任务号',
          'align': 'center',
          'prop': 'task_num'
        },
        {
          'label': '项目编码',
          'align': 'center',
          'prop': 'project_code'
        }, {
          'label': '零件编号',
          'align': 'center',
          'prop': 'material_code'
        }, {
          'label': '零件长',
          'align': 'center',
          'prop': 'material_length'
        }, {
          'label': '零件宽',
          'align': 'center',
          'prop': 'material_width'
        }, {
          'label': '零件厚',
          'align': 'center',
          'prop': 'material_thickness'
        }, {
          'label': '垛位区域',
          'align': 'center',
          'prop': 'stock_group_code'
        }, {
          'label': '垛位号',
          'align': 'center',
          'prop': 'stock_code'
        }, {
          'label': '零件放置横坐标',
          'align': 'center',
          'prop': 'position_x',
          'width': '130'
        }, {
          'label': '零件放置纵坐标',
          'align': 'center',
          'prop': 'position_y',
          'width': '130'
        },
        {
          'label': '零件放置高坐标',
          'align': 'center',
          'prop': 'position_z',
          'width': '130'
        },
        {
          'label': '抓取方向',
          'align': 'center',
          'prop': 'grab_direction'
        },
        {
          'label': '入库任务开始时间',
          'align': 'center',
          'prop': 'start_date',
          'width': '160'
        },
        {
          'label': '入库任务结束时间',
          'align': 'center',
          'prop': 'end_date',
          'width': '160'
        },
        {
          'label': '任务状态',
          'align': 'center',
          'prop': 'task_status'
        },
        {
          'label': '大车偏移量',
          'align': 'center',
          'prop': 'off_set_x',
          'width': '100'
        },
        {
          'label': '小车偏移量',
          'align': 'center',
          'prop': 'off_set_y',
          'width': '100'
        },
        {
          'label': '零件放置开始行',
          'align': 'center',
          'prop': 'start_cell_row',
          'width': '160'
        }, {
          'label': '零件放置结束行',
          'align': 'center',
          'prop': 'end_cell_row',
          'width': '160'
        },
        {
          'label': '零件放置开始单元格',
          'align': 'center',
          'prop': 'start_cell_col',
          'width': '160'
        }
      ],
      'checkType': '',
      search: [
        { key: 'task_num', label: '任务号:', inputType: 'input' },
        { key: 'project_code', label: '项目编码:', inputType: 'input' },
        { key: 'material_code', label: '零件编号:', inputType: 'input' },
        { key: 'stock_code', label: '垛位号:', inputType: 'input' }
      ]
    },
    // 3.Wms库存查询
    Wmskccx: {
      'api': 'aisEsbWeb/dcs/project/wms/DcsWmsMapMeStockSelect',
      'title': 'Wms库存查询',
      'width': '70%',
      'columns': [
        {
          'label': '是否锁定',
          'align': 'center',
          'prop': 'lock_flag',
          'model': 'boole'
        },
        {
          'label': '库位区域',
          'align': 'center',
          'prop': 'stock_group_code'
        },
        {
          'label': '库位号',
          'align': 'center',
          'prop': 'stock_code',
          'width': '130'
        }, {
          'label': '库存量',
          'align': 'center',
          'prop': 'stock_count'
        }, {
          'label': '板长',
          'align': 'center',
          'prop': 'm_length'
        }, {
          'label': '板宽',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '起始像素单格行号',
          'align': 'center',
          'prop': 'start_cell_row',
          'width': '130'
        }, {
          'label': '终点像素单格行号',
          'align': 'center',
          'prop': 'end_cell_row',
          'width': '130'
        }, {
          'label': '起始像素单格列号',
          'align': 'center',
          'prop': 'start_cell_col',
          'width': '130'
        }, {
          'label': '终点像素单格列号',
          'align': 'center',
          'prop': 'end_cell_col',
          'width': '130'
        }, {
          'label': '中心X坐标',
          'align': 'center',
          'prop': 'position_x'
        },
        {
          'label': '中心Y坐标',
          'align': 'center',
          'prop': 'position_y'
        },
        {
          'label': '库位状态',
          'align': 'center',
          'prop': 'stock_status'
        },
        {
          'label': '是否饱和',
          'align': 'center',
          'prop': 'full_flag',
          'model': 'boole'
        }
      ],
      'checkType': '',
      search: [
        { key: 'stock_group_code', label: '库位区域:', inputType: 'input' },
        { key: 'stock_code', label: '库位号:', inputType: 'input' },
        { key: 'full_flag', label: '是否饱和:', inputType: 'selectManuallyAdd', selectData: [{ id: '1', label: '是', value: 'Y' }, { id: '2', label: '否', value: 'N' }] },
        { key: 'lock_flag', label: '是否锁定:', inputType: 'selectManuallyAdd', selectData: [{ id: '1', label: '是', value: 'Y' }, { id: '2', label: '否', value: 'N' }] }
      ]
    },
    // 能源信息
    nyxx: {
      'api': 'aisEsbWeb/eap/core/EapScreenConfigStationSel',
      'title': v.$t('lang_pack.guanghe.energyDetail'),
      'width': '60%',
      'columns': [{
        'label': v.$t('lang_pack.guanghe.parmas'),
        'align': 'center',
        'prop': 'tag_des',
        'width': ''
      }, {
        'label': v.$t('lang_pack.hmiMain.company'),
        'align': 'center',
        'prop': 'tag_uom',
        'width': ''
      }, {
        'label': v.$t('lang_pack.hmiMain.setValue'),
        'align': 'center',
        'prop': 'set_value',
        'width': ''
      }, {
        'label': v.$t('lang_pack.hmiMain.setValue'),
        'align': 'center',
        'prop': 'actualValue',
        'width': ''
      }],
      'checkType': '',
      search: []
    },
    // 按时间统任务数据
    sjrwsj: {
      'api': 'aisEsbWeb/eap/core/EapHmiTaskSelect',
      'title': v.$t('lang_pack.guanghe.timeTaskData'),
      'width': '60%',
      'columns': [{
        'label': v.$t('view.field.pdSetRecord.batch_no'),
        'align': 'center',
        'prop': 'lot_num',
        'width': ''
      }, {
        'label': v.$t('lang_pack.hmiMain.blowOff'),
        'align': 'center',
        'prop': 'port_code',
        'width': ''
      }, {
        'label': v.$t('lang_pack.hmiMain.plan'),
        'align': 'center',
        'prop': 'plan_lot_count',
        'width': ''
      }, {
        'label': v.$t('lang_pack.guanghe.finish'),
        'align': 'center',
        'prop': 'finish_count',
        'width': ''
      }],
      'checkType': '',
      search: []
    },
    // 板件履历
    bjll: {
      'api': 'aisEsbWeb/eap/core/EapHmiPanelAndReadingRateSelect',
      'title': v.$t('view.title.boardPractice'),
      'width': '60%',
      'columns': [
        {
          'label': v.$t('lang_pack.hmiMain.plateCode'),
          'align': 'center',
          'prop': 'panel_barcode',
          'width': ''
        }, {
          'label': v.$t('lang_pack.hmiMain.serial'),
          'align': 'center',
          'prop': 'panel_index',
          'width': ''
        }, {
          'label': v.$t('lang_pack.hmiMain.panelStatus'),
          'align': 'center',
          'prop': 'panel_status',
          'width': ''
        }, {
          'label': v.$t('lang_pack.messageReport.cim_msg'),
          'align': 'center',
          'prop': 'task_from',
          'width': ''
        }],
      'checkType': '',
      search: []
    },
    // 报警信息
    bjxx: {
      'api': '/cell/core/scada/CoreScadaAlarmSelect',
      'addRess': '',
      'title': v.$t('lang_pack.cuttingZone.AlarmMessage'),
      'width': '60%',
      'columns': [{
        'label': v.$t('lang_pack.cuttingZone.AlarmLevel'),
        'align': 'center',
        'prop': 'alarm_level',
        'width': ''
      }, {
        'label': v.$t('lang_pack.cuttingZone.AlarmMessage'),
        'align': 'center',
        'prop': 'alarm_des',
        'width': ''
      },
      {
        'label': v.$t('lang_pack.hmiMain.time'),
        'align': 'center',
        'prop': 'item_date',
        'width': ''
      }],
      'checkType': '',
      search: []
    },
    // 层别信息
    cbxx: {
      'api': 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjApsPlanDSel',
      'title': '层别信息',
      'width': '60%',
      'columns': [
        {
          'label': '层级排序',
          'align': 'center',
          'prop': 'level_index',
          'width': ''
        }, {
          'label': v.$t('lang_pack.taskTable.batchNumber'),
          'align': 'center',
          'prop': 'batch_num',
          'width': ''
        }, {
          'label': '半成品型号',
          'align': 'center',
          'prop': 'product_no',
          'width': ''
        }, {
          'label': 'Top层别',
          'align': 'center',
          'prop': 'top_level',
          'width': ''
        }, {
          'label': 'Bot层别',
          'align': 'center',
          'prop': 'bot_level',
          'width': ''
        }, {
          'label': '有效标识',
          'align': 'center',
          'prop': 'enable_flag',
          'model': 'boole',
          'width': ''
        }, {
          'label': '扫入是否完成了校验',
          'align': 'center',
          'prop': 'scan_check_flag',
          'model': 'boole',
          'width': ''
        }],
      'checkType': '',
      search: []
    },
    // 配板记录
    pbjl: {
      'api': 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjMeArraySel',
      'title': '配板记录',
      'width': '60%',
      'columns': [
        {
          'label': '半成品型号',
          'align': 'center',
          'prop': 'product_no',
          'width': '200'
        }, {
          'label': '时间',
          'align': 'center',
          'prop': 'item_date',
          'width': '170'
        }, {
          'label': '配板机',
          'align': 'center',
          'prop': 'port_index',
          'width': '60'
        }, {
          'label': '完工顺序',
          'align': 'center',
          'prop': 'task_index',
          'width': '80'
        }, {
          'label': '总层级',
          'align': 'center',
          'prop': 'lot_level',
          'width': '60'
        }, {
          'label': '第几层',
          'align': 'center',
          'prop': 'level_index',
          'width': '60'
        }, {
          'label': '上层板件条码',
          'align': 'center',
          'prop': 'panel_top_barcode',
          'width': '200'
        }, {
          'label': '判别代码',
          'align': 'center',
          'prop': 'set_ng_code',
          'model': 'style',
          'width': '80'
        }, {
          'label': '判别错误信息',
          'align': 'center',
          'prop': 'set_ng_msg',
          'width': '110'
        }, {
          'label': 'Top层别',
          'align': 'center',
          'prop': 'top_level',
          'width': '80'
        }, {
          'label': 'Bot层别',
          'align': 'center',
          'prop': 'bot_level',
          'width': '80'
        }, {
          'label': '订单号',
          'align': 'center',
          'prop': 'lot_num',
          'width': '160'
        }, {
          'label': '下层板件条码',
          'align': 'center',
          'prop': 'panel_bot_barcode',
          'width': '140'
        }, {
          'label': '批次号',
          'align': 'center',
          'prop': 'batch_num',
          'width': '160'
        }],
      'checkType': '',
      search: []
    },
    pljh: {
      'api': 'aisEsbWeb/core/system/CoreSysFunctionSel',
      'title': '批量计划',
      'width': '60%',
      'columns': [
        {
          'label': 'creation_date',
          'align': 'center',
          'prop': 'creation_date'
        }, {
          'label': 'function_des',
          'align': 'center',
          'prop': 'function_des'
        }],
      'checkType': '',
      search: []
    },
    // 威海wms
    dhqd: {
      'api': 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListDetailSelect',
      'title': '到货清单详细',
      'width': '80%',
      'columns': [
        {
          'label': '项目号',
          'align': 'center',
          'prop': 'project_code'
        }, {
          'label': '分段号',
          'align': 'center',
          'prop': 'block_code'
        }, {
          'label': '批次号',
          'align': 'center',
          'prop': 'lot_num'
        }, {
          'label': '序列号',
          'align': 'center',
          'prop': 'serial_num'
        }, {
          'label': '钢板钢印号',
          'align': 'center',
          'prop': 'steel_seal_code'
        }, {
          'label': '物料编码',
          'align': 'center',
          'prop': 'material_code'
        }, {
          'label': '物料描述',
          'align': 'center',
          'prop': 'material_des'
        }, {
          'label': '材质',
          'align': 'center',
          'prop': 'm_texture'
        }, {
          'label': '船级社',
          'align': 'center',
          'prop': 'ship_class'
        }, {
          'label': '长',
          'align': 'center',
          'prop': 'm_length'
        }, {
          'label': '宽',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '高',
          'align': 'center',
          'prop': 'm_height'
        }, {
          'label': '重',
          'align': 'center',
          'prop': 'm_weight'
        }, {
          'label': '型号',
          'align': 'center',
          'prop': 'model_type'
        }
      ],
      'checkType': '',
      search: []
    },
    ykjh: { // 移库计划
      'api': 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveDetailSelect',
      'title': '移库计划详细',
      'width': '80%',
      'columns': [
        {
          'label': '分道流向代码',
          'align': 'center',
          'prop': 'flow_code'
        }, {
          'label': '切割计划单号',
          'align': 'center',
          'prop': 'scli_plan_no'
        }, {
          'label': '切割顺序',
          'align': 'center',
          'prop': 'scli_plan_order'
        }, {
          'label': '序列号',
          'align': 'center',
          'prop': 'serial_num'
        }, {
          'label': '物料编码',
          'align': 'center',
          'prop': 'material_code'
        }, {
          'label': '物料描述',
          'align': 'center',
          'prop': 'material_des'
        }, {
          'label': '材质',
          'align': 'center',
          'prop': 'm_texture'
        }, {
          'label': '船级社',
          'align': 'center',
          'prop': 'ship_class'
        }, {
          'label': '长',
          'align': 'center',
          'prop': 'm_length'
        }, {
          'label': '宽',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '高',
          'align': 'center',
          'prop': 'm_height'
        }, {
          'label': '重',
          'align': 'center',
          'prop': 'm_weight'
        }
      ],
      'checkType': '',
      search: []
    },
    qgjh: { // 切割计划
      'api': 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanCutDetailSelect',
      'title': '切割计划详细',
      'width': '80%',
      'columns': [
        {
          'label': '切割顺序',
          'align': 'center',
          'prop': 'scli_plan_order'
        }, {
          'label': '序列号',
          'align': 'center',
          'prop': 'serial_num'
        }, {
          'label': '物料编码',
          'align': 'center',
          'prop': 'material_code'
        }, {
          'label': '物料描述',
          'align': 'center',
          'prop': 'material_des'
        }, {
          'label': '材质',
          'align': 'center',
          'prop': 'm_texture'
        }, {
          'label': '船级社',
          'align': 'center',
          'prop': 'ship_class'
        }, {
          'label': '长',
          'align': 'center',
          'prop': 'm_length'
        }, {
          'label': '宽',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '高',
          'align': 'center',
          'prop': 'm_height'
        }, {
          'label': '重',
          'align': 'center',
          'prop': 'm_weight'
        }
      ],
      'checkType': '',
      search: []
    },
    gdrw: { // 辊道任务
      'api': 'aisEsbWeb/dcs/project/whzsj/DcsWmsRollerTaskDetailSelect',
      'title': '辊道任务详细',
      'width': '80%',
      'columns': [
        {
          'label': '是否锁定',
          'align': 'center',
          'prop': 'lock_flag'
        },
        {
          'label': '项目号',
          'align': 'center',
          'prop': 'project_code'
        },
        {
          'label': '分段号',
          'align': 'center',
          'prop': 'block_code'
        }, {
          'label': '批次号',
          'align': 'center',
          'prop': 'lot_num'
        }, {
          'label': '序列号',
          'align': 'center',
          'prop': 'serial_num'
        }, {
          'label': '钢板钢印号',
          'align': 'center',
          'prop': 'steel_seal_code'
        }, {
          'label': '物料编码',
          'align': 'center',
          'prop': 'material_code'
        }, {
          'label': '物料描述',
          'align': 'center',
          'prop': 'material_des'
        }, {
          'label': '材质',
          'align': 'center',
          'prop': 'm_texture'
        }, {
          'label': '船级社',
          'align': 'center',
          'prop': 'ship_class'
        }, {
          'label': '长',
          'align': 'center',
          'prop': 'm_length'
        }, {
          'label': '宽',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '高',
          'align': 'center',
          'prop': 'm_height'
        }, {
          'label': '重',
          'align': 'center',
          'prop': 'm_weight'
        }, {
          'label': '型号',
          'align': 'center',
          'prop': 'model_type'
        }
      ],
      'checkType': '',
      search: []
    },
    // 慧生 库存明细
    hskcmx: {
      'api': 'aisEsbWeb/eap/project/zhcd/pbj/EapPbjMeArraySel',
      'title': '库存明细',
      'width': '80%',
      'columns': [
        {
          'label': '任务号',
          'align': 'center',
          'prop': 'scli_plan_order'
        }, {
          'label': '合同号',
          'align': 'center',
          'prop': 'serial_num'
        }, {
          'label': '批次号',
          'align': 'center',
          'prop': 'material_code'
        }, {
          'label': '层数',
          'align': 'center',
          'prop': 'material_des'
        }, {
          'label': '钢板Z坐标',
          'align': 'center',
          'prop': 'm_texture'
        }, {
          'label': '是否锁定',
          'align': 'center',
          'prop': 'ship_class'
        }, {
          'label': '入库方式',
          'align': 'center',
          'prop': 'm_length'
        }, {
          'label': '长',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '宽',
          'align': 'center',
          'prop': 'm_width'
        }, {
          'label': '高',
          'align': 'center',
          'prop': 'm_height'
        }, {
          'label': '重',
          'align': 'center',
          'prop': 'm_weight'
        }, {
          'label': '任务类型',
          'align': 'center',
          'prop': 'm_height'
        }, {
          'label': '天车编码',
          'align': 'center',
          'prop': 'm_weight'
        }
      ],
      'checkType': '',
      search: []
    }
  }
  return selectData
}

export { getSelectData }
