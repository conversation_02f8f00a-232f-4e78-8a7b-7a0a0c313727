* {
    margin: 0;
    padding: 0;
  }
  @font-face {
    font-family: YouSheBiaoTiHei;
    src: url('../../../../../assets/fonts/YouSheBiaoTiHei.ttf');
  }
  @font-face {
    font-family: light;
    src: url('../../../../../assets/fonts/LCD2B___.TTF');
  }
  #bigScreen{
    background-size:100% 100%;width:100%;height:100%;
      .el-header{
          height: 94px !important;
          padding: 10px;
          .line{
            width: 100%;
            display: flex;
            .line_1{
              width: 20px;
              height: 20px;
              border: 3px solid #066f7a;
              border-bottom: none;
              border-right: none;
            }
            .line_2{
              margin-left: 10px;
              width: 3%;
              height: 2px;
              background: #0F6593;
            }
            .line_3{
              margin-left: 10px;
              width: 50%;
              height: 2px;
              border: 1px dashed #405c5c;
              border-left: none;
              border-right: none;
              border-top: 1px dashed #405c5c;
              border-bottom: 1px dashed #405c5c;
            }
            .line_4{
              width: 5%;
              height: 4px;
              background-color: #0F6593;
              margin: 0 5px;
            }
            .line_5{
              width: 20%;
              height: 4px;
              background-color: #0F6593;
            }
            .line_6{
              width: 10%;
              height: 4px;
              background-color: #0F6593;
              margin: 0 5px;
            }
            .line_7{
              width: 4px;
              height: 4px;
              border-radius: 50%;
              background-color: #f174e7;
            }
            .line_8{
              width: 3%;
              height: 4px;
              background-color: #0F6593;
              margin: 0 5px;
            }
            .line_9{
              width: 3%;
              height: 4px;
              background-color: #0F6593;
            }
            .line_10{
              width: 7%;
              height: 4px;
              background-color: #0F6593;
              margin: 0 5px;
            }
          }
          .info{
            display: flex;
            .left{
              width: 70%;
              display: flex;
              align-items: center;
              img{
                  margin-top: -10px;
              }
              .title{
                  width: 450px;
                  font-size: 48px;
                  background-color: rgb(15, 55, 75);
                  padding:0 10px;
                  clip-path: polygon(0 0, 100% 0, 90% 100%, 0 100%);
                  backdrop-filter: blur(10px);
                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
                  color: #fff;
                  font-family: YouSheBiaoTiHei;
              }
              .trapezoid{
                width: 50px;
                height: 55px;
                background-color: #0E6393;
                clip-path: polygon(85% 0, 100% 0, 15% 100%, 0 100%);
                margin-left: -35px;
              }
              .btn-sty{
                font-size: 20px;
                color: #157eba;
                transform: skewX(-25deg);
                margin-left: 5%;
              }
              .btn{
                background-color: #091018;  
                padding:3px 20px;  
                border: 1px solid #1880af;  
                display: inline-block; 
                transform: skew(-35deg); 
                margin-left: 10px;
                font-weight: 600;
                cursor: pointer;
                span{
                  transform: skew(35deg); 
                  color: #1880af;  
                  font-size: 20px;  
                  display: inline-block; 
                }
              }
              .active{
                  border: 1px solid #c2a606;
                  span{
                    color: #c2a606;
                  }
              }
            }
            .mrt-30{
              margin-top: -30px;
            }
            .right{
              width: 30%;
              display: flex;
              align-items: center;
              justify-content: flex-end;
              .time{
                  font-size: 36px;
                  color: #5FD7F8;
                  font-family: light;
                  margin-right: 20px;
              }
              .date{
                margin-right: 20px;
                .time1{
                  font-size: 26px;
                  color: #5FD7F8;
                  font-family: YouSheBiaoTiHei;
                }
                .time2{
                  font-size: 22px;
                  color: #5FD7F8;
                  font-family: YouSheBiaoTiHei;
                }
              }
            }
          }
      }
      .el-main{
        .el-row{
          margin-top: 10px;
          margin-left: 10px !important;
          .box-info{
            height: 550px;
            padding: 0 !important;
            position: relative;
            // border: 1px solid #157eba;
          }
          .box-error{
            height: 550px;
            .step-info{
              height: 510px;
              margin-top: 10px;
              .el-steps--vertical{
                .is-vertical{
                  flex-basis: 15% !important;
                  
                }
              }
              .el-step__icon-inner{
                display: none !important;
              }
            }
          }
          .plan-btn{
            span{
              font-size: 24px;
              color: #fff;
              margin-right: 10px;
              font-family: YouSheBiaoTiHei;
              cursor: pointer;
            }
            .activeTitle{
              color: #13dcf6!important;
            }
          }
          .img-info{
            margin-top: 20px;
            img{
              width: 49%;
            }
          }
        }
      }
      .info-title{
        width: 100%;
        .error-msg{
          display: flex;
          align-items: center;
          justify-content: space-between;
          span{
            font-size: 24px;
            color: #fff;
            font-family: YouSheBiaoTiHei;
          }
          .triangle{
            width: 0;
            height: 0;  
            border-top: 8px solid transparent;  
            border-bottom: 8px solid transparent;  
            border-right: 12px solid #528DE5; /* 这里可以自定义颜色 */  
          }
        }
        .line-sty{
          width: 100%;         
          height: 2px;           
          background: #007bff;    
          position: relative;
        }
        .line-sty::after{
          content: "";              
          position: absolute;        
          right: 0;                
          width: 15px;              
          height: 2px;              
          background: #ff5733;   
        }
      }
      .table-wrapper1{
        margin-top: 10px;
        width: 100%;
        overflow-y: auto;
        table {  
          width: 100%;  
          border-collapse: collapse; /* 去掉表格间距 */  
          table-layout: auto; /* 自适应宽度 */  
        }  
        th, td {  
          width: 3%;
            padding: 5px 0; /* 内边距 */  
            text-align: left; /* 左对齐 */  
        }  
        th {  
          color: #fff;
        }
        td{
          color: #0d82c6;
        }
      }
      .table-wrapper{
        margin-top: 10px;
        width: 100%;
        overflow-y: auto;
        max-height: calc(100vh - 720px); /* 可以根据需求调整最大高度 */
        table {  
            width: 100%;  
            border-collapse: collapse; /* 去掉表格间距 */  
            table-layout: auto; /* 自适应宽度 */  
        }  
        th, td {  
            padding: 5px 0; /* 内边距 */  
            text-align: left; /* 左对齐 */  
        }  
        th {  
          color: #fff;
        }
        td{
          color: #0d82c6;
        }
      }
      .table-wrapper::-webkit-scrollbar {
          width: 3px;
          height: 3px;
          background-color: #122450;
          cursor: pointer !important;
        }
      .table-wrapper::-webkit-scrollbar-thumb {
        box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        background-color: aqua;
        cursor: pointer !important;
      }
      
  }