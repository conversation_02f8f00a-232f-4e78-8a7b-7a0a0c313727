<template>
  <!--电芯排废-->
  <el-card shadow="never">
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <!--<el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="678" highlight-current-row @selection-change="crud.selectionChangeHandler">-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" size="small" :data="tableDataTable" style="width: 100%" :header-cell-style="{ background: '#eef1f6', color: '#545559' }" height="478" max-height="678" highlight-current-row>
          <!-- Table单条操作-->
          <el-table-column  label="操作" align="center" fixed="left" width="60">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="toTableButDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="dx_num" label="电芯序号">
            <template slot-scope="scope">
              <el-input v-model="scope.row.dx_num" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="line_num" label="线体">
            <template slot-scope="scope">
              <el-select v-model="scope.row.line_num">
                <el-option v-for="item in dict.LINE_NUM" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="dx_type" label="电芯类型" width="120">
            <template slot-scope="scope">
              <el-select v-model="scope.row.dx_type">
                <el-option v-for="item in dict.DX_TYPE" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="positive_cut" label="正极剪裁量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positive_cut" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="negative_cut" label="负极剪裁量">
            <template slot-scope="scope">
              <el-input v-model="scope.row.negative_cut" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="sheet_type" label="片材类型" width="110">
            <template slot-scope="scope">
              <el-select v-model="scope.row.sheet_type">
                <el-option v-for="item in dict.DX_SHEET_TPYE" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="dx_direct" label="方向" width="110">
            <template slot-scope="scope">
              <el-select v-model="scope.row.dx_direct">
                <el-option v-for="item in dict.DX_DIRECT" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="dx_gear" label="档位">
            <template slot-scope="scope">
              <el-select v-model="scope.row.dx_gear">
                <el-option v-for="item in dict.DX_GEAR" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="dx_notes" label="备注说明" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.dx_notes" type="textarea" :rows="1" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="attribute1" label="属性1" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.attribute1" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="attribute2" label="属性2" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.attribute2" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="attribute3" label="属性3" width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.attribute3" />
            </template>
          </el-table-column>
          <el-table-column  label="有效标识" align="center" prop="enable_flag" width="100">
            <template slot-scope="scope">
              <el-select v-model="scope.row.enable_flag">
                <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <div style="text-align: center;margin-top:10px">
      <!--<el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>-->
      <el-button type="primary" size="small" icon="el-icon-plus" @click="toButAdd">新增行</el-button>
      <!-- 取消 -->
      <el-button type="primary" size="small" icon="el-icon-check" :loading="saveBtnLoading" :disabled="tableDataTable.length === 0" @click="saveData">保存</el-button>
      <!-- 确认 -->
    </div>
  </el-card>
</template>

<script>
import { sel, del, batchEdit } from '@/api/mes/core/mzDxRule'
import Cookies from 'js-cookie'
export default {
  name: 'MzDxItem',
  props: {
    mz_id: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      listLoadingTable: true,
      saveBtnLoading: false,
      tableDataTable: []
    }
  },
  watch: {
    mz_id: {
      immediate: true,
      deep: true,
      handler() {
        this.toQuery()
      }
    }
  },
  // 数据字典
  dicts: ['DX_TYPE', 'LINE_NUM', 'DX_DIRECT', 'DX_GEAR', 'DX_SHEET_TPYE', 'ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    toQuery() {
      const query = {
        user_name: Cookies.get('userName'),
        mz_id: this.mz_id,
        sort: 'dx_num',
        page: 1,
        size: 1000
      }
      sel(query)
        .then(res => {
          this.listLoadingTable = false
          const defaultQuery = JSON.parse(JSON.stringify(res))

          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
              this.refreshSeq()
            } else {
              this.tableDataTable = []
            }
          }
        })
        .catch(() => {
          this.listLoadingTable = false
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 保存
    saveData() {
      console.log(this.tableDataTable)
      for (let i = 0; i < this.tableDataTable.length; i++) {
        if (this.tableDataTable[i].dx_num === '' || this.tableDataTable[i].dx_gear === '' ||
        this.tableDataTable[i].sheet_type === '' || this.tableDataTable[i].dx_type === '' ||
        this.tableDataTable[i].dx_direct === '') {
          this.$message({ message: '[电芯序号、电芯类型、片材类型、方向、档位]不能为空，请检查无误后再保存！', type: 'info' })
          return
        }

        for (let j = i + 1; j < this.tableDataTable.length; j++) {
          if (this.tableDataTable[j].dx_num === this.tableDataTable[i].dx_num) {
            this.$message({ message: '电芯序号[' + this.tableDataTable[j].dx_num + ']重复，请修改后再保存！', type: 'info' })
            return
          }
        }
      }

      const save = {
        user_name: Cookies.get('userName'),
        dataObjs: this.tableDataTable
      }
      this.saveBtnLoading = true
      batchEdit(save)
        .then(res => {
          this.saveBtnLoading = false
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '保存成功！', type: 'success' })
            this.toQuery()
          } else {
            this.$message({ message: '保存失败：' + defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.saveBtnLoading = false
          this.$message({
            message: '保存异常',
            type: 'error'
          })
        })
    },
    toTableButDelete(row) {
      console.info(row.seq)
      if (row.mz_dx_id === '') {
        this.tableDataTable.splice(row.seq, 1)
        this.refreshSeq()
      } else {
        this.$confirm(`确认删除选中的数据?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            del({
              user_name: Cookies.get('userName'),
              id: row.mz_dx_id
            }).then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: '删除成功！', type: 'success' })
                this.tableDataTable.splice(row.seq, 1)
                this.refreshSeq()
              } else {
                this.$message({ message: '删除失败：' + defaultQuery.msg, type: 'error' })
              }
            })
              .catch(() => {
                this.$message({
                  message: '删除异常',
                  type: 'error'
                })
              })
          })
          .catch(() => {})
      }
    },
    toButAdd() {
      this.tableDataTable.push({
        seq: this.tableDataTable.length,
        mz_id: this.mz_id,
        mz_dx_id: '',
        dx_num: '',
        line_num: '',
        dx_direct: '',
        dx_gear: '',
        dx_notes: '',
        sheet_type: '',
        dx_type: '',
        positive_cut: '0',
        negative_cut: '0',
        enable_flag: 'Y',
        attribute1: '',
        attribute2: '',
        attribute3: ''
      })
    },
    refreshSeq() {
      for (let i = 0; i < this.tableDataTable.length; i++) {
        this.tableDataTable[i].seq = i
      }
    }
  }
}
</script>
