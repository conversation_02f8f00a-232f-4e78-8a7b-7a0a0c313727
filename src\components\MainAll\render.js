
function render(g, node, isSelected,that) {
  if(node.type === 'rect'){
    g.append('rect')
    .style('width', node.width)
    .style('height', node.height)
    .style('stroke', node.borderColor)
    .style('stroke-width', node.strokeWidth)
    .style('fill', node.bgColor)
    .attr('stroke', node.bgColor)
    .attr('x', node.x)
    .attr('y', node.y)
    g.append('text')
    .attr('x', node.x + 65)
    .attr('y', node.y + 15)
    .style('fill', '#ffffff')
    .attr('class', 'unselectable')
    .attr('text-anchor', 'middle')
    .text(() => node.title)
    .style('font-size', '14px')
    .style('font-weight', 'bold')
  }else if(node.type === 'icon'){
    g.append('image')
    .attr('x', node.x)
    .attr('y', node.y)
    .attr('preserveAspectRatio', 'none')
    .attr('vector-effect', 'non-scaling-stroke')
    .style('width', node.width)
    .style('height', node.height)
    .attr('href', node.href)
    g.append('text')
    .attr('x', node.x + 60)
    .attr('y', node.y + 18)
    .style('fill', '#ffffff')
    .attr('class', 'unselectable')
    .attr('text-anchor', 'middle')
    .text(() => node.title)
    .style('font-size', '14px')
    .style('font-weight', 'bold')
  }else if(node.type === 'code'){
    g.append('rect')
    .style('width', node.width)
    .style('height', node.height)
    .style('fill', node.status === 1 ? '#005ee2' : '#1fa3e3')
    .attr('stroke', node.status === 1 ? '#005ee2' : '#1fa3e3')  
    .attr('x', node.x)
    .attr('y', node.y)
    g.append('image')
    .attr('x', node.x - 2)
    .attr('y', node.y - 1)
    .attr('preserveAspectRatio', 'none')
    .attr('vector-effect', 'non-scaling-stroke')
    .style('width', '144px')
    .style('height', '62px')
    .attr('href', node.status === 1 ?  require('@/assets/images/dcs/whiteBorder.png')  :  require('@/assets/images/dcs/blueBorder.png'))
    g.append('text')
    .attr('x', node.x + 65)
    .attr('y', node.y + 42)
    .style('fill', '#ffffff')
    .attr('class', 'unselectable')
    .attr('text-anchor', 'middle')
    .text(() => node.title)
    .style('font-size', '28px')
    .style('font-weight', 'bold')
    // g.append('title')
    // .text(()=>`任务号:${node.task_num ? node.task_num :'暂无数据'}\n托盘号:${node.pallet_num ? node.pallet_num : '暂无数据'}\n钢板型号:${node.model_type ? node.model_type : '暂无数据'}`)
  if(node.manual_Auto_Flag){
      g.append('image')
      .attr('x', node.x + 120)
      .attr('y', node.y - 15)
      .attr('preserveAspectRatio', 'none')
      .attr('vector-effect', 'non-scaling-stroke')
      .style('width', '35px')
      .style('height', '35px')
      .attr('href', node.manual_Auto_Flag === '1' ? require('@/assets/images/dcs/dcs_automatic.png') : require('@/assets/images/dcs/dcs_handMove.png'))
  }
  if(node.pallet_Flag === '1'){
    g.append('image')
    .attr('x', node.x - 10)
    .attr('y', node.y - 15)
    .attr('preserveAspectRatio', 'none')
    .attr('vector-effect', 'non-scaling-stroke')
    .style('width', '35px')
    .style('height', '35px')
    .attr('href', node.pallet_Flag === '1' ? require('@/assets/images/dcs/dcs_PalletOver.png') : '')
    g.append('title')
    .text(()=>`托盘号:${node.pallet_Num ? node.pallet_Num : '暂无数据'}`)
  }
    g.on('click',()=>{
      that.open(node)
    })
  }
  // if (node.type === 'image') {
  //   g.append('image')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //     .attr('preserveAspectRatio', 'none')
  //     .attr('vector-effect', 'non-scaling-stroke')
  //     .style('width', node.width + 'px')
  //     .style('height', node.height + 'px')
  //     .attr('href', 'data:image/png;base64,' + node.href)
  // } else {
  //   const borderColor = isSelected ? '#445ddb' : node.stroke
  //   const borderWidth = isSelected ? '3px' : node.strokeWidth
  //   g.append('circle')
  //     .attr('cx', node.x)
  //     .attr('cy', node.y)
  //     .attr('r', node.size)
  //     .attr('stroke', borderColor)
  //     .style('stroke-width', borderWidth)
  //     .attr('fill', node.status === 'LOCKSTATUS' ? '#21f121' :node.ok_color)
  //   g.append('text')
  //     .attr('x', node.x)
  //     .attr('y', node.y + 6 || 0)
  //     .style('fill', '#2D333D')
  //     .attr('class', 'unselectable')
  //     .attr('text-anchor', 'middle')
  //     .text(() => node.point_id)
  //     .style('font-size', '14px')
  //     .style('font-weight', 'bold')
  //   g.append('title')
  //     .text(()=>`任务号:${node.task_num ? node.task_num :'暂无数据'}\n托盘号:${node.pallet_num ? node.pallet_num : '暂无数据'}\n钢板型号:${node.model_type ? node.model_type : '暂无数据'}`)
  //   g.on('click',()=>{
  //     that.jumpCut(node)
  //   })
  // }
}

export default render
