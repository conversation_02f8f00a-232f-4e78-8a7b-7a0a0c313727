<template>
    <div class="workstationScreen">
        <el-card class="cardStyle">
            <div class="headerStyle">
                <div class="logoStyle"><img src="@/assets/images/qyc1.png" alt=""><span>{{ this.$route.query.station_code }}</span></div>
                <div class="orderInfo" v-if="orderData.length > 0">
                    <p>{{orderData[0].orderProd}} 加注异常AGV停线</p>
                </div>
                <div class="wrapmarquee">
                    <marquee loop="infinite">
                        <div class="wraptext">
                        <p v-for="(item,index) in emergency" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                        </div>
                    </marquee>
                </div>
                <div class="headerL">
                    <p class="pImg"><img src="@/assets/images/log.png" alt=""></p>
                    <p class="pTime">{{ nowDateTime }} {{ nowDateWeek }}</p>
                </div>
            </div>
        </el-card>
        <el-card class="cardStyle" >
            <el-row :gutter="24" class="elRowStyle elRowStyleFlex elRowStyleNmargin linePie">
                <el-col :span="12" style="padding-right:0">
                    <el-card class="cardStyle" style="margin: 0 10px 0 0 !important;">
                        <div id="comPreLine" style="width: 100%;height: 285px" />
                    </el-card>
                </el-col>
                <el-col :span="12" style="padding-right:0">
                    <el-card class="cardStyle" >
                        <div id="circumAB" style="width: 100%;height: 285px" />
                    </el-card>
                </el-col>
            </el-row>
        </el-card>
        <el-card class="cardStyle"  style="margin: 0px 10px 10px 10px;">
            <el-row :gutter="24" class="elRowStyle elRowStyleFlex elRowStyleNmargin">
                <el-col :span="24">
                    <div class="titleTip">质量奖激励明细</div>
                        <el-table border :data="qualityData" style="width: 100%" height="180px" >
                            <el-table-column type="index" label="序号" width="80"/>
                            <el-table-column prop="employeeNum" :show-overflow-tooltip="true" label="员工编码" width="160"/>
                            <el-table-column prop="name" :show-overflow-tooltip="true" label="姓名" width="160"/>
                            <el-table-column prop="rewardAmount" :show-overflow-tooltip="true" label="奖励金额" width="180"/>
                            <el-table-column prop="rewardDescription" :show-overflow-tooltip="true" label="激励说明" />
                        </el-table>
                </el-col>
            </el-row>
        </el-card>
        <el-card class="cardStyle"  style="margin: 10px;">
            <div class="headerStyle">
                <div class="wrapmarquee">
                    <marquee loop="infinite">
                        <div class="wraptext">
                            <p style="color: red;">{{ description }}</p>
                        </div>
                    </marquee>
                </div>
            </div>
        </el-card>
    </div>
</template>
<script>
import { getStationTime,getAqmisDefectInfo ,getOilStop,getStationEmergency,getIssueClosureRate,getBigScreenFooter,getNumberofDuplicateIssueOccurrences,qualityAwardIncentiveDetails} from '@/api/pmc/sysworkstationScreen'
export default {
    name:'workstationScreenLine',
    data(){
        return {
            nowDateTime: '', // 当前日期
            nowDateWeek: '', // 周末
            timer:null,
            timer2:null,
            timer3:null,
            comPreLine:null,
            circumAB:null,
            orderData:[],
            emergency: [],
            work_center_code: 'ZA',
            description:'',
            qualityPageNo:1,
            qualityData:[],
        }
    },
    created(){
        // 加载工位屏时间
        this.initstationTime()
        this.timer = setInterval(() => {
            this.initstationTime()
        }, 1000)
    },
    mounted(){
        // 加载工位屏紧急事件
        this.initStationEmergency()
        this.getOrderInfo()
        this.getComPreLine() //获取综合线
        this.getCircumData()//获取周度问题
        this.getFooterData() //获取底部大屏文字
        this.getQuilatyData() //获取激励说明
        this.timer = setInterval(()=>{
            this.initStationEmergency()
        },15000)
        this.timer2 = setInterval(() =>{
            this.getFooterData()
            this.getOrderInfo()
        },1000 * 60)
        this.timer3 = setInterval(() =>{
            this.getComPreLine()
            this.getCircumData()
            this.getQuilatyData()
        },1000 * 60 * 60)
        var that = this
        window.addEventListener('resize', function() {
            that.comPreLine.resize()
            that.circumAB.resize()
      })
    },
    beforeCreate(){
        clearInterval(this.timer)
        clearInterval(this.timer2)
        clearInterval(this.timer3)
    },
    methods:{
        // 加载工位屏紧急事件
        initStationEmergency() {
            var query = {
                station_code: this.$route.query.station_code,
                work_center_code: this.work_center_code
            }
            getStationEmergency(query)
                .then(res => {
                if (res.code !== 0 && res.count < 0) {
                    console.log('请求数据异常')
                    return
                }
                // 紧急事件
                this.emergency = res.data[0].andon_list
                if (this.emergency === null || this.emergency.length === 0) {
                    this.emergency = []
                }
                // 紧急事件
                })
                .catch(() => {
                    console.log('请求数据为空')
            })
        },
        getOrderInfo(){
            const query = `queryMode=page&pageNo=1&limit=1&status=0&station=${this.$route.query.station_code}`
            getOilStop(query).then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                this.orderData = res.data
                if (this.orderData === null || this.orderData.length === 0) {
                    this.orderData = []
                }
            })
            .catch(() => {
                console.log('请求数据为空')
            })
        },
        initstationTime() {
            var query = {
                station_code: this.$route.query.station_code
            }
            getStationTime(query).then(res => {
                if (res.code !== 0 && res.count < 0) {
                    console.log('请求数据异常')
                    return
                }
                    this.nowDateTime = res.data[0].sysdate
                    this.nowDateWeek = res.data[0].sysweek
                })
                .catch(() => {
                    console.log('请求数据为空')
            })
        },
        getComPreLine(){
            const query = {
                station:this.$route.query.station_code,
            }
            getIssueClosureRate(query).then(res=>{
                if(res.code === '200' && res.data.data.length > 0){
                    res.data.data.map(item=>{
                        item.count = item.count.replace('.00%','')
                    })
                    this.comPreLine = this.$echarts.init(document.getElementById('comPreLine'))
                    const  option = {
                        title: {
                            text: res.data.teamName + '问题关闭率趋势图',
                            top:'3%',
                            left: 'center',
                            // 文字颜色
                            textStyle: {
                                color: '#000',
                                fontSize:20
                            }
                        },
                        legend: {
                            itemWidth: 10,
                            itemHeight: 10,
                            itemGap: 20,
                            textStyle: {
                                color: '#000', 
                                fontFamily: 'Alibaba PuHuiTi', 
                                fontSize: 16, 
                                fontWeight: 400, 
                            },
                            selectedMode: false
                        },
                        grid: {
                            left: "3%",
                            right: "3%",
                            bottom: "3%",
                            containLabel: true,
                        },
                        xAxis: {
                            type: 'category',
                            axisLabel: {
                                textStyle:{
                                    fontSize:16
                                },
                                interval:0,
                            },
                            axisLine: {
                                show: true,
                            },
                            axisTick: {
                                show: false
                            },
                            splitLine: {
                                show: false,
                            },
                            data: res.data.data.map(e=>{ return e.month}),
                        },
                        yAxis: {
                            type: 'value',
                            minInterval:1,
                            min:0,
                            max:100,
                            axisLine:{
                                show:false
                            },
                            axisTick: {
                                show: false
                            },
                            axisLabel:{
                                show:true,
                                textStyle:{
                                    fontSize:16,
                                },
                                formatter: function(value) {
                                        return value + '%';
                                    }
                            }
                        },
                        series: [
                            {   
                                data: res.data.data.map(e=>{return e.count}),
                                type: "line",
                                symbol: "true",
                                symbolSize: 2, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: '#2E65BF',//拐点颜色
                                        label: {
                                            show: true, //开启显示
                                            color: '#000',
                                            position: 'top', //在上方显示
                                            fontSize:16,
                                            formatter: function (res) {
                                                if (res.value) {
                                                    return res.value + '%'
                                                } else {
                                                    return 0
                                                }
                                            },
                                        },
                                    },
                                },
                            }
                    
                        ]
                    };
                    this.comPreLine.setOption(option)
                }else{
                    this.comPreLine = null
                    console.log('请求数据异常')
                }
            }).catch((ex)=>{
                this.comPreLine  = null
                console.log('请求数据为空')
            })
        },
        getCircumData(){
            const query = {
                station:this.$route.query.station_code,
            }
            getNumberofDuplicateIssueOccurrences(query).then(res=>{
                if(res.code === '200' && res.data.data.length > 0){
                    this.circumAB = this.$echarts.init(document.getElementById('circumAB'))
                    let maxCount = Math.max.apply(null,res.data.data.map(function(obj){ //找出最大值
                        return obj.count
                    }))
                    let option = {
                        title: {
                            text: res.data.teamName + '重复发生问题项数',
                            top:'3%',
                            left: 'center',
                            // 文字颜色
                            textStyle: {
                                color: '#000',
                                fontSize:20
                            }
                        },
                        legend: {
                            itemWidth: 10,
                            itemHeight: 10,
                            itemGap: 20,
                            textStyle: {
                                color: '#000', 
                                fontFamily: 'Alibaba PuHuiTi', 
                                fontSize: 20, 
                                fontWeight: 400, 
                            },
                            selectedMode: false
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow',
                                label: {
                                    show: true,
                                },
                            },
                        },
                        grid: {
                            left: "3%",
                            right: "3%",
                            bottom: "3%",
                            containLabel: true,
                        },
                        color: ['#4472C4'],
                        xAxis: {
                            type: 'category',
                            data: res.data.data.map(e=>{return e.month}),
                            axisLine:{
                                show:false
                            },
                            axisTick: {
                                show: false
                            },
                            axisLabel:{
                                textStyle:{
                                    fontSize:16,
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            minInterval:1,
                            min:0,
                            max:maxCount > 5 ? maxCount : 5,
                            axisLine:{
                                    show:false
                                    },
                            axisTick: {
                                show: false
                            },
                            axisLabel:{
                                textStyle:{
                                    fontSize:16,
                                }
                            }
                        },
                        series: [
                            {
                                name: '',
                                data: res.data.data.map(e=>{return e.count}),
                                barWidth: 20,
                                type: 'bar',
                                itemStyle: {
                                normal: {
                                    label: {
                                        show: true, //开启显示
                                        position: 'top', //顶部显示
                                        textStyle: {
                                            //数值样式
                                                color: 'black',
                                                fontSize: 16,
                                            },
                                        },
                                    },
                                },
                            },
                        ],
                    };
                this.circumAB.setOption(option)
                }else{
                    this.circumAB = null
                    console.log('请求数据异常')
                }
            }).catch((ex)=>{
                this.circumAB  = null
                console.log('请求数据为空')
            })
        },
        getQuilatyData(){
            const query = `queryMode=page&pageNo=${this.qualityPageNo}&limit=2`
            qualityAwardIncentiveDetails(query).then(res=>{
                if(res.code === '200' && res.total > 0){
                    if(this.qualityPageNo * 2 >=  res.total){
                        this.qualityPageNo = 1
                    }else{
                        this.qualityPageNo ++
                    }
                    this.qualityData = res.data
                }else{
                    this.qualityData = []
                    console.log('请求数据异常')
                }
            }).catch(ex=>{
                this.qualityData = []

                console.log('请求数据为空')
            })
        },
        getFooterData(){
            const query = `code=TheSecond`
            getBigScreenFooter(query).then(res=>{
                if(res.code === '200' && res.data.length > 0){
                    this.description = res.data[0].description
                }else{
                    this.description = ''
                }
            }).catch(ex=>{
                this.description = ''
                console.log('请求数据为空')
            })

        }
    }
}
</script>
<style lang="less" scoped>
    .workstationScreen{
        ::v-deep .linePie{
            .el-col{
                padding: 0 !important;
                margin: 0 !important;
            }
            .el-card__body{
                background: transparent !important;
                padding: 0 !important;
            }
            .cardStyle {
                margin: 0 !important;
            }
        }
       ::v-deep .el-card__body{
            padding: 0 15px !important;
        }
        .cardStyle{
            margin: 10px;
            padding: 0;
            border: 0;
            .headerStyle{
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px;
                .logoStyle{
                        display: flex;
                        align-items: center;
                    img{
                        width: 60px;
                        margin-right: 10px;
                    }
                    span{
                        font-weight: 700;
                        font-size: 24px;
                    }
                }
                .headerL{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    p{
                        margin: 0;
                    }
                    .pImg{
                        width: 60px;
                        img{
                            width: 100%;
                            }
                    }
                    .pTime{
                        margin-top: 5px !important;
                        font-weight: 700;
                    }
                }
                .wrapmarquee{
                    width: 100%;
                    flex: 1;
                    .wraptext{
                        display: flex;
                        align-items: center;
                        p{
                            color: #ffba00;
                            font-size: 40px;
                            font-weight: 700;
                            margin: 0;
                            margin-right: 25px;
                            display: flex;
                            align-items: center;
                        }
                    }
                }
                .redactive{
                    color: red !important;
                }
                .orderInfo{
                    display: flex;
                    justify-content: left;
                    flex-direction: column;
                    p{
                    margin: 0 10px;
                    color: red;
                    font-weight: 700;
                    font-size:30px;
                    }
                }
            }
            .detail{
                display: flex;
                height: 210px;
                div{
                    padding: 5px;
                    font-weight: 700;
                    font-size: 20px;
                    color: red;
                    border: 1px #000 solid;
                    width: 40%;
                    white-space: wrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    span{
                        color: #0270bd;
                    }
                }
                img{
                    width: 60%;
                    height: 100%;
                }
            }
            .titleTip{
                font-size: 34px;
                font-weight: 700;
                text-align: center;
                margin: 5px 0;
            }
        }
        ::v-deep .el-table__body-wrapper .el-table__row td div,::v-deep .el-table__header-wrapper .has-gutter th div{
            line-height: 28px !important;
        }
        ::v-deep .el-table th.el-table__cell>.cell,div{
            font-size: 26px;
        }
        ::v-deep .el-table--small .el-table__cell{
            padding: 14px 0 !important;
        }

    }
</style>