import request from '@/utils/request'

// 查询安灯音响音乐
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonMusicSel',
    method: 'post',
    data
  })
}
// 新增安灯音响音乐
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonMusicIns',
    method: 'post',
    data
  })
}
// 修改安灯音响音乐
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonMusicUpd',
    method: 'post',
    data
  })
}
// 删除安灯音响音乐
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonMusicDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

