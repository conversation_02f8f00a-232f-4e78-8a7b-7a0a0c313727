<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="版本号：">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>

            <div class="formChild col-md-3 col-12">
              <el-form-item label="模组/描述：">
                <el-input v-model="query.mz_num_name" clearable size="mini" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="success" icon="el-icon-document" plain round @click="recipeDrawerVisible = true">
            配方
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="250px" :inline="true">
          <el-form-item label="配方" prop="recipe_id">
            <el-select v-model="form.recipe_id" filterable clearable>
              <el-option v-for="item in recipeData" :key="item.recipe_id" :label="item.recipe_name + ' ' + item.recipe_version" :value="item.recipe_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="模组编号" prop="mz_num">
            <el-input v-model="form.mz_num" :disabled="false" />
          </el-form-item>
          <el-form-item label="模组名称" prop="mz_name">
            <el-input v-model="form.mz_name" />
          </el-form-item>
          <el-form-item label="电芯宽度" prop="dx_width_size">
            <el-input v-model.number="form.dx_width_size" />
          </el-form-item>
          <el-form-item label="电芯高度" prop="dx_height_size">
            <el-input v-model.number="form.dx_height_size" />
          </el-form-item>

          <!--快速编码：DX_COLUMN-->
          <el-form-item label="电芯单双列" prop="dx_column">
            <fastCode fastcode_group_code="DX_COLUMN" :fastcode_code.sync="form.dx_column" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="电芯层数" prop="dx_layer_num">
            <el-input v-model.number="form.dx_layer_num" />
          </el-form-item>
          <el-form-item label="电芯块数" prop="dx_count">
            <el-input v-model.number="form.dx_count" />
          </el-form-item>
          <el-form-item label="模组P数" prop="mz_p_count">
            <el-input v-model.number="form.mz_p_count" />
          </el-form-item>
          <el-form-item label="模组S数" prop="mz_s_count">
            <el-input v-model.number="form.mz_s_count" />
          </el-form-item>

          <!--快速编码：MZ_TJ_TECHNOLOGY-->
          <el-form-item label="贴胶工艺" prop="mz_tj_technology">
            <fastCode fastcode_group_code="MZ_TJ_TECHNOLOGY" :fastcode_code.sync="form.mz_tj_technology" control_type="select" size="small" />
          </el-form-item>
          <!--快速编码：MZ_TYPE-->
          <el-form-item label="模组类型" prop="mz_type">
            <fastCode fastcode_group_code="MZ_TYPE" :fastcode_code.sync="form.mz_type" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="料框设定层数" prop="dx_lk_layer_num">
            <el-input v-model.number="form.dx_lk_layer_num" />
          </el-form-item>
          <el-form-item label="BMS端板宽度" prop="mz_end_width">
            <el-input v-model="form.mz_end_width" />
          </el-form-item>

          <el-form-item label="模块号" prop="module_num">
            <el-input v-model.number="form.module_num" />
          </el-form-item>
          <el-form-item label="当前电芯序号" prop="dx_num">
            <el-input v-model.number="form.dx_num" />
          </el-form-item>
          <el-form-item label="MES有无中隔板" prop="mid_partition">
            <el-select v-model="form.mid_partition" filterable clearable>
              <el-option v-for="item in dict.MID_PARTITION" :key="item.id" :label="item.label" :value="item.value+''" />
            </el-select>
          </el-form-item>
          <el-form-item label="MES隔板插入位置" prop="mid_partition_layer">
            <el-input v-model.number="form.mid_partition_layer" />
          </el-form-item>
          <el-form-item label="MES中隔板宽度" prop="mid_partition_breadth">
            <el-input v-model.number="form.mid_partition_breadth" />
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识">
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div class="ruleBottom">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <!--弹出界面-->
      <el-drawer append-to-body :wrapper-closable="false" title="配方信息" :visible.sync="recipeDrawerVisible" size="100%" @closed="getRecipeData()">
        <recipe v-if="recipeDrawerVisible" ref="recipe" />
      </el-drawer>
      <!--条码NG规则-->
      <el-drawer append-to-body :wrapper-closable="false" title="条码NG规则" :visible.sync="dxBarDrawerVisible" size="60%">
        <mzDxBarRuleItem v-if="dxBarDrawerVisible" ref="mzDxBarRuleItem" :mz_id="currentMzId" />
      </el-drawer>
      <!--模组范围约束-->
      <el-drawer append-to-body :wrapper-closable="false" title="模组范围约束" :visible.sync="limitDrawerVisible" size="60%">
        <mzLimitRuleItem v-if="limitDrawerVisible" ref="mzLimitRuleItem" :mz_id="currentMzId" />
      </el-drawer>
      <!--模组电芯-->
      <el-drawer append-to-body :wrapper-closable="false" title="模组范围约束" :visible.sync="mzDxDrawerVisible" size="60%">
        <mzDxRuleItem v-if="mzDxDrawerVisible" ref="mzDxRuleItem" :mz_id="currentMzId" />
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              fit
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-descriptions class="mzthWidth" style="margin-right:150px" :column="4" size="small" border>
                    <el-descriptions-item label="模组ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_id }}</el-descriptions-item>
                    <el-descriptions-item label="模组编号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_num }}</el-descriptions-item>
                    <el-descriptions-item label="模组名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_name }}</el-descriptions-item>
                    <el-descriptions-item label="电芯宽度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_width_size }}</el-descriptions-item>
                    <el-descriptions-item label="电芯高度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_height_size }}</el-descriptions-item>
                    <el-descriptions-item label="电芯单双列" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_column }}</el-descriptions-item>
                    <el-descriptions-item label="电芯层数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_layer_num }}</el-descriptions-item>
                    <el-descriptions-item label="电芯块数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_count }}</el-descriptions-item>
                    <el-descriptions-item label="模组P数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_p_count }}</el-descriptions-item>
                    <el-descriptions-item label="模组S数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_s_count }}</el-descriptions-item>
                    <el-descriptions-item label="贴胶工艺" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_tj_technology }}</el-descriptions-item>
                    <el-descriptions-item label="模组类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_type }}</el-descriptions-item>
                    <el-descriptions-item label="料框设定层数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_lk_layer_num }}</el-descriptions-item>
                    <el-descriptions-item label="BMS端板宽度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mz_end_width }}</el-descriptions-item>
                    <el-descriptions-item label="模块号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.module_num }}</el-descriptions-item>
                    <el-descriptions-item label="当前电芯序号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dx_num }}</el-descriptions-item>
                    <el-descriptions-item label="MES有无中隔板" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.MID_PARTITION[props.row.mid_partition] }}</el-descriptions-item>
                    <el-descriptions-item label="MES隔板插入位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mid_partition_layer }}</el-descriptions-item>
                    <el-descriptions-item label="MES中隔板宽度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mid_partition_breadth }}</el-descriptions-item>
                    <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                    <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                    <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                    <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag == 'N' ? '无效' : '有效' }}</el-descriptions-item>
                  </el-descriptions>
                </template>
              </el-table-column>
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="mz_id" label="id" />

              <el-table-column :show-overflow-tooltip="true" prop="recipe_type" label="配方类型" />
              <el-table-column :show-overflow-tooltip="true" prop="recipe_name" label="配方描述" />
              <el-table-column :show-overflow-tooltip="true" prop="recipe_version" label="版本号" />
              <el-table-column :show-overflow-tooltip="true" prop="liable_person" label="责任人" />
              <el-table-column :show-overflow-tooltip="true" prop="update_remark" label="更新说明" />

              <el-table-column :show-overflow-tooltip="true" prop="mz_num" label="模组编号" />
              <el-table-column :show-overflow-tooltip="true" prop="mz_name" label="模组名称" />
              <el-table-column :show-overflow-tooltip="true" prop="dx_width_size" label="电芯宽度" />
              <el-table-column :show-overflow-tooltip="true" prop="dx_height_size" label="电芯高度" />
              <el-table-column :show-overflow-tooltip="true" prop="dx_column" label="电芯单双列" />
              <el-table-column :show-overflow-tooltip="true" prop="dx_layer_num" label="电芯层数" />
              <el-table-column :show-overflow-tooltip="true" prop="dx_count" label="电芯块数" />
              <el-table-column :show-overflow-tooltip="true" prop="mz_type" label="模组类型" />
              <el-table-column :show-overflow-tooltip="true" prop="mz_p_count" label="模组P数" />
              <el-table-column :show-overflow-tooltip="true" prop="mz_s_count" label="模组S数" />
              <el-table-column :show-overflow-tooltip="true" prop="dx_lk_layer_num" label="料框设定层数" width="120" />
              <el-table-column :show-overflow-tooltip="true" prop="mz_end_width" label="BMS端板宽度" width="100" />
              <el-table-column :show-overflow-tooltip="true" prop="mid_partition" label="MES有无中隔板" width="120">
                <template slot-scope="scope">
                  {{ dict.label.MID_PARTITION[scope.row.mid_partition] }}
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" prop="mid_partition_layer" label="MES隔板插入位置" width="120" />
              <el-table-column :show-overflow-tooltip="true" prop="mid_partition_breadth" label="MES中隔板宽度" width="120" />
              <el-table-column label="是否有效" align="center" prop="enable_flag">
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="295" align="center" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="mzDxDrawerVisible = true">新增电芯</el-button>
                      <el-button slot="reference" type="text" size="small" @click="dxBarDrawerVisible = true">条码NG规则</el-button>
                      <el-button slot="reference" type="text" size="small" @click="limitDrawerVisible = true">模组范围约束</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <mzRuleItem ref="mzRuleItem" class="tableFirst" :mz_id="currentMzId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudMzRule from '@/api/mes/core/mzRule'
import mzRuleItem from './mzRuleItem'
import mzDxBarRuleItem from './mzDxBarRuleItem'
import mzLimitRuleItem from './mzLimitRuleItem'
import mzDxRuleItem from './mzDxRuleItem'
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import recipe from '@/views/mes/core/recipe/recipe'
const defaultForm = {
  mz_id: '',
  recipe_id: '',
  mz_num: '',
  mz_name: '',
  dx_width_size: '0',
  dx_height_size: '0',
  dx_column: '1',
  dx_layer_num: '0',
  dx_count: '0',
  mz_p_count: '0',
  mz_s_count: '0',
  mz_tj_technology: '1',
  mz_type: '',
  dx_lk_layer_num: '0',
  mz_end_width: '',
  module_num: 'A',
  dx_num: '0',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: '',
  mid_partition: '',
  mid_partition_layer: '',
  mid_partition_breadth: ''
}
export default {
  name: 'MzRule',
  components: { crudOperation, rrOperation, udOperation, pagination, mzRuleItem, mzDxBarRuleItem, mzLimitRuleItem, recipe, mzDxRuleItem },
  cruds() {
    return CRUD({
      title: '模组规则',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mz_id',
      // 排序
      sort: ['mz_id asc'],
      // CRUD Method
      crudMethod: { ...crudMzRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['MID_PARTITION'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {},
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_mz:add'],
        edit: ['admin', 'c_mes_fmod_recipe_mz:edit'],
        del: ['admin', 'c_mes_fmod_recipe_mz:del'],
        down: ['admin', 'c_mes_fmod_recipe_mz:down']
      },
      rules: {
        mz_num: [{ required: true, message: '请输入模组编号', trigger: 'blur' }]
      },
      currentMzId: 0,
      recipeDrawerVisible: false,
      dxBarDrawerVisible: false,
      limitDrawerVisible: false,
      mzDxDrawerVisible: false
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.getRecipeData()
  },
  methods: {
    // 格式化表单数据
    [CRUD.HOOK.beforeToCU](crud, from) {
      from.mid_partition = String(from.mid_partition)
    },
    handleRowClick(row, column, event) {
      this.currentMzId = row.mz_id
    },

    getRecipeData() {
      this.recipeData = []
      crudRecipe
        .sel({
          user_name: Cookies.get('userName'),
          recipe_type: 'MODELDX',
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.recipeData = defaultQuery.data
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
::v-deep(.el-table .cell) {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.ruleBottom {
  text-align: center;
  margin: 40px 0;
}
// ::v-deep .mzthWidth{
//   th{
//     width: 160px;
//   }
//   td{
//     width: 100px;
//   }
// }
</style>
