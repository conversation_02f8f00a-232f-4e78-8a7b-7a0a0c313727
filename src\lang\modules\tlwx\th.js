// TLWX project Thai translation

export default {
  // TLWX project specific translations
  tlwx: {
    // Control modes
    manualMode: 'โหมดแมนนวล',
    autoMode: 'โหมดอัตโนมัติ',
    selectMode: 'เลือกโหมด',
    
    // Start status
    autoStart: 'เริ่มอัตโนมัติ',
    systemStop: 'หยุดระบบ',
    
    // Heater status
    heaterRun: 'เปิดฮีตเตอร์',
    heaterStop: 'ปิดฮีตเตอร์',
    
    // Other possible translations
    controlMode: 'โหมดควบคุม',
    startStatus: 'สถานะการเริ่มต้น',
    heaterStatus: 'สถานะฮีตเตอร์',
    
    // Prompt messages
    pleaseStartMonitor: 'กรุณาเริ่มการตรวจสอบก่อน'
  }
}
