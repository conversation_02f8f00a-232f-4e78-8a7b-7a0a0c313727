<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="当前型号：">
                <!-- 当前型号： -->
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="厚：">
                <!-- 厚： -->
                <el-input v-model="query.m_height" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation>
                <template slot="right">
                  <el-button size="small" type="primary" @click="handleOrderSql">
                    请求送货单号
                  </el-button>
                </template>
              </rrOperation>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-row :gutter="20">
      <el-col :span="14">
        <el-card shadow="always" style="margin-top: 10px">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.mfTable.model_type')"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.model_type }}</span>
                <span v-if="scope.row.selected_flag === 'Y'">{{ getR1Data(scope.row) }}</span>
              </template>
            </el-table-column>
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.mfTable.length')"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.mfTable.width')"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              :label="$t('lang_pack.mfTable.thick')"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              :label="$t('lang_pack.mfTable.materialQuality')"
              align="center"
            />
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.mfTable.mainMaterialCode')"
              align="center"
            />
            <!-- Table单条操作-->
            <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" width="120" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" type="text" :disabled="scope.row.selected_flag ==='Y'" size="small" @click="changeSelect(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-card>
      </el-col>
      <el-col :span="10">
        <div id="taskInfo" :style="{'height':taskInfoHeight + 'px'}">
          <el-card shadow="always" style="margin-top: 10px">
            <el-descriptions class="taskROne" title="R1-当前任务信息" :column="1" :content-style="content_style" :label-style="label_style">
              <el-descriptions-item label="长宽厚" class="warningInfo">{{ stationCodeR1.m_length }}/{{ stationCodeR1.m_width }}/{{ stationCodeR1.m_height }}</el-descriptions-item>
              <el-descriptions-item label="当前型号">{{ stationCodeR1.model_type }}</el-descriptions-item>
              <el-descriptions-item label="报警信息" class="warningInfo">
                <el-tooltip class="item" effect="dark" :content="R1AlarmMsg" placement="top">
                  <el-tag type="danger" effect="dark" size="medium" style="font-size: 22px;">{{ R1AlarmMsg }}</el-tag>
                </el-tooltip>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
          <el-card shadow="always" style="margin-top: 10px">
            <el-descriptions class="taskRTwo" title="R2-当前任务信息" :column="1" :content-style="content_style" :label-style="label_style">
              <el-descriptions-item label="长宽厚">{{ stationCodeR2.m_length }}/{{ stationCodeR2.m_width }}/{{ stationCodeR2.m_height }}</el-descriptions-item>
              <el-descriptions-item label="当前型号">{{ stationCodeR2.model_type }}</el-descriptions-item>
              <el-descriptions-item label="当前任务号">{{ stationCodeR2.task_num }}</el-descriptions-item>
              <el-descriptions-item label="报警信息" class="warningInfo">
                <el-tooltip class="item" effect="dark" :content="R1AlarmMsg" placement="top">
                  <el-tag type="danger" effect="dark" size="medium" style="font-size: 22px;">{{ R2AlarmMsg }}</el-tag>
                </el-tooltip>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
          <el-card shadow="always" style="margin-top: 10px">
            <el-descriptions class="taskRFour" title="R4-当前任务信息" :column="1" :content-style="content_style" :label-style="label_style">
              <el-descriptions-item label="长宽厚" class="warningInfo">{{ stationCodeR4.m_length }}/{{ stationCodeR4.m_width }}/{{ stationCodeR4.m_height }}</el-descriptions-item>
              <el-descriptions-item label="当前型号">{{ stationCodeR4.model_type }}</el-descriptions-item>
              <el-descriptions-item label="当前任务号">{{ stationCodeR4.task_num }}</el-descriptions-item>
              <el-descriptions-item label="报警信息" class="warningInfo">
                <el-tooltip class="item" effect="dark" :content="R1AlarmMsg" placement="top">
                  <el-tag type="danger" effect="dark" size="medium" style="font-size: 22px;">{{ R4AlarmMsg }}</el-tag>
                </el-tooltip>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>
      </el-col>
    </el-row>
    <order ref="orderDialogVisible" />
  </div>
</template>
<script>
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import crudMeStationMis from '@/api/dcs/core/meStationMis/meStationMis'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import pagination from '@crud/Pagination'
import { lovCell, selCellIP } from '@/api/core/center/cell'
import { sel } from '@/api/core/system/sysParameter'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import order from './order.vue'
const defaultForm = {
  material_code: '',
  material_draw: '',
  model_type: '',
  m_length: '',
  m_width: '',
  m_height: '',
  m_weight: '',
  m_texture: '',
  select_flag: 'false'
}
export default {
  name: 'WEB_Stock_Model_IN',
  components: { rrOperation, pagination, order },
  cruds() {
    return CRUD({
      title: '钢板入库',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'model_id',
      // 排序
      sort: ['selected_flag desc,model_id asc'],
      // CRUD Method
      crudMethod: { ...crudFmodModel },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 220,
      taskInfoHeight: document.documentElement.clientHeight - 140,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      content_style: {
        'overflow': 'hidden',
        'max-width': '90%',
        'white-space': 'nowrap',
        'text-overflow': 'ellipsis',
        'font-size': '18px'
      },
      label_style: {
        'display': 'flex',
        'justify-content': 'right',
        'width': '100px',
        'font-size': '18px'
      },
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
                'ScadaEsb_' +
                Cookies.get('userName') +
                '_' +
                Math.random()
                  .toString(16)
                  .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      stationCodeR1: {},
      stationCodeR2: {},
      stationCodeR4: {},
      R1AlarmMsg: Cookies.get('R1AlarmMsg') ? Cookies.get('R1AlarmMsg') : '',
      R2AlarmMsg: Cookies.get('R2AlarmMsg') ? Cookies.get('R2AlarmMsg') : '',
      R4AlarmMsg: Cookies.get('R4AlarmMsg') ? Cookies.get('R4AlarmMsg') : '',
      timer: null,
      steel_plate_time: ''
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 220
      that.taskInfoHeight = document.documentElement.clientHeight - 140
    }
    const query = {
      user_name: Cookies.get('userName'),
      parameter_code: 'steel_plate_time',
      enable_flag: 'Y'
    }
    sel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data !== '') {
          const parasInfo = defaultQuery.data[0]
          this.steel_plate_time = parasInfo['parameter_val']
        }
      }
    })
  },
  created() {
    this.getStationCode('R2', 'R4')
    this.timer = setInterval(() => {
      this.getStationCode('R2', 'R4')
    }, 10000)
    this.toStartWatch()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    async getStationCode(params2, params4) {
      try {
        const response2 = await crudMeStationMis.sel({ station_code: params2 })
        const response4 = await crudMeStationMis.sel({ station_code: params4 })
        this.stationCodeR2 = response2.data.length ? response2.data[0] : {}
        this.stationCodeR4 = response4.data.length ? response4.data[0] : {}
      } catch (err) {
        console.error('Error')
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.selected_flag === 'Y') {
        return `background-color:#0de133;color:#fff`
      }
    },
    getR1Data(row) {
      if (row.selected_flag !== 'Y') return
      this.stationCodeR1 = row || {}
    },
    changeSelect(data) {
      this.$confirm(`确定要选择当前${data.model_type}型号吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudFmodModel
          .FlagUpdate({
            user_name: Cookies.get('userName'),
            model_id: data.model_id,
            selected_flag: 'Y'
          })
          .then(res => {
            if (res.code === 0) {
              this.$message({ message: '修改成功', type: 'success' })
              this.crud.toQuery()
              this.updateFomdData(data.model_id)
            } else {
              this.$message({ message: '操作失败：' + res.msg, type: 'error' })
            }
          })
          .catch(ex => {
            this.$message({ message: '操作失败：' + ex, type: 'error' })
          })
      })
    },
    updateFomdData(model_id) {
      setTimeout(() => {
        crudFmodModel.FlagUpdate2({ user_name: Cookies.get('userName'), model_id }).then(res => {
          if (res.code === 0) {
            this.crud.toQuery()
          }
        })
      }, 1000 * this.steel_plate_time)
    },
    handleOrderSql() {
      this.$nextTick(() => {
        this.$refs.orderDialogVisible.dialogVisible = true
      })
    },
    toStartWatch() {
      var query = {
        userName: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          // var connectUrl = 'ws://*************:8083/mqtt'
          console.log('拼接URL：' + connectUrl)

          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // for (var i = 0; i < this.tableDataTable.length; i++) {
            //     var clientCode = this.tableDataTable[i].client_code
            //     console.log(clientCode)
            //     订阅主题
            //     var topic_clientStatus = 'SCADA_STATUS/' + clientCode   SCADA_STATUS代表状态
            //     var topic_clientBeat = 'SCADA_BEAT/' + clientCode   SCADA_STATUS代表心跳
            //     var topic_clientMsg = 'SCADA_MSG/' + clientCode   CADA_STATUS代表消息
            //     this.topicSubscribe(topic_clientStatus)
            //     this.topicSubscribe(topic_clientBeat)
            //     this.topicSubscribe(topic_clientMsg)
            // }
            var topic_clientStatus = 'SCADA_CHANGE/SlAisSimPlc/RStatus'
            this.topicSubscribe(topic_clientStatus)
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())
            // const res = JSON.parse(message.toString())
            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // ----------------------------------【MQTT消息触发】----------------------------------
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      console.log('MQTT收到来自', channel, '的消息', message.toString())
      console.log(channel, message)
      // if (channel.indexOf('SlAisSimPlc/') >= 0 ) {
      //     this.reflashClientInfo(channel, message)
      // }
      // if (channel.indexOf('SlAisSimPlc/') >= 0) {
      //     this.reflashTagInfo(channel, message)
      // }
      if (channel.indexOf('SCADA_STATUS/') >= 0 || channel.indexOf('SCADA_BEAT/') >= 0 || channel.indexOf('SCADA_MSG/') >= 0) {
        console.log(channel, message)
        this.reflashClientInfo(channel, message)
      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    // 接受到CLIENT对应的消息时，进行界面更新
    reflashClientInfo(channel, message) {
      var jsonData = JSON.parse(message)
      var clientCode = jsonData.ClientCode
      console.log(jsonData)
      // if (channel.indexOf('SCADA_STATUS/') >= 0) {
      //     var Status = jsonData.Status
      //     var OkTime = jsonData.OkTime

      //     this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdstatus = Status
      //     this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdOkTime = OkTime
      // } else if (channel.indexOf('SCADA_BEAT/') >= 0) {
      //     var Beat = jsonData.Beat
      //     this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdbeat = Beat
      // } else if (channel.indexOf('SCADA_MSG/') >= 0) {
      //         // 明细
      //         var msgClient = jsonData.ClientCode
      //         var msgFunc = jsonData.FuncCode
      //         var msgStatus = jsonData.Status
      //         var msgInfo = jsonData.Message
      //         var msgTime = jsonData.Time

      //         var newRow = {
      //         MSG_CLIENT: msgClient,
      //         MSG_TIME: msgTime,
      //         MSG_STATUS: msgStatus,
      //         MSG_FUNC: msgFunc,
      //         MSG_INFO: msgInfo
      //         }
      //         if (this.tableDataItemTable.length >= 100) {
      //             this.tableDataItemTable.splice(this.tableDataItemTable.length - 1, 1)
      //         }
      //         this.tableDataItemTable.unshift(newRow)
      //     }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      if (jsonData.TagKey.indexOf('R1AlarmMsg') >= 0) {
        Cookies.set('R1AlarmMsg', jsonData.TagNewValue)
        this.R1AlarmMsg = jsonData.TagNewValue
      }
      if (jsonData.TagKey.indexOf('R2AlarmMsg') >= 0) {
        Cookies.set('R2AlarmMsg', jsonData.TagNewValue)
        this.R2AlarmMsg = jsonData.TagNewValue
      }
      if (jsonData.TagKey.indexOf('R4AlarmMsg') >= 0) {
        Cookies.set('R4AlarmMsg', jsonData.TagNewValue)
        this.R4AlarmMsg = jsonData.TagNewValue
      }
      // var TagKey = jsonData.TagKey
      // var TagCode = jsonData.TagCode
      // // var TagOldValue = jsonData.TagOldValue
      // var TagNewValue = jsonData.TagNewValue
      // if (this.mesTagWatchList.indexOf(TagKey) < 0) return
      // this.tableDataGroupTable.filter(item => item.tag_code === TagCode)[0].tdtagvalue = TagNewValue
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
          // this.toWrigeFormFromSubmit()
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    toWrigeFormFromSubmit() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'SlAisSimPlcRStatus/R1AlarmMsg'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/SlAisSimPlc'
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-descriptions{
    height: 150px;
}
::v-deep .el-descriptions-item__cell{
    padding-bottom: 0px !important;
}
::v-deep .el-descriptions__header{
    margin-bottom: 10px !important;
}
::v-deep .taskRFour{
    .el-descriptions-item__cell{
        padding-bottom: 8px !important;
    }
}
.el-pagination{
    margin-bottom: 10px !important;
}
</style>
<style lang="less">
 #taskInfo{
    display: flex;
    flex-direction: column;
   .el-card{
        flex: 1;
    }
}
</style>
