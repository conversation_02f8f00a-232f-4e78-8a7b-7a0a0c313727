import request from '@/utils/request'
// 查询辊道任务
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsRollerTaskSelect',
    method: 'post',
    data
  })
}
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsRollerTaskUpdate',
    method: 'post',
    data
  })
}
// 删除辊道任务
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsRollerTaskDelete',
    method: 'post',
    data
  })
}

// 任务状态更新
export function taskStatusUpd(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsRollerTaskStatusUpd',
    method: 'post',
    data
  })
}
export default { sel, edit, del, taskStatusUpd }
