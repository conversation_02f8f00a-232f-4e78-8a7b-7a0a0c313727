import request from '@/utils/request'

//查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModCsprojSel',
    method: 'post',
    data
  })
}
//新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModCsprojIns',
    method: 'post',
    data
  })
}
//修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModCsprojUpd',
    method: 'post',
    data
  })
}
//删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModCsprojDel',
    method: 'post',
    data
  })
}
//查询LOV
export function lovSel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModCsprojSelLov',
    method: 'post',
    data
  })
}
//编译
export function release(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModCsprojRelease',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, lovSel,release }

