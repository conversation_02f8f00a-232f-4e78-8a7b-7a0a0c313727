<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.andonMusicDes')" width="150">
                <!-- 音乐名称： -->
                <el-input v-model="query.andon_music_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.enableFlag')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
              <el-form-item :label="$t('lang_pack.andonmusic.andonMusicDes')" prop="andon_music_des">
                <!-- 音乐名称 -->
                <el-input v-model="form.andon_music_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.andonmusic.andonMusicPath')" prop="andon_music_path">
                <!-- 音乐路径 -->
                <el-input v-model="form.andon_music_path" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.andonmusic.andonMusicAttr')" prop="andon_music_attr">
                <!-- 音乐属性 -->
                <el-input v-model="form.andon_music_attr" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.andonmusic.enableFlag')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" :label="$t('lang_pack.andonmusic.creationDate')" />
            <!-- 创建时间 -->

            <el-table-column  :show-overflow-tooltip="true" prop="andon_music_des" :label="$t('lang_pack.andonmusic.andonMusicDes')" />
            <!-- 音乐名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="andon_music_path" :label="$t('lang_pack.andonmusic.andonMusicPath')" />
            <!-- 音乐路径 -->
            <el-table-column  :show-overflow-tooltip="true" prop="andon_music_attr" :label="$t('lang_pack.andonmusic.andonMusicAttr')" />
            <!-- 音乐属性 -->

            <el-table-column  :label="$t('lang_pack.andonmusic.enableFlag')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudAndonMusic from '@/api/pmc/andon/andonmusic'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  andon_music_id: '',
  andon_music_des: '',
  andon_music_path: '',
  andon_music_attr: '',
  enable_flag: 'Y'
}
export default {
  name: 'AndonMusic',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '安灯音响音乐',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'andon_music_id',
      // 排序
      sort: ['andon_music_id asc'],
      // CRUD Method
      crudMethod: { ...crudAndonMusic },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'fmod_andon_music:add'],
        edit: ['admin', 'fmod_andon_music:edit'],
        del: ['admin', 'fmod_andon_music:del']
      },
      rules: {
        andon_music_des: [{ required: true, message: '请输入音乐名称', trigger: 'blur' }]
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {}
}
</script>
