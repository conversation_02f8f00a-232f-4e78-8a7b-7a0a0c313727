// 服务器接入信息如下：
// Broker: broker.emqx.io
// TCP Port: 1883
// Websocket Port: 8083

// 连接字符串, 通过协议指定使用的连接方式
// ws 未加密 WebSocket 连接
// wss 加密 WebSocket 连接
// mqtt 未加密 TCP 连接
// mqtts 加密 TCP 连接
// wxs 微信小程序连接
// alis 支付宝小程序连接
export const MQTT_SERVICE = 'ws://192.168.3.40:8090/mqtt' // wss是https连接
// export const MQTT_SERVICE = 'ws://192.168.167.136:8083/mqtt'  //wss是https连接
export const MQTT_USERNAME = ''
export const MQTT_PASSWORD = ''
