<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.fastCode.groupCodeDescription')">
                <!-- 组编码/描述： -->
                <el-input v-model="query.fastcodeGroupCodeDes" clearable size="mini" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.fastCode.groupCode')" prop="fastcode_group_code" display:none>
            <!-- 组编码 -->
            <el-input v-model="form.fastcode_group_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.fastCode.groupDescription')" prop="fastcode_group_des">
            <!-- 组描述 -->
            <el-input v-model="form.fastcode_group_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler" @row-click="handleRowClick">
              <el-table-column  type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  v-if="1 == 0" width="10" prop="fastcode_group_id" label="id" />
              <el-table-column  :show-overflow-tooltip="true" prop="fastcode_group_code" width="210" :label="$t('lang_pack.fastCode.groupCode')" />
              <!-- 组编码 -->
              <el-table-column  :show-overflow-tooltip="true" prop="fastcode_group_des" :label="$t('lang_pack.fastCode.groupDescription')" />
              <!-- 组描述 -->

              <el-table-column  :label="$t('lang_pack.fastCode.isEnabled')" align="center" prop="enable_flag" width="100">
                <!-- 是否有效 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
                <!-- 操作 -->
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="$refs.fastCodeItem && $refs.fastCodeItem.crud.toAdd()">新增子编码</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <!-- <div
            style="
              margin-bottom: 15px;
            "
          >
            <el-button v-if="this.$refs.fastCodeItem && this.$refs.fastCodeItem.query.fastcode_group_id" class="filter-item" size="small" plain round type="primary" icon="el-icon-plus" @click="$refs.fastCodeItem && $refs.fastCodeItem.crud.toAdd()">
              新增
            </el-button>
          </div> -->
            <fastCodeItem ref="fastCodeItem" class="tableFirst" :fastcode_group_id="currentFastcodeGroupId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudFastCode from '@/api/core/system/sysFastcode'
import fastCodeItem from './fastCodeItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  fastcode_group_id: '',
  fastcode_group_code: '',
  fastcode_group_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_FASTCODE',
  components: { crudOperation, rrOperation, udOperation, pagination, fastCodeItem },
  cruds() {
    return CRUD({
      title: '快速编码',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'fastcode_group_id',
      // 排序
      sort: ['fastcode_group_id asc'],
      // CRUD Method
      crudMethod: { ...crudFastCode },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      },
      // 设置默认分页大小为20条每页
      props: {
        pageSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_fastcode:add'],
        edit: ['admin', 'sys_fastcode:edit'],
        del: ['admin', 'sys_fastcode:del'],
        down: ['admin', 'sys_fastcode:down']
      },
      rules: {
        fastcode_group_code: [{ required: true, message: '请输入快速编码', trigger: 'blur' }]
      },
      customPopover: false,
      currentFastcodeGroupId: 0
    }
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {
    handleRowClick(row, column, event) {
      this.currentFastcodeGroupId = row.fastcode_group_id
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
</style>
