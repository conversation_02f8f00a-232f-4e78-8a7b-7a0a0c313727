<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="送货明细" width="60%" :before-close="handleClose" :close-on-click-modal="false" :visible.sync="dialogVisible">
          <el-card ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-10 col-12">
                  <div class="formChild col-md-4 col-12">
                    <el-form-item label="单号：">
                      <!-- 单号 -->
                      <el-input v-model="query.task_no" clearable size="small" />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormSecond formChild col-md-2 col-12">
                  <el-form-item>
                    <rrOperation />
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </el-card>
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="matnr"
              label="物料号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="maktx"
              label="物料名称"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="sh_num"
              label="送货总重量"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="qualified_num"
              label="送货合格总重量"
            />
            <!-- Table单条操作-->
            <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" width="120" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" @click="computedWeight(scope.row)">计算分摊重量</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
    <el-dialog title="钢板分摊重量" width="60%" :before-close="handleCloseWeight" :close-on-click-modal="false" :visible.sync="dialogVisibleWeight">
      <el-card ref="queryCard" shadow="never" class="wrapCard">
        <el-form ref="query" :inline="true" size="small">
          <div class="wrapElForm">
            <div class="wrapElFormFirst col-md-9 col-12">
              <div class="formChild col-md-4 col-12">
                <el-form-item label="数量：">
                  <!-- 数量 -->
                  <el-input v-model.number="orderNumber" type="number" />
                </el-form-item>
              </div>
            </div>
            <div class="wrapElFormSecond formChild col-md-3 col-12">
              <el-form-item>
                <el-button class="filter-item" size="small" type="primary" @click="handleOk">数量确认</el-button>
                <el-button class="filter-item" size="small" type="primary" @click="handleAdd">新增一行</el-button>
              </el-form-item>
            </div>
          </div>
        </el-form>
        <el-form ref="baseForm" class="base-form" :model="baseForm" :rules="rules" auto-complete="on">
          <el-table
            ref="table1"
            border
            size="small"
            :data="baseForm.dataList"
            style="width: 100%"
            :height="height"
            :highlight-current-row="true"
          >
            <el-table-column align="center" prop="length" label="长">
              <template slot-scope="scope">
                <!-- <el-input v-model="scope.row.c" placeholder="请输入长" /> -->
                <el-form-item :prop="'dataList.'+scope.$index+'.length'" :rules="rules.length" class="all">
                  <el-input v-model.number="scope.row.length" type="number" placeholder="请输入长" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.length`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="width" label="宽">
              <template slot-scope="scope">
                <!-- <el-input v-model="scope.row.k" placeholder="请输入宽" /> -->
                <el-form-item :prop="'dataList.'+scope.$index+'.width'" :rules="rules.width" class="all">
                  <el-input v-model.number="scope.row.width" type="number" placeholder="请输入宽" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.width`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="area" label="面积">
              <template slot-scope="scope">
                <!-- <el-input v-model="scope.row.mj" disabled placeholder="请输入面积" /> -->
                <el-form-item :prop="'dataList.'+scope.$index+'.area'" class="all">
                  <el-input v-model="scope.row.area" disabled placeholder="请输入面积" clearable />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="quantity" label="数量">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.quantity'" :rules="rules.quantity" class="all">
                  <el-input v-model.number="scope.row.quantity" type="number" placeholder="请输入数量" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.quantity`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="allocation" label="分摊重量">
              <template slot-scope="scope">
                <!-- <el-input v-model="scope.row.ftzl" disabled placeholder="请输入分摊重量" /> -->
                <el-form-item :prop="'dataList.'+scope.$index+'.allocation'" class="all">
                  <el-input v-model="scope.row.allocation" disabled placeholder="请输入分摊重量" clearable />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" width="120" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" @click="copyData(scope.row)">复制</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="baseForm.dataList.length===0" @click="computedData">计算</el-button>
        <el-button type="primary" :disabled="resultCode !== 0" @click="handleSave">保存</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>
<script>
import crudOrder from '@/api/dcs/core/fmodModel/order'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  material_code: ''
}
export default {
  name: 'SORTRESULT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '分拣结果',
      // 唯一字段
      idField: 'page_id',
      // 排序
      sort: ['page_id asc'],
      // CRUD Method
      crudMethod: { ...crudOrder },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false,
      dialogVisibleWeight: false,
      orderNumber: '',
      baseForm: {
        dataList: []
      },
      index: 0,
      rules: {
        length: [{ required: true, message: '请输入长', trigger: 'blur' }],
        width: [{ required: true, message: '请输入宽', trigger: 'blur' }],
        quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }]
      },
      resultCode: -1,
      weightObject: {}
    }
  },
  dicts: ['PART_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    computedWeight(row) {
      console.log(row)
      this.weightObject = row
      this.dialogVisibleWeight = true
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    },
    copyData(row) {
      this.$confirm('确定要把所有数据改成当前行的长宽吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.baseForm.dataList = this.baseForm.dataList.map(item => {
          return {
            ...item,
            length: row.length,
            width: row.width,
            quantity: row.quantity
          }
        })
      })
    },
    // 数量确认，去循环
    handleOk() {
      if (this.orderNumber <= 0) {
        this.$message({ type: 'error', message: '请输入大于0的数量' })
        return
      }
      this.baseForm.dataList = []
      for (let i = 0; i < this.orderNumber; i++) {
        this.baseForm.dataList.push(
          { length: '', width: '', area: '', quantity: '', allocation: '' }
        )
      }
    },
    // 新增一行
    handleAdd() {
      this.orderNumber++
      this.baseForm.dataList.push(
        { length: '', width: '', area: '', quantity: '', allocation: '' }
      )
    },
    // 计算
    computedData() {
      // 表格里面添加输入框校验
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          // 得到长宽数量需要传给数据到接口
          const lengthAndWidthList = this.baseForm.dataList.map(item => {
            return {
              length: item.length,
              width: item.width,
              quantity: item.quantity
            }
          })
          //   totalWeight 是当前行的qualified_num
          crudOrder.dcsCalculate({ lengthAndWidthList, totalWeight: this.weightObject.qualified_num }).then(res => {
            if (res.code === 0) {
              this.resultCode = res.code
              this.baseForm.dataList = res.data || []
              this.$message({ type: 'success', message: '计算成功' })
              return
            }
            this.$message({ type: 'error', message: '计算失败：' + res.msg })
          }).catch(error => {
            this.$message({ type: 'error', message: '计算失败：' + error.msg })
          })
        }
      })
    },
    handleCloseWeight() {
      this.dialogVisibleWeight = false
    },
    // 保存
    handleSave() {
      const data = {
        maktx: this.weightObject.maktx, // 当前行的maktx
        matnr: this.weightObject.matnr, // 当前行的matnr
        _id: this.weightObject._id, // 当前行的_id
        lengthAndWidthList: this.baseForm.dataList // 计算后得到的重量和面积
      }
      crudOrder.dcsSave(data).then(res => {
        if (res.code === 0) {
          this.baseForm.dataList = [] // 保存后情况数据
          this.orderNumber = ''
          this.dialogVisibleWeight = false
          this.$message({ type: 'success', message: '保存成功' })
          return
        }
        this.$message({ type: 'error', message: '保存失败：' + res.msg })
      }).catch(error => {
        this.$message({ type: 'error', message: '保存失败：' + error.msg })
      })
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
::v-deep .el-dialog{
    margin-top: 5vh !important;
}
.base-form{
    .el-table--small .el-table__cell{
        // margin-top: 5px !important;
    }
}

</style>
