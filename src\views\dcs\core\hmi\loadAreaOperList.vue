<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务来源：">
                <!-- 任务来源： -->
                <el-select v-model="form.task_from" clearable filterable>
                    <el-option v-for="item in dict.DATA_SOURCES" :key="item.id" :label="item.label" :value="item.value" >
                        <span style="float: left">{{ item.label }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                    </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号：">
                <!-- 任务号： -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="钢板型号：">
                <!-- 钢板型号： -->
                <el-select v-model="query.model_type" clearable filterable>
                    <el-option v-for="item in modelList" :key="item.model_id" :label="item.model_type"
                        :value="item.model_type" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务状态：">
                <!-- 任务状态： -->
                <el-select v-model="query.task_status" clearable filterable>
                    <el-option v-for="item in dict.PROD_TASK_STATUS" :key="item.id" :label="item.label" :value="item.value">
                      <span style="float: left">{{ item.label }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                    </el-option> 
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button size="small" type="warning" icon="el-icon-edit" plain round>
            导入
          </el-button>
          <el-button size="small" type="success" icon="el-icon-check" plain round>
            排程
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU" @closed="drawerClose" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px"
          :inline="true">
          <el-form-item :label="$t('lang_pack.taskList.TaskSource')" prop="flow_mod_main_id">
            <!-- 任务来源 -->
            <el-select v-model="form.flow_mod_main_id" filterable clearable>
              <el-option value="1">1</el-option>
              <el-option value="2">2</el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.NumberOfTasks')" prop="flow_main_code">
            <!-- 任务数量 -->
            <el-input v-model="form.flow_main_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.SteelPlateModel')">
            <!-- 钢板型号 -->
            <el-input v-model="form.flow_main_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.TargetCuttingMachine')" prop="flow_main_code">
            <!-- 目标切割机 -->
            <el-select v-model="form.flow_mod_main_id" filterable clearable>
              <el-option value="1">1</el-option>
              <el-option value="2">2</el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.ScheduleDate')" prop="flow_main_code">
            <!-- 计划日期 -->
            <el-input v-model="form.flow_main_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.CutType')" prop="flow_main_code">
            <!-- 切割类型 -->
            <el-select v-model="form.flow_mod_main_id" filterable clearable>
              <el-option value="1">1</el-option>
              <el-option value="2">2</el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.DXFFiles')" prop="flow_main_code">
            <!-- DXF 文件 -->
            <el-upload ref="upload" :multiple="true" class="upload-demo" action="" drag="" :limit="uploadLimit"
              :accept="uploadAccept" :on-change="handleImport" :auto-upload="false" :http-request="uploadFile"
              :on-progress="progressA" :file-list="fileList" name="file">
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
            </el-upload>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.NCFiles')" prop="flow_main_code">
            <!-- NC 文件 -->
            <el-upload ref="upload" :multiple="true" class="upload-demo" action="" drag="" :limit="uploadLimit"
              :accept="uploadAccept" :on-change="handleImport" :auto-upload="false" :http-request="uploadFile"
              :on-progress="progressA" :file-list="fileList" name="file">
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
            </el-upload>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.CrownBlockStatus')">
            <!-- 天车状态 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskList.CuttingStatus')">
            <!-- 切割状态 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel')
          }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
            @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border  ref="table" v-loading="crud.loading" size="small"
            :data="crud.data" :cell-style="crud.cellStyle" highlight-current-row :height="height"
            @selection-change="crud.selectionChangeHandler">
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" prop="flow_main_id" label="id" />
            <!-- 任务号 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_num"
              :label="$t('lang_pack.taskList.TaskNumber')" />
            <!-- 任务状态 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_status"
              :label="$t('lang_pack.taskList.TaskStatus')" >
              <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
                </template>
            </el-table-column>
            <!-- 计划日期 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="plan_date"
              :label="$t('lang_pack.taskList.ScheduleDate')" />
            <!-- 钢板型号 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="model_type"
              :label="$t('lang_pack.taskList.SteelPlateModel')" />
            <!-- 目标切割机 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="cut_code"
              :label="$t('lang_pack.taskList.TargetCuttingMachine')" />
            <!-- 切割类型 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cut_type"
                :label="$t('lang_pack.taskTable.cutterbarType')" width="100" align='center'>
                <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ dict.label.CUT_TYPE[scope.row.cut_type] }}
                </template>
            </el-table-column>
            <!-- 任务创建时间 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_creation_time"
              :label="$t('lang_pack.taskList.TaskCreationTime')" />
            <!-- 任务来源 -->
            <el-table-column  :show-overflow-tooltip="true" prop="task_from"
                :label="$t('lang_pack.taskTable.taskSource')" width="80" align='center'>
                <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
                </template>
            </el-table-column>
            <!-- 是否自动天车 -->
            <el-table-column  :show-overflow-tooltip="true" prop="is_auto_car"
                :label="$t('lang_pack.taskTable.automaticCrownBlockOrNot')" width="100" align='center'>
                <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.is_auto_car == 'Y' ? '自动' : '手动' }}
                </template>
            </el-table-column>
            <!-- 是否切割 -->
            <el-table-column  :show-overflow-tooltip="true" prop="is_auto_cut"
                :label="$t('lang_pack.taskTable.whetherToAutomaticallyCut')" width="100" align='center' >
                <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.is_auto_cut == 'Y' ? '是' : '否' }}
                </template>
            </el-table-column>
            <!-- 是否分拣 -->
            <el-table-column  :show-overflow-tooltip="true" prop="is_auto_sort"
                :label="$t('lang_pack.taskTable.automaticSortingOrNot')" width="100" align='center'>
                <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.is_auto_sort == 'Y' ? '是' : '否' }}
                </template>
            </el-table-column>
            <!-- 是否有效 -->
            <el-table-column  :label="$t('lang_pack.recipequality.enableFlag')"
                prop="enable_flag" width="80" align='center'>
                <!-- 有效标识 -->
                <template slot-scope="scope">
                    <!--取到当前单元格-->
                    <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
                </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
  
<script>
import crudTask from '@/api/dcs/core/aps/task'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    item_date:'',
    item_date_val:'',
    mo_id:'',
    task_num:'',
    task_from:'',
    serial_num:'',
    lot_num:'',
    task_type:'',
    material_code:'',
    material_des:'',
    material_draw:'',
    model_type:'',
    m_length:'',
    m_width:'',
    m_height:'',
    m_weight:'',
    m_texture:'',
    cut_texture:'',
    npa_startx:'',
    npa_starty:'',
    dxf_name:'',
    dxf_url:'',
    json_name:'',
    json_url:'',
    json_data:'',
    nc_name:'',
    nc_url:'',
    plan_date:'',
    task_order:'',
    task_status:'',
    task_msg:'',
    cut_code:'',
    cut_type:'',
    cut_plan_minutes:'',
    task_number:1,
    is_auto_blast:'Y',
    is_auto_car:'Y',
    is_auto_cut:'Y',
    is_auto_sort:'Y',
    is_center:'Y',
    is_materialt:'Y',
    now_station_code:'',
    bg_flag:'Y',
    enable_flag:'Y',
    attribute1:'',
    attribute2:'',
    attribute3:'',
}
export default {
  name: 'RCS_FLOW_MAIN',
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
  },
  cruds() {
    return CRUD({
      title: '任务信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mo_id',
      // 排序
      sort: ['mo_id asc'],
      // CRUD Method
      crudMethod: { ...crudTask },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'APS_TASK_TYPE','CUT_TYPE','DATA_SOURCES','CUTTERBAR_TEXTURE','PROD_TASK_STATUS'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      uploadLimit: 1, // 注意：加密程序需要传2个文件
      uploadAccept: '.jar,.tar.gz',
      fileList: [],
      permission: {
        add: ['admin', 'rcs_flow_main:add'],
        edit: ['admin', 'rcs_flow_main:edit'],
        del: ['admin', 'rcs_flow_main:del'],
        down: ['admin', 'rcs_flow_main:down']
      },
      rules: {
        flow_mod_main_id: [{ required: true, message: '请选择流程模板', trigger: 'blur' }],
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        flow_main_code: [{ required: true, message: '请输入主流程编码', trigger: 'blur' }],
        flow_main_des: [{ required: true, message: '请输入主流程描述', trigger: 'blur' }],
        flow_task_head: [{ required: true, message: '请输入任务编号前缀', trigger: 'blur' }],
        client_id_list: [{ required: true, message: '请选择实例集合', trigger: 'blur' }],
        create_task_tag_id: [{ required: true, message: '请选择触发点位', trigger: 'blur' }],
        cycle_time: [{ required: true, message: '请输入轮询时间', trigger: 'blur' }]
      },
      customPopover: false,
      currentRow: {},
      modelList:[],
    }
  },

  mounted: function () {
    const that = this
    that.$refs.table.doLayout()
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function () {
    const query = {
          userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
                if(defaultQuery.data.length > 0){
                    this.modelList = defaultQuery.data
                }
            }
        })
        .catch(() => {
          this.$message({
              message: '型号查询异常',
              type: 'error'
          })
      })
  },
  methods: {
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      // crud.form.client_id_list = crud.form.client_id_list === '' ? '' : crud.form.client_id_list.split(',')
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      // crud.form.client_id_list = this.form.client_id_list === '' ? '' : this.form.client_id_list.join(',')
      return true
    },
    drawerClose() {
      this.fileList = []
    },
    // 抽屉上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      return 
    },
    // 文件
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.append('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {}
  }
}
</script>
<style lang="scss" scoped>
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px;
}

::v-deep .step-attr-dialog .el-dialog__body {
  padding: 0px;
  overflow-y: auto;
}
::v-deep .el-upload-dragger{
  width: 200px;
}
</style>
  