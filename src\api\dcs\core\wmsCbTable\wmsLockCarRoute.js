import request from '@/utils/request'

// 查询WMS调度任务路线表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarRouteSelect',
    method: 'post',
    data
  })
}
// 新增WMS调度任务路线表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarRouteInsert',
    method: 'post',
    data
  })
}
// 修改WMS调度任务路线表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarRouteUpdate',
    method: 'post',
    data
  })
}
// 删除WMS调度任务路线表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarRouteDelete',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

