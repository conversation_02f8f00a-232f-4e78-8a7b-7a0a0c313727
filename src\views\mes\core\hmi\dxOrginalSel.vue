<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="120px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="电芯码：">
                <el-input ref="dxBarcode" v-model="query.dx_barcode" clearable size="small" @input="handleInput" />
              </el-form-item>
            </div>
            <div class="wrapElFormSecond formChild col-md-2 col-12">
              <el-form-item>
                <!-- <rrOperation /> -->
                <span class="wrapRRItem">
                  <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                  <el-button class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
                </span>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            border
            size="small"
            :data="originalData"
            style="width: 100%"
            :height="height"
            highlight-current-row
          >
            <el-table-column :show-overflow-tooltip="true" prop="RFD001" label="电芯档位" />
            <el-table-column :show-overflow-tooltip="true" prop="TOTAL_END_CAP" label="电芯容量4" />
            <el-table-column :show-overflow-tooltip="true" prop="OCV4" label="电芯V4电压" />
            <el-table-column :show-overflow-tooltip="true" prop="OCR4" label="电芯V4电阻" />
            <el-table-column :show-overflow-tooltip="true" prop="K_VALUE" label="电芯K值" />
            <el-table-column :show-overflow-tooltip="true" prop="C_THI" label="电芯厚度" />
            <el-table-column :show-overflow-tooltip="true" prop="TEST_TIME" label="0CV4时间" />
            <el-table-column :show-overflow-tooltip="true" prop="RBJ002" label="电芯V5电压" />
            <el-table-column :show-overflow-tooltip="true" prop="RBJ003" label="包胶壳压" />
            <el-table-column :show-overflow-tooltip="true" prop="RBJ004" label="电芯V5内阻" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { sel } from '@/api/mes/core/mesDxOriginalSel'
export default {
  name: 'DX_ORIGINAL_SEL',
  data() {
    return {
      height: document.documentElement.clientHeight - 160,
      query: {
        dx_barcode: ''
      },
      originalData: []

    }
  },
  mounted: function() {
    this.$nextTick(() => {
      this.$refs.dxBarcode.focus()
    })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 160
    }
  },
  methods: {
    handleInput(e) {
      if (e.length !== 24) return
      this.getOriginalData(e)
    },
    toQuery() {
      if (!this.query.dx_barcode || this.query.dx_barcode.length !== 24) {
        this.$message({ type: 'warning', message: '请输入电芯码或电芯码不符合标准' })
        return
      }
      this.getOriginalData(this.query.dx_barcode)
    },
    getOriginalData(dx_barcode) {
      const query = {
        tenantID: 6000002,
        dxCode: dx_barcode
      }
      // 这个地址是唐山国轩mes的接口，我们环境掉不通 ，这是他们的返回的数据格式
      //   {
      //     "traceID": "1852541602904797184",
      //     "code": "200",
      //     "data": {
      //         "K_VALUE": "0.8600",
      //         "RFD001": "0",
      //         "OCV4": "3239.3346",
      //         "TOTAL_END_CAP": "-337.2134",
      //         "RBJ003": "3228.59",
      //         "RBJ002": "3235.00",
      //         "RBJ004": "0.16",
      //         "C_THI": "71.440",
      //         "OCR4": "0.1810",
      //         "TEST_TIME": "2024-08-29 04:04:49"
      //     },
      //     "message": "操作成功",
      //     "status": true,
      //     "errorMsg": "are no errors"
      // }
      sel(query).then(res => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.$message({ type: 'success', message: '查询成功' })
            this.originalData = res.data
            return
          }
          this.originalData = []
          this.$message({ type: 'success', message: '根据当前电芯码未查询到相关数据' })
          return
        }
        this.$message({ type: 'error', message: res.msg })
      }).catch(err => {
        this.originalData = []
        this.$message({ type: 'error', message: '获取失败' + err.msg })
      }).finally(() => {
        this.resetQuery()
      })
    },
    resetQuery() {
      this.query.dx_barcode = ''
      this.$nextTick(() => {
        this.$refs.dxBarcode.focus()
      })
      // this.originalData = []
    }
  }
}
</script>
