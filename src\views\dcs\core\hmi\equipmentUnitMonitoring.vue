<template>
    <div class="mainPage-container">
        <el-card class="wrapCard CardTwo">
            <el-row :gutter="24" class="elRowStyle">
                <el-col :span="24">
                    <el-card class="cardFirst">
                        <el-descriptions :column="3" :labelStyle="{'font-size':'18px'}" :contentStyle="{'font-size':'18px'}">
                            <el-descriptions-item label="设备名称">GF-CBS600型</el-descriptions-item>
                            <el-descriptions-item label="工位号">JX01001交叉带分拣</el-descriptions-item>
                            <el-descriptions-item label="设备厂家">中国制造</el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                </el-col>
            </el-row>
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-card class="cardFirst">
                        <el-col :span="3" v-for="item in deviceStatus" :key="item.id">
                            <div>
                                <el-statistic :title="item.des">
                                    <span slot="formatter" class="deviceTpye" :style="{ background: item.type }"></span>
                                </el-statistic>
                            </div>
                        </el-col>
                    </el-card>
                    <el-card class="cardFirst">
                        <el-table border ref="table"  v-loading="loading"
                            :data="deviceData" :row-key="row => row.id" :height="deviceHeight">
                            <!-- 分类 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="fl"
                                label="分类"   />
                            <!-- 参数名称 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="csmc"
                                label="参数名称"   />
                            <!-- 监控值 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="jkz"
                                label="监控值"   />
                            <!-- 曲线分析 -->
                            <el-table-column :show-overflow-tooltip="true" align="center" label="曲线分析"  >
                                <template slot-scope="scope">
                                    <span class="inbOutNum">1111</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </el-col>
                <el-col :span="12" class="cardFirst" >
                    <el-card shadow="never" :style="{height:deviceHeight > 0 ? deviceHeight + 100 + 'px' :deviceHeight + 440 + 'px'}">
                        <div slot="header" class="clearfix1" style="font-size: 14px; color: #757575; font-weight: 700">
                            <div style="
                                    height: 33px;
                                    line-height: 33px;
                                    font-size: 15px;
                                    color: #333333;
                                    ">
                                产能数据
                            </div>
                            <div>
                                <el-button   size="small"
                                    type="primary">周产能</el-button>
                                    <el-button  size="small"
                                    type="primary">日产能</el-button>
                            </div>
                        </div>
                        <report />
                    </el-card>
                </el-col>
            </el-row>
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-card class="cardFirst">
                        <div class="wrapTextSelect">
                            <div class="elliptic">
                                <span>报警信息与故障分析</span>
                            </div>
                        </div>
                        <el-table border ref="table"  v-loading="loading"
                            :data="faultData" :row-key="row => row.id" :height="faultHeight">
                            <!-- 设备名称 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="sbmc"
                                label="设备名称"  />
                            <!-- 报警分类 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjfl"
                                label="报警分类"  />
                            <!-- 报警级别 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjjb"
                                label="报警级别"  />
                            <!-- 报警时间 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjsj"
                                label="报警时间"  />
                            <!-- 报警时间信息 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="bjsjxx"
                                label="报警时间信息"   />
                            <!-- 时间 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="sj"
                                label="时间"   />
                        </el-table>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card class="cardFirst" :style="{height:faultHeight > 0 ? faultHeight + 50+'px' :faultHeight + 395+'px'}">
                        <el-col :span="12">
                            <div class="wrapTextSelect">
                                <div class="elliptic">
                                    <span>设备故障分析</span>
                                </div>
                            </div>
                            <devFault />
                        </el-col>
                        <el-col :span="12">
                            <div class="wrapTextSelect">
                                <div class="elliptic">
                                    <span>设备OEE</span>
                                </div>
                            </div>
                            <devOee />
                        </el-col>
                    </el-card>
                </el-col>
            </el-row>
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-card class="cardFirst">
                        <div class="wrapTextSelect">
                            <div class="elliptic">
                                <span>过站履历信息</span>
                            </div>
                        </div>
                        <el-table border ref="table"  v-loading="loading"
                            :data="stationData" :row-key="row => row.id" :height="faultHeight">
                            <!-- 任务号 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="rwh"
                                label="任务号"  />
                            <!-- 钢板型号 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="gbxh"
                                label="钢板型号"  />
                            <!-- 到达时间 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="ddsj"
                                label="到达时间"  />
                            <!-- 离开时间 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="lksj"
                                label="离开时间"  />
                            <!-- 节拍 -->
                            <el-table-column  :show-overflow-tooltip="true" align="center" prop="jp"
                                label="节拍"   />
                            <!-- 时间 -->
                        </el-table>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card class="cardFirst" :style="{height:faultHeight > 0 ? faultHeight + 50+'px' :  faultHeight + 375+'px'}">
                        <div class="wrapTextSelect">
                            <div class="elliptic">
                                <span>参数设定</span>
                            </div>
                        </div>
                        <el-form ref="query" :inline="true"  size="small" >
                            <div class="wrapElForm" style="display:flex">
                                <div class="wrapElFormFirst col-md-8 col-12">
                                    <div class="formChild col-md-12 col-12" >
                                        <el-form-item :label="$t('lang_pack.sortingArea.CurrentSortingStation')">
                                            <!-- 当前分拣工位 -->
                                            <el-input class="filter-item"  />
                                        </el-form-item>
                                    </div>
                                    <div class="formChild col-md-12 col-12">
                                        <el-form-item :label="$t('lang_pack.sortingArea.TargetTransmissionStation')">
                                            <!-- 目标传输工位 -->
                                            <el-input class="filter-item" />
                                        </el-form-item>
                                    </div>
                                    <div class="formChild col-md-12 col-12">
                                        <el-form-item :label="$t('lang_pack.sortingArea.TargetTransmissionStation')">
                                            <!-- 目标传输工位 -->
                                            <el-input class="filter-item" />
                                        </el-form-item>
                                    </div>
                                </div>
                                <div class="wrapElFormFirst col-md-4 col-12">
                                    <div class="formChild col-md-12 col-12" >
                                        <el-button   size="small"
                                    type="primary">周产能</el-button>
                                    </div>
                                    <div class="formChild col-md-12 col-12" >
                                        <el-button   size="small"
                                    type="primary">周产能</el-button>
                                    </div>
                                    <div class="formChild col-md-12 col-12" >
                                        <el-button   size="small"
                                    type="primary">周产能</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-form>
                    </el-card>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
<script>
import report from '../../modules/echartReport.vue'
import devOee from '../../modules/deviceOee.vue'
import devFault from '../../modules/Analysis.vue'
export default {
    name: 'equipmentUnitMonitoring',
    components: {report,devOee,devFault},
    data() {
        return {
            deviceStatus: [
                { id: 1, des: '自动', type: '#50f73c' },
                { id: 2, des: '联机', type: 'gray' },
                { id: 3, des: '通讯', type: 'gray' },
                { id: 4, des: '故障', type: 'gray' },
                { id: 5, des: '保养', type: 'gray' },
                { id: 6, des: '维修', type: 'gray' },
                { id: 7, des: '生产', type: 'gray' },
            ],
            loading: false,
            deviceHeight: document.documentElement.clientHeight - 640,
            deviceData: [
                { id: 1, fl: '工艺参数', csmc: '参数1', jkz: '工艺值1' },
                { id: 2, fl: '工艺参数', csmc: '参数1', jkz: '工艺值1' },
                { id: 3, fl: '工艺参数', csmc: '参数1', jkz: '工艺值1' },
                { id: 4, fl: '工艺参数', csmc: '参数1', jkz: '工艺值1' }
            ],
            faultHeight: document.documentElement.clientHeight - 620,
            faultData:[
                { id: 1, sbmc: '工艺参数', bjfl: '参数1', bjjb: '工艺值1',bjsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',sj:'2023-05-19' },
                { id: 2, sbmc: '工艺参数', bjfl: '参数1', bjjb: '工艺值1' ,bjsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',sj:'2023-05-19'},
                { id: 3, sbmc: '工艺参数', bjfl: '参数1', bjjb: '工艺值1' ,bjsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',sj:'2023-05-19'},
                { id: 4, sbmc: '工艺参数', bjfl: '参数1', bjjb: '工艺值1',bjsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',sj:'2023-05-19' }
            ],
            stationData:[
            { id: 1, rwh: '工艺参数', gbxh: '参数1', bjjb: '工艺值1',ddsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',lksj:'2023-05-19' },
            { id: 2, rwh: '工艺参数', gbxh: '参数1', bjjb: '工艺值1' ,ddsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',lksj:'2023-05-19'},
            { id: 3, rwh: '工艺参数', gbxh: '参数1', bjjb: '工艺值1' ,ddsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',lksj:'2023-05-19'},
            { id: 4, rwh: '工艺参数', gbxh: '参数1', bjjb: '工艺值1',ddsj:'2023-05-19 17:55:25',bjsjxx:'温度异常',lksj:'2023-05-19' }
            ]
        }
    }
}
</script>
<style scoped lang="less">
.cardFirst {
    margin: 5px 0;
   
    .deviceTpye {
        display: block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin: 5px 0 10px;
    }
    .clearfix1{
        margin: 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .wrapTextSelect{
        margin-bottom: 10px;
    }
    .wrapElFormFirst{
        .formChild{
            margin: 11px 0;
        }
    }
}
::v-deep .el-card__header{
        padding: 10px !important;
        border: none;
    }
</style>