<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="区域描述：">
                <el-input v-model="query.region_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="工位：">
                <el-select v-model="query.station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap form-colum" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item label="工位" prop="station_id">
            <el-select v-model="form.station_id" filterable clearable>
              <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="区域编码" prop="region_code">
            <el-input v-model="form.region_code" />
          </el-form-item>
          <el-form-item label="区域描述" prop="region_des">
            <el-input v-model="form.region_des" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-select v-model="form.enable_flag" clearable>
              <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column  type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  :show-overflow-tooltip="true" prop="station_id" label="工位" />
              <el-table-column  :show-overflow-tooltip="true" prop="region_code" label="区域编码" />
              <el-table-column  :show-overflow-tooltip="true" prop="region_des" label="区域描述" />
              <el-table-column  label="是否有效" align="center" prop="enable_flag">
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column  label="操作" width="175" align="center" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="$refs.detail && $refs.detail.crud.toAdd()">新增</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <detail ref="detail" class="tableFirst" :screen_config_detail_id="currentScreenDeployID" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import eapScreenConfig from '@/api/eap/core/eapScreenConfig'
import { sel as selStation } from '@/api/core/factory/sysStation'
import detail from './detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  station_id: '',
  screen_config_id: '',
  region_code: '',
  region_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'EAP_SCREEN_CONFIG',
  components: { crudOperation, rrOperation, udOperation, pagination, detail },
  props: {},
  cruds() {
    return CRUD({
      title: '大屏配置',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'screen_config_id',
      // 排序
      sort: ['screen_config_id asc'],
      // CRUD Method
      crudMethod: { ...eapScreenConfig },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_eap_fmod_screen_deploy:add'],
        edit: ['admin', 'c_eap_fmod_screen_deploy:edit'],
        del: ['admin', 'c_eap_fmod_screen_deploy:del']
      },
      currentScreenDeployID: 0,
      stationData: [],
      rules: {
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        region_code: [{ required: true, message: '请填写区域编码', trigger: 'blur' }],
        region_des: [{ required: true, message: '请填写区域描述', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      }
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    handleRowClick(row, column, event) {
      this.currentScreenDeployID = row.screen_config_id
      console.log(row.station_id)
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.form-colum{
  flex-direction: column;
  .el-form-item{
    width: 100%;
  }
}
</style>
