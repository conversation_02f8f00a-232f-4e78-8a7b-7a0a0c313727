import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodShipAddressSel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodShipAddressIns',
    method: 'post',
    data
  })
}
// 编辑
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodShipAddressUpdate',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodShipAddressDel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del }

