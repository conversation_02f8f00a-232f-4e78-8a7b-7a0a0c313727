<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!-- 料号 -->
            <!-- <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div> -->
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :header-cell-style="headerCellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="规则名称" width="130" align="center" prop="name" />
            <el-table-column :show-overflow-tooltip="true" label="规则代码" width="130" align="center" prop="code" />
            <el-table-column :show-overflow-tooltip="true" label="板件类型" width="100" align="center" prop="boardType">
              <template slot-scope="scope">
                {{ boardTypes.find(item => item.value === scope.row.boardType).label }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="源字段" width="200" align="center" prop="srcKey" />
            <el-table-column label="截取起始" width="90" align="center" prop="srcSplitIndex" />
            <el-table-column label="截取长度" width="90" align="center" prop="srcSplitLength" />
            <el-table-column label="比对函数" width="100" align="center" prop="compareFunc">
              <template slot-scope="scope">
                {{ compareFuncs.find(item => item.value === scope.row.compareFunc).label }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="目标字段" width="200" align="center" prop="dstKey" />
            <el-table-column label="截取起始" width="90" align="center" prop="dstSplitIndex" />
            <el-table-column label="截取长度" width="90" align="center" prop="dstSplitLength" />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
        <el-form-item label="规则名称" prop="name" display:none>
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="规则代码" prop="code" display:none>
          <el-input v-model="form.code" />
        </el-form-item>
        <el-form-item label="板件类型" prop="boardType" display:none>
          <el-select v-model="form.boardType" clearable filterable>
            <el-option v-for="item in boardTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="源字段" prop="srcKey">
          <el-input v-model="form.srcKey" />
        </el-form-item>
        <el-form-item label="截取起始" prop="srcSplitIndex">
          <el-input-number v-model="form.srcSplitIndex" :min="0" :max="99999" :step="1" clearable />
        </el-form-item>
        <el-form-item label="截取长度" prop="srcSplitLength">
          <el-input-number v-model="form.srcSplitLength" :min="0" :max="99999" :step="1" clearable />
        </el-form-item>
        <el-form-item label="比对函数" prop="compareFunc">
          <el-select v-model="form.compareFunc" clearable filterable>
            <el-option v-for="item in compareFuncs" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标字段" prop="dstKey">
          <el-input v-model="form.dstKey" />
        </el-form-item>
        <el-form-item label="截取起始" prop="dstSplitIndex">
          <el-input-number v-model="form.dstSplitIndex" :min="0" :max="99999" :step="1" :step-strictly="true" />
        </el-form-item>
        <el-form-item label="截取长度" prop="dstSplitLength">
          <el-input-number v-model="form.dstSplitLength" :min="0" :max="99999" :step="1" :step-strictly="true" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确认 -->
      </div>
    </el-drawer>
  </div>
</template>

<script>
import api from '@/api/pack/core/sortRule'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  id: '',
  name: ''
}
export default {
  name: 'PACK_SORT_RULE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.parkSortRule'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'sortId',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      boardTypes: [
        { label: 'SET', value: 'Set' },
        { label: 'PCS', value: 'Pcs' }
      ],
      compareFuncs: [
        { label: '等于', value: '=' },
        { label: '不等于', value: '!=' },
        { label: '大于', value: '>' },
        { label: '大于等于', value: '>=' },
        { label: '小于', value: '<' },
        { label: '小于等于', value: '<=' },
        { label: '包含', value: 'contains' },
        { label: '不包含', value: 'not_contains' }
      ],
      srcProperties: ['srcKey', 'srcSplitIndex', 'srcSplitLength'],
      dstProperties: ['dstKey', 'dstSplitIndex', 'dstSplitLength'],
      form: {},
      rules: {
        name: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        srcKey: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        compareFunc: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        dstKey: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              last_update_by: Cookies.get('userName'),
              id: data.id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    test(data) {
      console.log(data)
    },
    [CRUD.HOOK.beforeToAdd](crud) {
      this.form = defaultForm
    },
    // 开始 "编辑" - 之前
    [CRUD.HOOK.beforeToEdit](crud, data) {
      this.form = data
      return true
    },
    [CRUD.HOOK.afterAddCancel](crud) {
      this.form = defaultForm
    },
    [CRUD.HOOK.afterEditCancel](crud) {
      this.form = defaultForm
    },
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      return (this.srcProperties.includes(column.property) && 'background:#FFC000 !important') || // 源字段
              (this.dstProperties.includes(column.property) && 'background:#00B0F0 !important') || // 目标字段
              ''
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
