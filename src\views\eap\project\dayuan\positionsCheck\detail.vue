<template>
  <!--模组子维护-->
  <el-card shadow="never" class="cardOther">
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="478" highlight-current-row @selection-change="crud.selectionChangeHandler">
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="work_order_d_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="tag_des" label="参数名称" />
          <el-table-column  :show-overflow-tooltip="true" prop="tag_value" label="参数值" />
          <el-table-column  :show-overflow-tooltip="true" prop="unit" label="单位" />
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import eapDyWorkOrderDetail from '@/api/eap/project/dayuan/eapDyWorkOrderDetail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'
const defaultForm = {
  work_order_d_id: ''
}
export default {
  name: 'EAP_DY_WORK_ORDER_DETAIL',
  components: { pagination },
  props: {
    work_order_d_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '工单详情',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'work_order_d_id',
      // 排序
      sort: ['work_order_d_id asc'],
      // CRUD Method
      crudMethod: { ...eapDyWorkOrderDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'eap_dy_work_order_detail:add'],
        edit: ['admin', 'eap_dy_work_order_detail:edit'],
        del: ['admin', 'eap_dy_work_order_detail:del'],
        down: ['admin', 'eap_dy_work_order_detail:down']
      }
    }
  },
  watch: {
    work_order_d_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.work_order_id = this.work_order_d_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
  }
}
</script>
<style lang="less" scoped>
.cardOther{
  border: 0 !important;
}
</style>
