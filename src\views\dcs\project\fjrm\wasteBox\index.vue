<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="废料框编码:">
                <!-- 废料框编码 -->
                <el-input v-model="query.waste_box_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.fjrm.wasteBoxCode')" prop="waste_box_code">
                <!-- 废料框编码 -->
                <el-input v-model="form.waste_box_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.wasteBoxDes')" prop="waste_box_des">
                <!-- 废料框描述 -->
                <el-input v-model="form.waste_box_des" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.width')" prop="waste_box_width">
                <!-- 总重量(KG) -->
                <el-input v-model="form.waste_box_width" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.height')" prop="waste_box_height">
                <!-- 高度 -->
                <el-input v-model="form.waste_box_height" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.enableFlag')" prop="enable_flag">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" prop="waste_box_id" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->

            <!-- 废料框编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_code"
              :label="$t('lang_pack.fjrm.wasteBoxCode')"
              align="center"
            />
            <!-- 废料框描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_des"
              :label="$t('lang_pack.fjrm.wasteBoxDes')"
              align="center"
            />

            <!-- 总重量(KG) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_width"
              :label="$t('lang_pack.fjrm.width')"
              align="center"
            />

            <!-- 高度 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_height"
              :label="$t('lang_pack.fjrm.height')"
              align="center"
            />

            <el-table-column
              :label="$t('lang_pack.fjrm.enableFlag')"
              align="center"
              width="120"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              prop="button"
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWasteBox from '@/api/dcs/project/fjrm/wasteBox/wasteBox'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  waste_box_id: '',
  waste_box_code: '',
  waste_box_des: '',
  waste_box_width: '0',
  waste_box_height: '0',
  enable_flag: 'Y'
}
export default {
  name: 'WASTE_BOX',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '废料框基础表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'waste_box_id',
      // 排序
      sort: ['waste_box_id asc'],
      // CRUD Method
      crudMethod: { ...crudWasteBox },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'dcs_fmod_waste_box:add'],
        edit: ['admin', 'dcs_fmod_waste_box:edit'],
        del: ['admin', 'dcs_fmod_waste_box:del']
      },
      rules: {
        waste_box_code: [{ required: true, message: '请选择废料框编码', trigger: 'blur' }],
        waste_box_des: [{ required: true, message: '请选择废料框描述', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudWasteBox
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              waste_box_id: data.waste_box_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
