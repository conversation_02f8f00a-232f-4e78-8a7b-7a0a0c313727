<template>
  <div id="loadMonitor" class="app-container">
    <el-container :style="'height:'+height+'px'">
      <el-header style="height:45px;text-align:center">
        <span style="font-weight:600;float:left;cursor: pointer;text-decoration:underline" @click="openStationChoose">{{ currentStation.station_des }}</span>
        <transition name="el-zoom-in-center">
          <span v-show="messageShow" :class="'message message-'+MessageLevel"><i :class="MessageLevel==='warning'?'el-icon-warning':MessageLevel==='error'?'el-icon-error':'el-icon-info' " />&nbsp;{{ messageContent }}</span>
        </transition>
        <div style="width:335px;float:right">
          <el-button icon="el-icon-thumb" type="primary" plain @click="openManualPage">手动画面</el-button>
          <el-button icon="el-icon-camera" type="primary" plain @click="openUserLogin">扫描员工卡</el-button>
          <el-button icon="el-icon-switch-button" type="primary" plain @click="handleUserLogout">员工登出</el-button>
        </div>
      </el-header>
      <el-main>
        <el-descriptions :column="4" size="medium" border>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              员工号
            </template>
            {{ loginInfo.user_name }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              员工姓名
            </template>
            {{ loginInfo.nick_name }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              部门
            </template>
            {{ loginInfo.dept_id }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              班次
            </template>
            {{ loginInfo.shift_id }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              机台模式
            </template>
            {{ monitorData.OnOffLine.value==='1'?'在线':'离线模式' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              机台状态
            </template>
            <template v-if="monitorData.DeviceStatus.value==='1'">
              运行
            </template>
            <template v-else-if="monitorData.DeviceStatus.value==='2'">
              停止
            </template>
            <template v-else-if="monitorData.DeviceStatus.value==='3'">
              待料
            </template>
            <template v-else-if="monitorData.DeviceStatus.value==='4'">
              异常
            </template>
            <template v-else-if="monitorData.DeviceStatus.value==='5'">
              保养
            </template>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              生产模式
            </template>
            {{ monitorData.SysModel.value==='1'?'EAP远程':'AIS本地' }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">
            <template slot="label">
              <i class="el-icon-tickets" />
              Panel模式
            </template>
            {{ monitorData.PanelModel.value==='1'?'有Panel':'无Panel' }}
          </el-descriptions-item>
        </el-descriptions>
        <el-row :gutter="20" style="margin:0px auto;">
          <el-col :span="12" style="padding-left:0px;padding-right:0px;">
            <div class="port-title">
              端口1
              <div style="width:350px;float:right">
                <el-button icon="el-icon-search" type="primary" plain>任务明细</el-button>
                <el-button icon="el-icon-shopping-cart-2" type="primary" plain @click="handlePortBackRequest(1)">强制退载具</el-button>
                <el-button icon="el-icon-open" type="primary" plain>设备状态</el-button>
              </div>
            </div>
            <el-descriptions :column="1" size="medium" border>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  启用状态
                </template>
                {{ monitorData.PortOn1.value==='1'?'启用':'禁用' }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  搬运模式
                </template>
                {{ monitorData.Port1Status.value==='1'?'AGV':'MGV' }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  当前步骤
                </template>

              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  步骤错误
                </template>

              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  载具/天盖条码
                </template>
                {{ monitorData.PalletCcd1.value }}{{ monitorData.TrayCcd1.value }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  Panel条码
                </template>
                {{ monitorData.PanelCcd.value }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  在线工单
                </template>
                {{ port1TaskInfo.lot_num }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  物料号
                </template>
                {{ port1TaskInfo.material_code }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  加工面次
                </template>
                {{ port1TaskInfo.face_code }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  首板模式
                </template>
                {{ port1TaskInfo.pdb_rule }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  首板数量
                </template>
                {{ port1TaskInfo.inspect_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  完成首板数量
                </template>
                {{ port1TaskInfo.inspect_finish_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  计划数量
                </template>
                {{ port1TaskInfo.plan_lot_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  完工数量
                </template>
                {{ port1TaskInfo.finish_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  NG数量
                </template>
                {{ port1TaskInfo.finish_ng_count }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="12" style="padding-left:0px;padding-right:0px;">
            <div class="port-title">
              端口2
              <div style="width:350px;float:right">
                <el-button icon="el-icon-search" type="primary" plain>任务明细</el-button>
                <el-button icon="el-icon-shopping-cart-2" type="primary" plain @click="handlePortBackRequest(2)">强制退载具</el-button>
                <el-button icon="el-icon-open" type="primary" plain>设备状态</el-button>
              </div>
            </div>
            <el-descriptions :column="1" size="medium" border>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  启用状态
                </template>
                {{ monitorData.PortOn2.value==='1'?'启用':'禁用' }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  搬运模式
                </template>
                {{ monitorData.Port2Status.value==='1'?'AGV':'MGV' }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  当前步骤
                </template>

              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  步骤错误
                </template>

              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  载具/天盖条码
                </template>
                {{ monitorData.PalletCcd2.value }}{{ monitorData.TrayCcd2.value }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  Panel条码
                </template>
                {{ monitorData.PanelCcd.value }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  在线工单
                </template>
                {{ port2TaskInfo.lot_num }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  物料号
                </template>
                {{ port2TaskInfo.material_code }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  加工面次
                </template>
                {{ port2TaskInfo.face_code }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  首板模式
                </template>
                {{ port2TaskInfo.pdb_rule }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  首板数量
                </template>
                {{ port2TaskInfo.inspect_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  完成首板数量
                </template>
                {{ port2TaskInfo.inspect_finish_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  计划数量
                </template>
                {{ port2TaskInfo.plan_lot_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  完工数量
                </template>
                {{ port2TaskInfo.finish_count }}
              </el-descriptions-item>
              <el-descriptions-item label-class-name="port-table-descriptions-label" content-class-name="port-table-descriptions-content">
                <template slot="label">
                  <i class="el-icon-tickets" />
                  NG数量
                </template>
                {{ port2TaskInfo.finish_ng_count }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
    <el-dialog title="工位选择" :visible.sync="stationDialogVisible" width="80%" top="65px">
      <div style="width:100%">
        <div v-for="(item, index) in stationData" :key="index" style="margin-bottom:20px;">
          <el-tag :key="index" type="info" :disable-transitions="false" style="line-height:40px;height:40px;">
            {{ item.prod_line_code + ' ' + item.prod_line_des }}
          </el-tag>
          <div v-if="item.station_list.length > 0" style="margin-top:10px;">
            <el-tag v-for="(item1, index1) in item.station_list" :key="index1" :disable-transitions="false" style="margin:10px 10px 10px 0;line-height:50px;height:50px;cursor: pointer;" @click="handleStationChoose(item, item1)">
              {{ item1.station_code + ' ' + item1.station_des }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="员工登录" :visible.sync="userDialogVisible" width="650px" top="65px">
      <el-input ref="userId" v-model="userId" clearable size="mini" style="width:100%" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleUserLogin">登 录</el-button>
      </span>
    </el-dialog>
    <el-dialog title="手动画面" :visible.sync="manualDialogVisible" width="750px" top="65px">
      <div style="margin-bottom:10px;">
        <el-button type="primary" style="line-height:30px;font-size:24px;" plain @click="handleManualEvent('manual_order')">工单输入</el-button>
        <el-button type="primary" style="line-height:30px;font-size:24px;" plain @click="handleManualEvent('change_recipe')">切批确认</el-button>
        <el-button type="primary" style="line-height:30px;font-size:24px;" plain @click="handleManualEvent('pallet_manual_scan')">Pallet/Tray输入</el-button>
        <el-button type="primary" style="line-height:30px;font-size:24px;" plain @click="handleManualEvent('inspect_confirm')">首检判定</el-button>
      </div>
      <div>
        <el-button type="primary" style="line-height:30px;font-size:24px;" plain @click="handleManualEvent('panel_manual_scan')">Panel输入</el-button>
        <el-button type="primary" style="line-height:30px;font-size:24px;" plain @click="handleManualEvent('panel_manual_confirm')">混板确认</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="msgDialogTitle" :visible.sync="mgsDialogVisible" width="650px" top="65px" :close-on-click-modal="false" @close="handleCloseDialog">
      <manualOrder v-if="manualOrderShow" ref="manualOrder" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
      <changeRecipe v-if="changeRecipeShow" ref="changeRecipe" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
      <palletManualScan v-if="palletManualScanShow" ref="palletManualScan" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
      <inspectConfirm v-if="inspectConfirmShow" ref="inspectConfirm" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
      <panelManualScan v-if="panelManualScanShow" ref="panelManualScan" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
      <panelManualConfirm v-if="panelManualConfirmShow" ref="panelManualConfirm" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
    </el-dialog>
  </div>
</template>

<script>
import { selTree as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import { selLoginInfo, userLogin, userLogout } from '@/api/eap/eapMeStationUser'
import { stationTaskSel } from '@/api/eap/eapApsPlan'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
import manualOrder from '@/views/eap/core/loadMonitor/manualOrder'
import changeRecipe from '@/views/eap/core/loadMonitor/changeRecipe'
import palletManualScan from '@/views/eap/core/loadMonitor/palletManualScan'
import inspectConfirm from '@/views/eap/core/loadMonitor/inspectConfirm'
import panelManualScan from '@/views/eap/core/loadMonitor/panelManualScan'
import panelManualConfirm from '@/views/eap/core/loadMonitor/panelManualConfirm'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'EAP_LOAD_MONITOR',
  components: { manualOrder, changeRecipe, palletManualScan, inspectConfirm, panelManualScan, panelManualConfirm },
  // 数据模型
  data() {
    return {
      timer: '',
      timer1: '',
      height: document.documentElement.clientHeight - 100,
      stationDialogVisible: false,
      prodLineData: [],
      stationData: [],
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '《请选择工位》',
        cell_id: '0'
      },
      manualDialogVisible: false,
      userDialogVisible: false,
      userId: '',
      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      port1TaskInfo: {},
      port2TaskInfo: {},
      messageList: [],
      messageContent: '',
      MessageLevel: 'info',
      messageShow: false,
      // 监控弹窗相关
      msgDialogTitle: '提示',
      mgsDialogVisible: false,
      manualOrderShow: false,
      changeRecipeShow: false,
      palletManualScanShow: false,
      inspectConfirmShow: false,
      panelManualScanShow: false,
      panelManualConfirmShow: false,
      tagKeyList: {},
      currentFuncCode: '',
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 启用监听数据模式 AIS-PC=电脑模式,AIS-SERVER=服务器模式
      // AIS-SERVER模式，监听的Client Code需要拼接上工位编码,例如：LoadPlc_OP1010
      aisMonitorMode: 'AIS-PC',
      // 监听数据
      monitorData: {
        OnOffLine: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'OnOffLine', tag_des: '[PlcConfig]在线/离线模式', value: '' },
        DeviceStatus: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'DeviceStatus', tag_des: '[PlcStatus]设备状态(1运行、2停止 、3待料、4异常、5保养)', value: '' },
        SysModel: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'SysModel', tag_des: '[PlcConfig]AIS本地/EAP远程模式切换', value: '' },
        PanelModel: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PanelModel', tag_des: '[PlcConfig]Panel模式(1有/0无)', value: '' },
        PortOn1: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PortOn1', tag_des: '[PlcConfig]端口1启用状态(1启用/0禁用)', value: '' },
        PortOn2: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PortOn2', tag_des: '[PlcConfig]端口2启用状态(1启用/0禁用)', value: '' },
        Port1Status: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'Port1Status', tag_des: '[PlcConfig]端口1搬运模式(1自动/0手动)', value: '' },
        Port2Status: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'Port2Status', tag_des: '[PlcConfig]端口2搬运模式(1自动/0手动)', value: '' },
        PanelCcd: { client_code: 'LoadPanelCcd', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[板件CCD]条码', value: '' },
        PalletCcd1: { client_code: 'LoadPalletCcd1', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[载具CCD1]条码', value: '' },
        PalletCcd2: { client_code: 'LoadPalletCcd2', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[载具CCD2]条码', value: '' },
        TrayCcd1: { client_code: 'LoadTrayCcd1', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[天盖CCD1]条码', value: '' },
        TrayCcd2: { client_code: 'LoadTrayCcd2', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[天盖CCD2]条码', value: '' },
        AisPort1BackRequest: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'AisPort1BackRequest', tag_des: '[PlcStatus]AIS请求端口1强制退载具', value: '' },
        AisPort2BackRequest: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'AisPort2BackRequest', tag_des: '[PlcStatus]AIS请求端口2强制退载具', value: '' }
      },
      manualOrder: { client_code: 'LoadAis', WebLotNum: 'AisStatus/WebLotNum', WebLotPortCode: 'AisStatus/WebLotPortCode', WebLotRequest: 'AisStatus/WebLotRequest' },
      changeRecipe: { client_code: 'LoadAis', WebChangeRecipeFinish: 'AisStatus/WebChangeRecipeFinish' },
      palletManualScan: { client_code: 'LoadAis', WebPalletNum: 'AisStatus/WebPalletNum', WebPalletConfirmModel: 'AisStatus/WebPalletConfirmModel', WebPalletInfoRequest: 'AisStatus/WebPalletInfoRequest' },
      inspectConfirm: { client_code: 'LoadAis', WebInspectConfirmStatus: 'AisStatus/WebInspectConfirmStatus' },
      panelManualScan: { client_code: 'LoadAis', WebPanelNum: 'AisStatus/WebPanelNum', WebPanelletConfirmModel: 'AisStatus/WebPanelletConfirmModel', WebPanelInfoRequest: 'AisStatus/WebPanelInfoRequest' },
      panelManualConfirm: { client_code: 'LoadAis', WebPanelNum: 'AisStatus/WebPanelNum', WebPanelletConfirmModel: 'AisStatus/WebPanelletConfirmModel', WebPanelInfoRequest: 'AisStatus/WebPanelInfoRequest' }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 100
    }
    this.timer = setInterval(this.messageScroll, 5000)
    this.timer1 = setInterval(this.getStationTaskInfo, 1000)
  },
  created: function() {
    // 获取系统参数信息
    var queryParameter = {
      userName: Cookies.get('userName'),
      cell_id: '0',
      parameter_code: 'AIS_MONITOR_MODE',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.aisMonitorMode = defaultQuery.data[0].parameter_val
            console.log(this.aisMonitorMode)
          }
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    this.getStationData()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    getStationData(type) {
      this.stationData = []
      const query = {
        user_name: Cookies.get('userName'),
        station_attr: 'Load'
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.stationData = JSON.parse(defaultQuery.result)
            if (this.stationData.length > 0) {
              this.currentStation = {
                prod_line_id: this.stationData[0].prod_line_id,
                prod_line_code: this.stationData[0].prod_line_code,
                prod_line_des: this.stationData[0].prod_line_des,
                station_id: this.stationData[0].station_list[0].station_id,
                station_code: this.stationData[0].station_list[0].station_code,
                station_des: this.stationData[0].station_list[0].station_des,
                cell_id: this.stationData[0].station_list[0].cell_id
              }
              this.getCellIp()
              this.getLoginInfo()
              this.getStationTaskInfo()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    messageScroll() {
      if (this.messageList.length > 0) {
        this.messageShow = false
        setTimeout(() => {
          this.messageShow = true
          this.messageList.push(this.messageList[0]) // 将数组的第一个元素追加到数组最后面
          this.messageList.shift() // 然后删除数组的第一个元素
          this.messageContent = this.messageList[0].content
          this.MessageLevel = this.messageList[0].level
        }, 300)
      }
    },
    openStationChoose() {
      this.stationDialogVisible = true
    },
    handleStationChoose(item, item1) {
      this.currentStation = {
        prod_line_id: item.prod_line_id,
        prod_line_code: item.prod_line_code,
        prod_line_des: item.prod_line_des,
        station_id: item1.station_id,
        station_code: item1.station_code,
        station_des: item1.station_des
      }
      this.getCellIp()
      this.getLoginInfo()
      this.getStationTaskInfo()
      this.stationDialogVisible = false
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code = client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter(item => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // 获取当前登录信息
    getLoginInfo() {
      this.loginInfo.user_name = ''
      this.loginInfo.nick_name = ''
      this.loginInfo.dept_id = ''
      this.loginInfo.shift_id = ''
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '获取当前登录信息异常', type: 'error' })
        })
    },
    // 获取工位当前任务计划
    getStationTaskInfo() {
      if (this.currentStation.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      stationTaskSel(query)
        .then(res => {
          this.port1TaskInfo = ''
          this.port2TaskInfo = ''
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              for (var i = 0; i < defaultQuery.data.length; i++) {
                const taskInfo = defaultQuery.data[i]
                var item = {}
                item.lot_num = taskInfo.lot_num
                item.material_code = taskInfo.material_code
                item.face_code = taskInfo.face_code === 0 ? '正面' : '反面'
                item.pdb_rule = taskInfo.pdb_rule === 0 ? '生产板' : 'dummy(陪镀板)'
                item.inspect_count = taskInfo.inspect_count
                item.inspect_finish_count = taskInfo.inspect_finish_count
                item.plan_lot_count = taskInfo.plan_lot_count
                item.finish_count = taskInfo.finish_count
                item.finish_ng_count = taskInfo.finish_ng_count
                if (taskInfo.port_index === '1') {
                  this.port1TaskInfo = item
                } else if (taskInfo.port_index === '2') {
                  this.port2TaskInfo = item
                }
              }
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '获取工位当前任务计划异常', type: 'error' })
        })
    },
    // 打开登录操作
    openUserLogin() {
      this.userId = ''
      this.userDialogVisible = true
      this.$nextTick(x => {
      // 正确写法
        this.$refs.userId.focus()
      })
    },
    // 处理登录
    handleUserLogin() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        user_code: this.userId
      }
      userLogin(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const loginInfo = defaultQuery.data[0]
            this.loginInfo.user_name = loginInfo.user_name
            this.loginInfo.nick_name = loginInfo.nick_name
            this.loginInfo.dept_id = loginInfo.dept_id
            this.loginInfo.shift_id = loginInfo.shift_id
            this.userDialogVisible = false
            this.$message({ message: '登录成功', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '操作异常', type: 'error' })
        })
    },
    // 处理登出
    handleUserLogout() {
      this.$confirm('确定登出当前员工吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.currentStation.station_id
          }
          userLogout(query)
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.loginInfo.user_name = ''
                this.loginInfo.nick_name = ''
                this.loginInfo.dept_id = ''
                this.loginInfo.shift_id = ''
                this.$message({ message: '登出成功', type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              this.$message({ message: '操作异常', type: 'error' })
            })
        })
        .catch(() => {
        })
    },
    // 打开手动操作页面
    openManualPage() {
      this.manualDialogVisible = true
    },
    // 处理手动操作事件
    handleManualEvent(type) {
      var station_code = ''
      if (this.aisMonitorMode === 'AIS-SERVER') {
        station_code = '_' + this.currentStation.station_code
      }
      if (type === 'manual_order') {
        this.manualOrder.client_code += station_code
        this.manualOrder.WebLotNum = this.manualOrder.client_code + '/' + this.manualOrder.WebLotNum
        this.manualOrder.WebLotPortCode = this.manualOrder.client_code + '/' + this.manualOrder.WebLotPortCode
        this.manualOrder.WebLotRequest = this.manualOrder.client_code + '/' + this.manualOrder.WebLotRequest
        this.tagKeyList = this.manualOrder
        this.msgDialogTitle = '工单输入'
        this.manualOrderShow = true
      } else if (type === 'change_recipe') {
        this.changeRecipe.client_code += station_code
        this.changeRecipe.WebChangeRecipeFinish = this.changeRecipe.client_code + '/' + this.changeRecipe.WebChangeRecipeFinish
        this.tagKeyList = this.changeRecipe
        this.msgDialogTitle = '批次需要切换主制程配方,请确认切换完成后点击确定'
        this.changeRecipeShow = true
      } else if (type === 'pallet_manual_scan') {
        this.palletManualScan.client_code += station_code
        this.palletManualScan.WebPalletNum = this.palletManualScan.client_code + '/' + this.palletManualScan.WebPalletNum
        this.palletManualScan.WebPalletConfirmModel = this.palletManualScan.client_code + '/' + this.palletManualScan.WebPalletConfirmModel
        this.palletManualScan.WebPalletInfoRequest = this.palletManualScan.client_code + '/' + this.palletManualScan.WebPalletInfoRequest
        this.tagKeyList = this.palletManualScan
        this.msgDialogTitle = 'Pallet/Tray输入'
        this.palletManualScanShow = true
      } else if (type === 'inspect_confirm') {
        this.inspectConfirm.client_code += station_code
        this.inspectConfirm.WebInspectConfirmStatus = this.inspectConfirm.client_code + '/' + this.inspectConfirm.WebInspectConfirmStatus
        this.tagKeyList = this.inspectConfirm
        this.msgDialogTitle = '首检判定'
        this.inspectConfirmShow = true
      } else if (type === 'panel_manual_scan') {
        this.panelManualScan.client_code += station_code
        this.panelManualScan.WebPanelNum = this.panelManualScan.client_code + '/' + this.panelManualScan.WebPanelNum
        this.panelManualScan.WebPanelletConfirmModel = this.panelManualScan.client_code + '/' + this.panelManualScan.WebPanelletConfirmModel
        this.panelManualScan.WebLotRequest = this.panelManualScan.client_code + '/' + this.panelManualScan.WebPanelInfoRequest
        this.tagKeyList = this.panelManualScan
        this.msgDialogTitle = 'Panel输入'
        this.panelManualScanShow = true
      } else if (type === 'panel_manual_confirm') {
        this.panelManualConfirm.client_code += station_code
        this.panelManualConfirm.WebPanelNum = this.panelManualConfirm.client_code + '/' + this.panelManualConfirm.WebPanelNum
        this.panelManualConfirm.WebPanelletConfirmModel = this.panelManualConfirm.client_code + '/' + this.panelManualConfirm.WebPanelletConfirmModel
        this.panelManualConfirm.WebLotRequest = this.panelManualConfirm.client_code + '/' + this.panelManualConfirm.WebPanelInfoRequest
        this.tagKeyList = this.panelManualConfirm
        this.msgDialogTitle = '混批确认'
        this.panelManualConfirmShow = true
      }
      this.mgsDialogVisible = true
    },
    // 处理端口强制退载具
    handlePortBackRequest(portIndex) {
      this.$confirm('确定对端口' + portIndex.toString() + '进行强制退载具操作吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          var tag_key = ''
          var client_code = ''
          var group_code = ''
          var tag_code = ''
          if (portIndex === 1) {
            client_code = this.monitorData.AisPort1BackRequest.client_code
            group_code = this.monitorData.AisPort1BackRequest.group_code
            tag_code = this.monitorData.AisPort1BackRequest.tag_code
          } else if (portIndex === 2) {
            client_code = this.monitorData.AisPort2BackRequest.client_code
            group_code = this.monitorData.AisPort2BackRequest.group_code
            tag_code = this.monitorData.AisPort2BackRequest.tag_code
          }
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          tag_key = client_code + '/' + group_code + '/' + tag_code
          var sendJson = {}
          var rowJson = []
          var newRow = {
            TagKey: tag_key,
            TagValue: '1'
          }
          rowJson.push(newRow)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + client_code
          this.sendMessage(topic, sendStr)
        })
        .catch(() => {
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }

      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        Object.keys(this.monitorData).forEach(key => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
        })
        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        if (topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0) {
          this.handleMessage(jsonData)
        } else if (topic.indexOf('SCADA_CHANGE/' + this.currentStation.station_code) >= 0) {
          Object.keys(this.monitorData).forEach(key => {
            var client_code = this.monitorData[key].client_code
            var group_code = this.monitorData[key].group_code
            var tag_code = this.monitorData[key].tag_code
            if (this.aisMonitorMode === 'AIS-SERVER') {
              client_code = client_code + '_' + this.currentStation.station_code
            }
            var tag_key = client_code + '/' + group_code + '/' + tag_code
            if (tag_key === jsonData.TagKey) {
              this.monitorData[key].value = jsonData.TagNewValue
            }
          })
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      console.log(topic, msg)
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          this.handleCloseDialog()
          this.$message({ message: '操作成功！', type: 'success' })
        } else {
          this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'load_monitor') return
      this.msgDialogTitle = json.func_des
      this.tagKeyList = json.func_paras
      this.currentFuncCode = json.func_code
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      } else if (json.func_code === 'manual_order') {
        this.manualOrderShow = true
      } else if (json.func_code === 'change_recipe') {
        this.changeRecipeShow = true
      } else if (json.func_code === 'pallet_manual_scan') {
        this.palletManualScanShow = true
      } else if (json.func_code === 'inspect_confirm') {
        this.inspectConfirmShow = true
      } else if (json.func_code === 'panel_manual_scan') {
        this.panelManualScanShow = true
      } else if (json.func_code === 'panel_manual_confirm') {
        this.panelManualConfirmShow = true
      }
      this.mgsDialogVisible = true
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      var msg_level = 'info'
      if (json.msg_level === 'INFO') {
        msg_level = 'info'
      } else if (json.msg_level === 'ERROR') {
        msg_level = 'error'
      } else if (json.msg_level === 'WARN') {
        msg_level = 'warning'
      }
      if (json.func_dlg === 'Y') {
        this.$confirm(json.msg, '提示', {
          confirmButtonText: '确定',
          type: msg_level
        })
          .then(() => {
          })
          .catch(() => {
          })
      } else {
        this.messageList.push({ content: json.msg, level: msg_level })
      }
    },
    // 处理关闭弹窗
    handleCloseDialog() {
      this.manualOrderShow = false
      this.changeRecipeShow = false
      this.palletManualScanShow = false
      this.inspectConfirmShow = false
      this.panelManualScanShow = false
      this.panelManualConfirmShow = false
      this.mgsDialogVisible = false
    }
  }
}
</script>

<style>
#loadMonitor .el-header {
    background-image: linear-gradient(to right, #D0DEFA,#D0DEFA,#9EBAF4);
    color: #333;
    line-height: 45px;
    border-left:5px solid #9EBAF4
}
#loadMonitor .el-main {
    background-color: #fff;
    color: #333;
    padding:5px;
}
#loadMonitor .el-descriptions{
    background-color: #9EBAF4;
}
#loadMonitor .table-descriptions-label {
  width: 150px;
}
#loadMonitor .table-descriptions-content {
  width: 150px;
  color: #333;
  font-weight: 600;
}
#loadMonitor .port-title{
    background-image: linear-gradient(to right, #D0DEFA,#D0DEFA,#9EBAF4);
    color: #333;
    line-height: 45px;
    font-weight:600;
    padding-left:15px;
}
#loadMonitor .port-table-descriptions-label {
  width: 150px;
}
#loadMonitor .port-table-descriptions-content {
  color: #333;
  font-weight: 600;
}
#loadMonitor .message{
  color: #000;
  font-weight:600;
  padding:5px 10px 5px 10px;
  border-radius: 10px;
}
#loadMonitor .message-info{
  background-color: #7AA1EF;
}
#loadMonitor .message-warning{
  background-color: #FBB85A;
}
#loadMonitor .message-error{
  background-color: #F56C6C;
}
</style>
