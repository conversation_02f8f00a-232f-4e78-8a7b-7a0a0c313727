<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="焊接任务号:">
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="项目编号:">
                <el-input v-model="query.project_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="构件号:">
                <el-input v-model="query.structure_no" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="零件类型:">
                <el-select v-model="query.material_type" clearable filterable>
                  <el-option v-for="item in dict.KIT_MATERIAL_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="task_num" label="焊接任务号" />
            <el-table-column :show-overflow-tooltip="true" prop="project_code" width="140" label="项目编号" />
            <el-table-column :show-overflow-tooltip="true" prop="material_code" width="140" label="零件名称" />
            <el-table-column :show-overflow-tooltip="true" prop="structure_no" width="140" label="构件号" />
            <el-table-column :show-overflow-tooltip="true" prop="structure_sn" width="140" label="构件件号" />
            <el-table-column :show-overflow-tooltip="true" prop="material_type" width="140" label="零件类型">
              <template slot-scope="scope">
                <!--LEFT从左到右,RIGHT从右到左-->
                {{ dict.label.KIT_MATERIAL_TYPE[scope.row.material_type] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="m_texture" width="140" label="钢板材质" />
            <el-table-column :show-overflow-tooltip="true" prop="material_length" width="130" label="钢板长度" />
            <el-table-column :show-overflow-tooltip="true" prop="task_from" width="130" label="任务来源">
              <template slot-scope="scope">
                <!--LEFT从左到右,RIGHT从右到左-->
                {{ dict.label.TASK_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_date" width="130" label="零件生产日期" />
            <el-table-column :show-overflow-tooltip="true" prop="kit_flag" width="130" label="是否成套">
              <template slot-scope="scope">
                <!--UP从上到下,DOWN从下到上-->
                {{ scope.row.kit_flag == 'Y' ? '成套' : '不成套' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="use_flag" width="130" label="分拣是否已使用">
              <template slot-scope="scope">
                <!--PLUS增加方式,MINUS减少方式-->
                {{ scope.row.use_flag == 'Y' ? '使用' : '未使用' }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsDailykitTask from '@/api/dcs/project/wms/wmsDailykitTask'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
}
export default {
  name: 'WMS_DAILY_KIT_TASK',
  components: { crudOperation, udOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: 'WMS库区基础信息配置',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'kit_task_id',
      // 排序
      sort: ['kit_task_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsDailykitTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_dailykit_task:add'],
        edit: ['admin', 'b_dcs_wms_dailykit_task:edit'],
        del: ['admin', 'b_dcs_wms_dailykit_task:del']
      }
    }
  },
  dicts: ['TASK_TYPE', 'TASK_WAY', 'KIT_MATERIAL_TYPE', 'TASK_FROM'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>
