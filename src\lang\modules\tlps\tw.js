// TLPS项目的繁体中文翻译

export default {
  // 这里可以添加TLPS项目特定的翻译
  tlps: {
    // 配方管理
    recipe: {
      // 页面标题和通用文本
      maintenance: '配方維護',
      detail: '子配方維護詳情',
      recipeDetail: '配方詳情',
      noRecipeSelected: '未選擇配方或配方詳情加載中...',

      // 表单字段
      name: '配方描述',
      version: '版本號',
      type: '配方類型',
      deviceCode: '設備編碼',
      deviceDes: '設備描述',
      materialCode: '物料編碼',
      materialDes: '物料描述',
      parameterCode: '參數編碼',
      parameterDes: '參數描述',
      parameterVal: '參數值',
      enableFlag: '有效標識',
      valid: '有效',
      invalid: '無效',

      // 操作相关
      scanDownload: '掃碼下載配方',
      modifyParameter: '是否修改參數',
      addParameter: '新增參數',
      confirmDelete: '確認刪除選中的{0}條數據?',
      confirmDistribute: '確認下發該配方嗎?',
      confirmChangeEnableFlag: '確定要將有效標識修改為【{value}】嗎？',
      deleteSuccess: '刪除成功',
      deleteFailed: '刪除失敗',
      modifySuccess: '修改成功',
      modifyFailed: '修改失敗',
      distributeSuccess: '下發成功',

      // 按钮和交互元素
      triggerPoint: '觸發點位',
      selectTriggerPoint: '選擇',
      cancel: '取消',
      confirm: '確認',
      download: '下載',
      upload: '上傳',
      distribute: '下發',
      delete: '刪除',
      downloadSuccess: '配方下載中',
      downloadFailed: '配方下載出現異常',

      // 提示信息
      startMonitoring: '請啟動監控',
      connectionFailed: '連接失敗',
      reconnecting: '連接斷開，正在重連...',
      connectionDisconnected: '服務連接斷開',
      connectionClosed: '服務連接斷開',
      subscribeSuccess: 'MQTT訂閱成功: {0}',
      subscribeFailed: 'MQTT訂閱失敗: {0}',
      unsubscribeSuccess: 'MQTT取消訂閱成功: {0}',
      unsubscribeFailed: 'MQTT取消訂閱失敗: {0}',
      operationException: '操作異常',
      operationFailed: '操作失敗',
      queryException: '查詢異常',
      uploadMesSuccess: '上傳MES成功',
      uploadSuccess: '上傳成功',
      uploadTaskExists: '已存在上傳任務，請等待上傳完成',
      recipeDownloading: '發起Scada請求配方,配方下載中',
      recipeDownloadException: '發起Scada請求配方,配方下載出現異常',

      // 验证规则提示
      selectRecipeType: '請選擇配方類型',
      fillVersion: '請填寫版本號',
      fillRecipeName: '請填寫配方描述',
      fillMaterialCode: '請填寫物料編碼',
      fillMaterialDes: '請填寫物料描述',
      fillDeviceCode: '請填寫設備編碼',
      fillDeviceDes: '請填寫設備描述',
      selectEnableFlag: '請選擇有效標識',
      selectTriggerTag: '請選擇採集項目標籤'
    },

    // 主页面
    index: {
      title: 'TLPS系統',
      recipeManagement: '配方管理',
      deviceStatus: '設備狀態',
      alarmInfo: '報警信息',

      // 操作模式
      offlineMode: '離線模式',
      onlineLocal: '在線/本地',
      onlineRemote: '在線/遠程',

      // 设备状态
      deviceStatusLabel: '設備狀態',
      deviceStatus0: '未知',
      deviceStatus1: '初始化',
      deviceStatus2: '待機',
      deviceStatus3: '報警',
      deviceStatus4: '停止',
      deviceStatus5: '運行',
      deviceStatus6: 'PM',

      // 状态指示
      alarmLight: '告警燈',
      plcHeartbeat: 'PLC心跳',

      // 表单字段
      batchNumber: '批次號',
      materialCode: '料號',
      recipe: '配方',
      selectRecipe: '請選擇配方',
      processingQuantity: '加工數量',
      distributePreview: '下發預覽',

      // 生产信息
      productionInfo: '生產信息',

      // 报警信息
      alarmInfo: '報警信息',
      station: '工位',
      instanceCode: '實例編號',
      instanceDescription: '實例描述',
      alarmCode: '報警代碼',
      alarmLevel: '報警級別',
      alarmDescription: '報警描述',
      alarmTime: '報警時間',
      resetTime: '復位時間',
      isReset: '是否復位',
      resetYes: '已復位',
      resetNo: '待復位',
      isSimulated: '是否模擬',
      yes: '是',
      no: '否',

      // 图表
      capacity: '產能',
      oee: 'OEE',
      readRate: '讀碼率',

      // 表格列
      projectName: '項目名稱',
      currentValue: '當前值',
      unit: '單位',
      upperLimit: '上限',
      lowerLimit: '下限',
      status: '狀態',

      // 参数字段
      parameterCode: '參數編碼',
      parameterDes: '參數描述',
      parameterVal: '參數值',
      enableFlag: '有效標識',
      valid: '有效',
      invalid: '無效',

      // 配方对话框
      modifyRecipe: '修改配方',
      modifyParameter: '是否修改參數',
      cancel: '取消',
      confirmDistribute: '確定下發',

      // CIM消息对话框
      cimMessage: 'CIM消息',
      code: '代碼',
      message: '消息',
      confirm: '確認',

      // 配方确认对话框
      recipeConfirmTitle: '下發配方確認',
      recipeInfo: '配方信息',
      recipeName: '配方名稱',
      stationCode: '工站編碼',
      batchNumber: '批次號',
      materialCode: '料號',
      quantity: '數量',
      employeeInfo: '員工信息',
      recipeDetails: '配方詳情',

      // 验证消息
      pleaseSelectRecipe: '請先選擇配方',
      pleaseEnterBatchNumber: '請輸入批次號',
      pleaseEnterMaterialCode: '請輸入料號',
      deviceMustBeStandby: '設備必須處於待機狀態才能下發配方',
      deviceRunning: '設備運行中，不允許下發配方',
      quantityMustBePositive: '數量必須為大於等於0的整數',
      recipeConfigIncomplete: '配方參數配置不完整，無法下發配方，請聯繫管理員補全scada_tag表的block_addr配置',
      getRecipeDetailFailed: '獲取配方詳情失敗，無法下發配方',
      getRecipeDetailFailedWithMsg: '獲取配方詳情失敗: {msg}',
      recipeBaseInfoNotFound: '未找到選中的配方基本信息',
      missingBlockAddrConfig: '以下參數缺少正確的block_addr配置，無法下發配方，請聯繫工程師補全：',
      noMaterialFound: '當前未獲取到物料，請先去維護物料',
      queryFailed: '查詢失敗',
      queryException: '查詢異常',
      queryExceptionWithMsg: '查詢異常：{msg}',
      duplicateRecipeCode: '發現重複的配方編碼: {codes}，請聯繫管理員修改配方配置',
      duplicateRecipeCodeMark: '重複配方編碼!',
      recipeListRefreshSuccess: '配方列表刷新成功，共{count}條數據',
      noRecipeData: '未找到配方數據',
      refreshRecipeListFailed: '刷新配方列表失敗: {msg}',
      refreshingRecipeList: '正在刷新配方列表...',
      pleaseStartMonitoring: '請啟動監控',
      operationFailed: '操作失敗！',

      // 成功消息
      distributeSuccess: '下發配方成功',
      recipeDistributeSuccess: '下發配方成功',
      connectionSuccess: '連接成功',
      connectionFailed: '連接失敗',
      connectionDisconnected: '連接斷開，正在重連...',

      // 配方詳情對話框
      recipeInfo: '配方信息',
      recipeDetails: '配方詳情',
      recipeName: '配方名稱',
      stationCode: '工站編碼',
      batchNumber: '批次號',
      materialCode: '料號',
      quantity: '數量',
      employeeInfo: '員工信息',
      parameterCode: '參數編碼',
      parameterDescription: '參數描述',
      parameterValue: '參數值',
      enableFlag: '有效標識',
      valid: '有效',
      invalid: '無效',
      noRecipeSelectedOrLoading: '未選擇配方或配方詳情加載中...',
      loadingRecipeDetails: '正在加載配方詳情...',
      cancel: '取消',
      confirmDistribute: '確認下發',

      // 報警信息
      alarmCode: '報警代碼',
      alarmLevel: '報警級別',
      alarmDescription: '報警描述',
      alarmTime: '報警時間',
      resetTime: '復位時間',
      isReset: '是否復位',
      resetted: '已復位',
      waitingReset: '待復位',
      isSimulated: '是否模擬',
      yes: '是',
      no: '否',

      // 生產信息
      itemName: '項目名稱',
      currentValue: '當前值',
      unit: '單位',
      upperLimit: '上限',
      lowerLimit: '下限',
      status: '狀態',

      // CIM消息
      cimMessage: 'CIM消息',
      code: '代碼',
      message: '消息',
      confirm: '確認',

      // 配方下發確認
      recipeDistributeConfirm: '下發配方確認',

      // 設備狀態
      deviceStatusLabel: '設備狀態',
      deviceStatus0: '未知',
      deviceStatus1: '初始化',
      deviceStatus2: '待機',
      deviceStatus3: '報警',
      deviceStatus4: '停止',
      deviceStatus5: '運行',
      deviceStatus6: 'PM',

      // 其他UI元素
      alarmLight: '三色燈',
      plcHeartbeat: 'PLC心跳',
      recipe: '配方',
      selectRecipe: '請選擇配方',
      refreshRecipeList: '刷新配方列表',
      processingQuantity: '加工數量',
      distributePreview: '下發預覽',
      productionInfo: '生產信息',
      alarmInfo: '報警信息',
      station: '工站',
      instanceCode: '實例編碼',
      instanceDescription: '實例描述',
      modifyRecipe: '修改配方',
      modifyParameter: '修改參數',
      parameterDes: '參數描述',
      parameterVal: '參數值',

      // 图表标题
      productionCapacity: '產能',
      readRate: '讀碼率'
    },

    // 确认对话框
    confirm: {
      title: '提示',
      confirmButtonText: '確定',
      cancelButtonText: '取消'
    }
  }
}
