<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <MainChart
          ref="chart"
          v-loading="loading"
          :nodes="nodes"
          :connections="connections"
          :width="'1651'"
          :height="'313'"
          :readonly="false"
          element-loading-text="拼命绘制流程图中"
          @editnode="handleEditNode"
          @ok="handleCutNode"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import MainChart from '@/components/MainAll/index'
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  components: {
    MainChart
  },
  props: {
    task_num: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      fullHeight: 0,
      diameter: 15,
      connections: [],
      thisH: null,
      loading: false,
      nodeForm: {},
      timer: null,
      // MQTT
      // MQTT配置全局变量
      clientMqtt_8083: null,
      clientMqtt_8084: null,
      clientMqtt_8085: null,
      mqttConnStatus_8083: false, // 8083MQTT连接状态(true、false)
      mqttConnStatus_8084: false, // 8084MQTT连接状态(true、false)
      mqttConnStatus_8085: false, // 8085MQTT连接状态(true、false)
      optionsMqtt8083: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userId') +
          '_' +
          Math.random().toString(16).substr(2, 8) + '8083', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt8084: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userId') +
          '_' +
          Math.random().toString(16).substr(2, 8) + '8084', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt8085: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userId') +
          '_' +
          Math.random().toString(16).substr(2, 8) + '8085', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 判断工位是手动还是自动
      stationKeyList: [
        'SlTransGSlPlc/PlcStatusG2/PlcS_Manual-Auto_1', // G2
        'QgTransGPmPlc/PlcStatusG3/PlcS_Manual-Auto_1', // G3
        'QgTransGCutPlc01/PlcStatusG5/PlcS_Manual-Auto_1', // G5
        'QgTransGHcPlc/PlcStatusG4/PlcS_Manual-Auto_1', // G4
        'QgTransGCutPlc01/PlcStatusG6/PlcS_Manual-Auto_1', // G6
        'QgTransGCutPlc02/PlcStatusG7/PlcS_Manual-Auto_1', // G7
        'QgTransGHcPlc/PlcStatusG8/PlcS_Manual-Auto_1', // G8
        'QgRgvPlc01/PlcStatus/PlcS_Manul-Auto', // H1
        'QgTransGCutPlc02/PlcStatusG9/PlcS_Manual-Auto_1', // G9
        'QgTransGCutPlc02/PlcStatusG10/PlcS_Manual-Auto_1', // G10
        'QgTransGHcPlc/PlcStatusG11/PlcS_Manual-Auto_1', // G11
        'FjTransGPlc01/PlcStatusG12/PlcS_Manual-Auto_1', // G12
        'FjTransGPlc01/PlcStatusG13/PlcS_Manual-Auto_1', // G13
        'FjTransGPlc01/PlcStatusG14/PlcS_Manual-Auto_1', // G14
        'FjTransGPlc01/PlcStatusG15/PlcS_Manual-Auto_1', // G15
        'FjTransGPlc02/PlcStatusG16/PlcS_Manual-Auto_1'// G16
      ],
      nodes: [
        {
          type: 'rect',
          x: 10,
          y: 10,
          borderColor: '#519bef',
          bgColor: '#73b6f6',
          width: 20,
          height: 20,
          strokeWidth: 2,
          title: '工位无任务'
        },
        {
          type: 'rect',
          x: 10,
          y: 40,
          borderColor: '',
          bgColor: '#005ee2',
          width: 20,
          height: 20,
          strokeWidth: '',
          title: '工位有任务'
        },
        {
          type: 'icon',
          x: 8,
          y: 70,
          width: 25,
          height: 25,
          title: '工位自动',
          href: require('@/assets/images/dcs/dcs_automatic.png')
        },
        {
          type: 'icon',
          x: 8,
          y: 100,
          width: 25,
          height: 25,
          title: '工位手动',
          href: require('@/assets/images/dcs/dcs_handMove.png')
        },
        {
          type: 'icon',
          x: 8,
          y: 130,
          width: 25,
          height: 25,
          title: '托盘到位',
          href: require('@/assets/images/dcs/dcs_PalletOver.png')
        },
        // {
        //   type:'code',
        //   x:200,
        //   y:100,
        //   borderColor:'#ffffff',
        //   bgColor:'#1fa3e3',
        //   width:80,
        //   height:30,
        //   strokeWidth:3,
        //   title:'G1',
        //   goStationCode:'G2'
        // },
        {
          type: 'code',
          x: 70,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G2',
          goStationCode: 'G3',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 290,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G3',
          goStationCode: 'G4',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 510,
          y: 170,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G5',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 510,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G4',
          goStationCode: 'G5,G6,G7,G8,G9,G10,G11,G12',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 510,
          y: 410,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G6',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 510,
          y: 530,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G7',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 510,
          y: 650,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G8',
          goStationCode: 'G5,G6,G7,G9,G10,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'icon',
          x: 730,
          y: 160,
          width: 170,
          height: 560,
          title: '',
          href: require('@/assets/images/dcs/dcs_aisle.png')
        },
        // {
        //   type:'code',
        //   x:685,
        //   y:100,
        //   borderColor:'#ffffff',
        //   bgColor:'#1fa3e3',
        //   width:80,
        //   height:30,
        //   strokeWidth:3,
        //   title:'G18',
        //   goStationCode:'G1'
        // },
        {
          type: 'code',
          x: 745,
          y: 530,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'H1',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 980,
          y: 180,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G9',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 980,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G13',
          goStationCode: 'G14',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 980,
          y: 410,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G10',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 980,
          y: 530,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G11',
          goStationCode: 'G5,G6,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 980,
          y: 650,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G12',
          goStationCode: 'G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 1200,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G14',
          goStationCode: 'G15',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 1420,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G15',
          goStationCode: 'G16',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        },
        {
          type: 'code',
          x: 1660,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 140,
          height: 60,
          strokeWidth: 3,
          title: 'G16',
          goStationCode: 'G17',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: ''
        }
        // {
        //   type:'code',
        //   x:1290,
        //   y:510,
        //   borderColor:'#ffffff',
        //   bgColor:'#1fa3e3',
        //   width:140,
        //   height:60,
        //   strokeWidth:3,
        //   title:'G17',
        //   goStationCode:'G18'
        // },
      ],
      stationArrFlag: true
    }
  },
  watch: {
    fullHeight: {
      immediate: true,
      handler() {
        this.nodes.map(item => {
          if (item.type === 'code' || (item.type === 'icon' && item.title === '')) {
            item.y = item.y + this.fullHeight
          }
        })
      }
    }
    // task_num:{
    //   immediate: true,
    //   deep: true,
    //   handler() {
    //     this.nodes.map(item=>{
    //       this.getHighlight(item)
    //     })
    //   }
    // }
  },
  mounted() {
    var that = this
    this.getPointData()
    this.timer = setInterval(() => {
      this.getPointData()
    }, 5000)
    window.onresize = () => {
      if (document.documentElement.clientHeight >= 1000) {
        that.fullHeight = 100
      } else {
        that.fullHeight = -100
      }
    }
  },
  created() {
  },
  beforeDestory() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    // 离开此页面时销毁mqtt链接
    if (this.clientMqtt_8083) {
      this.clientMqtt_8083.end()
      this.mqttConnStatus_8083 = false
    }
    if (this.clientMqtt_8084) {
      this.clientMqtt_8084.end()
      this.mqttConnStatus_8084 = false
    }
    if (this.clientMqtt_8085) {
      this.clientMqtt_8085.end()
      this.mqttConnStatus_8085 = false
    }
  },
  methods: {
    getPointData() {
      crudMainPage.stationMinStatus({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.nodes.map(item => {
              const obj = defaultQuery.data.find(e => e.station_code === item.title)
              item = Object.assign(item, obj)
            })
            // 从这里拿到三个单元的端口加IP
            if (this.stationArrFlag) {
              this.stationArrFlag = false
              var uniqueArr = defaultQuery.data.reduce((acc, current) => {
                const x = acc.find(item => item.cell_mqtt_port === current.cell_mqtt_port && item.server_host_1 === current.server_host_1)
                if (!x) {
                  return acc.concat([current])
                } else {
                  return acc
                }
              }, [])
              // 获取scada每个mqtt的点位 的ip + prot ,port一致的，ip也一致
              var result = uniqueArr.map(item => ({ cell_mqtt_port: item.cell_mqtt_port, server_host_1: item.server_host_1 }))
              this.$nextTick(() => {
                for (let i = 0; i < result.length; i++) {
                  this[`toStartWatch_${result[i].cell_mqtt_port}`](result[i].server_host_1, result[i].cell_mqtt_port)
                }
              })
            }
            this.connections = []
          }
        } else {
          this.$message({
            message: defaultQuery.msg || '获取点位异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.$message({
            message: '获取点位异常',
            type: 'error'
          })
        })
    },
    handleCutNode(data) {
      this.$emit('ok', data)
    },
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
    getHighlight(item) {
      if (this.task_num && this.task_num === item.task_num) {
        item.status = 'LOCKSTATUS'
        this.$refs.chart.renderNode(item, false)
      } else {
        item.status = ''
        this.$refs.chart.renderNode(item, false)
      }
    },
    // ----------------------------------【MQTT】----------------------------------
    // 8083启动监控
    toStartWatch_8083(ip, port) {
      var connectUrl = 'ws://' + ip + ':' + port + '/mqtt'
      // this.GetTagValue(result.ip,result.webapi_port)
      // var connectUrl ="ws://*************:8083/mqtt";
      // connectUrl=MQTT_SERVICE;
      console.log('拼接URL：' + connectUrl)
      this.clientMqtt_8083 = mqtt.connect(connectUrl, this.optionsMqtt8083) // 开启连接
      this.clientMqtt_8083.on('connect', (e) => {
        this.mqttConnStatus_8083 = true
        // 8083端口的点位只有一个
        const Arr = [
          // 判断手动自动点位
          'SlTransGSlPlc/PlcStatusG2/PlcS_Manual-Auto_1',
          // 判断托盘是否到位的点位
          'SlTransGSlPlc/PlcStatusG2/PlcS_PalletOver',
          // 托盘号点位
          'SlTransGSlPlc/PlcStatusG2/PlcS_PalletNum'
        ]
        for (let i = 0; i < Arr.length; i++) {
          this.topicSubscribe_8083('SCADA_CHANGE/' + Arr[i])
        }
      })
      // MQTT连接失败
      this.clientMqtt_8083.on('error', (error) => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })

        this.clientMqtt_8083.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt_8083.on('reconnect', (error) => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt_8083.on('disconnect', (error) => {
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      this.clientMqtt_8083.on('close', () => {
        this.clientMqtt_8083.end()
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt_8083.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    // 8084启动监控
    toStartWatch_8084(ip, port) {
      var connectUrl = 'ws://' + ip + ':' + port + '/mqtt'
      // this.GetTagValue(result.ip,result.webapi_port)
      // var connectUrl ="ws://*************:8083/mqtt";
      console.log('拼接URL：' + connectUrl)
      this.clientMqtt_8084 = mqtt.connect(connectUrl, this.optionsMqtt8084) // 开启连接
      this.clientMqtt_8084.on('connect', (e) => {
        this.mqttConnStatus_8084 = true
        const Arr = [
          // 判断手动自动点位
          'QgTransGPmPlc/PlcStatusG3/PlcS_Manual-Auto_1', // G3
          'QgTransGCutPlc01/PlcStatusG5/PlcS_Manual-Auto_1', // G5
          'QgTransGHcPlc/PlcStatusG4/PlcS_Manual-Auto_1', // G4
          'QgTransGCutPlc01/PlcStatusG6/PlcS_Manual-Auto_1', // G6
          'QgTransGCutPlc02/PlcStatusG7/PlcS_Manual-Auto_1', // G7
          'QgTransGHcPlc/PlcStatusG8/PlcS_Manual-Auto_1', // G8
          'QgRgvPlc01/PlcStatus/PlcS_Manul-Auto', // H1
          'QgTransGCutPlc02/PlcStatusG9/PlcS_Manual-Auto_1', // G9
          'QgTransGCutPlc02/PlcStatusG10/PlcS_Manual-Auto_1', // G10
          'QgTransGHcPlc/PlcStatusG11/PlcS_Manual-Auto_1', // G11
          // 判断托盘是否到位的点位
          'QgTransGPmPlc/PlcStatusG3/PlcS_PalletOver', // G3
          'QgTransGHcPlc/PlcStatusG4/PlcS_PalletOver', // G4
          'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletOver', // G5
          'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletOver', // G6
          'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletOver', // G7
          'QgTransGHcPlc/PlcStatusG8/PlcS_PalletOver', // G8
          'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletOver', // G9
          'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletOver', // G10
          'QgTransGHcPlc/PlcStatusG11/PlcS_PalletOver', // G11
          'QgRgvPlc01/PlcStatus/PlcS_PalletOn', // H1
          // 托盘号点位  H1没有托盘号
          'QgTransGPmPlc/PlcStatusG3/PlcS_PalletNum', // G3
          'QgTransGHcPlc/PlcStatusG4/PlcS_PalletNum', // G4
          'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletNum', // G5
          'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletNum', // G6
          'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletNum', // G7
          'QgTransGHcPlc/PlcStatusG8/PlcS_PalletNum', // G8
          'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletNum', // G9
          'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletNum', // G10
          'QgTransGHcPlc/PlcStatusG11/PlcS_PalletNum'// G11
        ]
        for (let i = 0; i < Arr.length; i++) {
          this.topicSubscribe_8084('SCADA_CHANGE/' + Arr[i])
        }
      })
      // MQTT连接失败
      this.clientMqtt_8084.on('error', (error) => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })

        this.clientMqtt_8084.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt_8084.on('reconnect', (error) => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt_8084.on('disconnect', (error) => {
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      this.clientMqtt_8084.on('close', () => {
        this.clientMqtt_8084.end()
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt_8084.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    toStartWatch_8085(ip, port) {
      var connectUrl = 'ws://' + ip + ':' + port + '/mqtt'
      // this.GetTagValue(result.ip,result.webapi_port)
      // var connectUrl ="ws://*************:8083/mqtt";
      console.log('拼接URL：' + connectUrl)
      this.clientMqtt_8085 = mqtt.connect(connectUrl, this.optionsMqtt8085) // 开启连接
      this.clientMqtt_8085.on('connect', (e) => {
        this.mqttConnStatus_8085 = true
        const Arr = [
          // 判断手动自动点位
          'FjTransGPlc01/PlcStatusG12/PlcS_Manual-Auto_1', // G12
          'FjTransGPlc01/PlcStatusG13/PlcS_Manual-Auto_1', // G13
          'FjTransGPlc01/PlcStatusG14/PlcS_Manual-Auto_1', // G14
          'FjTransGPlc01/PlcStatusG15/PlcS_Manual-Auto_1', // G15
          'FjTransGPlc02/PlcStatusG16/PlcS_Manual-Auto_1', // G16
          // 判断托盘是否到位的点位
          'FjTransGPlc01/PlcStatusG12/PlcS_PalletOver', // G12
          'FjTransGPlc01/PlcStatusG13/PlcS_PalletOver', // G13
          'FjTransGPlc01/PlcStatusG14/PlcS_PalletOver', // G14
          'FjTransGPlc01/PlcStatusG15/PlcS_PalletOver', // G15
          'FjTransGPlc02/PlcStatusG16/PlcS_PalletOver', // G16
          // 托盘号点位  H1没有托盘号
          'FjTransGPlc01/PlcStatusG12/PlcS_PalletNum', // G12
          'FjTransGPlc01/PlcStatusG13/PlcS_PalletNum', // G13
          'FjTransGPlc01/PlcStatusG14/PlcS_PalletNum', // G14
          'FjTransGPlc01/PlcStatusG15/PlcS_PalletNum', // G15
          'FjTransGPlc02/PlcStatusG16/PlcS_PalletNum'// G16
        ]
        for (let i = 0; i < Arr.length; i++) {
          this.topicSubscribe_8085('SCADA_CHANGE/' + Arr[i])
        }
      })
      // MQTT连接失败
      this.clientMqtt_8085.on('error', (error) => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })

        this.clientMqtt_8085.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt_8085.on('reconnect', (error) => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt_8085.on('disconnect', (error) => {
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      this.clientMqtt_8085.on('close', () => {
        this.clientMqtt_8085.end()
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt_8085.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue
      const tagToNode = {
        // 判断手动自动
        'SlTransGSlPlc/PlcStatusG2/PlcS_Manual-Auto_1': 'G2',
        'QgTransGPmPlc/PlcStatusG3/PlcS_Manual-Auto_1': 'G3',
        'QgTransGCutPlc01/PlcStatusG5/PlcS_Manual-Auto_1': 'G5',
        'QgTransGHcPlc/PlcStatusG4/PlcS_Manual-Auto_1': 'G4',
        'QgTransGCutPlc01/PlcStatusG6/PlcS_Manual-Auto_1': 'G6',
        'QgTransGCutPlc02/PlcStatusG7/PlcS_Manual-Auto_1': 'G7',
        'QgTransGHcPlc/PlcStatusG8/PlcS_Manual-Auto_1': 'G8',
        'QgRgvPlc01/PlcStatus/PlcS_Manul-Auto': 'H1',
        'QgTransGCutPlc02/PlcStatusG9/PlcS_Manual-Auto_1': 'G9',
        'FjTransGPlc01/PlcStatusG13/PlcS_Manual-Auto_1': 'G13',
        'QgTransGCutPlc02/PlcStatusG10/PlcS_Manual-Auto_1': 'G10',
        'QgTransGHcPlc/PlcStatusG11/PlcS_Manual-Auto_1': 'G11',
        'FjTransGPlc01/PlcStatusG12/PlcS_Manual-Auto_1': 'G12',
        'FjTransGPlc01/PlcStatusG14/PlcS_Manual-Auto_1': 'G14',
        'FjTransGPlc01/PlcStatusG15/PlcS_Manual-Auto_1': 'G15',
        'FjTransGPlc02/PlcStatusG16/PlcS_Manual-Auto_1': 'G16'
      }
      const tagToNodeOver = {
        // 判断托盘是否到位的点位
        'SlTransGSlPlc/PlcStatusG2/PlcS_PalletOver': 'G2',
        'QgTransGPmPlc/PlcStatusG3/PlcS_PalletOver': 'G3',
        'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletOver': 'G5',
        'QgTransGHcPlc/PlcStatusG4/PlcS_PalletOver': 'G4',
        'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletOver': 'G6',
        'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletOver': 'G7',
        'QgTransGHcPlc/PlcStatusG8/PlcS_PalletOver': 'G8',
        'QgRgvPlc01/PlcStatus/PlcS_PalletOn': 'H1',
        'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletOver': 'G9',
        'FjTransGPlc01/PlcStatusG13/PlcS_PalletOver': 'G13',
        'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletOver': 'G10',
        'QgTransGHcPlc/PlcStatusG11/PlcS_PalletOver': 'G11',
        'FjTransGPlc01/PlcStatusG12/PlcS_PalletOver': 'G12',
        'FjTransGPlc01/PlcStatusG14/PlcS_PalletOver': 'G14',
        'FjTransGPlc01/PlcStatusG15/PlcS_PalletOver': 'G15',
        'FjTransGPlc02/PlcStatusG16/PlcS_PalletOver': 'G16'
      }
      const tagToNodeNum = {
        // 托盘号点位  H1没有托盘号
        'SlTransGSlPlc/PlcStatusG2/PlcS_PalletNum': 'G2',
        'QgTransGPmPlc/PlcStatusG3/PlcS_PalletNum': 'G3',
        'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletNum': 'G5',
        'QgTransGHcPlc/PlcStatusG4/PlcS_PalletNum': 'G4',
        'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletNum': 'G6',
        'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletNum': 'G7',
        'QgTransGHcPlc/PlcStatusG8/PlcS_PalletNum': 'G8',
        'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletNum': 'G9',
        'FjTransGPlc01/PlcStatusG13/PlcS_PalletNum': 'G13',
        'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletNum': 'G10',
        'QgTransGHcPlc/PlcStatusG11/PlcS_PalletNum': 'G11',
        'FjTransGPlc01/PlcStatusG12/PlcS_PalletNum': 'G12',
        'FjTransGPlc01/PlcStatusG14/PlcS_PalletNum': 'G14',
        'FjTransGPlc01/PlcStatusG15/PlcS_PalletNum': 'G15',
        'FjTransGPlc02/PlcStatusG16/PlcS_PalletNum': 'G16'
      }
      for (const key in tagToNode) {
        if (TagKey === key) {
          const nodeTitle = tagToNode[key]
          this.nodes.find(e => e.title === nodeTitle).manual_Auto_Flag = TagNewValue || ''
          break
        }
      }
      for (const key in tagToNodeOver) {
        if (TagKey === key) {
          const nodeTitle = tagToNodeOver[key]
          this.nodes.find(e => e.title === nodeTitle).pallet_Flag = TagNewValue || ''
          break
        }
      }
      for (const key in tagToNodeNum) {
        if (TagKey === key) {
          const nodeTitle = tagToNodeOver[key]
          this.nodes.find(e => e.title === nodeTitle).pallet_Num = TagNewValue || ''
          break
        }
      }
    },
    // 8083端口订阅主题函数
    topicSubscribe_8083(topic) {
      if (!this.mqttConnStatus_8083) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt_8083.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus_8083) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 8084端口订阅主题函数
    topicSubscribe_8084(topic) {
      if (!this.mqttConnStatus_8084) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt_8084.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus_8084) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 8084端口订阅主题函数
    topicSubscribe_8085(topic) {
      if (!this.mqttConnStatus_8085) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt_8085.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus_8085) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>

<style lange="less" scoped>
.app-container{
   padding: 0 !important;
   width: 100%;
   height: 100%;
}
</style>
