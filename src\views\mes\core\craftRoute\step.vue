<template>
  <div>
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工步编号/内容：">
                <el-input v-model="query.step" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="工步编号" prop="step_num">
            <el-input v-model="form.step_num" />
          </el-form-item>
          <el-form-item label="工步内容" prop="step_content">
            <el-input v-model="form.step_content" />
          </el-form-item>
          <el-form-item label="工步属性" prop="step_attr">
            <fastCode fastcode_group_code="STEP_ATTR" :fastcode_code.sync="form.step_attr" control_type="select" size="mini" />
          </el-form-item>
          <el-form-item label="技术要求" prop="step_tech_require">
            <el-input v-model="form.step_tech_require" />
          </el-form-item>
          <el-form-item label="物料要求" prop="step_material_require">
            <el-input v-model="form.step_material_require" />
          </el-form-item>
          <el-form-item label="工具要求" prop="step_tool_require">
            <el-input v-model="form.step_tool_require" />
          </el-form-item>
          <el-form-item label="步骤图标" prop="step_ico">
            <el-input v-model="form.step_ico" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-input v-model="form.enable_flag" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="工步ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_id }}</el-descriptions-item>
                  <el-descriptions-item label="工步编号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_num }}</el-descriptions-item>
                  <el-descriptions-item label="工步内容" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_content }}</el-descriptions-item>
                  <el-descriptions-item label="工步属性" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_attr }}</el-descriptions-item>
                  <el-descriptions-item label="技术要求" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_tech_require }}</el-descriptions-item>
                  <el-descriptions-item label="物料要求" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_material_require }}</el-descriptions-item>
                  <el-descriptions-item label="工具要求" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_tool_require }}</el-descriptions-item>
                  <el-descriptions-item label="步骤图标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_ico }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="step_num" min-width="100" label="工步编号" />
            <el-table-column  :show-overflow-tooltip="true" prop="step_content" min-width="100" label="工步内容" />
            <el-table-column  :show-overflow-tooltip="true" prop="step_attr" min-width="100" label="工步属性" />
            <el-table-column  :show-overflow-tooltip="true" prop="step_tech_require" min-width="100" label="技术要求" />
            <el-table-column  :show-overflow-tooltip="true" prop="step_material_require" min-width="100" label="物料要求" />
            <el-table-column  :show-overflow-tooltip="true" prop="step_tool_require" min-width="100" label="工具要求" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudrecipeCrPdureStep from '@/api/mes/core/recipeCrPdureStep'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  step_id: '',
  proceduce_id: '',
  step_num: '',
  step_content: '',
  step_attr: '',
  step_tech_require: '',
  step_material_require: '',
  step_tool_require: '',
  step_ico: '',
  enable_flag: 'Y'
}
export default {
  name: 'MES_RECIPE_CR_PDURE_STEP',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '工步信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'step_id',
      // 排序
      sort: ['step_id asc'],
      // CRUD Method
      crudMethod: { ...crudrecipeCrPdureStep },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    proceduce_id: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_cr_pdure_step:add'],
        edit: ['admin', 'mes_recipe_cr_pdure_step:edit'],
        del: ['admin', 'mes_recipe_cr_pdure_step:del'],
        down: ['admin', 'mes_recipe_cr_pdure_step:down']
      },
      rules: {
        step_num: [{ required: true, message: '请输入工步编号', trigger: 'blur' }],
        step_content: [{ required: true, message: '请输入工步内容', trigger: 'blur' }]
      }
    }
  },
  watch: {
    proceduce_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.proceduce_id = this.proceduce_id
        defaultForm.proceduce_id = this.proceduce_id
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.proceduce_id = this.proceduce_id
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将工步为【' + data.step_content + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudrecipeCrPdureStep
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              step_id: data.step_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
