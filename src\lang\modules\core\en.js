// English translations for the core module

export default {
  // Add core module specific translations here
  core: {
    // SECS/AIS Recipe Management
    secsais: {
      // Page titles and general text
      maintenance: 'SECS/AIS Recipe Management',
      detail: 'Recipe Details',
      noRecipeSelected: 'No recipe selected or recipe details loading...',
      clientList: 'Instance List',
      modelList: 'Recipe List',

      // Form fields
      clientDes: 'Client Description',
      tagGroupCode: 'Tag Group Code',
      modelName: 'Model Name',
      tagGroupId: 'Tag Group ID',
      tagCodePrefix: 'Tag Code Prefix',
      clientId: 'Client ID',
      clientCode: 'Client Code',
      parameterCode: 'Parameter Code',
      parameterDes: 'Parameter Description',
      parameterVal: 'Parameter Value',
      enableFlag: 'Enable Flag',
      valid: 'Valid',
      invalid: 'Invalid',

      // Operations
      modifyParameter: 'Modify Parameters',
      addParameter: 'Add Parameter',
      confirmDelete: 'Confirm deletion of {0} selected items?',
      confirmDistribute: 'Confirm distribution of this recipe?',
      confirmChangeEnableFlag: 'Are you sure you want to change the enable flag to [{value}]?',
      deleteSuccess: 'Delete successful',
      deleteFailed: 'Delete failed',
      modifySuccess: 'Modify successful',
      modifyFailed: 'Modify failed',
      emptyValueSet: 'Empty value has been set',
      distributeSuccess: 'Distribution successful',
      distributeFailed: 'Distribution failed',
      mqttConnectionFailed: 'MQTT connection failed',
      mqttNotConnected: 'Please connect to MQTT service first',
      mqttConnectSuccess: 'MQTT connection successful',
      mqttUpdateTimeout: 'MQTT update timeout, please check connection',
      serviceCell: 'Unable to get cell server information',
      getCellIPFailed: 'Failed to get cell IP',

      // Parameter related
      parameterCode: 'Parameter Code',
      parameterDes: 'Parameter Description',
      parameterVal: 'Current Value',
      parameterUnit: 'Unit',
      parameterLimit: 'Limits',
      parameterInput: 'Input Value',
      modifyParameter: 'Modify Parameters',
      noRecipeSelected: 'Please select a recipe',
      fetchRecipeDataFailed: 'Failed to fetch recipe data',
      valueTooLow: 'Input value is below the lower limit',
      valueTooHigh: 'Input value is above the upper limit',

      // Search and prompts
      search: 'Search',
      reset: 'Reset',
      confirm: 'Confirm',
      cancel: 'Cancel',
      prompt: 'Prompt',
      required: 'Required',
      fetchModelDataFailed: 'Failed to fetch recipe model data',
      fetchRecipeDataFailed: 'Failed to fetch recipe details',
      maintenance: 'SECS/AIS Recipe Maintenance',
      confirmDelete: 'Confirm delete {0} items?',

      // Status and operation prompts
      refreshInstanceList: 'Refresh Instance List',
      refreshingInstanceList: 'Refreshing instance list...',
      online: 'Online',
      offline: 'Offline',
      stationCodeMissing: 'Station code missing, unable to query recipe model data',
      stationCodeMissingMqtt: 'Station code missing, unable to connect to MQTT',
      cancelDelete: 'Delete cancelled',
      unknownMqttFormat: 'Unknown MQTT message format',

      // Recipe parameter related
      recipeList: 'Recipe List',
      recipeDetails: 'Recipe Details',
      modelList: 'Recipe List',
      modifyParameter: 'Modify Parameter',
      parameterCode: 'Parameter Code',
      parameterDes: 'Parameter Description',
      parameterVal: 'Parameter Value',
      parameterInput: 'Input Value',
      parameterUnit: 'Unit',
      parameterLimit: 'Parameter Range',
      noRecipeSelected: 'Please select a recipe',
      valueTooLow: 'Input value below lower limit',
      valueTooHigh: 'Input value above upper limit',
      modifySuccess: 'Modified successfully',
      modifyFailed: 'Modification failed',

      // MQTT related
      mqttNotConnected: 'MQTT not connected',
      mqttConnectSuccess: 'MQTT connected successfully',
      mqttConnectionFailed: 'MQTT connection failed',
      serviceCell: 'Failed to get service cell information',
      getCellIPFailed: 'Failed to get cell IP'
    }
  }
}
