<template>
  <div>
    <el-collapse v-model="activeNames" :disabled="true">
      <el-collapse-item title="最新日志信息" name="1">
        <div>{{ LogInfo }}</div>
      </el-collapse-item>
    </el-collapse>
    <div style="text-align: center; margin-top: 10px; margin-bottom: 10px">
      <el-button type="primary" size="small" icon="el-icon-refresh" @click="handleRetryStep">重试</el-button>
      <el-button type="primary" size="small" icon="el-icon-top-right" @click="handleRetryOtherStep">跳转</el-button>
      <el-button type="primary" size="small" icon="el-icon-close" @click="handleAbort">取消</el-button>
      <el-button type="primary" size="small" icon="el-icon-top-right" @click="handleJump">跳过</el-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeNames: ['1'],
      LogInfo: ''
    }
  },
  methods: {
    handleRetryStep() {},
    handleRetryOtherStep() {},
    handleAbort() {},
    handleJump() {}
  }
}
</script>
