import request from '@/utils/request'

// 查询自动化逻辑属性
export function selLogicFunc(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncSel',
    method: 'post',
    data
  })
}
// 新增自动化逻辑属性
export function insLogicFunc(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncIns',
    method: 'post',
    data
  })
}
// 修改自动化逻辑属性
export function updLogicFunc(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncUpd',
    method: 'post',
    data
  })
}
// 删除自动化逻辑属性
export function delLogicFunc(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncDel',
    method: 'post',
    data
  })
}

// 逻辑程序查询--树形结构
export function logicFuncTree(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncTree',
    method: 'post',
    data
  })
}
export default { selLogicFunc, insLogicFunc, updLogicFunc, delLogicFunc, logicFuncTree }
