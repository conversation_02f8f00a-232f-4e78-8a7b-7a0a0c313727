// TLWX project English translation

export default {
  // TLWX project specific translations
  tlwx: {
    // Control modes
    manualMode: 'Manual Mode',
    autoMode: 'Auto Mode',
    selectMode: 'Select Mode',
    
    // Start status
    autoStart: 'Auto Start',
    systemStop: 'System Stop',
    
    // Heater status
    heaterRun: 'Heater Run',
    heaterStop: 'Heater Stop',
    
    // Other possible translations
    controlMode: 'Control Mode',
    startStatus: 'Start Status',
    heaterStatus: 'Heater Status',
    
    // Prompt messages
    pleaseStartMonitor: 'Please start monitoring first'
  }
}
