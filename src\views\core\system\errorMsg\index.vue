<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorType')">
                <!-- 报错类型： -->
                <el-select v-model="query.type" clearable>
                  <el-option v-for="item in dict.ERROR_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorModule')">
                <!-- 所属模块： -->
                <el-input v-model="query.module" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorFunctionCode')">
                <!-- 功能编码 -->
                <el-input v-model="query.function_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorChinese')">
                <!-- 中文异常： -->
                <el-input v-model="query.chinese" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorEnglish')">
                <!-- 英文异常： -->
                <el-input v-model="query.english" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorOther')">
                <!-- 其他语言异常： -->
                <el-input v-model="query.other_languages" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorKeyword')">
                <!-- 关键字： -->
                <el-input v-model="query.keyword" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            slot="reference"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="batchDelete()"
          >
            {{ $t('lang_pack.commonPage.remove') }}
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="150px" :inline="true">
          <!-- 新增、修改弹窗 -->
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorType')" prop="type">
            <!-- 报错类型： -->
            <el-select v-model="form.type" clearable>
              <el-option v-for="item in dict.ERROR_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorModule')" prop="module">
            <!-- 所属模块 -->
            <el-input v-model="form.module" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorFunctionCode')" prop="function_code">
            <!-- 功能编码 -->
            <el-input v-model="form.function_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorChinese')" prop="chinese">
            <!-- 中文异常 -->
            <el-input v-model="form.chinese" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorEnglish')" prop="english">
            <!-- 英文异常 -->
            <el-input v-model="form.english" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorThai')" prop="thai">
            <!-- 泰文异常 -->
            <el-input v-model="form.thai" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorOther')" prop="other_languages">
            <!-- 其他语言异常 -->
            <el-input v-model="form.other_languages" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceErrorMsg.errorKeyword')" prop="keyword">
            <!-- 关键字段 -->
            <el-input v-model="form.keyword" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="id" label="id" />
            <el-table-column :show-overflow-tooltip="true" prop="type" :label="$t('lang_pack.maintenanceErrorMsg.errorType')">
              <template #default="{row}">
                {{ dict.label.ERROR_TYPE[row.type] }}
              </template>
            </el-table-column>
            <!-- 报错类型 -->
            <el-table-column :show-overflow-tooltip="true" prop="module" :label="$t('lang_pack.maintenanceErrorMsg.errorModule')" />
            <!-- 所属模块 -->
            <el-table-column :show-overflow-tooltip="true" prop="function_code" :label="$t('lang_pack.maintenanceErrorMsg.errorFunctionCode')" />
            <!-- 功能编码 -->
            <el-table-column :show-overflow-tooltip="true" prop="chinese" :label="$t('lang_pack.maintenanceErrorMsg.errorChinese')" />
            <!-- 中文异常 -->
            <el-table-column :show-overflow-tooltip="true" prop="english" :label="$t('lang_pack.maintenanceErrorMsg.errorEnglish')" />
            <!-- 英文异常 -->
            <el-table-column :show-overflow-tooltip="true" prop="thai" :label="$t('lang_pack.maintenanceErrorMsg.errorThai')" />
            <!-- 泰文异常 -->
            <el-table-column :show-overflow-tooltip="true" prop="other_languages" :label="$t('lang_pack.maintenanceErrorMsg.errorOther')" />
            <!-- 其他语言异常 -->
            <el-table-column :show-overflow-tooltip="true" prop="keyword" :label="$t('lang_pack.maintenanceErrorMsg.errorKeyword')" />
            <!-- 关键字段 -->
            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <operation :data="scope.row" :permission="permission" :disabled-dle="false" @ok="doDelete" />
                <!-- <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" /> -->
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudError from '@/api/core/system/sysErrorMsg'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import operation from './operation.vue'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
// import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  id: '',
  type: '',
  module: '',
  function_code: '',
  chinese: '',
  english: '',
  thai: '',
  other_languages: '',
  keyword: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_ERROR_MSG',
  components: { crudOperation, rrOperation, operation, pagination },
  cruds() {
    return CRUD({
      title: '异常消息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['id asc'],
      // CRUD Method
      crudMethod: { ...crudError },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        // del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    // 自定义验证
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'error:add'],
        edit: ['admin', 'error:edit'],
        del: ['admin', 'error:del'],
        down: ['admin', 'error:down']
      },
      rules: {
        type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        module: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        function_code: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        chinese: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        english: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        other_languages: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      pop: false

    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'ERROR_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 300
    }
  },
  created: function() {
  },
  methods: {
    batchDelete() {
      this.$confirm(`确认删除选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const ids = this.crud.selections.map(item => item[this.crud.idField]).join(',')
          const function_codes = this.crud.selections.map(item => item.function_code).join(',')
          const query = {
            ids,
            function_codes,
            user_name: Cookies.get('userName')
          }
          this.delete(query)
        }).catch(() => {})
    },
    doDelete(data) {
      const query = {
        id: data.id,
        function_codes: data.function_code,
        user_name: Cookies.get('userName')
      }
      this.delete(query)
    },
    delete(data) {
      crudError.del(data).then(res => {
        if (res.code === 0) {
          this.crud.toQuery()
        }
      }).catch((e) => {
        this.$message({
          message: '删除失败',
          type: 'error'
        })
      })
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.type = typeof crud.form.type === 'number' ? crud.form.type + '' : crud.form.type
      return true
    }
  }
}
</script>
