<template>
  <div id="loadMonitor">
    <el-container>
      <el-header style="background-color: #d2dae7">
        <div class="statuHead">
          <div>
            <el-popover placement="right" width="100" trigger="click">
              <el-button
                size="medium"
                type="primary"
                :class="monitorData.SysModel.value === '1' ? 'btnone' : 'btnone0'"
                @click="handleSysModelRoleCheck('1')"
              >Local</el-button><br>
              <el-button
                size="medium"
                type="primary"
                :class="monitorData.SysModel.value === '2' ? 'btnone' : 'btnone0'"
                style="margin: 10px 0"
                @click="handleSysModelRoleCheck('2')"
              >Semi-Auto</el-button><br>
              <el-button
                size="medium"
                type="primary"
                :class="monitorData.SysModel.value === '3' ? 'btnone' : 'btnone0'"
                @click="handleSysModelRoleCheck('3')"
              >Auto</el-button>
              <el-button slot="reference" class="playBtn">
                {{ monitorData.SysModel.value === '3' ? 'Auto' : (monitorData.SysModel.value === '2' ? 'Semi-Auto':'Local') }}
              </el-button>
            </el-popover>
            <el-button
              class="btntwo playBtn"
              type="primary"
              @click="sendEapPingTest"
            >{{ $t("lang_pack.hmiMain.eapPing") }}</el-button>
            <el-button
              type="primary"
              class="btntwo playBtn"
              @click="handleManualEvent('manual_lot_scan')"
            >{{ $t('lang_pack.hmiMain.manualBatch') }}</el-button>
            <el-button
              type="primary"
              class="btntwo playBtn"
              @click="handleManualEvent('manual_order')"
            >{{ $t('lang_pack.hmiMain.localTask') }}</el-button>
          </div>
          <div>
            <div class="wrappstyle">
              <p>
                <span
                  :class="
                    controlStatus.plc_status === '1'
                      ? 'wholeline wholelinenormal'
                      : controlStatus.plc_status === '2'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">PLC</span>
              </p>
              <p>
                <span
                  :class="monitorData.EapOnlineStatus.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelineerror'"
                />
                <span class="statuText">EAP</span>
              </p>
              <p>
                <span
                  :class="monitorData.UpOnlineStatus.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelineerror'"
                />
                <span class="statuText">{{
                  $t("lang_pack.hmiMain.upStatus")
                }}</span>
              </p>
              <p>
                <span
                  :class="monitorData.DownOnlineStatus.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelineerror'"
                />
                <span class="statuText">{{
                  $t("lang_pack.hmiMain.DownStatus")
                }}</span>
              </p>
            </div>
          </div>
        </div>
      </el-header>
      <el-main>
        <div class="peopleInfo">
          <div class="peopleDetail">
            <div class="peopleMsg" style="margin-left: 20px">
              {{ $t("lang_pack.vie.employeeID") }}
              <div class="dataInfo">{{ loginInfo.user_name }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.vie.name") }}
              <div class="dataInfo">{{ loginInfo.nick_name }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t('lang_pack.vie.department') }}
              <div class="dataInfo">{{ loginInfo.dept_id }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.vie.classes") }}
              <div class="dataInfo">{{ loginInfo.shift_id }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.hmiMain.status") }}
              <div class="dataInfo">
                <span
                  :class="monitorData.DeviceStatus.value === '1' ? 'deviceRun' :
                    monitorData.DeviceStatus.value === '2' ? 'deviceStop' :
                    monitorData.DeviceStatus.value === '3' ? 'deviceIdle' :
                    monitorData.DeviceStatus.value === '4' ? 'deviceDown' :
                    monitorData.DeviceStatus.value === '5' ? 'devicePm' :'deviceInit'"
                  class="normal"
                >{{
                  monitorData.DeviceStatus.value === '1' ? $t('lang_pack.hmiMain.deviceRun') :
                  monitorData.DeviceStatus.value === '2' ? $t('lang_pack.hmiMain.deviceStop') :
                  monitorData.DeviceStatus.value === '3' ? $t('lang_pack.hmiMain.deviceIdle') :
                  monitorData.DeviceStatus.value === '4' ? $t('lang_pack.hmiMain.deviceDown') :
                  monitorData.DeviceStatus.value === '5' ? $t('lang_pack.hmiMain.devicePm') :
                  $t('lang_pack.hmiMain.deviceInit') }}</span>
              </div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.hmiMain.carrierStatus") }}
              <div class="dataInfo">{{ monitorData.EapCarryStatus.value }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.hmiMain.processQuantity") }}
              <div class="dataInfo">{{ monitorData.InSideCount.value }}</div>
            </div>
          </div>
          <div>
            <el-button type="primary" @click="openUserLogin">{{
              $t("lang_pack.vie.login")
            }}</el-button>
            <el-button type="danger" @click="handleUserLogout">{{
              $t("lang_pack.vie.logOut")
            }}</el-button>
          </div>
        </div>
        <el-card shadow="always" style="margin-top: 10px">
          <el-row :gutter="20">
            <el-col :span="18">
              <div class="boardTasks">
                <div class="info">
                  <el-descriptions class="margin-top" :column="2" border>
                    <el-descriptions-item>
                      <template slot="label">
                        {{ $t("lang_pack.hmiMain.importTask") }}
                      </template>
                      {{ InPanelInfo.lot_num }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        {{ $t("lang_pack.hmiMain.complete") }}
                      </template>
                      {{ InPanelInfo.finish_plan }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        {{ $t("lang_pack.hmiMain.plateCode") }}
                      </template>
                      {{ InPanelInfo.panel_barcode }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div class="info">
                  <el-descriptions class="margin-top" :column="2" border>
                    <el-descriptions-item>
                      <template slot="label">
                        {{ $t("lang_pack.hmiMain.productionTask") }}
                      </template>
                      {{ OutPanelInfo.lot_num }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        {{ $t("lang_pack.hmiMain.complete") }}
                      </template>
                      {{ OutPanelInfo.finish_plan }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">
                        {{ $t("lang_pack.hmiMain.plateCode") }}
                      </template>
                      {{ OutPanelInfo.panel_barcode }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div class="wraptable">
                <el-table
                  :data="tableData"
                  border
                  style="width: 100%"
                  :height="height"
                >
                  <!-- 操作 -->
                  <el-table-column
                    prop="task_from"
                    :label="$t('lang_pack.commonPage.operate')"
                  >
                    <template slot-scope="scope">
                      <el-button size="small" type="danger" plain :disabled="scope.row.can_abort_flag === 'N'" @click="handleFinal(scope.row)">{{ $t('view.enum.boardErrorCode.forcedFinal') }}</el-button>
                    </template>
                  </el-table-column>
                  <!-- 批次号 -->
                  <el-table-column
                    prop="lot_num"
                    :label="$t('lang_pack.taskTable.batchNumber')"
                  />
                  <!-- 物料号 -->
                  <el-table-column
                    prop="material_code"
                    :label="$t('view.form.materialNo')"
                  />、
                  <!-- 载具码 -->
                  <el-table-column
                    prop="pallet_num"
                    :label="$t('lang_pack.hmiMain.vehicleCode')"
                  />
                  <!-- 计划数量 -->
                  <el-table-column
                    prop="plan_lot_count"
                    :label="$t('lang_pack.hmiMain.plannedQuantity')"
                  />
                  <!-- 任务状态 -->
                  <el-table-column
                    prop="task_status"
                    :label="$t('lang_pack.taskTable.taskStatus')"
                  >
                    <template slot-scope="scope">
                      <el-tag
                        :type="(scope.row.task_status == '生产中' ? '' : (scope.row.task_status == '正常完板' ? 'success' : 'danger'))"
                        class="elTag"
                      >
                        {{ scope.row.task_status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <!-- 入口数量 -->
                  <el-table-column
                    prop="in_count"
                    :label="$t('lang_pack.hmiMain.entranNum')"
                  />
                  <!-- 出口数量 -->
                  <el-table-column
                    prop="out_count"
                    :label="$t('lang_pack.hmiMain.exportNum')"
                  />
                  <!-- 生产时间 -->
                  <el-table-column
                    prop="task_start_time"
                    :label="$t('lang_pack.hmiMain.productionTime')"
                  />
                  <!-- <el-table-column prop="unit_count" :label="$t('lang_pack.commonPage.operate')">
                    <template slot-scope="scope">
                      <el-button size="small" type="text">{{ $t('lang_pack.commonPage.remove') }}</el-button>
                      <el-button size="small" type="text">{{ '强制结批' }}</el-button>
                    </template>
                  </el-table-column> -->
                </el-table>
              </div>
              <div
                id="containerDemo"
                style="width: 100%; height: calc(100vh - 590px); margin: 15px 0"
              />
              <el-table
                :data="warningData"
                border
                style="width: 100%"
                height="85"
              >
                <!-- 发生时间 -->
                <el-table-column
                  prop="item_date"
                  :label="$t('lang_pack.hmiMain.occurrenTime')"
                />
                <!-- 报警级别 -->
                <el-table-column
                  prop="level"
                  :label="$t('lang_pack.hmiMain.alarmLevel')"
                />
                <!-- 报警消息 -->
                <el-table-column
                  prop="level_msg"
                  :label="$t('lang_pack.hmiMain.alarmMsg')"
                />
              </el-table>
            </el-col>
            <el-col :span="6">
              <div style="display: flex">
                <div
                  id="oeeDemo"
                  style="
                    width: 50%;
                    height: calc(100vh - 650px);
                    margin: 15px 0;
                  "
                />
                <div class="schedule">
                  <span>Availability</span>
                  <el-progress
                    :text-inside="true"
                    :stroke-width="26"
                    :percentage="70"
                  />
                  <span>Performance</span>
                  <el-progress
                    :text-inside="true"
                    :stroke-width="24"
                    :percentage="100"
                    status="success"
                  />
                  <span>Quality</span>
                  <el-progress
                    :text-inside="true"
                    :stroke-width="22"
                    :percentage="80"
                    status="warning"
                  />
                </div>
              </div>
              <el-table
                :data="oeeData"
                border
                style="width: 100%"
                :height="scaheight"
              >
                <!-- 参数名称 -->
                <el-table-column
                  prop="params"
                  :label="$t('lang_pack.hmiMain.parameterName')"
                />
                <!-- 单元 -->
                <el-table-column
                  prop="unit"
                  :label="$t('lang_pack.hmiMain.company')"
                />
                <!-- 设定值 -->
                <el-table-column
                  prop="setValue"
                  :label="$t('lang_pack.hmiMain.setValue')"
                />
                <!-- 实际值 -->
                <el-table-column
                  prop="actualValue"
                  :label="$t('lang_pack.hmiMain.actualValue')"
                />
              </el-table>
            </el-col>
          </el-row>
        </el-card>
      </el-main>
    </el-container>
    <el-dialog :title="$t('lang_pack.vie.employeeLogin')" :visible.sync="userDialogVisible" width="650px" top="65px" class="elDialog dialog_hmi">
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.employee') }}
      <el-input ref="userId" v-model="userId" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.name') }}
      <el-input ref="nickName" v-model="nickName" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.department') }}
      <el-input ref="deptId" v-model="deptId" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.vie.department') }}
      <el-input ref="shiftId" v-model="shiftId" clearable size="mini" style="width:100%" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">{{ $t('lang_pack.vie.cancel') }}</el-button>
        <el-button type="primary" @click="handleUserLogin">{{ $t('lang_pack.SignOn') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.hmiMain.CIMMessage')" width="50%" top="20px" :visible.sync="dialogCIMMsgVisible" :close-on-click-modal="false" class="elDialog">
      <table class="table">
        <tr>
          <td class="label" style="width:100px;">{{ $t('lang_pack.messageReport.screen_code') + '：' }}</td>
          <td class="content">{{ screen_code }}</td>
        </tr>
        <tr>
          <td class="label">{{ $t('lang_pack.messageReport.cim_msg') + '：' }}</td>
          <td class="content">{{ cim_msg }}</td>
        </tr>
        <tr v-if="ConfirmBtnVisible">
          <td colspan="2" style="text-align:center;">
            <el-button
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-check"
              @click="handleConfirmCIMMsg"
            >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          </td>
        </tr>
      </table>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="warningMsgDialogVisible" :close-on-click-modal="false" class="elDialog">
      <span>{{ tagValueSecond + $t('lang_pack.hmiMain.inSeconds') }}</span>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="jsonMsgFlag" :close-on-click-modal="false" class="elDialog">
      <span>{{ jsonMsg }}</span>
    </el-dialog>
    <el-dialog
      :title="$t('lang_pack.hmiMain.manualBatch')"
      :visible.sync="mgsDialogVisible"
      :modal-append-to-body="false"
      width="800px"
      top="65px"
      :close-on-click-modal="false"
      class="elDialog dialog_hmi"
      @close="handleCloseDialog"
    >
      <manualLotList
        v-if="manualLotListShow"
        ref="manualLotList"
        @sendMessage="sendMessageByUpEap"
      />
      <manualOrder
        v-if="manualOrderShow"
        ref="manualOrder"
        @sendMessage="sendMessageBySaveLot"
      />
    </el-dialog>

    <roleCheck
      v-if="roleCheckShow"
      ref="roleCheck"
      class="userinfo_hmi"
      :role_user_id="userId"
      :role_func_code="role_func_code"
      @roleCheck="roleCheck"
    />
  </div>
</template>
<script>

import { selCellIP } from '@/api/core/center/cell'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import { selLoginInfo, userLogin, userLogout } from '@/api/eap/project/dy/eapDyCjMeStationUser'
import { carrierIDReport, eapPing, cimModeChangeReport, eapApsPlanCancel, eapApsJudgeIsCanAbort,eapManualPlanSave } from '@/api/eap/project/dy/eapDyCjApsPlan'
import { eapCimMsgShow } from '@/api/eap/eapApsPlan'
import { taskPassRate } from '@/api/pack/task'
import { eapEquipTask, EapApsPlanCurrentSel } from '@/api/eap/eapEquipTask'
import Cookies from 'js-cookie'
import roleCheck from '@/views/core/hmi/roleCheck'
import manualLotList from '@/views/eap/project/dy/hmi/cjMonitor/manualLotList'
import manualOrder from '@/views/eap/project/dy/hmi/cjMonitor/manualOrder'

import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'EAP_MASTER_MONITOR_HMI',
  components: {
    roleCheck, manualLotList,manualOrder
  },
  data() {
    return {
      controlStatus: {
        plc_status: '0'
      },
      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      height: document.documentElement.clientHeight - 565,
      scaheight: document.documentElement.clientHeight - 360,
      userDialogVisible: false,
      manualLotListShow: false,
      manualOrderShow: false,
      userId: '',
      nickName: '',
      deptId: '',
      shiftId: '',
      role_func_code: '',
      oeeData: [
        { params: '参数1', unit: '', setValue: 12, actualValue: 15 },
        { params: '参数2', unit: '', setValue: 22, actualValue: 6 },
        { params: '参数3', unit: '', setValue: 13, actualValue: 10 },
        { params: '参数4', unit: '', setValue: 18, actualValue: 16 },
        { params: '参数5', unit: '', setValue: 17, actualValue: 11 },
        { params: '参数6', unit: '', setValue: 15, actualValue: 8 },
        { params: '参数7', unit: '', setValue: 11, actualValue: 9 },
        { params: '参数8', unit: '', setValue: 2, actualValue: 12 }
      ],
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      monitorData: {
        SysModel: { client_code: 'MasterPlc', group_code: 'PlcContrl', tag_code: 'SysModel', tag_des: '生产模式(0:OffLine离线模式,1:Local模式,2:Semi-Auto模式,3:Auto模式)', value: '' },
        DeviceStatus: { client_code: 'MasterPlc', group_code: 'PlcStatus', tag_code: 'DeviceStatus', tag_des: '设备状态(1运行、2停止 、3待料、4异常、5保养)', value: '' },
        InSideCount: { client_code: 'MasterPlc', group_code: 'PlcStatus', tag_code: 'InSideCount', tag_des: '设备内板数量', value: '' },
        EapCarryStatus: { client_code: 'MasterEap', group_code: 'EapStatus', tag_code: 'CarryStatus1', tag_des: '载具状态', value: '' },
        EapOnlineStatus: { client_code: 'MasterEap', group_code: 'EapStatus', tag_code: 'EapOnlineStatus', tag_des: 'EAP在线状态:0离线;1在线', value: '' },
        UpOnlineStatus: { client_code: 'MasterEap', group_code: 'EapStatus', tag_code: 'UpOnlineStatus', tag_des: '上游在线状态:0离线;1在线', value: '' },
        DownOnlineStatus: { client_code: 'MasterEap', group_code: 'EapStatus', tag_code: 'DownOnlineStatus', tag_des: '下游在线状态:0离线;1在线', value: '' },
        EapApiTimeOutSeconds: { client_code: 'MasterAis', group_code: 'AisConfig', tag_code: 'EapApiTimeOutSeconds', tag_des: '调用EAP的接口超时时间', value: '' },
        InPanelInfo: { client_code: 'MasterAis', group_code: 'AisStatus', tag_code: 'InPanelInfo', tag_des: '[AisStatus]入料信息', value: '' },
        OutPanelInfo: { client_code: 'MasterAis', group_code: 'AisStatus', tag_code: 'OutPanelInfo', tag_des: '[AisStatus]出料信息', value: '' }
      },
      portTaskInfo: {},
      warningData: [
        {
          item_date: '2024-05-06 20:30:45',
          level: '重大',
          level_msg: '服务器故障'
        }
      ],
      moTableData: [],
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      roleCheckShow: false,
      // CIM消息相关
      queryCim: true,
      dialogCIMMsgVisible: false,
      ConfirmBtnVisible: false,
      screen_code: '',
      cim_msg: '',
      aisMonitorMode: 'AIS-PC',
      dyVersion: '3',
      mgsDialogVisible: false,
      tagValueSecond: 0,
      tagValueTimer: null,
      warningMsgDialogVisible: false,
      delayCount: 0,
      jsonMsgFlag: false,
      jsonMsg: '',
      msgDialogTitle: this.$t('lang_pack.Prompt'),
      indicatorr: null,
      oeeicatorr: null,
      timer: null,
      timer1: null,
      select_sys_model: '',
      tagKeyList: {},
      currentFuncCode: '',
      InPanelInfo: {},
      OutPanelInfo: {},
      tableData: []
    }
  },
  mounted() {
    var that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 565
      that.scaheight = document.documentElement.clientHeight - 360
    }
    window.addEventListener('resize', function() {
      that.indicatorr.resize()
    })
    this.getOrderCharts()
    this.getOeeCharts()
    this.getEapWorkTask()

    this.timer = setInterval(this.messageScroll, 5000)
    this.timer1 = setInterval(() => {
    //  this.getOrderCharts()
      this.getEapWorkTask() // 获取工单信息
    }, 1000 * 5)
  },
  created: function() {
    this.getStationData()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.timer1) {
      clearInterval(this.timer1)
    }
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    // 获取基础工位信息
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        cell_id: this.$route.query.cell_id
      }
      this.getCellIp()
      this.getLoginInfo()
    },
    // 获取基础单元信息
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    // 打开登录操作
    openUserLogin() {
      this.userId = ''
      this.userDialogVisible = true
      this.$nextTick(x => {
        // 正确写法
        this.$refs.userId.focus()
      })
    },
    // 处理登录
    handleUserLogin() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        user_code: this.userId,
        dept_id: this.deptId,
        shift_id: this.shiftId,
        nick_name: this.nickName,
        check_out_flag: 'N'
      }
      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      userLogin(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const loginInfo = defaultQuery.data[0]
            this.loginInfo.user_name = loginInfo.user_name
            this.loginInfo.nick_name = loginInfo.nick_name
            this.loginInfo.dept_id = loginInfo.dept_id
            this.loginInfo.shift_id = loginInfo.shift_id
            this.userDialogVisible = false
            this.$message({ message: this.$t('lang_pack.vie.loginSuccess'), type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
          if (defaultQuery.code === -1) {
            this.tagValueSecond = Number(this.monitorData['EapApiTimeOutSeconds'].value)
            if (this.tagValueSecond > 0) {
              this.tagValueTimer && clearInterval(this.tagValueTimer)
              this.warningMsgDialogVisible = true
              this.timerTemp = setInterval(() => {
                this.tagValueSecond--
                if (this.tagValueSecond === 0) {
                  this.warningMsgDialogVisible = false
                  clearInterval(this.timerTemp)
                  this.tagValueTimer = null
                }
              }, 1000)
              return
            }
          }
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理登出
    handleUserLogout() {
      this.$confirm(this.$t('lang_pack.vie.AreYouSureToLogOut'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          debugger
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.currentStation.station_id,
            user_code: this.loginInfo.user_name,
            dept_id: '',
            shift_id: '',
            nick_name: '',
            check_out_flag: 'Y'
          }

          // 倒计时功能
          this.delayCount = 45
          const ths = this
          const loading = this.$loading({
            lock: true,
            text: '数据处理中（' + this.delayCount + '）',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.timer3 = setInterval(function() {
            ths.delayCount--
            loading.text = '数据处理中（' + ths.delayCount + '）'
            if (ths.delayCount === 0) {
              loading.close()
              clearInterval(this.timer3)
            }
          }, 1000)

          userLogout(query)
            .then(res => {
              // 清除计时器
              this.delayCount = 0
              loading.close()
              clearInterval(this.timer3)

              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.loginInfo.user_name = ''
                this.loginInfo.nick_name = ''
                this.loginInfo.dept_id = ''
                this.loginInfo.shift_id = ''
                this.$message({ message: this.$t('lang_pack.hmiMain.logoutSuccess'), type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              // 清除计时器
              this.delayCount = 0
              loading.close()
              clearInterval(this.timer3)
              this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
            })
        })
        .catch(() => {
        })
    },
    // 获取当前登录信息
    getLoginInfo() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            } else {
              this.loginInfo.user_name = '---'
              this.loginInfo.nick_name = '---'
              this.loginInfo.dept_id = '---'
              this.loginInfo.shift_id = '---'
            }
          } else {
            this.loginInfo.user_name = '---'
            this.loginInfo.nick_name = '---'
            this.loginInfo.dept_id = '---'
            this.loginInfo.shift_id = '---'
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.$message({ message: this.$t('lang_pack.hmiMain.procedure'), type: 'error' })
        })
    },
    // 切换模式Pre判断
    handleSysModelSwitchPre() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        station_code: this.currentStation.station_code,
        station_attr: 'Master',
        model_value: this.select_sys_model
      }
      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)
      // 请求是否允许切换
      cimModeChangeReport(query)
        .then(res => {
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // 允许操作
            this.handleSysModelSwitch()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理本地远程状态切换
    handleSysModelSwitch() {
      var tag_key = ''
      var client_code = this.monitorData.SysModel.client_code
      var group_code = this.monitorData.SysModel.group_code
      var tag_code = this.monitorData.SysModel.tag_code
      var tag_value = this.select_sys_model
      if (this.aisMonitorMode === 'AIS-SERVER') {
        client_code = client_code + '_' + this.currentStation.station_code
      }
      tag_key = client_code + '/' + group_code + '/' + tag_code
      // 再写入
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + client_code
      this.sendMessage(topic, sendStr)
    },
    getEapWorkTask() {
      const query = {
        station_id: this.currentStation.station_id
      }
      EapApsPlanCurrentSel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.tableData = defaultQuery.data
          return
        }
        this.tableData = []
        this.$message({
          message: defaultQuery.msg,
          type: 'error'
        })
      }).catch(err => {
        this.tableData = []
        this.$message({ message: err.msg, type: 'error' })
      })
    },
    handleFinal(data) {
      this.$confirm(this.$t('lang_pack.vie.determine') + this.$t('view.enum.boardErrorCode.forcedFinal') + '？', this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      }).then(() => {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: 'MasterAis/AisStatus/WorkGroupId',
          TagValue: data.group_lot_num
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/MasterAis'
        this.sendMessage(topic, sendStr)
      })
    },
    // 切模式权限验证
    handleSysModelRoleCheck(sys_model) {
      this.select_sys_model = sys_model
      if (sys_model === this.monitorData.SysModel.value) {
        return
      }
      if (sys_model !== '3') {
        const query = {
          user_name: Cookies.get('userName'),
          station_id: this.currentStation.station_id
        }
        eapApsJudgeIsCanAbort(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              // 允许操作
              this.role_func_code = 'handleSysModel'
              this.roleCheckShow = true
            } else {
              this.$message({ message: defaultQuery.msg, type: 'error' })
            }
          })
          .catch(() => {
            this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
          })
      } else {
        this.handleSysModelSwitchPre()
      }
    },
    // 获取24小时统计信息
    getOrderCharts() {
      this.indicatorr = this.$echarts.init(
        document.getElementById('containerDemo')
      )
      taskPassRate({})
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const result = defaultQuery.data
            const maxCount = Math.max.apply(
              null,
              result.map(function(obj) {
                // 找出最大值
                return Math.max(obj.OK, obj.NG)
              })
            )
            var option = {
              grid: {
                left: '0%',
                right: '0%',
                bottom: '0%',
                containLabel: true
              },
              title: {
                left: 'center',
                // left: '0%',
                top: '3%',
                // 文字颜色
                textStyle: {
                  color: '#000',
                  fontSize: 16
                }
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'cross',
                  crossStyle: {
                    color: '#999'
                  }
                }
              },
              legend: {
                data: [
                  this.$t('lang_pack.vie.qualifiedQuantity'),
                  this.$t('lang_pack.vie.unqualifiedQuantity'),
                  this.$t('lang_pack.vie.passRate')
                ]
              },
              xAxis: [
                {
                  type: 'category',
                  data: result.map((e) => {
                    return e.hour
                  })
                }
              ],
              yAxis: [
                {
                  type: 'value',
                  name: this.$t('lang_pack.vie.quantity'),
                  min: 0,
                  max: maxCount > 5 ? maxCount : 5,
                  interval: 50,
                  axisLabel: {
                    formatter: '{value} '
                  }
                },
                {
                  type: 'value',
                  name: this.$t('lang_pack.vie.passRate'),
                  min: 0,
                  max: 100,
                  interval: 10,
                  axisLabel: {
                    formatter: '{value} %'
                  }
                }
              ],
              series: [
                {
                  name: this.$t('lang_pack.vie.qualifiedQuantity'),
                  type: 'bar',
                  color: 'lightgreen',
                  tooltip: {
                    valueFormatter: function(value) {
                      return value
                    }
                  },
                  data: result.map((e) => {
                    return e.OK
                  })
                },
                {
                  name: this.$t('lang_pack.vie.unqualifiedQuantity'),
                  type: 'bar',
                  color: 'red',
                  tooltip: {
                    valueFormatter: function(value) {
                      return value
                    }
                  },
                  data: result.map((e) => {
                    return e.NG
                  })
                },
                {
                  name: this.$t('lang_pack.vie.passRate'),
                  type: 'line',
                  yAxisIndex: 1,
                  tooltip: {
                    valueFormatter: function(value) {
                      return value + ' %'
                    }
                  },
                  data: result.map((e) => {
                    return e.qualificationRate
                  })
                }
              ]
            }
            if (option && typeof option === 'object') {
              this.indicatorr.setOption(option)
            }
          } else {
            this.indicatorr.setOption(this.indicaOption)
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.indicatorr.setOption(this.indicaOption)
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    // 获取OEE数据
    getOeeCharts() {
      this.oeeicatorr = this.$echarts.init(document.getElementById('oeeDemo'))
      const handred = 100
      const point = 81.3
      const option = {
        title: {
          text: point + '%',
          x: 'center',
          y: 'center',
          textStyle: {
            fontWeight: 'normal',
            color: '#29EEF3',
            fontSize: '20'
          }
        },

        series: [
          {
            name: 'circle',
            type: 'pie',
            startAngle: 0,
            clockWise: true,
            center: ['50%', '50%'],
            radius: ['70%', '80%'],
            itemStyle: {
              normal: {
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            data: [
              {
                value: point,
                name: '占比',
                itemStyle: {
                  normal: {
                    color: {
                      // 颜色渐变
                      colorStops: [
                        {
                          offset: 0,
                          color: '#4FADFD' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#28E8FA' // 100% 处的颜色1
                        }
                      ]
                    },
                    label: {
                      show: false
                    },
                    labelLine: {
                      show: false
                    }
                  }
                }
              },
              {
                name: '剩余',
                value: handred - point,
                itemStyle: {
                  normal: {
                    color: '#f7f7f7'
                  }
                }
              }
            ]
          },
          {
            name: 'circle',
            type: 'pie',
            clockWise: true,
            startAngle: 0,
            center: ['50%', '50%'],
            radius: ['55%', '70%'],
            itemStyle: {
              normal: {
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            data: [
              {
                value: point,
                name: '占比',
                itemStyle: {
                  normal: {
                    color: '#e1eae1',
                    label: {
                      show: false
                    },
                    labelLine: {
                      show: false
                    }
                  }
                }
              },
              {
                name: '剩余',
                value: handred - point,
                itemStyle: {
                  normal: {
                    color: '#fff'
                  }
                }
              }
            ]
          }
        ]
      }
      if (option && typeof option === 'object') {
        this.oeeicatorr.setOption(option)
      }
    },
    // 获取工单生产信息
    getEapEquipTask() {
      const query = {}
      eapEquipTask(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.result !== '') {
              var response = JSON.parse(defaultQuery.result)
              this.portTaskInfo = response.currentTaskInfo
              this.moTableData = response.taskList
            } else {
              this.portTaskInfo = {}
            }
          }
        })
        .catch((e) => {
          this.portTaskInfo = {}
        })
    },
    // 测试EAP通讯
    sendEapPingTest() {
      eapPing({})
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({
              message: this.$t('lang_pack.commonPage.pingSuccessful'),
              type: 'success'
            })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    // CIM消息处理功能
    handleConfirmCIMMsg() {
      this.queryCim = true
      this.dialogCIMMsgVisible = false
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          if (this.showMsg) {
            this.handleCloseDialog()
            this.$message({
              message: this.$t('lang_pack.commonPage.operationSuccessful'),
              type: 'success'
            })
          }
          // 执行完成后都复位为true
          this.showMsg = true
        } else {
          // 执行完成后都复位为true
          this.showMsg = true
          this.$message({
            message: this.$t('lang_pack.commonPage.operationfailure'),
            type: 'error'
          })
        }
      })
    },
    // 定制:人工扫描任务并提交到EAP验证
    sendMessageByUpEap(palletNum, lotList) {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        pallet_num: palletNum,
        lot_list: JSON.stringify(lotList)
      }

      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      carrierIDReport(query)
        .then((res) => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.handleCloseDialog()
            this.$message({
              message: this.$t('lang_pack.commonPage.operationSuccessful'),
              type: 'success'
            })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    //定制:人工填写任务
    sendMessageBySaveLot(lot_num,lot_short_num,material_code,pallet_num,plan_lot_count,panel_length,panel_width) {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        lot_num:lot_num,
        lot_short_num:lot_short_num,
        material_code:material_code,
        pallet_num:pallet_num,
        plan_lot_count:plan_lot_count,
        panel_length:panel_length,
        panel_width: panel_width
      }
      eapManualPlanSave(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.handleCloseDialog()
            this.$message({
              message: this.$t('lang_pack.commonPage.operationSuccessful'),
              type: 'success'
            })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'master_monitor') return
      this.msgDialogTitle = json.func_des
      this.tagKeyList = json.func_paras
      this.currentFuncCode = json.func_code
      // 若是员工登入
      if (json.func_code === 'user_login_in') {
        if (!this.userDialogVisible) {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.openUserLogin()
        }
        return
      }
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      } else {
        this.mgsDialogVisible = true
      }
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      var msg_level = 'info'
      if (json.msg_level === 'INFO') {
        msg_level = 'info'
      } else if (json.msg_level === 'ERROR') {
        msg_level = 'error'
      } else if (json.msg_level === 'WARN') {
        msg_level = 'warning'
      }
      if (json.func_dlg === 'Y') {
        this.jsonMsgFlag = true
        this.jsonMsg = json.msg
      } else {
        this.$message({
          message: json.msg,
          type: msg_level,
          duration: json.dlg_second * 1000
        })
      }
    },
    // 处理CIM消息
    messageScroll() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      if (this.queryCim) {
        eapCimMsgShow(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.count !== 0) {
                var msgInfo = defaultQuery.data[0]
                this.screen_code = msgInfo.screen_code
                this.cim_msg = msgInfo.cim_msg
                var interval_second_time = msgInfo.interval_second_time
                if (msgInfo.screen_control === '0') {
                  this.ConfirmBtnVisible = false
                  var _time = setTimeout(() => {
                    this.queryCim = true
                    this.dialogCIMMsgVisible = false
                    clearTimeout(_time)
                  }, interval_second_time * 1000)
                } else {
                  this.ConfirmBtnVisible = true
                }
                this.queryCim = false
                this.dialogCIMMsgVisible = true
              }
            }
          })
          .catch(() => {
          })
      }
      this.getLoginInfo()
    },
    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      this.roleCheckShow = false
      if (roleFuncCode === 'handleSysModel') {
        if (status === 'OK') {
          this.handleSysModelSwitchPre()
        }
      }
    },
    // 手工处理弹窗
    handleManualEvent(type) {
      this.mgsDialogVisible=true
      if (type === 'manual_lot_scan') {
        this.manualLotListShow = true
      } else if(type === 'manual_order'){
        this.manualOrderShow = true
      }
    },
    // 关闭手工弹窗
    handleCloseDialog() {
      this.mgsDialogVisible=false
      this.manualLotListShow = false
      this.manualOrderShow = false
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        // 注册监控CCD照片
        this.topicSubscribe('SCADA_CHANGE/Ccd/CcdStatus/PanelPic')
        // 监控通讯状态
        var plcClientCode = 'MasterPlc'
        if (this.aisMonitorMode === 'AIS-SERVER') {
          plcClientCode =
            plcClientCode + '_' + this.currentStation.station_code
        }
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        // 其他标准点位监控
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          this.topicSubscribe(
            'SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code
          )
        })
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })
      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return

          if (
            topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0
          ) {
            this.handleMessage(jsonData)
          } else if (topic.indexOf('SCADA_BEAT/') >= 0) {
            // 心跳
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf('SCADA_BEAT/MasterPlc') >= 0) {
              if (this.controlStatus.plc_status !== '2') {
                this.controlStatus.plc_status = heartBeatValue
              }
            }
          } else if (topic.indexOf('SCADA_STATUS/') >= 0) {
            // 通讯结果状态
            var statusValue = jsonData.Status
            if (topic.indexOf('SCADA_STATUS/MasterPlc') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.plc_status = '2'
                this.$message({
                  message: this.$t('lang_pack.hmiMain.PLCCommunication'),
                  type: 'error'
                })
              } else {
                if (this.controlStatus.plc_status === '2') {
                  this.controlStatus.plc_status = '1'
                }
              }
            }
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              if (this.aisMonitorMode === 'AIS-SERVER') {
                client_code =
                  client_code + '_' + this.currentStation.station_code
              }
              var tag_key = client_code + '/' + group_code + '/' + tag_code
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue
              }
              if (tag_key === 'MasterAis/AisStatus/InPanelInfo') {
                this.InPanelInfo = jsonData.TagNewValue && JSON.parse(jsonData.TagNewValue)
              }
              if (tag_key === 'MasterAis/AisStatus/OutPanelInfo') {
                this.OutPanelInfo = jsonData.TagNewValue && JSON.parse(jsonData.TagNewValue)
              }
            })
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code =
                    client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter((item) => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? '' : item[0].tag_value
                  if (tag_key === 'MasterAis/AisStatus/InPanelInfo') {
                    this.InPanelInfo = item.length && JSON.parse(item[0].tag_value)
                  }
                  if (tag_key === 'MasterAis/AisStatus/OutPanelInfo') {
                    this.OutPanelInfo = item.length && JSON.parse(item[0].tag_value)
                  }
                }
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException') + '：' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>
<style scoped lang="less">
#loadMonitor {
  .el-main {
    background-color: #fff;
    color: #333;
    padding: 5px;
    overflow: hidden;
  }
  .el-header {
    .playBtn {
      border-radius: 0.25rem;
      font-size: 24px;
      border: none;
    }
    .playStart {
      background-color: #0cd80c;
    }

    .playEnd {
      background-color: #bfbfbf;
    }
    .statuHead {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 5px;
      .wrappstyle {
        display: flex;
        align-items: center;

        p {
          margin: 0 16px !important;
          display: flex;
          flex-direction: column;
          align-items: center;

          span {
            font-size: 12px;
            font-weight: 700;
          }

          .statuText {
            line-height: 30px;
            height: 30px;
          }
        }

        p:last-child {
          margin-right: 0 !important;
        }

        .el-divider--vertical {
          width: 2px;
          height: 2em;
        }
      }
      .btntwo {
        background: #5a6671;
        border: 1px solid #5a6671;
      }
      .wholeline {
        width: 20px;
        height: 20px;
        margin-top: 5px;
        border-radius: 50%;
      }

      .wholelinenormal {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #0d0;
        box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        background-image: radial-gradient(
          0.3em 0.25em at 50% 25%,
          rgb(255, 255, 255) 25%,
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          0.25em 0.25em at 30% 75%,
          rgba(255, 255, 255, 0.5),
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          0.3em 0.3em at 60% 80%,
          rgba(255, 255, 255, 0.5),
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          100% 100%,
          rgba(255, 255, 255, 0) 30%,
          rgba(255, 255, 255, 0.3) 40%,
          rgba(0, 0, 0, 0.5) 50%
        );
      }

      .wholelineerror {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #f00;
        box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        background-image: radial-gradient(
          0.3em 0.25em at 50% 25%,
          rgb(255, 255, 255) 25%,
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          0.25em 0.25em at 30% 75%,
          rgba(255, 255, 255, 0.5),
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          0.3em 0.3em at 60% 80%,
          rgba(255, 255, 255, 0.5),
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          100% 100%,
          rgba(255, 255, 255, 0) 30%,
          rgba(255, 255, 255, 0.3) 40%,
          rgba(0, 0, 0, 0.5) 50%
        );
      }

      .wholelinegray {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #606266;
        box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        background-image: radial-gradient(
          0.3em 0.25em at 50% 25%,
          rgb(255, 255, 255) 25%,
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          0.25em 0.25em at 30% 75%,
          rgba(255, 255, 255, 0.5),
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          0.3em 0.3em at 60% 80%,
          rgba(255, 255, 255, 0.5),
          rgba(255, 255, 255, 0)
        ),
        radial-gradient(
          100% 100%,
          rgba(255, 255, 255, 0) 30%,
          rgba(255, 255, 255, 0.3) 40%,
          rgba(0, 0, 0, 0.5) 50%
        );
      }

      .wholeline1 {
        width: 20px;
        height: 20px;
        margin-top: 5px;
      }

      .wholelinenormal1,
      .deviceGreen {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #0d0;
        box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
      }

      .deviceRed {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #f00;
        box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
      }

      .wholelineerror1,
      .deviceYellow {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #eeff00;
        box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
      }

      .wholelinegray1 {
        -webkit-animation: heart 1s linear infinite;
        animation: heart 1s linear infinite;
        background-color: #606266;
        box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
        //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
      }
    }
  }
  .peopleInfo {
    margin-bottom: 5px;
    box-shadow: 0 2px 10px #d1d1d1;
    border-radius: 0;
    background: #fff;
    padding: 10px 5px;
    display: flex;
    justify-content: space-between;

    .peopleDetail {
      display: flex;

      .peopleMsg {
        display: flex;
        align-items: center;
        margin-left: 20px;

        .dataInfo {
          min-width: 100px;
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #d2dae7;
          text-align: center;
          margin-left: 10px;
        }
      }
    }

    ul {
      padding: 0;
      margin: 0;
      display: flex;
      justify-content: space-around;

      .longString {
        width: 1px;
        height: 26px;
        background-color: #d8e4fd;
        margin-top: 5px;
      }

      .lastLi {
        ::after {
          content: "";
          position: absolute;
          right: 0;
          top: 10px;
          width: 0;
          height: 0;
          background-color: #9a9a9a;
        }
      }

      .lastLi {
        display: flex;
        flex-direction: inherit;
        align-items: center;
        width: 15%;
      }

      list-style: none;
    }
  }
  .boardTasks {
    display: flex;
    justify-content: space-between;
    .info {
      width: 49.5%;
    }
  }
  .schedule {
    width: 50%;
    .el-progress {
      margin: 10px 0;
    }
  }
  ::v-deep .el-descriptions--small.is-bordered .el-descriptions-item__cell {
    width: 25%;
  }
  ::v-deep .el-table {
    margin-top: 5px;
    border: 1px solid #9596a5 !important;
  }
  ::v-deep .el-table th {
    color: #fff;
    font-size: 16px;
    font-weight: normal;
    background-color: #4777db !important;
    outline: none;
    height: 35px !important;
    padding: 0;
    border-bottom: 1px solid #9596a5 !important;
  }
}
.btnone {
  background: #50d475;
  border-color: #50d475;
}

.btnone0 {
  background: #959595;
  border-color: #e8efff;
  color: #ffffff;
}

.deviceInit {
  background: #070707 !important;
  color: #fff;
}
.deviceRun {
  background: #81ff42 !important;
}
.deviceStop {
  background: #ff42dcbc !important;
}
.deviceIdle {
  background: #f2ff42 !important;
}
.deviceDown {
  background: #ff4242 !important;
}
.devicePm {
  background: #ffd042 !important;
}

</style>
