import request from '@/utils/request'

// 查询WMS调度任务表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarTaskSelect',
    method: 'post',
    data
  })
}
// 新增WMS调度任务表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarTaskInsert',
    method: 'post',
    data
  })
}
// 修改WMS调度任务表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarTaskUpdate',
    method: 'post',
    data
  })
}
// 删除WMS调度任务表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsLockCarTaskDelete',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

