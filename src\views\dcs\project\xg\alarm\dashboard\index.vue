<template>
  <el-container id="origin">
    <el-header height="90px">
      <el-row align="middle">
        <el-col :span="8">
          <div id="dcs_alarm_dashboard_header_time"><img src="@/assets/images/time.png">
            <span>{{ header.time }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div id="dcs_alarm_dashboard_header_title">{{ header.title }}</div>
        </el-col>
        <el-col :span="8">
          <div id="dcs_alarm_dashboard_header_week">{{ header.week }}</div>
        </el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20" class="dcs-alarm-dashboard-main-hint">
        <div class="green-light" /><span class="light-explain">正常</span>
        <div class="red-light" /><span class="light-explain">报警</span>
      </el-row>
      <div
        v-for="(alarmData, i) in data"
        :key="i"
        class="dcs-alarm-dashboard-main-content"
      >
        <div v-for="(item, j) in alarmData" :key="j" class="dcs-alarm-dashboard-main-content-item">
          <el-card shadow="hover" class="box-card">
            <div class="clearfix box-card-title">
              <span>{{ item.clientName }}</span>
            </div>
            <div class="clearfix box-card-content">
              <div class="box-card-content-is-alarm">
                <div class="box-card-content-is-alarm-label marquee">是否报警</div>
                <div :class="item.faulted ? 'red-light' : 'green-light'" />
              </div>
              <div class="box-card-content-alarm-content">
                <div class="marquee">报警内容</div>
                <div class="marquee">
                  <div :class="{'marquee-content': item.faultContent !== '无' }">{{ item.faultContent }}</div>
                </div>
              </div>
              <el-button type="info" round @click="showDetail(item)">查看历史报警信息</el-button>
            </div>
          </el-card>
        </div>
      </div>
    </el-main>
    <el-dialog :title="dialog.title" :visible.sync="dialog.visible" width="80%" :before-close="() => { dialog.visible = false }">
      <detail ref="detail" />
    </el-dialog>
  </el-container>
</template>
<script>
import Paho from 'paho-mqtt'
import autofit from 'autofit.js'
import alarmApi from '@/api/dcs/project/xg/alarm'
import alarmHistoryApi from '@/api/dcs/project/xg/alarmHistory'
// import scadaClientApi from '@/api/core/scada/client/index'
import { CoreScadaReadTag } from '@/api/hmi/mainIndex'
import detail from './detail'

export default {
  name: 'DCS_ALARM_DASHBOARD',
  components: { detail },
  data() {
    return {
      timer: null,
      header: {
        title: '智能下料产线设备报警信息看板',
        time: '',
        week: ''
      },
      dialog: {
        title: '',
        visible: false
      },
      data: [],
      clients: {},
      clientPorts: {},
      tagKeysMap: {},
      triggerMonitors: {},
      codeMonitors: {},
      alarmCodes: {}
    }
  },
  created() {
    fetch('/static/data/alarm_dashboard_data.json?v=' + new Date().getTime()) // 从 public/static/data 目录下请求 JSON 文件
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        return response.json() // 解析 JSON 数据
      })
      .then(data => {
        this.data = data // 将解析后的数据赋值给组件的属性
        this.init()
      })
      .catch(error => {
        console.error('There has been a problem with your fetch operation:', error)
      })
    this.timer = setInterval(() => {
      this.refreshHeader()
    }, 1000)
  },
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#origin',
      resize: true
    }, false) // 可关闭控制台运行提示输出
  },
  beforeDestroy() {
    autofit.off()
    if (this.timer) {
      clearInterval(this.timer)
    }
    for (const key in this.clients) {
      this.clients[key] && this.clients[key].disconnect()
    }
  },
  methods: {
    // 刷新头部信息
    refreshHeader() {
      const now = new Date()
      // 当前年月日时分秒
      const yy = now.getFullYear()
      const mm = (now.getMonth() + 1) < 10 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1
      const dd = now.getDate() < 10 ? '0' + now.getDate() : now.getDate()
      const hh = now.getHours()
      const mf = now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes()
      const ss = now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds()
      this.header.time = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
      // 当前星期
      const wk = now.getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.header.week = weeks[wk]
    },
    init() {
      this.loadAlarmCodes()
      this.data.forEach((group) => {
        group.forEach((item) => {
          const triggerTopic = this.getTopic(item.tags.trigger)
          const codeTopic = this.getTopic(item.tags.code)
          const mqttPort = item.ports.mqtt
          const cellPort = item.ports.cell
          this.triggerMonitors[triggerTopic] = item

          this.codeMonitors[codeTopic] = item
          if (this.clientPorts[mqttPort] == null) {
            this.clientPorts[mqttPort] = true
          }
          if (this.tagKeysMap[cellPort] == null) {
            this.tagKeysMap[cellPort] = []
          }
          this.tagKeysMap[cellPort].push({ 'tag_key': item.tags.trigger })
          this.tagKeysMap[cellPort].push({ 'tag_key': item.tags.code })
        })
      })
      const host = window.location.hostname
      for (const key in this.tagKeysMap) {
        const cellUrl = `http://${host}:${key}/`
        CoreScadaReadTag(cellUrl, this.tagKeysMap[key])
          .then(res => {
            if (res.code === 0 && res.data) {
              for (const i in res.data) {
                const item = res.data[i]
                const k = this.getTopic(item.tag_key)
                const v = item.tag_value
                this.setContent(k, v)
              }
            }
          })
      }
      for (const key in this.clientPorts) {
        const port = parseInt(key)
        this.connectMQTT(host, port, (c) => {
          this.clients[key] = c
          c.subscribe('SCADA_CHANGE/#', {
            onSuccess: () => {
              console.debug('Subscribe success.')
            },
            onFailure: (responseObject) => {
              console.error('Subscribe fail:', responseObject.errorMessage)
            }
          })
        })
      }
    },
    loadAlarmCodes(call) {
      alarmApi.sel().then(res => {
        if (res.code === 0 && res.data) {
          for (const i in res.data) {
            const item = res.data[i]
            let k = item.code
            const v = item.desc
            if (item.clientId) {
              k = k + '_' + item.clientId
            }
            this.alarmCodes[k] = v
          }
          call && call()
        }
      })
    },
    saveAlarmHistory(alarmCode, alarmDesc, clientId, clientName) {
      alarmHistoryApi.add({
        code: alarmCode,
        desc: alarmDesc,
        clientId: clientId,
        clientName: clientName
      }).catch(err => {
        console.error('Save alarm history fail:', err)
      })
    },
    getTopic(tagKey) {
      return `SCADA_CHANGE/${tagKey}`
    },
    getFaultContent(alarmCode, clientId) {
      if (this.alarmCodes[alarmCode + '_' + clientId]) {
        return this.alarmCodes[alarmCode + '_' + clientId]
      } else if (this.alarmCodes[alarmCode]) {
        return this.alarmCodes[alarmCode]
      } else {
        return null
      }
    },
    setContent(k, v) {
      if (this.triggerMonitors[k]) {
        const client = this.triggerMonitors[k]
        const clientId = client.clientId
        const clientName = client.clientName
        client.faulted = v === '1'
        if (!client.faulted) {
          client.faultCode = null
          client.faultContent = '无'
          this.saveAlarmHistory(null, '无', clientId, clientName)
        }
      } else if (this.codeMonitors[k]) {
        const client = this.codeMonitors[k]
        const clientId = client.clientId
        const clientName = client.clientName
        const alarmCode = v
        const alarmContent = this.getFaultContent(alarmCode, clientId)
        if (alarmContent == null) {
          this.loadAlarmCodes(() => {
            const alarmContent = this.getFaultContent(alarmCode, clientId) || alarmCode
            client.faultCode = alarmCode
            client.faultContent = alarmContent
            this.saveAlarmHistory(alarmCode, alarmContent, clientId, clientName)
          })
        } else {
          client.faultCode = alarmCode
          client.faultContent = alarmContent
          this.saveAlarmHistory(alarmCode, alarmContent, clientId, clientName)
        }
      } else {
        console.error('Invalid topic:', k)
      }
    },
    showDetail(item) {
      this.dialog.title = item.clientName + '历史报警信息'
      this.dialog.visible = true
      this.$nextTick(() => {
        this.$refs.detail.load(item.clientId, item.clientName)
      })
    },
    connectMQTT(host, port, onConnected) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const topic = message.destinationName
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          this.setContent(topic, data.TagNewValue)
        } else {
          console.error('Invalid data:', payload)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }
}
</script>
<style lang="less" scoped>
body>.el-container {
    margin-bottom: 40px;
}

#origin {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transform-origin: 0 0;
    background-image: url('~@/assets/images/dcs/bgScreen.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;

    .el-header {
        background: url('~@/assets/images/dcs/topHeader.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        text-align: center;

        .el-row {
            margin: 0;
            height: 100%;

            &:last-child {
                margin-bottom: 0;
            }

            .el-col {
                height: 100%;
                padding: 0;
                border-radius: 0;

                div {
                    height: 100%;
                    border-radius: 0;
                }

                #dcs_alarm_dashboard_header_title {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                    font-size: 32px;
                    font-weight: bold;
                    color: #23CEFD;
                }

                #dcs_alarm_dashboard_header_time {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    font-size: 22px;
                    font-weight: bold;
                    color: #23CEFD;

                    img {
                        width: 22px;
                        height: 22px;
                        margin-left: 10px;
                        margin-right: 10px;
                    }
                }

                #dcs_alarm_dashboard_header_week {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    font-size: 22px;
                    font-weight: bold;
                    color: #23CEFD;
                    padding-right: 15%;
                }
            }
        }
    }

    .el-main {
        text-align: center;
        padding: 0 1% 0 1%;
    }
}

.dcs-alarm-dashboard-main-hint {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    padding: 10px 0 10px 0;

    div {
        margin-left: 50px;
    }
}

.dcs-alarm-dashboard-main-content {
    display: flex;
    flex-wrap: wrap;
}

.dcs-alarm-dashboard-main-content-item {
    flex: 0 0 14.28%;
    box-sizing: border-box;
    padding: 10px;
}

/* 小绿灯的动画 */
@keyframes greenPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7);
    }

    50% {
        box-shadow: 0 0 0 15px rgba(0, 255, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
    }
}

/* 小红灯的动画 */
@keyframes redPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
    }

    50% {
        box-shadow: 0 0 0 15px rgba(255, 0, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
    }
}

/* 小绿灯的样式 */
.green-light {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #75f94c;
    animation: greenPulse 2s infinite;
}

/* 小红灯的样式 */
.red-light {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: red;
    animation: redPulse 2s infinite;
}

/* 小灯的解释 */
.light-explain {
    font-size: 22px;
    font-weight: bold;
    color: white;
    margin-left: 10px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}

.box-card {
    height: 180px;
    width: 100%;
    background-color: #202436;
    border: 0;
}

.box-card-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    padding: 0;
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 20px;
}

.box-card-content-is-alarm {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    padding: 0;
    margin-bottom: 20px;
}

.box-card-content-is-alarm-label {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #f5ca45;
}

.box-card-content-alarm-content {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    padding: 0;
    font-size: 18px;
    font-weight: bold;
    color: #23CEFD;
    margin-right: 10px;
    margin-bottom: 20px;
}

.marquee {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  width: 100%;
  max-width: 120px;
  overflow: hidden; /* 隐藏超出部分 */
  white-space: nowrap; /* 防止内容换行 */
}

.marquee-content {
  display: inline-block;
  padding-left: 100%; /* 开始时，文本在容器外 */
  animation: marquee 5s linear infinite; /* 应用动画 */
}

@keyframes marquee {
  from {
    transform: translateX(0); /* 开始位置 */
  }
  to {
    transform: translateX(-100%); /* 结束位置，向左移动 */
  }
}
</style>
