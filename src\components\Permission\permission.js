import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    
    const roles = store.getters && store.getters.roles
    if (value && value instanceof Array && value.length > 0) {
      const permissionRoles = value

      const hasPermission = roles.some(role => {
        return permissionRoles.includes(role)
      })
      // 先注释权限控制，后续完善后统一添加 2022-04-18 wangxiong
      // if (!hasPermission) {
      //   el.parentNode && el.parentNode.removeChild(el)
      // }
    } else {
      throw new Error(`使用方式： v-permission="['admin','editor']"`)
    }
  }
}
