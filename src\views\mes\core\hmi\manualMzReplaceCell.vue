<template>
  <div class="app-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="24" style="position: relative;">
          <el-descriptions class="margin-top" title="模组信息" :column="4" border>
            <el-descriptions-item label="模组码" :span="2">
              {{ mzDxData.mz_barcode }}
            </el-descriptions-item>
            <el-descriptions-item label="模组工位状态" :span="2">
              {{ mzDxData.mzStatus }}
            </el-descriptions-item>
            <el-descriptions-item label="容量和" :span="2">
              {{ mzDxData.sum }}
            </el-descriptions-item>
            <el-descriptions-item label="容量差" :span="2">
              {{ mzDxData.diff }}
            </el-descriptions-item>
            <el-descriptions-item label="最大容量" :span="1">
              {{ mzDxData.maxCapacity }}
            </el-descriptions-item>
            <el-descriptions-item label="最大位置" :span="1">
              {{ mzDxData.maxIndex }}
            </el-descriptions-item>
            <el-descriptions-item label="最小容量" :span="1">
              {{ mzDxData.minCapacity }}
            </el-descriptions-item>
            <el-descriptions-item label="最小位置" :span="1">
              {{ mzDxData.minIndex }}
            </el-descriptions-item>
          </el-descriptions>
          <el-button size="small" type="primary" plain style="position: absolute;right: 10px;top: 0;" @click="handleOk">电芯信息确认</el-button>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="wrapCard CardTwo" style="margin-top: 10px;">
      <!--表格渲染-->
      <el-table ref="table" border size="small" :data="detailData" style="width: 100%" :height="height" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
        <el-table-column width="100" label="替换电芯" align="center" fixed="left">
          <template slot-scope="scope">
            <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="updateDx(scope.row)">替换电芯</el-tag>
          </template>
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="dx_barcode" label="电芯码" />
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column :show-overflow-tooltip="true" prop="dx_status" label="合格标志">
          <template slot-scope="scope">
            <el-tag :type="scope.row.dx_status==='OK'?'success':'danger'" effect="dark" style="cursor: pointer;" size="medium">{{ scope.row.dx_status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true" prop="dx_ori_capacity" label="原始容量" />
        <el-table-column :show-overflow-tooltip="true" prop="dx_pressure_kl_value" label="OCV测试开路电压" />
        <el-table-column :show-overflow-tooltip="true" prop="dx_pressure_fd_value" label="OCV测试负路电压" />
        <el-table-column :show-overflow-tooltip="true" prop="dx_dianzu_value" label="OCV测试电阻" />
        <el-table-column :show-overflow-tooltip="true" prop="dx_k2_value" label="K2计算值" />
        <el-table-column :show-overflow-tooltip="true" prop="dx_ori_ocv4_time" label="OCV开始时间" />
      </el-table>
    </el-card>
    <el-dialog append-to-body :close-on-click-modal="false" title="替换电芯" :visible.sync="updDialogVisbleSync" width="520px">
      <el-input ref="dxCode" v-model="dx_barcode" placeholder="请输入电芯条码" />
      <div style="text-align: center; margin: 20px 0 0 0">
        <el-button size="small" icon="el-icon-close" plain @click="updDialogVisbleSync=false">取消</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :disabled="dx_barcode === ''" @click="saveDxBarcode()">确认</el-button>
        <!-- 确认 -->
      </div>
    </el-dialog>
    <el-dialog append-to-body :close-on-click-modal="false" title="提示" :visible.sync="dxDialogVisbleSync" width="520px">
      <span style="font-size: 18px;">请前去替换电芯</span>
      <div style="text-align: center; margin: 20px 0 0 0">
        <el-button size="small" icon="el-icon-close" plain @click="dxDialogVisbleSync=false">关闭</el-button>
        <!-- 关闭 -->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import { selDetail, mzQualityUpd, sel, MesMeMzDxQualitySel, MesMeMzCheck } from '@/api/mes/core/meMzQuality'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'manualMzReplaceCell',
  data() {
    return {
      height: document.documentElement.clientHeight - 230,
      detailData: [],
      updDialogVisbleSync: false,
      currentRowData: {},
      dx_barcode: '',
      dxCount: 0,
      mzThickness: 0,
      monitorData: {
        // 模组码
        PlcQ_ModuleBlockCode: { client_code: 'OP1070', group_code: 'PlcQuality', tag_code: 'PlcQ_ModuleBlockCode', value: '' },
        PlcQ_ModuleStationRecord: { client_code: 'OP1070', group_code: 'PlcQuality', tag_code: 'PlcQ_ModuleStationRecord', value: '' }
      },
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      mzDxData: {
        mz_barcode: '',
        mzStatus: '',
        sum: '',
        diff: '',
        maxCapacity: '',
        minCapacity: '',
        maxIndex: '',
        minIndex: ''
      },
      dxDialogVisbleSync: false
    }
  },
  mounted: function() {
    this.toStartWatch()
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 230
    }
  },
  methods: {
    toQuery(mz_barcode) {
      const query = {
        mz_barcode
      }
      selDetail(query).then(res => {
        this.dxCount = res.count
        if (res.code === 0 && res.count > 0) {
          this.detailData = res.data
          this.mzThickness = 0
          this.detailData.forEach(item => {
            this.mzThickness += item.dx_ori_thickness
          })
        } else {
          this.detailData = []
        }
      })
        .catch(ex => {
          this.$message({ message: '操作失败：' + ex, type: 'error' })
          this.detailData = []
        })
    },
    handleOk() {
      const dx_barcode_list = this.detailData.filter(item => item.dx_barcode).map(val => val.dx_barcode).join(',')
      if (!dx_barcode_list) {
        this.$message({
          type: 'warning',
          success: '当前没有可替换的电芯'
        })
        return
      }
      if (!this.monitorData.PlcQ_ModuleBlockCode.value) {
        this.$message({
          type: 'warning',
          success: '未读取到模组码'
        })
        return
      }
      const query = {
        station_code: this.$route.query.station_code,
        prod_line_id: this.$route.query.prod_line_id,
        mz_barcode_list: this.monitorData.PlcQ_ModuleBlockCode.value,
        dx_barcode_list
      }
      MesMeMzCheck(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        console.log(defaultQuery)
        if (defaultQuery.code === 0) {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
        } else {
          this.$message({ message: '操作失败：' + res.msg, type: 'error' })
        }
      }).catch(ex => {
        this.$message({ message: '操作失败' + ex, type: 'error' })
      })
    },
    getMzQualityData(mz_barcode) {
      const query = {
        page: 1,
        mz_barcode,
        user_name: Cookies.get('userName')
      }
      MesMeMzDxQualitySel(query).then(res => {
        if (res.code === 0) {
          if (res.result !== '') {
            const result = JSON.parse(res.result)
            this.mzDxData.mz_barcode = this.monitorData.PlcQ_ModuleBlockCode.value,
            this.mzDxData.mzStatus = result.mzStatus,
            this.mzDxData.sum = result.sum,
            this.mzDxData.diff = result.diff,
            this.mzDxData.maxCapacity = result.maxCapacity,
            this.mzDxData.minCapacity = result.minCapacity,
            this.mzDxData.maxIndex = result.maxIndex,
            this.mzDxData.minIndex = result.minIndex
          } else {
            for (const key in this.mzDxData) {
              this.mzDxData[key] = ''
            }
            this.$message({ message: '查询失败：' + res.msg, type: 'error' })
          }
        }
      }).catch(ex => {
        this.$message({ message: '查询失败：' + ex, type: 'error' })
        for (const key in this.mzDxData) {
          this.mzDxData[key] = ''
        }
      })
    },
    saveDxBarcode() {
      if (this.dx_barcode === '') {
        this.$message({ message: '请输入电芯条码', type: 'info' })
        return
      }
      this.$confirm(`确认将电芯条码${this.currentRowData.dx_barcode}替换成${this.dx_barcode}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          mzQualityUpd({
            mz_dx_rel_id: this.currentRowData.mz_dx_rel_id,
            dx_barcode: this.dx_barcode
          })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.updDialogVisbleSync = false
                this.toQuery(this.monitorData.PlcQ_ModuleBlockCode.value)
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    },
    updateDx(row) {
      this.currentRowData = row
      this.dx_barcode = ''
      this.updDialogVisbleSync = true
      this.$nextTick(() => {
        this.$refs.dxCode.focus()
      })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          // var connectUrl = 'ws://*************:8083' + '/mqtt'
          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // 获取Tag值
            this.GetTagValue()
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            var jsonData = JSON.parse(message)
            if (jsonData == null) return
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
              // 模组工位记录 1合格，2NG
              if (this.monitorData.PlcQ_ModuleStationRecord.value === '2') {
                this.dxDialogVisbleSync = true
              }
              if (this.monitorData.PlcQ_ModuleBlockCode.value !== '') {
                this.toQuery(this.monitorData.PlcQ_ModuleBlockCode.value)
                this.getMzQualityData(this.monitorData.PlcQ_ModuleBlockCode.value)
              }
            }
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 从后台REDIS获取数据
    GetTagValue() {
      // 读取Tag集合(Key)
      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                Object.keys(this.monitorData).forEach(key => {
                  var client_code = this.monitorData[key].client_code
                  var group_code = this.monitorData[key].group_code
                  var tag_code = this.monitorData[key].tag_code
                  var tag_key = client_code + '/' + group_code + '/' + tag_code
                  const item = result.filter(item => item.tag_key === tag_key)
                  if (item.length > 0) {
                    this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                  }
                })
                // 模组工位记录 1合格，2NG
                if (this.monitorData.PlcQ_ModuleStationRecord.value === '2') {
                  this.dxDialogVisbleSync = true
                }
                if (this.monitorData.PlcQ_ModuleBlockCode.value !== '') {
                  this.toQuery(this.monitorData.PlcQ_ModuleBlockCode.value)
                  this.getMzQualityData(this.monitorData.PlcQ_ModuleBlockCode.value)
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .el-descriptions--small.is-bordered .el-descriptions-item__cell{
    text-align: center;
}
</style>
