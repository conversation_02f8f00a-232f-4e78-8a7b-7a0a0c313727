import request from '@/utils/request'

// 电芯排废查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDXBarSel',
    method: 'post',
    data
  })
}
// 电芯排废增加
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDXBarIns',
    method: 'post',
    data
  })
}
// 电芯排废修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDXBarUpd',
    method: 'post',
    data
  })
}
// 电芯排废删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackDXBarDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
