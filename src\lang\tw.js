export const view = {
  title: {
    boardPractice: '板件履歷',
    cimMessageReport: 'CIM消息報表',
    interfaceLog: '接口日誌',
    threeRateReport: '三率報表',
    communicationDiagnosis: '通訊診斷',
    pointChangeTracking: '點位變化追蹤',
    pointWriteTracking: '點位寫入追蹤',
    setting: '設置',
    dcsAlarm: '警報',
    parkSortRule: '分選規則設定',
    parkSortSplitRule: '分選截取比對規則設定',
    parkXNPSort: 'X數/位分選模式記錄',
    parkExtra: '額外參數設置'
  },
  button: {
    export: '導出',
    search: '搜索',
    reset: '重置',
    view: '查看',
    close: '關閉',
    confirm: '確認',
    cancel: '取消',
    disable: '禁用',
    unbind: '解綁',
    setting: '設置',
    addPosition: '新增倉位'
  },
  enum: {
    placeholder: {
      pleaseChosen: '請選擇',
      pleaseEnter: '請輸入'
    },
    boardErrorCode: {
      normal: '正常',
      mixedBoard: '混板',
      readFailureBoard: '讀碼失敗板',
      duplicateCode: '重碼',
      forcedPass: '強制越過',
      forcedFinal: '強制結批',
      otherDefined: '其他定義'
    },
    manualProcessingBoard: {
      manualInputMode: '人工輸入模式',
      reread: '重讀',
      forcedPass: '強制越過'
    },
    communication: {
      normal: '通訊正常',
      abnormal: '通訊異常'
    },
    writeStatus: {
      normal: '寫入正常',
      abnormal: '寫入異常'
    }
  },
  field: {
    plan: {
      lotNum: '批號',
      lotNo: 'Lot No',
      plantOrderNum: '工廠訂單號',
      tailCount: '尾包數量'
    },
    jobOrder: {
      batchNo: '批號',
      laserBatchNo: '鐳射批號',
      originalLotNo: '原始批號',
      certifyLotNo: '驗證批號',
      typesettingNumber: '排版數',
      customerMaterialNo: '客戶料號',
      lot: '批次',
      model: '料號',
      ul: 'UL',
      bdBarcodes: 'PCS條碼集',
      xoutPositions: 'XOUT位置集',
      bdLevels: 'PCS等級集',
      bdLevelJudgments: 'PCS等級判定集',
      shipAddress: '出貨地'
    },
    setRecord: {
      created_time: '創建時間',
      lot_num: '訂單號',
      array_index: '訂單SET順序',
      board_sn: '線掃流水號',
      pile_barcode: '包裝條碼',
      array_barcode: 'SET條碼',
      array_status: 'SET狀態',
      array_ng_code: 'SET分選NG代碼',
      array_ng_msg: 'SET分選NG描述',
      array_level: '線掃SET等級',
      array_mark: '線掃光學點檢測結果',
      array_bd_count: '線掃SET下PCS數量',
      board_result: '板件判斷結果',
      board_turn: '旋轉方向',
      deposit_position: '堆疊位置',
      xout_flag: '是否為XOUT分選',
      xout_set_num: 'XOUT設定數量',
      xout_act_num: 'XOUT實際數量',
      array_front_info: 'SET正面線掃數據',
      array_back_info: 'SET反面線掃數據',
      task_type: '任務類型',
      model_type: '料號(型號)',
      model_version: '料號(型號)版本',
      array_type: 'SET類型',
      bd_type: 'PCS類型',
      m_length: '長,單位毫米',
      m_width: '寬,單位毫米',
      m_tickness: '厚,單位毫米',
      m_weight: '重,單位克',
      batch_no: '批次',
      cycle_period: '周期',
      laser_batch_no: '鐳射批號',
      typesetting_no: '排版數',
      bd_barcodes: 'PCS條碼集',
      xout_positions: 'XOUT位置集',
      bd_levels: 'PCS等級集',
      bd_level_judgments: 'PCS等級判定集',
      customer_mn: '客戶料號',
      ul_code: 'UL號',
      pile_use_flag: '是否被打包使用',
      enable_flag: '有效標識',
      unbind_flag: '是否解綁',
      unbind_time: '解綁時間',
      unbind_way: '解綁方式說明'
    },
    pdSetRecord: {
      created_time: '創建時間',
      lot_num: '訂單號',
      array_index: '訂單SET順序',
      board_sn: '線掃流水號',
      pile_barcode: '包裝條碼',
      array_barcode: '廠内碼',
      array_status: 'SET狀態',
      array_ng_code: 'SET分選NG代碼',
      array_ng_msg: 'SET分選NG描述',
      array_level: '線掃SET等級',
      array_mark: '線掃光學點檢測結果',
      array_bd_count: '線掃SET下PCS數量',
      board_result: '板件判斷結果',
      board_turn: '旋轉方向',
      deposit_position: '堆疊位置',
      xout_flag: '是否為XOUT分選',
      xout_set_num: 'XOUT設定數量',
      xout_act_num: 'XOUT實際數量',
      array_front_info: 'SET正面線掃數據',
      array_back_info: 'SET反面線掃數據',
      task_type: '任務類型',
      model_type: '料號(型號)',
      model_version: '料號(型號)版本',
      array_type: 'SET類型',
      bd_type: 'PCS類型',
      m_length: '長,單位毫米',
      m_width: '寬,單位毫米',
      m_tickness: '厚,單位毫米',
      m_weight: '重,單位克',
      batch_no: '批次',
      cycle_period: '周期',
      laser_batch_no: '鐳射批號',
      typesetting_no: '排版數',
      bd_barcodes: 'PCS條碼集',
      xout_positions: 'XOUT位置集',
      bd_levels: 'PCS等級集',
      bd_level_judgments: 'PCS等級判定集',
      customer_mn: '客戶料號',
      ul_code: 'UL號',
      pile_use_flag: '是否被打包使用',
      enable_flag: '有效標識',
      unbind_flag: '是否解綁',
      unbind_time: '解綁時間',
      unbind_way: '解綁方式說明'
    },
    pileRecord: {
      intef_return_code: '接口返回碼',
      intef_return_msg: '接口返回訊息',
      apply_return_code: '應用返回碼',
      apply_return_msg: '應用返回訊息',
      print_return_code: '打印返回碼',
      print_return_msg: '打印返回訊息',
      inspect_result_code: '檢驗結果碼',
      inspect_result_msg: '檢驗結果訊息'
    },
    mappingResultRecord: {
      enable_flag: '有效標識',
      item_date_val: '下載時間',
      upload_date_val: '上傳時間',
      product_no: '料號',
      product_rev: '料號版本',
      lot_no: '批號',
      barcode: '廠內碼',
      bad_mark: 'mapping結果',
      data_from: '數據來源',
      process_no: '製程代號',
      twodlot_no: '2Dmapping批號',
      parentlot_no: '原始母批',
      reworknum: '重工數',
      angle: '旋轉角度',
      flip_type: 'X,Y（翻轉）',
      stripeachpiecex: 'Each Piece（Strip）X',
      stripeachpiecey: 'Each Piece（Strip）Y',
      err_type: '不良數據結果'
    },
    splitRules: {
      order_lot: '訂單截取批次規則',
      custom_model: '自定義截取料號規則',
      array_set: 'SET截取SET板件號規則',
      array_inner: '廠内碼截取板件號規則',
      array_lot: 'SET截取鐳射批次規則',
      array_innerLot: '廠内碼截取鐳射批次規則',
      array_model: 'SET截取料號規則',
      array_cycle: 'SET截取周期規則',
      array_innerCycle: '廠内碼截取周期規則',
      array_dc: 'SET截取DateCode規則',
      array_ln: 'SET截取LotNum規則',
      array_innerDc: '廠内碼板件字符截取周期規則',
      array_innerIn: '廠内碼板件字符截取鐳射批次規則',
      bd_set: 'PCS截取SET板件號規則',
      bd_lot: 'PCS截取批次規則',
      bd_model: 'PCS截取料號規則',
      bd_cycle: 'PCS截取周期規則',
      bd_inner: 'PCS截取廠内碼板件號規則',
      bd_innerCycle: 'PCS截取廠内碼周期規則',
      bd_dc: 'PCS截取DateCode規則',
      bd_ln: 'PCS截取LotNum規則',
      bd_index: 'PCS截取索引規則'
    },
    recipe: {
      weight_error: '重量誤差(g)',
      inkjet_tpl: '噴碼模板'
    },
    stationFlow: {
      station_code: '站點編碼',
      station_desc: '站點描述',
      serial_num: '標籤ID',
      recipe_paras: '配方參數',
      arrive_date: '到達時間',
      leave_date: '離開時間',
      cost_time: '耗時(ms)'
    },
    stationQuality: {
      station_flow_id: '流程ID',
      station_code: '站點編碼',
      station_desc: '站點描述',
      serial_num: '標籤ID',
      quality_for: '質量來源',
      tag_code: '標籤編碼',
      tag_value: '標籤值',
      quality_d_sign: '質量標誌',
      trace_d_time: '追溯時間'
    },
    ccdValidationResult: {
      station_code: '站點編碼',
      station_desc: '站點描述',
      serial_num: '標籤ID',
      validate_result: '校驗結果',
      validate_msg: '校驗信息',
      validate_time: '校驗時間'
    },
    workpiece: {
      prod_line_code: '生產線',
      serial_num: '總成號',
      quality_sign: '合格標誌',
      make_order: '訂單號',
      records: '過站記錄-list',
      arrive_date: '到達時間-datetime',
      leave_date: '離開時間-datetime',
      cost_time: '耗時(ms)'
    },
    pile: {
      item_date_val: '時間',
      lot_num: '訂單號',
      finish_lable_count: '打包順序',
      pile_barcode: '打包條碼',
      custom_barcode: 'EAP打包條碼',
      pile_index: '訂單中打包序號',
      array_count: '打包中SET總數',
      pile_status: '打包狀態',
      pile_ng_code: '打包驗證NG代碼',
      pile_ng_msg: '打包驗證NG描述',
      pc_array_list: 'PC計劃SET集合',
      plc_array_list: 'PLC回饋SET集合',
      trunk_flag: '是否尾箱',
      pile_weight: '打包重量',
      pile_user: '打包人員',
      enable_flag: '是否有效',
      unbind_flag: '是否解绑',
      unbind_user: '解绑人員',
      unbind_time: '解绑時間'
    }
  },
  form: {
    batchNo: '母批號',
    cardWorkOrder: '拆卡工單',
    subBatchNo: '子批號',
    subSimplifiedCode: '子批簡碼',
    materialNo: '物料號',
    carrierBarcode: '載具條碼',
    taskSource: '任務來源',
    station: '工位',
    portNo: '端口號',
    boardBarcode: '板件條碼',
    boardStatus: '板件狀態',
    boardErrorCode: '板件錯誤代碼',
    isFirstCheck: '是否首檢',
    isDummy: '是否Dummy',
    manualProcessingBoard: '手工處理板',
    hasPanelMode: '有無Panel模式',
    operator: '操作工',
    rooftopBarcode: '天蓋條碼',
    orientation: '朝向',
    orientationToFront: '正面',
    orientationToBack: '背面',
    isOffline: '是否離線',
    time: '時間',
    timePeriod: '時間範圍',
    timePeriodStart: '開始時間',
    timePeriodEnd: '結束時間',
    messageSource: '消息來源',
    displayMode: '顯示方式',
    completionFlag: '完成標識',
    interfaceName: '接口名稱',
    interfaceDescription: '接口描述',
    sourceSystem: '來源系統',
    targetSystem: '目標系統',
    requestParameters: '請求參數',
    responseParameters: '響應參數',
    isSuccess: '是否成功',
    interfaceMethod: '接口方式',
    instance: '實例',
    communicationStatus: '通訊狀態',
    tagGroup: '標籤組',
    tag: '標籤',
    processWarning: '工藝預警',
    shiftCode: '班次代碼',
    uploadFlag: '上傳標識',
    writeStatus: '寫入狀態',
    isReversed: '是否反轉',
    reversedFlag: '反轉標識'
  },
  table: {
    name: '名稱',
    time: '時間',
    subTaskNo: '子任務編號',
    portNo: '端口號',
    carrierBarcode: '載具條碼',
    rooftopBarcode: '天蓋條碼',
    boardBarcode: '板子條碼',
    boardStatus: '板子狀態',
    ngBoardErrorCode: 'NG板子異常代碼',
    ngBoardErrorDescription: 'NG板子異常描述',
    isFirstCheckBoard: '是否為首檢板子',
    taskSource: '任務來源',
    motherBatch: '母批',
    subTaskSimplifiedCode: '子任務簡碼',
    subTaskSorting: '子任務排序',
    materialNo: '物料編號',
    carrierType: '載具類型',
    layer: '層別',
    subDiskSorting: '分盤排序',
    boardLength: '板長',
    boardWidth: '板寬',
    boardThickness: '板厚',
    boardSorting: '板件排序',
    isDummyBoard: '是否為Dummy板',
    manualProcessingBoard: '人工處理板',
    isPanelMode: '是否Panel模式',
    operator: '操作員',
    isEAP: '是否為EAP',
    orientation: '朝向',
    messageSource: '消息來源',
    displayMode: '顯示方式',
    completionFlag: '完成標識',
    message: '消息',
    code: '代碼',
    serialNumber: '序號',
    esbInterfaceName: 'ESB接口名稱',
    esbInterfaceDescription: 'ESB接口描述',
    interfaceMethod: '接口方式',
    esbInterfaceAddress: 'ESB接口地址',
    sourceSystem: '來源系統',
    targetSystem: '目標系統',
    startTime: '開始時間',
    endTime: '結束時間',
    requestDuration: '請求時長',
    isSuccess: '是否成功',
    interfaceMessage: '接口消息',
    requestParameters: '請求參數',
    responseParameters: '響應參數',
    customizedParameters: '定制化參數',
    requestMessage: '請求消息',
    remark: '備註',
    shift: '班次',
    shiftCode: '班次代碼',
    shiftStart: '班次開始',
    shiftEnd: '班次結束',
    uploadFlag: '上傳標識',
    uploadTimes: '上傳次數',
    uploadMessage: '上傳消息',
    dropBoardQuantity: '掉板數量',
    totalQuantity: '總數量',
    dropBoardRate: '掉板率',
    boardOkQuantity: '板件OK數量',
    boardNgQuantity: '板件NG數量',
    boardFailureRate: '板件失敗率',
    carrierOkQuantity: '載具OK數量',
    carrierNgQuantity: '載具NG數量',
    carrierFailureRate: '載具失敗率',
    workingTime: '工作時間',
    shiftTime: '班次時間',
    oee: 'OEE',
    tableForm: '表格形式',
    curveForm: '曲線形式',
    exceptionStatistics: '異常統計',
    communicationInfo: '通訊信息',
    simulationFlag: '模擬標識',
    sampleQuantity: '樣本數量',
    instanceCode: '實例編碼',
    instanceDescription: '實例描述',
    communicationStatus: '通訊狀態',
    messageDescription: '消息描述',
    communicationStatusScatterPlotAnalysis: '通訊狀態散點圖分析',
    statusValue: '狀態值',
    communicationExceptionFrequencyStatistics: '通訊異常頻次統計',
    networkOutageFrequency: '斷網頻次',
    tagID: '標籤ID',
    tagDescription: '標籤描述',
    collectionValue: '採集值',
    isWarningSet: '是否設置預警',
    processWarning: '工藝預警',
    lowerLimitValue: '下限值',
    upperLimitValue: '上限值',
    oldValue: '舊值',
    tagUniqueKey: '標籤唯一鍵',
    dataAlarm: '數據報警',
    dataNormal: '數據正常',
    tagAttribute: '標籤屬性',
    dataType: '數據類型',
    area: '區域',
    areaAddress: '區域地址',
    startAddress: '起始地址',
    dataLength: '數據長度',
    bit: '位',
    OPCAddress: 'OPC地址',
    tagDataScatterPlotAnalysis: '標籤數據散點圖分析',
    processWarningSign: '工藝預警標識',
    normal: '正常',
    alarm: '報警',
    writeActivityScatterPlotAnalysis: '寫入活動散點圖分析',
    writeFrequencyStatistics: '寫入頻次統計',
    statisticsFromWriterRatio: '統計來自寫入者比例',
    writeNormal: '寫入正常',
    writeException: '寫入異常',
    writeValue: '寫入值',
    writer: '寫入者',
    writeStatus: '寫入狀態',
    writeTagQuantity: '寫入標籤數量',
    consumeTime: '消耗時間(ms)',
    frequencyStatistics: '頻次統計',
    pieChartAnalysis: '餅圖分析',
    writeDetail: '寫入明細'
  },
  pagination: {
    total: '總數量',
    current: '當前第',
    previous: '上一頁',
    next: '下一頁',
    unit: '頁'
  },
  dialog: {
    top: '已置頂',
    bottom: '已置底',
    queryException: '查詢異常',
    selectStation: '請選擇工位',
    exportFailed: '導出失敗',
    selectInstance: '請選擇實例',
    pleaseSelectTagGroup: '請選擇標籤組',
    pleaseSelectTag: '請選擇標籤',
    noData: '當前無數據',
    scadaCommunicationReportQueryFailed: 'Scada通訊報表查詢失敗',
    scadaCommunicationReportQueryTimeoutOrSQLError:
      'Scada通訊報表查詢超時或者SQL錯誤',
    querySelCellIPException: '查詢selCellIP異常',
    noUnitIPAndPortNumberObtained: '未獲取到單元IP與端口號',
    scadaInstanceBaseDataQueryFailed: 'Scada實例基礎數據查詢失敗',
    scadaInstanceBaseDataQueryTimeoutOrSQLError:
      'Scada實例基礎數據查詢超時或者SQL錯誤',
    scadaInstanceTagGroupDataQueryFailed: 'Scada實例標籤組數據查詢失敗',
    scadaInstanceTagGroupDataQueryTimeoutOrSQLError:
      'Scada實例標籤組數據查詢超時或者SQL錯誤',
    scadaInstanceTagDataQueryFailed: 'Scada實例標籤數據查詢失敗',
    scadaInstanceTagDataQueryTimeoutOrSQLError:
      'Scada實例標籤數據查詢超時或者SQL錯誤',
    scadaReadDataChangeReportQueryFailed: 'Scada讀取數據變化報表查詢失敗',
    scadaReadDataChangeReportQueryTimeoutOrSQLError:
      'Scada讀取數據變化報表查詢超時或者SQL錯誤',
    scadaWriteReportQueryFailed: 'Scada寫入報表查詢失敗',
    scadaWriteReportQueryTimeoutOrSQLError: 'Scada寫入報表查詢超時或者SQL錯誤',
    reconfirmedToClose: '請再次確認是否關閉',
    hint: '提示',
    operationSucceed: '操作成功',
    operationFailed: '操作失敗',
    operationCanceled: '操作取消',
    confirmToUnbindThisPCSRecord: '確定解綁此條PCS記錄',
    confirmToDisableThisSETRecord: '確定禁用此條SET記錄',
    confirmedToSend: '確認發送',
    formatError: '格式錯誤'
  }
}

// 志圣- 珠海超毅
export const zhcy = {
  // 操作模式
  offlineMode: '離線模式',
  onlineRemote: '在線/遠程',
  onlineLocal: '在線/本地',

  // 生產狀態
  productionAllowed: '允許生產',
  productionNotAllowed: '不允許生產',
  recipeUpdateAllowed: '允許下發配方',
  recipeUpdateNotAllowed: '禁止下發配方',

  // 指示燈
  triColorLight: '報警燈',
  fourColorLight: '報警燈',
  greenLight: '綠燈',
  yellowLight: '黃燈',
  redLight: '紅燈',
  blueLight: '藍燈',

  // 心跳狀態
  plcHeartbeat: 'PLC',
  eapStatus: 'EAP',
  ccdStatus: 'CCD',
  online: '在線',
  offline: '離線',

  // 登入/登出
  login: '登入',
  logout: '登出',
  employeeLogin: '員工登入',
  employeeId: '員工號',
  name: '姓名',
  userName: '姓名',
  permission: '權限',
  pleaseEnterEmployeeId: '請輸入員工號',
  enterEmployeeId: '請輸入員工號',
  cancel: '取消',
  confirm: '確定',
  employee: '員工',
  engineer: '工程師',
  admin: '管理員',
  confirmLogout: '確定登出當前員工嗎？',
  noUserLoggedIn: '當前沒有登入用戶',
  // 模式切換
  switchingTo: '正在切換到',
  // 其他按鈕
  syncAccountInfo: '同步賬戶信息',
  scanAndLoad: '上機',
  start: '啟動',
  end: '結束',
  loading: '正在上機中...',
  loadSuccess: '上機成功',
  loadFail: '上機失敗',
  loadFailCheckContent: '上機失敗，請檢查掃碼內容',
  pleaseLoginFirst: '請先登入後再進行上機操作',
  offlineModeCannotLoad: '當前為離線模式，無法進行上機操作',
  // 其他文本
  pleaseSelectStation: '未選擇工位或工位信息不完整',
  materialNumber: '物料號',
  dryFilmCode: '乾膜物料編碼',
  pleaseSelectStatus: '請至少選擇一種狀態進行查詢',
  queryFailed: '獲取任務列表失敗',
  queryException: '獲取任務列表異常',
  // 消息提示
  mqttConnSuccess: 'MQTT連接成功',
  mqttConnFailed: 'MQTT連接失敗',
  mqttReconnecting: 'MQTT連接斷開，正在重連...',
  plcCommError: 'PLC通訊中斷',
  eapCommError: 'EAP通訊中斷',
  ccdCommError: 'CCD通訊中斷',
  syncingAccountInfo: '正在請求同步賬戶信息...',
  syncAccountSuccess: '成功同步{0}條權限賬戶信息',
  syncAccountFailed: '請求權限賬戶信息失敗',
  syncAccountError: '請求權限賬戶信息異常',
  enterEmployeeId: '請輸入員工號',
  loggingIn: '正在登錄中...',
  loginSuccess: '登錄成功',
  loginFailed: '登錄失敗',
  loginError: '登錄異常',
  noUserLoggedIn: '當前沒有登錄用戶',
  confirmLogout: '確定登出當前員工嗎？',
  logoutSuccess: '登出成功',
  logoutFailed: '登出失敗',
  logoutError: '登出異常',
  enterOrScanMaterial: '請輸入或掃碼物料號',
  startingTask: '正在啟動任務...',
  taskStartSuccess: '任務啟動成功',
  taskStartFailed: '任務啟動失敗，請檢查設備狀態',
  endingTask: '正在結束任務...',
  taskEndSuccess: '結束請求發送成功，等待設備做完剩餘材料',
  taskEndFailed: '任務結束失敗，請檢查設備狀態',
  noRecipeData: '沒有可下發的配方數據',
  recipeDeploySuccess: '配方下發成功',
  queryException: '查詢異常',
  queryExceptionWithError: '查詢異常：{0}',
  switchingToMode: '正在切換到{0}……',
  rescanRequired: '請重新掃碼上機',
  confirm: '確定',
  cancel: '取消',
  warning: '提示',
  requestingSyncAccountInfo: '正在請求同步賬戶信息...',
  successfullySynced: '成功同步',
  permissionAccountInfo: '條權限賬戶信息',
  requestPermissionAccountInfoFailed: '請求權限賬戶信息失敗',
  requestPermissionAccountInfoException: '請求權限賬戶信息異常',
  noStationSelectedOrIncompleteInfo: '未選擇工位或工位信息不完整',
  selectAtLeastOneStatusForQuery: '請至少選擇一種狀態進行查詢',
  handleReloadMachine: '確認重新上機',
  taskList: '任務列表',
  alarmInfo: '報警信息',
  taskName: '任務名稱',
  status: '狀態',
  productNumber: '產品料號',
  taskQuantity: '任務數量',
  createTime: '創建時間',
  startTime: '開始時間',
  endTime: '結束時間',
  operations: '操作',
  delete: '刪除',
  query: '查詢',
  planning: '計劃中',
  inProduction: '生產中',
  completed: '已完成',
  cancelled: '已取消',
  confirmStartTask: '確定要啟動任務 {0} 嗎？',
  confirmEndTask: '確定要結束任務 {0} 嗎？',
  station: '工位',
  instanceCode: '實例編號',
  instanceDesc: '實例描述',
  alarmCode: '報警代碼',
  alarmLevel: '報警級別',
  alarmDesc: '報警描述',
  alarmTime: '報警時間',
  resetTime: '復位時間',
  isReset: '是否復位',
  isSimulated: '是否模擬',
  resetYes: '已復位',
  resetNo: '待復位',
  yes: '是',
  no: '否',
  capacity: '產能',
  dailyProduction: '日產量',
  readRate: '讀碼率',
  oee: 'OEE',
  projectName: '項目名稱',
  currentValue: '當前值',
  unit: '單位',
  upperLimit: '上限',
  lowerLimit: '下限',
  statusText: '狀態',
  modifyRecipe: '修改配方',
  isModifyParameters: '是否修改參數',
  parameterCode: '參數代碼',
  parameterDesc: '參數描述',
  parameterValue: '參數值',
  validFlag: '有效標誌',
  valid: '有效',
  invalid: '無效',
  parameterCodeLabel: '參數編碼',
  parameterDescLabel: '參數描述',
  parameterValueLabel: '參數值',
  validFlagLabel: '有效標識',
  confirmAndIssue: '確認並下發',
  cimMessage: 'CIM消息',
  code: '代碼',
  message: '消息',
  reloadMachineRequired: '需要重新上機',
  reloadMachineToContinue: '請重新上機以繼續操作',
  offDuty: '下班',
  onDuty: '上班',
  enterMaterialCode: '請輸入物料代碼',
  enterMaterialDesc: '請輸入物料描述',
  productionMode: '生產模式',
  firstPieceMode: '首件模式',
  dummyMode: '空跑模式',
  recipeMaintenance: '配方維護',
  pagination: {
    total: '總計',
    current: '當前第',
    unit: '頁',
    previous: '上一頁',
    next: '下一頁'
  }
}

export const lang_pack = {
  locale: '繁体',
  SystemName: 'AIS流程設計與作業系統',
  SystemNameOne: '訂單資訊系統',
  UserName: '系統賬號',
  Password: '系統密碼',
  Role: '系統角色',
  Code: '驗證',
  SignOn: '登 錄',
  Prompt: '提示',
  codeError: '驗證碼不存在或已過期！',
  LoggingIn: '登 錄 中...',

  // 日期時間選擇器快捷選項
  dateTimePicker: {
    last10Minutes: '最近10分鐘',
    last30Minutes: '最近30分鐘',
    lastHour: '最近1小時',
    lastDay: '最近1天',
    lastWeek: '最近7天',
    last30Days: '最近30天',
    last90Days: '最近90天',
    lastYear: '最近1年'
  },
  /** ************************************************************/
  /* Core定义*/
  commonPage: {
    /* 公共 */ add: '新增',
    edit: '編輯',
    remove: '刪除',
    search: '搜索',
    reset: '重置',
    operate: '操作',
    cancel: '取消',
    close: '關閉',
    confirm: '確認',
    checkAll: '全選',
    back: '返回',
    upload: '上傳',
    validIdentificationt: '有效標識',
    validIdentification: '有效標識：',
    detail: '詳情',
    required: '必填項',
    submitSuccesful: '提交成功',
    addSuccesful: '新增成功',
    editSuccessful: '編輯成功',
    deleteSuccesful: '删除成功',
    operationSuccessful: '操作成功',
    operationfailure: '操作失敗!',
    pingSuccessful: 'Ping測試成功',
    pingfailure: 'Ping測試失敗'
  },
  maintenanceMenu: {
    /* 菜单维护 */ menuEncodingdescription: '菜單編碼/描述：',
    menuGroupCoding: '菜單組編碼',
    menuGroupDescription: '菜單組描述',
    order: '順序',
    menuType: '菜單類型',
    icon: '圖標',
    submenuCoding: '子菜單編碼',
    submenuCodingDescription: '子菜單描述',
    addSubmenu: '新增子菜單',
    procedure: '程序'
  },
  maintenancePeople: {
    /* 人员维护 */ nameEmployeeID: '姓名/員工號：',
    emailPhone: '郵箱/手機號：',
    EmployeeID: '員工號',
    name: '姓名',
    username: '用戶名',
    password: '密碼',
    email: '郵箱',
    phone: '電話',
    phonenumber: '手機號',
    department: '部門',
    position: '職位',
    role: '角色',
    photo: '頭像'
  },
  maintenanceErrorMsg: {
    /* 国际化配置 */ errorType: '配置類型',
    errorModule: '所屬模塊',
    errorFunctionCode: '功能編碼',
    errorChinese: '中文',
    errorEnglish: '英文',
    errorOther: '其他語言',
    errorKeyword: '關鍵字段'
  },
  maintenanceRole: {
    /* 角色维护 */ roleNumberDescription: '角色編號/描述：',
    roleNumber: '角色編號',
    roleDescription: '角色描述',
    productionLine: '產線',
    process: '工序',
    locationCoding: '工位編碼',
    locationDescription: '工位描述',
    roleEncoding: '角色編碼',
    permission: '權限',
    menu: '菜單',
    station: '工位'
  },
  applicationMenu: {
    /* 程序菜单 */ programCodingDescription: ' 程序編碼/描述：',
    programCode: '程序編碼',
    programDescription: '程序描述',
    programType: '程序類型',
    programPath: '程序路徑',
    programDependence: '程序依賴',
    programAttribute: '程序屬性'
  },
  fastCode: {
    /* 快速编码 */ groupCodeDescription: '組編碼/描述：',
    groupCode: '組編碼',
    groupDescription: '組描述',
    isEnabled: '是否有效',
    childCode: '子編碼',
    childDescription: '子描述',
    order: '順序'
  },
  systemParameter: {
    /* 系统参数 */ parametricDescription: '參數/描述：',
    cell: '單元：',
    parameterCode: '參數編碼',
    parameterDescription: '參數描述',
    parameterValue: '參數值',
    parameterNumber: '參數編號',
    parameterdescription: '參數描述'
  },
  errorMessage: {
    /* 错误消息定义 */ errorMessageIDDescription: '錯誤消息ID/描述：',
    errorMessageDescription: '錯誤消息描述',
    errorMessageID: '錯誤消息ID'
  },
  taskScheduling: {
    /* 任务调度 */ taskName: '任務名稱：',
    cellId: '單元ID',
    BeanName: 'Bean名稱',
    cronExpression: 'Cron表達式',
    mannerExecution: '執行方法',
    failureSuspend: '失敗暫停',
    parameter: '參數',
    successSuspendRemove: '成功暫停/刪除',
    remark: '備註',
    taskStatus: '任務狀態',
    status: '狀態',
    pauseAfterFailure: '失敗後暫停',
    pauseAfterSuccessRemove: '成功後暫停/刪除'
  },
  maintenancePline: {
    /* 产线维护 */ lineCodeDescription: '產線編碼/描述：',
    lineType: '產線類型：',
    productionScheduleType: '排產類型：',
    lineCode: '產線編碼',
    lineDescription: '產線描述',
    factory: '工廠',
    workCenter: '工作中心',
    activation: '稼動率',
    lockNumber: '鎖定數量',
    sort: '排序'
  },
  maintenanceStation: {
    /* 工位维护 */ stationCodeDescription: '工位編碼/描述：',
    productionLine: '產線：',
    productionLinet: '產線',
    stationCode: '工位編碼',
    stationDescription: '工位描述',
    shortNumber: '短序號',
    productionLineSegmentCode: '產線分段碼',
    divisionNumber: '分工位號',
    stationRelatedAttributes: '工位相關屬性',
    stationCombinationCode: '工位組合碼',
    ipAddress: 'IP地址',
    reportWorkingProcedureNumber: '報工工序號',
    workDescription: '報工描述',
    downTime: '故障時間',
    serviceTime: '保養時間',
    runTime: '運行時間',
    taktImage: '理想節拍',
    scrollingMessage: '滾動信息',
    methodLis: '數據採集方式',
    productType: '產品類型',
    cellId: '單元ID',
    scadaExample: 'SCADA實例',
    exampleFlowChart: '流程圖實例',
    automatedLogicalInstance: '自動化邏輯實例',
    interfaceConfigurationEnvironment: '接口環境配置',
    sort: '排序',
    OnlineStation: '上線工位',
    offStation: '下線工位',
    repairStation: '返修工位',
    stationDisplayedIndependently: '工位獨立顯示',
    CombinatorialCode: '組合編碼',
    optionPeriod: '選擇權限',
    orderSelectionAuthority: '訂單選擇權限',
    segmentedEncoding: '分段編碼',
    onlineStationCode: '上綫工位號',
    reportWorkingProcedureDescription: '報工工序描述',
    attribute1: '屬性1',
    attribute2: '屬性2',
    attribute3: '屬性3',
    attribute4: '屬性4',
    attribute5: '屬性5'
  },
  maintenanceDrive: {
    /* 驱动维护 */ driverNameSeries: '驅動名稱/區域：',
    driverName: '驅動名稱',
    area: '區域'
  },
  maintenanceType: {
    /* 型号维护 */ driverNameType: '驅動名稱/型號：',
    driverName: '驅動名稱',
    type: '型號'
  },
  tagsDefined: {
    /* 标签定义 */ productionLineId: '產線ID',
    exampleCode: '實例編碼',
    exampleDescription: '實例描述',
    exampleGroupCode: '實例組編碼',
    exampleGroupDes: '實例組描述',
    driveProgram: '驅動程序',
    type: '型號',
    exampleAttribute: '實例屬性',
    stationCode: '工位號',
    simpleDb: '大數據存儲',
    simulation: '是否模擬'
  },
  monitor: {
    /* 实例监控 */ emptyMonitoring: '清空監控',
    stopMonitoring: '停止監控',
    startupMonitoring: '啟動監控',
    addTag: '添加監控',
    startupKafkaMonitoring: '啟動KAFKA監控',
    exampleCode: '實例編碼',
    exampleDescription: '實例描述',
    driveProgram: '驅動程序',
    simulation: '是否模擬',
    timeDuration: '持續時間(分)',
    status: '狀態',
    heartbeat: '心跳',
    addWatch: '添加監控',
    labelCode: '標籤代碼',
    labelDescription: '標籤描述',
    currentValue: '當前值',
    labelGroups: '標籤組',
    time: '時間',
    feature: '功能',
    message: '消息',
    labelValue: '標籤值',
    labelAttr: '標籤屬性',
    groupCode: '組編碼',
    groupDescription: '組描述',
    regionName: '區域名稱',
    areaNumber: '區域編號',
    regionPosition: '區域位置',
    dataType: '數據類型',
    initialAddress: '起始地址',
    dataLength: '數據長度',
    length: '長度',
    bit: '位',
    opcRealAddress: ' OPC真實地址',
    opcVirtualAddress: 'OPC模擬地址',
    readWriteAccessPermission: '讀寫權限',
    dataPermissions: '數據權限',
    changePush: '變化推送',
    snapshotStorage: '快照存儲',
    otherAttributes: '其他屬性',
    convertFormat: '轉換格式',
    saveOnChange: '變化時保存',
    warningProcessing: '預警處理',
    warningUpperLimit: '預警上限',
    lowerWarningLimit: '預警下限',
    disDataTypes: '區分數據類型',
    openOPCUA: '開放OPCUA',
    push: '是否推送',
    dataConversion: '數據轉換',
    messageDriven: '驅動消息',
    emptyMessage: '清空消息',
    refresh: '刷新',
    write: '寫入',
    attr: '屬性',
    selectProduction: '請選擇產線',
    selectCell: '請選擇單元',
    codeOrDes: '實例編碼或描述',
    addMon: '添加監控標籤',
    queryInstance: '請先查詢實例，再啓動監控',
    serviceCell: '請先維護服務、單元信息',
    selectTAG: '請選擇一個TAG',
    refreshException: '刷新異常'
  },
  logicattr: {
    /* 逻辑属性 */ menuType: '菜單類型',
    groupCode: '組編碼',
    groupDescription: '組描述',
    childCode: '子編碼',
    childDescription: '子描述',
    attribute1: '屬性1',
    attribute2: '屬性2',
    attribute3: '屬性3',
    attribute4: '屬性4',
    attribute5: '屬性5',
    logicalPropertyMaintenance: '邏輯屬性維護'
  },
  logicfunc: {
    /* 自动化逻辑属性 */
    automatedLogicalPropertyConfiguration: '自動化邏輯屬性配置',
    logicalProgram: '邏輯程序',
    schedulerCode: '調度程序代碼',
    schedulerDescription: '調度程序描述',
    setControllers: '控制器集合',
    subproject: '子項目',
    value: '值',
    attribute1: '屬性1',
    attribute2: '屬性2',
    attribute3: '屬性3',
    attribute14: '屬性4',
    attribute5: '屬性5',
    isEnabled: '是否有效',
    attributeGroup: '屬性組',
    groupCode: '組編碼',
    groupDescription: '組描述',
    subprojectDescription: '子項目描述'
  },
  modmain: {
    /* 主流程图模板维护 */ templateCode: '模板代碼：',
    templateDescription: '模板描述：',
    templateCodet: '模板代碼',
    templateDescriptiont: '模板描述',
    programDriven: '程序驅動'
  },
  modfunctionm: {
    /* 函数维护 */ functionCode: '函數編碼：',
    functionCodet: '函數編碼',
    funcname: '函數名：',
    funcnamet: '函數名',
    functionDescription: '函數描述',
    csprojCode: '項目編碼:',
    csprojCodet: '項目編碼',
    functionversion: '函數版本',
    functionversiondes: '版本説明'
  },
  modcsproj: {
    /* 工程维护 */ csprojCode: '項目編碼：',
    csprojCodet: '項目編碼',
    csprojdes: '項目描述：',
    csprojdest: '項目描述',
    csprojtargetframework: '目標框架',
    csprojreference: '項目引用',
    csprojversion: '項目版本',
    csprojversiondes: '版本説明',
    csprojpathWin: '文件路徑Win',
    csprojpathLinux: '文件路徑Linux'
  },
  mainmain: {
    /* 主流程图维护 */ mainProcessCode: '主流程編碼：',
    mainProcessDescription: '主流程描述：',
    basicAttribute: '基礎屬性',
    processTemplate: '流程模板',
    station: '工位',
    mainProcessCodet: '主流程編碼',
    mainProcessDescriptiont: '主流程描述',
    taskNumberPrefix: '任務編號前綴',
    programProperties: '程序屬性',
    processFlow: '流程圖標',
    examplesCollections: '實例集合',
    triggerPoint: '觸發點位',
    triggerPointValue: '觸發點位值',
    taskTriggeringMode: '觸發任務方式',
    pollingTime: '輪詢時間',
    conditionsSet: '條件組',
    conditionGroupDescription: '條件組描述',
    conditionsDescribed: '條件描述',
    monitoringPoint: '監控點位',
    setUpInstructions: '成立說明',
    processCodet: '流程編碼',
    processDescriptiont: '流程描述',
    taskInfo: '任務信息',
    view: '查看',
    dataException: '初始化數據異常',
    selectProduction: '請選擇產線與工位',
    taskNumber: '任務編號',
    creationDate: '創建時間',
    detailInfo: '詳細信息',
    pendEvents: '待處理事件',
    strDrawFlow: '拼命繪製流程圖中',
    attrMomitor: '屬性監控',
    abnormalData: '初始化模式數據異常',
    amplify: '放大',
    reduce: '縮小',
    sureDelete: '確定要刪除當前選中',
    step: '項步驟嗎?',
    cancelledDeletion: '已取消删除',

    log: '日誌',
    attr: '基礎屬性',
    inputParams: '輸入參數',
    outParams: '輸出參數',
    subprocess: '所屬子流程',
    stepDes: '步驟描述',
    sort: '排序',
    stepType: '步驟類型',
    startStep: '開始步驟',
    endStep: '結束步驟',
    generalSteps: '常規步驟',
    methodName: '方法名',
    nextOK: 'OK時下一個步驟',
    stepToNg: 'NG對應的步驟',
    afterLoop: '循環結束後下一步',
    selectNext1: '選擇1下一步步驟',
    selectNext2: '選擇2下一步步驟',
    selectNext3: '選擇3下一步步驟',
    selectNext4: '選擇4下一步步驟',
    selectNext5: '選擇5下一步步驟',
    cancelNext: '取消下一步步骤',
    retryCount: '重試次數',
    limiTime: '限制時間(ms)',
    colorStateOK: 'OK狀態時顏色',
    colorStateNG: 'NG狀態時顏色',
    colorStateCancel: '取消狀態時顏色',
    colorStateRetry: '重試狀態時顏色',
    initColor: '初始顏色',
    code: '代碼',
    conditionGroup: '條件組',
    notHave: '無',
    endAll: '結束全部',
    logQuery: '日誌查詢',
    logType: '日誌類型',
    logCode: '日誌代碼',
    date: '日期',
    logInfo: '日誌消息',

    stepJump: '步驟跳轉',
    radio: '單選',
    index: '序號',
    stepName: '步驟名稱',
    selectFlow: '请先选择流程',
    jumpSuccessful: '跳转成功',

    subDes: '子流程描述',
    processType: '流程類型',
    startSub: '開始子流程',
    endSub: '結束子流程',
    regularSub: '常規子流程',
    nextSub: '下一個子流程',
    subEnd: '子流程結束',
    afterEnd: '當前子流程結束後結束整個流程',
    subFunctionDll: '子流程FunctionDll',
    subAttr: '子流程程序屬性',
    controlIcon: '控件圖標',
    controlWidth: '控件寬度',
    controlHeight: '控件高度',
    colorStateWait: '等待狀態時顏色',
    cannotBeEmpty: '不能爲空',
    enterNumber: '請輸入數字值',
    enterSub: '請輸入子流程編碼',
    enterDes: '請輸入子流程描述',
    nextSelect: '請選擇下一個子流程',
    enterFunction: '請輸入子流程FunctionDll',
    addSub: '子流程新增成功',
    addError: '子流程新增異常',
    editSuccess: '子流程修改成功',
    editError: '子流程修改異常'
  },
  taskList: {
    /* 任务列表*/ TaskSource: '任務來源:',
    taskNumber: '任務號:',
    NumberOfTasks: '任務數量',
    UrgentOrder: '急單',
    ScheduleDate: '計畫日期',
    TargetCuttingMachine: '目標切割機',
    CutType: '切割類型',
    TaskCreationTime: '任務創建時間',
    CrownBlockAutomatic: '天車自動',
    CrownBlockStatus: '天车狀態',
    CuttingStatus: '切割狀態',
    WhetherToCutOrNot: '是否切割',
    SortOrNot: '是否分揀',
    ValidOrNot: '是否有效',
    SteelPlateModel: '鋼板型號:',
    DXFcode: 'DXF 編碼:',
    DXFFiles: 'DXF 文件:',
    NCcode: 'NC 編碼:',
    NCFiles: 'NC 文件:',
    TaskStatus: '任務狀態:',
    validIdentification: '有效標識：'
  },
  cuttingZone: {
    // 切割区
    CacheBit: '緩存比特',
    TaskNumber: '任務號',
    SteelPlateModel: '鋼板型號',
    SteelPlateSource: '鋼板來源',
    CacheStatus: '緩存狀態',
    DelayTime: '到緩時間',
    ManualOperation: '手工操作',
    CuttingPosition: '切割比特',
    CuttingMachineStatus: '切割机狀態',
    cutNCName: '切割文件名稱',
    PROGRAMNO: '程式號',
    schedule: '進度',
    DeviceName: '設備名稱',
    AlarmClassification: '報警分類',
    AlarmLevel: '報警級別',
    AlarmMessage: '報警資訊',
    AlarmOccurrenceTime: '報警發生時間',
    startTime: '開始時間',
    endTime: '結束時間',
    PlannedCuttingTime: '計畫切割時間',
    ProgramUploadTime: '程式上傳時間',
    CurrentWorkstation: '當前工位',
    TargetWorkstation: '目標工位',
    StockCode: '庫比特號',
    StockCount: '庫存量',
    MaterialModel: '物料型號',
    ExecutionOrder: '執行順序',
    AdjustTheOrder: '調整順序',
    WarehousingTaskNumber: '入庫任務號',
    WarehousingTime: '入庫時間',
    LocationSorting: '庫比特排序',
    ZAxisCoordinates: 'Z軸座標',
    IsItLocked: '是否鎖定',
    LockTaskNumber: '鎖定任務號'
  },
  sortingArea: {
    // 分拣区
    SortingStation: '分揀工位',
    SortType: '分揀類型',
    PlannedQuantity: '計畫數量',
    ActualQuantity: '實際數量',
    SheetName: '圖紙名稱',
    MaterialFrameNumber: '料框號',
    MaterialFrameParts: '料框零件',
    Number: '數量',
    Status: '狀態',
    MaterialFrameDetails: '料框明細',
    AGVStatus: 'AGV狀態',
    StationInspectionStatus: '過站檢查狀態',
    WorkReportingStatus: '報工狀態',
    AbnormalWorkReporting: '報工异常',
    TransitDetails: '過站明細',
    ProductionTaskNumber: '生產任務號',
    CurrentStationNumber: '當前工位號',
    SourceStaus: '來源任務號',
    Cutterbar: '切割機',
    arriveDate: '到達時間',
    leaveDate: '離開時間',
    StationConsumptionTime: '工位消耗時間',
    PalletNumber: '託盤號',
    ReportWorkOrNot: '是否報工',
    MaterialFrameTaskNumber: '料框任務號',
    PartLength: '零件長',
    PartWidth: '零件寬',
    PartType: '零件類型',
    PartThickness: '零件厚',
    PartWeight: '零件重',
    PlannedSortingTime: '計畫分揀時間',
    SortingStationNumber: '分揀工位號',
    PartNumber: '零件編號',
    PartBarcodeNumber: '零件條碼號',
    PartDrawingNumber: '零件圖號',
    partMaterialNumber: '零件物料號',
    PlannedTimeSpent: '計畫花費時間/s',
    CurrentSortingStation: '當前分揀工位',
    TargetTransmissionStation: '目標傳輸工位',
    CompulsoryRelease: '強制放行',
    MandatoryReportingInterface: '強制上報介面',
    IsAutomaticSortingCompleted: '是否自動分揀完成',
    CurrentSortingDXFDrawing: '當前分揀DXF圖紙',
    CurrentSortingSteelPlateModel: '當前分揀鋼板型號',
    ForceComplete: '強制完成',
    WorkReportingInterfaceAddress: '報工介面地址',
    DXFFileName: 'DXF檔案名稱',
    GNCFileName: 'GNC檔案名稱',
    PersonInChargeOfWorkReporting: '報工責任人',
    ClickToReportWork: '點擊報工'
  },
  equipment: {
    stationCode: '工位號',
    deviceCode: '設備編號',
    DeviceName: '設備名稱',
    PartNumber: '零件編號',
    PartName: '零件名稱',
    TheoreticalLifespan: '理論壽命',
    ServiceLife: '使用壽命',
    Status: '狀態',
    PartDescription: '零件描述',
    OriginalPartNumber: '原零件號',
    OriginalPartName: '原零件名稱',
    PartNumberAfterReplacement: '更換後零件號',
    PartNameAfterReplacement: '更換後零件名稱',
    ReplacementTime: '更換時間'
  },
  mfTable: {
    // 机型基础
    creator: '創建者',
    creationTime: '創建時間',
    ModifiedBy: '修改者',
    time: '時間',
    link: '鏈接',
    mainMaterialCode: '物料編碼',
    mainMaterialDescription: '物料描述',
    partDrawingNumber: '零件圖號',
    partMaterialNumber: '零件物料號',
    model_type: '型號',
    length: '長',
    width: '寬',
    thick: '厚',
    height: '高',
    weight: '重',
    materialQuality: '材質',
    cuttingMachine: '材質(切割機)',
    dataSources: '數據來源',
    cuttingXCoordinate: '切割x座標',
    cuttingyCoordinate: '切割y座標',
    enableFlag: '有效標識：'
  },
  smaterialbox: {
    // 分拣箱基础
    creator: '創建者',
    creationTime: '創建時間',
    modifiedBy: '修改者',
    time: '時間',
    materialFrameCoding: '料框編碼',
    materialFrameName: '料框名稱',
    materialFrameType: '料框類型',
    AGVLocationCode: 'AGV位置碼'
  },
  qualityData: {
    // 质量数据采集
    creator: '創建者',
    creationTime: '創建時間',
    modifiedBy: '修改者',
    time: '時間',
    stationId: '工位ID',
    sourceOfQualityData: '質量數據來源',
    collectionItems: '採集項目',
    markOfConformity: '合格標誌',
    groupNumber: '組號',
    groupDescription: '組描述',
    positionOfColumn: '列位置',
    columnSorting: '列排序',
    measurementObject: '測量對象',
    entryName: '項目名稱',
    collectionProjectUnit: '採集項目單位',
    standardValue: '標準值',
    lowerLimitingValue: '下限值',
    upperLimitValue: '上限值',
    saveOrNot: '是否保存'
  },
  taskTable: {
    // 生产任务表
    taskNumber: '任務號',
    taskSource: '任務來源',
    serialNumber: '序列號',
    batchNumber: '批次號',
    productionMode: '製程分類',
    taskType: '任務類型',
    materialCode: '物料編碼',
    materialDescription: '物料描述',
    partDrawingNumber: '零件圖號',
    partMaterialNumber: '零件物料號',
    modelY: '型號',
    length: '長',
    width: '寬',
    thick: '厚',
    height: '高',
    weight: '重',
    materialQuality: '材質',
    cuttingMachine: '材質(切割機)',
    xCoordinate: 'x座標',
    yCoordinate: 'y座標',
    DXFFileName: 'DXF文件名稱',
    DXFFileURLPath: 'DXF路徑',
    JSONFileName: 'JSON文件名稱',
    JSONFileURLPath: 'JSON路徑',
    JSONFileURLTxt: 'JSON文本',
    JSONData: 'JSON數據',
    NCFileName: 'NC文件名稱',
    NCFileURLPath: 'NC路徑',
    scheduledTime: '切割計劃時間',
    sort: '任務排序',
    taskStatus: '任務狀態',
    taskMessage: '任務消息',
    cutterbar: '切割機',
    cutterbarType: '切割機類型',
    cuttingPlanTime: '切割計劃時間',
    shotBlastingOrNot: '是否已抛丸',
    automaticCrownBlockOrNot: '天車狀態',
    whetherToAutotheboard: '是否自動取板',
    whetherToSprayCode: '是否噴碼',
    whetherToAutomaticallyCut: '是否切割',
    automaticSortingOrNot: '是否分揀',
    isTheCarCentered: '橫移小車對中',
    onSiteSiteMaterials: '場內/外物料',
    stationPosition: '工位位置',
    reportWorkOrNot: '是否完成報工',
    area: '面次',
    NoBowPlate: '是否首板'
  },
  surplusMaterialTable: {
    // 生产任务余料表
    description: '描述',
    mainTaskID: '主任務ID',
    plusTaskNumber: '餘料編號',
    plusRemnantMat: '餘料材質',
    plusRemnantThick: '餘料厚度',
    plusRemnantLength: '餘料長度',
    plusRemnantWidth: '餘料寬度',
    plusRemnantWeight: '餘料重量'
  },
  eventRecordForm: {
    // 事件记录表
    mainTaskID: '主任務ID',
    user: '用戶',
    eventType: '事件類型',
    eventName: '事件名稱',
    sendContent: '發送內容',
    acceptContent: '接受內容',
    messageCODE: '消息CODE',
    messageContent: '消息內容',
    eventStatus: '事件狀態',
    startTime: '開始時間',
    endTime: '結束時間',
    timeConsuming: '消耗時間(秒)',
    remake: '備註',
    condition: ' 條件',
    timeoutLimit: ' 超時限制（ms）',
    lastUpdateTime: ' 最後更新時間'
  },
  sortingResults: {
    // 分拣解析结果
    mainTaskID: '主任務ID',
    stationCode: '工位號',
    partBarcodeNumber: '零件條碼',
    robotCoding: '機器人編碼',
    partCode: '零件編碼',
    partDrawingNumber: '零件圖號',
    partMaterialNumber: '零件物料號',
    sortSequenceNumber: '分揀順序號',
    nedCuttingTime: '切割時間',
    routingCode: '工藝路線編碼',
    sprayCodeXCoordinate: '噴碼頂點X座標',
    sprayCodeYCoordinate: '噴碼頂點Y座標',
    PartType: '零件類型',
    sortResultCode: '分揀結果代碼',
    exceptionInformation: '消息/異常信息',
    NCFileName: 'NC 文件名稱',
    length: '長',
    width: '寬',
    thick: '厚',
    weight: '重',
    nextRouting: '零件加工工序',
    reservedPartProperties: '零件配送地點',
    timeRequiredForSorting: '分揀所需時間(毫秒)',
    startTime: '開始時間',
    endTime: '結束時間',
    sortStartTime: '分揀開始時間',
    sortEndTime: '分揀結束時間'
  },
  sortingBoxResults: {
    // 料框结果
    materialFramePosition: '料框位置',
    materialFrameBarcode: '料框條碼',
    taskNumber: '任務號',
    partBarcodeNumber: '零件條碼',
    partCode: '零件編碼',
    partType: '零件類型',
    resultCode: '結果代碼',
    stackingMessage: '碼垛消息/異常',
    stackingTime: '碼垛時間',
    stackingStartTime: '碼垛開始時間',
    stackingEndTime: '碼垛結束時間'
  },
  realTimeTable: {
    // 工位MIS实时状态表
    username: '用戶名',
    arriveDate: '到達時間',
    leaveDate: '離開時間',
    stationCode: '工位號',
    stationDescription: '工位描述',
    stationProperties: '工位屬性',
    stationStatus: '工位狀態',
    taskNumber: '任務號',
    taskSource: '任務來源',
    serialNumber: '序列號',
    batchNumber: '批次號',
    palletNumber: '托盤號',
    palletUsageTimes: '使用次數',
    maximumPalletUsage: '最高使用次數',
    trayAlarmIdentification: '托盤報警標識',
    mainMaterialCode: '物料編碼',
    mainMaterialDescription: '物料描述',
    partDrawingNumber: '零件圖號',
    partMaterialNumber: '零件物料號',
    model_type: '型號',
    length: '長',
    width: '寬',
    thick: '厚',
    weight: '重',
    materialQuality: '材質',
    cuttingMachine: '材質(切割機)',
    uptime: '上線時間',
    workpieceInProcessTime: '工件在制時間(秒)',
    idealBeat: '理想節拍(秒)',
    actualBeat: '實際節拍(秒)',
    shifCode: '班次代碼',
    shifDes: '班次描述',
    sourceStationNumber: '來源工位號',
    stationErrorMessage: '工位錯誤信息',
    DXFName: 'DXF名稱',
    JSONName: 'JSON名稱',
    NCName: 'NC名稱'
  },
  wmsCbTable: {
    // WMS天车基础表
    libraryArea: '庫區域',
    crownBlockCode: '天車編碼',
    processName: '流程名稱',
    subProcess: '子流程',
    crownBlockDescription: '天車描述',
    crownBlockNumber: '天車序號',
    crownBlockAttributes: '天車屬性',
    crownBlockType: '天車類型',
    groupCollection: '管轄庫位組集合',
    originXCoordinate: '原點X座標',
    originYCoordinate: '原點Y座標',
    originZCoordinate: '原點Z座標',
    farPointXCoordinate: '遠點X座標',
    farPointYCoordinate: '遠點Y座標',
    farPointZCoordinate: '遠點Z座標',
    safeDistance: '安全距離',
    warehouseLocationGroupCode: '庫位組編碼',
    locationGroupDescription: '庫位組描述',
    warehouseLocationNumber: '庫位號',
    warehouseLocationDescription: '庫位描述',
    inventoryLocationSorting: '庫位排序',
    manageInventoryOrNot: '是否管理庫存',
    isTheModelFixed: '是否固定型號',
    modelID: '型號ID',
    xCoordinate: 'X座標',
    YCoordinate: 'Y座標',
    ZCoordinate: 'Z座標',
    isTheZAxisDefinedValue: 'Z軸是否爲定義值',
    zAxisDynamicCalculationMethod: 'Z軸動態計算方法',
    inventory: '庫存量',
    minimumInventory: '最小庫存',
    maximumInventory: '最大庫存',
    loopCode: '循環代碼',
    maximumLimitExpirationTime: '最大限定超期時間(分鐘)',
    warehouseLocationStatus: '庫位狀態',
    locationType: '庫位類型',
    taskNumber: '任務號',
    taskSource: '任務來源',
    serialNumber: '序列號',
    batchNumber: '批次號',
    ZAxisCoordinates: 'Z軸座標',
    isInventoryLocked: '庫存是否鎖定',
    warehousingMethod: '入庫方式',
    taskType: '任務類型',
    findTheYCoordinate: '尋中Y座標',
    isTheSearchCompleted: '是否完成尋中',
    warehousingTime: '存放天數',
    isInventoryAlarm: '是否庫存超期報警',
    mainTaskID: '任務ID',
    model_type: '型號',
    locationID: '庫位ID',
    taskExecutionStatus: '任務執行狀態',
    taskExecutionErrorMessage: '任務執行錯誤信息',
    startingStorageLocation: '起始庫位',
    targetInventoryLocation: '目標庫位',
    taskStartTime: '任務開始時間',
    taskEndTime: '任務結束時間',
    timeConsuming: '消耗時間(秒)',
    isTheModelChecked: '是否檢查型號',
    checkTheRollerTableTime: '檢查輥道時間',
    checkModelTime: '檢查型號時間',
    checkModelResults: '檢查型號結果',
    crownBlockTaskType: '天車任務類型',
    stepCode: '步驟代碼',
    stepName: '步驟名稱',
    stepStatus: '步驟狀態',
    restrictionStepID: '限制步驟ID',
    restrictionStepName: '限制步驟名稱',
    restrictionStepStatus: '限制步驟狀態',
    isTheRollerTableChecked: '是否檢查輥道'
  },
  meStationQuality: {
    stationCode: '工位號',
    taskNumber: '任務號',
    serialNumber: '序列號',
    groupNumber: '組號',
    groupDescription: '組描述',
    positionOfColumn: '列位置',
    collectionItems: '採集項目',
    columnSorting: '列排序',
    measurementObject: '測量對象',
    entryName: '採集項目名稱',
    collectionProjectUnit: '採集項目單位',
    standardValue: '標準值',
    lowerLimitingValue: '下限值',
    upperLimitValue: '上限值',
    collectValues: '子合格標誌',
    retrospectiveTime: '追溯時間'
  },
  center: {
    /* 中心维护 */ centreNumberDescription: '中心編號/描述：',
    centreCode: '中心編碼',
    centreDescription: '中心描述',
    mainCard: '主網卡',
    attachCard1: '附網卡1',
    attachCard2: '附網卡2',
    attachCard3: '附網卡3'
  },
  server: {
    /* 服务维护 */ centre: '中心',
    serviceTagDescription: '服务编号/ 描述：',
    serviceCode: '服务编码',
    serviceDescription: '服务描述',
    mainCard: '主網卡',
    attachCard1: '附網卡1',
    attachCard2: '附網卡2',
    attachCard3: '附網卡3',
    esbServer: 'ESB服務器',
    system: '系統'
  },
  cell: {
    /* 单元维护 */ cellNameDescription: '單元名稱/描述：',
    serve: '服務',
    cellName: '單元名稱',
    cellDescription: '單元描述',
    mirrorName: '鏡像名稱',
    webPort: 'Web端口',
    mqttPort: 'Mqtt端口',
    opcUaPort: 'OPC UA端口',
    cellPort: '單元端口',
    startOpcUa: '啟動OPC UA'
  },
  interf: {
    /* 接口维护 */ interfaceCodeDescription: '接口編碼/ 描述：',
    interfaceCode: '接口代碼',
    interfaceDescription: '接口描述',
    sourceSystem: '來源系統',
    targetSystem: '目標系統',
    interfaceMeans: '接口方式',
    testAddress: '測試地址',
    officialAddress: '正式地址',
    testReturnValue: '測試返回值',
    interfaceNumber: '接口編號',
    productionAddress: '生產地址',
    remark: '備註',
    cell: '單元',
    parameterValue: '傳參值'
  },
  file: {
    /* 文件管理 */ autoDesk: '文件夾目錄',
    fileName: '文件名稱',
    fileSize: '文件大小'
  },
  celldeploy: {
    /* 单元部署 */ serve: '服務：',
    uploadMirroring: '上傳鏡像',
    installAll: '全部安裝',
    installSelected: '選擇安裝',
    unitName: '單元名稱',
    unitDescription: '單元描述',
    cellMirrorName: '單元鏡像名稱',
    installMirrorName: '安裝時鏡像名稱',
    serviceMirroringInstall: '服務鏡像安裝'
  },
  cellprogupd: {
    /* 单元程序更新 */ serve: '服務：',
    massUpdate: '批量更新',
    selectiveupdate: '選擇更新',
    updateObject: '更新對象',
    updateMode: '更新方式',
    file: '文件',
    serialNumber: '序號',
    cell: '單元',
    procedure: '程序',
    fileName: '文件名稱',
    fileType: '文件類型',
    fileSize: '文件大小'
  },
  progbatchupd: {
    /* 单元程序更新批量 */ serve: '服務：',
    massUpdate: '批量更新',
    selectiveupdate: '選擇更新',
    updateObject: '更新對象',
    updateMode: '更新方式',
    file: '文件',
    serialNumber: '序號',
    cell: '单元',
    procedure: '程序',
    fileName: '文件名稱',
    fileType: '文件類型',
    fileSize: '文件大小'
  },
  /** ************************************************************/
  /* MES定义*/

  /** ************************************************************/
  /* PMC定义*/
  pmcquery: {
    /* 查询条件 */ creationDate: '時間範圍：',
    workCenterCode: '車間：',
    prodLineCode: '產線：',
    stationCode: '工位：',
    makeOrder: '訂單：',
    vin: 'VIN：',
    vinFlag: 'VIN最終標識：',
    workStatus: '生產狀態：',
    setSign: '拉入/出狀態：',
    zkName: '下發訂單類型：',
    lastMakeOrder: '最後訂單號：',
    stationDeviceType: '設備類型：',
    deviceSectionCode: '工位與擰緊槍匹配：',
    qualityFrom: '數據來源：',
    screenSetType: '大屏類型：',
    andonType: '安燈類型：',
    andonCardStatus: '工位牌狀態：',
    andonControlName: '工位控制名稱：',
    happenDate: '發生時間：',
    resetFlag: '是否復位：',
    andonVoiceSerial: '設備序列號：',
    andonVoiceDes: '音響名稱：',
    andonMusicDes: '音樂名稱：',
    enableFlag: '有效標識：'
  },
  flowonline: {
    /* 线首 */ creationDate: '創建時間',
    flowOnlineId: 'ID',
    workCenterCode: '車間',
    prodLineCode: '產綫',
    stationCode: '工位',
    makeOrder: '訂單',
    serialNum: '工件編號',
    dms: 'DMS',
    itemProject: '行項目',
    vin: 'VIN',
    smallModelType: '型號',
    mainMaterialCode: '物料編號',
    materialColor: '顔色',
    materialSize: '尺寸',
    shaftProcNum: '擰緊程序號',
    staffId: '操作者',
    palletNum: '托盤號',
    engineNum: '發動機',
    driverWay: '驅動形式',
    publishNumber: '發佈順序號',
    vinFlag: 'VIN最終標識',
    repairFlag: '返修標識',
    enableFlag: '有效標識'
  },
  stationmo: {
    /* 工位生产订单 */ creationDate: '創建時間',
    stationMoId: 'ID',
    prodLineCode: '產綫',
    stationCode: '工位',
    makeOrder: '訂單',
    targetMakeOrder: '目標訂單',
    dms: 'DMS',
    itemProject: '行項目',
    moWorkOrder: '生產訂單順序',
    workStatus: '生產狀態',
    setSign: '拉入/出狀態',
    setDate: '時間',
    setMarks: '備注',
    preSetOutBut: '預約拉出',
    cancelPreSetOutBut: '取消預約拉出',
    setOutBut: '直接拉出',
    preSetInBut: '預約拉入',
    cancelPreSetInBut: '取消預約拉入',
    setInBut: '直接拉入'
  },
  modownstatus: {
    /* 下发订单状态 */ creationDate: '創建時間',
    lastUpdateDate: '修改時間',
    zkName: '下發訂單類型',
    lastMakeOrder: '最後訂單號'
  },
  stationflow: {
    /* 过站 */ creationDate: '創建時間',
    stationFlowId: 'ID',
    workCenterCode: '車間',
    prodLineCode: '產綫',
    stationCode: '工位',
    stationDes: '工位描述',
    proceduceCode: '報工工序號',
    proceduceDes: '報工工序描述',
    serialType: '條碼類型',
    serialNum: '工件編號',
    palletNum: '托盤號',
    staffId: '操縱者',
    makeOrder: '訂單號',
    dms: 'DMS',
    itemProject: '行項目',
    vin: 'VIN',
    smallModelType: '型號',
    mainMaterialCode: '物料編號',
    materialColor: '顔色',
    materialSize: '尺寸',
    shaftProcNum: '擰緊程序號',
    repair_flag: '返修標識',
    qualitySign: '縂合格標志',
    dataCollectWay: '數據采集方式',
    arriveDate: '到達時間',
    leaveDate: '離開時間',
    costTime: '消耗時間',
    shifCode: '班次代碼',
    shifDes: '班次描述',
    setSign: '拉入/出狀態',
    checkStatus: '過站校驗狀態',
    checkCode: '過站校驗代碼',
    checkMsg: '過站校驗描述',
    flowStautsCode: '過站狀態',
    assemblyFinishFlag: '是否校驗合件完成',
    mesAviFlag: '是否上傳MES',
    upFlag: '上傳標識',
    upNgCode: '上傳錯誤代碼',
    upNgMsg: '上傳錯誤消息',
    vinAviFlag: '是否上傳VIN打刻',
    downVinFlag: '下發標識(VIN打刻)',
    downVinNgCode: '下發錯誤代碼(VIN打刻)',
    downVinNgMsg: '下發錯誤消息(VIN打刻)',
    jzAviFlag: '是否上傳油液加注',
    downJzFlag: '下發標識(油液加注)',
    downJzNgCode: '下發錯誤代碼(油液加注)',
    downJzNgMsg: '下發錯誤消息(油液加注)',
    lesAviFlag: '是否上傳LES',
    downLesFlag: '下發標識(LES)',
    downLesNgCode: '下發錯誤代碼(LES)',
    downLesNgMsg: '下發錯誤消息(LES)',
    downAgvFlag: '下發標識(AGV)',
    downAgvNgCode: '下發錯誤代碼(AGV)',
    downAgvNgMsg: '下發錯誤消息(AGV)',
    enableFlag: '有效標識'
  },
  meStationFlow: {
    stationCode: '工位號',
    stationDes: '工位描述',
    taskNumber: '任務號',
    taskSource: '任務來源',
    serialNumber: '序列號',
    batchNumber: '批次號',
    palletNum: '托盤號',
    mainMaterialCode: '物料編碼',
    mainMaterialDescription: '物料描述',
    partDrawingNumber: '零件圖號',
    partMaterialNumber: '零件物料號',
    model_type: '型號',
    length: '長',
    width: '寬',
    thick: '厚',
    weight: '重',
    materialQuality: '材質',
    cuttingMachine: '材質(切割機)',
    releaseMethod: '放行方式',
    releaseUser: '放行人',
    releaseInstructions: '放行說明',
    releaseTime: '放行時間',
    arriveDate: '到達時間',
    leaveDate: '離開時間',
    costTime: '消耗時間(單位秒)',
    shifCode: '班次代碼',
    shifDes: '班次描述',
    onlineWorkstationIdentification: '上線工位標識',
    offlineWorkstationIdentification: '下線工位標識',
    tagQualitySignId: '合格標誌',
    reportWorkOrNot: '是否完成報工',
    workReportInspectionInformation: '報工檢查信息',
    upFlag: '上傳標識',
    upNgCode: '上傳錯誤代碼',
    upNgMsg: '上傳錯誤消息'
  },
  stationstatus: {
    /* 当前工位实时工件信息 */ creationDate: '創建時間',
    stationStatusId: 'ID',
    workCenterCode: '車間',
    prodLineCode: '產綫',
    stationCode: '工位',
    stationDes: '工位描述',
    stationStatus: '工位狀態',
    emptyBanFlag: '是否為空板',
    serialNum: '工件編號',
    palletNum: '托盤號',
    staffId: '操縱者',
    makeOrder: '訂單號',
    dms: 'DMS',
    itemProject: '行項目',
    vin: 'VIN',
    smallModelType: '型號',
    mainMaterialCode: '物料編號',
    materialColor: '顔色',
    materialSize: '尺寸',
    shaftProcNum: '擰緊程序號',
    qualitySign: '縂合格標志',
    setSign: '拉入/出狀態',
    checkStatus: '過站校驗狀態',
    checkCode: '過站校驗代碼',
    checkMsg: '過站校驗描述',
    engineNum: '發動機',
    driverWay: '驅動形勢',
    statusWay: '狀態計算方式',
    lineSectionCode: '產綫分斷編碼',
    allowWay: '允許信息來源'
  },
  setinout: {
    /* 拉入拉出履历 */ creationDate: '創建時間',
    setReportId: 'ID',
    workCenterCode: '車間',
    prodLineCode: '產綫',
    stationCode: '工位',
    stationDes: '工位描述',
    setType: '拉入/出狀態',
    serialNum: '工件編號',
    palletNum: '托盤號',
    staffId: '操縱者',
    makeOrder: '訂單號',
    dms: 'DMS',
    itemProject: '行項目',
    vin: 'VIN',
    smallModelType: '型號',
    mainMaterialCode: '物料編號',
    materialColor: '顔色',
    materialSize: '尺寸',
    shaftProcNum: '擰緊程序號',
    setDate: '時間',
    setMarks: '備注',
    preSetInBut: '預約拉入',
    setInBut: '直接拉入'
  },
  stationdevice: {
    /* 工位设备对应关系 */ creationDate: '創建時間',
    stationDeviceId: 'ID',
    workCenterCode: '車間',
    prodLineCode: '產綫',
    stationCode: '工位',
    stationDeviceType: '設備類型',
    deviceCode: '設備編號',
    deviceDes: '設備描述',
    deviceSectionCode: '工位與擰緊槍匹配',
    enableFlag: '有效標識'
  },
  recipequality: {
    /* 工位数据采集定义 */ creationDate: '創建時間',
    qualityId: 'ID',
    stationId: '工位',
    qualityFrom: '數據來源',
    tagId: '采集項目',
    tagQualitySignId: '合格標志',
    groupOrder: '組號',
    groupName: '組描述',
    tagColOrder: '列位置',
    tagColInnerOrder: '排序',
    qualityFor: '測量對象',
    tagDes: '采集項目名稱',
    tagUom: '采集項目單位',
    theoryValue: '標準值',
    downLimit: '下限值',
    upperLimit: '上限值',
    saveOrNot: '數據是否保存',
    enableFlag: '有效標志'
  },
  screenset: {
    /* 大屏基础 */ creationDate: '創建時間',
    screenSetId: 'ID',
    screenSetType: '大屏類型',
    screenSetCode: '大屏編碼',
    screenSetDes: '大屏描述',
    screenSetUrl: '頁面路由',
    screenSetUrlDes: '頁面路由描述',
    orderNum: '順序號',
    switchTime: '切換時長',
    enableFlag: '有效標識'
  },
  screensetcontent: {
    /* 大屏内容 */ creationDate: '創建時間',
    prodLineCode: '產綫',
    todayPlan: '今日計劃',
    currentOffline: '當前下綫',
    finishRate: '完成率',
    jphActual: 'JPH實績',
    deviceMobility: '設備可動率',
    todayStop: '今日停綫',
    aboveShow: '上面顯示',
    belowShow: '下面顯示',
    backgroundImage: '背景圖片',
    enableFlag: '有效標識'
  },
  andonbtn: {
    /* 安灯按钮基础 */ creationDate: '創建時間',
    andonBtnId: 'ID',
    stationId: '工位',
    andonType: '安燈類型',
    andonDes: '安燈描述',
    andonColor: '安燈按鈕顔色',
    btnLevel: '優先級',
    action: '動作',
    btnTagId: '按鈕TAG',
    lineStopTagId: '產綫停綫TAG',
    lineStopApi: '產綫停綫API',
    andonMusicId: '音響音樂',
    andonVoiceIdList: '音響',
    andonLimitTime: '相應超時時間',
    andonBtnStatus: '當前按鈕狀態',
    attribute1: '屬性1',
    attribute2: '屬性2',
    attribute3: '屬性3',
    enableFlag: '有效標識'
  },
  andoncard: {
    /* 安灯工位牌基础 */ creationDate: '創建時間',
    andonCardId: 'ID',
    stationId: '工位',
    andonCardType: '工位牌類型',
    andonCardColor: '顔色',
    cardTagId: '工位牌狀態TAG',
    andonCardStatus: '工位牌狀態',
    andonCardCode: '工位牌編碼',
    enableFlag: '有效標識'
  },
  andoncontrol: {
    /* 安灯工位状态控制基础 */ creationDate: '創建時間',
    andonControlId: 'ID',
    stationId: '工位',
    andonControlName: '工位控制名稱',
    andonCardValue: '工位牌控制值',
    andonCardTagId: '工位牌狀態TAG',
    andonMusicId: '音響音樂',
    lineStopTagId: '產綫停綫TAG',
    lineStopApi: '產綫停綫API',
    attribute1: '屬性1',
    attribute2: '屬性2',
    attribute3: '屬性3',
    enableFlag: '有效標識'
  },
  andonevent: {
    /* 安灯事件 */ creationDate: '創建時間',
    andonEventId: 'ID',
    workCenterCode: '車間',
    prodLineCode: '產綫',
    stationCode: '工位',
    stationName: '工位名稱',
    stationDes: '工位描述',
    lineSectionCode: '產綫分斷編碼',
    andonBtnId: '安燈按鈕',
    andonType: '安燈類型',
    andonDes: '安燈描述',
    andonLimitTime: '相應超時時間',
    happenDate: '發生時間',
    resetDate: '復位時間',
    resetFlag: '是否復位',
    costTime: '消耗時間',
    overTimeFlag: '是否超時',
    enableFlag: '有效標識'
  },
  andonvoice: {
    /* 安灯音响基础 */ creationDate: '創建時間',
    andonVoiceId: 'ID',
    andonVoiceSerial: '設備序列號',
    andonVoiceDes: '音響名稱',
    andonVoiceAttr: '音響屬性',
    btnTagId: '按鈕TAG',
    btnLevel: '優先級',
    enableFlag: '有效標識'
  },
  andonmusic: {
    /* 安灯音响音乐 */ creationDate: '創建時間',
    andonMusicId: 'ID',
    andonMusicDes: '音樂名稱',
    andonMusicPath: '音樂路徑',
    andonMusicAttr: '音樂屬性',
    enableFlag: '有效標識'
  },
  haCarTypeRoute: {
    /* 焊装车型工艺路线维护 */ haCarTypeDes: '白車身號：',
    productionLine: '產綫編碼：',
    productionLinet: '產綫編碼',
    carType: '白車身號',
    onlineStation: '上綫工位',
    routeStation: '工藝路綫',
    attribute1: '屬性1',
    attribute2: '屬性2',
    attribute3: '屬性3'
  },

  /** ************************************************************/
  /* PCB定义*/

  /** ************************************************************/
  /* DCS定义*/

  // 越南包装项目定义
  vie: {
    // 配方维护
    partNum: '料號',
    version: '版本',
    setType: 'SET類型',
    innerType: '廠内碼類型',
    pcsType: 'PCS類型',
    plateLen: '板長(mm)',
    plateWid: '板寬(mm)',
    plateThi: '板厚(mm)',
    plateWei: '板重(g)',
    setCodeLen: 'SET條碼長度',
    innerCodeLen: '廠内碼長度',
    pcsCodeLen: 'PCS條碼長度',
    setCase: 'SET大小寫',
    innerCase: '廠内碼大小寫',
    pcsCase: 'PCS大小寫',
    index: '序號',

    ordnumTruBatchRule: '訂單號截取批次規則',
    cusPartNumIntRule: '客戶料號截取料號規則',
    setTruBatchRule: 'SET截取批次規則',
    setPartNumIntRule: 'SET截取料號規則',
    setIntCycRule: 'SET截取週期規則',
    pcsTruBatchRule: 'PCS截取批次規則',
    pcsPartNumIntRule: 'PCS截取料號規則',
    pcsIntSeriaRule: 'PCS截取序號規則',
    pcsNumStartNum: 'PCS序號開始數字',
    pcsNumValadd: 'PCS序號遞增值',
    // 工单管理
    OriginLotNum: '原始批號',
    taskSource: '任務來源',
    taskStatus: '任務狀態',
    orderNum: '訂單號',
    task_type: '任務類型',
    task_status: '任務狀態',
    cusPartNum: '客戶料號',
    planQuantity: '計劃數量',
    singlePackQua: '單包數量',
    planTime: '計劃時間',
    orderStatus: '訂單狀態',
    customCode1: '客戶編碼',
    orderType: '訂單類型',
    okFinishe: 'OK完工',
    ngFinishe: 'NG完工',
    cycle: '週期',
    cutBatchNum: '截取批號',
    intPartNumb: '截取料號',
    taskStarTime: '任務開始時間',
    taskEndTime: '任務結束時間',
    taskUserTime: '任務消耗時間',
    operator: '操作者',
    confirmIssuance: '開始生產',
    LWHTHK: '長/寬/高/厚',
    plantCode: '工廠編碼',
    salesOrder: '銷售訂單號',
    salesItem: '銷售訂單行',
    salesOrg: '銷售組織',
    salesType: '銷售類型',
    customPn: '終端客戶PN',
    customPo: '終端客戶訂單',
    customCode: '終端客戶編碼',
    customName: '終端客戶名稱',
    splitLot: '截取批次號',
    splitModel: '截取料號',
    attribute1: '預留屬性1',
    attribute2: '預留屬性2',
    attribute3: '預留屬性3',

    xoutNum: 'XOUT數量',
    xoutType: 'XOUT 類型',
    quaOfOkBoard: '放板OK數量',
    quaOfNgBoard: '放板NG數量',
    passRate: '合格率',
    numOfComBoards: '收板完成數量',
    packComplete: '打包完成',
    orderStarTime: '訂單開始時間',
    orderUseTime: '訂單耗時',
    setOrder: 'SET順序',

    judgingTheResult: '判斷結果',
    setCode: 'SET條碼',
    setInnerCode: '廠内碼',
    rotationDirection: '旋轉方向',
    setLevel: 'SET等級',
    stackingPosition: '堆疊位置',
    setFront: 'SET正面',
    setOpposite: 'SET反面',
    sortingError: '分選錯誤',
    packOrder: '打包順序',
    packCode: '打包條碼',
    setNum: 'SET數量',
    IsItATrunk: '是否尾箱',
    packError: '打包異常',
    cus2D1: '客戶2D1',
    cus2D2: '客戶2D2',
    inner2D: '廠内2D',
    xout_mapping_result: 'Mapping',
    InnerOrder: '廠内碼順序',
    InnerLevel: '廠内碼等級',
    InnerFront: '廠内碼正面',
    InnerOpposite: '廠内碼反面',

    equAllPLCIss: '設備允許PLC下發',
    triColorLight: '三色燈',
    lineScan: '線掃',
    printer: '打印機',
    employeeID: '員工名稱',
    name: '姓名',
    department: '部門',
    classes: '班次',
    login: '登 入',
    logOut: '登 出',
    viewDetails: '查看明細',
    NGmulCod: '重碼',
    OKPosition: 'OK位',
    NGPosition: 'NG位',
    rotate: '旋轉',
    NORotate: '不旋轉',
    Yes: '是',
    NO: '否',
    cancel: '取 消',
    close: '關 閉',
    employeeLogin: '員工登錄',
    messageAlert: '消息提醒',
    workOrder: '工單管理',
    run: '運行',
    alarm: '警報',
    stop: '停止',
    preserve: '保養',
    mecFai: '機故',
    repair: '維修狀態',
    queryException: '查詢異常',
    loginSuccess: '登錄成功',
    operationException: '操作異常',
    AreYouSureToLogOut: '確定登出當前員工嗎？',
    AreYouSureDelete: '確定删除本條數據嗎？',
    prompt: '提示',
    determine: '確定',
    qualifiedQuantity: '合格數量',
    unqualifiedQuantity: '不合格數量',
    quantity: '數量',
    cnneSuccess: '連接成功',
    cnneFailed: '連接失敗',
    connDisconRecon: '連接斷開，正在重連。。。',
    PLCCommunInter: 'PLC通訊中斷',
    lineScanCommunInter: '線掃通訊中斷',
    printerCommunInter: '打印機通訊中斷',
    MESCommunInter: 'MES通訊中斷',
    pleaseStartMonitor: '請啓動監控',
    noIDFound: '未查詢到id',
    save: '保存',
    TotalNumberOfPackages: '總包數',
    TotalNumberOfPieces: '總顆數',
    MaximumNumberOfFullPackages: '最大滿包數',
    TrayMaterialCode: 'Tray盤物料編碼',
    TrayDiskCapacity: 'Tray盤容積',
    SortingComparisonRuleSetting: '分選對比規則設定',
    pileBarcode: '包裝條碼',
    arrayIndex: '訂單SET順席',
    boardSn: '線掃流水號',
    arrayLevel: '線掃SET等級',
    arrayFrontLevel: '線掃反面SET等級',
    arrayBackLevel: '線掃正面set等級',
    setFrontReadingCodeTime: '正面讀碼時間',
    setReverseCodeReadingTime: '反面讀碼時間',
    arrayMark: '線掃光學點檢測結果',
    arrayBdCount: '線掃SET下PCS數量',
    boardResult: '板件判斷結果',
    depositPosition: '堆疊位置',
    xoutFlag: '是否爲XOUT分選',
    xoutSetNum: 'XOUT設定數量',
    xoutActNum: 'XOUT實際數量',
    arrayNgCode: 'SET分選NG代碼',
    arrayNgMsg: 'SET分選NG描述',
    arrayFrontInfo: 'SET正面線掃數據',
    arrayBackInfo: 'SET反面線掃數據',
    arrayInnerLevel: '線掃廠内碼等級',
    arrayInnerBdCount: '線掃廠内碼下PCS數量',
    arrayInnerNgCode: '廠内碼分選NG代碼',
    arrayInnerNgMsg: '廠内碼分選NG描述',
    arrayInnerFrontInfo: '廠内碼正面線掃數據',
    arrayInnerBackInfo: '廠内碼反面線掃數據',
    upFlag: '上傳標識',
    upNgCode: '上傳錯誤代碼',
    pileUseFlag: '是否被打包使用',
    unbindFlag: '是否解綁',
    unbindUser: '解綁人',
    unbindTime: '解綁時間',
    unbindWay: '解綁方式說明',
    pileIndex: '訂單中打包序號',
    arrayCount: '打包中SET總數',
    pileWeight: '打包重量',
    pileUser: '打包人員',
    pcArraylist: 'PC計劃SET集合',
    plcArraylist: 'PLC反饋SET集合',
    pileStatus: '打包狀態',
    pileFrontLevel: '線掃正面PILE等級',
    pileBackLevel: '線掃反面PILE等級',
    pileFrontReadingCodeTime: '正面讀碼時間',
    pileReverseCodeReadingTime: '反面讀碼時間',
    pilengCode: '打包驗證NG代碼',
    pile_ng_msg: '打包驗證NG描述',
    dailyPassRate: '當日合格率',
    dailyOKQuantity: '當日OK數量',
    dailyNGQuantity: '當日NG數量',
    dailyHourProduct: '當日小時生產峯值',
    dailyHourProductVal: '當日小時生產谷值',
    dailyHourProductAve: '當日小時生產平均值',
    arrayInnerCount: '打包中廠内碼總數',
    pcArrayInnerlist: 'PC計劃廠内碼集合',
    plcArrayInnerlist: 'PLC反饋廠内碼集合',
    form: '從',
    start: '開始',
    locat: '位置',
    cut: '截取',
    charac: '個字符',
    formulaMainten: '配方維護',
    cannotBeEmptyOrAPositiveInteger: '不能爲空或正整數',
    otherValuesCannotBeEmpty: '其他值不能爲空',
    editSuccess: '修改成功',
    effective: '有效',
    invalid: '無效',
    what: '嗎？',
    changeTo: '確定要將有效標識修改爲',
    Cancellation: '任務取消',
    TaskInit: '任務初始化',
    scan: '掃描',
    scanOrdNumMESTask: '掃描Lot No獲取MES任務：',
    select: '選擇',
    success: '成功',
    PleaseSelect: '請至少選擇一項!',
    conSel: '確認選中的',
    Piece: '條數據?',
    SuccessIss: '下發成功',
    pcsCode: 'PCS條碼',
    PCSReverse: 'PCS反面線掃數據',
    PCSFront: 'PCS正面面線掃數據',
    PCSOrder: 'PCS順序',
    PCSLevel: 'PCS等級',
    LineScanPCSResults: '線掃PCS光學點檢測結果',
    PCSSortingNGCode: 'PCS分選NG代碼',
    PCSSortingNGMsg: 'PCS分選NG描述',
    PCSStatus: 'PCS狀態',
    whetherXOUT: '是否XOUT',
    time: '時間',
    setStatus: 'SET狀態',
    InnerStatus: '廠内碼狀態',
    export: '導出',
    setOutside: 'set記錄表',
    packInformation: '包裝配方維護資訊',

    // 解绑功能相关
    unbind: '解綁',
    manual: '手動',
    masterLot: '母批狀態', // 母批狀態
    targetQuantity: '目標數量',
    completed: '完成數量', // 完成數量
    NGQuantity: 'NG數量', // NG數量
    checkplans: '首檢計劃數量', // 首檢計劃數量
    checksCompleted: '首檢完成數量', // 首檢完成數量
    platingplates: '陪鍍板數量', // 陪鍍板數量
    plates: '分盤數量', // 分盤數量
    area: '面次', // 面次
    MaxNumber: 'Tray盤最大使用次數', // Tray盤最大使用次數
    orderParameters: '工單參數集合', // 工單參數集合
    selectedTaskData: '確認刪除選中的任務數據?',
    confirmUnbindAllSetTips: '確定解綁此條Pile記錄下所有的Set數據嗎？',
    confirmUnbindAllBdTips: '確定解綁此條Set記錄下所有的Board數據嗎？',
    BoardNumber: '板子序號', // 板子序號
    BarCode: '板子條碼', // 板子條碼
    Features: '功能對比' // 功能对比
  },
  interfaceLogs: {
    time: '時間',
    interName: '接口名稱',
    interDes: '接口描述',
    sourceSystem: '來源系統',
    requestParams: '請求參數',
    responseParams: '響應參數',
    successFlag: '是否成功',
    ESBinterName: 'ESB接口名稱',
    ESBinterDes: 'ESB接口描述',
    ESBinterAddress: 'ESB接口地址',
    targetSystem: '目標系統',
    interMeans: '接口方式',
    startTime: '開始時間',
    endTime: '結束時間',
    costTime: '請求時長',
    InterMessages: '接口消息',
    parasList: '定製化參數',
    requestInfo: '請求消息',
    remark: '備註',
    total: '總數量',
    pre: '上一頁',
    next: '下一頁',
    current: '當前第',
    page: '頁',
    paramsDetails: '參數詳情',
    pleaseEnter: '請輸入內容',
    Topped: '已置頂',
    bottomSet: '已置底',
    ExportFailed: '導出失敗',
    serialNumber: '序號'
  },
  scadaAlarmReport: {
    title: 'SCADA報警報表',
    prodLine: '產線',
    station: '工位',
    instanceCode: '實例編號',
    alarmCode: '報警代碼',
    alarmDesc: '報警描述',
    resetFlag: '是否復位',
    resetYes: '已復位',
    resetNo: '待復位',
    time: '時間',
    startTime: '開始時間',
    endTime: '結束時間',
    instanceDesc: '實例描述',
    alarmLevel: '報警級別',
    alarmTime: '報警時間',
    resetTime: '復位時間',
    isSimulated: '是否模擬',
    simulatedYes: '是',
    simulatedNo: '否',
    tag: 'Tag',
    export: '導出',
    search: '搜索',
    reset: '重置',
    totalCount: '總數量',
    currentPage: '當前第',
    previousPage: '上一頁',
    nextPage: '下一頁',
    page: '頁',
    top: '已置頂',
    bottom: '已置底',
    selectProdLineAndStation: '請選擇產線與工位',
    queryException: '查詢異常',
    exportFailed: '導出失敗'
  },
  messageReport: {
    cim_from: '消息來源',
    finish_flag: '完成標識',
    screen_control: '顯示方式',
    screen_code: '代碼',
    cim_msg: '消息',
    selectStation: '請選擇工位'
  },
  diagnosis: {
    client_code: '實例',
    link_status: '通訊狀態',
    link_message: '通訊信息',
    creationDate: '時間範圍',
    table: '表格形式',
    simulated_flag: '模擬標識',
    curved: '曲線形式',
    abnormal: '異常統計',
    link_normal: '通訊正常',
    link_abnormal: '通訊異常',
    scadaFaild: 'Scada實例基礎數據查詢失敗',
    scadaError: 'Scada實例基礎數據查詢超時或者SQL錯誤',
    link_analysis: '通訊狀態散點圖分析',
    sampleSize: '樣本數量',
    statusValue: '狀態值',
    abnormalStatistics: '通訊異常頻次統計',
    frequency: '斷網頻次',
    exception: '查詢selCellIP異常',
    instance: '請選擇實例',
    UnableNumber: '未獲取到單元IP與端口號',
    NoData: '已到頂無數據',
    scadaQueryFailed: 'Scada通訊報表查詢失敗:',
    scadaSqlError: 'Scada通訊報表查詢超時或者SQL錯誤',
    serialNumber: '序號:',
    time: '時間:',
    messageDes: '消息描述:',
    NoDataAvailable: '當前無數據'
  },
  proMonitor: {
    processTasks: '流程任務:',
    total: '總數量:',
    dayQuantity: '當日數量:',
    dayNormal: '當日正常:',
    dayAbnormal: '當日異常',
    cancel: '取消流程',
    view: '查看',
    time: '時間：',
    info: '信息：',
    step: '步驟：',
    log: '日誌：',
    queryException: '查詢異常',
    cancelSucess: '取消成功:',
    abnormal: '異常:'
  },
  header: {
    user: '當前用戶：',
    onlineDuration: '在線時長：',
    min: '分鐘',
    signOut: '退出登錄',
    lockScreen: '鎖屏',
    screen: '全屏縮放',
    stationSelect: '工位選擇',
    greenNewEnergy: '綠色環保新能源',
    loginPassword: '請輸入登陸密碼',
    unlock: '解鎖',
    exitSystem: '確定註銷並退出系統嗎？',
    passwordUnlock: '請輸入登陸密碼解鎖',
    passwordError: '解鎖密碼錯誤，請輸入登陸密碼解鎖',
    unlockingSuccessful: '解鎖成功',
    error: '錯誤',
    usernameRequire: '用戶名不能爲空',
    passwordRequire: '密碼不能爲空',
    roleRequire: '系統角色不能爲空',
    codeRequire: '驗證碼不能爲空'
  },
  hmiMain: {
    onLine: '在線模式', // 在线
    offline: '脫機模式', // 脱机
    prot1: '工位1', // 端口1
    prot2: '工位2', // 端口2
    protAgv1: 'AGV(1)', // AGV1
    protAgv2: 'AGV(2)', // AGV2
    enable: '啓用', // 启用
    disabled: '禁用', // 禁用
    manual: '手動畫面', // 手动画面
    eapPing: 'PING網絡測試', // Ping-EAP
    reOnline: '收板機在線', // 收板机在线
    CCD: '板件CCD',
    PlateCCD2: '板件CCD2',
    CCD1: '載具CCD1',
    CCD2: '載具CCD2',
    DownStatus: '下游',
    employee: '員工號', // 员工号
    name: '姓名', // 姓名
    department: '部門', // 部门
    status: '機臺狀態', // 机台状态
    EAP: 'EAP遠程', // EAP远程
    AIS: 'AIS本地', // AIS本地
    production: '生產模式', // 生产模式
    plate: '板件模式', // 板件模式
    transport: '搬運模式', // 搬运模式
    panel: '有Panel', // 有Panel
    NoPanel: '無Panel', // 无Panel
    work: '作業端口', // 作业端口
    Login: '登 入', // 登入
    LogOut: '登 出', // 登出
    prot1Step: '端口1步序', // 端口1步序
    prot1info: '端口1消息', // 端口1消息
    forced: '強 制 退 載 具', // 强制退载具
    prot2Step: '端口2步序', // 端口2步序
    prot2info: '端口2消息', // 端口2消息
    portStatus: '端口狀態', // 端口状态
    carrierStatus: '載具狀態', // 载具状态
    firstEdition: '是否首板', // 是否首板
    complete: '完工/計劃', // 完工/计划
    firstPlan: '首板完工/計劃', // 首板完工/计划
    workMode: '作業模式', // 作业模式
    LWH: '長/寬/高', // 长宽高
    vehicle: '載具/天蓋', // 载具/天盖
    Tray: 'Tray盤碼', // Tray盘码
    plateCode: '板件碼', // 板件码
    fmale: '母批號', // 母批号
    blowOff: '放口', // 放口,
    plan: '計劃', // 计划
    put: '已放', // 已放
    putStatus: '放狀態', // 放状态
    panelStatus: '狀態', // 板件状态
    received: '已收', // 已收,
    close: '收口', // 收口
    receivingState: '收狀態', // 收状态
    SupportBrowning: '配套棕化', // 配套棕化
    SupportPunching: '配套沖孔', // 配套冲孔
    serial: '序號', // 序号
    firstPiece: '首件', // 首件
    abnormal: '異常', // 异常
    time: '時間', // 时间
    sideboard: '陪板', // 陪板
    vehicleInput: '載具輸入', // 载具输入
    manualBatch: '手工批次上報', // 手工批次上报
    localTask: '本地任務增加', // 本地任务增加
    firstCheck: '首檢判定', // 首检判定
    panelInput: 'Panel輸入', // Panel输入
    panelJudge: 'Panel判定', // Panel判定
    confirmation: '切批確認', // 切批确认
    CIMMessage: 'CIM消息', // CIM消息
    dailyOnline: '日在線生產', // 日在线生产
    dailyOffLine: '日脫機生產', // 日脱机生产
    procedure: '獲取當前登錄信息異常', // 获取当前登录信息异常
    side: '正面', // 正面
    reverse: '反面', // 反面
    productionBoard: '生產板', // 生产板
    dummy: 'Dummy',
    have: '有', // 有
    isNo: '無', // 无
    logoutSuccess: '登出成功', // 登出成功
    workOrderEntry: '工單輸入', // 工单输入
    clickOK: '批次需要切換主製程配方,請確認切換完成後點擊確定', // 批次需要切换主制程配方,请确认切换完成后点击确定
    palletInput: 'Pallet/Tray輸入', // Pallet/Tray输入
    mixedBatch: '混批確認', // 混批确认
    forValidation: '人工掃描任務並提交到EAP驗證', // 人工扫描任务并提交到EAP验证
    forFinish: '手動結批上報', // 手動結批上報
    forFinish2: '手動确认WIP', // 手動結批上報
    Identify: '確定對端口', // 确定对端口
    mandatory: '進行強制退載具操作嗎？', // 进行强制退载具操作吗？
    machineProduction: '當前機臺生產中,不允許狀態修改', // 当前机台生产中,不允许状态修改
    cancelFlowchart: '取消流程圖成功', // 取消流程图成功
    cancelException: '取消流程圖異常', // 取消流程图异常
    abnormalError: '查詢點位數據異常：', // 查询点位数据异常
    EAPCommunication: 'EAP通訊中斷', // EAP通讯中断
    PLCCommunication: 'PLC通訊中斷', // PLC通讯中断
    CCDinterrupted: '板件讀碼CCD通訊中斷', // 板件读码CCD通讯中断
    CCD1Interrupted: '載具1讀碼CCD通訊中斷', // 载具1读码CCD通讯中断
    CCD2Interrupted: '載具2讀碼CCD通訊中斷', // 载具2读码CCD通讯中断
    startTime: '開始時間', // 开始时间
    endTime: '結束時間', // 结束时间
    implement: '實放',
    implementUnLoad: '實收',
    scanConfirmation: '工單掃描確認',
    inSeconds: '秒後接口超時',
    BOXInput: 'BOX輸入',
    deviceInit: '初始化',
    deviceRun: '運行',
    deviceStop: '停止',
    deviceIdle: '待機',
    deviceDown: '停機',
    devicePm: '保養'
  },
  workOrder: {
    workOrderNumber: '工單號',
    processCode: '製程代碼',
    productUse: '產品用途',
    batchNumber: '批號簡碼',
    materialNumber: '物料號',
    NumbeOfPlates: '板件數量',
    platSe: '板序',
    flip: '翻轉',
    parameterInfo: '參數信息'
  },
  productionTasks: {
    masterStatus: '母批狀態',
    subbatchState: '子批狀態',
    methodCompletion: '完工方式',
    normalFinish: '正常完板',
    lessBoard: '少板完板',
    boardsFinish: '多板完板',
    ForcedFinish: '強制完板',
    viewPNL: '查看PNL',
    PNLlist: 'PNL列表',
    deleteTask: '刪除任務',
    FinishedCondition: '完工狀態'
  },
  stationParameter: {
    stationConfiguration: '工位參數配置',
    theSettings: '未查詢到對應IP,請檢查設置',
    theReceiver: '未查詢到收板機IP,請設置收板機IP',
    notEnabled: '獲取收扳機監控未開啓',
    interfaceCode: '接口編碼',
    interDes: '接口描述',
    NGDegree: 'NG次數',
    synchronization: '同步',
    continuation: '續傳',
    enable: '啓用',
    cutOnline: '放板機不在線，無法切在線',
    onlineMode: '線上模式下不可操作'
  },
  // 广合
  guanghe: {
    parmas: '參數',
    finish: '完成',
    plateNG: '板件NG數量',
    trayNG: '託盤NG數量',
    trayOK: '託盤OK數量',
    panelNG: '面板NG數量',
    panelOK: '面板OK數量',
    deviceOee: '設備OEE',
    OkQuantity: 'OK數量',
    NGQuantity: 'NG數量',
    total: '總量',
    NGPass: 'NG_PASS數量',
    OfflineCodeReading: '離線讀碼量',
    OnlineCodeReading: '線上讀碼量',
    readBitRate: '讀碼率',
    port1Return: '埠1退載具',
    port2Return: '埠2退載具',
    port3Return: '埠3退載具',
    port4Return: '埠4退載具',
    exitSystem: '退出系統',
    logout: '員工登出',
    energyDetail: '能源資訊明細',
    timeTaskData: '時間統任務數據'
  },
  // 定颖
  dy: {
    auto: '自動',
    offLine: '離線模式',
    local: 'Local模式',
    semiAuto: '半自動模式',
    model: '模式',
    Semi_Auto: 'Semi-Auto模式',
    Auto: 'Auto模式'
  },
  // 维信
  wx: {
    recipeName: ' 配方名稱',
    downRecipe: ' 掃碼下載配方：',
    editParameters: ' 是否修改參數：',
    import: ' 導入',
    export: ' 匯出',
    equipSelfTest: ' 設備自檢',
    recipeType: ' 配方類型',
    recipeDesc: ' 配方描述',
    lotNo: ' 批次條碼',
    version: ' 版本號',
    recipeOperation: ' 配方操作',
    down: ' 下載配方',
    upload: ' 上傳配方',
    download: '下載',
    distributed: ' 下發設備',
    equipSelfTestInfo: ' 設備自檢資訊',
    waitUpload: ' 已存在上傳任務，請等待上傳完成',
    uploadFormula: ' 觸發上傳配方流程',
    confirmDelete: ' 確認删除選中的',
    articleData: ' 條數據',
    SuccessfullyMes: ' 上傳mes成功',
    selected: ' 確認上傳選中的',
    uploadTask: ' 已存在上傳任務，請等待上傳完成',
    trigger: ' 觸發上傳配方流程',
    confirmRecipe: ' 確認上傳配方？',
    alreadyUploadTask: '已存在上傳任務，請等待上傳完成',
    confirmDownload: ' 確認從上層系統下載配方',
    pleaseWait: ' 已存在下載任務，請等下載完成',
    triggerDownload: ' 觸發下載配方流程',
    IssuedEquipment: ' 確認下發配方到設備？',
    alreadyExistsTask: ' 已存在下發任務，請等下發完成',
    SuccessfullyIssued: ' 下發配方到設備成功',
    selectOne: '請選擇一條數據!',
    exportName: '確定匯出配方名稱為',
    recipeDownload: '配方下载',

    comfirmReset: '確認要復位流程嗎？ ',
    resetSuccess: '流程復位成功',
    currently: '當前未獲取到物料，請先去維護物料',
    queryFailed: '流程復位失敗',
    failedMaterial: '未能根據當前物料號',
    foundRecipe: '査詢到相關配方資訊',
    pleaseScan: '請先掃描或輸入工單號',
    reset: '復位流程',
    lotNum: '工單',
    batchCheck: '批次校驗',
    employeeID: '員工名稱',
    productInfo: '配方資訊',
    alarmMsg: '報警資訊',
    alarmTime: '報警時間',
    alarmLevel: '報警級別',
    alarmCode: '報警程式碼',
    alarmDesc: '報警描述',
    feedbackInfo: '迴響資訊',
    userInfo: '用戶登錄',
    equipmentInfo: '設備自檢',
    productResult: '產品登入',
    otherInfo: '其他資訊',
    panelInfo: '掃碼結果',
    paramInfo: '參數上傳',
    statusInfo: '狀態上傳',
    producrParameter: '實时參數',
    projectName: '項目名稱',
    currentValue: '當前值',
    state: '狀態',
    unit: '組織',
    upperLimit: '上限',
    lowerLimit: '下限',
    modifyFormula: '修改配方',
    thickness: '塞孔機板厚',
    modifyParams: '是否修改參數',
    parameterCode: '參數編碼',
    parameterDesc: '參數描述',
    parameterValue: '參數值',
    cancel: '取消',
    confirm: '確定下發',
    plcHeartbeat: 'PLC心跳',
    eapHeartbeat: 'EAP心跳',
    upstream: '上游',
    downstream: '下游',
    electricity: '電量',

    productionMode: '量產模式',
    firstArticleMode: '首件模式',
    dummyMode: 'Dummy模式',
    stationCode: '工位號',
    capacity: '產能',
    readBitRate: '讀碼率',
    onDuty: '在崗',
    leave: '脫崗',
    enterCode: '請輸入物料編碼',
    enterDesc: '請輸入物料編碼',
    hole: '選擇填孔+',
    selectSupface: '選擇表面+',
    materialMaintenance: '物料維護',
    equipment: '設備號',
    scanDownload: '掃碼下載配方：',
    addParams: '新增參數',
    scadaRequest: '發起Scada請求配方，配方下載中',
    scadaError: '發起Scada請求配方，配方下載出現異常',
    uploadSuccessful: '上傳成功',
    downLoadRecipe: '確認下發該配方嗎',
    subDetail: '子配方維護詳情',
    modificationFailed: '修改失敗'
  },
  // 福建瑞闽
  fjrm: {
    wasteBoxCode: '廢料框編碼',
    wasteBoxDes: '廢料框描述',
    height: '高度',
    rgvCode: 'RGV編碼',
    rgvDes: 'RGV描述',
    wharfCode: '碼頭編碼',
    wharfDes: '碼頭描述',
    wharfType: '碼頭類型',
    locationX: 'X座標',
    locationY: 'Y座標',
    locationZ: 'Z座標',
    wharfOrder: '碼頭排序',
    wharfTag: '碼頭點位',
    lockFlag: '是否鎖定',
    lotNo: '批次號',
    si: 'si',
    fe: 'fe',
    cu: 'cu',
    mn: 'mn',
    mg: 'mg',
    ni: 'ni',
    zn: 'zn',
    ti: 'ti',
    cr: 'cr',
    na: 'na',
    ca: 'ca',
    taskNum: '任務號',
    taskFrom: '任務來源',
    taskWay: '任務方式',
    taskType: '任務類型',
    wareHouse: '庫區域',
    fromStockCode: '起始庫位',
    toStockCode: '目標庫位',
    lotNum: '批次號',
    materialCode: '物料編碼',
    width: '總重量',
    errorMin: '最小值',
    errorMax: '最大值',
    executeWidth: '執行總重量',
    taskOrder: '任務排序',
    taskStatus: '任務狀態',
    enableFlag: '有效標識'
  }
}
