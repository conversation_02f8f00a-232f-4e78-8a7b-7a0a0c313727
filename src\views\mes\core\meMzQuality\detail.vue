<template>
  <div>
    <el-card ref="queryCard" shadow="never">
      <el-descriptions class="margin-top" :column="5" size="medium" border>

        <el-descriptions-item>
          <template slot="label">
            电芯数量
          </template>
          {{ dxCount }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            容量和
          </template>
          {{ data.mz_capacity_plus }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            容量差
          </template>
          {{ data.mz_capacity_diff }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            最大容量
          </template>
          {{ data.mz_max_capacity }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            最小容量
          </template>
          {{ data.mz_min_capacity }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            模组理论厚度
          </template>
          {{ mzThickness/data.col_num }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-dialog append-to-body :close-on-click-modal="false" title="替换电芯" :visible.sync="updDialogVisbleSync" width="520px">
            <el-input v-model="dx_barcode" placeholder="请输入电芯条码" />
            <div style="text-align: center; margin: 20px 0 0 0">
              <el-button size="small" icon="el-icon-close" plain @click="updDialogVisbleSync=false">取消</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :disabled="dx_barcode === ''" @click="saveDxBarcode()">确认</el-button>
              <!-- 确认 -->
            </div>
          </el-dialog>
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" size="small" :data="detailData" style="width: 100%" height="450px" highlight-current-row>

            <el-table-column  width="100" label="替换电芯" align="center" fixed="left">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="updateDx(scope.row)">替换电芯</el-tag>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="item_date" min-width="150" label="时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_barcode" min-width="220" label="电芯码" />
            <el-table-column  :show-overflow-tooltip="true" prop="mk_num" min-width="100" label="串" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_code" min-width="100" label="工位号" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_location_num" min-width="100" label="位置号" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_status" min-width="100" label="合格标志">
              <template slot-scope="scope">
                <el-tag :type="scope.row.dx_status==='OK'?'success':'danger'" effect="dark" style="cursor: pointer;" size="medium">{{ scope.row.dx_status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="dx_pressure_kl_value" min-width="150" label="OCV测试开路电压" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_pressure_fd_value" min-width="150" label="OCV测试负路电压" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_dianzu_value" min-width="150" label="OCV测试电阻" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_k2_value" min-width="150" label="K2计算值" />
            <el-table-column  :show-overflow-tooltip="true" prop="box_barcode" min-width="150" label="料箱条码" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_gear" min-width="150" label="原始档位" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_batch" min-width="150" label="原始批次" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_capacity" min-width="150" label="原始容量" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_ocv4_pressure" min-width="150" label="原始电压" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_ocr4_air" min-width="150" label="原始电阻" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_k_value" min-width="150" label="原始K值" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_dcir" min-width="150" label="原始dcir内阻" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_thickness" min-width="150" label="原始厚度" />
            <el-table-column  :show-overflow-tooltip="true" prop="dx_ori_ocv4_time" min-width="150" label="OCV开始时间" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { selDetail, edit } from '@/api/mes/core/meMzQuality'
export default {
  name: 'MES_ME_MZ_QUALITY',
  components: { },
  // 数据字典
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      detailData: [],
      updDialogVisbleSync: false,
      currentRowData: {},
      dx_barcode: '',
      dxCount: 0,
      mzThickness: 0
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.toQuery()
  },
  methods: {
    toQuery() {
      selDetail({
        mz_quality_id: this.data.mz_quality_id
      })
        .then(res => {
          this.dxCount = res.count
          if (res.code === 0 && res.count > 0) {
            this.detailData = res.data
            this.mzThickness = 0
            this.detailData.forEach(item => {
              this.mzThickness += item.dx_ori_thickness
            })
          } else {
            this.detailData = []
          }
        })
        .catch(ex => {
          this.$message({ message: '操作失败：' + ex, type: 'error' })
          this.detailData = []
        })
    },
    updateDx(row) {
      this.currentRowData = row
      this.dx_barcode = ''
      this.updDialogVisbleSync = true
    },
    saveDxBarcode() {
      if (this.dx_barcode === '') {
        this.$message({ message: '请输入电芯条码', type: 'info' })
        return
      }
      this.$confirm(`确认将电芯条码${this.currentRowData.dx_barcode}替换成${this.dx_barcode}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          edit({
            mz_dx_rel_id: this.currentRowData.mz_dx_rel_id,
            dx_barcode: this.dx_barcode
          })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.updDialogVisbleSync = false
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
