import request from '@/utils/request'

// 查询WMS天车库位表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodStockSelect',
    method: 'post',
    data
  })
}
// 新增WMS天车库位表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodStockInsert',
    method: 'post',
    data
  })
}
// 修改WMS天车库位表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodStockUpdate',
    method: 'post',
    data
  })
}
// 删除WMS天车库位表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodStockDelete',
    method: 'post',
    data
  })
}

// 修改WMS天车库位表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodStockEnableFlagUpdate',
    method: 'post',
    data
  })
}

// 手动入库WMS天车库位表
export function inStock(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsManualInStock',
    method: 'post',
    data
  })
}

// 手动出库WMS天车库位表
export function outStock(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsManualOutStock',
    method: 'post',
    data
  })
}
// 重新抛丸
export function reShotBlasting(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsReShotBlasting',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag, inStock, outStock, reShotBlasting }

