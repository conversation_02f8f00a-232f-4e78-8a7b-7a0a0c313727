<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.mainmain.mainProcessCode')">
                <!-- 主流程编码： -->
                <el-input
                  v-model="query.flow_main_code"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item
                :label="$t('lang_pack.mainmain.mainProcessDescription')"
              >
                <!-- 主流程描述： -->
                <el-input
                  v-model="query.flow_main_des"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentification')"
              >
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="650px"
      >
        <el-tabs v-model="activeName">
          <el-tab-pane
            :label="$t('lang_pack.mainmain.basicAttribute')"
            name="first"
          >
            <!-- 基础属性 -->
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="140px"
              :inline="true"
            >
              <el-form-item
                :label="$t('lang_pack.mqmain.eventMqMainDes')"
                prop="event_mq_main_id"
              >
                <!--主MQ事件描述 -->
                <el-select
                  v-model="form.event_mq_main_id"
                  filterable
                  clearable
                  @change="getEvengSubs(form.event_mq_main_id)"
                >
                  <el-option
                    v-for="item in eventMqs"
                    :key="item.event_mq_main_id"
                    :label="
                      item.event_mq_main_code + ' ' + item.event_mq_main_des
                    "
                    :value="item.event_mq_main_id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.mqmain.eventModMqSub')"
                prop="event_mq_sub_id"
              >
                <!--子MQ事件描述 -->
                <el-select v-model="form.event_mq_sub_id" filterable clearable>
                  <el-option
                    v-for="item in eventSubs"
                    :key="item.event_mq_sub_id"
                    :label="
                      'mqSubId:' +
                      item.event_mq_sub_id +
                      ' ' +
                      item.event_mq_sub_des
                    "
                    :value="item.event_mq_sub_id"
                  />
                </el-select>
              </el-form-item>

              <!-- 表格id -->
              <el-form-item
                v-show="false"
                lable="event_mq_sub_monitor_id"
                prop="event_mq_sub_monitor_id"
              >
                <el-input
                  v-model="form.event_mq_sub_monitor_id"
                  :value="currentMqSubMonitorId"
                />
              </el-form-item>

              <el-form-item
                :label="$t('lang_pack.mqmain.monitorType')"
                prop="monitor_type"
              >
                <!-- 触发任务方式 -->
                <el-select v-model="form.monitor_type" clearable>
                  <el-option
                    v-for="item in [
                      { value: 'TAG_ID', label: 'TAG_ID' },
                      { value: 'TAG_GROUP', label: 'TAG_GROUP' },
                      { value: 'ELSE', label: 'ELSE' },
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                :label="$t('lang_pack.mainmain.taskTriggeringMode')"
                prop="monitor_way"
              >
                <!-- 触发任务方式 -->
                <el-select v-model="form.monitor_way" clearable>
                  <el-option
                    v-for="item in [
                      { value: 'EQUAL', label: '值相等' },
                      { value: 'CHANGE', label: '值变化' },
                      { value: 'LIST', label: '值集合' },
                      { value: 'CHANGE_LIMIT_LEN', label: '固定长度值变化' },
                      { value: 'INITIAL_VALUE', label: '初始值' },
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="form.monitor_type === 'TAG_ID'"
                :label="$t('lang_pack.mainmain.triggerPoint')"
                prop="monitor_topic"
              >
                <!-- 触发点位 -->
                <el-input v-model.number="form.monitor_topic">
                  <div slot="append">
                    <el-popover
                      v-model="customPopover"
                      placement="left"
                      width="650"
                    >
                      <tagSelect
                        ref="tagSelect"
                        :client-id-list="form.client_id_list"
                        :tag-id="this.form.monitor_topic"
                        @chooseTag="handleChooseTag"
                      />
                      <el-button slot="reference">选择</el-button>
                    </el-popover>
                  </div>
                </el-input>
              </el-form-item>

              <el-form-item
                v-else
                :label="$t('lang_pack.mainmain.triggerPoint')"
                prop="monitor_topic"
              >
                <!-- 触发点位 -->
                <el-input v-model.number="form.monitor_topic" />
              </el-form-item>

              <el-form-item
                :label="$t('lang_pack.mainmain.triggerPointValue')"
                prop="monitor_value"
              >
                <!-- 触发点位值 -->
                <el-input v-model="form.monitor_value" />
              </el-form-item>

              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentificationt')"
              >
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-form>

            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="crud.cancelCU"
                >{{ $t("lang_pack.commonPage.cancel") }}</el-button
              >
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
                >{{ $t("lang_pack.commonPage.confirm") }}</el-button
              >
              <!-- 确认 -->
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @row-click="handleRowClick"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column
              v-if="1 == 0"
              width="10"
              prop="event_mq_sub_monitor_id"
              label="id"
            />

            <el-table-column
              :show-overflow-tooltip="true"
              prop="event_mq_main_des"
              :label="$t('lang_pack.mqmain.eventMqMainDes')"
              width="200"
            />
            <!-- 主MQ事件模版 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="event_mq_sub_des"
              width="200"
              :label="$t('lang_pack.maintenanceMenu.mqSubDes')"
            />
            <!-- 子MQ事件 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="monitor_type"
              width="100"
              :label="$t('lang_pack.mqmain.monitorType')"
            />
            <!-- 监控主题 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="monitor_topic"
              width="150"
              :label="$t('lang_pack.mainmain.triggerPoint')"
            />
            <!-- 监控主题 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="monitor_way"
              width="100"
              :label="$t('lang_pack.mqmonitor.monitor_way')"
            />
            <!-- 监控方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="monitor_value"
              width="200"
              :label="$t('lang_pack.mainmain.triggerPointValue')"
            />
            <!-- 监控值 -->
            <el-table-column
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :disabled-dle="false"
                />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudEventMqMainSubAttrMonitor from "@/api/core/event/eventMqMainSubAttrMonitor";
import { sel as selSub } from "@/api/core/event/eventMqMainSub";
import { sel as selMain } from "@/api/core/event/eventMqMain";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import AttrGroup from "@/views/core/event/mod/mq/attr-group";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import udOperation from "@crud/UD.operation";
import pagination from "@crud/Pagination";

const defaultForm = {
  event_mq_main_des: "",
  event_mq_sub_des: "",
  client_id_list: "",
  event_mq_main_id: "",

  event_mq_sub_monitor_id: 0,
  event_mq_sub_id: "",
  monitor_type: "",
  monitor_way: "",
  monitor_topic: "",
  monitor_value: "",
  enable_flag: "Y",
};
export default {
  name: "RCS_MQ_SUB_MONITOR",
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    AttrGroup,
  },
  cruds() {
    return CRUD({
      title: "MONITOR",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "event_mq_sub_monitor_id",
      // 启动关联查询
      query: { event_mq_main_id: "", event_mod_mq_id: "" },
      // 排序
      sort: ["event_mq_sub_monitor_id asc"],
      // CRUD Method
      crudMethod: { ...crudEventMqMainSubAttrMonitor },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true,
      },
    });
  },
  // 数据字典
  dicts: ["ENABLE_FLAG"],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      activeName: "first",
      permission: {
        add: ["admin", "rcs_event_mq_sub_monitor:add"],
        edit: ["admin", "rcs_event_mq_sub_monitor:edit"],
        del: ["admin", "rcs_event_mq_sub_monitor:del"],
        down: ["admin", "rcs_event_mq_sub_monitor:down"],
      },
      rules: {
        event_mq_sub_id: [
          { required: true, message: "请选择子菜单模板", trigger: "blur" },
        ],
        monitor_way: [
          { required: true, message: "请选择监控方式", trigger: "blur" },
        ],
        monitor_topic: [
          { required: true, message: "请输入监控主题", trigger: "blur" },
        ],
        enable_flag: [
          { required: true, message: "请选择是否有效", trigger: "blur" },
        ],
      },
      customPopover: false,
      currentMqSubMonitorId: "",
      eventSubs: [],
      eventMqs: [],
      exampleObj: {
        title: "实例集合",
        tableLable: [{ prop: "client_des", label: "描述" }],
      },
    };
  },

  mounted: function () {
    const that = this;
    that.$refs.table.doLayout();
    selMain({
      user_name: Cookies.get("userName"),
      enable_flag: "Y",
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res));
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.eventMqs = defaultQuery.data;
          }
        }
      })
      .catch(() => {
        this.$message({
          message: "查询异常",
          type: "error",
        });
      });
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270;
    };
  },
  created: function () {},
  methods: {
    getEvengSubs(event_mq_main_id) {
      if (event_mq_main_id === 0) return;
      selMain({
        event_mq_main_id: event_mq_main_id,
        user_name: Cookies.get("userName"),
        enable_flag: "Y",
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.form.client_id_list = defaultQuery.data[0].client_id_list;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });

      selSub({
        event_mq_main_id: event_mq_main_id,
        user_name: Cookies.get("userName"),
        enable_flag: "Y",
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.eventSubs = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },
    handleRowClick(row, column, event) {
      this.currentMqSubMonitorId = row.event_mq_sub_monitor_id;
    },

    handleChooseTag(tagId) {
      this.form.monitor_topic = tagId;
      this.customPopover = false;
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.client_id_list =
        crud.form.client_id_list === ""
          ? ""
          : crud.form.client_id_list.split(",").toString();
      return true;
    },

    updateAdd(value) {
      const list = [];
      if (value.length > 0) {
        value.forEach((e) => {
          list.push(e.client_id);
        });
      }
      this.form.client_id_list = list.toString();
    },
  },
};
</script>
  <style lang="scss">
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px;
}
.step-attr-dialog .el-dialog__body {
  padding: 0px;
  overflow-y: auto;
}
</style>
