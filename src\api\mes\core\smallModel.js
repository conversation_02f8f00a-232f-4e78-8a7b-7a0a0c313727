import request from '@/utils/request'

// 产品型号信息查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelSel',
    method: 'post',
    data
  })
}
// 产品型号信息增加
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelIns',
    method: 'post',
    data
  })
}
// 产品型号信息修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelUpd',
    method: 'post',
    data
  })
}
// 产品型号信息删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelDel',
    method: 'post',
    data
  })
}

// 产品型号Lov
export function lovSmallModel(data) {
  return request({
    url: 'aisEsbWeb/core/system/MesSmallModelLov',
    method: 'post',
    data
  })
}

// 产品型号信息复制
export function copy(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelCopy',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, lovSmallModel, copy }
