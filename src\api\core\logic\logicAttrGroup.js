import request from '@/utils/request'

// 查询逻辑属性组
export function selLogicAttrGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrGroupSel',
    method: 'post',
    data
  })
}
// 新增逻辑属性组
export function insLogicAttrGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrGroupIns',
    method: 'post',
    data
  })
}
// 修改逻辑属性组
export function updLogicAttrGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrGroupUpd',
    method: 'post',
    data
  })
}
// 删除逻辑属性组
export function delLogicAttrGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrGroupDel',
    method: 'post',
    data
  })
}

// 查询逻辑属性组(LOV)
export function logicAttrGroupLov(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrGroupLov',
    method: 'post',
    data
  })
}
export default { selLogicAttrGroup, insLogicAttrGroup, updLogicAttrGroup, delLogicAttrGroup,
  logicAttrGroupLov }
