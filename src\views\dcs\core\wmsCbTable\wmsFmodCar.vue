<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="145px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="天车编码:">
                                <!-- 天车编码 -->
                                <el-input v-model="query.car_code" clearable size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.libraryArea')" prop="ware_house">
                                <!-- 库区域 -->
                                <!-- <fastCode fastcode_group_code="WARE_HOUSE" :fastcode_code.sync="form.ware_house" control_type="select" size="mini" /> -->
                                <el-select v-model="form.ware_house" clearable filterable>
                                    <el-option v-for="item in dict.WARE_HOUSE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockCode')" prop="car_code">
                                <!-- 天车编码 -->
                                <el-input v-model="form.car_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockDescription')" prop="car_des">
                                <!-- 天车描述 -->
                                <el-input v-model="form.car_des" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockNumber')" prop="car_num">
                                <!--  天车序号 -->
                                <el-input v-model="form.car_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockAttributes')" prop="car_attr">
                                <!-- 天车属性 -->
                                <el-select v-model="form.car_attr" clearable filterable>
                                    <el-option v-for="item in dict.CROWM_BLOCK_ATTR" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockType')" prop="auto_car_flag">
                                <!-- 天车类型 -->
                                <el-select v-model="form.auto_car_flag" clearable filterable>
                                    <el-option v-for="item in dict.CROWN_BLOCK_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.groupCollection')" prop="stock_group_code_list">
                                <!-- 管辖库位组集合 -->
                                <el-input v-model="form.stock_group_code_list" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.originXCoordinate')" prop="zero_location_x">
                                <!-- 原点X坐标 -->
                                <el-input type="number" v-model="form.zero_location_x" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.originYCoordinate')" prop="zero_location_y">
                                <!-- 原点Y坐标 -->
                                <el-input type="number" v-model="form.zero_location_y" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.originZCoordinate')" prop="zero_location_z">
                                <!-- 原点Z坐标 -->
                                <el-input type="number" v-model="form.zero_location_z" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.farPointXCoordinate')" prop="far_location_x">
                                <!-- 远点X坐标 -->
                                <el-input type="number" v-model="form.far_location_x" clearable size="small" />
                            </el-form-item>
                            <el-form-item  :label="$t('lang_pack.wmsCbTable.farPointYCoordinate')" prop="far_location_y">
                                <!-- 远点Y坐标 -->
                                <el-input type="number" v-model="form.far_location_y" clearable size="small" />
                            </el-form-item>
                            <el-form-item  :label="$t('lang_pack.wmsCbTable.farPointZCoordinate')" prop="far_location_z">
                                <!-- 远点Z坐标 -->
                                <el-input type="number" v-model="form.far_location_z" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.safeDistance')" prop="safe_width">
                                <!-- 安全距离 -->
                                <el-input type="number" v-model="form.safe_width" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.recipequality.enableFlag')" prop="enable_flag">
                                <!-- 有效标识 -->
                                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" prop="car_id" fixed/>
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                                <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                                <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                                <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_id }}</el-descriptions-item>
                                <el-descriptions-item label="库区域" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.WARE_HOUSE[props.row.ware_house]}}</el-descriptions-item>
                                <el-descriptions-item label="天车编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_code }}</el-descriptions-item>
                                <el-descriptions-item label="天车描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_des }}</el-descriptions-item>
                                <el-descriptions-item label=" 天车序号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_num }}</el-descriptions-item>
                                <el-descriptions-item label="天车属性" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.CROWM_BLOCK_ATTR[props.row.car_attr] }}</el-descriptions-item>
                                <el-descriptions-item label="天车类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.CROWN_BLOCK_TYPE[props.row.auto_car_flag] }}</el-descriptions-item>
                                <el-descriptions-item label="天车管辖库位组集合" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_group_code_list }}</el-descriptions-item>
                                <el-descriptions-item label="原点X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.zero_location_x }}</el-descriptions-item>
                                <el-descriptions-item label="原点Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.zero_location_y }}</el-descriptions-item>
                                <el-descriptions-item label="原点Z坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.zero_location_z }}</el-descriptions-item>
                                <el-descriptions-item label="远点X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.far_location_x }}</el-descriptions-item>
                                <el-descriptions-item label="远点Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.far_location_y }}</el-descriptions-item>
                                <el-descriptions-item label="远点Z坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.far_location_z }}</el-descriptions-item>
                                <el-descriptions-item label="安全距离" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.safe_width }}</el-descriptions-item>
                                <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                                <el-descriptions-item label="预留属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                                <el-descriptions-item label="预留属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                                <el-descriptions-item label="预留属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                            </el-table-column>
                        <!-- 库区域 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="ware_house"
                            :label="$t('lang_pack.wmsCbTable.libraryArea')" width="140" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
                            </template>
                        </el-table-column>
                        <!-- 天车编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_code"
                            :label="$t('lang_pack.wmsCbTable.crownBlockCode')" width="140" align='center'/>
                        <!-- 天车描述 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_des"
                            :label="$t('lang_pack.wmsCbTable.crownBlockDescription')" width="140" align='center'/>
                        <!--  天车序号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_num"
                            :label="$t('lang_pack.wmsCbTable.crownBlockNumber')" width="140" align='center'/>
                        <!-- 天车属性 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_attr"
                            :label="$t('lang_pack.wmsCbTable.crownBlockAttributes')" width="120" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CROWM_BLOCK_ATTR[scope.row.car_attr] }}
                            </template>
                        </el-table-column>
                        <!-- 天车类型 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="auto_car_flag"
                            :label="$t('lang_pack.wmsCbTable.crownBlockType')" width="120" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CROWN_BLOCK_TYPE[scope.row.auto_car_flag] }}
                            </template>
                        </el-table-column>
                        <!-- 管辖库位组集合 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="stock_group_code_list"
                            :label="$t('lang_pack.wmsCbTable.groupCollection')" width="120" align='center'/>
                        <!-- 原点X坐标 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="zero_location_x"
                            :label="$t('lang_pack.wmsCbTable.originXCoordinate')" width="80" align='center'/> -->
                        <!-- 原点Y坐标 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="zero_location_y"
                            :label="$t('lang_pack.wmsCbTable.originYCoordinate')" width="80" align='center'/> -->
                        <!-- 原点Z坐标 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="zero_location_z"
                            :label="$t('lang_pack.wmsCbTable.originZCoordinate')" width="80" align='center'/> -->
                        <!-- 远点X坐标 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="far_location_x"
                            :label="$t('lang_pack.wmsCbTable.farPointXCoordinate')" width="80" align='center'/> -->
                        <!-- 远点Y坐标 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="far_location_y"
                            :label="$t('lang_pack.wmsCbTable.farPointYCoordinate')" width="80" align='center'/> -->
                        <!-- 远点Z坐标 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="far_location_z"
                            :label="$t('lang_pack.wmsCbTable.farPointZCoordinate')" width="80" align='center'/> -->
                        <!-- 安全距离 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="safe_width"
                            :label="$t('lang_pack.wmsCbTable.safeDistance')" width="120" align='center'/>
                        <el-table-column :label="$t('lang_pack.recipequality.enableFlag')" align="center" width="120"
                            prop="enable_flag">
                            <!-- 有效标识 -->
                            <template slot-scope="scope">
                               <!--取到当前单元格-->
                                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
                            </template>
                        </el-table-column>
                        <!-- Table单条操作-->
                        <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" align="center"
                            fixed="right">
                            <!-- 操作 -->
                            <template slot-scope="scope">
                                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudWmsFmodCar from '@/api/dcs/core/wmsCbTable/wmsFmodCar'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    car_id: '',
    ware_house: '',
    car_code: '',
    car_des: '',
    car_num: '',
    car_attr: '',
    auto_car_flag: '',
    stock_group_code_list: '',
    zero_location_x: '',
    zero_location_y: '',
    zero_location_z: '',
    far_location_x: '',
    far_location_y: '',
    far_location_z: '',
    enable_flag: 'Y',
}
export default {
    name: 'WEB_WMS_FMOD_CAR',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: 'WMS天车基础表',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'car_id',
            // 排序
            sort: ['car_id asc'],
            // CRUD Method
            crudMethod: { ...crudWmsFmodCar },
            // 按钮显示
            optShow: {
                add: true,
                edit: true,
                del: true,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                car_id: [{ required: true, message: '请选择天车基础表ID', trigger: 'blur' }],
                ware_house: [{ required: true, message: '请选择库区域', trigger: 'blur' }],
                car_code: [{ required: true, message: '请选择天车编码', trigger: 'blur' }],
                car_des: [{ required: true, message: '请选择天车描述', trigger: 'blur' }],
                car_num: [{ required: true, message: '请选择 天车序号', trigger: 'blur' }],
                car_attr: [{ required: true, message: '请选择天车属性', trigger: 'blur' }],
                auto_car_flag: [{ required: true, message: '请选择天车类型', trigger: 'blur' }],
                stock_group_code_list: [{ required: true, message: '请选择管辖库位组集合', trigger: 'blur' }],
                zero_location_x: [{ required: true, message: '请选择原点X坐标', trigger: 'blur' }],
                zero_location_y: [{ required: true, message: '请选择原点Y坐标', trigger: 'blur' }],
                zero_location_z: [{ required: true, message: '请选择原点Z坐标', trigger: 'blur' }],
                enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }],
            },
        }
    },
    dicts: ['ENABLE_FLAG','CROWN_BLOCK_TYPE','CROWM_BLOCK_ATTR','WARE_HOUSE'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
        changeEnabled(data, val) {
            this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
        .then(() => {
            crudWmsFmodCar
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              car_id: data.car_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    }
}
</script>
  