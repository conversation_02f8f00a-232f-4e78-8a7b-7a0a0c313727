<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.server.serviceTagDescription')">
                <!-- 服务编号/ 描述： -->
                <el-input v-model="query.serverCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <!--表：sys_core_center-->
          <el-form-item :label="$t('lang_pack.server.centre')">
            <!-- 中心 -->
            <el-select v-model="form.center_id">
              <el-option v-for="item in centerData" :key="item.center_code" :label="item.center_des" :value="item.center_id" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.server.serviceCode')" prop="server_code" display:none>
            <!-- 服务编码 -->
            <el-input v-model="form.server_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.server.serviceDescription')" prop="server_des">
            <!-- 服务描述 -->
            <el-input v-model="form.server_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.server.mainCard')" prop="server_host_1">
            <!-- 主网卡 -->
            <el-input v-model="form.server_host_1" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.server.attachCard1')" prop="server_host_2">
            <!-- 附网卡1 -->
            <el-input v-model.number="form.server_host_2" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.server.attachCard2')" prop="server_host_3">
            <!-- 附网卡2 -->
            <el-input v-model.number="form.server_host_3" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.server.attachCard3')" prop="server_host_4">
            <!-- 附网卡3 -->
            <el-input v-model.number="form.server_host_4" />
          </el-form-item>

          <el-form-item :label="$t('lang_pack.server.esbServer')">
            <!-- 是否为ESB服务器 -->
            <el-radio-group v-model="form.esb_server_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <!--快速编码：SYSTEM_TYPE-->
          <el-form-item :label="$t('lang_pack.server.system')" prop="server_system">
            <!-- 系统 -->
            <el-select v-model="form.server_system" clearable filterable>
              <el-option v-for="item in dict.SYSTEM_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="server_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="center_id" :label="$t('lang_pack.server.centre')">
              <!-- 中心 -->
              <template slot-scope="scope">
                {{ getCenterDes(scope.row.center_id) }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="server_code" :label="$t('lang_pack.server.serviceCode')" width="160"/>
            <!-- 服务编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="server_des" :label="$t('lang_pack.server.serviceDescription')" width="160"/>
            <!-- 服务描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="server_host_1" :label="$t('lang_pack.server.mainCard')" width="160"/>
            <!-- 主网卡 -->
            <el-table-column  :show-overflow-tooltip="true" prop="server_host_2" :label="$t('lang_pack.server.attachCard1')" width="160"/>
            <!-- 附网卡1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="server_host_3" :label="$t('lang_pack.server.attachCard2')" width="220"/>
            <!-- 附网卡2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="server_host_4" :label="$t('lang_pack.server.attachCard3')" width="220"/>
            <!-- 附网卡3 -->

            <el-table-column  :label="$t('lang_pack.server.esbServer')" align="center" prop="esb_server_flag" width="100">
              <!-- 是否为ESB服务器 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.esb_server_flag] }}
              </template>
            </el-table-column>
            <el-table-column  :label="$t('lang_pack.server.system')" align="center" prop="server_system" width="100">
              <!-- 系统 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.SYSTEM_TYPE[scope.row.server_system] }}
              </template>
            </el-table-column>
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudServer from '@/api/core/center/server'
import { lovCenter } from '@/api/core/center/center'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  server_id: '',
  center_id: '',
  server_code: '',
  server_des: '',
  server_host_1: '',
  server_host_2: '',
  server_host_3: '',
  server_host_4: '',
  esb_server_flag: 'Y',
  server_system: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_SERVER',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '服务维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'server_id',
      // 排序
      sort: ['server_id asc'],
      // CRUD Method
      crudMethod: { ...crudServer },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_core_server:add'],
        edit: ['admin', 'sys_core_server:edit'],
        del: ['admin', 'sys_core_server:del'],
        down: ['admin', 'sys_core_server:down']
      },
      rules: {
        server_code: [{ required: true, message: '请输入服务编号', trigger: 'blur' }],
        server_system: [{ required: true, message: '请输入系统', trigger: 'blur' }]
      },
      // 中心数据
      centerData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WHETHER_FLAG', 'SYSTEM_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    lovCenter(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.centerData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 获取中心的中文描述
    getCenterDes(center_id) {
      var item = this.centerData.find(item => item.center_id === center_id)
      if (item !== undefined) {
        return item.center_des
      }
      return center_id
    }
  }
}
</script>
