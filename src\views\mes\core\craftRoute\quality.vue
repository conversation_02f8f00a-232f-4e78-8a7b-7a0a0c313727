<template>
  <div>
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="数据来源：">
                <el-select v-model="query.quality_from" clearable size="small">
                  <el-option v-for="item in [{ label: 'SCADA', id: 'SCADA' }, { label: 'WEBSERVICE', id: 'WEBSERVICE' }]" :key="item.id" :label="item.label" :value="item.id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="采集项目名称：">
                <el-input v-model="query.tag_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button size="small" type="primary" icon="el-icon-plus" plain round @click="qualityBatchDialogVisible = true">
            批量新增
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="数据来源" prop="quality_from">
            <el-select v-model="form.quality_from" clearable>
              <el-option v-for="item in [{ label: 'SCADA', id: 'SCADA' }, { label: 'WEBSERVICE', id: 'WEBSERVICE' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="采集项目" prop="tag_id">
            <el-input v-model.number="form.tag_id" readonly="readonly">
              <div slot="append">
                <el-popover v-model="customPopover1" placement="left" width="650">
                  <tagSelect ref="tagSelect" client-id-list="" :tag-id="form.tag_id" @chooseTag="handleChooseTag1" />
                  <el-button slot="reference">选择</el-button>
                </el-popover>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="合格标志" prop="tag_quality_sign_id">
            <el-input v-model.number="form.tag_quality_sign_id" readonly="readonly">
              <div slot="append">
                <el-popover v-model="customPopover2" placement="left" width="650">
                  <tagSelect ref="tagSelect" client-id-list="" :tag-id="form.tag_quality_sign_id" @chooseTag="handleChooseTag2" />
                  <el-button slot="reference">选择</el-button>
                </el-popover>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="组号" prop="group_order">
            <el-input v-model.number="form.group_order" />
          </el-form-item>
          <el-form-item label="组描述" prop="group_name">
            <el-input v-model="form.group_name" />
          </el-form-item>
          <el-form-item label="列位置" prop="tag_col_order">
            <el-input v-model.number="form.tag_col_order" />
          </el-form-item>
          <el-form-item label="列位置内排序" prop="tag_col_inner_order">
            <el-input v-model.number="form.tag_col_inner_order" />
          </el-form-item>
          <el-form-item label="X坐标位置" prop="location_x">
            <el-input v-model.number="form.location_x" />
          </el-form-item>
          <el-form-item label="Y坐标位置" prop="location_y">
            <el-input v-model.number="form.location_y" />
          </el-form-item>
          <el-form-item label="套筒号" prop="bolt_code">
            <el-input v-model.number="form.bolt_code" />
          </el-form-item>
          <el-form-item label="扭矩值" prop="torque">
            <el-input v-model="form.torque" />
          </el-form-item>
          <el-form-item label="程序号" prop="progm_num">
            <el-input v-model.number="form.progm_num" />
          </el-form-item>
          <el-form-item label="控件类型" prop="control_type">
            <fastCode fastcode_group_code="CONTROL_TYPE" :fastcode_code.sync="form.control_type" control_type="select" size="mini" />
          </el-form-item>
          <el-form-item label="控件长度" prop="control_width">
            <el-input v-model.number="form.control_width" />
          </el-form-item>
          <el-form-item label="控件宽度" prop="control_height">
            <el-input v-model.number="form.control_height" />
          </el-form-item>
          <el-form-item label="测量对象" prop="quality_for">
            <el-input v-model="form.quality_for" />
          </el-form-item>
          <el-form-item label="采集项目名称" prop="tag_des">
            <el-input v-model="form.tag_des" />
          </el-form-item>
          <el-form-item label="采集项目单位" prop="tag_uom">
            <el-input v-model="form.tag_uom" />
          </el-form-item>
          <el-form-item label="标准值" prop="theory_value">
            <el-input v-model="form.theory_value" />
          </el-form-item>
          <el-form-item label="下限值" prop="down_limit">
            <el-input v-model="form.down_limit" />
          </el-form-item>
          <el-form-item label="上限值" prop="upper_limit">
            <el-input v-model="form.upper_limit" />
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <!--批量新增-->
      <el-dialog :fullscreen="false" top="10px" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true" :modal-append-to-body="false" title="批量新增" custom-class="step-attr-dialog" width="90%" :visible.sync="qualityBatchDialogVisible">
        <qualityBatch v-if="qualityBatchDialogVisible" ref="qualityBatch" :proceduce_id="proceduce_id" @updateVisible="updateVisible" />
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="质量ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.quality_id }}</el-descriptions-item>
                  <el-descriptions-item label="数据来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.quality_from }}</el-descriptions-item>
                  <el-descriptions-item label="数据采集项目" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_id }}</el-descriptions-item>
                  <el-descriptions-item label="合格标志" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_quality_sign_id }}</el-descriptions-item>
                  <el-descriptions-item label="组号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.group_order }}</el-descriptions-item>
                  <el-descriptions-item label="组描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.group_name }}</el-descriptions-item>
                  <el-descriptions-item label="列位置num" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_col_order }}</el-descriptions-item>
                  <el-descriptions-item label="列位置内排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_col_inner_order }}</el-descriptions-item>
                  <el-descriptions-item label="X坐标位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_x }}</el-descriptions-item>
                  <el-descriptions-item label="Y坐标位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_y }}</el-descriptions-item>
                  <el-descriptions-item label="套筒号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bolt_code }}</el-descriptions-item>
                  <el-descriptions-item label="扭矩值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.torque }}</el-descriptions-item>
                  <el-descriptions-item label="程序号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.progm_num }}</el-descriptions-item>
                  <el-descriptions-item label="控件类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.control_type }}</el-descriptions-item>
                  <el-descriptions-item label="控件长度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.control_width }}</el-descriptions-item>
                  <el-descriptions-item label="控件宽度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.control_height }}</el-descriptions-item>
                  <el-descriptions-item label="测量对象" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.quality_for }}</el-descriptions-item>
                  <el-descriptions-item label="采集项目名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_des }}</el-descriptions-item>
                  <el-descriptions-item label="采集项目单位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_uom }}</el-descriptions-item>
                  <el-descriptions-item label="标准值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.theory_value }}</el-descriptions-item>
                  <el-descriptions-item label="下限值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.down_limit }}</el-descriptions-item>
                  <el-descriptions-item label="上限值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.upper_limit }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="quality_from" min-width="100" label="数据来源" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_des" min-width="100" label="采集项目名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_uom" min-width="100" label="采集项目单位" />
            <el-table-column  :show-overflow-tooltip="true" prop="group_order" min-width="100" label="组号" />
            <el-table-column  :show-overflow-tooltip="true" prop="group_name" min-width="100" label="组描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_id" min-width="100" label="数据采集项目" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_quality_sign_id" min-width="100" label="合格标志" />
            <el-table-column  :show-overflow-tooltip="true" prop="theory_value" min-width="100" label="标准值" />
            <el-table-column  :show-overflow-tooltip="true" prop="down_limit" min-width="100" label="下限值" />
            <el-table-column  :show-overflow-tooltip="true" prop="upper_limit" min-width="100" label="上限值" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudrecipeCrPdureQuality from '@/api/mes/core/recipeCrPdureQuality'
import qualityBatch from './qualityBatch'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  quality_id: '',
  proceduce_id: '',
  quality_from: '',
  tag_id: '',
  tag_quality_sign_id: '',
  group_order: '',
  group_name: '',
  tag_col_order: '',
  tag_col_inner_order: '',
  location_x: '',
  location_y: '',
  bolt_code: '',
  torque: '',
  progm_num: '',
  control_type: '',
  control_width: '',
  control_height: '',
  quality_for: '',
  tag_des: '',
  tag_uom: '',
  theory_value: '',
  down_limit: '',
  upper_limit: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MES_RECIPE_CR_PDURE_QUALITY',
  components: { crudOperation, rrOperation, udOperation, pagination, qualityBatch },
  cruds() {
    return CRUD({
      title: '质量信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'quality_id',
      // 排序
      sort: ['quality_id asc'],
      // CRUD Method
      crudMethod: { ...crudrecipeCrPdureQuality },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    proceduce_id: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback()
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_cr_pdure_quality:add'],
        edit: ['admin', 'mes_recipe_cr_pdure_quality:edit'],
        del: ['admin', 'mes_recipe_cr_pdure_quality:del'],
        down: ['admin', 'mes_recipe_cr_pdure_quality:down']
      },
      rules: {
        quality_from: [{ required: true, message: '请选择数据来源', trigger: 'blur' }],
        tag_id: [{ required: true, message: '请选择采集项目标签', trigger: 'blur' }],
        tag_quality_sign_id: [{ required: true, message: '请选择合格标志标签', trigger: 'blur' }],
        group_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        tag_col_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        tag_col_inner_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        location_x: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        location_y: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        bolt_code: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        progm_num: [{ required: true, message: '请填写程序号', trigger: 'blur' }],
        control_width: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        control_height: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      customPopover1: false,
      customPopover2: false,
      qualityBatchDialogVisible: false
    }
  },
  watch: {
    proceduce_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.proceduce_id = this.proceduce_id
        defaultForm.proceduce_id = this.proceduce_id
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.proceduce_id = this.proceduce_id
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudrecipeCrPdureQuality
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              quality_id: data.quality_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    handleChooseTag1(tagId) {
      this.form.tag_id = tagId
      this.customPopover1 = false
    },
    handleChooseTag2(tagId) {
      this.form.tag_quality_sign_id = tagId
      this.customPopover2 = false
    },
    updateVisible(flag) {
      this.qualityBatchDialogVisible = flag
      this.crud.toQuery()
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
