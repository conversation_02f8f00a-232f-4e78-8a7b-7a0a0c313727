<template>
  <!--电芯排废-->
  <el-card shadow="never">
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <!--<el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="678" highlight-current-row @selection-change="crud.selectionChangeHandler">-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" size="small" :data="tableDataTable" style="width: 100%" :header-cell-style="{ background: '#eef1f6', color: '#545559' }" height="478" max-height="678" highlight-current-row>
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="pack_limit_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="pack_limit_code" label="范围值约束编码" />
          <el-table-column  :show-overflow-tooltip="true" prop="pack_limit_des" label="范围值约束描述" />
          <el-table-column  :show-overflow-tooltip="true" prop="down_limit" label="下限">
            <template slot-scope="scope">
              <el-input v-model="scope.row.down_limit" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="upper_limit" label="上限">
            <template slot-scope="scope">
              <el-input v-model="scope.row.upper_limit" />
            </template>
          </el-table-column>

          <el-table-column  label="有效标识" align="center" prop="enable_flag">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <div style="text-align: center">
      <!--<el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>-->
      <!-- 取消 -->
      <el-button type="primary" size="small" icon="el-icon-check" @click="saveData">保存</el-button>
      <!-- 确认 -->
    </div>
  </el-card>
</template>

<script>
import crudLimitRule from '@/api/mes/core/packLimitRule'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  pack_limit_id: '',
  pack_id: '',
  pack_limit_code: '',
  pack_limit_des: '',
  down_limit: '0',
  upper_limit: '0',
  enable_flag: 'Y'
}
export default {
  name: 'PackLimitRule',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    pack_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '电芯排废',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'pack_limit_id',
      // 排序
      sort: ['pack_limit_id asc'],
      // CRUD Method
      crudMethod: { ...crudLimitRule },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_pack_limit:add'],
        edit: ['admin', 'c_mes_fmod_recipe_pack_limit:edit'],
        del: ['admin', 'c_mes_fmod_recipe_pack_limit:del'],
        down: ['admin', 'c_mes_fmod_recipe_pack_limit:down']
      },
      rules: {},
      listLoadingTable: false,
      tableDataTable: []
    }
  },
  watch: {
    pack_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.pack_id = this.pack_id
        // this.crud.toQuery()
        this.toQuery()
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.pack_id = this.pack_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.pack_id = this.pack_id
      return true
    },

    toQuery() {
      const query = {
        user_name: Cookies.get('userName'),
        pack_id: this.query.pack_id
      }
      crudLimitRule
        .sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          console.log('查询')
          console.log(defaultQuery)

          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定范围值约束编码【' + data.pack_limit_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudLimitRule
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              pack_limit_id: data.pack_limit_id,
              enable_flag: data.enable_flag
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    // 修改下/上限、排废槽号
    saveData() {
      console.log('保存')
      console.log(this.tableDataTable)

      var rowcount = this.tableDataTable.length
      var index = 0
      crud.loading = true
      this.errorData = []
      var successData = []
      this.tableDataTable.forEach(item => {
        console.log('进入循环')
        console.log(item)

        const save = {
          user_name: Cookies.get('userName'),
          pack_limit_id: item.pack_limit_id,
          down_limit: item.down_limit,
          upper_limit: item.upper_limit
        }
        crudLimitRule
          .edit(save)
          .then(res => {
            console.log('进入保存函数')

            index++
            const defaultQuery = JSON.parse(JSON.stringify(res))
            console.log('进入保存函数')
            if (defaultQuery.code === 0) {
              successData.push({ id: item.pack_limit_id, msg: '' })
            } else {
              this.errorData.push({ id: item.pack_limit_id, msg: defaultQuery.msg })
            }
            if (rowcount === index) {
              crud.loading = false
              if (this.errorData.length === 0) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                })
                this.$emit('RefreshTagTable')
              } else {
                this.$message({
                  message: '保存操作共' + rowcount + '条数据，成功' + successData.length + '条，失败' + this.errorData.length + '条',
                  type: 'info'
                })
              }
            }
          })
          .catch(() => {
            if (rowcount === index) {
              this.listLoadingTable = false
            }
            this.$message({
              message: '新增异常',
              type: 'error'
            })
          })
      })
    }
  }
}
</script>
