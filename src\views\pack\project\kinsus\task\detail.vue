<template>
  <el-card shadow="always" style="margin-top: 10px">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog
          :title="$t('lang_pack.vie.packInformation')"
          width="55%"
          :before-close="handleClose"
          :visible.sync="dialogVisible"
        >
          <el-card
            ref="queryCard"
            shadow="never"
            class="wrapCard"
            style="margin-bottom: 10px"
          >
            <el-form
              ref="query"
              :inline="true"
              size="small"
              label-width="100px"
            >
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-10 col-12">
                  <div class="formChild col-md-4 col-12">
                    <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                      <!-- 料号 -->
                      <el-input
                        v-model="query.model_type"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormSecond formChild col-md-2 col-12">
                  <el-form-item>
                    <rrOperation />
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </el-card>
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 操作 -->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              width="100"
              align="center"
              fixed="left"
            >
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="text"
                  @click="changeSelect(scope.row)"
                >选择</el-button>
              </template>
            </el-table-column>
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="130"
              align="center"
            />
            <!-- 版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_version"
              :label="$t('lang_pack.vie.version')"
              width="120"
              align="center"
            />
            <!-- 板重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pcs_weight"
              :label="$t('lang_pack.vie.plateWei')"
              width="130"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.pcs_weight / 1000 }}
            </template>
            </el-table-column>
            <!-- Tray高度 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_height"
              label="Tray高度(um)"
              width="130"
              align="center"
            /> -->
            <!-- Tray重量 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_weight"
              label="Tray重量(mg)"
              width="130"
              align="center"
            /> -->
            <!-- Tray容积 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_volume"
              label="Tray容积"
              width="130"
              align="center"
            /> -->
            <!-- Overlay高度 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="overlay_height"
              label="Overlay高度(um)"
              width="130"
              align="center"
            />
            <!-- Overlay重量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="overlay_weight"
              label="Overlay重量(g)"
              width="130"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.overlay_weight / 1000 }}
            </template>
            </el-table-column>
            <!-- 重量偏差 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="weight_error"
              :label="$t('view.field.recipe.weight_error')"
              width="130"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.weight_error / 1000 }}
            </template>
            </el-table-column>
            <!-- 束带方式(十字) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_way1"
              label="束带方式(十字)"
              width="130"
              align="center"
            />
            <!-- 束带方式(一字) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_way3"
              label="束带方式(一字)"
              width="130"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import api from '@/api/pack/core/meReicpe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
const defaultForm = {}
export default {
  name: 'CARTASK',
  components: { rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 350,
      dialogVisible: false
    }
  },
  dicts: ['QR_TYPE', 'QR_CASE', 'QR_DIRECT'],
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    changeSelect(row) {
      this.dialogVisible = false
      this.$emit('ok', row)
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination {
  float: none !important;
  text-align: right;
}
::v-deep .el-dialog {
  margin-top: 5vh !important;
}
</style>
