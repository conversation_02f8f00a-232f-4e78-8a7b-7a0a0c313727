import request from '@/utils/request'

// 查询模组信息
export function mesGxMzManualMzInfoSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzManualMzInfoSelect',
    method: 'post',
    data
  })
}
// 查询扫描电芯数据
export function mesGxMzManualSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzManualSelect',
    method: 'post',
    data
  })
}
// 人工配组电芯校验
export function mesGxMzManualDxCheck(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzManualDxCheck',
    method: 'post',
    data
  })
}

// 查询当前工位IP地址
export function mesGxMzOffLineIpSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineIpSelect',
    method: 'post',
    data
  })
}

// 清除当前配组信息
export function mesGxMzOffLineStart(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineStart',
    method: 'post',
    data
  })
}

// 人工配组电芯校验(离线人工方式)
export function mesGxMzOffLineDxCheck(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineDxCheck',
    method: 'post',
    data
  })
}

// 选择订单
export function chooseOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineHmiMoSel',
    method: 'post',
    data
  })
}
// 获取订单详情
export function getOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineHmiMoIns',
    method: 'post',
    data
  })
}
// 展示订单详情
export function showOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineHmiMoMzRecipeSel',
    method: 'post',
    data
  })
}

// 提交离线数据
export function mesGxMzOffLineUpData(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzOffLineUpData',
    method: 'post',
    data
  })
}

export default { mesGxMzManualMzInfoSelect, mesGxMzManualSelect, mesGxMzManualDxCheck, mesGxMzOffLineIpSelect, mesGxMzOffLineStart, mesGxMzOffLineDxCheck, chooseOrderInfo, getOrderInfo, showOrderInfo, mesGxMzOffLineUpData }
