<template>
  <el-card shadow="always" style="margin-top: 10px">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog :title="$t('lang_pack.vie.packInformation')" width="55%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-card ref="queryCard" shadow="never" class="wrapCard" style="margin-bottom: 10px;">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-10 col-12">
                  <div class="formChild col-md-4 col-12">
                    <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                      <!-- 料号 -->
                      <el-input v-model="query.model_type" clearable size="small" />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormSecond formChild col-md-2 col-12">
                  <el-form-item>
                    <rrOperation />
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </el-card>
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :label="$t('lang_pack.commonPage.operate')" width="100" align="center" fixed="left">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button size="small" type="text" @click="changeSelect(scope.row)">选择</el-button>
              </template>
            </el-table-column>
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="130"
              align="center"
            />
            <!-- 版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_version"
              :label="$t('lang_pack.vie.version')"
              width="120"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_type"
              :label="$t('lang_pack.vie.setType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.array_type] }}
              </template>
            </el-table-column>
            <!-- PCS类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_type"
              :label="$t('lang_pack.vie.pcsType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.bd_type] }}
              </template>
            </el-table-column>
            <!-- 板长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.vie.plateLen')"
              width="130"
              align="center"
            />
            <!-- 板宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.vie.plateWid')"
              width="130"
              align="center"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_tickness"
              :label="$t('lang_pack.vie.plateThi')"
              width="130"
              align="center"
            />
            <!-- 板重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.vie.plateWei')"
              width="130"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudPack from '@/api/pack/pack'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
const defaultForm = {
}
export default {
  name: 'CARTASK',
  components: { rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...crudPack },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 350,
      dialogVisible: false
    }
  },
  dicts: ['QR_TYPE', 'QR_CASE', 'QR_DIRECT'],
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    changeSelect(row) {
      this.dialogVisible = false
      this.$emit('ok', row)
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
::v-deep .el-dialog{
  margin-top: 5vh !important;
}
</style>
