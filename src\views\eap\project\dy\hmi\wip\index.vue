<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.workOrder.workOrderNumber') + ':'">
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item style="margin: 0; float: right">
              <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="toButQuery('reset');sendCode()">{{ $t('view.button.search') }}</el-button>
              <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="toButResetQuery">{{ $t('view.button.reset') }}</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <!--扫描补报-->
    <el-drawer append-to-body :wrapper-closable="false" :title="$t('lang_pack.hmiMain.wipManualReport')" :visible.sync="wipScanVisible" size="60%" @close="handleClose">
      <wipScanReport v-if="wipScanVisible" ref="wipScanReport"
      :plan_id="plan_id"
      :group_lot_num="group_lot_num"
      :lot_num="lot_num"
      :pallet_num="pallet_num"
      :port_code="port_code"
      :plan_lot_count="plan_lot_count"
      :wip_ok_count="wip_ok_count"
      :wip_noread_count="wip_noread_count"
      :wip_short_count="wip_short_count" />
    </el-drawer>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <!-- <crudOperation show=""  /> -->
      <el-row :gutter="20">

        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="listLoadingTable"
            border
            size="small"
            :data="tableDataTable"
            style="width: 100%"
            :cell-style="cellStyle"
            :height="height"
            :highlight-current-row="true"
          >
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('lang_pack.workOrder.workOrderNumber')"
              align="center"
            />
            <!-- 端口号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="port_code"
              :label="$t('view.form.station')"
              align="center"
            />
            <!-- 物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.workOrder.materialNumber')"
              align="center"
            />
            <!-- 计划数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_lot_count"
              :label="$t('lang_pack.hmiMain.plannedQuantity')"
              align="center"
            />
            <!-- OK数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wip_ok_count"
              :label="$t('lang_pack.hmiMain.okQuantity')"
              align="center"
            />
            <!-- NoRead数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wip_noread_count"
              :label="$t('lang_pack.hmiMain.noReadQuantity')"
              align="center"
            />
            <!-- 少片数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wip_short_count"
              :label="$t('lang_pack.hmiMain.shortQuantity')"
              align="center"
            />
            <!-- 开始时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_start_time"
              :label="$t('lang_pack.hmiMain.startTime')"
              align="center"
            />
            <!-- 结束时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_end_time"
              :label="$t('lang_pack.hmiMain.endTime')"
              align="center"
            />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" width="150" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="manualWipDlg(scope.row)">{{ $t('lang_pack.hmiMain.wipManualReport') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <!--<pagination />-->
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { eapDyPlanManualWipSel } from '@/api/eap/project/dy/eapDyApsPlan'
import Cookies from 'js-cookie'
import wipScanReport from '@/views/eap/project/dy/hmi/wip/detail'
export default {
  name: 'WipManualReport',
  components: { wipScanReport },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      query: {
        lot_num: ''
      },
      listLoadingTable: false,
      tableDataTable: [],
      wipScanVisible: false,
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.toButQuery()
  },
  methods: {
    // 单元格样式控制
    cellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec'
    },
    toButQuery(op) {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        lot_num: this.query.lot_num
      }
      this.listLoadingTable = true
      eapDyPlanManualWipSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.tableDataTable = defaultQuery.data
          } else {
            this.tableDataTable = []
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    toButResetQuery() {
      // 重置
      this.query.lot_num = ''
      this.toButQuery()
    },
    manualWipDlg(row) {
      this.plan_id = row.plan_id
      this.group_lot_num = row.group_lot_num
      this.lot_num = row.lot_num
      this.pallet_num=row.pallet_num
      this.port_code= row.port_code
      this.plan_lot_count = row.plan_lot_count
      this.wip_ok_count = row.wip_ok_count
      this.wip_noread_count = row.wip_noread_count
      this.wip_short_count = row.wip_short_count
      this.wipScanVisible = true
    },
    //关闭抽屉事件
    handleClose(done){
      this.wipScanVisible = false
      this.toButQuery()
    },
  }
}
</script>
