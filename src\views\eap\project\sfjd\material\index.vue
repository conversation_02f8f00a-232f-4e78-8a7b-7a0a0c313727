<template>
  <div class="app-container">
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col :span="24">
        <el-card
          v-if="crud.props.searchToggle"
          class="box-card1"
          shadow="always"
          style="margin-top: 5px"
        >
          <el-form ref="query" :inline="true" size="small" label-width="100px">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-8 col-12">
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.meStationFlow.mainMaterialCode') + ':'">
                    <el-input v-model="query.material_code" clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.commonPage.validIdentification') + ':'">
                    <el-select v-model="query.enable_flag" clearable>
                      <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-2 col-12">
                <el-form-item>
                  <rrOperation />
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card1" shadow="always" style="margin-top: 5px">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-drawer
                append-to-body
                :wrapper-closable="false"
                :title="crud.status.title"
                :visible="crud.status.cu > 0"
                :before-close="crud.cancelCU"
                size="450px"
              >
                <el-form
                  ref="form"
                  class="el-form-wrap el-form-column"
                  :model="form"
                  :rules="rules"
                  size="small"
                  label-width="145px"
                  :inline="true"
                >
                  <el-form-item :label="$t('lang_pack.meStationFlow.mainMaterialCode')" prop="material_code">
                    <el-input v-model="form.material_code" clearable size="small" />
                  </el-form-item>
                  <el-form-item :label="$t('lang_pack.meStationFlow.mainMaterialDescription')" prop="material_des">
                    <el-input v-model="form.material_des" clearable size="small" />
                  </el-form-item>
                  <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                    <!-- 有效标识 -->
                    <el-radio-group v-model="form.enable_flag">
                      <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                        item.label
                      }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-form>
                <el-divider />
                <div style="text-align: center">
                  <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                    $t('lang_pack.commonPage.cancel') }}</el-button>
                  <!-- 取消 -->
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-check"
                    :loading="crud.status.cu === 2"
                    @click="crud.submitCU"
                  >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                  <!-- 确认 -->
                </div>
              </el-drawer>
              <!--工具栏-->
              <crudOperation show="" :permission="permission" />
              <el-table
                ref="table"
                v-loading="crud.loading"
                border
                size="small"
                :data="crud.data"
                style="width: 100%"
                :cell-style="crud.cellStyle"
                :height="height"
                :highlight-current-row="true"
                @header-dragend="crud.tableHeaderDragend()"
                @selection-change="crud.selectionChangeHandler"
              >
                <el-table-column type="selection" width="45" align="center" />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="material_code"
                  :label="$t('lang_pack.meStationFlow.mainMaterialCode')"
                  align="center"
                />
                <!-- 订单号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="material_des"
                  :label="$t('lang_pack.meStationFlow.mainMaterialDescription')"
                  align="center"
                />
                <!-- 有效标识 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="enable_flag"
                  :label="$t('lang_pack.commonPage.validIdentificationt')"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.enable_flag === 'Y' ? $t('zhcy.yes') : $t('zhcy.no') }}
                  </template>
                </el-table-column>
                <el-table-column :label="$t('zhcy.operations')" width="115" align="center" fixed="right">
                  <template slot-scope="scope">
                    <udOperation :data="scope.row" :permission="permission" />
                  </template>
                </el-table-column>
              </el-table>
              <!--分页组件-->
              <pagination />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import crudMaterial from '@/api/eap/project/sfjd/eapMaterial'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  material_code: '',
  material_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'MATERIAL_MAINTENANCE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.wx.materialMaintenance'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_id',
      // 排序
      sort: ['material_id desc'],
      // CRUD Method
      crudMethod: { ...crudMaterial },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  data() {
    return {
      height: document.documentElement.clientHeight - 245,
      permission: {
        add: ['admin', 'material_maintenance:add'],
        edit: ['admin', 'material_maintenance:edit'],
        del: ['admin', 'material_maintenance:del']
      },
      rules: {
        material_code: [{ required: true, message: this.$t('lang_pack.wx.enterCode'), trigger: 'blur' }],
        material_des: [{ required: true, message: this.$t('lang_pack.wx.enterDesc'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 245
    }
  },
  methods: {
  }
}
</script>
  <style scoped lang="less">
  .app-container {
    .analysisValue {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .reportVal {
        display: flex;
        flex-direction: column;
        text-align: center;
        width: 10%;
        background: #4874cb;
        border-radius: 8px;
        color: #fff;

        .val {
          font-size: 18px;
          margin: 10px 0;
        }

        .name {
          font-size: 16px;
          margin-bottom: 10px;
        }
      }
    }
    .btnGreen {
      background-color: #0d0 !important;
      border: none !important;
    }

    .btnRed {
      background-color: #ee1216 !important;
      border: none !important;
    }
  }
  </style>
