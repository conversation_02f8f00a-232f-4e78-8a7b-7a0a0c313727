<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modmain.templateCode')">
                <!-- 模板代码： -->
                <el-input v-model="query.flow_mod_main_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modmain.templateDescription')">
                <!-- 模板描述： -->
                <el-input v-model="query.flow_mod_main_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="group_left">
          <el-button size="small" icon="el-icon-upload2" title="流程图导入" @click="importDialogVisible = true" />
          <el-button v-permission="permission.down" :loading="crud.downloadLoading" :disabled="!crud.data.length" title="流程图导出" size="small" icon="el-icon-download" @click="doExport" />
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.modmain.templateCodet')" prop="flow_mod_main_code">
            <!-- 模板代码 -->
            <el-input v-model="form.flow_mod_main_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modmain.templateDescriptiont')" prop="flow_mod_main_des">
            <!-- 模板描述 -->
            <el-input v-model="form.flow_mod_main_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modmain.programDriven')" prop="mod_function_dll">
            <!-- 程序驱动 -->
            <el-input v-model="form.mod_function_dll" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <el-drawer id="flow-chart-drawer" :wrapper-closable="false" :with-header="false" :visible.sync="editFlowChart" direction="rtl" size="90%">
        <flowChart
          v-if="mainchartShow"
          ref="flowChart"
          :flow_mod_main_id="currentRow.flow_mod_main_id"
          :flow_mod_main_des="currentRow.flow_mod_main_des"
          @closeDrawer="
            editFlowChart = false
            mainchartShow = false
          "
        />
      </el-drawer>
      <el-dialog title="代码设计" :fullscreen="true" custom-class="code-dialog-height" :visible.sync="codeEditDialogVisible">
        <div slot="title">
          {{ currentRow.flow_mod_main_des }}
        </div>
        <div class="code-dialog-content">
          <coding v-if="codeEditDialogVisible" ref="coding" :flow_mod_main_id="currentRow.flow_mod_main_id" />
        </div>
      </el-dialog>
      <!-- 流程图json导入 -->
      <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="流程图导入" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input
            v-if="isUpLoadError"
            v-model="errorMsg"
            type="textarea"
            :rows="5"
          />
          <div style="text-align: center;margin-top:10px">
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" style="margin-left:10px" @click="toButDrawerUpload">上传</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="flow_mod_main_id" label="id" />
            <el-table-column :show-overflow-tooltip="true" prop="flow_mod_main_code" :label="$t('lang_pack.modmain.templateCodet')" width="220" />
            <!-- 模板代码 -->
            <el-table-column :show-overflow-tooltip="true" prop="flow_mod_main_des" :label="$t('lang_pack.modmain.templateDescriptiont')" width="400" />
            <!-- 模板描述 -->
            <el-table-column :show-overflow-tooltip="true" prop="mod_function_dll" :label="$t('lang_pack.modmain.programDriven')" width="400" />
            <!-- 程序驱动 -->
            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                  <template slot="right">
                    <el-button size="small" type="text" @click="openFlowChart(scope.row)">设计</el-button>
                    <!-- <el-button size="small" type="text" style="margin-left:0px;" @click="openCoding(scope.row)">代码</el-button> -->
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { exportPost } from '@/utils/manage'
import { handleJson } from '@/utils/manageFunction'
import crudFlowModMain from '@/api/core/flow/rcsFlowModMain'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import flowChart from '@/views/core/flow/mod/flow-chart'
import coding from '@/views/core/flow/mod/coding'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  flow_mod_main_id: '',
  flow_mod_main_code: '',
  flow_mod_main_des: '',
  mod_function_dll: '',
  enable_flag: 'Y'
}
export default {
  name: 'RCS_FLOW_MOD_MAIN',
  components: { flowChart, coding, crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '流程模板',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'flow_mod_main_id',
      // 排序
      sort: ['flow_mod_main_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowModMain },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'rcs_flow_mod_main:add'],
        edit: ['admin', 'rcs_flow_mod_main:edit'],
        del: ['admin', 'rcs_flow_mod_main:del'],
        down: ['admin', 'rcs_flow_mod_main:down']
      },
      rules: {
        flow_mod_main_code: [{ required: true, message: '请输入模板代码', trigger: 'blur' }],
        flow_mod_main_des: [{ required: true, message: '请输入模板描述', trigger: 'blur' }],
        mod_function_dll: [{ required: true, message: '请输入程序驱动', trigger: 'blur' }]
      },
      selectedRows: [],
      currentRow: {},
      mainchartShow: false,
      editFlowChart: false,
      codeEditDialogVisible: false,
      smallModelData: [],
      uploadLimit: 1,
      uploadAccept: '.json',
      fileList: [],
      importDialogVisible: false,
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: ''
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {},
  methods: {
    openFlowChart(row) {
      this.currentRow = row
      this.mainchartShow = true
      this.editFlowChart = true
    },
    // openCoding(row) {
    //   this.currentRow = row
    //   this.codeEditDialogVisible = true
    // },
    doExport() {
      this.selectedRows = this.$refs.table.selection
      if (!this.selectedRows.length) {
        this.$message.warning('请先勾选要导出数据!')
        return
      }
      const ids = this.selectedRows.map(row => row.flow_mod_main_id) // 获取勾选行的 id 列表
      const data = {
        flowModMainIds: ids
      }
      this.crud.downloadLoading = true
      const url = 'aisEsbWeb/core/flow/CoreFlowModMainExport'
      exportPost(url, data).then((res) => {
        this.crud.downloadLoading = false
        handleJson(res)
      })
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = 'core/flow/CoreFlowModMainImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px !important;
}
.code-dialog-height .el-dialog__body {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-y: auto;
}
.code-dialog-content {
  top: 60px;
  right: 5px;
  bottom: 5px;
  left: 5px;
  overflow-y: auto;
  position: absolute;
}
</style>
