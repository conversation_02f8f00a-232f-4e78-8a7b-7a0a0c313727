
#svg {
    width: 1920px;
    height: calc(100vh - 40px);
    /* overflow:visible; */
}
#chartSop {
    position: relative;
    width: 1920px;
    height: calc(100vh - 40px);
    border: 0px solid #dfdfdf;
}
.title {
    font-size:18px;
    font-weight: bold;
}
.unselectable {
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color:#dfdfdf;
}

.connector {
    cursor: crosshair;
    opacity: 0;
}

.connector.active {
    opacity: 1;
    fill: white;
    stroke: #bbbbbb;
    stroke-width: 1px;
}

.connector:hover {
    stroke: red;
}
.tool_btn {
    cursor:pointer;
    opacity: 0;
}

.tool_btn.active {
    opacity: 1;
    fill: white;
    stroke: #bbbbbb;
    stroke-width: 1px;
}

#svg .selection {
    stroke: lightblue;
    fill: lightblue;
    fill-opacity: 0.8;
    display: none;
}

#svg .selection.active {
    display: block;
}
.commodity-sign-wrap{
	animation:blink 1s infinite;
	-webkit-animation:blink 1s infinite; /*Safari and Chrome*/
}
@keyframes blink{
	0%{
		opacity: 0;
	}
	50%{
		opacity: 100;
	}
	100%{
		opacity: 0;
	}
}
@-webkit-keyframes blink{
	0%{
		opacity: 0;
	}
	50%{
		opacity: 100;
	}
	100%{
		opacity: 0;
	}
}