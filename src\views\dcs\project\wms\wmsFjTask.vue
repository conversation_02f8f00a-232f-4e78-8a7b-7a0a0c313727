<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="任务号:">
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="项目编码：">
                <el-input v-model="query.project_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="零件编号：">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="垛位号：">
                <el-input v-model="query.stock_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            slot="reference"
            v-permission="permission.del"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="handleSelectionChange"
          >
            删除
          </el-button>
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="task_num" width="120" label="任务号" />
            <el-table-column :show-overflow-tooltip="true" prop="project_code" width="120" label="项目编码" />
            <el-table-column :show-overflow-tooltip="true" prop="material_code" width="120" label="零件编号" />
            <el-table-column :show-overflow-tooltip="true" prop="serial_num" width="120" label="钢板自编号" />
            <el-table-column :show-overflow-tooltip="true" prop="material_length" label="零件长" />
            <el-table-column :show-overflow-tooltip="true" prop="material_width" label="零件宽" />
            <el-table-column :show-overflow-tooltip="true" prop="material_thickness" label="零件厚" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_group_code" label="垛位区域" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_code" width="120" label="垛位号" />
            <el-table-column :show-overflow-tooltip="true" prop="start_date" width="160" label="入库任务开始时间" />
            <el-table-column :show-overflow-tooltip="true" prop="end_date" width="160" label="入库任务结束时间" />
            <el-table-column :show-overflow-tooltip="true" prop="task_status" width="120" label="任务状态">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="position_x" width="160" label="零件放置横坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="position_y" width="160" label="零件放置纵坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="position_z" width="160" label="零件放置高坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="kit_structure_no" width="120" label="构件号" />
            <el-table-column :show-overflow-tooltip="true" prop="kit_task_num" width="160" label="焊接任务号" />
            <el-table-column :show-overflow-tooltip="true" prop="kit_flag" width="100" label="是否成套">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.KIT_FLAG[scope.row.kit_flag] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="kit_material_type" width="100" label="零件类型">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.KIT_MATERIAL_TYPE[scope.row.kit_material_type] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="kit_structure_sn" width="140" label="构件件号" />
            <el-table-column :show-overflow-tooltip="true" prop="kit_task_date" width="140" label="零件生产日期" />
            <el-table-column :show-overflow-tooltip="true" prop="grab_direction" label="抓取方向" />
            <el-table-column :show-overflow-tooltip="true" prop="off_set_x" width="140" label="大车偏移量" />
            <el-table-column :show-overflow-tooltip="true" prop="off_set_y" width="140" label="小车偏移量" />
            <el-table-column :show-overflow-tooltip="true" prop="start_cell_row" width="160" label="零件放置开始行" />
            <el-table-column :show-overflow-tooltip="true" prop="end_cell_row" width="160" label="零件放置结束行" />
            <el-table-column :show-overflow-tooltip="true" prop="start_cell_col" width="160" label="零件放置开始单元格" />
            <el-table-column :show-overflow-tooltip="true" prop="end_cell_col" width="160" label="零件放置结束单元格" />
            <!-- Table单条操作-->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="100"
              fixed="right"
            >
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="!(scope.row.task_status === 'FINISH' || scope.row.task_status === 'PLAN')" :delete-edit="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsFjTask from '@/api/dcs/project/wms/wmsFjTask'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_FJ_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '分拣入库任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'fj_task_id',
      // 排序
      sort: ['fj_task_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsFjTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_fj_task:add'],
        edit: ['admin', 'b_dcs_wms_fj_task:edit'],
        del: ['admin', 'b_dcs_wms_fj_task:del']
      }
    }
  },
  dicts: ['PROD_TASK_STATUS', 'KIT_FLAG', 'KIT_MATERIAL_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    handleSelectionChange() {
      const ids = this.crud.selections.filter(item => item.task_status === 'FINISH' || item.task_status === 'PLAN').map(temp => temp.fj_task_id).join(',')
      this.$confirm(`确认要删除选中的${this.crud.selections.length}条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!ids) {
          this.$message({ type: 'error', message: '请至少勾选一条任务状态在【已完成】或【计划中】的数据' })
          return
        }
        crudWmsFjTask.del({ ids }).then(res => {
          if (res.code === 0) {
            this.crud.toQuery()
            this.$message({ type: 'success', message: '删除成功' })
          }
        })
      })
    }
  }
}
</script>

    <style>
    .table-descriptions-label {
      width: 150px;
    }
    .table-descriptions-content {
      width: 150px;
    }
    </style>
