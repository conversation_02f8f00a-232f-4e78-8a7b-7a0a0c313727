import request from '@/utils/request'

// 查询大屏配置详情信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigDetailSel',
    method: 'post',
    data
  })
}
// 新增大屏配置详情信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigDetailIns',
    method: 'post',
    data
  })
}
// 修改大屏配置详情信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigDetailUpd',
    method: 'post',
    data
  })
}
// 删除大屏配置详情信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigDetailDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

