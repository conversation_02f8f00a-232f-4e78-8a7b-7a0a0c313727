<template>
    <div class="app-container">
        <el-row :gutter="24">
            <el-col :span="12">
                <el-card class="box-card1">
                    <div slot="header" class="wrapTextSelect">
                        <span class="title">服务器监控</span>
                        <template>
                            <span>服务器：
                                <el-select v-model="serverID" clearable filterable @change="changeServer">
                                    <el-option v-for="item in modalData.serverInfos" :key="item.serverId"
                                        :label="item.serverIP" :value="item.serverId" />
                                </el-select>
                            </span>
                        </template>
                    </div>
                    <template>
                        <span class="progress">
                            <span>CPU:</span>
                            <el-progress :text-inside="true" :stroke-width="30"
                                :percentage="serverObj.CPUUtilizationRate > 100 ? 100 : serverObj.CPUUtilizationRate"
                                status="success"></el-progress>
                        </span>
                        <span class="progress">
                            <span>硬盘:</span>
                            <el-progress :text-inside="true" :stroke-width="30"
                                :percentage="serverObj.diskRemaining > 100 ? 100 : serverObj.diskRemaining"
                                status="success"></el-progress>
                        </span>
                        <span class="progress">
                            <span>内存:</span>
                            <el-progress :text-inside="true" :stroke-width="30"
                                :percentage="serverObj.memoryRemainingQuantity > 100 ? 100 : serverObj.memoryRemainingQuantity"
                                status="success"></el-progress>
                        </span>
                    </template>
                    <template>
                        <div class="serverControl">
                            <div v-for="(item,index) in serverAppData"  @click="handleControl(item,index)">
                                <!-- <svg-icon :icon-class="item.startIcon" /> -->
                                
                                <el-badge :hidden="!item.abnormalFlag" :value="'!'"  class="item">
                                    <!-- <div v-html="item.status === 0 ? item.startIcon : item.errorIcon" class="svg-icon1"></div> -->
                                    <svg-icon :icon-class="item.appName + item.status" class="svg-icon1"/>
                                </el-badge>
                                <p>{{ item.appName }}</p>
                            </div>
                        </div>
                    </template>
                </el-card>
                <el-card class="box-card1" style="margin-top: 20px;">
                    <div slot="header" class="wrapTextSelect">
                        <span class="title">
                            <span :class="deviceFlag ? 'active' : ''" @click="changeStep">设备异常</span>
                            <span>&nbsp;/&nbsp;</span>
                            <span :class="!deviceFlag ? 'active' : ''" @click="changeStep">流程异常</span>
                        </span>
                    </div>
                    <div class="InfoRoll">
                        <deviceScroll ref="deviceScroll" :list="dataList" :deviceFlag="deviceFlag"></deviceScroll>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card class="box-card1 costums">
                    <div slot="header" class="wrapTextSelect">
                        <span class="title">应用指标监控--{{ controlObj.appName }}</span>
                    </div>
                        <!-- <el-alert v-for="item in controlObj.appMonitorInfo" :title="item.monitor_item"
                            :type="item.alarm_type == 0 ? 'success' : item.alarm_type == 1 ? 'warning' : item.alarm_type == 2 ? 'error' : '' " 
                            :description="isJsonString(item)" :closable="false" show-icon
                             :class="`custom-alert custom${item.id}`" effect="light">
                    </el-alert>  -->
                    <div v-for="item in controlObj.appMonitorInfo" class="control" 
                    :class="item.alarm_type == 0 ? 'controlSuccess' : item.alarm_type == 1 ? 'controlWarning' : item.alarm_type == 2 ? 'controlError' : '' " 
                    >
                        <span class="left">{{ item.monitor_item }}</span>
                        <div v-if="Object.prototype.toString.call(item.monitor_value) === '[object Object]'" class="right">
                            <span v-for="(val,key) in item.monitor_value" class="value">
                                <span class="block">    
                                    <span class="leftKey">{{ key }}</span>
                                    <span class="rightVal">{{ val }}</span>
                                </span>
                            </span>
                        </div>
                        <span v-else class="value2">{{ item.monitor_value }}</span>
                    </div>
                    <el-empty v-if="!controlObj.appMonitorInfo" :image-size="200"></el-empty>
                </el-card>
                <el-card class="box-card1 costums" style="margin-top: 20px;">
                    <div slot="header" class="wrapTextSelect">
                        <span class="title">实时调控--{{ controlObj.appName }}</span>
                    </div>
                    <div class="InfoRoll">
                        <!-- <el-alert v-for="item in controlObj.control"  type="info" :closable="false" class="custom-button1" show-icon
                            effect="light">
                            <el-tooltip class="item" effect="dark" :content="item.content" placement="top">
                                <span class="el-alert__title">{{item.content}}</span>
                            </el-tooltip>
                            <div class="custom-right">
                                <el-button type="primary" size="mini" @click="haneleRelease(item)">{{ item.name }}</el-button>
                            </div>
                        </el-alert> -->
                        <div v-for="item in controlObj.control"  class="custom-button1">
                            <div class="title">
                                <el-tooltip class="item" effect="dark" :content="item.content" placement="top">
                                    <span >{{item.content}}</span>
                                </el-tooltip>
                            </div>
                            <div class="custom-right">
                                <el-button type="primary" size="mini" @click="haneleRelease(item)">{{ item.name }}</el-button>
                            </div>
                        </div>
                        <el-empty v-if="!controlObj.control" :image-size="200"></el-empty>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import deviceScroll from '../components/homeModules/index2.vue'
import { getServer,getControlRelease } from '@/api/data'
export default {
    components: { deviceScroll },
    name: 'HOME',
    data() {
        return {
            socket: null,
            dataList: [],
            modalData: {
                alarmInfos: [], // 设备异常数据
                serverInfos: [], // 服务器监控数据
                stepLogs: [] // 流程异常数据
            },
            firstFlag: true,
            deviceFlag: true,
            serverAppData: [], //服务器
            serverID: '',
            serverIP:'',
            serverIndex:0,
            serverObj: {},
            controlData:[],
            controlObj:{},
            controlIndex:0,
            timer:null,
            webSocketTimer:null,
        }
    },
    created() {
        this.init()
    },
    beforeDestroy(){
        if(this.timer){
            clearInterval(this.timer)
        }
        if(this.webSocketTimer){
            clearTimeout(this.webSocketTimer)
        }
    },
    methods: {
        // 调用websocket
        init() {
            if (typeof WebSocket === 'undefined') {
                alert('您的浏览器不支持socket')
            } else {
                try{
                    // 实例化socket
                    // this.socket = new WebSocket( 'ws://*************:8014/aisEsbWeb/websocket/main/core/1')
                    this.socket = new WebSocket( window._CONFIG['websocketURL'] + 'aisEsbWeb/websocket/main/core/1')
                    // 监听socket连接
                    this.socket.onopen = this.open
                    // 监听socket错误信息
                    this.socket.onerror = this.error
                    // 监听socket消息
                    this.socket.onmessage = this.getMessage
                    this.socket.onclose = this.closeSocket
                }catch(e){
                    this.$message.error(e.message || '连接失败')
                }
            }
        },
        open() {
            console.log("socket连接成功")
            if(this.timer){
                clearInterval(this.timer)
            }
            this.timer = setInterval(() => { //每4秒发送一条信息，防止websocket断开
                this.send();
            }, 4000)
        },
        error() {
            console.log("websocket链接失败，尝试重连")
            this.webSocketTimer = setTimeout(()=>{ //尝试重新连接
                this.init()
            },4500)
        },
        getMessage(msg) {
            let data = JSON.parse(msg.data);
            if (!data) return
            this.modalData.alarmInfos = data.alarmInfos //设备异常信息
            this.modalData.stepLogs = data.stepLogs //步骤消息
            data.serverInfos.map(item => {
                item.CPUUtilizationRate = +item.CPUUtilizationRate //转成Num类型
                item.diskRemaining = +item.diskRemaining
                item.memoryRemainingQuantity = +item.memoryRemainingQuantity
            })
            this.modalData.serverInfos = data.serverInfos
            this.serverObj = data.serverInfos[this.serverIndex]  //默认获取第一个对象
            // 判断第一次进来  后面就不进来
            if (this.firstFlag) {
                this.firstFlag = false
                this.dataList = data.alarmInfos //默认显示设备异常的数据
                this.serverID = data.serverInfos[0].serverId || '' //拿到第一个设备的id
                this.serverIP = data.serverInfos[0].serverIP || ''
                this.detailedInd(this.serverID,this.serverIP)
            }
        },
        send: function () {
            this.socket.send(1)
        },
        closeSocket: function () {
            console.log('socket已经关闭')
        },
        handleControl(item,index){
            if(item.status == 1 && !item.abnormalFlag) return 
            this.controlObj = item
            this.controlIndex = index
        },
        haneleRelease(item){
            if(!item.handleUrl && !item.key) return 
            const url = `${item.handleUrl}${this.controlObj.appName}/${item.key}`
            getControlRelease(this.serverIP + ':' + location.port,url).then(res=>{
                if(res.code == 200){
                    this.detailedInd(this.serverID,this.serverIP)
                    this.$message.success(this.controlObj.appName+'操作成功')
                }else{
                    this.$message.error(res.msg)
                }
            })
        },
        detailedInd(id,ip) {
            if(!id && !ip){
                this.$message.warning('请先设置服务器id或者服务器ip')
                return
            } 
            let url = ip + ':' + location.port
            getServer(url).then(res => {
                if (res.code == 200 && Object.keys(res.data).length > 0) {
                    for (let key in res.data.apps) {
                        if (id == key) {
                             res.data.apps[key].forEach(app=>{
                               if(app.appMonitorInfo){
                                    app.appMonitorInfo.forEach(info=>{
                                        if(info.monitor_value && typeof info.monitor_value === 'string'){
                                            try {
                                                info.monitor_value = JSON.parse(info.monitor_value);
                                            } catch (e) {
                                            // 如果抛出异常则不做任何操作
                                            }
                                        }
                                    })
                               }
                            })
                            this.serverAppData = res.data.apps[key]
                            this.controlObj = this.serverAppData[this.controlIndex]
                        }
                    }
                }
            })
        },
        isJsonString(item){
            if (Object.prototype.toString.call(item.monitor_value) === '[object Object]') {
                // var str = '';
                this.$nextTick(()=>{
                    var dom = document.querySelector('.custom' + item.id).querySelector('.el-alert__content')
                    dom.innerHTML = ''
                    for (let key in item.monitor_value) {
                        dom.innerHTML += `
                        `; 
                        // <span>${key}-${item.monitor_value[key]}</span><br>
                    }
                })
                // for (let key in item.monitor_value) {
                //     str += `${key}-${item.monitor_value[key]}`; // Hier wird ein Zeilenumbruch hinzugefügt
                // }
                // return str;
            } else {
                return `${item.monitor_value}<br>`; // Hier wird ein Zeilenumbruch hinzugefügt
            }
        },
        changeStep() {
            this.deviceFlag = !this.deviceFlag
            this.$refs.deviceScroll.currentPage = 1
            this.$refs.deviceScroll.pageSize = 10
            this.$refs.deviceScroll.deviceData = []
            if (this.deviceFlag) {
                this.dataList = this.modalData.alarmInfos
                return
            }
            this.dataList = this.modalData.stepLogs
        },
        changeServer(val) {
            if (!val) return
            this.serverIndex = this.modalData.serverInfos.findIndex(e=>{return e.serverId === val})
            this.serverObj = this.modalData.serverInfos.filter(e => { return e.serverId === val })[0] || {}
            this.serverIP = this.modalData.serverInfos.filter(e => { return e.serverId === val })[0].serverIP || ''
            this.detailedInd(val,this.serverIP)
        },
        format(percentage) {
            return `${percentage}%`
        }
    }
}
</script>
<style scoped lang="less">
.app-container {
    .box-card1{
        height: 420px !important;
    }
    .costums{
        ::v-deep .el-card__body{
            height: 88%;
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 10px;
                height: 10px;
                background-color: #ebeef5;
                cursor: pointer !important;
            }
            &::-webkit-scrollbar-thumb{
                box-shadow: inset 0 0 6px rgba(255,255,255,.3);
                -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
                background-color: #f6f9ff;
                cursor: pointer !important;
            }
        }
        .control{
            width: 100%;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-radius: 5px;
            .left{
                color: #fff;
            }
            .right{
                min-width: 45%;
               display: flex;
               flex-direction: column;
               .value{
                    
                    display: block;
                    color: #fff;
                    .block{
                        padding: 5px 0;
                        display: flex;
                        justify-content: space-around;
                        .leftKey{
                            width: 70%;
                            display: block;
                        }
                        .rightVal{
                            width: 20%;
                            display: block;
                        }
                    }
               }
            }
            .value2{
                color: #fff;
               }
        }
        .controlSuccess{
            background: #67c23a;
        }
        .controlWarning{
            background: #e6a23c;
        }
        .controlError{
            background: #f56c6c;
        }
    }
    ::v-deep .el-card__header,
    ::v-deep .el-card__body {
        padding: 10px !important;
    }
    .item {
        margin-top: 10px;
        margin-right: 40px;
        }

    .wrapTextSelect {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            font-size: 24px;
            font-weight: 500;
            cursor: pointer;

            .active {
                color: #79a0f1;
            }
        }
    }

    // 右上部分
    .progress {
        display: flex;
        align-items: center;
        margin: 24px 0;

        span {
            font-size: 16px;
            font-weight: 550;
        }

        .el-progress {
            width: 90%;
            margin-left: 3%;
        }
    }
    .serverControl {
        display: flex;
        overflow: auto;
        div {
            flex-direction: column;
            text-align: center;
            width: 120px;
            cursor: pointer;
            // .svg-icon {
            //     width: 5em;
            //     height: 5em;
            // }

            p {
                font-size: 18px;
                font-weight: 550;
            }
        }
    }
    .InfoRoll {
        overflow: hidden;
        height: 350px;
    }

    .home-message {
        width: 100%;
        height: 35px;
        line-height: 35px;
        color: #333333;
        border-radius: 5px;
        margin-bottom: 5px;
        font-size: 13px;
        display: flex;
    }

    //   右边部分
    ::v-deep .custom-alert {
        margin-bottom: 10px;

        .el-alert__content {
            width: 95%;

            .el-alert__title {
                font-size: 16px;
                font-weight: 550;
            }

            .el-alert__description {
                float: right;
                font-size: 16px;
                font-weight: 550;

            }
        }

    }

    ::v-deep .custom-button1 {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f4f4f5;
        border-radius: 4px;
        padding: 8px 16px;
        .title{
            width: 85%;
            font-size: 18px;
            font-weight: 550;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 18px;
            font-weight: 700;
            color: #909399;
        }
        .custom-right{
            width: 15%;
            display: flex;
            justify-content: right;
        }
        // .el-alert__content {
        //     width: 100%;
        //     display: flex;
        //     align-items: center;
        //     justify-content: space-between;

            

        //     .el-alert__description {
        //         width: 100%;
        //         display: flex;
        //         align-items: center;
        //         justify-content: space-around;
        //         .el-alert__title {
        //             font-size: 18px;
        //             font-weight: 550;
        //             overflow: hidden;
        //             white-space: nowrap;
        //             text-overflow: ellipsis;
        //             width: 560px;
        //         }
        //         .custom-right {
        //             display: flex;
        //             align-items: center;
        //             justify-content: center;

        //             span {
        //                 font-size: 16px;
        //                 font-weight: 550;
        //             }
        //         }

        //     }
        // }
    }
}
</style>
<style lang="less">
.svg-icon1{
    width: 5em !important;
    height: 5em !important;
}
.serverControl {
    &::-webkit-scrollbar{
        width: 10px;
        height: 10px;
        background-color: #ebeef5;
        cursor: pointer !important;
    }
    &::-webkit-scrollbar-thumb{
        box-shadow: inset 0 0 6px rgba(255,255,255,.3);
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        background-color: #f6f9ff;
        cursor: pointer !important;
    }
}
</style>