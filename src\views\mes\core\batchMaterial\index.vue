<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="工位：">
                <el-select
                  v-model="currentStation"
                  filterable
                  clearable
                  @change="stationChange"
                >
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_id"
                    :label="item.station_code + ' ' + item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item label="物料条码：">
                <el-input v-model="barCode" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item>
                <el-button type="warning" @click="handleScan">扫 描</el-button>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            :data="batchData"
            style="width: 100%"
            height="520"
            :row-style="{ height: '45px' }"
            :cell-style="{ padding: '0px' }"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              min-width="100"
              label="物料号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_des"
              min-width="100"
              label="物料描述"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lable_barcode"
              min-width="100"
              label="物料条码"
            />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px; text-align: right">
            <el-button-group>
              <el-button type="primary">总数量：{{ page.total }}</el-button>
              <el-button type="primary">当前第{{ nowPageIndex }}页</el-button>
              <el-button type="primary" @click="pageQuery('pre')"
                >&lt;&nbsp;上一页</el-button
              >
              <el-button type="primary" @click="pageQuery('next')"
                >下一页&nbsp;&gt;</el-button
              >
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudStationFlow from "@/api/mes/core/stationFlow";
import { sel as selStation } from "@/api/core/factory/sysStation";
import stationMonitor from "@/api/mes/core/hmi/stationMonitor";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud, pagination } from "@crud/crud";
import crudOperation from "@crud/CRUD.operation";
const defaultForm = {};
export default {
  name: "MES_BATCH_MATERIAL",
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: "批次物料管理",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "station_flow_id",
      // 排序
      sort: ["station_flow_id desc"],
      // CRUD Method
      crudMethod: { ...crudStationFlow },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      query: {
        tableSize: 40,
        enable_flag: "Y",
      },
    });
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 390,
      stationData: [],
      batchData: [],
      nowPageIndex: 1, // 当前页数
      pageList: [],
      barCode: "",
      currentStation: "",
    };
  },
  computed: {},
  mounted: function () {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270;
    };
  },
  created: function () {
    this.stationData = [];
    selStation({
      userID: Cookies.get("userName"),
      enable_flag: "Y",
      prod_line_id: 1,
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res));
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data;
          }
        }
      })
      .catch(() => {
        this.$message({
          message: "查询异常",
          type: "error",
        });
      });
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1;
      this.pageList = [];
      this.query.page_dirct = "";
      this.query.page_id = "";
      this.crud.toQuery();
    },
    pageQuery(pageDirct) {
      if (pageDirct === "pre") {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1;
          this.$message({
            message: "已置顶",
            type: "info",
          });
          return;
        }
        this.nowPageIndex = this.nowPageIndex - 1;
        const preId = this.pageList[this.nowPageIndex];
        this.query.page_dirct = pageDirct;
        this.query.page_id = preId;
        this.crud.toQuery();
      } else {
        const total_page =
          this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize);
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: "已置底",
            type: "info",
          });
          return;
        }
        const preId = this.crud.data[0].id;
        this.pageList[this.nowPageIndex] = preId;
        this.nowPageIndex = this.nowPageIndex + 1;
        this.query.page_dirct = pageDirct;
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id;
        this.crud.toQuery();
      }
    },

    stationChange(val) {
      this.batchData = [];
      const query = {
        user_name: Cookies.get("userName"),
        station_code: val,
      };
      stationMonitor
        .mesRecipeBatchMaterialSelect(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.batchData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },

    // 扫描
    handleScan() {
      const query = {
        user_name: Cookies.get("userName"),
        station_code: this.currentStation,
        barcode: this.barCode,
      };
      stationMonitor
        .mesRecipeBatchMaterialScan(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            this.$message({ message: "扫描与验证成功", type: "success" });
            this.stationChange(this.currentStation);
          } else {
            this.$message({ message: defaultQuery.msg, type: "error" });
          }
        })
        .catch(() => {
          this.$message({ message: "扫描物料异常", type: "error" });
        });
    },
  },
};
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
