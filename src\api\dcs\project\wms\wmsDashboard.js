import request from '@/utils/request'
// 插入写入点位事件
export function saveScadaEvent(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsSaveScadaEventInsert',
    method: 'post',
    data
  })
}
// 报警查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsAlarmHistoriesSelect',
    method: 'post',
    data
  })
}
// 报警新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsAlarmHistoriesInsert',
    method: 'post',
    data
  })
}
// 报警编辑
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsAlarmHistoriesUpdate',
    method: 'post',
    data
  })
}
export default { saveScadaEvent, sel, add, edit }
