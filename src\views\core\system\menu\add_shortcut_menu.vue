<template>
  <div>
    <div id="shortcutMenuDiv" v-loading="loading" style="height: 450px">
      <el-scrollbar style="height: 100%">
        <el-collapse v-model="activeNames">
          <el-collapse-item v-for="(item, index) in treeData" :key="index" :title="item.menu_group_des" :name="item.menu_group_id">
            <div v-for="(item1, index1) in item.children" :key="index1" class="grid-content bg-purple" @click="handleChoose(item1, index1)">
              <el-avatar size="large" icon="el-icon-check" :class="item1.shortcut_menu_id === 0 ? 'el-avatar1' : 'el-avatar2'" />

              <div class="el-avatar">
                <svg-icon :icon-class="item1.menu_item_ico" />
              </div>
              <div class="menu-title">{{ item1.menu_item_des }}</div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
    </div>
    <el-divider />
    <div style="text-align: center">
      <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">取消</el-button>
      <el-button type="primary" size="small" icon="el-icon-check" @click="toFormFromSubmit('form')">确认</el-button>
    </div>
  </div>
</template>
<script>
import { queryCurrentUserMenuTree, queryShortcutMenuSelect, insShortcutMenuInsert } from '@/api/core/system/sysMenu'
import Cookies from 'js-cookie'
export default {
  data() {
    return {
      height: document.documentElement.clientHeight - 50,
      activeNames: [],
      chooseData: [],
      treeData: [],
      loading: true
    }
  },
  created() {
    this.initTree()
  },
  methods: {
    toFromCancel() {
      this.$emit('cancel')
    },
    toFormFromSubmit(form) {
      if (this.chooseData.length === 0) {
        this.$message({
          message: '请至少选择一个菜单',
          type: 'info'
        })
        return
      }
      const query = {
        userID: Cookies.get('userId'),
        idList: this.chooseData.join(',')
      }
      this.loading = true
      insShortcutMenuInsert(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          console.log(defaultQuery)
          if (defaultQuery.code === 0) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.$emit('refresh')
          } else {
            this.$message({
              message: defaultQuery.msg,
              type: 'info'
            })
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleChoose(item, index) {
      if (item.shortcut_menu_id !== 0) {
        item.shortcut_menu_id = 0
        const itemIndex = this.chooseData.findIndex(d => d == item.menu_item_id)
        if (itemIndex != -1) {
          this.chooseData.splice(itemIndex, 1)
        }
        console.log(this.chooseData)
      } else {
        if (this.chooseData.length === 5) {
          this.$message({
            message: '大家都是胖子，5个就够了',
            type: 'info'
          })
        } else {
          item.shortcut_menu_id = item.menu_item_id
          this.chooseData.push(item.menu_item_id)
        }
      }
    },
    initTree() {
      const query = {
        userID: Cookies.get('userId')
      }
      queryCurrentUserMenuTree(query)
        .then(resSel => {
          const defaultQuerySel = JSON.parse(JSON.stringify(resSel))
          console.log(defaultQuerySel)
          if (defaultQuerySel.data.length > 0) {
            for (let index = 0; index < defaultQuerySel.data.length; index++) {
              const item = defaultQuerySel.data[index]
              for (let index1 = 0; index1 < item.children.length; index1++) {
                const item1 = item.children[index1]
                item1.shortcut_menu_id
                if (item1.shortcut_menu_id === 0) {
                } else {
                  this.chooseData.push(item1.menu_item_id)
                }
              }
              this.activeNames.push(item.menu_group_id.toString())
            }
            this.treeData = defaultQuerySel.data
            this.loading = false
          }
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss">
#shortcutMenuDiv {
  .bg-purple {
    border: 1px solid #dfe4eb;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background: #f5f7f9;
    text-align: center;
  }
  .bg-purple:hover {
    border: 1px solid #bdc7d5;
    cursor: pointer;
    font-weight: bold;
  }
  .bg-purple-add {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background: #ffffff;
    text-align: center;
    border: 2px dashed #cfd7e1;
  }
  .bg-purple-add:hover {
    border: 2px dashed #bdc7d5;
    cursor: pointer;
    font-weight: bold;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #b4c0cf;
  }
  .grid-content {
    border-radius: 4px;
    padding-top: 15px;
    padding-bottom: 15px;
    min-height: 47px;
    max-width: 150px;
    float: left;
    margin-left: 15px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .el-avatar {
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #0b4ab1;
    width: 50px;
    height: 50px;
    margin-left: -30px;
    line-height: 50px;
    font-size: 25px;
  }
  .el-avatar1 {
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #f5f7f9;
    background: #f5f7f9;
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 15px;
    float: left;
    margin-left: 10px;
    margin-top: -10px;
  }
  .el-avatar2 {
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #67c23a;
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 15px;
    float: left;
    margin-left: 10px;
    margin-top: -10px;
  }
  .menu-title {
    width: 150px;
    color: #0f83cb;
    font-size: 12px;
    padding-top: 10px;
  }
}
</style>
<style lang="scss">
#shortcutMenuDiv {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-collapse-item__header {
    font-weight: 600;
    padding-left: 20px;
  }
}
</style>
