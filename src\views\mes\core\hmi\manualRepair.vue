<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          扫描电芯/模组
        </template>
        <el-input ref="repairBarCode" v-model="repairBarCode" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          模组码
        </template>
        <el-input ref="mzBarCode" v-model="mzBarCode" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          选择返修起始工位
        </template>
        <el-radio v-for="(item,index) in opList" :key="index" v-model="stationIndex" :label="item.station_index" style="margin-left:0px;" border>{{ item.station_des }}</el-radio>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleRepairInfo">确定返修</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
  },
  // 数据模型
  data() {
    return {
      opList: [{ station_index: '1', station_des: '端板中隔板安装' },
        { station_index: '2', station_des: '加压整形' },
        { station_index: '3', station_des: '打包或侧板安装' },
        { station_index: '4', station_des: '烘置' },
        { station_index: '5', station_des: '测极柱' },
        { station_index: '6', station_des: '清洗' },
        { station_index: '7', station_des: '安装汇流排' },
        { station_index: '8', station_des: '焊接' },
        { station_index: '9', station_des: '焊后' },
        { station_index: '10', station_des: '人工复检' },
        { station_index: '11', station_des: 'EOL检测' },
        { station_index: '12', station_des: '全尺寸绝缘测试' },
        { station_index: '13', station_des: '贴码下线' }],
      repairBarCode: '',
      repairBarCodeOk: false,
      mzBarCode: '',
      stationIndex: ''
    }
  },
  mounted: function() {
  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.repairBarCode.focus()
    })
  },
  methods: {
    // 接受母页面扫描条码值,显示到界面
    repairScan(repair_barcode, mz_barcode, is_ok) {
      this.repairBarCode = repair_barcode
      this.repairBarCodeOk = is_ok
      this.mzBarCode = mz_barcode
    },
    // 确定返修
    handleRepairInfo() {
      if (!this.repairBarCodeOk) {
        this.$message({ message: '当前扫描电芯码/模组码不符合返修条件', type: 'info' })
        return
      }
      if (this.stationIndex === '') {
        this.$message({ message: '必须选择返修起始工位', type: 'info' })
        return
      }
      this.$emit('repairConfirm', this.mzBarCode, this.stationIndex)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
