<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="名称：">
                <el-input v-model="query.split_rule_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="10" class="leftTable">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              :height="height"
              :highlight-current-row="true"
              @header-dragend="crud.tableHeaderDragend()"
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <!-- 名称 -->
              <el-table-column
                prop="name"
                :label="$t('view.table.name')"
                align="center"
              />
              <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="handleAddDetail(scope.row)">新增规则明细</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <pagination />
          </el-col>
        </el-row>
        <el-row :gutter="20" class="rightTable">
          <el-col :span="24">
            <detail
              ref="detail"
              :list="contentData"
              :loading="crud.loading"
              :permission="permission"
              :parent-crud="crud"
              :parent-form="parentForm"
              class="tableFirst box-card1"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from '@/api/pack/core/sortSplitRule'
import detail from './detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  id: null,
  name: '',
  content: ''
}
export default {
  name: 'PACK_SORT_SPLIT_RULE',
  components: { crudOperation, rrOperation, udOperation, pagination, detail },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.parkSortSplitRule'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      parentForm: {},
      contentData: []
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    getData(row) {
      this.parentForm = row
      const data = []
      if (row.content == null || row.content === '') {
        return
      }
      try {
        const cc = JSON.parse(row.content)
        if (cc == null) {
          return
        }
        for (const i in cc) {
          const item = {
            id: i
          }
          const itemStr = cc[i]
          const c = itemStr.split(';')
          if (c.length === 6) {
            item.name = c[0]
            item.code = c[1]
            item.boardType = c[2]
            item.compareFunc = c[3]
            const s = c[4].split(',')
            const d = c[5].split(',')
            if (s.length === 4) {
              item.srcKey = s[0]
              item.srcSplitIndex = s[1]
              item.srcSplitLength = s[2]
              item.srcSplitReverse = s[3]
            }
            if (d.length === 4) {
              item.dstKey = d[0]
              item.dstSplitIndex = d[1]
              item.dstSplitLength = d[2]
              item.dstSplitReverse = d[3]
            }
          }
          if (Object.keys(item).length > 0) {
            data.push(item)
          }
        }
      } catch (e) {
        console.warn(e)
      }
      return data
    },
    handleRowClick(row, column, event) {
      const data = this.getData(row)
      this.contentData = data
      this.$nextTick(() => {
        this.$refs.detail && this.$refs.detail.crud.toQuery()
      })
    },
    handleAddDetail(row) {
      const data = this.getData(row)
      this.contentData = data
      this.$nextTick(() => {
        this.$refs.detail && this.$refs.detail.crud.toAdd()
      })
    },
    [CRUD.HOOK.afterDelete](v) {
      this.parentForm = {}
      this.contentData = []
      this.$nextTick(() => {
        this.$refs.detail && this.$refs.detail.crud.toQuery()
      })
    },
    [CRUD.HOOK.afterRefresh]() {
      this.handleRowClick(this.parentForm)
    }
  }
}
</script>
  <style scoped lang="less">
  ::v-deep .el-dialog{
    margin-top: 3vh !important;
  }
  .subInput{
      width: 90px !important;
      margin:0 10px;
  }
  .el-form-item-type{
      width:100%;
      span{
          font-size: 12px;
          color: #5f5f5f;
      }
  }
  .wrapRowItem {
    display: flex;
    justify-content: space-between;
    .el-table {
      border-radius: 10px;
      border-color: rgba(0, 0, 0, 0.09);
      box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
    }
    .leftTable {
      width: 30%;
    }
    .rightTable {
      width: 70%;
    }
  }
  ::v-deep .crud-opts-left{
    display: flex;
      .distStyle{
        display: flex;
        align-items: center;
        .left{
          width: 40px;
          height: 20px;
          background-color: #FFC000;
          margin: 0 10px
        }
        .right{
          width: 40px;
          height: 20px;
          background-color: #00B0F0;
          margin: 0 10px
        }
      }
    }
  </style>
