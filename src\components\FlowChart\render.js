import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'

function render(g, node, isSelected) {
  node.width = node.width || 120
  node.height = node.height || 60

  const borderColor = isSelected ? '#445ddb' : 'none'
  const borderWidth = isSelected ? 5 : node.strokeWidth
  // title
  // body
  if (node.type === 'judge') {
    // 左，上，右，下
    // 100,100 175,50 250,100 175,150
    const leftX = node.x
    const leftY = node.y
    const topX = node.x + 75
    const topY = node.y - 50
    const rightX = node.x + 150
    const rightY = node.y
    const bottomX = node.x + 75
    const bottomY = node.y + 50
    g.append('polygon')
      .attr('points', leftX + ',' + leftY + ' ' + topX + ',' + topY + ' ' + rightX + ',' + rightY + ' ' + bottomX + ',' + bottomY)
      .style('stroke-width', borderWidth + 'px')
      .style('fill', node.color)
      .attr('stroke', '#445ddb')
    g.append('text')
      .attr('x', node.x + 75)
      .attr('y', node.y + 5)
      .style('fill', '#2D333D')
      .attr('class', 'unselectable')
      .attr('text-anchor', 'middle')
      .text(() => node.describe)
      .style('font-size', '14px')
      .style('font-weight', 'bold')
  } else {
    if (node.type === 'step') {
      const body = g
        .append('rect')
        .attr('class', 'body')
        .attr('rx', 15)
      body
        .style('width', node.width + 'px')
        .style('height', '83px')
        .style('fill', 'white')
        .style('stroke-width', borderWidth + 'px')
        .attr('x', node.x)
        .attr('y', node.y + 10)
        .style('fill', node.color)
        .attr('stroke', borderColor)

      g.append('image')
        .attr('x', node.x)
        .attr('y', node.y + 10)
        .style('height', '83px')

        .attr('href', require('@/assets/images/step_bg.png'))

      g.append('text')
        .attr('x', node.x + roundTo20(node.width) / 2)
        .attr('y', node.y + 35)
        .attr('class', 'unselectable')
        .attr('text-anchor', 'middle')
        .attr('fill', '#FFFFFF')
        .text(() => node.name)
        .style('font-size', '20px')
        .style('font-weight', 'bold')
        .each(function wrap() {
          const self = d3.select(this)
          let textLength = self.node().getComputedTextLength()
          let text = self.text()
          while (textLength > node.width - 2 * 4 && text.length > 0) {
            text = text.slice(0, -1)
            self.text(text + '...')
            textLength = self.node().getComputedTextLength()
          }
        })
    } else if (node.type === 'label') {
      const text = node.describe
      g.append('text')
        .attr('fill', isSelected ? '#DB8E00' : '#2E4AD5')
        .style('font-size', '14px')
        .attr('x', node.x)
        .attr('y', node.y)
        .attr('class', 'unselectable')
        .attr('text-anchor', 'middle')
        .style('font-weight', 'bold')
        .text(function() {
          return text
        })

      return
    } else {
      const body = g
        .append('rect')
        .attr('class', 'body')
        .attr('rx', 15)
      body
        .style('width', node.width + 'px')
        .style('height', '93px')
        .style('fill', 'white')
        .style('stroke-width', borderWidth + 'px')
        .attr('x', node.x)
        .attr('y', node.y + 10)
        .style('fill', node.color)
        .attr('stroke', borderColor)

      g.append('image')
        .attr('x', node.x)
        .attr('y', node.y + 10)
        .style('height', '93px')

        .attr('href', require('@/assets/images/sub_bg.png'))

      g.append('text')
        .attr('x', node.x + roundTo20(node.width) / 2 - 8)
        .attr('y', node.y + 38)
        .attr('class', 'unselectable')
        .attr('fill', '#FFFFFF')
        .text(() => node.name)
        .style('font-size', '25px')
        .style('font-weight', 'bold')
        .each(function wrap() {
          const self = d3.select(this)
          let textLength = self.node().getComputedTextLength()
          let text = self.text()
          while (textLength > node.width - 2 * 4 && text.length > 0) {
            text = text.slice(0, -1)
            self.text(text + '...')
            textLength = self.node().getComputedTextLength()
          }
        })
    }

    // body text
    const text = node.describe
    let bodyTextY
    if (node.type !== 'sub') {
      bodyTextY = node.y + 40 + roundTo20(node.height) / 2
    } else {
      bodyTextY = node.y + 45 + roundTo20(node.height - 20) / 2
    }
    g.append('text')
      .style('font-size', '14px')
      .attr('x', node.x + node.width / 2)
      .attr('y', bodyTextY)
      .attr('fill', '#333333')
      .attr('class', 'unselectable')
      .attr('text-anchor', 'middle')
      .style('font-weight', 'bold')
      .text(function() {
        return text
      })
      .each(function wrap() {
        const self = d3.select(this)
        let textLength = self.node().getComputedTextLength()
        let text = self.text()
        while (textLength > node.width - 2 * 4 && text.length > 0) {
          text = text.slice(0, -1)
          self.text(text + '...')
          textLength = self.node().getComputedTextLength()
        }
      })
  }
}

export default render
