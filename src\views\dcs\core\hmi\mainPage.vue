<template>
  <div class="mainPage-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="24">
        <el-col :span="24">
          <el-card class="cardFirst">
            <mainIndex @ok="handleCutNode" />
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import mainIndex from './mainIndex.vue' // 主页面
export default {
  name: 'mainPage',
  components: { mainIndex },
  data() {
    return {
      loading: false,
      height: 0,
      activeName: 'main'
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
  },
  methods: {
    handleCutNode(data) {
      this.activeName = 'slicing'
    }
  }
}
</script>
<style lang="less" scoped>
.mainPage-container {
    ::v-deep .borderStyle .el-card.is-always-shadow{
        border: 1px solid #00479d;
    }
    ::v-deep .el-tabs__header {
        margin: 0;
    }

    ::v-deep .el-tabs {
        margin: 0 !important;
    }

    .cardFirst {
        margin-bottom: 10px;
        height: calc(100vh - 40px);
        background-color: #99c1ff;
        ::v-deep .el-card__body{
            width: 100%;
            height: 100%;
            padding: 0 !important;
        }
        .search {
            position: absolute;
            top: 10px;
            right: 20px;

            div {
                text-align: center;
                display: flex;
            }
        }

        .dcs-main {
            margin: 10px 0;
            width: 100%;
            position: relative;
            img{
                background-size: 100% 100%;
                width: 100%;
                height: 100%;
            }
            .point{
                position:absolute;
                width: 20px;
                height: 20px;
                border-radius: 50%;
            }
            .active{
                background-color: #21f121;
                cursor: pointer;
            }
        }
        .dcs-cut{
            position: relative;
            img{
                background-size: 100% 100%;
                width: 100%;
                height: 100%;
            }
            .point{
                position:absolute;
                width: 20px;
                height: 20px;
                border-radius: 50%;
            }
            .active{
                background-color: #21f121;
                cursor: pointer;
            }
        }
        .loadingArea {
            width: 50%;
            display: flex;
            justify-content: space-between;
            .loadingAreaLeft {
                width: 65%;
                .area {
                    display: flex;
                    justify-content: space-between;

                    .left {
                        img {
                            height: 60px;
                            width: 100%;
                        }
                        div{
                            display: flex;
                            justify-content: space-around;
                            .stockNum{
                                width:63px;
                                text-align: center;
                            }
                            .modelType{
                                width: 160px;
                                text-align: center;
                            }
                        }
                    }
                }

                .feed {
                    margin: 5px 0;
                    float: right;
                    height: 45px;
                    width: 60%;
                }

                .blasting {
                    width: 100%;
                    height: 45px;
                }
            }

            .loadingAreaMain {
                width: 35%;
            }

        }

        .loadingAreaRight {
            width: 50%;

            .loadingAreaRightBottom {
                margin: 5px 0 0 10px;
                display: flex;
                justify-content: space-between;
            }
        }
    }

    .wrapTextSelect {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .elliptic {
            display: flex;
            justify-content: space-between;
            align-items: center;

            div {
                background-color: #00479d;
                width: 6px;
                height: 24px;
                border-radius: 10px;
                margin-right: 5px;
            }
        }
    }

    .title {
        width: 100%;
        height: 40px;
        background-color: #E8EFFF;
        display: flex;
        align-items: center;

        div {
            background-color: #00479d;
            width: 6px;
            height: 24px;
            border-radius: 10px;
            margin: 0 5px;
        }
    }

    .Inb-out-bound {
        flex-direction: column;

        .wrappstyle {
            width: 90%;
            margin: 15px auto;

            .name {
                display: flex;
                justify-content:
                    space-between;
                width: 100%;

                div {
                    width: 50%;
                    display: flex;
                    align-items: center;

                    .smallPiece {
                        margin: 0 0 0 5px;
                    }

                    .finish {
                        display: block;
                        width: 70%;
                        text-align: center;
                    }
                }
            }
        }
    }
    .wholeline {
        display: block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #21f121;
    }
    .sorting {
        height: 27vh;

        .wrappstyle {
            width: 90%;
            margin: 15px auto;

            .name {
                display: flex;
                justify-content:
                    space-between;
                width: 100%;

                div {
                    width: 50%;
                    display: flex;
                    align-items: center;

                    .smallPiece {
                        margin: 0 0 0 5px;
                    }

                    .finish {
                        display: block;
                        width: 70%;
                        text-align: center;
                    }
                }
            }

        }

        ::v-deep .el-divider--horizontal {
            margin: 0;
        }
    }

    .device {
        ::v-deep .el-card__body {
            padding: 10px !important;
        }

        ::v-deep .el-tabs__nav-wrap {
            margin-bottom: 10px;
        }
    }

    .slicing {
        margin: 5px 0;

        .daynoborder {
            width: 100%;
            display:flex;
            .left{
                width:15%;
                span{
                    display:block;
                    padding: 7px 0;
                    width:100%;
                    text-align:center;
                    font-weight:500;
                    color: #00479d;
                    margin:1px 0;
                }
            }
            .right{
                width:85%;
                display:flex;
                div{
                    width:100px;
                    span{
                        display:block;
                        padding: 7px 0;
                        width:100%;
                        text-align:center;
                        font-weight:500;
                        margin:1px 0;
                    }
                }

            }
            // .trtitle {
            //     display: flex;
            //     text-align: center;

            //     // justify-content: space-around;
            //     .riyueactive {
            //         width: 16%;
            //         font-weight: 500;
            //         color: #00479d;
            //     }

            //     .tdName {
            //         width: 14%;
            //     }
            // }
        }
    }

    .slicingNum {
        width: 100%;

        .slicingName {
            display: flex;
            justify-content: space-around;
            text-align: center;

            span {
                width: 16.5%;
                display: block;

            }

        }

        .slicingValue {
            display: flex;
            justify-content: space-around;
            text-align: center;

            span {
                width: 16.5%;
                display: block;

            }
        }
    }
}

.left {
    div {
        width: 280px;
        border: 1px solid gray;
        margin-top: 5px;
        display: flex;
        justify-content: center;

        span {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

@media screen and (max-height: 768px) {
    .mainPage-container {
        .dcs-main {
            height: 150px !important;
            .point{
                width: 10px !important;
                height: 10px !important;
            }
        }
    }

    .left {
        div {
            width: 205px;
            .stockNum{
                transform: scale(0.7);
                margin-left: -10px;
            }
            .modelType{
                transform: scale(0.7);
                margin-left: -15px;
                width: 165px !important;
            }
            span{
                text-overflow:clip;
            }
        }
    }
}
@media screen and (max-height: 1080px) {
    .sorting {

    }
    .Inb-out-bound {
        .wrappstyle {
            margin: 25px auto !important;
        }
    }
}
</style>
