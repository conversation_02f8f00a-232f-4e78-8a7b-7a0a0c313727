import request from '@/utils/request'

// 查询标签组
export function selScadaTagGroup(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagGroupSel',
    method: 'post',
    data
  })
}
// 新增标签组
export function insScadaTagGroup(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagGroupIns',
    method: 'post',
    data
  })
}
// 修改标签组
export function updScadaTagGroup(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagGroupUpd',
    method: 'post',
    data
  })
}
// 删除标签组
export function delScadaTagGroup(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagGroupDel',
    method: 'post',
    data
  })
}

// 查询标签组(树)
export function scadaTagGroupTree(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagGroupTree',
    method: 'post',
    data
  })
}
export default { selScadaTagGroup, insScadaTagGroup, updScadaTagGroup, delScadaTagGroup,
  scadaTagGroupTree }
