<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="批次号：">
                <el-input v-model="query.lot_no" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="设备号：">
                <el-input v-model="query.device_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <!-- <template slot="right"> -->
        <!-- <el-button
            slot="reference"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="batchDelete()"
          >
            {{ $t('lang_pack.commonPage.remove') }}
          </el-button> -->

        <!-- <el-button
              slot="reference"
              class="filter-item"
              type="primary"
              icon="el-icon-upload"
              plain
              round
              size="small"
              :loading="crud.delAllLoading"
              :disabled="crud.selections.length === 0"
              @click="batchUplaod()"
            >
              {{ $t('lang_pack.commonPage.upload') }}
            </el-button> -->

        <!-- </template> -->
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item label="配方类型" prop="recipe_type">
            <el-select v-model="form.recipe_type" clearable @change="chooseRecipeType">
              <el-option v-for="item in dict.EAP_RECIPE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="配方描述" prop="recipe_name">
            <el-input v-model="form.recipe_name" />
          </el-form-item>
          <el-form-item label="版本号" prop="recipe_version">
            <el-input v-model="form.recipe_version" />
          </el-form-item>
          <el-form-item label="设备编码" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item label="批次号" prop="lot_no">
            <el-input v-model="form.lot_no" />
          </el-form-item>
          <el-form-item label="设备描述" prop="device_des">
            <el-input v-model="form.device_des" />
          </el-form-item>
          <el-form-item label="物料编码" prop="material_code">
            <el-input v-model="form.material_code" />
          </el-form-item>
          <el-form-item label="物料描述" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item>

          <el-form-item label="有效标识" prop="enable_flag">
            <el-select v-model="form.enable_flag" clearable>
              <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="recipe_id" label="id" />
              <el-table-column :show-overflow-tooltip="true" prop="lot_no" label="批次号" />
              <!-- <el-table-column :show-overflow-tooltip="true" prop="recipe_type" label="配方类型" /> -->
              <!-- <el-table-column :show-overflow-tooltip="true" prop="recipe_name" label="配方描述" /> -->
              <!-- <el-table-column :show-overflow-tooltip="true" prop="recipe_version" label="版本号" /> -->
              <el-table-column :show-overflow-tooltip="true" prop="device_code" label="设备编码" />
              <!-- <el-table-column :show-overflow-tooltip="true" prop="device_des" label="设备描述" /> -->
              <el-table-column :show-overflow-tooltip="true" prop="material_code" label="物料编码" />
              <el-table-column :show-overflow-tooltip="true" width="160" prop="creation_date" label="历史版本生成时间" />
              <el-table-column label="是否有效" align="center" prop="enable_flag">
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <!-- <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                      <template slot="right">

                      </template>
                    </udOperation> -->
                  <operation :data="scope.row" :permission="permission" :disabled-dle="false" @ok="doDelete">
                    <template slot="right">
                      <!-- <el-button slot="reference" type="text" size="small" @click="$refs.detail && $refs.detail.crud.toAdd()">新增</el-button> -->
                      <!-- <el-button slot="reference" type="text" size="small" style="margin-left:0px" @click="upload(scope.row)">上传</el-button> -->
                      <!-- <el-button slot="reference" type="text" size="small" style="margin-left:0px" @click="downLoad(scope.row)">下载</el-button> -->
                      <el-button slot="reference" type="text" size="small" style="margin-left:0px" @click="down(scope.row)">下发</el-button>
                    </template>
                  </operation>

                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <detail ref="detail" class="tableFirst" :recipe_detail_id="currentPackId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import eapRecipe from '@/api/eap/project/sh/history/eapRecipe'
import detail from '@/views/eap/project/sh/history/detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import operation from '@/views/core/system/errorMsg/operation.vue'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
  recipe_id: '',
  recipe_name: '',
  recipe_version: '',
  device_code: '',
  device_des: '',
  material_code: '',
  material_des: '',
  recipe_type: '',
  enable_flag: 'Y',
  recipeFileNames: ''
}
export default {
  name: 'EAP_RECIPE',
  components: { crudOperation, rrOperation, udOperation, pagination, detail, operation },
  props: {},
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id asc'],
      // CRUD Method
      crudMethod: { ...eapRecipe },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: false
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EAP_RECIPE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      recipeInfoScan: '',
      permission: {
        // add: ['admin', 'c_eap_fmod_recipe_maintain:add'],
        // edit: ['admin', 'c_eap_fmod_recipe_maintain:edit'],
        del: ['admin', 'c_eap_fmod_recipe_maintain:del']
      },
      rules: {
        recipe_type: [{ required: true, message: '请选择配方类型', trigger: 'blur' }],
        recipe_version: [{ required: true, message: '请填写版本号', trigger: 'blur' }],
        lot_no: [{ required: true, message: '请填写批次号', trigger: 'blur' }],
        recipe_name: [{ required: true, message: '请填写配方描述', trigger: 'blur' }],
        material_code: [{ required: false, message: '请填写物料编码', trigger: 'blur' }],
        material_des: [{ required: false, message: '请填写物料描述', trigger: 'blur' }],
        device_code: [{ required: false, message: '请填写设备编码', trigger: 'blur' }],
        device_des: [{ required: false, message: '请填写设备描述', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      },
      currentPackId: 0,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
            'ScadaWeb_' +
            Cookies.get('userId') +
            '_' +
            Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      tagKeyList: ['DesAis/AisStatus/ApplyUploadRecipe', 'DesAis/AisStatus/ApplyDownLoadRecipe', 'DesAis/AisStatus/ApplyDistributeRecipe'],
      applyObj: {
        uploadValue: '',
        downloadValue: '',
        downValue: ''
      },
      popoverContent: ''

    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
    this.$refs.recipeInfoScan.focus()
  },
  created: function() {
    this.toStartWatch()
    this.fetchContent()
  },
  methods: {
    // 多个删除
    batchDelete() {
      this.$confirm(`确认删除选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const ids = this.crud.selections.map(item => item[this.crud.idField]).join(',')
          const recipeFileNames = this.crud.selections.map(item => item.recipe_name + '-' + item.recipe_version).join(',')
          const query = {
            ids,
            recipeFileNames,
            user_name: Cookies.get('userName')
          }
          this.delete(query)
        }).catch(() => {})
    },
    // 单个删除
    doDelete(data) {
      const query = {
        ids: data.recipe_id,
        recipeFileNames: data.recipe_name + '-' + data.recipe_version,
        user_name: Cookies.get('userName')
      }
      this.delete(query)
    },
    delete(data) {
      eapRecipe.del(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.crud.toQuery()
        } else {
          this.$message({
            message: res.msg || '删除失败',
            type: 'error'
          })
        }
      }).catch((e) => {
        this.$message({
          message: '删除失败',
          type: 'error'
        })
      })
    },
    chooseRecipeType(val) {
      console.log(val)
      if (val === 'MATERIAL') {
        this.rules.material_code[0].required = true
        this.rules.material_des[0].required = true
        this.rules.device_code[0].required = false
        this.rules.device_des[0].required = false
      } else {
        this.rules.material_code[0].required = false
        this.rules.material_des[0].required = false
        this.rules.device_code[0].required = true
        this.rules.device_des[0].required = true
      }
    },
    handleRowClick(row, column, event) {
      this.currentPackId = row.recipe_id
      console.log(row.recipe_id)
    },
    async handleMouseEnter() {
      await this.fetchContent()
    },
    handleMouseLeave() {
    },
    async fetchContent() {

      // eapRecipe.selectEquipStatusInfo({}).then(res => {
      //   const defaultQuery = JSON.parse(JSON.stringify(res))
      //   if (defaultQuery.code === 0) {
      //     if (defaultQuery.result !== '') {
      //       this.popoverContent = defaultQuery.result.replace(/\\r\\n/g, '<br>')
      //     }
      //   }
      // }).catch(() => {
      //   this.$message({
      //     message: '查询异常',
      //     type: 'error'
      //   })
      // })
    },
    handleDevice() {
      eapRecipe.equipStatusReport({ station_code: this.$route.query.station_code }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message({
            type: 'success',
            message: '上传mes成功'
          })
        }
      }).catch(() => {
        this.$message({
          message: '操作异常',
          type: 'error'
        })
      })
    },
    // 批量上传
    batchUplaod() {
      if (this.applyObj.uploadValue === '1') {
        this.$message({
          type: 'warning',
          message: '已存在上传任务，请等待上传完成'
        })
        return
      }
      const dataKey = [
        { TagKey: 'DesAis/AisStatus/ApplyUploadRecipe', TagValue: '1' },
        { TagKey: 'DesAis/AisStatus/UploadRecipeName', TagValue: this.crud.selections.map(item => item.recipe_name).join(',') }
      ]
      this.scadaPoint(dataKey)
      this.$message({
        type: 'success',
        message: '上传成功'
      })
    },
    // 单个上传
    upload(row) {
      if (this.applyObj.uploadValue === '1') {
        this.$message({
          type: 'warning',
          message: '已存在上传任务，请等待上传完成'
        })
        return
      }
      const dataKey = [
        { TagKey: 'DesAis/AisStatus/ApplyUploadRecipe', TagValue: '1' },
        { TagKey: 'DesAis/AisStatus/UploadRecipeName', TagValue: row.recipe_name }
      ]
      this.scadaPoint(dataKey)
      this.$message({
        type: 'success',
        message: '上传成功'
      })
    },
    // 批量下载
    batchDownload() {
      // if (this.applyObj.downloadValue === '1') {
      //   this.$message({
      //     type: 'warning',
      //     message: '已存在下载任务，请等下载完成'
      //   })
      //   return
      // }
      // console.log(this.recipeInfoScan)
      // const dataKey = [
      //   { TagKey: 'DesAis/AisStatus/ApplyDownLoadRecipe', TagValue: '1' },
      //   { TagKey: 'DesAis/AisStatus/DownLoadRecipeName', TagValue: this.recipeInfoScan }
      // ]
      // this.scadaPoint(dataKey)
      // this.$message({
      //   type: 'success',
      //   message: '下载成功'
      // })
      var method = '/aisEsbWeb/eap/project/sh/recipe/download'
      var path = 'http://127.0.0.1:9090' + method
      var request = { 'lotNo': this.recipeInfoScan }
      axios.post(path, request, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            this.$message.success('发起Scada请求配方,配方下载中')
          } else {
            this.$message.warning('发起Scada请求配方,配方下载出现异常')
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    },
    // 单个下载
    downLoad(row) {
      var method = '/aisEsbWeb/eap/project/sh/recipe/download'
      var path = 'http://127.0.0.1:9090' + method
      var request = { 'lot_no': this.recipeInfoScan }
      axios.post(path, request, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  if (result[i].tag_key === 'DesAis/AisStatus/ApplyUploadRecipe') {
                    this.applyObj.uploadValue = result[i].tag_value
                  }
                  if (result[i].tag_key === 'DesAis/AisStatus/ApplyDownLoadRecipe') {
                    this.applyObj.downloadValue = result[i].tag_value
                  }
                  if (result[i].tag_key === 'DesAis/AisStatus/ApplyDistributeRecipe') {
                    this.applyObj.downValue = result[i].tag_value
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    },
    // 单个下发
    down(row) {
      this.$confirm(`确认下发改配方么?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const dataKey = [
          { TagKey: 'DesAis/AisStatus/ApplyDistributeRecipe', TagValue: '1' },
          { TagKey: 'DesAis/AisStatus/DistributeRecipeName', TagValue: row.recipe_name + '_' + row.recipe_version }
        ]
        this.scadaPoint(dataKey)
        this.$message({
          type: 'success',
          message: '下发成功'
        })
      })
    },
    scadaPoint(dataKey) {
      for (let i = 0; i < dataKey.length; i++) {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: dataKey[i].TagKey,
          TagValue: dataKey[i].TagValue
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/DesAis'
        this.sendMessage(topic, sendStr)
      }
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    toStartWatch() {
      // 获取连接地址
      // 'ws://***************:8090/mqtt'
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.getTagValue(result.ip, result.webapi_port)
          //  var connectUrl ="ws://*************:8083/mqtt";
          // connectUrl=MQTT_SERVICE;
          console.log('拼接URL：' + connectUrl)
          // mqtt连接
          // this.clientMqtt = mqtt.connect(MQTT_SERVICE, this.optionsMqtt); //开启连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', (e) => {
            this.mqttConnStatus = true
            for (let index = 0; index < this.tagKeyList.length; index++) {
              this.topicSubscribe('SCADA_CHANGE/' + this.tagKeyList[index])
            }
          })

          // MQTT连接失败
          this.clientMqtt.on('error', (error) => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })

            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', (error) => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', (error) => {
            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          this.clientMqtt.on('close', () => {
            this.clientMqtt.end()

            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getTagValue(ip, port) {
      var readTagArray = []
      for (var i = 0; i < this.tagKeyList.length; i++) {
        var readTag = {}
        readTag.tag_key = this.tagKeyList[i].toString()
        readTagArray.push(readTag)
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
      } else {
        path = 'http://' + ip + ':' + port + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  if (result[i].tag_key === 'DesAis/AisStatus/ApplyUploadRecipe') {
                    this.applyObj.uploadValue = result[i].tag_value
                  }
                  if (result[i].tag_key === 'DesAis/AisStatus/ApplyDownLoadRecipe') {
                    this.applyObj.downloadValue = result[i].tag_value
                  }
                  if (result[i].tag_key === 'DesAis/AisStatus/ApplyDistributeRecipe') {
                    this.applyObj.downValue = result[i].tag_value
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },

    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagNewValue = jsonData.TagNewValue
      if (TagKey === 'DesAis/AisStatus/ApplyUploadRecipe') {
        this.applyObj.uploadValue = TagNewValue
      }
      if (TagKey === 'DesAis/AisStatus/ApplyDownLoadRecipe') {
        this.applyObj.downLoadValue = TagNewValue
      }
      if (TagKey === 'DesAis/AisStatus/ApplyDistributeRecipe') {
        this.applyObj.downValue = TagNewValue
      }
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.old_recipe_name = crud.form.recipe_name
      crud.form.old_recipe_version = crud.form.recipe_version
      return true
    }
  }
}
</script>
  <style lang="less" scoped>
  .wrapRowItem {
    display: flex;
    justify-content: space-between;
    .el-table {
      border-radius: 10px;
      border-color: rgba(0, 0, 0, 0.09);
      box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
    }
    .el-row {
      width: 50%;
    }
  }
  .recipeInfo {
    font-size: 13px;
    display: flex;
    align-items: center;;
    justify-content: space-between;
  }
  </style>
