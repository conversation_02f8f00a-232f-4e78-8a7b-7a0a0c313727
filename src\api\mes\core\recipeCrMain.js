import request from '@/utils/request'

// 查询工艺路线信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeCrMainSel',
    method: 'post',
    data
  })
}
// 新增工艺路线信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeCrMainIns',
    method: 'post',
    data
  })
}
// 修改工艺路线信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeCrMainUpd',
    method: 'post',
    data
  })
}
// 修改工艺路线信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeCrMainEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除工艺路线信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeCrMainDel',
    method: 'post',
    data
  })
}
// 工艺路线信息复制
export function copyCrMain(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeCrMainCopy',
    method: 'post',
    data
  })
}
// 工艺路线信息-质量数据导出
export function exportQualityData(data) {
  return request({
    url: 'aisEsbWeb//mes/core/MesRecipeCrMainQualityExport',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag, copyCrMain, exportQualityData }

