import request from '@/utils/request'

// 查询WMS辊道任务
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsTransferTaskSelect',
    method: 'post',
    data
  })
}
// 新增WMS辊道任务
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsTransferTaskInsert',
    method: 'post',
    data
  })
}
// 修改WMS辊道任务
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsTransferTaskUpdate',
    method: 'post',
    data
  })
}
// 删除WMS辊道任务
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsTransferTaskDelete',
    method: 'post',
    data
  })
}

// 修改WMS辊道任务--修改有效标识
export function editEnableFlag(data) {
  return request({
      url: 'aisEsbWeb/dcs/core/DcsApsWmsTransferTaskEnableFlag',
      method: 'post',
      data
  })
}
export default { sel,add,edit,del,editEnableFlag}