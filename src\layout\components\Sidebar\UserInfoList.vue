<template>
  <div class="sidebar-user-container">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" :to="$route.fullPath">
        <el-dropdown ref="messageDrop" size="medium" trigger="click" style="float: right; margin-right: 15px">
          <div class="sidebar-userinfo-collapse">
            <img :src="user.avatarPath ? 'data:image/png;base64,' + user.avatarPath : Avatar" style="width:35px;height:35px;border:1px solid #FFFFFF;border-radius: 50%;">
          </div>
          <el-dropdown-menu slot="dropdown">
            <span @click="$router.push({ path: '/user/center' })">
              <el-dropdown-item icon="el-icon-user">个人信息</el-dropdown-item>
            </span>
            <!-- <el-dropdown-item icon="el-icon-edit">修改密码</el-dropdown-item> -->
            <span style="display:block;" @click="open">
              <el-dropdown-item icon="el-icon-back" divided>退出系统</el-dropdown-item>
            </span>
          </el-dropdown-menu>
        </el-dropdown>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" :to="$route.fullPath">
        <el-dropdown ref="messageDrop" size="medium" trigger="click" style="float: right; margin-right: 15px">
          <div class="sidebar-userinfo">
            <div class="wrapImg">
              <img :src="user.avatarPath ? 'data:image/png;base64,' + user.avatarPath : Avatar" title="点击查看个人信息">
              <!-- <span>{{ user.nickName }}</span> -->
              <!-- <i ref="arrowRef" class="el-icon-more arrowStyle" /> -->
            </div>
          </div>
          <el-dropdown-menu slot="dropdown">
            <span @click="show = true">
              <el-dropdown-item icon="el-icon-s-data">布局设置</el-dropdown-item>
            </span>
            <span @click="openPeople">
              <el-dropdown-item icon="el-icon-user">个人信息</el-dropdown-item>
            </span>
            <!-- <el-dropdown-item icon="el-icon-edit">修改密码</el-dropdown-item> -->
            <span style="display:block;" @click="open">
              <el-dropdown-item icon="el-icon-back" divided>退出系统</el-dropdown-item>
            </span>
          </el-dropdown-menu>
        </el-dropdown>
      </router-link>
    </transition>
  </div>
</template>

<script>
import Avatar from '@/assets/images/avatar.png'
import { mapGetters } from 'vuex'
export default {
  name: 'SidebarUserInfo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return { Avatar: Avatar }
  },
  computed: {
    ...mapGetters(['user']),
    show: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    }
  },
  methods: {
    open() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.logout()
      })
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload()
      })
    },
    openPeople() {
      this.$router.push({ path: '/user/center' })
    }
  }
}
</script>

  <style lang="scss" scoped>
  @import '~@/assets/styles/variables.scss';
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  .sidebar-user-container {
    // position: absolute;
    // width: 100%;
    // height: 30px;
    // line-height: 50px;
    overflow: hidden;
    bottom: 0;
    // background: #6e99f3;
    padding: 0;
    // border-top: 1px solid #bcd0fc;
    .el-dropdown {
      display: flex;
      justify-content: center;
      margin-right: 0 !important;
      float: inherit !important;
    }

    .sidebar-userinfo {
      // width: 220px;
      height: 35px;
      line-height: 35px;
      border-radius: 35px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      // background-color: #263bb0;
      .wrapImg{
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        img{
              width: 100%;
              height: 100%;
              border: 1px solid #ffffff;
              border-radius: 50%;
              bottom: 0;
              // margin-right: 20px;
            }
      }
      span{
        color: #ffffff;
        font-size: 13px;
      }
    }
    .sidebar-userinfo-collapse {
      width: 35px;
      height: 35px;
      line-height: 35px;
      border-radius: 50%;
      background-color: #00479d;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        width: 24px !important;
        height: 24px !important;
        display: block;
        line-height: 24px;
        text-align: center;
      }
      i {
        color: rgb(106, 155, 255);
        font-size: 16px;
      }
    }
  }
  .arrowStyle{
    color: #ffffff;
    transform: rotate(90deg);
    padding-bottom: 5px;
  }
  </style>
