<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="机型：">
                <el-select v-model="query.small_model_type" filterable clearable size="small">
                  <el-option v-for="item in smallModelData" :key="item.small_type_id" :label="item.small_model_type + ' ' + item.model_code" :value="item.small_model_type" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button size="small" type="primary" icon="el-icon-upload2" plain round @click="importDialogVisible = true">
            导入
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="220px" :inline="true">
          <el-form-item label="机型" prop="small_model_type">
            <el-select v-model="form.small_model_type" filterable clearable :disabled="disabled">
              <el-option v-for="item in smallModelData" :key="item.small_type_id" :label="item.small_model_type + ' ' + item.model_code" :value="item.small_model_type" />
            </el-select>
          </el-form-item>
          <el-form-item label="OP3040" prop="station_recipe_3040">
            <el-input v-model.number="form.station_recipe_3040" />
          </el-form-item>
          <el-form-item label="OP3050A" prop="station_recipe_3050a">
            <el-input v-model.number="form.station_recipe_3050a" />
          </el-form-item>
          <el-form-item label="OP3050B" prop="station_recipe_3050b">
            <el-input v-model.number="form.station_recipe_3050b" />
          </el-form-item>
          <el-form-item label="OP3080" prop="station_recipe_3080">
            <el-input v-model.number="form.station_recipe_3080" />
          </el-form-item>
          <el-form-item label="OP3090" prop="station_recipe_3090">
            <el-input v-model.number="form.station_recipe_3090" />
          </el-form-item>
          <el-form-item label="OP3110" prop="station_recipe_3110">
            <el-input v-model.number="form.station_recipe_3110" />
          </el-form-item>
          <el-form-item label="OP3130" prop="station_recipe_3130">
            <el-input v-model.number="form.station_recipe_3130" />
          </el-form-item>
          <el-form-item label="OP3145A" prop="station_recipe_3145a">
            <el-input v-model.number="form.station_recipe_3145a" />
          </el-form-item>
          <el-form-item label="OP3160" prop="station_recipe_3160">
            <el-input v-model.number="form.station_recipe_3160" />
          </el-form-item>
          <el-form-item label="OP3190A" prop="station_recipe_3190a">
            <el-input v-model.number="form.station_recipe_3190a" />
          </el-form-item>
          <el-form-item label="OP3270" prop="station_recipe_3270">
            <el-input v-model.number="form.station_recipe_3270" />
          </el-form-item>
          <el-form-item label="OP3290A" prop="station_recipe_3290a">
            <el-input v-model.number="form.station_recipe_3290a" />
          </el-form-item>
          <el-form-item label="OP3290B" prop="station_recipe_3290b">
            <el-input v-model.number="form.station_recipe_3290b" />
          </el-form-item>
          <el-form-item label="OP3310" prop="station_recipe_3310">
            <el-input v-model.number="form.station_recipe_3310" />
          </el-form-item>
          <el-form-item label="OP3330" prop="station_recipe_3330">
            <el-input v-model.number="form.station_recipe_3330" />
          </el-form-item>
          <el-form-item label="OP4040" prop="station_recipe_4040">
            <el-input v-model.number="form.station_recipe_4040" />
          </el-form-item>
          <el-form-item label="OP4120" prop="station_recipe_4120">
            <el-input v-model.number="form.station_recipe_4120" />
          </el-form-item>
          <el-form-item label="OP4140" prop="station_recipe_4140">
            <el-input v-model.number="form.station_recipe_4140" />
          </el-form-item>
          <el-form-item label="OP4160" prop="station_recipe_4160">
            <el-input v-model.number="form.station_recipe_4160" />
          </el-form-item>
          <el-form-item label="OP4180" prop="station_recipe_4180">
            <el-input v-model.number="form.station_recipe_4180" />
          </el-form-item>
          <el-form-item label="OP4400" prop="station_recipe_4400">
            <el-input v-model.number="form.station_recipe_4400" />
          </el-form-item>
          <el-form-item label="OP4430" prop="station_recipe_4430">
            <el-input v-model.number="form.station_recipe_4430" />
          </el-form-item>
          <el-form-item label="预留工位1" prop="station_recipe1">
            <el-input v-model.number="form.station_recipe1" />
          </el-form-item>
          <el-form-item label="预留工位2" prop="station_recipe2">
            <el-input v-model.number="form.station_recipe2" />
          </el-form-item>
          <el-form-item label="预留工位3" prop="station_recipe3">
            <el-input v-model.number="form.station_recipe3" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <!--导入BOM-->
      <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="导入配方" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input
            v-if="isUpLoadError"
            v-model="errorMsg"
            type="textarea"
            :rows="5"
          />
          <div style="text-align: center;margin-top:10px">
            <el-link
              href="../static/plcRecipeTemplate.xlsx"
              target="_blank"
              type="primary"
              icon="el-icon-download"
              download="小配方导入模板.xlsx"
            >下载导入模板</el-link>
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" @click="toButDrawerUpload" style="margin-left:10px">上传</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  :show-overflow-tooltip="true" prop="small_model_type" width="120" label="机型" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3040" width="100" label="OP3040" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3050a" width="100" label="OP3050A" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3050b" width="100" label="OP3050B" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3080" width="100" label="OP3080" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3090" width="100" label="OP3090" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3110" width="100" label="OP3110" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3130" width="100" label="OP3130" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3145a" width="100" label="OP3145A" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3160" width="100" label="OP3160" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3190a" width="100" label="OP3190A" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3270" width="100" label="OP3270" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3290a" width="100" label="OP3290A" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3290b" width="100" label="OP3290B" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3310" width="100" label="OP3310" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_3330" width="100" label="OP3330" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4040" width="100" label="OP4040" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4120" width="100" label="OP4120" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4140" width="100" label="OP4140" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4160" width="100" label="OP4160" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4180" width="100" label="OP4180" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4400" width="100" label="OP4400" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe_4430" width="100" label="OP4430" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe1" width="100" label="预留工位1" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe2" width="100" label="预留工位2" />
            <el-table-column  :show-overflow-tooltip="true" prop="station_recipe3" width="100" label="预留工位3" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudQcSmallRecipe from '@/api/mes/project/qcSmallRecipe'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  recipe_id: '',
  small_type_id: '',
  small_model_type: '',
  station_recipe_3040: '',
  station_recipe_3050a: '',
  station_recipe_3050b: '',
  station_recipe_3080: '',
  station_recipe_3090: '',
  station_recipe_3110: '',
  station_recipe_3130: '',
  station_recipe_3145a: '',
  station_recipe_3160: '',
  station_recipe_3190a: '',
  station_recipe_3270: '',
  station_recipe_3290a: '',
  station_recipe_3290b: '',
  station_recipe_3310: '',
  station_recipe_3330: '',
  station_recipe_4040: '',
  station_recipe_4120: '',
  station_recipe_4140: '',
  station_recipe_4160: '',
  station_recipe_4180: '',
  station_recipe_4400: '',
  station_recipe_4430: '',
  station_recipe1: '',
  station_recipe2: '',
  station_recipe3: ''
}
export default {
  name: 'MES_QC_SMALL_RECIPE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: 'plc小配方',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段:'
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id desc'],
      // CRUD Method
      crudMethod: { ...crudQcSmallRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback()
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_small_model_bom:add'],
        edit: ['admin', 'mes_small_model_bom:edit'],
        del: ['admin', 'mes_small_model_bom:del'],
        down: ['admin', 'mes_small_model_bom:down']
      },
      rules: {
        small_model_type: [{ required: true, message: '请选择机型', trigger: 'blur' }],
        station_recipe_3040: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3050a: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3050b: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3080: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3090: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3110: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3130: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3145a: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3160: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3190a: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3270: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3290a: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3290b: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3310: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_3330: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4040: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4120: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4140: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4160: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4180: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4400: [{ required: false, validator: checkNumber, trigger: 'blur' }],
        station_recipe_4430: [{ required: false, validator: checkNumber, trigger: 'blur' }]
      },
      smallModelData: [],
      uploadLimit: 1,
      uploadAccept: '.xlsx',
      fileList: [],
      importDialogVisible: false,
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: '',
      disabled: false
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query1 = {
      userID: Cookies.get('userName'),
      sort: '',
      page: '',
      size: ''
    }
    selSmallModel(query1)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.smallModelData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    [CRUD.HOOK.afterToAdd]() {
      this.disabled = false
    },
    [CRUD.HOOK.afterToEdit]() {
      this.disabled = true
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = '/mes/project/quanchai/MesQcSmallRecipeImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
