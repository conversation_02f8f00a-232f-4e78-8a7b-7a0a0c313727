<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="任务号:">
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="项目编码：">
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="零件编号：">
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="上料轨道位置：">
                <el-input v-model="query.to_stock_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            slot="reference"
            v-permission="permission.del"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="handleSelectionChange"
          >
            删除
          </el-button>
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="120"
              fixed="left"
            >
              <template slot-scope="scope">
                <el-button :disabled="scope.row.task_status !== 'WORK' " size="small" type="text" @click="taskForceFinish(scope.row)">任务强制完成</el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_num" label="任务号" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_num" label="项目编码" />
            <el-table-column :show-overflow-tooltip="true" prop="model_type" label="零件编号" />
            <el-table-column :show-overflow-tooltip="true" prop="to_stock_code" label="上料轨道位置" />
            <el-table-column :show-overflow-tooltip="true" prop="serial_num" label="钢板自编号" />
            <el-table-column :show-overflow-tooltip="true" prop="task_status" label="任务状态">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_from" label="任务来源">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_way" label="任务模式" />
            <el-table-column :show-overflow-tooltip="true" prop="lock_flag" label="是否锁定">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.lock_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="attribute1" width="160" label="版本号" />
            <el-table-column :show-overflow-tooltip="true" prop="task_type" width="160" label="任务类型">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="100"
              fixed="right"
            >
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="!(scope.row.task_status === 'FINISH' || scope.row.task_status === 'PLAN')" :delete-edit="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import Paho from 'paho-mqtt'
import { selCellIP } from '@/api/core/center/cell'
import crudWmsCarTask from '@/api/dcs/project/wms/wmsCarTask'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_CAR_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '中控出库任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'task_id',
      // 排序
      sort: ['task_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsCarTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_car_task:add'],
        edit: ['admin', 'b_dcs_wms_car_task:edit'],
        del: ['admin', 'b_dcs_wms_car_task:del']
      },
      isFinishFlag: false,
      clients: {},
      mqttClient: {}
    }
  },
  dicts: ['ENABLE_FLAG', 'TASK_WAY', 'TASK_FROM', 'PROD_TASK_STATUS', 'TASK_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.getCellIp()
  },
  methods: {
    handleSelectionChange() {
      const ids = this.crud.selections.filter(item => item.task_status === 'FINISH' || item.task_status === 'PLAN').map(temp => temp.task_id).join(',')
      this.$confirm(`确认要删除选中的${this.crud.selections.length}条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!ids) {
          this.$message({ type: 'error', message: '请至少勾选一条任务状态在【已完成】或【计划中】的数据' })
          return
        }
        crudWmsCarTask.del({ ids }).then(res => {
          if (res.code === 0) {
            this.crud.toQuery()
            this.$message({ type: 'success', message: '删除成功' })
          }
        })
      })
    },
    taskForceFinish(data) {
      const h = this.$createElement
      this.$msgbox({
        title: '通知中控上料完成',
        message: h('p', null, [
          h('span', null, '确认要将当前任务强制完成吗? ')
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            if (!data.task_id) {
              this.$message({ type: 'error', message: '当前没有任务ID,请前往数据库查看' })
              return
            }
            this.handleOk(data, done, instance)
            // crudWmsCarTask.flowTask({ line_section_code: 'FEED' }).then(res => {
            //   console.log(res)
            // })
          } else {
            done()
          }
        }
      }).then(action => {
      })
    },
    handleOk(data, done, instance) {
      const cellApi = () => {
        crudWmsCarTask.taskFinish({ task_id: data.task_id, task_num: data.task_num }).then(res => {
          if (res && res.code === 0) {
            this.crud.toQuery()
            done()
            this.isFinishFlag = true
            // 写入点位
            var topic = 'SCADA_WRITE/SlAisSimPlc'
            const sendStr = JSON.stringify({ 'Data': [{ 'TagKey': 'SlAisSimPlc/WmsStatus/CarCreate01', 'TagValue': '1' }], 'ClientName': 'SCADA_WEB' })
            this.mqttClient.send(topic, sendStr)
            this.$message({ type: 'success', message: '任务已强制完成' })
            const address = this.cellApiInfo.ip + ':' + this.cellApiInfo.webapi_port
            // 更新流程图参数
            crudWmsCarTask.flowUpdate(address, {}).then((response) => {
              if (response.code !== 0) {
                this.$message({ type: 'error', message: '流程图数据更新异常：' + res.msg })
              }
            })
            instance.confirmButtonLoading = false
            return
          }
          // 如果他们 接口编码中【SendZkFeedFinish】调不通，则一直调用
          setTimeout(() => {
            cellApi()
          }, 3000)
          this.isFinishFlag = false
          this.$message({ type: 'error', message: '任务强制完成【失败】：' + res.msg })
        }).catch(err => {
          setTimeout(() => {
            cellApi()
          }, 3000)
          this.isFinishFlag = false
          this.$message({ type: 'error', message: '任务强制完成【失败】：' + err.msg })
        })
      }
      cellApi()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            // this.cellApiInfo = info
            this.cellApiInfo = {
              ip: ipInfo.ip,
              mqtt_port: ipInfo.mqtt_port,
              webapi_port: ipInfo.webapi_port
            }
            this.getScadaPointdata(ipInfo)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    getScadaPointdata(ipInfo) {
      const port = parseInt(ipInfo.mqtt_port)
      this.connectMQTT(ipInfo.ip, port, (c) => {
        this.clients['SCADA_CHANGE'] = c
        // 发布订阅
        c.subscribe(`SlAisSimPlc/WmsStatus/CarCreate01`, {
          onSuccess: () => {
            console.log('订阅成功：', 'SlAisSimPlc/WmsStatus/CarCreate01')
          },
          onFailure: (responseObject) => {
            console.log('订阅失败：', 'SlAisSimPlc/WmsStatus/CarCreate01', responseObject.errorMessage)
          }
        })
      })
    },
    connectMQTT(host, port, onConnected) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      this.mqttClient = mqttClient
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          this.setContent(data.TagKey, data.TagNewValue)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }
}
</script>

      <style>
      .table-descriptions-label {
        width: 150px;
      }
      .table-descriptions-content {
        width: 150px;
      }
      </style>
