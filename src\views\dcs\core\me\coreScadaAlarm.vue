<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="工位编号:">
                                <!-- 工位编号 -->
                                <el-input v-model="query.station_code" clearable size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item label="工位编号:" prop="station_code">
                                <!-- 工位编号  -->
                                <el-input v-model="form.station_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="报警描述:" prop="alarm_des">
                                <!-- 报警描述   -->
                                <el-input v-model="form.alarm_des" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="报警编码" prop="alarm_code">
                                <!-- 报警编码 -->
                                <el-input v-model="form.alarm_code" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border  ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <!-- 工位编号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="station_code"
                            label="工位编号" min-width="100" align='center'/>
                        <!-- 报警描述  -->
                        <el-table-column  :show-overflow-tooltip="true" prop="alarm_des"
                            label="报警描述" min-width="100" align='center'/>
                        <!-- 报警时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="item_date"
                            label="报警时间" min-width="100" align='center'/>
                        <!-- 报警编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="alarm_code"
                            label="报警编码" min-width="100" align='center'/>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudCoreScadaAlarm from '@/api/dcs/core/me/coreScadaAlarm'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    alarm_code:'',
    alarm_des:'',
    client_code:'',
    client_des:'',
    id:'',
    item_date:'',
    item_date_val:'',
    reset_date:'',
    reset_flag:'',
    simulated_flag:'',
    station_code:'',
    tag_id:'',
}
export default {
    name: 'CORE_SCADA_ALARM',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '设备异常信息',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'page_id',
            // 排序
            sort: ['page_id asc'],
            // CRUD Method
            crudMethod: { ...crudCoreScadaAlarm },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'CORE_SCADA_ALARM:add'],
                edit: ['admin', 'CORE_SCADA_ALARM:edit'],
                del: ['admin', 'CORE_SCADA_ALARM:del']
            },
            rules: {
                quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
            },
        }
    },
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
    }
}
</script>
  