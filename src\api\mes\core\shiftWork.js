import request from '@/utils/request'

// 班次工作时间信息查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftWorkSel',
    method: 'post',
    data
  })
}
// 班次工作时间信息新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftWorkIns',
    method: 'post',
    data
  })
}
// 班次工作时间信息修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftWorkUpd',
    method: 'post',
    data
  })
}
// 班次工作时间信息删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftWorkDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
