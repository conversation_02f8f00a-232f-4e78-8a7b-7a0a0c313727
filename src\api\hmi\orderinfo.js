import request from '@/utils/request'

// 选择订单
export function chooseOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/core/hmi/CoreHmiMoSel',
    method: 'post',
    data
  })
}
// 获取订单详情
export function getOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/core/hmi/CoreHmiMoIns',
    method: 'post',
    data
  })
}
// 展示订单详情
export function showOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/core/hmi/CoreHmiMoMzRecipeSel',
    method: 'post',
    data
  })
}

// 展示Pack订单详情
export function showPackOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/core/hmi/CoreHmiMoPackRecipeSel',
    method: 'post',
    data
  })
}

// 电芯上线校验
export function gxDxOnlineCheck(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxDxOnlineCheckOfWeb',
    method: 'post',
    data
  })
}
// 展示电芯详情
export function showtelecomInfo(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxDxOnlineSelect',
    method: 'post',
    data
  })
}
// 扫描确定返修模组信息
export function mesGxMzManualRepairSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzManualRepairBarCodeSelect',
    method: 'post',
    data
  })
}

export default { chooseOrderInfo, getOrderInfo, showOrderInfo,
  gxDxOnlineCheck, showtelecomInfo, mesGxMzManualRepairSelect,
  showPackOrderInfo }
