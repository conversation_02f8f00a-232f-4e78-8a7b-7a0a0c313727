import request from '@/utils/request'
// 查询物料
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/SfMaterialSel',
    method: 'post',
    data
  })
}

// 编辑物料
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/SfMaterialUpdate',
    method: 'post',
    data
  })
}

// 删除物料
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/SfMaterialDelete',
    method: 'post',
    data
  })
}
// 新增物料
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/SfMaterialInsert',
    method: 'post',
    data
  })
}
export default { sel, add, del, edit }
