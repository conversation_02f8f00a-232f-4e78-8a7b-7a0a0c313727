<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceMenu.menuEncodingdescription')">
                <!-- 菜单编码/描述： -->
                <el-input v-model="query.menuCodeDesGroup" clearable size="mini" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.maintenanceMenu.menuGroupCoding')" prop="menu_group_code" display:none>
            <!-- 菜单组编码 -->
            <el-input v-model="form.menu_group_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceMenu.menuGroupDescription')" prop="menu_group_des">
            <!-- 菜单组描述 -->
            <el-input v-model="form.menu_group_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceMenu.order')" prop="group_order_by">
            <!-- 顺序 -->
            <el-input v-model="form.group_order_by" />
          </el-form-item>
          <!--快速编码：TERMINAL_TYPE-->
          <el-form-item :label="$t('lang_pack.maintenanceMenu.menuType')" prop="menu_type_code">
            <!-- 菜单类型 -->
            <fastCode fastcode_group_code="TERMINAL_TYPE" :fastcode_code.sync="form.menu_type_code" control_type="select" size="small" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceMenu.icon')" prop="menu_group_ico" class="iconItem">
            <!-- 图标 -->
            <el-popover v-model="customPopover" placement="top">
              <sysIcon @chooseIcon="chooseIcon" />
              <el-input slot="reference" v-model="form.menu_group_ico" />
            </el-popover>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
            size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
            :highlight-current-row="true" @selection-change="crud.selectionChangeHandler" @row-click="handleRowClick">
              <el-table-column  type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  v-if="1 == 0" width="10" prop="menu_group_id" label="id" />
              <el-table-column  :show-overflow-tooltip="true" prop="menu_group_code" width="100" :label="$t('lang_pack.maintenanceMenu.menuGroupCoding')" />
              <!-- 菜单组编码 -->
              <el-table-column  :show-overflow-tooltip="true" prop="menu_group_des"  width="100" :label="$t('lang_pack.maintenanceMenu.menuGroupDescription')" />
              <!-- 菜单组描述 -->
              <el-table-column  :show-overflow-tooltip="true" prop="group_order_by"  width="50" :label="$t('lang_pack.maintenanceMenu.order')" />
              <!-- 顺序 -->
              <el-table-column  :show-overflow-tooltip="true" prop="menu_type_code"  width="100" :label="$t('lang_pack.maintenanceMenu.menuType')" />
              <!-- 菜单类型 -->
              <el-table-column  :show-overflow-tooltip="true" prop="menu_group_ico"  width="100" :label="$t('lang_pack.maintenanceMenu.icon')" />
              <!-- 图标 -->
              <el-table-column  align="center" prop="enable_flag" width="100" :label="$t('lang_pack.commonPage.validIdentificationt')">
                <!-- 有效标识 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
                <!-- 操作 -->
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="$refs.menuItem && $refs.menuItem.crud.toAdd()">{{ $t('lang_pack.maintenanceMenu.addSubmenu') }}</el-button>
                      <!-- 新增子菜单 -->
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>

        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <menuItem ref="menuItem" class="tableFirst box-card1" :menu_group_id="currentMenuGroupId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import sysIcon from '@/components/SvgIcon/chooseIcon' // 图标
import crudMenu from '@/api/core/system/sysMenu'
import menuItem from './menuItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  menu_group_id: '',
  menu_group_code: '',
  menu_group_des: '',
  group_order_by: '',
  menu_type_code: '',
  menu_group_ico: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_MENU',
  components: { crudOperation, rrOperation, udOperation, pagination, sysIcon, menuItem },
  props: {},
  cruds() {
    return CRUD({
      title: '菜单组',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'menu_group_id',
      // 排序
      sort: ['menu_group_id asc'],
      // CRUD Method
      crudMethod: { ...crudMenu },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      },
      // 设置默认分页大小为20条每页
      props: {
        pageSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_menu:add'],
        edit: ['admin', 'sys_menu:edit'],
        del: ['admin', 'sys_menu:del'],
        down: ['admin', 'sys_menu:down']
      },
      rules: {
        menu_group_code: [{ required: true, message: '请输入菜单组编码', trigger: 'blur' }],
        group_order_by: [{ required: true, message: '请输入顺序', trigger: 'blur' }]
      },
      customPopover: false,
      currentMenuGroupId: 0
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  methods: {
    chooseIcon(iconId) {
      this.form.menu_group_ico = iconId
      this.customPopover = false
    },
    handleRowClick(row, column, event) {
      this.currentMenuGroupId = row.menu_group_id
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
