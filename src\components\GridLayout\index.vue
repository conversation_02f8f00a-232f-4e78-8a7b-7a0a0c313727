<template>
  <grid-layout
    :layout.sync="layout"
    :col-num="gridLayoutObject.colNum || 12"
    :row-height="gridLayoutObject.rowHeight || 20"
    :is-draggable="gridLayoutObject.isDraggable || true"
    :is-resizable="gridLayoutObject.isResizable || true"
    :is-mirrored="gridLayoutObject.isMirrored || false"
    :vertical-compact="gridLayoutObject.verticalCompact || false"
    :margin="gridLayoutObject.margin || [10, 10]"
    :max-rows="gridLayoutObject.maxRows || 12"
    :use-css-transforms="gridLayoutObject.useCssTransforms || false"
    :style="{ height: gridLayoutObject.height,overflow: 'hidden' }"
    @layout-updated="layoutUpdatedEvent"
  >
    <grid-item
      v-for="item in layout"
      :key="item.i"
      :x="item.x"
      :y="item.y"
      :w="item.w"
      :h="item.h"
      :i="item.i"
      :style="item.style"
      :is-resizable="false"
      @move="moveEvent"
    >
      {{ item.i }}
    </grid-item>
  </grid-layout>
</template>
<script>

import { GridLayout, GridItem } from 'vue-grid-layout'
export default {
  components: {
    GridLayout,
    GridItem
  },
  props: {
    gridLayoutObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      currentDragId: null, // 拖动的元素id
      layout: [
        { 'x': 17, 'y': 0, 'w': 1, 'h': 1, 'i': '0', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 18, 'y': 0, 'w': 1, 'h': 1, 'i': '1', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 19, 'y': 0, 'w': 1, 'h': 1, 'i': '2', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 20, 'y': 0, 'w': 1, 'h': 1, 'i': '3', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 24, 'y': 0, 'w': 2, 'h': 1, 'i': '11', style: { backgroundColor: '#FEE69B', border: '1px solid #ccc' }},
        { 'x': 29.5, 'y': 0, 'w': 1.5, 'h': 1, 'i': '14', style: { backgroundColor: '#FCD964', border: '1px solid #ccc' }},
        { 'x': 31, 'y': 0, 'w': 1, 'h': 2, 'i': '15', style: { backgroundColor: '#D8D9D9', border: '1px solid #ccc' }},
        { 'x': 32, 'y': 0, 'w': 1, 'h': 1, 'i': '16', style: { backgroundColor: '#FCD964', border: '1px solid #ccc' }},

        { 'x': 17, 'y': 1, 'w': 1, 'h': 1, 'i': '4', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 18, 'y': 1, 'w': 1, 'h': 1, 'i': '5', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 19, 'y': 1, 'w': 1, 'h': 1, 'i': '6', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 20, 'y': 1, 'w': 1, 'h': 1, 'i': '7', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 21, 'y': 1, 'w': 1, 'h': 1, 'i': '8', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 22, 'y': 1, 'w': 1, 'h': 1, 'i': '9', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 23, 'y': 1, 'w': 1, 'h': 1, 'i': '10', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 26, 'y': 1, 'w': 1, 'h': 1, 'i': '12', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 27, 'y': 1, 'w': 1, 'h': 1, 'i': '13', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},

        { 'x': 0, 'y': 2, 'w': 1, 'h': 1, 'i': '17', style: { backgroundColor: '#A1A6A7', border: '1px solid #ccc' }},
        { 'x': 1, 'y': 2, 'w': 1, 'h': 1, 'i': '18', style: { backgroundColor: '#A1A6A7', border: '1px solid #ccc' }},
        { 'x': 2, 'y': 2, 'w': 1, 'h': 4, 'i': '19', style: { backgroundColor: '#D9D9D9', border: '1px solid #ccc' }},
        { 'x': 3, 'y': 2, 'w': 1, 'h': 1, 'i': '20', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 4, 'y': 2, 'w': 1, 'h': 1, 'i': '21', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 5, 'y': 2, 'w': 1, 'h': 1, 'i': '22', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 6, 'y': 2, 'w': 1, 'h': 1, 'i': '23', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 7, 'y': 2, 'w': 1, 'h': 1, 'i': '24', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 8, 'y': 2, 'w': 1, 'h': 1, 'i': '25', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 9, 'y': 2, 'w': 1, 'h': 1, 'i': '26', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 10, 'y': 2, 'w': 1, 'h': 1, 'i': '27', style: { backgroundColor: '#9CC3E9', border: '1px solid #ccc' }},
        { 'x': 11, 'y': 2, 'w': 1, 'h': 1, 'i': '28', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 12, 'y': 2, 'w': 1, 'h': 1, 'i': '29', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 13, 'y': 2, 'w': 1, 'h': 1, 'i': '30', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 14, 'y': 2, 'w': 1, 'h': 1, 'i': '31', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 15, 'y': 2, 'w': 1, 'h': 1, 'i': '32', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 16, 'y': 2, 'w': 1, 'h': 1, 'i': '33', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 17, 'y': 2, 'w': 1, 'h': 1, 'i': '35', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 18, 'y': 2, 'w': 1, 'h': 1, 'i': '36', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 19, 'y': 2, 'w': 1, 'h': 1, 'i': '37', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 20, 'y': 2, 'w': 1, 'h': 1, 'i': '38', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 21, 'y': 2, 'w': 1, 'h': 1, 'i': '39', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 22, 'y': 2, 'w': 1, 'h': 1, 'i': '40', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 23, 'y': 2, 'w': 1, 'h': 1, 'i': '41', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 24, 'y': 2, 'w': 1, 'h': 1, 'i': '42', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 25, 'y': 2, 'w': 1, 'h': 1, 'i': '43', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 26, 'y': 2, 'w': 1, 'h': 1, 'i': '44', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 27, 'y': 2, 'w': 1, 'h': 1, 'i': '45', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 28, 'y': 2, 'w': 1, 'h': 1, 'i': '46', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 29, 'y': 2, 'w': 1, 'h': 1, 'i': '47', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 31, 'y': 2, 'w': 1, 'h': 2, 'i': '50', style: { backgroundColor: '#D8D9D9', border: '1px solid #ccc' }},
        { 'x': 0, 'y': 3, 'w': 1, 'h': 1, 'i': '51', style: { backgroundColor: '#A1A6A7', border: '1px solid #ccc' }},
        { 'x': 1, 'y': 3, 'w': 1, 'h': 1, 'i': '52', style: { backgroundColor: '#A1A6A7', border: '1px solid #ccc' }},
        { 'x': 3, 'y': 3, 'w': 1, 'h': 1, 'i': '53', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 4, 'y': 3, 'w': 1, 'h': 1, 'i': '54', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 5, 'y': 3, 'w': 1, 'h': 1, 'i': '55', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 6, 'y': 3, 'w': 1, 'h': 1, 'i': '56', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 7, 'y': 3, 'w': 1, 'h': 1, 'i': '57', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 8, 'y': 3, 'w': 1, 'h': 1, 'i': '58', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 9, 'y': 3, 'w': 1, 'h': 1, 'i': '59', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 10, 'y': 3, 'w': 1, 'h': 1, 'i': '60', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 11, 'y': 3, 'w': 1, 'h': 1, 'i': '61', style: { backgroundColor: '#9CC3E9', border: '1px solid #ccc' }},
        { 'x': 12, 'y': 3, 'w': 1, 'h': 1, 'i': '62', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 13, 'y': 3, 'w': 1, 'h': 1, 'i': '63', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 14, 'y': 3, 'w': 1, 'h': 1, 'i': '64', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 15, 'y': 3, 'w': 1, 'h': 1, 'i': '65', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 16, 'y': 3, 'w': 1, 'h': 1, 'i': '66', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 17, 'y': 3, 'w': 1, 'h': 1, 'i': '68', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 18, 'y': 3, 'w': 1, 'h': 1, 'i': '69', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 19, 'y': 3, 'w': 1, 'h': 1, 'i': '70', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 20, 'y': 3, 'w': 1, 'h': 1, 'i': '71', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 21, 'y': 3, 'w': 1, 'h': 1, 'i': '72', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 22, 'y': 3, 'w': 1, 'h': 1, 'i': '73', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 23, 'y': 3, 'w': 1, 'h': 1, 'i': '74', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 24, 'y': 3, 'w': 1, 'h': 1, 'i': '75', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 25, 'y': 3, 'w': 1, 'h': 1, 'i': '76', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 26, 'y': 3, 'w': 1, 'h': 1, 'i': '77', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 27, 'y': 3, 'w': 1, 'h': 1, 'i': '78', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 28, 'y': 3, 'w': 1, 'h': 1, 'i': '79', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 29, 'y': 3, 'w': 1, 'h': 1, 'i': '80', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 30, 'y': 3, 'w': 1, 'h': 3, 'i': '82', style: { backgroundColor: '#D8D9D9', border: '1px solid #ccc' }},
        { 'x': 0, 'y': 4, 'w': 1, 'h': 1, 'i': '83', style: { backgroundColor: '#A1A6A7', border: '1px solid #ccc' }},
        { 'x': 1, 'y': 4, 'w': 1, 'h': 1, 'i': '84', style: { backgroundColor: '#A1A6A7', border: '1px solid #ccc' }},
        { 'x': 3, 'y': 4, 'w': 1, 'h': 1, 'i': '85', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 4, 'y': 4, 'w': 1, 'h': 1, 'i': '86', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 5, 'y': 4, 'w': 1, 'h': 1, 'i': '87', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 6, 'y': 4, 'w': 1, 'h': 1, 'i': '88', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 7, 'y': 4, 'w': 1, 'h': 1, 'i': '89', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 8, 'y': 4, 'w': 1, 'h': 1, 'i': '90', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 9, 'y': 4, 'w': 1, 'h': 1, 'i': '91', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 10, 'y': 4, 'w': 1, 'h': 1, 'i': '92', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 11, 'y': 4, 'w': 1, 'h': 1, 'i': '93', style: { backgroundColor: '#9CC3E9', border: '1px solid #ccc' }},
        { 'x': 12, 'y': 4, 'w': 1, 'h': 1, 'i': '94', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 13, 'y': 4, 'w': 1, 'h': 1, 'i': '95', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 14, 'y': 4, 'w': 1, 'h': 1, 'i': '96', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 15, 'y': 4, 'w': 1, 'h': 1, 'i': '97', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 16, 'y': 4, 'w': 1, 'h': 1, 'i': '98', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 17, 'y': 4, 'w': 1, 'h': 1, 'i': '100', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 18, 'y': 4, 'w': 1, 'h': 1, 'i': '101', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 19, 'y': 4, 'w': 1, 'h': 1, 'i': '102', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 20, 'y': 4, 'w': 1, 'h': 1, 'i': '103', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 21, 'y': 4, 'w': 1, 'h': 1, 'i': '104', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 22, 'y': 4, 'w': 1, 'h': 1, 'i': '105', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 23, 'y': 4, 'w': 1, 'h': 1, 'i': '106', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 24, 'y': 4, 'w': 1, 'h': 1, 'i': '107', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 25, 'y': 4, 'w': 1, 'h': 1, 'i': '108', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 26, 'y': 4, 'w': 1, 'h': 1, 'i': '109', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 27, 'y': 4, 'w': 1, 'h': 1, 'i': '110', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 28, 'y': 4, 'w': 1, 'h': 1, 'i': '111', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 29, 'y': 4, 'w': 1, 'h': 1, 'i': '113', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 31, 'y': 4, 'w': 1, 'h': 2, 'i': '115', style: { backgroundColor: '#D8D9D9', border: '1px solid #ccc' }},
        { 'x': 0, 'y': 5, 'w': 1, 'h': 1, 'i': '116', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 1, 'y': 5, 'w': 1, 'h': 1, 'i': '117', style: { backgroundColor: '#C29103', border: '1px solid #ccc' }},
        { 'x': 3, 'y': 5, 'w': 1, 'h': 1, 'i': '119', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 4, 'y': 5, 'w': 1, 'h': 1, 'i': '120', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 5, 'y': 5, 'w': 1, 'h': 1, 'i': '121', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 6, 'y': 5, 'w': 1, 'h': 1, 'i': '122', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 7, 'y': 5, 'w': 1, 'h': 1, 'i': '123', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 8, 'y': 5, 'w': 1, 'h': 1, 'i': '124', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 9, 'y': 5, 'w': 1, 'h': 1, 'i': '125', style: { backgroundColor: '#4571C7', border: '1px solid #ccc' }},
        { 'x': 10, 'y': 5, 'w': 1, 'h': 1, 'i': '126', style: { backgroundColor: '#9CC3E9', border: '1px solid #ccc' }},
        { 'x': 11, 'y': 5, 'w': 1, 'h': 1, 'i': '127', style: { backgroundColor: '#9CC3E9', border: '1px solid #ccc' }},
        { 'x': 12, 'y': 5, 'w': 1, 'h': 1, 'i': '128', style: { backgroundColor: '#9CC3E9', border: '1px solid #ccc' }},
        { 'x': 13, 'y': 5, 'w': 1, 'h': 1, 'i': '129', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 14, 'y': 5, 'w': 1, 'h': 1, 'i': '130', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 15, 'y': 5, 'w': 1, 'h': 1, 'i': '131', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 16, 'y': 5, 'w': 1, 'h': 1, 'i': '132', style: { backgroundColor: '#BED6F5', border: '1px solid #ccc' }},
        { 'x': 17, 'y': 5, 'w': 1, 'h': 1, 'i': '134', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 18, 'y': 5, 'w': 1, 'h': 1, 'i': '135', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 19, 'y': 5, 'w': 1, 'h': 1, 'i': '136', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 20, 'y': 5, 'w': 1, 'h': 1, 'i': '137', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 21, 'y': 5, 'w': 1, 'h': 1, 'i': '138', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 22, 'y': 5, 'w': 1, 'h': 1, 'i': '139', style: { backgroundColor: '#C15B11', border: '1px solid #ccc' }},
        { 'x': 23, 'y': 5, 'w': 1, 'h': 1, 'i': '140', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 24, 'y': 5, 'w': 1, 'h': 1, 'i': '141', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 25, 'y': 5, 'w': 1, 'h': 1, 'i': '142', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 26, 'y': 5, 'w': 1, 'h': 1, 'i': '143', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 27, 'y': 5, 'w': 1, 'h': 1, 'i': '144', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 28, 'y': 5, 'w': 1, 'h': 1, 'i': '145', style: { backgroundColor: '#538234', border: '1px solid #ccc' }},
        { 'x': 29, 'y': 5, 'w': 1, 'h': 1, 'i': '147', style: { backgroundColor: '#538234', border: '1px solid #ccc' }}
      ]
    }
  },
  methods: {
    moveEvent(i) {
      this.currentDragId = i
    },
    layoutUpdatedEvent(newLayout) {
      if (!this.currentDragId) return
      const movedItem = newLayout.find(item => item.i === this.currentDragId)
      if (!movedItem) return
      console.log(movedItem)
      // 查找目标位置的元素
      const targetElement = newLayout.find(
        item => item.x === movedItem.x && item.y === movedItem.y && item.i !== movedItem.i
      )
      if (targetElement) {
      // 判断是否交换合理（范围内）
        if (
          this.checkInRange(movedItem.x, movedItem.y, movedItem.w, movedItem.h) &&
        this.checkInRange(targetElement.x, targetElement.y, targetElement.w, targetElement.h)
        ) {
        // 交换位置
          const tempX = movedItem.x
          const tempY = movedItem.y

          movedItem.x = targetElement.x
          movedItem.y = targetElement.y
          targetElement.x = tempX
          targetElement.y = tempY

          this.layout = [...newLayout] // 更新布局
        }
      }
      // 还原拖动ID
      this.currentDragId = null
    },
    checkInRange(x, y, w, h) {
      const maxCols = this.gridLayoutObject.colNum // 你的布局列数
      const maxRows = this.gridLayoutObject.maxRows // 你定义的最大行数
      if (x < 0 || y < 0 || x + w > maxCols || y + h > maxRows) return false
      return true
    }
  }
}

</script>
