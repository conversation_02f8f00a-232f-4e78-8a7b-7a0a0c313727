<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务类型:">
                <!-- 任务类型 -->
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option v-for="item in dict.TASK_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号:">
                <!-- 任务号 -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="序列号:">
                <!-- 序列号 -->
                <el-input v-model="query.serial_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号:">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="型号:">
                <el-select v-model="query.model_type" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_type"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务状态:">
                <!-- 任务状态 -->
                <el-select v-model="query.task_status" clearable filterable>
                  <el-option v-for="item in dict.PROD_TASK_STATUS" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="是否锁定:">
                <!-- 是否锁定 -->
                <el-select v-model="query.lock_flag" clearable>
                  <el-option v-for="item in [{id:1,lable:'是',value:'Y'},{id:2,lable:'否',value:'N'}]" :key="item.id" :label="item.lable" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-plus" plain round @click="blastAdd">
            新增抛丸任务
          </el-button>
          <el-button slot="reference" class="filter-item" type="danger" icon="el-icon-delete" plain round size="small" :loading="crud.delAllLoading" :disabled="crud.selections.length === 0" @click="toDelete(crud.selections)">
            {{ $t('lang_pack.commonPage.remove') }}
          </el-button>  <!-- 删除 -->
        </template>
      </crudOperation>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.sortingResults.stationCode')" prop="station_code">
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partBarcodeNumber')" prop="part_barcode">
                <!-- 零件条码 -->
                <el-input v-model="form.part_barcode" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partCode')" prop="part_code">
                <!-- 零件编码 -->
                <el-input v-model="form.part_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.PartType')" prop="part_type">
                <!-- 零件类型 -->
                <el-input v-model="form.part_type" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed="left" :selectable="selectable"/>
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_id }}</el-descriptions-item>
                  <el-descriptions-item label="任务来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_from }}</el-descriptions-item>
                  <el-descriptions-item label="任务方式" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.TASK_WAY[props.row.task_way] }}</el-descriptions-item>
                  <el-descriptions-item label="任务类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content"> {{ dict.label.TASK_TYPE[props.row.task_type] }}</el-descriptions-item>
                  <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                  <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                  <el-descriptions-item label="批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_num }}</el-descriptions-item>
                  <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="指定起始库位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.from_stock_code }}</el-descriptions-item>
                  <el-descriptions-item label="指定目标库位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.to_stock_code }}</el-descriptions-item>
                  <el-descriptions-item label="是否需要检查辊道" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_gd_flag == 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="是否需要检查型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_model_flag == 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="是否需要告之开始启动任务" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_tell_start_flag== 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="告之启动时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_start_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之取消时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_cancel_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之暂停时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_stop_date }}</el-descriptions-item>
                  <el-descriptions-item label="告之启动者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_start_by }}</el-descriptions-item>
                  <el-descriptions-item label="告之取消者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_cancel_by }}</el-descriptions-item>
                  <el-descriptions-item label="告之暂停者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_stop_by }}</el-descriptions-item>
                  <el-descriptions-item label="任务状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.PROD_TASK_STATUS[props.row.task_status] }}</el-descriptions-item>
                  <el-descriptions-item label="是否锁定" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lock_flag == 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              label="任务号"
              width="130"
              align="center"
            />
            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              label="任务来源"
              width="120"
              align="center"
            />
            <!-- 任务方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_way"
              label="任务方式"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_WAY[scope.row.task_way] }}
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              label="任务类型"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 序列号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              label="序列号"
              width="130"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              label="批次号"
              width="130"
              align="center"
            />
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              label="型号"
              width="130"
              align="center"
            />
            <!-- 指定起始库位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_stock_code"
              label="指定起始库位"
              width="130"
              align="center"
            />
            <!-- 指定目标库位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_stock_code"
              label="指定目标库位"
              width="130"
              align="center"
            />
            <!-- 是否需要检查辊道 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="need_check_gd_flag"
              label="是否检查辊道"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.need_check_gd_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否需要检查型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="need_check_model_flag"
              label="是否检查型号"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.need_check_model_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 任务状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_status"
              label="任务状态"
              width="140"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <!-- 是否锁定 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lock_flag"
              label="是否锁定"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.lock_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              label="有效标识"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="90"
              fixed="right"
            >
            <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabledEdit="true" :disabledDle="scope.row.task_status !== 'PLAN' && scope.row.task_status !== 'WAIT'">
              </udOperation>
            </template>
          </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <blastModal v-if="blastModal" ref="blastModalFlag" @ok="blastModal = false" @handleOk="crud.toQuery()" />
  </div>
</template>

<script>
import blastModal from '../../modules/blastModal'// 新增抛丸任务
import crudCarTask from '@/api/dcs/core/wms/carTask'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  task_id: '',
  task_from: '',
  task_way: '',
  task_type: '',
  task_num: '',
  serial_num: '',
  lot_num: '',
  model_type: '',
  from_stock_code: '',
  to_stock_code: '',
  need_check_gd_flag: 'Y',
  need_check_model_flag: 'Y',
  need_tell_start_flag: 'Y',
  tell_start_date: '',
  tell_cancel_date: '',
  tell_stop_date: '',
  tell_start_by: '',
  tell_cancel_by: '',
  tell_stop_by: '',
  task_status: '',
  lock_flag: 'Y',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'WEB_CAR_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination, blastModal },
  cruds() {
    return CRUD({
      title: 'WMS天车任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'task_id',
      // 排序
      sort: ['task_id asc'],
      // CRUD Method
      crudMethod: { ...crudCarTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 350,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
      },
      modelList: [],
      blastModal: false,
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'TASK_TYPE', 'TASK_WAY', 'EXECUTE_STATUS', 'PROD_TASK_STATUS'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 350
    }
  },
  created: function() {
    this.getModelType()
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudCarTask
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              task_id: data.task_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    getModelType() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    },
    blastAdd() {
      this.blastModal = true
      this.$nextTick(() => {
        this.$refs.blastModalFlag.dialogVisible = true
      })
    },
    toDelete(data){
      if (!data.length) {
        this.$message.warning('请至少选择一项')
        return false
      }
      this.$confirm(`确定要删除${data.length}项内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          var ids = data.filter(e => e.task_status === 'PLAN' || e.task_status === 'WAIT').map(item => item.task_id).join(',')
          crudCarTask.del({ ids }).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('操作成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '发布失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
        })
        .catch(() => {

        })
    },
    selectable(row,index){
      if(row.task_status === 'PLAN' || row.task_status === 'WAIT'){
        return true
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-table__row {
  td{
    .cell > div > button{
      display: none;
    }
  }
}
</style>
