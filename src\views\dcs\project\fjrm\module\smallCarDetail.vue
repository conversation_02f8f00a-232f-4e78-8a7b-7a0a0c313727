<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="modalTitle" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <div v-for="(item,index) in carData" :key="index" class="car-info">
      <span>{{ item.name }}</span> <el-input v-model="item.value" /><span>{{ item.radioName }}</span><el-radio v-model="item.flag" />
    </div>
    <div class="code-dialog-footer">
      <el-button @click="dialogVisible = false">小车启用</el-button>
      <el-button type="primary">小车禁用</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'CARDETAIL',
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      modalTitle: '平板车详情',
      modalWidth: '25%',
      carData: [
        { name: '料框号：', value: '3', radioName: '启动指令', flag: false },
        { name: '料框重量：', value: '3', radioName: '超高报警', flag: false },
        { name: '当前位置：', value: '3', radioName: '就绪', flag: false }
      ]
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    handleClose(done) {
      done()
    }
  }
}
</script>
<style lang="less" scoped>
.car-info{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    span{
        display: block;
        width: 100px;
        text-align: right;
        margin-right: 10px;
        color: #fff;
        font-size: 18px;
    }
    ::v-deep .el-input{
        width: 150px;
        border: 2px solid #57d6f6;
        border-radius: 5px;
        .el-input__inner{
            background-color: #084aa0;
            color: #fff;
        }
    }
    ::v-deep .el-radio__inner{
        width: 20px;
        height: 20px;
    }
}
.code-dialog-footer {
  width: 100%;
  height: auto;
  padding: 10px;
  text-align: center;
}
</style>
