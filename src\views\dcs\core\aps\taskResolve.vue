<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位号:">
                <!-- 工位号 -->
                <el-select v-model="query.station_code" clearable filterable>
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_id"
                    :label="item.station_code"
                    :value="item.station_code"
                  >
                    <span style="float: left">{{ item.station_code }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.station_des }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件条码:">
                <!-- 零件条码 -->
                <el-input v-model="query.part_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件编码:">
                <!-- 零件编码 -->
                <el-input v-model="query.part_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件图号:">
                <!-- 零件图号 -->
                <el-input v-model="query.part_draw" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="零件类型:">
                <!-- 零件类型 -->
                <el-select v-model="query.part_type" clearable filterable>
                  <el-option
                    v-for="item in dict.PART_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料号:">
                <!-- 零件条码 -->
                <el-input v-model="query.material_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="切割时间:">
                <!-- 切割时间 -->
                <el-date-picker
                  v-model="query.cut_datetime"
                  type="datetimerange"
                  size="small"
                  align="right"
                  unlink-panels
                  range-separator="~"
                  start-placeholder="切割开始时间"
                  end-placeholder="切割结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item
                :label="$t('lang_pack.sortingResults.stationCode')"
                prop="station_code"
              >
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.partBarcodeNumber')"
                prop="part_barcode"
              >
                <!-- 零件条码 -->
                <el-input v-model="form.part_barcode" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.partCode')"
                prop="part_code"
              >
                <!-- 零件编码 -->
                <el-input v-model="form.part_code" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.partDrawingNumber')"
                prop="part_draw"
              >
                <!-- 零件图号 -->
                <el-input v-model="form.part_draw" clearable size="small" />
              </el-form-item>
              <!-- 物料号 -->
              <el-form-item
                :label="$t('lang_pack.sortingResults.partMaterialNumber')"
                prop="material_num"
              >
                <!-- 零件图号 -->
                <el-input v-model="form.material_num" clearable size="small" />
              </el-form-item>

              <el-form-item
                :label="$t('lang_pack.sortingResults.routingCode')"
                prop="craft_path_code"
              >
                <!-- 工艺路线编码 -->
                <el-input
                  v-model="form.craft_path_code"
                  clearable
                  size="small"
                />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.sprayCodeXCoordinate')"
                prop="print_top_x"
              >
                <!-- 喷码顶点X坐标 -->
                <el-input v-model="form.print_top_x" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.sprayCodeYCoordinate')"
                prop="print_top_y"
              >
                <!-- 喷码顶点Y坐标 -->
                <el-input v-model="form.print_top_y" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.PartType')"
                prop="part_type"
              >
                <!-- 零件类型 -->
                <el-select v-model="form.part_type" clearable filterable>
                  <el-option
                    v-for="item in dict.PART_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.nextRouting')"
                prop="next_process"
              >
                <!-- 零件加工工序 -->
                <el-input v-model="form.next_process" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.sortingResults.reservedPartProperties')"
                prop="part_attr"
              >
                <!-- 零件配送地点 -->
                <el-input v-model="form.part_attr" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="crud.cancelCU"
              >{{ $t("lang_pack.commonPage.cancel") }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions
                  style="margin-right: 150px"
                  :column="4"
                  size="small"
                  border
                >
                  <el-descriptions-item
                    label="ID"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.mo_resolve_id }}</el-descriptions-item>
                  <el-descriptions-item
                    label="主任务ID"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.mo_id }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件分拣所在工位号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.station_code }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件条码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_barcode }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件编码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_code }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件图号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_draw }}</el-descriptions-item>
                  <el-descriptions-item
                    label="物料号"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.material_num }}</el-descriptions-item>
                  <el-descriptions-item
                    label="切割时间"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.cut_datetime }}</el-descriptions-item>
                  <el-descriptions-item
                    label="工艺路线编码"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.craft_path_code }}</el-descriptions-item>
                  <el-descriptions-item
                    label="喷码顶点X坐标"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.print_top_x }}</el-descriptions-item>
                  <el-descriptions-item
                    label="喷码顶点Y坐标"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.print_top_y }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件类型"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.PART_TYPE[props.row.part_type]
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="长"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_length }}</el-descriptions-item>
                  <el-descriptions-item
                    label="宽"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_width }}</el-descriptions-item>
                  <el-descriptions-item
                    label="厚"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_thickness }}</el-descriptions-item>
                  <el-descriptions-item
                    label="重"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_weight }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件加工工序"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.next_process }}</el-descriptions-item>
                  <el-descriptions-item
                    label="零件配送地点"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.part_attr }}</el-descriptions-item>
                  <el-descriptions-item
                    label="计划分拣该零件所需时间(单位：毫秒)"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.plan_time }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 工位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              :label="$t('lang_pack.sortingResults.stationCode')"
              width="120"
              align="center"
            />
            <!-- 零件条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_barcode"
              :label="$t('lang_pack.sortingResults.partBarcodeNumber')"
              width="120"
              align="center"
            />
            <!-- 零件编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_code"
              :label="$t('lang_pack.sortingResults.partCode')"
              width="120"
              align="center"
            />
            <!-- 零件图号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_draw"
              :label="$t('lang_pack.sortingResults.partDrawingNumber')"
              width="120"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_num"
              :label="$t('lang_pack.sortingResults.partMaterialNumber')"
              width="120"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="nc_name"
              :label="$t('lang_pack.sortingResults.NCFileName')"
              width="120"
              align="center"
            />
            <!-- 切割时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_datetime"
              :label="$t('lang_pack.sortingResults.nedCuttingTime')"
              width="120"
              align="center"
            />
            <!-- 工艺路线编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="craft_path_code"
              :label="$t('lang_pack.sortingResults.routingCode')"
              width="100"
              align="center"
            />
            <!-- 喷码顶点X坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="print_top_x"
              :label="$t('lang_pack.sortingResults.sprayCodeXCoordinate')"
              width="120"
              align="center"
            />
            <!-- 喷码顶点Y坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="print_top_y"
              :label="$t('lang_pack.sortingResults.sprayCodeYCoordinate')"
              width="120"
              align="center"
            />
            <!-- 零件类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_type"
              :label="$t('lang_pack.sortingResults.PartType')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PART_TYPE[scope.row.part_type] }}
              </template>
            </el-table-column>
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_length"
              :label="$t('lang_pack.sortingResults.length')"
              width="80"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_width"
              :label="$t('lang_pack.sortingResults.width')"
              width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_thickness"
              :label="$t('lang_pack.sortingResults.thick')"
              width="80"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_weight"
              :label="$t('lang_pack.sortingResults.weight')"
              width="80"
              align="center"
            />
            <!-- 零件加工工序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="next_process"
              :label="$t('lang_pack.sortingResults.nextRouting')"
              width="110"
              align="center"
            />
            <!-- 零件配送地点 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_attr"
              :label="$t('lang_pack.sortingResults.reservedPartProperties')"
              width="100"
              align="center"
            />
            <!-- 分拣所需时间(毫秒) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_time"
              :label="$t('lang_pack.sortingResults.timeRequiredForSorting')"
              width="130"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudTaskResolve from '@/api/dcs/core/aps/taskResolve'
import crudStation from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  mo_resolve_id: '',
  mo_id: '',
  station_code: '',
  part_barcode: '',
  part_code: '',
  part_draw: '',
  cut_datetime: '',
  craft_path_code: '',
  print_top_x: '',
  print_top_y: '',
  part_type: '',
  part_length: '',
  part_width: '',
  part_thickness: '',
  part_weight: '',
  next_process: '',
  part_attr: '',
  plan_time: ''
}
export default {
  name: 'WEB_TASK_RESOLVE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '生产任务分拣解析',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mo_resolve_id',
      // 排序
      sort: ['mo_resolve_id asc'],
      // CRUD Method
      crudMethod: { ...crudTaskResolve },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_from: [
          { required: true, message: '请选择工位', trigger: 'blur' }
        ]
      },
      // 工位数据
      stationData: []
    }
  },
  dicts: ['PART_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 310
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    crudStation
      .sel(query)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data || []
          }
        } else {
          this.stationData = []
          this.$message({
            message: '工位查询异常',
            type: 'error'
          })
        }
      })
      .catch(() => {
        this.stationData = []
        this.$message({
          message: '工位查询异常',
          type: 'error'
        })
      })
  },
  methods: {}
}
</script>
