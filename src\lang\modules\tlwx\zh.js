// TLWX项目的中文翻译

export default {
  // TLWX项目特定的翻译
  tlwx: {
    // 控制模式
    manualMode: '手动模式',
    autoMode: '自动模式',
    selectMode: '选择模式',
    
    // 启动状态
    autoStart: '自动开启',
    systemStop: '系统停止',
    
    // 加热状态
    heaterRun: '加热启动',
    heaterStop: '加热停止',
    
    // 其他可能需要的翻译
    controlMode: '操作模式',
    startStatus: '启动状态',
    heaterStatus: '加热状态',
    
    // 提示信息
    pleaseStartMonitor: '请先启动监控'
  }
}
