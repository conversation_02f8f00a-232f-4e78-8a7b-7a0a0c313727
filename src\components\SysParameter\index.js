import SysParameter from './SysParameter'

const install = function(Vue) {
  Vue.mixin({
    data() {
      if (this.$options.sysParameters instanceof Array) {
        const sysParameter = {
          sysParameter: {},
          label: {}
        }
        return {
          sysParameter
        }
      }
      return {}
    },
    created() {
      if (this.$options.sysParameters instanceof Array) {
        new SysParameter(this.sysParameter).init(this.$options.sysParameters, () => {
          this.$nextTick(() => {
            this.$emit('sysParameterReady')
          })
        })
      }
    }
  })
}

export default { install }
