<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog
          :title="$t('lang_pack.vie.viewDetails')"
          width="80%"
          :before-close="handleClose"
          :visible.sync="dialogVisible"
        >
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
          >
            <!-- 时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_date"
              :label="$t('lang_pack.vie.time')"
              width="140"
              align="center"
            />
            <!-- set条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_barcode"
              :label="$t('lang_pack.vie.setCode')"
              width="120"
              align="center"
            />
            <!-- SET状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_status"
              :label="$t('lang_pack.vie.setStatus')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-button
                  size="small"
                  type="primary"
                  :class="scope.row.array_status == 'OK' ? 'btnGreen' : 'btnRed'"
                >
                  {{ scope.row.array_status }}
                </el-button>
              </template>
            </el-table-column>
            <!-- PCS条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_barcode"
              :label="$t('lang_pack.vie.pcsCode')"
              width="120"
              align="center"
            />
            <!-- BD中PCS顺序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_index"
              :label="$t('lang_pack.vie.PCSOrder')"
              width="120"
              align="center"
            />
            <!-- 是否XOUT -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="xout_flag"
              :label="$t('lang_pack.vie.whetherXOUT')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.xout_flag === 'Y' ? $t('lang_pack.vie.Yes') : $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- PCS状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_status"
              :label="$t('lang_pack.vie.PCSStatus')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="primary"
                  :class="scope.row.bd_status == 'OK' ? 'btnGreen' : 'btnRed'"
                >
                  {{ scope.row.bd_status }}
                </el-button>
              </template>
            </el-table-column>
            <!-- PCS分选NG代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_ng_code"
              :label="$t('lang_pack.vie.PCSSortingNGCode')"
              width="140"
              align="center"
            />
            <!-- PCS分选NG描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_ng_msg"
              :label="$t('lang_pack.vie.PCSSortingNGMsg')"
              width="150"
              align="center"
            />
            <!-- PCS等级 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_level"
              :label="$t('lang_pack.vie.PCSLevel')"
              width="120"
              align="center"
            />
            <!-- 线扫PCS光学点检测结果 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_mark"
              :label="$t('lang_pack.vie.LineScanPCSResults')"
              width="160"
              align="center"
            />
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="left">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button
                  v-if="canUnbind(scope.row)"
                  slot="reference"
                  type="text"
                  size="small"
                  @click="unbind(scope.row)"
                >{{
                  $t('view.button.unbind')
                }}</el-button> <!-- 解绑 -->
              </template>
            </el-table-column>
          </el-table>
          <!-- <pagination /> -->
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudSet from '@/api/pack/set'
import pcsApi from '@/api/pack/pcs'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {}
export default {
  name: 'INVENTORYDETAILS',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: '',
      // 唯一字段
      idField: 'array_id',
      // 排序
      sort: ['array_id asc'],
      // CRUD Method
      crudMethod: { ...crudSet },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        array_ids: this.propsData.array_id
      }
    })
  },
  props: {
    array_id: {
      type: String
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['edit', 'fmod_recipe_quality:edit'],
        del: ['edit', 'fmod_recipe_quality:del']
      },
      dialogVisible: false
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    },
    canUnbind(item) {
      return item.enable_flag === 'Y' && item.bd_barcode != null && item.bd_barcode !== ''
    },
    unbind(item) {
      this.$confirm(this.$t('view.dialog.confirmToUnbindThisPCSRecord'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          pcsApi
            .unbind({
              bd_barcode: item.bd_barcode
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: this.$t('view.dialog.operationSucceed'), type: 'success' })
                this.crud.toQuery()
              } else {
                this.$message({
                  message: this.$t('view.dialog.operationFailed') + ': ' + res.msg,
                  type: 'error'
                })
              }
            })
            .catch((ex) => {
              this.$message({ message: this.$t('view.dialog.operationCanceled') + ': ' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination {
    float: none !important;
    text-align: right;
}

.btnGreen {
    background-color: #0d0 !important;
    border: none !important;
}

.btnRed {
    background-color: #ee1216 !important;
    border: none !important;
}
</style>
