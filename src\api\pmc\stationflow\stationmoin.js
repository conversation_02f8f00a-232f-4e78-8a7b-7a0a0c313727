import request from '@/utils/request'

// 工位生产订单拉入查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationMoInSel',
    method: 'post',
    data
  })
}

// 预约/直接拉入
export function setin(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationMoPreSetIn',
    method: 'post',
    data
  })
}

// 取消预约拉出/入
export function cancelin(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcMeStationMoPreSetOutInCancel',
    method: 'post',
    data
  })
}

export default { sel, setin, cancelin }
