<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="分拣结果" width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 分拣工位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="station_code"
              :label="$t('lang_pack.sortingArea.SortingStation')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="fj_code"
              label="分拣编码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="fj_msg"
              label="分拣描述"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_num"
              :label="$t('lang_pack.sortingResults.partMaterialNumber')"
              width="120"
              align="center"
            />
            <!-- 零件类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="part_type"
              :label="$t('lang_pack.sortingArea.PartType')"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PART_TYPE[scope.row.part_type] }}
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="fj_start_time"
              label="分拣开始时间"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="fj_end_time"
              label="分拣结束时间"
            />
          </el-table>
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudSortResult from '@/api/dcs/core/aps/sortResult'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'SORTRESULT',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '分拣结果',
      // 唯一字段
      idField: 'mo_id',
      // 排序
      sort: ['mo_id asc'],
      // CRUD Method
      crudMethod: { ...crudSortResult },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        mo_id: this.propsData.mo_id
      }
    })
  },
  props: {
    mo_id: {
      type: [String, Number],
      default: ''
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false
    }
  },
  dicts: ['PART_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
</style>
