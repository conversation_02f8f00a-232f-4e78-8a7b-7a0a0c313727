<template>
  <el-container id="bigScreen" ref="bigScreen" style="height:100%;" class="el-container">
    <el-header class="header">
      <div class="headerLogo">
        <div><img :src="logo" class="hmi-logo"></div>
      </div>
      <span class="title">{{ currentStation.station_code + '-' + currentStation.station_des }}</span>
      <div class="wrappstyle">
        <p v-if="currentStation.station_id == '1'">
          <span :class="controlStatus.unload_status === '1' ? 'wholeline wholelinenormal' : (controlStatus.unload_status === '0' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')" />
          <span class="statuText">{{ $t('lang_pack.hmiMain.reOnline') }}</span> <!-- 收板机 -->
        </p>
        <p v-if="currentStation.station_id == '1'">
          <span :class="controlStatus.eap_status === '1' ? 'wholeline wholelinenormal' : (controlStatus.eap_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')" />
          <span class="statuText">EAP</span>
        </p>
        <p v-if="currentStation.station_id == '2'">
          <span :class="controlStatus.Un_Eap_Status === '1' ? 'wholeline wholelinenormal' : (controlStatus.Un_Eap_Status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')" />
          <span class="statuText">EAP</span>
        </p>
        <p v-if="currentStation.station_id == '1'">
          <span :class="controlStatus.plc_status === '1' ? 'wholeline wholelinenormal' : (controlStatus.plc_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')" />
          <span class="statuText">PLC</span>
        </p>
        <p v-if="currentStation.station_id == '2'">
          <span :class="controlStatus.Un_Plc_Status === '1' ? 'wholeline wholelinenormal' : (controlStatus.Un_Plc_Status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')" />
          <span class="statuText">PLC</span>
        </p>
        <p>
          <span class="wholeline wholelinenormal" />
          <span class="statuText">{{ $t('lang_pack.hmiMain.DownStatus') }}</span> <!-- 下游 -->
        </p>
      </div>
    </el-header>
    <el-row :gutter="20" class="elRowStyle">
      <el-col :span="5" style="padding-right: 0;">
        <el-card class="cardFirst">
          <table cellspacing="0" style="width: 100%" class="tableHeader">
            <tr>
              <th>{{ $t('lang_pack.hmiMain.employee') }}</th><!-- 员工号 -->
              <th>{{ $t('lang_pack.hmiMain.name') }}</th><!-- 姓名 -->
              <th>{{ $t('lang_pack.hmiMain.department') }}</th><!-- 部门 -->
            </tr>
            <tr>
              <th>{{ loginInfo.user_name }}</th>
              <th>{{ loginInfo.nick_name }}</th>
              <th>{{ loginInfo.dept_id }}</th>
            </tr>
          </table>
          <table cellspacing="0" class="tableHeader">
            <tr>
              <th>{{ $t('lang_pack.hmiMain.petName') }}</th><!-- 昵称 -->
              <th>{{ $t('view.table.shift') }}</th><!-- 班次 -->
              <th>{{ $t('lang_pack.hmiMain.status') }}</th><!-- 机台状态 -->
            </tr>
            <tr>
              <th>---</th>
              <th>{{ loginInfo.shift_id }}</th>
              <th>
                <!-- 运行  停止  待机  停机  保养   初始化-->
                {{
                  monitorData.DeviceStatus.value === '1' ? $t('lang_pack.hmiMain.deviceRun') :
                  monitorData.DeviceStatus.value === '2' ? $t('lang_pack.hmiMain.deviceStop') :
                  monitorData.DeviceStatus.value === '3' ? $t('lang_pack.hmiMain.deviceIdle') :
                  monitorData.DeviceStatus.value === '4' ? $t('lang_pack.hmiMain.deviceDown') :
                  monitorData.DeviceStatus.value === '5' ? $t('lang_pack.hmiMain.devicePm') : $t('lang_pack.hmiMain.deviceInit')
                }}
              </th>
            </tr>
          </table>
          <table cellspacing="0" class="tableHeader">
            <tr>
              <th>{{ $t('lang_pack.hmiMain.production') }}</th> <!-- 生产模式 -->
              <th>{{ $t('lang_pack.hmiMain.transport') }}</th> <!-- 搬运模式 -->
              <th>{{ $t('lang_pack.hmiMain.plate') }}</th> <!-- 板件模式 -->
            </tr>
            <tr>
              <!--EAP远程    AIS本地 -->
              <th>
                {{ monitorData.SysModel.value === '1' ? $t('lang_pack.hmiMain.EAP') : $t('lang_pack.hmiMain.AIS') }}
              </th>
              <th>{{ loginInfo.dept_id }}</th>
              <!--有Panel  无Panel -->
              <th>{{ monitorData.PanelModel.value === '1' ? $t('lang_pack.hmiMain.panel') : $t('lang_pack.hmiMain.NoPanel') }}</th>
            </tr>
          </table>
        </el-card>
        <el-card class="cardFirst mar-10">
          <div id="readRateDom" />
          <div id="deviceOeeDom">
            <div class="more">{{ $t('lang_pack.hmiMain.more') }}</div><!-- 更多-->
          </div>
          <div id="straSquareDom" />
        </el-card>
      </el-col>
      <el-col :span="13" style="padding: 0;">
        <!-- <el-card class="cardFirst"> -->
        <!-- <mainImage></mainImage> -->
        <!-- <iframge :height="'240'" :src="'http://127.0.0.1:4200/lab'" /> -->
        <!-- </el-card> -->
        <el-card class="cardFirst iframeCard" style="position: relative;padding: 0;">
          <iframge :height="'976'" :src="`http://${cellIp}:1881/`" />
        </el-card>
      </el-col>
      <el-col :span="6" style="padding-left: 5px;">
        <el-card class="cardFirst">
          <div class="tableBorder">
            <el-table border height="200" :data="stationData" style="width: 100%" :highlight-current-row="true">
              <el-table-column :label="$t('lang_pack.guanghe.parmas')" prop="tag_des" /><!-- 参数-->
              <el-table-column :label="$t('lang_pack.hmiMain.company')" prop="tag_uom" /><!-- 单位-->
              <el-table-column :label="$t('lang_pack.hmiMain.setValue')" prop="set_value" /><!-- 设定值-->
              <el-table-column :label="$t('lang_pack.hmiMain.actualValue')" prop="actual_value" /><!-- 实际值-->
            </el-table>
            <div class="more" @click="handleOpen('energyInfo','nyxx')">{{ $t('lang_pack.hmiMain.more') }}</div><!-- 更多-->
          </div>
          <div class="tableBorder">
            <el-table border height="245" :data="eapHmiTaskData" style="width: 100%;margin-top: 10px" :highlight-current-row="true">
              <el-table-column :label="$t('view.field.jobOrder.lot')" prop="lot_num" width="200" /><!-- 批次-->
              <el-table-column :label="$t('lang_pack.hmiMain.blowOff')" prop="port_code" /><!-- 放口-->
              <el-table-column :label="$t('lang_pack.hmiMain.plan')" prop="plan_lot_count" /><!-- 计划-->
              <el-table-column :label="$t('lang_pack.guanghe.finish')" prop="finish_count" /><!-- 完成-->
            </el-table>
            <div class="more" @click="handleOpen('taskInfo','sjrwsj','',false)">{{ $t('lang_pack.hmiMain.more') }}</div><!-- 更多-->
          </div>
          <div class="tableBorder">
            <el-table border height="245" :data="panelData" style="width: 100%;margin-top: 10px" :highlight-current-row="true">
              <el-table-column :label="$t('lang_pack.mainmain.index')" prop="panel_index" /><!-- 序号-->
              <el-table-column :label="$t('lang_pack.hmiMain.plateCode')" prop="panel_barcode" /><!-- 板件码-->
              <el-table-column :label="$t('lang_pack.hmiMain.panelStatus')" prop="panel_status" /><!-- 状态-->
              <el-table-column :label="$t('view.table.message')" prop="task_from" /><!-- 消息-->
            </el-table>
            <div class="more" @click="handleOpen('paneal','bjll','',true)">{{ $t('lang_pack.hmiMain.more') }}</div><!-- 更多-->
          </div>
          <div class="tableBorder">
            <el-table border height="240" :data="warningData" style="width: 100%;margin-top: 10px" :highlight-current-row="true">
              <el-table-column :label="$t('lang_pack.mfTable.time')" prop="item_date" /><!-- 时间-->
              <el-table-column :label="$t('lang_pack.hmiMain.alarmLevel')" prop="alarm_level" /><!-- 级别-->
              <el-table-column :label="$t('lang_pack.hmiMain.alarmMsg')" prop="alarm_des" /><!-- 报警消息-->
            </el-table>
            <div class="more" @click="handleOpen('warningInfo','bjxx','http://' + cellIp + ':' + webapiPort)">{{ $t('lang_pack.hmiMain.more') }}</div><!-- 更多-->
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :title="$t('lang_pack.vie.employeeLogin')" :visible.sync="userDialogVisible" width="650px" top="65px" class="elDialog"><!-- 员工登录-->
      <el-input ref="userId" v-model="userId" clearable size="mini" style="width:100%" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">{{ $t('lang_pack.vie.cancel') }}</el-button><!-- 取 消-->
        <el-button type="primary" @click="handleUserLogin">{{ $t('lang_pack.SignOn') }}</el-button><!-- 登 录-->
      </span>
    </el-dialog>
    <selectModal ref="energyInfo" :footer="false" />
    <selectModal ref="taskInfo" :footer="false" />
    <selectModal ref="paneal" :footer="false" />
    <selectModal ref="warningInfo" :footer="false" />
    <FloatingButton :current-station="currentStation" :current-menu="currentMenu" @attrKey="attrKey" />
    <el-dialog
      :title="msgDialogTitle"
      :visible.sync="mgsDialogVisible"
      :modal-append-to-body="false"
      width="650px"
      top="65px"
      :close-on-click-modal="false"
      class="elDialog"
      @close="handleCloseDialog"
    >
      <palletManualScan
        v-if="palletManualScanShow"
        ref="palletManualScan"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessage"
      />
      <manualLotList
        v-if="manualLotListShow"
        ref="manualLotList"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessageByUpEap"
      />
      <manualOrder v-if="manualOrderShow" ref="manualOrder" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
      <inspectConfirm
        v-if="inspectConfirmShow"
        ref="inspectConfirm"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessage"
      />

      <panelManualScan
        v-if="panelManualScanShow"
        ref="panelManualScan"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessageByCheck"
      />
      <panelManualConfirm
        v-if="panelManualConfirmShow"
        ref="panelManualConfirm"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessageByCheck"
      />
      <manualFinishCount
        v-if="manualFinishCountShow"
        ref="manualFinishCount"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessage"
      />
      <changeRecipe v-if="changeRecipeShow" ref="changeRecipe" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
    </el-dialog>
    <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="jsonMsgFlag" :close-on-click-modal="false" class="elDialog">
      <span>{{ jsonMsg }}</span>
    </el-dialog>
  </el-container>
</template>
<script>
// 悬浮按钮
import autofit from 'autofit.js'
import iframge from './iframe.vue'
import FloatingButton from '@/components/FloatingButton/index'
import Cookies from 'js-cookie'
import { sel as sysLogo } from '@/api/core/system/sysLogo'
import { selCellIP } from '@/api/core/center/cell'
import { sel as selHmiInfo } from '@/api/hmi/main'
import { unLoadStatusSelCore4 } from '@/api/eap/eapApsPlan'
import selectModal from '@/components/selectModal'
import { selLoginInfo, userLogin, userLogout } from '@/api/eap/eapMeStationUser'
import { EapHmiShiftWorkTimeSelect, EapHmiStatUtilitySelect, EapHmiPanelAndReadingRateSelect, EapHmiTaskSelect, CoreScadaAlarmSelect, EapHmiPanelCountPerHourSelect, CoreScadaReadTag, EapScreenConfigStationSel } from '@/api/hmi/mainIndex'
import { getStartAndEndTime } from '@/utils/index.js'
import manualOrder from '@/views/eap/project/dy/loadMonitor/manualOrder'
import changeRecipe from '@/views/eap/core/loadMonitor/changeRecipe'
import palletManualScan from '@/views/eap/core/loadMonitor/palletManualScan'
import inspectConfirm from '@/views/eap/core/loadMonitor/inspectConfirm'
import panelManualScan from '@/views/eap/core/loadMonitor/panelManualScan'
import panelManualConfirm from '@/views/eap/core/loadMonitor/panelManualConfirm'
import manualLotList from '@/views/eap/project/dy/loadMonitor/manualLotList'
import manualFinishCount from '@/views/eap/core/loadMonitor/manualFinishCount'
import mqtt from 'mqtt'
import { carrierIDReport } from '@/api/eap/project/dy/eapDyApsPlan'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'MAININDEX',
  components: { selectModal, FloatingButton, iframge, manualOrder, changeRecipe, palletManualScan, inspectConfirm, panelManualScan, panelManualConfirm, manualLotList, manualFinishCount },
  data() {
    return {
      height: document.documentElement.clientHeight - 685,
      imageHeight: document.documentElement.clientHeight - 250,
      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      warningData: [
        { item_date: '2024-12-27 11:40:12', alarm_level: '中级', alarm_des: '断开连接，通讯异常' }
      ],
      readRate: null,
      deviceOee: null,
      straSquare: null,
      stationData: [
        { tag_des: '产速', tag_uom: 'm/s', set_value: '10', actual_value: '8' }

      ],
      startTime: '',
      endTime: '',
      panelData: [], // 板件履历
      eapHmiTaskData: [], // 任务数据
      monitorData: {
        OnOffLine: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'OnOffLine', tag_des: '[PlcConfig]在线/离线模式', value: '' },
        DeviceStatus: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'DeviceStatus', tag_des: '[PlcStatus]设备状态(1运行、2停止 、3待料、4异常、5保养)', value: '' },
        // 生产模式
        SysModel: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'SysModel', tag_des: '[PlcConfig]AIS本地/EAP远程模式切换', value: '' },
        // 板件模式
        PanelModel: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PanelModel', tag_des: '[PlcConfig]Panel模式(1有/0无)', value: '' },
        AisPort1BackRequest: { client_code: 'LoadPlc', group_code: 'AisReqControl1', tag_code: 'AisPortBackRequest', tag_des: '[PlcStatus]AIS请求端口1强制退载具', value: '' },
        AisPort2BackRequest: { client_code: 'LoadPlc', group_code: 'AisReqControl2', tag_code: 'AisPortBackRequest', tag_des: '[PlcStatus]AIS请求端口2强制退载具', value: '' }
      },
      controlStatus: {
        unload_status: '', // 收板机在线
        eap_status: '0', // 放板机EAP
        Un_Eap_Status: '0',
        plc_status: '0', // 放板机PLC
        Un_Plc_Status: '0'// 收板机PLC
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: ''
      },
      currentMenu: [],
      timer: null,
      userId: '',
      userDialogVisible: false,
      parameterData: {},
      logo: '',
      // 监控弹窗相关
      msgDialogTitle: this.$t('lang_pack.Prompt'),
      tagKeyList: {},
      jsonMsgFlag: false,
      jsonMsg: '',
      mgsDialogVisible: false,
      manualOrderShow: false,
      changeRecipeShow: false,
      palletManualScanShow: false,
      inspectConfirmShow: false,
      panelManualScanShow: false,
      panelManualConfirmShow: false,
      manualLotListShow: false,
      manualFinishCountShow: false,
      delayCount: 0
    }
  },
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 685
      that.imageHeight = document.documentElement.clientHeight - 307
    }
    window.addEventListener('resize', this.getResize)
  },
  created() {
    this.handleRefresh()
    window.addEventListener('message', this.receiveMessage, false)
  },
  beforeDestroy() {
    autofit.off()
    clearInterval(this.timer)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    handleRefresh() {
      const query = { user_name: Cookies.get('userName'), userID: Cookies.get('userId') }
      const params = { now_time: getStartAndEndTime().currentDateString }
      Promise.all([selHmiInfo(query), EapHmiShiftWorkTimeSelect(params)]).then(
        ([res1, res2]) => {
          // 返回的菜单 和账户信息
          if (res1.station.length > 0 && res1.station[0].station_list.length > 0) {
            const station = res1.station[0]
            const firstStation = station.station_list[0]
            this.currentStation = {
              prod_line_id: station.prod_line_id,
              prod_line_code: station.prod_line_code,
              prod_line_des: station.prod_line_des,
              station_id: firstStation.station_id,
              station_code: firstStation.station_code,
              station_des: firstStation.station_des,
              cell_id: firstStation.cell_id
            }
            this.getCellIp()
            this.getUnLoadStatusInfo()
            this.timer = setInterval(() => {
              this.getUnLoadStatusInfo() // 获取离线状态
            }, 5000)
            this.getLoginInfo() // 获取人员信息
            this.getLogo() // 获取logo
          }
          if (res1.menu.length > 0) {
            for (let i = 0; i < res1.menu.length; i++) {
              this.currentMenu.push({
                function_path: res1.menu[i].function_path,
                menu_item_des: res1.menu[i].menu_item_des,
                menu_item_code: res1.menu[i].menu_item_code,
                menu_item_ico: res1.menu[i].menu_item_ico,
                menu_item_id: res1.menu[i].menu_item_id
              })
            }
          }
          if (res2.data.length > 0) {
            const data = res2.data[0]
            const startTime = this.startTime = data.shift_start_time
            const endTime = this.endTime = data.shift_end_time
            setTimeout(() => {
              this.getDeviceOee(startTime, endTime) // 设备oee
              this.getAlarmInfo(startTime, endTime) // 报警信息
              this.getPanelRate(startTime, endTime) // 板件履历及读码率
              this.getStraSquare(startTime, endTime) // 投板数量
              this.getEapTaskSelect(startTime, endTime) // 时间统任务数据
              this.getEapScreenConfigStationSel(startTime, endTime)// 能源信息
            }, 500)
          }
        }, (err) => {
          this.$message({ type: 'error', message: err.msg + this.$t('lang_pack.vie.queryException') })
        })
    },
    receiveMessage(event) {
      const data = event.data
      if (!data.key) return
      this[data.key]()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    handleOpen(attr, type, addRess, flag) {
      const search = {
        page: 1,
        size: 20,
        station_id: this.currentStation.station_id,
        startTime: this.startTime,
        endTime: this.endTime,
        user_name: Cookies.get('userName')
      }
      // 有的接口是不需要开始时间与结束时间
      if (!flag) {
        delete search.startTime
        delete search.endTime
      }
      this.$refs[attr].open({
        addRess, // 这个代表不走aisEsbWeb 的代理
        type,
        checkType: '',
        search
      })
    },
    getLogo() {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName')
      }
      sysLogo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.logo = 'data:image/png;base64,' + defaultQuery.data[0].logo
            }
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: this.$t('lang_pack.vie.queryException')
          })
        })
    },
    getResize() {
      this.straSquare.resize()
    },
    getDeviceOee(startTime, endTime) {
      const query = {
        station_id: this.currentStation.station_id,
        startTime,
        endTime
      }
      EapHmiStatUtilitySelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            const arr = []
            defaultQuery.data.forEach(e => {
              arr.push(
                { name: this.$t('lang_pack.guanghe.plateNG'), value: e.fail_board_count }, // 板件NG数量
                { name: this.$t('lang_pack.guanghe.trayNG'), value: e.pallet_ng_count }, // 托盘NG数量
                { name: this.$t('lang_pack.guanghe.trayOK'), value: e.pallet_ok_count }, // 托盘OK数量
                { name: this.$t('lang_pack.guanghe.panelNG'), value: e.panel_ng_count }, // 面板NG数量
                { name: this.$t('lang_pack.guanghe.panelOK'), value: e.panel_ok_count }, // 面板OK数量
              )
            })
            this.deviceOee = this.$echarts.init(document.getElementById('deviceOeeDom'))
            var option = {
              tooltip: {
                trigger: 'item'
              },
              color: ['#F4716F', '#FBAF03', '#49CA40', '#7E6CFE', '#5697FB'],
              legend: {
                top: '0%',
                left: 'left',
                textStyle: {
                  color: '#90979c'
                }
              },
              grid: {
                bottom: '0%'// 生成的echarts图片和顶部的距离
              },
              title: [
                {
                  text: defaultQuery.data.length && defaultQuery.data[0].total_count,
                  subtext: this.$t('lang_pack.guanghe.deviceOee'), // 设备oee
                  top: '40%',
                  left: '49%',
                  textAlign: 'center',
                  itemGap: 20,
                  subtextStyle: {
                    color: '#90979c',
                    fontSize: 16,
                    align: 'center'
                  },
                  textStyle: {
                    color: '#90979c',
                    fontSize: 24
                  }
                }
              ],
              series: [
                {
                  name: '',
                  type: 'pie',
                  radius: ['60%', '80%'],
                  center: ['50%', '55%'],
                  avoidLabelOverlap: false,
                  itemStyle: {
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 3
                  },
                  label: {
                    show: false,
                    position: 'center'
                  },
                  labelLine: {
                    show: false
                  },
                  data: arr
                }
              ]
            }
            this.deviceOee.setOption(option)
          }
        }
      })
    },
    getStraSquare(startTime, endTime) {
      const query = {
        station_id: this.currentStation.station_id,
        startTime,
        endTime
      }
      EapHmiPanelCountPerHourSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.straSquare = this.$echarts.init(document.getElementById('straSquareDom'))
            var xData = []
            var offData = []
            var okData = []
            var allData = []
            for (let i = 0; i < defaultQuery.data.length; i++) {
              xData.push(defaultQuery.data[i].hour + 'h')
              okData.push(defaultQuery.data[i].okCount)
              offData.push(defaultQuery.data[i].offCount)
              allData.push(defaultQuery.data[i].allCount)
            }
            var option = {
              title: {
                x: '4%',
                textStyle: {
                  color: '#fff',
                  fontSize: '22'
                },
                subtextStyle: {
                  color: '#90979c',
                  fontSize: '16'
                }
              },
              color: ['#DDB310', '#556EC5'],
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow',
                  textStyle: {
                    color: '#fff'
                  }
                }
              },
              grid: {
                borderWidth: 0,
                top: '15%'
              },
              legend: {
                x: '1%',
                top: '1%',
                textStyle: {
                  color: '#90979c'
                },
                data: [this.$t('lang_pack.guanghe.OkQuantity'), this.$t('lang_pack.guanghe.NGQuantity')] // ok数量 ng数量
              },

              calculable: true,
              xAxis: [
                {
                  type: 'category',
                  axisLine: {
                    lineStyle: {
                      color: '#90979c'
                    }
                  },
                  splitLine: {
                    show: false
                  },
                  axisTick: {
                    show: false
                  },
                  splitArea: {
                    show: false
                  },
                  axisLabel: {
                    interval: 0
                  },
                  data: xData
                }
              ],
              yAxis: [
                {
                  type: 'value',
                  splitLine: {
                    show: false
                  },
                  axisLine: {
                    lineStyle: {
                      color: '#90979c'
                    }
                  },
                  axisTick: {
                    show: false
                  },
                  axisLabel: {
                    interval: 0
                  },
                  splitArea: {
                    show: false
                  }
                }
              ],
              dataZoom: [
                {
                  show: true,
                  height: 10,
                  bottom: 5,
                  start: 0,
                  end: 80,
                  handleIcon:
                      'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
                  handleSize: '110%',
                  handleStyle: {
                    color: '#d3dee5'
                  },
                  textStyle: {
                    color: '#000'
                  },
                  borderColor: '#90979c'
                },
                {
                  type: 'inside',
                  show: true,
                  height: 15,
                  start: 1,
                  end: 35
                }
              ],
              series: [
                {
                  name: this.$t('lang_pack.guanghe.OkQuantity'),
                  type: 'bar',
                  stack: this.$t('lang_pack.guanghe.total'),
                  barMaxWidth: 15,
                  barGap: '10%',
                  itemStyle: {
                    normal: {
                      color: '#556EC5',
                      label: {
                        show: true,
                        textStyle: {
                          color: '#473904'
                        },
                        position: 'inside',
                        formatter: function(p) {
                          return p.value > 0 ? p.value : ''
                        }
                      }
                    }
                  },
                  data: okData
                },
                {
                  name: this.$t('lang_pack.guanghe.NGQuantity'),
                  type: 'bar',
                  stack: this.$t('lang_pack.guanghe.total'),
                  itemStyle: {
                    normal: {
                      color: '#DDB310',
                      barBorderRadius: 0,
                      label: {
                        textStyle: {
                          color: '#473904'
                        },
                        show: true,
                        position: 'inside',
                        formatter: function(p) {
                          return p.value > 0 ? p.value : ''
                        }
                      }
                    }
                  },
                  data: offData
                }
              ]
            }
            this.straSquare.setOption(option)
          }
        }
      })
    },
    getAlarmInfo(startTime, endTime) {
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort
      }
      const query = {
        station_code: this.currentStation.station_code,
        item_date: [startTime, endTime]
      }
      CoreScadaAlarmSelect(path, query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.warningData = defaultQuery.data
          }
        }
      })
    },
    attrKey(item) {
      this[item.key](item)
    },
    openUserLogin() {
      this.userId = ''
      this.userDialogVisible = true
      this.$nextTick(x => {
        // 正确写法
        this.$refs.userId.focus()
      })
    },
    // 获取当前登录信息
    getLoginInfo() {
      this.loginInfo.user_name = '---'
      this.loginInfo.nick_name = '---'
      this.loginInfo.dept_id = '---'
      this.loginInfo.shift_id = '---'
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.hmiMain.procedure'), type: 'error' }) // 获取当前登录信息异常
        })
    },
    // 处理登录
    handleUserLogin() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        user_code: this.userId
      }
      userLogin(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const loginInfo = defaultQuery.data[0]
            this.loginInfo.user_name = loginInfo.user_name
            this.loginInfo.nick_name = loginInfo.nick_name
            this.loginInfo.dept_id = loginInfo.dept_id
            this.loginInfo.shift_id = loginInfo.shift_id
            this.userDialogVisible = false
            this.$message({ message: this.$t('lang_pack.vie.loginSuccess'), type: 'success' }) // 登录成功
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' }) // 操作异常
        })
    },
    // 处理登出
    handleUserLogout() {
      this.$confirm(this.$t('lang_pack.vie.AreYouSureToLogOut'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        type: 'warning'
      })
        .then(() => {
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.currentStation.station_id
          }
          userLogout(query)
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.loginInfo.user_name = ''
                this.loginInfo.nick_name = ''
                this.loginInfo.dept_id = ''
                this.loginInfo.shift_id = ''
                this.$message({ message: this.$t('lang_pack.vie.loginSuccess'), type: 'success' }) // 登出成功
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' }) // 操作异常
            })
        })
        .catch(() => {
        })
    },
    getPanelRate(startTime, endTime) {
      const query = {
        station_id: this.currentStation.station_id,
        startTime,
        endTime,
        page: 1,
        size: 5
      }
      EapHmiPanelAndReadingRateSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.panelData = defaultQuery.data.splice(0, 5)
          }
          const countObj = JSON.parse(defaultQuery.result)
          var series = [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['60%', '80%'],
              center: ['50%', '55%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 5,
                borderColor: '#fff',
                borderWidth: 3
              },
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: [
                { name: this.$t('lang_pack.guanghe.OkQuantity'), value: countObj.okCount }, // ok数量
                { name: this.$t('lang_pack.guanghe.NGQuantity'), value: countObj.ngCount }, // ng 数量
                { name: this.$t('lang_pack.guanghe.NGPass'), value: countObj.ngpassCount }, // NG_PASS数量
                { name: this.$t('lang_pack.guanghe.OfflineCodeReading'), value: countObj.offlineCount }, // 离线读码量
                { name: this.$t('lang_pack.guanghe.OnlineCodeReading'), value: countObj.onlineCount }, // 在线读码量
                { name: this.$t('lang_pack.guanghe.readBitRate'), value: countObj.readingRate }// 读码率
              ]
            }
          ]
          var that = this
          this.readRate = this.$echarts.init(document.getElementById('readRateDom'))
          var option = {
            tooltip: {
              trigger: 'item',
              formatter: function(params) {
                // 总数 读码率
                var res = params.data.name + '：' + params.data.value + '<br/>' + `${that.$t('lang_pack.guanghe.total') + ':'}` + countObj.allCount + '<br/>' + `${that.$t('lang_pack.guanghe.readBitRate') + ':'}` + countObj.readingRate + '<br/>'
                return res
              }
            },
            color: ['#F4716F', '#FBAF03', '#49CA40', '#7E6CFE', '#5697FB'],
            legend: {
              top: '0%',
              left: 'left',
              textStyle: {
                color: '#90979c'
              }
            },
            grid: {
              bottom: '1%'// 生成的echarts图片和顶部的距离
            },
            title: [
              {
                text: countObj.allCount,
                subtext: this.$t('lang_pack.guanghe.total'),
                top: '30%',
                left: '49%',
                textAlign: 'center',
                itemGap: 20,
                subtextStyle: {
                  color: '#90979c',
                  fontSize: 16,
                  align: 'center'
                },
                textStyle: {
                  color: '#90979c',
                  fontSize: 24
                }
              }
            ],
            series
          }
          this.readRate.setOption(option)
        }
      })
    },
    getEapTaskSelect(startTime, endTime) {
      const query = {
        station_id: this.currentStation.station_id
      }
      EapHmiTaskSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.eapHmiTaskData = defaultQuery.data.splice(0, 5)
          }
        }
      })
    },
    getEapScreenConfigStationSel(startTime, endTime) {
      const query = {
        page: 1,
        size: 5,
        sort: 'quality_id asc',
        startTime,
        endTime,
        station_id: this.currentStation.station_id,
        user_name: Cookies.get('userName')
      }
      EapScreenConfigStationSel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
          if (defaultQuery.result !== '') {
            const Arr = JSON.parse(defaultQuery.result)
          }
        }
      })
    },
    getUnLoadStatusInfo() {
      if (this.currentStation.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      unLoadStatusSelCore4(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.result !== '') {
              const resultData = JSON.parse(defaultQuery.result)
              var unload_onoff_value = resultData.unload_onoff_value
              if (unload_onoff_value !== '0' && unload_onoff_value) {
                this.controlStatus.unload_status = '1'
              } else {
                this.controlStatus.unload_status = '0'
              }
            }
          }
        })
        .catch(() => {
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://*************:8083' + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        // 监控通讯状态
        var eapClientCode = 'LoadEap'
        var plcClientCode = 'LoadPlc'
        if (this.aisMonitorMode === 'AIS-SERVER') {
          eapClientCode = eapClientCode + '_' + this.currentStation.station_code
          plcClientCode = plcClientCode + '_' + this.currentStation.station_code
        }
        this.topicSubscribe('SCADA_STATUS/' + eapClientCode)
        this.topicSubscribe('SCADA_BEAT/' + eapClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        // 其他标准点位监控
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })
      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0) {
            this.handleMessage(jsonData)
          } else if (topic.indexOf('SCADA_BEAT/') >= 0) { // 心跳
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf('SCADA_BEAT/LoadEap') >= 0 && this.controlStatus.eap_status !== '2') { // 放板机EAP
              this.controlStatus.eap_status = heartBeatValue
            } else if (topic.indexOf('SCADA_BEAT/UnLoadEap') >= 0 && this.controlStatus.Un_Eap_Status !== '2') { // 收板机PLC
              this.controlStatus.Un_Eap_Status = heartBeatValue
            } else if (topic.indexOf('SCADA_BEAT/LoadPlc') >= 0 && this.controlStatus.plc_status !== '2') { // 放板机PLC
              this.controlStatus.plc_status = heartBeatValue
            } else if (topic.indexOf('SCADA_BEAT/UnLoadEap') >= 0 && this.controlStatus.Un_Plc_Status !== '2') { // 收板机PLC
              this.controlStatus.Un_Plc_Status = heartBeatValue
            }
          }
        } catch (e) { console.log(e) }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    handleWrite(item) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: item.tag_key,
        TagValue: item.tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = `SCADA_WRITE/${item.tag_key.split('/')[0]}`
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          this.handleCloseDialog()
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessageByCheck(topic, msg, checkFlag, panelBarCode) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          this.handleCloseDialog()
          this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
        } else {
          this.$message({ message: this.$t('lang_pack.commonPage.operationfailure'), type: 'error' })
        }
      })
    },
    // 定制:人工扫描任务并提交到EAP验证
    sendMessageByUpEap(topic, msg, palletNum, lotList, portCodeIndex) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        port_index: portCodeIndex,
        pallet_num: palletNum,
        lot_list: JSON.stringify(lotList)
      }
      // 倒计时功能
      this.delayCount = 15
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      carrierIDReport(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // qos消息发布服务质量
            this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
              if (!error) {
                this.handleCloseDialog()
                this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.commonPage.operationfailure'), type: 'error' })
              }
            })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'load_monitor') return
      this.manualOrderShow = false
      this.msgDialogTitle = json.func_des
      this.tagKeyList = json.func_paras
      this.currentFuncCode = json.func_code
      // 若是员工登入
      if (json.func_code === 'user_login_in') {
        if (!this.userDialogVisible) {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.openUserLogin()
        }
        return
      }
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      } else {
        if (json.func_code === 'manual_order') {
          this.manualOrderShow = true
        } else if (json.func_code === 'change_recipe') {
          this.changeRecipeShow = true
        } else if (json.func_code === 'pallet_manual_scan') {
          this.palletManualScanShow = true
        } else if (json.func_code === 'inspect_confirm') {
          this.inspectConfirmShow = true
        } else if (json.func_code === 'panel_manual_scan') {
          this.panelManualScanShow = true
        } else if (json.func_code === 'panel_manual_confirm') {
          this.panelManualConfirmShow = true
        } else if (json.func_code === 'manual_lot_scan') {
          this.manualLotListShow = true
        } else if (json.func_code === 'wip_manual_finish_count') {
          this.manualFinishCountShow = true
        }
        this.mgsDialogVisible = true
      }
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      var msg_level = 'info'
      if (json.msg_level === 'INFO') {
        msg_level = 'info'
      } else if (json.msg_level === 'ERROR') {
        msg_level = 'error'
      } else if (json.msg_level === 'WARN') {
        msg_level = 'warning'
      }
      if (json.func_dlg === 'Y') {
        this.jsonMsgFlag = true
        this.jsonMsg = json.msg
      } else {
        this.messageList.push({ content: json.msg, level: msg_level })
      }
    },
    // 从后台REDIS获取数据
    getTagValue() {
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort
        // path = 'http://*************:' + 8089 + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort
        // path = 'http://*************:' + 8089 + method
      }

      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      CoreScadaReadTag(path, readTagArray).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          // if (defaultQuery.data.length > 0) {
          // }
        }
      })
    },
    // 处理关闭弹窗
    handleCloseDialog() {
      this.manualOrderShow = false
      this.changeRecipeShow = false
      this.palletManualScanShow = false
      this.inspectConfirmShow = false
      this.panelManualScanShow = false
      this.panelManualConfirmShow = false
      this.manualLotListShow = false
      this.mgsDialogVisible = false
      this.manualFinishCountShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/HmiMain.scss';
</style>
