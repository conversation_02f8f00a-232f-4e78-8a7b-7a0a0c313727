export const view = {
  title: {
    boardPractice: 'ซ้อมกระดาน',
    cimMessageReport: 'รายงานข้อความ CIM',
    interfaceLog: 'บันทึกอินเทอร์เฟซ',
    threeRateReport: 'รายงานอัตราสาม',
    communicationDiagnosis: 'การวินิจฉัยการสื่อสาร',
    pointChangeTracking: 'การติดตามการเปลี่ยนแปลงจุด',
    pointWriteTracking: 'การติดตามการเขียนจุด',
    setting: 'การตั้งค่า',
    dcsAlarm: 'การเตือนภัย',
    parkSortRule: 'การตั้งค่ากฎการคัดแยก',
    parkSortSplitRule: 'การตั้งค่ากฎการเปรียบเทียบการคัดแยก',
    parkXNPSort: 'บันทึกโหมดการคัดแยกตามจำนวน/ตำแหน่ง X',
    parkExtra: 'การตั้งค่าพารามิเตอร์เพิ่มเติม'
  },
  button: {
    export: 'ส่งออก',
    search: 'ค้นหา',
    reset: 'รีเซ็ต',
    view: 'ดู',
    close: 'ปิด',
    confirm: 'ยืนยัน',
    cancel: 'ยกเลิก',
    disable: 'ปิดการใช้งาน',
    unbind: 'ยกเลิกการผูกพัน',
    setting: 'การตั้งค่า',
    addPosition: 'เพิ่มตำแหน่ง'
  },
  enum: {
    placeholder: {
      pleaseChosen: 'กรุณาเลือก',
      pleaseEnter: 'กรุณาใส่'
    },
    boardErrorCode: {
      normal: 'ปกติ',
      mixedBoard: 'บอร์ดผสม',
      readFailureBoard: 'บอร์ดที่ไม่สามารถอ่านได้',
      duplicateCode: 'รหัสซ้ำ',
      forcedPass: 'ผ่านด้วยความบังคับ',
      forcedFinal: 'บังคับแบทช์',
      otherDefined: 'ที่กำหนดเองอื่น ๆ'
    },
    manualProcessingBoard: {
      manualInputMode: 'โหมดการป้อนข้อมูลด้วยมือ',
      reread: 'อ่านซ้ำ',
      forcedPass: 'ผ่านด้วยความบังคับ'
    },
    communication: {
      normal: 'การสื่อสารปกติ',
      abnormal: 'การสื่อสารผิดปกติ'
    },
    writeStatus: {
      normal: 'การเขียนปกติ',
      abnormal: 'การเขียนข้อยกเว้น'
    }
  },
  field: {
    plan: {
      lotNum: 'หมายเลขชุด',
      lotNo: 'Lot No',
      plantOrderNum: 'หมายเลขคำสั่งซื้อโรงงาน',
      tailCount: 'จำนวนหาง'
    },
    jobOrder: {
      batchNo: 'หมายเลขชุด',
      laserBatchNo: 'หมายเลขชุดเลเซอร์',
      originalLotNo: 'หมายเลขล็อตต้นฉบับ',
      certifyLotNo: 'ตรวจสอบหมายเลขล็อต',
      typesettingNumber: 'จำนวนการจัดรูป',
      customerMaterialNo: 'หมายเลขวัสดุของลูกค้า',
      lot: 'ล็อต',
      model: 'รุ่น',
      ul: 'UL',
      bdBarcodes: 'ชุดบาร์โค้ด PCS',
      xoutPositions: 'ชุดตำแหน่ง XOUT',
      bdLevels: 'ระดับ PCS',
      bdLevelJudgments: 'การตัดสินใจระดับ PCS'
    },
    setRecord: {
      created_time: 'เวลาที่สร้าง',
      lot_num: 'หมายเลขคำสั่งซื้อ',
      array_index: 'ลำดับ SET ของคำสั่งซื้อ',
      board_sn: 'หมายเลขซีเรียลการสแกนเส้น',
      pile_barcode: 'บาร์โค้ดการห่อหุ้ม',
      array_barcode: 'บาร์โค้ด SET',
      array_status: 'สถานะ SET',
      array_ng_code: 'รหัส NG การแยก SET',
      array_ng_msg: 'คำอธิบาย NG การแยก SET',
      array_level: 'ระดับ SET การสแกนเส้น',
      array_mark: 'ผลการตรวจสอบจุดออปติคการสแกนเส้น',
      array_bd_count: 'จำนวน PCS ใต้ SET การสแกนเส้น',
      board_result: 'ผลการตัดสินใจบอร์ด',
      board_turn: 'ทิศทางการหมุน',
      deposit_position: 'ตำแหน่งการฝาก',
      xout_flag: 'เป็นการแยก XOUT หรือไม่',
      xout_set_num: 'จำนวนที่ตั้ง XOUT',
      xout_act_num: 'จำนวนที่แท้จริง XOUT',
      array_front_info: 'ข้อมูลการสแกนเส้นด้านหน้า SET',
      array_back_info: 'ข้อมูลการสแกนเส้นด้านหลัง SET',
      task_type: 'ประเภทงาน',
      model_type: 'หมายเลขรุ่น (รุ่น)',
      model_version: 'เวอร์ชันหมายเลขรุ่น (รุ่น)',
      array_type: 'ประเภท SET',
      bd_type: 'ประเภท PCS',
      m_length: 'ความยาว, หน่วยมิลลิเมตร',
      m_width: 'ความกว้าง, หน่วยมิลลิเมตร',
      m_tickness: 'ความหนา, หน่วยมิลลิเมตร',
      m_weight: 'น้ำหนัก, หน่วยกรัม',
      batch_no: 'หมายเลขชุด',
      cycle_period: 'รอบระยะเวลา',
      laser_batch_no: 'หมายเลขชุดเลเซอร์',
      typesetting_no: 'จำนวนการจัดรูป',
      bd_barcodes: 'ชุดบาร์โค้ด PCS',
      xout_positions: 'ชุดตำแหน่ง XOUT',
      bd_levels: 'ชุดระดับ PCS',
      bd_level_judgments: 'การตัดสินใจระดับ PCS',
      customer_mn: 'หมายเลขวัสดุของลูกค้า',
      ul_code: 'รหัส UL',
      pile_use_flag: 'ถูกห่อหุ้มสำหรับการใช้งานหรือไม่',
      enable_flag: 'ธงที่ถูกต้อง',
      unbind_flag: 'เป็นการยกเลิกการผูกหรือไม่',
      unbind_time: 'เวลายกเลิกการผูก',
      unbind_way: 'คำอธิบายวิธีการยกเลิกการผูก'
    },
    pileRecord: {
      intef_return_code: 'รหัสการส่งคืนอินเตอร์เฟซ',
      intef_return_msg: 'ข้อความการส่งคืนอินเตอร์เฟซ',
      apply_return_code: 'รหัสการส่งคืนการใช้',
      apply_return_msg: 'ข้อความการส่งคืนการใช้',
      print_return_code: 'รหัสการส่งคืนการพิมพ์',
      print_return_msg: 'ข้อความการส่งคืนการพิมพ์',
      inspect_result_code: 'รหัสผลการตรวจสอบ',
      inspect_result_msg: 'ข้อความผลการตรวจสอบ'
    },
    splitRules: {
      order_lot: 'ระเบียบการตัดเข็ม',
      custom_model: 'ระเบียบการตัดเข็มที่กำหนดเอง',
      array_set: 'SET ตัดเข็ม SET หมายเลขบอร์ด',
      array_lot: 'SET ตัดเข็มกฎ',
      array_model: 'SET ตัดเข็มระเบียบ',
      array_cycle: 'SET ตัดเข็มรอบ',
      array_dc: 'SET ตัดเข็ม DateCode กฎ',
      array_ln: 'SET ตัดเข็ม LotNum กฎ',
      bd_set: 'PCS ตัดเข็ม SET หมายเลขบอร์ด',
      bd_lot: 'PCS ตัดเข็มกฎ',
      bd_model: 'PCS ตัดเข็มระเบียบ',
      bd_cycle: 'PCS ตัดเข็มรอบ',
      bd_dc: 'PCS ตัดเข็ม DateCode กฎ',
      bd_ln: 'PCS ตัดเข็ม LotNum กฎ',
      bd_index: 'PCS ตัดเข็มดัชนี'
    },
    recipe: {
      weight_error: 'ความคลาดเคลื่อนของน้ำหนัก, หน่วยกรัม(g)',
      inkjet_tpl: 'พิมพ์เทมเพลต'
    },
    stationFlow: {
      station_code: 'รหัสสถานี',
      station_desc: 'คำอธิบายสถานี',
      serial_num: 'รหัสแท็ก',
      recipe_paras: 'พารามิเตอร์สูตรอาหาร',
      arrive_date: 'เวลามาถึง',
      leave_date: 'เวลาออก',
      cost_time: 'เวลาที่ใช้ (ms)'
    },
    stationQuality: {
      station_flow_id: 'ID ของกระบวนการ',
      station_code: 'รหัสสถานี',
      station_desc: 'คำอธิบายสถานี',
      serial_num: 'รหัสแท็ก',
      quality_for: 'แหล่งคุณภาพ',
      tag_code: 'รหัสแท็ก',
      tag_value: 'ค่าแท็ก',
      quality_d_sign: 'เครื่องหมายคุณภาพ',
      trace_d_time: 'เวลาติดตาม'
    },
    ccdValidationResult: {
      station_code: 'รหัสสถานี',
      station_desc: 'คำอธิบายสถานี',
      serial_num: 'รหัสแท็ก',
      validate_result: 'ผลการตรวจสอบ',
      validate_msg: 'ข้อความการตรวจสอบ',
      validate_time: 'เวลาการตรวจสอบ'
    }
  },
  form: {
    batchNo: 'แบทช์',
    cardWorkOrder: 'ใบสั่งงาน',
    subBatchNo: 'ย่อยแบทช์',
    subSimplifiedCode: 'รหัสย่อย',
    materialNo: 'วัสดุ',
    carrierBarcode: 'บาร์โค้ดขนส่ง',
    taskSource: 'แหล่งงาน',
    station: 'ตำแหน่งงาน',
    portNo: 'ท่าเรือ',
    boardBarcode: 'บาร์โค้ดบอร์ด',
    boardStatus: 'สถานะบอร์ด',
    boardErrorCode: 'รหัสข้อผิดพลาดบอร์ด',
    isFirstCheck: 'ตรวจสอบครั้งแรก',
    isDummy: 'ดัมมี่',
    manualProcessingBoard: 'บอร์ดงานแบบฉบับ',
    hasPanelMode: 'ไม่มีโหมดแผง',
    operator: 'ผู้ประกอบการ',
    rooftopBarcode: 'บาร์โค้ดบนหลังคา',
    orientation: 'ทิศทาง',
    orientationToFront: 'หัว',
    orientationToBack: 'ด้านหลัง',
    isOffline: 'ออฟไลน์',
    time: 'เวลา',
    timePeriod: 'ช่วงเวลา',
    timePeriodStart: 'เวลาเริ่ม',
    timePeriodEnd: 'เวลาสิ้นสุด',
    messageSource: 'แหล่งข้อมูล',
    displayMode: 'โหมดการแสดงผล',
    completionFlag: 'เครื่องหมายการเสร็จสิ้น',
    interfaceName: 'ชื่ออินเทอร์เฟซ',
    interfaceDescription: 'คำอธิบายอินเทอร์เฟซ',
    sourceSystem: 'ระบบต้นทาง',
    targetSystem: 'ระบบปลายทาง',
    requestParameters: 'พารามิเตอร์คำขอ',
    responseParameters: 'พารามิเตอร์การตอบสนอง',
    isSuccess: 'สำเร็จหรือไม่',
    interfaceMethod: 'วิธีอินเทอร์เฟซ',
    instance: 'ตัวอย่าง',
    communicationStatus: 'สถานะการสื่อสาร',
    tagGroup: 'กลุ่มแท็ก',
    tag: 'แท็ก',
    processWarning: 'การเตือนกระบวนการ',
    shiftCode: 'รหัสกะ',
    uploadFlag: 'สัญญาณอัปโหลด',
    writeStatus: 'สถานะการเขียน',
    isReversed: 'กลับ',
    reversedFlag: 'ธงกลับ'
  },
  table: {
    name: 'ชื่อ',
    time: 'เวลา',
    subTaskNo: 'หมายเลขงานย่อย',
    portNo: 'หมายเลขพอร์ต',
    carrierBarcode: 'บาร์โค้ดขนส่ง',
    rooftopBarcode: 'บาร์โค้ดบนหลังคา',
    boardBarcode: 'บาร์โค้ดบอร์ด',
    boardStatus: 'สถานะบอร์ด',
    ngBoardErrorCode: 'รหัสข้อผิดพลาดบอร์ด NG',
    ngBoardErrorDescription: 'รายละเอียดข้อผิดพลาดบอร์ด NG',
    isFirstCheckBoard: 'เป็นการตรวจสอบบอร์ดครั้งแรกหรือไม่',
    taskSource: 'แหล่งที่มาของงาน',
    motherBatch: 'แม่แบทช์',
    subTaskSimplifiedCode: 'รหัสงานย่อยที่ทำให้เรียบง่าย',
    subTaskSorting: 'การเรียงงานย่อย',
    materialNo: 'หมายเลขวัสดุ',
    carrierType: 'ประเภทขนส่ง',
    layer: 'ชั้น',
    subDiskSorting: 'การเรียงดิสก์ย่อย',
    boardLength: 'ความยาวบอร์ด',
    boardWidth: 'ความกว้างบอร์ด',
    boardThickness: 'ความหนาบอร์ด',
    boardSorting: 'การเรียงบอร์ด',
    isDummyBoard: 'เป็นบอร์ดดัมมี่หรือไม่',
    manualProcessingBoard: 'บอร์ดการประมวลผลด้วยมือ',
    isPanelMode: 'เป็นโหมดแผงหรือไม่',
    operator: 'ผู้ประกอบการ',
    isEAP: 'เป็น EAP หรือไม่',
    orientation: 'ทิศทาง',
    messageSource: 'แหล่งข้อมูล',
    displayMode: 'โหมดการแสดงผล',
    completionFlag: 'เครื่องหมายการเสร็จสิ้น',
    message: 'ข้อความ',
    code: 'รหัส',
    serialNumber: 'หมายเลขซีเรียล',
    esbInterfaceName: 'ชื่ออินเทอร์เฟซ ESB',
    esbInterfaceDescription: 'คำอธิบายอินเทอร์เฟซ ESB',
    interfaceMethod: 'วิธีอินเทอร์เฟซ',
    esbInterfaceAddress: 'ที่อยู่อินเทอร์เฟซ ESB',
    sourceSystem: 'ระบบต้นทาง',
    targetSystem: 'ระบบปลายทาง',
    startTime: 'เวลาเริ่มต้น',
    endTime: 'เวลาสิ้นสุด',
    requestDuration: 'ระยะเวลาของคำขอ',
    isSuccess: 'สำเร็จหรือไม่',
    interfaceMessage: 'ข้อความอินเทอร์เฟซ',
    requestParameters: 'พารามิเตอร์คำขอ',
    responseParameters: 'พารามิเตอร์การตอบสนอง',
    customizedParameters: 'พารามิเตอร์ที่กำหนดเอง',
    requestMessage: 'ข้อความคำขอ',
    remark: 'หมายเหตุ',
    shift: 'กะ',
    shiftCode: 'รหัสกะ',
    shiftStart: 'เริ่มกะ',
    shiftEnd: 'สิ้นสุดกะ',
    uploadFlag: 'สัญญาณอัปโหลด',
    uploadTimes: 'จำนวนการอัปโหลด',
    uploadMessage: 'ข้อความอัปโหลด',
    dropBoardQuantity: 'จำนวนบอร์ดที่ตก',
    totalQuantity: 'จำนวนทั้งหมด',
    dropBoardRate: 'อัตราการตกของบอร์ด',
    boardOkQuantity: 'จำนวนบอร์ด OK',
    boardNgQuantity: 'จำนวนบอร์ด NG',
    boardFailureRate: 'อัตราความล้มเหลวของบอร์ด',
    carrierOkQuantity: 'จำนวนคาร์เรียร์ OK',
    carrierNgQuantity: 'จำนวนคาร์เรียร์ NG',
    carrierFailureRate: 'อัตราความล้มเหลวของคาร์เรียร์',
    workingTime: 'เวลาทำงาน',
    shiftTime: 'เวลากะ',
    oee: 'OEE',
    tableForm: 'รูปแบบตาราง',
    curveForm: 'รูปแบบเส้นโค้ง',
    exceptionStatistics: 'สถิติข้อยกเว้น',
    communicationInfo: 'ข้อมูลการสื่อสาร',
    simulationFlag: 'ธงจำลอง',
    sampleQuantity: 'จำนวนตัวอย่าง',
    instanceCode: 'รหัสตัวอย่าง',
    instanceDescription: 'คำอธิบายตัวอย่าง',
    communicationStatus: 'สถานะการสื่อสาร',
    messageDescription: 'คำอธิบายข้อความ',
    communicationStatusScatterPlotAnalysis: 'การวิเคราะห์กราฟจุดการสื่อสาร',
    statusValue: 'ค่าสถานะ',
    communicationExceptionFrequencyStatistics:
      'สถิติความถี่ข้อยกเว้นการสื่อสาร',
    networkOutageFrequency: 'ความถี่การขาดการเชื่อมต่อเครือข่าย',
    tagID: 'รหัสแท็ก',
    tagDescription: 'คำอธิบายแท็ก',
    collectionValue: 'ค่าที่เก็บรวบรวม',
    isWarningSet: 'ตั้งค่าการเตือน',
    processWarning: 'การเตือนกระบวนการ',
    lowerLimitValue: 'ค่าขั้นต่ำ',
    upperLimitValue: 'ค่าสูงสุด',
    oldValue: 'ค่าเดิม',
    tagUniqueKey: 'คีย์ที่ไม่ซ้ำกันของแท็ก',
    dataAlarm: 'ข้อมูลการเตือน',
    dataNormal: 'ข้อมูลปกติ',
    tagAttribute: 'คุณสมบัติแท็ก',
    dataType: 'ประเภทข้อมูล',
    area: 'พื้นที่',
    areaAddress: 'ที่อยู่ของพื้นที่',
    startAddress: 'ที่อยู่เริ่มต้น',
    dataLength: 'ความยาวข้อมูล',
    bit: 'บิต',
    OPCAddress: 'ที่อยู่ OPC',
    tagDataScatterPlotAnalysis: 'การวิเคราะห์กราฟจุดข้อมูลแท็ก',
    processWarningSign: 'สัญญาณการเตือนกระบวนการ',
    normal: 'ปกติ',
    alarm: 'เตือนภัย',
    writeActivityScatterPlotAnalysis: 'การวิเคราะห์กราฟจุดของกิจกรรมการเขียน',
    writeFrequencyStatistics: 'สถิติความถี่การเขียน',
    statisticsFromWriterRatio: 'สถิติจากอัตราส่วนของผู้เขียน',
    writeNormal: 'การเขียนปกติ',
    writeException: 'การเขียนข้อยกเว้น',
    writeValue: 'ค่าการเขียน',
    writer: 'ผู้เขียน',
    writeStatus: 'สถานะการเขียน',
    writeTagQuantity: 'จำนวนแท็กที่เขียน',
    consumeTime: 'เวลาที่ใช้ (ms)',
    frequencyStatistics: 'สถิติความถี่',
    pieChartAnalysis: 'การวิเคราะห์แผนภูมิวงกลม',
    writeDetail: 'รายละเอียดการเขียน'
  },
  pagination: {
    total: 'จำนวนทั้งหมด',
    current: 'หน้าปัจจุบัน',
    previous: 'หน้าก่อนหน้า',
    next: 'หน้าถัดไป',
    unit: 'หน้า'
  },
  dialog: {
    top: 'ที่ด้านบน',
    bottom: 'ที่ด้านล่าง',
    queryException: 'ข้อผิดพลาดในการค้นหา',
    selectStation: 'กรุณาเลือกสถานี',
    exportFailed: 'การส่งออกล้มเหลว',
    selectInstance: 'โปรดเลือกตัวอย่าง',
    pleaseSelectTagGroup: 'กรุณาเลือกกลุ่มแท็ก',
    pleaseSelectTag: 'กรุณาเลือกแท็ก',
    noData: 'ไม่มีข้อมูลในขณะนี้',
    scadaCommunicationReportQueryFailed:
      'การสืบค้นรายงานการสื่อสาร Scada ล้มเหลว',
    scadaCommunicationReportQueryTimeoutOrSQLError:
      'การสืบค้นรายงานการสื่อสาร Scada หมดเวลาหรือข้อผิดพลาด SQL',
    querySelCellIPException: 'ข้อยกเว้นการสืบค้น selCellIP',
    noUnitIPAndPortNumberObtained: 'ไม่ได้รับ IP และหมายเลขพอร์ตของยูนิต',
    scadaInstanceBaseDataQueryFailed:
      'การสืบค้นข้อมูลพื้นฐานของตัวอย่าง Scada ล้มเหลว',
    scadaInstanceBaseDataQueryTimeoutOrSQLError:
      'การสืบค้นข้อมูลพื้นฐานของตัวอย่าง Scada หมดเวลาหรือข้อผิดพลาด SQL',
    scadaInstanceTagGroupDataQueryFailed:
      'การสืบค้นข้อมูลกลุ่มแท็กของตัวอย่าง Scada ล้มเหลว',
    scadaInstanceTagGroupDataQueryTimeoutOrSQLError:
      'การสืบค้นข้อมูลกลุ่มแท็กของตัวอย่าง Scada หมดเวลาหรือข้อผิดพลาด SQL',
    scadaInstanceTagDataQueryFailed:
      'การสืบค้นข้อมูลแท็กของตัวอย่าง Scada ล้มเหลว',
    scadaInstanceTagDataQueryTimeoutOrSQLError:
      'การสืบค้นข้อมูลแท็กของตัวอย่าง Scada หมดเวลาหรือข้อผิดพลาด SQL',
    scadaReadDataChangeReportQueryFailed:
      'การสืบค้นรายงานการเปลี่ยนแปลงข้อมูล Scada ล้มเหลว',
    scadaReadDataChangeReportQueryTimeoutOrSQLError:
      'การสืบค้นรายงานการเปลี่ยนแปลงข้อมูล Scada หมดเวลาหรือข้อผิดพลาด SQL',
    scadaWriteReportQueryFailed: 'การสืบค้นรายงานการเขียน Scada ล้มเหลว',
    scadaWriteReportQueryTimeoutOrSQLError:
      'การสืบค้นรายงานการเขียน Scada หมดเวลาหรือข้อผิดพลาด SQL',
    reconfirmedToClose: 'โปรดยืนยันอีกครั้งว่าจะปิด',
    hint: 'คำแนะนำ',
    operationSucceed: 'การดำเนินการสำเร็จ',
    operationFailed: 'การดำเนินการล้มเหลว',
    operationCanceled: 'การดำเนินการถูกยกเลิก',
    confirmToUnbindThisPCSRecord: 'ยืนยันการยกเลิกการผูกคู่รายการ PCS นี้',
    confirmToDisableThisSETRecord: 'ยืนยันการปิดการใช้งานรายการ SET นี้',
    confirmedToSend: 'ยืนยันการส่ง',
    formatError: 'ข้อผิดพลาดในรูปแบบ'
  }
}

// 志圣- 珠海超毅
export const zhcy = {
  // โหมดการทำงาน
  offlineMode: 'โหมดออฟไลน์',
  onlineRemote: 'ออนไลน์/ระยะไกล',
  onlineLocal: 'ออนไลน์/ท้องถิ่น',
  offline: 'ออฟไลน์',

  // โหมดการสลับ
  switchingTo: 'กำลังสลับไปยัง',

  // สถานะการผลิต
  productionAllowed: 'อนุญาตให้ผลิต',
  productionNotAllowed: 'ไม่อนุญาตให้ผลิต',
  recipeUpdateAllowed: 'อนุญาตให้อัพเดตสูตร',
  recipeUpdateNotAllowed: 'ห้ามอัพเดตสูตร',

  // ไฟแสดงสถานะ
  triColorLight: 'ไฟเตือนภัย',
  fourColorLight: 'ไฟ',
  greenLight: 'ไฟสีเขียว',
  yellowLight: 'ไฟสีเหลือง',
  redLight: 'ไฟสีแดง',
  blueLight: 'ไฟสีน้ำเงิน',

  // สถานะสัญญาณชีพจร
  plcHeartbeat: 'PLC',
  eapHeartbeat: 'EAP Heartbeat',
  eapStatus: 'EAP',
  ccdStatus: 'CCD',
  online: 'ออนไลน์',
  offline: 'ออฟไลน์',

  // เข้าสู่ระบบ/ออกจากระบบ
  login: 'เข้าสู่ระบบ',
  logout: 'ออกจากระบบ',
  employeeLogin: 'พนักงานเข้าสู่ระบบ',
  employeeId: 'รหัสพนักงาน',
  name: 'ชื่อ',
  userName: 'ชื่อ',
  permission: 'สิทธิ์',
  pleaseEnterEmployeeId: 'กรุณาป้อนรหัสพนักงาน',
  enterEmployeeId: 'กรุณาป้อนรหัสพนักงาน',
  cancel: 'ยกเลิก',
  confirm: 'ยืนยัน',
  employee: 'พนักงาน',
  engineer: 'วิศวกร',
  admin: 'ผู้ดูแลระบบ',
  confirmLogout: 'คุณแน่ใจหรือไม่ว่าต้องการออกจากระบบพนักงานปัจจุบัน?',

  // ปุ่มอื่นๆ
  syncAccountInfo: 'ซิงค์ข้อมูลบัญชี',
  scanAndLoad: 'โหลด',
  start: 'เริ่ม',
  end: 'สิ้นสุด',
  loading: 'กำลังโหลด...',
  loadSuccess: 'โหลดสำเร็จ',
  loadFail: 'โหลดไม่สำเร็จ',
  loadFailCheckContent: 'โหลดไม่สำเร็จ กรุณาตรวจสอบเนื้อหาที่สแกน',
  pleaseLoginFirst: 'กรุณาเข้าสู่ระบบก่อนดำเนินการโหลด',
  offlineModeCannotLoad: 'ไม่สามารถดำเนินการโหลดในโหมดออฟไลน์ได้',
  warning: 'คำเตือน',
  startingTask: 'กำลังเริ่มงาน...',
  endingTask: 'กำลังสิ้นสุดงาน...',
  noUserLoggedIn: 'ไม่มีผู้ใช้ที่เข้าสู่ระบบในปัจจุบัน',

  // ข้อความอื่นๆ
  pleaseSelectStation: 'ไม่ได้เลือกสถานีหรือข้อมูลสถานีไม่ครบถ้วน',
  materialNumber: 'หมายเลขวัสดุ',
  pleaseSelectStatus: 'กรุณาเลือกสถานะอย่างน้อยหนึ่งสถานะสำหรับการค้นหา',
  queryFailed: 'ไม่สามารถดึงข้อมูลรายการงานได้',
  queryException: 'เกิดข้อผิดพลาดขณะดึงข้อมูลรายการงาน',
  requestingSyncAccountInfo: 'กำลังร้องขอการซิงค์ข้อมูลบัญชี...',
  successfullySynced: 'ซิงค์สำเร็จ',
  permissionAccountInfo: 'บัญชีสิทธิ์',
  requestPermissionAccountInfoFailed: 'การร้องขอข้อมูลบัญชีสิทธิ์ล้มเหลว',
  requestPermissionAccountInfoException: 'เกิดข้อผิดพลาดในการร้องขอข้อมูลบัญชีสิทธิ์',
  noStationSelectedOrIncompleteInfo: 'ไม่ได้เลือกสถานีหรือข้อมูลสถานีไม่ครบถ้วน',
  selectAtLeastOneStatusForQuery: 'กรุณาเลือกสถานะอย่างน้อยหนึ่งสถานะสำหรับการค้นหา',
  handleReloadMachine: 'ยืนยันการโหลดเครื่องใหม่',
  taskList: 'รายการงาน',
  alarmInfo: 'ข้อมูลการแจ้งเตือน',
  taskName: 'ชื่องาน',
  status: 'สถานะ',
  productNumber: 'หมายเลขผลิตภัณฑ์',
  taskQuantity: 'จำนวนงาน',
  createTime: 'เวลาสร้าง',
  startTime: 'เวลาเริ่มต้น',
  endTime: 'เวลาสิ้นสุด',
  operations: 'การดำเนินการ',
  start: 'เริ่ม',
  end: 'สิ้นสุด',
  cancel: 'ยกเลิก',
  delete: 'ลบ',
  confirmStartTask: 'คุณแน่ใจหรือไม่ว่าต้องการเริ่มงาน',
  confirmEndTask: 'คุณแน่ใจหรือไม่ว่าต้องการสิ้นสุดงาน',
  query: 'สอบถาม',
  planning: 'กำลังวางแผน',
  inProduction: 'กำลังผลิต',
  completed: 'เสร็จสิ้น',
  cancelled: 'ยกเลิกแล้ว',
  station: 'สถานี',
  instanceCode: 'รหัสอินสแตนซ์',
  instanceDesc: 'รายละเอียดอินสแตนซ์',
  alarmCode: 'รหัสแจ้งเตือน',
  alarmLevel: 'ระดับการแจ้งเตือน',
  alarmDesc: 'รายละเอียดการแจ้งเตือน',
  alarmTime: 'เวลาแจ้งเตือน',
  resetTime: 'เวลารีเซ็ต',
  isReset: 'รีเซ็ตแล้วหรือไม่',
  isSimulated: 'จำลองหรือไม่',
  resetYes: 'รีเซ็ตแล้ว',
  resetNo: 'รอการรีเซ็ต',
  yes: 'ใช่',
  no: 'ไม่',
  capacity: 'กำลังการผลิต',
  dailyProduction: 'การผลิตรายวัน',
  readRate: 'อัตราการอ่าน',
  oee: 'OEE',
  projectName: 'ชื่อโครงการ',
  currentValue: 'ค่าปัจจุบัน',
  unit: 'หน่วย',
  upperLimit: 'ขีดจำกัดบน',
  lowerLimit: 'ขีดจำกัดล่าง',
  statusText: 'สถานะ',
  modifyRecipe: 'แก้ไขสูตร',
  isModifyParameters: 'แก้ไขพารามิเตอร์',
  parameterCode: 'รหัสพารามิเตอร์',
  parameterDesc: 'คำอธิบายพารามิเตอร์',
  parameterValue: 'ค่าพารามิเตอร์',
  validFlag: 'สถานะการใช้งาน',
  valid: 'ใช้งานได้',
  invalid: 'ใช้งานไม่ได้',
  parameterCodeLabel: 'รหัสพารามิเตอร์',
  parameterDescLabel: 'คำอธิบายพารามิเตอร์',
  parameterValueLabel: 'ค่าพารามิเตอร์',
  validFlagLabel: 'สถานะการใช้งาน',
  confirmAndIssue: 'ยืนยันและเผยแพร่',
  cimMessage: 'ข้อความ CIM',
  code: 'รหัส',
  message: 'ข้อความ',
  reloadMachineRequired: 'ต้องโหลดเครื่องใหม่',
  reloadMachineToContinue: 'กรุณาโหลดเครื่องใหม่เพื่อดำเนินการต่อ',
  offDuty: 'เลิกงาน',
  onDuty: 'เข้างาน',
  enterMaterialCode: 'ป้อนรหัสวัสดุ',
  enterMaterialDesc: 'ป้อนคำอธิบายวัสดุ',
  productionMode: 'โหมดการผลิต',
  firstPieceMode: 'โหมดชิ้นงานแรก',
  dummyMode: 'โหมดจำลอง',
  recipeMaintenance: 'การบำรุงรักษาสูตร',
  materialNumber: 'หมายเลขวัสดุ',
  pagination: {
    total: 'ทั้งหมด',
    current: 'หน้าปัจจุบัน',
    unit: '',
    previous: 'ก่อนหน้า',
    next: 'หน้าถัดไป'
  },
  prompt: 'คำแนะนำ'
}

export const lang_pack = {
  locale: 'ภาษาจีน',
  SystemName: 'ระบบจัดการเอซิส',
  SystemNameOne: 'เป็นการบ้านของคน',
  UserName: 'เลขบัญชี',
  Password: 'รหัสผ่าน',
  Role: 'บทบาท',
  Code: 'รหัสยืนยัน',
  SignOn: 'เข้าสู่ ระบบ',
  Prompt: 'เคล็ดลับ',
  codeError: 'ยังไม่มีแคปช่า หรือหมดอายุแล้ว!',
  LoggingIn: 'ในเทปบันทึก...',
  /** ************************************************************/
  /* Core定义*/
  commonPage: {
    /* 公共 */ add: 'เพิ่ม',
    edit: 'บรรณาธิการ',
    remove: 'การกำจัด',
    search: 'ค้นหา',
    reset: 'รีเซ็ต',
    operate: 'วิ่ง',
    cancel: 'การยกเลิก',
    close: 'กวน',
    confirm: 'ยืนยัน', // 确认,
    checkAll: 'เลือกทั้งหมด',
    back: 'กลับ',
    upload: 'อัพโหลด',
    download: 'ดาวน์โหลด',
    validIdentificationt: 'บัตรประจำตัวที่ถูกต้อง', // 有效标识
    validIdentification: 'บัตรประจำตัวที่ถูกต้อง:', // 有效标识：
    detail: 'รายละเอียด',
    required: 'ภาคบังคับ',
    submitSuccesful: 'ส่งเรียบร้อยแล้ว',
    addSuccesful: 'ความสำเร็จใหม่',
    editSuccessful: 'แก้ไขสำเร็จ',
    deleteSuccesful: 'ลบสำเร็จ',
    operationSuccessful: 'ปฏิบัติการสำเร็จ!', // 操作成功
    operationfailure: 'ปฏิบัติการล้มเหลว!', // 操作失败
    pingSuccessful: 'Ping测试成功',
    pingfailure: 'Ping ประธานาธิบดี'
  },
  aintenanceMenu: {
    /* 菜单维护 */ menuEncodingdescription: 'การเข้ารหัสเมนู/คำอธิบาย：',
    menuGroupCoding: 'การเข้ารหัสกลุ่มเมนู',
    menuGroupDescription: 'รายละเอียดกลุ่มเมนู',
    order: 'ลำดับ',
    menuType: 'ชนิดของเมนู',
    icon: 'Icon',
    submenuCoding: 'การเข้ารหัสเมนูย่อย',
    submenuCodingDescription: 'รายละเอียดเมนูย่อย',
    addSubmenu: 'เพิ่มเมนูย่อย',
    procedure: 'โปรแกรม'
  },
  maintenancePeople: {
    /* 人员维护 */ nameEmployeeID: 'ชื่อ/ชื่อ：',
    emailPhone: 'มล/โทรศัพท์：',
    EmployeeID: 'เลข ที่',
    name: 'ชื่อ',
    username: 'ชื่อผู้ใช้',
    password: 'รหัสผ่าน',
    email: 'อีเมล',
    phone: 'โทรศัพท์',
    phonenumber: 'โทรศัพท์',
    department: 'ดิวิชั่น',
    position: 'ตำแหน่ง',
    role: 'บทบาท',
    photo: 'หัว'
  },
  maintenanceRole: {
    /* 角色维护 */
    roleNumberDescription: 'หม/คำอ', // 角色编号/描述：
    roleNumber: 'หมายเลข',
    roleDescription: 'คำอธิ',
    productionLine: 'เส้นกา',
    process: 'ขนาด',
    locationCoding: 'ขนาด',
    locationDescription: 'คำอธิบาย',
    roleEncoding: 'ขนาดตัวอักษร',
    permission: '权限',
    menu: 'เมนู',
    station: 'ตำงงาน'
  },
  maintenanceErrorMsg: {
    /* 国际化配置 */ errorType: 'ประเภท',
    errorModule: 'โมดูล',
    errorFunctionCode: 'การเข้ารหัสฟังก์ชั่น',
    errorChinese: 'ภาษาจีน',
    errorEnglish: 'ภาษาไทย',
    errorOther: 'ภาษาอื่น ๆ',
    errorKeyword: 'คำสำคัญ'
  },
  applicationMenu: {
    /* 程序菜单 */ programCodingDescription: ' คำอธิบาย：',
    programCode: 'การเข้ารหัส',
    programDescription: 'คำอธิบาย',
    programType: 'ประเภท',
    programPath: 'เส้นทาง',
    programDependence: 'พึ่งพา',
    programAttribute: 'คุณสมบัติ'
  },
  fastCode: {
    /* 快速编码 */ groupCodeDescription: 'รายละเอียด：',
    groupCode: 'ขนาดกลุ่ม',
    groupDescription: 'คำอธิบายกลุ่ม',
    isEnabled: 'ใช้งานได้ หรือไม่',
    childCode: 'การเข้ารหัสย่อย',
    childDescription: 'รายละเอียดย่อย',
    order: 'ลำดับ'
  },
  systemParameter: {
    /* 系统参数 */ parametricDescription: 'คำอธิบาย：',
    cell: 'หน่วย：',
    parameterCode: 'การเข้ารหัส',
    parameterDescription: 'คำอธิบาย',
    parameterValue: 'ค่า',
    parameterNumber: 'หมายเลข',
    parameterdescription: 'คำอธิบาย'
  },
  errorMessage: {
    /* 错误消息定义 */ errorMessageIDDescription: 'ข้อความผิดพลาด/ID：',
    errorMessageDescription: 'ข้อความผิดพลาด',
    errorMessageID: 'ข้อความผิดพลาดID'
  },
  taskScheduling: {
    /* 任务调度 */ taskName: 'ชื่องาน：',
    cellId: 'หมายเลขหน่วย',
    BeanName: 'ชื่อ Bean',
    cronExpression: 'นิพจน์เลิกเรียน',
    mannerExecution: 'การประมวลผล',
    failureSuspend: 'ล้มเหลว',
    parameter: 'อาร์กิวเมนต์',
    successSuspendRemove: 'หยุด/ลบสำเร็จ',
    remark: 'บันทึก',
    taskStatus: 'สถานะงาน',
    status: 'สถานะ',
    pauseAfterFailure: 'หยุดหลังจากล้มเหลว',
    pauseAfterSuccessRemove: 'หยุดชั่วคราว/ลบหลังจากเสร็จสิ้น'
  },
  maintenancePline: {
    /* 产线维护 */ lineCodeDescription: 'การเข้ารหัส/คำอธิบาย：',
    lineType: 'ประเภทเส้น：',
    productionScheduleType: 'เรียงลำดับ：',
    lineCode: 'รหัสผ่าน',
    lineDescription: 'รายละเอียด',
    factory: 'โรงงาน',
    workCenter: 'ศูนย์ทำงาน',
    activation: 'อัตราการเคลื่อนไหว',
    lockNumber: 'จำนวนการล็อค',
    sort: 'เรียงลำดับ'
  },
  maintenanceStation: {
    /* 工位维护 */ stationCodeDescription: 'รหัสงาน/คำอธิบาย：',
    productionLine: 'เส้นการผลิต：',
    productionLinet: 'เส้นการผลิต',
    stationCode: 'รหัสงาน',
    stationDescription: 'คำอธิบาย',
    shortNumber: 'หมายเลขสั้น',
    productionLineSegmentCode: 'ระดับเขต',
    divisionNumber: 'โฟร์แมน',
    stationRelatedAttributes: 'คุณสมบัติ',
    stationCombinationCode: 'รูปแบบรหัส',
    ipAddress: 'ไอพีแอดเดรส',
    reportWorkingProcedureNumber: 'ขนาด',
    workDescription: 'คำอธิบาย',
    downTime: 'เวลา ที่ผิดปกติ',
    serviceTime: 'เวลาบำรุงรักษา',
    runTime: '&ประมวลผล',
    taktImage: 'จังหวะ ที่เหมาะสม',
    scrollingMessage: 'เลื่อนจดหมาย',
    methodLis: 'โหมดการเก็บ',
    productType: 'ประเภทสินค้า',
    cellId: 'หมายเลขหน่วย',
    scadaExample: 'สกาด้าอินสแตนซ์',
    exampleFlowChart: 'การจดจำ',
    automatedLogicalInstance: 'อินสแตนซ์อัตโนมัติ',
    interfaceConfigurationEnvironment: 'ปรับ แต่งส่วนติดต่อ',
    sort: 'เรียงลำดับ',
    OnlineStation: 'สถานะงานออนไลน์',
    offStation: 'ทำงานแบบออฟไลน์',
    repairStation: 'กลับไปทำงาน',
    stationDisplayedIndependently: 'แสดงสถานะงาน',
    CombinatorialCode: 'การเข้ารหัสภาษา',
    optionPeriod: 'เลือกสิทธิ์ ที่อนุญาต',
    orderSelectionAuthority: 'สิทธิ์ในการเลือกคำสั่ง',
    segmentedEncoding: 'การเข้ารหัสระดับเขต',
    onlineStationCode: 'ไป ที่เรือ',
    reportWorkingProcedureDescription: 'ขนาดงานพิมพ์',
    attribute1: 'คุณสมบัติ1',
    attribute2: 'คุณสมบัติ2',
    attribute3: 'คุณสมบัติ3',
    attribute4: 'คุณสมบัติ4',
    attribute5: 'คุณสมบัติ5'
  },
  maintenanceDrive: {
    /* 驱动维护 */ driverNameArea: 'ชื่อผู้ขับขี่/พื้น ที่：',
    driverName: 'ชื่อผู้ขับขี่',
    area: 'พื้น ที่'
  },
  maintenanceType: {
    /* 型号维护 */ driverNameType: 'ชื่อไดรเวอร์/รุ่น：',
    driverName: 'ชื่อไดรเวอร์',
    type: 'รุ่น'
  },
  tagsDefined: {
    /* 标签定义 */ productionLineId: 'เส้นการผลิต',
    exampleCode: 'การเข้ารหัสอินสแตนซ์',
    exampleDescription: 'คำอธิบาย',
    exampleGroupCode: 'การเข้ารหัสกลุ่ม',
    exampleGroupDes: 'คำอธิบายกลุ่ม',
    driveProgram: 'ไดรเวอร์',
    type: 'รุ่น',
    exampleAttribute: 'คุณสมบัติ',
    stationCode: 'จำนวนคนงาน',
    simpleDb: 'จัดเก็บข้อมูล',
    simulation: 'จำลอง'
  },
  monitor: {
    /* 实例监控 */ emptyMonitoring: 'ล้างการตรวจสอบ',
    stopMonitoring: 'หยุดการตรวจสอบ',
    startupMonitoring: 'เริ่มการตรวจสอบ',
    addTag: 'เพิ่มการตรวจสอบ',
    startupKafkaMonitoring: 'เริ่มการตรวจสอบ Kafka', // 启动Kafka监控
    exampleCode: 'ตัวอย่างรหัส',
    exampleDescription: 'ตัวอย่างคำอธิบาย',
    driveProgram: 'โปรแกรมไดรฟ์',
    simulation: 'การจำลอง',
    timeDuration: 'ระยะเ(points)',
    status: 'สถาน',
    heartbeat: 'การเต้',
    addWatch: 'เพิ่มก',
    labelCode: 'รหัสฉ',
    labelDescription: 'คำอธิบา',
    currentValue: 'ค่าปัจจุบัน',
    labelGroups: 'กลุ่มแท็ก',
    time: 'เวลา',
    feature: 'ลักษณะพิเศษ',
    message: 'ข้อความ',
    labelValue: 'ค่าฉลาก',
    labelAttr: 'คุณสมบัติป้ายกำกับ', // 标签属性
    groupCode: 'การเข้ารหัสกลุ่ม',
    groupDescription: 'คำอธิบายกลุ่ม',
    regionName: 'ชื่อพื้นที่',
    areaNumber: 'หมายเลขพื้นที่',
    regionPosition: 'ตำแหน่งภูมิภาค',
    dataType: 'ประเภทข้อมูล',
    initialAddress: 'ที่อยู่เริ่มต้น',
    dataLength: 'ความยาวของข้อมูล', // 数据长度
    length: 'ความยาว',
    bit: 'บิต',
    opcRealAddress: 'ที่อยู่จริงของ OPC',
    opcVirtualAddress: 'ที่อยู่จำลอง OPC',
    readWriteAccessPermission: 'การอ่านและการเขียนการเข้าถึง', // 读写权限
    dataPermissions: 'สิทธิ์ข้อมูล',
    changePush: 'การผลักดันการเปลี่ยนแปลง', // 变化推送
    snapshotStorage: 'การจัดเก็บสแนปช็อต', // 快照存储
    otherAttributes: 'ทรัพย์สินอื่นๆ',
    convertFormat: 'แปลงรูปแบบ',
    saveOnChange: 'บันทึกเมื่อมีการเปลี่ยนแปลง', // 变化时保存
    warningProcessing: 'การจัดการคำเตือน',
    warningUpperLimit: 'เพดานคำเตือน',
    lowerWarningLimit: 'ขีด จำกัด ล่างของการเตือนภัย', // 预警下限
    disDataTypes: 'แยกแยะประเภทข้อมูล', // 区分数据类型
    openOPCUA: 'เปิด OPCUA',
    push: 'ดัน',
    dataConversion: 'การแปลงข้อมูล',
    messageDriven: 'ไดรฟ์ข้อความ',
    emptyMessage: 'ข้อความว่าง',
    refresh: 'รีเฟรช',
    write: 'การเขียน',
    attr: 'คุณสมบัติ',
    selectProduction: 'เลือกการผลิต',
    selectCell: 'เลือกเซลล์',
    codeOrDes: 'การเข้ารหัสอินสแตนซ์หรือการอธิบาย', // 实例编码或描述
    addMon: 'เพิ่มแท็กการตรวจสอบ', // 添加监控标签
    queryInstance: 'สอบถามอินสแตนซ์ก่อนแล้วจึงเริ่มการตรวจสอบ', // 请先查询实例，再启动监控
    serviceCell: 'กรุณาเก็บรักษาข้อมูลบริการและหน่วยงานก่อน', // 请先维护服务、单元信息
    selectTAG: 'ข้อความ TAG',
    refreshException: 'รีเฟรชข้อยกเว้น'
  },
  logicattr: {
    /* 逻辑属性 */ menuType: 'ประเภทของเมนู',
    groupCode: 'รหัสภาษากลุ่ม',
    groupDescription: 'คำอธิบายของกลุ่ม',
    childCode: 'การเข้ารหัส',
    childDescription: 'รายละเอียด',
    attribute1: 'คุณสมบัติ ที่ 1',
    attribute2: 'คุณสมบัติ ที่ 2',
    attribute3: 'คุณสมบัติ ที่ 3',
    attribute4: 'คุณสมบัติ ที่ 4',
    attribute5: 'คุณสมบัติ ที่ 5',
    logicalPropertyMaintenance: 'การดูแลรักษาคุณสมบัติตรรกะ'
  },
  logicfunc: {
    /* 自动化逻辑属性 */
    automatedLogicalPropertyConfiguration:
      'การปรับ แต่งคุณสมบัติทางตรรกะอัตโนมัติ',
    logicalProgram: 'โปรแกรมตรรกะโปรแกรม',
    schedulerCode: 'รหัสการจัดตาราง',
    schedulerDescription: 'รายละเอียดของตาราง',
    setControllers: 'รวมศูนย์ควบคุม',
    subproject: 'โครงการย่อย',
    value: 'ค่า',
    attribute1: 'คุณสมบัติ ที่ 1',
    attribute2: 'คุณสมบัติ ที่ 2',
    attribute3: 'คุณสมบัติ ที่ 3',
    attribute4: 'คุณสมบัติ ที่ 4',
    attribute5: 'คุณสมบัติ ที่ 5',
    isEnabled: 'มันได้ผล หรือไม่',
    attributeGroup: 'คุณสมบัติ',
    groupCode: 'รหัสภาษากลุ่ม',
    groupDescription: 'คำอธิบายของกลุ่ม',
    subprojectDescription: 'รายละเอียดของรายการ'
  },
  modmain: {
    /* 主流程图模板维护 */ templateCode: 'แม่แบบ：',
    templateDescription: 'แม่แบบ：',
    templateCodet: 'แม่แบบ',
    templateDescriptiont: 'รายละเอียดของแม่แบบ',
    programDriven: 'ผู้ขับขี่'
  },
  modfunctionm: {
    /* 函数维护 */ functionCode: 'การรักษาฟังก์ชัน：',
    functionCodet: 'การรักษาฟังก์ชัน',
    funcname: 'ชื่อฟังก์ชัน：',
    funcnamet: 'ชื่อฟังก์ชัน',
    functionDescription: 'รายละเอียดของฟังก์ชัน',
    csprojCode: 'รหัสของโครงการ',
    csprojCodet: 'รหัสของโครงการ',
    functionversion: 'ฟังก์ชัน',
    functionversiondes: 'รุ่นของคำอธิบาย'
  },
  modcsproj: {
    /* 工程维护 */ csprojCode: 'รหัสของโครงการ',
    csprojCodet: 'รหัสของโครงการ',
    csprojdes: 'รายการคำอธิบาย：',
    csprojdest: 'รายการคำอธิบาย',
    csprojtargetframework: 'กรอบเป้าหมาย',
    csprojreference: 'โครงการอ้างถึง',
    csprojversion: 'รุ่นของรายการ',
    csprojversiondes: 'รุ่นของคำอธิบาย',
    csprojpathWin: 'ที่จะใช้วินเทอร์',
    csprojpathLinux: 'พาธของแฟ้ม Linux'
  },
  mainmain: {
    /* 主流程图维护 */ mainProcessCode: 'รหัสของกระบวนการหลัก：',
    mainProcessDescription: 'กระบวนการหลัก：',
    basicAttribute: 'คุณสมบัติพื้นฐาน',
    processTemplate: 'แม่แบบกระบวนการ',
    station: 'ตำแหน่งงาน',
    mainProcessCodet: 'รหัสของกระบวนการหลัก',
    mainProcessDescriptiont: 'กระบวนการหลัก',
    taskNumberPrefix: 'นำหน้าหมายเลขหน้างาน',
    programProperties: 'โปรแกรมคุณสมบัติ',
    processFlow: 'การไหลของกระบวนการ',
    examplesCollections: 'คลังภาพ',
    triggerPoint: 'จุดเริ่มต้น',
    triggerPointValue: 'ค่าจุดกระตุ้น',
    taskTriggeringMode: 'วิธีการทำงาน',
    pollingTime: 'เวลารอคอย',
    conditionsSet: 'เงื่อนไข',
    conditionGroupDescription: 'รายละเอียดของกลุ่มเงื่อนไข',
    conditionsDescribed: 'คำอธิบายเงื่อนไข',
    monitoringPoint: 'จุดตรวจสอบ',
    setUpInstructions: 'คำแนะนำ',
    processCodet: 'การเข้ารหัส',
    processDescriptiont: 'กระบวนการ',
    taskInfo: 'ข้อมูลงาน',
    view: 'ตรวจสอบ',
    dataException: 'ริ่มการทำงานข้อมูล ที่ผิดปกติ',
    selectProduction: 'โปรดเลือกเส้นผลิตกับตำแหน่งงาน',
    taskNumber: 'หมายเลขภารกิจ',
    creationDate: 'สร้างเวลา',
    detailInfo: 'ข้อมูลรายละเอียด',
    pendEvents: 'จัดการกับเหตุการณ์',
    strDrawFlow: 'การสร้างแผนภูมิอย่างสิ้นหวัง',
    attrMomitor: 'ตรวจสอบคุณสมบัติ',
    abnormalData: 'ข้อมูลรูปแบบการเริ่มต้น ที่ผิดปกติ',
    amplify: 'ขยาย',
    reduce: 'ย่อตัวลง',
    sureDelete: 'ตรวจสอบให้แน่ใจว่า คุณต้องการลบรายการ ที่เลือกไว้ในปัจจุบัน',
    step: 'ขั้นตอน??',
    cancelledDeletion: 'ยกเลิกการลบ',

    log: 'บันทึก',
    attr: 'คุณสมบัติพื้นฐาน',
    inputParams: 'ป้อนพารามิเตอร์',
    outParams: 'พารามิเตอร์ออก',
    subprocess: 'กระบวนการย่อย',
    stepDes: 'ขั้นตอน ที่อธิบาย',
    sort: 'เรียงลำดับ',
    stepType: 'ขั้นตอน',
    startStep: 'เริ่มขั้นตอน',
    endStep: 'ขั้นตอนสุดท้าย',
    generalSteps: 'ขั้นตอนปกติ',
    methodName: 'ชื่อวิธีการ',
    nextOK: 'ไม่เป็นไรขั้นต่อไป',
    stepToNg: 'ng เป็นขั้นตอน',
    afterLoop: 'หลังจากวงจรสิ้นสุดลง',
    selectNext1: 'เลือก 1 ขั้นตอนต่อไป',
    selectNext2: 'เลือก 2 ขั้นตอนต่อไป',
    selectNext3: 'เลือก 3 ขั้นตอนต่อไป',
    selectNext4: 'เลือก 4 ขั้นตอนต่อไป',
    selectNext5: 'เลือก 5 ขั้นตอนต่อไป',
    cancelNext: 'ยกเลิกขั้นตอนต่อไป',
    retryCount: 'จำนวนการลองใหม่',
    limiTime: 'เวลา จำกัด(ms)',
    colorStateOK: 'สีเมื่อสถานะโอเค',
    colorStateNG: 'สีเมื่อสถานะ ng',
    colorStateCancel: 'สีเมื่อทำการยกเลิกสถานะ',
    colorStateRetry: 'สีเมื่อทำการทดสอบสถานะใหม่',
    initColor: 'สีเริ่มต้น',
    code: 'รหัส',
    conditionGroup: 'กลุ่มเงื่อนไข:',
    notHave: 'ไม่มีอะไร',
    endAll: 'จบเรื่องทั้งหมด',
    logQuery: 'ล็อกเกอร์',
    logType: 'ชนิดของปูมบันทึก',
    logCode: 'รหัสล็อก',
    date: 'วัน ที่',
    logInfo: 'ข้อความล็อกข้อความ',

    stepJump: 'ก้าวกระโดด',
    radio: 'ตัวเลือกเดียว',
    index: 'อินเตอร์เซ็ค',
    stepName: 'ชื่อขั้นตอน',
    selectFlow: 'โปรดเลือกกระบวนการก่อน',
    jumpSuccessful: 'โดดสำเร็จ',

    subDes: 'คำอธิบายของโพรเซสย่อย',
    processType: 'ประเภทของโพรเซส',
    startSub: 'เริ่มกระบวนการย่อย',
    endSub: 'จบกระบวนการย่อย',
    regularSub: 'กระบวนการย่อยปกติ',
    nextSub: 'กระบวนการย่อยถัดไป',
    subEnd: 'จบกระบวนการย่อย',
    afterEnd: 'จบกระบวนการทั้งหมดหลังจากสิ้นสุดกระบวนการย่อย',
    subFunctionDll: 'ขั้นตอนย่อย',
    subAttr: 'คุณสมบัติของโปรแกรมย่อย',
    controlIcon: 'ไอคอนควบคุม',
    controlWidth: 'ความกว้างของการควบคุม',
    controlHeight: 'ความสูงของการควบคุม',
    colorStateWait: 'สีเมื่อรอสถานะ',
    cannotBeEmpty: 'ไม่สามารถว่า งได้',
    enterNumber: 'กรุณาใส่ค่าตัวเลข',
    enterSub: 'โปรดป้อนการเข้ารหัสของกระบวนการย่อย',
    enterDes: 'โปรดป้อนคำอธิบายของกระบวนการย่อย',
    nextSelect: 'โปรดเลือกโพรโทคอลย่อยตัวถัดไป',
    enterFunction: 'โปรดป้อนกระบวนการย่อย',
    addSub: 'สร้างกระบวนการย่อย ที่สำเร็จ',
    addError: 'เพิ่มข้อผิดพลาดของกระบวนการย่อย',
    editSuccess: 'แก้ไขโพรเซสย่อยเรียบร้อยแล้ว',
    editError: 'กระบวนการย่อยจะถูกแก้ไขอย่างผิดปกติ'
  },
  taskList: {
    /* 任务列表*/ TaskSource: 'แหล่ง ที่มาของภารกิจ:',
    TaskNumber: 'หมายเลขงาน',
    NumberOfTasks: 'จำนวนงาน',
    UrgentOrder: 'ด่วน',
    ScheduleDate: 'วัน ที่วางแผน',
    TargetCuttingMachine: 'ตัดเป้าหมาย',
    CutType: 'ชนิดการตัด',
    TaskCreationTime: 'เวลา ที่จะสร้างทาสก์',
    CrownBlockAutomatic: 'ยานอวกาศอัตโนมัติ',
    CrownBlockStatus: 'สถานะยานพาหนะ',
    CuttingStatus: 'สถานะการตัด',
    WhetherToCutOrNot: 'ตัด หรือไม่',
    SortOrNot: 'จัดเรียง หรือไม่',
    ValidOrNot: 'ใช้งานได้ หรือไม่',
    SteelPlateModel: 'แบบแผ่นเหล็ก',
    DXFcode: 'การเข้ารหัสแบบ DXF',
    DXFFiles: 'แฟ้ม DXF:',
    NCcode: 'การเข้ารหัสของเอ็นซี',
    NCFiles: 'แฟ้มเอ็นซี',
    TaskStatus: 'สถานะงาน',
    validIdentification: 'แสดงตัว ที่ใช้ได้:'
  },
  cuttingZone: {
    // 切割区
    CacheBit: 'ที่อยู่แคช',
    TaskNumber: 'หมายเลขงาน',
    SteelPlateModel: 'แบบแผ่นเหล็ก',
    SteelPlateSource: 'ต้นแบบแผ่นเหล็ก',
    CacheStatus: 'สถานะแคช',
    DelayTime: 'จนกว่า จะถึงเวลา',
    ManualOperation: 'การประมวลผล',
    CuttingPosition: 'ระดับการตัด',
    CuttingMachineStatus: 'สถานะเครื่องตัด',
    cutNCName: 'ชื่อแฟ้ม ที่ใช้ตัด',
    PROGRAMNO: 'หมายเลขโปรแกรม',
    schedule: 'ความคืบหน้า',
    DeviceName: 'ชื่ออุปกรณ์',
    AlarmClassification: 'การจัดเรียง',
    AlarmLevel: 'ระดับการแจ้งเตือน',
    AlarmMessage: 'ข้อความแจ้งเตือน',
    AlarmOccurrenceTime: 'เวลา ที่จะเรียกตำรวจ',
    startTime: 'เวลาเริ่ม',
    endTime: 'เวลาสิ้นสุด',
    PlannedCuttingTime: 'เวลาการตัด',
    ProgramUploadTime: 'เวลา ที่อัปโหลดโปรแกรม',
    CurrentWorkstation: 'ตำแหน่งงานปัจจุบัน',
    TargetWorkstation: 'ตำแหน่งเป้าหมาย',
    StockCode: 'เลข ที่ประจำตำแหน่ง',
    StockCount: 'รูปแบบปริยาย',
    MaterialModel: 'รุ่นวัสดุ',
    ExecutionOrder: 'ลำดับการประมวลผล',
    AdjustTheOrder: 'ปรับลำดับ',
    WarehousingTaskNumber: 'หมายเลขบัญชีผู้ใช้',
    WarehousingTime: 'เวลาการเข้ารหัส',
    LocationSorting: 'เรียงลำดับตำแหน่ง ที่ตั้งไว้',
    ZAxisCoordinates: 'พิกัดแกน Z',
    IsItLocked: 'ล็อค หรือไม่',
    LockTaskNumber: 'ล็อคเป้าหมาย'
  },
  sortingArea: {
    // 分拣区
    SortingStation: 'แยกคนงาน',
    SortType: 'ชนิดการจัดเรียง',
    PlannedQuantity: 'จำนวนของแผนงาน',
    ActualQuantity: 'จำนวนจริง',
    SheetName: 'ชื่อภาพวาด',
    MaterialFrameNumber: 'จำนวนกล่อง',
    MaterialFrameParts: 'ชิ้นส่วนกล่อง',
    Number: 'จำนวน',
    Status: 'สถานะ',
    MaterialFrameDetails: 'รายละเอียดกล่องวัสดุ',
    AGVStatus: 'สถานะเอวี',
    StationInspectionStatus: 'ตรวจสอบสถานี',
    WorkReportingStatus: 'รายงานสถานะ',
    AbnormalWorkReporting: 'โทรหาผิดปกติ',
    TransitDetails: 'รายละเอียดของสถานี',
    ProductionTaskNumber: 'หมายเลขงานการผลิต',
    CurrentStationNumber: 'เลขสถานะปัจจุบัน',
    SourceStaus: 'ไม่มี ที่อยู่',
    Cutterbar: 'คัตเตอร์',
    arriveDate: 'ถึงกี่โมงอ่ะ',
    leaveDate: 'ออกจากเวลา',
    StationConsumptionTime: 'ระยะเวลาการทำงาน',
    PalletNumber: 'ถาด',
    ReportWorkOrNot: 'โทรกลับ หรือไม่',
    MaterialFrameTaskNumber: 'จำนวนกล่องของงาน',
    PartLength: 'ความยาวของส่วน',
    PartWidth: 'กว้างอะไหล่',
    PartType: 'ชนิดของชิ้นส่วน',
    PartThickness: 'ส่วน ที่หนา',
    PartWeight: 'น้ำหนักอะไหล่',
    PlannedSortingTime: 'เวลาการเลือก',
    SortingStationNumber: 'แยกประเภทคนงาน',
    PartNumber: 'หมายเลขอะไหล่',
    PartBarcodeNumber: 'หมายเลขชิ้นส่วน',
    PartDrawingNumber: 'หมายเลขอะไหล่',
    partMaterialNumber: 'ส่วนจำนวน วัสดุ',
    PlannedTimeSpent: 'แผนใช้เวลา/s',
    CurrentSortingStation: 'ตำแหน่งการจัดเรียงปัจจุบัน',
    TargetTransmissionStation: 'สถานีขนส่งเป้าหมาย',
    CompulsoryRelease: 'บังคับให้ปล่อย',
    MandatoryReportingInterface: 'บังคับให้มีส่วนเชื่อมต่อ ที่เรียกให้',
    IsAutomaticSortingCompleted: 'จะแยกประเภทอัตโนมัติเสร็จสิ้น หรือไม่',
    CurrentSortingDXFDrawing: 'ค้นหาภาพวาดของ DXF ปัจจุบัน',
    CurrentSortingSteelPlateModel: 'เลือกชนิดของแผ่นเหล็กในปัจจุบัน',
    ForceComplete: 'บังคับให้สมบูรณ์',
    WorkReportingInterfaceAddress: 'ที่อยู่อินเทอร์เฟส',
    DXFFileName: 'ชื่อแฟ้ม',
    GNCFileName: 'ชื่อแฟ้มของ GNC',
    PersonInChargeOfWorkReporting: 'ผู้รับผิดชอบงาน',
    ClickToReportWork: 'คลิก ที่ตัวพิมพ์'
  },
  equipment: {
    stationCode: 'จำนวนคนงาน',
    deviceCode: 'หมายเลขอุปกรณ์',
    DeviceName: 'ชื่ออุปกรณ์',
    PartNumber: 'หมายเลขอะไหล่',
    PartName: 'ชื่ออะไหล่',
    TheoreticalLifespan: 'อายุการใช้งานทฤษฎี',
    ServiceLife: 'การใช้งาน',
    Status: 'สถานะ',
    PartDescription: 'คำอธิบายส่วนต่าง ๆ',
    OriginalPartNumber: 'หมายเลขอะไหล่ดั้งเดิม',
    OriginalPartName: 'ชื่ออะไหล่ดั้งเดิม',
    PartNumberAfterReplacement: 'หมายเลขอะไหล่สำรอง',
    PartNameAfterReplacement: 'เปลี่ยนชื่ออะไหล่',
    ReplacementTime: 'เปลี่ยนเวลา'
  },
  mfTable: {
    // 机型基础
    creator: 'ผู้สร้าง',
    creationTime: 'เวลา ที่สร้าง',
    ModifiedBy: 'แก้ไข',
    time: 'เวลา',
    link: 'เชื่อมโยง',
    mainMaterialCode: 'การเข้ารหัสวัสดุ',
    mainMaterialDescription: 'คำอธิบายวัสดุ',
    partDrawingNumber: 'หมายเลขอะไหล่',
    partMaterialNumber: 'ส่วนจำนวน วัสดุ',
    model_type: 'รุ่น',
    length: 'ยาว',
    width: 'กว้าง:',
    thick: 'หนา',
    height: 'สูง',
    weight: 'น้ำหนัก',
    materialQuality: 'วัสดุ',
    cuttingMachine: 'วัสดุ',
    dataSources: 'แหล่งข้อมูล',
    cuttingXCoordinate: 'ตัดพิกัด x',
    cuttingyCoordinate: 'ตัดพิกัด y',
    enableFlag: 'แสดงตัว ที่ใช้ได้:'
  },
  smaterialbox: {
    // 分拣箱基础
    creator: 'ผู้สร้าง',
    creationTime: 'เวลา ที่สร้าง',
    modifiedBy: 'แก้ไข',
    time: 'เวลา',
    materialFrameCoding: 'การเข้ารหัสกล่องการป้อน',
    materialFrameName: 'ชื่อกล่องป้อน',
    materialFrameType: 'ชนิดของกล่องวัสดุ',
    AGVLocationCode: 'รหัสตำแหน่ง V'
  },
  qualityData: {
    // 质量数据采集
    creator: 'ผู้สร้าง',
    creationTime: 'เวลา ที่สร้าง',
    modifiedBy: 'แก้ไข',
    time: 'เวลา',
    stationId: 'เลขประจำตัว',
    sourceOfQualityData: 'แหล่งข้อมูลคุณภาพ',
    collectionItems: 'เก็บรายการ',
    markOfConformity: 'ป้ายกำกับ ที่มีคุณสมบัติ',
    groupNumber: 'หมายเลขกลุ่ม',
    groupDescription: 'คำอธิบายกลุ่ม',
    positionOfColumn: 'ตำแหน่งคอลัมน์',
    columnSorting: 'เรียงลำดับ',
    measurementObject: 'วัดวัตถุ',
    entryName: 'ชื่อรายการ',
    collectionProjectUnit: 'เพิ่มหน่วยของรายการ',
    standardValue: 'ค่าเบี่ยงเบนมาตรฐาน',
    lowerLimitingValue: 'จำกัด ค่าต่ำสุด',
    upperLimitValue: 'ค่าสิ้นสุด',
    saveOrNot: 'จะทำการบันทึก'
  },
  taskTable: {
    taskNumber: 'หมายเลขงาน',
    taskSource: 'แหล่ง ที่มาของภารกิจ',
    serialNumber: 'หมายเลขซีเรียล',
    batchNumber: 'จำนวน',
    productionMode: 'การเรียงลำดับระยะทาง',
    taskType: 'ชนิดของงาน',
    materialCode: 'การเข้ารหัสวัสดุ',
    materialDescription: 'คำอธิบายวัสดุ',
    partDrawingNumber: 'หมายเลขอะไหล่',
    partMaterialNumber: 'ส่วนจำนวน วัสดุ',
    modelY: 'รุ่น',
    length: 'ยาว',
    width: 'กว้าง:',
    thick: 'หนา',
    height: 'สูง',
    weight: 'น้ำหนัก',
    materialQuality: 'วัสดุ',
    cuttingMachine: 'วัสดุ(切割机)',
    xCoordinate: 'พิกัด x',
    yCoordinate: 'พิกัด y',
    DXFFileName: 'ชื่อแฟ้ม',
    DXFFileURLPath: 'พาธของ DXF',
    JSONFileName: 'ชื่อแฟ้มเจสัน',
    JSONFileURLPath: 'พาธของ JSON',
    JSONFileURLTxt: 'ข้อความของเจสัน',
    JSONData: 'ข้อมูลของเจสัน',
    NCFileName: 'ชื่อแฟ้ม NC',
    NCFileURLPath: 'พาธของ NC',
    scheduledTime: 'เวลาของการตัด',
    sort: 'เรียงลำดับงาน',
    taskStatus: 'สถานะงาน',
    taskMessage: 'จดหมายภารกิจ',
    cutterbar: 'คัตเตอร์',
    cutterbarType: 'ประเภทเครื่องตัด',
    cuttingPlanTime: 'เวลาของการตัด',
    shotBlastingOrNot: 'แป้งหนานุ่มนะคะ',
    automaticCrownBlockOrNot: 'สถานะยานพาหนะ',
    whetherToAutotheboard: 'จะให้ไปรับอัตโนมัติ หรือไม่',
    whetherToSprayCode: 'ไม่ใช้รหัส',
    whetherToAutomaticallyCut: 'ตัด หรือไม่',
    automaticSortingOrNot: 'จัดเรียง หรือไม่',
    isTheCarCentered: 'รถเลื่อนถูกต้อง',
    onSiteSiteMaterials: 'สนาม/วัสดุภายนอก',
    stationPosition: 'ตำแหน่งงาน',
    reportWorkOrNot: 'จะทำให้เสร็จ หรือไม่',
    area: 'ครั้งหน้า', // 面次
    NoBowPlate: 'รุ่นแรก' // 是否首板
  },
  surplusMaterialTable: {
    // 生产任务余料表
    description: 'คำอธิบาย',
    mainTaskID: 'หมายเลขงานหลัก',
    surplusMaterialTaskNumber: 'ยูจิน',
    residualMaterialSerialNumber: 'ตัวเลขของซีเรียล',
    surplusMaterialBatchNumber: 'ตัวเลขผู้ให้บริการ'
  },
  eventRecordForm: {
    // 事件记录表
    mainTaskID: 'หมายเลขงานหลัก',
    user: 'ผู้ใช้',
    eventType: 'ประเภทเหตุการณ์',
    eventName: 'ชื่อเหตุการณ์',
    sendContent: 'ส่งเนื้อหา',
    acceptContent: 'ยอมรับเนื้อหา',
    messageCODE: 'รหัสจดหมาย',
    messageContent: 'เนื้อหาจดหมาย',
    eventStatus: 'สถานะของเหตุการณ์',
    startTime: 'เวลาเริ่ม',
    endTime: 'เวลาสิ้นสุด',
    timeConsuming: 'ใช้เวลา(s)',
    remake: 'บันทึก',
    condition: 'เงื่อนไข',
    timeoutLimit: 'ข้อ จำกัด',
    lastUpdateTime: 'เวลาปรับปรุงล่าสุด'
  },
  sortingResults: {
    // 分拣解析结果
    mainTaskID: 'หมายเลขงานหลัก',
    stationCode: 'จำนวนคนงาน',
    partBarcodeNumber: 'บาร์โค้ดส่วนต่าง ๆ',
    robotCoding: 'รหัสหุ่นยนต์',
    partCode: 'การเข้ารหัสส่วน',
    NCFileName: 'ชื่อแฟ้ม NC',
    partDrawingNumber: 'หมายเลขอะไหล่',
    partMaterialNumber: 'ส่วนจำนวน วัสดุ',
    sortSequenceNumber: 'หมายเลขประจำตัว',
    nedCuttingTime: 'เวลาตัด',
    routingCode: 'การเข้ารหัสของเส้นทางงาน',
    sprayCodeXCoordinate: 'พิกัดจุดยอด X',
    sprayCodeYCoordinate: 'พิกัดจุดยอดจุดยอด',
    PartType: 'ชนิดของชิ้นส่วน',
    sortResultCode: 'รหัสผลการค้นหา',
    exceptionInformation: 'จดหมาย/ข้อความ',
    length: 'ยาว',
    width: 'กว้าง:',
    thick: 'หนา',
    weight: 'น้ำหนัก',
    nextRouting: 'การประมวลผล',
    reservedPartProperties: 'จุดขายอะไหล่',
    timeRequiredForSorting: 'ต้องใช้เวลาในการแยกประเภท(ms)',
    startTime: 'เวลาเริ่ม',
    endTime: 'เวลาสิ้นสุด',
    sortStartTime: 'เวลาเริ่มการเลือก',
    sortEndTime: 'เวลาสิ้นสุดการเลือก'
  },
  sortingBoxResults: {
    // 料框结果
    materialFramePosition: 'ตำแหน่งของช่องป้อน',
    materialFrameBarcode: 'กล่องวัสดุ',
    taskNumber: 'หมายเลขงาน',
    partBarcodeNumber: 'บาร์โค้ดส่วนต่าง ๆ',
    partCode: 'การเข้ารหัสส่วน',
    partType: 'ชนิดของชิ้นส่วน',
    resultCode: 'ผลลัพธ์',
    stackingMessage: 'ตั้งค่าโครงการ/ผิดปกติ',
    stackingTime: 'เวลากระดาษ',
    stackingStartTime: 'เวลาเริ่มกระดาษ',
    stackingEndTime: 'เวลาสิ้นสุดของกระดาษ'
  },
  realTimeTable: {
    // 工位MIS实时状态表
    username: 'ชื่อผู้ใช้',
    arriveDate: 'ถึงกี่โมงอ่ะ',
    leaveDate: 'ออกจากเวลา',
    stationCode: 'จำนวนคนงาน',
    stationDescription: 'คำอธิบายหลัก',
    stationProperties: 'คุณสมบัติงาน',
    stationStatus: 'สถานะการทำงาน',
    taskNumber: 'หมายเลขงาน',
    taskSource: 'แหล่ง ที่มาของภารกิจ',
    serialNumber: 'หมายเลขซีเรียล',
    batchNumber: 'จำนวน',
    palletNumber: 'ถาด',
    palletUsageTimes: 'จำนวนครั้ง ที่ใช้',
    maximumPalletUsage: 'จำนวนครั้ง ที่ใช้สูงสุด',
    trayAlarmIdentification: 'การแสดงตัวของถาด',
    mainMaterialCode: 'การเข้ารหัสวัสดุ',
    mainMaterialDescription: 'คำอธิบายวัสดุ',
    partDrawingNumber: 'หมายเลขอะไหล่',
    partMaterialNumber: 'ส่วนจำนวน วัสดุ',
    model_type: 'รุ่น',
    length: 'ยาว',
    width: 'กว้าง:',
    thick: 'หนา',
    weight: 'น้ำหนัก',
    materialQuality: 'วัสดุ',
    cuttingMachine: 'วัสดุ',
    uptime: 'เวลาออนไลน์',
    workpieceInProcessTime: 'ชิ้นงานในเวลา ที่ทำ(s)',
    idealBeat: 'จังหวะ ที่เหมาะสม(s)',
    actualBeat: 'จังหวะจริง(s)',
    shifCode: 'รหัสแบบแบน',
    shifDes: 'รายละเอียดแบบแบน',
    sourceStationNumber: 'ตัวเลขต้นทาง',
    stationErrorMessage: 'ข้อมูลการทำงานผิดพลาด',
    DXFName: 'ชื่อ DXF',
    JSONName: 'ชื่อเจสัน',
    NCName: 'ชื่อเอ็นซี'
  },
  wmsCbTable: {
    // WMS天车基础表
    libraryArea: 'พื้น ที่คลังสื่อ',
    crownBlockCode: 'การเข้ารหัสของยานพาหนะ',
    processName: 'ชื่อของโพรเซส',
    subProcess: 'กระบวนการย่อย',
    crownBlockDescription: 'รายละเอียดของรถบรรทุกท้องฟ้า',
    crownBlockNumber: 'หมายเลขของสกายเน็ท',
    crownBlockAttributes: 'คุณสมบัติของยานพาหนะ',
    crownBlockType: 'ชนิดของยานพาหนะ',
    groupCollection: 'มารวมกัน ที่หน่วยพิทักษ์สิทธิ',
    originXCoordinate: 'จุดกำเนิด X',
    originYCoordinate: 'จุดกำเนิด',
    originZCoordinate: 'จุดกำเนิด',
    farPointXCoordinate: 'ไปไกล ๆ พิกัด X',
    farPointYCoordinate: 'ไปไกล ๆ พิกัด Y',
    farPointZCoordinate: 'ไปไกล ๆ พิกัด Z',
    safeDistance: 'ระยะทาง ที่ปลอดภัย',
    warehouseLocationGroupCode: 'การเข้ารหัสชุดรหัสอักขระของชุดรหัสอักขระ',
    locationGroupDescription: 'รายละเอียดของกลุ่มผู้ใช้',
    warehouseLocationNumber: 'เลข ที่ประจำตำแหน่ง',
    warehouseLocationDescription: 'คำอธิบายของหมวดหมู่',
    inventoryLocationSorting: 'เรียงลำดับตำแหน่ง ที่ตั้งไว้',
    manageInventoryOrNot: 'จัดการคลังสินค้า',
    isTheModelFixed: 'ให้เป็นรุ่นคง ที่ หรือไม่',
    modelID: 'หมายเลขรุ่น',
    xCoordinate: 'พิกัด X',
    YCoordinate: 'พิกัด Y',
    ZCoordinate: 'พิกัด Z',
    isTheZAxisDefinedValue: 'ค่าแกน Z ที่กำหนด',
    zAxisDynamicCalculationMethod: 'วิธีการคำนวณแกน Z แบบไดนามิก',
    inventory: 'รูปแบบปริยาย',
    minimumInventory: 'คลังสินค้าขนาดเล็ก ที่สุด',
    maximumInventory: 'คลังสินค้าสูงสุด',
    loopCode: 'รหัสวน',
    maximumLimitExpirationTime: 'เวลา ที่ จำกัด สูงสุด(m)',
    warehouseLocationStatus: 'สถานะของตำแหน่ง',
    locationType: 'ประเภทของ ที่พัก',
    taskNumber: 'หมายเลขงาน',
    taskSource: 'แหล่ง ที่มาของภารกิจ',
    serialNumber: 'หมายเลขซีเรียล',
    batchNumber: 'จำนวน',
    ZAxisCoordinates: 'พิกัดแกน Z',
    isInventoryLocked: 'ล็อคสต็อก หรือไม่',
    warehousingMethod: 'โหมดการเข้ารหัส',
    taskType: 'ชนิดของงาน',
    findTheYCoordinate: 'ค้นหาพิกัด Y',
    isTheSearchCompleted: 'เสร็จสิ้นการค้นหา',
    warehousingTime: 'จำนวนวัน ที่',
    isInventoryAlarm: 'โทรแจ้งตำรวจเรื่องสินค้าคงคลัง',
    mainTaskID: 'หมายเลขงาน',
    model_type: 'รุ่น',
    locationID: 'เลขประจำตัว',
    taskExecutionStatus: 'สถานะการทำงาน',
    taskExecutionErrorMessage: 'Comment = รายการจดหมาย Name',
    startingStorageLocation: 'ตำแหน่งเริ่มต้น',
    targetInventoryLocation: 'ตำแหน่งเป้าหมาย',
    taskStartTime: 'เวลาเริ่มภารกิจ',
    taskEndTime: 'เวลาสิ้นสุด',
    timeConsuming: 'ใช้เวลา(s)',
    isTheModelChecked: 'ตรวจสอบรุ่น',
    checkTheRollerTableTime: 'ตรวจสอบเวลาลูกกลิ้ง',
    checkModelTime: 'ตรวจสอบเวลาของรุ่น',
    checkModelResults: 'ตรวจสอบหมายเลขรุ่น',
    crownBlockTaskType: 'ชนิดของภารกิจยานพาหนะ',
    stepCode: 'รหัสขั้นตอน',
    stepName: 'ชื่อขั้นตอน',
    stepStatus: 'สถานะ',
    restrictionStepID: 'จำกัด หมายเลขขั้นตอน',
    restrictionStepName: 'จำกัด ชื่อขั้นตอน',
    restrictionStepStatus: 'จำกัด สถานะของขั้นตอน',
    isTheRollerTableChecked: 'ตรวจสอบช่องลูกกลิ้ง'
  },
  meStationQuality: {
    stationCode: 'จำนวนคนงาน',
    taskNumber: 'หมายเลขงาน',
    serialNumber: 'หมายเลขซีเรียล',
    groupNumber: 'หมายเลขกลุ่ม',
    groupDescription: 'คำอธิบายกลุ่ม',
    positionOfColumn: 'ตำแหน่งคอลัมน์',
    collectionItems: 'เก็บรายการ',
    columnSorting: 'เรียงลำดับ',
    measurementObject: 'วัดวัตถุ',
    entryName: 'ชื่อรายการ ที่จะเก็บ',
    collectionProjectUnit: 'เพิ่มหน่วยของรายการ',
    standardValue: 'ค่าเบี่ยงเบนมาตรฐาน',
    lowerLimitingValue: 'จำกัด ค่าต่ำสุด',
    upperLimitValue: 'ค่าสิ้นสุด',
    collectValues: 'ป้ายสัญลักษณ์ย่อย',
    retrospectiveTime: 'ย้อนเวลา'
  },
  center: {
    /* 中心维护 */ centreNumberDescription: 'หมายเลขศูนย์/คำอธิบาย：',
    centreCode: 'การเข้ารหัสภาษาของศูนย์',
    centreDescription: 'คำอธิบายของศูนย์',
    mainCard: 'บัตรออนไลน์หลัก',
    attachCard1: 'แนบการ์ดอินเทอร์เน็ต 1',
    attachCard2: 'แนบการ์ดตาข่าย 2',
    attachCard3: 'แนบการ์ดตาข่าย 3'
  },
  server: {
    /* 服务维护 */ centre: 'ศูนย์กลาง',
    serviceTagDescription: 'หมายเลขบริการ/คำอธิบาย：',
    serviceCode: 'การเข้ารหัสบริการ',
    serviceDescription: 'คำอธิบายบริการ',
    mainCard: 'บัตรออนไลน์หลัก',
    attachCard1: 'แนบการ์ดอินเทอร์เน็ต 1',
    attachCard2: 'แนบการ์ดตาข่าย 2',
    attachCard3: 'แนบการ์ดตาข่าย 3',
    esbServer: 'เซิร์ฟเวอร์ ESB',
    system: 'ระบบ'
  },
  cell: {
    /* 单元维护 */ cellNameDescription: 'ชื่อ/คำอธิบายของหน่วย:',
    serve: 'บริการ',
    cellName: 'ชื่อของหน่วย',
    cellDescription: 'คำอธิบายของหน่วย',
    mirrorName: 'กระจกเงา',
    webPort: 'เว็บพอร์ต',
    mqttPort: ' พอร์ตmqtt ',
    opcUaPort: 'พอร์ต OPCUA',
    cellPort: 'พอร์ตยูนิต Comment',
    startOpcUa: 'เริ่มการทำงาน'
  },
  interf: {
    /* 接口维护 */
    interfaceCodeDescription: 'การเข้ารหัสส่วนเชื่อมต่อ/คำอธิบาย：',
    interfaceCode: 'รหัสส่วนเชื่อมต่อ',
    interfaceDescription: 'คำอธิบายส่วนติดต่อ',
    sourceSystem: 'ระบบ ที่มาจาก',
    targetSystem: 'ระบบเป้าหมาย',
    interfaceMeans: 'โหมดส่วนติดต่อ',
    testAddress: 'ที่อยู่ทดสอบ',
    officialAddress: 'ที่อยู่ทางการ',
    testReturnValue: 'ทดสอบค่า ที่ส่งกลับ',
    interfaceNumber: 'หมายเลขอินเทอร์เฟส',
    productionAddress: 'ที่อยู่การผลิต',
    remark: 'บันทึก',
    cell: 'หน่วย',
    parameterValue: 'ส่งต่อ'
  },
  file: {
    /* 文件管理 */ autoDesk: 'ไดเรกทอรีโฟลเดอร์',
    fileName: 'ชื่อแฟ้ม',
    fileSize: 'ขนาดแฟ้ม'
  },
  celldeploy: {
    /* 单元部署 */ serve: 'บริการ:',
    uploadMirroring: 'อัปโหลดภาพสะท้อน',
    installAll: 'ติดตั้งทั้งหมด',
    installSelected: 'เลือกการติดตั้ง',
    unitName: 'ชื่อหน่วย',
    unitDescription: 'คำอธิบายหน่วย',
    cellMirrorName: 'ชื่อกระจกเงาของหน่วย',
    installMirrorName: 'ชื่อกระจกเงาเมื่อทำการติดตั้ง',
    serviceMirroringInstall: 'ติดตั้งบริการกระจกเงา'
  },
  cellprogupd: {
    /* 单元程序更新 */ serve: 'บริการ:',
    massUpdate: 'ปรับปรุงเป็นชุด',
    selectiveupdate: 'เลือกการปรับปรุง',
    updateObject: 'ปรับปรุงวัตถุ',
    updateMode: 'โหมดปรับปรุง',
    file: 'แฟ้ม',
    serialNumber: 'เพิ่มเลเยอร์',
    cell: 'หน่วย',
    procedure: 'โปรแกรม',
    fileName: 'ชื่อแฟ้ม',
    fileType: 'ประเภทแฟ้ม',
    fileSize: 'ขนาดแฟ้ม'
  },
  progbatchupd: {
    /* 单元程序更新批量 */ serve: 'บริการ:',
    massUpdate: 'ปรับปรุงเป็นชุด',
    selectiveupdate: 'เลือกการปรับปรุง',
    updateObject: 'ปรับปรุงวัตถุ',
    updateMode: 'โหมดปรับปรุง',
    file: 'แฟ้ม',
    serialNumber: 'เพิ่มเลเยอร์',
    cell: 'หน่วย',
    procedure: 'โปรแกรม',
    fileName: 'ชื่อแฟ้ม',
    fileType: 'ประเภทแฟ้ม',
    fileSize: 'ขนาดแฟ้ม'
  },
  /** ************************************************************/
  /* MES定义*/

  /** ************************************************************/
  /* PMC定义*/
  pmcquery: {
    /* 查询条件 */ creationDate: '、ช่วงเวลา: ',
    workCenterCode: 'ห้องทำงาน: ',
    prodLineCode: 'เส้น ที่กำหนด: ',
    stationCode: 'ตำแหน่งงาน: ',
    makeOrder: 'คำสั่ง: ',
    vin: 'วิน: ',
    vinFlag: 'การแสดงตัวสุดท้ายของวิน: ',
    workStatus: 'สถานะการผลิต: ',
    setSign: 'ดึงเข้า /สถานะ:',
    zkName: 'ประเภทการสั่งซื้อ: ',
    lastMakeOrder: 'หมายเลขคำสั่งสุดท้าย: ',
    stationDeviceType: 'ประเภทอุปกรณ์: ',
    deviceSectionCode: 'ตำแหน่งงานตรงกับการกระชับปืน: ',
    qualityFrom: 'แหล่งข้อมูล: ',
    screenSetType: 'ประเภทหน้าจอขนาดใหญ่: ',
    andonType: 'ประเภทของแสง: ',
    andonCardStatus: 'สถานะการทำงาน: ',
    andonControlName: 'ชื่อของการควบคุมหลัก: ',
    happenDate: 'เวลา ที่จะทำ: ',
    resetFlag: 'ตั้งค่าใหม่ หรือไม่: ',
    andonVoiceSerial: 'หมายเลขของอุปกรณ์: ',
    andonVoiceDes: 'ชื่อเครื่องเสียง: ',
    andonMusicDes: 'ชื่อเพลง: ',
    enableFlag: 'แสดงตัว ที่ใช้ได้:'
  },
  flowonline: {
    /* 线首 */ creationDate: 'เวลา ที่สร้าง',
    flowOnlineId: 'หมายเลข',
    workCenterCode: 'ห้องทำงาน',
    prodLineCode: 'เส้นการผลิต',
    stationCode: 'ตำแหน่งงาน',
    makeOrder: 'คำสั่ง',
    serialNum: 'หมายเลขชิ้นงาน',
    dms: 'ข้อความ',
    itemProject: 'รายการบรรทัด',
    vin: 'วิน',
    smallModelType: 'รุ่น',
    mainMaterialCode: 'หมายเลขวัสดุ',
    materialColor: 'สี',
    materialSize: 'ขนาด',
    shaftProcNum: 'แก้ไขหมายเลขของโปรแกรม',
    staffId: 'ผู้ประกอบการ',
    palletNum: 'ถาด',
    engineNum: 'เครื่องยนต์',
    driverWay: 'รูปแบบไดรเวอร์',
    publishNumber: 'เปิดตัวเลขการเปิดตัว',
    vinFlag: 'แสดงตัวสุดท้ายของวิน',
    repairFlag: 'การแสดงตัวกลับ',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  stationmo: {
    /* 工位生产订单 */ creationDate: 'เวลา ที่สร้าง',
    stationMoId: 'หมายเลข',
    prodLineCode: 'เส้นการผลิต',
    stationCode: 'ตำแหน่งงาน',
    makeOrder: 'คำสั่ง',
    targetMakeOrder: 'คำสั่งเป้าหมาย',
    dms: 'ข้อความ',
    itemProject: 'รายการบรรทัด',
    moWorkOrder: 'ลำดับการผลิต',
    workStatus: 'สถานะการผลิต',
    setSign: 'ดึงเข้า/สถานะ',
    setDate: 'เวลา',
    setMarks: 'บันทึก',
    preSetOutBut: 'นัดหมายดึงออก',
    cancelPreSetOutBut: 'ยกเลิกการจอง และดึงออก',
    setOutBut: 'ดึงออกมา',
    preSetInBut: 'นัดหมาย และดึง',
    cancelPreSetInBut: 'ยกเลิกการจอง และดึง',
    setInBut: 'ดึงเข้ามา'
  },
  modownstatus: {
    /* 下发订单状态 */ creationDate: 'เวลา ที่สร้าง',
    lastUpdateDate: 'แก้ไขเวลา',
    zkName: 'ประเภทสั่งอาหาร',
    lastMakeOrder: 'หมายเลขสุดท้าย'
  },
  stationflow: {
    /* 过站 */ creationDate: 'เวลา ที่สร้าง',
    stationFlowId: 'หมายเลข',
    workCenterCode: 'ห้องทำงาน',
    prodLineCode: 'เส้นการผลิต',
    stationCode: 'ตำแหน่งงาน',
    stationDes: 'คำอธิบายหลัก',
    proceduceCode: 'ขนาดงานพิมพ์',
    proceduceDes: 'ขนาดงานพิมพ์',
    serialType: 'ชนิดบาร์โค้ด',
    serialNum: 'หมายเลขชิ้นงาน',
    palletNum: 'ถาด',
    staffId: 'ผู้ประกอบการ',
    makeOrder: 'หมายเลขคำสั่ง',
    dms: 'ข้อความ',
    itemProject: 'รายการบรรทัด',
    vin: 'วิน',
    smallModelType: 'รุ่น',
    mainMaterialCode: 'หมายเลขวัสดุ',
    materialColor: 'สี',
    materialSize: 'ขนาด',
    shaftProcNum: 'แก้ไขหมายเลขของโปรแกรม',
    repair_flag: 'การแสดงตัวกลับ',
    qualitySign: 'เครื่องหมายการแสดงผลรวม',
    dataCollectWay: 'โหมดการเก็บข้อมูล',
    arriveDate: 'ถึงกี่โมงอ่ะ',
    leaveDate: 'ออกจากเวลา',
    costTime: 'ใช้เวลา',
    shifCode: 'รหัสแบบแบน',
    shifDes: 'รายละเอียดแบบแบน',
    setSign: 'ดึงเข้า/สถานะ',
    checkStatus: 'ตรวจสอบสถานะ',
    checkCode: 'ตรวจสอบรหัสของสถานี',
    checkMsg: 'ตรวจสอบรายละเอียดของสถานี',
    flowStautsCode: 'ผ่านสถานะ',
    assemblyFinishFlag: 'จะให้ผลการตรวจสอบเสร็จสมบูรณ์ หรือไม่',
    mesAviFlag: 'จะทำการอัปโหลด',
    upFlag: 'อัปโหลดการแสดงตัว',
    upNgCode: 'อัปโหลดรหัสข้อผิดพลาด',
    upNgMsg: 'อัปโหลดข้อความผิดพลาด',
    vinAviFlag: 'จะทำการอัปโหลดรูปแบบการพิมพ์ของวิน?',
    downVinFlag: 'แสดงตัว(VIN)',
    downVinNgCode: 'ส่งรหัสผิดพลาด(VIN)',
    downVinNgMsg: 'ส่งข้อความผิดพลาด(VIN)',
    jzAviFlag: 'จะทำการอัปโหลดเพิ่มเติมน้ำมัน หรือไม่',
    downJzFlag: 'แสดงตัว',
    downJzNgCode: 'ส่งรหัสผิดพลาด',
    downJzNgMsg: 'ส่งข้อความผิดพลาด',
    lesAviFlag: 'กำลังอัปโหลด',
    downLesFlag: 'แสดงตัว(LES)',
    downLesNgCode: 'ส่งรหัสผิดพลาด(LES)',
    downLesNgMsg: 'ส่งข้อความผิดพลาด(LES)',
    downAgvFlag: 'แสดงตัว(AGV)',
    downAgvNgCode: 'ส่งรหัสผิดพลาด(AGV)',
    downAgvNgMsg: 'ส่งข้อความผิดพลาด(AGV)',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  meStationFlow: {
    stationCode: 'จำนวนคนงาน',
    stationDes: 'คำอธิบายหลัก',
    taskNumber: 'หมายเลขงาน',
    taskSource: 'แหล่ง ที่มาของภารกิจ',
    serialNumber: 'หมายเลขซีเรียล',
    batchNumber: 'จำนวน',
    palletNum: 'ถาด',
    mainMaterialCode: 'การเข้ารหัสวัสดุ',
    mainMaterialDescription: 'คำอธิบายวัสดุ',
    partDrawingNumber: 'หมายเลขอะไหล่',
    partMaterialNumber: 'ส่วนจำนวน วัสดุ',
    model_type: 'รุ่น',
    length: 'ยาว',
    width: 'กว้าง:',
    thick: 'หนา',
    weight: 'น้ำหนัก',
    materialQuality: 'วัสดุ',
    cuttingMachine: 'วัสดุ',
    releaseMethod: 'โหมดการปล่อย',
    releaseUser: 'คน ที่ไม่สนใจ',
    releaseInstructions: 'คำอธิบาย',
    releaseTime: 'เวลาการเดินทาง',
    arriveDate: 'ถึงกี่โมงอ่ะ',
    leaveDate: 'ออกจากเวลา',
    costTime: 'ใช้เวลา(s)',
    shifCode: 'รหัสแบบแบน',
    shifDes: 'รายละเอียดแบบแบน',
    onlineWorkstationIdentification: 'แสดงสถานะการทำงานออนไลน์',
    offlineWorkstationIdentification: 'แสดงสถานะการทำงานแบบออฟไลน์',
    tagQualitySignId: 'ป้ายกำกับ ที่มีคุณสมบัติ',
    reportWorkOrNot: 'จะทำให้เสร็จ หรือไม่',
    workReportInspectionInformation: 'แสดงข้อมูลการตรวจสอบของพนักงาน',
    upFlag: 'อัปโหลดการแสดงตัว',
    upNgCode: 'อัปโหลดรหัสข้อผิดพลาด',
    upNgMsg: 'อัปโหลดข้อความผิดพลาด'
  },
  stationstatus: {
    /* 当前工位实时工件信息 */ creationDate: 'เวลา ที่สร้าง',
    stationStatusId: 'หมายเลข',
    workCenterCode: 'ห้องทำงาน',
    prodLineCode: 'เส้นการผลิต',
    stationCode: 'ตำแหน่งงาน',
    stationDes: 'คำอธิบายหลัก',
    stationStatus: 'สถานะการทำงาน',
    emptyBanFlag: 'ให้ว่า ง หรือไม่',
    serialNum: 'หมายเลขชิ้นงาน',
    palletNum: 'ถาด',
    staffId: 'ผู้ประกอบการ',
    makeOrder: 'หมายเลขคำสั่ง',
    dms: 'ข้อความ',
    itemProject: 'รายการบรรทัด',
    vin: 'วิน',
    smallModelType: 'รุ่น',
    mainMaterialCode: 'หมายเลขวัสดุ',
    materialColor: 'สี',
    materialSize: 'ขนาด',
    shaftProcNum: 'แก้ไขหมายเลขของโปรแกรม',
    qualitySign: 'เครื่องหมายการแสดงผลรวม',
    setSign: 'ดึงเข้า/สถานะ',
    checkStatus: 'ตรวจสอบสถานะ',
    checkCode: 'ตรวจสอบรหัสของสถานี',
    checkMsg: 'ตรวจสอบรายละเอียดของสถานี',
    engineNum: 'เครื่องยนต์',
    driverWay: 'รูปแบบไดรเวอร์',
    statusWay: 'โหมดการคำนวณสถานะ',
    lineSectionCode: 'การเข้ารหัสส่วน ที่ผ่านเส้น',
    allowWay: 'แหล่งข้อมูล ที่อนุญาต'
  },
  setinout: {
    /* 拉入拉出履历 */ creationDate: 'เวลา ที่สร้าง',
    setReportId: 'หมายเลข',
    workCenterCode: 'ห้องทำงาน',
    prodLineCode: 'เส้นการผลิต',
    stationCode: 'ตำแหน่งงาน',
    stationDes: 'คำอธิบายหลัก',
    setType: 'ดึงเข้า/สถานะ',
    serialNum: 'หมายเลขชิ้นงาน',
    palletNum: 'ถาด',
    staffId: 'ผู้ประกอบการ',
    makeOrder: 'หมายเลขคำสั่ง',
    dms: 'ข้อความ',
    itemProject: 'รายการบรรทัด',
    vin: 'วิน',
    smallModelType: 'รุ่น',
    mainMaterialCode: 'หมายเลขวัสดุ',
    materialColor: 'สี',
    materialSize: 'ขนาด',
    shaftProcNum: 'แก้ไขหมายเลขของโปรแกรม',
    setDate: 'เวลา',
    setMarks: 'บันทึก',
    preSetInBut: 'นัดหมาย และดึง',
    setInBut: 'ดึงเข้ามา'
  },
  stationdevice: {
    /* 工位设备对应关系 */ creationDate: 'เวลา ที่สร้าง',
    stationDeviceId: 'หมายเลข',
    workCenterCode: 'ห้องทำงาน',
    prodLineCode: 'เส้นการผลิต',
    stationCode: 'ตำแหน่งงาน',
    stationDeviceType: 'ประเภทอุปกรณ์',
    deviceCode: 'หมายเลขอุปกรณ์',
    deviceDes: 'คำอธิบายอุปกรณ์',
    deviceSectionCode: 'ตำแหน่งงานตรงกับการขันกระบอกปืน',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  recipequality: {
    /* 工位数据采集定义 */ creationDate: 'เวลา ที่สร้าง',
    qualityId: 'หมายเลข',
    stationId: 'ตำแหน่งงาน',
    qualityFrom: 'แหล่งข้อมูล',
    tagId: 'เก็บรายการ',
    tagQualitySignId: 'ป้ายกำกับ ที่มีคุณสมบัติ',
    groupOrder: 'หมายเลขกลุ่ม',
    groupName: 'คำอธิบายกลุ่ม',
    tagColOrder: 'ตำแหน่งคอลัมน์',
    tagColInnerOrder: 'เรียงลำดับ',
    qualityFor: 'วัดวัตถุ',
    tagDes: 'ชื่อรายการ ที่จะเก็บ',
    tagUom: 'เพิ่มหน่วยของรายการ',
    theoryValue: 'ค่าเบี่ยงเบนมาตรฐาน',
    downLimit: 'จำกัด ค่าต่ำสุด',
    upperLimit: 'ค่าสิ้นสุด',
    saveOrNot: 'จะบันทึกข้อมูล หรือไม่',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  screenset: {
    /* 大屏基础 */ creationDate: 'เวลา ที่สร้าง',
    screenSetId: 'หมายเลข',
    screenSetType: 'ประเภทหน้าจอขนาดใหญ่',
    screenSetCode: 'การเข้ารหัสภาษาขนาดใหญ่',
    screenSetDes: 'คำอธิบายขนาดใหญ่',
    screenSetUrl: 'การส่งจดหมาย',
    screenSetUrlDes: 'ส่งคำอธิบายหน้า',
    orderNum: 'เพิ่มช่องสัญญาณ',
    switchTime: 'สลับระยะ',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  screensetcontent: {
    /* 大屏内容 */ creationDate: 'เวลา ที่สร้าง',
    prodLineCode: 'เส้นการผลิต',
    todayPlan: 'วันนี้',
    currentOffline: 'ออฟไลน์ปัจจุบัน',
    finishRate: 'ผลสำเร็จ',
    jphActual: 'ความรู้สึก ที่แท้จริงของ JPH',
    deviceMobility: 'อัตราการเคลื่อนไหวของอุปกรณ์',
    todayStop: 'วันนี้ หยุดบรรทัด',
    aboveShow: 'แสดงบน',
    belowShow: 'แสดงด้านล่าง',
    backgroundImage: 'ภาพพื้นหลัง',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  andonbtn: {
    /* 安灯按钮基础 */ creationDate: 'เวลา ที่สร้าง',
    andonBtnId: 'หมายเลข',
    stationId: 'ตำแหน่งงาน',
    andonType: 'ประเภทโคมไฟแอน',
    andonDes: 'คำอธิบายของโคมไฟแอน',
    andonColor: 'สีปุ่มปรับแสงแอน',
    btnLevel: 'ระดับความสำคัญ',
    action: 'การกระทำ',
    btnTagId: 'แท็บปุ่ม',
    lineStopTagId: 'เส้น ที่กำหนดเอง',
    lineStopApi: 'สายการผลิตหยุดการทำงาน',
    andonMusicId: 'เสียงดนตรี',
    andonVoiceIdList: 'เครื่องเสียง',
    andonLimitTime: 'เวลาหมดเวลาตอบสนอง',
    andonBtnStatus: 'สถานะปุ่มปัจจุบัน',
    attribute1: 'คุณสมบัติ 1',
    attribute2: 'คุณสมบัติ 2',
    attribute3: 'คุณสมบัติ 3',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  andoncard: {
    /* 安灯工位牌基础 */ creationDate: 'เวลา ที่สร้าง',
    andonCardId: 'หมายเลข',
    stationId: 'ตำแหน่งงาน',
    andonCardType: 'ชนิดของบัตรพนักงาน',
    andonCardColor: 'สี',
    cardTagId: 'ตารางงาน',
    andonCardStatus: 'สถานะบัตรพนักงาน',
    andonCardCode: 'การเข้ารหัสบัตรพนักงาน',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  andoncontrol: {
    /* 安灯工位状态控制基础 */ creationDate: 'เวลา ที่สร้าง',
    andonControlId: 'หมายเลข',
    stationId: 'ตำแหน่งงาน',
    andonControlName: 'ชื่อการควบคุมหลัก',
    andonCardValue: 'ค่าการควบคุมบัตรพนักงาน',
    andonCardTagId: 'ตารางงาน',
    andonMusicId: 'เสียงดนตรี',
    lineStopTagId: 'เส้น ที่กำหนดเอง',
    lineStopApi: 'สายการผลิตหยุดการทำงาน',
    attribute1: 'คุณสมบัติ 1',
    attribute2: 'คุณสมบัติ 2',
    attribute3: 'คุณสมบัติ 3',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  andonevent: {
    /* 安灯事件 */ creationDate: 'เวลา ที่สร้าง',
    andonEventId: 'หมายเลข',
    workCenterCode: 'ห้องทำงาน',
    prodLineCode: 'เส้นการผลิต',
    stationName: 'ชื่องาน',
    stationCode: 'ตำแหน่งงาน',
    stationDes: 'คำอธิบายหลัก',
    lineSectionCode: 'การเข้ารหัสส่วน ที่ผ่านเส้น',
    andonBtnId: 'ปุ่มปรับแสงแอน',
    andonType: 'ประเภทโคมไฟแอน',
    andonDes: 'คำอธิบายของโคมไฟแอน',
    andonLimitTime: 'เวลาหมดเวลาตอบสนอง',
    happenDate: 'เวลา ที่จะทำ',
    resetDate: 'ตั้งเวลาใหม่',
    resetFlag: 'ตั้งค่าใหม่',
    costTime: 'ใช้เวลา',
    overTimeFlag: 'หมดเวลา หรือไม่',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  andonvoice: {
    /* 安灯音响基础 */ creationDate: 'เวลา ที่สร้าง',
    andonVoiceId: 'หมายเลข',
    andonVoiceSerial: 'หมายเลขของอุปกรณ์',
    andonVoiceDes: 'ชื่อเครื่องเสียง',
    andonVoiceAttr: 'คุณสมบัติเครื่องเสียง',
    btnTagId: 'แท็บปุ่ม',
    btnLevel: 'ระดับความสำคัญ',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  andonmusic: {
    /* 安灯音响音乐 */ creationDate: 'เวลา ที่สร้าง',
    andonMusicId: 'หมายเลข',
    andonMusicDes: 'ชื่อเพลง',
    andonMusicPath: 'พาธของเพลง',
    andonMusicAttr: 'คุณสมบัติเพลง',
    enableFlag: 'แสดงตัว ที่ใช้ได้'
  },
  haCarTypeRoute: {
    /* 焊装车型工艺路线维护 */ haCarTypeDes: 'ตัวถังสีขาว:',
    productionLine: 'รหัสผ่าน:',
    productionLinet: 'การเข้ารหัสภาษาโดยใช้ลวดลาย',
    carType: 'ตัวถังสีขาว',
    onlineStation: 'สถานะงานออนไลน์',
    routeStation: 'เส้นทาง',
    attribute1: 'คุณสมบัติ 1',
    attribute2: 'คุณสมบัติ 2',
    attribute3: 'คุณสมบัติ 3'
  },

  /** ************************************************************/
  /* PCB定义*/

  /** ************************************************************/
  /* DCS定义*/

  // 越南包装项目定义
  vie: {
    // 配方维护
    partNum: 'จำนวน',
    version: 'รุ่น',
    setType: 'ประเภทการตั้งค่า',
    pcsType: 'ชนิดของตัวนำ',
    plateLen: 'หัวกระดาน(mm)',
    plateWid: 'กว้างกระดาน(mm)',
    plateThi: 'กระดานหนา(mm)',
    plateWei: 'น้ำหนักกระดาน(g)',
    setCodeLen: 'ความยาวของบาร์โค้ด',
    pcsCodeLen: 'ความยาวบาร์โค้ด',
    setCase: 'ตั้งค่าตัวพิมพ์เล็ก',
    pcsCase: 'ตัวพิมพ์เล็กตัวใหญ่',
    index: 'เพิ่มเลเยอร์',

    ordnumTruBatchRule: 'กฎการดักจับหมายเลขการสั่งซื้อ',
    cusPartNumIntRule: 'กฎการดักจับหมายเลขวัสดุ',
    setTruBatchRule: 'ตั้งค่ากฎการดักฟัง',
    setPartNumIntRule: 'กฎการตั้งค่าการดักฟัง',
    setIntCycRule: 'กฎการตั้งค่าการดักฟัง',
    pcsTruBatchRule: 'กฎการดักจับกลุ่ม',
    pcsPartNumIntRule: 'กฎการดักจับข้อมูล',
    pcsIntSeriaRule: 'กฎการดักฟังรหัสการดักฟัง',
    pcsNumStartNum: 'ตัวเลขเริ่มต้นของตัวเลข',
    pcsNumValadd: 'เพิ่มหมายเลข PCS',
    // 工单管理
    OriginLotNum: 'หมายเลขล็อตต้นฉบับ',
    taskSource: 'แหล่ง ที่มาของภารกิจ',
    taskStatus: 'สถานะงาน',
    orderNum: 'หมายเลขคำสั่ง',
    task_type: 'ชนิดของงาน',
    task_status: 'สถานะงาน',
    cusPartNum: 'หมายเลขผู้ใช้',
    planQuantity: 'จำนวนของแผนงาน',
    singlePackQua: 'จำนวนแพ็ค 1 แพ็ค',
    planTime: 'ตารางเวลา',
    orderStatus: 'สถานะการสั่งซื้อ',
    customCode1: 'รหัสอักขระของลูกค้า',
    orderType: 'ประเภทคำสั่ง',
    okFinishe: 'โอเคเสร็จแล้ว',
    ngFinishe: 'ไม่เป็นไรหรอกเสร็จแล้ว',
    cycle: 'วนรอบ',
    cutBatchNum: 'หมายเลขใบเสร็จ',
    intPartNumb: 'หมายเลขดึงข้อมูล',
    taskStarTime: 'เวลาเริ่มภารกิจ',
    taskEndTime: 'เวลาสิ้นสุด',
    taskUserTime: 'ภาระกิจใช้เวลา',
    operator: 'ผู้ประกอบการ',
    confirmIssuance: 'แน่ใจการส่ง',
    LWHTHK: 'L/W/H/THK',
    plantCode: 'การเข้ารหัสของโรงงาน',
    salesOrder: 'หมายเลขการสั่งซื้อ',
    salesItem: 'ขายคำสั่ง',
    salesOrg: 'องค์กรการขาย',
    salesType: 'ประเภทการขาย',
    customPn: 'ลูกค้าเทอร์มินัล PN',
    customPo: 'คำสั่งลูกค้าเทอร์มินัล',
    customCode: 'การเข้ารหัสภาษาเทอร์มินัล',
    customName: 'ชื่อลูกค้าเทอร์มินัล',
    splitLot: 'หมายเลขฐานข้อมูล',
    splitModel: 'หมายเลขดึงข้อมูล',
    attribute1: 'คุณสมบัติการจอง ที่ 1',
    attribute2: 'คุณสมบัติการจอง ที่ 2',
    attribute3: 'คุณสมบัติการจอง 3',

    xoutNum: 'จำนวนการไล่ออก',
    xoutType: 'ไล่ออก ประเภท',
    quaOfOkBoard: 'จำนวนของพื้น ที่วาง',
    quaOfNgBoard: 'จำนวนของบอร์ด NG',
    passRate: 'อัตราเสีย',
    numOfComBoards: 'จำนวนของกระดานสมบูรณ์',
    packComplete: 'เก็บของเสร็จแล้ว',
    orderStarTime: 'เวลาเริ่มการสั่งซื้อ',
    orderUseTime: 'ใช้เวลาในการสั่งซื้อ',

    setOrder: 'ตั้งค่า ',
    judgingTheResult: 'ตัดสินใจ ',
    setCode: 'ตั้งค่าบาร์โค้ด ',
    rotationDirection: 'หมุนภาพ ',
    setLevel: 'จัดระดับความชื่นชอบ ',
    stackingPosition: 'วางซ้อนกัน ',
    setFront: 'ตั้งค่า ',
    setOpposite: 'ตั้งค่าหาง ',
    sortingError: 'การเลือก ',
    packOrder: 'เรียงตามลำดับ ',
    packCode: 'เก็บบาร์โค้ด ',
    setNum: 'จำนวนของชุด ',
    IsItATrunk: 'กล่องท้าย ',
    packError: 'บรรจุหีบห่อผิดปกติ ',

    equAllPLCIss: 'อุปกรณ์อนุญาตให้วาง PLC ได้ ',
    triColorLight: 'ไฟ 3 สี ',
    lineScan: 'กวาดสาย ',
    printer: 'เครื่องพิมพ์ ',
    employeeID: 'ชื่อพนักงาน ',
    name: 'ชื่อ ',
    department: 'ดิวิชั่น ',
    classes: 'กะ. ',
    Login: 'ล็อก อิน', // 登入
    LogOut: 'ออ ก', // 登出
    viewDetails: 'แสดงรายละเอียด ',
    NGmulCod: 'ขนาด ',
    OKPosition: 'พื้น ที่ใช้ได้ ',
    NGPosition: 'บิตของ NG ',
    rotate: 'หมุน ',
    NORotate: 'ไม่หมุน ',
    Yes: 'ใช่. ',
    NO: 'ไม่ ',
    cancel: 'การยกเลิก',
    close: 'กวน',
    employeeLogin: 'ล็อกอินพนักงาน ',
    messageAlert: 'แจ้งเตือนจดหมาย ',
    workOrder: 'การจัดการเพียงด้านเดียว ',
    run: 'กำลังทำงาน ',
    alarm: 'แจ้งเตือน Name ',
    stop: 'หยุด ',
    preserve: 'การดูแล ',
    mecFai: 'ตัวพาด ',
    repair: 'สถานะซ่อมบำรุง ',
    queryException: 'ค้นหาข้อผิดพลาด ',
    loginSuccess: 'ล็อกอินเรียบร้อยแล้ว ',
    operationException: 'ปฏิบัติการผิดปกติ ',
    AreYouSureToLogOut: 'คุณแน่ใจ หรือว่า จะออกจากระบบพนักงานปัจจุบัน? ',
    AreYouSureDelete: 'แน่ใจที่จะลบข้อมูลรายการนี้?',
    prompt: 'คำแนะนำ ',
    determine: 'แน่ใจ ',
    qualifiedQuantity: 'จำนวน ที่ผ่านการตรวจสอบ ',
    unqualifiedQuantity: 'จำนวน ที่ไม่ผ่านการตรวจสอบ ',
    quantity: 'จำนวน ',
    cnneSuccess: 'เชื่อมต่อสำเร็จแล้ว ',
    cnneFailed: 'การเชื่อมต่อล้มเหลว ',
    connDisconRecon:
      'การเชื่อมต่อถูกตัดการเชื่อมต่อกำลังทำการเชื่อมต่อใหม่... ',
    PLCCommunInter: 'การสื่อสารล้มเหลว ',
    lineScanCommunInter: 'หยุดการสื่อสารของสาย ',
    printerCommunInter: 'การสื่อสารของเครื่องพิมพ์ถูกตัด ',
    MESCommunInter: 'การสื่อสารล้มเหลว ',
    pleaseStartMonitor: 'กรุณาเริ่มการเฝ้าระวัง ',
    noIDFound: 'ไม่มีการตรวจสอบหมายเลข ',
    save: 'บันทึก',
    TotalNumberOfPackage: 'จำนวนแพ็คเกจทั้งหมด',
    TotalNumberOfPieces: 'จำนวนดาว',
    MaximumNumberOfFullPackages: 'จำนวนแพ็กเกจเต็มสูงสุด',
    TrayMaterialCode: 'Tray การเข้ารหัสวัสดุ ',
    TrayDiskCapacity: 'ปริมาณดิสก์ Tray',
    SortingComparisonRuleSetting: ' การตั้งค่ากฎการเปรียบเทียบการเรียงลำดับ',
    // 包装set表
    pileBarcode: 'บรรจุบาร์โค้ด',
    arrayIndex: 'ตั้งค่าคำสั่ง',
    boardSn: 'แถบกำกับเส้น',
    arrayLevel: 'เกรดกวาดลวด',
    arrayFrontLevel: 'ระดับการกวาดสาย',
    arrayBackLevel: 'เกรดชุดด้านหน้า',
    setFrontReadingCodeTime: 'เวลาอ่านโค้ดด้านหน้า',
    setReverseCodeReadingTime: 'เวลาอ่านรหัสย้อนกลับ',
    arrayMark: 'ตรวจหาจุดเชื่อมต่อแบบไร้สาย',
    arrayBdCount: 'จำนวนตัวอักษรภายใต้การกวาดสาย',
    boardResult: 'ตัดสินผลของชิ้นส่วนบอร์ด',
    depositPosition: 'วางซ้อนกัน',
    xoutFlag: 'ให้เลือกสำหรับการแยกออก',
    xoutSetNum: 'จำนวนการตั้งค่า',
    xoutActNum: 'ไล่ระดับสี',
    arrayNgCode: 'ตั้งค่าการเลือก NG',
    arrayNgMsg: 'ตั้งค่าการเลือก',
    arrayFrontInfo: 'ตั้งค่าการปัดกวาดข้อมูล',
    arrayBackInfo: 'ตั้งค่าการปัดกวาดข้อมูล',
    upFlag: 'อัปโหลดการแสดงตัว',
    upNgCode: 'อัปโหลดรหัสข้อผิดพลาด',
    pileUseFlag: 'จะให้บรรจุ หรือไม่',
    unbindFlag: 'จะแก้มัด หรือไม่',
    unbindUser: 'แก้มัดผู้คน',
    unbindTime: 'แก้ไขเวลา',
    unbindWay: 'คำอธิบายวิธีการแก้',
    pileIndex: 'สั่งซื้อด้วยหมายเลขประจำตัว',
    arrayCount: 'รวมชุดรวม',
    pileWeight: 'เก็บน้ำหนักของคุณ',
    pileUser: 'คนเก็บของ',
    pcArraylist: 'ชุดชุดแผนงานพีซี',
    plcArraylist: 'ชุดคำติชมของ plateComment',
    pileStatus: 'เก็บของ',
    pileFrontLevel: 'เกรดหน้ากวาดลวด',
    pileBackLevel: 'เกรดลวดกวาดกลับ',
    pileFrontReadingCodeTime: 'เวลาอ่านโค้ดด้านหน้า',
    pileReverseCodeReadingTime: 'เวลาอ่านรหัสย้อนกลับ',
    pilengCode: 'บรรจุ และตรวจสอบรหัส NG',
    pile_ng_msg: 'บรรจุเพื่อยืนยันคำอธิบาย NG',
    dailyPassRate: 'อัตราของวันเสียชีวิต',
    dailyOKQuantity: 'จำนวนของดวงอาทิตย์',
    dailyNGQuantity: 'จำนวนของวัน',
    dailyHourProduct: 'จุดสูงสุดของชั่วโมงชั่วโมง',
    dailyHourProductVal: 'ผลิตยากูซ่าในวันเดียวกัน',
    dailyHourProductAve: 'ค่าเฉลี่ยของชั่วโมงการผลิต',
    form: 'จาก',
    start: 'เริ่มได้',
    locat: 'ตำแหน่ง',
    cut: 'ดึงข้อมูล',
    charac: 'อักขระ',
    formulaMainten: 'การดูแลสูตร',
    cannotBeEmptyOrAPositiveInteger: 'ไม่สามารถว่า ง หรือเป็นจำนวนเต็มบวกได้',
    otherValuesCannotBeEmpty: 'ค่าอื่นไม่สามารถว่า งได้',
    editSuccess: 'แก้ไขเรียบร้อยแล้ว',
    effective: 'ได้ผล',
    invalid: 'ไม่ถูกต้อง',
    what: 'ใช่ไหม?',
    changeTo: 'ตรวจสอบว่า คุณต้องการแก้ไขการแสดงตัว ที่มีประสิทธิภาพเป็น',
    Cancellation: 'ยกเลิกภารกิจ',
    TaskInit: 'เริ่มการทำงาน',
    scan: 'กำลังสแกน',
    scanOrdNumMESTask: 'สแกนหมายเลขคำสั่งเพื่อรับภารกิจ M :',
    select: 'การเลือก',
    success: 'สำเร็จ',
    PleaseSelect: 'โปรดเลือกอย่างน้อยหนึ่งรายการ!',
    conSel: 'ยืนยันการเลือก',
    Piece: 'แถบข้อมูล?',
    SuccessIss: 'ทำสำเร็จ',
    pcsCode: 'บาร์โค้ด',
    PCSReverse: 'ทำการตรวจสอบ และตรวจสอบข้อมูล',
    PCSFront: 'ทำการกวาดข้อมูลเส้นข้างหน้า',
    PCSOrder: 'ลำดับตัวประกอบ',
    PCSLevel: 'เกรด PCS',
    LineScanPCSResults: 'ตรวจหาจุดสะท้อนแสงของสาย',
    PCSSortingNGCode: 'รหัส NG',
    PCSSortingNGMsg: 'ตัวเลือก NG รายละเอียด',
    PCSStatus: 'สถานะ',
    whetherXOUT: 'ไล่ระดับสี',
    time: 'เวลา',
    setStatus: 'ตั้งสถานะ',
    export: 'ส่งออก',
    setOutside: 'บันทึกการทำงาน',
    packInformation: 'ข้อมูลการบำรุงรักษาสูตรบรรจุภัณฑ์',

    // 解绑功能相关
    unbind: 'แก้มัด',
    manual: 'ปรับเอง',
    masterLot: 'สถานะการใช้ส่วนตัว', // 母批状态
    targetQuantity: 'จำนวนเป้าหมาย',
    completed: 'จำนวน ที่สมบูรณ์', // 完成数量
    NGQuantity: 'จำนวน NG', // NG数量
    checkplans: 'จำนวนการตรวจสอบเบื้องต้น', // 首检计划数量
    checksCompleted: 'จำนวนการตรวจสอบแรกเสร็จสมบูรณ์', // 首检完成数量
    platingplates: 'จำนวนไม้บรรทัด', // 陪镀板数量
    plates: 'จำนวนแผ่น', // 分盘数量
    area: 'ครั้งหน้า', // 面次
    MaxNumber: 'จำนวนครั้งสูงสุด ที่ใช้จานย่อย', // Tray盘最大使用次数
    orderParameters: 'ชุดพารามิเตอร์แบบเดี่ยวแบบเดี่ยว', // 工单参数集合
    selectedTaskData: 'ยืนยันการลบข้อมูลงาน ที่เลือกไว้?',
    confirmUnbindAllSetTips:
      'คุณแน่ใจ หรือว่า จะแก้มัดนี้ และบันทึกข้อมูลชุดทั้งหมด?',
    confirmUnbindAllBdTips:
      'คุณแน่ใจ หรือว่า การแก้ไขชุดนี้ จะบันทึกข้อมูลทั้งหมดของคณะกรรมการ?',
    BoardNumber: 'เปิดกระดาน', // 板子序号
    BarCode: 'บอร์ดบาร์โค้ด', // 板子条码
    Features: 'ความคมชัดของฟังก์ชั่น' // 功能对比
  },
  interfaceLogs: {
    time: 'เวลา',
    interName: 'ชื่ออินเทอร์เฟส',
    interDes: 'คำอธิบายส่วนติดต่อ',
    sourceSystem: 'ระบบ ที่มาจาก',
    requestParams: 'ขอพารามิเตอร์',
    responseParams: 'อาร์กิวเมนต์ ที่ตอบสนอง',
    successFlag: 'สำเร็จ หรือไม่',
    ESBinterName: 'ชื่อส่วนติดต่อของ ESB',
    ESBinterDes: 'คำอธิบายส่วนติดต่อของ ESB',
    ESBinterAddress: 'ที่อยู่อินเทอร์เฟสของ ESB',
    targetSystem: 'ระบบเป้าหมาย',
    interMeans: 'โหมดส่วนติดต่อ',
    startTime: 'เวลาเริ่ม',
    endTime: 'เวลาสิ้นสุด',
    costTime: 'ระยะเวลา ที่ร้องขอ',
    InterMessages: 'ข้อความส่วนติดต่อ',
    parasList: 'ปรับ แต่งพารามิเตอร์',
    requestInfo: 'ร้องขอจดหมาย',
    remark: 'บันทึก',
    total: 'จำนวนรวม',
    pre: 'หน้าสุดท้าย',
    next: 'หน้าถัดไป',
    current: 'ลำดับ',
    page: 'หน้ากระดาษ',
    paramsDetails: 'รายละเอียดพารามิเตอร์',
    pleaseEnter: 'กรุณาใส่เนื้อหา',
    Topped: 'ด้านบนสุด',
    bottomSet: 'ด้านล่างสุด',
    ExportFailed: 'การส่งออกล้มเหลว',
    serialNumber: 'หมายเลขลำดับ'
  },
  scadaAlarmReport: {
    title: 'รายงานการแจ้งเตือน SCADA',
    prodLine: 'สายการผลิต',
    station: 'สถานี',
    instanceCode: 'รหัสอินสแตนซ์',
    alarmCode: 'รหัสการแจ้งเตือน',
    alarmDesc: 'คำอธิบายการแจ้งเตือน',
    resetFlag: 'สถานะการรีเซ็ต',
    resetYes: 'รีเซ็ตแล้ว',
    resetNo: 'ยังไม่รีเซ็ต',
    time: 'เวลา',
    startTime: 'เวลาเริ่มต้น',
    endTime: 'เวลาสิ้นสุด',
    instanceDesc: 'คำอธิบายอินสแตนซ์',
    alarmLevel: 'ระดับการแจ้งเตือน',
    alarmTime: 'เวลาแจ้งเตือน',
    resetTime: 'เวลารีเซ็ต',
    isSimulated: 'จำลอง',
    simulatedYes: 'ใช่',
    simulatedNo: 'ไม่',
    tag: 'แท็ก',
    export: 'ส่งออก',
    search: 'ค้นหา',
    reset: 'รีเซ็ต',
    totalCount: 'จำนวนทั้งหมด',
    currentPage: 'หน้าปัจจุบัน',
    previousPage: 'หน้าก่อนหน้า',
    nextPage: 'หน้าถัดไป',
    page: 'หน้า',
    top: 'ด้านบนสุด',
    bottom: 'ด้านล่างสุด',
    selectProdLineAndStation: 'กรุณาเลือกสายการผลิตและสถานี',
    queryException: 'ข้อผิดพลาดในการค้นหา',
    exportFailed: 'การส่งออกล้มเหลว',
    pleaseEnter: 'โปรดป้อนเนื้อหา',
    Topped: 'อยู่ด้านบน',
    bottomSet: 'ด้านล่าง',
    ExportFailed: 'การส่งออกล้มเหลว',
    serialNumber: 'เพิ่มเลเยอร์'
  },
  messageReport: {
    cim_from: 'แหล่ง ที่มา',
    finish_flag: 'แสดงตัว',
    screen_control: 'โหมดการแสดงผล',
    screen_code: 'รหัส',
    cim_msg: 'จดหมาย',
    selectStation: 'โปรดเลือกตำแหน่งงาน'
  },
  diagnosis: {
    client_code: 'อินสแตนซ์',
    link_status: 'สถานะจดหมายข่าว',
    link_message: 'ข้อมูลจดหมายข่าว',
    creationDate: 'ช่วงเวลา',
    table: 'แบบฟอร์ม',
    simulated_flag: 'โลโก้จำลอง',
    curved: 'รูปแบบโค้ง',
    abnormal: 'สถิติความผิดปกติ',
    link_normal: 'ปกติ',
    link_abnormal: 'ความผิดปกติ',
    scadaFaild: 'แบบสอบถามข้อมูล Scada ล้มเหลว', // Scada实例基础数据查询失败
    scadaError: 'หมดเวลาแบบสอบถาม Scada หรือข้อผิดพลาด SQL', // Scada实例基础数据查询超时或者SQL错误
    link_analysis: 'การวิเคราะห์กราฟแบบกระจาย', // 通讯状态散点图分析
    sampleSize: 'จำนวนตัวอย่าง',
    statusValue: 'ค่าสถานะ',
    abnormalStatistics: 'ความถี่การสื่อสารที่ผิดปกติ', // 通讯异常频次统计
    frequency: 'ความถี่เครือข่ายหัก',
    exception: 'selCellIP ผิดปกติ', // 查询selCellIP异常
    instance: 'โปรดเลือกอินสแตนซ์',
    UnableNumber: 'ไม่ได้รับ IP หน่วยกับหมายเลขพอร์ต', // 未获取到单元IP与端口号
    NoData: 'ไม่มีข้อมูลเพิ่มเติม', // 已到顶无数据
    scadaQueryFailed: 'แบบสอบถามรายงานของ Scada ล้มเหลว:', // Scada通讯报表查询失败:
    scadaSqlError: 'หมดเวลาแบบสอบถามรายงาน Scada หรือข้อผิดพลาด SQL', // Scada报表查询超时或者SQL错误
    serialNumber: 'หมายเลขซีเรียล:',
    time: 'เวลา:',
    messageDes: 'คำอธิบายข้อความ:',
    NoDataAvailable: 'ปัจจุบันไม่มีข้อมูล'
  },
  proMonitor: {
    processTasks: 'บริการจราจร:',
    total: 'จำนวนข้อสรุป:',
    dayQuantity: 'จำนวนวัน:',
    dayNormal: 'วันนั้นเป็นปกติ:',
    dayAbnormal: 'ความผิดปกติของวัน',
    cancel: 'ขั้นตอนการยกเลิก',
    view: 'ดู',
    time: 'เวลา:',
    info: 'ข้อมูล:',
    step: 'ขั้นตอน:',
    log: 'บันทึก:',
    queryException: 'สอบถามความผิดปกติ',
    cancelSucess: 'การยกเลิกสำเร็จ:',
    abnormal: 'ความผิดปกติ:'
  },
  header: {
    user: 'ผู้ใช้ปัจจุบัน:',
    onlineDuration: 'ระยะเวลาออนไลน์:',
    min: 'นาที',
    signOut: 'ออกจากการล็อกอิน',
    lockScreen: 'ล็อคหน้าจอ',
    screen: 'ย่อ/ขยายเต็มจอภาพ',
    stationSelect: 'การเลือกหลัก',
    greenNewEnergy: 'พลังงานสีเขียว และสิ่งแวดล้อมใหม่',
    loginPassword: 'กรุณาใส่รหัสผ่าน',
    unlock: 'ปลดล็อค',
    exitSystem: 'ยืนยันการออกจากระบบ หรือไม่?',
    passwordUnlock: 'กรุณาใส่รหัสผ่านเพื่อปลดล็อค',
    passwordError: 'รหัสผ่านปลดล็อคผิดพลาดโปรดป้อนรหัสผ่านเพื่อปลดล็อค',
    unlockingSuccessful: 'ปลดล็อคสำเร็จ',
    error: 'ผิดพลาด',
    usernameRequire: 'ชื่อผู้ใช้ไม่สามารถว่า งได้',
    passwordRequire: 'รหัสผ่านไม่สามารถว่า งได้',
    roleRequire: 'ตัวละครต้องไม่ว่างเปล่า',
    codeRequire: 'ไม่สามารถปล่อยให้เป็นแคปช่าได้'
  },
  hmiMain: {
    onLine: 'ออนไลน์', // 在线
    offline: 'ไม่เชื่อมต่อ', // 脱机
    prot1: 'พอร์ต1', // 端口1
    prot2: 'พอร์ต2', // 端口2
    protAgv1: 'AGV(1)', // AGV1
    protAgv2: 'AGV(2)', // AGV2
    enable: 'เริ่มได', // 启用
    disabled: 'ห้ามใช้', // 禁用
    manual: 'ปรับเอง', // 手动画面
    eapPing: 'Ping ( EAP )', // Ping-EAP
    reOnline: 'รับออนไลน์', // 收板机在线
    CCD: 'CCD',
    PlateCCD2: 'CCD2',
    CCD1: 'CCD1',
    CCD2: 'CCD2',
    DownStatus: 'DownStatus',
    employee: 'เลขบัญชี', // 员工号
    name: 'ชื่อ', // 姓名
    department: 'ดิวิชั่น', // 部门
    status: 'สถานะ', // 状态
    EAP: 'EAP', // EAP远程
    AIS: 'AIS', // AIS本地
    production: 'การผลิต', // 生产模式
    plate: 'กระดาน', // 板件模式
    transport: 'การจัดการ', // 搬运模式
    panel: 'มีสิPanel', // 有Panel
    NoPanel: 'ไม่มีPanel', // 无Panel
    more: 'มากกว่า', // 更多
    work: 'งานพิมพ์', // 作业端口
    Login: 'ล็อก อิน', // 登入
    LogOut: 'ออ ก', // 登出
    prot1Step: 'พอร์ต1ขั้นตอน', // 端口1步序
    prot1info: 'พอร์ต1จดหมาย', // 端口1消息
    forced: 'ต้องถอนกำลัง', // 强制退载具
    prot2Step: 'พอร์ต1ขั้นตอน', // 端口2步序
    prot2info: 'พอร์ต2จดหมาย', // 端口2消息
    portStatus: 'สถานะพอร์ต', // 端口状态
    carrierStatus: 'กำลังโหลด', // 载具状态
    firstEdition: 'ว่ารุ่นแรก', // 是否首板
    complete: 'จบ/วาง', // 完工/计划
    firstPlan: 'ชื่อจบ/วาง', // 首板完工/计划
    workMode: 'โหมด', // 作业模式
    LWH: 'กว้าง/สูง', // 长宽高
    vehicle: 'พาห/หลัง', // 载具/天盖
    Tray: 'โหมด', // Tray盘码
    plateCode: 'ขนาด', // 板件码
    fmale: 'จำนว', // 母批号
    blowOff: 'ปิด', // 放口,
    plan: 'แผน', // 计划
    put: 'ปล่อ', // 已放
    putStatus: 'ปล่อ', // 放状态
    panelStatus: 'สถานะ', // 板件状态
    received: 'ได้รั', // 已收,
    close: 'ปิดป', // 收口
    receivingState: 'ปิดสถ', // 收状态
    SupportBrowning: 'การจับคู่สีน้ำตาล', // 配套棕化
    SupportPunching: 'จับคู่การเจาะ', // 配套冲孔
    serial: 'เพิ่ม', // 序号
    firstPiece: 'ชิ้นแร', // 首件
    abnormal: 'ผิดป', // 异常
    time: 'เวลา', // 时间
    sideboard: 'กระด', // 陪板
    vehicleInput: 'ข้อมูลเข้า', // 载具输入
    manualBatch: 'รายงานจดหมา', // 手工批次上报
    localTask: 'เพิ่มงานภา', // 本地任务增加
    firstCheck: 'อัยการค', // 首检判定
    panelInput: 'ข้อมูลเข้า', // Panel输入
    panelJudge: 'การตัดสินชิ้นส่วนบอร์ด', // Panel判定
    confirmation: 'ยืนยันการตัด', // 切批确认
    CIMMessage: 'จดหม', // CIM消息
    dailyOnline: 'ผลิตวันออนไลน์', // 日在线生产
    dailyOffLine: 'ผู้ผลิตวันออก', // 日脱机生产
    procedure: 'ผิดปกติในการรับข้อมูลการล็อกอินปัจจุบัน', // 获取当前登录信息异常
    side: 'หัว', // 正面
    reverse: 'ก้อย', // 反面
    productionBoard: 'กระดา', // 生产板
    dummy: 'dummy',
    have: 'มีสิ', // 有
    isNo: 'ไม่มี', // 无
    logoutSuccess: 'ที่จะออกจากระบบ', // 登出成功
    workOrderEntry: 'รายการสิ่งพิมพ์', // 工单输入
    clickOK:
      'จำเป็นต้องสลับสูตรระบบหลักโปรดยืนยันเมื่อสลับเสร็จแล้วให้คลิกเพื่อยืนยัน', // 批次需要切换主制程配方,请确认切换完成后点击确定
    palletInput: 'ป้อนรูปภาพ', // Pallet/Tray输入
    mixedBatch: 'การเลือก', // 混批确认
    forValidation: 'ภารกิจสแกนเทียมไปยังอีพีเอสตรวจสอบ', // 人工扫描任务并提交到EAP验证
    forFinish: 'รายงานแบทช์ด้วยตนเอง', // 手动结批上报
    forFinish2: 'สิ้นสุดล็อตด้วยมือ', // 手动结批上报
    Identify: 'ตรวจสอบการเชื่อมต่อ', // 确定对端口
    mandatory: 'จะดำเนินการส่งคืนอุปกรณ์ควบคุม หรือไม่?', // 进行强制退载具操作吗？
    machineProduction: 'ในการผลิตของสถานะปัจจุบันไม่อนุญาตให้มีการดัดแปลงสถานะ', // 当前机台生产中,不允许状态修改
    cancelFlowchart: 'ยกเลิกการโยกย้ายสำเร็จ', // 取消流程图成功
    cancelException: 'การโยกย้าย ที่ผิดปกติ', // 取消流程图异常
    abnormalError: 'ค้นหาข้อมูล ที่ผิดปกติ：', // 查询点位数据异常
    EAPCommunication: 'การสื่อสารของ EAP ถูกตัด', // EAP通讯中断
    PLCCommunication: 'การสื่อสารของ PLC ถูกตัด', // PLC通讯中断
    CCDinterrupted: 'บอร์ดอ่านขนาดอักษรซีดีการสนทนาหยุดทำงาน', // 板件读码CCD通讯中断
    CCD1Interrupted: 'ยานพาหนะ 1 อ่านรหัสการสื่อสาร CCD ขัดจังหวะ', // 载具1读码CCD通讯中断
    CCD2Interrupted: 'ยานพาหนะ 2 อ่านรหัสการสื่อสาร CCD ขัดจังหวะ', // 载具2读码CCD通讯中断
    startTime: 'เวลาเริ่ม', // 开始时间
    endTime: 'เวลาสิ้นสุด', // 结束时间
    implement: 'เล่นจริง',
    implementUnLoad: 'การยอมรับ',
    scanConfirmation: 'ยืนยัน',
    inSeconds: 'หมดเวลาในการเชื่อมต่อภายในวินาที',
    BOXInput: 'เปิดกล่อง',
    deviceInit: 'เริ่มการทำงาน',
    deviceRun: 'กำลังทำงาน',
    deviceStop: 'หยุด',
    deviceIdle: 'เตรียมพร้อม',
    deviceDown: 'หยุดการทำงาน',
    devicePm: 'การดูแล'
  },
  workOrder: {
    workOrderNumber: 'หมายเล', // 工单号
    processCode: 'รหัสระยะท', // 制程代码
    productUse: 'การใช้ผลิ', // 产品用途
    batchNumber: 'หมายเลข', // 批号简码
    materialNumber: 'หมายเล', // 物料号
    NumbeOfPlates: 'จำนวนถ', // 板件数量
    platSe: 'เรียงก', // 板序
    flip: 'พลิก', // 翻转
    parameterInfo: 'ข้อมูลพาราม' // 参数信息
  },
  productionTasks: {
    masterStatus: 'สถานะการใช้',
    subbatchState: 'สถานะขอ',
    methodCompletion: 'โหมด',
    normalFinish: 'ปกติ',
    lessBoard: 'กระดานเสร็จสิ้น',
    boardsFinish: 'กระดานเสร็จสิ้น',
    viewPNL: 'PNL',
    ForcedFinish: 'บังคับให้จบกระดาน',
    PNLlist: 'รายการ Pnl',
    deleteTask: 'ลบงาน',
    FinishedCondition: 'เสร็จสมบูรณ์'
  },
  stationParameter: {
    stationConfiguration: 'การปรับ แต่งค่าพารามิเตอร์ตำแหน่งงาน',
    theSettings: 'ไม่พบไอพี ที่ตรงกันโปรดตรวจสอบการตั้งค่า',
    theReceiver: 'ไม่มีการค้นหาไปยังไอพีเก็บโปรดตั้งค่าไอพี',
    notEnabled: 'ดึงข้อมูลการเฝ้าระวังการดึงข้อมูล',
    interfaceCode: 'การเข้ารหัสส่ว',
    interDes: 'คำอธิบายส่วนติดต่อ',
    NGDegree: 'จำนวนครั้ง',
    synchronization: 'ปรับเทียบ',
    continuation: 'ต่อไป',
    enable: 'เปิดใช้งาน',
    cutOnline: 'เครื่องเล่นไม่ออนไลน์ และไม่สามารถตัดออนไลน์',
    onlineMode: 'ไม่สามารถใช้งานได้ในโหมดออนไลน์'
  },
  // 广合
  guanghe: {
    parmas: 'พารามิเตอร์', // 参数
    finish: 'เสร็จสิ้น', // 完成
    plateNG: 'ชิ้นส่วนบอร์ด NG', // 板件NG数量
    trayNG: 'พาเลท NG', // 托盘NG数量
    trayOK: 'ถาดตกลง', // 托盘OK数量
    panelNG: 'แผง NG', // 面板NG数量
    panelOK: 'แผง OK', // 面板OK数量
    deviceOee: 'อุปกรณ์ OEE', // 设备OEE
    OkQuantity: 'จำนวน OK', // OK数量
    NGQuantity: 'จำนวน NG', // NG数量
    total: 'ยอดรวม', // 总量
    NGPass: 'NG_PASS', // NG_PASS数量
    OfflineCodeReading: 'ออฟไลน์', // 离线读码量
    OnlineCodeReading: 'ออนไลน์', // 在线读码量
    readBitRate: 'อัตรา', // 读码率
    port1Return: 'พอร์ต 1', // 端口1退载具
    port2Return: 'พอร์ต 2', // 端口2退载具
    port3Return: 'พอร์ต 3', // 端口3退载具
    port4Return: 'พอร์ต 4', // 端口4退载具
    exitSystem: 'ออกจาก', // 退出系统
    logout: 'ขึ้น-ลง', // 员工登出
    energyDetail: 'รายละเอียดข้อมูลพลังงาน',
    timeTaskData: 'ข้อมูลงานเวลา'
  },
  // 定颖
  dy: {
    auto: 'อัตโนมัติ',
    offLine: 'โหมดออก',
    local: 'Local',
    semiAuto: 'โหมดครึ่ง',
    model: 'โหมด',
    Semi_Auto: 'Semi-Auto',
    Auto: 'Auto'
  },
  wx: {
    recipeName: ' ชื่อสูตร',
    downRecipe: ' สแกนรหัสสูตรดาวน์โหลด:',
    editParameters: ' ไม่ว่าจะเป็นการปรับเปลี่ยนพารามิเตอร์:',
    import: ' นำเข้า',
    export: ' ส่งออก',
    equipSelfTest: ' อุปกรณ์ทดสอบตัวเอง ',
    recipeType: ' ประเภทสูตร',
    recipeDesc: ' คำอธิบายสูตร',
    lotNo: ' บาร์โค้ดแบทช์ ',
    version: ' หมายเลขรุ่น ',
    recipeOperation: ' การดำเนินการสูตร',
    down: ' ดาวน์โหลดสูตร',
    upload: ' อัพโหลดสูตร ',
    distributed: ' อุปกรณ์ลงผม',
    equipSelfTestInfo: ' อุปกรณ์การทดสอบตัวเองข้อมูล ',
    waitUpload: 'มีงานอัปโหลดอยู่แล้ว โปรดรอให้การอัปโหลดเสร็จสมบูรณ์',
    uploadFormula: ' เรียกกระบวนการอัปโหลดสูตร',
    confirmDelete: ' ยืนยันการลบที่เลือก ',
    articleData: ' แถบข้อมูล ',
    SuccessfullyMes: ' อัปโหลด mes success',
    selected: ' ยืนยันการอัปโหลดที่เลือก ',
    uploadTask: 'มีงานอัปโหลดอยู่แล้ว โปรดรอให้การอัปโหลดเสร็จสมบูรณ์',
    trigger: ' เรียกกระบวนการอัปโหลดสูตร',
    confirmRecipe: ' ยืนยันอัพโหลดสูตร?',
    alreadyUploadTask: 'มีงานอัปโหลดอยู่แล้ว โปรดรอให้การอัปโหลดเสร็จสมบูรณ์',
    confirmDownload: ' ยืนยันการดาวน์โหลดสูตรจากระบบบน',
    pleaseWait: ' มีงานดาวน์โหลดอยู่แล้วโปรดรอการดาวน์โหลดเสร็จสิ้น',
    triggerDownload: ' เรียกกระบวนการดาวน์โหลดสูตร',
    IssuedEquipment: ' ยืนยันสูตรลงอุปกรณ์?',
    alreadyExistsTask: ' มีภารกิจที่ส่งต่อไปแล้ว โปรดรอจนกว่าการส่งต่อไปจะเสร็จสิ้น',
    SuccessfullyIssued: ' สูตรผมลงเพื่อความสำเร็จของอุปกรณ์',
    selectOne: ' กรุณาเลือกข้อมูล!',
    exportName: ' กำหนดชื่อสูตรส่งออกเป็น',
    recipeDownload: 'ดาวน์โหลดสูตร',

    comfirmReset: ' ยืนยันคุณต้องการรีเซ็ตกระบวนการหรือไม่?',
    resetSuccess: 'รีเซ็ตกระบวนการสำเร็จ',
    currently: 'ตอนนี้ยังไม่ได้รับวัสดุโปรดไปบำรุงรักษาวัสดุก่อน',
    queryFailed: 'กระบวนการรีเซ็ตล้มเหลว',
    failedMaterial: 'ล้มเหลวตามหมายเลขวัสดุปัจจุบัน',
    foundRecipe: 'สอบถามข้อมูลสูตรที่เกี่ยวข้อง',
    pleaseScan: 'กรุณาสแกนหรือใส่หมายเลขงานก่อน',
    reset: 'รีเซ็ต',
    lotNum: 'คำสั่งงาน',
    batchCheck: 'check',
    employeeID: 'ชื่อพนักงาน',
    productInfo: 'ข้อมูลการผลิต',
    alarmMsg: 'ข้อความเตือน',
    alarmTime: 'เวลาเตือนภัย',
    alarmLevel: 'ระดับการเตือนภัย',
    alarmCode: 'รหัสปลุก',
    alarmDesc: 'คำอธิบายการเตือนภัย',
    feedbackInfo: 'ข้อเสนอแนะ',
    userInfo: 'เข้าสู่ระบบผู้ใช้',
    equipmentInfo: 'อุปกรณ์ข้อมูล',
    productResult: 'เข้าสู่ระบบผลิตภัณฑ์',
    otherInfo: 'ข้อมูลอื่น ๆ',
    panelInfo: 'ผลการสแกนรหัส',
    paramInfo: 'อัปโหลดพารามิเตอร์',
    statusInfo:'อัพโหลดสถานะ',
    producrParameter: 'พารามิเตอร์การผลิต',
    projectName: 'ชื่อโครงการ',
    currentValue: 'ค่าปัจจุบัน',
    state: 'สถานะ',
    unit: 'หน่วย',
    upperLimit: 'เพดาน',
    lowerLimit: 'ลง',
    modifyFormula: 'ปรับเปลี่ยนสูตร',
    thickness: 'ความหนาของแผ่นเครื่องเสียบ',
    modifyParams: 'จะปรับเปลี่ยนพารามิเตอร์หรือไม่',
    parameterCode: 'การเข้ารหัส',
    parameterDesc: 'คำอธิบาย',
    parameterValue: 'ค่า',
    cancel: 'ยกเลิก',
    confirm: 'ระบุนามสกุลที่ต่ำกว่า',
    plcheartbeat: 'PLC',
    upstream: 'ต้นน้ำ',
    downstream: 'ปลายน้ำ',
    electricity: 'ไฟฟ้า',
    productionMode: 'โหมดการผลิตมวล',
    firstArticleMode: 'โหมดชิ้นแรก',
    dummyMode: 'โหมด Dummy',
    stationCode: 'หมายเลขสถานี',
    capacity: 'ความจุ',
    readbitRate: 'อัตราการอ่านรหัส',
    onDuty: 'ในหน้าที่',
    leave: 'ปลดประจำการ',
    enterCode: 'โปรดป้อนรหัสวัสดุ',
    enterDesc: 'โปรดป้อนรหัสวัสดุ',
    hole: 'เลือกหลุมเติม + ',
    selectSupface: 'เลือกพื้นผิว +',
    materialmaintenance: 'การบำรุงรักษาวัสดุ',
    equipment: 'อุปกรณ์',
    scanDownload: 'ดาวน์โหลดสูตร:',
    addParams: 'ใหม่',
    scadaRequest: 'เปิดตัว Scada ขอสูตรในการดาวน์โหลดสูตร',
    scadaError: 'มีความผิดปกติในการดาวน์โหลดสูตร',
    uploadSuccessful: 'อัพโหลดสำเร็จ',
    downLoadRecipe: 'ยืนยันการส่งสูตร',
    subDetail: 'รายละเอียดการบำรุงรักษาสูตรย่อย',
    modificationFailed: 'แก้ไขล้มเหลว'
  },
  // 福建瑞闽
  fjrm: {
    wasteBoxCode: 'การเข้ารหัสกล่องเสีย',
    wasteBoxDes: 'เศษกล่อง Description',
    height: 'ความสูง',
    rgvCode: 'การเข้ารหัส RGV',
    rgvDes: 'RGV รายละเอียด',
    wharfCode: 'รหัสท่าเรือ',
    wharfDes: 'ท่าเรือ Description',
    wharfType: 'ประเภทของท่าเรือ',
    locationX: 'พิกัด X',
    locationY: 'พิกัด Y',
    locationZ: 'พิกัด Z',
    wharfOrder: 'การเรียงลำดับท่าเรือ',
    wharfTag: 'จุดจอดเรือ',
    lockFlag: 'ไม่ว่าจะเป็นการล็อค',
    lotNo: 'หมายเลขล็อต',
    si: 'si',
    fe: 'fe',
    cu: 'cu',
    mn: 'mn',
    mg: 'mg',
    ni: 'ni',
    zn: 'zn',
    ti: 'ti',
    cr: 'cr',
    na: 'na',
    ca: 'ca',
    taskNum: 'หมายเลขงาน',
    taskFrom: 'แหล่งที่มาของงาน',
    taskWay: 'โหมดงาน',
    taskType: 'ประเภทของงาน',
    wareHouse: 'พื้นที่ห้องสมุด',
    fromStockCode: 'ไลบรารีเริ่มต้น',
    toStockCode: 'ไลบรารีเป้าหมาย',
    lotNum: 'หมายเลขล็อต',
    materialCode: 'การเข้ารหัสวัสดุ',
    width: 'น้ำหนักรวม',
    errorMin: 'ค่าต่ำสุด',
    errorMax: 'สูงสุด',
    executeWidth: 'ดำเนินการน้ำหนักรวม',
    taskOrder: 'การเรียงลำดับงาน',
    taskStatus: 'สถานะงาน',
    enableFlag: 'บัตรประจำตัวที่ถูกต้อง'
  },

  // ทางลัดตัวเลือกวันที่เวลา
  dateTimePicker: {
    last10Minutes: '10 นาทีที่ผ่านมา',
    last30Minutes: '30 นาทีที่ผ่านมา',
    lastHour: '1 ชั่วโมงที่ผ่านมา',
    lastDay: '1 วันที่ผ่านมา',
    lastWeek: '7 วันที่ผ่านมา',
    last30Days: '30 วันที่ผ่านมา',
    last90Days: '90 วันที่ผ่านมา',
    lastYear: '1 ปีที่ผ่านมา'
  }
}
