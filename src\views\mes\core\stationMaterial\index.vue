<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :default-time="['00:00:00', '23:59:59']"
                    :picker-options="pickerOptions1"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="生产线：">
                <el-select
                  v-model="query.prod_line_code"
                  filterable
                  clearable
                  @change="prodLineChange"
                >
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_id"
                    :label="item.prod_line_code + ' ' + item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位：">
                <el-select v-model="query.station_code" filterable clearable>
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_id"
                    :label="item.station_code + ' ' + item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料号：">
                <el-input v-model="query.material_code" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="订单号：">
                <el-input v-model="query.make_order" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产品型号：">
                <el-select v-model="query.small_model_type" filterable clearable>
                  <el-option
                    v-for="item in smallModelTypeData"
                    :key="item.small_model_type"
                    :label="item.small_model_type + ' ' + item.main_material_des"
                    :value="item.small_model_type"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="合格标志：">
                <el-select v-model="query.quality_sign" clearable>
                  <el-option
                    v-for="item in dict.QUALIFIED_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="上传标识：">
                <el-select v-model="query.up_flag" clearable>
                  <el-option
                    v-for="item in ['Y', 'N']"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号：">
                <el-input v-model="query.material_batch" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工件编号：">
                <el-input v-model="query.serial_num" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="机体编码：">
                <el-input v-model="query.print_barcode" clearable />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="启用标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in ['Y', 'N']"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="精追条码：">
                <el-input v-model="query.exact_barcode" clearable />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button
                  class="filter-item"
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  @click="toQuery"
                >{{ $t("lang_pack.commonPage.search") }}</el-button>
                <!-- 搜索 -->
                <el-button
                  v-if="crud.optShow.reset"
                  class="filter-item"
                  size="small"
                  icon="el-icon-refresh-left"
                  @click="resetQuery()"
                >{{ $t("lang_pack.commonPage.reset") }}</el-button>
                <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button
            class="filter-item"
            size="small"
            type="primary"
            icon="el-icon-close"
            plain
            round
            :disabled="crud.selections.length <= 0"
            @click="unbind"
          >
            解绑
          </el-button>
        </template>
        <template slot="group_left">
          <el-button
            v-permission="permission.down"
            :loading="crud.downloadLoading"
            :disabled="!crud.data.length"
            size="small"
            icon="el-icon-download"
            @click="doExport"
          />
        </template>
      </crudOperation>
      <el-dialog :fullscreen="false" top="10px" :show-close="true" :close-on-click-modal="false" title="替换物料" custom-class="step-attr-dialog" width="30%" :visible.sync="detailDialogVisible">
        <el-input v-model="exact_barcode" clearable placeholder="请输入要替换的物料码" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCannel">取消</el-button>
          <el-button type="primary" @click="handleOk">确定</el-button>
        </span>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" fixed="left" />
            <el-table-column width="100" label="操作" align="center" fixed="left">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row)">替换物料</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_line_code"
              width="100"
              label="生产线"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              width="100"
              label="工位号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.station_des"
              width="100"
              label="工位描述"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              width="220"
              label="工件编号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="print_barcode"
              width="150"
              label="打印总成号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.quality_sign"
              width="100"
              label="总合格标志"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="
                    scope.row.station_docs.quality_sign === 'OK' ? 'success' : 'danger'
                  "
                  effect="dark"
                  style="cursor: pointer"
                  size="medium"
                >{{
                  scope.row.station_docs.quality_sign === "OK" ? "OK" : "NG"
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_date"
              width="150"
              label="采集时间"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              width="100"
              label="物料号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_des"
              width="150"
              label="物料描述"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="use_count"
              width="150"
              label="使用数量"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_uom"
              width="100"
              label="单位"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_batch"
              width="150"
              label="批次号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lable_barcode"
              width="150"
              label="标签条码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="exact_barcode"
              width="150"
              label="精准二维码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bom_version"
              width="100"
              label="物料版本号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_supplier"
              width="150"
              label="供应商代码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="supplier_des"
              width="100"
              label="供应商描述"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.make_order"
              width="150"
              label="订单号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.small_model_type"
              width="150"
              label="机型"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.container_num"
              width="150"
              label="载具条码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.staff_id"
              width="150"
              label="操作者"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.product_batch"
              width="150"
              label="订单批号"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.up_flag"
              width="100"
              label="上传标识"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.station_docs.up_flag === 'Y' ? 'success' : 'warning'"
                  effect="dark"
                  style="cursor: pointer"
                  size="medium"
                >{{ scope.row.station_docs.up_flag }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.up_ng_code"
              width="150"
              label="上传错误代码"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.up_ng_msg"
              width="150"
              label="上传错误消息"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_docs.enable_flag"
              width="100"
              label="有效标识"
            >
              <template slot-scope="scope">
                {{ scope.row.station_docs.enable_flag === "Y" ? "有效" : "无效" }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px; text-align: right">
            <el-button-group>
              <el-button type="primary">总数量：{{ page.total }}</el-button>
              <el-button type="primary">当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getParamsSel } from '@/utils/index'
import crudStationMaterial from '@/api/mes/core/stationMaterial'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import { mesExportSelOne } from '@/api/mes/core/meExport'
import { downloadFile } from '@/utils/index'
import { fileDownload } from '@/api/core/file/file'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {}
export default {
  name: 'MES_ME_STATION_MATERIAL',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: '过站物料数据',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_trace_id',
      // 排序
      sort: ['material_trace_id desc'],
      // CRUD Method
      crudMethod: { ...crudStationMaterial },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        tableSize: 40,
        enable_flag: 'Y'
      }
    })
  },
  // 数据字典
  dicts: ['QUALIFIED_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 390,
      permission: {
        add: ['admin', 'mes_me_station_material:add'],
        edit: ['admin', 'mes_me_station_material:edit'],
        del: ['admin', 'mes_me_station_material:del'],
        down: ['admin', 'mes_me_station_material:down']
      },
      stationData: [],
      prodLineData: [],
      projectData: [],
      smallModelTypeData: [],
      nowPageIndex: 1, // 当前页数
      pageList: [],
      exportId: '',
      detailDialogVisible: false,
      exact_barcode: '',
      timer: '',
      pickerMinDate: null,
      pickerMaxDate: null,
      day15: 31 * 24 * 3600 * 1000,
      pickerOptions1: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            return (time.getTime() > (this.pickerMinDate + this.day15)) || (time.getTime() < (this.pickerMinDate - this.day15))
          }
          return false
        }
      }
    }
  },
  computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime =
        start.getFullYear() +
        '-' +
        (start.getMonth() + 1 >= 10
          ? start.getMonth() + 1
          : '0' + (start.getMonth() + 1)) +
        '-' +
        (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) +
        ' 00:00:00'
      const defalutEndTime =
        end.getFullYear() +
        '-' +
        (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : '0' + (end.getMonth() + 1)) +
        '-' +
        (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) +
        ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.crud.query.item_date = this.timeDefault

    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    doExport() {
      if (!this.query.item_date) {
        this.$message({
          message: '一次最多只能导出31天数据，请重选时间后再导出！',
          type: 'info'
        })
        return
      }
      let dateStr1 = this.query.item_date[0]
      dateStr1 = dateStr1.replace(/-/g, '/')
      let dateStr2 = this.query.item_date[1]
      dateStr2 = dateStr2.replace(/-/g, '/')
      var date1 = new Date(dateStr1)
      var date2 = new Date(dateStr2)
      var Difference_In_Time = date2.getTime() - date1.getTime()
      var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
      if (Difference_In_Days > 31) {
        this.$message({
          message: '一次最多只能导出31天数据，请重选时间后再导出！',
          type: 'info'
        })
        return
      }

      this.crud.downloadLoading = true
      crudStationMaterial
        .exportEventInsert(this.query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.exportId = defaultQuery.result
            this.timer = setInterval(this.getFileStatus, 2000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
            this.crud.downloadLoading = false
          }
        })
        .catch(() => {
          this.$message({ message: '导出数据异常', type: 'error' })
          this.crud.downloadLoading = false
        })
    },
    getFileStatus() {
      // 获取文件下载状态
      mesExportSelOne({ export_id: this.exportId }).then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0 && defaultQuery.data) {
          if (defaultQuery.data.finish_status === 'OK') {
            clearInterval(this.timer)
            // 文件已生成，则下载该文件
            fileDownload({ file_path: defaultQuery.data.down_url })
              .then((result) => {
                downloadFile(result, defaultQuery.data.export_name + '数据', 'csv')
                this.crud.downloadLoading = false
              })
              .catch(() => {
                this.crud.downloadLoading = false
              })
          } else if (defaultQuery.data.finish_status === 'NG') {
            this.$message({ message: defaultQuery.data.error_msg, type: 'error' })
            clearInterval(this.timer)
            this.crud.downloadLoading = false
          }
        }
      })
    },
    opendDetail(row) {
      this.currentRowData = row
      this.detailDialogVisible = true
    },
    handleCannel() {
      this.detailDialogVisible = false
      this.exact_barcode = ''
    },
    handleOk() {
      if (!this.exact_barcode) {
        this.$message({ type: 'error', message: '物料码不能为空' })
        return
      }
      const query = {
        material_trace_id: this.currentRowData.material_trace_id,
        exact_barcode: this.exact_barcode
      }
      crudStationMaterial.materialUpd(query).then(res => {
        if (res.code === 0) {
          this.$message({ type: 'success', message: '物料码替换成功' })
          this.handleCannel()
          this.toQuery()
          return
        }
        this.$message({ type: 'error', message: res.msg })
      }).catch(ex => {
        this.$message({ type: 'error', message: ex.msg })
      })
    },
    toQuery() {
      const Arr = ['item_date', 'prod_line_code', 'station_code', 'material_code', 'make_order', 'small_model_type', 'quality_sign', 'up_flag', 'material_batch', 'serial_num', 'print_barcode', 'enable_flag', 'exact_barcode']
      if (!getParamsSel(this, Arr)) {
        this.$message({
          type: 'warning',
          message: '请至少选择一个查询条件'
        })
        return
      }
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      Object.keys(this.query).forEach(key => {
        this.query[key] = this.crud.defaultQuery[key]
      })
      // this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page =
          this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    prodLineChange(val) {
      const lineItem = this.prodLineData.find((item) => item.prod_line_code === val)
      if (lineItem) {
        this.getStationList(lineItem.prod_line_id)
        this.getSmallModel(lineItem.prod_line_id)
      }
    },
    getStationList(prod_line_id) {
      this.stationData = []
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getSmallModel(prod_line_id) {
      this.smallModelTypeData = []
      selSmallModel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.smallModelTypeData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },

    unbind() {
      this.$confirm(`确认解绑选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var idList = []
          this.crud.selections.forEach((item) => {
            idList.push(item.material_trace_id)
          })
          crudStationMaterial
            .del({
              idList: idList
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch((ex) => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
