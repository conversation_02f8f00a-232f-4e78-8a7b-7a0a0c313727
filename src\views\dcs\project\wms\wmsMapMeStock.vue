<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="库位区域:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="库位号：">
                <el-input v-model="query.stock_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="是否饱和：">
                <el-select v-model="query.full_flag" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'是',value:'Y'},{id:'2',label:'否',value:'N'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="是否锁定：">
                <el-select v-model="query.lock_flag" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'是',value:'Y'},{id:'2',label:'否',value:'N'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column label="操作" align="center" width="80" fixed="left">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-tag style="cursor: pointer;" @click="viewDetail(scope.row)">查看明细</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="stock_group_code" label="库位区域" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_code" width="160" label="库位号" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_count" label="库存量" />
            <el-table-column :show-overflow-tooltip="true" prop="m_length" label="板长" />
            <el-table-column :show-overflow-tooltip="true" prop="m_width" label="板宽" />
            <el-table-column :show-overflow-tooltip="true" prop="m_thickness" label="板厚" />
            <el-table-column :show-overflow-tooltip="true" prop="start_cell_row" width="160" label="起始像素单格行号" />
            <el-table-column :show-overflow-tooltip="true" prop="end_cell_row" width="160" label="终点像素单格行号" />
            <el-table-column :show-overflow-tooltip="true" prop="start_cell_col" width="130" label="起始像素单格列号" />
            <el-table-column :show-overflow-tooltip="true" prop="end_cell_col" width="130" label="终点像素单格列号" />
            <el-table-column :show-overflow-tooltip="true" prop="position_x" label="中心X坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="position_y" label="中心Y坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_status" label="库位状态">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.stock_status == 'NORMAL' ? '正常' : '异常' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="full_flag" label="是否饱和">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.full_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="lock_flag" label="是否锁定">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.lock_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="temp_flag" width="120" label="是否转运库位">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.temp_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <selectModal ref="selectModal" :dict="dict" />
  </div>
</template>

<script>
import crudWmsMapMeStock from '@/api/dcs/project/wms/wmsMapMeStock'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import selectModal from '@/components/selectModal'
const defaultForm = {

}
export default {
  name: 'WMS_MAP_ME_STOCK',
  components: { crudOperation, rrOperation, pagination, selectModal },
  cruds() {
    return CRUD({
      title: '库存查询',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'stock_id',
      // 排序
      sort: ['stock_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapMeStock },
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        search: false,
        refresh: false,
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_me_stock:add'],
        edit: ['admin', 'b_dcs_wms_map_me_stock:edit'],
        del: ['admin', 'b_dcs_wms_map_me_stock:del']
      },
      queryCreated: true
    }
  },
  dicts: ['TASK_TYPE', 'TASK_WAY', 'LOCK_FLAG', 'STOCK_STATUS', 'KIT_FLAG', 'KIT_MATERIAL_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.$nextTick(() => {
      this.queryCreated && this.crud.toQuery()
    })
  },
  methods: {
    viewDetail(data) {
      if (!data.stock_id) {
        this.$message({ type: 'error', message: '未获取到库位id' })
        return
      }
      this.$refs.selectModal.open({
        type: 'ckkcmx',
        checkType: '',
        search: {
          stock_id: data.stock_id,
          sort: 'stock_d_id desc',
          user_name: Cookies.get('userName')
        }
      })
    }
  }
}
</script>
