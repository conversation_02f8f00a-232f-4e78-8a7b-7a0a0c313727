import request from '@/utils/request'

// 查询安灯工位牌基础
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonCardSel',
    method: 'post',
    data
  })
}
// 新增安灯工位牌基础
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonCardIns',
    method: 'post',
    data
  })
}
// 修改安灯工位牌基础
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonCardUpd',
    method: 'post',
    data
  })
}
// 删除安灯工位牌基础
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonCardDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

