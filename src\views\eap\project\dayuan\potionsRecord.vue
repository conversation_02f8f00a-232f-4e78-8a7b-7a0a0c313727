<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm" style="align-items:center">
          <div class="wrapElFormFirst col-md-9 col-12">
            <div class="formChild col-md-6 col-12">
              <el-form-item label="添加时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width: 100%"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="添加人：">
                <el-input v-model="query.added_by" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="药水名称：">
                <el-input v-model="query.potions_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-3 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="药水名称" prop="potions_name">
            <el-input v-model="form.potions_name" />
          </el-form-item>
          <el-form-item label="用量" prop="dosage">
            <el-input v-model.number="form.dosage" />
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" />
          </el-form-item>
          <el-form-item label="添加时间" prop="add_time">
            <el-date-picker
              v-model="form.add_time"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%"
              default-time="00:00:00"
            />
          </el-form-item>
          <el-form-item label="添加人" prop="added_by">
            <el-input v-model="form.added_by" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </el-form>
        <el-divider />
        <div class="ruleBottom">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  :show-overflow-tooltip="true" prop="potions_name" min-width="100" label="药水名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="dosage" min-width="100" label="用量" />
            <el-table-column  :show-overflow-tooltip="true" prop="unit" min-width="100" label="单位" />
            <el-table-column  :show-overflow-tooltip="true" prop="add_time" min-width="100" label="添加时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="added_by" min-width="100" label="添加人" />
            <el-table-column  :show-overflow-tooltip="true" prop="remark" min-width="100" label="备注" />
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudPotionsRecord from '@/api/eap/project/eapDyPotionsRecord'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  potions_name: '',
  dosage: '',
  unit: '',
  add_time: '',
  added_by: '',
  remark: ''
}
export default {
  name: 'EAP_POTIONS_RECORD',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '药水添加记录',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'potions_record_id',
      // 排序
      sort: ['add_time desc'],
      // CRUD Method
      crudMethod: { ...crudPotionsRecord },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('用量不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'proa_eap_me_potions_record:add'],
        edit: ['admin', 'proa_eap_me_potions_record:edit'],
        del: ['admin', 'proa_eap_me_potions_record:del'],
        down: ['admin', 'proa_eap_me_potions_record:down']
      },
      rules: {
        potions_name: [{ required: true, message: '请填写药水名称', trigger: 'blur' }],
        dosage: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        unit: [{ required: true, message: '请填写单位', trigger: 'blur' }],
        add_time: [{ required: true, message: '请选择添加时间', trigger: 'blur' }],
        added_by: [{ required: true, message: '请填写添加人', trigger: 'blur' }]
      }
    }
  },
  watch: {
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.ruleBottom {
  text-align: center;
  margin: 40px 0;
}
</style>
