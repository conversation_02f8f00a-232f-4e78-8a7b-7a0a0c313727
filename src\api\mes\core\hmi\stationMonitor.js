import request from '@/utils/request'

// 查询当前工位作业信息
export function mesHmiStationMisSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationMisSel',
    method: 'post',
    data
  })
}

// 查询当前工位作业信息-全柴
export function mesQcHmiStationMisSel(data) {
  return request({
    url: 'aisEsbWeb/mes/project/quanchai/MesQcHmiStationMisSel',
    method: 'post',
    data
  })
}

// 查询当前工位作业状态监控配置
export function mesHmiStationStatusSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationStatusSel',
    method: 'post',
    data
  })
}
// 查询过站列表
export function mesHmiStationFlowSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationFlowSel',
    method: 'post',
    data
  })
}
// 查询工序物料，质量信息
export function mesHmiPdureMaterialAndQualitySel(data, type) {
  return request({
    url: `aisEsbWeb/mes/core/MesHmiPdureMaterialAndQualitySel${type || ''}`,
    method: 'post',
    data
  })
}
// 国轩拧紧枪数据接口
export function mesHmiPdureMaterialAndQualitySel02(data) {
  return request({

    url: 'aisEsbWeb/mes/core/MesHmiPdureMaterialAndQualitySel02',
    method: 'post',
    data
  })
}
// 查询工位过站日、月、小时统计数量
export function mesHmiProductionStatisticSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiProductionStatisticSel',
    method: 'post',
    data
  })
}
// 查询工位过站质量信息
export function mesHmiStationFlowQualitySel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationFlowQualitySel',
    method: 'post',
    data
  })
}
// 查询工位过站物料信息
export function mesHmiStationFlowMaterialSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationFlowMaterialSel',
    method: 'post',
    data
  })
}

// 物料扫描验证API接口
export function mesHmiStationMaterialScan(data, type) {
  return request({
    url: `aisEsbWeb/mes/core/MesMeMaterialScan${type || ''}`,
    method: 'post',
    data
  })
}
// 物料强制下线
export function MesMeMaterialOffline(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMaterialOffline',
    method: 'post',
    data
  })
}

// 重新拧紧或者NG越过
export function mesHmiStationShaftManual(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationShaftManual',
    method: 'post',
    data
  })
}

// 设备NG放行(南京国轩)
export function mesHmiStationManualNgPass(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationManualNgPass',
    method: 'post',
    data
  }) 
}

// 设备OK放行或者NG放行(设备接口方式)[国轩]
export function mesInterfGxConfirmGo(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesInterfGxConfirmGo',
    method: 'post',
    data
  })
}

// 工位订单信息查询
export function mesHmiStationMoSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesHmiStationMoSel',
    method: 'post',
    data
  })
}

// 批次物料信息查询
export function mesRecipeBatchMaterialSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBatchMaterialSelect',
    method: 'post',
    data
  })
}

// 批次物料扫描
export function mesRecipeBatchMaterialScan(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBatchMaterialScan',
    method: 'post',
    data
  })
}

export default { mesHmiStationMisSel, mesHmiStationStatusSel, mesHmiStationFlowSel,
  mesHmiPdureMaterialAndQualitySel, mesHmiPdureMaterialAndQualitySel02, mesHmiProductionStatisticSel, mesHmiStationFlowQualitySel,
  mesHmiStationFlowMaterialSel, mesHmiStationMaterialScan, MesMeMaterialOffline, mesHmiStationShaftManual,
  mesInterfGxConfirmGo, mesHmiStationMoSel, mesQcHmiStationMisSel, mesRecipeBatchMaterialSelect,
  mesRecipeBatchMaterialScan,mesHmiStationManualNgPass }
