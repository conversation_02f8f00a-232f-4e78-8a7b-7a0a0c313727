<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="料框编码:">
                                <!-- 料框编码 -->
                                <el-input v-model="query.box_location" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="料框类型:">
                                <!-- 料框类型 -->
                                <el-select v-model="query.box_type" clearable filterable>
                                    <el-option v-for="item in dict.BOX_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="有效标识：">
                                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.smaterialbox.materialFrameCoding')" prop="box_location">
                                <!-- 料框编码 -->
                                <el-input v-model="form.box_location" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.smaterialbox.materialFrameName')" prop="box_location_des">
                                <!-- 料框名称 -->
                                <el-input v-model="form.box_location_des" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.smaterialbox.materialFrameType')" prop="box_type">
                                <!-- 料框类型 -->
                                <el-select v-model="form.box_type" clearable filterable>
                                    <el-option v-for="item in dict.BOX_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.smaterialbox.AGVLocationCode')" prop="box_agv_location">
                                <!-- AGV位置码 -->
                                <el-input v-model="form.box_agv_location" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.recipequality.enableFlag')" prop="enable_flag">
                                <!-- 有效标识 -->
                                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" fixed />
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                    <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                                    <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                                    <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                                    <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                                    <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.sort_box_id }}</el-descriptions-item>
                                    <el-descriptions-item label="料框编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_location }}</el-descriptions-item>
                                    <el-descriptions-item label="料框名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_location_des }}</el-descriptions-item>
                                    <el-descriptions-item label="料框类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.BOX_TYPE[props.row.box_type] }}</el-descriptions-item>
                                    <el-descriptions-item label="AGV位置码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_agv_location }}</el-descriptions-item>
                                    <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 创建时间 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="creation_date"
                            :label="$t('lang_pack.smaterialbox.creationTime')" /> -->
                        <!-- 修改者 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="last_updated_by"
                            :label="$t('lang_pack.smaterialbox.modifiedBy')" /> -->
                        <!-- 时间 -->
                        <!-- <el-table-column  :show-overflow-tooltip="true" prop="last_update_date"
                            :label="$t('lang_pack.smaterialbox.time')" /> -->
                        <!-- 料框编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="box_location"
                            :label="$t('lang_pack.smaterialbox.materialFrameCoding')" width="220" align='center'/>
                        <!-- 料框名称 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="box_location_des"
                            :label="$t('lang_pack.smaterialbox.materialFrameName')" width="220" align='center'/>
                        <!-- 料框类型 -->
                        <el-table-column :show-overflow-tooltip="true" prop="box_type"
                            :label="$t('lang_pack.smaterialbox.materialFrameType')"  align='center'  width="220">
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.BOX_TYPE[scope.row.box_type] }}
                            </template>
                        </el-table-column>
                        <!-- AGV位置码 -->
                        <el-table-column :show-overflow-tooltip="true" prop="box_agv_location"
                            :label="$t('lang_pack.smaterialbox.AGVLocationCode')" width="220" align='center'/>
                        <el-table-column :label="$t('lang_pack.recipequality.enableFlag')" align="center"   width="220"
                            prop="enable_flag" >
                            <!-- 有效标识 -->
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
                            </template>
                        </el-table-column>
                        <!-- Table单条操作-->
                        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right" >
                            <!-- 操作 -->
                            <template slot-scope="scope">
                                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudFmodSortBox from '@/api/dcs/core/fmodSortBox/fmodSortBox'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    sort_box_id: '',
    last_updated_by: '',
    last_update_date: '',
    box_location: '',
    box_location_des: '',
    box_type: '',
    box_agv_location: '',
    enable_flag: 'Y'
}
export default {
    name: 'WEB_FMOD_SORT_BOX',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '分拣料箱基础',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'sort_box_id',
            // 排序
            sort: ['sort_box_id asc'],
            // CRUD Method
            crudMethod: { ...crudFmodSortBox },
            // 按钮显示
            optShow: {
                add: true,
                edit: true,
                del: true,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                sort_box_id: [{ required: true, message: '请选择分拣箱id', trigger: 'blur' }],
                box_location: [{ required: true, message: '请选择料框编码', trigger: 'blur' }],
                box_location_des: [{ required: true, message: '请选择料框名称', trigger: 'blur' }],
                box_type: [{ required: true, message: '请选择料框类型', trigger: 'blur' }],
                enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }],
            },
        }
    },
    // 数据字典
    dicts: ['ENABLE_FLAG','BOX_TYPE'],
    mounted: function () {
        console.log(this.dict)
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {},
    methods: {
        changeEnabled(data, val) {
            this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
        .then(() => {
            crudFmodSortBox
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              sort_box_id: data.sort_box_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    }
}
</script>
  