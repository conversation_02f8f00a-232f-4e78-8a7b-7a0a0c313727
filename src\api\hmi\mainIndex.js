import request from '@/utils/request'

export function EapHmiShiftWorkTimeSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapHmiShiftWorkTimeSelect',
    method: 'post',
    data
  })
}

// 按时间统计OEE
export function EapHmiStatUtilitySelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapHmiStatUtilitySelect',
    method: 'post',
    data
  })
}

// 按时间统计板件履历及读码率
export function EapHmiPanelAndReadingRateSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapHmiPanelAndReadingRateSelect',
    method: 'post',
    data
  })
}
// 当天单位小时实际投板数量
export function EapHmiPanelCountPerHourSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapHmiPanelCountPerHourSelect',
    method: 'post',
    data
  })
}
// 按时间统任务数据
export function EapHmiTaskSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapHmiTaskSelect',
    method: 'post',
    data
  })
}
// 报警信息
export function CoreScadaAlarmSelect(addRess, data) {
  return request({
    url: `${addRess}/cell/core/scada/CoreScadaAlarmSelect`,
    method: 'post',
    data
  })
}
// 读取点位
export function CoreScadaReadTag(addRess, data) {
  return request({
    url: `${addRess}cell/core/scada/CoreScadaReadTag`,
    method: 'post',
    data
  })
}
// 能源信息
export function EapScreenConfigStationSel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigStationSel',
    method: 'post',
    data
  })
}

export default { EapHmiShiftWorkTimeSelect, EapHmiStatUtilitySelect, EapHmiPanelAndReadingRateSelect, EapHmiPanelCountPerHourSelect, EapHmiTaskSelect, CoreScadaAlarmSelect, EapScreenConfigStationSel, CoreScadaReadTag }
