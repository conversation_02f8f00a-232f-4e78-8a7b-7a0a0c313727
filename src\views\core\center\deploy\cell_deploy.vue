<template>
  <div class="box-card">
    <el-card shadow="always" class="wrapCard">
      <el-form ref="formServer" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.celldeploy.serve')">
                <!-- 服务： -->
                <el-select v-model="formServer.server_id" clearable size="small" @change="serverChange">
                  <el-option v-for="item in serverData" :key="item.server_id" :label="item.server_des" :value="item.server_id" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <el-button class="filter-item" size="small" type="primary" plain round icon="el-icon-upload" style="margin-left: 10px" :disabled="formServer.server_id === ''" @click="uploadFiles(true)">{{ $t('lang_pack.celldeploy.uploadMirroring') }}</el-button>
              <!-- 上传镜像 -->
              <el-button v-if="true" class="filter-item" size="small" type="warning" plain round icon="el-icon-folder-opened" :disabled="formServer.server_id === ''" @click="toButInstall">{{ $t('lang_pack.celldeploy.installAll') }}</el-button>
              <!-- 全部安装 -->
              <el-button v-if="true" class="filter-item" size="small" type="danger" plain round icon="el-icon-folder-checked" :disabled="formServer.server_id === ''" @click="toButChooseInstall">{{ $t('lang_pack.celldeploy.installSelected') }}</el-button>
              <!-- 选择安装 -->
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <!--抽屉：文件上传-->
    <el-dialog v-dialogDrag title="文件上传" center :visible.sync="uploadVisible" width="60%">
      <uploader ref="uploader" :options="options" :auto-start="false" :file-status-text="fileStatusText" class="uploader-ui" @file-added="onFileAdded" @file-success="onFileSuccess" @file-progress="onFileProgress" @file-error="onFileError">
        <uploader-unsupport />
        <uploader-drop>
          <div>
            <uploader-btn id="global-uploader-btn" ref="uploadBtn" :attrs="attrs">选择文件<i class="el-icon-upload el-icon--right"/></uploader-btn>
          </div>
        </uploader-drop>
        <uploader-list />
      </uploader>

      <div style="text-align: center;margin-top:30px;">
        <el-button size="small" icon="el-icon-close" plain @click="toButDrawerCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" @click="toButDrawerUpload">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确定 -->
      </div>
    </el-dialog>

    <!--服务列表-->
    <el-card shadow="always" style="margin-top: 10px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :stripe="true" :height="height" :highlight-current-row="true" :row-class-name="tableRowClassName" @selection-change="toSelectionTableChangeHandler">
            <el-table-column  type="selection" width="55" />
            <el-table-column  :show-overflow-tooltip="true" prop="cell_container_name" :label="$t('lang_pack.celldeploy.unitName')" />
            <!-- 单元名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_container_des" :label="$t('lang_pack.celldeploy.unitDescription')" />
            <!-- 单元描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_image_name" :label="$t('lang_pack.celldeploy.cellMirrorName')" />
            <!-- 单元镜像名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="update_image_name" :label="$t('lang_pack.celldeploy.installMirrorName')" />
            <!-- 安装时镜像名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="server_image_flag" :label="$t('lang_pack.celldeploy.serviceMirroringInstall')" width="110">
              <!-- 服务镜像安装 -->
              <template slot-scope="scope">
                <el-switch v-model="scope.row.server_image_flag" style="display: block" active-value="Y" inactive-value="N" active-color="#13ce66" inactive-color="#ff4949" :disabled="scope.row.update_cell_id === 0" />
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-link class="linkItem" type="primary" @click="toTableButUpload(scope.row)">上传</el-link>
                <el-link class="linkItem" type="primary" :disabled="scope.row.update_cell_id === 0" @click="toTableButInstall(scope.row)">安装</el-link>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { lovServer } from '@/api/core/center/server'
// 上传
import { ACCEPT_CONFIG } from '@/components/Upload/config'
import SparkMD5 from 'spark-md5'
import { mergeFile } from '@/api/core/file/uploadFile'

import { cellInstallSel, ServerInstallIns, CellInstallIns, ServerAndCellInstallSel, InstallSuccessDel, CellFileUpdateDel, ServerInstall, CellInstall } from '@/api/core/center/cellDeploy'

export default {
  name: 'CELL_DEPLOY',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 170,
      // 查询条件
      formServer: {
        server_id: ''
      },
      serverData: [], // 服务LOV

      // 服务列表：Table
      listLoadingTable: false,
      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],
      currentRowData: [], // 选中行
      selectedTable: [], // 已选择项

      currentServerHost: '', // 当前服务IP

      // 抽屉：文件上传
      uploadVisible: false, // 是否弹出抽屉
      serverUpload: true, // 标记是服务(Y)/Cell(N)上传文件
      options: {
        // 目标上传 URL，默认POST
        target: 'http://localhost:7089/uploader/chunk',
        // 分块大小(单位：字节)
        chunkSize: '2048000',
        // 上传文件时文件内容的参数名，对应chunk里的Multipart对象名，默认对象名为file
        fileParameterName: 'upfile',
        // 失败后最多自动重试上传次数
        maxChunkRetries: 3,
        // 是否开启服务器分片校验，对应GET类型同名的target URL
        testChunks: true,

        /*
          服务器分片校验函数，判断秒传及断点续传,传入的参数是Uploader.Chunk实例以及请求响应信息
          reponse码是successStatuses码时，才会进入该方法
          reponse码如果返回的是permanentErrors 中的状态码，不会进入该方法，直接进入onFileError函数 ，并显示上传失败
          reponse码是其他状态码，不会进入该方法，正常走标准上传
          checkChunkUploadedByResponse函数直接return true的话，不再调用上传接口
          */
        checkChunkUploadedByResponse: function(chunk, response_msg) {
          const objMessage = JSON.parse(response_msg)
          if (objMessage.skipUpload) {
            return true
          }
          return (objMessage.uploadedChunks || []).indexOf(chunk.offset + 1) >= 0
        }
      },
      attrs: {
        accept: ACCEPT_CONFIG.getAll()
      },
      fileStatusText: {
        success: '上传成功',
        error: '上传失败',
        uploading: '上传中',
        paused: '暂停',
        waiting: '等待上传'
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 170
    }
  },
  created: function() {
    // 服务Lov
    this.serverData = []
    var query = {
      userName: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    lovServer(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.count > 0) {
          this.serverData = defaultQuery.data
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    // ===============================================================================
    // Server变更事件
    serverChange() {
      this.tableDataTable = []

      // 当前服务IP
      const serverInfo = this.serverData.filter(item => item.server_id === this.formServer.server_id)[0]
      if (serverInfo.server_host_1 !== '') {
        this.currentServerHost = serverInfo.server_host_1
      } else if (serverInfo.server_host_2 !== '') {
        this.currentServerHost = serverInfo.server_host_2
      } else if (serverInfo.server_host_3 !== '') {
        this.currentServerHost = serverInfo.server_host_3
      } else if (serverInfo.server_host_4 !== '') {
        this.currentServerHost = serverInfo.server_host_4
      }

      console.log('获取currentServerHost：' + this.currentServerHost)

      this.listLoadingTable = true
      var query = {
        userName: Cookies.get('userName'),
        server_id: this.formServer.server_id
      }
      cellInstallSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            }
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
    },

    // ===============================================================================
    // 上传镜像
    uploadFiles(op) {
      if (this.formServer.server_id === '') {
        this.$message({
          message: '请选择服务',
          type: 'info'
        })
        return
      }

      this.serverUpload = op
      this.uploadVisible = true
      // 配置路径
      var method = ':7089/uploader/chunk'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost' + method
      } else {
        path = 'http://' + this.currentServerHost + method
      }
      this.options.target = path

      console.log('[uploadFiles]上传镜像：process.env.NODE_ENV：' + process.env.NODE_ENV)
      console.log('获取参数options.target：' + this.options.target)
    },
    // 新增文件
    onFileAdded(file) {
      console.log('文件值：' + JSON.stringify(file, null, '\t'))

      if (file.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      // if (this.serverUpload) {
      //  if (file.name !== this.formServer.server_image.split(':')[0] + '.tar') {
      //    this.$message({
      //      message: '上传的镜像文件名必须与服务镜像名称一致',
      //      type: 'info'
      //    })
      //    return
      //  }
      // } else {
      //  if (this.currentRowData.cell_image_name.split(':')[0] + '.tar' !== file.name) {
      //    this.$message({
      //      message: '上传的镜像文件名必须与单元镜像名称一致',
      //      type: 'info'
      //    })
      //    return
      //  }
      // }

      this.computeMD5(file)
    },
    /**
     * 计算md5，实现断点续传及秒传
     * @param file
     */
    computeMD5(file) {
      file.pause()

      // 单个文件的大小限制5G
      const fileSizeLimit = 5 * 1024 * 1024 * 1024
      if (file.size > fileSizeLimit) {
        this.$message({
          showClose: true,
          message: '文件大小不能超过5G'
        })
        file.cancel()
      }

      const fileReader = new FileReader()
      const time = new Date().getTime()
      const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 10 * 1024 * 1000
      const chunks = Math.ceil(file.size / chunkSize)
      const spark = new SparkMD5.ArrayBuffer()
      // 由于计算整个文件的Md5太慢，因此采用只计算第1块文件的md5的方式
      const chunkNumberMD5 = 1

      loadNext()

      fileReader.onload = e => {
        spark.append(e.target.result)

        if (currentChunk < chunkNumberMD5) {
          loadNext()
        } else {
          const md5 = spark.end()
          file.uniqueIdentifier = md5
          file.resume()

          console.log(`MD5计算完毕：${file.name} \nMD5：${md5} \n分片：${chunks} 大小:${file.size} 用时：${new Date().getTime() - time} ms`)
        }
      }

      fileReader.onerror = function() {
        this.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }

      function loadNext() {
        const start = currentChunk * chunkSize
        const end = start + chunkSize >= file.size ? file.size : start + chunkSize

        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
        currentChunk++

        console.log('计算第' + currentChunk + '块')
      }
    },
    /* 成功
    第一个参数 rootFile 就是成功上传的文件所属的根 Uploader.File 对象，它应该包含或者等于成功上传文件；
    第二个参数 file 就是当前成功的 Uploader.File 对象本身；
    第三个参数就是 message 就是服务端响应内容，永远都是字符串；
    第四个参数 chunk 就是 Uploader.Chunk 实例，它就是该文件的最后一个块实例，如果你想得到请求响应码的话，chunk.xhr.status就是
    */
    onFileSuccess(rootFile, file, response, chunk) {
      // refProjectId为预留字段，可关联附件所属目标，例如所属档案，所属工程等
      // file.refProjectId = "123456789";

      // 传参
      var server_image_flag = 'Y' // 是否使用服务上传的镜像文件
      if (this.serverUpload) {
        server_image_flag = 'Y'
      } else {
        server_image_flag = 'N'
      }
      file.cellContainerName = this.currentRowData.cell_container_name
      file.serverImageFlag = server_image_flag

      // 配置路径
      var method = ':7089/uploader/mergeFile'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost' + method
      } else {
        path = 'http://' + this.currentServerHost + method
      }
      console.log('[onFileSuccess]合并文件path：' + path)

      mergeFile(path, file)
        .then(responseData => {
          if (responseData.data.code === 415) {
            console.log('合并操作未成功，结果码：' + responseData.data.code)
          }
        })
        .catch(function(error) {
          console.log('合并后捕获的未知异常：' + error)
        })
    },
    // 进度条
    onFileProgress(rootFile, file, response, chunk) {},
    onFileError(rootFile, file, response, chunk) {
      console.log('上传完成后异常信息：' + response)
    },
    close() {
      this.uploader.cancel()
    },
    error(msg) {
      this.$notify({
        title: '错误',
        message: msg,
        type: 'error',
        duration: 2000
      })
    },
    // 抽屉取消按钮事件
    toButDrawerCancel() {
      this.uploadVisible = false
    },
    // 抽屉上传按钮事件
    toButDrawerUpload() {
      // Server上传文件成功后处理
      if (this.serverUpload) {
        const _parameter = {
          userName: Cookies.get('userName'),
          server_id: this.formServer.server_id
        }
        ServerInstallIns(_parameter)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message({
                message: '初始化单元安装配置成功',
                type: 'success'
              })
              // 查询
              this.serverChange()
            }
          })
          .catch(() => {
            this.$message({
              message: '初始化单元安装配置异常',
              type: 'error'
            })
          })
      } else {
        // Cell上传文件成功后处理
        const _parameter = {
          userName: Cookies.get('userName'),
          cell_id: this.currentRowData.cell_id,
          update_cell_id: this.currentRowData.update_cell_id,
          cell_image_name: this.currentRowData.cell_image_name
        }
        CellInstallIns(_parameter)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message({
                message: '初始化单元安装配置成功',
                type: 'success'
              })
              // 查询
              this.serverChange()
            }
          })
          .catch(() => {
            this.$message({
              message: '初始化单元安装配置异常',
              type: 'error'
            })
          })
      }
      this.currentRowData = []
      this.uploadVisible = false
    },

    // ===============================================================================
    // 全部安装
    toButInstall() {
      this.$confirm('此操作将安装当前Server中所有的单元, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var _parameter = {
            userName: Cookies.get('userName'),
            server_id: this.formServer.server_id
          }
          ServerAndCellInstallSel(_parameter)
            .then(res => {
              const defaultQuery = JSON.parse(res.result)
              console.log('全部安装')
              console.log(defaultQuery.deploy_info)
              console.log(defaultQuery.cellList)

              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                // 配置路径
                var method = ':7089/SysContainerDeploy'
                var path = ''
                if (process.env.NODE_ENV === 'development') {
                  path = 'http://localhost' + method
                } else {
                  path = 'http://' + this.currentServerHost + method
                }
                console.log('[toButInstall]全部安装，路径：' + path)

                ServerInstall(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '安装成功',
                        type: 'success'
                      })

                      // 删除安装记录
                      var _parameter1 = {
                        server_id: this.formServer.server_id
                      }
                      InstallSuccessDel(_parameter1)
                        .then(res => {
                          const defaultQuery = JSON.parse(JSON.stringify(res))
                          if (defaultQuery.code === 0) {
                            this.$message({
                              message: '相关数据清理成功',
                              type: 'success'
                            })
                            // 查询
                            this.serverChange()
                          }
                        })
                        .catch(() => {
                          this.$message({
                            message: '相关数据清理异常',
                            type: 'error'
                          })
                        })
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '安装异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可安装信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '安装异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // ===============================================================================
    // 选择安装
    toButChooseInstall() {
      if (this.selectedTable.length === 0) {
        this.$message({
          message: '请选择需要安装的单元',
          type: 'info'
        })
        return
      }
      var ids = [] // 提取选中项的id
      var cell_ids = []
      var cell_names = []
      this.selectedTable.forEach(item => {
        ids.push(item.update_cell_id)
        cell_ids.push(item.cell_id)
        cell_names.push(item.cell_container_name)
      })
      this.$confirm('此操作将根据选择的' + cell_names + '进行安装, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var _parameter = {
            userName: Cookies.get('userName'),
            server_id: this.formServer.server_id,
            cell_ids: cell_ids.join(',')
          }
          ServerAndCellInstallSel(_parameter)
            .then(res => {
              const defaultQuery = JSON.parse(res.result)

              console.log('[ServerAndCellInstallSel]选择安装')
              console.log(defaultQuery)
              console.log(defaultQuery.deploy_info)
              console.log(defaultQuery.cellList)

              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                // 配置路径
                var method = ':7089/SysContainerDeploy'
                var path = ''
                if (process.env.NODE_ENV === 'development') {
                  path = 'http://localhost' + method
                } else {
                  path = 'http://' + this.currentServerHost + method
                }

                console.log('[toButChooseInstall]选择安装，路径：' + path)

                ServerInstall(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '安装成功',
                        type: 'success'
                      })

                      // 删除安装记录
                      var _parameter = {
                        ids: ids
                      }
                      CellFileUpdateDel(_parameter)
                        .then(res => {
                          const defaultQuery = JSON.parse(JSON.stringify(res))
                          if (defaultQuery.code === 0) {
                            this.$message({
                              message: '相关数据清理成功',
                              type: 'success'
                            })
                            // 查询
                            this.serverChange()
                          }
                        })
                        .catch(() => {
                          this.$message({
                            message: '相关数据清理异常',
                            type: 'error'
                          })
                        })
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '安装异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可安装信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '安装异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // ===============================================================================
    tableRowClassName({ row, rowIndex }) {
      if (this.selectedTable.filter(item => item.cell_id.toString() === row.cell_id.toString()).length > 0) {
        return 'row-select'
      }
      return ''
    },
    // 列表 上传按钮事件
    toTableButUpload(row) {
      this.currentRowData = row
      // 上传按钮
      this.uploadFiles(false)
      this.uploadVisible = true
    },
    // 安装
    toTableButInstall(row) {
      this.$confirm('此操作将安装单元【' + row.cell_container_name + '】的【' + row.update_image_name + '】镜像文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var ids = [] // 提取选中项的id
          ids.push(row.update_cell_id)
          var _parameter = {
            userName: Cookies.get('userName'),
            server_id: this.formServer.server_id,
            cell_ids: row.cell_id
          }
          ServerAndCellInstallSel(_parameter)
            .then(res => {
              const defaultQuery = JSON.parse(res.result)

              console.log('[ServerAndCellInstallSel]单元安装：')
              console.log(defaultQuery)
              console.log(defaultQuery.deploy_info)
              console.log(defaultQuery.cellList)

              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                // 配置路径
                var method = ':7089/SysContainerDeploy'
                var path = ''
                if (process.env.NODE_ENV === 'development') {
                  path = 'http://localhost' + method
                } else {
                  path = 'http://' + this.currentServerHost + method
                }
                console.log('[toTableButInstall]单元安装，路径：' + path)

                CellInstall(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '安装成功',
                        type: 'success'
                      })

                      // 删除安装记录
                      var _parameter = {
                        ids: ids
                      }
                      CellFileUpdateDel(_parameter)
                        .then(res => {
                          const defaultQuery = JSON.parse(JSON.stringify(res))
                          if (defaultQuery.code === 0) {
                            this.$message({
                              message: '相关数据清理成功',
                              type: 'success'
                            })
                            // 查询
                            this.serverChange()
                          }
                        })
                        .catch(() => {
                          this.$message({
                            message: '相关数据清理异常',
                            type: 'error'
                          })
                        })
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '安装异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可安装信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '安装异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    // 列表选择事件
    toSelectionTableChangeHandler(val) {
      // Table 选中事件
      this.selectedTable = val
    }
  }
}
</script>

<style scoped lang="less">
.box-card {
  height: calc(100vh - 55px);
  padding: 10px;
  padding-top: 20px;
}
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-card__body {
  padding: 10px;
}
.el-table th {
  background: #eef1f6;
}
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}
.table {
  width: 100%;
  font-size: 14px;
}
.red {
  color: #ff0000;
}
.mr10 {
  margin-right: 10px;
}
.table-td-thumb {
  display: block;
  margin: auto;
  width: 40px;
  height: 40px;
}
.uploadSlot {
  margin: -10px 10px 10px 30px;
}
.uploader-ui {
  padding: 15px;
  font-size: 12px;
  font-family: Microsoft YaHei;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
}
.uploader-ui .uploader-btn {
  margin-right: 4px;
  font-size: 12px;
  border-radius: 3px;
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
}
.uploader-ui .uploader-list {
  max-height: 440px;
  overflow: auto;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
