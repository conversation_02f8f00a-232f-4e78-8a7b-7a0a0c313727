<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="库位号:">
                <!-- 库位号 -->
                <el-input v-model="query.stock_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="型号:">
                <!-- 型号 -->
                <el-select v-model="query.model_id" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_id"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="长:">
                <!-- 长 -->
                <el-input v-model="query.m_length" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="宽:">
                <!-- 宽 -->
                <el-input v-model="query.m_width" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="厚:">
                <!-- 厚 -->
                <el-input v-model="query.m_height" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="库位状态:">
                <!-- 库位状态 -->
                <el-select v-model="query.stock_status" clearable filterable>
                  <el-option v-for="item in dict.STOCK_STATUS" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识:">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.wmsCbTable.libraryArea')" prop="ware_house">
                <!-- 库区域 -->
                <fastCode fastcode_group_code="WARE_HOUSE" :fastcode_code.sync="form.ware_house" control_type="select" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationGroupCode')" prop="stock_group_code">
                <!-- 库位组编码 -->
                <el-input v-model="form.stock_group_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.locationGroupDescription')" prop="stock_group_des">
                <!-- 库位组描述 -->
                <el-input v-model="form.stock_group_des" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationNumber')" prop="stock_code">
                <!-- 库位号 -->
                <el-input v-model="form.stock_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationDescription')" prop="stock_des">
                <!-- 库位描述 -->
                <el-input v-model="form.stock_des" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.inventoryLocationSorting')" prop="stock_order">
                <!-- 库位排序 -->
                <el-input v-model="form.stock_order" clearable size="small" />
              </el-form-item>

              <el-form-item v-if="form.model_flag === 'Y'" :label="$t('lang_pack.wmsCbTable.modelID')" prop="model_id">
                <!-- 型号ID -->
                <el-select v-model="form.model_id" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-else :label="$t('lang_pack.wmsCbTable.modelID')" prop="">
                <!-- 型号ID -->
                <el-select v-model="form.model_id" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.wmsCbTable.xCoordinate')" prop="location_x">
                <!-- X坐标 -->
                <el-input v-model="form.location_x" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.YCoordinate')" prop="location_y">
                <!-- Y坐标 -->
                <el-input v-model="form.location_y" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.ZCoordinate')" prop="location_z">
                <!-- Z坐标 -->
                <el-input v-model="form.location_z" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.zAxisDynamicCalculationMethod')" prop="z_math_way">
                <!-- Z轴动态计算方法 -->
                <el-input v-model="form.z_math_way" clearable size="small" />
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.wmsCbTable.inventory')" prop="stock_count">
                                库存量
                                <el-input v-model="form.stock_count" disabled clearable size="small" />
                            </el-form-item> -->
              <el-form-item :label="$t('lang_pack.wmsCbTable.minimumInventory')" prop="min_count">
                <!-- 最小库存 -->
                <el-input v-model.number="form.min_count" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.maximumInventory')" prop="max_count">
                <!-- 最大库存 -->
                <el-input v-model.number="form.max_count" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.maximumLimitExpirationTime')" prop="max_times">
                <!-- 最大限定超期时间(分钟) -->
                <el-input v-model="form.max_times" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationStatus')" prop="stock_status">
                <!-- 库位状态 -->
                <fastCode fastcode_group_code="STOCK_STATUS" :fastcode_code.sync="form.stock_status" control_type="select" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.locationType')" prop="stock_region_code">
                <!-- 库位类型 -->
                <fastCode fastcode_group_code="STOCK_REGION_CODE" :fastcode_code.sync="form.stock_region_code" control_type="select" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.manageInventoryOrNot')" prop="stock_flag">
                <!-- 是否管理库存 -->
                <fastCode fastcode_group_code="STOCK_FLAG" :fastcode_code.sync="form.stock_flag" control_type="radio" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.isTheModelFixed')" prop="model_flag">
                <!-- 是否固定型号 -->
                <fastCode fastcode_group_code="MODEL_FLAG" :fastcode_code.sync="form.model_flag" control_type="radio" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.isTheZAxisDefinedValue')" prop="z_set_flag">
                <!-- Z轴是否为定义值 -->
                <fastCode fastcode_group_code="Z_SET_FLAG" :fastcode_code.sync="form.z_set_flag" control_type="radio" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.enableFlag')" prop="enable_flag">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
            <el-form
              ref="ruleForm"
              class="el-form-wrap"
              :model="formRules"
              :rules="rules"
              size="small"
              label-width="100px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.wmsCbTable.warehouseLocationNumber')">
                <!-- 库位号 -->
                <el-input v-model="formRules.stock_code" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.inventory')">
                <!-- 库位量 -->
                <el-input v-model.number="formRules.stock_count" type="number" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.wmsCbTable.maximumInventory')" prop="max_count">
                <!-- 最大库存 -->
                <el-input v-model="formRules.max_count" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.taskList.SteelPlateModel')" prop="model_id">
                <!-- 钢板型号 -->
                <el-select v-model="formRules.model_id" :disabled="formRules.model_flag === 'Y' || formRules.stock_count>0" clearable filterable>
                  <el-option
                    v-for="item in modelList"
                    :key="item.model_id"
                    :label="item.model_type"
                    :value="item.model_id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingArea.Number')" prop="count">
                <!-- 数量 -->
                <el-input v-model.number="formRules.count" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item v-if="dialogType==='1'" :label="$t('lang_pack.wmsCbTable.batchNumber')">
                <!-- 批次号 -->
                <el-input v-model="formRules.lot_num" clearable size="small" />
              </el-form-item>
              <el-divider />
              <div style="text-align: center;width: 100%;">
                <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
                <!-- 取消 -->
                <el-button type="primary" size="small" icon="el-icon-check" :loading="dialogLoading" @click="handleOk">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                <!-- 确认 -->
              </div>
            </el-form>
          </el-dialog>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed prop="stock_id" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_id }}</el-descriptions-item>
                  <el-descriptions-item label="库区域" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.WARE_HOUSE[props.row.ware_house] }}</el-descriptions-item>
                  <el-descriptions-item label="库位组编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_group_code }}</el-descriptions-item>
                  <el-descriptions-item label="库位组描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_group_des }}</el-descriptions-item>
                  <el-descriptions-item label="库位号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_code }}</el-descriptions-item>
                  <el-descriptions-item label="库位描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_des }}</el-descriptions-item>
                  <el-descriptions-item label="库位排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_order }}</el-descriptions-item>
                  <el-descriptions-item label="是否管理库存" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.STOCK_FLAG[props.row.stock_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="是否固定型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.MODEL_FLAG[props.row.model_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="型号ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_id }}</el-descriptions-item>
                  <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="长" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_length }}</el-descriptions-item>
                  <el-descriptions-item label="宽" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_width }}</el-descriptions-item>
                  <el-descriptions-item label="厚" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_height }}</el-descriptions-item>
                  <el-descriptions-item label="重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_weight }}</el-descriptions-item>
                  <el-descriptions-item label="材质" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_texture }}</el-descriptions-item>
                  <el-descriptions-item label="X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_x }}</el-descriptions-item>
                  <el-descriptions-item label="Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_y }}</el-descriptions-item>
                  <el-descriptions-item label="Z坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_z }}</el-descriptions-item>
                  <el-descriptions-item label="Z轴是否为定义值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.Z_SET_FLAG[props.row.z_set_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="Z轴动态计算方法" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.z_math_way }}</el-descriptions-item>
                  <el-descriptions-item label="库存量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_count }}</el-descriptions-item>
                  <el-descriptions-item label="最小库存" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.min_count }}</el-descriptions-item>
                  <el-descriptions-item label="最大库存" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.max_count }}</el-descriptions-item>
                  <el-descriptions-item label="最大限定超期时间(分钟)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.max_times }}</el-descriptions-item>
                  <el-descriptions-item label="库位状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.STOCK_STATUS[props.row.stock_status] }}</el-descriptions-item>
                  <el-descriptions-item label="库位类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.STOCK_REGION_CODE[props.row.stock_region_code] }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="预留属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              label="出入库操作"
              width="240"
              align="center"
            >
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" icon="el-icon-share" @click="handleDialog('手动入库', scope.row, '1')">手动入库</el-button>
                <el-button slot="reference" type="text" size="small" icon="el-icon-share" @click="handleDialog('手动出库', scope.row, '3')">手动出库</el-button>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              label="事件明细"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" icon="el-icon-share" @click="handleDetails(scope.row)">事件明细</el-button>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              label="库存明细"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" icon="el-icon-share" @click="handleMudules(scope.row)">库存明细</el-button>
              </template>
            </el-table-column>
            <!-- 库区域 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ware_house"
              :label="$t('lang_pack.wmsCbTable.libraryArea')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
              </template>
            </el-table-column>
            <!-- 库位组编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_group_code"
              :label="$t('lang_pack.wmsCbTable.warehouseLocationGroupCode')"
              width="100"
              align="center"
            />
            <!-- 库位组描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_group_des"
              :label="$t('lang_pack.wmsCbTable.locationGroupDescription')"
              width="100"
              align="center"
            />
            <!-- 库位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_code"
              :label="$t('lang_pack.wmsCbTable.warehouseLocationNumber')"
              width="100"
              align="center"
            />
            <!-- 库存量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_count"
              :label="$t('lang_pack.wmsCbTable.inventory')"
              width="100"
              align="center"
            />
            <!--长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.taskTable.length')"
              width="80"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.taskTable.width')"
              width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              :label="$t('lang_pack.taskTable.thick')"
              width="80"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.taskTable.weight')"
              width="80"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              :label="$t('lang_pack.taskTable.materialQuality')"
              width="80"
              align="center"
            />
            <!-- 型号ID -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_id"
              :label="$t('lang_pack.wmsCbTable.modelID')"
              width="80"
              align="center"
            />
            <!-- 钢板型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.taskList.SteelPlateModel')"
              width="80"
              align="center"
            />
            <!-- 库位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_des"
              :label="$t('lang_pack.wmsCbTable.warehouseLocationDescription')"
              width="100"
              align="center"
            />
            <!-- 库位排序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_order"
              :label="$t('lang_pack.wmsCbTable.inventoryLocationSorting')"
              width="100"
              align="center"
            />
            <!-- 是否管理库存 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_flag"
              :label="$t('lang_pack.wmsCbTable.manageInventoryOrNot')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.STOCK_FLAG[scope.row.stock_flag] }}
              </template>
            </el-table-column>
            <!-- 是否固定型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_flag"
              :label="$t('lang_pack.wmsCbTable.isTheModelFixed')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.MODEL_FLAG[scope.row.model_flag] }}
              </template>
            </el-table-column>
            <!-- X坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_x"
              :label="$t('lang_pack.wmsCbTable.xCoordinate')"
              width="100"
              align="center"
            />
            <!-- Y坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_y"
              :label="$t('lang_pack.wmsCbTable.YCoordinate')"
              width="100"
              align="center"
            />
            <!-- Z坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_z"
              :label="$t('lang_pack.wmsCbTable.ZCoordinate')"
              width="100"
              align="center"
            />
            <!-- Z轴是否为定义值 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="z_set_flag"
              :label="$t('lang_pack.wmsCbTable.isTheZAxisDefinedValue')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.Z_SET_FLAG[scope.row.z_set_flag] }}
              </template>
            </el-table-column>
            <!-- Z轴动态计算方法 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="z_math_way"
              :label="$t('lang_pack.wmsCbTable.zAxisDynamicCalculationMethod')"
              width="120"
              align="center"
            />
            <!-- 最小库存 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="min_count"
              :label="$t('lang_pack.wmsCbTable.minimumInventory')"
              width="100"
              align="center"
            />
            <!-- 最大库存 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="max_count"
              :label="$t('lang_pack.wmsCbTable.maximumInventory')"
              width="100"
              align="center"
            />
            <!-- 最大限定超期时间(分钟) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="max_times"
              :label="$t('lang_pack.wmsCbTable.maximumLimitExpirationTime')"
              width="160"
              align="center"
            />
            <!-- 库位状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_status"
              :label="$t('lang_pack.wmsCbTable.warehouseLocationStatus')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.STOCK_STATUS[scope.row.stock_status] }}
              </template>
            </el-table-column>
            <!-- 库位类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="stock_region_code"
              :label="$t('lang_pack.wmsCbTable.locationType')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.STOCK_REGION_CODE[scope.row.stock_region_code] }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.recipequality.enableFlag')"
              width="100"
              align="center"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              prop="button"
              :label="$t('lang_pack.commonPage.operate')"
              width="150"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation
                  slot="reference"
                  :data="scope.row"
                  :permission="permission"
                  :disabled-dle="true"
                >
                  <template slot="right">
                    <el-button v-if="scope.row.stock_code.indexOf('P') > -1" type="text" size="small" @click="handleReShotBlasting(scope.row)">重新抛丸</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <inventory v-if="inventoryFlag" ref="inventory" :stock_id="stock_id" @ok="inventoryFlag=false" />
    <eventDetails v-if="eventDetailsFlag" ref="eventDetails" :stock_id="stock_id" @ok="eventDetailsFlag=false" />
  </div>
</template>
<script>
import crudWmsFmodStock from '@/api/dcs/core/wmsCbTable/wmsFmodStock'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import inventory from '../../modules/inventoryDetailsModel'
import eventDetails from '../../modules/eventDetailsModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  stock_id: '',
  ware_house: '',
  stock_group_code: '',
  stock_group_des: '',
  stock_code: '',
  stock_des: '',
  stock_order: '',
  stock_flag: 'Y',
  model_flag: 'Y',
  model_id: '',
  location_x: '',
  location_y: '',
  location_z: '',
  z_set_flag: 'Y',
  z_math_way: '',
  stock_count: 0,
  min_count: 0,
  max_count: '',
  max_times: '',
  stock_status: '',
  stock_region_code: '',
  enable_flag: 'Y'
}
export default {
  name: 'WEB_WMS_FMOD_STOCK',
  components: { crudOperation, rrOperation, udOperation, pagination, inventory, eventDetails },
  cruds() {
    return CRUD({
      title: 'WMS天车库位表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'stock_id',
      // 排序
      sort: ['stock_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsFmodStock },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 350,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },

      rules: {
        stock_id: [{ required: true, message: '请选择ID', trigger: 'blur' }],
        ware_house: [{ required: true, message: '请选择库区域', trigger: 'change' }],
        stock_group_code: [{ required: true, message: '请选择库位组编码', trigger: 'blur' }],
        stock_group_des: [{ required: true, message: '请选择库位组描述', trigger: 'blur' }],
        stock_code: [{ required: true, message: '请选择库位号', trigger: 'blur' }],
        stock_order: [{ required: true, message: '请选择库位排序', trigger: 'blur' }],
        stock_flag: [{ required: true, message: '请选择是否管理库存', trigger: 'blur' }],
        model_flag: [{ required: true, message: '请选择是否固定型号', trigger: 'blur' }],
        location_x: [{ required: true, message: '请选择X坐标', trigger: 'blur' }],
        location_y: [{ required: true, message: '请选择Y坐标', trigger: 'blur' }],
        location_z: [{ required: true, message: '请选择Z坐标', trigger: 'blur' }],
        min_count: [{ required: true, message: '请选择最小库存量', trigger: 'blur' }],
        max_count: [{ required: true, message: '请选择最大库存量', trigger: 'blur' }],
        stock_count: [{ required: true, message: '请选择库存量', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }],
        lot_num: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
        model_id: [{ required: true, message: '请选择钢板型号', trigger: 'change' }],
        count: [{ required: true, message: '请选择数量', trigger: 'blur' }]
      },
      formRules: {
        stock_group_code: '',
        stock_count: '',
        max_count: '',
        min_count: 0,
        stock_code: '',
        model_id: '',
        count: '',
        lot_num: '',
        lot_number: '',
        model_flag: ''
      },
      modelList: [],
      dialogTitle: '',
      loading: false,
      dialogVisible: false,
      dialogLoading: false,
      dialogType: '',
      inventoryFlag: false,
      eventDetailsFlag: false,
      stock_id: ''
    }
  },
  dicts: ['ENABLE_FLAG', '', 'STOCK_FLAG', 'MODEL_FLAG', 'Z_SET_FLAG', 'TASK_TYPE', 'STOCK_REGION_CODE', 'STOCK_STATUS', 'WARE_HOUSE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 350
    }
  },
  created: function() {
    this.getModelType()
  },
  methods: {
    BlurText(e) {
      const boolean = new RegExp('^[0-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning('数量不能为空或正整数')
        e.target.value = ''
      }
    },
    // 有/无效标识写法
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudWmsFmodStock
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              stock_id: data.stock_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    // 开始 "新建/编辑" - 之前
    // [CRUD.HOOK.beforeToCU](crud) {
    //     return true
    // },
    // 获取型号方法
    getModelType() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudFmodModel.sel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.modelList = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '型号查询异常',
            type: 'error'
          })
        })
    },
    handleDialog(title, row, flag) {
      this.dialogTitle = title
      this.dialogType = flag
      this.formRules.stock_code = row.stock_code
      this.formRules.stock_id = row.stock_id
      this.formRules.stock_group_code = row.stock_group_code,
      this.formRules.stock_count = row.stock_count,
      this.formRules.max_count = row.max_count,
      this.formRules.min_count = row.min_count,
      this.formRules.model_id = row.model_id,
      this.formRules.model_flag = row.model_flag
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    handleMudules(row) {
      this.stock_id = row.stock_id
      this.inventoryFlag = true
      this.$nextTick(() => {
        this.$refs.inventory.dialogVisible = true
      })
    },
    handleDetails(row) {
      this.stock_id = row.stock_id
      this.eventDetailsFlag = true
      this.$nextTick(() => {
        this.$refs.eventDetails.dialogVisible = true
      })
    },
    handleReShotBlasting(row) {
      const query = {
        stock_id: row.stock_id
      }
      this.$confirm('确定要重新抛丸吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudWmsFmodStock.reShotBlasting(query).then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '未抛丸库位修改成功', type: 'success' })
            this.crud.toQuery()
            return
          }
          this.$message({ message: defaultQuery.msg, type: 'error' })
        }).catch(err => {
          this.$message({ message: err.msg, type: 'error' })
        })
      })
    },
    handleClose() {
      this.$refs.ruleForm.resetFields()
      this.dialogLoading = false
      this.dialogVisible = false
    },
    handleOk() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          if (this.dialogType === '1') { // 入库
            if (this.formRules.count + this.formRules.stock_count > this.formRules.max_count) {
              this.$message.warning(`入库数量已超额且最多入库数量为${this.formRules.max_count - this.formRules.stock_count}`)
              this.dialogLoading = false
              return false
            }
            const data = {
              stock_code: this.formRules.stock_code,
              stock_id: this.formRules.stock_id,
              count: this.formRules.count,
              lot_num: this.formRules.lot_num,
              model_id: this.formRules.model_id
            }
            crudWmsFmodStock.inStock(data).then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.handleClose()
                this.crud.refresh()
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'warning' })
              }
            }).catch(err => {
              this.dialogLoading = false
              this.$message({ message: '手动录入异常：' + err, type: 'error' })
            })
          }
          // 手动出库的传数量和stock_id
          if (this.dialogType === '3') { // 出库
            if (this.formRules.count > (this.formRules.stock_count - this.formRules.min_count)) {
              this.$message.warning(`当前出库数量最多为${this.formRules.stock_count - this.formRules.min_count}`)
              this.dialogLoading = false
              return false
            }
            const data = {
              stock_code: this.formRules.stock_code,
              stock_id: this.formRules.stock_id,
              count: this.formRules.count,
              lot_num: this.formRules.lot_num,
              model_id: this.formRules.model_id
            }
            crudWmsFmodStock.outStock(data).then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.handleClose()
                this.crud.refresh()
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'warning' })
              }
            }).catch(err => {
              this.dialogLoading = false
              this.$message({ message: '手动出库异常：' + err, type: 'error' })
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>
