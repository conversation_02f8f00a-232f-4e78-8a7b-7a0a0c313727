<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-9 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="车辆VIN：">  <!-- 车辆VIN： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item label="时间范围：">  <!-- 时间范围： -->
                <div class="block">
                  <el-date-picker
                    v-model="query.quality_data"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" label="打刻类型：">
                <el-select v-model="query.dk_type" clearable>
                  <el-option v-for="item in dict.ENGRAVING_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-3 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column  :show-overflow-tooltip="true" prop="vin" label="VIN号" width="180" />  <!-- 车辆VIN -->
            <el-table-column  label="打刻类型" align="center" prop="dk_type">
              <!-- 打刻类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENGRAVING_TYPE[scope.row.dk_type] }}
              </template>
            </el-table-column>
            <el-table-column  label="打刻结果" align="center" prop="dk_result">
              <!-- 打刻结果 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <span v-if="scope.row.dk_result === 'Y'">打刻成功</span>
                <span v-else>打刻失败</span>
              </template>
            </el-table-column>
            <el-table-column  prop="dk_time" label="打刻时间" width="180" />  <!-- 打刻时间 -->
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >总数量：{{ page.total }}</el-button>
              <el-button
                type="primary"
              >当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import getPlayTimeData from '@/api/pmc/quality/sysPlayTime'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {

}
export default {
  name: 'playTime',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: '拧紧',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'vin',
      // // 排序
      sort: ['vin asc'],
      // CRUD Method
      crudMethod: { ...getPlayTimeData },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据字典
  dicts: ['ENGRAVING_TYPE'],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      }
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
    color: #fff;
    background-color: #1473c5;
    border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
    background: #438fd1;
    border-color: #438fd1;
    color: #fff;
}
.labelIline{
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label{
    white-space: nowrap;
  }
}
::v-deep .marginL{
  margin-left: 10px;
}
</style>
