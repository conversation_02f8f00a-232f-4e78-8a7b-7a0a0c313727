<template>
  <div class="app-container">
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col :span="24">
        <el-card
          class="box-card1"
          shadow="always"
        >
          <div class="analysisValue">
            <div
              v-for="(item, index) in reportData"
              :key="index"
              class="reportVal"
            >
              <span class="val">{{ item.val }}</span>
              <span class="name">{{ item.name }}</span>
            </div>
          </div>
        </el-card>
        <el-card
          v-if="crud.props.searchToggle"
          class="box-card1"
          shadow="always"
          style="margin-top: 5px"
        >
          <el-form
            ref="query"
            :inline="true"
            size="small"
            label-width="100px"
          >
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-8 col-12">
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('view.field.plan.lotNum') + ':'">
                    <!-- 订单号 -->
                    <el-input
                      v-model="query.lot_num"
                      clearable
                      size="small"
                    />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.vie.pileStatus') + ':'">
                    <!-- 打包状态 -->
                    <el-select v-model="query.pile_status" clearable filterable>
                      <el-option
                        v-for="item in [{id:'0',label:'OK',value:'OK'},{id:'1',label:'NG',value:'NG'}]"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-2 col-12">
                <el-form-item>
                  <rrOperation />
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card
          class="box-card1"
          shadow="always"
          style="margin-top: 5px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!--工具栏-->
              <crudOperation
                show=""
                :permission="permission"
              />
              <el-table
                ref="table"
                v-loading="crud.loading"
                border
                size="small"
                :data="crud.data"
                style="width: 100%"
                :cell-style="crud.cellStyle"
                :height="height"
                :highlight-current-row="true"
                @header-dragend="crud.tableHeaderDragend()"
                @selection-change="crud.selectionChangeHandler"
              >
                <!-- <el-table-column type="selection" width="55" /> -->
                <!-- 时间 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  :label="$t('lang_pack.vie.time')"
                  width="140"
                  align="center"
                />
                <!-- 订单号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="lot_num"
                  :label="$t('view.field.plan.lotNum')"
                  width="120"
                  align="center"
                />
                <!-- 打包顺序 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="finish_lable_count"
                  :label="$t('lang_pack.vie.packOrder')"
                  width="130"
                  align="center"
                />
                <!-- 打包条码 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_barcode"
                  :label="$t('lang_pack.vie.packCode')"
                  width="120"
                  align="center"
                />
                <!-- 客户编码 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="custom_barcode"
                  :label="$t('lang_pack.vie.customCode1')"
                  width="120"
                  align="center"
                />
                <!-- 订单中打包序号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_index"
                  :label="$t('lang_pack.vie.pileIndex')"
                  width="130"
                  align="center"
                />
                <!-- 打包中SET总数 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_count"
                  :label="$t('lang_pack.vie.arrayCount')"
                  width="120"
                  align="center"
                />
                <!-- 料号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="model_type"
                  :label="$t('lang_pack.vie.partNum')"
                  width="130"
                  align="center"
                />
                <!-- 打包状态 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_status"
                  :label="$t('lang_pack.vie.pileStatus')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    <el-button
                      size="small"
                      type="primary"
                      :class="scope.row.pile_status == 'OK' ? 'btnGreen' : 'btnRed'"
                    >
                      {{ scope.row.pile_status }}
                    </el-button>
                  </template>
                </el-table-column>
                <!-- 打包验证NG代码 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_ng_code"
                  :label="$t('lang_pack.vie.pilengCode')"
                  width="120"
                  align="center"
                />
                <!-- 打包验证NG描述 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_ng_msg"
                  :label="$t('lang_pack.vie.pile_ng_msg')"
                  width="120"
                  align="center"
                />
                <!-- PC计划SET集合 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pc_array_list"
                  :label="$t('lang_pack.vie.pcArraylist')"
                  width="120"
                  align="center"
                />
                <!-- PLC反馈SET集合 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="plc_array_list"
                  :label="$t('lang_pack.vie.plcArraylist')"
                  width="120"
                  align="center"
                />
                <!-- 是否尾箱 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="trunk_flag"
                  :label="$t('lang_pack.vie.IsItATrunk')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.trunk_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- 打包重量 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile weight"
                  :label="$t('lang_pack.vie.pileWeight')"
                  width="120"
                  align="center"
                />
                <!-- 打包人员 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile user"
                  :label="$t('lang_pack.vie.pileUser')"
                  width="120"
                  align="center"
                />
                <!-- 有效标识 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="enable_flag"
                  :label="$t('lang_pack.commonPage.validIdentificationt')"
                  width="130"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- 是否解绑 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="unbind_flag"
                  :label="$t('lang_pack.vie.unbindFlag')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.unbind_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- 解绑人 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="unbind_user"
                  :label="$t('lang_pack.vie.unbindUser')"
                  width="120"
                  align="center"
                />
                <!-- 解绑时间 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="unbind_time"
                  :label="$t('lang_pack.vie.unbindTime')"
                  width="120"
                  align="center"
                />
                <el-table-column
                  :label="$t('lang_pack.commonPage.operate')"
                  align="center"
                  fixed="left"
                >
                  <!-- 操作 -->
                  <template slot-scope="scope">
                    <el-button
                      v-if="canUnbind(scope.row)"
                      slot="reference"
                      type="text"
                      size="small"
                      @click="unbind(scope.row)"
                    >
                      {{ $t('lang_pack.vie.unbind') }}
                    </el-button> <!-- 解绑 -->
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      @click="viewsDetails(scope.row)"
                    >{{ $t('lang_pack.vie.viewDetails')
                    }}</el-button> <!-- 查看明细 -->
                  </template>
                </el-table-column>
              </el-table>
              <!--分页组件-->
              <pagination />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <detail
      v-if="tableDialogVisible"
      ref="tableDialog"
      :pile_barcode="pile_barcode"
      @ok="tableDialogVisible = false"
    />
  </div>
</template>
<script>
import detail from './modules/pileIndex'
import Cookies from 'js-cookie'
import crudPile from '@/api/pack/pile'
import crudPileReport from '@/api/pack/pileReport'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  model_type: ''
}
export default {
  name: 'PILEREPORTINDEX',
  components: { crudOperation, rrOperation, pagination, detail },
  cruds() {
    return CRUD({
      title: 'PILE记录表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'pile_id',
      // 排序
      sort: ['pile_id desc'],
      // CRUD Method
      crudMethod: { ...crudPileReport },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  data() {
    return {
      height: document.documentElement.clientHeight - 348,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      reportData: [
        {
          val: '100%',
          name: this.$t('lang_pack.vie.dailyPassRate'),
          key: 'qualificationRate'
        },
        { val: 0, name: this.$t('lang_pack.vie.dailyOKQuantity'), key: 'ok' },
        { val: 0, name: this.$t('lang_pack.vie.dailyNGQuantity'), key: 'ng' },
        { val: 0, name: this.$t('lang_pack.vie.dailyHourProduct'), key: 'max' },
        {
          val: 0,
          name: this.$t('lang_pack.vie.dailyHourProductVal'),
          key: 'min'
        },
        {
          val: 0,
          name: this.$t('lang_pack.vie.dailyHourProductAve'),
          key: 'avg'
        }
      ],
      pile_barcode: '',
      tableDialogVisible: false,
      timer: null
    }
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 348
    }
    this.getPackIndex()
    this.timer = setInterval(() => {
      this.getPackIndex()
      this.crud.refresh()
    }, 1000 * 30)
  },
  methods: {
    getPackIndex() {
      crudPileReport
        .packIndex({})
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const obj = defaultQuery.data
            this.reportData.forEach((item) => {
              const objKey = item.key
              if (obj[objKey]) {
                item.val =
                  objKey === 'qualificationRate'
                    ? `${obj[objKey]}%`
                    : obj[objKey]
              }
            })
          } else {
            this.reportData.forEach((item) => {
              item.val = item.key === 'qualificationRate' ? '100%' : 0
            })
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          this.reportData.forEach((item) => {
            item.val = item.key === 'qualificationRate' ? '100%' : 0
          })
          this.$message({
            message: err.msg,
            type: 'error'
          })
        })
    },
    viewsDetails(row) {
      this.pile_barcode = row.pile_barcode
      this.tableDialogVisible = true
      this.$nextTick(() => {
        this.$refs.tableDialog.dialogVisible = true
      })
    },
    canUnbind(item) {
      return item.enable_flag === 'Y' && item.unbind_flag !== 'Y' && item.pile_barcode != null && item.pile_barcode !== ''
    },
    unbind(item) {
      this.$confirm(this.$t('lang_pack.vie.confirmUnbindAllSetTips'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudPile
            .unbind({
              user_name: Cookies.get('userName'),
              pile_barcode: item.pile_barcode,
              unbind_way: this.$t('lang_pack.vie.manual')
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
                this.crud.toQuery()
              } else {
                this.$message({
                  message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + res.msg,
                  type: 'error'
                })
              }
            })
            .catch((ex) => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style scoped lang="less">
.app-container {
  .analysisValue {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .reportVal {
      display: flex;
      flex-direction: column;
      text-align: center;
      width: 10%;
      background: #4874cb;
      border-radius: 8px;
      color: #fff;

      .val {
        font-size: 18px;
        margin: 10px 0;
      }

      .name {
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
  }
  .btnGreen {
    background-color: #0d0 !important;
    border: none !important;
  }

  .btnRed {
    background-color: #ee1216 !important;
    border: none !important;
  }
}
</style>
