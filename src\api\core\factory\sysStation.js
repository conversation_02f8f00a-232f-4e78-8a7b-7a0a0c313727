import request from '@/utils/request'

// 查询产线工位
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryStationSel',
    method: 'post',
    data
  })
}
// 新增产线工位
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryStationIns',
    method: 'post',
    data
  })
}
// 修改产线工位
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryStationUpd',
    method: 'post',
    data
  })
}
// 删除产线工位
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryStationDel',
    method: 'post',
    data
  })
}

// 查询产线工位LOV
export function lovStation(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryStationLov',
    method: 'post',
    data
  })
}
// 工位信息查询-产线工位树结构
export function selTree(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryStationTree',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, lovStation, selTree }
