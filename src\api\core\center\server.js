import request from '@/utils/request'

// 查询服务
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerSel',
    method: 'post',
    data
  })
}
// 新增服务
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerIns',
    method: 'post',
    data
  })
}
// 修改服务
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerUpd',
    method: 'post',
    data
  })
}
// 删除服务
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerDel',
    method: 'post',
    data
  })
}

// 查询服务LOV
export function lovServer(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerLov',
    method: 'post',
    data
  })
}

// 查询Server&Cell信息
export function selCoreServerCell(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerCellSel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, lovServer, selCoreServerCell }

