import request from '@/utils/request'

// 全柴物料查询
export function selMaterialWare(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcMaterialWarehouseBoardData',
    method: 'post',
    data
  })
}
// andon大屏查询
export function selQcWorkshopAndonBoard(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcWorkshopAndonBoardData',
    method: 'post',
    data
  })
}
// andon大屏节拍/产量数据查询
export function selQcWorkshopBoardBeat(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcWorkshopBoardBeat',
    method: 'post',
    data
  })
}
// andon大屏节拍/产量数据查询
export function selQcWorkshopBoardOutput(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcWorkshopBoardOutput',
    method: 'post',
    data
  })
}
// andon大屏右侧数据查询
export function selQcBottleneckProcess(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcWorkshopBoardBottleneckProcess',
    method: 'post',
    data
  })
}
// 理论节拍时间查询
export function selQcBeatTime(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysParameterSel',
    method: 'post',
    data
  })
}

export default { selMaterialWare, selQcWorkshopAndonBoard, selQcWorkshopBoardBeat, selQcWorkshopBoardOutput, selQcBottleneckProcess, selQcBeatTime }
