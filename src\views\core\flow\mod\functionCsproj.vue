<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modcsproj.csprojCode')">
                <!-- 项目编码： -->
                <el-input v-model="query.project_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modcsproj.csprojdes')">
                <!-- 项目描述： -->
                <el-input v-model="query.project_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="450px"
      >
        <el-form
          ref="form"
          class="el-form-wrap el-form-column"
          :model="form"
          :rules="rules"
          size="small"
          label-width="100px"
          :inline="true"
        >
          <el-form-item :label="$t('lang_pack.modcsproj.csprojCodet')" prop="csproj_code">
            <!-- 项目编码 -->
            <el-input v-model="form.csproj_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojdest')" prop="csproj_des">
            <!-- 项目描述 -->
            <el-input v-model="form.csproj_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojtargetframework')" prop="csproj_targetframework">
            <!-- 目标框架 -->
            <el-input v-model="form.csproj_targetframework" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojreference')" prop="csproj_reference">
            <!-- 项目引用 -->
            <el-input v-model="form.csproj_reference" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojversion')" prop="csproj_version">
            <!-- 项目版本 -->
            <el-input v-model="form.csproj_version" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojversiondes')" prop="csproj_version_des">
            <!-- 版本说明 -->
            <el-input v-model="form.csproj_version_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojpathWin')" prop="csproj_path_windows">
            <!-- 文件路径 -->
            <el-input v-model="form.csproj_path_windows" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.modcsproj.csprojpathLinux')" prop="csproj_path_linux">
            <!-- 文件路径 -->
            <el-input v-model="form.csproj_path_linux" />
          </el-form-item>
        </el-form>
        <div style="text-align: center" class="wrapButtonM">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel')
          }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="csproj_id" label="id" />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_code"
              :label="$t('lang_pack.modcsproj.csprojCodet')"
            />
            <!-- 项目编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_des"
              :label="$t('lang_pack.modcsproj.csprojdest')"
            />
            <!-- 项目描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_targetframework"
              :label="$t('lang_pack.modcsproj.csprojtargetframework')"
            />
            <!-- 目标框架 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_reference"
              :label="$t('lang_pack.modcsproj.csprojreference')"
            />
            <!-- 项目引用 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_version"
              :label="$t('lang_pack.modcsproj.csprojversion')"
            />
            <!-- 项目版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_version_des"
              :label="$t('lang_pack.modcsproj.csprojversiondes')"
            />
            <!-- 版本说明 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_path_windows"
              :label="$t('lang_pack.modcsproj.csprojpathWin')"
            />
            <!-- 文件路径Windows -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="csproj_path_linux"
              :label="$t('lang_pack.modcsproj.csprojpathLinux')"
            />
            <!-- 文件路径Linux -->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                  <template slot="right">
                    <el-button slot="reference" type="text" size="small" @click="handleRelese(scope.row)">编译</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudFlowModCsproj from '@/api/core/flow/rcsFlowModCsproj'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  csproj_id: 0,
  csproj_code: '',
  csproj_des: '',
  csproj_targetframework: 'netcoreapp2.1',
  csproj_reference: 'JhongRcs.Core.dll;Newtonsoft.Json.dll',
  csproj_version: 'v1.0.0',
  csproj_version_des: '',
  csproj_path_windows: '',
  csproj_path_linux: ''
}
export default {
  name: 'RCSFLOWMODCSPROJ',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '项目',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'csproj_id',
      // 排序
      sort: ['csproj_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowModCsproj },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'rcs_flow_mod_csproj:add'],
        edit: ['admin', 'rcs_flow_mod_csproj:edit'],
        del: ['admin', 'rcs_flow_mod_csproj:del'],
        down: ['admin', 'rcs_flow_mod_csproj:down']
      },
      rules: {
        csproj_code: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
        csproj_targetframework: [{ required: true, message: '请输入目标框架', trigger: 'blur' }],
        csproj_des: [{ required: true, message: '请输入项目描述', trigger: 'blur' }]
      },
      code_content: '',
      currentRow: [],
      codeEditDialogVisible: false
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 300
    }
  },
  created: function() { },
  methods: {
    opendCodeEdit(row) {
      this.currentRow = row
      this.codeEditDialogVisible = true
    },
    handleRelese(row) {
      const releseData = {
        user_name: Cookies.get('userName'),
        csproj_id: row.csproj_id,
        csproj_code: row.csproj_code,
        csproj_targetframework: row.csproj_targetframework,
        csproj_reference: row.csproj_reference,
        csproj_path_windows: row.csproj_path_windows,
        csproj_path_linux: row.csproj_path_linux
      }
      crudFlowModCsproj.release(releseData)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.msg === '') {
            this.$message({
              message: '编译成功',
              type: 'success'
            })
          } else {
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.$message({
            message: '编译异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.wrapButtonM {
  margin: 40px 0;
}

::v-deep .CodeMirror-vscrollbar {
  overflow-x: inherit !important;
  overflow-y: inherit !important;
}

.CodeMirror-vscrollbar::-webkit-scrollbar {
  width: 0px;
  height: 4px;
  background-color: #ebeef5;
}

.CodeMirror-vscrollbar::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f6f9ff;
}

.CodeMirror-vscrollbar::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}

.lzg-dialog-height {
  width: 70%;
  top: 7%;
  right: 15%;
  bottom: 7%;
  left: 15%;
  transform: translate(0%, 0%);
}

.code-dialog-height .el-dialog__body {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-y: auto;
}

.code-dialog-content {
  top: 60px;
  right: 5px;
  bottom: 5px;
  left: 5px;
  overflow-y: auto;
  position: absolute;
}
</style>
