import request from '@/utils/request'

const context = 'aisEsbWeb/pack/project/avary'

export function sel(data) {
  return request({
    url: context + '/PackFmodRecipeSel',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: context + '/PackFmodRecipeUpd',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: context + '/PackFmodRecipeIns',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: context + '/PackFmodRecipeDel',
    method: 'post',
    data
  })
}

export default { add, del, edit, sel }
