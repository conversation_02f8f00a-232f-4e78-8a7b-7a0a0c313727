<template>
  <div style="margin-top:10px;">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工序号/描述：">
                <el-input v-model="query.proceduce" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="155px" :inline="true">
          <el-form-item label="工序号" prop="proceduce_code">
            <el-input v-model="form.proceduce_code" />
          </el-form-item>
          <el-form-item label="工序描述" prop="proceduce_des">
            <el-input v-model="form.proceduce_des" />
          </el-form-item>
          <el-form-item label="工序类型" prop="proceduce_type">
            <el-select v-model="form.proceduce_type" clearable>
              <el-option v-for="item in dict.PROCEDUCE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="工序属性" prop="proceduce_attr">
            <el-input v-model="form.proceduce_attr" />
          </el-form-item>
          <el-form-item label="排序" prop="work_order_by">
            <el-input v-model.number="form.work_order_by" />
          </el-form-item>
          <el-form-item label="数据转移标识" prop="data_move_flag">
            <el-select v-model="form.data_move_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否允许跳步" prop="allow_jump_flag">
            <el-select v-model="form.allow_jump_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="工序显示照片名称" prop="pic_name">
            <el-input v-model="form.pic_name" />
          </el-form-item>
          <el-form-item label="工序显示照片路径" prop="pic_path">
            <el-input v-model="form.pic_path" readonly="readonly">
              <el-button slot="append" icon="el-icon-upload" @click="handleUpload" />
            </el-input>
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="false" :title="uploadDrawerTitle" :visible.sync="uploadDrawerVisbleSync" size="390px" @closed="handleUploadDrawerClose">
        <el-upload
          ref="upload"
          name="file"
          :multiple="false"
          action=""
          drag=""
          :limit="uploadLimit"
          :on-change="handleUploadOnChange"
          :http-request="handleUploadHttpRequest"
          :accept="uploadAccept"
          :auto-upload="false"
          :file-list="fileList"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
        </el-upload>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="handleUploadDrawerCancel">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleUploadFile">上传</el-button>
        </div>
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="true" :title="detailDrawerTitle" :visible.sync="detailDrawerVisible" size="70%" @closed="detailDrawerClosed()">
        <quality v-if="qualityVisible" ref="quality" :proceduce_id="currentId" />
        <bom v-if="bomVisible" ref="bom" :proceduce_id="currentId" />
        <step v-if="stepVisible" ref="step" :proceduce_id="currentId" />
        <sop v-if="sopVisible" ref="sop" :proceduce_id="currentId" />
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="工序ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.proceduce_id }}</el-descriptions-item>
                  <el-descriptions-item label="工序号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.proceduce_code }}</el-descriptions-item>
                  <el-descriptions-item label="工序描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.proceduce_des }}</el-descriptions-item>
                  <el-descriptions-item label="工序类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.proceduce_type }}</el-descriptions-item>
                  <el-descriptions-item label="工序属性" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.proceduce_attr }}</el-descriptions-item>
                  <el-descriptions-item label="排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.work_order_by }}</el-descriptions-item>
                  <el-descriptions-item label="数据转移标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.data_move_flag }}</el-descriptions-item>
                  <el-descriptions-item label="是否允许跳步" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.allow_jump_flag }}</el-descriptions-item>
                  <el-descriptions-item label="工序显示照片名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pic_name }}</el-descriptions-item>
                  <el-descriptions-item label="工序显示照片路径" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pic_path }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="proceduce_code" min-width="100" label="工序号" />
            <el-table-column  :show-overflow-tooltip="true" prop="proceduce_des" min-width="100" label="工序描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="proceduce_type" min-width="100" label="工序类型" />
            <el-table-column  :show-overflow-tooltip="true" prop="proceduce_attr" min-width="100" label="工序属性" />
            <el-table-column  :show-overflow-tooltip="true" prop="work_order_by" min-width="100" label="排序" />
            <el-table-column  :show-overflow-tooltip="true" prop="data_move_flag" min-width="100" label="数据转移标识" />
            <el-table-column  :show-overflow-tooltip="true" prop="allow_jump_flag" min-width="100" label="是否允许跳步" />
            <el-table-column  :show-overflow-tooltip="true" width="260" label="物料/质量/工步/Sop" align="center">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row, 'bom')">物料</el-tag>
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row, 'quality')">质量</el-tag>
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row, 'step')">工步</el-tag>
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row, 'sop')">Sop</el-tag>
              </template>
            </el-table-column>
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'
import crudrecipeCrPdure from '@/api/mes/core/recipeCrPdure'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import quality from '@/views/mes/core/craftRoute/quality'
import bom from '@/views/mes/core/craftRoute/bom'
import step from '@/views/mes/core/craftRoute/step'
import sop from '@/views/mes/core/craftRoute/sop'
const defaultForm = {
  proceduce_id: '',
  craft_route_id: '',
  proceduce_code: '',
  proceduce_des: '',
  proceduce_type: '',
  proceduce_attr: '',
  work_order_by: '',
  data_move_flag: 'N',
  allow_jump_flag: 'N',
  pic_name: '',
  pic_path: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MES_RECIPE_CR_PDURE',
  components: { crudOperation, rrOperation, udOperation, pagination, quality, bom, step, sop },
  cruds() {
    return CRUD({
      title: '工序信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'proceduce_id',
      // 排序
      sort: ['proceduce_id asc'],
      // CRUD Method
      crudMethod: { ...crudrecipeCrPdure },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  }, // 数据字典
  dicts: ['ENABLE_FLAG', 'PROCEDUCE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    craft_route_id: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_cr_pdure:add'],
        edit: ['admin', 'mes_recipe_cr_pdure:edit'],
        del: ['admin', 'mes_recipe_cr_pdure:del'],
        down: ['admin', 'mes_recipe_cr_pdure:down']
      },
      rules: {
        proceduce_code: [{ required: true, message: '请输入工序号', trigger: 'blur' }],
        proceduce_des: [{ required: true, message: '请输入工序描述', trigger: 'blur' }],
        proceduce_type: [{ required: true, message: '请选择工序类型', trigger: 'blur' }],
        work_order_by: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      currentId: 0,
      detailDrawerTitle: '',
      detailDrawerVisible: false,
      qualityVisible: false,
      bomVisible: false,
      stepVisible: false,
      sopVisible: false,
      // 文件上传
      uploadDrawerTitle: '文件上传',
      uploadDrawerVisbleSync: false,
      uploadLimit: 1,
      uploadAccept: '.png,.jpg',
      fileList: []
    }
  },
  watch: {
    craft_route_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.craft_route_id = this.craft_route_id
        defaultForm.craft_route_id = this.craft_route_id
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.craft_route_id = this.craft_route_id
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将工序为【' + data.proceduce_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudrecipeCrPdure
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              proceduce_id: data.proceduce_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    opendDetail(row, type) {
      this.currentId = row.proceduce_id
      if (type === 'bom') {
        this.detailDrawerTitle = '物料信息'
        this.bomVisible = true
      } else if (type === 'quality') {
        this.detailDrawerTitle = '质量信息'
        this.qualityVisible = true
      } else if (type === 'step') {
        this.detailDrawerTitle = '工步信息'
        this.stepVisible = true
      } else if (type === 'sop') {
        this.detailDrawerTitle = 'Sop'
        this.sopVisible = true
      }
      this.detailDrawerVisible = true
    },
    detailDrawerClosed() {
      this.detailDrawerTitle = ''
      this.bomVisible = false
      this.qualityVisible = false
      this.stepVisible = false
      this.sopVisible = false
    },
    handleUploadDrawerClose() {
      this.fileList = []
    },
    handleUpload() {
      this.uploadDrawerVisbleSync = true
    },
    handleUploadDrawerCancel() {
      this.uploadDrawerVisbleSync = false
    },
    // 导入文件时将文件存入数组中
    handleUploadOnChange(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequest(file) {
      this.fileData.append('file', file.file)
    },
    // 处理上传文件
    handleUploadFile() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      this.fileData = new FormData()
      this.fileData.append('file_path', '/u01/ais/upload/pdure')
      this.$refs.upload.submit()

      // 配置路径
      var method = 'core/file/CoreFileUpload'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method

      const loading = this.$loading({
        lock: true,
        text: '上传文件处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            const fileInfo = JSON.parse(defaultQuery.data.result)
            this.form.pic_name = fileInfo.file_name
            this.form.pic_path = fileInfo.file_dir_path
            this.uploadDrawerVisbleSync = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
        .catch(er => {
          loading.close()
          this.$message({
            message: '上传文件异常：' + er,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
