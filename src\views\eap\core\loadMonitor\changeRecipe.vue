<template>
  <div>
    <div style="margin-top:10px;text-align:center">
      <el-button type="primary" @click="handleSendInfo">确 定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {

    }
  },
  mounted: function() {

  },
  created: function() {
  },
  methods: {
    handleSendInfo() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebChangeRecipeFinish,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebChangeRecipeFinish.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
