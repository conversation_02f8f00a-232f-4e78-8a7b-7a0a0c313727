<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-9 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="车辆VIN：">  <!-- 车辆VIN： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-5 col-12">
              <el-form-item label="时间范围：">  <!-- 时间范围： -->
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" label="合格标志：">
                <el-select v-model="query.dx_status" clearable>
                  <el-option v-for="item in dict.QUALIFIED_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-3 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" border :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column  :show-overflow-tooltip="true" prop="vin" label="VIN号" width="180" />  <!-- 车辆VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="shebeibh" label="设备编号" width="180" />  <!-- 写入的VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="dh" label="图号" />  <!-- CALID -->
            <el-table-column  :show-overflow-tooltip="true" prop="shedingjzl" label="设定加注量" />  <!-- CVN -->
            <el-table-column  :show-overflow-tooltip="true" prop="shijijzl" label="实际加注量" /> <!-- 检测结果 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shedingyl" label="设定压力" />  <!-- 不合格原因 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shijiyl" label="实际压力" />  <!-- 检测日期 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shedingczkz" label="设定粗抽真空值" />  <!-- CALID1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shijiczkz" label="实际粗抽真空值" />  <!-- CVN1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shedingxzkz" label="设定细抽真空值" />  <!-- CALID2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shijixzkz" label="实际细抽真空值" />  <!-- CVN2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shedingzkjl" label="设定真空检漏" />  <!-- OBD类型 -->
            <el-table-column  :show-overflow-tooltip="true" prop="shijizkjl" label="实际真空检漏" />  <!-- EngineID -->
            <el-table-column  :show-overflow-tooltip="true" prop="shedingzyjl" label="设定正压检漏" />  <!-- RearID -->
            <el-table-column  :show-overflow-tooltip="true" prop="shijizyjl" label="实际正压检漏" />  <!-- OtherID -->
            <el-table-column  :show-overflow-tooltip="true" prop="zuoyesj" label="作业时间" />  <!-- OtherID -->
            <el-table-column  :show-overflow-tooltip="true" prop="jiazhujp" label="加注节拍" />  <!-- OtherID -->
            <el-table-column  :show-overflow-tooltip="true" prop="gongyicsly" label="工艺参数来源" />  <!-- OtherID -->
            <el-table-column  :show-overflow-tooltip="true" prop="panding" label="加注结果判定" />  <!-- OtherID -->
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import syscheckFilldata from '@/api/pmc/quality/sysFill'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
export default {
  name: 'sysFill',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '加注',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'vin',
      // // 排序
      sort: ['vin asc'],
      // CRUD Method
      crudMethod: { ...syscheckFilldata },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  // 数据字典
  dicts: ['QUALIFIED_FLAG', 'DX_NG_CODE'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      }
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {

  }
}
</script>
<style lang="less" scoped>
::v-deep .marginL{
  margin-left: 10px;
}
::v-deep .el-button--primary {
    color: #fff;
    background-color: #1473c5;
    border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
    background: #438fd1;
    border-color: #438fd1;
    color: #fff;
}
.labelIline{
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label{
    white-space: nowrap;
  }
}
</style>
