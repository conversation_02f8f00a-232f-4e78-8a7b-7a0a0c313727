<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-9 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="车辆VIN：">  <!-- 车辆VIN： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item label="时间范围：">  <!-- 时间范围： -->
                <div class="block">
                  <el-date-picker
                    v-model="query.quality_data"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-3 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="车辆VIN" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.vin }}</el-descriptions-item>
                  <el-descriptions-item label="CALID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.calid }}</el-descriptions-item>
                  <el-descriptions-item label="CVN" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cvn }}</el-descriptions-item>
                  <el-descriptions-item label="检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.testresult }}</el-descriptions-item>
                  <el-descriptions-item label="不合格原因" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.failreason }}</el-descriptions-item>
                  <el-descriptions-item label="检测日期" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.testtime }}</el-descriptions-item>
                  <el-descriptions-item label="CALID1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.calid1 }}</el-descriptions-item>
                  <el-descriptions-item label="CVN1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cvn1 }}</el-descriptions-item>
                  <el-descriptions-item label="CALID2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.calid2 }}</el-descriptions-item>
                  <el-descriptions-item label="CVN2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cvn2 }}</el-descriptions-item>
                  <el-descriptions-item label="OBD类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.obdtype }}</el-descriptions-item>
                  <el-descriptions-item label="EngineID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.engineid }}</el-descriptions-item>
                  <el-descriptions-item label="RearID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rearid }}</el-descriptions-item>
                  <el-descriptions-item label="OtherID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.otherid }}</el-descriptions-item>
                  <el-descriptions-item label="通讯是否成功" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.connectresult }}</el-descriptions-item>

                  <el-descriptions-item label="外廓长" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.outlength }}</el-descriptions-item>
                  <el-descriptions-item label="外廓宽" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.outwidth }}</el-descriptions-item>
                  <el-descriptions-item label="外廓高" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.outhigh }}</el-descriptions-item>
                  <el-descriptions-item label="外廓尺寸判定" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.wai_judge }}</el-descriptions-item>
                  <el-descriptions-item label="车速表_实测值（km/h）_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.speed_res }}</el-descriptions-item>
                  <el-descriptions-item label="车速表_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.speed_judge }}</el-descriptions-item>

                  <el-descriptions-item label="一轴左重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_1l_res }}</el-descriptions-item>
                  <el-descriptions-item label="一轴右重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_1r_res }}</el-descriptions-item>
                  <el-descriptions-item label="一轴重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_1_res }}</el-descriptions-item>
                  <el-descriptions-item label="二轴左重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_2l_res }}</el-descriptions-item>
                  <el-descriptions-item label="二轴右重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_2r_res }}</el-descriptions-item>
                  <el-descriptions-item label="二轴重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_2_res }}</el-descriptions-item>
                  <el-descriptions-item label="三轴左重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_3l_res }}</el-descriptions-item>
                  <el-descriptions-item label="三轴右重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_3r_res }}</el-descriptions-item>
                  <el-descriptions-item label="三轴重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_3_res }}</el-descriptions-item>
                  <el-descriptions-item label="四轴左重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_4l_res }}</el-descriptions-item>
                  <el-descriptions-item label="四轴右重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_4r_res }}</el-descriptions-item>
                  <el-descriptions-item label="四轴重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_4_res }}</el-descriptions-item>
                  <el-descriptions-item label="整车重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_t_res }}</el-descriptions-item>
                  <el-descriptions-item label="整备质量检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_zbzl_res }}</el-descriptions-item>
                  <el-descriptions-item label="整备质量判定" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_zbzl_judge }}</el-descriptions-item>

                  <el-descriptions-item label="制动_一轴_左制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_1l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_右制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_1r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_1_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_左平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_1l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_右平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_1r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_差_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_1_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_左阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_1l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_右阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_1r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_左制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_2l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_右制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_2r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_2_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_左平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_右平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_差_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_左阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_2l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_右阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_2r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_左制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_3l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_右制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_3r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_3_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_左平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_右平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_差_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_左阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_3l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_右阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_3r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_左制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_4l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_右制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_4r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_4_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_左平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_4l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_右平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_4r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_差_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_4_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_左阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_4l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_右阻滞_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_4r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_手刹_左驻车_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_park_l_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_手刹_右驻车_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_park_r_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_手刹_手刹力_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_park_t_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_整车_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_t_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_1_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_差_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_1_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_左阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_1l_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_一轴_右阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_1r_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_2_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_差_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_左阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_2l_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_二轴_右阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_2r_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_3_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_差_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_左阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_3l_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_三轴_右阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_3r_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_4_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_差_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_4_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_左阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_4l_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_四轴_右阻滞_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_drg_4r_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_手刹_驻车_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_park_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_整车_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_t_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_t_judge }}</el-descriptions-item>
                  <el-descriptions-item label="ABS_一轴检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.abs_f_judge }}</el-descriptions-item>
                  <el-descriptions-item label="ABS_二轴检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.abs_r_judge }}</el-descriptions-item>
                  <el-descriptions-item label="ABS_三轴检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.abs_m_judge }}</el-descriptions-item>
                  <el-descriptions-item label="ABS_四轴检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.abs_4_judge }}</el-descriptions-item>
                  <el-descriptions-item label="ABS检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.abs_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴左重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_2ls_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴右重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_2rs_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴左重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_3ls_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴右重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.aw_3rs_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_左制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_2ls_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_右制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_2rs_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_2s_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_2s_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_左平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2ls_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_右平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2rs_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_差_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2s_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_二轴_差_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_2s_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_左制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_3ls_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_右制动_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_max_3rs_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_和_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_3s_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_和_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_sum_3s_judge }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_左平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3ls_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_右平衡_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3rs_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_差_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3s_res }}</el-descriptions-item>
                  <el-descriptions-item label="制动_加载_三轴_差_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.b_dif_3s_judge }}</el-descriptions-item>

                  <el-descriptions-item label="前照_左灯_远光光强_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_i_lo_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_远光垂直_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_lo_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_远光水平_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_lo_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_近光垂直_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_lo_low_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_近光水平_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_lo_low_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_车灯灯高_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_high_lo_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_远光光强_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_i_ro_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_远光垂直_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_ro_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_远光水平_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_ro_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_近光垂直_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_ro_low_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_近光水平_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_ro_low_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_车灯灯高_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_high_ro_far_res }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_远光光强_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_i_lo_far_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_远光垂直_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_lo_far_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_远光水平_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_lo_far_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_近光垂直_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_lo_low_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_左灯_近光水平_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_lo_low_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_远光光强_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_i_ro_far_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_远光垂直_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_ro_far_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_远光水平_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_ro_far_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_近光垂直_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_v_ro_low_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照_右灯_近光水平_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_h_ro_low_judge }}</el-descriptions-item>
                  <el-descriptions-item label="左灯检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_left_judge }}</el-descriptions-item>
                  <el-descriptions-item label="右灯检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_right_judge }}</el-descriptions-item>
                  <el-descriptions-item label="前照灯总判定结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lt_judge }}</el-descriptions-item>

                  <el-descriptions-item label="喇叭声级_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.noise_res }}</el-descriptions-item>
                  <el-descriptions-item label="喇叭声级_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.noise_judge }}</el-descriptions-item>

                  <el-descriptions-item label="侧滑_检测数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.sslip_res }}</el-descriptions-item>
                  <el-descriptions-item label="侧滑2轴_检测数据(双前桥的车)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.sslip_res2 }}</el-descriptions-item>
                  <el-descriptions-item label="侧滑_检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.sslip_judge }}</el-descriptions-item>

                  <el-descriptions-item label="整体检测时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.test_duration }}</el-descriptions-item>
                  <el-descriptions-item label="整体检测次数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.test_times }}</el-descriptions-item>
                  <el-descriptions-item label="整体检测结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.test_judge }}</el-descriptions-item>

                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="vin" label="VIN号" />  <!-- 车辆VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="testresult" label="OBD设备检测" />  <!-- OBD设备检测 -->
            <el-table-column  :show-overflow-tooltip="true" prop="wai_judge" label="外廓尺寸测量设备" />  <!-- 外廓尺寸测量设备 -->
            <el-table-column  :show-overflow-tooltip="true" prop="speed_judge" label="速度校验" />  <!-- 速度校验 -->
            <el-table-column  :show-overflow-tooltip="true" prop="b_zbzl_judge" label="地泵" />  <!-- 地泵 -->
            <el-table-column  :show-overflow-tooltip="true" prop="b_sum_t_judge" label="制动" />  <!-- 制动 -->
            <el-table-column  :show-overflow-tooltip="true" prop="lt_judge" label="大灯检测仪" />  <!-- 大灯检测仪 -->
            <el-table-column  :show-overflow-tooltip="true" prop="noise_judge" label="声级" />  <!-- 声级 -->
            <el-table-column  :show-overflow-tooltip="true" prop="sslip_judge" label="侧滑" />  <!-- 侧滑 -->
            <el-table-column  :show-overflow-tooltip="true" prop="test_judge" label="整体检测" />  <!-- 整体检测 -->
            <el-table-column  :show-overflow-tooltip="true" prop="test_duration" label="整体检测时间" width="180" />  <!-- 整体检测时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="test_times" label="整体检测次数" />  <!-- 整体检测次数 -->
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import getTestingResult from '@/api/pmc/quality/sysTestingResult'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
const defaultForm = {

}
export default {
  name: 'playTime',
  components: { crudOperation, rrOperation, udOperation },
  cruds() {
    return CRUD({
      title: '测量结果',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'vin',
      // // 排序
      sort: ['vin asc'],
      // CRUD Method
      crudMethod: { ...getTestingResult },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据字典
  dicts: ['ENGRAVING_TYPE'],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      }
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
    color: #fff;
    background-color: #1473c5;
    border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
    background: #438fd1;
    border-color: #438fd1;
    color: #fff;
}
.labelIline{
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label{
    white-space: nowrap;
  }
}
::v-deep .marginL{
  margin-left: 10px;
}
</style>
