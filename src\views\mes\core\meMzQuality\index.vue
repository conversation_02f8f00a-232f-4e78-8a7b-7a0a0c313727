<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="120px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width: 100%"
                    :picker-options="pickerOptions1"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="模组码：">
                <el-input v-model="query.mz_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="电芯码：">
                <el-input v-model="query.dx_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位：">
                <el-select v-model="query.station_code" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="上传完成标志：">
                <el-select v-model="query.up_flag" clearable>
                  <el-option v-for="item in ['Y','N']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="订单号：">
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="机型：">
                <el-select v-model="query.small_model_type" filterable clearable size="small">
                  <el-option v-for="item in smallModelTypeData" :key="item.small_model_type" :label="item.small_model_type + ' ' + item.main_material_des" :value="item.small_model_type" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="合格标志：">
                <el-select v-model="query.mz_status" clearable>
                  <el-option v-for="item in dict.QUALIFIED_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="NG类型：">
                <el-select v-model="query.ng_code" multiple clearable>
                  <el-option v-for="item in dict.MZ_NG_CODE" :key="item.id" :label="item.value+' '+item.label" :value="parseInt(item.value)" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="排废曹号：">
                <el-select v-model="query.ng_rack" clearable>
                  <el-option v-for="item in [1,2,3,4,5,6]" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="上传NG标志：">
                <el-select v-model="query.up_ng_code" clearable>
                  <el-option v-for="item in [{label:'上传成功',value:'0'},{label:'上传失败',value:'-1'}]" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-close" plain round :disabled="crud.selections.length <=0" @click="unbind">
            解绑
          </el-button>
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-upload2" plain round :disabled="crud.selections.length <=0" @click="reupload">
            重新上传
          </el-button>
        </template>
        <template slot="group_left">
          <el-button v-permission="permission.down" :loading="crud.downloadLoading" :disabled="!crud.data.length" size="small" icon="el-icon-download" @click="doExport" />
        </template>
      </crudOperation>
      <el-dialog :fullscreen="false" top="10px" :show-close="true" :close-on-click-modal="false" title="电芯列表" custom-class="step-attr-dialog" width="90%" :visible.sync="detailDialogVisible">
        <detail v-if="detailDialogVisible" ref="detail" :data="currentRowData" />

      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" fixed="left" />
            <el-table-column width="100" label="电芯列表" align="center" fixed="left">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row)">电芯列表</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="item_date" min-width="150" label="时间" />
            <el-table-column :show-overflow-tooltip="true" prop="station_code" min-width="100" label="工位号" />
            <el-table-column :show-overflow-tooltip="true" prop="make_order" min-width="100" label="订单号" />
            <el-table-column :show-overflow-tooltip="true" prop="small_model_type" min-width="100" label="机型" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_barcode" min-width="220" label="模组码" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_status" min-width="100" label="合格标志">
              <template slot-scope="scope">
                <el-tag :type="scope.row.mz_status==='OK'?'success':'danger'" effect="dark" style="cursor: pointer;" size="medium">{{ scope.row.mz_status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="ng_rack" min-width="100" label="排废曹号" />
            <el-table-column :show-overflow-tooltip="true" prop="ng_code" min-width="100" label="排废原因代码" />
            <el-table-column :show-overflow-tooltip="true" prop="ng_msg" min-width="450" label="排废原因描述" />
            <el-table-column :show-overflow-tooltip="true" prop="col_num" min-width="150" label="单列/双列" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_capacity_plus" min-width="150" label="容量和" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_capacity_diff" min-width="150" label="容量差" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_max_capacity" min-width="150" label="最大容量" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_min_capacity" min-width="150" label="最小容量" />
            <el-table-column :show-overflow-tooltip="true" prop="up_flag" min-width="100" label="上传标识">
              <template slot-scope="scope">
                <el-tag :type="scope.row.up_flag==='Y'?'success':'warning'" effect="dark" style="cursor: pointer;" size="medium">{{ scope.row.up_flag }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="up_ng_code" min-width="100" label="上传错误代码" />
            <el-table-column :show-overflow-tooltip="true" prop="up_ng_msg" min-width="300" label="上传错误说明" />
            <el-table-column :show-overflow-tooltip="true" prop="enable_flag" min-width="100" label="有效标识">
              <template slot-scope="scope">
                {{ scope.row.enable_flag==="Y"?"有效":"无效" }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >总数量：{{ page.total }}</el-button>
              <el-button
                type="primary"
              >当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getParamsSel } from '@/utils/index'
import crudMeMzQuality from '@/api/mes/core/meMzQuality'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import { mesExportSelOne } from '@/api/mes/core/meExport'
import { downloadFile } from '@/utils/index'
import { fileDownload } from '@/api/core/file/file'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import detail from '@/views/mes/core/meMzQuality/detail'
const defaultForm = {

}
export default {
  name: 'MES_ME_MZ_QUALITY',
  components: { rrOperation, crudOperation, detail },
  cruds() {
    return CRUD({
      title: '模组质量数据',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mz_quality_id',
      // 排序
      sort: ['mz_quality_id desc'],
      // CRUD Method
      crudMethod: { ...crudMeMzQuality },
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        tableSize: 20
      }
    })
  },
  // 数据字典
  dicts: ['QUALIFIED_FLAG', 'MZ_NG_CODE'],
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 395,
      permission: {
        add: ['admin', 'mes_me_mz_quality:add'],
        edit: ['admin', 'mes_me_mz_quality:edit'],
        del: ['admin', 'mes_me_mz_quality:del'],
        down: ['admin', 'mes_me_mz_quality:down']
      },
      stationData: [],
      smallModelTypeData: [],
      nowPageIndex: 1, // 当前页数
      pageList: [],
      detailDialogVisible: false,
      currentRowData: {},
      exportId: '',
      timer: '',
      pickerMinDate: null,
      pickerMaxDate: null,
      day15: 31 * 24 * 3600 * 1000,
      pickerOptions1: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            return (time.getTime() > (this.pickerMinDate + this.day15)) || (time.getTime() < (this.pickerMinDate - this.day15))
          }
          return false
        }
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 395
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y',
      station_group_code: 'R2'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })

    selSmallModel({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y',
      prod_line_type: 'MZ'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.smallModelTypeData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    doExport() {
      if (!this.query.item_date) {
        this.$message({ message: '一次最多只能导出31天数据，请重选时间后再导出！', type: 'info' })
        return
      }
      let dateStr1 = this.query.item_date[0]
      dateStr1 = dateStr1.replace(/-/g, '/')
      let dateStr2 = this.query.item_date[1]
      dateStr2 = dateStr2.replace(/-/g, '/')
      var date1 = new Date(dateStr1)
      var date2 = new Date(dateStr2)
      var Difference_In_Time = date2.getTime() - date1.getTime()
      var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
      if (Difference_In_Days > 31) {
        this.$message({ message: '一次最多只能导出31天数据，请重选时间后再导出！', type: 'info' })
        return
      }

      this.crud.downloadLoading = true
      crudMeMzQuality.exportEventInsert(this.query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.exportId = defaultQuery.result
            this.timer = setInterval(this.getFileStatus, 2000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
            this.crud.downloadLoading = false
          }
        })
        .catch(() => {
          this.$message({ message: '导出数据异常', type: 'error' })
          this.crud.downloadLoading = false
        })
    },
    getFileStatus() {
      // 获取文件下载状态
      mesExportSelOne({ export_id: this.exportId })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0 && defaultQuery.data) {
            if (defaultQuery.data.finish_status === 'OK') {
              clearInterval(this.timer)
              // 文件已生成，则下载该文件
              fileDownload({ file_path: defaultQuery.data.down_url }).then(result => {
                downloadFile(result, defaultQuery.data.export_name + '数据', 'csv')
                this.crud.downloadLoading = false
              }).catch(() => {
                this.crud.downloadLoading = false
              })
            } else if (defaultQuery.data.finish_status === 'NG') {
              this.$message({ message: defaultQuery.data.error_msg, type: 'error' })
              clearInterval(this.timer)
              this.crud.downloadLoading = false
            }
          }
        })
    },
    toQuery() {
      const Arr = ['item_date', 'mz_barcode', 'dx_barcode', 'station_code', 'up_flag', 'make_order', 'small_model_type', 'mz_status', 'ng_rack', 'up_ng_code']
      if (!getParamsSel(this, Arr)) {
        this.$message({
          type: 'warning',
          message: '请至少选择一个查询条件'
        })
        return
      }
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      Object.keys(this.query).forEach(key => {
        this.query[key] = this.crud.defaultQuery[key]
      })
      // this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    opendDetail(row) {
      this.currentRowData = row
      this.detailDialogVisible = true
    },
    unbind() {
      this.$confirm(`确认解绑选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var idList = []
          this.crud.selections.forEach((item) => {
            idList.push(item.mz_quality_id)
          })
          crudMeMzQuality
            .del({
              idList: idList
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    },
    reupload() {
      this.$confirm(`确认重新上传选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var idList = []
          console.log(this.crud.selections)
          this.crud.selections.forEach((item) => {
            idList.push(item.mz_quality_id)
          })
          crudMeMzQuality
            .reupload({
              idList: idList
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
