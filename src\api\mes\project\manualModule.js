import request from '@/utils/request'

// 扫描模组信息
export function MesGxScanMzCheck(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxScanMzCheck',
    method: 'post',
    data
  })
}
// 查询模组信息
export function MesGxScanMzSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxScanMzSelect',
    method: 'post',
    data
  })
}

// 删除模组信息
export function MesGxScanMzDelete(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxScanMzDelete',
    method: 'post',
    data
  })
}
// 确定按钮 提交写入四个模组码点位
export function MesGxScanMzSubmit(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxScanMzSubmit',
    method: 'post',
    data
  })
}

export function MesGxMzRepairStatus(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxMzRepairStatus',
    method: 'post',
    data
  })
}
export default { MesGxScanMzCheck, MesGxScanMzSelect, MesGxScanMzDelete ,MesGxScanMzSubmit,MesGxMzRepairStatus}
