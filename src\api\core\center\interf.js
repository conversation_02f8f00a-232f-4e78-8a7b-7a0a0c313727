import request from '@/utils/request'

//查询接口
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfSel',
    method: 'post',
    data
  })
}
//新增接口
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfIns',
    method: 'post',
    data
  })
}
//修改接口
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfUpd',
    method: 'post',
    data
  })
}
//删除接口
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfDel',
    method: 'post',
    data
  })
}

//查询接口LOV
export function lovInterf(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysInterfLov',
    method: 'post',
    data
  })
}

export function down(data){
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfLogToExcel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del,down, lovInterf }
