import request from '@/utils/request'

// 查询Cell更新程序信息
export function CellUpdateProcSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreCellUpdateProcSel',
    method: 'post',
    data
  })
}
// 保存Cell程序更新信息
export function CellUpdateProcSave(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreCellUpdateProcSave',
    method: 'post',
    data
  })
}

// 程序更新模块，查看上传文件列表
export function UpdateProcDSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreUpdateProcDSel',
    method: 'post',
    data
  })
}

// 查询server,cell,proc更新信息
export function ServerCellProcInfoSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreServerCellProcInfoSel',
    method: 'post',
    data
  })
}

// 根据Server安装
export function ServerUpdate(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}
// Cell更新成功后清除Proc和Proc_d的数据
export function ClearProcAndProcD(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreClearProcAndProcD',
    method: 'post',
    data
  })
}

export default { CellUpdateProcSel, CellUpdateProcSave,
  UpdateProcDSel,
  ServerCellProcInfoSel, ServerUpdate, ClearProcAndProcD }

