<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="logoStyle"><img src="@/assets/images/logb.png" alt=""></div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="andonlist">
            <span v-html="above_show" />
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbgone">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="wrapmarquee">
            <marquee loop="infinite" scrollamount="15">
              <div class="wraptext">
                <p>{{ below_show }}</p>
              </div>
            </marquee>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getAndonContent } from '@/api/pmc/sysAndonContent'
export default {
  name: 'welcome',
  components: {
  },
  data() {
    return {
      above_show: '',
      below_show: ''
    }
  },
  created() {
    this.initAndonContent()
    setInterval(() => {
      this.initAndonContent()
    }, 5000)
  },
  mounted() {
  },
  methods: {
    // 加载工位屏标语
    initAndonContent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code
      }
      getAndonContent(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data === null || res.data.length === 0) {
            this.above_show = ''
            this.below_show = ''
          }
          this.above_show = res.data[0].above_show.replace(/(\\n)/g, '<br/>')
          console.log(this.above_show)
          this.below_show = res.data[0].below_show
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: "milky";
  src: url('../../../../assets/fonts/milky.ttf');
  font-weight: normal;
  font-style: normal;
}
.carInfoTable{
  min-height: calc(100vh - 50px);
}
.tableheight{
  min-height: calc(100vh - 70px);
}
.marginT{
  margin-top: 10px;
}
.slideT{
  margin-top: 20px;
}
.cardheadbg{
  // background-color: #d1030f;
  background: linear-gradient(calc(591deg), rgba(163, 163, 163, 0.09) 0%, rgba(163, 163, 163, 0.09) 33.3%, rgba(100, 100, 100, 0.09) 33.3%, rgba(100, 100, 100, 0.09) 66.6%, rgba(162, 162, 162, 0.09) 66.6%, rgba(162, 162, 162, 0.09) 99%), linear-gradient(calc(700deg), rgba(193, 193, 193, 0.06) 0%, rgba(193, 193, 193, 0.06) 33.3%, rgba(169, 169, 169, 0.06) 33.3%, rgba(169, 169, 169, 0.06) 66.6%, rgba(92, 92, 92, 0.06) 66.6%, rgba(92, 92, 92, 0.06) 99%), linear-gradient(calc(558deg), rgba(45, 45, 45, 0.03) 0%, rgba(45, 45, 45, 0.03) 33.3%, rgba(223, 223, 223, 0.03) 33.3%, rgba(223, 223, 223, 0.03) 66.6%, rgba(173, 173, 173, 0.03) 66.6%, rgba(173, 173, 173, 0.03) 99%), linear-gradient(calc(606deg), rgba(226, 226, 226, 0.06) 0%, rgba(226, 226, 226, 0.06) 33.3%, rgba(81, 81, 81, 0.06) 33.3%, rgba(81, 81, 81, 0.06) 66.6%, rgba(186, 186, 186, 0.06) 66.6%, rgba(186, 186, 186, 0.06) 99%), linear-gradient(calc(478deg), rgba(225, 225, 225, 0.04) 0%, rgba(225, 225, 225, 0.04) 33.3%, rgba(95, 95, 95, 0.04) 33.3%, rgba(95, 95, 95, 0.04) 66.6%, rgba(39, 39, 39, 0.04) 66.6%, rgba(39, 39, 39, 0.04) 99%), linear-gradient(calc(575deg), rgba(184, 184, 184, 0.06) 0%, rgba(184, 184, 184, 0.06) 33.3%, rgba(0, 0, 0, 0.06) 33.3%, rgba(0, 0, 0, 0.06) 66.6%, rgba(140, 140, 140, 0.06) 66.6%, rgba(140, 140, 140, 0.06) 99.9%), linear-gradient(calc(708deg), rgba(40, 40, 40, 0.07) 0%, rgba(40, 40, 40, 0.07) 33.3%, rgba(214, 214, 214, 0.07) 33.3%, rgba(214, 214, 214, 0.07) 66.6%, rgba(190, 190, 190, 0.07) 66.6%, rgba(190, 190, 190, 0.07) 99.9%), linear-gradient(calc(409deg), rgba(230, 230, 230, 0) 0%, rgba(230, 230, 230, 0) 33.3%, rgba(241, 241, 241, 0) 33.3%, rgba(241, 241, 241, 0) 66.6%, rgba(55, 55, 55, 0) 66.6%, rgba(55, 55, 55, 0) 99%), linear-gradient(calc(340deg), #ff0000, rgb(255 0 26));
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  // margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  justify-content: flex-start;
  height: 100px;
  background-color: #e6c700;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    z-index: 2;
    margin-left: 30px;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #ffc864;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .andonlist{
  font-family: "milky";
  height: 545px;
  padding: 25px;
  line-height: 160px;
  text-align: center;
  color: #e9f515;
  font-size: 92px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  ul{
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    li{
      list-style: none;
      width: 11.84%;
      height: 100px;
      text-align: center;
      color: #000000;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
      margin: 5px;
      position: relative;
      span{
        font-size: 40px;
        font-weight: bold;
        z-index: 2;
        padding-left: 5px;
      }
      .carorder{
        font-size: 16px;
        margin-top: 5px;
      }
      img{
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
    }
  }
}
.cardheadbgone{
  background-color: #e6c700;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  font-family: "milky";
  color: #ff0000;
  font-size: 60px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  height: 80px;
  word-spacing: 150px;
  }
}
::v-deep .commonstatus{
  background-color: #ffffff;
}
::v-deep .nomalstatus{
  background-color: #00a047;
}
::v-deep .errorstatus{
  background-color: #ff0000;
}
::v-deep .warningstatus{
  background-color: #ffd600;
}
::v-deep .el-card{
  border-radius: 0;
}
</style>
