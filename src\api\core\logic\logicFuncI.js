import request from '@/utils/request'

// 查询自动化逻辑属性子
export function selLogicFuncItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncISel',
    method: 'post',
    data
  })
}
// 新增自动化逻辑属性子
export function insLogicFuncItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncIIns',
    method: 'post',
    data
  })
}
// 修改自动化逻辑属性子
export function updLogicFuncItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncIUpd',
    method: 'post',
    data
  })
}
// 删除自动化逻辑属性子
export function delLogicFuncItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncIDel',
    method: 'post',
    data
  })
}
export default { selLogicFuncItem, insLogicFuncItem, updLogicFuncItem, delLogicFuncItem }
