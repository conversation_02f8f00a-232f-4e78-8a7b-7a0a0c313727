<template>
  <el-container :style="'height:' + height">
    <el-main style="background-color: #e6e8ee">
      <el-tabs
        v-model="editableTabsValue1"
        :closable="true"
        @tab-remove="handleRemove"
      >
        <el-tab-pane
          v-for="(item, index) in editableTabs1"
          :key="index"
          :label="item.title"
          :name="item.name"
          style="padding: 0px; background-color: #e6e8ee"
        >
          <!-- <elFrame :src="item.path" /> -->
          <elFrame :src="item.path" />
        </el-tab-pane>
      </el-tabs>
    </el-main>
    <el-footer style="height: 65px">
      <el-switch
        v-model="switchValue"
        class="div_switch"
        active-color="#8BB7F7"
        inactive-color="#0C53C4"
        @change="handleSwitchChange"
      />

      <el-carousel
        ref="carousel"
        trigger="click"
        :autoplay="false"
        direction="vertical"
        indicator-position="none"
        height="65px"
      >
        <el-carousel-item>
          <table style="width: 100%; border: 0px">
            <tr>
              <td style="width: 20px">
                <el-button
                  v-show="hmiMenuData.length !== 0"
                  class="filter-item"
                  size="medium"
                  type="primary"
                  icon="el-icon-arrow-left"
                  style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
                  plain
                  @click="marginLeft !== 0 ? (marginLeft += 100) : 0"
                />
              </td>
              <td>
                <el-scrollbar ref="scrollbar" style="width: 100%">
                  <div :style="'width: 400%;margin-left:' + marginLeft + 'px;'">
                    <template v-for="(item, index) in hmiMenuData">
                      &nbsp;
                      <template v-if="item.children.length > 0">
                        <el-popover
                          :key="item.id"
                          v-model="popoverVisibleList['popoverVisible' + index]"
                          placement="top"
                          width="230"
                        >
                          <el-button
                            v-for="subItem in item.children"
                            :key="subItem.id"
                            size="medium"
                            type="primary"
                            :icon="subItem.menu_icon"
                            style="
                              width: 205px;
                              height: 40px;
                              margin-bottom: 5px;
                              margin-left: 0px;
                            "
                            plain
                            @click="handlesSelect(subItem, index, item)"
                          >{{ subItem.menu_des }}</el-button>

                          <el-button
                            slot="reference"
                            :key="item.id"
                            class="filter-item"
                            size="medium"
                            type="primary"
                            icon="el-icon-menu"
                            style="
                              height: 50px;
                              margin-top: 5px;
                              font-weight: bold;
                              margin-left: 0px;
                            "
                            plain
                          >
                            {{
                              item.current_menu_des === ""
                                ? item.menu_des
                                : item.current_menu_des
                            }}
                          </el-button>
                        </el-popover>
                      </template>
                      <template v-else>
                        <el-button
                          :key="item.id"
                          class="filter-item"
                          size="medium"
                          type="primary"
                          :icon="item.menu_icon"
                          style="
                            height: 50px;
                            margin-top: 5px;
                            font-weight: bold;
                            margin-left: 0px;
                          "
                          plain
                          @click="handlesSelect(item, index, item)"
                        >
                          {{
                            item.current_menu_des === ""
                              ? item.menu_des
                              : item.current_menu_des
                          }}
                        </el-button>
                      </template>
                    </template>
                  </div>
                </el-scrollbar>
              </td>
              <td style="width: 20px">
                <el-button
                  v-show="hmiMenuData.length !== 0"
                  class="filter-item"
                  size="medium"
                  type="primary"
                  icon="el-icon-arrow-right"
                  style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
                  plain
                  @click="marginLeft -= 100"
                />
              </td>
            </tr>
          </table>
        </el-carousel-item>
        <el-carousel-item>
          <table style="width: 100%; border: 0px">
            <tr>
              <td style="width: 20px">
                <el-button
                  v-show="editableTabs1.length !== 0"
                  class="filter-item"
                  size="medium"
                  type="primary"
                  icon="el-icon-arrow-left"
                  style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
                  plain
                  @click="marginLeft1 !== 0 ? (marginLeft1 += 100) : 0"
                />
              </td>
              <td>
                <el-scrollbar ref="scrollbar" style="width: 100%">
                  <div
                    :style="'width: 400%;margin-left:' + marginLeft1 + 'px;'"
                  >
                    <template v-for="(item, index) in editableTabs1">
                      &nbsp;
                      <el-button
                        :key="item.TabName"
                        class="filter-item"
                        size="medium"
                        type="primary"
                        :icon="item.icon"
                        style="
                          height: 50px;
                          margin-top: 5px;
                          font-weight: bold;
                          margin-left: 0px;
                        "
                        plain
                        @click="handlesOpenedMenu(item, index)"
                      >
                        {{ item.title }}
                      </el-button>
                    </template>
                  </div>
                </el-scrollbar>
              </td>
              <td style="width: 20px">
                <el-button
                  v-show="editableTabs1.length !== 0"
                  class="filter-item"
                  size="medium"
                  type="primary"
                  icon="el-icon-arrow-right"
                  style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
                  plain
                  @click="marginLeft1 -= 100"
                />
              </td>
            </tr>
          </table>
        </el-carousel-item>
      </el-carousel>

      <!-- <el-scrollbar style="width:100%;" >
        <div style="width: 400%;">
          <template v-for="(item,index) in hmiMenuData">
            &nbsp;
            <template v-if="item.children.length > 0">
              <el-popover
                :key="item.id"
                placement="top"
                width="230"
                v-model="popoverVisibleList['popoverVisible'+index]"
              >
                <el-button
                  v-for="subItem in item.children"
                  :key="subItem.id"
                  size="medium"
                  type="primary"
                  @click="handlesSelect(subItem,index)"
                  :icon="subItem.menu_icon"
                  style="
                    width:205px;
                    height: 40px;
                    margin-bottom: 5px;
                    margin-left: 0px;
                  "
                  >{{ subItem.menu_des }}</el-button
                >

                <el-button
                  slot="reference"
                  :key="item.id"
                  class="filter-item"
                  size="medium"
                  type="primary"
                  icon="el-icon-menu"
                  style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                  "
                >
                  {{ item.menu_des }}
                </el-button>
              </el-popover>
            </template>
            <template v-else>
              <el-button
                :key="item.id"
                class="filter-item"
                size="medium"
                type="primary"
                @click="handlesSelect(item,index)"
                :icon="item.menu_icon"
                style="
                  height: 50px;
                  margin-top: 5px;
                  font-weight: bold;
                  margin-left: 0px;
                "
              >
                {{ item.menu_des }}
              </el-button>
            </template>
          </template>
        </div>
      </el-scrollbar> -->

      <!-- <el-menu
               class="el-menu-demo"
               mode="horizontal"
               background-color="#545c64"
               text-color="#fff"
               active-text-color="#ffd04b">
        <template  v-for="item in hmiMenuData" >
            <template v-if="item.children.length>0">
                        <el-submenu :key="item.id" :index="item.id" >
                            <template slot="title">{{ item.menu_des }}</template>
                            <el-menu-item v-for="subItem in item.children" :key="subItem.id" @click="handlesSelect(subItem)">{{ subItem.menu_des }}</el-menu-item>
                        </el-submenu>
            </template>
            <template v-else>
                <el-menu-item :key="item.id" :index="item.id" @click="handlesSelect(item)">{{ item.menu_des }}</el-menu-item>
            </template>
        </template>
      </el-menu> -->
    </el-footer>
  </el-container>
</template>

<script>
import elFrame from '@/views/core/hmi/iframe.vue'
import { queryAllHmiMenu } from '@/api/hmi/index'
import Cookies from 'js-cookie'

export default {
  name: 'layout',
  components: {
    elFrame
  },
  props: ['server_id', 'cell_id'],
  data() {
    return {
      height: document.documentElement.clientHeight - 50 + 'px;',
      hmiMenuData: [],
      editableTabsValue1: '0',
      editableTabs1: [],
      popoverVisible: false,
      popoverVisibleList: {
        popoverVisible1: false,
        popoverVisible2: false,
        popoverVisible3: false,
        popoverVisible4: false,
        popoverVisible5: false,
        popoverVisible6: false,
        popoverVisible7: false,
        popoverVisible8: false,
        popoverVisible9: false,
        popoverVisible10: false,
        popoverVisible11: false,
        popoverVisible12: false,
        popoverVisible13: false,
        popoverVisible14: false,
        popoverVisible15: false,
        popoverVisible16: false,
        popoverVisible17: false,
        popoverVisible18: false,
        popoverVisible19: false,
        popoverVisible20: false,
        popoverVisible21: false,
        popoverVisible22: false,
        popoverVisible23: false
      },
      marginLeft: 0,
      marginLeft1: 0,
      switchValue: false
    }
  },
  watch: {
    cell_id: function(indexVal, oldVal) {}
  },
  mounted: function() {
    // const server_host = this.$parent.$attrs.server_host;
    // const cell_port = this.$parent.$attrs.cell_port;
    // const cell_id = this.$parent.$attrs.cell_id; //this.$parent.$options.propsData.name; //该组件在父组件中的父级是tab组件，tab组件的name唯一，且规则是Tab+server_id+cell_id
    // if (typeof cell_id != "undefined") {
    //   var query = {
    //     cell_id: cell_id, //tabName.split("_")[2],
    //   };
    //   queryAllHmiMenu(query)
    //     .then((res) => {
    //       const defaultQuery = JSON.parse(JSON.stringify(res));
    //       if (defaultQuery.code == 0) {
    //         if (defaultQuery.data.length > 0) {
    //           //this.hmiMenuData = defaultQuery.data;
    //           var dd = [];
    //           for (let i = 0; i < defaultQuery.data.length; i++) {
    //             const element = defaultQuery.data[i];
    //             const id = element.cell_hmi_menu_id;
    //             const menu_des = element.hmi_menu_des;
    //             const path = element.hmi_menu_page;
    //             const menu_type = element.hmi_menu_type;
    //             const hmi_menu_ico = element.hmi_menu_ico;
    //             var _chekc = true;
    //             for (var ii = 0; ii < dd.length; ii++) {
    //               if (dd[ii].id == "1-" + id) {
    //                 _chekc = false;
    //                 break;
    //               }
    //             }
    //             if (_chekc) {
    //               var aa = {};
    //               aa.id = "1-" + id;
    //               aa.menu_des = menu_des;
    //               aa.button_type = "primary";
    //               aa.current_menu_des = "";
    //               aa.menu_icon =
    //                 hmi_menu_ico === "" ? "el-icon-s-platform" : hmi_menu_ico;
    //               aa.path = "http://" + server_host + ":" + cell_port + path; //http://localhost:8013/hmi
    //               aa.children = [];
    //               if (menu_type == "mainMenu") {
    //                 const _this = this;
    //                 const subMenu = defaultQuery.data.filter(function (item) {
    //                   return (
    //                     item.cell_hmi_menu_id == id &&
    //                     item.cell_hmi_menu_b_id != ""
    //                   );
    //                 });
    //                 for (let j = 0; j < subMenu.length; j++) {
    //                   const element1 = subMenu[j];
    //                   const cell_hmi_menu_b_id = element1.cell_hmi_menu_b_id;
    //                   const hmi_menu_b_des = element1.hmi_menu_b_des;
    //                   const hmi_menu_b_page = element1.hmi_menu_b_page;
    //                   const hmi_menu_b_ico = element1.hmi_menu_b_ico;
    //                   var cc = {};
    //                   cc.id = "2-" + cell_hmi_menu_b_id;
    //                   cc.menu_des = hmi_menu_b_des;
    //                   cc.menu_icon =
    //                     hmi_menu_b_ico === ""
    //                       ? "el-icon-s-platform"
    //                       : hmi_menu_b_ico;
    //                   cc.path =
    //                     "http://" +
    //                     server_host +
    //                     ":" +
    //                     cell_port +
    //                     hmi_menu_b_page;
    //                   aa.children.push(cc);
    //                 }
    //               }
    //               dd.push(aa);
    //             }
    //           }
    //           this.hmiMenuData = dd;
    //         } else {
    //           this.hmiMenuData = [];
    //         }
    //       }
    //     })
    //     .catch(() => {
    //       this.$message({
    //         message: "初始化模式数据异常",
    //         type: "error",
    //       });
    //       this.hmiMenuData = [];
    //     });
    // }
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 50 + 'px;'
    }
  },
  created: function() {
    // this.hmiMenuData=[{id:1,menu_des:'首页',children:[{id:31,menu_des:'我的工作31'}]},{id:2,menu_des:'我的工作',children:[{id:3,menu_des:'我的工作3'},{id:4,menu_des:'我的工作4'}]}];
  },
  methods: {
    handleRefresh(server_host, cell_port, cell_id) {
      console.log(1)
      this.editableTabs1 = []
      this.hmiMenuData = []

      var query = {
        userID: Cookies.get('userId'),
        cell_id: cell_id // tabName.split("_")[2],
      }
      queryAllHmiMenu(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code == 0) {
            if (defaultQuery.data.length > 0) {
              // this.hmiMenuData = defaultQuery.data;
              var dd = []
              for (let i = 0; i < defaultQuery.data.length; i++) {
                const element = defaultQuery.data[i]
                const id = element.cell_hmi_menu_id
                const menu_des = element.hmi_menu_des
                const path = element.hmi_menu_page
                const menu_type = element.hmi_menu_type
                const hmi_menu_ico = element.hmi_menu_ico
                var _chekc = true
                for (var ii = 0; ii < dd.length; ii++) {
                  if (dd[ii].id == '1-' + id) {
                    _chekc = false
                    break
                  }
                }
                if (_chekc) {
                  var aa = {}
                  aa.id = '1-' + id
                  aa.menu_des = menu_des
                  aa.button_type = 'primary'
                  aa.current_menu_des = ''
                  aa.menu_icon =
                      hmi_menu_ico === '' ? 'el-icon-s-platform' : hmi_menu_ico
                  aa.path = 'http://' + server_host + ':' + cell_port + path // http://localhost:8013/hmi
                  aa.children = []
                  if (menu_type == 'mainMenu') {
                    const _this = this
                    const subMenu = defaultQuery.data.filter(function(item) {
                      return (
                        item.cell_hmi_menu_id == id &&
                          item.cell_hmi_menu_b_id != ''
                      )
                    })
                    for (let j = 0; j < subMenu.length; j++) {
                      const element1 = subMenu[j]
                      const cell_hmi_menu_b_id = element1.cell_hmi_menu_b_id
                      const hmi_menu_b_des = element1.hmi_menu_b_des
                      const hmi_menu_b_page = element1.hmi_menu_b_page
                      const hmi_menu_b_ico = element1.hmi_menu_b_ico
                      var cc = {}
                      cc.id = '2-' + cell_hmi_menu_b_id
                      cc.menu_des = hmi_menu_b_des
                      cc.menu_icon =
                          hmi_menu_b_ico === ''
                            ? 'el-icon-s-platform'
                            : hmi_menu_b_ico
                      cc.path =
                          'http://' +
                          server_host +
                          ':' +
                          cell_port +
                          hmi_menu_b_page
                      aa.children.push(cc)
                    }
                  }
                  dd.push(aa)
                }
              }
              this.hmiMenuData = dd
            } else {
              this.hmiMenuData = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
          this.hmiMenuData = []
        })
    },
    handleSwitchChange(value) {
      if (value) {
        this.$refs.carousel.next()
      } else {
        this.$refs.carousel.prev()
      }
    },
    handlesSelect(item, index, parent) {
      console.log(item)
      this.popoverVisibleList['popoverVisible' + index] = false
      let obj = {}
      const obj1 = {}
      const TabName = 'Tab_' + item.id
      obj = this.editableTabs1.find((item) => {
        return item.name === TabName
      })
      const currentMenu1 = this.editableTabs1.filter(
        (item) => item.button_type === 'warning'
      )
      if (currentMenu1.length > 0) {
        currentMenu1[0].button_type = 'primary'
      }
      if (typeof obj !== 'undefined') {
        obj.button_type = 'warning'
        this.editableTabsValue1 = TabName
      } else {
        this.editableTabs1.push({
          title: item.menu_des,
          name: TabName,
          path: item.path,
          icon: item.menu_icon,
          button_type: 'warning'
        })
        this.editableTabsValue1 = TabName
      }

      const currentMenu = this.hmiMenuData.filter(
        (item) => item.button_type === 'warning'
      )
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
        currentMenu[0].current_menu_des = ''
      }
      parent.current_menu_des = item.menu_des
      parent.button_type = 'warning'
      this.$emit('showCellNavbar', item.menu_des)
    },
    handleRemove(tabName) {
      for (var i = 0; i < this.editableTabs1.length; i++) {
        if (this.editableTabs1[i].name == tabName) {
          this.editableTabs1.splice(i, 1)
        }
      }
    },
    handlesOpenedMenu(item, index) {
      const currentMenu = this.editableTabs1.filter(
        (item) => item.button_type === 'warning'
      )
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
      }
      item.button_type = 'warning'
      this.editableTabsValue1 = item.name
      this.$emit('showCellNavbar', item.title)
    }
  }
}
</script>
<style lang="less" scoped>
.el-header {
  background-color: #b3c0d1;
  color: #333;
}
.el-footer {
  background-color: #d2d6df;
  color: #333;
  line-height: 60px;
  padding: 0px;
  z-index: 2333;
  overflow: hidden;
}
.el-main {
  background-color: #ffffff;
  color: #333;
  padding: 0px;
}
body > .el-container {
  margin-bottom: 40px;
}
.div_switch {
  position: absolute;
  margin-left: 2px;
  z-index: 1000;
  width: 50px;
  height: 30px;
  line-height: 30px;
  margin-top: -30px;
  opacity: 0.3;
}
.div_switch:hover {
  opacity: 1;
}
</style>
