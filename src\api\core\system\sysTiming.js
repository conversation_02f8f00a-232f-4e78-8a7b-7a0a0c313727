import request from '@/utils/request'

//查询任务调度
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysTimingSel',
    method: 'post',
    data
  })
}
//新增任务调度
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysTimingIns',
    method: 'post',
    data
  })
}
//修改任务调度
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysTimingUpd',
    method: 'post',
    data
  })
}
//删除任务调度
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysTimingDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
