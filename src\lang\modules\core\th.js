// การแปลภาษาไทยสำหรับโมดูลหลัก

export default {
  // เพิ่มการแปลเฉพาะโมดูลหลักที่นี่
  core: {
    // การจัดการสูตร SECS/AIS
    secsais: {
      // หัวข้อหน้าและข้อความทั่วไป
      maintenance: 'การจัดการสูตร SECS/AIS',
      detail: 'รายละเอียดสูตร',
      noRecipeSelected: 'ไม่ได้เลือกสูตรหรือกำลังโหลดรายละเอียดสูตร...',
      clientList: 'รายการอินสแตนซ์',
      modelList: 'รายการสูตร',

      // ฟิลด์แบบฟอร์ม
      clientDes: 'คำอธิบายไคลเอนต์',
      tagGroupCode: 'รหัสกลุ่มแท็ก',
      modelName: 'ชื่อโมเดล',
      tagGroupId: 'รหัสกลุ่มแท็ก',
      tagCodePrefix: 'คำนำหน้ารหัสแท็ก',
      clientId: 'รหัสไคลเอนต์',
      clientCode: 'รหัสไคลเอนต์',
      parameterCode: 'รหัสพารามิเตอร์',
      parameterDes: 'คำอธิบายพารามิเตอร์',
      parameterVal: 'ค่าพารามิเตอร์',
      enableFlag: 'เปิดใช้งานแฟล็ก',
      valid: 'ใช้งานได้',
      invalid: 'ใช้งานไม่ได้',

      // การดำเนินการ
      modifyParameter: 'แก้ไขพารามิเตอร์',
      addParameter: 'เพิ่มพารามิเตอร์',
      confirmDelete: 'ยืนยันการลบ {0} รายการที่เลือก?',
      confirmDistribute: 'ยืนยันการกระจายสูตรนี้?',
      confirmChangeEnableFlag: 'คุณแน่ใจหรือไม่ว่าต้องการเปลี่ยนแฟล็กเปิดใช้งานเป็น [{value}]?',
      deleteSuccess: 'ลบสำเร็จ',
      deleteFailed: 'ลบล้มเหลว',
      modifySuccess: 'แก้ไขสำเร็จ',
      modifyFailed: 'แก้ไขล้มเหลว',
      emptyValueSet: 'ตั้งค่าเป็นค่าว่างแล้ว',
      distributeSuccess: 'การกระจายสำเร็จ',
      distributeFailed: 'การกระจายล้มเหลว',
      mqttConnectionFailed: 'การเชื่อมต่อ MQTT ล้มเหลว',
      mqttNotConnected: 'โปรดเชื่อมต่อกับบริการ MQTT ก่อน',
      mqttConnectSuccess: 'การเชื่อมต่อ MQTT สำเร็จ',
      mqttUpdateTimeout: 'หมดเวลาการอัปเดต MQTT โปรดตรวจสอบการเชื่อมต่อ',
      serviceCell: 'ไม่สามารถรับข้อมูลเซิร์ฟเวอร์เซลล์',
      getCellIPFailed: 'ไม่สามารถรับ IP ของเซลล์',

      // พารามิเตอร์ที่เกี่ยวข้อง
      parameterCode: 'รหัสพารามิเตอร์',
      parameterDes: 'คำอธิบายพารามิเตอร์',
      parameterVal: 'ค่าปัจจุบัน',
      parameterUnit: 'หน่วย',
      parameterLimit: 'ขีดจำกัด',
      parameterInput: 'ค่าที่ป้อน',
      modifyParameter: 'แก้ไขพารามิเตอร์',
      noRecipeSelected: 'กรุณาเลือกสูตร',
      fetchRecipeDataFailed: 'ไม่สามารถดึงข้อมูลสูตร',
      valueTooLow: 'ค่าที่ป้อนต่ำกว่าขีดจำกัดล่าง',
      valueTooHigh: 'ค่าที่ป้อนสูงกว่าขีดจำกัดบน',

      // การค้นหาและข้อความแจ้งเตือน
      search: 'ค้นหา',
      reset: 'รีเซ็ต',
      confirm: 'ยืนยัน',
      cancel: 'ยกเลิก',
      prompt: 'แจ้งเตือน',
      required: 'จำเป็น',
      fetchModelDataFailed: 'ไม่สามารถดึงข้อมูลโมเดลสูตรได้',
      fetchRecipeDataFailed: 'ไม่สามารถดึงข้อมูลรายละเอียดสูตรได้',
      maintenance: 'การบำรุงรักษาสูตร SECS/AIS',
      confirmDelete: 'ยืนยันการลบ {0} รายการ?',

      // สถานะและข้อความแจ้งเตือนการดำเนินการ
      refreshInstanceList: 'รีเฟรชรายการอินสแตนซ์',
      refreshingInstanceList: 'กำลังรีเฟรชรายการอินสแตนซ์...',
      online: 'ออนไลน์',
      offline: 'ออฟไลน์',
      stationCodeMissing: 'รหัสสถานีหายไป ไม่สามารถค้นหาข้อมูลโมเดลสูตรได้',
      stationCodeMissingMqtt: 'รหัสสถานีหายไป ไม่สามารถเชื่อมต่อกับ MQTT ได้',
      cancelDelete: 'ยกเลิกการลบ',
      unknownMqttFormat: 'รูปแบบข้อความ MQTT ไม่รู้จัก',

      // เกี่ยวกับพารามิเตอร์สูตร
      recipeList: 'รายการสูตร',
      recipeDetails: 'รายละเอียดสูตร',
      modelList: 'รายการสูตร',
      modifyParameter: 'แก้ไขพารามิเตอร์',
      parameterCode: 'รหัสพารามิเตอร์',
      parameterDes: 'คำอธิบายพารามิเตอร์',
      parameterVal: 'ค่าพารามิเตอร์',
      parameterInput: 'ค่าที่ป้อน',
      parameterUnit: 'หน่วย',
      parameterLimit: 'ช่วงพารามิเตอร์',
      noRecipeSelected: 'กรุณาเลือกสูตร',
      valueTooLow: 'ค่าที่ป้อนต่ำกว่าขีดจำกัดล่าง',
      valueTooHigh: 'ค่าที่ป้อนสูงกว่าขีดจำกัดบน',
      modifySuccess: 'แก้ไขสำเร็จ',
      modifyFailed: 'การแก้ไขล้มเหลว',

      // เกี่ยวกับ MQTT
      mqttNotConnected: 'MQTT ไม่ได้เชื่อมต่อ',
      mqttConnectSuccess: 'เชื่อมต่อ MQTT สำเร็จ',
      mqttConnectionFailed: 'การเชื่อมต่อ MQTT ล้มเหลว',
      serviceCell: 'ไม่สามารถรับข้อมูลเซลล์บริการ',
      getCellIPFailed: 'ไม่สามารถรับ IP ของเซลล์'
    }
  }
}
