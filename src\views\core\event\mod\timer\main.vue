<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modmain.templateCode')">
                <!-- 模板代码： -->
                <el-input v-model="query.event_mod_timer_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modmain.templateDescription')">
                <!-- 模板描述： -->
                <el-input v-model="query.event_mod_timer_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-tabs v-model="activeName" @tab-click="handletabsclick">
          <el-tab-pane label="基础属性" name="first">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
              <el-form-item :label="$t('lang_pack.modmain.templateCodet')" prop="event_mod_timer_code">
                <!-- Timer事件模板代码(唯一) -->
                <el-input v-model="form.event_mod_timer_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.modmain.templateDescriptiont')" prop="event_mod_timer_des">
                <!-- Timer事件模板描述(唯一) -->
                <el-input v-model="form.event_mod_timer_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.modmain.programDriven')" prop="event_mod_timer_dll">
                <!-- MQ程序库DLL -->
                <el-input v-model="form.event_mod_timer_dll" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceMenu.fun')" prop="mod_timer_func_dll">
                <!-- MQ执行函数DLL -->
                <el-input v-model="form.mod_timer_func_dll" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceMenu.funClass')" prop="mod_timer_func_class">
                <!-- MQ执行函数类名 -->
                <el-input v-model="form.mod_timer_func_class" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-tab-pane>
          <el-tab-pane label="属性组" name="second" >
            <div id="scrollbar1" :style="'height:' + height + 'px'">
              <el-scrollbar style="height: 100%">
                <attrGroup v-if="true" ref="attrGroup" :event_mod_timer_id="this.form.event_mod_timer_id" />
              </el-scrollbar>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="event_mod_timer_id" label="id" />
            <el-table-column :show-overflow-tooltip="true" prop="event_mod_timer_code" :label="$t('lang_pack.modmain.templateCodet')" width="220" />
            <!-- 模板代码 -->
            <el-table-column :show-overflow-tooltip="true" prop="event_mod_timer_des" :label="$t('lang_pack.modmain.templateDescriptiont')" width="220" />
            <!-- 模板描述 -->
            <el-table-column :show-overflow-tooltip="true" prop="event_mod_timer_dll" :label="$t('lang_pack.modmain.programDriven')" width="220" />
            <!-- 程序驱动 -->
            <el-table-column :show-overflow-tooltip="true" prop="mod_timer_func_dll" width="220" :label="$t('lang_pack.maintenanceMenu.fun')" />
            <!-- MQ执行函数DLL -->
            <el-table-column :show-overflow-tooltip="true" prop="mod_timer_func_class" width="220" :label="$t('lang_pack.maintenanceMenu.funClass')" />
            <!-- MQ执行函数类名 -->
            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudEventTimerModMain from '@/api/core/event/eventTimerModMain'
import attrGroup from './attr-group'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  event_mod_timer_id: 0,
  event_mod_timer_code: '',
  event_mod_timer_des: '',
  event_mod_timer_dll: '',
  mod_timer_func_dll:'',
  mod_timer_func_class:'',
  enable_flag: 'Y'
}
export default {
  name: 'RCS_EVENT_TIMER_MOD_MAIN',
  components: { crudOperation, rrOperation, udOperation, pagination, attrGroup },
  cruds() {
    return CRUD({
      title: '流程模板',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'event_mod_timer_id',
      // 排序
      sort: ['event_mod_timer_id asc'],
      // CRUD Method
      crudMethod: { ...crudEventTimerModMain },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'rcs_event_mod_timer:add'],
        edit: ['admin', 'rcs_event_mod_timer:edit'],
        del: ['admin', 'rcs_event_mod_timer:del'],
        down: ['admin', 'rcs_event_mod_timer:down']
      },
      rules: {
        event_mod_timer_code: [{ required: true, message: '请输入模板代码', trigger: 'blur' }],
        event_mod_timer_des: [{ required: true, message: '请输入模板描述', trigger: 'blur' }],
        event_mod_timer_dll: [{ required: true, message: '请输入程序驱动', trigger: 'blur' }],
        mod_timer_func_dll: [{ required: true, message: '请输入Timer执行函数DLL', trigger: 'blur' }],
        mod_timer_func_class: [{ required: true, message: '请输入Timer执行函数类名', trigger: 'blur' }]
      },
      activeName: 'first',
      customPopover: false
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {},
  methods: {
    handletabsclick() {
      // this.$refs.functionI.$refs.codeEditor._refresh()
    },
    handleRowClick(row, column, event) {
      this.form.event_mod_timer_id = row.event_mod_timer_id
    }
  }
}
</script>

<style lang="less" scoped>
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px !important;
}
.code-dialog-height .el-dialog__body {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-y: auto;
}
.code-dialog-content {
  top: 60px;
  right: 5px;
  bottom: 5px;
  left: 5px;
  overflow-y: auto;
  position: absolute;
}
</style>
