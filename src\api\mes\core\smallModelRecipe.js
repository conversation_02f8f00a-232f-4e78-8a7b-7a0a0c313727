import request from '@/utils/request'

// 产品型号对应配方信息查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelRecipeSel',
    method: 'post',
    data
  })
}
// 产品型号对应配方信息修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelRecipeUpd',
    method: 'post',
    data
  })
}
// 产品型号对应配方信息删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelRecipeDel',
    method: 'post',
    data
  })
}

export default { sel, edit, del }
