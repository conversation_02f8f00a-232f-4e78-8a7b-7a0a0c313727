import request from '@/utils/request'

// 查询线尾下线履历
export function recordResume(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsApsMeStationMisRecord',
    method: 'post',
    data: data
  })
}

// 查询分拣任务信息
export function sortingList(data) {
    return request({
      url: 'aisEsbWeb/dcs/hmi/DcsApsMeStationMisSortingList',
      method: 'post',
      data: data
    })
  }

// 查询过站明细
export function recordDetail(data) {
    return request({
      url: 'aisEsbWeb/dcs/hmi/DcsApsMeStationMisRecordDetail',
      method: 'post',
      data: data
    })
  }
// 手动录入实际数量
export function sortingNum(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsApsMeSInputSortingNum',
    method: 'post',
    data: data
  })
}
// 查看零件明细
export function sortResult(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskResolveSelect',
    method: 'post',
    data: data
  })
}

export default { recordResume,sortingList,recordDetail,sortingNum ,sortResult}

