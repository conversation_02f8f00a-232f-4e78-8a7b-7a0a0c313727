<template>
  <div class="orderinfo-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="24">
          <el-card class="cardFirst">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="statuStyle">
                  <p><span :class="allowScan=='0'?'commonsatu statuZero':'commonsatu statuOne'" /><span>允许扫描信号</span></p>
                </div>
              </el-col>
              <el-col :span="6">
                <span>订单号：</span><span style="font-size:20px;font-weight:700">{{ orderNo }}</span>
              </el-col>
              <el-col :span="6">
                <span>机型：</span><span style="font-size:20px;font-weight:700">{{ smallModel }}</span>
              </el-col>
              <el-col :span="6">
                <span>工件标识：</span><span style="font-size:20px;font-weight:700">{{ workFlag }}</span>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top:25px;">
              <el-col :span="24">
                <div class="scanStyle">
                  <span>模组码：</span>
                  <span>
                    {{ mzBarcode }}
                  </span>
                </div>
                <el-tag v-if="materialScanFinish" style="float:right;" type="success">物料已全部扫描完成</el-tag>
              </el-col>
            </el-row>
          </el-card>
          <el-card>
            <div class="scanStyle">
              <span>物料条码：</span>
              <div class="wrapimin">
                <el-input v-model="materialBarcode" type="text" size="large" placeholder="请输入内容" />
                <img :src="keyboard" @click="showKeyboard">
              </div>
              <el-button class="scanBtn" size="large" type="primary" @click="ManualMaterialScan">扫描</el-button>
              <el-button class="scanBtn" size="large" type="primary" @click="handleNgPass">NG放行</el-button>
            </div>
            <el-table border @header-dragend="crud.tableHeaderDragend()" :data="materialTableData" style="width: 100%" height="460" :highlight-current-row="true">
              <el-table-column  label="物料号" width="200" prop="material_code" />
              <el-table-column  label="物料描述" width="250" prop="material_des" />
              <el-table-column  label="配套用量" prop="usage" />
              <el-table-column  label="批次完成标识" prop="batch_finish_flag" />
              <el-table-column  label="精追完成标识" prop="verify_finish_flag" />
              <el-table-column  label="精追条码" prop="exact_barcode" />
              <el-table-column  label="批次号" prop="material_batch" />
              <el-table-column  label="供应商" prop="material_supplier">
                <template slot-scope="scope">
                  {{ scope.row.material_supplier+' '+scope.row.supplier_des }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <!-- <el-card class="box-card1" /> -->
        </el-col>
      </el-row>
    </el-card>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { MesMePdureMaterialSel, MesMePdureMaterialScan } from '@/api/mes/project/materialScan.js'
import Cookies from 'js-cookie'
import { selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
export default {
  name: 'orderInfo',
  components: {
    SimpleKeyboard
  },
  data() {
    return {
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      iframeStaionId: '',
      iframeprodLineId: '',
      iframeStaionCode: '',
      allowScan: 0, // 允许扫描电芯
      orderNo: '', // 订单号
      smallModel: '', // 机型
      workFlag: '', // 工件标识
      mzBarcode: '', // 模组码
      proceduceId: '', //
      materialBarcode: '', // 物料条码
      materialTableData: [],
      materialScanFinish: false,

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      connectUrl: '', // MQTT连接地址
      tagOnlyKey: ''
    }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  created() {},
  mounted() {
    this.iframeprodLineId = this.$route.query.prod_line_id
    this.iframeStaionId = this.$route.query.station_id
    this.iframeStaionCode = this.$route.query.station_code
    // 启动监控
    this.toStartWatch()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')

    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellId = result.cell_id
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          if (process.env.NODE_ENV === 'development') {
            this.connectUrl = 'ws://***************:8083/mqtt'
          } else {
            this.connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          }
          console.log('拼接URL：' + this.connectUrl)

          // Tag点位集合
          var newClientTagGroupList = []
          var newClientTagList = ['OP1130/MesStatus/SerialInfo', 'OP1131/BarStatus/BarS_GetBarCodeResult']

          // mqtt连接
          this.clientMqtt = mqtt.connect(this.connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // var topic_clientStatus = 'SCADA_STATUS/OP1010'
            // 获取Tag值
            if (newClientTagList.length > 0) {
              this.GetTagValue(newClientTagList)
              // MesSleep(100);
            }
            // 订阅Tag组
            if (newClientTagList.length > 0) {
              for (var i = 0; i < newClientTagList.length; i++) {
                var tagTopicArray = newClientTagList[i].toString().split('/')
                var client_code = tagTopicArray[0]
                var tag_group_code = tagTopicArray[1]

                var clientGroupMultyKey = 'SCADA_CHANGE/' + client_code + '/' + tag_group_code
                if (newClientTagGroupList.indexOf(clientGroupMultyKey) < 0) {
                  newClientTagGroupList.push(clientGroupMultyKey)
                  this.topicSubscribe(clientGroupMultyKey)
                }
              }
            }

            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())
            // const res = JSON.parse(message.toString())
            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, error => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // this.clientMqtt.disconnect()
      // this.clientMqtt = null
      this.clientMqtt.end()
      this.mqttConnStatus = false
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < newTagList.length; i++) {
        var readTag = {}
        readTag.tag_key = newTagList[i].toString()
        readTagArray.push(readTag)
      }
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
      }
      console.log('环境process.env.NODE_ENV：' + process.env.NODE_ENV)
      console.log('调用接口：' + path)

      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');

              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  if (tagKey === 'OP1130/MesStatus/SerialInfo') {
                    if (tagValue !== '') {
                      const SerialInfo = tagValue.split(',')// 允许扫描状态(0/1)，订单号，机型，工件标识，模组码，proceduce_id
                      this.allowScan = SerialInfo[0]
                      this.orderNo = SerialInfo[1]
                      this.smallModel = SerialInfo[2]
                      this.workFlag = SerialInfo[3]
                      this.mzBarcode = SerialInfo[4]
                      this.proceduceId = SerialInfo[5]
                      this.getMaterialData()
                    }
                  } else if (tagKey === 'OP1131/BarStatus/BarS_GetBarCodeResult') {
                    this.materialBarcode = tagValue
                    this.handleMaterialScan()
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      console.log('MQTT收到来自', channel, '的消息', message.toString())
      // var jsonData = JSON.parse(message)
      // var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        var TagKey = jsonData.TagKey
        // var TagCode = jsonData.TagCode
        // var TagOldValue = jsonData.TagOldValue
        var TagNewValue = jsonData.TagNewValue
        if (TagKey === 'OP1130/MesStatus/SerialInfo') {
          if (TagNewValue !== '') {
            const SerialInfo = TagNewValue.split(',')// 允许扫描状态(0/1)，订单号，机型，工件标识，模组码，proceduce_id
            this.allowScan = SerialInfo[0]
            this.orderNo = SerialInfo[1]
            this.smallModel = SerialInfo[2]
            this.workFlag = SerialInfo[3]
            this.mzBarcode = SerialInfo[4]
            this.proceduceId = SerialInfo[5]
            this.getMaterialData()
          }
        } else if (TagKey === 'OP1131/BarStatus/BarS_GetBarCodeResult') {
          this.materialBarcode = TagNewValue
          this.handleMaterialScan()
        }
      }
    },

    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate() + ' ' + dt.getHours() + ':' + dt.getMinutes() + ':' + dt.getSeconds()
      }
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
            // console.log(inputDom.type, i);
          }
        }
      }
    },
    ManualMaterialScan() {
      if (this.materialBarcode === '') {
        this.$message({
          message: '请输入物料条码',
          type: 'warning'
        })
        return
      }
      this.handleMaterialScan()
    },
    getMaterialData() {
      this.materialTableData = []
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        proceduce_id: this.proceduceId
      }
      MesMePdureMaterialSel(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
          if (res.count > 0) {
            this.materialTableData = res.data
          }
        })
        .catch(() => {
          this.$message({
            message: '查询物料数据异常',
            type: 'error'
          })
        })
    },
    handleMaterialScan() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        material_barcode: this.materialBarcode,
        proceduce_id: this.proceduceId
      }
      MesMePdureMaterialScan(query)
        .then(res => {
          console.log(res)
          this.materialScanFinish = false
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            if (res.result === 'Y') {
              // 扫描完成
              this.materialScanFinish = true
              this.$message({
                message: '物料已全部扫描完成',
                type: 'success',
                duration: 5000
              })
            }
          }

          this.getMaterialData()
        })
        .catch(() => {
          this.$message({
            message: '电芯上线校验异常',
            type: 'error'
          })
        })
    },
    handleNgPass() {

    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  height: calc(100vh - 80px);
}
.orderButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 15px;
  button {
    margin-left: 0 !important;
  }
  .buttonone {
    width: 90px;
    height: 30px;
    margin-top: -10px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonone:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .buttonTwo {
    margin-top: 5px !important;
    width: 90px;
    height: 30px;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonTwo:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .btnRequestPallet1{
    width: 100px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #57EF99, #13ce66);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #57EF99, -1px 1px 1px 1px #13ce66, -1px 3px 18px 0px #13ce66;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .btnRequestPallet1:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #57EF99, -1px 1px 1px 0.5px #13ce66, -1px 4px 10px 1px #13ce66;
  }
  .btnRequestPallet0{
    width: 100px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #AAAAAA, #AAAAAA);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #AAAAAA, -1px 1px 1px 1px #AAAAAA, -1px 3px 18px 0px #AAAAAA;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .btnRequestPallet0:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #AAAAAA, -1px 1px 1px 0.5px #AAAAAA, -1px 4px 10px 1px #AAAAAA;
  }
}
// .wrapDes {
//   margin-top: 30px;
// }
.stepStyle {
  padding: 10px 0;
  padding-bottom: 30px;
}
.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
}
.el-menu {
  display: flex;
  justify-content: space-between;
  overflow: auto;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: #ffffff !important;
}
::v-deep .el-menu--horizontal > .el-menu-item {
  flex: 1;
  text-align: center;
  margin: 0 5px;
}
.flagActive {
  background-color: #e8efff;
}
.orderrecipe {
  display: flex;
  align-items: center;
}
.cardFirst {
  margin-bottom: 10px;
}
.statuStyle {

  justify-content: space-around;
  p {
    display: flex;
    align-items: center;
    color: #333333;
    margin: 0;
    margin-bottom: 10px;
    span {
      display: flex;
      align-items: center;
    }
    .commonsatu {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: block;
    }
    .statuOne {
      background-color: #13ce66;
    }
    .statuZero {
      background-color: #cccccc;
    }
    .statuTwo {
      background-color: #ff4949;
    }
    .statuSecond {
      background-color: #cccccc;
    }
  }
}
::v-deep .el-card__header {
  text-align: center;
  background: #79a0f1;
  color: #ffffff;
  padding: 10px 0;
}
::v-deep .el-tabs--border-card > .el-tabs__header {
  background-color: #ffffff;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333 !important;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #ffffff !important;
    background: #79a0f1;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #333333;
}
::v-deep .el-table th {
  background-color: #e8efff !important;
}
::v-deep .el-table__body tr.current-row > td {
  background-color: #79a0f1 !important;
  color: #ffffff;
}
::v-deep .el-input__inner{
  padding-right: 40px;
}
.keyboard-mask{
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 99;
    left: 0;
    right: 0;
}
.wrapimin{
  position: relative;
  width: 100%;
  img{
    position: absolute;
    right: 7px;
    top: -3px;
    width: 45px;
    z-index: 2;
  }
}
</style>
