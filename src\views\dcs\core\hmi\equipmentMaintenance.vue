<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-8 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item :label="$t('lang_pack.equipment.deviceCode')">
                                <!-- 设备编号 -->
                                <el-input v-model="query.flow_main_code" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item :label="$t('lang_pack.equipment.DeviceName')">
                                <!-- 设备名称 -->
                                <el-input v-model="query.flow_main_des" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item :label="$t('lang_pack.sortingResults.stationCode')">
                                <!-- 工位号 -->
                                <el-select v-model="query.station_code">
                                    <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code" >
                                        <span style="float: left">{{ item.station_code }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormFirst col-md-8 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item :label="$t('lang_pack.taskScheduling.status')">
                                <!-- 状态 -->
                                <el-input v-model="query.flow_main_des" clearable size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-4 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="never" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation :permission="permission">
            </crudOperation>
            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.equipment.stationCode')" prop="station_code">
                                <!-- 工位号 -->
                                <fastCode fastcode_group_code="STATION_CODE" :fastcode_code.sync="form.station_code" control_type="select" size="mini" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.equipment.PartNumber')" prop="stock_group_code">
                                <!-- 设备编号 -->
                                <el-input v-model="form.stock_group_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.equipment.DeviceName')" prop="stock_group_des">
                                <!-- 设备名称 -->
                                <el-input v-model="form.stock_group_des" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.equipment.PartNumber')" prop="part_code">
                                <!-- 零件编号 -->
                                <el-input v-model="form.part_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.equipment.PartName')" prop="stock_des">
                                <!-- 零件名称 -->
                                <el-input v-model="form.stock_des" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.equipment.TheoreticalLifespan')" prop="stock_order">
                                <!-- 理论寿命 -->
                                <el-input v-model="form.stock_order" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.equipment.ServiceLife')" prop="stock_order">
                                <!-- 使用寿命 -->
                                <el-input v-model="form.stock_order" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.taskScheduling.status')" prop="location_x">
                                <!-- 状态 -->
                                <el-input v-model="form.location_x" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>
                    <!--表格渲染-->
                    <el-table border  ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" :cell-style="crud.cellStyle" highlight-current-row :height="height"
                        @selection-change="crud.selectionChangeHandler">
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column  v-if="1 == 0" prop="flow_main_id" label="id" />
                        <!-- 工位号 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_num"
                            :label="$t('lang_pack.equipment.stationCode')" />
                        <!-- 设备编号 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_status"
                            :label="$t('lang_pack.equipment.deviceCode')" />
                        <!-- 设备名称 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="plan_date"
                            :label="$t('lang_pack.equipment.DeviceName')" />
                        <!-- 零件编号 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="model_type"
                            :label="$t('lang_pack.equipment.PartNumber')" />
                        <!-- 零件名称 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="cut_code"
                            :label="$t('lang_pack.equipment.PartName')" />
                        <!-- 理论寿命 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="cut_type"
                            :label="$t('lang_pack.equipment.TheoreticalLifespan')" />
                        <!-- 使用寿命 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="dxf_code"
                            :label="$t('lang_pack.equipment.ServiceLife')" />
                        <!-- 状态 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="nc_code"
                            :label="$t('lang_pack.equipment.Status')" />
                        <el-table-column :label="$t('lang_pack.commonPage.operate')" width="300" align="center"
                            fixed="right">
                            <!-- 操作 -->
                            <template slot-scope="scope">
                                <div class="cuting">
                                    <p>复位当前寿命</p>
                                    <p @click="handleDialog(scope.row, '1')">更换零件</p>
                                    <p @click="handleDialog(scope.row, '2')">更换记录</p>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
        <el-dialog :title="title" :visible.sync="dialogVisible" :width="width" :before-close="handleClose">
            <el-card shadow="never" class="wrapCard"  v-if="type == 1">
                <el-form ref="query" :inline="true" size="small">
                    <div class="wrapElForm">
                        <div class="wrapElFormFirst col-md-12 col-12">
                            <div class="formChild col-md-4 col-12">
                                <el-form-item :label="$t('lang_pack.equipment.stationCode')">
                                    <!-- 工位号 -->
                                    <el-input placeholder="请输入任务号" class="filter-item" />
                                </el-form-item>
                            </div>
                            <div class="formChild col-md-4 col-12">
                                <el-form-item :label="$t('lang_pack.equipment.deviceCode')">
                                    <!-- 设备编号 -->
                                    <el-input placeholder="请输入钢板型号" class="filter-item" />
                                </el-form-item>
                            </div>
                            <div class="formChild col-md-4 col-12">
                                <el-form-item :label="$t('lang_pack.equipment.PartNumber')">
                                    <!-- 零件编号 -->
                                    <el-input placeholder="请输入目标切割机" class="filter-item" />
                                </el-form-item>
                            </div>
                        </div>
                        <div class="wrapElFormSecond formChild col-md-4 col-12">
                            <el-form-item>
                                <!-- <rrOperation /> -->
                                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search">{{
                                    $t('lang_pack.commonPage.search') }}</el-button>
                                <!-- 搜索 -->
                                <el-button class="filter-item" size="small" icon="el-icon-refresh-left">{{
                                    $t('lang_pack.commonPage.reset') }}</el-button> <!-- 重置 -->
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
                <el-table border ref="table" v-loading="loading" :data="tableData"
                    :row-key="row => row.id" :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                    <!-- 零件号 -->
                    <el-table-column  :show-overflow-tooltip="true" align="center" prop="qgw"
                        :label="$t('lang_pack.equipment.PartNumber')" />
                    <!-- 零件描述 -->
                    <el-table-column  :show-overflow-tooltip="true" align="center" prop="rwh"
                        :label="$t('lang_pack.equipment.PartDescription')" />
                    <el-table-column :label="$t('lang_pack.commonPage.operate')" width="300" align="center" fixed="right">
                        <!-- 操作 -->
                        <template slot-scope="scope">
                            <div class="cuting">
                                <p>选择</p>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
            <el-table v-if="type == 2" border ref="table" v-loading="loading" :data="tableData" :row-key="row => row.id"
                :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                <!-- 工位号 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="qgw"
                    :label="$t('lang_pack.equipment.stationCode')" />
                <!-- 设备编号 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="cxh"
                    :label="$t('lang_pack.equipment.deviceCode')" />
                <!-- 原零件号 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="jhqgsj"
                    :label="$t('lang_pack.equipment.OriginalPartNumber')" />
                <!-- 原零件名称 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="cxscsj"
                    :label="$t('lang_pack.equipment.OriginalPartName')" />
                <!-- 更换后零件号 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="jhqgsj"
                    :label="$t('lang_pack.equipment.PartNumberAfterReplacement')" />
                <!-- 更换后零件名称 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="cxscsj"
                    :label="$t('lang_pack.equipment.PartNameAfterReplacement')" />
                <!-- 更换时间 -->
                <el-table-column  :show-overflow-tooltip="true" align="center" prop="jhqgsj"
                    :label="$t('lang_pack.equipment.ReplacementTime')" />
            </el-table>
        </el-dialog>
    </div>
</template>
    
<script>
import crudFlowMain from '@/api/core/flow/rcsFlowMain'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

    enable_flag: 'Y'
}
export default {
    name: 'EQUIPMENTMAINTENANCE',
    components: {
        crudOperation,
        rrOperation,
        udOperation,
        pagination,
    },
    cruds() {
        return CRUD({
            title: '设备维护',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'flow_main_id',
            // 排序
            sort: ['flow_main_id asc'],
            // CRUD Method
            crudMethod: { ...crudFlowMain },
            // 按钮显示
            optShow: {
                add: true,
                edit: true,
                del: true,
                down: false,
                reset: true
            }
        })
    },
    // 数据字典
    dicts: ['ENABLE_FLAG'],
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'rcs_flow_main:add'],
                edit: ['admin', 'rcs_flow_main:edit'],
                del: ['admin', 'rcs_flow_main:del'],
                down: ['admin', 'rcs_flow_main:down']
            },
            tableData: [],
            type: '',
            title:'',
            width:'60%',
            dialogVisible: false
        }
    },

    mounted: function () {
        const that = this
        that.$refs.table.doLayout()
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
        const query = {
            user_name: Cookies.get('userName'),
            enable_flag: 'Y'
        }
    },
    methods: {
        // 开始 "新建/编辑" - 之前
        [CRUD.HOOK.beforeToCU](crud) {
            // crud.form.client_id_list = crud.form.client_id_list === '' ? '' : crud.form.client_id_list.split(',')
            return true
        },
        // 提交前做的操作
        [CRUD.HOOK.beforeSubmit](crud) {
            // crud.form.client_id_list = this.form.client_id_list === '' ? '' : this.form.client_id_list.join(',')
            return true
        },
        handleDialog(record, type) {
            this.tableData = []
            if (type == 1) {
                this.title = '更换零件'
                this.tableData = record.info || []
            }
            if (type == 2) {
                this.title = '更换记录'
                this.tableData = record.info2 || []
            }
            this.type = type
            this.dialogVisible = true
        },
        handleClose() {
            this.dialogVisible = false
            this.visible = false
        },
    }
}
</script>
<style lang="scss" scoped>
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
    padding: 0px 0px 0px 0px;
}

::v-deep .step-attr-dialog .el-dialog__body {
    padding: 0px;
    overflow-y: auto;
}

::v-deep .el-upload-dragger {
    width: 200px;
}

.cuting {
    display: flex;

    p {
        width: 80px;
        background: #00479d;
        margin: 2px 3px;
        color: #fff;
        cursor: pointer;
        border: 1px solid #fff;
        border-radius: 3px;
    }
}
</style>
    