
#svgElement {
    background-size: 20px 20px, 20px 20px, 10px 10px, 10px 10px;
    background-color: #0c3967;
    background-position: left -1px top -1px, left -1px top -1px, left -1px top -1px, left -1px top -1px;
    background-image: linear-gradient(90deg,#372e2e 1px,transparent 0),linear-gradient(180deg,#2b2b2b 1px,transparent 0),linear-gradient(90deg,#2b2b2b 1px,transparent 0),linear-gradient(180deg,#2b2b2b 1px,transparent 0);
    height: 100%;
    width: 100%;
    border: 1px solid #2C88B3;
}
#chartSop {
    position: relative;
    width: 800px;
    height: 600px;
    border: 0px solid #dfdfdf;
}
.title {
    font-size:18px;
    font-weight: bold;
}
.unselectable {
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color:#dfdfdf;
}

.connector {
    cursor: crosshair;
    opacity: 0;
}

.connector.active {
    opacity: 1;
    fill: white;
    stroke: #bbbbbb;
    stroke-width: 1px;
}

.connector:hover {
    stroke: red;
}
.tool_btn {
    cursor:pointer;
    opacity: 0;
}

.tool_btn.active {
    opacity: 1;
    fill: white;
    stroke: #bbbbbb;
    stroke-width: 1px;
}

#svgElement .selection {
    stroke: lightblue;
    fill: lightblue;
    fill-opacity: 0.8;
    display: none;
}

#svgElement .selection.active {
    display: block;
}
.commodity-sign-wrap{
	animation:blink 1s infinite;
	-webkit-animation:blink 1s infinite; /*Safari and Chrome*/
}
@keyframes blink{
	0%{
		opacity: 0;
	}
	50%{
		opacity: 100;
	}
	100%{
		opacity: 0;
	}
}
@-webkit-keyframes blink{
	0%{
		opacity: 0;
	}
	50%{
		opacity: 100;
	}
	100%{
		opacity: 0;
	}
}