<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="modalTitle" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <div class="stock-info">
      <div v-for="(item,index) in stackData" :key="index" class="entirety">
        <div class="column"><span>{{ layer[index] + '层垛位：' }}</span><div class="box">{{ item.stock }}</div><svg-icon icon-class="Unlocking" /></div>
        <div class="column"><span>料框号：</span><div class="box">{{ item.lkh }}</div></div>
        <div class="column"><span>料种：</span><div class="box">{{ item.lz }}</div></div>
        <div class="column"><span>批次号：</span><div class="box">{{ item.pch }}</div></div>
        <div class="column"><span>合金牌号：</span><div class="box">{{ item.hjph }}</div></div>
        <div class="column"><span>重量：</span><div class="box">{{ item.zl }}</div></div>
        <div class="column"><span>X坐标：</span><div class="box">{{ item.x }}</div></div>
        <div class="column"><span>Y坐标：</span><div class="box">{{ item.y }}</div></div>
        <div class="column"><span>Z坐标：</span><div class="box">{{ item.z }}</div><div class="box del">删除料框</div></div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'SELECTMODAL',
  props: {
    dict: {
      type: [Object, Array],
      default: () => ({})
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      modalTitle: '查看明细',
      modalWidth: '88%',
      stackData: [
        { stock: 'Y1A06081', lkh: '8012', lz: '6系PIR边角废铝', pch: 'c202411210004', hjph: '6014', zl: '4030kg', x: '29.000m', y: '18.346m', z: '1.350m' },
        { stock: 'Y1A06082', lkh: '8012', lz: '6系PIR边角废铝', pch: 'c202411210004', hjph: '6014', zl: '4030kg', x: '29.000m', y: '18.346m', z: '1.350m' },
        { stock: 'Y1A06083', lkh: '8012', lz: '6系PIR边角废铝', pch: 'c202411210004', hjph: '6014', zl: '4030kg', x: '29.000m', y: '18.346m', z: '1.350m' },
        { stock: 'Y1A06084', lkh: '8012', lz: '6系PIR边角废铝', pch: 'c202411210004', hjph: '6014', zl: '4030kg', x: '29.000m', y: '18.346m', z: '1.350m' }
      ],
      layer: {
        '0': '一',
        '1': '二',
        '2': '三',
        '3': '四'
      }
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
      console.log('111')
    },
    handleClose(done) {
      done()
    }
  }

}

</script>

<style scoped lang="less">
.stock-info{
  display: flex;
  .entirety{
    .column{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      span{
        font-size: 18px;
        color: #fff;
        font-weight: 600;
        display: block;
        min-width: 100px;
        text-align: right;
      }
      svg{
        font-size: 30px;
        margin-left: 30px;
      }
      .box{
        font-size: 18px;
        border: 2px solid #57d6f6;
        padding: 5px;
        border-radius: 5px;
        background-color: #084aa0;
        color: #fff;
        width: 150px;
        text-align: center;
      }
      .del{
        margin-left: 5px;
        cursor: pointer;
      }
    }
  }
}
</style>
