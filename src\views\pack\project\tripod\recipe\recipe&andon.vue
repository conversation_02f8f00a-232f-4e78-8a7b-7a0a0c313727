<template>
  <div class="app-container">
    <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
      <el-col :span="12">
        <el-card shadow="never" class="wrapCard">
          <div slot="header" class="wrapTextSelect">
            <span>配方管理</span>
          </div>
          <el-form ref="query" :inline="true" size="small" label-width="100px">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-12 col-12">
                <div class="formChild col-md-12 col-12 barcode">
                  <el-form-item label="条码扫描：">
                    <!-- 条码扫描： -->
                    <el-input ref="barCode" v-model="barCode" clearable size="small" @input="getBarCode" />
                  </el-form-item>
                  <el-button type="primary" @click="manInput()">人工输入</el-button>
                </div>
                <div v-for="(item,index) in filteredArr" :key="index" class="formChild col-md-12 col-12">
                  <el-form-item :label="item.label + ':'">
                    <el-input v-model="item.tag_value" :disabled="item.disabled" clearable size="small" />
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormFirst col-md-12 col-12">
                <div class="formChild col-md-4 col-12" style="padding-left: 15px;">
                  <el-form-item :label="monitorData.aisStatusData[monitorData.aisStatusData.length - 2].label + ':'">
                    <!-- 生产类别： -->
                    <fastCode fastcode_group_code="TASK_TYPE" :fastcode_code.sync="monitorData.aisStatusData[monitorData.aisStatusData.length - 2].tag_value" control_type="radio" size="small" @Change="getRadioValue" />
                  </el-form-item>
                </div>
                <div v-if="false" class="formChild col-md-8 col-12">
                  <el-form-item :label="monitorData.aisStatusData[monitorData.aisStatusData.length - 1].label + ':'">
                    <!-- 重工数量 -->
                    <el-input v-model="monitorData.aisStatusData[monitorData.aisStatusData.length - 1].tag_value" :disabled="monitorData.aisStatusData[monitorData.aisStatusData.length - 1].disabled" clearable size="small" />
                  </el-form-item>
                </div>
              </div>
            </div>
          </el-form>
          <el-button type="primary" class="formula" @click="mesQuery">
            向EAP请求配方
          </el-button>
          <el-table
            ref="table"
            border
            :data="recipeData"
            :row-key="row => row.id"
            :height="height"
          >
            <!-- 批號 -->
            <el-table-column :show-overflow-tooltip="true" align="center" label="批號" prop="lot_num" />
            <!-- 料號 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="model_type"
              label="料號"
            />
            <!-- 板長 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="m_length"
              label="板長"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="m_tickness"
              width="150"
              label="板厚"
            />
            <!-- 板寬 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="m_width"
              label="板寬"
            />
            <!-- 板序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="panel_index"
              label="板序"
            />
            <!-- 週期 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="cycle_period"
              label="週期"
            />
            <!-- 鐳射批號 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="laser_batch_no"
              label="鐳射批號"
            />
            <!-- 雙層板數 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="double_layer_count"
              label="雙層板數"
            />
            <!-- 客戶料號 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="customer_mn"
              label="客戶料號"
            />
            <!-- 廠內訂單號 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="plant_code"
              label="廠內訂單號"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="m_tickness"
              label="板厚"
            />
            <!-- 排版數 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="typesetting_no"
              label="排版數"
            />
            <!-- QC代碼 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="qc_code"
              width="120"
              label="QC代碼"
            />
            <!-- 出貨地 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="ship_address"
              label="出貨地"
            />
            <!-- 標籤到期日 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="label_due_date"
              label="標籤到期日"
            />
            <!-- 備註 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="note"
              label="備註"
            />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never" class="wrapCard">
          <div slot="header" class="wrapTextSelect">
            <span>安灯操作</span>
            <div class="wrapSearch">
              <div class="wrappstyle">
                <p>
                  <span
                    :class="controlStatus.ais_status === '1' ? 'wholeline wholelinenormal' :
                      (controlStatus.ais_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                  />
                  <span class="statuText">运行</span>
                </p>
                <p>
                  <span
                    :class="controlStatus.plc_status === '1' ? 'wholeline wholelinenormal' :
                      (controlStatus.plc_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                  />
                  <span class="statuText">PLC</span>
                </p>
                <p>
                  <span
                    :class="controlStatus.eap_status === '1' ? 'wholeline wholelinenormal' :
                      (controlStatus.eap_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                  />
                  <span class="statuText">EAP</span>
                </p>
              </div>
            </div>
          </div>
          <div class="modeType" style="margin-bottom: 30px;">
            <div style="display: flex;flex-wrap: wrap;">
              <transition v-for="item in monitorData.stateData" :key="item.id" name="el-fade-in">
                <el-button
                  v-show="show"
                  type="primary"
                  class="transition-box"
                  :class="{'active': item.tag_value === '1'}"
                  @click="changeDeviceStatus(item)"
                >{{ item.name }}</el-button>
              </transition>
            </div>
          </div>
          <el-table
            ref="table"
            border
            :data="andonData"
            :row-key="row => row.id"
            :height="height"
            :row-class-name="tableRowClassName"
          >
            <!-- 时间 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="item_date" label="时间" />
            <!-- 安灯类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="andon_type"
              label="安灯类型"
            />
            <!-- 偶发日期 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="happen_date"
              label="偶发日期"
            />
            <!-- 重置日期 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="reset_date"
              label="重置日期"
            />
            <!-- 消耗时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="cost_times"
              label="消耗时间"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-right: 0px; padding: 0px;margin-top: 10px">
      <el-col :span="24">
        <el-card shadow="never" class="wrapCard">
          <el-table
            ref="table"
            border
            :data="opelogData"
            :row-key="row => row.id"
            height="170"
          >
            <!-- 时间 -->
            <el-table-column :show-overflow-tooltip="true" align="center" label="时间" prop="item_date" />
            <!-- 事件 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="operation_code"
              label="事件"
            />
            <!-- 方向 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="operation_type"
              label="方向"
            />
            <!-- 内容 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="operation_des"
              label="内容"
              width="700"
            />
            <!-- 状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="operation_status"
              label="状态"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :fullscreen="false"
      :show-close="true"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      title="报警信息"
      width="400px"
      :visible.sync="aisNoteFlag"
    >
      <span>{{ monitorData.aisStatusData.find(e=>e.tag_code === 'Note').tag_value }}</span>
    </el-dialog>
    <el-dialog
      :fullscreen="false"
      :show-close="true"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      title="安灯工号录入"
      width="400px"
      :visible.sync="andonFlag"
    >
      <span>安灯人员工号：</span><el-input v-model="andonUserName" placeholder="请输入安灯人员工号" style="width: 200px;" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="andonFlag = false">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { recipeSelect, andonSelect, EapProjectSfjdOpeLogIns, EapProjectSfjdOpeLogSelect } from '@/api/eap/project/sfjd/eapSfjd'
export default {
  name: 'PACK_PROJ_TRIPOD_RECIPE',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 550,
      monitorData: {
        stateData: [
          { name: '专案模式', id: 1, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'ProjectMode', tag_value: '0' },
          { name: '维修模式', id: 2, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'MaintenanceMode', tag_value: '0' },
          { name: '制造叫修', id: 3, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'RepairMode', tag_value: '0' },
          { name: '机动派工', id: 4, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'MobileDispatch', tag_value: '0' },
          { name: '月保养', id: 5, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'MonthMaintenance', tag_value: '0' },
          { name: '周保养', id: 6, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'WeekMaintenance', tag_value: '0' },
          { name: '班保养', id: 7, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'ShiftMaintenance', tag_value: '0' },
          { name: '临时保养', id: 8, client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'TempMaintenance', tag_value: '0' }
        ],
        aisStatusData: [
          { label: '工单批号', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'BatchNo', tag_value: '', disabled: true },
          { label: '工单批号(ALL)', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'BatchNoAll', tag_value: '', disabled: true }, // 不需要显示，只需要写入
          { label: '工单厂别', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'OrderFactory', tag_value: '', disabled: true }, // 不需要显示，只需要写入
          { label: '请求配方', client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'RecipeRequest', tag_value: '', disabled: true }, // 不需要显示，只需要写入
          { label: '报警信息', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'Note', tag_value: '', disabled: true }, // 不需要显示，只需要写入
          { label: '人员工号', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'CurrentUser', tag_value: '', disabled: true },
          { label: '生产类别', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'TaskType', tag_value: '', disabled: false },
          { label: '重工数量', client_code: 'LoadAis', group_code: 'AisStatus', tag_code: 'RepairCount', tag_value: '', disabled: true }
        ]
      },
      show: false,
      recipeData: [],
      andonData: [],
      opelogData: [],
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      controlStatus: {
        ais_status: '0',
        plc_status: '0',
        eap_status: '0'
      },
      barCode: '',
      aisNoteFlag: false,
      timer: null,
      currentFlag: true,
      andonFlag: false,
      andonUserName: '',
      andonObj: {}
    }
  },
  dicts: ['TASK_TYPE'],
  computed: {
    filteredArr() {
      const Arr = ['TaskType', 'RepairCount', 'BatchNoAll', 'OrderFactory', 'RecipeRequest', 'Note']
      return this.monitorData.aisStatusData.filter(item => !Arr.includes(item.tag_code))
    }
  },
  watch: {
    barCode: {
      handler(newVal, oldVal) {
        if (newVal.length < 10 && this.currentFlag) {
          this.currentFlag = false
          this.monitorData.aisStatusData.find(e => e.tag_code === 'CurrentUser').tag_value = newVal
          this.barCode = ''
        }
      },
      deep: true
    }
  },
  mounted: function() {
    this.show = !this.show
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 630
    }
  },
  beforeCreate() {
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  created: function() {
    this.$nextTick(() => {
      this.$refs.barCode.focus()
    })
    // 配方
    this.getRecipe()
    // 安灯
    this.getAndon()

    // 查询事件
    this.getOpeLog()

    this.timer = setInterval(() => {
      this.getRecipe()
      this.getAndon()
      this.getOpeLog()
    }, 1000 * 10)

    this.getCellIp()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 0) {
        return 'pink-row'
      }
      return 'gray-row'
    },
    getRadioValue(e) {
      const data = this.monitorData.aisStatusData.find(item => item.tag_code === 'RepairCount')
      if (e === 'Normal') {
        data.tag_value = ''
        data.disabled = true
        return
      }
      data.disabled = false
    },
    getBarCode(e) {
      console.log(e)
      this.timerBarCode && clearTimeout(this.timerBarCode)
      this.timerBarCode = setTimeout(() => {
        if (e.length <= 10) {
          this.monitorData.aisStatusData.find(e => e.tag_code === 'CurrentUser').tag_value = e
          this.barCode = ''
        } else {
          this.monitorData.aisStatusData.find(e => e.tag_code === 'BatchNoAll').tag_value = e
          this.monitorData.aisStatusData.find(e => e.tag_code === 'OrderFactory').tag_value = e.split('-')[0]
          this.monitorData.aisStatusData.find(e => e.tag_code === 'BatchNo').tag_value = e.split('-')[1]
          this.barCode = ''
        }
      }, 1000 * 5)
    },
    manInput() {
      if (!this.barCode) {
        this.$message({ message: '请扫描或输入条码', type: 'warning' })
        return
      }
      if (this.barCode.length <= 10) {
        this.monitorData.aisStatusData.find(e => e.tag_code === 'CurrentUser').tag_value = this.barCode
        this.barCode = ''
      } else {
        this.monitorData.aisStatusData.find(e => e.tag_code === 'BatchNoAll').tag_value = this.barCode
        this.monitorData.aisStatusData.find(e => e.tag_code === 'OrderFactory').tag_value = this.barCode.split('-')[0]
        this.monitorData.aisStatusData.find(e => e.tag_code === 'BatchNo').tag_value = this.barCode.split('-')[1]
        this.barCode = ''
      }
    },
    getRecipe() {
      const query = {
        page: 1,
        size: 1000
      }
      recipeSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.recipeData = defaultQuery.data
          }
        }
      }).catch(() => {
        this.$message({
          message: '配方查询异常',
          type: 'error'
        })
      })
    },
    getAndon() {
      const query = {
        page: 1,
        size: 1000
      }
      andonSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.andonData = defaultQuery.data
          }
        }
      }).catch(() => {
        this.$message({
          message: '安灯查询异常',
          type: 'error'
        })
      })
    },
    getOpeLog() {
      const query = {
        page: 1,
        size: 1000
      }
      EapProjectSfjdOpeLogSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.opelogData = defaultQuery.data
          }
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getTagValue() {
      var readTagArray = []
      this.monitorData.stateData.forEach(item => {
        readTagArray.push({ tag_key: item.client_code + '/' + item.group_code + '/' + item.tag_code })
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach(e => {
                this.monitorData.stateData.forEach(k => {
                  if ((k.client_code + '/' + k.group_code + '/' + k.tag_code) === e.tag_key) {
                    k.tag_value = e.tag_value
                  }
                })
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://*************:8083'  + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        var aisClientCode = 'LoadAis'
        var plcClientCode = 'LoadPlc'
        var eapClientCode = 'LoadEap'
        this.topicSubscribe('SCADA_STATUS/' + aisClientCode)
        this.topicSubscribe('SCADA_BEAT/' + aisClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        this.topicSubscribe('SCADA_STATUS/' + eapClientCode)
        this.topicSubscribe('SCADA_BEAT/' + eapClientCode)
        Object.keys(this.monitorData).forEach(item => {
          this.monitorData[item].forEach(e => {
            this.topicSubscribe('SCADA_CHANGE/' + e.client_code + '/' + e.group_code + '/' + e.tag_code)
          })
        })
        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('SCADA_BEAT/') >= 0) {
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf('SCADA_BEAT/LoadEap') >= 0) {
              if (this.controlStatus.eap_status !== '2') {
                this.controlStatus.eap_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPlc') >= 0) {
              if (this.controlStatus.plc_status !== '2') {
                this.controlStatus.plc_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadAis') >= 0) {
              if (this.controlStatus.ais_status !== '2') {
                this.controlStatus.ais_status = heartBeatValue
              }
            }
          } else if (topic.indexOf('SCADA_STATUS/') >= 0) { // 通讯结果状态
            var statusValue = jsonData.Status
            if (topic.indexOf('SCADA_STATUS/LoadEap') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.eap_status = '2'
                this.$message({ message: 'EAP通讯中断', type: 'error' })
              } else {
                if (this.controlStatus.eap_status === '2') {
                  this.controlStatus.eap_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPlc') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.plc_status = '2'
                this.$message({ message: 'PLC通讯中断', type: 'error' })
              } else {
                if (this.controlStatus.plc_status === '2') {
                  this.controlStatus.plc_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadAis') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.ais_status = '2'
                this.$message({ message: 'AIS通讯中断', type: 'error' })
              } else {
                if (this.controlStatus.ais_status === '2') {
                  this.controlStatus.ais_status = '1'
                }
              }
            }
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            Object.keys(this.monitorData).forEach(item => {
              this.monitorData[item].forEach(e => {
                var tag_key = e.client_code + '/' + e.group_code + '/' + e.tag_code
                if (tag_key === jsonData.TagKey) {
                  e.tag_value = jsonData.TagNewValue
                }
                if (tag_key === 'LoadAis/AisStatus/Note' && e.tag_value !== '') {
                  this.aisNoteFlag = true
                }
              })
            })
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    mesQuery() {
      if (this.barCode && this.barCode.length < 16) {
        this.$message({ message: '请输入大于等于16位的正确条码', type: 'warning' })
        return
      }
      if (this.barCode && this.barCode.indexOf('-') < 0) {
        this.$message({ message: '条码扫描格式如F001-M1909120023', type: 'warning' })
        return
      }
      if (!this.barCode) {
        this.$message({ message: '请扫描或输入条码', type: 'warning' })
        return
      }
      if (!this.monitorData.aisStatusData.find(e => e.tag_code === 'BatchNo').tag_value) {
        this.$message({ message: '请输入工单批号', type: 'warning' })
        return
      }
      if (!this.monitorData.aisStatusData.find(e => e.tag_code === 'CurrentUser').tag_value) {
        this.$message({ message: '请输入人员工号', type: 'warning' })
        return
      }
      var val = this.monitorData.aisStatusData[this.monitorData.aisStatusData.length - 2].tag_value
      var num = this.monitorData.aisStatusData[this.monitorData.aisStatusData.length - 1].tag_value
      if (!val) {
        this.$message({ message: '请选择生产类别', type: 'warning' })
        return
      }
      if (val === 'Repair' && !num) {
        this.$message({ message: '请输入重工数量', type: 'warning' })
        return
      }
      this.monitorData.aisStatusData.forEach(item => {
        var tagKey = item.client_code + '/' + item.group_code + '/' + item.tag_code
        if (item.client_code !== 'LoadPlc') {
          if (item.tag_code === 'OrderFactory') {
            item.tag_value = this.barCode.split('-')[0]
          }
          if (item.tag_code === 'BatchNo') {
            item.tag_value = this.barCode.split('-')[1]
          }
          if (item.tag_code === 'RecipeRequest') {
            item.tag_value = `${this.barCode.split('-')[0]}_${this.barCode.split('-')[1]}_S`
          }
          if (item.tag_code === 'BatchNoAll') {
            item.tag_value = this.barCode
          }
          var sendJson = {}
          var rowJson = []
          var newRow = {
            TagKey: tagKey,
            TagValue: item.tag_value
          }
          rowJson.push(newRow)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + item.client_code
          this.sendMessage(topic, sendStr)
        }
      })
      const operation_des = {
        'userID': 'Client',
        'andonInfo': {
          'station_id': '1',
          'andon_mode': '0',
          'andon_type': 'ProjectMode',
          'current_user': '',
          'CurrentUser': this.monitorData.aisStatusData.find(e => e.tag_code === 'CurrentUser').tag_value,
          'TaskType': this.monitorData.aisStatusData.find(e => e.tag_code === 'TaskType').tag_value,
          'RepairCount': this.monitorData.aisStatusData.find(e => e.tag_code === 'RepairCount').tag_value
        }
      }
      const query = {
        operation_code: '向EAP请求配方',
        operation_type: 'EQP->EAP',
        operation_des: JSON.stringify(operation_des).replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r'),
        operation_status: '向EAP请求配方'
      }
      EapProjectSfjdOpeLogIns(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message({ type: 'success', message: defaultQuery.msg || '保存记录成功' })
        }
      }).catch(() => {
        this.$message({
          message: '保存异常',
          type: 'error'
        })
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    },
    changeDeviceStatus(item) {
      this.andonUserName = ''
      this.andonObj = item
      this.andonFlag = true
    },
    handleOk() {
      this.andonObj.tag_value = this.andonObj.tag_value === '1' ? '0' : '1'
      const clientData = [
        { client_code: this.andonObj.client_code, group_code: this.andonObj.group_code, tag_code: this.andonObj.tag_code, tag_value: this.andonObj.tag_value },
        { client_code: 'LoadAis', group_code: 'AisAndon', tag_code: 'AndonUser', tag_value: this.andonUserName }
      ]
      clientData.forEach(item => {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: item.client_code + '/' + item.group_code + '/' + item.tag_code,
          TagValue: item.tag_value
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/' + item.client_code
        this.sendMessage(topic, sendStr)
      })
      setTimeout(() => {
        this.andonFlag = false
      }, 200)
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
    .wrapTextSelect {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
   .wrapElForm{
    ::v-deep .barcode{
            display: flex;
            .el-form-item--small{
                width: 90%;
                margin-right: 10px;
                .el-form-item__content{
                    .el-input__inner{
                        background: yellow;
                    }
                }
            }
        }
    }
    .formula{
        width: 100%;
        // background: #E8EFFF;
        padding: 15px 10px;
        text-align: center;
        margin-bottom: 10px;
        font-weight: 550;
        font-size: 18px;
    }
    .wrappstyle {
        display: flex;
        align-items: center;
        p {
            margin: 0 16px !important;
            display: flex;
            align-items: center;
            span {
                font-size: 18px;
                font-weight: 700;
            }
            .statuText {
                line-height: 20px;
                height: 20px;
            }
        }
        p:last-child {
            margin-right: 0 !important;
        }
    }
    .wholeline {
        display: block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
    }
    .modeType{
        .transition-box {
            width: 170px;
            height: 80px;
            border-radius: 4px;
            background-color: #409EFF;
            text-align: center;
            color: #fff;
            box-sizing: border-box;
            margin-left: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 30px;
        }
        ::v-deep .textareaInput{
            width: 50%;
            .el-textarea__inner{
                min-height: 400px !important;
                border: 1px solid #409eff;
            }
        }
        ::v-deep .stationDialog{
            .el-icon-tickets{
                font-size: 26px;
            }
            .station{
                font-size: 26px
            }
            .el-input__inner{
                margin-top: 20px;
                height: 40px;
            }
        }
        .active{
            background-color: #F5B7BF;
            border: 1px solid #F5B7BF;
            color: #000;
        }
    }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
}
::v-deep .el-table .pink-row {
    background: #F5B7BF;
  }
::v-deep .el-table .gray-row {
    background: #BFBFBF;
  }
</style>
