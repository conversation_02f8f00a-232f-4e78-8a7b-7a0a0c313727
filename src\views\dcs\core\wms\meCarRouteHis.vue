<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="库区域:">
                <!-- 库区域 -->
                <el-select v-model="query.ware_house" clearable filterable>
                  <el-option v-for="item in dict.WARE_HOUSE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="天车编码:">
                <!-- 天车编码 -->
                <el-input v-model="query.car_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="天车任务类型:">
                <!-- 天车任务类型 -->
                <el-select v-model="query.car_task_type" clearable filterable>
                  <el-option v-for="item in dict.CAR_TASK_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="步骤状态:">
                <!-- 步骤状态 -->
                <el-input v-model="query.step_status" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.sortingResults.stationCode')" prop="station_code">
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partBarcodeNumber')" prop="part_barcode">
                <!-- 零件条码 -->
                <el-input v-model="form.part_barcode" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.partCode')" prop="part_code">
                <!-- 零件编码 -->
                <el-input v-model="form.part_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.sortingResults.PartType')" prop="part_type">
                <!-- 零件类型 -->
                <el-input v-model="form.part_type" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_route_id }}</el-descriptions-item>
                  <el-descriptions-item label="库区域" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.ware_house }}</el-descriptions-item>
                  <el-descriptions-item label="天车编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_code }}</el-descriptions-item>
                  <el-descriptions-item label="天车任务类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.CAR_TASK_TYPE[props.row.car_task_type] }}</el-descriptions-item>
                  <el-descriptions-item label="步骤代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content"> {{ props.row.step_code }}</el-descriptions-item>
                  <el-descriptions-item label="步骤名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_name }}</el-descriptions-item>
                  <el-descriptions-item label="X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_x }}</el-descriptions-item>
                  <el-descriptions-item label="Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_y }}</el-descriptions-item>
                  <el-descriptions-item label="Z坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_z }}</el-descriptions-item>
                  <el-descriptions-item label="步骤状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_status }}</el-descriptions-item>
                  <el-descriptions-item label="限制步骤ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_start_limit_id }}</el-descriptions-item>
                  <el-descriptions-item label="限制步骤名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_start_limit_step }}</el-descriptions-item>
                  <el-descriptions-item label="限制步骤状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_start_limit_status }}</el-descriptions-item>
                  <el-descriptions-item label="是否需要检查辊道" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_gd_flag == 'Y' ? '是' :'否' }}</el-descriptions-item>
                  <el-descriptions-item label="是否需要检查型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_model_flag == 'Y' ? '是' :'否' }}</el-descriptions-item>
                  <el-descriptions-item label="步骤约束信号点TagID集合" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.if_tags_list }}</el-descriptions-item>
                  <el-descriptions-item label="步骤约束信号点TagID对应值集合" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.if_tags_value }}</el-descriptions-item>
                  <el-descriptions-item label="天车调度任务ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_task_id }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 库区域 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ware_house"
              label="库区域"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
              </template>
            </el-table-column>
            <!-- 天车编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="car_code"
              label="天车编码"
              min-width="100"
              align="center"
            />
            <!-- 天车任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="car_task_type"
              label="天车任务类型"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.CAR_TASK_TYPE[scope.row.car_task_type] }}
              </template>
            </el-table-column>
            <!-- 步骤代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="step_code"
              label="步骤代码"
              min-width="100"
              align="center"
            />
            <!-- 步骤名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="step_name"
              label="步骤名称"
              min-width="100"
              align="center"
            />
            <!-- X坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_x"
              label="X坐标"
              min-width="100"
              align="center"
            />
            <!-- Y坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_y"
              label="Y坐标"
              min-width="100"
              align="center"
            />
            <!-- Z坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_z"
              label="Z坐标"
              min-width="100"
              align="center"
            />
            <!-- 步骤状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="step_status"
              label="步骤状态"
              min-width="100"
              align="center"
            />
            <!-- 限制步骤名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="step_start_limit_step"
              label="限制步骤名称"
              min-width="100"
              align="center"
            />
            <!-- 限制步骤状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="step_start_limit_status"
              label="限制步骤状态"
              min-width="130"
              align="center"
            />
            <!-- 是否检查辊道 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="need_check_gd_flag"
              label="是否检查辊道"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.need_check_gd_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否需要检查型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="need_check_model_flag"
              label="是否需要检查型号"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.need_check_model_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMeCarRoutehis from '@/api/dcs/core/wms/meCarRoutehis'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  car_route_id: '',
  ware_house: '',
  car_code: '',
  car_task_type: '',
  step_code: '',
  step_name: '',
  location_x: '',
  location_y: '',
  location_z: '',
  step_status: '',
  step_start_limit_id: '',
  step_start_limit_step: '',
  step_start_limit_status: '',
  need_check_gd_flag: 'Y',
  need_check_model_flag: 'Y',
  if_tags_list: '',
  if_tags_value: '',
  car_task_id: ''
}
export default {
  name: 'MECARROUTEHIS',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: 'WMS天车调度任务路线历史表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'car_route_id',
      // 排序
      sort: ['car_route_id asc'],
      // CRUD Method
      crudMethod: { ...crudMeCarRoutehis },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 320,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
      }
    }
  },
  // 数据字典
  dicts: ['CAR_TASK_TYPE', 'WARE_HOUSE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 320
    }
  },
  created: function() {
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudSortResult
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              mo_id: data.mo_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
