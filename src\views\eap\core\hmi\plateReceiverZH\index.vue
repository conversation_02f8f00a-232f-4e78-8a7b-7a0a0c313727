<template>
  <div class="plateReceiver">
    <div v-if="!pptStatus">
      <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="plateHead">
            <div class="wrapInnormal">
              <div :class="masterData.sansedenghongse==='1'?'innormal redActive':'innormal grayActive'" />
              <div :class="masterData.sansedenghuangse==='1'?'innormal yellowActive':'innormal grayActive'" />
              <div :class="masterData.sansedenglvse==='1'?'innormal greenActive':'innormal grayActive'" />
              <div :class="masterData.sansedenglanse==='1'?'innormal blueActive':'innormal grayActive'" />
            </div>
            <div class="plateTitle">{{ masterData.title }}</div>
            <div class="plateRight">
              <div :class="masterData.plcStatus==='1'?'commonItem commonFirst1':'commonItem commonFirst2'">PLC</div>
            </div>
          </div>
        </el-col>
      </el-row>
      </el-card>
      <el-card class="cardStyle" shadow="never">
        <el-row :gutter="24" class="elRowStyle">
          <el-col :span="24">
            <div class="plateCenter">
              <div class="plateAwait">
                <div class="plateAwaitOne">
                  <div class="awaitStatus">
                    <h2 :style="{background:masterData.deviceStatusBack}">
                      {{ masterData.deviceStatus }}
                    </h2>
                  </div>
                  <div class="plateLine"><p>作业模式:<span class="mgv"> {{ masterData.portMode }} </span></p></div>
                  <div class="plateLine"><p>吸嘴寿命:<span class="times">{{ masterData.suctionNozzleLife }}</span></p></div>
                  <template v-for="(item,index) in gArea.item_list">
                    <div :key="index" class="plateLine"><p>{{ item.item_des }}:<span class="kpa">{{ item.tag_value ? parseFloat(item.tag_value).toFixed(2) : '' }}</span>{{ item.unit }}</p></div>
                  </template>
                </div>
                <div class="plateAwaitTwo">
                  <!-- <ul>
                    <li>{{ masterData.station1Left }}</li>
                    <li class="plateEmpty">{{ masterData.station2Left }}</li>
                    <li>{{ masterData.station3LeftQty }}</li>
                  </ul>
                  <ul>
                    <li>{{ masterData.station1Right }}</li>
                    <li class="plateEmpty">{{ masterData.station2Right }}</li>
                    <li>{{ masterData.station3RightQty }}</li>
                  </ul>
                  <ul class="nGcommonbg">
                    <li class="ngBg">{{ masterData.stationNG }}</li>
                    <li class="plateEmpty" />
                    <li class="ngBg">暂存{{ masterData.station3StagingQty }}</li>
                  </ul> -->
                  <div class="plateTop">
                    <div v-for="item in eapScreenStation" :key="item.id">
                      <span :style="{background: item.color}" class="back"></span>
                      <span>{{ item.label }}</span>
                    </div>
                  </div>
                  <div class="plateBottom">
                    <ul :style="{'border':`1px solid ${masterData.station1LeftBack}`}">
                      <li :style="{background:masterData.station1LeftBack}">{{ masterData.station1Left }}</li>
                      <li :style="{background:masterData.station3LeftQtyBack}">{{ masterData.station3LeftQty }}</li>
                    </ul>
                    <ul :style="{'border':`1px solid ${masterData.station1RightBack}`}">
                      <li :style="{background:masterData.station1RightBack}">{{ masterData.station1Right }}</li>
                      <li :style="{background:masterData.station3RightQtyBack}">{{ masterData.station3RightQty }}</li>
                    </ul>
                    <ul class="nGcommonbg" :style="{'border':`1px solid ${masterData.stationNGBack}`}">
                      <li class="ngBg" :style="{background:masterData.stationNGBack}">{{ masterData.stationNG }}</li>
                      <li class="ngBg" :style="{background:masterData.station3StagingQtyBack}">{{ masterData.station3StagingQty }}</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="platePower">
                <div class="platePowerOne">
                  <div class="powerItem">
                    <p><span>当班耗电：{{ masterData.shizaigonglv }}</span></p>
                    <!-- <h2 style="padding-top: 30px;"></h2> -->
                    <h2>总&ensp;耗&ensp;电：{{ masterData.jitaihaodian }}</h2>
                    <div class="banner-hidden">
                      <div class="banner-background-01" />
                      <div class="banner-background-02" />
                    </div>
                  </div>
                  <div class="powerItem powerItemTwo">
                    <p><span>当班耗气：{{ masterData.xizuihaoqi }}</span></p>
                    <!-- <h2 style="padding-top: 30px;"></h2> -->
                    <h2>总&ensp;耗&ensp;气：{{ masterData.dangqianbancixizuihaoqiliang }}</h2>
                    <div class="banner-hidden">
                      <div class="banner-background-01" />
                      <div class="banner-background-02" />
                    </div>
                  </div>
                  <!-- <div class="powerItem powerItemThree">
                    <p><span>气缸耗气</span><span>{{ masterData.qiganghaoqi }}</span></p>
                    <h2>{{ masterData.dangqianbanciqiganghaoqiliang }}</h2>
                    <div class="banner-hidden">
                      <div class="banner-background-01" />
                      <div class="banner-background-02" />
                    </div>
                  </div> -->
                </div>
                <div class="platePowerTwo">
                  <div class="plateSuccess">
                    <div class="successFour">
                      <div class="successStatus"><span>计划数：</span><span>{{ masterData.baibanshezhichanliang }}</span></div>
                      <div class="successStatus"><span>完成数：</span><span class="noSuccess">{{ masterData.yiwanchengchanliang }}</span></div>
                      <div class="successStatus nmbb"><span>完成率：</span><span class="successLv">{{ masterData.wanchenglv+"%" }}</span></div>
                    </div>
                    <div class="optionfour">
                      <ECharts
                        ref="chart4"
                        :options="orgOptions4"
                        style="width: 100%;height:200px;"
                      />
                    </div>
                  </div>
                  <div class="platePie">
                    <div class="platePieOne">
                      <!-- <p>待机 {{ masterData.daijilv }}</p>
                      <p >运行 {{ masterData.yunxlv }}</p>
                      <p >异常停机 {{ masterData.yichanglv }}</p>
                      <p >手动{{ masterData.shoudonglv }}</p> -->
                      <p v-for="(item,index) in dataArr" :key="index" :style="{background:item.color}">{{ item.name}}{{ item.value + '%' }}</p>
                    </div>
                    <div class="platePieTwo" style="text-align:right">
                      <el-radio-group v-model="showType" size="small">
                        <el-radio-button label="day">天</el-radio-button>
                        <el-radio-button label="week">周</el-radio-button>
                        <el-radio-button label="month">月</el-radio-button>
                      </el-radio-group>
                      <ECharts
                        ref="chart1"
                        :options="orgOptions1"
                        style="width: 100%;height:200px;"
                      />
                    </div>
                  </div>
                </div>
                <div class="plateLineOne">
                  <span class="cureentSU">当前产速：{{ masterData.dangqianchansu }}</span>
                  <ECharts
                    ref="chart2"
                    :options="orgOptions2"
                    style="width: 100%;height:288px"
                  />
                </div>
              </div>
              <div class="plateCall">
                <div class="plateCallOne">
                  <div class="plateBar">
                    <ECharts
                      ref="chart3"
                      :options="orgOptions3"
                      style="width: 100%;height:420px"
                    />
                  </div>
                </div>
                <div class="plateCallTwo">
                  <div class="plateCallText">
                    <marquee loop="infinite" height="420" onmouseover="this.stop()" onmouseout="this.start()" direction="up" scrollamount="3">
                      <div>
                        <p v-for="(item,index) in alarmData" :key="index" class="tipOne">{{ item.alarm_des }}</p>
                      </div>
                    </marquee>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="cardStyle cardheadbg">
        <el-row :gutter="24" class="elRowStyle">
          <el-col :span="24">
            <div class="plateFooter">
              <marquee loop="infinite" onmouseover="this.stop()" onmouseout="this.start()" scrollamount="5">
                <p>
                  <span v-for="(item,index) in alarmData" :key="index" style="margin-right:50px;">{{ item.alarm_des }}</span>
                </p>
              </marquee>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <vue-office-ppt v-if="pptStatus" :src="pptStatus"></vue-office-ppt>
  </div>
</template>

<script>
import ECharts from 'vue-echarts'

import { selTree as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import { selStationConfig ,selGroupCodeFast} from '@/api/eap/core/eapScreenConfig'
import { sel } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import vueOfficePpt from '@/components/VueOfficePpt/index'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'plateReceiver',
  components: {
    ECharts,vueOfficePpt
  },
  data() {
    return {
      timer: '',
      timer1: '',
      timer2: '',
      showType: 'day',
      orgOptions1: {},
      orgOptions2: {},
      orgOptions3: {},
      orgOptions4: {},
      indicatorrPie: null,
      indicatorrLine: null,
      indicatorrBar: null,
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // A区域
      aArea: { },
      masterData: {
        title: '', // 标题
        plcStatus: '', // PLC通讯状态
        deviceStatus: '', // 机台状态
        deviceStatusBack:'',//几台背景
        portMode: '', // 作业模式
        suctionNozzleLife: '', // 吸嘴寿命
        station1Left: '', // 工位1左名称
        station1LeftBack:'',// 工位1左名称背景
        station1Right: '', // 工位1右名称
        station1RightBack:'',// 工位1右名称背景
        stationNG: '', // NG工位名称
        stationNGBack:'',// NG工位名称背景
        station2Left: '', // 工位2左名称
        station2Right: '', // 工位2右名称wanchenglv
        station3LeftQty: '', // 工位3左数量
        station3LeftQtyBack:'',// 工位3左数量背景
        station3RightQty: '', // 工位3右数量
        station3RightQtyBack:'',//工位3右数量背景
        station3StagingQty: '', // 暂存工位数量
        station3StagingQtyBack:'',// 暂存工位数量背景
        jitaihaodian: '', // 机台耗电
        shizaigonglv: '', // 视在功率
        xizuihaoqi: '', // 吸嘴耗气
        dangqianbancixizuihaoqiliang: '', // 当前班次吸嘴耗气量
        qiganghaoqi: '', // 气缸耗气
        dangqianbanciqiganghaoqiliang: '', // 当前班次气缸耗气量
        baibanshezhichanliang: '', // 白班设置产量
        yiwanchengchanliang: '', // 已完成产量
        weiwancheng: '', // 未完成
        wanchenglv: '', // 完成率
        dangqianchansu: '', // 当前产速
        sansedenghongse: '', // 三色灯，红色
        sansedenghuangse: '', // 三色灯，黄色
        sansedenglvse: '', // 三色灯，绿色
        sansedenglanse: '', // 三色灯，蓝色
        daijilv: '',
        yunxlv: '',
        yichanglv: '',
        shoudonglv: ''
      },
      // b区域
      bArea: { },
      // c区域
      cArea: { },
      // d区域
      dArea: { },
      // e区域
      eArea: { },
      // F区域
      fArea: { },
      // G区域
      gArea: { },
      alarmData: [],
      eapScreenDevice:[],
      eapScreenStation:[],
      dataArr:[],
      pptStatus:'',
      pptFlag:true,
    }
  },
  created: function() {
    this.getStationData()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  // 数据字典
  dicts: ['EAP_SCREEN_STATION_STATUS','EAP_SCREEN_STATION_STATUS_COLOR','EAP_SCREEN_DEVICE_STATUS','EAP_SCREEN_DEVICE_STATUS_COLOR'],
  mounted() {
    this.orgOptions2 = {
      color: ['#2648c2'],
      tooltip: {
        trigger: 'axis'
      },
      dataset: {
        source: []
      },
      xAxis: {
        type: 'category',
        // 设置显示所有的X轴刻度
        axisLabel: {
          color: '#ffffff',
          interval: -1,
          rotate: 40 // label旋转角度
        }
      },
      yAxis: {
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed', // y轴分割线类型
            color: '#3c4479',
            width: 1
          }
        },
        axisLabel: {
          color: '#ffffff' // 刻度线标签颜色
        }
      },
      grid: {
        left: '5%',
        right: '5%'
      },
      // Declare several bar series, each will be mapped
      // to a column of dataset.source by default.
      series: [
        {
          name: '小时产量',
          type: 'line',
          stack: 'stack',
          label: {
            show: true,
            position: 'inside'
          }
        },
        {
          name: '小时产量',
          type: 'bar',
          stack: 'stack',
          label: {
            show: false,
            position: 'inside'
          }
        }
      ]
    }
    this.orgOptions3 = {
      tooltip: {
        trigger: 'axis'
      },
      dataset: {
        source: []
      },
      xAxis: {
        type: 'category',
        // 设置显示所有的X轴刻度
        axisLabel: {
          color: '#ffffff',
          interval: -1,
          rotate: 40 // label旋转角度
        }
      },
      yAxis: {
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed', // y轴分割线类型
            color: '#3c4479',
            width: 1
          }
        },
        axisLabel: {
          color: '#ffffff' // 刻度线标签颜色
        }
      },
      // Declare several bar series, each will be mapped
      // to a column of dataset.source by default.
      series: [
        {
          name: '已完成产量',
          type: 'bar',
          label: {
            show: true,
            position: 'top',
            textStyle: { // 数值样式
              color: '#ffffff'
            }
          },
          color: '#2648c2'
        },
        {
          name: '计划产量',
          type: 'bar',
          label: {
            show: true,
            position: 'top',
            textStyle: { // 数值样式
              color: '#ffffff'
            }
          },
          color: '#559AD3'
        }
      ]
    }
    this.orgOptions4 = {
      color: ['#838383', '#ffffff'],
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '完成状态',
          type: 'pie',
          radius: ['60%', '100%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 40,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 1048, name: '已完成' },
            { value: 735, name: '未完成' }
          ],
          top: '30%'
        }
      ]
    }
    this.timer = setInterval(this.handlerMasterData, 5000)
    this.timer1 = setInterval(this.getAlarmData, 3000)
    this.timer2 = setInterval(this.handlePlcHeartbeat, 3000)
  },
  methods: {
    getStationData() {
      const query = {
        user_name: Cookies.get('userName')
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const stationData = JSON.parse(defaultQuery.result)
            if (stationData.length > 0) {
              this.currentStation = {
                prod_line_id: stationData[0].prod_line_id,
                prod_line_code: stationData[0].prod_line_code,
                prod_line_des: stationData[0].prod_line_des,
                station_id: stationData[0].station_list[0].station_id,
                station_code: stationData[0].station_list[0].station_code,
                station_des: stationData[0].station_list[0].station_des,
                cell_id: stationData[0].station_list[0].cell_id
              }
              this.getStationConfig()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getStationConfig() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selStationConfig(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const StationConfig = JSON.parse(defaultQuery.result)
            if (StationConfig.length > 0) {
              this.aArea = StationConfig.filter(item => item.region_code === 'A')
              if (this.aArea.length > 0) {
                this.aArea = this.aArea[0]
                console.log(this.aArea)
              }
              this.bArea = StationConfig.filter(item => item.region_code === 'B')
              if (this.bArea.length > 0) {
                this.bArea = this.bArea[0]
              }
              this.cArea = StationConfig.filter(item => item.region_code === 'C')
              if (this.cArea.length > 0) {
                this.cArea = this.cArea[0]
              }
              this.dArea = StationConfig.filter(item => item.region_code === 'D')
              if (this.dArea.length > 0) {
                this.dArea = this.dArea[0]
              }
              this.eArea = StationConfig.filter(item => item.region_code === 'E')
              if (this.eArea.length > 0) {
                this.eArea = this.eArea[0]
              }
              this.fArea = StationConfig.filter(item => item.region_code === 'F')
              if (this.fArea.length > 0) {
                this.fArea = this.fArea[0]
              }
              this.gArea = StationConfig.filter(item => item.region_code === 'G')
              if (this.gArea.length > 0) {
                this.gArea = this.gArea[0]
              }
              this.getCellIp()
              this.getGroupCodeFast()
              this.getPieEcharts()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getPieEcharts(){
      var colors = this.eapScreenDevice.map(e => e.color)
      this.orgOptions1 = {
        color: colors,
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '占比',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#0e2055',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      }
    },
    getGroupCodeFast(){
      // 大屏设备状态颜色
      this.dict.EAP_SCREEN_DEVICE_STATUS.map(e=>{
        this.dict.EAP_SCREEN_DEVICE_STATUS_COLOR.map(item=>{
          if(e.value === item.value){
            this.eapScreenDevice.push(
              {id:e.id,label:e.label,value:e.value,color:item.label}
            )
          }
        })
      })
      // 大屏工位状态颜色
      this.dict.EAP_SCREEN_STATION_STATUS.map(e=>{
        this.dict.EAP_SCREEN_STATION_STATUS_COLOR.map(item=>{
          if(e.value === item.value){
            this.eapScreenStation.push(
              {id:e.id,label:e.label,value:e.value,color:item.label}
            )
          }
        })
      })
    },
    // 处理A区数据
    handlerMasterData() {
      this.aArea.item_list.forEach(item => {
        const item_code = item.item_code
        if (item_code === 'A001') {
          this.masterData.title = item.item_des
        } else if (item_code === 'A002') {
          this.masterData.plcStatus = item.tag_value
        } else if (item_code === 'A003') {
          // if (item.tag_value === '0') {
          //   this.masterData.deviceStatus = '待机'
          // } else if (item.tag_value === '1') {
          //   this.masterData.deviceStatus = '运行'
          // } else if (item.tag_value === '2') {
          //   this.masterData.deviceStatus = '异常停机'
          // } else if (item.tag_value === '3') {
          //   this.masterData.deviceStatus = '手动'
          // } else if (item.tag_value === '4') {
          //   this.masterData.deviceStatus = '备用'
          // } else if (item.tag_value === '5') {
          //   this.masterData.deviceStatus = '异常不停机'
          // }
          this.eapScreenDevice.forEach(e=>{
            if(item.tag_value === e.value){
              this.masterData.deviceStatus = e.label
              this.masterData.deviceStatusBack = e.color
            }
          })
        } else if (item_code === 'A004') {
          if (item.tag_value === '0') {
            this.masterData.portMode = 'MGV'
          } else if (item.tag_value === '1') {
            this.masterData.portMode = 'AGV'
          }
        } else if (item_code === 'A005') {
          this.masterData.suctionNozzleLife = item.tag_value + ' ' + item.unit
        } else if (item_code === 'A006') {
          this.masterData.station1Left = item.item_des
          this.masterData.station1LeftBack = this.eapScreenStation[+item.tag_value].color
        } else if (item_code === 'A007') {
          this.masterData.station1Right = item.item_des
          this.masterData.station1RightBack = this.eapScreenStation[+item.tag_value].color
        } else if (item_code === 'A008') {
          this.masterData.stationNG = item.item_des
          this.masterData.stationNGBack = this.eapScreenStation[+item.tag_value].color
        } else if (item_code === 'A009') {
          this.masterData.station2Left = item.item_des
        } else if (item_code === 'A010') {
          this.masterData.station2Right = item.item_des
        } else if (item_code === 'A011') {
          // this.masterData.station3LeftQty = item.tag_value
          this.masterData.station3LeftQty = item.item_des
          this.masterData.station3LeftQtyBack = this.eapScreenStation[+item.tag_value].color
        } else if (item_code === 'A012') {
          // this.masterData.station3RightQty = item.tag_value
          this.masterData.station3RightQty = item.item_des
          this.masterData.station3RightQtyBack = this.eapScreenStation[+item.tag_value].color
        } else if (item_code === 'A013') {
          // this.masterData.station3StagingQty = item.tag_value
          this.masterData.station3StagingQty = item.item_des
          this.masterData.station3StagingQtyBack = this.eapScreenStation[+item.tag_value].color
        } else if (item_code === 'A014') {
          this.masterData.jitaihaodian = item.tag_value && parseFloat(item.tag_value).toFixed(2) + ' ' + item.unit 
        } else if (item_code === 'A015') {
          this.masterData.shizaigonglv = item.tag_value && parseFloat(item.tag_value).toFixed(2) + ' ' + item.unit 
        } else if (item_code === 'A016') {
          this.masterData.xizuihaoqi = item.tag_value  + ' ' + item.unit 
        } else if (item_code === 'A017') {
          this.masterData.dangqianbancixizuihaoqiliang = item.tag_value  + ' ' + item.unit 
        } else if (item_code === 'A018') {
          this.masterData.qiganghaoqi = item.tag_value + ' ' + item.unit
        } else if (item_code === 'A019') {
          this.masterData.dangqianbanciqiganghaoqiliang = item.tag_value + ' ' + item.unit
        } else if (item_code === 'A020') {
          this.masterData.baibanshezhichanliang = item.tag_value
        } else if (item_code === 'A021') {
          this.masterData.yiwanchengchanliang = item.tag_value
        } else if (item_code === 'A022') {
          this.masterData.dangqianchansu = item.tag_value + ' ' + item.unit
        } else if (item_code === 'A023') {
          this.masterData.sansedenghongse = item.tag_value
        } else if (item_code === 'A024') {
          this.masterData.sansedenghuangse = item.tag_value
        } else if (item_code === 'A025') {
          this.masterData.sansedenglvse = item.tag_value
        } else if (item_code === 'A026') {
          this.masterData.sansedenglanse = item.tag_value
        }
      })
      // 计算未完成与完成率
      if (this.masterData.baibanshezhichanliang !== '' && this.masterData.yiwanchengchanliang !== '') {
        this.masterData.weiwancheng = parseInt(this.masterData.baibanshezhichanliang) - parseInt(this.masterData.yiwanchengchanliang)
        this.masterData.wanchenglv = ((parseInt(this.masterData.yiwanchengchanliang) / parseInt(this.masterData.baibanshezhichanliang)) * 100).toFixed(1)
      }
      // 计算机台状态占比
      // var daiji = 0
      // var yunxin = 0
      // var yichang = 0
      // var shoudong = 0
      // if (this.showType === 'day') {
      //   this.bArea.item_list.forEach(item => {
      //     const item_code = item.item_code
      //     if (item.tag_value !== '') {
      //       if (item_code === 'B001') {
      //         daiji = parseInt(item.tag_value)
      //       } else if (item_code === 'B002') {
      //         yunxin = parseInt(item.tag_value)
      //       } else if (item_code === 'B003') {
      //         yichang = parseInt(item.tag_value)
      //       } else if (item_code === 'B004') {
      //         shoudong = parseInt(item.tag_value)
      //       }
      //     }
      //   })
      // } else if (this.showType === 'week') {
      //   this.cArea.item_list.forEach(item => {
      //     const item_code = item.item_code
      //     if (item.tag_value !== '') {
      //       if (item_code === 'C001') {
      //         daiji = parseInt(item.tag_value)
      //       } else if (item_code === 'C002') {
      //         yunxin = parseInt(item.tag_value)
      //       } else if (item_code === 'C003') {
      //         yichang = parseInt(item.tag_value)
      //       } else if (item_code === 'C004') {
      //         shoudong = parseInt(item.tag_value)
      //       }
      //     }
      //   })
      // } else if (this.showType === 'month') {
      //   this.dArea.item_list.forEach(item => {
      //     const item_code = item.item_code
      //     if (item.tag_value !== '') {
      //       if (item_code === 'D001') {
      //         daiji = parseInt(item.tag_value)
      //       } else if (item_code === 'D002') {
      //         yunxin = parseInt(item.tag_value)
      //       } else if (item_code === 'D003') {
      //         yichang = parseInt(item.tag_value)
      //       } else if (item_code === 'D004') {
      //         shoudong = parseInt(item.tag_value)
      //       }
      //     }
      //   })
      // }
      // const total = daiji + yunxin + yichang + shoudong
      // var daijilv = 0
      // var yunxlv = 0
      // var yichanglv = 0
      // var shoudonglv = 0
      // if (total !== 0) {
      //   daijilv = (daiji / total * 100)
      //   yunxlv = (yunxin / total * 100)
      //   yichanglv = (yichang / total * 100)
      //   shoudonglv = (shoudong / total * 100)
      // }
      // this.masterData.daijilv = daijilv.toFixed(2).toString() + '%'
      // this.masterData.yunxlv = yunxlv.toFixed(2).toString() + '%'
      // this.masterData.yichanglv = yichanglv.toFixed(2).toString() + '%'
      // this.masterData.shoudonglv = shoudonglv.toFixed(2).toString() + '%'

      var total = 0
      this.dataArr = []
      if (this.showType === 'day') {
        this.bArea.item_list.forEach(item => {
          this.eapScreenDevice.forEach((e,index)=>{
            if(item.tag_value && item.item_code === `B00${index+1}`){
              total = this.bArea.item_list.reduce((sum,obj)=>sum + Number(obj.tag_value),0)
              var Proportion = (Number(item.tag_value) / total * 100).toFixed(2)
              this.dataArr.push(
                {
                  value:Proportion > 0 ? Proportion : '0.00',
                  name:e.label,
                  color:e.color
                }
              )
            }
          })
        })
      }else if(this.showType === 'week'){
        this.cArea.item_list.forEach(item => {
          this.eapScreenDevice.forEach((e,index)=>{
            if(item.tag_value && item.item_code === `C00${index+1}`){
              total = this.cArea.item_list.reduce((sum,obj)=>sum + parseInt(obj.tag_value),0)
              var Proportion = (parseInt(item.tag_value) / total * 100).toFixed(2)
              this.dataArr.push(
                {
                  value: Proportion > 0 ? Proportion : '0.00',
                  name:e.label,
                  color:e.color
                }
              )
            }
          })
        })
      }else if(this.showType === 'month'){
        this.dArea.item_list.forEach(item => {
          this.eapScreenDevice.forEach((e,index)=>{
            if(item.tag_value && item.item_code === `D00${index+1}`){
              total = this.dArea.item_list.reduce((sum,obj)=>sum + parseInt(obj.tag_value),0)
              var Proportion = (parseInt(item.tag_value) / total * 100).toFixed(2)
              this.dataArr.push(
                {
                  value:Proportion > 0 ? Proportion : '0.00',
                  name:e.label,
                  color:e.color
                }
              )
            }
          })
        })
      }
      this.orgOptions1.series[0].data = this.dataArr
      // this.orgOptions1.series[0].data = [
      //   { value: daijilv, name: '待机' },
      //   { value: yunxlv, name: '运行' },
      //   { value: yichanglv, name: '异常停机' },
      //   { value: shoudonglv, name: '手动' }
      // ]
      var a = []
      this.eArea.item_list.forEach(item => {
        const item_des = item.item_des
        var tagValue = 0
        if (item.tag_value !== '') {
          tagValue = parseInt(item.tag_value)
        }
        a.push([item_des, tagValue])
      })
      this.orgOptions2.dataset.source = a

      var b = []
      var currentTime = new Date()
      var nowDayOfWeek = currentTime.getDay()
      var nowDay = currentTime.getDate()
      var nowMonth = currentTime.getMonth()
      var nowYear = currentTime.getFullYear()
      currentTime = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1)

      for (var j = 0; j < 7; j++) {
        if (j !== 0) {
          currentTime = currentTime.setDate(currentTime.getDate() + 1)
        }
        currentTime = new Date(currentTime)
        var _month = ''
        if (currentTime.getMonth() < 9) {
          _month = '0' + (currentTime.getMonth() + 1).toString()
        } else {
          _month = (currentTime.getMonth() + 1).toString()
        }
        var _day = ''
        if (currentTime.getDate() < 10) {
          _day = '0' + currentTime.getDate().toString()
        } else {
          _day = currentTime.getDate().toString()
        }
        var planCount = 0
        var finishCount = 0
        if (j === 0) {
          var tagValue1 = this.fArea.item_list.filter(item => item.item_code === 'F001')[0].tag_value
          var tagValue2 = this.fArea.item_list.filter(item => item.item_code === 'F002')[0].tag_value
          if (tagValue1 !== '') {
            planCount = parseInt(tagValue1)
          }
          if (tagValue2 !== '') {
            finishCount = parseInt(tagValue2)
          }
        } else if (j === 1) {
          var tagValue3 = this.fArea.item_list.filter(item => item.item_code === 'F003')[0].tag_value
          var tagValue4 = this.fArea.item_list.filter(item => item.item_code === 'F004')[0].tag_value
          if (tagValue3 !== '') {
            planCount = parseInt(tagValue3)
          }
          if (tagValue4 !== '') {
            finishCount = parseInt(tagValue4)
          }
        } else if (j === 2) {
          var tagValue5 = this.fArea.item_list.filter(item => item.item_code === 'F005')[0].tag_value
          var tagValue6 = this.fArea.item_list.filter(item => item.item_code === 'F006')[0].tag_value
          if (tagValue5 !== '') {
            planCount = parseInt(tagValue5)
          }
          if (tagValue6 !== '') {
            finishCount = parseInt(tagValue6)
          }
        } else if (j === 3) {
          var tagValue7 = this.fArea.item_list.filter(item => item.item_code === 'F007')[0].tag_value
          var tagValue8 = this.fArea.item_list.filter(item => item.item_code === 'F008')[0].tag_value
          if (tagValue7 !== '') {
            planCount = parseInt(tagValue7)
          }
          if (tagValue8 !== '') {
            finishCount = parseInt(tagValue8)
          }
        } else if (j === 4) {
          var tagValue9 = this.fArea.item_list.filter(item => item.item_code === 'F009')[0].tag_value
          var tagValue10 = this.fArea.item_list.filter(item => item.item_code === 'F010')[0].tag_value
          if (tagValue9 !== '') {
            planCount = parseInt(tagValue9)
          }
          if (tagValue10 !== '') {
            finishCount = parseInt(tagValue10)
          }
        } else if (j === 5) {
          var tagValue11 = this.fArea.item_list.filter(item => item.item_code === 'F011')[0].tag_value
          var tagValue12 = this.fArea.item_list.filter(item => item.item_code === 'F012')[0].tag_value
          if (tagValue11 !== '') {
            planCount = parseInt(tagValue11)
          }
          if (tagValue12 !== '') {
            finishCount = parseInt(tagValue12)
          }
        } else if (j === 6) {
          var tagValue13 = this.fArea.item_list.filter(item => item.item_code === 'F013')[0].tag_value
          var tagValue14 = this.fArea.item_list.filter(item => item.item_code === 'F014')[0].tag_value
          if (tagValue13 !== '') {
            planCount = parseInt(tagValue13)
          }
          if (tagValue14 !== '') {
            finishCount = parseInt(tagValue14)
          }
        }
        b.push([_month + '-' + _day, finishCount, planCount])
      }
      this.orgOptions3.dataset.source = b
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            // this.getAlarmData()
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getAlarmData() {
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://***********:8089'  + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      var queryData = {
        reset_flag: 'N'
      }
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          this.alarmData = []
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      if (this.aArea.item_list !== undefined) {
        this.aArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.bArea.item_list !== undefined) {
        this.bArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.cArea.item_list !== undefined) {
        this.cArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.dArea.item_list !== undefined) {
        this.dArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.eArea.item_list !== undefined) {
        this.eArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.fArea.item_list !== undefined) {
        this.fArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.gArea.item_list !== undefined) {
        this.gArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://***********:8089'  + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach(item => {
                const tag_value = item.tag_value === undefined ? '' : item.tag_value
                if (this.aArea.item_list !== undefined) {
                  const aAreaItem = this.aArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (aAreaItem.length > 0) {
                    aAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.bArea.item_list !== undefined) {
                  const bAreaItem = this.bArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (bAreaItem.length > 0) {
                    bAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.cArea.item_list !== undefined) {
                  const cAreaItem = this.cArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (cAreaItem.length > 0) {
                    cAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.dArea.item_list !== undefined) {
                  const dAreaItem = this.dArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (dAreaItem.length > 0) {
                    dAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.eArea.item_list !== undefined) {
                  const eAreaItem = this.eArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (eAreaItem.length > 0) {
                    eAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.fArea.item_list !== undefined) {
                  const fAreaItem = this.fArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (fAreaItem.length > 0) {
                    fAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.gArea.item_list !== undefined) {
                  const gAreaItem = this.gArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (gAreaItem.length > 0) {
                    gAreaItem[0].tag_value = tag_value
                  }
                }
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // 处理plc心跳交互
    handlePlcHeartbeat() {
      if (this.masterData.plcStatus === '1') {
        const item = this.aArea.item_list.filter(item => item.item_code === 'A002')[0]
        // 再写入
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: item.tag_key,
          TagValue: '2'
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/' + item.tag_key.split('/')[0]
        this.sendMessage(topic, sendStr)
      }
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }

      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        if (this.aArea.item_list !== undefined) {
          this.aArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.bArea.item_list !== undefined) {
          this.bArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.cArea.item_list !== undefined) {
          this.cArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.dArea.item_list !== undefined) {
          this.dArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.eArea.item_list !== undefined) {
          this.eArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.fArea.item_list !== undefined) {
          this.fArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.gArea.item_list !== undefined) {
          this.gArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }

        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        var jsonData = JSON.parse(message)
        if (jsonData == null) return

        if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          if (this.aArea.item_list !== undefined) {
            const aAreaItem = this.aArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (aAreaItem.length > 0) {
              aAreaItem[0].tag_value = jsonData.TagNewValue
            }
            if(jsonData.TagKey.indexOf('PlcConfig/OnOffLine')> -1 ){//只调用一次接口
              if(jsonData.TagNewValue != '1'  && this.pptFlag){
                this.pptFlag = false
                  const query = {
                    user_name: Cookies.get('userName'),
                    parameter_code: 'HMI_PPT_URL',
                    enable_flag: 'Y'
                  }
                sel(query).then(res=>{
                  const defaultQuery = JSON.parse(JSON.stringify(res))
                  if (defaultQuery.code === 0 && defaultQuery.data.length >0) {
                    this.pptStatus = defaultQuery.data[0].parameter_val || ''
                  }else{
                    this.pptStatus = ''
                    
                  }
                }).catch(()=>{
                  this.pptStatus = ''
                })
              }else{
                this.pptStatus = ''
                this.pptFlag = true
              }
            }
          }
          if (this.bArea.item_list !== undefined) {
            const bAreaItem = this.bArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (bAreaItem.length > 0) {
              bAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.cArea.item_list !== undefined) {
            const cAreaItem = this.cArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (cAreaItem.length > 0) {
              cAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.dArea.item_list !== undefined) {
            const dAreaItem = this.dArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (dAreaItem.length > 0) {
              dAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.eArea.item_list !== undefined) {
            const eAreaItem = this.eArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (eAreaItem.length > 0) {
              eAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.fArea.item_list !== undefined) {
            const fAreaItem = this.fArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (fAreaItem.length > 0) {
              fAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.gArea.item_list !== undefined) {
            const gAreaItem = this.gArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (gAreaItem.length > 0) {
              gAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          // console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          // this.$message({ message: '操作成功！', type: 'success' })
        } else {
          // this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.plateReceiver{
  background-image: linear-gradient(310deg, #111f4a, #200f39, #0e2055, #200f39, #0e2055, #200816, #200816, #200f39);
}
.plateHead{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  .wrapInnormal{
    display: flex;
    align-items: center;
    min-width: 26%;
    .innormal{
        width: 60px;
        height: 60px;
        border-radius: 50%;
        box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0,0,0,0.3);
        margin: 0 10px;
      }
      .redActive{
        background-image: linear-gradient(0deg, #ba0731, #ee676a);
      }
      .yellowActive{
        background-image: linear-gradient(0deg, #f8800c,#f9ab0b);
      }
      .greenActive{
        background-image: linear-gradient(0deg, #0db336,#01bd7d);
      }
      .blueActive{
        background-image: linear-gradient(0deg, #307AE8,#639CEC);
      }
      .grayActive{
        background-image: linear-gradient(0deg, #7a777f,#d3ccd5);
      }
  }
  .plateTitle{
    font-size: 50px;
    font-weight: 700;
    color: #29d0e0;
  }
  .plateRight{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 26%;
    .commonItem{
      border: 1px solid;
      margin: 0 20px;
      font-size: 30px;
      padding: 1px 4px;
    }
    .commonFirst1{
      font-size: 30px;
      color: #48a9ae;
      border-color: #48a9ae;
      background-color: #0e6b15;
    }
    .commonFirst2{
      font-size: 30px;
      color: #48a9ae;
      border-color: #48a9ae;
    }
    .commonSecond{
      color: #970e0b;
      border-color: #970e0b;
    }
  }
}
.plateCenter{
  display: flex;
  justify-content: space-between;
  height:calc(100vh - 195px);
  .plateAwait{
    width: 30%;
    .plateAwaitOne{
      text-align: center;
      text-align: -webkit-center;
      border: 3px solid #1d5f70;
      padding: 36px 0;
      margin: 4px;
      min-height: 590px;
      .awaitStatus{
        width: 50%;
        padding: 3px;
        background-image: linear-gradient(60deg, #636368, #636368, #164159, #164159, #164159, #636368, #636368, #636368);
        h2{
          margin: 2px;
          text-align: center;
          background: #808080;
          color: #ffffff;
          font-weight: normal;
          font-size: 40px;
          min-height: 60px;
          line-height: 60px;
        }
      }
      .plateLine{
        color: #51c3d5;
        font-size: 30px;
        text-align: left;
        padding-left: 40px;
        .mgv{
          color: #f1ef2e;
        }
        .times{
          color: #dc0b0d;
          margin: 0 5px;
        }
        .kpa{
          color: #807a7e;
          margin: 0 5px;
        }
      }
    }
    .plateAwaitTwo{
      
      padding: 2px;
      margin: 4px;
      border: 3px solid #1d5f70;
      .plateTop{
        display:flex;
        min-height: 70px;
        line-height: 70px;
        margin-bottom: 15px;
        div{
          display: flex;
          align-items: center;
          .back{
            width:30px;
            height: 20px;
            display: block;
            margin: 0 10px;
            border-radius: 5px;
          }
          span{
            font-size: 16px;
            color: #fff;
          }
        }
      }
      .plateBottom{
        display: flex;
        align-items: center;
        justify-content: space-between;
        ul{
          list-style: none;
          margin: 0;
          padding: 0;
          width: 33%;
          // border: 1px solid #0e6b15;
          li{
            // background: #0e6b15;
            color: #ffffff;
            text-align: center;
            margin: 2px;
            margin-bottom: 15px;
            min-height: 70px;
            line-height: 70px;
            font-size: 30px;
          }
          :last-of-type{
            margin-bottom: 0;
          }
          .plateEmpty{
            background: #808080;
          }
          .ngBg{
            background: #ffa500;
          }
        }
        .nGcommonbg{
          border: 1px solid #ffa500;
        }
      }
    }
  }
  .platePower{
    width: 60%;
    .platePowerOne{
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 3px solid #1d5f70;
      padding: 2px;
      margin: 4px;
      .powerItem{
        width: 50%;
        position: relative;
        overflow: hidden;
        p{
        margin: 0;
        color: #ffffff;
        background: #086779;
        padding: 10px;
        font-size: 32px;
        min-height: 66px;
        line-height: 66px;
        display: flex;
        justify-content: space-between;
        font-weight: 550;
        }
        h2{
          margin: 0;
          color: #ffffff;
          background: #87ceea;
          padding-top: 25px;
          padding-bottom: 10px;
          padding-left: 10px;
          font-size: 32px;
          height: 126px;
          // line-height: 80px;
        }
      }
      .powerItemTwo{
        p{
          background: #b3669c;
        }
        h2{
          background: #ffa9ea;
        }
      }
      .powerItemThree{
        p{
          background: #383838;
        }
        h2{
          background: #808080;
        }
      }
    }
    .platePowerTwo{
        display: flex;
        justify-content: space-between;
        margin: 4px;
        min-height: 280px;
      .plateSuccess{
        width: 50%;
        border: 3px solid #1d5f70;
        padding: 10px 0;
        padding-left: 15px;
        margin-right: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        .successStatus{
          color: #ffffff;
          display: flex;
          // flex-direction: column;
          // margin-bottom: 25px;
          font-size: 30px;
          height: 33.3%;
          .noSuccess{
            color: #c60d15;
          }
          .successLv{
            color: #176f22;
          }
        }
        .nmbb{
          margin-bottom: 0;
        }
      }
      .platePie{
        width: 50%;
        display: flex;
        justify-content: space-between;
        border: 3px solid #1d5f70;
        padding: 10px;
        .platePieOne{
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: space-between;
          p{
            color: #ffffff;
            font-size: 16px;
            margin: 0;
            border-radius: 2px;
            text-align: center;
            padding: 8px;
            width: 165px;
          }
          .dj{
            background: #838383;
          }
          .yx{
            background: #178d51;
          }
          .yc{
            background: #c00709;
          }
          .sd{
            background: #2648c2;
          }
        }
        .platePieTwo{
          width: 65%;
          .indicatorrPie{
              width: 100%;
              height: 154px;
            }
        }
      }
    }
    .plateLineOne{
          border: 3px solid #1d5f70;
          margin: 4px;
          padding-top: 10px;
          .indicatorrLine{
            width: 100%;
            height: 154px;
          }
          .cureentSU{
          color:#ffffff;
          margin-left:10px;
          font-size: 32px;
          }
        }
  }
  .plateCall{
    width: 30%;
    .plateBar{
          width: 100%;
          .indicatorrBar{
            width: 100%;
            height: 154px;
          }
        }
    .plateButton{
      color: #ffffff;
      background: #0c3b71;
      border: 1px solid #2a5874;
    }
    .plateCallOne{
      border: 3px solid #1d5f70;
      margin: 4px;
    }
    .plateCallTwo{
      margin: 4px;
      .plateCallText{
      border: 3px solid #817e6f;
      min-height: 420px;
      overflow: auto;
      padding-left: 15px;
      .callOne{
        color: #eb0c02;
      }
      .tipOne{
        color: #c69927;
        font-size: 40px;
        font-weight: 700;
      }
    }
    }
  }
}
.plateFooter{
  background: #ffa500;
  text-align: center;
  min-height:100px;
  p{
    color: #ffffff;
    font-size: 50px;
    font-weight: 700;
    margin: 0;
    min-height:100px;
    line-height: 100px;
  }
}
.el-card{
  background: none;
  border: 0;
  border-radius: 0;
}
:deep(.el-card__body) {
    padding: 0 !important;
}
:deep(.el-input__inner){
    background-color: #0c3b71;
    color: #ffffff !important;
    border: 1px solid #2a5874 !important;
}
:deep(.el-radio-button__orig-radio:checked+.el-radio-button__inner) {
    color: #fff;
    background-color: #46c3d5;
    border-color: #46c3d5;
    -webkit-box-shadow: -1px 0 0 0 #46c3d5;
    box-shadow: -1px 0 0 0 #46c3d5;
}
:deep(.el-radio-button--small .el-radio-button__inner){
  font-size: 16px;
}
.banner-hidden {
  position: absolute;
  bottom: -114px;
  width: 100%;
  height: 70%;
  left: 0;
  z-index: 1;
}
.banner-background-01 {
  height: 85px;
  background-image: url('~@/assets/images/background01.png');
  background-repeat: repeat-x;
  position: absolute;
  width: 400%;
  z-index: 999;
  animation: wave1 15s linear 0.4s infinite normal;
  -moz-animation: wave1 15s linear 0.4s infinite normal;
  -webkit-animation: wave1 15s linear 0.4s infinite normal;
  -o-animation: wave1 15s linear 0.4s infinite normal;
}

.banner-background-02 {
  height: 100px;
  background-image: url('~@/assets/images/background02.png');
  background-repeat: repeat-x;
  position: absolute;
  width: 400%;
  left: 0;
  z-index: 998;
  animation: wave2 30s linear 0.4s infinite normal;
  -moz-animation: wave2 30s linear 0.4s infinite normal;
  -webkit-animation: wave2 30s linear 0.4s infinite normal;
  -o-animation: wave2 30s linear 0.4s infinite normal;
}
@-webkit-keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-moz-keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-o-keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-webkit-keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-moz-keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-o-keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}
.successFour{
  width: 75%;
  height: 85%;
  margin-top: 40px;
}
.optionfour{
  width: 50%;
}
</style>

