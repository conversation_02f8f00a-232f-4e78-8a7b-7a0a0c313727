<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.makeOrder')">
                <!-- 生产订单： -->
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.vin')">
                <!-- VIN码： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workCenterCode')">
                <!-- 车间： -->
                <el-select
                  v-model="query.work_center_code"
                  clearable
                  filterable
                  @change="changeWorkCenterCode"
                >
                  <el-option
                    v-for="item in dict.WORK_CENTER_CODE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.prodLineCode')">
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_code" @change="changeProdLine">
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_code"
                    :label="item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.stationCode')">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_code">
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_code"
                    :label="item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.creationDate')">
                <!-- 时间范围： -->
                <div class="block">
                  <el-date-picker
                    v-model="query.creation_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            border
            @header-dragend="crud.tableHeaderDragend()"
            ref="table"
            v-loading="crud.loading"
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions
                  style="margin-right: 150px"
                  :column="4"
                  size="small"
                  border
                >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.stationStatusId')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.station_status_id }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.workCenterCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.work_center_code }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.prodLineCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.prod_line_code }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.stationCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.station_code }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.stationDes')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.station_des }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.stationStatus')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{
                      dict.label.STATION_STATUS[props.row.station_status]
                    }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.emptyBanFlag')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.empty_ban_flag }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.serialNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.serial_num }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.palletNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.pallet_num }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.staffId')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.staff_id }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.makeOrder')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.make_order }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.dms')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.dms }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.itemProject')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.item_project }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.vin')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.vin }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.smallModelType')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.small_model_type }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.mainMaterialCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.main_material_code }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.materialColor')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.material_color }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.materialSize')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.material_size }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.shaftProcNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.shaft_proc_num }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.qualitySign')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.quality_sign }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.setSign')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.set_sign }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.checkStatus')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.check_status }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.checkCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.check_code }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.checkMsg')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.check_msg }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.engineNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.engine_num }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.driverWay')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.driver_way }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.statusWay')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{
                      dict.label.STATUS_WAY[props.row.status_way]
                    }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.lineSectionCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ props.row.line_section_code }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('lang_pack.stationstatus.allowWay')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                    >{{ dict.label.ALLOW_WAY[props.row.allow_way] }}</el-descriptions-item
                  >
                </el-descriptions>
              </template>
            </el-table-column>

            <el-table-column
              :show-overflow-tooltip="true"
              prop="creation_date"
              width="150"
              :label="$t('lang_pack.stationstatus.creationDate')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="work_center_code"
              :label="$t('lang_pack.stationstatus.workCenterCode')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_line_code"
              :label="$t('lang_pack.stationstatus.prodLineCode')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              min-width="100"
              :label="$t('lang_pack.stationstatus.stationCode')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_des"
              min-width="120"
              :label="$t('lang_pack.stationstatus.stationDes')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_status"
              :label="$t('lang_pack.stationstatus.stationStatus')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="empty_ban_flag"
              :label="$t('lang_pack.stationstatus.emptyBanFlag')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              min-width="170"
              :label="$t('lang_pack.stationstatus.serialNum')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pallet_num"
              min-width="100"
              :label="$t('lang_pack.stationstatus.palletNum')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="staff_id"
              :label="$t('lang_pack.stationstatus.staffId')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="make_order"
              min-width="100"
              :label="$t('lang_pack.stationstatus.makeOrder')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="dms"
              min-width="150"
              :label="$t('lang_pack.stationstatus.dms')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_project"
              :label="$t('lang_pack.stationstatus.itemProject')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="vin"
              min-width="100"
              :label="$t('lang_pack.stationstatus.vin')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="small_model_type"
              min-width="150"
              :label="$t('lang_pack.stationstatus.smallModelType')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="main_material_code"
              min-width="150"
              :label="$t('lang_pack.stationstatus.mainMaterialCode')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_color"
              min-width="100"
              :label="$t('lang_pack.stationstatus.materialColor')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_size"
              min-width="100"
              :label="$t('lang_pack.stationstatus.materialSize')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="set_sign"
              min-width="100"
              :label="$t('lang_pack.stationstatus.setSign')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="engine_num"
              min-width="150"
              :label="$t('lang_pack.stationstatus.engineNum')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="driver_way"
              min-width="150"
              :label="$t('lang_pack.stationstatus.driverWay')"
            />

            <!--<el-table-column  :label="$t('lang_pack.stationflow.enableFlag')" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>-->
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from "@/api/core/factory/sysProdLine";
import crudStation from "@/api/core/factory/sysStation";
import crudStationStatusRule from "@/api/pmc/stationflow/stationstatus";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import pagination from "@crud/Pagination";
const defaultForm = {
  station_status_id: "",
  work_center_code: "",
  prod_line_code: "",
  station_code: "",
  station_des: "",
  station_status: "",
  empty_ban_flag: "N",
  serial_num: "",
  pallet_num: "",
  staff_id: "",
  make_order: "",
  dms: "",
  item_project: "",
  vin: "",
  small_model_type: "",
  main_material_code: "",
  material_color: "",
  material_size: "",
  shaft_proc_num: "",
  quality_sign: "",
  set_sign: "",
  check_status: "",
  check_code: "0",
  check_msg: "",
  engine_num: "",
  driver_way: "",
  status_way: "",
  line_section_code: "",
  allow_way: "",
};
export default {
  name: "MESTATIONSTATUS",
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: "当前工位实时工件信息",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "station_status_id",
      // 排序
      sort: ["station_status_id asc"],
      // CRUD Method
      crudMethod: { ...crudStationStatusRule },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true,
      },
    });
  },
  // 数据字典
  dicts: ["ENABLE_FLAG", "WORK_CENTER_CODE", "STATION_STATUS", "STATUS_WAY", "ALLOW_WAY"],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ["admin", "me_station_status:add"],
        edit: ["admin", "me_station_status:edit"],
        del: ["admin", "me_station_status:del"],
        down: ["admin", "me_station_status:down"],
      },
      // 车间数据
      currentWorkCenterCode: "", // 当前车间(单笔)
      // 产线数据
      currentProdLineCode: "", // 当前产线(单笔)
      prodLineData: [],
      // 工位数据
      stationData: [],
    };
  },

  mounted: function () {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270;
    };
  },
  created: function () {},
  methods: {
    // 获取产线的中文描述
    getProdLineDes(prod_line_code) {
      var item = this.prodLineData.find((item) => item.prod_line_code === prod_line_code);
      if (item !== undefined) {
        return item.prod_line_des;
      }
      return prod_line_code;
    },
    // 获取工位的中文描述
    getStationDes(station_code) {
      var item = this.stationData.find((item) => item.station_code === station_code);
      if (item !== undefined) {
        return item.station_des;
      }
      return station_code;
    },

    // 更改车间
    changeWorkCenterCode(val) {
      this.currentWorkCenterCode = val; // 当前车间
      // 加载 产线LOV
      this.queryProdLine();
    },
    // 产线LOV
    queryProdLine() {
      const query = {
        work_center_code: this.currentWorkCenterCode,
        userID: Cookies.get("userName"),
      };
      lovProdLine(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.prodLineData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },
    // 更改产线
    changeProdLine(val) {
      this.currentProdLineCode = val; // 当前产线
      // 加载 工位LOV
      this.queryStation();
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get("userName"),
        prod_line_code: this.currentProdLineCode,
        enable_flag: "Y",
      };
      crudStation
        .lovStation(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },
    [CRUD.HOOK.beforeRefresh](crud) {
      var now = new Date();
      var monthn = now.getMonth() + 1;
      var monthnStart = now.getMonth();
      var yearn = now.getFullYear();
      var dayn = now.getDate();
      var h = now.getHours();
      var m = now.getMinutes();
      var s = now.getSeconds();
      var datetimeStart =
        yearn + "-" + monthnStart + "-" + dayn + " " + h + ":" + m + ":" + s;
      var datetimeEnd = yearn + "-" + monthn + "-" + dayn + " " + h + ":" + m + ":" + s;
      crud.query.creation_date = [
        // format：格式化日期函数；subDays：获得当前日期之前n天的日期
        datetimeStart,
        datetimeEnd,
      ];
    },
    /*
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将条码规则为【' + data.barcode_rule_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudBarcodeRule
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              barcode_rule_id: data.barcode_rule_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }*/
  },
};
</script>

<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
.ruleBottom {
  text-align: center;
  margin: 40px 0;
}
</style>
