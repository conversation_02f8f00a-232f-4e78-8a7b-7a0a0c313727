<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="andonlist">
            <ul ref="andon" />
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="andonboss">
            <div class="andonbossleft">
              <p><span>今日计划</span><span>{{ today_plan }}</span></p>
              <p><span>当前下线</span><span>{{ current_offline }}</span></p>
              <p><span>完成率</span><span>{{ finish_rate }}%</span></p>
            </div>
            <div class="line" />
            <div class="andonbossright">
              <p><span>JPH实绩</span><span>{{ jph_actual }}</span></p>
              <p><span>设备可动率</span><span>{{ device_mobility }}%</span></p>
              <p><span>今日停线</span><span>{{ today_stop }}分钟</span></p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbgone">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="wrapmarquee">
            <marquee loop="infinite" scrollamount="15">
              <div class="wraptext">
                <div v-if="andon_event_list.length !==0" class="wrapandonlist">
                  <p v-for="(item,index) in andon_event_list" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                </div>
                <p v-else class="redactive">{{ below_show }}</p>
              </div>
            </marquee>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getStationTime } from '@/api/pmc/sysTime'
import { getAndonContent } from '@/api/pmc/sysAndonContent'
import { getAndonReport, getAndonEvent } from '@/api/pmc/sysworkshopScreenone'
import headlbg from '@/assets/images/headlbg.png'
export default {
  name: 'workshopScreenone',
  components: {
  },
  data() {
    return {
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      headlbg: headlbg,
      today_plan: '',
      current_offline: '',
      finish_rate: '',
      jph_actual: '',
      device_mobility: '',
      today_stop: '',
      below_show: '',
      andon_event_list: [],
      andon_card_list: [],
      rownum: '',
      prod_line_des: '',
      stationTimeTimer: null,
      andonReportTimer: null,
      andonContentTimer: null,
      andonEventTimer: null

    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.stationTimeTimer = setInterval(() => {
      this.initstationTime()
    }, 1000)
    // 加载工位屏andon显示
    this.initAndonReport()
    this.andonReportTimer = setInterval(() => {
      this.initAndonReport()
    }, 1000)
    // 加载工位屏andon数据
    this.initAndonContent()
    this.andonContentTimer = setInterval(() => {
      this.initAndonContent()
    }, 5000)
    // 加载工位屏andon事件
    this.initAndonEvent()
    this.andonEventTimer = setInterval(() => {
      this.initAndonEvent()
    }, 2000)
    // 启动前端清缓存机制
    this.setStorage()
  },
  mounted() {
    this.setAndonStyle()
    this.setAndonStyleone()
  },
  beforeDestroy() {
    clearInterval(this.stationTimeTimer)
    clearInterval(this.andonReportTimer)
    clearInterval(this.andonContentTimer)
    clearInterval(this.andonEventTimer)
  },
  methods: {
    // 启动前端30分钟清缓存机制
    setStorage() {
      setInterval(this.clearStorage, 1800000)
    },
    clearStorage() {
      clearInterval(this.stationTimeTimer)
      this.stationTimeTimer = null
      this.stationTimeTimer = setInterval(() => {
        this.initstationTime()
      }, 1000)
      clearInterval(this.andonReportTimer)
      this.andonReportTimer = null
      this.andonReportTimer = setInterval(() => {
        this.initAndonReport()
      }, 1000)
      clearInterval(this.andonContentTimer)
      this.andonContentTimer = null
      this.andonContentTimer = setInterval(() => {
        this.initAndonContent()
      }, 5000)
      clearInterval(this.andonEventTimer)
      this.andonEventTimer = null
      this.andonEventTimer = setInterval(() => {
        this.initAndonEvent()
      }, 2000)
    },
    setAndonStyle() {
      switch (this.$route.query.prod_line_code) {
        case 'FF':
          this.$refs.andon.classList.add('otherliMF')
          break
        case 'YF':
          this.$refs.andon.classList.add('otherliYF')
          break
        case 'PF':
          this.$refs.andon.classList.add('otherliQF')
          break
        case 'XF':
          this.$refs.andon.classList.add('otherli')
          break
        case 'MF':
          this.$refs.andon.classList.add('otherliMF')
          break
        case 'HF':
          this.$refs.andon.classList.add('otherliQF')
          break
        case 'QF':
          this.$refs.andon.classList.add('otherliQF')
          break
        case 'HC':
          this.$refs.andon.classList.add('otherli')
          break
        case 'ZZ':
          this.$refs.andon.classList.add('otherliNS1')
          break
        case 'DP':
          this.$refs.andon.classList.add('otherliDP')
          break
      }
    },
    setAndonStyleone() {
      switch (this.$route.query.prod_line_type) {
        case 'NS1':
          this.$refs.andon.classList.add('otherliNS1')
          break
        case 'NS2':
          this.$refs.andon.classList.add('otherliNS2')
          break
      }
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏andon显示
    initAndonReport() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        prod_line_type: this.$route.query.prod_line_type,
        work_center_code: this.$route.query.work_center_code
      }
      getAndonReport(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.andon_card_list = res.data[0].andon_card_list
          if (this.andon_card_list === null || this.andon_card_list.length === 0) {
            this.andon_card_list = []
          }
          this.prod_line_des = res.data[0].prod_line_des
          this.rownum = res.data[0].rownum
          var andonArr = []
          for (let i = 0; i < this.andon_card_list.length; i++) {
            andonArr.push(`<li id="id_${i + this.rownum}" class="commonstatus"><span>${this.$route.query.prod_line_code}</span><span>${i + this.rownum}</span></li>`)
          }
          this.$refs.andon.innerHTML = andonArr.join('')
          // console.log(document.querySelectorAll('.commonstatus'))
          for (let i = 0; i < document.querySelectorAll('.commonstatus').length; i++) {
            document.querySelectorAll('.commonstatus')[i].classList.remove('nomalstatus', 'warningstatus', 'errorstatus')
          }
          for (let i = 0; i < this.andon_card_list.length; i++) {
            var station_order = 'id_' + this.andon_card_list[i].station_order
            var andon_card_type = this.andon_card_list[i].andon_card_type
            if (andon_card_type === '0') {
              document.querySelector(`#${station_order}`).classList.add('nomalstatus')
            } else if (andon_card_type === '1') {
              document.querySelector(`#${station_order}`).classList.add('warningstatus')
            } else if (andon_card_type === '2') {
              document.querySelector(`#${station_order}`).classList.add('errorstatus')
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏andon数据
    initAndonContent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        line_section_code:this.$route.query.line_section_code,
        fastcode_group_code: this.$route.query.prod_line_code + '_UP_DOWN_LINE_STATION'
      }
      getAndonContent(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            this.below_show = ''
            this.today_plan = ''
            this.current_offline = ''
            this.finish_rate = ''
            this.jph_actual = ''
            this.device_mobility = ''
            this.today_stop = ''
            return
          }
          this.current_offline = res.contentList[0].current_offline
          if (this.current_offline === '-1') {
            this.today_plan = res.today_plan
            this.current_offline = res.current_offline
            this.finish_rate = res.finish_rate
            this.jph_actual = res.jph_actual
            // 下面【device_mobility、today_stop】当current_offline = -1时候取contentList外面的值
            this.device_mobility = res.device_mobility
            this.today_stop = res.today_stop
          } else {
            this.today_plan = res.contentList[0].today_plan
            this.current_offline = res.contentList[0].current_offline
            this.finish_rate = res.contentList[0].finish_rate
            this.jph_actual = res.contentList[0].jph_actual
            this.device_mobility = res.contentList[0].device_mobility
            this.today_stop = res.contentList[0].today_stop
          }
          this.below_show = res.contentList[0].below_show
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏andon事件
    initAndonEvent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        prod_line_type: this.$route.query.prod_line_type,
        work_center_code: this.$route.query.work_center_code
      }
      getAndonEvent(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data[0].andon_event_list === null || res.data[0].andon_event_list.length === 0) {
            this.andon_event_list = []
          }
          this.andon_event_list = res.data[0].andon_event_list
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.carInfoTable{
  min-height: calc(100vh - 50px);
}
.tableheight{
  min-height: calc(100vh - 70px);
}
.marginT{
  margin-top: 10px;
}
.slideT{
  margin-top: 20px;
}
.cardheadbg{
  background-color: #031c45;
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  // margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .andonlist{
  height: 237px;
  ul{
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    li{
      list-style: none;
      width: 5.2%;
      height: 6%;
      text-align: center;
      color: #ffffff;
      display: flex;
      align-items: center;
      flex-direction: column;
      margin: 6px;
      margin-right: 2px;
      border-radius: 5px;
      span{
        font-size: 30px;
        font-weight: bold;
      }
    }
  }
}
.andonboss{
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  .line{
    width: 1px;
    height: 190px;
    background: #5275b9;
  }
  .andonbossleft{
    margin-left: 90px;
    p{
      margin: 23px 0;
      display: flex;
      align-items: center;
      span:nth-of-type(1){
        font-size: 36px;
    font-weight: 700;
    color: #ffffff;
    background-color: #0070c2;
    width: 230px;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
      }
      span:nth-of-type(2){
      font-size: 45px;
    font-weight: 700;
    color: #ffffff;
    width: 300px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
      }
    }
  }
  .andonbossright{
    margin-left: 100px;
    p{
      margin: 23px 0;
      display: flex;
      align-items: center;
      span:nth-of-type(1){
        font-size: 36px;
    font-weight: 700;
    color: #ffffff;
    background-color: #0070c2;
    width: 230px;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
      }
      span:nth-of-type(2){
      font-size: 45px;
    font-weight: 700;
    color: #ffffff;
    width: 300px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
      }
    }
  }
}
.cardheadbgone{
  background-color: #0070c2;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  .wrapandonlist{
    display: flex;
    align-items: center;
    p{
      margin-right: 50px;
    }
  }
  p{
  color: #fbff03;
  font-size: 60px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  height: 80px;
  word-spacing: 150px;
  }
}
::v-deep .commonstatus{
  background-color: #00a047;
}
::v-deep .nomalstatus{
  background-color: #00a047;
}
::v-deep .errorstatus{
  background-color: #ff0000;
}
::v-deep .warningstatus{
  background-color: #ffd600;
}
::v-deep .el-card{
  border-radius: 0;
}
.redactive{
  color: red !important;
}
::v-deep .otherliYF{
  li{
      width: 13.58% !important;
      span{
        font-size: 46px !important;
      }
  }
}
::v-deep .otherliMF{
  li{
      width: 15.9% !important;
      span{
        font-size: 46px !important;
      }
  }
}
::v-deep .otherliQF{
  flex-wrap: nowrap !important;
  li{
      width: 15.9% !important;
      span{
        font-size: 66px !important;
      }
  }
}
::v-deep .otherliNS1{
  li{
      width: 7.64% !important;
  }
}
::v-deep .otherliNS2{
  li{
      width: 6.46% !important;
  }
}
::v-deep .otherliDP{
  li{
      width: 13.58% !important;
  }
}
</style>
