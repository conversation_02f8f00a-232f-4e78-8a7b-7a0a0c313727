<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="版本号：">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工艺路线编码：">
                <el-input v-model="query.craft_route_main_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工艺路线描述：">
                <el-input v-model="query.craft_route_main_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-document" plain round @click="recipeDrawerVisible = true">
            配方
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="配方" prop="recipe_id">
            <el-select v-model="form.recipe_id" filterable clearable>
              <el-option v-for="item in recipeList" :key="item.recipe_id" :label="item.recipe_name + ' ' + item.recipe_version" :value="item.recipe_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="工艺路线编码" prop="craft_route_main_code">
            <el-input v-model="form.craft_route_main_code" />
          </el-form-item>
          <el-form-item label="工艺路线描述" prop="craft_route_main_des">
            <el-input v-model="form.craft_route_main_des" />
          </el-form-item>
          <el-form-item label="主物料号" prop="main_material_code">
            <el-input v-model="form.main_material_code" />
          </el-form-item>
          <el-form-item label="主物料描述" prop="main_material_des">
            <el-input v-model="form.main_material_des" />
          </el-form-item>
          <el-form-item label="数据分类" prop="data_save_type">
            <el-select v-model="form.data_save_type">
              <el-option v-for="item in [{ label: '分线', id: 'SUB' }, { label: '总线', id: 'BUS' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否为主路线" prop="main_route_flag">
            <el-select v-model="form.main_route_flag">
              <el-option v-for="item in [{ label: '否', id: 'N' }, { label: '是', id: 'Y' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="目标主路线ID" prop="target_craft_route_main_id">
            <el-select v-model="form.target_craft_route_main_id" filterable clearable>
              <el-option v-for="item in craftRouteList" :key="item.craft_route_main_id" :label="item.craft_route_main_code + '/' + item.craft_route_main_des + '/' + item.main_material_code + '/' + item.main_material_des" :value="item.craft_route_main_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="main_order_by">
            <el-input v-model.number="form.main_order_by" />
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="true" title="配方信息" :visible.sync="recipeDrawerVisible" size="80%" @closed="getRecipeList()">
        <recipe v-if="recipeDrawerVisible" ref="recipe" />
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="true" title="工位信息" :visible.sync="stationDrawerVisible" size="80%">
        <station v-if="stationDrawerVisible" ref="station" :craft_route_main_id="currentId" />
      </el-drawer>

      <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="导入质量数据" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input
            v-if="isUpLoadError"
            v-model="errorMsg"
            type="textarea"
            :rows="5"
          />
          <p>注：</p>
          <p>1、确认导入数据表格是从当前页面操作导出的工艺路线质量数据</p>
          <p>2、导入数据中A至F列为数据识别列，用于匹配是否是当前导入工艺路线数据，不可编辑</p>
          <div style="text-align: center;margin-top:10px">
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" @click="toButDrawerUpload">导入</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="配方ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_id }}</el-descriptions-item>
                  <el-descriptions-item label="配方类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_type }}</el-descriptions-item>
                  <el-descriptions-item label="配方描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_name }}</el-descriptions-item>
                  <el-descriptions-item label="版本号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_version }}</el-descriptions-item>
                  <el-descriptions-item label="责任人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.liable_person }}</el-descriptions-item>
                  <el-descriptions-item label="更新说明" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.update_remark }}</el-descriptions-item>
                  <el-descriptions-item label="工艺路线ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.craft_route_main_id }}</el-descriptions-item>
                  <el-descriptions-item label="工艺路线编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.craft_route_main_code }}</el-descriptions-item>
                  <el-descriptions-item label="工艺路线描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.craft_route_main_des }}</el-descriptions-item>
                  <el-descriptions-item label="主物料号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.main_material_code }}</el-descriptions-item>
                  <el-descriptions-item label="主物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.main_material_des }}</el-descriptions-item>
                  <el-descriptions-item label="数据分类" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.data_save_type }}</el-descriptions-item>
                  <el-descriptions-item label="是否为主路线" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.main_route_flag }}</el-descriptions-item>
                  <el-descriptions-item label="目标主路线ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.target_craft_route_main_id }}</el-descriptions-item>
                  <el-descriptions-item label="排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.main_order_by }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_name" min-width="100" label="配方描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_version" min-width="100" label="版本号" />
            <el-table-column  :show-overflow-tooltip="true" prop="liable_person" min-width="100" label="责任人" />
            <el-table-column  :show-overflow-tooltip="true" prop="craft_route_main_code" min-width="100" label="工艺路线编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="craft_route_main_des" min-width="100" label="工艺路线描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="main_material_code" min-width="100" label="主物料号" />
            <el-table-column  :show-overflow-tooltip="true" prop="main_material_des" min-width="100" label="主物料描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="data_save_type" min-width="100" label="数据分类" />
            <el-table-column  :show-overflow-tooltip="true" prop="main_route_flag" min-width="100" label="是否为主路线" />
            <el-table-column  :show-overflow-tooltip="true" prop="main_order_by" min-width="50" label="排序" />
            <el-table-column  :show-overflow-tooltip="true" width="70" label="工位" align="center">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendStation(scope.row)">工位</el-tag>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="200" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission">
                  <template slot="right">
                    <el-button slot="reference" type="text" size="small" @click="handlerCopy(scope.row)">复制</el-button>
                    <el-button slot="reference" type="text" size="small" style="margin-left:0px;" @click="handlerExportQuality(scope.row)">导出</el-button>
                    <el-button slot="reference" type="text" size="small" style="margin-left:0px;" @click="handlerImportQuality(scope.row)">导入</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudRecipeCrMain from '@/api/mes/core/recipeCrMain'
import { downloadFile } from '@/utils/index'
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import recipe from '@/views/mes/core/recipe/recipe'
import station from '@/views/mes/core/craftRoute/station'
const defaultForm = {
  craft_route_main_id: '',
  recipe_id: '',
  craft_route_main_code: '',
  craft_route_main_des: '',
  main_material_code: '',
  main_material_des: '',
  data_save_type: 'BUS',
  main_route_flag: 'N',
  target_craft_route_main_id: '',
  main_order_by: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MES_RECIPE_CR_MAIN',
  components: { crudOperation, rrOperation, udOperation, pagination, recipe, station },
  cruds() {
    return CRUD({
      title: '工艺路线信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'craft_route_main_id',
      // 排序
      sort: ['craft_route_main_id asc'],
      // CRUD Method
      crudMethod: { ...crudRecipeCrMain },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_cr_main:add'],
        edit: ['admin', 'mes_recipe_cr_main:edit'],
        del: ['admin', 'mes_recipe_cr_main:del'],
        down: ['admin', 'mes_recipe_cr_main:down']
      },
      rules: {
        recipe_id: [{ required: true, message: '请选择配方', trigger: 'blur' }],
        craft_route_main_code: [{ required: true, message: '请输入工艺路线编码', trigger: 'blur' }],
        craft_route_main_des: [{ required: true, message: '请输入工艺路线描述', trigger: 'blur' }],
        main_material_code: [{ required: true, message: '请输入主物料号', trigger: 'blur' }],
        main_material_des: [{ required: true, message: '请输入主物料描述', trigger: 'blur' }],
        main_order_by: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      recipeDrawerVisible: false,
      recipeList: [],
      craftRouteList: [],
      stationDrawerVisible: false,
      currentId: 0,
      importDialogVisible: false,
      uploadLimit: 1,
      uploadAccept: '.xlsx',
      fileList: [],
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: ''
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.getRecipeList()
  },
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将工艺路线为【' + data.craft_route_main_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudRecipeCrMain
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              craft_route_main_id: data.craft_route_main_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    getRecipeList() {
      this.recipeList = []
      crudRecipe
        .sel({
          user_name: Cookies.get('userName'),
          recipe_type: 'PROCESS',
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.recipeList = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    getCraftRouteList(recipe_id) {
      this.craftRouteList = []
      crudRecipeCrMain
        .sel({
          user_name: Cookies.get('userName'),
          recipe_id: recipe_id,
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.craftRouteList = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    [CRUD.HOOK.beforeToCU](crud) {
      this.getCraftRouteList(crud.form.recipe_id)
    },
    opendStation(row) {
      this.currentId = row.craft_route_main_id
      this.stationDrawerVisible = true
    },
    handlerCopy(row) {
      this.$confirm('确定要复制工艺路线【' + row.craft_route_main_des + '】来创建一个新的工艺路线吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudRecipeCrMain
            .copyCrMain({
              user_name: Cookies.get('userName'),
              craft_route_main_id: row.craft_route_main_id
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '复制创建成功', type: 'success' })
                this.crud.toQuery()
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: ex, type: 'error' })
            })
        })
        .catch(() => {

        })
    },
    handlerExportQuality(row) {
      const loading = this.$loading({
        lock: true,
        text: '数据处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      crudRecipeCrMain.exportQualityData({ craft_route_main_id: row.craft_route_main_id }).then(result => {
        loading.close()
        downloadFile(result, '工艺路线信息-质量数据数据', 'xlsx')
      }).catch(() => {
        loading.close()
      })
    },
    handlerImportQuality(row) {
      this.currentId = row.craft_route_main_id
      this.errorMsg = ''
      this.isUpLoadError = false
      this.importDialogVisible = true
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.fileData.set('craftRouteMainId', this.currentId.toString())
      this.$refs.upload.submit()

      // 配置路径
      var method = '/mes/core/MesRecipeCrMainQualityImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      const loading = this.$loading({
        lock: true,
        text: '数据处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          loading.close()
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
