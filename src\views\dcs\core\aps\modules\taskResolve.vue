<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="解析结果" width="80%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 工位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              :label="$t('lang_pack.sortingResults.stationCode')"
              width="120"
              align="center"
            />
            <!-- 零件条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_barcode"
              :label="$t('lang_pack.sortingResults.partBarcodeNumber')"
              width="120"
              align="center"
            />
            <!-- 零件编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_code"
              :label="$t('lang_pack.sortingResults.partCode')"
              width="120"
              align="center"
            />
            <!-- 零件图号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_draw"
              :label="$t('lang_pack.sortingResults.partDrawingNumber')"
              width="120"
              align="center"
            />
            <!-- 零件物料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_num"
              :label="$t('lang_pack.sortingResults.partMaterialNumber')"
              width="120"
              align="center"
            />
            <!-- 切割时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_datetime"
              :label="$t('lang_pack.sortingResults.nedCuttingTime')"
              width="120"
              align="center"
            />
            <!-- 工艺路线编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="craft_path_code"
              :label="$t('lang_pack.sortingResults.routingCode')"
              width="100"
              align="center"
            />
            <!-- 喷码顶点X坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="print_top_x"
              :label="$t('lang_pack.sortingResults.sprayCodeXCoordinate')"
              width="120"
              align="center"
            />
            <!-- 喷码顶点Y坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="print_top_y"
              :label="$t('lang_pack.sortingResults.sprayCodeYCoordinate')"
              width="120"
              align="center"
            />
            <!-- 零件类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_type"
              :label="$t('lang_pack.sortingResults.PartType')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PART_TYPE[scope.row.part_type] }}
              </template>
            </el-table-column>
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_length"
              :label="$t('lang_pack.sortingResults.length')"
              width="80"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_width"
              :label="$t('lang_pack.sortingResults.width')"
              width="80"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_thickness"
              :label="$t('lang_pack.sortingResults.thick')"
              width="80"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_weight"
              :label="$t('lang_pack.sortingResults.weight')"
              width="80"
              align="center"
            />
            <!-- 零件加工工序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="next_process"
              :label="$t('lang_pack.sortingResults.nextRouting')"
              width="110"
              align="center"
            />
            <!-- 零件配送地点 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_attr"
              :label="$t('lang_pack.sortingResults.reservedPartProperties')"
              width="100"
              align="center"
            />
            <!-- 分拣所需时间(毫秒) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_time"
              :label="$t('lang_pack.sortingResults.timeRequiredForSorting')"
              width="130"
              align="center"
            />
          </el-table>
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudTaskResolve from '@/api/dcs/core/aps/taskResolve'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: '',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '解析结果',
      // 唯一字段
      idField: 'mo_id',
      // 排序
      sort: ['mo_id asc'],
      // CRUD Method
      crudMethod: { ...crudTaskResolve },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        mo_id: this.propsData.mo_id
      }
    })
  },
  props: {
    mo_id: {
      type: [String, Number],
      default: ''
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false
    }
  },
  dicts: ['PART_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
</style>
