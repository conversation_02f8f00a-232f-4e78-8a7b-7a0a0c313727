import request from '@/utils/request'

// 查询逻辑属性
export function selLogicAttrItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrItemSel',
    method: 'post',
    data
  })
}
// 新增逻辑属性
export function insLogicAttrItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrItemIns',
    method: 'post',
    data
  })
}
// 修改逻辑属性
export function updLogicAttrItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrItemUpd',
    method: 'post',
    data
  })
}
// 删除逻辑属性
export function delLogicAttrItem(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrItemDel',
    method: 'post',
    data
  })
}

// 逻辑属性LOV
export function logicAttrItemLov(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicAttrItemLov',
    method: 'post',
    data
  })
}
export default { selLogicAttrItem, insLogicAttrItem, updLogicAttrItem, delLogicAttrItem,
  logicAttrItemLov }
