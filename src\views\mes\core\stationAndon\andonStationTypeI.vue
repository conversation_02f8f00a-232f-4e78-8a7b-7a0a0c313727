<template>
  <el-card class="box-card2" shadow="always" style="margin-top: 10px">
    <div
      slot="header"
      class="clearfix"
      style="
        font-size: 14px;
        color: #757575;
        font-weight: 700;
        text-align: right;
      "
    >
      <span style="float: left">安灯明细维护</span>

      <el-button
        v-if="true"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-plus"
        style="margin-top: 3px; margin-left: 5px"
        plain
        @click="handleAdd"
      >
        新增
      </el-button>
    </div>
    <el-table
      ref="table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%"
      :cell-style="crud.cellStyle"
      :stripe="true"
      height="250px"
      :highlight-current-row="true"
      @sort-change="sortChage"
    >
      <el-table-column 
        :show-overflow-tooltip="true"
        prop="andon_station_type_i_code"
        width="120"
        label="安灯明细编码"
        sortable="custom"
      />
      <el-table-column 
        :show-overflow-tooltip="true"
        prop="andon_station_type_i_des"
        min-width="100"
        label="安灯明细描述"
        sortable="custom"
      />
      <el-table-column 
        :show-overflow-tooltip="true"
        prop="andon_i_type"
        min-width="100"
        label="安灯明细类型"
        sortable="custom"
      />
      <el-table-column 
        :show-overflow-tooltip="true"
        prop="material_code"
        min-width="100"
        label="物料编码"
        sortable="custom"
      />
      <el-table-column 
        :show-overflow-tooltip="true"
        prop="material_des"
        min-width="100"
        label="物料描述"
        sortable="custom"
      />
      <el-table-column 
        :show-overflow-tooltip="true"
        prop="andon_i_status"
        min-width="100"
        label="安灯明细状态"
        sortable="custom"
      />
      <el-table-column 
        label="有效标识"
        align="center"
        prop="enable_flag"
        width="100"
      >
        <template
          slot-scope="scope"
        ><!--取到当前单元格-->
          {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
        </template>
      </el-table-column>
      <!-- Table单条操作-->
      <el-table-column  label="操作" width="125" align="center" fixed="right">
        <template slot-scope="scope">
          <el-link
            icon="el-icon-edit"
            type="primary"
            style="color: #0072d6; font-size: 12px"
            @click="handleTableEdit(scope.row)"
          >编辑</el-link>
          &nbsp;&nbsp;
          <el-link
            icon="el-icon-delete"
            type="primary"
            style="color: #0072d6; font-size: 12px"
            @click="handleTableDel(scope.row)"
          >删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <!--<pagination />-->
    <el-pagination
      :page-size.sync="pageTable.size"
      :total="pageTable.total"
      :current-page.sync="pageTable.page"
      :page-sizes="[20, 30, 40, 50, 100]"
      style="margin-top: 8px; float: right"
      layout="total, prev, pager, next, sizes"
      @size-change="toSizeTableChangeHandler($event)"
      @current-change="toPageTableChangeHandler"
    />

    <el-drawer
      append-to-body
      :title="dialogTitle"
      :visible.sync="dialogVisbleSync"
      size="450px"
      @closed="drawerClosed"
    >
      <!--<el-form ref="form" :inline="true" :model="form" :rules="rules" size="small" label-width="100px">-->
      <el-form
        ref="form"
        :inline="false"
        :model="form"
        :rules="rules"
        size="small"
        label-width="110px"
      >
        <el-form-item label="安灯明细编码" prop="andon_station_type_i_code">
          <el-input
            v-model="form.andon_station_type_i_code"
            style="width: 90%"
          />
        </el-form-item>
        <el-form-item label="安灯明细描述" prop="andon_station_type_i_des">
          <el-input
            v-model="form.andon_station_type_i_des"
            style="width: 90%"
          />
        </el-form-item>
        <el-form-item label="安灯明细类型" prop="andon_i_type">
          <el-input v-model="form.andon_i_type" style="width: 90%" />
        </el-form-item>
        <el-form-item label="物料编码" prop="material_code">
          <el-input v-model="form.material_code" style="width: 90%" />
        </el-form-item>
        <el-form-item label="物料描述" prop="material_des">
          <el-input v-model="form.material_des" style="width: 90%" />
        </el-form-item>
        <el-form-item label="安灯明细状态" prop="andon_i_status">
          <el-input v-model="form.andon_i_status" style="width: 90%" />
        </el-form-item>
        <el-form-item label="有效标识">
          <el-radio-group
            v-model="form.enable_flag"
            :disabled="false"
            style="width: 200px"
          >
            <el-radio label="Y">有效</el-radio>
            <el-radio label="N">失效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center; margin-bottom: 10px">
        <el-button
          size="small"
          icon="el-icon-close"
          plain
          @click="handleFromCancel"
        >取消</el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-check"
          @click="handleFormSubmit('form')"
        >确认</el-button>
      </div>
      <!--</el-dialog>-->
    </el-drawer>
  </el-card>
</template>
<script>
import Cookies from 'js-cookie'
import {
  queryAllMesAndonStationTypeI,
  insMesAndonStationTypeI,
  updMesAndonStationTypeI,
  delMesAndonStationTypeI
} from '@/api/mes/core/andonStationTypeI'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'MES_ANDON_STATION_TYPE_I',
  components: {},
  cruds() {
    return CRUD({ title: 'Tag', queryOnPresenterCreated: false })
  },
  mixins: [presenter(), header(), form(null), crud()],

  // Table 内按钮状态(组/明细)
  props: {},

  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',

      andonStationTypeId: 0,
      tableLoading: false,
      tableData: [],
      selectedTable: [], // 已选择项
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      query: {
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'andon_station_type_i_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      dialogVisbleSync: false,
      dialogTitle: '安灯明细维护',
      form: {
        andon_station_type_i_id: '',
        andon_station_type_id: '',
        andon_station_type_i_code: '',
        andon_station_type_i_des: '',
        andon_i_type: '',
        material_code: '',
        material_des: '',
        andon_i_status: '',
        enable_flag: ''
      },
      proceduceTypeData: [],
      rules: {
        // 提交验证规则
        andon_station_type_i_code: [
          { required: true, message: '请输入安灯明细编码', trigger: 'blur' }
        ],
        andon_station_type_i_des: [
          { required: true, message: '请输入安灯明细描述', trigger: 'blur' }
        ]
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  created: function() {
    // 查询
    this.handleQuery()
  },

  methods: {
    handleAdd() {
      this.form.andon_station_type_i_id = ''
      this.form.andon_station_type_id = this.andonStationTypeId
      this.form.andon_station_type_i_code = ''
      this.form.andon_station_type_i_des = ''
      this.form.andon_i_type = ''
      this.form.material_code = ''
      this.form.material_des = ''
      this.form.andon_i_status = ''
      this.form.enable_flag = 'Y'
      this.dialogTitle = '新增安灯明细'
      this.dialogVisbleSync = true
    },
    parentQuery(andonStationTypeId) {
      this.andonStationTypeId = andonStationTypeId
      this.handleQuery()
    },
    handleQuery() {
      const query = {
        andon_station_type_id: this.andonStationTypeId,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }

      // this.tableLoading = true;
      queryAllMesAndonStationTypeI(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableData = defaultQuery.data
            } else {
              this.tableData = []
            }
            this.pageTable.total = defaultQuery.count
            this.tableLoading = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleTableEdit(data) {
      this.form.andon_station_type_i_id = data.andon_station_type_i_id
      this.form.andon_station_type_id = data.andon_station_type_id
      this.form.andon_station_type_i_code = data.andon_station_type_i_code
      this.form.andon_station_type_i_des = data.andon_station_type_i_des
      this.form.andon_i_type = data.andon_i_type
      this.form.material_code = data.material_code
      this.form.material_des = data.material_des
      this.form.andon_i_status = data.andon_i_status
      this.form.enable_flag = data.enable_flag
      this.dialogTitle = '编辑安灯明细'
      this.dialogVisbleSync = true
    },
    handleTableDel(data) {
      // Table删除(单笔)
      this.$confirm(
        `此操作将永久删除${data.andon_station_type_i_des}是否继续?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          // 格式化删除条件
          const del = {
            id: data.andon_station_type_i_id
          }

          delMesAndonStationTypeI(del)
            .then((res) => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                // 查询
                this.handleQuery()
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: 'error'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    handleFormSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            andon_station_type_i_id: this.form.andon_station_type_i_id,
            andon_station_type_id: this.form.andon_station_type_id,
            andon_station_type_i_code: this.form.andon_station_type_i_code,
            andon_station_type_i_des: this.form.andon_station_type_i_des,
            andon_i_type: this.form.andon_i_type,
            material_code: this.form.material_code,
            material_des: this.form.material_des,
            andon_i_status: this.form.andon_i_status,
            enable_flag: this.form.enable_flag
          }

          // 新增
          if (
            this.form.andon_station_type_i_id === undefined ||
            this.form.andon_station_type_i_id.length <= 0
          ) {
            insMesAndonStationTypeI(save)
              .then((res) => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code == 0) {
                  this.dialogVisbleSync = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })

                  // 查询
                  this.handleQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updMesAndonStationTypeI(save)
              .then((res) => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  this.dialogVisbleSync = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })

                  // 查询
                  this.handleQuery()
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    handleFromCancel() {
      // 取消
      this.dialogVisbleSync = false // 弹出框隐藏
    },
    drawerClosed() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val

      // 查询
      this.handleQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val

      // 查询
      this.handleQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'andon_station_type_i_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.handleQuery()
    }
  }
}
</script>
