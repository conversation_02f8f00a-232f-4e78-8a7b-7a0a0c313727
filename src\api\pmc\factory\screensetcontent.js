import request from '@/utils/request'

// 查询大屏内容
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetContentSel',
    method: 'post',
    data
  })
}
// 新增大屏内容
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetContentIns',
    method: 'post',
    data
  })
}
// 修改大屏内容
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetContentUpd',
    method: 'post',
    data
  })
}
// 删除大屏内容
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetContentDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

