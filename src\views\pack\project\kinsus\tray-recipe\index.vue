<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!-- Tray盘物料编码  -->
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.TrayMaterialCode') + ':'">
                <el-input v-model="query.tray_material_code" clearable size="small" />
              </el-form-item>
            </div>
            <!-- 版本 -->
            <!-- <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.version') + ':'">
                <el-input v-model="query.model_version" clearable size="small" />
              </el-form-item>
            </div> -->
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title "
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="265px"
              :inline="true"
            >
              <!-- Tray盘物料编码 -->
              <el-form-item :label="$t('lang_pack.vie.TrayMaterialCode')" prop="tray_material_code">
                <el-input v-model="form.tray_material_code" clearable size="small" />
              </el-form-item>
              <!-- 版本 -->
              <!-- <el-form-item :label="$t('lang_pack.vie.version')" prop="model_version">
                <el-input v-model="form.model_version" clearable size="small" />
              </el-form-item> -->
              <!-- 长 -->
              <!-- <el-form-item :label="$t('lang_pack.vie.plateLen')" prop="pcs_length">
                <el-input v-model.number="form.pcs_length" type="number" clearable size="small" @input="handleInput('pcs_length')" />
              </el-form-item> -->
              <!-- 宽 -->
              <!-- <el-form-item :label="$t('lang_pack.vie.plateWid')" prop="pcs_width">
                <el-input v-model.number="form.pcs_width" type="number" clearable size="small" @input="handleInput('pcs_width')" />
              </el-form-item> -->
              <!-- 厚 -->
              <!-- <el-form-item :label="$t('lang_pack.vie.plateThi')" prop="pcs_thickness">
                <el-input v-model.number="form.pcs_thickness" type="number" clearable size="small" @input="handleInput('pcs_thickness')" />
              </el-form-item> -->
              <!-- 重 -->
              <!-- <el-form-item label="板重(mg)" prop="pcs_weight">
                <el-input v-model.number="form.pcs_weight" type="number" clearable size="small" @input="handleInput('pcs_weight')" />
              </el-form-item> -->
              <!-- Tray高度 -->
              <el-form-item :label="'Tray高度(um)'" prop="tray_height">
                <el-input v-model="form.tray_height" type="number" clearable size="small" @input="handleInput('tray_height')" />
              </el-form-item>
              <!-- Tray重量 -->
              <el-form-item :label="'Tray重量(g)'" prop="tray_weight">
                <el-input v-model="form.tray_weight" type="number" clearable size="small" @input="handleInput('tray_weight')" />
              </el-form-item>
              <!-- Tray容积 -->
              <!-- <el-form-item :label="'Tray容积'" prop="tray_volume">
                <el-input v-model.number="form.tray_volume" type="number" clearable size="small" @input="handleInput('tray_volume')" />
              </el-form-item> -->
              <!-- Overlay高度 -->
              <!-- <el-form-item :label="'Overlay高度(um)'" prop="overlay_height">
                <el-input v-model.number="form.overlay_height" type="number" clearable size="small" @input="handleInput('overlay_height')" />
              </el-form-item> -->
              <!-- Overlay重量 -->
              <!-- <el-form-item :label="'Overlay重量(mg)'" prop="overlay_weight">
                <el-input v-model.number="form.overlay_weight" type="number" clearable size="small" @input="handleInput('overlay_weight')" />
              </el-form-item> -->
              <!-- 重量偏差 -->
              <!-- <el-form-item :label="$t('view.field.recipe.weight_error')" prop="weight_error">
                <el-input v-model="form.weight_error" clearable size="small" />
              </el-form-item> -->
              <!-- 束带方式(一字) -->
              <!-- <el-form-item :label="'束带方式(一字)'" prop="bd_way1">
                <el-input v-model="form.bd_way1" clearable size="small" />
              </el-form-item> -->
              <!-- 束带方式(三字) -->
              <!-- <el-form-item :label="'束带方式(三字)'" prop="bd_way3">
                <el-input v-model="form.bd_way3" clearable size="small" />
              </el-form-item> -->
              <!-- 喷墨模板 -->
              <!-- <el-form-item :label="$t('view.field.recipe.inkjet_tpl')" prop="inkjet_tpl">
                <el-input v-model="form.inkjet_tpl" clearable size="small" />
              </el-form-item> -->
              <!-- 有效标识 -->
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <!-- 注释内容 -->
            <p>
              <span style="color: red">注：1mm = 1000um</span><br>
              <!-- <span style="color: red">注：1g = 1000mg</span><br> -->
            </p>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item :label="$t('lang_pack.vie.partNum')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.version')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_version }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateLen')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_length }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateWid')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_width }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateThi')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_thickness }}</el-descriptions-item>
                  <el-descriptions-item label="板重(g)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pcs_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="'Tray高度(um)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tray_height }}</el-descriptions-item>
                  <el-descriptions-item :label="'Tray重量(g)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tray_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="'Tray容积'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tray_volume }}</el-descriptions-item>
                  <el-descriptions-item :label="'Overlay高度(um)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.overlay_height }}</el-descriptions-item>
                  <el-descriptions-item :label="'Overlay重量(g)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.overlay_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.field.recipe.weight_error')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.weight_error }}</el-descriptions-item>
                  <el-descriptions-item :label="'束带方式(一字)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bd_way1 }}</el-descriptions-item>
                  <el-descriptions-item :label="'束带方式(三字)'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bd_way3 }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('view.field.recipe.inkjet_tpl')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.inkjet_tpl }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.commonPage.validIdentificationt')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <!-- Tray盘物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_material_code"
              :label="$t('lang_pack.vie.TrayMaterialCode')"
              width="260"
              align="center"
            />
            <!-- 版本 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_material_code"
              :label="$t('lang_pack.vie.TrayMaterialCode')"
              width="130"
              align="center"
            /> -->
            <!-- 板长 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="pcs_length"
              :label="$t('lang_pack.vie.plateLen')"
              width="130"
              align="center"
            /> -->
            <!-- 板宽 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="pcs_width"
              :label="$t('lang_pack.vie.plateWid')"
              width="130"
              align="center"
            /> -->
            <!-- 板厚 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="pcs_thickness"
              :label="$t('lang_pack.vie.plateThi')"
              width="130"
              align="center"
            /> -->
            <!-- 板重 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="pcs_weight"
              label="板重(mg)"
              width="130"
              align="center"
            /> -->
            <!-- Tray高度 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_height"
              label="Tray高度(um)"
              width="260"
              align="center"
            />
            <!-- Tray重量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_weight"
              label="Tray重量(g)"
              width="260"
              align="center"
            ><template slot-scope="scope">
              {{ scope.row.tray_weight / 1000 }}
            </template>
            </el-table-column>
            <!-- Tray容积 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="tray_volume"
              label="Tray容积"
              width="130"
              align="center"
            /> -->
            <!-- Overlay高度 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="overlay_height"
              label="Overlay高度(um)"
              width="130"
              align="center"
            /> -->
            <!-- Overlay重量 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="overlay_weight"
              label="Overlay重量(mg)"
              width="130"
              align="center"
            /> -->
            <!-- 重量偏差 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="weight_error"
              :label="$t('view.field.recipe.weight_error')"
              width="130"
              align="center"
            /> -->
            <!-- 束带方式(一字) -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_way1"
              label="束带方式(一字)"
              width="130"
              align="center"
            /> -->
            <!-- 束带方式(三字) -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_way3"
              label="束带方式(三字)"
              width="130"
              align="center"
            /> -->
            <!-- 喷墨模板 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="inkjet_tpl"
              :label="$t('view.field.recipe.inkjet_tpl')"
              width="130"
              align="center"
            /> -->
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import api from '@/api/pack/project/kinsus/tray-recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  created_by: '',
  creation_date: '',
  last_updated_by: '',
  last_update_date: '',
  recipe_id: '',
  model_type: '',
  model_version: '',
  pcs_length: '',
  pcs_width: '',
  pcs_thickness: '',
  pcs_weight: '',
  bd_index_incre: '',
  enable_flag: 'Y',
  tray_height: '',
  tray_weight: '',
  tray_material_code: ''
}
export default {
  name: 'CARTASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.formulaMainten'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG', 'QR_TYPE', 'QR_CASE', 'QR_DIRECT'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        model_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        model_version: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // pcs_length: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // pcs_width: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        // pcs_thickness: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        pcs_weight: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        tray_height: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        tray_weight: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        tray_volume: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        overlay_height: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        overlay_weight: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        weight_error: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        bd_way1: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        bd_way3: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        inkjet_tpl: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    handleInput(value) {
      const reg = /^\d+(\.\d{0,2})?$/ // 有效的数字格式
      if (!reg.test(this.form[value])) {
        // 输入不符合要求，清空输入框
        this.form[value] = ''
      }
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.tray_weight = crud.form.tray_weight ? (crud.form.tray_weight / 1000) : ''
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      delete crud.form.item_date
      crud.form.tray_weight = crud.form.tray_weight * 1000
    },
    BlurText(e) {
      const boolean = new RegExp('^[0-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger'))
        e.target.value = ''
      }
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              user_name: Cookies.get('userName'),
              recipe_id: data.recipe_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
