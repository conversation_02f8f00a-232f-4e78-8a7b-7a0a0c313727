<template>
  <div class="app-container">
    <el-row :gutter="12">
      <el-col :span="24">
        <el-card shadow="never" class="wrapCard">
          <el-form ref="query" :inline="true" size="small">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-8 col-12">
                <div class="formChild col-md-4 col-12">
                  <el-form-item label="产线：">
                    <el-select
                      v-model="query.prod_line_id"
                      filterable
                      clearable
                      size="small"
                    >
                      <el-option
                        v-for="item in prodLineData"
                        :key="item.prod_line_id"
                        :label="item.prod_line_code + ' ' + item.prod_line_des"
                        :value="item.prod_line_id"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div class="formChild col-md-4 col-12">
                  <el-form-item label="工位：">
                    <el-select v-model="query.station_code" filterable clearable>
                      <el-option
                        v-for="item in stationAllData"
                        :key="item.station_code"
                        :label="item.station_code + ' ' + item.station_des"
                        :value="item.station_code"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-4 col-12">
                <el-form-item style="margin: 0">
                  <el-button
                    class="filter-item"
                    size="small"
                    type="primary"
                    icon="el-icon-search"
                    @click="getFlowTaskData"
                  >搜索</el-button>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <div class="wrapRowss">
      <el-row :gutter="12" style="margin-top: 20px">
        <div class="wrapCol">
          <div class="wrapColOne">
            <el-col :span="4" style="width: 150px">
              <table cellspacing="0" style="width: 100%">
                <tr>
                  <td style="width: 80px">
                    <div class="flow_prod_line">
                      <svg-icon icon-class="tree" class="icon" />
                      <div class="prod_line_name">
                        {{ this.curProdLineDes }}
                      </div>
                    </div>
                  </td>
                  <td style="width: 100%">
                    <hr class="jtHr">
                  </td>
                  <td style="width: 80px">
                    <img class="jtImg" src="~@/assets/images/jt.png">
                  </td>
                </tr>
              </table>
            </el-col>
          </div>
          <div class="wrapColTwo">
            <el-col
              v-for="(item, index) in stationData"
              :key="item.station_id"
              class="col-12 col-sm-6 col-md-4 col-lg-3"
            >
              <table cellspacing="0" style="width: 100%">
                <tr>
                  <td class="jtTd">
                    <div class="flow_station">
                      <i class="el-icon-document-copy" />
                      <span class="station_name">{{
                        item.station_code + " " + item.station_des
                      }}</span>
                    </div>
                  </td>
                  <td v-show="stationData.length - index > 1" style="width: 100%">
                    <hr class="jtHr">
                  </td>
                  <td v-show="stationData.length - index > 1" style="width: 80px">
                    <img class="jtImg" src="~@/assets/images/jt.png">
                  </td>
                </tr>
                <tr>
                  <td colspan="3">
                    <el-timeline>
                      <!-- 超过10分钟变红提醒 -->
                      <el-timeline-item
                        v-for="flow in flowTaskData.filter(
                          (data) => data.station_code === item.station_code
                        )"
                        :key="flow.flow_main_id"
                        :timestamp="flow.flow_main_des"
                        :color="
                          new Date(flow.start_date).getTime() <
                            new Date().getTime() - 600000
                            ? '#f60606'
                            : ''
                        "
                        placement="top"
                      >
                        <div class="flow_station_main">
                          <div class="flow_info">时间：{{ flow.start_date }}</div>
                          <div class="flow_info">信息：{{ flow.task_info }}</div>
                          <div class="flow_info">
                            步骤：{{ flow.step_mod_des }}&nbsp;&nbsp;<el-button
                              type="text"
                              class="flow_button"
                              @click="opendFlowTask(flow)"
                            >查看</el-button>
                          </div>
                          <div class="flow_info">日志：{{ flow.log_msg }}</div>
                        </div>
                      </el-timeline-item>
                    </el-timeline>
                  </td>
                </tr>
              </table>
            </el-col>
          </div>
        </div>
        <el-drawer
          id="flow-chart-drawer"
          :with-header="false"
          :visible.sync="flowTaskDialogVisible"
          direction="rtl"
          size="100%"
        >
          <flowTask
            v-if="flowTaskDialogVisible"
            ref="flowTask"
            :flow_task="currentFlowTask"
            :cel_api="cellIp + ':' + webapiPort"
            :mqtt_url="cellIp + ':' + mqttPort"
            @closeDrawer="flowTaskDialogVisible = false"
          />
        </el-drawer>
      </el-row>
    </div>
  </div>
</template>
<script>
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { mapGetters } from 'vuex'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import flowTask from '@/views/core/flow/task/task'
import axios from 'axios'
export default {
  name: 'PROD_LINE_FLOW',
  components: { flowTask },
  data() {
    return {
      height: document.documentElement.clientHeight - 40,
      stepDes: '通知EAP任务已接收',
      query: {
        prod_line_id: '', // 产线ID
        station_code: '' // 工位code
      },
      curProdLineDes: '', // 产线des
      cellId: '', // 单元ID
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      prodLineData: [],
      stationAllData: [],
      stationData: [],
      flowTaskData: [],
      flowTaskDialogVisible: false,
      currentFlowTask: {}
    }
  },
  watch: {
    'query.prod_line_id': {
      handler() {
        this.curProdLineDes = this.prodLineData.find((item) => {
          return item.prod_line_id === this.query.prod_line_id
        }).prod_line_des
        this.getStationAllData()
      }
    },
    'query.station_code': {
      handler() {
        this.getStationData()
      }
    }
  },
  computed: {
    ...mapGetters(['permission_routers'])
  },
  mounted() {
    const that = this
    this.timer = setInterval(function() {
      that.getFlowTaskData()
    }, 5000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  beforeRouteLeave(to, from, next) {
    if (from.name === 'PROD_LINE_FLOW') {
      clearInterval(this.timer)
    }
    // 在这里处理你需要的逻辑
    next() // 确保调用 next() 来继续路由导航
  },
  created() {
    console.log(new Date())
    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
            this.query.prod_line_id = this.prodLineData[0].prod_line_id
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    getStationAllData() {
      this.query.station_id = ''
      this.stationAllData = []
      this.stationData = []
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: this.query.prod_line_id
      }
      selStation(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationAllData = defaultQuery.data
              this.stationData = defaultQuery.data
              this.getFlowTaskData()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getStationData() {
      this.query.station_id = ''
      this.stationData = []
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: this.query.prod_line_id,
        stationCodeDes: this.query.station_code
      }
      selStation(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
              this.getFlowTaskData()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getCellIp(station_id, item) {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.cellId,
        station_id: station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getTaskData(item)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getFlowTaskData() {
      this.flowTaskData = []
      if (this.stationData === null || this.stationData.length === 0) return
      for (let i = 0; i < this.stationData.length; i++) {
        this.getCellIp(this.stationData[i].station_id, this.stationData[i])
      }
    },
    getTaskData(item) {
      var method = '/cell/core/flow/CoreFlowTaskListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      const data = { station_id: item.station_id }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            this.flowTaskData = this.flowTaskData.concat(defaultQuery.data.data)
          }
        })
        .catch((ex) => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    opendFlowTask(item) {
      this.currentFlowTask = item
      this.flowTaskDialogVisible = true
    }
  }
}
</script>
<style lang="scss">
.flow_prod_line {
  height: 100px;
  width: 100px;
  background-color: #79a0f1;
  text-align: center;
  color: #ffffff;
  padding-top: 15px;
  border-radius: 10px;
}
.flow_prod_line .icon {
  font-size: 24px;
  height: 25px;
}
.flow_prod_line .prod_line_name {
  margin-top: 3px;
  height: 25px;
  line-height: 25px;
  font-size: 14px;
}
.flow_station {
  height: 40px;
  line-height: 40px;
  width: 150px;
  background-color: #79a0f1;
  color: #ffffff;
  border-radius: 10px;
  padding-left: 10px;
}
.flow_station .icon {
  font-size: 24px;
  height: 25px;
}
.flow_station .station_name {
  margin-top: 3px;
  height: 25px;
  line-height: 25px;
  font-size: 14px;
}
.flow_station_main {
  background-color: #e5e5e5;
  padding-top: 10px;
  margin-left: 5px;
  border-radius: 5px;
}
.flow_station_main .flow_info {
  line-height: 25px;
  color: #777777;
  font-size: 12px;
}
.flow_station_main .flow_button {
  padding: 0;
  font-weight: 600;
  color: #79a0f1;
}
.wrapRowss {
  background: #ffffff;
  padding: 10px 15px;
  border-radius: 4px;
  margin-top: 10px;
  .wrapCol {
    display: flex;
  }
  .wrapColTwo {
    display: flex;
    flex-wrap: wrap;
  }
}
.el-timeline {
  padding: 0;
}
.el-timeline-item__wrapper {
  padding-left: 16px;
}
.el-timeline-item__node {
  background-color: #c9daff;
  box-shadow: 0 1px 4px #4269ba;
}
.el-timeline-item__timestamp.is-top {
  margin-bottom: 15px;
  padding-top: 1px;
  padding-left: 5px;
}
.el-timeline-item__timestamp {
  font-size: 13px;
  color: #333333;
  line-height: 18px;
}
.el-timeline-item__tail {
  border-left: 1px solid #c9daff;
}
.flow_station_main {
  background-color: #f1f3ff;
  margin-left: 0;
  padding: 10px;
  border-color: rgba(0, 0, 0, 0.09);
  -webkit-box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transition: 0.2s;
}
.flow_station_main:hover {
  border-color: rgba(0, 0, 0, 0.09);
  box-shadow: 0 2px 8px rgb(191 200 220);
}
.jtTd {
  width: 700px;
  height: 80px;
}
.jtHr {
  border: 1px solid #f1f3ff;
  width: 100%;
}
.jtImg {
  width: 20px;
  height: 15px;
  margin-top: 3px;
  margin-left: -10px;
}
</style>
