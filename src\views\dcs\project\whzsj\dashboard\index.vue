<template>
  <el-container id="bigScreen" :style="'background-image:url(' +mainBackground +')'">
    <el-header>
      <el-row :gutter="20" style="margin: 0px; padding: 0px">
        <div class="line">
          <div class="line_1" />
          <div class="line_2" />
          <div class="line_3" />
          <div class="line_4" />
          <div class="line_5" />
          <div class="line_6" />
          <div class="line_7" />
          <div class="line_8" />
          <div class="line_9" />
          <div class="line_10" />
          <div class="line_11" />
        </div>
        <div class="info">
          <div class="left">
            <img
              :src="headerLogo"
              style="width: 100px; height: 90px ;"
            >
            <div class="title mrt-30">
              智能堆场可视化看板
            </div>
            <div class="trapezoid mrt-30" />
            <div class="trapezoid mrt-30" />
            <div class="btn-sty mrt-30">/// ///</div>
            <div v-for="(item,index) in errorData" :key="index" :class="{'active':activeIndex === index}" class="btn mrt-30">
              <span>{{ item.name }}</span>
            </div>
            <div class="btn-sty mrt-30" style="margin-left: 10px;">/// ///</div>
          </div>
          <div class="right mrt-30">
            <div class="time">{{ DateTime[1] }}</div>
            <div class="date">
              <div class="time1">{{ DateTime[2] }}</div>
              <div class="time2">{{ DateTime[0] }}</div>
            </div>
          </div>
        </div>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px;display: flex;align-items: center;">
        <el-col :span="18" class="box-info">
          <MainChart
            ref="chart"
            v-loading="loading"
            :nodes="nodes"
            :connections="connections"
            :width="'1425'"
            :height="'550'"
            :readonly="false"
            element-loading-text="拼命绘制流程图中"
          />
          
        </el-col>
        <el-col :span="6" class="box-error">
          <div class="info-title">
            <div class="error-msg">
              <span>报警信息</span>
              <div class="triangle" />
            </div>
            <div class="line-sty" />
          </div>
          <div class="step-info">
            <el-steps direction="vertical">
              <el-step v-for="(item,index) in stepData" :key="index" :title="item.time" :description="item.name">1111</el-step>
            </el-steps>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px;">
        <el-col :span="6">
          <div class="info-title">
            <div class="error-msg">
              <span>入库视觉</span>
              <div class="triangle" />
            </div>
            <div class="line-sty" />
          </div>
          <div class="table-wrapper1">
            <thead>
              <tr>
                <th>钢板编号</th>
                <th>角度偏移值</th>
                <th>中心偏移值</th>
                <th>外观检测</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>118</td>
                <td>110</td>
                <td>78</td>
                <td>合格</td>
              </tr>
            </tbody>
          </div>
          <div class="img-info">
            <img src="~@/assets/images/whwms/img.png" alt="">
            <img src="~@/assets/images/whwms/img2.png" alt="">
          </div>
        </el-col>
        <el-col :span="18">
          <div class="plan-btn">
            <span v-for="(item,index) in planButton" :key="index" :class="{'activeTitle':activeTitleIndex === index}">{{ item.name }}</span>
          </div>
          <div class="table-wrapper">
            <table>
              <thead>
                <tr>
                  <th>到货单号</th>
                  <th>项目号</th>
                  <th>分段号</th>
                  <th>批次号</th>
                  <th>唯一码</th>
                  <th>钢印号</th>
                  <th>钢材编码</th>
                  <th>钢材名称</th>
                  <th>船级社</th>
                  <th>长</th>
                  <th>宽</th>
                  <th>高</th>
                  <th>规格型号</th>
                  <th>重量</th>
                  <th>数量</th>
                  <th>库区编码</th>
                  <th>库区名称</th>
                  <th>库位编码</th>
                  <th>库位名称</th>
                  <th>任务状态</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>DH2023001</td>
                  <td>XM001</td>
                  <td>FG001</td>
                  <td>BC0001</td>
                  <td>YMD001</td>
                  <td>GY2023-01</td>
                  <td>SC123</td>
                  <td>钢材A</td>
                  <td>DNV GL</td>
                  <td>10m</td>
                  <td>2m</td>
                  <td>1m</td>
                  <td>规格A</td>
                  <td>500kg</td>
                  <td>5</td>
                  <td>KQ001</td>
                  <td>库区A</td>
                  <td>KW001</td>
                  <td>库位A</td>
                  <td>已完成</td>
                </tr>
                <tr>
                  <td>DH2023002</td>
                  <td>XM002</td>
                  <td>FG002</td>
                  <td>BC0002</td>
                  <td>YMD002</td>
                  <td>GY2023-02</td>
                  <td>SC124</td>
                  <td>钢材B</td>
                  <td>LR</td>
                  <td>8m</td>
                  <td>1.5m</td>
                  <td>0.5m</td>
                  <td>规格B</td>
                  <td>300kg</td>
                  <td>3</td>
                  <td>KQ002</td>
                  <td>库区B</td>
                  <td>KW002</td>
                  <td>库位B</td>
                  <td>进行中</td>
                </tr>
                <tr>
                  <td>DH2023003</td>
                  <td>XM003</td>
                  <td>FG003</td>
                  <td>BC0003</td>
                  <td>YMD003</td>
                  <td>GY2023-03</td>
                  <td>SC125</td>
                  <td>钢材C</td>
                  <td>ABS</td>
                  <td>12m</td>
                  <td>2.5m</td>
                  <td>1.2m</td>
                  <td>规格C</td>
                  <td>700kg</td>
                  <td>7</td>
                  <td>KQ003</td>
                  <td>库区C</td>
                  <td>KW003</td>
                  <td>库位C</td>
                  <td>已取消</td>
                </tr>
                <tr>
                  <td>DH2023004</td>
                  <td>XM004</td>
                  <td>FG004</td>
                  <td>BC0004</td>
                  <td>YMD004</td>
                  <td>GY2023-04</td>
                  <td>SC126</td>
                  <td>钢材D</td>
                  <td>BV</td>
                  <td>9m</td>
                  <td>2m</td>
                  <td>1m</td>
                  <td>规格D</td>
                  <td>450kg</td>
                  <td>4</td>
                  <td>KQ004</td>
                  <td>库区D</td>
                  <td>KW004</td>
                  <td>库位D</td>
                  <td>已完成</td>
                </tr>
                <tr>
                  <td>DH2023005</td>
                  <td>XM005</td>
                  <td>FG005</td>
                  <td>BC0005</td>
                  <td>YMD005</td>
                  <td>GY2023-05</td>
                  <td>SC127</td>
                  <td>钢材E</td>
                  <td>CCS</td>
                  <td>11m</td>
                  <td>2.3m</td>
                  <td>1.1m</td>
                  <td>规格E</td>
                  <td>600kg</td>
                  <td>6</td>
                  <td>KQ005</td>
                  <td>库区E</td>
                  <td>KW005</td>
                  <td>库位E</td>
                  <td>进行中</td>
                </tr>
                <tr>
                  <td>DH2023005</td>
                  <td>XM005</td>
                  <td>FG005</td>
                  <td>BC0005</td>
                  <td>YMD005</td>
                  <td>GY2023-05</td>
                  <td>SC127</td>
                  <td>钢材E</td>
                  <td>CCS</td>
                  <td>11m</td>
                  <td>2.3m</td>
                  <td>1.1m</td>
                  <td>规格E</td>
                  <td>600kg</td>
                  <td>6</td>
                  <td>KQ005</td>
                  <td>库区E</td>
                  <td>KW005</td>
                  <td>库位E</td>
                  <td>进行中</td>
                </tr>
                <tr>
                  <td>DH2023005</td>
                  <td>XM005</td>
                  <td>FG005</td>
                  <td>BC0005</td>
                  <td>YMD005</td>
                  <td>GY2023-05</td>
                  <td>SC127</td>
                  <td>钢材E</td>
                  <td>CCS</td>
                  <td>11m</td>
                  <td>2.3m</td>
                  <td>1.1m</td>
                  <td>规格E</td>
                  <td>600kg</td>
                  <td>6</td>
                  <td>KQ005</td>
                  <td>库区E</td>
                  <td>KW005</td>
                  <td>库位E</td>
                  <td>进行中</td>
                </tr>
                <tr>
                  <td>DH2023005</td>
                  <td>XM005</td>
                  <td>FG005</td>
                  <td>BC0005</td>
                  <td>YMD005</td>
                  <td>GY2023-05</td>
                  <td>SC127</td>
                  <td>钢材E</td>
                  <td>CCS</td>
                  <td>11m</td>
                  <td>2.3m</td>
                  <td>1.1m</td>
                  <td>规格E</td>
                  <td>600kg</td>
                  <td>6</td>
                  <td>KQ005</td>
                  <td>库区E</td>
                  <td>KW005</td>
                  <td>库位E</td>
                  <td>进行中</td>
                </tr>
                <tr>
                  <td>DH2023005</td>
                  <td>XM005</td>
                  <td>FG005</td>
                  <td>BC0005</td>
                  <td>YMD005</td>
                  <td>GY2023-05</td>
                  <td>SC127</td>
                  <td>钢材E</td>
                  <td>CCS</td>
                  <td>11m</td>
                  <td>2.3m</td>
                  <td>1.1m</td>
                  <td>规格E</td>
                  <td>600kg</td>
                  <td>6</td>
                  <td>KQ005</td>
                  <td>库区E</td>
                  <td>KW005</td>
                  <td>库位E</td>
                  <td>进行中</td>
                </tr>
                <tr>
                  <td>DH2023005</td>
                  <td>XM005</td>
                  <td>FG005</td>
                  <td>BC0005</td>
                  <td>YMD005</td>
                  <td>GY2023-05</td>
                  <td>SC127</td>
                  <td>钢材E</td>
                  <td>CCS</td>
                  <td>11m</td>
                  <td>2.3m</td>
                  <td>1.1m</td>
                  <td>规格E</td>
                  <td>600kg</td>
                  <td>6</td>
                  <td>KQ005</td>
                  <td>库区E</td>
                  <td>KW005</td>
                  <td>库位E</td>
                  <td>进行中</td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-col>
      </el-row>
    </el-main>

  </el-container>
</template>
<script>

import MainChart from './wmsDashboard/index'
import { getFormatDate } from '@/utils/index.js'
import mainBackground from '@/assets/images/whwms/bg.jpg'
import headerLogo from '@/assets/images/whwms/logo.png'
import crownBlock from '@/api/dcs/project/whzsj/crownBlock.json'
export default {
  name: 'WH_INDEX_DASHBOARD',
  components: { MainChart },
  data() {
    return {
      headerLogo,
      mainBackground,
      height: document.documentElement.clientHeight - 755,
      DateTime: [],
      errorData: [{ id: 1, name: '异常报警' }, { id: 2, name: '出入库记录' }, { id: 3, name: '统计报表' }, { id: 4, name: '系统设置' }],
      activeIndex: 0,
      activeTitleIndex: 0,
      stepData: [
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' },
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' },
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' },
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' },
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' },
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' },
        { time: '2025-04-10 17:48:50', name: 'xxx发生报警，请前往处理' }
      ],
      planButton: [
        { id: 1, name: '入库计划' },
        { id: 2, name: '粗排计划' },
        { id: 3, name: '精排计划' },
        { id: 4, name: '预处理计划' },
        { id: 5, name: '切割计划' }
      ],
      nodes: crownBlock,
      loading: false,
      connections: []
    }
  },
  mounted() {
    this.DateTime = getFormatDate().split(' ')
    this.timer = setInterval(() => {
      this.DateTime = getFormatDate().split(' ')
    }, 1000)
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 755
    }
  }
}
</script>
<style scoped  lang="scss">
@import '~@/assets/styles/whzsj/index.scss';
</style>
<style scoped lang="less">
::v-deep .step-info {
  .el-step__icon{
      width: 15px !important;
      height: 15px !important;
      background-color: rgb(255, 0, 0);
      .el-step__icon-inner{
        display: none !important;
      }
      animation: greenPulse 2s infinite;
  }
  .el-step__icon.is-text{
    border:none!important;
  }
  .el-step__line{
    left: 7px !important;
    top: 2px!important;
  }
  @keyframes greenPulse {
      0% {
          box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
      }

      50% {
          box-shadow: 0 0 0 15px rgba(0, 255, 0, 0);
      }

      100% {
          box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
      }
  }
}
</style>
