import request from '@/utils/request'

// 查询
export function selAll(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreTimerMainAllSubAttrSel',
    method: 'post',
    data
  })
}
// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreTimerMainSubAttrSel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreTimerMainSubAttrIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreTimerMainSubAttrUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreTimerMainSubAttrDel',
    method: 'post',
    data
  })
}
// 保存
export function saveAttr(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreTimerMainSubAttrSave',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, selAll, saveAttr }

