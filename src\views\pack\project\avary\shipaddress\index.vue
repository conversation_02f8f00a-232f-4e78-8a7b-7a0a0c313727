<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!-- 名称 -->
            <div class="formChild col-md-4 col-12">
              <el-form-item label="名称：">
                <el-input v-model="query.shipaddress_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <div class="distStyle">
            <div class="left" />
            <span>标签识别设定</span>
            <div class="right" />
            <span>比对MES字段设定</span>
          </div>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item label="名称" prop="shipaddress_name">
            <!-- 出货地名称 -->
            <el-input v-model="form.shipaddress_name" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="10" class="leftTable">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              :height="height"
              :highlight-current-row="true"
              @header-dragend="crud.tableHeaderDragend()"
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <!-- 名称 -->
              <el-table-column
                prop="shipaddress_name"
                :label="$t('view.table.name')"
                align="center"
              />
              <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
                <!-- 操作 -->
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <!-- 新增子配方 -->
                      <el-button slot="reference" type="text" size="small" @click="$refs.menuItem && $refs.menuItem.crud.toAdd()">{{ '新增出货地明细' }}</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20" class="rightTable">
          <el-col :span="24">
            <menuItem ref="menuItem" class="tableFirst box-card1" :shipaddress_id="shipaddressId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import pdReicpe from '@/api/pack/packPdReicpe'
import menuItem from './menuItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  shipaddress_id: '',
  shipaddress_name: ''
}
export default {
  name: 'CARTASK',
  components: { crudOperation, rrOperation, udOperation, pagination, menuItem },
  cruds() {
    return CRUD({
      title: '配方设定',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'shipaddress_id',
      // 排序
      sort: ['shipaddress_id desc'],
      // CRUD Method
      crudMethod: { ...pdReicpe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      // recipeData:{
      //   shipaddress_name:'',
      // }
      // recipeData: {
      //   shipaddress_name: '',
      //   shipaddress_value: []
      // },
      rules: {
        shipaddress_name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      shipaddressId: 0
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    handleRowClick(row, column, event) {
      this.shipaddressId = row.shipaddress_id
    }
  }
}
</script>
  <style scoped lang="less">
  ::v-deep .el-dialog{
    margin-top: 3vh !important;
  }
  .subInput{
      width: 90px !important;
      margin:0 10px;
  }
  .el-form-item-type{
      width:100%;
      span{
          font-size: 12px;
          color: #5f5f5f;
      }
  }
  .wrapRowItem {
    display: flex;
    justify-content: space-between;
    .el-table {
      border-radius: 10px;
      border-color: rgba(0, 0, 0, 0.09);
      box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
    }
    .leftTable {
      width: 30%;
    }
    .rightTable {
      width: 70%;
    }
  }
  ::v-deep .crud-opts-left{
    display: flex;
      .distStyle{
        display: flex;
        align-items: center;
        .left{
          width: 40px;
          height: 20px;
          background-color: #FFC000;
          margin: 0 10px
        }
        .right{
          width: 40px;
          height: 20px;
          background-color: #00B0F0;
          margin: 0 10px
        }
      }
    }
  </style>
