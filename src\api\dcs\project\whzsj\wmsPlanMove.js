import request from '@/utils/request'
// 查询移库计划
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveSelect',
    method: 'post',
    data
  })
}
// 删除移库计划
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveDelete',
    method: 'post',
    data
  })
}

// 计划状态更新
export function planStatusUpd(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveStatusUpd',
    method: 'post',
    data
  })
}

// 粗排、精排、预处理
export function planManual(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveManual',
    method: 'post',
    data
  })
}

// 库存同步
export function planInventorySend(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveInventorySend',
    method: 'post',
    data
  })
}

// 移库报工
export function planReportSend(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanMoveReportSend',
    method: 'post',
    data
  })
}
export default { sel, del, planStatusUpd, planManual, planInventorySend, planReportSend }
