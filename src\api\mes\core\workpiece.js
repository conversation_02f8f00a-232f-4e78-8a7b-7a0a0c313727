import request from '@/utils/request'

const context = 'aisEsbWeb/mes/core/workpieces'

export function sel(params) {
  return request({
    url: context,
    method: 'get',
    params
  })
}

export function edit(data, params) {
  return request({
    url: context + '/' + data.id,
    method: 'patch',
    data,
    params
  })
}

export function add(data, params) {
  return request({
    url: context,
    method: 'post',
    data,
    params
  })
}

export function del(data, params) {
  return request({
    url: context + '/' + (data.id || data.ids),
    method: 'delete',
    data,
    params
  })
}

export function exportExcel(data) {
  return request({
    url: context + '/excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export default { add, del, edit, sel, exportExcel }
