import request from '@/utils/request'

// 查询设备维修
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipMaintainSel',
    method: 'post',
    data
  })
}
// 新增设备维修
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipMaintainIns',
    method: 'post',
    data
  })
}
// 修改设备维修
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipMaintainUpd',
    method: 'post',
    data
  })
}

// 删除设备维修
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/quanchai/MesQcEquipMaintainDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

