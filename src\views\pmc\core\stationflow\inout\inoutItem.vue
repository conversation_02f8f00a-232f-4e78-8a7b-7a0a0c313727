<template>
  <!--拉入明细-->
  <el-card shadow="never">
    <!--预约拉入-->
    <el-drawer
      append-to-body
      :wrapper-closable="false"
      :title="dialogTitlePreSetIn"
      :visible.sync="dialogVisbleSyncPreSetIn"
      :before-close="toBeforeClosePreSetIn"
      size="650px"
    >
      <el-form
        ref="formPreSetIn"
        class="el-form-wrap"
        :model="formPreSetIn"
        size="small"
        label-width="100px"
        :inline="true"
      >
        <el-form-item :label="$t('lang_pack.stationmo.makeOrder')" prop="make_order">
          <!-- 订单号 -->
          <el-input v-model="formPreSetIn.make_order" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.stationmo.dms')" prop="dms">
          <!-- DMS号 -->
          <el-input v-model="formPreSetIn.dms" />
        </el-form-item>
        <!-- <el-form-item :label="$t('lang_pack.stationmo.itemProject')" prop="item_project">
          行项目
          <el-input v-model="formPreSetIn.item_project" />
        </el-form-item> -->
        <el-form-item :label="$t('lang_pack.pmcquery.vin')" prop="vin">
          VIN
          <el-input v-model="formPreSetIn.vin" />
        </el-form-item>
        <el-form-item
          :label="$t('lang_pack.stationmo.targetMakeOrder')"
          prop="target_make_order"
        >
          <!-- 目标订单： -->
          <!--表：d_pmc_me_station_mo-->
          <el-select v-model="formPreSetIn.target_make_order" filterable>
            <el-option
              v-for="item in makeOrderData"
              :key="item.make_order"
              :label="item.make_order"
              :value="item.make_order"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.stationmo.setMarks')" prop="set_marks">
          <!-- 拉入拉出备注 -->
          <el-input v-model="formPreSetIn.set_marks" />
        </el-form-item>
        <el-form-item label="拉入方式">
          <!-- 拉出方式 -->
          <el-radio-group v-model="formPreSetIn.type">
            <el-radio :label="0">区域拉入</el-radio>
            <el-radio :label="1">整体拉入</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="toPreSetInCancel">{{
          $t("lang_pack.commonPage.cancel")
        }}</el-button>
        <!-- 取消 -->
        <el-button
          type="primary"
          size="small"
          icon="el-icon-check"
          @click="toPreSetInSubmit('formPreSetIn')"
          >{{ $t("lang_pack.commonPage.confirm") }}</el-button
        >
        <!-- 确认 -->
      </div>
    </el-drawer>

    <!--直接拉入-->
    <el-drawer
      append-to-body
      :wrapper-closable="false"
      :title="dialogTitleSetIn"
      :visible.sync="dialogVisbleSyncSetIn"
      :before-close="toBeforeCloseSetIn"
      size="650px"
    >
      <el-form
        ref="formSetIn"
        class="el-form-wrap"
        :model="formSetIn"
        :rules="rulesSetIn"
        size="small"
        label-width="100px"
        :inline="true"
      >
        <el-form-item :label="$t('lang_pack.stationmo.makeOrder')" prop="make_order">
          <!-- 订单号 -->
          <el-input v-model="formSetIn.make_order" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.stationmo.dms')" prop="dms">
          <!-- DMS号 -->
          <el-input v-model="formSetIn.dms" />
        </el-form-item>
        <!-- <el-form-item :label="$t('lang_pack.stationmo.itemProject')" prop="item_project"> -->
          <!-- 行项目 -->
          <!-- <el-input v-model="formSetIn.item_project" />
        </el-form-item> -->
        <el-form-item :label="$t('lang_pack.pmcquery.vin')" prop="vin">
          <!-- VIN -->
          <el-input v-model="formSetIn.vin" />
        </el-form-item>
        <el-form-item
          :label="$t('lang_pack.stationmo.targetMakeOrder')"
          prop="target_make_order"
        >
          <!-- 目标订单： -->
          <!--表：d_pmc_me_station_mo-->
          <el-select v-model="formSetIn.target_make_order" filterable>
            <el-option
              v-for="item in makeOrderData"
              :key="item.make_order"
              :label="'订单号:' + item.make_order + ',序号:' + item.mo_work_order"
              :value="item.make_order"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.stationmo.setMarks')" prop="set_marks">
          <!-- 拉入拉出备注 -->
          <el-input v-model="formSetIn.set_marks" />
        </el-form-item>
        <el-form-item label="拉入方式">
          <!-- 拉出方式 -->
          <el-radio-group v-model="formSetIn.type">
            <el-radio :label="0">区域拉入</el-radio>
            <el-radio :label="1">整体拉入</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="toSetInCancel">{{
          $t("lang_pack.commonPage.cancel")
        }}</el-button>
        <!-- 取消 -->
        <el-button
          type="primary"
          size="small"
          icon="el-icon-check"
          @click="toSetInSubmit('formSetIn')"
          >{{ $t("lang_pack.commonPage.confirm") }}</el-button
        >
        <!-- 确认 -->
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table
          border
          @header-dragend="crud.tableHeaderDragend()"
          ref="table"
          v-loading="crud.loading"
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          class="box-card1"
          min-height="523px"
          highlight-current-row
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="station_mo_id" label="id" />
          <el-table-column
            v-if="1 == 0"
            width="10"
            prop="station_code"
            label="station_code"
          />
          <el-table-column
            :show-overflow-tooltip="true"
            prop="make_order"
            :label="$t('lang_pack.stationmo.makeOrder')"
          />
          <!-- 订单号 -->
          <el-table-column
            :show-overflow-tooltip="true"
            prop="dms"
            :label="$t('lang_pack.stationmo.dms')"
          />
          <!-- DMS号 -->
          <el-table-column
                :show-overflow-tooltip="true"
                prop="vin"
                :label="$t('lang_pack.flowonline.vin')"
              />
          <!-- vin -->

          <el-table-column
            :label="$t('lang_pack.commonPage.operate')"
            align="center"
            fixed="right"
          >
            <!-- 操作 -->
            <template slot-scope="scope">
              <!-- <el-button v-if="scope.row.set_sign === 'SET_OUT'" slot="reference" type="text" size="small" @click="toTableButPreSetIn(scope.row)">{{ $t('lang_pack.stationmo.preSetInBut') }}</el-button> -->
              <!-- 预约拉入 -->
              <!-- <el-button v-if="scope.row.set_sign === 'PRE_SET_IN'" slot="reference" type="text" size="small" @click="toTableButCancelPreSetIn(scope.row)">{{ $t('lang_pack.stationmo.cancelPreSetInBut') }}</el-button> -->
              <!-- 取消预约拉入 -->
              <el-button
                v-if="scope.row.set_sign === 'SET_OUT'"
                slot="reference"
                type="text"
                size="small"
                @click="toTableButSetIn(scope.row)"
                >{{ $t("lang_pack.stationmo.setInBut") }}</el-button
              >
              <!-- 直接拉入 -->
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import crudStationMoIn from "@/api/pmc/stationflow/stationmoin";
import { stationmolov } from "@/api/pmc/stationflow/stationmo";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import udOperation from "@crud/UD.operation";
import pagination from "@crud/Pagination";
const defaultForm = {};
export default {
  name: "StationMoIn",
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    station_code: {
      type: [String, String],
      default: "",
    },
  },
  cruds() {
    return CRUD({
      title: "拉入",
      // 登录用户
      userName: Cookies.get("userName"),
      // 菜单组ID
      query: { station_code: "" },
      // 唯一字段
      idField: "station_mo_id",
      // 排序
      sort: ["mo_work_order asc"],
      // CRUD Method
      crudMethod: { ...crudStationMoIn },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false,
      },
    });
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {},

      // 订单数据
      makeOrderData: [],

      // 弹窗(预约拉出)
      dialogTitlePreSetIn: "",
      dialogVisbleSyncPreSetIn: false,
      formPreSetIn: {
        station_mo_id: "",
        station_code: "",
        make_order: "",
        target_make_order: "",
        dms: "",
        vin:"",
        item_project: "",
        set_date: "",
        set_marks: "",
        type: 0,
      },

      // 弹窗(直接拉入)
      dialogTitleSetIn: "",
      dialogVisbleSyncSetIn: false,
      formSetIn: {
        station_mo_id: "",
        station_code: "",
        make_order: "",
        target_make_order: "",
        dms: "",
        vin:"",
        item_project: "",
        set_marks: "",
        type: 0,
      },
      rulesSetIn: {},
    };
  },
  watch: {
    station_code: {
      immediate: true,
      deep: true,
      handler() {
        this.query.station_code = this.station_code;
        this.crud.toQuery();
      },
    },
  },

  mounted: function () {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200;
    };
  },
  created: function () {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.station_code = this.station_code;
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.station_code = this.station_code;
      return true;
    },

    // 订单LOV
    queryMakeOrder(station_code) {
      const query = {
        user_name: Cookies.get("userName"),
        station_code: station_code,
      };
      stationmolov(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.makeOrderData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },

    // 预约拉入:
    toBeforeClosePreSetIn(done) {
      // 新增弹出框(关闭前的回调)
      this.dialogVisbleSyncPreSetIn = false; // 弹出框隐藏
    },
    toTableButPreSetIn(data) {
      // Table编辑(单笔)
      this.formPreSetIn.station_mo_id = data.station_mo_id;
      this.formPreSetIn.station_code = data.station_code;
      this.formPreSetIn.make_order = data.make_order;
      this.formPreSetIn.target_make_order = "";
      this.formPreSetIn.dms = data.dms;
      this.formPreSetIn.vin = data.vin;
      this.formPreSetIn.item_project = data.item_project;
      this.formPreSetIn.set_date = "";
      this.formPreSetIn.set_marks = "";
      this.formPreSetIn.type = 0;

      // 订单LOV
      this.queryMakeOrder(data.station_code);

      this.dialogTitlePreSetIn = "预约拉入";
      this.dialogVisbleSyncPreSetIn = true; // 预约拉入弹出框
    },
    toTableButCancelPreSetIn(data) {
      this.$confirm(`该订单${data.make_order}将取消预约拉入，是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const save = {
            user_name: Cookies.get("userName"),
            station_mo_id: data.station_mo_id,
            set_sign: "SET_OUT",
          };
          crudStationMoIn
            .cancelin(save)
            .then((res) => {
              const defaultDel = JSON.parse(JSON.stringify(res));
              if (defaultDel.code === 0) {
                this.$message({
                  message: "取消预约拉入成功",
                  type: "success",
                });

                // 查询
                this.crud.toQuery();
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: "info",
                });
              }
            })
            .catch(() => {
              this.$message({
                message: "取消预约拉入异常",
                type: "error",
              });
            });
        })
        .catch(() => {});
    },
    toPreSetInCancel() {
      // 取消
      this.dialogVisbleSyncPreSetIn = false; // 预约拉入弹出框隐藏
    },
    toPreSetInSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get("userName"),
            station_mo_id: this.formPreSetIn.station_mo_id,
            station_code: this.formPreSetIn.station_code,
            make_order: this.formPreSetIn.make_order,
            target_make_order: this.formPreSetIn.target_make_order,
            set_sign: "PRE_SET_IN",
            set_date: this.formPreSetIn.set_date,
            set_marks: this.formPreSetIn.set_marks,
            type: this.formPreSetIn.type,
          };
          crudStationMoIn
            .setin(save)
            .then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res));
              if (defaultQuery.code === 0) {
                this.dialogVisbleSyncPreSetIn = false; // 预约拉入弹出框隐藏

                this.$message({
                  message: "预约拉入成功",
                  type: "success",
                });
                // 查询
                this.crud.toQuery();
                this.$emit("toOutQuery");
              } else {
                this.$message({
                  message: defaultQuery.msg,
                  type: "info",
                });
              }
            })
            .catch(() => {
              this.$message({
                message: "预约拉入异常",
                type: "error",
              });
            });
        }
      });
    },

    // 直接拉入:
    toBeforeCloseSetIn(done) {
      // 新增弹出框(关闭前的回调)
      this.dialogVisbleSyncPreSetIn = false; // 弹出框隐藏
    },
    toTableButSetIn(data) {
      // Table编辑(单笔)
      this.formSetIn.station_mo_id = data.station_mo_id;
      this.formSetIn.station_code = data.station_code;
      this.formSetIn.make_order = data.make_order;
      this.formSetIn.target_make_order = "";
      this.formSetIn.dms = data.dms;
      this.formSetIn.vin = data.vin;
      this.formSetIn.item_project = data.item_project;
      this.formSetIn.set_marks = "";
      this.formSetIn.type = 0;

      // 订单LOV
      this.queryMakeOrder(data.station_code);

      this.dialogTitleSetIn = "直接拉入";
      this.dialogVisbleSyncSetIn = true; // 直接拉入弹出框
    },
    toSetInCancel() {
      // 取消
      this.dialogVisbleSyncSetIn = false; // 直接拉入弹出框隐藏
    },
    toSetInSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get("userName"),
            station_mo_id: this.formSetIn.station_mo_id,
            station_code: this.formSetIn.station_code,
            make_order: this.formSetIn.make_order,
            target_make_order: this.formSetIn.target_make_order,
            set_sign: "SET_IN",
            set_date: "",
            set_marks: this.formSetIn.set_marks,
            type: this.formSetIn.type,
          };
          crudStationMoIn
            .setin(save)
            .then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res));
              if (defaultQuery.code === 0) {
                this.dialogVisbleSyncSetIn = false; // 直接拉出弹出框隐藏

                this.$message({
                  message: "直接拉入成功",
                  type: "success",
                });
                // 查询
                this.crud.toQuery();
                this.$emit("toOutQuery");
              } else {
                this.$message({
                  message: defaultQuery.msg,
                  type: "info",
                });
              }
            })
            .catch(() => {
              this.$message({
                message: "直接拉入异常",
                type: "error",
              });
            });
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.el-table {
  border-radius: 10px;
}
.el-card {
  border: 0 !important;
  overflow: inherit;
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
