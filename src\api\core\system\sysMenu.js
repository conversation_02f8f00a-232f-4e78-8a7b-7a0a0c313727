import request from '@/utils/request'

//查询菜单组
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuGroupSel',
    method: 'post',
    data
  })
}
//新增菜单组
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuGroupIns',
    method: 'post',
    data
  })
}
//修改菜单组
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuGroupUpd',
    method: 'post',
    data
  })
}
//删除菜单组
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuGroupDel',
    method: 'post',
    data
  })
}


//登录 获取菜单(权限)
export function sysBuildMenus(data) {
  return request({
    url: 'aisEsbWeb/core/system/sysMenus/sysBuild',
    method: 'post',
    data
  })
}

//查询当前用户菜单权限
export function queryCurrentUserMenuTree(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuCurrentUserMenuTree',
    method: 'post',
    data
  })
}
//查询当前用户的快捷菜单
export function queryShortcutMenuSelect(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuShortcutMenuSelect',
    method: 'post',
    data
  })
}
//设置当前用户的快捷菜单
export function insShortcutMenuInsert(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuShortcutMenuInsert',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del,
  sysBuildMenus,
  queryCurrentUserMenuTree,queryShortcutMenuSelect,insShortcutMenuInsert }
