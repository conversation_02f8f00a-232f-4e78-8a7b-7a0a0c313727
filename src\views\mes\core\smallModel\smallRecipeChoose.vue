<template>
  <div>
    <el-form ref="query" :inline="true" size="small">
      <el-form-item label="配方描述/版本号：">
        <el-input v-model="query.recipe_name_version" style="width:420px">
          <el-button slot="append" icon="el-icon-search" @click="crud.toQuery" />
        </el-input>
      </el-form-item>
    </el-form>
    <el-row :gutter="20" style="margin-top:10px;">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  :show-overflow-tooltip="true" prop="recipe_name" min-width="100" label="配方描述" />
          <el-table-column  :show-overflow-tooltip="true" prop="recipe_version" min-width="100" label="版本号" />
          <el-table-column  label="操作" width="115" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="handleChoose(scope.row)">确定</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'
const defaultForm = {
  recipe_id: '',
  recipe_type: '',
  recipe_name: '',
  recipe_version: '',
  liable_person: '',
  update_remark: '',
  enable_flag: 'Y'
}
export default {
  name: 'SmallModelChoose',
  components: { pagination },
  cruds() {
    return CRUD({
      title: '配方信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id asc'],
      // CRUD Method
      crudMethod: { ...crudRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    recipe_type: {
      type: String,
      default: '无'
    }
  },
  // 数据模型
  data() {
    return {}
  },
  watch: {
    recipe_type: {
      immediate: true,
      deep: true,
      handler() {
        this.query.recipe_type = this.recipe_type
      }
    }
  },

  mounted: function() {},
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.recipe_type = this.recipe_type
    },
    handleChoose(row) {
      this.$emit('chooseRecipe', row.recipe_id, row.recipe_name + ' ' + row.recipe_version)
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
