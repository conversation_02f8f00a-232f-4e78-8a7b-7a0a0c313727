<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceStation.stationCodeDescription')">
                <!-- 工位编码/描述： -->
                <el-input v-model="query.stationCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceStation.productionLine')">
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_id">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="175px" :inline="true">
              <!--表：sys_fmod_prod_line-->
              <el-form-item :label="$t('lang_pack.maintenanceStation.productionLinet')" prop="prod_line_id">
                <!-- 产线 -->
                <el-select v-model="form.prod_line_id" size="small" placeholder="请选择产线" @change="changeProdLine">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.stationCode')" prop="station_code">
                <!-- 工位编码 -->
                <el-input v-model="form.station_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.stationDescription')" prop="station_des">
                <!-- 工位描述 -->
                <el-input v-model="form.station_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.shortNumber')" prop="station_short_code">
                <!-- 短序号 -->
                <el-input v-model="form.station_short_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.productionLineSegmentCode')" prop="line_section_code">
                <!-- 产线分段码 -->
                <el-input v-model="form.line_section_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.divisionNumber')" prop="sub_station_code">
                <!-- 分工位号 -->
                <el-input v-model="form.sub_station_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.stationRelatedAttributes')" prop="station_attr">
                <!-- 工位相关属性 -->
                <el-input v-model="form.station_attr" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenanceStation.stationCombinationCode')" prop="station_group_code">
                <!-- 工位组合码 -->
                <el-input v-model="form.station_group_code" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenanceStation.ipAddress')" prop="pc_host">
                <!-- IP地址 -->
                <el-input v-model="form.pc_host" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.reportWorkingProcedureNumber')" prop="bg_proceduce_code">
                <!-- 报工工序号 -->
                <el-input v-model="form.bg_proceduce_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.workDescription')" prop="bg_proceduce_des">
                <!-- 报工描述 -->
                <el-input v-model="form.bg_proceduce_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.downTime')" prop="alarm_set_times">
                <!-- 故障时间 -->
                <el-input v-model="form.alarm_set_times" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.serviceTime')" prop="fix_set_times">
                <!-- 保养时间 -->
                <el-input v-model="form.fix_set_times" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.runTime')" prop="plan_run_times">
                <!-- 计划运行时间 -->
                <el-input v-model="form.plan_run_times" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.taktImage')" prop="beat_times">
                <!-- 理想节拍 -->
                <el-input v-model="form.beat_times" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.scrollingMessage')" prop="station_scroll_info">
                <!-- 滚动信息 -->
                <el-input v-model="form.station_scroll_info" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenanceStation.methodLis')" prop="data_collect_type">
                <!-- 数据采集方式 -->
                <el-select v-model="form.data_collect_type" clearable filterable>
                  <el-option v-for="item in dict.DATA_COLLECT_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.productType')" prop="product_type">
                <!-- 产品类型 -->
                <el-input v-model="form.product_type" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.cellId')" prop="cell_id">
                <!-- 单元ID -->
                <el-select v-model="form.cell_id" filterable clearable>
                  <el-option v-for="item in cellData" :key="item.cell_id" :label="item.cell_container_des" :value="item.cell_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.scadaExample')" prop="client_id_list">
                <!-- SCADA实例 -->
                <el-select v-model="form.client_id_list" filterable clearable multiple>
                  <el-option v-for="item in clientData" :key="item.client_id" :label="item.client_des" :value="item.client_id + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.exampleFlowChart')" prop="flow_id_list">
                <!-- 流程图实例 -->
                <el-select v-model="form.flow_id_list" filterable clearable multiple>
                  <el-option v-for="item in flowMainData" :key="item.flow_main_id" :label="item.flow_main_des" :value="item.flow_main_id + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.automatedLogicalInstance')" prop="logic_id_list">
                <!-- 自动化逻辑实例 -->
                <el-select v-model="form.logic_id_list" filterable clearable multiple>
                  <el-option v-for="item in logicData" :key="item.logic_func_id" :label="item.func_des" :value="item.logic_func_id + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.interfaceConfigurationEnvironment')" prop="interf_paras">
                <!-- 接口环境配置 -->
                <el-input v-model="form.interf_paras">
                  <el-button slot="append" icon="el-icon-edit" @click="openEditInterfParas" />
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.sort')" prop="station_order">
                <!-- 排序 -->
                <el-input v-model="form.station_order" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenanceStation.OnlineStation')">
                <!-- 上线工位 -->
                <el-radio-group v-model="form.online_flag">
                  <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.onlineStationCode')" prop="online_station_code">
                <!-- 上线工位号 -->
                <el-select v-model="form.online_station_code" filterable clearable multiple>
                  <el-option v-for="item in sysFmodStationData" :key="item.station_id" :label="item.station_code + item.station_des" :value="item.station_code + ''" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.offStation')">
                <!-- 下线工位 -->
                <el-radio-group v-model="form.offline_flag">
                  <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.repairStation')" class="formItemStyle">
                <!-- 返修工位 -->
                <el-radio-group v-model="form.repair_flag">
                  <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.stationDisplayedIndependently')">
                <!-- 工位独立显示 -->
                <el-radio-group v-model="form.show_only_flag">
                  <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.orderSelectionAuthority')">
                <!-- 订单选择权限 -->
                <el-radio-group v-model="form.select_order_flag">
                  <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')" class="formItemStyle">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenanceStation.attribute1')" prop="attribute1">
                <!-- 属性1 -->
                <el-input v-model="form.attribute1" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.attribute2')" prop="attribute2">
                <!-- 属性2 -->
                <el-input v-model="form.attribute2" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.attribute3')" prop="attribute3">
                <!-- 属性3 -->
                <el-input v-model="form.attribute3" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.attribute4')" prop="attribute4">
                <!-- 属性4 -->
                <el-input v-model="form.attribute4" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenanceStation.attribute5')" prop="attribute5">
                <!-- 属性5 -->
                <el-input v-model="form.attribute5" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center;">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <el-dialog title="接口环境配置" width="50%" top="20px" :visible.sync="dialogInterfParasVisible" :close-on-click-modal="false">
            <el-input v-model="interf_paras" type="textarea" :rows="20" placeholder="请输入内容" />
            <span style="color:red;">{{ jsonErrorMsg }}</span>
          </el-dialog>

          <!--表格渲染-->
          <el-table border ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" fixed="left" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="station_code" :label="$t('lang_pack.maintenanceStation.stationCode')" min-width="100" align="center"/>
            <!-- 工位编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="station_des" :label="$t('lang_pack.maintenanceStation.stationDescription')"  min-width="100" align="center"/>
            <!-- 工位描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="station_short_code" :label="$t('lang_pack.maintenanceStation.shortNumber')" />
            <!-- 短序号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="line_section_code" :label="$t('lang_pack.maintenanceStation.segmentedEncoding')" />
            <!-- 分段编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="sub_station_code" :label="$t('lang_pack.maintenanceStation.divisionNumber')" />
            <!-- 分工位号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="station_attr" :label="$t('lang_pack.maintenanceStation.stationRelatedAttributes')" width="120" />
            <!-- 工位相关属性 -->

            <el-table-column  :label="$t('lang_pack.maintenanceStation.OnlineStation')" align="center" prop="online_flag">
              <!-- 上线工位 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.online_flag] }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="online_station_code" :label="$t('lang_pack.maintenanceStation.onlineStationCode')"  min-width="100" align="center" />
            <!-- 上线工位号 -->
            <el-table-column  :label="$t('lang_pack.maintenanceStation.offStation')" align="center" prop="offline_flag" >
              <!-- 下线工位 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.offline_flag] }}
              </template>
            </el-table-column>
            <el-table-column  :label="$t('lang_pack.maintenanceStation.repairStation')" align="center" prop="repair_flag">
              <!-- 返修工位 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.repair_flag] }}
              </template>
            </el-table-column>
            <el-table-column  :label="$t('lang_pack.maintenanceStation.stationDisplayedIndependently')" align="center" prop="show_only_flag" width="120">
              <!-- 工位独立显示 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.show_only_flag] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="station_group_code" :label="$t('lang_pack.maintenanceStation.CombinatorialCode')" />
            <!-- 组合编码 -->

            <el-table-column  :label="$t('lang_pack.maintenanceStation.optionPeriod')" align="center" prop="select_order_flag">
              <!-- 选择权限 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.select_order_flag] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="pc_host" :label="$t('lang_pack.maintenanceStation.ipAddress')" />
            <!-- IP地址 -->
            <el-table-column  :show-overflow-tooltip="true" prop="bg_proceduce_code" :label="$t('lang_pack.maintenanceStation.reportWorkingProcedureNumber')" width="100" />
            <!-- 报工工序号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="bg_proceduce_des" :label="$t('lang_pack.maintenanceStation.reportWorkingProcedureDescription')" width="120" />
            <!-- 报工工序描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="alarm_set_times" :label="$t('lang_pack.maintenanceStation.downTime')" />
            <!-- 故障时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="fix_set_times" :label="$t('lang_pack.maintenanceStation.serviceTime')" />
            <!-- 保养时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="plan_run_times" :label="$t('lang_pack.maintenanceStation.runTime')" />
            <!-- 运行时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="beat_times" :label="$t('lang_pack.maintenanceStation.taktImage')" />
            <!-- 理想节拍 -->
            <el-table-column  :show-overflow-tooltip="true" prop="station_scroll_info" :label="$t('lang_pack.maintenanceStation.scrollingMessage')" />
            <!-- 滚动信息 -->
            <el-table-column  :label="$t('lang_pack.maintenanceStation.methodLis')" align="center" prop="data_collect_type" width="100">
              <!-- 采集方式 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_COLLECT_TYPE[scope.row.data_collect_type] }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="product_type" :label="$t('lang_pack.maintenanceStation.productType')" />
            <!-- 产品类型 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_id" :label="$t('lang_pack.maintenanceStation.cellId')">
              <!-- 单元ID -->
              <template slot-scope="scope">
                {{ getCelDes(scope.row.cell_id) }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="client_id_list" :label="$t('lang_pack.maintenanceStation.scadaExample')" width="100" />
            <!-- SCADA实例 -->
            <el-table-column  :show-overflow-tooltip="true" prop="flow_id_list" :label="$t('lang_pack.maintenanceStation.exampleFlowChart')" width="100" />
            <!-- 流程图实例 -->
            <el-table-column  :show-overflow-tooltip="true" prop="logic_id_list" :label="$t('lang_pack.maintenanceStation.automatedLogicalInstance')" width="120" />
            <!-- 自动化逻辑实例 -->
            <el-table-column  :show-overflow-tooltip="true" prop="interf_paras" :label="$t('lang_pack.maintenanceStation.interfaceConfigurationEnvironment')" width="120" />
            <!-- 接口环境配置 -->
            <el-table-column  :show-overflow-tooltip="true" prop="station_order" :label="$t('lang_pack.maintenanceStation.sort')" />
            <!-- 排序 -->

            <el-table-column  :show-overflow-tooltip="true" prop="attribute1" :label="$t('lang_pack.maintenanceStation.attribute1')" />
            <!-- 属性1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="attribute2" :label="$t('lang_pack.maintenanceStation.attribute2')" />
            <!-- 属性2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="attribute3" :label="$t('lang_pack.maintenanceStation.attribute3')" />
            <!-- 属性3 -->
            <el-table-column  :show-overflow-tooltip="true" prop="attribute4" :label="$t('lang_pack.maintenanceStation.attribute4')" />
            <!-- 属性4 -->
            <el-table-column  :show-overflow-tooltip="true" prop="attribute5" :label="$t('lang_pack.maintenanceStation.attribute5')" />
            <!-- 属性5 -->

            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import crudStation from '@/api/core/factory/sysStation'
import { sel as selFlowMain } from '@/api/core/flow/rcsFlowMain'
import { selLogicFunc } from '@/api/core/logic/logicFunc'
import { selScadaClient } from '@/api/core/scada/client'
import { sel as selCell } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  station_id: '',
  prod_line_id: '',
  station_code: '',
  station_des: '',
  station_short_code: '',
  line_section_code: '',
  sub_station_code: '',
  station_attr: '',
  online_flag: 'Y',
  offline_flag: 'N',
  repair_flag: 'N',
  show_only_flag: 'Y',
  station_group_code: '',
  select_order_flag: 'Y',
  pc_host: '',
  bg_proceduce_code: '',
  bg_proceduce_des: '',
  alarm_set_times: '0',
  fix_set_times: '0',
  plan_run_times: '0',
  beat_times: '0',
  station_scroll_info: '',
  data_collect_type: '',
  product_type: '',
  cell_id: '0',
  client_id_list: '',
  flow_id_list: '',
  logic_id_list: '',
  interf_paras: '',
  station_order: '1',
  online_station_code: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: '',
  attribute4: '',
  attribute5: ''
}
export default {
  name: 'SYS_STATION',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '工位管理',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'station_id',
      // 排序
      sort: ['station_id asc'],
      // CRUD Method
      crudMethod: { ...crudStation },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_fmod_station:add'],
        edit: ['admin', 'sys_fmod_station:edit'],
        del: ['admin', 'sys_fmod_station:del'],
        reset: ['admin', 'sys_fmod_station:reset']
      },
      rules: {
        station_code: [{ required: true, message: '请输入工位编码', trigger: 'blur' }]
      },
      // 产线数据
      currentProdLineId: '', // 当前产线(单笔)
      prodLineData: [],
      cellData: [],
      clientData: [],
      flowMainData: [],
      logicData: [],
      sysFmodStationData: [],
      dialogInterfParasVisible: false,
      interf_paras: '{}',
      jsonErrorMsg: ''
    }
  },
  watch: {
    interf_paras: {
      handler() {
        try {
          var _json = JSON.parse(this.interf_paras)
          if (typeof _json === 'object' && _json) {
            this.interf_paras = JSON.stringify(_json, null, 4)
          }
          this.jsonErrorMsg = ''
        } catch (ex) {
          this.jsonErrorMsg = ex
        }
        this.form.interf_paras = this.interf_paras
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WHETHER_FLAG', 'DATA_COLLECT_TYPE'],
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {
    // 加载 产线LOV
    const query = {
      userID: Cookies.get('userName')
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    selScadaClient({
      userID: Cookies.get('userName'),      
      sort: 'client_id',
      enable_flag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.clientData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    selFlowMain({
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.flowMainData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    selLogicFunc({
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.logicData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    selCell({
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.cellData = defaultQuery.data
            this.cellData.push({ cell_id: 0, cell_container_name: '', cell_container_des: '通用参数' })
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 获取单元的中文描述
    getCelDes(cell_id) {
      var item = this.cellData.find(item => item.cell_id === cell_id)
      if (item !== undefined) {
        return item.cell_container_name + ' ' + item.cell_container_des
      }
      return cell_id
    },
    openEditInterfParas() {
      this.interf_paras = '{}'
      this.jsonErrorMsg = ''
      if (this.form.interf_paras !== '') {
        try {
          var _json = JSON.parse(this.form.interf_paras)

          if (typeof _json === 'object' && _json) {
            this.interf_paras = JSON.stringify(_json, null, 4)
          }
        } catch (ex) {
          this.jsonErrorMsg = ex
          this.interf_paras = this.form.interf_paras
        }
      }
      this.dialogInterfParasVisible = true
    },
    // 更改产线
    changeProdLine(val) {
      this.currentProdLineId = val // 当前产线
      // 加载 工位LOV
      this.queryStation()
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId,
        enable_flag: 'Y'
      }
      crudStation
        .lovStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.sysFmodStationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      this.currentProdLineId = crud.form.prod_line_id // 当前产线
      // 加载 工位LOV
      this.queryStation()
      crud.form.client_id_list = crud.form.client_id_list === '' ? '' : crud.form.client_id_list.split(',')
      crud.form.flow_id_list = crud.form.flow_id_list === '' ? '' : crud.form.flow_id_list.split(',')
      crud.form.logic_id_list = crud.form.logic_id_list === '' ? '' : crud.form.logic_id_list.split(',')
      crud.form.online_station_code = crud.form.online_station_code === '' ? '' : crud.form.online_station_code.split(',')
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.client_id_list = this.form.client_id_list === '' ? '' : this.form.client_id_list.join(',')
      crud.form.flow_id_list = this.form.flow_id_list === '' ? '' : this.form.flow_id_list.join(',')
      crud.form.logic_id_list = this.form.logic_id_list === '' ? '' : this.form.logic_id_list.join(',')
      crud.form.online_station_code = this.form.online_station_code === '' ? '' : this.form.online_station_code.join(',')
      return true
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-form-wrap {
  height: 65%;
  overflow: auto;
}
::v-deep .el-textarea__inner {
  color: #f8c555;
  background-color: #2d2d2d;
}
</style>
