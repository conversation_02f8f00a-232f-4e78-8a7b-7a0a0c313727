<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="余料编号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="规格:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="材质:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="材质:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="余料类型:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="状态：">
                <el-select v-model="query.lock_flag" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'是',value:'Y'},{id:'2',label:'否',value:'N'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="500px"
      >
        <el-form
          ref="form"
          class="el-form-wrap el-form-column"
          :model="form"
          :rules="rules"
          size="small"
          label-width="145px"
          :inline="true"
        >
          <el-form-item label="余料类型" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入余料类型" />
          </el-form-item>
          <el-form-item label="规格(mm)" prop="task_from">
            <el-input v-model="form.gg" style="width: 110px;" placeholder="长" />
            <el-input v-model="form.gg" style="width: 110px;" placeholder="宽" />
            <el-input v-model="form.gg" style="width: 110px;" placeholder="厚" />
          </el-form-item>
          <el-form-item label="重量" prop="task_from">
            <el-input v-model="form.zl" placeholder="请输入重量" />
          </el-form-item>
          <el-form-item label="材质" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入材质" />
          </el-form-item>
          <el-form-item label="备注" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入备注" />
          </el-form-item>
          <el-form-item label="存放人员" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入存放人员" />
          </el-form-item>
          <el-form-item label="操作人员" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入操作人员" />
          </el-form-item>
          <el-divider />
          <div style="text-align: center">
            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
              $t('lang_pack.commonPage.cancel') }}</el-button>
            <!-- 取消 -->
            <el-button
              type="primary"
              size="small"
              icon="el-icon-check"
              :loading="crud.status.cu === 2"
              @click="crud.submitCU"
            >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
            <!-- 确认 -->
          </div>
        </el-form>
      </el-drawer>
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="tableData"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="ylbh" label="余料编号" />
            <el-table-column :show-overflow-tooltip="true" prop="gg" label="规格(mm)" />
            <el-table-column :show-overflow-tooltip="true" prop="cz" label="材质" />
            <el-table-column :show-overflow-tooltip="true" prop="zl" label="重量(kg)" />
            <el-table-column :show-overflow-tooltip="true" prop="wz" label="位置" />
            <el-table-column :show-overflow-tooltip="true" prop="zt" label="状态" />
            <el-table-column :show-overflow-tooltip="true" prop="sj" label="入库时间" />
            <el-table-column :show-overflow-tooltip="true" prop="yllx" width="120" label="余料类型" />
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >余料出库</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >删除</el-button>
              </template></el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <dcsWmsMaterialInfoDetail />
  </div>
</template>

<script>
import crudWmsMapStockCell from '@/api/dcs/project/wms/wmsMapStockCell'
import dcsWmsMaterialInfoDetail from './dcsWmsMaterialInfoDetail.vue'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_MATERIAL_INFO',
  components: { crudOperation, rrOperation, udOperation, pagination, dcsWmsMaterialInfoDetail },
  cruds() {
    return CRUD({
      title: '余料信息管理',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapStockCell },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 690,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      tableData: [
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' }
      ],
      rules: {
        task_from: [{ required: true, message: '请选择任务来源', trigger: 'change' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 690
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>

    <style>
    .table-descriptions-label {
    width: 150px;
    }
    .table-descriptions-content {
    width: 150px;
    }
    </style>
