<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <!--查询条件-->
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
          <el-form ref="query" :inline="true" size="mini" style="margin: 0; padding: 0">
            <el-form-item :label="$t('view.form.instance') + ': '" style="margin: 0px 0px 5px 0px">
              <el-select
                v-model="query.client_code"
                filterable
                size="mini"
                style="width: 300px"
                :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                @change="getCellIp()"
              >
                <el-option
                  v-for="(item, index) in scadaClientLov"
                  :key="index"
                  :label="item.client_des"
                  :value="item.client_code"
                >
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.client_code }}</span>
                  <span style="float: right">{{ item.client_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('view.form.communicationStatus') + ': '" style="margin: 0px 0px 5px 0px">
              <el-select
                v-model="query.link_status"
                clearable
                size="mini"
                style="width: 120px"
                :placeholder="$t('view.enum.placeholder.pleaseChosen')"
              >
                <el-option
                  v-for="item in linkStatusLov"
                  :key="item.link_status"
                  :label="item.link_status_des"
                  :value="item.link_status"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('view.form.timePeriod') + ': '" style="margin: 0px 0px 5px 0px">
              <el-date-picker
                ref="timepicker"
                v-model="query.create_time"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="~"
                :start-placeholder="$t('view.form.timePeriodStart')"
                :end-placeholder="$t('view.form.timePeriodEnd')"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                align="right"
                :picker-options="pickerOptions"
              />
            </el-form-item>
            <el-form-item style="margin: 0;float:right">
              <el-button
                class="filter-item"
                size="mini"
                type="primary"
                icon="el-icon-search"
                style="margin-left: 10px"
                @click="btnQuery('search', '', '')"
              >
                {{ $t('view.button.search') }}
              </el-button>
              <el-button
                v-if="true"
                class="filter-item"
                size="mini"
                icon="el-icon-refresh-left"
                @click="btnQuery('reset', '', '')"
              >
                {{ $t('view.button.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!--查询结果-->
        <el-card shadow="always" style="margin-top: 10px">
          <el-tabs v-model="tabValue" :tab-position="tabPosition" style="height:500px;" @tab-click="tabSelect">
            <el-tab-pane name="tableTab">
              <span slot="label"><i class="el-icon-s-grid" />
                {{ $t('view.table.tableForm') }}
              </span>
              <el-card>
                <el-table
                  ref="tableReport"
                  v-loading="loadingReport"
                  :data="dataReport"
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F4F7', color: '#757575' }"
                  border
                  height="450px"
                  :highlight-current-row="true"
                >
                  <el-table-column v-if="1 == 1" width="200" prop="id" label="ID" />
                  <el-table-column
                    :show-overflow-tooltip="true"
                    width="200"
                    prop="create_time"
                    :label="$t('view.table.time')"
                  />
                  <el-table-column
                    :show-overflow-tooltip="true"
                    width="150"
                    prop="device_code"
                    :label="$t('view.table.instanceCode')"
                  />
                  <el-table-column
                    :show-overflow-tooltip="true"
                    width="200"
                    prop="device_des"
                    :label="$t('view.table.instanceDescription')"
                  />
                  <el-table-column
                    :show-overflow-tooltip="true"
                    align="center"
                    width="100"
                    prop="link_status"
                    :label="$t('view.table.communicationStatus')"
                  >
                    <template slot-scope="scope">
                      <el-tag :type="scope.row.link_status === 'OK' ? 'success' : 'warning'" disable-transitions>
                        {{ scope.row.link_status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :show-overflow-tooltip="true"
                    prop="link_message"
                    :label="$t('view.table.communicationInfo')"
                  />
                  <el-table-column
                    :show-overflow-tooltip="true"
                    align="center"
                    width="130"
                    prop="simulated_flag"
                    :label="$t('view.table.simulationFlag')"
                  />
                </el-table>
                <el-row :gutter="20">
                  <el-col :span="4" :offset="10">
                    <div style="margin-top: 5px">
                      <el-button-group>
                        <el-button type="primary" icon="el-icon-arrow-left" @click="pageQuery('pre')">
                          {{ $t('view.pagination.previous') }}
                        </el-button>
                        <el-button type="primary" @click="pageQuery('next')">
                          {{ $t('view.pagination.next') }}
                          <i class="el-icon-arrow-right el-icon--right" />
                        </el-button>
                      </el-button-group>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-tab-pane>
            <el-tab-pane name="chartTab">
              <span slot="label">
                <i class="el-icon-s-data" />
                {{ $t('view.table.curveForm') }}
              </span>
              <el-card>
                <ECharts
                  ref="chartReport"
                  v-loading="loadingChartReport"
                  :options="chartOptions"
                  style="width: 1200px;height:450px;"
                />
              </el-card>
            </el-tab-pane>
            <el-tab-pane name="ngChartTab">
              <span slot="label">
                <i class="el-icon-coin" />
                {{ $t('view.table.exceptionStatistics') }}
              </span>
              <el-card>
                <ECharts
                  ref="ngChartReport"
                  v-loading="loadingChartReport"
                  :options="ngChartOptions"
                  style="width: 1200px;height:450px;"
                />
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<!--JS-->
<script>
// 导入
import { selCellIP } from '@/api/core/center/cell'
import { InitTimePickRange, ScadaClientSelect } from '@/api/core/scada/report'
import ECharts from 'vue-echarts'
import Cookies from 'js-cookie'
import axios from 'axios'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'

// 执行
export default {
  name: 'REPORT_SCADA_LINK',
  // 1.初始化组件
  components: {
    ECharts
  },
  // 2.初始化参数设置
  data() {
    return {
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      clientInfo: {},
      query: {
        client_code: '',
        link_status: '',
        create_time: null
      },
      scadaClientLov: [],
      linkStatusLov: [
        { link_status: 'OK', link_status_des: this.$t('view.enum.communication.normal') },
        { link_status: 'NG', link_status_des: this.$t('view.enum.communication.abnormal') }
      ],
      tabPosition: 'left', // Tab方向
      tabValue: 'tableTab', // 当前Tab选择
      dataReport: [], // 表格数据
      loadingReport: false, // 表格是否正在加载
      loadingChartReport: false,
      tableSize: 100, // 默认表格单次查询100行数据
      chartOptions: {},
      ngChartOptions: {},
      pickerOptions: {}
    }
  },
  // 3.页面创建时加载
  created: function() {
    // 初始化日期选择器快捷选项
    this.pickerOptions = createDatePickerShortcuts(this.$i18n)

    // Scada实例查询
    ScadaClientSelect().then((res) => {
      var result = JSON.parse(JSON.stringify(res))
      if (result.hasOwnProperty('error')) {
        this.$message({
          message: this.$t('view.dialog.scadaInstanceBaseDataQueryFailed') + result.error,
          type: 'error'
        })
        this.scadaClientLov = []
        return
      }
      this.scadaClientLov = result.data
    }).catch(() => {
      this.$message({
        message: this.$t('view.dialog.scadaInstanceBaseDataQueryTimeoutOrSQLError'),
        type: 'error'
      })
    })
    // 自动加载开始时间结束时间(默认7天以内)
    this.query.create_time = InitTimePickRange(7)
  },
  // 5.页面渲染
  mounted() {
    this.chartOptions = {
      title: {
        text: this.$t('view.table.communicationStatusScatterPlotAnalysis'),
        left: 'center'
      },
      legend: {
        data: [this.$t('view.enum.communication.normal'), this.$t('view.enum.communication.abnormal')],
        left: 'right'
      },
      dataZoom: [{
        type: 'inside'
      }, {
        type: 'slider'
      }],
      xAxis: {
        name: this.$t('view.table.sampleQuantity')
      },
      yAxis: {
        name: this.$t('view.table.statusValue')
      },
      series: [{
        name: this.$t('view.enum.communication.normal'),
        type: 'scatter',
        itemStyle: {
          normal: {
            color: 'green'
          }
        }
      }, {
        name: this.$t('view.enum.communication.abnormal'),
        type: 'scatter',
        itemStyle: {
          normal: {
            color: 'red'
          }
        }
      }]
    }
    // NG统计
    this.ngChartOptions = {
      title: {
        text: this.$t('view.table.communicationStatusScatterPlotAnalysis'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      dataZoom: [{
        type: 'inside'
      }, {
        type: 'slider'
      }],
      xAxis: [{
        type: 'category',
        axisTick: {
          alignWithLabel: true
        }
      }],
      yAxis: [{
        type: 'value'
      }],
      series: [{
        name: this.$t('view.table.networkOutageFrequency'),
        type: 'bar',
        barWidth: '60%'
      }]
    }
  },
  // 4.页面执行方法事件
  methods: {
    getCellIp() {
      const cell_id = this.scadaClientLov.filter(a => a.client_code === this.query.client_code)[0].cell_id
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('view.dialog.querySelCellIPException'), type: 'error' })
        })
    },
    // 4.1 查询方法
    btnQuery(code, pageDirct, pageId) {
      if (code === 'reset') { // 重置
        this.query.client_code = ''
        this.query.link_status = ''
        this.query.create_time = InitTimePickRange(7)
      } else {
        if (this.query.client_code === '') {
          this.$message({
            message: this.$t('view.dialog.selectInstance'),
            type: 'info'
          })
          return
        }
        var start_time = this.query.create_time === null ? '' : this.query.create_time[0]
        var end_time = this.query.create_time === null ? '' : this.query.create_time[1]
        var isKT = true
        if (start_time !== '' && end_time !== '') {
          var start_time2 = start_time.replace(/\-/g, '/')
          var end_time2 = end_time.replace(/\-/g, '/')
          var sTime = new Date(start_time2) // 开始时间
          var eTime = new Date(end_time2) // 结束时间
          var diff = parseInt((eTime.getTime() - sTime.getTime()) / (1000 * 3600))
          if (diff <= 24) {
            isKT = false
          }
        }
        this.loadingReport = true
        if (this.cellIp === '' || this.webapiPort === '') {
          this.$message({
            message: this.$t('view.dialog.noUnitIPAndPortNumberObtained'),
            type: 'info'
          })
          return
        }
        var method = '/cell/core/scada/CoreScadaLinkReportSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        // A:表格刷新
        const data = {
          device_code: this.query.client_code,
          link_status: this.query.link_status,
          start_time: start_time,
          end_time: end_time,
          tableSize: this.tableSize,
          page_dirct: pageDirct,
          page_id: pageId
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0) {
              if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                if (pageId !== '') {
                  this.$message({
                    message: this.$t('view.dialog.bottom'),
                    type: 'info'
                  })
                } else {
                  this.dataReport = []
                }
                this.loadingReport = false
                return
              }
              this.dataReport = defaultQuery.data.data
              this.loadingReport = false
            } else {
              this.$message({
                message: this.$t('view.dialog.scadaCommunicationReportQueryFailed') + defaultQuery.data.msg,
                type: 'error'
              })
              this.dataReport = []
              this.loadingReport = false
            }
          })
          .catch(ex => {
            this.loadingReport = false
            this.$message({
              message: this.$t('view.dialog.scadaCommunicationReportQueryTimeoutOrSQLError'),
              type: 'error'
            })
          })
        // B:曲线刷新
        if (code === 'search') {
          this.loadingChartReport = true
          const data1 = {
            device_code: this.query.client_code,
            link_status: this.query.link_status,
            start_time: start_time,
            end_time: end_time,
            tableSize: '',
            page_dirct: '',
            page_id: ''
          }
          axios
            .post(path, data1, {
              headers: {
                'Content-Type': 'application/json'
              }
            })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.data.code === 0) {
                var result = defaultQuery.data
                if (result.data === undefined || result.data.length <= 0) {
                  this.chartOptions = {}
                  this.ngChartOptions = {}
                  this.loadingChartReport = false
                  return
                }
                var arrayData = result.data
                var okList = []
                var ngList = []
                var ngDateList = [] // 用于NG统计X
                var ngValueList = [] // 用于NG统计Y
                var oldDateOrHour = ''
                for (var i = 0; i < arrayData.length; i++) {
                  var nowDateOrHour = ''
                  var link_status = arrayData[i].link_status
                  var link_message = arrayData[i].link_message
                  var create_time = arrayData[i].create_time
                  var create_time_val = arrayData[i].create_time_val.toString()
                  var link_status_value = -1
                  if (link_status === 'OK') link_status_value = 1
                  var item = []
                  item.push(i + 1)
                  item.push(link_status_value)
                  item.push(create_time)
                  item.push(link_status)
                  item.push(link_message)
                  if (link_status === 'OK') okList.push(item)
                  else ngList.push(item)
                  // NG统计
                  if (link_status === 'NG') {
                    if (isKT) {
                      nowDateOrHour = create_time.substring(0, 10)
                    } else {
                      nowDateOrHour = create_time_val.substring(8, 10)
                    }
                    if (oldDateOrHour !== nowDateOrHour) {
                      oldDateOrHour = nowDateOrHour
                      ngDateList.push(nowDateOrHour)
                      ngValueList.push(1)
                    } else {
                      var newValue = ngValueList[ngValueList.length - 1]
                      newValue = newValue + 1
                      ngValueList.splice((ngValueList.length - 1), 1, newValue)
                    }
                  }
                }
                // 散点图
                this.chartOptions = {
                  title: {
                    text: this.$t('view.table.communicationStatusScatterPlotAnalysis'),
                    left: 'center'
                  },
                  legend: {
                    data: [this.$t('view.enum.communication.normal'), this.$t('view.enum.communication.abnormal')],
                    left: 'right'
                  },
                  dataZoom: [{
                    type: 'inside'
                  }, {
                    type: 'slider'
                  }],
                  tooltip: {
                    showDelay: 0,
                    formatter: function(params) {
                      if (params.value.length >= 5) {
                        return this.$t('view.table.serialNumber') + ': ' + params.value[0] + '<br/>' +
                               this.$t('view.table.time') + ': ' + params.value[2] + '<br/>' +
                               this.$t('view.table.communicationStatus') + ': ' + params.value[3] + '<br/>' +
                               this.$t('view.table.messageDescription') + ': ' + params.value[4]
                      }
                    },
                    axisPointer: {
                      show: true,
                      type: 'cross',
                      lineStyle: {
                        type: 'dashed',
                        width: 1
                      }
                    }
                  },
                  xAxis: {
                    name: this.$t('view.table.sampleQuantity')
                  },
                  yAxis: {
                    name: this.$t('view.table.statusValue'),
                    min: -2,
                    max: 2
                  },
                  series: [{
                    name: this.$t('view.enum.communication.normal'),
                    type: 'scatter',
                    symbolSize: 10,
                    data: okList,
                    itemStyle: {
                      normal: {
                        color: 'green'
                      }
                    }
                  },
                  {
                    name: this.$t('view.enum.communication.abnormal'),
                    type: 'scatter',
                    symbolSize: 10,
                    data: ngList,
                    itemStyle: {
                      normal: {
                        color: 'red'
                      }
                    }
                  }
                  ]
                }
                // 断网统计
                this.ngChartOptions = {
                  title: {
                    text: this.$t('view.table.communicationExceptionFrequencyStatistics'),
                    left: 'center'
                  },
                  tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                      type: 'shadow'
                    }
                  },
                  dataZoom: [{
                    type: 'inside'
                  }, {
                    type: 'slider'
                  }],
                  xAxis: [{
                    type: 'category',
                    data: ngDateList,
                    axisTick: {
                      alignWithLabel: true
                    }
                  }],
                  yAxis: [{
                    type: 'value'
                  }],
                  series: [{
                    name: this.$t('view.table.networkOutageFrequency'),
                    data: ngValueList,
                    type: 'bar',
                    barWidth: '60%'
                  }]
                }
                this.loadingChartReport = false
              } else {
                this.$message({
                  message: this.$t('view.dialog.scadaCommunicationReportQueryFailed') + defaultQuery.data.msg,
                  type: 'error'
                })
                this.chartOptions = {}
                this.ngChartOptions = {}
                this.loadingChartReport = false
              }
            }).catch(() => {
              this.loadingChartReport = false
              this.$message({
                message: this.$t('view.dialog.scadaCommunicationReportQueryTimeoutOrSQLError'),
                type: 'error'
              })
            })
        }
      }
    },
    // 4.2 Tab切换
    tabSelect(tab, event) { },
    // 4.3 上一页或者下一页翻页
    pageQuery(pageDirct) {
      if (this.dataReport == null || this.dataReport.length <= 0) {
        this.$message({
          message: this.$t('view.dialog.noData'),
          type: 'info'
        })
        return
      }
      if (pageDirct === 'pre') {
        this.btnQuery('tablePage', pageDirct, this.dataReport[0].id)
      } else {
        this.btnQuery('tablePage', pageDirct, this.dataReport[this.dataReport.length - 1].id)
      }
    }
  }
}
</script>
<!--CSS-->
<style lang="scss" >
.box-card {
  min-height: calc(100vh);
  padding: 10px;
}

:focus {
  outline: 0;
}

.el-card__body {
  padding: 10px;
}</style>
