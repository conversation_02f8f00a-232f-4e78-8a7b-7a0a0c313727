import request from '@/utils/request'

// 查询系统参数
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysParameterSel',
    method: 'post',
    data
  })
}
// 新增系统参数
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysParameterIns',
    method: 'post',
    data
  })
}
// 修改系统参数
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysParameterUpd',
    method: 'post',
    data
  })
}
// 删除系统参数
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysParameterDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
