import request from '@/utils/request'

// 查询角色
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleSel',
    method: 'post',
    data
  })
}
// 新增角色
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleIns',
    method: 'post',
    data
  })
}
// 修改角色
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleUpd',
    method: 'post',
    data
  })
}
// 删除角色
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleDel',
    method: 'post',
    data
  })
}
// 查询角色工位
export function selRoleStation(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleStationSel',
    method: 'post',
    data
  })
}
// 插入角色工位
export function addRoleStation(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysRoleStationIns',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, selRoleStation, addRoleStation }
