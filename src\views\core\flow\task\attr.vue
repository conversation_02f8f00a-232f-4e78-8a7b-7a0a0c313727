<template>
  <div>
    <el-form ref="query" :inline="false" size="small">
      <el-form-item label="属性组：">
        <el-select v-model="currentGroupId" filterable clearable style="width:300px;" @change="attrGroupChange">
          <el-option v-for="item in attrGroupData" :key="item.step_mod_attr_group_id" :label="item.step_mod_attr_group_des" :value="item.step_mod_attr_group_id" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-row :gutter="20" style="margin-top:10px;">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="250px" @selection-change="crud.selectionChangeHandler">
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  :show-overflow-tooltip="true" prop="step_mod_attr_item_code" label="属性代码" />
          <el-table-column  :show-overflow-tooltip="true" prop="step_mod_attr_item_des" label="属性描述" />
          <el-table-column  :show-overflow-tooltip="true" prop="step_mod_attr_n_value" label="值" />
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudFlowModAttrGroup from '@/api/core/flow/rcsFlowModAttrGroup'
import crudFlowModAttrItem from '@/api/core/flow/rcsFlowModAttrItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import pagination from '@crud/Pagination'
const defaultForm = {
  step_mod_attr_item_id: 0,
  step_mod_attr_group_id: 0,
  step_mod_attr_item_code: '',
  step_mod_attr_item_des: '',
  step_mod_attr_value_type: '',
  step_mod_attr_n_value: '',
  step_mod_tag_code_list: '',
  ok_remarks: '',
  enable_flag: 'Y',
  step_attr_item_id: '',
  flow_main_id: 0,
  step_attr_n_value: '',
  link_tag_id_list: '',
  error_flag: '',
  error_msg: ''
}
export default {
  name: 'RCSFLOWMODATTRGROUP',
  components: { pagination },
  props: {
    step_mod_id: {
      type: [String, Number],
      default: -1
    },
    readonly: {
      type: Boolean,
      default: false
    },
    flow_main_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '属性组',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'step_mod_attr_item_id',
      // 排序
      sort: ['step_mod_attr_item_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowModAttrItem },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      attrItemShow: false,
      currentGroupId: '',
      permission: {
        add: ['admin', 'rcs_flow_mod_attr_group:add'],
        edit: ['admin', 'rcs_flow_mod_attr_group:edit'],
        del: ['admin', 'rcs_flow_mod_attr_group:del']
      },
      attrGroupData: []
    }
  },
  watch: {
    step_mod_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.step_mod_id = this.step_mod_id
        this.query.step_mod_attr_group_id = 0
        this.query.flow_main_id = this.flow_main_id
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    crudFlowModAttrGroup
      .sel({
        user_name: Cookies.get('userName'),
        step_mod_id: this.step_mod_id
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.attrGroupData = defaultQuery.data
          }
        }
        this.loading = false
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    attrGroupChange(val) {
      if (val === '') {
        val = '0'
      }
      this.query.step_mod_attr_group_id = val
      this.crud.toQuery()
    }
  }
}
</script>
