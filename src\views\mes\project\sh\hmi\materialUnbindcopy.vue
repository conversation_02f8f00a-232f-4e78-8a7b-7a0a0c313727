<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <!-- <div style=" display: flex; align-items: center;justify-content: space-between;">
        <div class="orderInfo">
          <div style="width: 220px;">物料查询:</div>
          <el-input ref="workNumber" v-model="monitorData.MesS_PartID.value" clearable size="small" @keyup.enter.native="handleScan" />
          <div style="width: 220px;margin-left: 15px;">精准二维码:</div>
          <el-input v-model="query.exact_barcode" clearable size="small" />
          <el-button size="small" type="primary" @click="handleScan">
            <svg-icon icon-class="scan" style="margin-right: 5px" />{{ $t("lang_pack.vie.scan") }}
          </el-button>
          <el-button size="small" type="primary" @click="handleOk">查询</el-button>
        </div>
      </div> -->
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="总成号:">
                <el-input ref="workNumber" v-model="monitorData.MesS_PartID.value" clearable size="small" @keyup.enter.native="handleScan" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料二维码:">
                <el-input v-model="monitorData.MesS_PartID.value" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <el-button size="small" type="primary" style="width: 80px;height: 30px;margin-right: -10px;" @click="handleScan">
            <svg-icon icon-class="scan" style="margin-right: 5px" />{{ $t("lang_pack.vie.scan") }}
          </el-button>
          <el-button size="small" type="primary" style="width: 80px;height: 30px;" @click="handleOk">总成查询</el-button>
          <el-button size="small" type="primary" style="width: 80px;height: 30px;" @click="handleOk3">物料查询</el-button>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            size="small"
            type="primary"
            @click="unbind()"
          >
            确认解绑
          </el-button>
        </template>
      </crudOperation>
      <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column type="selection" width="55" fixed="left" />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="prod_line_code"
          width="100"
          label="生产线"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="station_docs.station_des"
          width="100"
          label="工位描述"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="serial_num"
          width="220"
          label="工件编号"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="item_date"
          width="150"
          label="采集时间"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="material_code"
          width="100"
          label="物料号"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="material_des"
          width="150"
          label="物料描述"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="use_count"
          width="150"
          label="使用数量"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="material_batch"
          width="150"
          label="批次号"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="exact_barcode"
          width="150"
          label="精准二维码"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="station_docs.make_order"
          width="150"
          label="订单号"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="station_docs.small_model_type"
          width="150"
          label="机型"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="station_docs.staff_id"
          width="150"
          label="操作者"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="station_docs.enable_flag"
          width="100"
          label="有效标识"
        >
          <template slot-scope="scope">
            {{ scope.row.station_docs.enable_flag === "Y" ? "有效" : "无效" }}
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <div style="margin-top: 5px; text-align: right">
        <el-button-group>
          <el-button type="primary">总数量：{{ page.total }}</el-button>
          <el-button type="primary">当前第{{ nowPageIndex }}页</el-button>
          <el-button
            type="primary"
            @click="pageQuery('pre')"
          >&lt;&nbsp;上一页</el-button>
          <el-button
            type="primary"
            @click="pageQuery('next')"
          >下一页&nbsp;&gt;</el-button>
        </el-button-group>
      </div>
    </el-card>
    <el-dialog
      :fullscreen="false"
      top="10px"
      :show-close="true"
      :close-on-click-modal="false"
      title="请输入解绑原因"
      custom-class="step-attr-dialog"
      width="30%"
      :visible.sync="unbindDialogVisible"
    >
      <el-input v-model="remarks" type="textarea" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="unbindDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleOk2">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import crudMaterialSel from '@/api/mes/project/sh/shMaterialUnbind'
import crudStationMaterial from '@/api/mes/project/sh/stationMaterial'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
// import pagination from '@crud/Pagination'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
}
export default {
  name: 'repairStationIndex',
  components: {
    crudOperation,
    pagination
  },
  cruds() {
    return CRUD({
      title: '工位管理',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_trace_id',
      // 排序
      sort: ['material_trace_id asc'],
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // CRUD Method
      crudMethod: { ...crudStationMaterial },
      query: {
        tableSize: 40,
        enable_flag: 'Y'
      },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  data() {
    return {
      height: document.documentElement.clientHeight - 220,
      permission: {
        add: ['admin', 'sys_fmod_station:add'],
        edit: ['admin', 'sys_fmod_station:edit'],
        del: ['admin', 'sys_fmod_station:del'],
        reset: ['admin', 'sys_fmod_station:reset']
      },
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      monitorData: {
        // PLC请求写入信号
        MesS_PartID: { client_code: this.$route.query.station_code + '-Bar', group_code: 'BarStatus', tag_code: 'Bar_GetBarCodeResult', value: '' }
      },
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      scanValue: '',
      unbindDialogVisible: false,
      remarks: '',
      nowPageIndex: 1, // 当前页数
      pageList: []
    }
  },
  mounted() {
    // 启动监控
    this.toStartWatch()
    this.$nextTick(() => {
      this.$refs.workNumber.focus()
    })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 220
    }
  },
  methods: {
    unbind() {
      this.remarks = ''
      this.unbindDialogVisible = true
    },
    handleOk() {
      this.query.serial_num = this.monitorData.MesS_PartID.value
      if (!this.query.serial_num.length) {
        this.$message({
          type: 'warning',
          message: '不可以空查询'
        })
        return
      }
      this.crud.toQuery()
    },
    handleOk3() {
      this.query.exact_barcode = this.monitorData.MesS_PartID.value
      if (!this.query.exact_barcode.length) {
        this.$message({
          type: 'warning',
          message: '不可以空查询'
        })
        return
      }
      this.crud.toQuery()
    },
    handleOk2() {
      if (!this.crud.selections.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.crud.selections.forEach(item => {
        if (!item.material_trace_id) {
          this.$message({
            type: 'error',
            message: '解绑失败，未找到相关属性id'
          })
          return
        }
        const query = {
          material_trace_id: item.material_trace_id,
          remarks: this.remarks
        }
        crudMaterialSel.unbind(query)
          .then(res => {
            if (res.code === 0) {
              this.$message({ message: '操作成功', type: 'success' })
              this.unbindDialogVisible = false
              this.crud.toQuery()
            } else {
              this.$message({ message: '操作失败：' + res.msg, type: 'error' })
            }
          })
          .catch(ex => {
            this.$message({ message: '操作失败：' + ex, type: 'error' })
          })
      })
    },
    handleScan() {
      const tagKey = this.$route.query.station_code + '-Bar/BarStatus/Bar_GetBarCodeResult'
      const value = this.monitorData.MesS_PartID.value
      const clientCode = this.$route.query.station_code + '-Bar'
      this.handleRequestCodeOrde(tagKey, value, clientCode)
    },
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page =
          this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        // 请求数据
        this.crud.loading = true
        console.info(this.query)
        crudStationQuality
          .sel(this.query)
          .then((data) => {
            this.crud.loading = false
            if (data.count === 0) {
              this.$message({ message: '已置底', type: 'info' })
              this.nowPageIndex = this.nowPageIndex - 1
            } else {
              this.crud.page.total = data.count
              this.crud.data = data.count === 0 ? [] : data.data
            }
          })
          .catch((ex) => {
            this.crud.loading = false
            this.$message({
              message: '查询异常' + ex,
              type: 'error'
            })
          })
        // this.crud.toQuery()
      }
    },
    // ----------------------------------【MQTT】----------------------------------
    toStartWatch() {
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          // var connectUrl = 'ws://*************:8083' + '/mqtt'
          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // 获取Tag值
            this.GetTagValue()
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            var jsonData = JSON.parse(message)
            if (jsonData == null) return
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleRequestCodeOrde(tagKey, value, clientCode) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = `SCADA_WRITE/${clientCode}`
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 从后台REDIS获取数据
    GetTagValue() {
      // 读取Tag集合(Key)
      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                Object.keys(this.monitorData).forEach(key => {
                  var client_code = this.monitorData[key].client_code
                  var group_code = this.monitorData[key].group_code
                  var tag_code = this.monitorData[key].tag_code
                  var tag_key = client_code + '/' + group_code + '/' + tag_code
                  const item = result.filter(item => item.tag_key === tag_key)
                  if (item.length > 0) {
                    this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                  }
                })
              }
            }
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    }
  }
}
</script>
<style lang="less" scoped>
.orderInfo {
    font-size: 14px;
    display: flex;
    align-items: center;
   ::v-deep .el-input__inner {
      height: 30px;
      margin: 0 5px;
    }
  }
 ::v-deep .el-pagination{
    margin-bottom: 10px !important;
  }
</style>
