<template>
  <el-container id="codeingContainer" style="height: 100%; border: 0px solid red;background-color: rgb(238, 241, 246)">
    <el-aside width="350px" style="background-color: rgb(238, 241, 246)">
      <el-input
        v-model="filterText"
        placeholder="输入关键字进行过滤"
      />

      <div id="selectTrees">
        <el-tree
          ref="tree"
          class="filter-tree"
          :highlight-current="true"
          :data="treeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          node-key="id"
          :default-expanded-keys="treeDefaultExpandedKeys"
          :current-node-key="treeCurrentNodeKey"
          :render-content="handleTreeRenderContent"
          @node-click="handleNodeClick"
        />
      </div>
    </el-aside>
    <el-main v-loading="loading" element-loading-text="拼命加载代码中...">
      <el-dialog title="选择模板" width="650px" :modal="true" :visible.sync="templateChooseDialog" append-to-body>
        <el-form ref="query" :inline="true" size="small">
          <el-form-item label="函数编码：">
            <el-input v-model="functionMCode" clearable size="small" />
          </el-form-item>
          <el-form-item label="函数名：">
            <el-input v-model="functionMName" clearable size="small" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryTemplate()">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="table" border size="small" :data="functionMData" style="width: 100%" highlight-current-row height="350px" @header-dragend="crud.tableHeaderDragend()">
          <el-table-column :show-overflow-tooltip="true" prop="function_m_code" label="函数编码" />
          <el-table-column :show-overflow-tooltip="true" prop="function_m_name" label="函数名" />
          <el-table-column :show-overflow-tooltip="true" prop="function_m_des" label="函数描述" />
          <el-table-column label="操作" width="115" align="center" fixed="right">
            <!-- 操作 -->
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="openCode(scope.row)">查看</el-button>
              <el-button size="small" type="text" @click="handleTemplateChoose(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 10px;height:35px;line-height:35px;">
          注：选择模板会覆盖当前代码，请谨慎操作！
          <el-pagination
            :page-size.sync="functionMPageData.size"
            :total="functionMPageData.total"
            :current-page.sync="functionMPageData.page"
            :page-sizes="[20, 30, 40, 50, 100]"
            style="float:right;margin: 2px 0px 0px 0px !important;"
            layout="total, prev, pager, next, sizes"
            @size-change="paginationSizeChange($event)"
            @current-change="paginationCurrentChange"
          />
        </div>
      </el-dialog>
      <el-dialog append-to-body title="查看代码" custom-class="code-dialog-height" :fullscreen="true" :visible.sync="codeDialogVisible">
        <codeEditor ref="codeEditor" :code_content="functionMContent" />
      </el-dialog>
      <el-dialog :title="saveAsTitle+'另存为模板'" width="650px" :modal="true" :close-on-click-modal="false" :visible.sync="saveAsDialog" append-to-body @closed="saveAsDialogClose">
        <el-form ref="form" label-width="80px" :inline="false" :model="form" :rules="rules" size="small" style="margin: 0; padding: 0;">
          <el-form-item label="模板编码" prop="function_m_code">
            <el-input v-model="form.function_m_code" style="width:90%" />
          </el-form-item>
          <el-form-item label="函数编码" prop="function_i_code">
            <el-input v-model="form.function_i_code" style="width:90%" />
          </el-form-item>
          <el-form-item label="函数名称" prop="function_i_name">
            <el-input v-model="form.function_i_name" style="width:90%" />
          </el-form-item>
          <el-form-item label="函数描述" prop="function_i_des">
            <el-input v-model="form.function_i_des" style="width:90%" />
          </el-form-item>
        </el-form>
        <div style="text-align:center">
          <el-button size="small" icon="el-icon-close" plain @click="saveAsDialog=false">取消</el-button>
          <el-button class="filter-item" size="mini" type="primary" icon="el-icon-circle-check" style="margin-left: 10px" @click="handleSaveAs">保存</el-button>
        </div>
      </el-dialog>
      <el-tabs v-model="tabsValue" :closable="true" @tab-remove="handleTabsClose">
        <el-tab-pane
          v-for="item in tabsData"
          :key="item.id"
          :label="item.title"
          :name="item.id"
          style="padding: 0px"
        >
          <span slot="label"><img class="icon-c2"> {{ item.title }}</span>
          <el-button-group style="position: fixed;top:105px;right:20px;z-index: 100;">
            <el-tooltip class="item" effect="dark" content="选择模板" placement="bottom">
              <el-button icon="el-icon-search" type="primary" plain @click="templateChooseDialog=true">选择</el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="校验代码" placement="bottom">
              <el-button icon="el-icon-check" type="primary" plain>校验</el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="发布程序" placement="bottom">
              <el-button icon="el-icon-s-promotion" type="primary" plain @click="handleRelease">发布</el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="另存为模板" placement="bottom">
              <el-button icon="el-icon-folder-opened" type="primary" plain @click="openSaveAsDialog">另存为</el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="保存" placement="bottom">
              <el-button icon="el-icon-folder-checked" type="primary" plain :loading="saveLoading" @click="handleSave(true)">{{ saveBtnText }}</el-button>
            </el-tooltip>
          </el-button-group>
          <div :style="'height:' + mainHeight+'px'">
            <codeEditor :ref="'codeEditor'+item.id" :code_content="item.content" :readonly="false" @change="handleCodeChange" />
          </div>
          <div :style="'border:1px solid #dfe4ed;height:'+footerHeight+'px;'">
            <el-tabs type="card" @tab-click="openFooter">
              <el-tab-pane style="padding: 0px">
                <span slot="label"><i class="el-icon-warning-outline" /> 信息</span>
                <div style="height:307px;">
                  <el-input v-model="footerMessage" type="textarea" readonly="readonly" :rows="15" />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-main>
  </el-container>
</template>

<script>
import codeEditor from '@/components/CodeEditor/CodeEditor'
import { selSubStepTree } from '@/api/core/flow/rcsFlowModMain'
import { sel as selM } from '@/api/core/flow/rcsFlowModFunctionM'
import { sel, add, edit, saveAs } from '@/api/core/flow/rcsFlowModFunctionI'
import Cookies from 'js-cookie'
export default {
  name: 'Coding',
  components: { codeEditor },
  props: {
    flow_mod_main_id: {
      type: [String, Number],
      default: -1
    },
    step_mod_id: {
      type: [String, Number],
      default: -1
    },
    step_mod_des: {
      type: String,
      default: ''
    },
    step_mod_function_dll: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      timer: '',
      mainHeight: document.documentElement.clientHeight - 141,
      footerHeight: 35,
      footerMessage: '',
      footerIsOpened: false,
      loading: false,
      codeChange: false,
      saveLoading: false,
      saveBtnText: '保存',
      filterText: '',
      treeData: [],
      treeDefaultExpandedKeys: [],
      treeCurrentNodeKey: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      tabsValue: '0',
      tabsData: [],
      templateChooseDialog: false,
      functionMCode: '',
      functionMName: '',
      functionMContent: '',
      functionMData: [],
      functionMPageData: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      codeDialogVisible: false,
      saveAsDialog: false,
      saveAsTitle: '',
      form: {
        user_name: Cookies.get('userName'),
        function_i_id: '0',
        function_m_code: '',
        function_i_code: '',
        function_i_name: '',
        function_i_des: '',
        function_i_content: '',
        step_mod_id: ''
      },
      rules: {
        // 提交验证规则
        function_m_code: [{ required: true, message: '请输入模板编码', trigger: 'blur' }],
        function_i_code: [{ required: true, message: '请输入函数编码', trigger: 'blur' }],
        function_i_name: [{ required: true, message: '请输入函数名称', trigger: 'blur' }],
        function_i_des: [{ required: true, message: '请输入函数描述', trigger: 'blur' }]
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    const that = this
    var dom = document.getElementById('selectTrees')
    dom.style.height = (window.innerHeight - 130) + 'px'
    window.onresize = function temp() {
      dom.style.height = (window.innerHeight - 140) + 'px'
      that.height = document.documentElement.clientHeight - 600

      that.mainHeight = document.documentElement.clientHeight - (106 + that.footerHeight)
    }
    this.timer = setInterval(this.VerifySave, 5000)
  },
  created: function() {
    selSubStepTree({
      userID: Cookies.get('userName'),
      flow_mod_main_id: this.flow_mod_main_id
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.treeData = JSON.parse(defaultQuery.result)
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
      // 处理从step表单页面调整过来时，直接打开对应的代码
    if (this.step_mod_id !== -1 && this.step_mod_id !== '') {
      this.treeDefaultExpandedKeys.push(this.step_mod_id.toString())
      this.treeCurrentNodeKey = this.step_mod_id.toString()
      this.handleNodeClick({ id: this.step_mod_id, label: this.step_mod_des, level: 2, step_mod_function_dll: this.step_mod_function_dll })
    }
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    // 树形节点查询
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 自定义树形展示，添加小图标
    handleTreeRenderContent(h, { node, data, store }) {
      if (node.level === 1) {
        return (
          <span class='custom-tree-node'>
            <img class='icon-c1'></img>
            <span>
              {node.label}
            </span>
          </span>
        )
      } else {
        return (
          <span class='custom-tree-node'>
            <img class='icon-c2'></img>
            <span>
              {node.label}
            </span>
          </span>
        )
      }
    },
    // 点击树形节点代码加载数据
    handleNodeClick(data) {
      if (data.level === 1) return
      if (this.tabsData.filter(item => item.id === data.id.toString()).length > 0) {
        this.tabsValue = data.id.toString()
        return
      } else {
        this.tabsData.push({
          id: data.id.toString(),
          title: data.label,
          content: '',
          newContent: '',
          function_i_id: '0',
          function_m_code: '',
          function_i_code: data.id.toString(),
          function_i_name: data.step_mod_function_dll,
          function_i_des: data.label,
          function_i_status: ''
        })
        this.tabsValue = data.id.toString()
      }
      this.currentNode = data
      this.loading = true
      const query = {
        step_mod_id: data.id
      }
      sel(query)
        .then(res => {
          this.loading = false
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var item = this.tabsData.filter(item => item.id === data.id.toString())[0]
              item.content = defaultQuery.data[0].function_i_content
              item.newContent = defaultQuery.data[0].function_i_content
              item.function_i_id = defaultQuery.data[0].function_i_id.toString()
              item.function_m_code = defaultQuery.data[0].function_m_code
              item.function_i_code = defaultQuery.data[0].function_i_code
              item.function_i_name = defaultQuery.data[0].function_i_name
              item.function_i_des = defaultQuery.data[0].function_i_des
              item.function_i_status = defaultQuery.data[0].function_i_status
            }
          } else {
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.loading = false
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 编辑代码发生改变，保存最新代码，并标识代码已改变
    handleCodeChange(content) {
      const item = this.tabsData.filter(item => item.id === this.tabsValue)[0]
      if (item.newContent.length !== content.length) {
        item.newContent = content
        this.codeChange = true
      }
    },
    // 关闭tabs，删除对应的数据
    handleTabsClose(name) {
      if (this.tabsData.length === 1) {
        this.tabsValue = '0'
        this.tabsData.splice(0, 1)
      } else {
        const index = this.tabsData.findIndex((item) => item.id === name)
        if (index < this.tabsData.length - 1) {
          this.tabsValue = this.tabsData[index + 1].id.toString()
        } else {
          this.tabsValue = this.tabsData[this.tabsData.length - 2].id.toString()
        }
        this.tabsData.splice(index, 1)
      }
    },
    // 查询模板
    queryTemplate() {
      selM({
        userID: Cookies.get('userName'),
        function_m_code: this.functionMCode,
        function_m_name: this.functionMName,
        sort: 'function_m_id',
        page: this.functionMPageData.page,
        size: this.functionMPageData.size

      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.count > 0) {
              this.functionMData = defaultQuery.data
              this.functionMPageData.total = defaultQuery.count
            } else {
              this.functionMData = []
              this.functionMPageData.total = 0
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // Page:分页
    paginationSizeChange(val) {
      // 查询
      this.queryTemplate()
    },
    paginationCurrentChange(val) {
      // 查询
      this.queryTemplate()
    },
    // 打开查看代码
    openCode(row) {
      this.functionMContent = row.function_m_content
      this.codeDialogVisible = true
    },
    // 处理选择模板
    handleTemplateChoose(row) {
      const item = this.tabsData.filter(item => item.id === this.tabsValue)[0]
      item.content = row.function_m_content
      item.function_m_code = row.function_m_code
      this.templateChooseDialog = false
    },
    // 校验代码
    handleCheck() {},
    // 校验是否执行保存事件
    VerifySave() {
      if (!this.codeChange) return
      this.codeChange = false
      this.handleSave(false)
    },
    // 保存代码
    handleSave(isShow) {
      this.saveLoading = true
      this.saveBtnText = '保存中'
      const item = this.tabsData.filter(item => item.id === this.tabsValue)[0]
      const saveData = {
        user_name: Cookies.get('userName'),
        function_i_id: item.function_i_id,
        function_m_code: item.function_m_code,
        function_i_code: item.function_i_code,
        function_i_name: item.function_i_name,
        function_i_des: item.function_i_des,
        function_i_content: item.newContent,
        function_i_status: item.function_i_status,
        step_mod_id: item.id
      }
      if (saveData.function_i_id.toString() === '0') {
        add(saveData)
          .then(res => {
            this.saveLoading = false
            this.saveBtnText = '保存'
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              item.function_i_id = defaultQuery.result
              this.handleNotify(isShow, item.title, '代码保存成功', 'success')
            } else if (defaultQuery.code === -1) {
              this.handleNotify(isShow, item.title, defaultQuery.msg, 'warning')
            }
          })
          .catch(() => {
            this.saveLoading = false
            this.saveBtnText = '保存'
            this.handleNotify(isShow, item.title, '保存异常', 'warning')
          })
      } else {
        edit(saveData)
          .then(res => {
            this.saveLoading = false
            this.saveBtnText = '保存'
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.handleNotify(isShow, item.title, '代码保存成功', 'success')
            } else if (defaultQuery.code === -1) {
              this.handleNotify(isShow, item.title, defaultQuery.msg, 'warning')
            }
          })
          .catch(() => {
            this.saveLoading = false
            this.saveBtnText = '保存'
            this.handleNotify(isShow, item.title, '保存异常', 'warning')
          })
      }
    },
    // 处理提示框
    handleNotify(isShow, title, message, type) {
      if (isShow) {
        this.$notify({ title: title, message: message, type: type, position: 'bottom-right', customClass: 'notifyCustom' })
      }
    },
    // 发布
    handleRelease() {},
    // 打开另存为模板操作窗口
    openSaveAsDialog() {
      const item = this.tabsData.filter(item => item.id === this.tabsValue)[0]
      this.form.function_i_id = item.function_i_id
      this.form.function_m_code = item.function_m_code
      this.form.function_i_code = item.function_i_code
      this.form.function_i_name = item.function_i_name
      this.form.function_i_des = item.function_i_des
      this.form.function_i_content = item.newContent
      this.form.step_mod_id = item.id
      this.saveAsTitle = item.title
      this.saveAsDialog = true
    },
    // 存为模板操作窗口关闭后清除验证提示
    saveAsDialogClose() {
      this.$refs['form'].resetFields()
    },
    // 处理另存为模板
    handleSaveAs() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          saveAs(this.form)
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.saveAsDialog = false
                this.handleNotify(true, this.saveAsTitle + '另存为模板', '保存成功', 'success')
                const item = this.tabsData.filter(item => item.id === this.tabsValue)[0]
                item.function_i_id = defaultQuery.result
                item.function_m_code = this.form.function_m_code
                item.function_i_code = this.form.function_i_code
                item.function_i_name = this.form.function_i_name
                item.function_i_des = this.form.function_i_des
              } else if (defaultQuery.code === -1) {
                this.handleNotify(true, this.saveAsTitle + '另存为模板', defaultQuery.msg, 'warning')
              }
            })
            .catch(() => {
              this.handleNotify(true, this.saveAsTitle + '另存为模板', '保存异常', 'warning')
            })
        }
      })
    },
    // 处理打开关闭页脚消息显示
    openFooter() {
      if (this.footerIsOpened) {
        this.footerIsOpened = false
        this.footerHeight = 35
      } else {
        this.footerIsOpened = true
        this.footerHeight = 350
      }
      this.mainHeight = document.documentElement.clientHeight - (106 + this.footerHeight)
    }
  }
}
</script>
<style lang="less" scoped>
.el-main{
    padding: 0px;
}
.el-aside {
    margin-bottom: 0px;
    padding: 10px;
}
.filter-tree{
    margin-top: 10px;
    overflow: auto;
    height: 100%;
}
#selectTrees{
    overflow: hidden;
}
.el-tree::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
}
.el-tree::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #f6f9ff;
    cursor: pointer !important;
}
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
::v-deep .el-textarea__inner {
  color: #BBBBBB;
  background-color: #2B2B2B;
}
</style>
<style>
#codeingContainer .el-tabs{
    margin-bottom: 0px;
}
#codeingContainer .el-tabs__header{
    margin: 0px 0px 0px 0px;
}
.icon-c1 {
  background-size: 100%;
  background-image: url('~@/assets/images/flow/c1.png');
  width: 15px;
  height: 15px;
  margin-right: 5px;
}

.icon-c2{
  background-size: 100%;
  background-image: url('~@/assets/images/flow/c2.png');
  width: 15px;
  height: 15px;
  margin-right: 5px;
}
.notifyCustom{
    background-color: #D7DEE9;
}
</style>
