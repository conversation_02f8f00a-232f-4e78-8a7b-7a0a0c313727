import request from '@/utils/request'
// 查询配方维护信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sf/recipe/SfPcRecipeSel',
    method: 'post',
    data
  })
}
// 新增配方维护信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sf/recipe/SfPcRecipeIns',
    method: 'post',
    data
  })
}
// 修改配方维护信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sf/recipe/SfPcRecipeUpd',
    method: 'post',
    data
  })
}
// 删除配方维护信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sf/recipe/SfPcRecipeDel',
    method: 'post',
    data
  })
}

// 查询配方维护信息
export function selByLotOrMaterial(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sf/recipe/SfPcRecipeDetailSelByLotOrMaterial',
    method: 'post',
    data
  })
}

// 根据订单号查询(昆山护士)
export function getWorkOrderInfo(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sf/recipe/SfHsGetWorkOrderInfo',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, selByLotOrMaterial,getWorkOrderInfo }

