<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="'Dummy码：'">
                <el-input v-model="query.dummy_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.time')+'：'">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :format="$t('view.form.timePeriodStart')"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:100%"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button type="primary" :disabled="crud.data.length <= 0" @click="resetDummyAll">{{ '复位使用次数' }}</el-button>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column v-if="false" :show-overflow-tooltip="true" prop="dummy_id" :label="'dummy_id'" />
            <el-table-column :show-overflow-tooltip="true" prop="item_date" :label="'日期'" />
            <el-table-column :show-overflow-tooltip="true" prop="dummy_code" :label="'Dummy码'" />
            <el-table-column :show-overflow-tooltip="true" prop="cur_usage_count" :label="'当前使用次数'" />
            <el-table-column :show-overflow-tooltip="true" prop="max_usage_count" :label="'最大使用次数'" />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="resetDummy(scope.row)">{{ '复位使用次数' }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >{{ $t('view.pagination.total') + '：' }}{{ page.total }}</el-button>
              <el-button
                type="primary"
              >{{ $t('view.pagination.current') }}{{ nowPageIndex }}{{ $t('view.pagination.unit') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;{{ $t('view.pagination.previous') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >{{ $t('view.pagination.next') }}&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import crudEapApsDummyReport from '@/api/eap/project/gx/eapApsDummyReport'
const defaultForm = {

}
export default {
  name: 'dummyreport',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: 'Dummy报表',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'dummy_num',
      // // 排序
      sort: ['dummy_num desc'],
      // CRUD Method
      crudMethod: { ...crudEapApsDummyReport },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      },
      query: {
        tableSize: 10
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      pnlDrawerVisible: false,
      flowDrawerVisible: false,
      dummy_id: '',
      height: document.documentElement.clientHeight - 190,
      permission: {
        add: ['admin', 'plan_report:add'],
        edit: ['admin', 'plan_report:edit'],
        del: ['admin', 'plan_report:del'],
        down: ['admin', 'plan_report:down']
      }
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 190
    }
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('view.dialog.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('view.dialog.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    resetDummy(row) {
      this.$confirm('确定复位当前dummy的使用次数？', '提示', {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          crudEapApsDummyReport.resetDummy({
            dummy_id: row.dummy_id
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.deleteSuccesful'), type: 'success' })
                this.toQuery()
              }
            })
            .catch((ex) => {
              this.$message({
                message: this.$t('lang_pack.vie.operationException') + ex,
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    resetDummyAll() {
      this.$confirm('确定复位所有超过最大使用次数dummy的使用次数？', '提示', {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          crudEapApsDummyReport.resetDummy({
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.deleteSuccesful'), type: 'success' })
                this.toQuery()
              }
            })
            .catch((ex) => {
              this.$message({
                message: this.$t('lang_pack.vie.operationException') + ex,
                type: 'error'
              })
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
  color: #fff;
  background-color: #1473c5;
  border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
  background: #438fd1;
  border-color: #438fd1;
  color: #fff;
}
.labelIline {
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label {
    white-space: nowrap;
  }
}
::v-deep .wrapElFormFirst {
  .el-form-item__label {
    white-space: nowrap;
    margin: 0;
    width: 80px;
  }
}
::v-deep .el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 235px;
}
::v-deep .el-date-editor .el-range-separator {
  width: 12%;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  width: 130px;
}
</style>
