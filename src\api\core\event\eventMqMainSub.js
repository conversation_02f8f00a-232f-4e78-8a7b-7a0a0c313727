import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventMqMainSubSel',
    method: 'post',
    data
  })
}

export function listSel(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventMqMainSubList',
    method: 'post',
    data
  })
}

// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventMqMainSubIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventMqMainSubUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreEventMqMainSubDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

