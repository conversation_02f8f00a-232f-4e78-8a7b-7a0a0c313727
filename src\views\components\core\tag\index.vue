<template>
  <div>
    <el-input v-model="tableTagQuery.tag_des" size="mini" :placeholder="$t('lang_pack.monitor.labelCode') + '/' + $t('lang_pack.monitor.labelDescription')" style="width: 130px" class="filter-item" />
    <el-input v-model="tableTagQuery.client_des" size="mini" :placeholder="$t('view.table.instanceDescription')" style="width: 100px" class="filter-item" />
    <el-input v-model="tableTagQuery.tag_group_des" size="mini" :placeholder="$t('lang_pack.monitor.labelGroups')" style="width: 100px" class="filter-item" />
    <el-input v-model="tableTagQuery.tag_id" size="mini" :placeholder="$t('view.table.tagID')" style="width: 100px" class="filter-item" />
    <el-button class="filter-item flRight" size="mini" icon="el-icon-refresh-left" @click="toButResetQuery">{{ $t('lang_pack.scadaAlarmReport.reset') }}</el-button>
    <el-button class="filter-item flRight serachStyle" size="mini" type="primary" icon="el-icon-search" @click="toButtableTagQuery(1)">{{ $t('lang_pack.scadaAlarmReport.search') }}</el-button>
    <el-table ref="tableTag" v-loading="tableTagLoading" border class="elTableStyle" :data="tagData" style="width: 100%" :stripe="true" :highlight-current-row="true" height="460px" @header-dragend="crud.tableHeaderDragend()">
      <el-table-column :label="$t('lang_pack.commonPage.operate')" width="70" align="center" fixed="left">
        <template slot-scope="scope">
          <el-link class="linkItem" type="primary" @click="chooseTag(scope.row)">{{ $t('lang_pack.vie.select') }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="tag_code" width="150" :label="$t('lang_pack.monitor.labelCode')" />
      <el-table-column :show-overflow-tooltip="true" prop="tag_des" width="200" :label="$t('lang_pack.monitor.labelDescription')" />
      <el-table-column :show-overflow-tooltip="true" prop="client_des" width="200" :label="$t('view.table.instanceDescription')" />
      <el-table-column :show-overflow-tooltip="true" prop="tag_group_des" width="200" :label="$t('lang_pack.monitor.labelGroups')" />
    </el-table>
    <el-pagination
      :page-size.sync="tagTablePage.size"
      :total="tagTablePage.total"
      :current-page.sync="tagTablePage.page"
      :page-sizes="[10, 30, 50]"
      style="margin-top: 8px; float: right"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChangeHandler($event)"
      @current-change="currentChangeHandler"
    />
  </div>
</template>
<script>
import { scadaTagList } from '@/api/core/scada/tag'

// import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  props: {
    clientIdList: {
      type: [Array, String],
      default: ''
    },
    tagId: {
      type: [String, Number],
      default: -1
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',

      tagTablePage: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 10,
        // 总数据条数
        total: 0
      },
      tableTagQuery: {
        client_des: '',
        tag_group_des: '',
        tag_id: '',
        tag_des: '',
        tableOrder: '', // 排序方式asc,desc
        tableOrderField: '' // 排序字段
      },
      tagData: [],
      tableTagLoading: false,
      tagConfigVisible: false
    }
  },
  watch: {
    clientIdList: {
      immediate: true,
      deep: true,
      handler() {
        this.tableTagQuery.client_des = ''
        this.tableTagQuery.tag_group_des = ''
        this.tableTagQuery.tag_des = ''
        this.tableTagQuery.tag_id = ''
        this.tagData = []
      }
    },
    tagId: {
      immediate: true,
      deep: true,
      handler() {
        if (this.tagId.length <= 0) {
          this.tableTagQuery.client_des = ''
          this.tableTagQuery.tag_group_des = ''
          this.tableTagQuery.tag_des = ''
          this.tableTagQuery.tag_id = ''
          this.tagData = []
        } else {
          this.tableTagQuery.client_des = ''
          this.tableTagQuery.tag_group_des = ''
          this.tableTagQuery.tag_des = ''
          this.tableTagQuery.tag_id = this.tagId
          this.toButtableTagQuery(1)
        }
      }
    }
  },

  mounted: function() {},
  created: function() {},
  methods: {
    chooseTag(data) {
      this.$emit('chooseTag', data.tag_id, data.tag_des)
      this.$emit('handleChooseTag', data.client_code + '/' + data.tag_group_code + '/' + data.tag_code, data.cell_id, data.cell_mqtt_port, data.tag_id)
    },
    toButResetQuery() {
      this.tableTagQuery.client_des = ''
      this.tableTagQuery.tag_group_des = ''
      this.tableTagQuery.tag_des = ''
      this.tableTagQuery.tag_id = ''
      this.tagTablePage.page = 1
      this.toButtableTagQuery(1)
    },
    toButtableTagQuery(page) {
      var query = {
        client_id_list: this.clientIdList,
        client_des: this.tableTagQuery.client_des,
        tag_group_des: this.tableTagQuery.tag_group_des,
        tag_id: this.tableTagQuery.tag_id,
        tag_des: this.tableTagQuery.tag_des,
        tableOrder: this.tableTagQuery.tableOrder, // 排序方式asc,desc
        tableOrderField: this.tableTagQuery.tableOrderField, // 排序字段
        tablePage: page, // 当前页
        tableSize: this.tagTablePage.size // 每页数据条数
      }
      this.tableTagLoading = true
      scadaTagList(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.count > 0) {
            this.tagData = defaultQuery.data
          } else {
            this.tagData = []
          }
          this.tagTablePage.total = defaultQuery.count
          this.tableTagLoading = false
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
          this.tagData = []
          this.tableTagLoading = false
        })
    },
    // Page:分页
    sizeChangeHandler(size) {
      // 查询
      this.tagTablePage.size = size
      // 当出现  当前页  *  条数 > 总条数   的情况时不调用接口
      if (this.tagTablePage.page * size > this.tagTablePage.total) return
      this.toButtableTagQuery(this.tagTablePage.page)
    },
    currentChangeHandler(page) {
      // 查询
      this.toButtableTagQuery(page)
    }
  }
}
</script>
<style lang="less" scoped>
.elTableStyle {
  margin-top: 15px;
}
.flRight {
  float: right;
}
.serachStyle {
  margin-right: 10px;
}
</style>
