import request from '@/utils/request'

// 模组范围约束模板查询
export function mesRecipePackLimitMustSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackLimitMustSel',
    method: 'post',
    data
  })
}

// 模组范围约束模板修改
export function mesRecipePackLimitMustUpd(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackLimitMustUpd',
    method: 'post',
    data
  })
}

// 模组范围约束模板修改
export function mesRecipePackLimitMustEnableFlagUpd(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipePackLimitMustEnableFlagUpd',
    method: 'post',
    data
  })
}

export default { mesRecipePackLimitMustSel, mesRecipePackLimitMustUpd, mesRecipePackLimitMustEnableFlagUpd }
