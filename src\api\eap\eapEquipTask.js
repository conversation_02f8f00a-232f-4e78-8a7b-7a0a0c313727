import request from '@/utils/request'
// 查询设备任务
export function eapEquipTask(data) {
  return request({
    url: 'aisEsbWeb/eap/project/cj/EapApsPlanStationMainEquipTaskSelect',
    method: 'post',
    data
  })
}
// 获取工单信息
export function EapApsPlanCurrentSel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/cj/plan/EapApsPlanCurrentSel',
    method: 'post',
    data
  })
}
export default { eapEquipTask, EapApsPlanCurrentSel }
