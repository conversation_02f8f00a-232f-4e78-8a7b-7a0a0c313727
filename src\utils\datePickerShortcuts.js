/**
 * Date picker shortcuts utility
 * Provides a reusable function to create date picker shortcuts with internationalization
 */

/**
 * Create date picker shortcuts with internationalization
 * @param {Object} i18n - Vue i18n instance
 * @returns {Object} - Picker options with shortcuts
 */
export function createDatePickerShortcuts(i18n) {
  return {
    shortcuts: [
      {
        text: i18n.t('lang_pack.dateTimePicker.last10Minutes'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 10 * 60 * 1000)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.last30Minutes'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 30 * 60 * 1000)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.lastHour'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.lastDay'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.lastWeek'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.last30Days'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.last90Days'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: i18n.t('lang_pack.dateTimePicker.lastYear'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
          picker.$emit('pick', [start, end])
        }
      }
    ]
  }
}
