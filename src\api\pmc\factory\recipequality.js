import request from '@/utils/request'

// 查询工位数据采集定义
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcRecipeQualitySel',
    method: 'post',
    data
  })
}
// 新增工位数据采集定义
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcRecipeQualityIns',
    method: 'post',
    data
  })
}
// 修改工位数据采集定义
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcRecipeQualityUpd',
    method: 'post',
    data
  })
}
// 删除工位数据采集定义
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcRecipeQualityDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

