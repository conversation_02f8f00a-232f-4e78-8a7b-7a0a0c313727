<template>
  <div id="#bigScreen" class="mesContainer">
    <div class="box-card-image">
      <img src="@/assets/images/sh/image.png" style="width: 100%;height: 700px;margin-top: 10px;" alt="">
      <div v-for="(item,index) in alarmData" :key="index" class="box-card-point">
        <div
          v-for="(temp,i) in item"
          :key="i"
          class="box-card-box"
          :style="{top:temp.x + '%',left:temp.y + '%',background: temp.tags.tag_value === '0' ? '#00b050' : '#ff0000'}"
        />
      </div>
    </div>
    <div style="width: 100%;margin-top: 10px;height: 100%;background-color: #343d6b;">
      <div class="table-head">
        <!-- <table>
          <thead>
            <tr>
              <th>工位</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>1</td></tr>
            <tr><td>1</td></tr>
            <tr><td>1</td></tr>
            <tr><td>1</td></tr>
            <tr><td>1</td></tr>
            <tr><td>1</td></tr>
            <tr><td>1</td></tr>
          </tbody>
        </table>
        <table>
          <thead>
            <tr>
              <th>工位</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>2</td></tr>
            <tr><td>2</td></tr>
            <tr><td>3</td></tr>
            <tr><td>4</td></tr>
          </tbody>
        </table>
        <table>
          <thead>
            <tr>
              <th>超时</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>2</td></tr>
            <tr><td>2</td></tr>
            <tr><td>3</td></tr>
            <tr><td>4</td></tr>
          </tbody>
        </table>
        <table>
          <thead>
            <tr>
              <th>报警</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>2</td></tr>
            <tr><td>2</td></tr>
            <tr><td>3</td></tr>
            <tr><td>4</td></tr>
          </tbody>
        </table> -->
        <div class="tab-box bor-right">
          <div class="headerTitle">欠料</div>
          <div v-for="(item,index) in alarmHistroyData.itemList1" :key="index" class="tab-content bor-right">{{ item.station_code }}</div>
        </div>
        <div class="tab-box bor-right">
          <div class="headerTitle">呼叫</div>
          <div v-for="(item,index) in alarmHistroyData.itemList2" :key="index" class="tab-content bor-right">{{ item.station_code }}</div>
        </div>
        <div class="tab-box bor-right">
          <div class="headerTitle">超时</div>
          <div v-for="(item,index) in alarmHistroyData.itemList3" :key="index" class="tab-content bor-right">{{ item.station_code }}</div>
        </div>
        <div class="tab-box">
          <div class="headerTitle">故障</div>
          <div v-for="(item,index) in alarmHistroyData.itemList4" :key="index" class="tab-content bor-right">{{ item.station_code }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import alarmApi from '@/api/mes/project/sh/shAlarmHistroy'
import Paho from 'paho-mqtt'
import Cookies from 'js-cookie'
import { CoreScadaReadTag } from '@/api/hmi/mainIndex'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import autofit from 'autofit.js'
export default {
  name: 'SH_ALARM_DASHBOARD',
  data() {
    return {
      alarmData: [],
      clients: {},
      clientPorts: {},
      tagKeysMap: {},
      alarmHistroyData: {
        itemList1: [],
        itemList2: [],
        itemList3: [],
        itemList4: []
      },
      timerValue: 0,
      timer: null,
      timerCode: null
    }
  },
  created() {
    fetch('/static/data/sh_alarm_dashboard_data.json?v=' + new Date().getTime()) // 从 public/static/data 目录下请求 JSON 文件
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        return response.json() // 解析 JSON 数据
      })
      .then(data => {
        this.alarmData = data // 将解析后的数据赋值给组件的属性
        this.init()
      })
      .catch(error => {
        console.error('There has been a problem with your fetch operation:', error)
      })
    this.refresAlarmHistroy()
    this.timer = setInterval(() => {
      this.refresAlarmHistroy()
    }, 1000 * 3)
    // this.timerCode = setInterval(() => {
    //   this.init()
    //   console.log('刷新')
    // }, 1000 * this.getShTime())
  },
  dicts: ['SH_WARING_CODE'],
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    // 获取系统参数信息
    var query = {
      userName: Cookies.get('userName'),
      parameter_code: 'SH_WARING_CODE',
      enable_flag: 'Y'
    }
    selSysParameter(query)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.timerValue = defaultQuery.data[0].parameter_val
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  beforeDestroy() {
    autofit.off()
    clearInterval(this.timer)
    clearInterval(this.timerCode)
  },
  methods: {
    writeScadaValue(tag_key, tag_value) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + tag_key.split('/')[0]
      this.mqttClient.send(topic, sendStr)
    },
    init() {
      this.alarmData.forEach(temp => {
        temp.forEach(item => {
          const mqttPort = item.ports.mqtt
          const cellPort = item.ports.cell
          if (this.clientPorts[mqttPort] == null) {
            this.clientPorts[mqttPort] = true
          }
          if (this.tagKeysMap[cellPort] == null) {
            this.tagKeysMap[cellPort] = []
          }
          this.tagKeysMap[cellPort].push({ 'tag_key': item.tags.tag_key })
          item.alarmGruop && item.alarmGruop.forEach(e => {
            this.tagKeysMap[cellPort].push({ 'tag_key': e.tag_key })
          })
        })
      })
      const host = window.location.hostname

      const poll = () => {
        for (const key in this.tagKeysMap) {
          const cellUrl = `http://${host}:${key}/`
          CoreScadaReadTag(cellUrl, this.tagKeysMap[key])
            .then(res => {
              if (res.code === 0 && res.data) {
                for (const i in res.data) {
                  const item = res.data[i]
                  const k = item.tag_key
                  const v = item.tag_value
                  this.setContent(k, v, false)
                }
              }
            })
        }
      }
      poll()
      this.timerCode = setInterval(() => {
        poll()
      }, 1000 * (parseInt(this.timerValue) > 0 ? parseInt(this.timerValue) : 5))
      for (const key in this.clientPorts) {
        const port = parseInt(key)
        this.connectMQTT(host, port, (c) => {
          this.clients[key] = c
          // 订阅点位
          Object.keys(this.tagKeysMap).forEach(item => {
            this.tagKeysMap[item].forEach(t => {
              c.subscribe(`SCADA_CHANGE/${t.tag_key}`, {
                onSuccess: () => {
                  // console.debug('Subscribe success.：', t.tag_key)
                },
                onFailure: (responseObject) => {
                  console.error('Subscribe fail:', responseObject.errorMessage)
                }
              })
            })
          })
        })
      }
    },
    setContent(k, v, alarmFlag) {
      if (v === undefined || v === null) return
      this.alarmData.forEach(temp => {
        temp.forEach(item => {
          if (item.tags.tag_key === k) {
            item.tags.tag_value = v
          }
          if (k.includes('/PlcAlarm/PlcA_AlarmBit') && item.alarmGruop) {
            const alarm = item.alarmGruop.find(e => e.tag_key === k)
            alarm && (alarm.tag_value = v)
            if (alarmFlag && alarm !== undefined) {
              if (alarm.tag_value === '0') {
                this.updateAlarmHistory(alarm.alarm_value, alarm.alarm_desc, alarm.tag_des)
              }
              if (alarm.tag_value === '1' && alarmFlag) {
                setTimeout(() => {
                  this.saveAlarmHistory(alarm.alarm_value, alarm.alarm_desc, alarm.tag_des)
                }, 1000)
              }
            }
          }
        })
      })
    },
    getTopic(tagKey) {
      return `SCADA_CHANGE/${tagKey}`
    },
    refresAlarmHistroy() {
      const query = {
        page: 1,
        size: 200,
        enable_flag: 'Y'
      }
      alarmApi.sel(query).then(res => {
        if (res.code === 0) {
          if (res.result !== '') {
            const data = JSON.parse(res.result)
            this.alarmHistroyData = data
            return
          }
          this.alarmHistroyData = { itemList1: [], itemList2: [], itemList3: [], itemList4: [] }
        }
      }).catch(error => {
        this.alarmHistroyData = { itemList1: [], itemList2: [], itemList3: [], itemList4: [] }
        console.error('获取失败' + error.msg)
      })
    },
    saveAlarmHistory(alarm_value, alarm_desc, station_code) {
      alarmApi.add({
        alarm_value,
        alarm_desc,
        station_code
      }).catch(err => {
        console.error('Save alarm history fail:', err)
      })
    },
    updateAlarmHistory(alarm_value, alarm_desc, station_code) {
      const query = {
        alarm_value,
        alarm_desc,
        station_code
      }
      alarmApi.edit(query).catch(err => {
        console.error('Save alarm history fail:', err)
      })
    },
    connectMQTT(host, port, onConnected) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const payload = message.payloadString
        const data = JSON.parse(payload)
        console.log(data)
        if (data.TagKey && data.TagNewValue && data.TagNewValue !== '') {
          this.setContent(data.TagKey, data.TagNewValue, true)
        } else {
          console.error('Invalid data:', payload)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }
}
</script>
<style lang="less" scoped>
.mesContainer{
    background: #2B304D;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transform-origin: 0 0;
    padding: 0 15px;
    .header{
        width: 100%;
        height: 105px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
            font-size: 48px;
            color: #fff;
            font-weight: 600;
        }
    }
    .box-card-image{
        .box-card-point{
            .box-card-box{
                position: absolute;
                height: 50px;
                width: 50px;
                border-radius: 50%;
                font-size: 22px;
            }
        }
    }
    .table-head {
        display: flex;
        .tab-box{
          width: 25%;
          border: 1px solid #fff;
          height: 90px;
          line-height: 90px;
          font-size: 75px;
          text-align: center;
          color:#fff;
          font-weight: 600;
          .tab-content{
            border: 1px solid #fff;
            height: 90px;
            line-height: 90px;
            font-size: 75px;
            text-align: center;
            color:#fff;
            border-top: none;
            background: red;
          }
        }
        .bor-right{
          border-right: none;
        }
    }
  //   .table-body {
  //       width: 100%;
  //   }
    .table-head table,
    .table-body table {
        width: 25%;
        border-collapse: collapse;
    }
    table tr,th,td {
        // height: 40px;
        // line-height: 40px;
        text-align: center;
        border: 1px solid #c0bebe;
        color: #808080;
    }
  //   th{color: #fff;}
  //   .table-body::-webkit-scrollbar {
  //       width: 4px;
  //       /*height: 4px;*/
  //   }
  //   .table-body::-webkit-scrollbar-thumb {
  //       border-radius: 10px;
  //       -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  //       background: rgba(0, 0, 0, 0.2);
  //   }
  //   .table-body::-webkit-scrollbar-track {
  //       -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  //       border-radius: 0;
  //       background: rgba(0, 0, 0, 0.1);
  //   }
  //   .scrollFormWarp::-webkit-scrollbar {
  //     width: 0px;
  //     height: 0px;
  //     background-color: #1ee4f6;
  //     cursor: pointer !important;
  //   }

  // .scrollFormWarp::-webkit-scrollbar-thumb {
  //   box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  //   -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  //   background-color: #f6f9ff;
  //   cursor: pointer !important;
  // }
}
</style>
