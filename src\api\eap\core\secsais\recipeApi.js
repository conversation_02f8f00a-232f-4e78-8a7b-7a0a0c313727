import request from '@/utils/request'
import Cookies from 'js-cookie'

/**
 * 查询配方模型数据
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function queryRecipeModels(params) {
  // 确保包含默认参数
  const defaultParams = {
    user_name: Cookies.get('userName'),
    sort: 'tag_group_id asc'
  }

  // 确保包含station_code参数
  if (!params.station_code) {
    console.warn('EapRecipeModelQuery: station_code参数缺失')
  }

  return request({
    url: 'aisEsbWeb/eap/core/recipe/secsais/EapRecipeModelQuery',
    method: 'post',
    data: { ...defaultParams, ...params }
  })
}
/**
 * 查询配方详情
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function queryRecipeDetails(params) {
  // 确保包含默认参数
  const defaultParams = {
    user_name: Cookies.get('userName')
  }

  // 确保包含station_code参数
  if (!params.station_code) {
    console.warn('EapRecipeInfoQuery: station_code参数缺失')
  }

  return request({
    url: 'aisEsbWeb/eap/core/recipe/secsais/EapRecipeInfoQuery',
    method: 'post',
    data: { ...defaultParams, ...params }
  })
}

export default {
  queryRecipeDetails,
  queryRecipeModels
}
