<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!-- 料号 -->
            <!-- <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div> -->
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="参数描述" align="center" prop="desc" />
            <el-table-column :show-overflow-tooltip="true" label="参数编码" width="200" align="center" prop="code" />
            <el-table-column :show-overflow-tooltip="true" label="参数值" align="center" prop="value" />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
        <el-form-item label="参数">
          <fastCode fastcode_group_code="EXTRA_PARAM" :fastcode_code.sync="form.code" :fastcode_des.sync="form.desc" control_type="select" size="small" />
        </el-form-item>
        <el-form-item label="值" prop="value">
          <fastCode v-if="form.code === 'tinkering'" fastcode_group_code="EXTRA_PARAM_TINKERING" :fastcode_code.sync="form.value" control_type="select" size="small" />
          <fastCode v-else-if="form.code === 'label_type'" fastcode_group_code="EXTRA_PARAM_LABEL_TYPE" :fastcode_code.sync="form.value" control_type="select" size="small" />
          <fastCode v-else-if="form.code === 'cg_type'" fastcode_group_code="EXTRA_PARAM_CGType" :fastcode_code.sync="form.value" control_type="select" size="small" />
          <fastCode v-else-if="form.code === 'cg_count'" fastcode_group_code="EXTRA_PARAM_CGCount" :fastcode_code.sync="form.value" control_type="select" size="small" />
          <el-input v-else v-model="form.value" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确认 -->
      </div>
    </el-drawer>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span style="font-size: 24px;color: red;">请修改重工次数与重工类型!!</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确定关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api/pack/core/extra'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = {
  code: '',
  desc: '',
  value: '',
  enable_flag: 'Y'
}

export default {
  name: 'PACK_PROJ_TRIPOD_EXTRA',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.parkExtra'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG', 'EXTRA_PARAM', 'EXTRA_PARAM_TINKERING', 'EXTRA_PARAM_LABEL_TYPE', 'EXTRA_PARAM_CGType', 'EXTRA_PARAM_CGCount'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'pack_extra:add'],
        edit: ['admin', 'pack_extra:edit'],
        del: ['admin', 'pack_extra:del']
      },
      form: {
        code: '',
        desc: '',
        value: '',
        enable_flag: 'Y'
      },
      rules: {
        code: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        value: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        desc: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      dialogVisible: false,
      isAdd: false
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          api
            .edit({
              last_update_by: Cookies.get('userName'),
              id: data.id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    [CRUD.HOOK.beforeToEdit](crud, data) {
      this.form = data
      this.isAdd = false
      return true
    },
    [CRUD.HOOK.beforeToAdd](crud) {
      this.form = Object.assign({}, defaultForm)
      this.isAdd = true
      return true
    },
    [CRUD.HOOK.afterAddCancel](crud) {
      this.form = defaultForm
      this.isAdd = false
      return true
    },
    [CRUD.HOOK.afterEditCancel](crud) {
      this.form = defaultForm
      return true
    },
    [CRUD.HOOK.beforeSubmit](crud) {
      // 当选择板子类型与重工板时，需提示，目的是提醒现场人员
      if (this.form.code === 'tinkering' && this.form.value === '1') {
        this.dialogVisible = true
      }
      if (this.isAdd) {
        for (const i in crud.data) {
          if (crud.data[i].code === this.form.code) {
            this.$message({ message: '参数已存在', type: 'error' })
            return false
          }
        }
      }
      crud.form = this.form
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
