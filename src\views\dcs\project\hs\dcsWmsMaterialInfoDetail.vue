<template>
  <div class="app-container" style="padding: 10px 0;">
    <el-card shadow="never">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="tableData"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="ylbh" label="余料编号" />
            <el-table-column :show-overflow-tooltip="true" prop="gg" label="规格(mm)" />
            <el-table-column :show-overflow-tooltip="true" prop="cz" label="材质" />
            <el-table-column :show-overflow-tooltip="true" prop="zl" label="重量(kg)" />
            <el-table-column :show-overflow-tooltip="true" prop="wz" label="位置" />
            <el-table-column :show-overflow-tooltip="true" prop="zt" label="状态" />
            <el-table-column :show-overflow-tooltip="true" prop="rwlx" label="任务类型" />
            <el-table-column :show-overflow-tooltip="true" prop="sj" label="入库时间" />
            <el-table-column :show-overflow-tooltip="true" prop="czry" label="操作人员" />
            <el-table-column :show-overflow-tooltip="true" prop="yllx" width="120" label="余料类型" />
            <el-table-column :show-overflow-tooltip="true" prop="bz" width="120" label="备注" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsMapStockCell from '@/api/dcs/project/wms/wmsMapStockCell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_MATERIAL_INFO_DETAIL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '余料信息管理详情',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapStockCell },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 690,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      tableData: [
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '入库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '出库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '出库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '入库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '出库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '入库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '出库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '入库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '入库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' },
        { ylbh: 'YL2025001', gg: '20*8000*800', rwlx: '入库', czry: '张三', bz: '切割使用', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', zt: '可用', sj: '2025-05-14 18:08:10', yllx: '小件' }
      ]
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 690
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>

    <style>
    .table-descriptions-label {
    width: 150px;
    }
    .table-descriptions-content {
    width: 150px;
    }
    </style>
