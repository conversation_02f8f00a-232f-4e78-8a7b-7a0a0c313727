<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard headerCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.wx.recipeName') + ':'">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt') + ':'">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 5px">
      <!-- 扫码栏 -->
      <div class="recipeInfo">
        <div>
          <span>{{ $t("lang_pack.wx.downRecipe") }}</span>
          <el-input
            ref="recipeInfoScan"
            v-model="recipeInfoScan"
            style="width: 500px"
            type="text"
          />
          <el-button
            slot="reference"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="recipeInfoScan === ''"
            @click="batchDownload()"
          >
            {{ $t("lang_pack.commonPage.download") }}
          </el-button>
            <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            v-if="!isShowFlag"
            size="small"
            icon="el-icon-upload2"
            @click="importDialogVisible = true"
            >{{ $t("lang_pack.wx.import") }}</el-button
          >
          <el-button
            v-if="!isShowFlag"
            slot="reference"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            size="small"
            @click="recipeDownLoad"
          >
            {{ $t("lang_pack.wx.export") }}
          </el-button>
        </template>
      </crudOperation>
        </div>
        <div
          v-if="!isShowFlag"
          style="display: flex; justify-content: right; align-items: center"
        >
          <span>{{ $t("lang_pack.wx.editParameters") + ":" }}</span>
          <el-switch
            v-model="disabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </div>
      </div>


      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="650px"
      >
        <el-form
          ref="form"
          class="el-form-wrap"
          :model="form"
          :rules="rules"
          size="small"
          label-width="100px"
          :inline="true"
        >
          <el-form-item
            :label="$t('lang_pack.wx.recipeType')"
            prop="recipe_type"
          >
            <el-select
              v-model="form.recipe_type"
              clearable
              @change="chooseRecipeType"
            >
              <el-option
                v-for="item in dict.EAP_RECIPE_TYPE"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('lang_pack.wx.recipeName')"
            prop="recipe_name"
          >
            <el-input v-model="form.recipe_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.wx.lotNo')" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item
            :label="$t('lang_pack.wx.version')"
            prop="recipe_version"
          >
            <el-input v-model="form.recipe_version" />
          </el-form-item>
          <!-- <el-form-item label="设备编码" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item label="设备描述" prop="device_des">
            <el-input v-model="form.device_des" />
          </el-form-item> -->
          <!-- <el-form-item label="物料编码" prop="material_code">
            <el-input v-model="form.material_code" />
          </el-form-item>
          <el-form-item label="物料描述" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item> -->
          <el-form-item
            :label="$t('lang_pack.commonPage.validIdentificationt')"
            prop="enable_flag"
          >
            <el-select v-model="form.enable_flag" clearable>
              <el-option
                v-for="item in dict.ENABLE_FLAG"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button
            size="small"
            icon="el-icon-close"
            plain
            @click="crud.cancelCU"
            >{{ $t("lang_pack.commonPage.cancel") }}</el-button
          >
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
            >{{ $t("lang_pack.commonPage.confirm") }}</el-button
          >
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column
                v-if="1 == 0"
                width="10"
                prop="recipe_id"
                label="id"
              />
              <!-- <el-table-column
                :show-overflow-tooltip="true"
                prop="recipe_type"
                label="配方类型"
              /> -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="recipe_name"
                :label="$t('lang_pack.wx.recipeName')"
                width="210"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="device_code"
                :label="$t('lang_pack.wx.lotNo')"
                width="170"
              />
              <!-- <el-table-column
                :show-overflow-tooltip="true"
                prop="recipe_version"
                label="版本号"
                width="120"
              /> -->
              <!-- <el-table-column
                :show-overflow-tooltip="true"
                prop="device_code"
                label="设备编码"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="device_des"
                label="设备描述"
              /> -->
              <!-- <el-table-column
                :show-overflow-tooltip="true"
                prop="material_code"
                label="物料编码"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="material_des"
                label="物料描述"
              /> -->
              <el-table-column
                :label="$t('lang_pack.fastCode.isEnabled')"
                align="center"
                prop="enable_flag"
              >
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{
                    scope.row.enable_flag === "Y"
                      ? $t("lang_pack.vie.effective")
                      : $t("lang_pack.vie.invalid")
                  }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('lang_pack.wx.recipeOperation')"
                width="280"
                align="center"
                prop=""
              >
                <template slot-scope="scope">
                  <el-tag
                    style="cursor: pointer; margin-left: 5px"
                    @click="downLoad(scope.row)"
                    >{{ $t("lang_pack.wx.down") }}</el-tag
                  >
                  <el-tag
                    v-if="!isShowFlag"
                    style="cursor: pointer; margin-left: 5px"
                    @click="upload(scope.row)"
                    >{{ $t("lang_pack.wx.upload") }}</el-tag
                  >
                  <el-tag
                    style="cursor: pointer; margin-left: 5px"
                    @click="down(scope.row)"
                    >{{ $t("lang_pack.wx.distributed") }}</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('lang_pack.commonPage.operate')"
                width="100"
                align="center"
                fixed="right"
              >
                <template slot-scope="scope">
                  <!-- <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                      <template slot="right">

                      </template>
                    </udOperation> -->
                  <operation
                    :data="scope.row"
                    :permission="permission"
                    :disabled-edit="isShowFlag"
                    :disabled-dle="isShowFlag"
                    @ok="doDelete"
                  />
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <detail
              ref="detail"
              class="tableFirst"
              :recipe_detail_id="currentPackId"
              :disabled="disabled"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
    <el-dialog
      :fullscreen="false"
      :show-close="true"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      title="导入配方"
      width="400px"
      :visible.sync="importDialogVisible"
    >
      <div class="uploadStyleone">
        <el-upload
          ref="upload"
          :multiple="false"
          class="upload-demo"
          action=""
          drag=""
          :limit="uploadLimit1"
          :accept="uploadAccept1"
          :auto-upload="false"
          :on-change="handleImport"
          :http-request="uploadFile"
          :on-progress="progressA"
          :file-list="fileList"
          name="file"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__text">只能上传{{ uploadAccept1 }}文件</div>
        </el-upload>
        <el-input
          v-if="isUpLoadError"
          v-model="errorMsg"
          type="textarea"
          :rows="5"
        />
        <div style="text-align: center; margin-top: 10px">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="upLoading"
            @click="toButDrawerUpload"
            >导入</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import eapRecipe from "@/api/wx/recipe/eapRecipe";
import {
  selLoginInfo,
  userLogin,
  userLogout,
} from "@/api/eap/eapMeStationUser";
import { downloadFile } from "@/utils/index";
import detail from "@/views/wx/recipe/project/recipe/detail";
import Cookies, { get } from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import operation from "@/views/core/system/errorMsg/operation.vue";
import { sel as selSysParameter } from "@/api/core/system/sysParameter";
import { sel as selStation } from "@/api/core/factory/sysStation";
import { isShow } from "@/utils/permission";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import udOperation from "@crud/UD.operation";
import pagination from "@crud/Pagination";
import axios from "axios";
import mqtt from "mqtt";
import { selCellIP } from "@/api/core/center/cell";
import { MQTT_USERNAME, MQTT_PASSWORD } from "@/utils/emq.js";
const defaultForm = {
  recipe_id: "",
  recipe_name: "",
  recipe_version: "",
  device_code: "",
  device_des: "",
  material_code: "",
  material_des: "",
  recipe_type: "",
  enable_flag: "Y",
  recipeFileNames: "",
};
export default {
  name: "EAP_RECIPE",
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    detail,
    operation,
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {},
  cruds() {
    return CRUD({
      title: "配方维护",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "recipe_id",
      // 排序
      sort: ["recipe_id asc"],
      // CRUD Method
      crudMethod: { ...eapRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        down: false,
        reset: true,
      },
    });
  },
  // 数据字典
  dicts: ["ENABLE_FLAG", "EAP_RECIPE_TYPE", "USER_PERMISS_CODE"],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      recipeInfoScan: "",
      permission: {
        add: ["admin", "c_eap_fmod_recipe_maintain:add"],
        edit: ["admin", "c_eap_fmod_recipe_maintain:edit"],
        del: ["admin", "c_eap_fmod_recipe_maintain:del"],
      },
      rules: {
        recipe_type: [
          {
            required: true,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        recipe_version: [
          {
            required: true,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        recipe_name: [
          {
            required: true,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        material_code: [
          {
            required: false,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        material_des: [
          {
            required: false,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        device_code: [
          {
            required: false,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        device_des: [
          {
            required: false,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
        enable_flag: [
          {
            required: true,
            message: this.$t("lang_pack.commonPage.required"),
            trigger: "blur",
          },
        ],
      },
      currentPackId: 0,
      cellIp: "", // 单元IP
      webapiPort: "", // 单元API端口号
      mqttPort: "", // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: "/mqtt",
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          "ScadaWeb_" +
          Cookies.get("userId") +
          "_" +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false,
      },

      stationAttr: "",
      // 监听数据
      monitorData: {
        ApplyUploadRecipe: {
          client_code: `${this.stationAttr}Ais`,
          group_code: "AisStatus",
          tag_code: "ApplyUploadRecipe",
          tag_des: "[AisStatus]申请上传配方",
          value: "",
        },
        ApplyDownLoadRecipe: {
          client_code: `${this.stationAttr}Ais`,
          group_code: "AisStatus",
          tag_code: "ApplyDownLoadRecipe",
          tag_des: "[AisStatus]申请下载配方",
          value: "",
        },
        ApplyDistributeRecipe: {
          client_code: `${this.stationAttr}Ais`,
          group_code: "AisStatus",
          tag_code: "ApplyDistributeRecipe",
          tag_des: "[AisStatus]申请下发配方",
          value: "",
        },
      },
      lockTime: 600,
      controlStatus: {
        device_plc_status: "",
        ais_status: "0",
        plc_status: "0",
      },
      loginInfo: {
        user_name: "",
        nick_name: "",
        dept_id: "",
        shift_id: "",
      },
      tagKeyList: [
        `${this.stationAttr}Ais`+"/AisStatus/ApplyUploadRecipe",
        `${this.stationAttr}Ais`+"/AisStatus/ApplyDownLoadRecipe",
        `${this.stationAttr}Ais`+"/AisStatus/ApplyDistributeRecipe",
      ],
      applyObj: {
        uploadValue: "",
        downloadValue: "",
        downValue: "",
      },
      popoverContent: "",
      deviceStatus: -1,
      tempTime: 0,
      disabled: false,
      importDialogVisible: false,
      uploadLimit1: 1,
      uploadAccept1: ".xls,.xlsx",
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: "",
      fileList: [],
      inTerfLog: {},
      exceptionLogs: {},
      isShowFlag: false,
      deviceSelfTestTimer: null, // 添加设备自检定时器
    };
  },
  watch: {},
  mounted() {
    // 首先更根据工位号查询，获取到所要查询的点位实例
    this.getStationAttr();
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200;
    };
    this.$refs.recipeInfoScan.focus();
  },
  created: function () {
    this.getStationData();
    this.handleMouseEnter();
    setTimeout(() => {
      this.setupReadonlyFlag();
    }, 1000);
  },
  beforeDestroy() {
    // 清除定时器
    if (this.recipeTimer) {
      clearInterval(this.recipeTimer);
    }
    if (this.deviceSelfTestTimer) {
      clearInterval(this.deviceSelfTestTimer);
    }
  },
  methods: {
    async setupReadonlyFlag() {
      this.isShowFlag = await isShow();
      console.log(this.isShowFlag);
      this.crud.optShow.add = !this.isShowFlag;
    },

    getStationAttr() {
      const query = {
        stationCodeDes: this.$route.query.station_code,
        user_name: Cookies.get("userName"),
      };
      selStation(query).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.stationAttr = res.data[0].station_attr;
            this.client_id_list = res.data[0].client_id_list;
            Object.keys(this.monitorData).forEach((key) => {
              this.monitorData[key].client_code =
                `${this.stationAttr}` + this.monitorData[key].client_code;
            });
            this.getScadaData();
            return;
          }
          this.stationAttr = "";
        }
      });
    },

    getScadaData() {
      const query = {
        client_id: this.client_id_list,
        enable_flag: "Y",
        sort: "tag_group_id",
        user_name: Cookies.get("userName"),
      };
      scadaTagGroupTree(query).then((res) => {
        if (res.data.length > 0) {
          const data = res.data.find(
            (item) => item.tag_group_code === "PlcRecipe"
          );
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`,
                tag_des: e.tag_des,
                value: "",
              });
            });
            this.tableData = data.children.reduce((acc, e) => {
              acc[`${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`] =
                "";
              return acc;
            }, {});
          }
        }
      });
      const params = {
        tableOrder: "asc",
        tableOrderField: "tag_id",
        tablePage: 1,
        tableSize: 1000,
        tag_group_id: this.client_id_list+"03",
        user_name: Cookies.get("userName"),
      };
      selScadaTag(params).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              const result = {
                tag_des: item.tag_des,
                tag_key: `${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}`,
                tag_value: "",
                unit: "",
                down_limit: item.down_limit,
                upper_limit: item.upper_limit,
                status: "",
              };
              this.plcCraftData.push(result);
            });
          }
        }
      });
    },
    // 多个删除
    batchDelete() {
      this.$confirm(
        this.$t("lang_pack.wx.confirmDelete") +
          `${this.crud.selections.length}` +
          this.$t("lang_pack.wx.articleData") +
          "?",
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          const ids = this.crud.selections
            .map((item) => item[this.crud.idField])
            .join(",");
          const recipeFileNames = this.crud.selections
            .map((item) => item.recipe_name + "-" + item.recipe_version)
            .join(",");
          const query = {
            ids,
            recipeFileNames,
            user_name: Cookies.get("userName"),
          };
          this.delete(query);
        })
        .catch(() => {});
    },
    // 单个删除
    doDelete(data) {
      const query = {
        ids: data.recipe_id,
        recipeFileNames: data.recipe_name + "-" + data.recipe_version,
        user_name: Cookies.get("userName"),
      };
      this.delete(query);
    },
    delete(data) {
      eapRecipe
        .del(data)
        .then((res) => {
          if (res.code === 0) {
            this.$message({
              message: this.$t("lang_pack.commonPage.deleteSuccesful"),
              type: "success",
            });
            this.crud.toQuery();
            // 删除父级的数据后，子级table也要清空
            this.$refs.detail.crud.data = [];
          } else {
            this.$message({
              message:
                res.msg || this.$t("lang_pack.commonPage.operationfailure"),
              type: "error",
            });
          }
        })
        .catch((e) => {
          this.$message({
            message: this.$t("lang_pack.commonPage.operationfailure"),
            type: "error",
          });
        });
    },
    chooseRecipeType(val) {
      console.log(val);
      if (val === "MATERIAL") {
        this.rules.material_code[0].required = true;
        this.rules.material_des[0].required = true;
        this.rules.device_code[0].required = false;
        this.rules.device_des[0].required = false;
      } else {
        this.rules.material_code[0].required = false;
        this.rules.material_des[0].required = false;
        this.rules.device_code[0].required = true;
        this.rules.device_des[0].required = true;
      }
    },
    handleRowClick(row, column, event) {
      this.currentPackId = row.recipe_id;
      console.log(row.recipe_id);
    },
    async handleMouseEnter() {
      await this.fetchContent();
    },
    handleMouseLeave() {},
    async fetchContent() {
      try {
        const res = await eapRecipe.selectEquipStatusInfo({});
        const defaultQuery = JSON.parse(JSON.stringify(res));
        if (defaultQuery.code === 0) {
          if (defaultQuery.result !== "") {
            this.popoverContent = defaultQuery.result.replace(
              /\\r\\n/g,
              "<br>"
            );
          }
        } else {
          this.popoverContent = res;
        }
      } catch (error) {
        this.$message({
          message: this.$t("lang_pack.vie.queryException"),
          type: "error",
        });
      }
      // eapRecipe.selectEquipStatusInfo({}).then(res => {
      //   const defaultQuery = JSON.parse(JSON.stringify(res))
      //   if (defaultQuery.code === 0) {
      //     if (defaultQuery.result !== '') {
      //       this.popoverContent = defaultQuery.result.replace(/\\r\\n/g, '<br>')
      //     }
      //   }
      // }).catch(() => {
      //   this.$message({
      //     message: '查询异常',
      //     type: 'error'
      //   })
      // })
    },
    // 批量上传
    batchUplaod() {
      this.$confirm(
        this.$t("lang_pack.wx.selected") +
          `${this.crud.selections.length}` +
          this.$t("lang_pack.wx.articleData") +
          "?",
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          if (this.monitorData.ApplyUploadRecipe.value === "1") {
            this.$message({
              type: "warning",
              message: this.$t("lang_pack.wx.uploadTask"),
            });
            return;
          }
          var sendJson = {};
          var rowJson = [];
          var newRow1 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/ApplyUploadRecipe",
            TagValue: "1",
          };
          rowJson.push(newRow1);
          var newRow2 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/UploadRecipeName",
            TagValue: this.crud.selections
              .map((item) => item.recipe_name)
              .join(","),
          };
          rowJson.push(newRow2);
          sendJson.Data = rowJson;
          sendJson.ClientName = "SCADA_WEB";
          var sendStr = JSON.stringify(sendJson);
          var topic = "SCADA_WRITE/"+`${this.stationAttr}Ais`;
          this.sendMessage(topic, sendStr);
          this.$message({
            type: "success",
            message: this.$t("lang_pack.wx.trigger"),
          });
        })
        .catch(() => {});
    },
    // 单个上传
    upload(row) {
      this.$confirm(
        this.$t("lang_pack.wx.confirmRecipe"),
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          if (this.monitorData.ApplyUploadRecipe.value === "1") {
            this.$message({
              type: "warning",
              message: this.$t("lang_pack.wx.uploadTask"),
            });
            return;
          }
          var sendJson = {};
          var rowJson = [];
          var newRow1 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/ApplyUploadRecipe",
            TagValue: "1",
          };
          rowJson.push(newRow1);
          var newRow2 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/UploadRecipeName",
            TagValue: row.recipe_name,
          };
          rowJson.push(newRow2);
          sendJson.Data = rowJson;
          sendJson.ClientName = "SCADA_WEB";
          var sendStr = JSON.stringify(sendJson);
          var topic = "SCADA_WRITE/"+`${this.stationAttr}Ais`;
          this.sendMessage(topic, sendStr);
          this.$message({
            type: "success",
            message: this.$t("lang_pack.wx.trigger"),
          });
        })
        .catch(() => {});
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false;
      this.fileList = fileList;
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function (file) {
      this.fileData.set("file", file.file);
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: "请选择文件",
          type: "error",
        });
        return;
      }

      const userName = Cookies.get("userName");
      if (userName) {
        this.fileData.set("userName", userName);
      }
      this.$refs.upload.submit();

      // 配置路径
      var method = "eap/project/mflex/recipe/EapRecipeImport";
      var path =
        (process.env.NODE_ENV === "development"
          ? process.env.VUE_APP_BASE_API + "/"
          : "/aisEsbWeb/") + method;
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true;
      axios
        .post(path, this.fileData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            const num =
              ((progressEvent.loaded / progressEvent.total) * 100) | 0; // 百分比
            this.$refs.upload.onProgress({ percent: num }); // 进度条
          },
        })
        .then((response) => {
          const defaultQuery = JSON.parse(JSON.stringify(response));
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: "success",
              message: "导入成功!",
            });
            this.fileList = [];
            this.isUpLoadError = false;
            this.upLoading = false;
            this.importDialogVisible = false;
            this.crud.toQuery();
          } else {
            this.upLoading = false;
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = [];
            this.errorMsg = defaultQuery.data.msg;
            this.isUpLoadError = true;
            this.$message({
              type: "error",
              message: "导入失败!",
            });
          }
        })
        .catch((ex) => {
          this.upLoading = false;
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = [];
          this.$message({
            message: "导入异常" + ex,
            type: "error",
          });
        });
    },
    // 配方导出
    recipeDownLoad() {
      if (
        this.crud.selections.length === 0 ||
        this.crud.selections.length > 1
      ) {
        this.$message({
          type: "warning",
          message: this.$t("lang_pack.wx.selectOne"),
        });
        return;
      }
      const selectData = this.crud.selections[0];
      this.$confirm(
        this.$t("lang_pack.wx.exportName") +
          `【${selectData.recipe_name}】` +
          this.$t("lang_pack.vie.what"),
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      ).then(() => {
        eapRecipe
          .EapRecipeExport({ recipe_id: selectData.recipe_id })
          .then((res) => {
            downloadFile(res, this.$t("lang_pack.wx.recipeDownload"), "xls");
            // if (res.code === 0) {
            //   this.$message({
            //     type: 'success',
            //     message: this.$t('lang_pack.wx.exportSuccess')
            //   })
            // } else {
            //   this.$message({
            //     type: 'error',
            //     message: res.msg || this.$t('lang_pack.commonPage.operationfailure')
            //   })
            // }
          });
      });
    },
    // 批量下载
    batchDownload() {
      this.$confirm(
        this.$t("lang_pack.wx.confirmDownload"),
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          if (this.monitorData.ApplyDownLoadRecipe.value === "1") {
            this.$message({
              type: "warning",
              message: this.$t("lang_pack.wx.pleaseWait"),
            });
            return;
          }
          var sendJson = {};
          var rowJson = [];
          var newRow1 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/ApplyDownLoadRecipe",
            TagValue: "1",
          };
          rowJson.push(newRow1);
          var newRow2 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/DownLoadRecipeName",
            TagValue: this.recipeInfoScan,
          };
          rowJson.push(newRow2);
          sendJson.Data = rowJson;
          sendJson.ClientName = "SCADA_WEB";
          var sendStr = JSON.stringify(sendJson);
          var topic = "SCADA_WRITE/"+`${this.stationAttr}Ais`;
          this.sendMessage(topic, sendStr);
          this.$message({
            type: "success",
            message: this.$t("lang_pack.wx.triggerDownload"),
          });
        })
        .catch(() => {});
    },
    // 单个下载
    downLoad(row) {
      this.$confirm(
        this.$t("lang_pack.wx.confirmDownload"),
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          if (this.monitorData.ApplyDownLoadRecipe.value === "1") {
            this.$message({
              type: "warning",
              message: this.$t("lang_pack.wx.pleaseWait"),
            });
            return;
          }
          var sendJson = {};
          var rowJson = [];
          var newRow1 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/ApplyDownLoadRecipe",
            TagValue: "1",
          };
          rowJson.push(newRow1);
          var newRow2 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/DownLoadRecipeName",
            TagValue: row.device_code,
          };
          rowJson.push(newRow2);
          sendJson.Data = rowJson;
          sendJson.ClientName = "SCADA_WEB";
          var sendStr = JSON.stringify(sendJson);
          var topic = "SCADA_WRITE/"+`${this.stationAttr}Ais`;
          this.sendMessage(topic, sendStr);
          this.$message({
            type: "success",
            message: this.$t("lang_pack.wx.triggerDownload"),
          });
        })
        .catch(() => {});
    },
    // 单个下发
    down(row) {
      this.$confirm(
        this.$t("lang_pack.wx.IssuedEquipment"),
        this.$t("lang_pack.Prompt"),
        {
          confirmButtonText: this.$t("lang_pack.vie.determine"),
          cancelButtonText: this.$t("lang_pack.commonPage.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          if (this.monitorData.ApplyDistributeRecipe.value === "1") {
            this.$message({
              type: "warning",
              message: this.$t("lang_pack.wx.alreadyExistsTask"),
            });
            return;
          }
          var sendJson = {};
          var rowJson = [];
          var newRow1 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/ApplyDistributeRecipe",
            TagValue: "1",
          };
          rowJson.push(newRow1);
          var newRow2 = {
            TagKey: `${this.stationAttr}Ais`+"/AisStatus/DistributeRecipeName",
            TagValue: row.recipe_name,
          };
          rowJson.push(newRow2);
          sendJson.Data = rowJson;
          sendJson.ClientName = "SCADA_WEB";
          var sendStr = JSON.stringify(sendJson);
          var topic = "SCADA_WRITE/"+`${this.stationAttr}Ais`;
          this.sendMessage(topic, sendStr);

          this.$message({
            type: "success",
            message: this.$t("lang_pack.wx.SuccessfullyIssued"),
          });
        })
        .catch(() => {});
    },
    scadaPoint(dataKey) {
      for (let i = 0; i < dataKey.length; i++) {
        var sendJson = {};
        var rowJson = [];
        var newRow = {
          TagKey: dataKey[i].TagKey,
          TagValue: dataKey[i].TagValue,
        };
        rowJson.push(newRow);
        sendJson.Data = rowJson;
        sendJson.ClientName = "SCADA_WEB";
        var sendStr = JSON.stringify(sendJson);
        var topic = "SCADA_WRITE/"+`${this.stationAttr}Ais`;
        this.sendMessage(topic, sendStr);
      }
    },
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: "",
        prod_line_des: "",
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: "",
        cell_id: this.$route.query.cell_id,
      };
      this.getCellIp();
      this.getLoginInfo();
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get("userName"),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname,
      };
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result);
            this.cellIp = ipInfo.ip;
            this.webapiPort = ipInfo.webapi_port;
            this.mqttPort = ipInfo.mqtt_port;
            this.toStartWatch();
            this.getTagValue();
          } else {
            this.$message({ message: defaultQuery.msg, type: "error" });
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t("lang_pack.vie.queryException"),
            type: "error",
          });
        });
    },
    // 获取当前登录信息
    getLoginInfo() {
      this.loginInfo.user_name = "---";
      this.loginInfo.nick_name = "---";
      this.loginInfo.dept_id = "---";
      this.loginInfo.shift_id = "---";
      const query = {
        user_name: Cookies.get("userName"),
        station_id: this.currentStation.station_id,
      };
      selLoginInfo(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== "") {
              const loginInfo = defaultQuery.data[0];
              this.loginInfo.user_name = loginInfo.user_name;
              this.loginInfo.nick_name = loginInfo.nick_name;
              this.loginInfo.dept_id = loginInfo.dept_id;
              this.loginInfo.shift_id = loginInfo.shift_id;
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: "error" });
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t("lang_pack.hmiMain.procedure"),
            type: "error",
          });
        });
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = [];
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code;
        var group_code = this.monitorData[key].group_code;
        var tag_code = this.monitorData[key].tag_code;
        if (this.aisMonitorMode === "AIS-SERVER") {
          client_code = client_code + "_" + this.currentStation.station_code;
        }
        var readTag = {};
        readTag.tag_key = client_code + "/" + group_code + "/" + tag_code;
        readTagArray.push(readTag);
      });
      var method = "/cell/core/scada/CoreScadaReadTag";
      var path = "";
      if (process.env.NODE_ENV === "development") {
        path = "http://localhost:" + this.webapiPort + method;
      } else {
        path = "http://" + this.cellIp + ":" + this.webapiPort + method;
      }
      axios
        .post(path, readTagArray, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data;
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code;
                var group_code = this.monitorData[key].group_code;
                var tag_code = this.monitorData[key].tag_code;
                if (this.aisMonitorMode === "AIS-SERVER") {
                  client_code =
                    client_code + "_" + this.currentStation.station_code;
                }
                debugger;
                var tag_key = client_code + "/" + group_code + "/" + tag_code;
                const item = result.filter((item) => item.tag_key === tag_key);
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? "" : item[0].tag_value;
                }
              });
            }
          }
        })
        .catch((ex) => {
          this.$message({
            message: this.$t("lang_pack.vie.queryException") + "：" + ex,
            type: "error",
          });
        });
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end();
        this.mqttConnStatus = false;
      }
      var connectUrl = "ws://" + this.cellIp + ":" + this.mqttPort + "/mqtt";
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt); // 开启连接
      this.clientMqtt.on("connect", (e) => {
        this.mqttConnStatus = true;
        var aisClientCode = `${this.stationAttr}Ais`;
        var plcClientCode = `${this.stationAttr}Plc`;
        this.topicSubscribe("SCADA_STATUS/" + aisClientCode);
        this.topicSubscribe("SCADA_BEAT/" + aisClientCode);
        this.topicSubscribe("SCADA_STATUS/" + plcClientCode);
        this.topicSubscribe("SCADA_BEAT/" + plcClientCode);
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code;
          var group_code = this.monitorData[key].group_code;
          var tag_code = this.monitorData[key].tag_code;
          if (this.aisMonitorMode === "AIS-SERVER") {
            client_code = client_code + "_" + this.$route.query.station_code;
          }
          this.topicSubscribe(
            "SCADA_CHANGE/" + client_code + "/" + group_code + "/" + tag_code
          );
        });
        this.$message({
          message: "连接成功",
          type: "success",
        });
      });

      // MQTT连接失败
      this.clientMqtt.on("error", () => {
        this.$message({
          message: "连接失败",
          type: "error",
        });
        this.clientMqtt.end();
      });
      // 断开发起重连(异常)
      this.clientMqtt.on("reconnect", () => {
        this.$message({
          message: "连接断开，正在重连。。。",
          type: "error",
        });
      });
      this.clientMqtt.on("disconnect", () => {});
      this.clientMqtt.on("close", () => {});
      // 接收消息处理
      this.clientMqtt.on("message", (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message);
          if (jsonData == null) return;
          if (topic.indexOf("SCADA_CHANGE/") >= 0) {
            Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code;
              var group_code = this.monitorData[key].group_code;
              var tag_code = this.monitorData[key].tag_code;
              if (this.aisMonitorMode === "AIS-SERVER") {
                client_code =
                  client_code + "_" + this.$route.query.station_code;
              }
              var tag_key = client_code + "/" + group_code + "/" + tag_code;
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue;
              }
            });
          }
        } catch (e) {
          console.log(e);
        }
      });
    },
    // 订阅主题函数
    topicSubscribeChange(topic) {
      if (!this.mqttChangeStatus) {
        this.$message({
          message: this.$t("lang_pack.vie.pleaseStartMonitor"),
          type: "error",
        });
        return;
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientChangeMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttChangeStatus) {
          this.$message({
            message: this.$t("lang_pack.vie.pleaseStartMonitor"),
            type: "error",
          });
          return;
        }
        if (!error) {
          console.log("MQTT订阅成功:" + topic);
        } else {
          console.log("MQTT订阅失败:" + topic);
        }
      });
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t("lang_pack.vie.pleaseStartMonitor"),
          type: "error",
        });
        return;
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t("lang_pack.vie.pleaseStartMonitor"),
            type: "error",
          });
          return;
        }
        if (!error) {
          console.log("MQTT订阅成功:" + topic);
        } else {
          console.log("MQTT订阅失败:" + topic);
        }
      });
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t("lang_pack.vie.pleaseStartMonitor"),
          type: "error",
        });
        return;
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          if (this.showMsg) {
            this.$message({
              message: this.$t("view.dialog.operationSucceed"),
              type: "success",
            });
          }
          // 执行完成后都复位为true
          this.showMsg = true;
        } else {
          // 执行完成后都复位为true
          this.showMsg = true;
          this.$message({
            message: this.$t("view.dialog.operationFailed"),
            type: "error",
          });
        }
      });
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.old_recipe_name = crud.form.recipe_name;
      crud.form.old_recipe_version = crud.form.recipe_version;
      return true;
    },
  },
};
</script>
  <style lang="less" scoped>
.app-container {
  padding: 10px;
}
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.recipeInfo {
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btnone {
  background: #50d475;
  border-color: #50d475;
}

.btnone0 {
  background: #959595;
  border-color: #e8efff;
  color: #ffffff;
}
.headerCard {
  ::v-deep .el-card__body {
    padding: 10px 15px 5px !important;
  }
}
</style>
