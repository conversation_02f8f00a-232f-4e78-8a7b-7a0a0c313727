<template>
  <div v-loading="loading" :style="'height:' + height">
    <iframe
      id="my-iframe"
      :src="src"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
      @load="iframeLoaded"
    />
  </div>
</template>
<script>
import debounce from 'lodash.debounce'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
export default {
  props: {
    src: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 113 + 'px;',
      loading: true,
      lockTime: 10,
      timer: null,
      iframeWindow: null,
      mouseMoveTriggered: true,
      lockTimeResetTimer: null
    }
  },
  mounted: function() {
    // this.resetTimer() // 5秒后自动切换页面
    setTimeout(() => {
      this.loading = false
    }, 230)
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 113 + 'px;'
    }
  },
  created: function() {
    this.getLockTime()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    iframeLoaded(event) {
      this.iframeWindow = event.target.contentWindow
      this.iframeWindow.addEventListener('mousemove', this.handleMouseMove)
    },
    handleMouseMove() {
      if (this.mouseMoveTriggered) {
        clearTimeout(this.lockTimeResetTimer)
      }
      this.lockTimeResetTimer = setTimeout(() => {
        this.mouseMoveTriggered = false
        this.$router.push('/whiteLoginWx')
      }, 1000 * this.lockTime)
    },
    // handleMouseMove(event) {
    //   // 处理鼠标移动事件
    //   this.resetTimer(event)
    // },
    // resetTimer(e) {
    //   e !== null && clearTimeout(this.timer)
    //   this.timer = setTimeout(() => {
    //     console.log(e)
    //     if (!e) {
    //       console.log(e)
    //       this.$router.push('/whiteLoginWx')
    //     }
    //   }, 1000 * 10)
    // },
    getLockTime() {
      var queryParameter = {
        cell_id: '0',
        parameter_code: 'LockTime',
        enable_flag: 'Y'
      }
      localStorage.setItem('lockTime', '') // 先清除缓存
      selSysParameter(queryParameter)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              this.lockTime = defaultQuery.data[0].parameter_val
              localStorage.setItem('lockTime', this.lockTime)
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
