import request from '@/utils/request'

// 查询WMS天车库存表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMeStockSelect',
    method: 'post',
    data
  })
}
// 新增WMS天车库存表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMeStockInsert',
    method: 'post',
    data
  })
}
// 修改WMS天车库存表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMeStockUpdate',
    method: 'post',
    data
  })
}
// 删除WMS天车库存表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsMeStockDelete',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

