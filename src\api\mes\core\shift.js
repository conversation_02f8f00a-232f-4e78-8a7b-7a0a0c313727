import request from '@/utils/request'

// 班次信息查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftSel',
    method: 'post',
    data
  })
}
// 班次信息新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftIns',
    method: 'post',
    data
  })
}
// 班次信息修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftUpd',
    method: 'post',
    data
  })
}
// 班次信息删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShiftDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
