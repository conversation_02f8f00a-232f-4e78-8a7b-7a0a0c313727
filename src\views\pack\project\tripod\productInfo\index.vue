<template>
  <div class="app-container">
    <el-card ref="queryCard">
      <el-descriptions class="margin-top" :column="3" :size="size" border>
        <el-descriptions-item>
          <template slot="label">
            <!-- 订单号 -->
            {{ $t('view.field.jobOrder.batchNo') }}
          </template>
          {{ productInfoData.lot_num }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 类型 -->
          <template slot="label">
            {{ $t('lang_pack.vie.task_type') }}
          </template>
          {{ dict.label.TASK_TYPE[productInfoData.task_type] }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 料号 -->
          <template slot="label">
            {{ $t('lang_pack.vie.partNum') }}
          </template>
          {{ productInfoData.model_type }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 订单状态 -->
          <template slot="label">
            {{ $t('lang_pack.vie.orderStatus') }}
          </template>
          {{ dict.label.TASK_STATUS[productInfoData.lot_status] }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- PCS类型 -->
          <template slot="label">
            {{ $t('lang_pack.vie.pcsType') }}
          </template>
          {{ dict.label.QR_TYPE[productInfoData.bd_type] }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 板长 -->
          <template slot="label">
            {{ $t('lang_pack.vie.plateLen') }}
          </template>
          {{ productInfoData.m_length }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 板宽 -->
          <template slot="label">
            {{ $t('lang_pack.vie.plateWid') }}
          </template>
          {{ productInfoData.m_width }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 板厚 -->
          <template slot="label">
            {{ $t('lang_pack.vie.plateThi') }}
          </template>
          {{ productInfoData.m_tickness }}
        </el-descriptions-item>
        <el-descriptions-item>
          <!-- 板重 -->
          <template slot="label">
            {{ $t('lang_pack.vie.plateWei') }}
          </template>
          {{ productInfoData.m_weight }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card ref="queryCard" style="margin-top: 20px;" :style="{height:height+'px'}">
      <div style="display: flex;position: relative;">
        <div v-if="attrFlag" style="position: absolute;top: 0px;left: 1%;display: flex;">
          <div style="background-color: darkgreen;width: 20px;height: 20px;border-radius: 5px;" />
          <span style="font-size: 20px;margin-left: 10px;">PASS板({{ resultDataObject.value }})({{ resultDataObject.percentage }}%)</span>
        </div>
        <div id="productInfoDom" style="width: 100%;height: 460px" />
      </div>
    </el-card>
  </div>
</template>
<script>
import api from '@/api/pack/project/avary/task'
import Cookies from 'js-cookie'
export default {
  name: 'productInfo',
  data() {
    return {
      size: '',
      productInfoData: {
        lot_num: '',
        task_type: '',
        model_type: '',
        lot_status: '',
        finish_ok_count: '',
        finish_ng_count: '',
        array_type: '',
        bd_type: '',
        m_length: '',
        m_width: '',
        m_tickness: '',
        m_weight: ''
      },
      resultDataObject: {
        value: '0',
        percentage: '100'
      },
      timer: null,
      attrFlag: false,
      productInfoDom: null,
      height: document.documentElement.clientHeight - 225
    }
  },
  dicts: ['TASK_TYPE', 'TASK_STATUS', 'TASK_STATUS', 'QR_TYPE'],
  mounted() {
    this.getWorkOrder()
    this.timer = setInterval(() => {
      this.getWorkOrder()
    }, 1000 * 30) // 每5分钟刷新一次
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 225
    }
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
  },
  methods: {
    getWorkOrder() {
      // 只拿取生产中最新的一条
      const query = {
        lot_status: 'WORK',
        page: 1,
        size: 1,
        sort: 'plan_id asc',
        user_name: Cookies.get('userName')
      }
      api.sel(query).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.productInfoData = res.data[0]
          if (this.productInfoData.lot_num) {
            api.PackProductInfoSelect({ lot_num: this.productInfoData.lot_num }).then(temp => {
              if (temp.code === 0 && temp.data.length > 0) {
                const resultMap = temp.data.reduce((acc, item) => {
                  if (item.array_status === 'NG') {
                    acc['NG板'] = (acc['NG板'] || 0) + 1
                  } else if (item.array_status === 'OK') {
                    acc['PASS板'] = (acc['PASS板'] || 0) + 1
                    if (item.xout_set_num === 0) {
                      const i = 'OK板'
                      acc[i] = (acc[i] || 0) + 1
                    } else {
                      const key = item.xout_set_num + 'XOUT板'
                      acc[key] = (acc[key] || 0) + 1
                    }
                  }
                  return acc
                }, {})
                // 转换为目标格式
                const result = Object.entries(resultMap).map(([name, value]) => ({
                  value: value,
                  name: name,
                  label: { fontSize: 28 }
                }))
                const OKcount = result.find(e => e.name === 'PASS板').value
                this.resultDataObject = {
                  value: OKcount,
                  percentage: ((OKcount / temp.count) * 100).toFixed(2)
                }
                this.attrFlag = true
                this.getProductLinePie(result, temp.count)
              } else {
                // 判断没有查询到数据的情况
                const data = [{ value: 0, name: 'NG板', label: { fontSize: 28 }}, { value: 0, name: 'PASS板', label: { fontSize: 28 }}]
                this.attrFlag = false
                this.getProductLinePie(data, 100)
              }
            })
          }
          return
        }
        // 判断没有查询到数据的情况
        const data = [{ value: 0, name: 'NG板', label: { fontSize: 28 }}, { value: 0, name: 'PASS板', label: { fontSize: 28 }}]
        this.attrFlag = false
        this.getProductLinePie(data, 100)
      })
    },
    getProductLinePie(data, total) {
      var that = this
      this.productInfoDom = this.$echarts.init(document.getElementById('productInfoDom'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        color: ['#BA863A', '#77B4E3', '#B95E49', '#428EB4', '#4388A8', '#75B1E4', '#4FB3C2'],
        title: {
          textStyle: {
            color: '#fff',
            fontSize: 40
          }
        },
        legend: {
          x: 'right',
          y: 'top',
          orient: 'vertical',
          align: 'left',
          textStyle: {
            color: '#000',
            fontFamily: 'Alibaba PuHuiTi',
            fontSize: 24
          },
          itemWidth: 20, // 修改图例标识的宽度
          itemHeight: 20, // 修改icon图形大小
          itemGap: 24, // 修改间距
          formatter: function(name) {
            // 找到对应的项
            const item = data.find(item => item.name === name)
            if (item) {
              const percentage = ((item.value / total) * 100).toFixed(2) // 计算百分比
              return `${name} (${item.value}) (${percentage}%)` // 格式化返回
            }
            return name // 若没有数据，返回名称
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '70%',
            center: ['50%', '60%'],
            label: {
              normal: {
                show: true,
                formatter: '{b}: {c}({d}%)'
              }
            },
            data: this.attrFlag ? data.filter(e => { return e.name !== 'PASS板' }) : data
          }
        ]
      }
      this.productInfoDom.setOption(option)
      window.addEventListener('resize', function() {
        that.productInfoDom.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-descriptions--small.is-bordered .el-descriptions-item__cell{
  font-size: 20px;
}
</style>
