<template>
  <!--产品型号对应配方-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="true" title="配方选择" :visible.sync="recipeDrawerVisible" size="450px">
      <smallRecipeChoose v-if="recipeDrawerVisible" ref="smallRecipeChoose" :recipe_type="currentRecipeType" @chooseRecipe="chooseRecipe" />
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"  highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="model_recipe_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="recipe_type" label="配方类型" />
          <el-table-column  :show-overflow-tooltip="true" prop="recipe_name_version" label="配方" />

          <el-table-column  label="操作" width="115" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="handleRecipeChoose(scope.row)">{{ scope.row.recipe_id !== '' ? '修改' : '添加' }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import crudSmallModelRecipe from '@/api/mes/core/smallModelRecipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import smallRecipeChoose from '@/views/mes/core/smallModel/smallRecipeChoose'
const defaultForm = {
  model_recipe_id: '',
  small_type_id: '',
  recipe_id: ''
}
export default {
  name: 'SmallModelRecipe',
  components: { crudOperation, rrOperation, udOperation, pagination, smallRecipeChoose },
  props: {
    small_type_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '产品型号对应配方',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'small_type_id',
      // 排序
      sort: ['small_type_id asc'],
      // CRUD Method
      crudMethod: { ...crudSmallModelRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'c_mes_fmod_small_model_recipe:add'],
        edit: ['admin', 'c_mes_fmod_small_model_recipe:edit'],
        del: ['admin', 'c_mes_fmod_small_model_recipe:del'],
        down: ['admin', 'c_mes_fmod_small_model_recipe:down']
      },
      rules: {
        // 提交验证规则
        recipe_type: [{ required: true, message: '请输入配方类型', trigger: 'blur' }]
      },
      recipeDrawerVisible: false,
      currentModelRecipeId: '',
      currentRecipeType: '无'
    }
  },
  watch: {
    small_type_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.small_type_id = this.small_type_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.small_type_id = this.small_type_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.small_type_id = this.small_type_id
      return true
    },

    // 选择配方
    handleRecipeChoose(row) {
      this.currentModelRecipeId = row.model_recipe_id
      this.currentRecipeType = row.recipe_type
      this.recipeDrawerVisible = true
    },
    chooseRecipe(recipe_id, recipe_name_version) {
      console.log('【this.currentModelRecipeId】' + this.currentModelRecipeId)
      console.log('【this.small_type_id】' + this.small_type_id)
      console.log('【recipe_id】' + recipe_id)
      console.log('【recipe_name_version】' + recipe_name_version)

      crudSmallModelRecipe
        .edit({
          user_name: Cookies.get('userName'),
          model_recipe_id: this.currentModelRecipeId,
          small_type_id: this.small_type_id,
          recipe_id: recipe_id
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.query.small_type_id = this.small_type_id
            this.crud.toQuery()

            this.recipeDrawerVisible = false
          }
        })
        .catch(() => {
          this.$message({
            message: '选择配方异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.el-table {
  border-radius: 10px;
}
.el-card {
  border: 0 !important;
  overflow: inherit;
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
