<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm" style="align-items:center">
          <div class="wrapElFormFirst col-md-8 col-12">
                        <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :default-time="['00:00:00', '23:59:59']"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="生产线：">
                <el-select v-model="query.prod_line_id" filterable clearable size="small">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产品型号：">
                <el-select v-model="query.small_model_type" filterable clearable size="small">
                  <el-option v-for="item in smallModelTypeData1" :key="item.small_model_type" :label="item.small_model_type + ' ' + item.main_material_des" :value="item.small_model_type" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="订单来源：">
                <el-input v-model="query.mo_from" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="订单号：">
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div v-if="projectCode !=='SH'" class="formChild col-md-4 col-12">
              <el-form-item label="订单批号：">
                <el-input v-model="query.product_batch" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button class="filter-item" size="small" type="success" icon="el-icon-check" :disabled="crud.selections.length === 0" plain round @click="handleMoRelease(crud.selections)">
            发布订单
          </el-button>
          <el-button size="small" type="primary" icon="el-icon-upload2" plain round @click="importDialogVisible = true">
            导入
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="生产线" prop="prod_line_id">
            <el-select v-model="form.prod_line_id" filterable clearable size="small">
              <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="产品型号" prop="small_model_type">
            <el-select v-model="form.small_model_type" filterable clearable size="small">
              <el-option v-for="item in smallModelTypeData2" :key="item.small_model_type" :label="item.small_model_type + ' ' + item.main_material_des" :value="item.small_model_type" />
            </el-select>
          </el-form-item>
          <el-form-item style="width:650px;padding-left:20px;">
            <el-table border size="small" :data="form.recipeData" style="width: 100%" highlight-current-row :header-cell-style="{ background: '#F1F4F7', padding: '0px' }" @header-dragend="crud.tableHeaderDragend()">
              <el-table-column :show-overflow-tooltip="true" prop="recipe_type_des" width="150" label="配方类型" />
              <el-table-column :show-overflow-tooltip="true" prop="recipe_name" min-width="100" label="配方" />
              <el-table-column v-if="form.mo_status === 'WAIT_PUBLISH'" :show-overflow-tooltip="true" prop="recipe_name" width="100">
                <template slot-scope="scope">
                  <el-button size="small" type="text" @click="handleRecipeChoose(scope.row.recipe_type)">{{ scope.row.recipe_id === '' ? '添加' : '修改' }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="订单来源" prop="mo_from">
            <el-input v-model="form.mo_from" />
          </el-form-item>
          <el-form-item label="订单类型" prop="mo_type">
            <fastCode fastcode_group_code="PLAN_MO_TYPE" :fastcode_code.sync="form.mo_type" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="订单号" prop="make_order">
            <el-input v-model="form.make_order" />
          </el-form-item>
          <el-form-item v-if="projectCode !=='SH'" label="打标号" prop="product_batch">
            <el-input v-model="form.product_batch" />
          </el-form-item>
          <el-form-item label="计划数量" prop="mo_plan_count">
            <el-input v-model.number="form.mo_plan_count" />
          </el-form-item>
          <el-form-item label="订单状态" prop="mo_status">
            <fastCode fastcode_group_code="PLAN_MO_STATUS" :fastcode_code.sync="form.mo_status" :option-disabled="projectCode === 'SH' && form.mo_status === 'SHUT_DOWN'" control_type="select" size="small" :clearable="false" />
          </el-form-item>
          <el-form-item label="订单排序" prop="mo_order_by">
            <el-input v-model.number="form.mo_order_by" />
          </el-form-item>
          <el-form-item label="责任人" prop="liable_person">
            <el-input v-model="form.liable_person" />
          </el-form-item>
          <el-form-item label="订单备注" prop="remarks">
            <el-input v-model="form.remarks" />
          </el-form-item>
          <el-form-item :label="projectCode === 'SH' ? '箱体号' : '总成编号'" prop="mo_custom_code">
            <el-input v-model="form.mo_custom_code" />
          </el-form-item>
          <el-form-item label="来源客户描述" prop="mo_custom_des">
            <el-input v-model="form.mo_custom_des" />
          </el-form-item>
          <el-form-item label="基准数" prop="attribute1">
            <el-input v-model="form.auctal_person" />
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div class="ruleBottom">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>

      <el-drawer append-to-body :wrapper-closable="true" title="配方选择" :visible.sync="recipeDrawerVisible" size="450px">
        <recipeChoose v-if="recipeDrawerVisible" ref="recipeChoose" :recipe_type="currentRecipeType" @chooseRecipe="chooseRecipe" />
      </el-drawer>

      <!--导入BOM-->
      <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="导入订单" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input
            v-if="isUpLoadError"
            v-model="errorMsg"
            type="textarea"
            :rows="5"
          />
          <div style="text-align: center;margin-top:10px">
            <el-link
              href="../static/order_template.xls"
              target="_blank"
              type="primary"
              download="订单信息导入模板.xls"
            >订单信息导入模板.xls</el-link>
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" @click="toButDrawerUpload">导入</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="订单ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_id }}</el-descriptions-item>
                  <el-descriptions-item label="生产线" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ getProdLineDes(props.row.prod_line_id) }}</el-descriptions-item>
                  <el-descriptions-item label="订单来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_from }}</el-descriptions-item>
                  <el-descriptions-item label="订单类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_type }}</el-descriptions-item>
                  <el-descriptions-item label="订单号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.make_order }}</el-descriptions-item>
                  <el-descriptions-item v-if="projectCode !=='SH'" label="打标号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.product_batch }}</el-descriptions-item>
                  <el-descriptions-item label="产品型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.small_model_type }}</el-descriptions-item>
                  <el-descriptions-item label="计划数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_plan_count }}</el-descriptions-item>
                  <el-descriptions-item label="实际上线数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_online_count }}</el-descriptions-item>
                  <el-descriptions-item label="实际下线数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_offline_count }}</el-descriptions-item>
                  <el-descriptions-item label="实际完成数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_finish_count }}</el-descriptions-item>
                  <el-descriptions-item label="实际报废数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_scrap_count }}</el-descriptions-item>
                  <el-descriptions-item label="班次代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.shift_code }}</el-descriptions-item>
                  <el-descriptions-item label="计划开始时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plan_start_time }}</el-descriptions-item>
                  <el-descriptions-item label="计划结束时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plan_end_time }}</el-descriptions-item>
                  <el-descriptions-item label="订单类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_sign }}</el-descriptions-item>
                  <el-descriptions-item label="订单状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.PLAN_MO_STATUS[props.row.mo_status] }}</el-descriptions-item>
                  <el-descriptions-item label="订单排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_order_by }}</el-descriptions-item>
                  <el-descriptions-item label="订单启动人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.start_by }}</el-descriptions-item>
                  <el-descriptions-item label="订单启动时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.start_date }}</el-descriptions-item>
                  <el-descriptions-item label="订单结束人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_by }}</el-descriptions-item>
                  <el-descriptions-item label="订单结束时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_date }}</el-descriptions-item>
                  <el-descriptions-item label="责任人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.liable_person }}</el-descriptions-item>
                  <el-descriptions-item label="可动率" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.standard_person }}</el-descriptions-item>
                  <el-descriptions-item label="基准数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.auctal_person }}</el-descriptions-item>
                  <el-descriptions-item label="下达者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.release_by }}</el-descriptions-item>
                  <el-descriptions-item label="下达时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.release_date }}</el-descriptions-item>
                  <el-descriptions-item label="订单备注" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.remarks }}</el-descriptions-item>
                  <el-descriptions-item :label="projectCode === 'SH' ? '箱体号' : '总成编号'" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_custom_code }}</el-descriptions-item>
                  <el-descriptions-item label="来源客户描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_custom_des }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="prod_line_id" width="100" label="生产线">
              <template slot-scope="scope">
                {{ getProdLineDes(scope.row.prod_line_id) }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="mo_from" width="100" label="订单来源" />
            <el-table-column :show-overflow-tooltip="true" prop="mo_type" width="100" label="订单类型" />
            <el-table-column :show-overflow-tooltip="true" prop="make_order" width="100" label="订单号" />
            <el-table-column v-if="projectCode !=='SH'" :show-overflow-tooltip="true" prop="product_batch" width="100" label="打标号" />
            <el-table-column :show-overflow-tooltip="true" prop="small_model_type" width="100" label="产品型号" />
            <el-table-column :show-overflow-tooltip="true" prop="mo_plan_count" width="100" label="计划数量" />
            <el-table-column :show-overflow-tooltip="true" prop="mo_online_count" width="100" label="实际上线数量" />
            <el-table-column :show-overflow-tooltip="true" prop="mo_offline_count" width="100" label="实际下线数量" />
            <el-table-column :show-overflow-tooltip="true" prop="mo_finish_count" width="100" label="实际完成数量" />
            <el-table-column :show-overflow-tooltip="true" prop="mo_scrap_count" width="100" label="实际报废数量" />
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="mo_status" width="100" label="订单状态" /> -->
            <el-table-column align="center" prop="mo_status" width="100" label="订单状态">
              <template slot-scope="scope">
                {{ dict.label.PLAN_MO_STATUS[scope.row.mo_status] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="mo_order_by" width="100" label="订单排序" />
            <el-table-column :show-overflow-tooltip="true" prop="remarks" width="100" label="订单备注" />
            <el-table-column label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="scope.row.mo_status !== 'WAIT_PUBLISH'" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import crudProdLine from '@/api/core/factory/sysProdLine'
import crudApsPlanMo from '@/api/mes/project/sh/apsPlanMo'
import crudSmallModel from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import recipeChoose from '@/views/mes/core/planMo/recipeChoose'
const defaultForm = {
  mo_id: '',
  prod_line_id: '',
  mo_from: '',
  mo_type: '',
  make_order: '',
  product_batch: '',
  small_model_type: '',
  mo_plan_count: '',
  mo_status: 'WAIT_PUBLISH',
  mo_order_by: '',
  liable_person: '',
  auctal_person: '',
  remarks: '',
  mo_custom_code: '',
  mo_custom_des: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: '',
  recipeData: []
}
export default {
  name: 'MES_APS_PLAN_MO',
  components: { crudOperation, rrOperation, udOperation, pagination, recipeChoose },
  cruds() {
    return CRUD({
      title: '订单信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mo_id',
      // 排序
      sort: ['mo_id asc'],
      // CRUD Method
      crudMethod: { ...crudApsPlanMo },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    var checkRecipe = (rule, value, callback) => {
      if (value.length !== 0) {
        if (this.form.recipeData.length === 0) {
          return callback(new Error('该产品型号未维护配方信息'))
        }
        if (this.form.recipeData.filter(item => item.recipe_id === '').length === 0) {
          callback()
        } else {
          return callback(new Error('请完善下面的配方信息'))
        }
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 310,
      permission: {
        add: ['admin', 'mes_aps_plan_mo:add'],
        edit: ['admin', 'mes_aps_plan_mo:edit'],
        del: ['admin', 'mes_aps_plan_mo:del'],
        down: ['admin', 'mes_aps_plan_mo:down']
      },
      rules: {
        prod_line_id: [{ required: true, message: '请选择产线', trigger: 'blur' }],
        mo_from: [{ required: true, message: '请选择订单来源', trigger: 'blur' }],
        mo_type: [{ required: true, message: '请选择订单类型', trigger: 'blur' }],
        make_order: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
        small_model_type: [{ required: true, message: '请选择产品型号', trigger: 'blur' }, { required: true, validator: checkRecipe, trigger: 'blur' }],
        product_batch: [{ required: true, message: '请输入订单批号', trigger: 'blur' }],
        mo_plan_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        mo_order_by: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      prodLineData: [],
      smallModelTypeData1: [],
      smallModelTypeData2: [],
      currentRecipeType: '无',
      recipeDrawerVisible: false,
      recipeTypeData: [
        { recipe_type: 'MODELDX', recipe_type_des: '模组电芯', recipe_id: '1', recipe_name: '模组配方 2022020' },
        { recipe_type: 'PROCESS', recipe_type_des: '工艺路线', recipe_id: '', recipe_name: '' },
        { recipe_type: 'PRINTMODE', recipe_type_des: '打印模板', recipe_id: '', recipe_name: '' },
        { recipe_type: 'CODERULE', recipe_type_des: '条码生成规则', recipe_id: '', recipe_name: '' },
        { recipe_type: 'SCANRULE', recipe_type_des: '扫描规则', recipe_id: '', recipe_name: '' },
        { recipe_type: 'CRAFTRULE', recipe_type_des: '工艺参数', recipe_id: '', recipe_name: '' }
      ],
      importDialogVisible: false,
      uploadLimit: 1,
      uploadAccept: '.xls',
      fileList: [],
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: '',
      projectCode: ''
    }
  },
  watch: {
    'query.prod_line_id': {
      handler() {
        this.getSmallModelTypeData(this.query.prod_line_id, 1)
      }
    },
    'form.prod_line_id': {
      handler() {
        this.getSmallModelTypeData(this.form.prod_line_id, 2)
      }
    },
    'form.small_model_type': {
      handler() {
        this.getMoRecipe()
      }
    }
  },
  // 数据字典
  dicts: ['PLAN_MO_STATUS'],
  mounted: function() {
    // 获取系统参数信息,判断是否是双环项目
    var queryParameter = {
      userName: Cookies.get('userName'),
      parameter_code: 'ProjectCode',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.projectCode = defaultQuery.data[0].parameter_val
            console.log(this.projectCode)
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 310
    }
  },
  created: function() {
    crudProdLine
      .sel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y'
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将订单为【' + data.make_order + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudApsPlanMo
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              mo_id: data.mo_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    handleProdLineChange1(val) {
      this.getSmallModelTypeData(val, 1)
    },
    handleProdLineChange2(val) {
      this.getSmallModelTypeData(val, 2)
    },
    getSmallModelTypeData(prod_line_id, type) {
      this.smallModelTypeData1 = []
      this.smallModelTypeData2 = []
      if (prod_line_id.length === 0) {
        return
      }
      crudSmallModel
        .sel({
          user_name: Cookies.get('userName'),
          prod_line_id: prod_line_id,
          sort: '',
          page: '',
          size: '',
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              if (type === 1) {
                this.smallModelTypeData1 = defaultQuery.data
              } else {
                this.smallModelTypeData2 = defaultQuery.data
              }
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    getProdLineDes(prod_line_id) {
      var item = this.prodLineData.find(item => item.prod_line_id === prod_line_id)
      if (item !== undefined) {
        return item.prod_line_code + ' ' + item.prod_line_des
      }
      return prod_line_id
    },
    getMoRecipe() {
      this.form.recipeData = []
      if (this.form.mo_id === '' && this.form.small_model_type === '') return
      crudApsPlanMo
        .selRecipe({
          user_name: Cookies.get('userName'),
          mo_id: this.form.mo_id,
          small_model_type: this.form.small_model_type
        })
        .then(res => {
          this.form.recipeData = res
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    handleRecipeChoose(recipe_type) {
      this.currentRecipeType = recipe_type
      this.recipeDrawerVisible = true
    },
    chooseRecipe(recipe_id, recipe_name) {
      const item = this.form.recipeData.filter(item => item.recipe_type === this.currentRecipeType)[0]
      item.recipe_id = recipe_id
      item.recipe_name = recipe_name
      this.recipeDrawerVisible = false
    },
    handleMoRelease(selections) {
      var moList = []
      selections.forEach(val => {
        moList.push({ mo_id: val.mo_id })
      })
      crudApsPlanMo
        .moRelease({
          user_name: Cookies.get('userName'),
          moList: moList
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '发布成功', type: 'success' })
            this.crud.toQuery()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '数据异常',
            type: 'error'
          })
        })
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = '/mes/core/MesApsPlanMoImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
.ruleBottom {
  text-align: center;
  margin: 40px 0;
}
</style>
