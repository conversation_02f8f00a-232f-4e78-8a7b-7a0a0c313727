<template>
  <el-container style="height:100%;">
    <el-header style="height:45px;padding-top:5px;">
      <el-select v-model="query.server_id" size="medium" clearable placeholder="服务" style="width: 200px;" class="filter-item" @change="changeServerId">
        <el-option v-for="item in serviceData" :key="item.server_id" :label="item.server_des" :value="item.server_id" />
      </el-select>
      <el-select v-model="query.cell_id" size="medium" clearable placeholder="Cell" style="width: 200px;" class="filter-item">
        <el-option v-for="item in cellData" :key="item.cell_id" :label="item.cell_container_des" :value="item.cell_id" />
      </el-select>
      <el-button v-if="true" class="filter-item" size="medium" type="primary" icon="el-icon-circle-plus" @click="handleAddService">
        确定
      </el-button>
      <el-tag
        v-for="(item, index) in editableTabs"
        :key="index"
        closable
        :effect="item.name === editableTabsValue ? 'dark' : 'plain'"
        :type="item.name === editableTabsValue ? 'warning' : ''"
        style="line-height:35px;height:35px;margin-left:5px;cursor:pointer;"
        @close="handleTagClose(item.name)"
        @click="editableTabsValue = item.name"
      >
        {{ item.title }}
      </el-tag>
    </el-header>
    <el-main id="hmiElMain">
      <el-tabs v-model="editableTabsValue" :closable="true" @tab-click="handleClick" @tab-remove="handleRemove">
        <el-tab-pane v-for="(item, index) in editableTabs" :key="index" :label="item.title" :name="item.name" :server_host="item.server_host" :cell_port="item.cell_port" style="padding:0px;">
          <!-- <elFrame :src="item.path" /> -->
          <hmiDetail :server_id="item.server_id + ''" :cell_id="item.cell_id + ''" />
        </el-tab-pane>
      </el-tabs>
    </el-main>
  </el-container>
</template>
<script>
import { queryAllCoreServer, queryAllCoreCell } from '@/api/hmi/index'
// import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import hmiDetail from '@/views/hmi/detail'

export default {
  components: {
    hmiDetail
  },
  data() {
    return {
      transitionName: 'slide-right',
      activeName: 'second',
      query: {
        server_id: '',
        cell_id: ''
      },
      serviceData: [],
      cellData: [],
      editableTabsValue: '0',
      editableTabs: []
    }
  },
  created: function() {
    var query = {
      enable_flag: 'Y'
    }
    queryAllCoreServer(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.serviceData = defaultQuery.data
          } else {
            this.serviceData = []
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
        this.serviceData = []
      })
  },
  methods: {
    handleTagClose(tabName) {
      this.handleRemove(tabName)
    },
    changeServerId() {
      if (this.query.server_id === '') {
        this.cellData = []
        return
      }
      var query = {
        server_id: this.query.server_id,
        enable_flag: 'Y'
      }
      queryAllCoreCell(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.count > 0) {
            this.cellData = defaultQuery.data
          } else {
            this.cellData = []
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
          this.cellData = []
        })
      this.query.cell_id = ''
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    handleAddService() {
      let obj = {}
      let obj1 = {}
      const TabName = 'Tab_' + this.query.server_id + '_' + this.query.cell_id // 规则变更需要调整detail组件中的取值逻辑
      obj = this.editableTabs.find(item => {
        return item.name === TabName
      })
      if (typeof obj !== 'undefined') {
        this.editableTabsValue = TabName
      } else {
        obj = this.serviceData.find(item => {
          return item.server_id === this.query.server_id
        })
        obj1 = this.cellData.find(item => {
          return item.cell_id === this.query.cell_id
        })
        this.editableTabs.push({
          title: obj.server_des + '/' + obj1.cell_container_des,
          name: TabName,
          path: 'http://www.baidu.com',
          server_id: this.query.server_id,
          cell_id: this.query.cell_id,
          server_host: obj.server_host,
          cell_port: obj1.cell_port
        })
        this.editableTabsValue = TabName
      }
    },
    handleRemove(tabName) {
      for (var i = 0; i < this.editableTabs.length; i++) {
        if (this.editableTabs[i].name === tabName) {
          this.editableTabs.splice(i, 1)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.el-header {
  background-color: #304156;
  color: #333;
  padding: 0 5px;
}

.el-main {
  background-color: #ffffff;
  color: #333;
  padding: 0px;
  overflow: hidden;
}

body > .el-container {
  margin-bottom: 40px;
}
.el-tabs__header {
  margin: 0 0 0px;
  display: none;
}
</style>
<style lang="less" scoped>
#hmiElMain {
  .el-tabs {
    margin-bottom: 0px;
  }
}
</style>
