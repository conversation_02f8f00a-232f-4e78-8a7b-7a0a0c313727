import request from '@/utils/request'

// 查询条码生成规则信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBarcodeRuleSel',
    method: 'post',
    data
  })
}
// 新增条码生成规则信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBarcodeRuleIns',
    method: 'post',
    data
  })
}
// 修改条码生成规则信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBarcodeRuleUpd',
    method: 'post',
    data
  })
}
// 修改条码生成规则信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBarcodeRuleEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除条码生成规则信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeBarcodeRuleDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

