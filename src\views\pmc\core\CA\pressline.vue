<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="andonlist">
            <div class="carInfoTable">
              <table class="table3 tableheight">
                <tbody>
                  <tr>
                    <td>当日计划</td>
                    <td colspan="8">
                      <table class="table3">
                        <tbody>
                          <tr class="trtitle">
                            <td class="nobordertl"><p><span>当班计划产量</span><span>(件)</span></p></td>
                            <td class="nobordert"><p><span>理论完成</span><span>(件)</span></p></td>
                            <td class="nobordert"><p><span>实际完成</span><span>(件)</span></p></td>
                            <td class="nobordert"><p><span>累计停线时间</span><span>(min)</span></p></td>
                            <td class="nobordert"><p><span>设备开动率</span><span>(%)</span></p></td>
                            <td class="nobordertr"><p><span>整线状态</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td class="noborderl"><p><span>{{ plan_num }}</span></p></td>
                            <td><p><span>{{ day_theory }}</span></p></td>
                            <td><p><span>{{ day_actual }}</span></p></td>
                            <td><p><span>{{ sum_stop_time }}</span></p></td>
                            <td><p><span>{{ device_start_rate }}</span></p></td>
                            <td class="noborderr"><p><span :class="tag_line ==='1'? 'wholelinenormal': 'wholelineerror' " class="wholeline" /></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="3" class="noborderl"><p><span>当前生产零件</span></p></td>
                            <td><p><span>计划产量</span><span>(件)</span></p></td>
                            <td><p><span>理论完成</span><span>(件)</span></p></td>
                            <td class="noborderr"><p><span>实际产量</span><span>(件)</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="3" class="noborderl"><p><span>{{ current_prod_part }}</span></p></td>
                            <!-- <td colspan="3" class="noborderl"><p><span>H650000000328  中顶盖加强板</span></p></td> -->
                            <td><p><span>{{ current_plan_num }}</span></p></td>
                            <!-- <td><p><span>400</span></p></td> -->
                            <td><p><span>{{ theory_finish }}</span></p></td>
                            <td class="noborderr"><p><span>{{ actual_num }}</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="2" class="noborderl"><p><span>目标SPM</span></p></td>
                            <td colspan="2"><p><span>实际SPM</span></p></td>
                            <td colspan="2" class="noborderr"><p><span>模具切换时间(min)</span></p></td>
                          </tr>
                          <tr class="trtitle">
                            <td colspan="2" class="noborderlb"><p><span>{{ target_spm }}</span></p></td>
                            <td colspan="2" class="noborderb"><p><span>{{ actual_spm }}</span></p></td>
                            <td colspan="2" class="noborderbr"><p><span>{{ mould_switch_time }}</span></p></td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
                <tbody>
                  <tr>
                    <td rowspan="2">下批次生产</td>
                    <td colspan="8">
                      <table class="table3">
                        <tbody>
                          <tr class="trtitle">
                            <td class="nobordertl"><p><span>下批次准备</span></p></td>
                            <td colspan="2" class="nobordert wdtd"><p><span>{{ next_prod_part }}</span></p></td>
                            <!-- <td colspan="2" class="nobordert wdtd"><p><span>H650000000004 右前车门内板本体</span></p></td> -->
                            <td class="nobordert"><p><span>计划产量</span></p></td>
                            <td class="nobordert wdtd"><p><span>{{ next_plan_num }}</span></p></td>
                            <!-- <td class="nobordert wdtd"><p><span>300</span></p></td> -->
                            <td class="nobordert"><p><span>计划生产时间</span><span>(min)</span></p></td>
                            <td class="nobordertr"><p><span>{{ plan_prod_time }}</span></p></td>
                          </tr>
                          <tr>
                            <td class="noborderl"><p><span>模具准备</span></p></td>
                            <td colspan="12" class="noborderr">
                              <div class="wrappstyle">
                                <p><span>OP10</span><span :class="tag_op10 ==='0'? 'wholelinenormal': 'wholelineerror' " class="wholeline" /></p>
                                <p><span>OP20</span><span :class="tag_op20 ==='0'? 'wholelinenormal': 'wholelineerror' " class="wholeline" /></p>
                                <p><span>OP30</span><span :class="tag_op30 ==='0'? 'wholelinenormal': 'wholelineerror' " class="wholeline" /></p>
                                <p><span>OP40</span><span :class="tag_op40 ==='0'? 'wholelinenormal': 'wholelineerror' " class="wholeline" /></p>
                                <p><span>OP50</span><span :class="tag_op50 ==='0'? 'wholelinenormal': 'wholelineerror' " class="wholeline" /></p>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle cardheadbgone">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="wrapmarquee">
            <marquee loop="infinite" scrollamount="15">
              <div class="wraptext">
                <div v-if="andon_event_list.length !==0" class="wrapandonlist">
                  <p v-for="(item,index) in andon_event_list" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                </div>
                <p v-else class="redactive">{{ below_show }}</p>
              </div>
            </marquee>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getStationTime } from '@/api/pmc/sysTime'
import { getAndonContent } from '@/api/pmc/sysAndonContent'
import { getAndonEvent } from '@/api/pmc/sysworkshopScreenone'
import { getPressLine, getTagPressLine } from '@/api/pmc/sysPressLine'
import headlbg from '@/assets/images/headlbg.png'
import qyc from '@/assets/images/qyc2.jpg'
export default {
  name: 'pressline',
  components: {
  },
  data() {
    return {
      current_prod_part: '',
      current_plan_num: '',
      next_prod_part: '',
      next_plan_num: '',
      plan_num: '',
      target_spm: '',
      actual_spm: '',
      mould_switch_time: '',
      plan_prod_time: '',
      sum_stop_time: '',
      device_start_rate: '',

      theory_finish: '',
      actual_num: '',
      day_theory: '',
      day_actual: '',
      tag_line: '0',
      tag_op10: '0',
      tag_op20: '0',
      tag_op30: '0',
      tag_op40: '0',
      tag_op50: '0',

      prod_line_des: '冲压A线',
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      stationCarArr: [],
      rownum: '',
      totalnum: '',
      headlbg: headlbg,
      qyc: qyc,
      below_show: '',
      andon_event_list: []
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    setInterval(() => {
      this.initstationTime()
    }, 1000)
    // 加载工位屏标语
    this.initAndonContent()
    setInterval(() => {
      this.initAndonContent()
    }, 5000)
    // 加载工位屏andon事件
    this.initAndonEvent()
    this.andonEventTimer = setInterval(() => {
      this.initAndonEvent()
    }, 2000)
    // 查询大屏冲压显示信息
    this.initPressLine()
    setInterval(() => {
      this.initPressLine()
    }, 30000)
    // 查询大屏冲压点位显示信息
    this.initTagPressLine()
    setInterval(() => {
      this.initTagPressLine()
    }, 1000)
  },
  mounted() {
  },
  methods: {
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏标语
    initAndonContent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        work_center_code: this.$route.query.work_center_code,        
        line_section_code:this.$route.query.line_section_code,
        fastcode_group_code: this.$route.query.prod_line_code + '_UP_DOWN_LINE_STATION'
      }
      getAndonContent(query)
        .then(res => {
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            this.sum_stop_time = ''
            this.device_start_rate = ''
            return
          }
          if (res.contentList === null || res.contentList.length === 0) {
            this.below_show = ''
            this.sum_stop_time = ''
            this.device_start_rate = ''
          }
          this.below_show = res.contentList[0].below_show
          this.device_start_rate = res.device_mobility
          this.sum_stop_time = res.today_stop
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏andon事件
    initAndonEvent() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        prod_line_type: this.$route.query.prod_line_type
      }
      getAndonEvent(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data[0].andon_event_list === null || res.data[0].andon_event_list.length === 0) {
            this.andon_event_list = []
          }
          this.andon_event_list = res.data[0].andon_event_list
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 查询大屏冲压显示信息
    initPressLine() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        work_center_code: this.$route.query.work_center_code
      }
      getPressLine(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data === null || res.data.length === 0) {
            this.current_prod_part = ''
            this.current_plan_num = ''
            this.next_prod_part = ''
            this.next_plan_num = ''
            this.plan_num = ''
            this.target_spm = ''
            this.plan_prod_time = ''
            // this.sum_stop_time = ''
            // this.device_start_rate = ''
          }
          this.current_prod_part = res.data[0].current_prod_part
          this.current_plan_num = res.data[0].current_plan_num
          this.next_prod_part = res.data[0].next_prod_part
          this.next_plan_num = res.data[0].next_plan_num
          this.plan_num = res.data[0].plan_num
          this.target_spm = res.data[0].target_spm
          this.plan_prod_time = res.data[0].plan_prod_time
          // this.sum_stop_time = res.data[0].sum_stop_time
          // this.device_start_rate = res.data[0].device_start_rate
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 查询大屏冲压点位显示信息
    initTagPressLine() {
      var query = {
        prod_line_code: this.$route.query.prod_line_code,
        work_center_code: this.$route.query.work_center_code
      }
      getTagPressLine(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data === null || res.data.length === 0) {
            this.theory_finish = ''
            this.actual_num = ''
            this.day_theory = ''
            this.day_actual = ''
            this.actual_spm = ''
            this.mould_switch_time = ''
            this.tag_line = '0'
            this.tag_op10 = '0'
            this.tag_op20 = '0'
            this.tag_op30 = '0'
            this.tag_op40 = '0'
            this.tag_op50 = '0'
          }
          this.theory_finish = res.data[0].theory_finish
          this.actual_num = res.data[0].actual_num
          this.day_theory = res.data[0].day_theory
          this.day_actual = res.data[0].day_actual
          this.actual_spm = res.data[0].actual_spm
          this.mould_switch_time = res.data[0].mould_switch_time
          this.tag_line = res.data[0].tag_line
          this.tag_op10 = res.data[0].tag_op10
          this.tag_op20 = res.data[0].tag_op20
          this.tag_op30 = res.data[0].tag_op30
          this.tag_op40 = res.data[0].tag_op40
          this.tag_op50 = res.data[0].tag_op50
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.marginT{
  margin-top: 10px;
}
.slideT{
  margin-top: 20px;
}
.cardheadbg{
  background-color: #031c45;
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  // margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .andonlist{
  height: 545px;
  ul{
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    margin-right: -17px;
        margin-left: 10px;
    li{
      list-style: none;
      width: 13%;
      height: 99px;
      text-align: center;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 5px;
      padding: 5px;
      position: relative;
      border-radius: 5px;
      margin-right: 8px;
      span{
        font-size: 30px;
        font-weight: 700;
        z-index: 2;
      }
      .carorder{
        font-size: 16px;
      }
      img{
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
    }
  }
}
.cardheadbgone{
  background-color: #0070c2;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  .wrapandonlist{
    display: flex;
    align-items: center;
    p{
      margin-right: 50px;
    }
  }
  p{
  color: #fbff03;
  font-size: 60px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  height: 80px;
  word-spacing: 150px;
  }
}
::v-deep .commonstatus{
  background-color: #ffffff;
}
::v-deep .nomalstatus{
  background-color: #00a047;
  span{
    color: #ffffff;
  }
}
::v-deep .errorstatus{
  background-color: #ff0000;
}
::v-deep .warningstatus{
  background-color: #ffd600;
}
::v-deep .el-card{
  border-radius: 0;
}
.redactive{
  color: red !important;
}

/* Table 3 Style */
table.table3{
    font-size: 18px;
    text-align:center;
    border-collapse:collapse;
    width: 100%;
}
.table3 tbody td{
    background-color: #1d3a6a;
    color: #fff;
    border: 1px solid #ffffff;
    font-size: 26px;
    font-weight: bold;
    text-shadow: 2px 2px 4px #000;
}
.trtitle td p{
  margin: 10px 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}
.wholeline{
  width:30px;
  height: 30px;
  border-radius: 50%;
}
.wholelinenormal{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.wholelineerror{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #f00;
  box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgb(0 0 0 / 30%);
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.noborder{
  border: 0 !important;
}
.wrappstyle{
  display: flex;
  justify-content: space-around;
  p{
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    span{
      margin: 0 5px;
    }
  }
}
.nobordertl{
  border-top: 0 !important;
  border-left: 0 !important;
}
.nobordertr{
  border-top: 0 !important;
  border-right: 0 !important;
}
.noborderbr{
  border-bottom: 0 !important;
  border-right: 0 !important;
}
.nobordert{
  border-top: 0 !important;
}
.noborderr{
  border-right: 0 !important;
}
.noborderb{
  border-bottom: 0 !important;
}
.noborderl{
  border-left: 0 !important;
}
.noborderlb{
  border-left: 0 !important;
  border-bottom: 0 !important;
}
.wdtd{
  width: 300px;
}
@-webkit-keyframes heart {
  0% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  30% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  50% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  75% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  80% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
}

@keyframes heart {
  0% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  30% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  50% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  75% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1);
  }
  80% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
  100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
  }
}
</style>
