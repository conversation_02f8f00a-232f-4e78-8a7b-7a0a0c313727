/* element表格自动滚动实现
* <AUTHOR>
* @param {} options <br>
* @date 2024-04-016编写
* @return crud instance.
* @example*/
export function autoScroll(flag, tableRef) {
  const table = this.$refs[tableRef]
  const divData = table.$refs.bodyWrapper
  if (flag) {
    clearInterval(this[tableRef])
  } else {
    this[tableRef] = setInterval(() => {
      // 元素自增距离顶部1像素
      divData.scrollTop += 1
      if (divData.clientHeight + divData.scrollTop === divData.scrollHeight) {
        divData.scrollTop = 0
      }
    }, 500)
  }
}
