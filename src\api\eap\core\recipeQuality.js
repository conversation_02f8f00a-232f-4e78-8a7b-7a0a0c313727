import request from '@/utils/request'

// 查询工艺路线-工位-工序-质量信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeQualitySel',
    method: 'post',
    data
  })
}
// 查询工艺路线-工位-工序-质量信息
export function selTagColOrder(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeTagColOrderSel',
    method: 'post',
    data
  })
}

// 新增工艺路线-工位-工序-质量信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeQualityIns',
    method: 'post',
    data
  })
}
// 修改工艺路线-工位-工序-质量信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeQualityUpd',
    method: 'post',
    data
  })
}
// 修改工艺路线-工位-工序-质量信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeQualityEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除工艺路线-工位-工序-质量信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeQualityDel',
    method: 'post',
    data
  })
}

// 批量新增工艺路线-工位-工序-质量信息
export function batchAdd(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeQualityBatchIns',
    method: 'post',
    data
  })
}

export default { sel, selTagColOrder, add, edit, del, editEnableFlag, batchAdd }
