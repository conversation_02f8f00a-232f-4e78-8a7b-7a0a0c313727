<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.applicationMenu.programCodingDescription')">  <!-- 程序编码/描述： -->
                <el-input v-model="query.functionCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">  <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.applicationMenu.programCode')" prop="function_code">  <!-- 程序编码 -->
            <el-input v-model="form.function_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.applicationMenu.programDescription')" prop="function_des">  <!-- 程序描述 -->
            <el-input v-model="form.function_des" />
          </el-form-item>
          <!--快速编码：TERMINAL_TYPE-->
          <el-form-item :label="$t('lang_pack.applicationMenu.programType')" prop="menu_type_code">  <!-- 程序类型 -->
            <fastCode fastcode_group_code="TERMINAL_TYPE" :fastcode_code.sync="form.menu_type_code" :multiple="true" :filterable="true" control_type="select" size="small" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.applicationMenu.programPath')" prop="function_path">  <!-- 程序路径 -->
            <el-input v-model="form.function_path" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.applicationMenu.programDependence')" prop="function_refer">  <!-- 程序依赖 -->
            <el-input v-model="form.function_refer" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.applicationMenu.programAttribute')" prop="function_attr">  <!-- 程序属性 -->
            <el-input v-model="form.function_attr" />
          </el-form-item>
          <!--快速编码：ENABLE_FLAG-->
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">  <!-- 有效标识 -->
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="function_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="function_code" :label="$t('lang_pack.applicationMenu.programCode')" />  <!-- 程序编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="function_des" :label="$t('lang_pack.applicationMenu.programDescription')" />  <!-- 程序描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="menu_type_code" width="100" :label="$t('lang_pack.applicationMenu.programType')" />  <!-- 程序类型 -->
            <el-table-column  :show-overflow-tooltip="true" prop="function_path" width="230" :label="$t('lang_pack.applicationMenu.programPath')" />  <!-- 程序路径 -->
            <el-table-column  :show-overflow-tooltip="true" prop="function_refer" width="200" :label="$t('lang_pack.applicationMenu.programDependence')" /> <!-- 程序依赖 -->
            <el-table-column  :show-overflow-tooltip="true" prop="function_attr" width="200" :label="$t('lang_pack.applicationMenu.programAttribute')" />  <!-- 程序属性 -->
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">  <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">  <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudFunction from '@/api/core/system/sysFunction'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  function_id: '',
  function_code: '',
  function_des: '',
  menu_type_code: '',
  function_path: '',
  function_refer: '',
  function_attr: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_FUNCTION',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '程序菜单',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'function_id',
      // 排序
      sort: ['function_id asc'],
      // CRUD Method
      crudMethod: { ...crudFunction },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      },
      rules: {
        // 提交验证规则
        function_code: [{ required: true, message: '请输入程序编码', trigger: 'blur' }],
        function_path: [{ required: true, message: '请输入路径', trigger: 'blur' }]
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  methods: {}
}
</script>
