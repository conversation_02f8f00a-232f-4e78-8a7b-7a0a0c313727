import request from '@/utils/request'

//查询接口明细
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfDSel',
    method: 'post',
    data
  })
}
//新增接口明细
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfDIns',
    method: 'post',
    data
  })
}
//修改接口明细
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfDUpd',
    method: 'post',
    data
  })
}
//删除接口明细
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysEsbInterfDDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
