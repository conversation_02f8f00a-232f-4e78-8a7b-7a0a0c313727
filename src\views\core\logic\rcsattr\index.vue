<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect wrapFile">
            <span>{{ $t('lang_pack.logicfunc.automatedLogicalPropertyConfiguration') }}</span>
            <!-- 自动化逻辑属性配置 -->
          </div>
          <el-tree :data="treeData" :props="defaultProps" :highlight-current="true" @node-click="handleNodeClick" />
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-card class="box-card1" :logicfuncid="currentLogicFuncId" :logicfuncgid="currentLogicFuncGId" :logicattrgroupid="currentLogicAttrGroupId">
          <div slot="header" class="wrapTextSelect">
            <span>{{ rightHeaderTitle }}</span>
            <div class="wrapSearch">
              <div class="inputSearch">
                <el-input v-model="queryHeader.content" :placeholder="contentPlaceholder" class="filter-item inputItem" size="small" />
                <el-button class="filter-item buttonItem" size="small" type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>
                <!-- 搜索 -->
              </div>
              <el-button v-if="true" class="filter-item" size="small" type="primary" icon="el-icon-plus" plain @click="handleAdd">{{ $t('lang_pack.commonPage.add') }}</el-button>
              <!-- 新增 -->
            </div>
          </div>

          <logicFunc v-if="logicFuncShow" ref="logicFunc" @AddTreeNode="handleLogicFuncAddTreeNode" @EditTreeNode="handleLogicFuncEditTreeNode" @DelTreeNode="handleLogicFuncDelTreeNode" />
          <logicFuncG v-if="logicFuncGShow" ref="logicFuncG" @AddTreeNode="handleLogicFuncGAddTreeNode" @EditTreeNode="handleLogicFuncGEditTreeNode" @DelTreeNode="handleLogicFuncGDelTreeNode" />
          <logicFuncI v-if="logicFuncIShow" ref="logicFuncI" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import logicFunc from '@/views/core/logic/rcsattr/logicFunc'
import logicFuncG from '@/views/core/logic/rcsattr/logicFuncG'
import logicFuncI from '@/views/core/logic/rcsattr/logicFuncI'

import { logicFuncTree } from '@/api/core/logic/logicFunc'

export default {
  name: 'RCS_LOGIC_FUNC',
  components: { logicFunc, logicFuncG, logicFuncI },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',

      contentPlaceholder: '自动化逻辑编码/描述',
      rightHeaderTitle: '自动化逻辑列表',
      queryHeader: {
        content: ''
      },

      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentNode: [],

      logicFuncShow: true,
      logicFuncGShow: false,
      logicFuncIShow: false,
      currentLogicFuncId: '0',
      currentLogicFuncGId: '0',
      currentLogicAttrGroupId: '0'
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // Fun 树
    logicFuncTree({})
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        this.treeData = []
        var a = {}
        a.level = 1
        a.label = '自动逻辑'
        a.children = []
        if (defaultQuery.data.length > 0) {
          a.children = defaultQuery.data
        }
        this.treeData.push(a)
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    handleNodeClick(data) {
      this.currentNode = data
      this.queryHeader.content = ''
      if (data.level === 1) {
        this.logicFuncGShow = false
        this.logicFuncIShow = false
        if (this.logicFuncShow) {
          this.$refs['logicFunc'].toButQuery('')
        } else {
          this.logicFuncShow = true
        }
        this.rightHeaderTitle = '自动化逻辑列表'
        this.contentPlaceholder = '自动化逻辑编码/描述'
      } else if (data.level === 2) {
        this.logicFuncShow = false
        this.logicFuncIShow = false
        this.currentLogicFuncId = data.logic_func_id
        if (this.logicFuncGShow) {
          this.$refs['logicFuncG'].toButQuery(data.logic_func_id, '')
        } else {
          this.logicFuncGShow = true
        }
        this.rightHeaderTitle = '属性组列表'
        this.contentPlaceholder = '属性组描述'
      } else if (data.level === 3) {
        this.logicFuncShow = false
        this.logicFuncGShow = false
        this.currentLogicFuncGId = data.logic_func_g_id
        this.currentLogicAttrGroupId = data.logic_attr_group_id
        if (this.logicFuncIShow) {
          this.$refs['logicFuncI'].toButQuery(data.logic_func_g_id, data.logic_attr_group_id, '')
        } else {
          this.logicFuncIShow = true
        }
        this.rightHeaderTitle = '属性列表'
        this.contentPlaceholder = '属性描述'
      }
    },

    // 自动化逻辑
    handleLogicFuncAddTreeNode(logic_func_id, func_code, func_des) {
      const item = this.treeData.filter(item => item.level === 1)[0]
      if (!item.children) {
        this.$set(item, 'children', [])
      }
      item.children.push({
        level: 2,
        logic_func_id: logic_func_id,
        func_code: func_code,
        func_des: func_des,
        label: func_des
      })
    },
    handleLogicFuncEditTreeNode(logic_func_id, func_code, func_des) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const items = children.filter(item => item.logic_func_id + '' === logic_func_id + '')[0]
      items.func_code = func_code
      items.func_des = func_des
      items.label = func_des
    },
    handleLogicFuncDelTreeNode(logic_func_id) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const index = children.findIndex(d => d.logic_func_id + '' === logic_func_id + '')
      children.splice(index, 1)
    },

    // 自动化逻辑组
    handleLogicFuncGAddTreeNode(logic_func_id, logic_func_g_id, logic_attr_group_des, logic_attr_group_id) {
      if (!this.currentNode.children) {
        this.$set(this.currentNode, 'children', [])
      }
      this.currentNode.children.push({
        level: 3,
        logic_func_id: logic_func_id,
        logic_func_g_id: logic_func_g_id,
        logic_attr_group_des: logic_attr_group_des,
        label: logic_attr_group_des,
        logic_attr_group_id: logic_attr_group_id
      })
    },
    handleLogicFuncGEditTreeNode(logic_func_g_id, logic_attr_group_des, logic_attr_group_id) {
      const items = this.currentNode.children.filter(item => item.logic_func_g_id + '' === logic_func_g_id + '')[0]
      items.logic_attr_group_des = logic_attr_group_des
      items.label = logic_attr_group_des
      items.logic_attr_group_id = logic_attr_group_id
    },
    handleLogicFuncGDelTreeNode(logic_func_g_id) {
      // const children = this.treeData.filter(item => item.level === 1)[0].children
      const index = this.currentNode.children.findIndex(d => d.logic_func_g_id + '' === logic_func_g_id + '')
      this.currentNode.children.splice(index, 1)
    },

    // 查询 按钮
    handleQuery() {
      if (this.logicFuncShow) {
        this.$refs['logicFunc'].toButQuery(this.queryHeader.content)
      } else if (this.logicFuncGShow) {
        this.$refs['logicFuncG'].toButQuery(this.currentLogicFuncId, this.queryHeader.content)
      } else if (this.logicFuncIShow) {
        this.$refs['logicFuncI'].toButQuery(this.currentLogicFuncGId, this.currentLogicAttrGroupId, this.queryHeader.content)
      }
    },
    // 新增 按钮
    handleAdd() {
      if (this.logicFuncShow) {
        this.$refs['logicFunc'].toButAdd()
      } else if (this.logicFuncGShow) {
        this.$refs['logicFuncG'].toButAdd()
      } else if (this.logicFuncIShow) {
        this.$refs['logicFuncI'].toButAdd()
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-card__header {
  padding: 8px;
  height: auto;
}
.box-card1 {
  min-height: calc(100vh - 60px);
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}
.wrapTextSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    white-space: nowrap;
    font-size: 14px;
    color: rgb(117, 117, 117);
    font-weight: bold;
    margin-right: 10px;
  }
}
.wrapSearch {
  display: flex;
  align-items: center;
  .inputSearch {
    display: flex;
    align-items: center;
    margin-right: 15px;
    .inputItem {
      .el-input__inner {
        width: 150px;
        border-radius: 4px 0 0 4px !important;
      }
    }
    .buttonItem {
      border-radius: 0 0.25rem 0.25rem 0;
      margin-left: -5px;
      z-index: 9;
    }
  }
}
.wrapFile {
  height: 34px;
  line-height: 34px;
}
.el-tree--highlight-current {
  .el-tree-node__content {
    padding: 10px;
    height: auto;
  }
}
.el-tree-node__expand-icon {
  color: #79a0f1;
}
.el-tree-node__label {
  font-size: 12px;
}
.el-tree-node__content:hover {
  background-color: #e8efff;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e8efff;
}
</style>
