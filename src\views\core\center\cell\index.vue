<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="elFirstCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.cell.cellNameDescription')">
                <!-- 单元名称/描述： -->
                <el-input v-model="query.cellContainerNameDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
          <!--表：sys_core_server-->
          <el-form-item :label="$t('lang_pack.cell.serve')">
            <!-- 服务 -->
            <el-select v-model="form.server_id">
              <el-option v-for="item in serverData" :key="item.server_code" :label="item.server_des" :value="item.server_id" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.cell.cellName')" prop="cell_container_name">
            <!-- 单元名称 -->
            <el-input v-model="form.cell_container_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.cellDescription')" prop="cell_container_des">
            <!-- 单元描述 -->
            <el-input v-model="form.cell_container_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.mirrorName')" prop="cell_image_name">
            <!-- 镜像名称 -->
            <el-input v-model="form.cell_image_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.webPort')" prop="cell_webapi_port">
            <!-- Web端口 -->
            <el-input v-model="form.cell_webapi_port" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.mqttPort')" prop="cell_mqtt_port">
            <!-- MQTT端口 -->
            <el-input v-model="form.cell_mqtt_port" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.opcUaPort')" prop="cell_opcua_port">
            <!-- OPC UA端口 -->
            <el-input v-model="form.cell_opcua_port" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.cellPort')" prop="cell_portlist_map">
            <!-- 单元端口 -->
            <el-input v-model="form.cell_portlist_map" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.cell.startOpcUa')">
            <!-- 启动OPC UA -->
            <el-radio-group v-model="form.cell_opcua_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="cell_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="server_id" :label="$t('lang_pack.cell.serve')">
              <!-- 服务 -->
              <template slot-scope="scope">
                {{ getServerDes(scope.row.server_id) }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="cell_container_name" :label="$t('lang_pack.cell.cellName')" width="160"/>
            <!-- 单元名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_container_des" :label="$t('lang_pack.cell.cellDescription')" width="160"/>
            <!-- 单元描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_image_name" :label="$t('lang_pack.cell.mirrorName')" width="160"/>
            <!-- 镜像名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_webapi_port" :label="$t('lang_pack.cell.webPort')" />
            <!-- Web端口 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_mqtt_port" :label="$t('lang_pack.cell.mqttPort')" />
            <!-- Mqtt端口 -->
            <el-table-column  :label="$t('lang_pack.cell.startOpcUa')" align="center" prop="cell_opcua_flag">
              <!-- 启动OPC UA -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WHETHER_FLAG[scope.row.cell_opcua_flag] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="cell_opcua_port" :label="$t('lang_pack.cell.opcUaPort')" />
            <!-- OPC UA端口 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_portlist_map" :label="$t('lang_pack.cell.cellPort')" width="160"/>
            <!-- 单元端口 -->
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudCell from '@/api/core/center/cell'
import { lovServer } from '@/api/core/center/server'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  cell_id: '',
  server_id: '',
  cell_container_name: '',
  cell_container_des: '',
  cell_image_name: '',
  cell_webapi_port: '8090',
  cell_mqtt_port: '8083',
  cell_opcua_flag: 'N',
  cell_opcua_port: '0',
  cell_portlist_map: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_CELL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '单元维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudCell },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_core_cell:add'],
        edit: ['admin', 'sys_core_cell:edit'],
        del: ['admin', 'sys_core_cell:del'],
        down: ['admin', 'sys_core_cell:down']
      },
      rules: {
        server_id: [{ required: true, message: '请选择服务', trigger: 'blur' }],
        cell_container_name: [{ required: true, message: '请输入单元名称', trigger: 'blur' }],
        cell_webapi_port: [{ required: true, message: '请输入Web端口', trigger: 'blur' }],
        cell_mqtt_port: [{ required: true, message: '请输入Mqtt端口', trigger: 'blur' }]
      },
      // 服务数据
      serverData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WHETHER_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    lovServer(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.serverData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 获取服务的中文描述
    getServerDes(server_id) {
      var item = this.serverData.find(item => item.server_id === server_id)
      if (item !== undefined) {
        return item.server_des
      }
      return server_id
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .elFirstCard {
  .el-card__body {
    padding: 15px !important;
    padding-bottom: 5px !important;
  }
}
</style>
