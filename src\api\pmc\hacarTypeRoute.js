import request from '@/utils/request'

// 查询焊装产线
export function haLineCodeSel(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/HaLineCodeSel',
    method: 'post',
    data
  })
}

// 查询焊装工位
export function haLineStationSel(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/HaLineStationSel',
    method: 'post',
    data
  })
}

// 查询工艺路线
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/HaCarTypeRouteSel',
    method: 'post',
    data
  })
}

// 增加工艺路线
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/HaCarTypeRouteIns',
    method: 'post',
    data
  })
}

// 修改工艺路线
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/HaCarTypeRouteUpd',
    method: 'post',
    data
  })
}

// 删除工艺路线
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/project/bq/HaCarTypeRouteDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, haLineCodeSel, haLineStationSel }

