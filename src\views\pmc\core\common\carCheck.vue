<template>
  <div class="screen_container">
    <div class="screen_head">
      <h2>北汽重卡车辆查询</h2>
      <div class="headLogo">
        <img class="logo" src="@/assets/images/logw.png">
      </div>
    </div>
    <div class="mainbox">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <el-input v-model="input1" placeholder="请输入你要搜索的车辆信息">
            <el-button slot="append" icon="el-icon-search" />
          </el-input>
        </div>
        <div class="text item">
          <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item>
              <template slot="label">
                当前位置：
              </template>
              kooriookami
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                VIN号：
              </template>
              18100000000
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                订单号：
              </template>
              苏州市
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                车型：
              </template>
              car1
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                DMS：
              </template>
              江苏省苏州市吴中区吴中大道 1188 号
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                颜色描述：
              </template>
              江苏省苏州市吴中区吴中大道 1188 号
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PMC_CAR_CHECK',
  data() {
    return {
      input1: ''
    }
  },
  created: function() {
  },
  mounted() {
  },
  // 离开此页面时销毁定时器
  beforeDestroy() {

  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.screen_container{
  background: url('~@/assets/images/carCheck.jpg') no-repeat #000;
  background-size: cover;
  height:calc(100vh);
  .screen_head{
    position: relative;
    height: 140px;
    h2{
        margin: 0;
        height: 140px;
        line-height: 120px;
        text-align: center;
        color: #ffffff;
        font-weight: 700;
        font-size: 50px;
    }
    .headLogo{
      width: 100%;
      position: absolute;
      top: 70px;
      padding: 0 30px;
      text-align: right;
      .logo{
       width: 180px;
       margin-right: 2%;
      }
    }
  }
  .mainbox{
    display: flex;
    justify-content: center;
  }
  .box-card {
    width: 60%;
    margin-top: 2%;
  }
}
:deep(.el-input__inner){
    height: 60px;
    line-height: 60px;
    font-size: 24px;
}
:deep(.el-input-group__append){
  padding: 0 26px;
}
:deep(.el-icon-search){
  font-size: 24px;
}
:deep(.el-descriptions--small.is-bordered .el-descriptions-item__cell){
    padding: 16px 10px;
    font-size: 18px;
}
</style>
