import request from '@/utils/request'
// 查询到货清单
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListSelect',
    method: 'post',
    data
  })
}
// 删除到货清单
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListDelete',
    method: 'post',
    data
  })
}
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListInsert',
    method: 'post',
    data
  })
}
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListUpdate',
    method: 'post',
    data
  })
}

// 计划状态更新
export function planStatusUpd(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListStatusUpd',
    method: 'post',
    data
  })
}

// 手动入库
export function planManual(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListManual',
    method: 'post',
    data
  })
}

// 库存同步
export function planInventorySend(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanListInventorySend',
    method: 'post',
    data
  })
}
export default { sel, del, add, edit, planStatusUpd, planManual, planInventorySend }
