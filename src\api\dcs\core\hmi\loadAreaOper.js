import request from '@/utils/request'

// 查询上料区库位
export function stockSel(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsAllStockSelect',
    method: 'post',
    data: data
  })
}

// 查询任务管理
export function planTask(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsPlanApsTaskSelect',
        method: 'post',
        data: data
    })
}

// 查询上移
export function taskUp(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsPlanApsTaskUp',
        method: 'post',
        data: data
    })
}

// 查询下移
export function taskDown(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsPlanApsTaskDown',
        method: 'post',
        data: data
    })
}

// 查询过站信息
export function sel(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsApsTaskStationFlow',
        method: 'post',
        data: data
    })
}

// 查询图纸信息
export function taskDraw(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsApsTaskResolve',
        method: 'post',
        data: data
    })
}

// 任务管理有效标识
export function editEnableFlag(data) {
    return request({
        url: 'aisEsbWeb/dcs/core/DcsApsIntefProducTaskEnableFlag',
        method: 'post',
        data: data
    })
}
// 手动控制
export function feedCon(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsApsFeedConsole',
        method: 'post',
        data: data
    })
}
// 流程管理
export function flowTask(data) {
    return request({
        url: 'aisEsbWeb/dcs/core/flowTaskListSelect',
        method: 'post',
        data: data
    })
}

// 库位查询接口
export function stockSelect(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsApsAtPresentRepositoryLocation',
        method: 'post',
        data: data
    })
}

// 获取目标库位接口
export function repository(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsApsTargetRepositoryLocation',
        method: 'post',
        data: data
    })
}

// 天车基础数据
export function carSelect(data) {
    return request({
        url: 'aisEsbWeb/dcs/core/DcsApsFmodCarSelect',
        method: 'post',
        data: data
    })
}

// 倒库
export function storeHouse(data) {
    return request({
        url: 'aisEsbWeb/dcs/hmi/DcsApsInvertedStorehouse',
        method: 'post',
        data: data
    })
}

// 新增抛丸任务
export function wmsPwTask(data) {
    return request({
        url: 'aisEsbWeb/dcs/core/DcsApsWmsPwTask',
        method: 'post',
        data: data
    })
}

export default { stockSel,planTask,taskUp,taskDown,sel,taskDraw,editEnableFlag,feedCon,
    flowTask,stockSelect,repository,carSelect,storeHouse,wmsPwTask}

