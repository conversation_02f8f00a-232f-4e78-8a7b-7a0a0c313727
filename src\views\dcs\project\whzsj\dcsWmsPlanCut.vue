<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="切割计划单号:">
                <el-input v-model="query.scli_plan_no" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="项目号:">
                <el-input v-model="query.project_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="批次号:">
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable filterable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="700px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="180px"
              :inline="true"
            >
              <el-form-item label="到货单号" prop="list_num">
                <el-input v-model="form.list_num" />
              </el-form-item>
              <el-form-item label="计划来源" prop="plan_from">
                <el-input v-model="form.plan_from" />
              </el-form-item>
              <el-form-item label="有效标识" prop="enable_flag">
                <el-radio-group v-model="form.enable_flag">
                  <el-radio label="Y">有效</el-radio>
                  <el-radio label="N">失效</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column
              label=""
              align="center"
              width="280"
              fixed="left"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" style="color: #00c2fd;" @click="viewDetail(scope)">查看明细</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  :disabled="scope.row.plan_status == 'PLAN'"
                  @click="planManualExecute(scope.row.plan_cut_id)"
                >切割上料</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  :disabled="scope.row.plan_status == 'PLAN'"
                  @click="planInventoryExecute(scope.row.plan_cut_id)"
                >库存同步</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  :disabled="scope.row.plan_status != 'PLAN'"
                  @click="planStatusExecute(scope.row.plan_cut_id,'WORK')"
                >执行</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  :disabled="scope.row.plan_status != 'PLAN' "
                  @click="planStatusExecute(scope.row.plan_cut_id,'CANCEL')"
                >取消</el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="scli_plan_no" label="切割计划单号" />
            <el-table-column :show-overflow-tooltip="true" prop="project_code" label="项目号" />
            <el-table-column :show-overflow-tooltip="true" prop="block_code" label="分段号" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_num" label="批次号" />
            <el-table-column :show-overflow-tooltip="true" prop="start_time" label="备料开始时间" />
            <el-table-column :show-overflow-tooltip="true" prop="end_time" label="备料结束时间" />

            <el-table-column label="计划来源" align="center" prop="计划来源">
              <template slot-scope="scope">
                {{ dict.label.PLAN_FROM[scope.row.plan_from] }}
              </template>
            </el-table-column>
            <el-table-column label="计划状态" align="center" prop="计划状态">
              <template slot-scope="scope">
                {{ dict.label.PLAN_STATUS[scope.row.plan_status] }}
              </template>
            </el-table-column>

            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!--
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="240"
              fixed="right"
            >
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" :delete-edit="false" />
              </template>
            </el-table-column>-->
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <selectModal ref="selectModal" :footer="false" />
  </div>
</template>

<script>
import selectModal from '@/components/selectModal'
import crudWmsPlanCut from '@/api/dcs/project/whzsj/wmsPlanCut'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  plan_from: 'AIS',
  enable_flag: 'Y'
}
export default {
  name: 'WMS_PLAN_CUT',
  components: { crudOperation, rrOperation, udOperation, pagination, selectModal },
  cruds() {
    return CRUD({
      title: '切割计划',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'plan_cut_id',
      // 排序
      sort: ['plan_cut_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsPlanCut },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      rules: {
        list_num: [{ required: true, message: '请输入到货单号', trigger: 'blur' }],
        plan_from: [{ required: true, message: '请输入计划来源', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG', 'PLAN_FROM', 'PLAN_STATUS'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    viewDetail(scope) {
      this.$refs.selectModal.open({
        type: 'qgjh', // 切割计划
        checkType: '',
        search: {
          plan_cut_id: scope.row.plan_cut_id,
          sort: 'plan_cut_d_id desc',
          user_name: Cookies.get('userName')
        }
      })
    },

    // 切割上料
    planManualExecute(planCutId) {
      this.$confirm(`当前计划确定要切割上料吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            plan_cut_id: planCutId,
            user_name: Cookies.get('userName')
          }
          crudWmsPlanCut.planManual(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('切割上料成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '切割上料失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '切割上料失败',
              type: 'error'
            })
          })
        })
        .catch(() => {})
    },

    // 库存同步
    planInventoryExecute(planCutId) {
      this.$confirm(`当前计划确定要库存同步吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            plan_cut_id: planCutId,
            user_name: Cookies.get('userName')
          }
          crudWmsPlanCut.planInventorySend(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('库存同步成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '库存同步失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '库存同步失败',
              type: 'error'
            })
          })
        })
        .catch(() => {})
    },

    // 执行
    planStatusExecute(planCutId, planStatus) {
      this.$confirm(`确定要执行当前计划吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            plan_cut_id: planCutId,
            plan_status: planStatus,
            user_name: Cookies.get('userName')
          }
          crudWmsPlanCut.planStatusUpd(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('执行成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '执行失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '执行失败',
              type: 'error'
            })
          })
        })
        .catch(() => {})
    }
  }
}
</script>

          <style>
          .table-descriptions-label {
          width: 150px;
          }
          .table-descriptions-content {
          width: 150px;
          color: #b1be26;
          }

          </style>
