<template>
  <div class="drawer-container1">
    <!-- <div>
      <h3 class="drawer-title">系统布局设置</h3>

      <div class="drawer-item">
        <span>主题颜色</span>
        <theme-picker style="float: right;height: 26px;margin: -3px 8px 0 0;" @change="themeChange" />
      </div>

      <div class="drawer-item">
        <span>显示标签</span>
        <el-switch v-model="tagsView" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>固定头部</span>
        <el-switch v-model="fixedHeader" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>显示LOGO</span>
        <el-switch v-model="sidebarLogo" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>菜单UniqueOpened</span>
        <el-switch v-model="uniqueOpened" class="drawer-switch" />
      </div>
    </div> -->
    <el-drawer append-to-body :wrapper-closable="false" title="系统布局设置" :visible.sync="$store.state.settings.showSettings"
      size="300px">
      <div class="themeStyle">
        <el-tooltip class="item" effect="dark" content="基础侧边栏" placement="top">
          <div class="settings-box-item item-left-dark"
            :class="$store.state.settings.sidebarStyle === 'dark' ? 'active' : ''"
            @click="handleSidebarTheme('dark', 'sidebarStyle')"></div>
        </el-tooltip>
        <el-tooltip key="light" effect="dark" content="亮色侧边栏" placement="top">
          <div class="settings-box-item item-left-light"
            :class="$store.state.settings.sidebarStyle === 'theme' ? 'active' : ''"
            @click="handleSidebarTheme('theme', 'sidebarStyle')"></div>
        </el-tooltip>
      </div>
      <div class="themeStyle">
        <el-tooltip key="light" effect="dark" content="基础顶栏" placement="top">
          <div class="settings-box-item item-top-light"
            :class="$store.state.settings.headerStyle === 'light' ? 'active' : ''"
            @click="handleHeaderTheme('light', 'headerStyle')"></div>
        </el-tooltip>
        <el-tooltip key="auto" effect="dark" content="主题色顶栏" placement="top">
          <div class="settings-box-item item-top-theme"
            :class="$store.state.settings.headerStyle === 'theme' ? 'active' : ''"
            @click="handleHeaderTheme('theme', 'headerStyle')"></div>
        </el-tooltip>
      </div>
      <div class="drawer-item1">
        <span>颜色详情</span>
        <theme-picker style="float: right;height: 26px;margin: -3px 8px 0 0;" @change="themeChange" />
      </div>

      <div class="drawer-item1">
        <span>显示标签</span>
        <el-switch v-model="tagsView" class="drawer-switch1"
          :disabled="Object.keys(this.$store.state.settings.systemData).length === 0" />
      </div>

      <div class="drawer-item1">
        <span>固定头部</span>
        <el-switch v-model="fixedHeader" class="drawer-switch1"
          :disabled="Object.keys(this.$store.state.settings.systemData).length === 0" />
      </div>

      <div class="drawer-item1">
        <span>显示LOGO</span>
        <el-switch v-model="sidebarLogo" class="drawer-switch1"
          :disabled="Object.keys(this.$store.state.settings.systemData).length === 0" />
      </div>
      <div class="drawer-item1">
        <span>是否显示报警信息</span>
        <el-switch v-model="alarmValue" class="drawer-switch1"
          :disabled="Object.keys(this.$store.state.settings.systemData).length === 0" />
      </div>
      <div class="drawer-item1">
        <span>用户显示方式</span>
        <el-select v-model="changeValue" @change="changeMethods" style="width:130px;"
          :disabled="Object.keys(this.$store.state.settings.systemData).length === 0">
          <el-option v-for="item in [{ id: '0', label: '左下角', value: '0' }, { id: '1', label: '右上角', value: '1' }]" :key="item.value"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class="drawer-item1">
        <span>系统语言设置</span>
        <el-select v-model="language" disabled style="width:130px;">
          <el-option
            v-for="item in [{ id: '0', label: '中文', value: 'zh-CN' }, { id: '1', label: '繁体', value: 'zh-TW' }, { id: '2', label: 'English', value: 'en-US' }]"
            :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <!-- <el-divider />
      <div style="text-align: center;width: 100%;">
          <el-button size="small" icon="el-icon-close" plain @click="$store.state.settings.showSettings = false">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          取消
          <el-button type="primary" size="small" icon="el-icon-check" >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          确认
      </div> -->
      <!-- <div class="drawer-item1">
        <span>菜单UniqueOpened</span>
        <el-switch v-model="uniqueOpened" class="drawer-switch1" />
      </div> -->
    </el-drawer>
  </div>
</template>

<script>
import crudParameter from '@/api/core/system/sysParameter'
import ThemePicker from '@/components/ThemePicker'
import Cookies from 'js-cookie'
export default {
  components: { ThemePicker },
  data() {
    return {
      sidebarStyle: Cookies.get('sidebarStyle') ? Cookies.get('sidebarStyle') : 'dark',
      headerStyle: Cookies.get('headerStyle') ? Cookies.get('headerStyle') : 'light',
      language:localStorage.getItem('language') ? localStorage.getItem('language') : 'zh-CN',
    }
  },
  mounted() {
  },
  computed: {
    fixedHeader: {
      get() {
        return this.$store.state.settings.fixedHeader
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'fixedHeader',
          value: val
        })
        this.changeSystemCode('fixedHeader', val)
      }
    },
    tagsView: {
      get() {
        return this.$store.state.settings.tagsView
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'tagsView',
          value: val
        })
        this.changeSystemCode('tagsView', val)
      }
    },
    sidebarLogo: {
      get() {
        return this.$store.state.settings.sidebarLogo
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'sidebarLogo',
          value: val
        })
        this.changeSystemCode('sidebarLogo', val)
      }
    },
    uniqueOpened: {
      get() {

        return this.$store.state.settings.uniqueOpened
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'uniqueOpened',
          value: val
        })
      }
    },
    changeValue: {
      get() {
        return this.$store.state.settings.userValue
      },
      set(val) {
        if (Object.keys(this.$store.state.settings.systemData).length === 0) {
          return
        }
        this.$store.dispatch('settings/changeSetting', {
          key: 'userValue',
          value: val
        })
        this.changeSystemCode('userValue', val)
      }
    },
    alarmValue: {
      get() {
        return this.$store.state.settings.warningValue
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'warningValue',
          value: val
        })
        this.changeSystemCode('warningValue', val)
      }
    }
  },
  methods: {
    themeChange(val) {
      if (Object.keys(this.$store.state.settings.systemData).length === 0) {
        return
      }
      this.$store.dispatch('settings/changeSetting', {
        key: 'theme',
        value: val
      })
      this.changeSystemCode('theme', val)
    },
    handleSidebarTheme(val, key) {
      if (Object.keys(this.$store.state.settings.systemData).length === 0) {
        return
      }
      this.sidebarStyle = val
      Cookies.set(key, val, { expires: 365 })
      if (val === 'dark') {
        document.documentElement.style.setProperty('--el-menuBg--color', '#79a0f1')
      } else {
        document.documentElement.style.setProperty('--el-menuBg--color', Cookies.get('theme'))
      }
      this.$store.dispatch('settings/changeSetting', {
        key: key,
        value: val
      })
      this.changeSystemCode(key, val)
    },
    handleHeaderTheme(val, key) {
      if (Object.keys(this.$store.state.settings.systemData).length === 0) {
        return
      }
      this.headerStyle = val
      Cookies.set(key, val, { expires: 365 })
      if (val === 'light') {
        document.documentElement.style.setProperty('--el-header--color', '#fff')
      } else {
        document.documentElement.style.setProperty('--el-header--color', Cookies.get('theme'))
      }
      this.$store.dispatch('settings/changeSetting', {
        key: key,
        value: val
      })
      this.changeSystemCode(key, val)
    },
    changeMethods(val) {
      if (Object.keys(this.$store.state.settings.systemData).length === 0) {
        return
      }
      this.changeValue = val
    },
    changeSystemCode(key, val) {
      const Arr = this.$store.state.settings.systemArr
      for (let i = 0; i < Arr.length; i++) {
        if (key === Arr[i].key) {
          Arr[i].val = val
        }
      }
      const result = Arr.map(item => {
        if (typeof item.val === 'boolean') {
          return item.val.toString()
        } else {
          return item.val
        }
      })
      const str = result.join(',')
      const query = this.$store.state.settings.systemData
      query.parameter_val = str
      crudParameter.edit(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          //不做任何操作
        }
      }).catch(e => {
        this.$message.warning('修改发生错误')
      })
    },
    // changeSystemLanguage(val) {
    //   const query = this.$store.state.settings.systemLanguageData
    //   query.parameter_val = val
    //   crudParameter.edit(query).then(res => {
    //     const defaultQuery = JSON.parse(JSON.stringify(res))
    //     if (defaultQuery.code === 0) {
    //       this.$i18n.locale = val
    //       localStorage.setItem('language', this.$i18n.locale)
    //     }
    //   }).catch(e => {
    //     this.$message.warning('修改发生错误')
    //   })
    // },
  }
}
</script>
<style lang="scss">
.drawer-container1 {
  padding: 24px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}

.themeStyle {
  display: flex;
}

.settings-box-item {
  position: relative;
  width: 50px;
  height: 35px;
  margin: 0 20px 20px 0;
  background-color: rgb(240 242 245);
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  cursor: pointer;

  &.active {
    &:after {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--el-button--color);
      position: absolute;
      left: 50%;
      bottom: -15px;
    }
  }
}

.item-left-light {
  &:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 33%;
    height: 100%;
    background-color: var(--el-button--color);
    content: '';
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
  }
}

.item-left-dark {
  &:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 33%;
    height: 100%;
    background-color: #79a0f1;
    content: '';
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
  }
}

.item-top-light {
  &:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 33%;
    background-color: #fff;
    content: '';
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }
}

.item-top-theme {
  &:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 33%;
    background-color: var(--el-button--color);
    content: '';
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }
}

.drawer-item1 {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  padding: 12px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.drawer-switch1 {
  float: right;
}
</style>
