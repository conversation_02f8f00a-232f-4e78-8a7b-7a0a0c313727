import request from '@/utils/request'

// 查询单元
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellSel',
    method: 'post',
    data
  })
}
// 新增单元
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellIns',
    method: 'post',
    data
  })
}
// 修改单元
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellUpd',
    method: 'post',
    data
  })
}
// 删除单元
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellDel',
    method: 'post',
    data
  })
}

// 查询单元LOV
export function lovCell(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellLov',
    method: 'post',
    data
  })
}
// 单元树
export function cellTree(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellTree',
    method: 'post',
    data
  })
}
// 查询单元IP
export function selCellIP(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellIPSel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, lovCell, cellTree, selCellIP }

