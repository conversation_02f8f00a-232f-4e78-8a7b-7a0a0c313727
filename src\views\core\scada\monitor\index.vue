<template>
  <div class="app-container">
    <el-row :gutter="20" class="el-row">
      <el-col :span="24" style="margin-bottom: 10px">
        <el-card class="box-card1" shadow="always">
          <!--查询条件-->
          <div slot="header" class="clearfix">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-8 col-12">
                <!--表：sys_fmod_prod_line-->
                <div class="formChild col-md-2 col-12">
                  <el-select v-model="query.prod_line_id" size="small" :placeholder="$t('lang_pack.monitor.selectProduction')" @change="changeProdLine">
                    <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
                  </el-select>
                </div>
                <!--表：sys_core_cell-->
                <div class="formChild col-md-2 col-12">
                  <el-select v-model="query.cell_id" size="small" :placeholder="$t('lang_pack.monitor.selectCell')" @change="changeCell">
                    <el-option v-for="item in cellData" :key="item.cell_container_name" :label="item.cell_container_name_des" :value="item.cell_id" />
                  </el-select>
                </div>
                <!--表：sys_fmod_station-->
                <div class="formChild col-md-2 col-12">
                  <el-select v-model="query.station_id" size="small" :placeholder="$t('lang_pack.messageReport.selectStation')" multiple @change="changeStation">
                    <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_id" />
                  </el-select>
                </div>
                <!--表：scada_client-->
                <div class="formChild col-md-2 col-12">
                  <el-select v-model="query.client_id" size="small" :placeholder="$t('lang_pack.diagnosis.instance')" multiple @change="changeClient">
                    <el-option v-for="item in clientData" :key="item.client_code" :label="item.client_des" :value="item.client_id" />
                  </el-select>

                  <el-input v-if="1 == 0" v-model="query.clientCodeDes" clearable size="small" :placeholder="$t('lang_pack.monitor.codeOrDes')" style="width: 150px" class="filter-item" />
                </div>
                <div class="formChild col-md-4 col-12">
                  <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toButQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>
                  <!-- 搜索 -->
                  <el-button v-if="true" class="filter-item" size="small" icon="el-icon-refresh-left" @click="toButResetQuery">{{ $t('lang_pack.commonPage.reset') }}</el-button>
                  <!-- 重置 -->
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-4 col-12">
                <el-button v-if="true" class="filter-item" size="small" type="primary" icon="el-icon-delete" :disabled="tableDataGroupTable.length == 0" style="float: right" plain @click="toGroupButClear()">{{ $t('lang_pack.monitor.emptyMonitoring') }}</el-button>
                <!-- 清空监控 -->
                <el-button v-if="true" class="filter-item" size="small" type="primary" icon="el-icon-video-pause" :disabled="!mqttConnStatus" style="float: right" plain @click="toStopWatch">{{ $t('lang_pack.monitor.stopMonitoring') }}</el-button>
                <!-- 停止监控 -->
                <el-button v-if="true" class="filter-item" size="small" icon="el-icon-video-play" type="primary" :disabled="mqttConnStatus" style="float: right" plain @click="toStartWatch">{{ $t('lang_pack.monitor.startupMonitoring') }}</el-button>
                <!-- 启动监控 -->
              </div>
            </div>
          </div>

          <el-row :gutter="20">
            <!--实例-->
            <el-col :span="12">
              <el-table ref="table" v-loading="false" border :data="tableDataTable" style="width: 100%" :stripe="true" height="400px" max-height="4000px" :highlight-current-row="true" @header-dragend="tableHeader()">
                <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                <el-table-column v-if="1 == 0" width="10" prop="cell_id" label="cell_id" />
                <el-table-column :show-overflow-tooltip="true" prop="client_code" width="100" :label="$t('lang_pack.monitor.exampleCode')" />
                <!-- 实例编码 -->
                <el-table-column :show-overflow-tooltip="true" prop="client_des" width="150" :label="$t('lang_pack.monitor.exampleDescription')" />
                <!-- 实例描述 -->
                <el-table-column :show-overflow-tooltip="true" prop="client_driver" :label="$t('lang_pack.monitor.driveProgram')" />
                <!-- 驱动程序 -->

                <el-table-column :label="$t('lang_pack.monitor.simulation')" align="center" prop="simulated_flag">
                  <!-- 是否模拟 -->
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === 'Y' ? $t('lang_pack.vie.Yes') : $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!--自定义列-->
                <el-table-column :show-overflow-tooltip="true" prop="tdOkTime" width="100" :label="$t('lang_pack.monitor.timeDuration') " />
                <!-- 持续时间(分) -->
                <el-table-column :label="$t('lang_pack.monitor.status') " align="center" prop="tdstatus" width="55" fixed="right">
                  <!-- 状态 -->
                  <template slot-scope="scope">
                    <!-- -1 灰色（无状态）；1 绿色（正常）；0 红色（异常）-->
                    <span v-show="scope.row.tdstatus == '-1'" class="statuStyle noNormalStyle" />
                    <span v-show="scope.row.tdstatus == '1'" class="statuStyle normalStyle" />
                    <span v-show="scope.row.tdstatus == '0'" class="statuStyle abnormalStyle" />
                  </template>
                </el-table-column>

                <el-table-column :label="$t('lang_pack.monitor.heartbeat')" align="center" prop="tdbeat" width="55" fixed="right">
                  <!-- 心跳 -->
                  <template slot-scope="scope">
                    <span v-show="scope.row.tdbeat == '-1'" class="statuStyle noNormalStyle" />
                    <span v-show="scope.row.tdbeat == '0'" class="statuStyle noNormalStyle" />
                    <span v-show="scope.row.tdbeat == '1'" class="statuStyle normalStyle" />
                  </template>
                </el-table-column>

                <!-- Table单条操作-->
                <el-table-column :label="$t('lang_pack.monitor.addWatch')" width="80" align="center" fixed="right">
                  <!-- 添加监控 -->
                  <template slot-scope="scope">
                    <el-link class="linkItem" type="primary" @click="toControlClientTagList(scope.row)">{{ $t('lang_pack.monitor.addTag') }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <!--Tag监控-->
            <el-col :span="12">
              <el-table ref="tableGroup" v-loading="false" border :data="tableDataGroupTable" style="width: 100%" :stripe="true" height="400px" max-height="400px" :highlight-current-row="true" @header-dragend="tableGroup()">
                <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                <el-table-column v-if="1 == 0" width="10" prop="tag_group_id" label="tag_group_id" />
                <el-table-column v-if="1 == 0" width="10" prop="tag_id" label="tag_id" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_code" width="110" :label="$t('lang_pack.monitor.labelCode')" />
                <!-- 标签代码 -->
                <el-table-column :show-overflow-tooltip="true" prop="tag_des" :label="$t('lang_pack.monitor.labelDescription')" />
                <!-- 标签描述 -->
                <el-table-column :show-overflow-tooltip="true" prop="tdtagvalue" width="70" :label="$t('lang_pack.monitor.currentValue')" />
                <!-- 当前值 -->
                <el-table-column :show-overflow-tooltip="true" prop="tag_group_code" width="100" :label="$t('lang_pack.monitor.labelGroups')" />
                <!-- 标签组 -->
                <el-table-column v-if="1 == 0" width="10" prop="tagOnlyKey" label="tagOnlyKey" />
                <!-- Table单条操作-->
                <el-table-column :label="$t('lang_pack.commonPage.operate')" width="100" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-link class="linkItem" type="primary" @click="toTagRefresh(scope.row)">{{ $t('lang_pack.monitor.refresh') }}</el-link>
                    <el-link class="linkItem" type="primary" @click="toTagWrite(scope.row)">{{ $t('lang_pack.monitor.write') }}</el-link>
                    <el-link class="linkItem" type="primary" @click="toTagAttr(scope.row)">{{ $t('lang_pack.monitor.attr') }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <!--驱动消息-->
              <el-card shadow="never" style="margin-top: 10px">
                <div slot="header" class="clearfix">
                  <span>{{ $t('lang_pack.monitor.messageDriven') }}</span>
                  <!-- 驱动消息 -->
                  <el-button style="float: right; padding: 3px 0" type="text" @click="tableDataItemTable = []">{{ $t('lang_pack.monitor.emptyMessage') }}</el-button>
                  <!-- 清空消息 -->
                </div>
                <el-table ref="tableItemTable" v-loading="false" border :data="tableDataItemTable" style="width: 100%" max-height="250px" :highlight-current-row="true" @header-dragend="tableItemTable()">
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_CLIENT" width="150" :label="$t('lang_pack.monitor.exampleCode')" />
                  <!-- 实例编码 -->
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_TIME" width="150" :label="$t('lang_pack.monitor.time')" />
                  <!-- 时间 -->
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_STATUS" width="100" :label="$t('lang_pack.monitor.status')" />
                  <!-- 状态 -->
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_FUNC" width="120" :label="$t('lang_pack.monitor.feature')" />
                  <!-- 功能 -->
                  <el-table-column prop="MSG_INFO" :label="$t('lang_pack.monitor.message')" />
                  <!-- 消息 -->
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>

      <!--弹框：添加监控标签-->
      <el-drawer append-to-body :title="$t('lang_pack.monitor.addMon') " :visible.sync="dialogVisbleSyncFrom" :before-close="toBeforeCloseFrom" size="40%">
        <el-form ref="form" :model="form" size="small">
          <el-form-item>
            <div id="scrollHeight">
              <el-tree ref="tree" :data="treeData" :props="treeProps" show-checkbox />
            </div>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormFromSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <!--弹框：标签写入-->
      <el-dialog append-to-body :close-on-click-modal="false" :title="wirteTagTitle" :visible.sync="writeDialogVisbleSyncFrom" :before-close="toWriteBeforeCloseFrom" width="520px">
        <el-form ref="formWrite" :model="formWrite" size="small" label-width="100px">
          <el-form-item v-if="1 == 0" label="id" prop="write_client_code" display:none> <el-input v-model="formWrite.write_client_code" />id </el-form-item>
          <el-form-item v-if="1 == 0" label="id" prop="write_tagOnlyKey" display:none> <el-input v-model="formWrite.write_tagOnlyKey" />id </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.labelValue') + ':'" prop="write_tag_value" display:none>
            <el-input ref="writeTagValue" v-model="formWrite.write_tag_value" />
          </el-form-item>
        </el-form>
        <div style="text-align: center; margin: 20px 0 0 0">
          <el-button size="small" icon="el-icon-close" plain @click="toWriteFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toWrigeFormFromSubmit('formWrite')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-dialog>

      <!--弹框：标签属性-->
      <el-drawer append-to-body :title="$t('lang_pack.monitor.labelAttr')" :visible.sync="attrDialogVisbleSyncFrom" :before-close="toAttrBeforeCloseFrom" size="50%">
        <el-form ref="formAttr" class="el-form-wrap" :model="formAttr" :inline="true" size="small" label-width="100px">
          <el-form-item :label="$t('lang_pack.monitor.exampleCode')" prop="client_code" display:none>
            <!-- 实例编码 -->
            <el-input v-model="formAttr.client_code" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.exampleDescription')" prop="client_des">
            <!-- 实例描述 -->
            <el-input v-model="formAttr.client_des" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.groupCode')" prop="tag_group_code">
            <!-- 组编码 -->
            <el-input v-model="formAttr.tag_group_code" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.groupDescription')" prop="tag_group_des">
            <!-- 组描述 -->
            <el-input v-model="formAttr.tag_group_des" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.labelCode')" prop="tag_code">
            <!-- 标签编码 -->
            <el-input v-model="formAttr.tag_code" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.labelDescription')" prop="tag_des">
            <!-- 标签描述 -->
            <el-input v-model="formAttr.tag_des" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.regionName')" prop="block_name">
            <!-- 区域名称 -->
            <el-input v-model="formAttr.block_name" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.regionPosition')" prop="block_addr">
            <!-- 区域位置 -->
            <el-input v-model="formAttr.block_addr" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.dataType')" prop="data_type">
            <!-- 数据类型 -->
            <el-input v-model="formAttr.data_type" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.initialAddress')" prop="start_addr">
            <!-- 起始地址 -->
            <el-input v-model="formAttr.start_addr" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.length')" prop="data_length">
            <!-- 长度 -->
            <el-input v-model="formAttr.data_length" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.bit')" prop="data_bit">
            <!-- 位 -->
            <el-input v-model="formAttr.data_bit" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.opcRealAddress')" prop="opc_addr">
            <!-- OPC真实地址 -->
            <el-input v-model="formAttr.opc_addr" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.opcVirtualAddress')" prop="opc_demo_addr">
            <!-- OPC模拟地址 -->
            <el-input v-model="formAttr.opc_demo_addr" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.readWriteAccessPermission')" prop="data_access">
            <!-- 读写权限 -->
            <el-input v-model="formAttr.data_access" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.push')" prop="pub_flag">
            <!-- 是否推送 -->
            <el-input v-model="formAttr.pub_flag" :disabled="true" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.dataConversion')" prop="data_format">
            <!-- 数据转换 -->
            <el-input v-model="formAttr.data_format" :disabled="true" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="toAttrFromCancel">{{ $t('lang_pack.commonPage.close') }}</el-button>
          <!-- 关闭 -->
        </div>
      </el-drawer>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import { lovCell, selCellIP } from '@/api/core/center/cell'
import { lovStation } from '@/api/core/factory/sysStation'
import { selScadaClient } from '@/api/core/scada/client'
import { scadaTagGroupTree } from '@/api/core/scada/tagGroup'
import { scadaTagInfo } from '@/api/core/scada/tag'

import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'

export default {
  name: 'SCADA_MONITOR',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',

      // 查询条件
      query: {
        prod_line_id: '',
        cell_id: '',
        station_id: '',
        client_id: '',
        clientCodeDes: ''
      },
      // 产线集合
      currentProdLineId: '', // 当前产线(单笔)
      prodLineData: [],
      // 单元集合
      currentCellId: '', // 当前单元(单笔)
      cellData: [],
      // 工位集合
      currentStationIds: [], // 当前工位(多笔)
      stationData: [],
      // 实例集合
      currentClientIds: [], // 当前实例(多笔)
      clientData: [],

      // 实例表
      tableDataTable: [],
      // 组、Tab表
      tableDataGroupTable: [],
      // 驱动消息
      tableDataItemTable: [],

      // 弹框：添加监控标签
      dialogVisbleSyncFrom: false,
      treeData: [], // Tag 树
      treeProps: {
        children: 'children',
        label: 'label'
      },
      treeCheckedList: [], // 树选择值

      // 弹框：标签写入
      writeDialogVisbleSyncFrom: false,
      form: {},
      formWrite: {
        write_client_code: '',
        write_tagOnlyKey: '',
        write_tag_value: ''
      },
      // 弹框：标签属性
      attrDialogVisbleSyncFrom: false,
      formAttr: {
        client_code: '',
        client_des: '',
        tag_group_code: '',
        tag_group_des: '',
        tag_code: '',
        tag_des: '',
        block_name: '',
        block_addr: '',
        data_type: '',
        start_addr: '',
        data_length: '',
        data_bit: '',
        opc_addr: '',
        opc_demo_addr: '',
        data_access: '',
        pub_flag: '',
        data_format: ''
      },

      // Tag监控集合
      mesTagWatchList: [],
      mesClientTopicList: [],
      currentClientCode: '',
      wirteTagTitle: this.$t('lang_pack.monitor.labelValue')
    }
  },

  created() {
    // 产线 LOV
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')

    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    tableHeader() {
      this.$refs.table.doLayout()
    },
    tableGroup() {
      this.$refs.tableGroup.doLayout()
    },
    tableItemTable() {
      this.$refs.tableItemTable.doLayout()
    },
    // ----------------------------------【实例】----------------------------------
    // 单元LOV
    queryCell() {
      const query = {
        userID: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId,
        enable_flag: 'Y'
      }
      lovCell(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.cellData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId,
        cell_id: this.currentCellId,
        enable_flag: 'Y'
      }
      lovStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    // 实例LOV
    queryScadaClient() {
      const query = {
        userID: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId,
        cell_id: this.currentCellId,
        sort: 'client_id',
        stationIds: this.currentStationIds.join(','),
        enable_flag: 'Y'
      }
      selScadaClient(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.clientData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },

    // 更改产线
    changeProdLine(val) {
      this.currentProdLineId = val // 当前产线
      // 清空
      this.currentCellId = '' // 当前单元(单笔)
      this.cellData = []
      this.currentStationIds = [] // 当前工位(多笔)
      this.stationData = [] // 工位LOV
      this.currentClientIds = [] // 当前实例(多笔)
      this.clientData = [] // 实例LOV

      // 加载 单元Lov
      this.queryCell()
      // 加载 工位LOV
      this.queryStation()
      // 加载 实例LOV
      this.queryScadaClient()
    },
    // 更改CELL
    changeCell(val) {
      this.currentCellId = val // 当前单元(单笔)
      // 清空
      this.currentStationIds = [] // 当前工位(多笔)
      this.stationData = [] // 工位LOV
      this.currentClientIds = [] // 当前实例(多笔)
      this.clientData = [] // 实例LOV

      // 加载 工位LOV
      this.queryStation()
      // 加载 实例LOV
      this.queryScadaClient()
    },
    // 更改工位
    changeStation(val) {
      this.currentStationIds = val // 当前工位(多笔)
      // 清空
      this.currentClientIds = [] // 当前实例(多笔)
      this.clientData = [] // 实例LOV

      // 加载 实例LOV
      this.queryScadaClient()
    },
    // 更改实例
    changeClient(val) {
      this.currentClientIds = val
    },

    // 按钮
    toButQuery() {
      if (this.currentProdLineId === '' || this.currentProdLineId === undefined || this.currentProdLineId == null) {
        this.$message({
          message: this.$t('lang_pack.monitor.selectProduction'),
          type: 'error'
        })
        return
      }

      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.currentProdLineId,
        cell_id: this.currentCellId,
        sort: 'client_id',
        stationIds: this.currentStationIds.join(','),
        clientIds: this.currentClientIds.join(','),
        clientCodeDes: this.query.clientCodeDes,
        enable_flag: 'Y'
      }
      selScadaClient(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          // 清空
          this.tableDataTable = []
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              defaultQuery.data.forEach(item => {
                var tableColumn = {} // 新增Tag监控
                tableColumn.cell_id = item.cell_id
                tableColumn.client_id = item.client_id
                tableColumn.client_code = item.client_code
                tableColumn.client_des = item.client_des
                tableColumn.client_driver = item.client_driver
                tableColumn.simulated_flag = item.simulated_flag
                tableColumn.tdOkTime = ''
                tableColumn.tdstatus = '-1'
                tableColumn.tdbeat = '-1'
                this.tableDataTable.push(tableColumn)
              })
            } else {
              this.tableDataTable = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    // 重置
    toButResetQuery() {
      this.query.prod_line_id = ''
      this.query.cell_id = ''
      this.query.station_id = ''
      this.query.client_id = ''
      this.query.clientCodeDes = ''
      this.query.enable_flag = ''

      // 产线集合
      this.currentProdLineId = '' // 当前产线(单笔)
      // CELL集合
      this.currentCellId = '' // 当前单元(单笔)
      // 工位集合
      this.currentStationIds = [] // 当前工位(多笔)
      // 实例集合
      this.currentClientIds = [] // 当前实例(多笔)

      // // 测试发送消息
      // const sendObjec = {
      //  title: '测试',
      //  detail: 'Hello mqtt'
      // }
      // this.sendMessage('myTopic', sendObjec)
    },

    // Table: 添加监控按钮(Tag列表)
    toControlClientTagList(data) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      this.currentClientCode = data.client_code
      this.dialogVisbleSyncFrom = true

      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        client_id: data.client_id,
        sort: 'tag_group_id',
        enable_flag: 'Y'
      }
      scadaTagGroupTree(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          this.dialogVisbleSyncFrom = true
          if (defaultQuery.data.length > 0) {
            this.treeData = defaultQuery.data
          }
        })
        .catch(() => {})
    },

    // Tree:
    toBeforeCloseFrom(done) {
      // 新增弹出框(关闭前的回调)
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toFormFromSubmit() {
      // 新增Tag监控
      this.toGroupButAdd()
    },

    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      if (this.tableDataTable.length === 0) {
        this.$message({
          message: this.$t('lang_pack.monitor.queryInstance'),
          type: 'error'
        })
      }
      if (this.tableDataTable.length > 0) {
        var query = {
          userName: Cookies.get('userName'),
          cell_id: this.tableDataTable[0].cell_id,
          current_ip: window.location.hostname
        }
        selCellIP(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res.result))
            const result = JSON.parse(defaultQuery)
            if (result === '' || result === undefined || result == null) {
              this.$message({
                message: this.$t('lang_pack.monitor.serviceCell'),
                type: 'error'
              })
              return
            }
            // 获取连接地址
            // 'ws://***************:8090/mqtt'
            this.cellIp = result.ip
            this.cellMqttPort = result.mqtt_port
            this.cellWebPort = result.webapi_port
            var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
            // var connectUrl = 'ws://**********:8083' + '/mqtt'
            console.log('拼接URL：' + connectUrl)

            // mqtt连接
            this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
            this.clientMqtt.on('connect', e => {
              this.mqttConnStatus = true
              // 当MQTT连接成功后，注册CLIENT相关TOPIC
              for (var i = 0; i < this.tableDataTable.length; i++) {
                var clientCode = this.tableDataTable[i].client_code
                // 订阅主题
                var topic_clientStatus = 'SCADA_STATUS/' + clientCode
                var topic_clientBeat = 'SCADA_BEAT/' + clientCode
                var topic_clientMsg = 'SCADA_MSG/' + clientCode
                this.topicSubscribe(topic_clientStatus)
                this.topicSubscribe(topic_clientBeat)
                this.topicSubscribe(topic_clientMsg)
              }
              this.$message({
                message: this.$t('lang_pack.vie.cnneSuccess'),
                type: 'success'
              })
            })

            // MQTT连接失败
            this.clientMqtt.on('error', () => {
              this.$message({
                message: this.$t('lang_pack.vie.cnneFailed'),
                type: 'error'
              })
              this.clientMqtt.end()
            })
            // 断开发起重连(异常)
            this.clientMqtt.on('reconnect', () => {
              this.$message({
                message: this.$t('lang_pack.vie.connDisconRecon'),
                type: 'error'
              })
            })
            this.clientMqtt.on('disconnect', () => {
              // this.$message({
              //  message: '服务连接断开',
              //  type: 'error'
              // })
            })
            this.clientMqtt.on('close', () => {
              // this.clientMqtt.end()
              // this.$message({
              //  message: '服务连接断开',
              //  type: 'error'
              // })
            })
            // 接收消息处理
            this.clientMqtt.on('message', (topic, message) => {
              // console.log('MQTT收到来自', topic, '的消息', message.toString())
              // const res = JSON.parse(message.toString())
              // 解析传过来的数据
              this.mqttUpdateTable(topic, message)
            })
          })
          .catch(() => {
            this.$message({
              message: this.$t('lang_pack.vie.queryException'),
              type: 'error'
            })
          })
      }
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, error => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // 还原状态颜色
      this.tableDataTable.forEach(val => {
        val.tdstatus = '-1'
        val.tdbeat = '-1'
      })

      // this.clientMqtt.disconnect()
      // this.clientMqtt = null
      this.clientMqtt.end()
      this.mqttConnStatus = false
    },

    // ----------------------------------【TAG监控】----------------------------------
    // 组Group
    toGroupButAdd() {
      // 查询Tag监控
      if (this.$refs.tree.getCheckedNodes().length <= 0) {
        this.$message({ message: this.$t('lang_pack.monitor.selectTAG'), type: 'warning' })
        return
      }
      // 循环选中Tag
      var newClientTagGroupList = []
      var newTagList = []
      this.$refs.tree.getCheckedNodes().forEach(item => {
        if (item.level === 2) {
          var tableColumn = {} // 新增Tag监控
          tableColumn.client_code = this.currentClientCode
          tableColumn.tag_group_id = item.tag_group_id
          tableColumn.tag_id = item.tag_id
          tableColumn.tag_group_code = item.tag_group_code
          tableColumn.tag_code = item.tag_code
          tableColumn.tag_des = item.tag_des
          tableColumn.data_type = item.data_type
          tableColumn.data_access = item.data_access
          tableColumn.simulated_flag = item.simulated_flag
          tableColumn.tdtagvalue = '-1'

          // 监控Tag
          var tagOnlyKey = tableColumn.client_code + '/' + tableColumn.tag_group_code + '/' + tableColumn.tag_code

          tableColumn.tagOnlyKey = tagOnlyKey
          if (this.tableDataGroupTable.filter(item => item.tag_id === tableColumn.tag_id).length === 0) {
            this.tableDataGroupTable.push(tableColumn)
          }
          // Tag组
          var clientGroupMultyKey = 'SCADA_CHANGE/' + tableColumn.client_code + '/' + tableColumn.tag_group_code
          if (this.mesClientTopicList.indexOf(clientGroupMultyKey) < 0) {
            this.mesClientTopicList.push(clientGroupMultyKey)
            newClientTagGroupList.push(clientGroupMultyKey)
          }
          if (this.mesTagWatchList.indexOf(tagOnlyKey) < 0) {
            newTagList.push(tagOnlyKey)
            this.mesTagWatchList.push(tagOnlyKey)
          }
        }
      })

      // 获取Tag值
      if (newTagList.length > 0) {
        this.GetTagValue(newTagList)
        // MesSleep(100);
      }
      // 订阅Tag组
      if (newClientTagGroupList.length > 0) {
        for (var i = 0; i < newClientTagGroupList.length; i++) {
          this.topicSubscribe(newClientTagGroupList[i].toString())
        }
      }
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toGroupButClear() {
      // 清空Tag监控
      for (var i = 0; i < this.mesClientTopicList.length; i++) {
        this.topicUnsubscribe(this.mesClientTopicList[i])
      }
      this.mesClientTopicList = []
      this.mesTagWatchList = []
      this.tableDataGroupTable = []
      this.tableDataItemTable = [] // 日志
    },

    // ----------------------------------【Tag明细】----------------------------------
    toTagRefresh(data) {
      // 刷新
      try {
        var lst = []
        lst.push(data.tagOnlyKey)
        this.GetTagValue(lst)
      } catch {
        this.$message({
          message: this.$t('lang_pack.monitor.refreshException'),
          type: 'error'
        })
      }
    },
    toTagWrite(data) {
      // 写入
      this.formWrite.write_client_code = data.client_code
      this.formWrite.write_tagOnlyKey = data.tagOnlyKey
      this.formWrite.write_tag_value = ''
      this.wirteTagTitle = data.tag_des

      this.$nextTick(x => {
        // 正确写法
        this.$refs.writeTagValue.focus()
      })
      this.writeDialogVisbleSyncFrom = true // 弹出框
    },
    toWriteBeforeCloseFrom() {
      this.writeDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toWriteFromCancel() {
      this.writeDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toWrigeFormFromSubmit() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.formWrite.write_tagOnlyKey,
        TagValue: this.formWrite.write_tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.formWrite.write_client_code
      this.sendMessage(topic, sendStr)

      this.writeDialogVisbleSyncFrom = false
    },

    toTagAttr(data) {
      // 属性
      this.attrDialogVisbleSyncFrom = true

      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        tag_id: data.tag_id
      }
      this.listLoadingTable = true
      scadaTagInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.formAttr.client_code = defaultQuery.data[0].client_code
              this.formAttr.client_des = defaultQuery.data[0].client_des
              this.formAttr.tag_group_code = defaultQuery.data[0].tag_group_code
              this.formAttr.tag_group_des = defaultQuery.data[0].tag_group_des
              this.formAttr.tag_code = defaultQuery.data[0].tag_code
              this.formAttr.tag_des = defaultQuery.data[0].tag_des
              this.formAttr.block_name = defaultQuery.data[0].block_name
              this.formAttr.block_addr = defaultQuery.data[0].block_addr
              this.formAttr.data_type = defaultQuery.data[0].data_type
              this.formAttr.start_addr = defaultQuery.data[0].start_addr
              this.formAttr.data_length = defaultQuery.data[0].data_length
              this.formAttr.data_bit = defaultQuery.data[0].data_bit
              this.formAttr.opc_addr = defaultQuery.data[0].opc_addr
              this.formAttr.opc_demo_addr = defaultQuery.data[0].opc_demo_addr
              this.formAttr.data_access = defaultQuery.data[0].data_access
              this.formAttr.pub_flag = defaultQuery.data[0].pub_flag
              this.formAttr.data_format = defaultQuery.data[0].data_format
            } else {
              this.formAttr.client_code = ''
              this.formAttr.client_des = ''
              this.formAttr.tag_group_code = ''
              this.formAttr.tag_group_des = ''
              this.formAttr.tag_code = ''
              this.formAttr.tag_des = ''
              this.formAttr.block_name = ''
              this.formAttr.block_addr = ''
              this.formAttr.data_type = ''
              this.formAttr.start_addr = ''
              this.formAttr.data_length = ''
              this.formAttr.data_bit = ''
              this.formAttr.opc_addr = ''
              this.formAttr.opc_demo_addr = ''
              this.formAttr.data_access = ''
              this.formAttr.pub_flag = ''
              this.formAttr.data_format = ''
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    toAttrBeforeCloseFrom() {
      this.attrDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toAttrFromCancel() {
      this.attrDialogVisbleSyncFrom = false // 弹出框隐藏
    },

    // ----------------------------------【MQTT消息触发】----------------------------------
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      console.log('MQTT收到来自', channel, '的消息', message.toString())

      if (channel.indexOf('SCADA_STATUS/') >= 0 || channel.indexOf('SCADA_BEAT/') >= 0 || channel.indexOf('SCADA_MSG/') >= 0) {
        this.reflashClientInfo(channel, message)
      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    // 接受到CLIENT对应的消息时，进行界面更新
    reflashClientInfo(channel, message) {
      var jsonData = JSON.parse(message)
      var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_STATUS/') >= 0) {
        var Status = jsonData.Status
        var OkTime = jsonData.OkTime
        this.tableDataTable.filter(item => item.client_code === clientCode)[0] && (this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdstatus = Status)
        this.tableDataTable.filter(item => item.client_code === clientCode)[0] && (this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdOkTime = OkTime)
      } else if (channel.indexOf('SCADA_BEAT/') >= 0) {
        var Beat = jsonData.Beat
        this.tableDataTable.filter(item => item.client_code === clientCode)[0] && (this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdbeat = Beat)
      } else if (channel.indexOf('SCADA_MSG/') >= 0) {
        // 明细
        var msgClient = jsonData.ClientCode
        var msgFunc = jsonData.FuncCode
        var msgStatus = jsonData.Status
        var msgInfo = jsonData.Message
        var msgTime = jsonData.Time

        var newRow = {
          MSG_CLIENT: msgClient,
          MSG_TIME: msgTime,
          MSG_STATUS: msgStatus,
          MSG_FUNC: msgFunc,
          MSG_INFO: msgInfo
        }
        if (this.tableDataItemTable.length >= 100) {
          this.tableDataItemTable.splice(this.tableDataItemTable.length - 1, 1)
        }
        this.tableDataItemTable.unshift(newRow)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      // var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue
      if (this.mesTagWatchList.indexOf(TagKey) < 0) return
      this.tableDataGroupTable.filter(item => item.tag_code === TagCode)[0].tdtagvalue = TagNewValue
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < newTagList.length; i++) {
        var readTag = {}
        readTag.tag_key = newTagList[i].toString()
        readTagArray.push(readTag)
      }
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
      }
      console.log('环境process.env.NODE_ENV：' + process.env.NODE_ENV)
      console.log('调用接口：' + path)

      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');

              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value
                  // if (tagValue === null) {
                  //  tagValue = ''
                  // } else {
                  //  tagValue = tagValue.toString()
                  // }

                  if (this.mesTagWatchList.indexOf(tagKey) >= 0) {
                    this.tableDataGroupTable.filter(item => item.tagOnlyKey === tagKey)[0].tdtagvalue = tagValue
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          this.flowTaskData = []
          this.$message({ message: this.$t('lang_pack.vie.queryException') + ':' + ex, type: 'error' })
        })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .el-card__header {
  padding: 10px;
}
.box-card {
  min-height: calc(100vh);
  padding: 10px;
  .el-card__body {
    padding: 5px;
  }
}
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-drawer {
  overflow-y: scroll;
}
.el-avatar {
  background-color: #ffffff;
}
.statuStyle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
}
.noNormalStyle {
  background-color: #b3b3b3;
}
.abnormalStyle {
  background-color: rgb(255, 57, 57);
}
.normalStyle {
  background-color: rgb(9, 196, 9);
}
::v-deep .el-select .el-select__tags > span {
  display: flex;
  overflow: hidden;
}
#scrollHeight{
  overflow: auto;
  max-height: 600px;
  &::-webkit-scrollbar{
      width: 10px;
      height: 10px;
      background-color: #ebeef5;
      cursor: pointer !important;
  }
    &::-webkit-scrollbar-thumb{
        box-shadow: inset 0 0 6px rgba(255,255,255,.3);
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        background-color: #f6f9ff;
        cursor: pointer !important;
    }
}
</style>
