<template>
  <div :style="'height:'+ height">
    <iframe src="item.screen_set_url" frameborder="no" style="width: 100%;height: 100%" scrolling="no" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      swiperOption: {

        loop: true, // 是否循环轮播

        speed: 500, // 切换速度

        observer: true, // 修改swiper自己或子元素时，自动初始化swiper

        observeParents: true, // 修改swiper的父元素时，自动初始化swiper

        // 自动轮播

        // autoplay: {

        //   delay: 2000,

        //   disableOnInteraction: false

        // },

        // 设置slider容器能够同时显示的slides数量

        slidesPerView: 1,

        // 左右切换

        navigation: {

          nextEl: '.swiper-button-next',

          prevEl: '.swiper-button-prev'

        }

        // 分页器

        // pagination: {

        //   el: '.swiper-pagination',

        //   clickable: true // 允许点击小圆点跳转

        // },

        // 设置轮播样式,此处为3d轮播效果

        // effect: 'coverflow',

        // coverflowEffect: {

        //   rotate: 30, // 旋转的角度

        //   stretch: 10, // 拉伸 图片间左右的间距和密集度

        //   depth: 60, // 深度 切换图片间上下的间距和密集度

        //   modifier: 2, // 修正值 该值越大前面的效果越明显

        //   slideShadows: true // 页面阴影效果

        // }

      },
      height: document.documentElement.clientHeight + 'px;',
      srcArr: []
    }
  },
  created() {
    setInterval(() => {
      this.initcheckiframesrc()
    }, 1000)
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight + 'px;'
    }

    // console.log(this.swiperOption.autoplay.delay)
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
::v-deep .swiper-button-prev, .swiper-button-next{
  height: 100%;
  top:0;
  margin-top: 0;
  width: 20%;
}
::v-deep .swiper-button-prev{
  left: 0;
}
::v-deep .swiper-button-next{
  right: 0;
}
::v-deep .swiper-button-next:after {
    content: '';
}
::v-deep .swiper-button-prev:after {
    content: '';
}
</style>
