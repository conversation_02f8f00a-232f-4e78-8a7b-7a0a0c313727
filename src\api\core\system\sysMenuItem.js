import request from '@/utils/request'

//查询菜单明细
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuItemSel',
    method: 'post',
    data
  })
}
//新增菜单明细
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuItemIns',
    method: 'post',
    data
  })
}
//修改菜单明细
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuItemUpd',
    method: 'post',
    data
  })
}
//删除菜单明细
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysMenuItemDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
