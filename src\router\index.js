import router from './routers'
import store from '@/store'
import Config from '@/settings'
import Cookies from 'js-cookie'

import NProgress from 'nprogress' // progress bar NProgress是页面跳转是出现在浏览器顶部的进度条
import 'nprogress/nprogress.css' // progress bar style

// 加载组件
import { getToken } from '@/utils/auth' // getToken from cookie
import { sysBuildMenus } from '@/api/core/system/sysMenu'
import { filterAsyncRouter } from '@/store/modules/permission'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/whiteLogin', '/whiteLoginWx', '/loginOfVerify', '/whiteLoginOfVerify', '/videoTest', '/ZA/workstationScreen', '/ZA/workstationScreenVoice', '/ZA/workstationScreenZh', '/ZA/workstationScreenNs', '/ZA/workstationScreenYz', '/ZA/workstationScreenImg', '/ZA/workstationScreenLine', '/ZA/workstationScreenNSLine', '/ZA/workshopScreenone', '/ZA/workshopScreencar', '/HA/workstationScreen', '/HA/workshopScreenone', '/HA/workshopScreencar', '/CA/pressline', '/DP/queue', '/ZA/dpqueue', '/ZA/zzqueue', '/welcome', '/hotwelcome', '/fill', '/tightening', '/playTime', '/testingResult', '/testingLet', '/glassGlue', '/queue', '/qualitygate', '/iframe', '/hmi_main', '/orderInfo', '/mainInterface', '/taskflow', '/confirm-dialog', '/barcodeRule', '/craftRoute',
  '/materialRule', '/mzRule', '/packRule', '/recipe', '/screenConfig', '/shift', '/smallModel', '/smallModelBom', '/plcRecipe', '/fmodEquip', '/bgScreen', '/frontDisposeScreen', '/positonsLists', '/planMo', '/obdDevice', '/meDxQuality', '/meMzQuality', '/taskrecord', '/manualGrouping', 'materialScan', '/stationMonitor', '/stationSet', '/stationFlow', '/scadaMonitor', '/taskRecord', '/stationMonitor2', '/PlanReport', '/interfLog', '/stationMonitor3', '/plateReceiver', '/plateReceiverZH', '/stationAndon', '/sop', '/materialScanQc', '/stationMonitorMes', '/stationMonitorMes2', '/eapPlanReport', '/eapStationFlownReport', '/scadaLink', '/scadaTagChange', '/scadaTagWrite',
  '/setinout', '/materialDel', '/manualPack', '/reportScadaAlarm', '/ZA/partWireOn', '/ZA/tireLineOn', '/andonScreen', '/productScreen', '/equipStatuScreen', '/qciframe', '/mainPage', '/carMainPage', '/wms_dashboard', '/wms_Intelboard', '/wms_screen', '/wms_controlboard', '/fmMaintenance', '/workOrder', '/stationOrder', '/stationSort', '/aoiStationMonitor', '/recipeAndon',
  '/dcs/alarm/dashboard', '/recipe&andon', '/ghStationMonitorPbj', '/ghStationMonitor', '/ghStationMonitor2', '/eapStatUtility', '/eapHmiShow', '/dyProdOrder', '/materialBatchScan', '/productionBoard', '/shOeeBoard1', '/shOeeBoard2', '/sfRecipeMain', '/hsRecipeMain', '/haqdRecipeMain', '/repairStationIndex', '/materialUnbind', '/sh/alarm/dashboard', '/dcs/project/wms/dashboard/index', '/hmi_main_wx', '/pack/project/kinsus/monitor', '/fjrm/dashboard/index', '/wh/dashboard/index', '/zhcyStationMonitor', 'tlpsStationMonitor', 'tlpsRecipeMain', 'secsaisRecipe',
  // 晟丰-黄石群立
  '/sfHsqlRecipeMain', '/sfHsqlRecipeMager', '/sfHsqlMaterialMaintenance'
] // no redirect whitelist 白名单中重定向

// -------------------------------------------------------------------------------
// vue-router作为vue里面最基础的服务
// 使用vue-cli作为开发前提 vue-router已经配置好了
// https://www.cnblogs.com/wuvkcyan/p/9311155.html Vue路由钩子
// (1)编程式的导航
// <router-link to="/" tag="p">耳机频道</router-link>
// to是一个prop.指定需要跳转的路径,也可以使用v-bind动态设置
// (2)函数式的导航
// this.$router.push('./product/' + list.id)
// 这个方法会向 history 栈添加一个新的记录，所以，当用户点击浏览器后退按钮时，则回到之前的 URL。
// router.replace		//它不会向 history 添加新记录，而是跟它的方法名一样 —— 替换掉当前的 history 记录
// router.go(1)	 	//在浏览器记录里面前进一步,等于history.forward()
// router.go(-1)		//后退一步记录，等同于 history.back()
// router.go(n)		//浏览器记录前进3步

// Vue路由钩子 afterEach beforeEach
// router.beforeEach() 一般用来做一些进入页面的限制(路由拦截)。比如没有登录，就不能进入某些页面，只有登录了之后才有权限查看某些页面。
// router.afterEach() 全局后置钩子

// -------------------------------------------------------------------------------
// vue store 存储 dispatch 和 commit
// dispatch() 含有异步操作 存储/取值
// commit() 同步操作 存储/取值

// -------------------------------------------------------------------------------
// NProgress

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - ' + Config.title
  }
  NProgress.start()

  if (getToken()) {
    // 已登录且要跳转的页面是登录页
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) { // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => { // 拉取user_info
          // 动态路由，拉取菜单
          loadMenus(next, to)
        }).catch(() => {
          store.dispatch('LogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
        // 登录时未拉取 菜单，在此处拉取
      } else if (store.getters.loadMenus) {
        // 修改成false，防止死循环
        store.dispatch('updateLoadMenus')
        loadMenus(next, to)
      } else {
        next()
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
  /**
     * 判断锁屏
     */
  const oldPasswordld = sessionStorage.getItem('lockPassword')
  const newlockPassword = sessionStorage.getItem('newlockPassword')
  if (newlockPassword !== oldPasswordld && to.path !== '/lockScreen') {
    if (to.path === '/whiteLogin?redirect=%2Findex' || to.path === '/whiteLogin') {
      next('/whiteLogin?redirect=%2Findex')
      sessionStorage.remove('token')
    } else {
      next('/lockScreen')
    }
  }
})

// 动态路由，拉取菜单
export const loadMenus = (next, to) => {
  sysBuildMenus({ userID: Cookies.get('userId') }).then(res => {
    const sdata = JSON.parse(JSON.stringify(res))
    const rdata = JSON.parse(JSON.stringify(res))
    const sidebarRoutes = filterAsyncRouter(sdata)
    const rewriteRoutes = filterAsyncRouter(rdata, false, true)
    rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
    store.dispatch('GenerateRoutes', rewriteRoutes).then(() => { // 存储路由
      router.addRoutes(rewriteRoutes) // 动态添加可访问路由表
      next({ ...to, replace: true })
    })
    store.dispatch('SetSidebarRouters', sidebarRoutes)
  })
}

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
