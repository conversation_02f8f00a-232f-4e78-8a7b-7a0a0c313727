<template>
  <swiper ref="mySwiper" :options="swiperOption" style="min-width:1200px;overflow:hidden;">
    <swiper-slide v-for="(item,index) in srcArr" :key="index">
      <div :style="'height:'+ height">
        <iframe ref="iframe" :src="item" frameborder="no" style="width: 100%;height: 100%" scrolling="no" allow="autoplay" allowfullscreen="true" />
      </div>
    </swiper-slide>
    <!-- 分页器 -->

    <!-- <div slot="pagination" class="swiper-pagination" /> -->

    <!-- 左右箭头 -->

    <!-- @click="prev" -->

    <div slot="button-prev" class="swiper-button-prev" />

    <!-- @click="next" -->

    <div slot="button-next" ref="next" class="swiper-button-next" />
  </swiper>
</template>
<script>
export default {
  data() {
    return {
      swiperOption: {

        loop: true, // 是否循环轮播

        speed: 500, // 切换速度

        observer: true, // 修改swiper自己或子元素时，自动初始化swiper

        observeParents: true, // 修改swiper的父元素时，自动初始化swiper

        // 自动轮播

        // autoplay: {

        //   delay: 2000,

        //   disableOnInteraction: false

        // },

        // 设置slider容器能够同时显示的slides数量

        slidesPerView: 1,

        // 左右切换

        navigation: {

          nextEl: '.swiper-button-next',

          prevEl: '.swiper-button-prev'

        },
        on: {
          // 使用es6的箭头函数，使this指向vue对象
          click: () => {
            // 通过$refs获取对应的swiper对象
            const swiper = this.$refs.mySwiper.swiper
            const i = swiper.activeIndex
            console.log(i)
          }
        }

        // 分页器

        // pagination: {

        //   el: '.swiper-pagination',

        //   clickable: true // 允许点击小圆点跳转

        // },

        // 设置轮播样式,此处为3d轮播效果

        // effect: 'coverflow',

        // coverflowEffect: {

        //   rotate: 30, // 旋转的角度

        //   stretch: 10, // 拉伸 图片间左右的间距和密集度

        //   depth: 60, // 深度 切换图片间上下的间距和密集度

        //   modifier: 2, // 修正值 该值越大前面的效果越明显

        //   slideShadows: true // 页面阴影效果

        // }

      },
      height: document.documentElement.clientHeight + 'px;',
      srcArr: ['http://**********:9090/andonScreen', 'http://**********:9090/productScreen', 'http://**********:9090/equipStatuScreen'],
      indexSwiper: ''
    }
  },
  computed: {
    // 定义swiper，在下面就可以引用这个swiper了；
    swiper() {
      return this.$refs.mySwiper.swiper
    }
  },
  created() {
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight + 'px;'
    }
    setTimeout(() => {
      this.setSwiper()
      this.changeBack()
    }, 1000)
  },
  methods: {
    changeBack() {
      var x = document.querySelector('iframe')
      var y = (x.contentWindow || x.contentDocument)
      if (y.document)y = y.document
      y.body.style.backgroundColor = '#030b4a'
    },
    setSwiper() {
      setInterval(this.getSwiper, 60000)
    },
    getSwiper() {
      this.indexSwiper++
      if (this.indexSwiper >= this.srcArr.length) {
        this.swiper.slideTo(0)
        this.indexSwiper = 0
      } else {
        this.$refs.next.click()
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .swiper-button-prev, .swiper-button-next{
  height: 100%;
  top:0;
  margin-top: 0;
  width: 20%;
}
::v-deep .swiper-button-prev{
  left: 0;
}
::v-deep .swiper-button-next{
  right: 0;
}
::v-deep .swiper-button-next:after {
    content: '';
}
::v-deep .swiper-button-prev:after {
    content: '';
}
::v-deep .divimg img{
  width: 100% !important;
  height: 100% !important;
}
</style>
