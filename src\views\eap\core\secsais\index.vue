<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('core.secsais.clientDes') + '：'">
                <el-input v-model="query.client_des" clearable size="small" @keyup.enter.native="fetchContent" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('core.secsais.clientCode') + '：'">
                <el-input v-model="query.client_code" clearable size="small" @keyup.enter.native="fetchContent" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond col-md-4 col-12">
            <div class="formChild col-md-12 col-12">
              <el-button type="primary" size="small" @click="fetchContent">{{ $t('core.secsais.search') }}</el-button>
              <el-button size="small" @click="resetQuery">{{ $t('core.secsais.reset') }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <div>
        <el-row :gutter="10">
          <!-- 级联选择区域 -->
          <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
            <!-- 第一级：实例列表 -->
            <div class="cascade-container">
              <div class="cascade-header">
                {{ $t('core.secsais.clientList') }}
                <el-button
                  type="text"
                  icon="el-icon-refresh"
                  class="refresh-button"
                  :title="$t('core.secsais.refreshInstanceList')"
                  @click="refreshClientList"
                />
              </div>
              <div class="cascade-list">
                <ul class="client-list">
                  <li
                    v-for="client in clientList"
                    :key="client.client_id"
                    :class="{ active: selectedClient && selectedClient.client_id === client.client_id }"
                    @click="selectClient(client)"
                  >
                    <div class="client-item">
                      <div class="client-name">
                        <span
                          class="status-indicator"
                          :class="client.heartbeat ? 'normal-status' : 'disabled-status'"
                          :title="client.heartbeat ? $t('core.secsais.online') : $t('core.secsais.offline')"
                        />
                        {{ client.client_des }}
                      </div>
                      <div class="client-code">{{ client.client_code }}</div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </el-col>

          <!-- 第二级：配方列表 -->
          <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
            <div class="cascade-container">
              <div class="cascade-header">{{ $t('core.secsais.modelList') }}</div>
              <div class="cascade-list">
                <ul class="model-list">
                  <li
                    v-for="model in filteredModels"
                    :key="model.model_name"
                    :class="{ active: selectedModel && selectedModel.model_name === model.model_name }"
                    @click="selectModel(model)"
                  >
                    <div class="model-item">
                      <div class="model-name">{{ model.model_name }}</div>
                      <!--
                      <div class="model-prefix">{{ model.tag_code_prefix }}</div>
                      -->
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </el-col>

          <!-- 配方详情区域 -->
          <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
            <div class="cascade-header">{{ $t('core.secsais.recipeDetails') }}</div>
            <detail
              ref="detail"
              class="tableFirst"
              :recipe_detail_id="currentRecipeId"
              :tag_code_prefix="currentTagCodePrefix"
              :client_id="selectedClient ? selectedClient.client_id : ''"
              :client_code="selectedClient ? selectedClient.client_code : ''"
              :tag_group_code="selectedModel ? selectedModel.tag_group_code : ''"
              :client_des="selectedClient ? selectedClient.client_des : ''"
              :model_name="selectedModel ? selectedModel.model_name : ''"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import detail from './detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import recipeApi from '@/api/eap/core/secsais/recipeApi'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { selCellIP } from '@/api/core/center/cell'

const defaultForm = {
  tag_group_id: '',
  client_des: '',
  tag_group_code: '',
  model_name: '',
  tag_code_prefix: '',
  client_id: '',
  client_code: '',
  enable_flag: 'Y'
}

export default {
  name: 'EAP_CORE_SECSAIS_RECIPE',
  components: { detail },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {},
  cruds() {
    return CRUD({
      title: 'SECS/AIS Recipe', // Use a static string first, will be updated in created hook
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'tag_group_id',
      // 排序
      sort: ['tag_group_id asc'],
      // CRUD Method
      crudMethod: {
        // 禁用自动查询，使用自定义的fetchRecipeModels方法
        sel: () => Promise.resolve({ code: 0, data: [] }),
        add: () => Promise.resolve({ code: 0 }),
        edit: () => Promise.resolve({ code: 0 }),
        del: () => Promise.resolve({ code: 0 })
      },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      },
      // 禁用自动查询
      query: false
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      currentRecipeId: -1,
      currentTagCodePrefix: '',
      permission: {
        add: ['admin', 'c_eap_core_secsais_recipe:add'],
        edit: ['admin', 'c_eap_core_secsais_recipe:edit'],
        del: ['admin', 'c_eap_core_secsais_recipe:del']
      },
      // 实例列表
      clientList: [],
      // 选中的实例
      selectedClient: null,
      // 选中的配方
      selectedModel: null,
      // 查询参数
      query: {
        client_des: '',
        client_code: '',
        station_code: this.$route.query.station_code || ''
      },
      // 初始化标志，确保fetchRecipeModels只被调用一次
      initialized: false,

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        clientId:
          'RecipeEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: ''
    }
  },
  computed: {
    // 根据选中的实例过滤配方列表
    filteredModels() {
      if (!this.selectedClient || !this.selectedClient.models) {
        return []
      }
      return this.selectedClient.models
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }

    // 连接MQTT，获取心跳状态
    this.connectMqtt()
  },

  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')

    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  created: function() {
    // Update the title after the component is created and i18n is available
    if (this.crud) {
      this.crud.title = this.$t('core.secsais.maintenance')
    }

    // 初始化查询，确保每次进入页面时只调用一次
    if (!this.initialized) {
      console.log('首次初始化，调用fetchRecipeModels')
      this.initialized = true
      this.fetchRecipeModels()
    }

    // 添加路由监听器，当路由参数中的station_code变化时重新初始化
    this.$watch(
      () => this.$route.query.station_code,
      (newVal, oldVal) => {
        if (newVal !== oldVal) {
          console.log('工站编码变化，重新获取数据:', oldVal, '->', newVal)
          this.fetchRecipeModels()
        }
      },
      { immediate: false } // 确保不会在组件创建时立即触发
    )
  },
  methods: {
    // 获取配方模型数据
    fetchRecipeModels() {
      console.log('fetchRecipeModels 被调用 - 调用栈:', new Error().stack)

      // 重置数据
      this.clientList = []
      this.selectedClient = null
      this.selectedModel = null
      this.currentRecipeId = -1
      this.currentTagCodePrefix = ''

      // 获取工站编码
      const stationCode = this.$route.query.station_code || ''

      // 如果没有工站编码，则不进行查询
      if (!stationCode) {
        console.warn('获取配方模型数据 - 工站编码缺失，无法查询')
        this.$message({
          type: 'warning',
          message: this.$t('core.secsais.stationCodeMissing')
        })
        return
      }

      // 构建查询参数
      const params = {
        client_des: this.query.client_des,
        client_code: this.query.client_code,
        station_code: stationCode,
        size: 1000 // 设置足够大的size，确保获取所有数据
      }

      console.log('获取配方模型数据 - 参数:', params)

      recipeApi.queryRecipeModels(params).then(res => {
        if (res.code === 0 && res.data) {
          this.clientList = this.processClientData(res.data)

          // 如果MQTT已连接，订阅心跳主题
          if (this.mqttConnStatus) {
            this.subscribeHeartbeats()
          }
        } else {
          this.$message({
            type: 'error',
            message: res.msg || this.$t('core.secsais.fetchModelDataFailed')
          })
        }
      }).catch(error => {
        console.error('获取配方模型数据失败:', error)
        this.$message({
          type: 'error',
          message: this.$t('core.secsais.fetchModelDataFailed')
        })
      })
    },

    // 处理客户端数据，将相同客户端的数据合并
    processClientData(data) {
      const clientMap = {}

      // 按客户端ID分组
      data.forEach(item => {
        if (!clientMap[item.client_id]) {
          clientMap[item.client_id] = {
            client_id: item.client_id,
            client_code: item.client_code,
            client_des: item.client_des,
            // 默认心跳状态为false（离线）
            heartbeat: false,
            models: []
          }
        }

        // 添加模型信息
        if (item.models && item.models.length > 0) {
          item.models.forEach(model => {
            clientMap[item.client_id].models.push({
              ...model,
              tag_group_id: item.tag_group_id,
              tag_group_code: item.tag_group_code,
              display_name: item.display_name
            })
          })
        }
      })

      // 转换为数组
      return Object.values(clientMap)
    },

    // 选择客户端
    selectClient(client) {
      this.selectedClient = client
      this.selectedModel = null
      this.currentRecipeId = -1
      this.currentTagCodePrefix = ''
    },

    // 选择模型
    selectModel(model) {
      this.selectedModel = model
      this.currentRecipeId = model.tag_group_id
      this.currentTagCodePrefix = model.tag_code_prefix

      // The detail component will automatically load the recipe details
      // through its props watcher, so we don't need to call loadRecipeDetails() here
    },

    // 重置查询条件
    resetQuery() {
      // 保留station_code参数
      const stationCode = this.$route.query.station_code || this.query.station_code || ''

      this.query = {
        client_des: '',
        client_code: '',
        station_code: stationCode
      }
      this.fetchRecipeModels()
    },

    // 获取数据
    fetchContent() {
      this.fetchRecipeModels()
    },

    // 刷新实例列表
    refreshClientList() {
      // 显示加载中提示
      this.$message({
        message: this.$t('core.secsais.refreshingInstanceList'),
        type: 'info',
        duration: 1000
      })

      // 重新获取数据
      this.fetchRecipeModels()
    },

    // 删除
    doDelete(data) {
      this.$confirm(this.$t('core.secsais.confirmDelete', [1]), this.$t('core.secsais.prompt'), {
        confirmButtonText: this.$t('core.secsais.confirm'),
        cancelButtonText: this.$t('core.secsais.cancel'),
        type: 'warning'
      }).then(() => {
        this.crud.doDelete(data)
      }).catch(() => {
        console.log('取消删除')
      })
    },

    // ----------------------------------【MQTT】----------------------------------
    // 连接MQTT
    connectMqtt() {
      console.log('MQTT连接启动中...')

      // 获取单元信息
      const stationCode = this.$route.query.station_code || ''
      if (!stationCode) {
        console.warn('工站编码缺失，无法连接MQTT')
        return
      }

      // 获取单元信息
      const query = {
        userName: Cookies.get('userName'),
        station_code: stationCode,
        current_ip: window.location.hostname
      }

      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (!result) {
            console.warn('获取单元信息失败')
            return
          }

          // 获取连接地址
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          const connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          console.log('MQTT连接URL：' + connectUrl)

          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt)

          // 连接成功
          this.clientMqtt.on('connect', () => {
            console.log('MQTT连接成功')
            this.mqttConnStatus = true

            // 订阅心跳主题
            this.subscribeHeartbeats()
          })

          // MQTT连接失败
          this.clientMqtt.on('error', (error) => {
            console.error('MQTT连接失败:', error)
            this.clientMqtt.end()
          })

          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            console.warn('MQTT连接断开，正在重连...')
          })

          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            console.log('MQTT收到来自', topic, '的消息', message.toString())
            this.handleMqttMessage(topic, message)
          })
        })
        .catch(error => {
          console.error('获取单元信息失败:', error)
        })
    },

    // 订阅心跳主题
    subscribeHeartbeats() {
      if (!this.mqttConnStatus) {
        console.warn('MQTT未连接，无法订阅心跳主题')
        return
      }

      // 订阅所有实例的心跳主题
      this.clientList.forEach(client => {
        const topic = 'SCADA_BEAT/' + client.client_code
        this.subscribeTopic(topic)
      })
    },

    // 订阅主题
    subscribeTopic(topic) {
      if (!this.mqttConnStatus) {
        console.warn('MQTT未连接，无法订阅主题:', topic)
        return
      }

      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!error) {
          console.log('MQTT订阅成功:', topic)
        } else {
          console.error('MQTT订阅失败:', topic, error)
        }
      })
    },

    // 处理MQTT消息
    handleMqttMessage(topic, message) {
      try {
        // 处理心跳消息
        if (topic.startsWith('SCADA_BEAT/')) {
          const clientCode = topic.replace('SCADA_BEAT/', '')
          const jsonData = JSON.parse(message)
          const beat = jsonData.Beat

          // 更新心跳状态
          const client = this.clientList.find(c => c.client_code === clientCode)
          if (client) {
            client.heartbeat = beat === '1'
          }
        }
      } catch (error) {
        console.error('处理MQTT消息失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.recipeInfo {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.tableFirst {
  margin-top: 0;
}

/* 级联选择器样式 */
.cascade-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  min-width: 150px;
}

.cascade-header {
  background-color: #f5f7fa;
  padding: 10px 15px;
  font-weight: bold;
  border-bottom: 1px solid #e4e7ed;
}

.cascade-list {
  height: 600px;
  overflow-y: auto;
}

.client-list, .model-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.client-list li, .model-list li {
  padding: 8px 10px;
  cursor: pointer;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
  word-break: break-all;
}

.client-list li:hover, .model-list li:hover {
  background-color: #f5f7fa;
}

.client-list li.active, .model-list li.active {
  background-color: #ecf5ff;
  color: #409EFF;
}

.client-item, .model-item {
  display: flex;
  flex-direction: column;
}

.client-name, .model-name {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-code, .model-prefix {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 状态指示器样式 */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
}

.normal-status {
  background-color: rgb(9, 196, 9); /* 绿色，表示可以修改编辑配方 */
}

.disabled-status {
  background-color: #b3b3b3; /* 灰色，表示不可修改 */
}

/* 刷新按钮样式 */
.refresh-button {
  float: right;
  padding: 0;
  margin: 0;
  font-size: 16px;
}

.refresh-button:hover {
  color: #409EFF;
}
</style>
