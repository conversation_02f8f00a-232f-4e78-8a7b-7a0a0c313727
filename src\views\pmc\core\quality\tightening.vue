<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-9 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="车辆VIN：">  <!-- 车辆VIN： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item label="时间范围：">  <!-- 时间范围： -->
                <div class="block">
                  <el-date-picker
                    v-model="query.quality_data"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" label="拧紧状态：">
                <el-select v-model="query.shaft_status" clearable>
                  <el-option v-for="item in dict.MARK_CERTIFICATION" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-3 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
                <el-button v-permission="permission.down" :loading="crud.downloadLoading" :disabled="!crud.data.length" size="small" icon="el-icon-download" @click="doExport">导出</el-button>
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column  :show-overflow-tooltip="true" prop="vin" label="VIN号" width="180" />  <!-- 车辆VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="device_code" width="140" label="设备编号" />  <!-- 设备编号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="device_des" width="180" label="设备名称" />  <!-- 设备名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_set_id" width="100" label="程序号" />  <!-- 程序号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="torque_min_limit" label="扭矩最小值" />  <!-- 扭矩最小值 -->
            <el-table-column  :show-overflow-tooltip="true" prop="torque_target" label="扭矩目标值" />  <!-- 扭矩目标值 -->
            <el-table-column  :show-overflow-tooltip="true" prop="torque_max_limit" label="扭矩最大值" />  <!-- 扭矩最大值 -->
            <el-table-column  :show-overflow-tooltip="true" prop="torque_value" label="扭矩实际值" />  <!-- 扭矩实际值 -->
            <el-table-column  label="扭距状态" align="center" prop="torque_status">
              <!-- 扭距状态 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <span v-if="scope.row.torque_status === 'NG'">NG</span>
                <span v-else-if="scope.row.torque_status === 'OK'">OK</span>
                <!-- <span v-else>HIGH</span> -->
              </template>
            </el-table-column>
            <el-table-column  label="拧紧状态" align="center" prop="shaft_status">
              <!-- 拧紧状态 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.MARK_CERTIFICATION[scope.row.shaft_status] }}
              </template>
            </el-table-column>
            <el-table-column  prop="shaft_time" label="拧紧时间" width="180" />  <!-- 拧紧时间 -->
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >总数量：{{ page.total }}</el-button>
              <el-button
                type="primary"
              >当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { PmcExportSelOne } from '@/api/pmc/pmcExport'
import { downloadFile } from '@/utils/index'
import { fileDownload } from '@/api/core/file/file'
import getTighteningData from '@/api/pmc/quality/sysTightening'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {

}
export default {
  name: 'sysTightening',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: '拧紧',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'vin',
      // // 排序
      sort: ['vin asc'],
      // CRUD Method
      crudMethod: { ...getTighteningData },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据字典
  dicts: ['MARK_CERTIFICATION'],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      }
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {
    doExport() {
      if (!this.query.quality_data && !this.query.vin) {
        this.$message({ message: '车辆vin号和时间范围必须二选一！', type: 'info' })
        return
      }
      this.crud.downloadLoading = true
      getTighteningData.exportEventInsert(this.query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.exportId = defaultQuery.result
            this.timer = setInterval(this.getFileStatus, 2000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
            this.crud.downloadLoading = false
          }
        })
        .catch(() => {
          this.$message({ message: '导出数据异常', type: 'error' })
          this.crud.downloadLoading = false
        })
    },
    getFileStatus() {
      // 获取文件下载状态
      PmcExportSelOne({ export_id: this.exportId })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0 && defaultQuery.data) {
            if (defaultQuery.data.finish_status === 'OK') {
              clearInterval(this.timer)
              // 文件已生成，则下载该文件
              fileDownload({ file_path: defaultQuery.data.down_url }).then(result => {
                downloadFile(result, defaultQuery.data.export_name, 'csv')
                this.crud.downloadLoading = false
              }).catch(() => {
                this.crud.downloadLoading = false
              })
            } else if (defaultQuery.data.finish_status === 'NG') {
              this.$message({ message: defaultQuery.data.error_msg, type: 'error' })
              clearInterval(this.timer)
              this.crud.downloadLoading = false
            }
          }
        })
    },
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
    color: #fff;
    background-color: #1473c5;
    border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
    background: #438fd1;
    border-color: #438fd1;
    color: #fff;
}
.labelIline{
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label{
    white-space: nowrap;
  }
}
::v-deep .marginL{
  margin-left: 10px;
}
</style>
