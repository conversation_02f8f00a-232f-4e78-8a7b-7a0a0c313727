<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
    <!-- <div v-if="$store.state.settings.showFooter" id="el-main-footer">
      <span v-html="$store.state.settings.footerTxt" />
      <span> ⋅ </span>
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{ $store.state.settings.caseNumber }}</a>
    </div> -->
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      console.log(this.$store.state.tagsView.cachedViews)
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh);
  }

  .fixed-header + .app-main {
    padding-top: 34px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.el-table {
  color: #333333;
}
.el-table thead {
  color: #333333;
}
.el-table th {
  background-color: #f0f0f0;
  font-weight: 500;
  font-size: 14px;
}
// 表格最外边框
.el-table--group,
.el-table--border {
  border: none;
}
.el-table__fixed-right-patch {
  background-color: #f0f0f0;
}
// 表格最外层边框-底部边框
.el-table--border::after,
.el-table--group::after {
  width: 0;
}
.el-table__fixed-right::before,
.el-table__fixed::before {
  width: 0;
}
.el-form-item__label {
  color: #333333;
}
.el-button {
  border-radius: 0px;
}
.el-button--primary {
  color: #fff;
  background-color: #445ddb;
  border-color: #445ddb;
}
.el-button--warning {
  color: #fff;
  background-color: #f4c326;
  border-color: #f4c326;
}
.el-button--danger {
  color: #fff;
  background-color: #ff9160;
  border-color: #ff9160;
}
.el-button--primary.is-plain.is-disabled {
  color: #445ddb;
  background: #e1e7f7;
  border-color: #ffffff;
}
.el-button--primary.is-plain {
  color: #445ddb;
  background: #e1e7f7;
  border-color: #ffffff;
}
.el-button--text {
  color: #445ddb;
}
</style>
