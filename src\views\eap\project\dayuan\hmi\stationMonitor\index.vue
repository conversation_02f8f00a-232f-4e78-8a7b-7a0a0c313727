<template>
  <div class="app-container">
    <el-card shadow="always">
      <el-row :gutter="20">
        <el-col :span="6">
          <div style="min-height:200px;border:1px solid #9EBAF4;border-radius: 5px;">
            <div class="title">
              {{ aArea.region_des }}
            </div>
            <div class="show_content">
              <div v-for="(item,index) in aArea.item_list" :key="index" class="item wrappstyle">
                <template v-if="item.display_type==='value'">
                  <span>{{ item.item_des }}</span> <span class="value" style="float:right">{{ item.tag_value+" "+item.unit }}&nbsp;&nbsp;</span>
                </template>
                <template v-else-if="item.display_type==='status'">
                  <template v-if="item.tag_value===item.green_value.toString()">
                    <p><span class="wholeline wholelinenormal" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else-if="item.tag_value===item.red_value.toString()">
                    <p><span class="wholeline wholelineerror" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else>
                    <p><span class="wholeline wholelinegray" /><span>{{ item.item_des }}</span></p>
                  </template>
                </template>
                <template v-else-if="item.display_type==='label'">
                  <span>{{ item.item_des }}</span>
                </template>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <img :src="devicePicUrl" alt="" style="height:200px;width:100%">
        </el-col>
        <el-col :span="6">
          <div style="min-height:200px;border:1px solid #9EBAF4;border-radius: 5px;">
            <div class="title">
              {{ bArea.region_des }}
            </div>
            <div class="show_content">
              <div v-for="(item,index) in bArea.item_list" :key="index" class="item wrappstyle">
                <template v-if="item.display_type==='value'">
                  <span>{{ item.item_des }}</span> <span class="value" style="float:right">{{ item.tag_value+" "+item.unit }}&nbsp;&nbsp;</span>
                </template>
                <template v-else-if="item.display_type==='status'">
                  <template v-if="item.tag_value===item.green_value.toString()">
                    <p><span class="wholeline wholelinenormal" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else-if="item.tag_value===item.red_value.toString()">
                    <p><span class="wholeline wholelineerror" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else>
                    <p><span class="wholeline wholelinegray" /><span>{{ item.item_des }}</span></p>
                  </template>
                </template>
                <template v-else-if="item.display_type==='label'">
                  <span>{{ item.item_des }}</span>
                </template>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top:10px;">
        <el-col :span="8">
          <div style="min-height:200px;border:1px solid #9EBAF4;border-radius: 5px;">
            <div class="title">
              {{ cArea.region_des }}
            </div>
            <div class="show_content">
              <div v-for="(item,index) in cArea.item_list" :key="index" class="item wrappstyle">
                <template v-if="item.display_type==='value'">
                  <span>{{ item.item_des }}</span> <span class="value" style="float:right">{{ item.tag_value+" "+item.unit }}&nbsp;&nbsp;</span>
                </template>
                <template v-else-if="item.display_type==='status'">
                  <template v-if="item.tag_value===item.green_value.toString()">
                    <p><span class="wholeline wholelinenormal" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else-if="item.tag_value===item.red_value.toString()">
                    <p><span class="wholeline wholelineerror" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else>
                    <p><span class="wholeline wholelinegray" /><span>{{ item.item_des }}</span></p>
                  </template>
                </template>
                <template v-else-if="item.display_type==='label'">
                  <span>{{ item.item_des }}</span>
                </template>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div style="min-height:200px;border:1px solid #9EBAF4;border-radius: 5px;">
            <div class="title">
              {{ dArea.region_des }}
            </div>
            <div class="show_content">
              <div v-for="(item,index) in dArea.item_list" :key="index" class="item wrappstyle">
                <template v-if="item.display_type==='value'">
                  <span>{{ item.item_des }}</span> <span class="value" style="float:right">{{ item.tag_value+" "+item.unit }}&nbsp;&nbsp;</span>
                </template>
                <template v-else-if="item.display_type==='status'">
                  <template v-if="item.tag_value===item.green_value.toString()">
                    <p><span class="wholeline wholelinenormal" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else-if="item.tag_value===item.red_value.toString()">
                    <p><span class="wholeline wholelineerror" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else>
                    <p><span class="wholeline wholelinegray" /><span>{{ item.item_des }}</span></p>
                  </template>
                </template>
                <template v-else-if="item.display_type==='label'">
                  <span>{{ item.item_des }}</span>
                </template>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div style="min-height:200px;border:1px solid #9EBAF4;border-radius: 5px;">
            <div class="title">
              {{ eArea.region_des }}
            </div>
            <div class="show_content">
              <div v-for="(item,index) in eArea.item_list" :key="index" class="item wrappstyle">
                <template v-if="item.display_type==='value'">
                  <span>{{ item.item_des }}</span> <span class="value" style="float:right">{{ item.tag_value+" "+item.unit }}&nbsp;&nbsp;</span>
                </template>
                <template v-else-if="item.display_type==='status'">
                  <template v-if="item.tag_value===item.green_value.toString()">
                    <p><span class="wholeline wholelinenormal" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else-if="item.tag_value===item.red_value.toString()">
                    <p><span class="wholeline wholelineerror" /><span>{{ item.item_des }}</span></p>
                  </template>
                  <template v-else>
                    <p><span class="wholeline wholelinegray" /><span>{{ item.item_des }}</span></p>
                  </template>
                </template>
                <template v-else-if="item.display_type==='label'">
                  <span>{{ item.item_des }}</span>
                </template>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top:10px;">
        <el-col :span="24">
          <div style="height:60px;border:1px solid #9EBAF4;border-radius: 5px;line-height:60px;padding-left:10px;">
            <el-input v-model="panel" placeholder="请输入扫描条码" clearable size="small" style="width:300px" @keyup.enter.native="scanGetJDEPruoduct" />
            <el-button icon="el-icon-search" type="primary" round @click="scanGetJDEPruoduct">获取JDE料号</el-button>&nbsp;&nbsp;
            <span style="font-weight:600;font-size:16px;color: #1F8E5C;">{{ jdeCode }}</span>
            <el-button icon="el-icon-search" type="warning" style="float:right;margin-top:10px;margin-right:10px;" round @click="repiceDialogVisible=true">配方选择</el-button>

            <el-dialog title="配方选择" :visible.sync="repiceDialogVisible" width="80%" top="65px">
              <recipe v-if="repiceDialogVisible" ref="recipe" :cell_ip="cellIp" :webapi_port="webapiPort" @sendMessage="sendMessage" />
            </el-dialog>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top:10px;">
        <el-col :span="24">
          <div style="height:350px;border:1px solid #9EBAF4;border-radius: 5px;">
            <!-- 机台指标 -->
            <div class="title">
              报警信息
            </div>
            <el-table
              :data="alarmData"
              style="width: 100%"
              height="310"
              :cell-style="tableRowClassName"
            >
              <el-table-column 
                prop="item_date"
                width="150"
                label="报警时间"
                show-overflow-tooltip
              />
              <el-table-column 
                prop="alarm_code"
                width="100"
                label="报警代码"
              />
              <el-table-column 
                prop="alarm_des"
                label="报警信息"
              />
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { selTree as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import { selStationConfig } from '@/api/eap/core/eapScreenConfig'
import { getJDEPruoduct } from '@/api/eap/project/dayuan/eapMflexInterf'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import recipe from '@/views/eap/project/dayuan/hmi/stationMonitor/recipe'
export default {
  name: 'EAP_DEVICE_MONITOR',
  components: { recipe },
  data() {
    return {
      timer: '',
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // A区域
      aArea: { },
      // b区域
      bArea: { },
      // c区域
      cArea: { },
      // d区域
      dArea: { },
      // e区域
      eArea: { },
      // 图片显示
      devicePicUrl: '',
      repiceDialogVisible: false,
      repiceData: [],
      repiceDetailData: [],
      alarmData: [],
      panel: '',
      jdeCode: '',
      scanError: ''
    }
  },
  mounted: function() {
    this.timer = setInterval(this.getAlarmData, 3000)
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  created: function() {
    this.getStationData()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      return { background: '#f00', color: '#000', fontWeight: 700 }
    },
    getStationData() {
      const query = {
        user_name: Cookies.get('userName')
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const stationData = JSON.parse(defaultQuery.result)
            if (stationData.length > 0) {
              this.currentStation = {
                prod_line_id: stationData[0].prod_line_id,
                prod_line_code: stationData[0].prod_line_code,
                prod_line_des: stationData[0].prod_line_des,
                station_id: stationData[0].station_list[0].station_id,
                station_code: stationData[0].station_list[0].station_code,
                station_des: stationData[0].station_list[0].station_des,
                cell_id: stationData[0].station_list[0].cell_id
              }
              this.getStationConfig()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getStationConfig() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selStationConfig(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const StationConfig = JSON.parse(defaultQuery.result)
            if (StationConfig.length > 0) {
              this.aArea = StationConfig.filter(item => item.region_code === 'A')
              if (this.aArea.length > 0) {
                this.aArea = this.aArea[0]
              }
              this.bArea = StationConfig.filter(item => item.region_code === 'B')
              if (this.bArea.length > 0) {
                this.bArea = this.bArea[0]
              }
              this.cArea = StationConfig.filter(item => item.region_code === 'C')
              if (this.cArea.length > 0) {
                this.cArea = this.cArea[0]
              }
              this.dArea = StationConfig.filter(item => item.region_code === 'D')
              if (this.dArea.length > 0) {
                this.dArea = this.dArea[0]
              }
              this.eArea = StationConfig.filter(item => item.region_code === 'E')
              if (this.eArea.length > 0) {
                this.eArea = this.eArea[0]
              }
              const devicePic = StationConfig.filter(item => item.region_code === 'DevicePic')
              if (devicePic.length > 0) {
                if (devicePic[0].item_list.length > 0) {
                  this.devicePicUrl = (process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : '/') + 'aisEsbWeb/eap/core/EapScreenConfigImage/' + devicePic[0].item_list[0].screen_config_d_id + '?id=' + Math.random().toString(16).substr(2, 8)
                }
              }
              this.getCellIp()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getAlarmData()
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getAlarmData() {
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      var queryData = {
        reset_flag: 'N'
      }
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      if (this.aArea.item_list !== undefined) {
        this.aArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.bArea.item_list !== undefined) {
        this.bArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.cArea.item_list !== undefined) {
        this.cArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.dArea.item_list !== undefined) {
        this.dArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.eArea.item_list !== undefined) {
        this.eArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach(item => {
                const tag_value = item.tag_value === undefined ? '' : item.tag_value
                if (this.aArea.item_list !== undefined) {
                  const aAreaItem = this.aArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (aAreaItem.length > 0) {
                    aAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.bArea.item_list !== undefined) {
                  const bAreaItem = this.bArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (bAreaItem.length > 0) {
                    bAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.cArea.item_list !== undefined) {
                  const cAreaItem = this.cArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (cAreaItem.length > 0) {
                    cAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.dArea.item_list !== undefined) {
                  const dAreaItem = this.dArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (dAreaItem.length > 0) {
                    dAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.eArea.item_list !== undefined) {
                  const eAreaItem = this.eArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (eAreaItem.length > 0) {
                    eAreaItem[0].tag_value = tag_value
                  }
                }
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }

      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        if (this.aArea.item_list !== undefined) {
          this.aArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.bArea.item_list !== undefined) {
          this.bArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.cArea.item_list !== undefined) {
          this.cArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.dArea.item_list !== undefined) {
          this.dArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.eArea.item_list !== undefined) {
          this.eArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }

        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        var jsonData = JSON.parse(message)
        if (jsonData == null) return

        if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          if (this.aArea.item_list !== undefined) {
            const aAreaItem = this.aArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (aAreaItem.length > 0) {
              aAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.bArea.item_list !== undefined) {
            const bAreaItem = this.bArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (bAreaItem.length > 0) {
              bAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.cArea.item_list !== undefined) {
            const cAreaItem = this.cArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (cAreaItem.length > 0) {
              cAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.dArea.item_list !== undefined) {
            const dAreaItem = this.dArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (dAreaItem.length > 0) {
              dAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.eArea.item_list !== undefined) {
            const eAreaItem = this.eArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (eAreaItem.length > 0) {
              eAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          // console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          this.$message({ message: '操作成功！', type: 'success' })
        } else {
          this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    },
    // 扫描获取JDE料号
    scanGetJDEPruoduct() {
      if (this.panel === '') {
        this.$message({ message: '请输入扫描条码', type: 'info' })
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '正在获取JDE料号，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code,
        panel: this.panel
      }
      getJDEPruoduct(query)
        .then(res => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.jdeCode = 'JDE料号：' + defaultQuery.result
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          loading.close()
          this.$message({ message: '查询异常', type: 'error' })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.title{
    background-image: linear-gradient(to right, #9EBAF4,#D0DEFA,#D0DEFA);
    color: #333;
    line-height: 35px;
    height: 35px;
    font-weight:600;
    padding-left:15px;
}
.show_content{
    background-color: #E8EFFC;
    min-height:162px;
}
.show_content .item{
    color: #333;
    font-weight:600;
    line-height: 30px;
    padding-left:15px;
    font-size: 14px;
}
.show_content .value{
    color: #1F8E5C;
    font-size: 16px;
}
.wrappstyle{
margin-bottom: 2px;
font-weight: normal;
font-size: 13px;
  p{
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    span{
      margin: 0 5px;
    }
  }
}
.wholeline{
  width:18px;
  height: 18px;
  border-radius: 50%;
  flex-shrink: 0;
}
.wholelinenormal{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #00B050;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.wholelineerror{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #f00;
  box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.wholelinegray{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
</style>
