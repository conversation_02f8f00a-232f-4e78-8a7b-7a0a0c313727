import store from '@/store'
import { sel } from '@/api/core/system/sysUser'
import { sel as sysRoleSel } from '@/api/core/system/sysRole'
import Cookies from 'js-cookie'

// https://blog.csdn.net/qlin_11/article/details/99290021 vue中@param 常用注释模板
// @param 常用注释模板
// 代码在注释区会有这样的写法，用来备注变量的类型

// https://blog.csdn.net/wang_xiao_ye/article/details/89385023 Vue中常用的数组方法
// .filter() 方法创建一个新的数组，新数组中的元素是通过检查指定数组中符合条件的所有元素
// .map() 返回一个新数组，数组中的元素为原始数组元素调用函数处理后的值(按照原始数组元素顺序依次处理元素)
// .forEach() 用于调用数组的每个元素，并将元素传递给回调函数
// .find() 返回通过测试（函数内判断）的数组的第一个元素的值
// .findIndex() 返回传入一个测试条件（函数）符合条件的数组第一个元素位置
// .some() 用于检测数组中的元素是否满足指定条件（函数提供）
// .every() 用于检测数组所有元素是否都符合指定条件（通过函数提供）

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
export default function checkPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.roles
    const permissionRoles = value

    const hasPermission = roles.some(role => {
      return permissionRoles.includes(role)
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like v-permission="['admin','editor']"`)
    return false
  }
}

export async function isDisabled(permissionRoles) {
  if (!Array.isArray(permissionRoles)) return false
  const user_id = store.getters && store.getters.user && store.getters.user.id
  const nickName = store.getters && store.getters.user && (store.getters.user.nickname || store.getters.user.nickName)
  console.log(store.getters)
  if (!nickName) return
  try {
    const res = await sel({ staffIdName: nickName, user_name: Cookies.get('userName'), page: 1, size: 100, sort: 'user_id asc' })
    if (res.code === 0 && res.data.length > 0) {
      const resData = res.data.filter(item => item.user_id === parseInt(user_id))
      const response = await sysRoleSel({ page: 1, size: 100, sort: 'role_id asc', roleCodeDes: nickName })
      const result = response.data.length > 0 && checkIfEqual(resData, response.data, 'role_id')
      const hasPermission = result && response.data.some(role =>
        permissionRoles.some(permissionRole =>
          role.role_id === resData[0].role_id && permissionRole.includes(role.role_code)
        )
      )
      return hasPermission
    }
  } catch (error) {
    console.error('Error:', error)
  }
  return false
}

export function checkIfEqual(arr1, arr2, key) {
  console.log(arr1, arr2, key)
  return arr1.some(obj1 => arr2.some(obj2 => obj1[key] === obj2[key]))
}

export function isShow() {
  const roleData = Cookies.get('wxRole') && JSON.parse(Cookies.get('wxRole'))
  if (!Object.keys(roleData).length) return false
  return roleData.roleCode === 'OP'
}

