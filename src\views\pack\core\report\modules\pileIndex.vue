<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog
          :title="$t('lang_pack.vie.viewDetails')"
          width="80%"
          :before-close="handleClose"
          :visible.sync="dialogVisible"
        >
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
          >
            <!-- 时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_date"
              :label="$t('lang_pack.vie.time')"
              width="140"
              align="center"
            />
            <!-- 订单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('view.field.plan.lotNum')"
              width="120"
              align="center"
            />
            <!-- SET顺序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_index"
              :label="$t('lang_pack.vie.setOrder')"
              width="80"
              align="center"
            />
            <!-- 线扫流水号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="board_sn"
              :label="$t('lang_pack.vie.boardSn')"
              width="130"
              align="center"
            />
            <!-- 包装条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pile_barcode"
              :label="$t('lang_pack.vie.pileBarcode')"
              width="120"
              align="center"
            />
            <!-- SET条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_barcode"
              :label="$t('lang_pack.vie.setCode')"
              width="120"
              align="center"
            />
            <!-- SET状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_status"
              :label="$t('lang_pack.vie.setStatus')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-button
                  size="small"
                  type="primary"
                  :class="scope.row.array_status == 'OK' ? 'btnGreen' : 'btnRed'"
                >
                  {{ scope.row.array_status }}
                </el-button>
              </template>
            </el-table-column>
            <!-- SET分选NG代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_ng_code"
              :label="$t('lang_pack.vie.arrayNgCode')"
              width="120"
              align="center"
            />
            <!-- SET分选NG描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_ng_msg"
              :label="$t('lang_pack.vie.arrayNgMsg')"
              width="120"
              align="center"
            />
            <!-- 线扫SET等级 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_level"
              :label="$t('lang_pack.vie.arrayLevel')"
              width="120"
              align="center"
            />
            <!-- 线扫光学点检测结果 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_mark"
              :label="$t('lang_pack.vie.arrayMark')"
              width="140"
              align="center"
            />
            <!-- 线扫SET下PCS数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_bd_count"
              :label="$t('lang_pack.vie.arrayBdCount')"
              width="140"
              align="center"
            />
            <!-- 板件判断结果 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="board_result"
              :label="$t('lang_pack.vie.boardResult')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-button
                  size="small"
                  type="primary"
                  :class="scope.row.board_result == '1' ? 'btnGreen' : (scope.row.board_result == '2' ? 'btnYellow' : (scope.row.board_result == '3' ? 'btnRed' : (scope.row.board_result == '4' ? 'btnRed' : 'btnRed')))"
                >
                  {{ scope.row.board_result == '1' ? 'OK' : (scope.row.board_result == '2' ? 'XOUT' : (scope.row.board_result == '3' ? 'NG' : (scope.row.board_result == '4' ? $t('lang_pack.vie.NGmulCod') : 'NG'))) }}
                </el-button>
              </template>
            </el-table-column>
            <!-- 旋转方向 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="board_turn"
              :label="$t('lang_pack.vie.rotationDirection')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.board_turn === '1' ? $t('lang_pack.vie.rotate') :
                  $t('lang_pack.vie.NORotate') }}
              </template>
            </el-table-column>
            <!-- 堆叠位置 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="deposit_position"
              :label="$t('lang_pack.vie.stackingPosition')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <!-- {{
                  scope.row.deposit_position === '1' ? $t('lang_pack.vie.OKPosition') :
                  scope.row.deposit_position === '2' ? $t('lang_pack.vie.NGPosition') :
                  scope.row.deposit_position
                }} -->
                {{ dict.label.DEPOSIT_POSITION[scope.row.deposit_position] }}
              </template>
            </el-table-column>
            <!-- 是否为XOUT分选 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="xout_flag"
              :label="$t('lang_pack.vie.xoutFlag')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.xout_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- XOUT设定数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="xout_set_num"
              :label="$t('lang_pack.vie.xoutSetNum')"
              width="120"
              align="center"
            />
            <!-- XOUT实际数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="xout_act_num"
              :label="$t('lang_pack.vie.xoutActNum')"
              width="120"
              align="center"
            />
            <!-- SET正面线扫数据 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_front_info"
              :label="$t('lang_pack.vie.arrayFrontInfo')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag
                  style="cursor:pointer"
                  :disabled="!scope.row.array_id"
                  @click="viewSetData(scope.row, 'array_front_info', $t('lang_pack.vie.setFront'))"
                >查看</el-tag>
              </template>
            </el-table-column>
            <!-- SET反面线扫数据 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_back_info"
              :label="$t('lang_pack.vie.arrayBackInfo')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag
                  style="cursor:pointer"
                  :disabled="!scope.row.array_id"
                  @click="viewSetData(scope.row, 'array_back_info', $t('lang_pack.vie.setOpposite'))"
                >查看</el-tag>
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column prop="task_type" :label="$t('lang_pack.vie.orderType')">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="130"
              align="center"
            />
            <!-- 版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_version"
              :label="$t('lang_pack.vie.version')"
              width="120"
              align="center"
            />
            <!-- SET类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_type"
              :label="$t('lang_pack.vie.setType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.array_type] }}
              </template>
            </el-table-column>
            <!-- PCS类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_type"
              :label="$t('lang_pack.vie.pcsType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.bd_type] }}
              </template>
            </el-table-column>
            <!-- 板长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.vie.plateLen')"
              width="130"
              align="center"
            />
            <!-- 板宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.vie.plateWid')"
              width="130"
              align="center"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_tickness"
              :label="$t('lang_pack.vie.plateThi')"
              width="130"
              align="center"
            />
            <!-- 板重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.vie.plateWei')"
              width="130"
              align="center"
            />
            <!-- 周期 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cycle_period"
              :label="$t('lang_pack.vie.cycle')"
              width="130"
              align="center"
            />
            <!-- 截取批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_lot"
              :label="$t('lang_pack.vie.splitLot')"
              width="120"
              align="center"
            />
            <!-- 截取料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_model"
              :label="$t('lang_pack.vie.splitModel')"
              width="120"
              align="center"
            />
            <!-- 是否被打包使用 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pile_use_flag"
              :label="$t('lang_pack.vie.pileUseFlag')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.pile_use_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- 是否解绑 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="unbind_flag"
              :label="$t('lang_pack.vie.unbindFlag')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.unbind_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                  $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <!-- 解绑时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="unbind_time"
              :label="$t('lang_pack.vie.unbindTime')"
              width="120"
              align="center"
            />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="left">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" @click="viewsDetails(scope.row)">{{
                  $t('lang_pack.vie.viewDetails')
                }}</el-button> <!-- 查看明细 -->
              </template>
            </el-table-column>
          </el-table>
          <pagination />
        </el-dialog>
        <el-dialog :title="jsonTitle" width="50%" :visible.sync="jsonDialogVisible">
          <el-input v-model="jsonStr" autosize type="textarea" :rows="20" :placeholder="$t('lang_pack.interfaceLogs.pleaseEnter')" style="max-height: 450px;overflow: auto;" />
          <span style="color:red;">{{ jsonErrorMsg }}</span>
        </el-dialog>
      </el-col>
    </el-row>
    <detail v-if="tableDialogVisible" ref="tableDialog" :array_id="array_id" @ok="tableDialogVisible = false" />
  </el-card>
</template>
<script>
import detail from './setIndex'
import { InfoSelect } from '@/api/pack/task'
import crudpile from '@/api/pack/pile'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'INVENTORYDETAILS',
  components: { crudOperation, pagination, detail },
  cruds() {
    return CRUD({
      title: '',
      // 唯一字段
      idField: 'array_id',
      // 排序
      sort: ['array_id asc'],
      // CRUD Method
      crudMethod: { ...crudpile },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        pile_barcodes: this.propsData.pile_barcode
      }
    })
  },
  props: {
    pile_barcode: {
      type: String
    }
  },
  dicts: ['TASK_TYPE', 'QR_TYPE', 'DEPOSIT_POSITION'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false,
      jsonStr: '',
      jsonErrorMsg: '',
      jsonTitle: '',
      jsonDialogVisible: false,
      array_id: '',
      tableDialogVisible: false
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    },
    viewSetData(row, dataKey, title) {
      const query = {
        array_id: row.array_id
      }
      this.jsonTitle = title
      this.jsonStr = '{}'
      this.jsonErrorMsg = ''
      InfoSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.jsonStr = JSON.stringify(JSON.parse(defaultQuery.data[0][dataKey]), null, 4)
          this.jsonDialogVisible = true
        } else {
          this.jsonStr = ''
          this.$message({ message: defaultQuery.msg, type: 'error' })
        }
      })
        .catch((ex) => {
          this.jsonStr = ''
          this.jsonErrorMsg = ex
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    viewsDetails(row) {
      this.array_id = row.array_id
      this.tableDialogVisible = true
      this.$nextTick(() => {
        this.$refs.tableDialog.dialogVisible = true
      })
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination {
    float: none !important;
    text-align: right;
}

.btnGreen {
    background-color: #0d0 !important;
    border: none !important;
}

.btnRed {
    background-color: #ee1216 !important;
    border: none !important;
}
</style>
