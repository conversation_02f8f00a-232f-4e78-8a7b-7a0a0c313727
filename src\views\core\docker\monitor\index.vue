<template>
  <div class="monitor-container">
    <el-card shadow="always" class="wrapCard CardOne">
      <el-form ref="formServer" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">Docker监控</div>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="6">
          <el-card class="box-card1">
            <div class="wrapTextSelect">
              <el-descriptions title="服务器配置" :column="1">
                <el-descriptions-item label="系统类型">{{ systemFrom.osType }}</el-descriptions-item>
                <el-descriptions-item label="系统版本">{{ systemFrom.operatingSystem }}</el-descriptions-item>
                <el-descriptions-item label="CPU">{{ systemFrom.ncpu }}</el-descriptions-item>
                <el-descriptions-item label="内存数量">{{ systemFrom.memTotal }}</el-descriptions-item>
                <el-descriptions-item label="版本">{{ systemFrom.serverVersion }}</el-descriptions-item>
                <el-descriptions-item label="启动时间">{{ systemFrom.systemTime }}</el-descriptions-item>
                <el-descriptions-item label="监控服务">
                  <!-- 服务： -->
                  <el-select v-model="systemFrom.server_id" clearable size="small" @change="serverChange">
                    <el-option v-for="item in serverData" :key="item.server_id" :label="item.server_des" :value="item.server_id" />
                  </el-select>
                </el-descriptions-item>

                <el-button v-if="true" class="filter-item" size="small" type="primary" plain round icon="el-icon-coin" :disabled="this.systemFrom.server_id === ''" @click="toRefresh">刷新</el-button>
                <!-- 刷新 -->
              </el-descriptions>
            </div>
          </el-card>
        </el-col>
        <el-col :span="18">
          <el-card class="box-card1">
            <el-row class="wrapColItem">
              <el-col class="col-lg-3 col-sm-6 col-12 nopadding">
                <el-card class="cardBgs">
                  <div class="wrapCard">
                    <div class="wrapCardIcon">
                      <i class="el-icon-data-board oneIconBg" />
                    </div>
                    <div class="numberText">
                      <div class="wrapCardNumber">{{ images }}</div>
                      <div class="wrapCardText">镜像数量</div>
                    </div>
                  </div>
                  <div class="banner-hidden">
                    <div class="banner-background-01" />
                    <div class="banner-background-02" />
                  </div>
                </el-card>
              </el-col>
              <el-col class="col-lg-3 col-sm-6 col-12 nopadding">
                <el-card shadow="always" class="cardBgs">
                  <div class="wrapCard">
                    <div class="wrapCardIcon">
                      <i class="el-icon-data-board twoIconBg" />
                    </div>
                    <div class="numberText">
                      <div class="wrapCardNumber">{{ containers }}</div>
                      <div class="wrapCardText">容器数量</div>
                    </div>
                  </div>
                  <div class="banner-hidden">
                    <div class="banner-background-01" />
                    <div class="banner-background-02" />
                  </div>
                </el-card>
              </el-col>
              <el-col class="col-lg-3 col-sm-6 col-12 nopadding">
                <el-card shadow="always" class="cardBgs">
                  <div class="wrapCard">
                    <div class="wrapCardIcon">
                      <i class="el-icon-data-board threeIconBg" />
                    </div>
                    <div class="numberText">
                      <div class="wrapCardNumber">{{ containersRunning }}</div>
                      <div class="wrapCardText">运行数量</div>
                    </div>
                  </div>
                  <div class="banner-hidden">
                    <div class="banner-background-01" />
                    <div class="banner-background-02" />
                  </div>
                </el-card>
              </el-col>
              <el-col class="col-lg-3 col-sm-6 col-12 nopadding">
                <el-card shadow="always" class="cardBgs">
                  <div class="wrapCard">
                    <div class="wrapCardIcon">
                      <i class="el-icon-data-board fourIconBg" />
                    </div>
                    <div class="numberText">
                      <div class="wrapCardNumber">{{ containersStopped }}</div>
                      <div class="wrapCardText">停止数量</div>
                    </div>
                  </div>
                  <div class="banner-hidden">
                    <div class="banner-background-01" />
                    <div class="banner-background-02" />
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" :data="tableDataTable" style="width: 100%" :highlight-current-row="true">
              <el-table-column  label="镜像" prop="image" />
              <el-table-column  label="名称" prop="names" />
              <el-table-column  label="状态" prop="state" />

              <!-- Table单条操作-->
              <el-table-column  label="操作" align="right" width="460px" fixed="right">
                <template slot-scope="scope">
                  <el-link type="success" :disabled="scope.row.state != 'exited'" @click="toButExec(scope.row, 'restart')">{{ scope.row.state != 'exited'?'已重启':'重启' }}</el-link>
                  <el-link type="danger" :disabled="scope.row.state != 'running'" @click="toButExec(scope.row, 'stop')">{{ scope.row.state != 'running'?'已停止':'停止' }}</el-link>
                  <el-link type="warning" :disabled="scope.row.state != 'running'" @click="toButExec(scope.row, 'pause')">{{ scope.row.state != 'running'?'已暂停':'暂停' }}</el-link>
                  <el-link type="success" :disabled="scope.row.state != 'paused'" @click="toButExec(scope.row, 'unpause')">{{ scope.row.state != 'paused'?'已恢复':'恢复' }}</el-link>
                  <el-link type="warning" :disabled="scope.row.state != 'exited'" @click="toButExec(scope.row, 'remove')">删除</el-link>
                  <el-link type="primary" @click="processVisible = true">查看进程</el-link>
                  <el-link type="primary" @click="logVisible = true">查看日志</el-link>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!--弹窗：进程监控-->
    <el-dialog title="进程监控" :visible.sync="processVisible" width="70%">
      <el-table border @header-dragend="crud.tableHeaderDragend()" :data="processTable" style="width: 100%" :highlight-current-row="true">
        <el-table-column  label="程序名称" prop="containerName" />
        <el-table-column  label="进程名" prop="pid" />
        <el-table-column  label="内存(%)" prop="mem" />
        <el-table-column  label="CPU(%)" prop="cpu" />
        <el-table-column  label="占用虚拟内存(KB)" prop="vsz" />
        <el-table-column  label="占用固定内存(%)" prop="rss" />
        <el-table-column  label="使用CPU(时间)" prop="time" />
        <el-table-column  label="状态" prop="heartBeat" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="processVisible = false">取 消</el-button>
        <el-button type="primary" @click="processVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!--弹窗：日志监控-->
    <el-dialog title="日志监控(Cell)" :visible.sync="logVisible" width="70%">
      <el-row>
        <el-col>
          <div class="dropdownStyle">
            <el-dropdown>
              <span class="el-dropdown-link"> 下拉菜单<i class="el-icon-arrow-down el-icon--right" /> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>nginx</el-dropdown-item>
                <el-dropdown-item divided>java</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-checkbox v-model="checked">是否滚动</el-checkbox>
          </div>
        </el-col>
      </el-row>
      <el-table border @header-dragend="crud.tableHeaderDragend()" :data="logTable" style="width: 100%" :highlight-current-row="true">
        <el-table-column  label="程序名称" prop="processName" />
        <el-table-column  label="进程名" prop="courseName" />
        <el-table-column  label="内存(%)" prop="memory" />
        <el-table-column  label="CPU(%)" prop="cpu" />
        <el-table-column  label="占用虚拟内存(KB)" prop="virtualMemory" />
        <el-table-column  label="占用固定内存(%)" prop="fixationMemory" />
        <el-table-column  label="使用CPU(时间)" prop="useCpu" />
        <el-table-column  label="状态" prop="state" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="logVisible = false">清屏</el-button>
        <el-button type="primary" @click="logVisible = false">关闭监控</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { lovServer } from '@/api/core/center/server'

import { dockerMonitor } from '@/api/core/docker/monitor'

export default {
  name: 'DOCKER_MONITOR',
  data() {
    return {
      // 查询条件
      systemFrom: {
        osType: 'Linux',
        operatingSystem: 'Ubuntu 20.04 LTS',
        ncpu: '30',
        memTotal: '33197125632',
        serverVersion: '20.10.17',
        systemTime: '00:00:00',
        server_id: ''
      },
      serverData: [], // 服务Lov

      images: '0',
      containers: '0',
      containersStopped: '0',
      containersRunning: '0',

      // 镜像明细
      tableDataTable: [],
      // 弹窗：进程监控
      processTable: [],
      processVisible: false,
      // 弹窗：日志监控
      logTable: [],
      logVisible: false,
      checked: true
    }
  },
  created: function() {
    // 服务Lov
    this.serverData = []
    var query = {
      userName: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    lovServer(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.count > 0) {
          this.serverData = defaultQuery.data
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // ===============================================================================
    // Server值改变时
    serverChange() {
      if (this.systemFrom.server_id === '') {
        return
      }

      // 当前服务IP
      const serverInfo = this.serverData.filter(item => item.server_id === this.systemFrom.server_id)[0]
      if (serverInfo.server_host_1 !== '') {
        this.currentServerHost = serverInfo.server_host_1
      } else if (serverInfo.server_host_2 !== '') {
        this.currentServerHost = serverInfo.server_host_2
      } else if (serverInfo.server_host_3 !== '') {
        this.currentServerHost = serverInfo.server_host_3
      } else if (serverInfo.server_host_4 !== '') {
        this.currentServerHost = serverInfo.server_host_4
      }

      console.log('获取currentServerHost：' + this.currentServerHost)

      // 刷新
      this.toRefresh()
    },

    // 刷新
    toRefresh() {
      this.toSysQuery()
      this.toContainersQuery()
    },
    // Docker服务器信息查询
    toSysQuery() {
      // 格式化 参数
      var defaultQuery = {
        server_host: this.currentServerHost
      }

      // 配置路径
      var method = ':7089/SysConnDockerInfo'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost' + method
      } else {
        path = 'http://' + this.currentServerHost + method
      }

      console.log('toSysQuery接口地址：' + path)

      dockerMonitor(path, defaultQuery)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          console.log('toSysQuery接口返回值')
          console.log(defaultQuery)

          if (defaultQuery.code === 0) {
            this.systemFrom.osType = defaultQuery.data[0].osType
            this.systemFrom.operatingSystem = defaultQuery.data[0].operatingSystem
            this.systemFrom.ncpu = defaultQuery.data[0].ncpu
            this.systemFrom.memTotal = defaultQuery.data[0].memTotal
            this.systemFrom.serverVersion = defaultQuery.data[0].serverVersion
            this.systemFrom.systemTime = defaultQuery.data[0].systemTime
            this.images = defaultQuery.data[0].images
            this.containers = defaultQuery.data[0].containers
            this.containersStopped = defaultQuery.data[0].containersStopped
            this.containersRunning = defaultQuery.data[0].containersRunning
          }
        })
        .catch(() => {
          this.$message({
            message: 'Docker服务器信息查询异常',
            type: 'error'
          })
        })
    },

    // 查看容器信息
    toContainersQuery() {
      // 格式化 参数
      var defaultQuery = {
        server_host: this.currentServerHost,
        name: '',
        status: 'exited,running,paused,restarting'
      }

      // 配置路径
      var method = ':7089/SysListContainers'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost' + method
      } else {
        path = 'http://' + this.currentServerHost + method
      }

      console.log('toContainersQuery接口地址：' + path)

      this.listLoadingTable = true

      dockerMonitor(path, defaultQuery)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          console.log('toContainersQuery接口返回值')
          console.log(defaultQuery)

          if (defaultQuery.code === 0) {
            this.tableDataTable = defaultQuery.data
          }
        })
        .catch(() => {
          this.$message({
            message: 'Docker服务器信息查询异常',
            type: 'error'
          })
        })
      this.listLoadingTable = false
    },

    // 容器执行(停止等操作)
    toButExec(data, execType) {
      var containerId = data.containerId
      var state = data.state

      console.log('ID：' + containerId + '状态：' + state + '执行类型：' + execType)

      // 格式化 参数
      var defaultQuery = {
        server_host: this.currentServerHost,
        containerId: containerId,
        execType: execType
      }

      // 配置路径
      var method = ':7089/SysDockerContainerExec'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost' + method
      } else {
        path = 'http://' + this.currentServerHost + method
      }

      console.log('toButExec接口地址：' + path)

      dockerMonitor(path, defaultQuery)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          console.log('toButExec接口返回值')
          console.log(defaultQuery)

          if (defaultQuery.code === 0) {
            this.$message({
              type: 'success',
              message: '执行成功!'
            })
            // 刷新
            this.toRefresh()
          }
        })
        .catch(() => {
          this.$message({
            message: '容器执行异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.elRowStyle {
  margin-top: -5px;
}
::v-deep .CardTwo {
  background-color: #f0f0f0;
  box-shadow: none !important;
  border: 0 !important;
}
.CardOne {
  margin: 20px 15px;
  margin-bottom: 0;
}
.box-card1 {
  height: calc(100vh - 120px);
}
::v-deep .el-descriptions-item__container .el-descriptions-item__content {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}
::v-deep .el-descriptions-item__label:not(.is-bordered-label) {
  width: 60px;
}
::v-deep .el-descriptions-item__container {
  align-items: center;
}
::v-deep .el-icon-edit {
  cursor: pointer;
}
::v-deep .el-icon-data-board {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  line-height: 32px;
  font-weight: bold;
  text-align: center;
}
::v-deep .el-card__body {
  position: relative;
}
::v-deep .el-link {
  font-size: 12px;
  margin: 0 5px;
}
.wrapColItem {
  .nopadding {
    padding: 0;
    padding-right: 10px;
  }
  .nopadding:last-child {
    padding-right: 0;
  }
  .wrapCard {
    text-align: center;
    .wrapCardIcon {
      text-align: left;
    }
    .numberText {
      z-index: 5;
      position: relative;
      .wrapCardNumber {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .wrapCardText {
        font-size: 12px;
      }
    }

    .oneIconBg {
      color: #007bff;
      background: rgba(115, 103, 240, 0.15);
    }
    .twoIconBg {
      color: #28a745;
      background: rgba(40, 199, 111, 0.15);
    }
    .threeIconBg {
      color: #dc3545;
      background: rgba(234, 84, 85, 0.15);
    }
    .fourIconBg {
      color: #ffc107;
      background: rgba(255, 159, 67, 0.15);
    }
  }
}
.cardBgs {
  background: #f6f9ff;
  border: 0;
}
.dropdownStyle {
  margin-bottom: 20px;
  text-align: right;
}
::v-deep .el-dropdown {
  color: #79a0f1;
  font-size: 12px;
  margin-right: 15px;
}
::v-deep .el-dropdown-menu__item {
  font-size: 12px;
}
::v-deep .el-descriptions--small:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 15px;
}
::v-deep .el-icon-edit {
  color: #83a7f2;
  font-weight: bold;
}
.banner-hidden {
  position: absolute;
  bottom: -88px;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 1;
}
.banner-background-01 {
  height: 85px;
  background-image: url('~@/assets/images/background01.png');
  background-repeat: repeat-x;
  position: absolute;
  width: 400%;
  z-index: 999;
  animation: wave1 15s linear 0.4s infinite normal;
  -moz-animation: wave1 15s linear 0.4s infinite normal;
  -webkit-animation: wave1 15s linear 0.4s infinite normal;
  -o-animation: wave1 15s linear 0.4s infinite normal;
}

.banner-background-02 {
  height: 100px;
  background-image: url('~@/assets/images/background02.png');
  background-repeat: repeat-x;
  position: absolute;
  width: 400%;
  left: 0;
  z-index: 998;
  animation: wave2 30s linear 0.4s infinite normal;
  -moz-animation: wave2 30s linear 0.4s infinite normal;
  -webkit-animation: wave2 30s linear 0.4s infinite normal;
  -o-animation: wave2 30s linear 0.4s infinite normal;
}
@-webkit-keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-moz-keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-o-keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@keyframes wave1 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-webkit-keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-moz-keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@-o-keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}

@keyframes wave2 {
  from {
    left: -100px;
  }

  to {
    left: -500px;
  }
}
</style>
