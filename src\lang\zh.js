export const view = {
  title: {
    boardPractice: '板件履历',
    cimMessageReport: 'CIM消息报表',
    interfaceLog: '接口日志',
    threeRateReport: '三率报表',
    communicationDiagnosis: '通讯诊断',
    pointChangeTracking: '点位变化追踪',
    pointWriteTracking: '点位写入追踪',
    setting: '设置',
    dcsAlarm: '警报',
    parkSortRule: '分选规则设定',
    parkSortSplitRule: '分选截取比对规则设定',
    parkXNPSort: 'X数/位分选模式记录',
    parkExtra: '额外参数设置'
  },
  button: {
    export: '导出',
    search: '搜索',
    reset: '重置',
    view: '查看',
    close: '关闭',
    confirm: '确认',
    cancel: '取消',
    disable: '禁用',
    unbind: '解绑',
    setting: '设置',
    addPosition: '新增仓位'
  },
  enum: {
    placeholder: {
      pleaseChosen: '请选择',
      pleaseEnter: '请输入'
    },
    boardErrorCode: {
      normal: '正常',
      mixedBoard: '混板',
      readFailureBoard: '读码失败板',
      duplicateCode: '重码',
      forcedPass: '强制越过',
      forcedFinal: '强制结批',
      otherDefined: '其他定义'
    },
    manualProcessingBoard: {
      manualInputMode: '人工输入模式',
      reread: '重读',
      forcedPass: '强制越过'
    },
    communication: {
      normal: '通讯正常',
      abnormal: '通讯异常'
    },
    writeStatus: {
      normal: '写入正常',
      abnormal: '写入异常'
    }
  },
  field: {
    plan: {
      lotNum: '批号',
      lotNo: 'Lot No',
      plantOrderNum: '工厂订单号',
      tailCount: '尾包数量'
    },
    jobOrder: {
      batchNo: '批号',
      laserBatchNo: '镭射批号',
      originalLotNo: '原始批号',
      certifyLotNo: '验证批号',
      typesettingNumber: '排版数',
      customerMaterialNo: '客户料号',
      lot: '批次',
      model: '料号',
      ul: 'UL',
      bdBarcodes: 'PCS条码集',
      xoutPositions: 'XOUT位置集',
      bdLevels: 'PCS等级集',
      bdLevelJudgments: 'PCS等级判定集',
      shipAddress: '出货地'
    },
    setRecord: {
      created_time: '创建时间',
      lot_num: '批号',
      array_index: '订单SET顺序',
      board_sn: '线扫流水号',
      pile_barcode: '包装条码',
      array_barcode: 'SET条码',
      array_status: 'SET状态',
      array_ng_code: 'SET分选NG代码',
      array_ng_msg: 'SET分选NG描述',
      array_level: '线扫SET等级',
      array_mark: '线扫光学点检测结果',
      array_bd_count: '线扫SET下PCS数量',
      board_result: '板件判断结果',
      board_turn: '旋转方向',
      deposit_position: '堆叠位置',
      xout_flag: '是否为XOUT分选',
      xout_set_num: 'XOUT设定数量',
      xout_act_num: 'XOUT实际数量',
      array_front_info: 'SET正面线扫数据',
      array_back_info: 'SET反面线扫数据',
      task_type: '任务类型',
      model_type: '料号(型号)',
      model_version: '料号(型号)版本',
      array_type: 'SET类型',
      bd_type: 'PCS类型',
      m_length: '长,单位毫米',
      m_width: '宽,单位毫米',
      m_tickness: '厚,单位毫米',
      m_weight: '重,单位克',
      batch_no: '批次',
      cycle_period: '周期',
      laser_batch_no: '镭射批号',
      typesetting_no: '排版数',
      bd_barcodes: 'PCS条码集',
      xout_positions: 'XOUT位置集',
      bd_levels: 'PCS等级集',
      bd_level_judgments: 'PCS等级判定集',
      customer_mn: '客户料号',
      ul_code: 'UL号',
      pile_use_flag: '是否被打包使用',
      enable_flag: '有效标识',
      unbind_flag: '是否解绑',
      unbind_time: '解绑时间',
      unbind_way: '解绑方式说明'
    },
    pdSetRecord: {
      created_time: '创建时间',
      lot_num: '订单号',
      array_index: '订单SET顺序',
      board_sn: '线扫流水号',
      pile_barcode: '包装条码',
      array_barcode: '厂内码',
      array_status: 'SET状态',
      array_ng_code: 'SET分选NG代码',
      array_ng_msg: 'SET分选NG描述',
      array_level: '线扫SET等级',
      array_mark: '线扫光学点检测结果',
      array_bd_count: '线扫SET下PCS数量',
      board_result: '板件判断结果',
      board_turn: '旋转方向',
      deposit_position: '堆叠位置',
      xout_flag: '是否为XOUT分选',
      xout_set_num: 'XOUT设定数量',
      xout_act_num: 'XOUT实际数量',
      array_front_info: 'SET正面线扫数据',
      array_back_info: 'SET反面线扫数据',
      task_type: '任务类型',
      model_type: '料号(型号)',
      model_version: '料号(型号)版本',
      array_type: 'SET类型',
      bd_type: 'PCS类型',
      m_length: '长,单位毫米',
      m_width: '宽,单位毫米',
      m_tickness: '厚,单位毫米',
      m_weight: '重,单位克',
      batch_no: '批次',
      cycle_period: '周期',
      laser_batch_no: '镭射批号',
      typesetting_no: '排版数',
      bd_barcodes: 'PCS条码集',
      xout_positions: 'XOUT位置集',
      bd_levels: 'PCS等级集',
      bd_level_judgments: 'PCS等级判定集',
      customer_mn: '客户料号',
      ul_code: 'UL号',
      pile_use_flag: '是否被打包使用',
      enable_flag: '有效标识',
      unbind_flag: '是否解绑',
      unbind_time: '解绑时间',
      unbind_way: '解绑方式说明'
    },
    pileRecord: {
      intef_return_code: '接口返回码',
      intef_return_msg: '接口返回信息',
      apply_return_code: '申请返回码',
      apply_return_msg: '申请返回信息',
      print_return_code: '打印返回码',
      print_return_msg: '打印返回信息',
      inspect_result_code: '检验结果码',
      inspect_result_msg: '检验结果信息'
    },
    mappingResultRecord: {
      enable_flag: '有效标识',
      item_date_val: '下载时间',
      upload_date_val: '上传时间',
      product_no: '料号',
      product_rev: '料号版本',
      lot_no: '批号',
      barcode: '厂内码',
      bad_mark: 'mapping结果',
      data_from: '数据来源',
      process_no: '制程代号',
      twodlot_no: '2Dmapping批号',
      parentlot_no: '原始母批',
      reworknum: '重工数',
      angle: '旋转角度',
      flip_type: 'X,Y（翻转）',
      stripeachpiecex: 'Each Piece（Strip）X',
      stripeachpiecey: 'Each Piece（Strip）Y',
      err_type: '不良数据结果'
    },
    splitRules: {
      order_lot: '订单号截取批次规则',
      custom_model: '客户料号截取料号规则',
      array_set: 'SET码截取SET板件号规则',
      array_inner: '厂内码截取板件号规则',
      array_lot: 'SET码截取镭射批次规则',
      array_innerLot: '厂内码截取镭射批次规则',
      array_model: 'SET截取料号规则',
      array_cycle: 'SET码截取SET周期规则',
      array_innerCycle: '厂内码截取周期规则',
      array_dc: 'SET板件字符截取周期规则',
      array_ln: 'SET板件字符截取镭射批次规则',
      array_innerDc: '厂内码板件字符截取周期规则',
      array_innerIn: '厂内码板件字符截取镭射批次规则',
      bd_set: 'PCS码截取SET板件号规则',
      bd_lot: 'PCS码截取镭射批次规则',
      bd_model: 'PCS截取料号规则',
      bd_cycle: 'PCS码截取SET周期规则',
      bd_inner: 'PCS码截取厂内码板件号规则',
      bd_innerCycle: 'PCS码截取厂内码周期规则',
      bd_dc: 'PCS板件字符截取周期规则',
      bd_ln: 'PCS板件字符截取镭射批次规则',
      bd_index: 'PCS码截取PCS序号规则'
    },
    recipe: {
      weight_error: '重量误差(g)',
      inkjet_tpl: '喷码模板'
    },
    stationFlow: {
      station_code: '站点编码',
      station_desc: '站点描述',
      serial_num: '标签ID',
      recipe_paras: '配方参数',
      arrive_date: '到达时间-datetime',
      leave_date: '离开时间-datetime',
      cost_time: '耗时(ms)'
    },
    stationQuality: {
      station_flow_id: '流程ID',
      station_code: '站点编码',
      station_desc: '站点描述',
      serial_num: '标签ID',
      quality_for: '质量来源',
      tag_code: '标签编码',
      tag_value: '标签值',
      quality_d_sign: '质量标志',
      trace_d_time: '追溯时间-datetime'
    },
    ccdValidationResult: {
      station_code: '站点编码',
      station_desc: '站点描述',
      serial_num: '标签ID',
      validate_result: '校验结果',
      validate_msg: '校验信息',
      validate_time: '校验时间-datetime'
    },
    workpiece: {
      prod_line_code: '生产线',
      serial_num: '总成号',
      quality_sign: '合格标志',
      make_order: '订单号',
      records: '过站记录-list',
      arrive_date: '到达时间-datetime',
      leave_date: '离开时间-datetime',
      cost_time: '耗时(ms)'
    },
    pile: {
      item_date_val: '时间',
      lot_num: '订单号',
      finish_lable_count: '打包顺序',
      pile_barcode: '打包条码',
      custom_barcode: 'EAP打包条码',
      pile_index: '订单中打包序号',
      array_count: '打包中SET总数',
      pile_status: '打包状态',
      pile_ng_code: '打包验证NG代码',
      pile_ng_msg: '打包验证NG描述',
      pc_array_list: 'PC计划SET集合',
      plc_array_list: 'PLC反馈SET集合',
      trunk_flag: '是否尾箱',
      pile_weight: '打包重量',
      pile_user: '打包人员',
      enable_flag: '是否有效',
      unbind_flag: '是否解绑',
      unbind_user: '解绑人员',
      unbind_time: '解绑时间'
    }
  },
  form: {
    batchNo: '母批号',
    cardWorkOrder: '拆卡工单',
    subBatchNo: '子批号',
    subSimplifiedCode: '子批简码',
    materialNo: '物料号',
    carrierBarcode: '载具条码',
    taskSource: '任务来源',
    station: '工位',
    portNo: '端口号',
    boardBarcode: '板件条码',
    boardStatus: '板件状态',
    boardErrorCode: '板件错误代码',
    isFirstCheck: '是否首检',
    isDummy: '是否Dummy',
    manualProcessingBoard: '手工处理板',
    hasPanelMode: '有无Panel模式',
    operator: '操作工',
    rooftopBarcode: '天盖条码',
    orientation: '朝向',
    orientationToFront: '正面',
    orientationToBack: '背面',
    isOffline: '是否离线',
    time: '时间',
    timePeriod: '时间范围',
    timePeriodStart: '开始时间',
    timePeriodEnd: '结束时间',
    messageSource: '消息来源',
    displayMode: '显示方式',
    completionFlag: '完成标识',
    interfaceName: '接口名称',
    interfaceDescription: '接口描述',
    sourceSystem: '来源系统',
    targetSystem: '目标系统',
    requestParameters: '请求参数',
    responseParameters: '响应参数',
    isSuccess: '是否成功',
    interfaceMethod: '接口方式',
    instance: '实例',
    communicationStatus: '通讯状态',
    tagGroup: '标签组',
    tag: '标签',
    processWarning: '工艺预警',
    shiftCode: '班次代码',
    uploadFlag: '上传标识',
    writeStatus: '写入状态',
    isReversed: '是否反转',
    reversedFlag: '反转标识'
  },
  table: {
    name: '名称',
    time: '时间',
    subTaskNo: '子任务编号',
    portNo: '端口号',
    carrierBarcode: '载具条码',
    rooftopBarcode: '天盖条码',
    boardBarcode: '板子条码',
    boardStatus: '板子状态',
    ngBoardErrorCode: 'NG板子异常代码',
    ngBoardErrorDescription: 'NG板子异常描述',
    isFirstCheckBoard: '是否为首检板子',
    taskSource: '任务来源',
    motherBatch: '母批',
    subTaskSimplifiedCode: '子任务简码',
    subTaskSorting: '子任务排序',
    materialNo: '物料编号',
    carrierType: '载具类型',
    layer: '层别',
    subDiskSorting: '分盘排序',
    boardLength: '板长',
    boardWidth: '板宽',
    boardThickness: '板厚',
    boardSorting: '板件排序',
    isDummyBoard: '是否为Dummy板',
    manualProcessingBoard: '人工处理板',
    isPanelMode: '是否Panel模式',
    operator: '操作员',
    isEAP: '是否为EAP',
    orientation: '朝向',
    messageSource: '消息来源',
    displayMode: '显示方式',
    completionFlag: '完成标识',
    message: '消息',
    code: '代码',
    interfaceLog: '接口日志',
    serialNumber: '序号',
    esbInterfaceName: 'ESB接口名称',
    esbInterfaceDescription: 'ESB接口描述',
    interfaceMethod: '接口方式',
    esbInterfaceAddress: 'ESB接口地址',
    sourceSystem: '来源系统',
    targetSystem: '目标系统',
    startTime: '开始时间',
    endTime: '结束时间',
    requestDuration: '请求时长',
    isSuccess: '是否成功',
    interfaceMessage: '接口消息',
    requestParameters: '请求参数',
    responseParameters: '响应参数',
    customizedParameters: '定制化参数',
    requestMessage: '请求消息',
    remark: '备注',
    shift: '班次',
    shiftCode: '班次代码',
    shiftStart: '班次开始',
    shiftEnd: '班次结束',
    uploadFlag: '上传标识',
    uploadTimes: '上传次数',
    uploadMessage: '上传消息',
    dropBoardQuantity: '掉板数量',
    totalQuantity: '总数量',
    dropBoardRate: '掉板率',
    boardOkQuantity: '板件OK数量',
    boardNgQuantity: '板件NG数量',
    boardFailureRate: '板件失败率',
    carrierOkQuantity: '载具OK数量',
    carrierNgQuantity: '载具NG数量',
    carrierFailureRate: '载具失败率',
    workingTime: '工作时间',
    shiftTime: '班次时间',
    oee: 'OEE',
    tableForm: '表格形式',
    curveForm: '曲线形式',
    exceptionStatistics: '异常统计',
    communicationInfo: '通讯信息',
    simulationFlag: '模拟标识',
    sampleQuantity: '样本数量',
    instanceCode: '实例编码',
    instanceDescription: '实例描述',
    communicationStatus: '通讯状态',
    messageDescription: '消息描述',
    communicationStatusScatterPlotAnalysis: '通讯状态散点图分析',
    statusValue: '状态值',
    communicationExceptionFrequencyStatistics: '通讯异常频次统计',
    networkOutageFrequency: '断网频次',
    tagID: '标签ID',
    tagDescription: '标签描述',
    collectionValue: '采集值',
    isWarningSet: '是否设置预警',
    processWarning: '工艺预警',
    lowerLimitValue: '下限值',
    upperLimitValue: '上限值',
    oldValue: '旧值',
    tagUniqueKey: '标签唯一键',
    dataAlarm: '数据报警',
    dataNormal: '数据正常',
    tagAttribute: '标签属性',
    dataType: '数据类型',
    area: '区域',
    areaAddress: '区域地址',
    startAddress: '起始地址',
    dataLength: '数据长度',
    bit: '位',
    OPCAddress: 'OPC地址',
    tagDataScatterPlotAnalysis: '标签数据散点图分析',
    processWarningSign: '工艺预警标识',
    normal: '正常',
    alarm: '报警',
    writeActivityScatterPlotAnalysis: '写入活动散点图分析',
    writeFrequencyStatistics: '写入频次统计',
    statisticsFromWriterRatio: '统计来自写入者比例',
    writeNormal: '写入正常',
    writeException: '写入异常',
    writeValue: '写入值',
    writer: '写入者',
    writeStatus: '写入状态',
    writeTagQuantity: '写入标签数量',
    consumeTime: '消耗时间(ms)',
    frequencyStatistics: '频次统计',
    pieChartAnalysis: '饼图分析',
    writeDetail: '写入明细'
  },
  pagination: {
    total: '总数量',
    current: '当前第',
    previous: '上一页',
    next: '下一页',
    unit: '页'
  },
  dialog: {
    top: '已置顶',
    bottom: '已置底',
    queryException: '查询异常',
    selectStation: '请选择工位',
    exportFailed: '导出失败',
    selectInstance: '请选择实例',
    pleaseSelectTagGroup: '请选择标签组',
    pleaseSelectTag: '请选择标签',
    noData: '当前无数据',
    scadaCommunicationReportQueryFailed: 'Scada通讯报表查询失败',
    scadaCommunicationReportQueryTimeoutOrSQLError: 'Scada通讯报表查询超时或者SQL错误',
    querySelCellIPException: '查询selCellIP异常',
    noUnitIPAndPortNumberObtained: '未获取到单元IP与端口号',
    scadaInstanceBaseDataQueryFailed: 'Scada实例基础数据查询失败',
    scadaInstanceBaseDataQueryTimeoutOrSQLError: 'Scada实例基础数据查询超时或者SQL错误',
    scadaInstanceTagGroupDataQueryFailed: 'Scada实例标签组数据查询失败',
    scadaInstanceTagGroupDataQueryTimeoutOrSQLError: 'Scada实例标签组数据查询超时或者SQL错误',
    scadaInstanceTagDataQueryFailed: 'Scada实例标签数据查询失败',
    scadaInstanceTagDataQueryTimeoutOrSQLError: 'Scada实例标签数据查询超时或者SQL错误',
    scadaReadDataChangeReportQueryFailed: 'Scada读取数据变化报表查询失败',
    scadaReadDataChangeReportQueryTimeoutOrSQLError: 'Scada读取数据变化报表查询超时或者SQL错误',
    scadaWriteReportQueryFailed: 'Scada写入报表查询失败',
    scadaWriteReportQueryTimeoutOrSQLError: 'Scada写入报表查询超时或者SQL错误',
    reconfirmedToClose: '请再次确认是否关闭',
    hint: '提示',
    operationSucceed: '操作成功',
    operationFailed: '操作失败',
    operationCanceled: '操作取消',
    confirmToUnbindThisPCSRecord: '确定解绑此条PCS记录',
    confirmToDisableThisSETRecord: '确定禁用此条SET记录',
    confirmedToSend: '确认发送',
    formatError: '格式错误'
  }
}

// 志圣- 珠海超毅
export const zhcy = {
  // 操作模式
  offlineMode: '离线模式',
  onlineRemote: '在线/远程',
  onlineLocal: '在线/本地',

  // 生产状态
  productionAllowed: '允许生产',
  productionNotAllowed: '不允许生产',
  recipeUpdateAllowed: '允许下发配方',
  recipeUpdateNotAllowed: '禁止下发配方',

  // 指示灯
  triColorLight: '报警灯',
  fourColorLight: '报警灯',

  // 状态指示
  plcHeartbeat: 'PLC',
  eapStatus: 'EAP',
  ccdStatus: 'CCD',
  greenLight: '绿灯',
  yellowLight: '黄灯',
  redLight: '红灯',
  blueLight: '蓝灯',

  // 心跳状态
  plcHeartbeat: 'PLC心跳',
  eapStatus: 'EAP状态',
  online: '在线',
  offline: '离线',

  // 登录/登出
  login: '登录',
  logout: '登出',
  employeeLogin: '员工登录',
  employeeId: '员工号',
  name: '姓名',
  userName: '姓名',
  permission: '权限',
  pleaseEnterEmployeeId: '请输入员工号',
  cancel: '取消',
  confirm: '确定',
  employee: '员工',
  engineer: '工程师',
  admin: '管理员',

  // 模式切换
  switchingTo: '正在切换到',

  // 其他按钮
  syncAccountInfo: '同步账户信息',
  scanAndLoad: '上机',
  start: '启动',
  end: '结束',
  loading: '正在上机中...',
  loadSuccess: '上机成功',
  loadFail: '上机失败',
  loadFailCheckContent: '上机失败，请检查扫码内容',
  pleaseLoginFirst: '请先登录后再进行上机操作',
  offlineModeCannotLoad: '当前为离线模式，无法进行上机操作',

  // 其他文本
  pleaseSelectStation: '未选择工位或工位信息不完整',
  materialNumber: '物料号',
  dryFilmCode: '干膜物料编码',
  pleaseSelectStatus: '请至少选择一种状态进行查询',
  queryFailed: '获取任务列表失败',
  queryException: '获取任务列表异常',

  // 消息提示
  mqttConnSuccess: 'MQTT连接成功',
  mqttConnFailed: 'MQTT连接失败',
  mqttReconnecting: 'MQTT连接断开，正在重连...',
  plcCommError: 'PLC通讯中断',
  eapCommError: 'EAP通讯中断',
  ccdCommError: 'CCD通讯中断',
  syncingAccountInfo: '正在请求同步账户信息...',
  syncAccountSuccess: '成功同步{0}条权限账户信息',
  syncAccountFailed: '请求权限账户信息失败',
  syncAccountError: '请求权限账户信息异常',
  enterEmployeeId: '请输入员工号',
  loggingIn: '正在登录中...',
  loginSuccess: '登录成功',
  loginFailed: '登录失败',
  loginError: '登录异常',
  noUserLoggedIn: '当前没有登录用户',
  confirmLogout: '确定登出当前员工吗？',
  logoutSuccess: '登出成功',
  logoutFailed: '登出失败',
  logoutError: '登出异常',
  enterOrScanMaterial: '请输入或扫码物料号',
  startingTask: '正在启动任务...',
  taskStartSuccess: '任务启动成功',
  taskStartFailed: '任务启动失败，请检查设备状态',
  endingTask: '正在结束任务...',
  taskEndSuccess: '结束请求发送成功，等待设备做完剩余材料',
  taskEndFailed: '任务结束失败，请检查设备状态',
  noRecipeData: '没有可下发的配方数据',
  recipeDeploySuccess: '配方下发成功',
  queryException: '查询异常',
  queryExceptionWithError: '查询异常：{0}',
  switchingToMode: '正在切换到{0}……',
  rescanRequired: '请重新扫码上机',
  confirm: '确定',
  cancel: '取消',
  warning: '提示',
  confirmCancelTask: '确定要取消任务 {0} 吗？',
  taskCancelSuccess: '任务取消成功',
  taskCancelFailed: '任务取消失败',
  taskCancelError: '取消任务异常',
  confirmDeleteTask: '确定要删除任务 {0} 吗？此操作不可恢复！',
  confirmDeleteButton: '确定删除',
  taskDeleteSuccess: '任务删除成功',
  taskDeleteFailed: '任务删除失败',
  taskDeleteError: '删除任务异常',
  confirmStartTask: '确定要启动任务 {0} 吗？',
  confirmEndTask: '确定要结束任务 {0} 吗？',
  requestingSyncAccountInfo: '正在请求同步账户信息...',
  successfullySynced: '成功同步',
  permissionAccountInfo: '条权限账户信息',
  requestPermissionAccountInfoFailed: '请求权限账户信息失败',
  requestPermissionAccountInfoException: '请求权限账户信息异常',
  noStationSelectedOrIncompleteInfo: '未选择工位或工位信息不完整',
  selectAtLeastOneStatusForQuery: '请至少选择一种状态进行查询',
  handleReloadMachine: '确认重新上机',

  // 任务状态
  planning: '计划中',
  inProduction: '生产中',
  completed: '已完成',
  cancelled: '已取消',

  // 其他提示
  pleaseLoginFirst: '请先登录',
  offlineModeCannotLoad: '离线模式下无法上机',
  loading: '加载中',
  loadSuccess: '上机成功',
  loadFail: '上机失败',
  loadFailCheckContent: '上机失败，请检查设备状态',
  rescanRequired: '请重新扫码上机',
  confirm: '确定',
  taskList: '任务列表',
  alarmInfo: '报警信息',
  taskName: '任务名称',
  status: '状态',
  productNumber: '产品料号',
  taskQuantity: '任务数量',
  createTime: '创建时间',
  startTime: '开始时间',
  endTime: '结束时间',
  operations: '操作',
  start: '开始',
  end: '结束',
  cancel: '取消',
  delete: '删除',
  query: '查询',
  planning: '计划中',
  inProduction: '生产中',
  completed: '已完成',
  cancelled: '已取消',
  station: '工位',
  instanceCode: '实例编号',
  instanceDesc: '实例描述',
  alarmCode: '报警代码',
  alarmLevel: '报警级别',
  alarmDesc: '报警描述',
  alarmTime: '报警时间',
  resetTime: '复位时间',
  isReset: '是否复位',
  isSimulated: '是否模拟',
  resetYes: '已复位',
  resetNo: '待复位',
  yes: '是',
  no: '否',
  capacity: '产能',
  dailyProduction: '日产量',
  readRate: '读码率',
  oee: 'OEE',
  projectName: '项目名称',
  currentValue: '当前值',
  unit: '单位',
  upperLimit: '上限',
  lowerLimit: '下限',
  statusText: '状态',
  modifyRecipe: '修改配方',
  isModifyParameters: '是否修改参数',
  parameterCode: '参数代码',
  parameterDesc: '参数描述',
  parameterValue: '参数值',
  validFlag: '有效标志',
  valid: '有效',
  invalid: '无效',
  parameterCodeLabel: '参数编码',
  parameterDescLabel: '参数描述',
  parameterValueLabel: '参数值',
  validFlagLabel: '有效标识',
  confirmAndIssue: '确认并下发',
  cimMessage: 'CIM消息',
  code: '代码',
  message: '消息',
  reloadMachineRequired: '需要重新上机',
  reloadMachineToContinue: '请重新上机以继续操作',
  offDuty: '下班',
  onDuty: '上班',
  enterMaterialCode: '请输入物料代码',
  enterMaterialDesc: '请输入物料描述',
  productionMode: '生产模式',
  firstPieceMode: '首件模式',
  dummyMode: '空跑模式',
  recipeMaintenance: '配方维护',
  pagination: {
    total: '总计',
    current: '当前第',
    unit: '页',
    previous: '上一页',
    next: '下一页'
  }
}

export const lang_pack = {
  locale: '中文',
  SystemName: 'AIS流程设计与作业系统',
  SystemNameOne: '人机作业',
  UserName: '系统账号',
  Password: '系统密码',
  Role: '系统角色',
  Code: '验证码',
  SignOn: '登 录',
  Prompt: '提示',
  codeError: '验证码不存在或已过期！',
  LoggingIn: '登 录 中...',

  // 日期时间选择器快捷选项
  dateTimePicker: {
    last10Minutes: '最近10分钟',
    last30Minutes: '最近30分钟',
    lastHour: '最近1小时',
    lastDay: '最近1天',
    lastWeek: '最近7天',
    last30Days: '最近30天',
    last90Days: '最近90天',
    lastYear: '最近1年'
  },
  /** ************************************************************/
  /* Core定义*/
  commonPage: { /* 公共 */
    add: '新增',
    edit: '编辑',
    remove: '删除',
    search: '搜索',
    reset: '重置',
    operate: '操作',
    cancel: '取消',
    close: '关闭',
    confirm: '确认',
    checkAll: '全选',
    back: '返回',
    upload: '上传',
    download: '下载',
    validIdentificationt: '有效标识',
    validIdentification: '有效标识：',
    detail: '详情',
    required: '必填项',
    submitSuccesful: '提交成功',
    addSuccesful: '新增成功',
    editSuccessful: '编辑成功',
    deleteSuccesful: '删除成功',
    operationSuccessful: '操作成功',
    operationfailure: '操作失败!',
    pingSuccessful: 'Ping测试成功',
    pingfailure: 'Ping测试失败',
    date: '日期',
    msg: '消息',
    scan: '扫描',
    check: '校验'
  },

  mqmonitor: {
    monitor_way: '监控方式'
  },

  maintenanceMenu: { /* 菜单维护 */
    menuEncodingdescription: '菜单编码/描述：',
    menuGroupCoding: '菜单组编码',
    menuGroupDescription: '菜单组描述',
    order: '顺序',
    menuType: '菜单类型',
    fun: '函数DLL',
    funClass: '函数类名',
    mqSubDes: '子事件描述',
    icon: '图标',
    submenuCoding: '子菜单编码',
    submenuCodingDescription: '子菜单描述',
    addSubmenu: '新增子菜单',
    procedure: '程序'
  },
  maintenancePeople: { /* 人员维护 */
    nameEmployeeID: '姓名/员工号：',
    emailPhone: '邮箱/手机号：',
    EmployeeID: '员工号',
    name: '姓名',
    username: '用户名',
    password: '密码',
    email: '邮箱',
    phone: '电话',
    phonenumber: '手机号',
    department: '部门',
    position: '职位',
    role: '角色',
    photo: '头像'
  },
  maintenanceErrorMsg: { /* 国际化配置 */
    errorType: '配置类型',
    errorModule: '所属模块',
    errorFunctionCode: '功能编码',
    errorChinese: '中文',
    errorEnglish: '英文',
    errorOther: '其他语言',
    errorKeyword: '关键字段'
  },
  maintenanceRole: { /* 角色维护 */
    roleNumberDescription: '角色编号/描述：',
    roleNumber: '角色编号',
    roleDescription: '角色描述',
    productionLine: '产线',
    process: '工序',
    locationCoding: '工位编码',
    locationDescription: '工位描述',
    roleEncoding: '角色编码',
    permission: '权限',
    menu: '菜单',
    station: '工位'
  },
  applicationMenu: { /* 程序菜单 */
    programCodingDescription: ' 程序编码/描述：',
    programCode: '程序编码',
    programDescription: '程序描述',
    programType: '程序类型',
    programPath: '程序路径',
    programDependence: '程序依赖',
    programAttribute: '程序属性'
  },
  fastCode: { /* 快速编码 */
    groupCodeDescription: '组编码/描述：',
    groupCode: '组编码',
    groupDescription: '组描述',
    isEnabled: '是否有效',
    childCode: '子编码',
    childDescription: '子描述',
    order: '顺序'
  },
  systemParameter: { /* 系统参数 */
    parametricDescription: '参数/描述：',
    cell: '单元：',
    parameterCode: '参数编码',
    parameterDescription: '参数描述',
    parameterValue: '参数值',
    parameterNumber: '参数编号',
    parameterdescription: '参数描述'
  },
  errorMessage: { /* 错误消息定义 */
    errorMessageIDDescription: '错误消息ID/描述：',
    errorMessageDescription: '错误消息描述',
    errorMessageID: '错误消息ID'
  },
  taskScheduling: { /* 任务调度 */
    taskName: '任务名称：',
    cellId: '单元ID',
    BeanName: 'Bean名称',
    cronExpression: 'Cron表达式',
    mannerExecution: '执行方法',
    failureSuspend: '失败暂停',
    parameter: '参数',
    successSuspendRemove: '成功暂停/删除',
    remark: '备注',
    taskStatus: '任务状态',
    status: '状态',
    pauseAfterFailure: '失败后暂停',
    pauseAfterSuccessRemove: '成功后暂停/删除'
  },
  maintenancePline: { /* 产线维护 */
    lineCodeDescription: '产线编码/描述：',
    lineType: '产线类型：',
    productionScheduleType: '排产类型：',
    lineCode: '产线编码',
    lineDescription: '产线描述',
    factory: '工厂',
    workCenter: '工作中心',
    activation: '稼动率',
    lockNumber: '锁定数量',
    sort: '排序'
  },
  maintenanceStation: { /* 工位维护 */
    stationCodeDescription: '工位编码/描述：',
    productionLine: '产线：',
    productionLinet: '产线',
    stationCode: '工位编码',
    stationDescription: '工位描述',
    shortNumber: '短序号',
    productionLineSegmentCode: '产线分段码',
    divisionNumber: '分工位号',
    stationRelatedAttributes: '工位相关属性',
    stationCombinationCode: '工位组合码',
    ipAddress: 'IP地址',
    reportWorkingProcedureNumber: '报工工序号',
    workDescription: '报工描述',
    downTime: '故障时间',
    serviceTime: '保养时间',
    runTime: '运行时间',
    taktImage: '理想节拍',
    scrollingMessage: '滚动信息',
    methodLis: '数据采集方式',
    productType: '产品类型',
    cellId: '单元ID',
    scadaExample: 'SCADA实例',
    exampleFlowChart: '流程图实例',
    automatedLogicalInstance: '自动化逻辑实例',
    interfaceConfigurationEnvironment: '接口环境配置',
    sort: '排序',
    OnlineStation: '上线工位',
    offStation: '下线工位',
    repairStation: '返修工位',
    stationDisplayedIndependently: '工位独立显示',
    CombinatorialCode: '组合编码',
    optionPeriod: '选择权限',
    orderSelectionAuthority: '订单选择权限',
    segmentedEncoding: '分段编码',
    onlineStationCode: '上线工位号',
    reportWorkingProcedureDescription: '报工工序描述',
    attribute1: '属性1',
    attribute2: '属性2',
    attribute3: '属性3',
    attribute4: '属性4',
    attribute5: '属性5'
  },
  maintenanceDrive: { /* 驱动维护 */
    driverNameArea: '驱动名称/区域：',
    driverName: '驱动名称',
    area: '区域'
  },
  maintenanceType: { /* 型号维护 */
    driverNameType: '驱动名称/型号：',
    driverName: '驱动名称',
    type: '型号'
  },
  tagsDefined: { /* 标签定义 */
    productionLineId: '产线ID',
    exampleCode: '实例编码',
    exampleDescription: '实例描述',
    exampleGroupCode: '实例组编码',
    exampleGroupDes: '实例组描述',
    driveProgram: '驱动程序',
    type: '型号',
    exampleAttribute: '实例属性',
    stationCode: '工位号',
    simpleDb: '大数据存储',
    simulation: '是否模拟'
  },
  monitor: { /* 实例监控 */
    emptyMonitoring: '清空监控',
    stopMonitoring: '停止监控',
    startupMonitoring: '启动监控',
    addTag: '添加监控',
    startupKafkaMonitoring: '启动KAFKA监控',
    exampleCode: '实例编码',
    exampleDescription: '实例描述',
    driveProgram: '驱动程序',
    simulation: '是否模拟',
    timeDuration: '持续时间(分)',
    status: '状态',
    heartbeat: '心跳',
    addWatch: '添加监控',
    labelCode: '标签代码',
    labelDescription: '标签描述',
    currentValue: '当前值',
    labelGroups: '标签组',
    time: '时间',
    feature: '功能',
    message: '消息',
    labelValue: '标签值',
    labelAttr: '标签属性',
    groupCode: '组编码',
    groupDescription: '组描述',
    regionName: '区域名称',
    areaNumber: '区域编号',
    regionPosition: '区域位置',
    dataType: '数据类型',
    initialAddress: '起始地址',
    dataLength: '数据长度',
    length: '长度',
    bit: '位',
    opcRealAddress: ' OPC真实地址',
    opcVirtualAddress: 'OPC模拟地址',
    readWriteAccessPermission: '读写权限',
    dataPermissions: '数据权限',
    changePush: '变化推送',
    snapshotStorage: '快照存储',
    otherAttributes: '其他属性',
    convertFormat: '转换格式',
    saveOnChange: '变化时保存',
    warningProcessing: '预警处理',
    warningUpperLimit: '预警上限',
    lowerWarningLimit: '预警下限',
    disDataTypes: '区分数据类型',
    openOPCUA: '开放OPCUA',
    push: '是否推送',
    dataConversion: '数据转换',
    messageDriven: '驱动消息',
    emptyMessage: '清空消息',
    refresh: '刷新',
    write: '写入',
    attr: '属性',
    selectProduction: '请选择产线',
    selectCell: '请选择单元',
    codeOrDes: '实例编码或描述',
    addMon: '添加监控标签',
    queryInstance: '请先查询实例，再启动监控',
    serviceCell: '请先维护服务、单元信息',
    selectTAG: '请选择一个TAG',
    refreshException: '刷新异常'
  },
  logicattr: { /* 逻辑属性 */
    menuType: '菜单类型',
    groupCode: '组编码',
    groupDescription: '组描述',
    childCode: '子编码',
    childDescription: '子描述',
    attribute1: '属性1',
    attribute2: '属性2',
    attribute3: '属性3',
    attribute4: '属性4',
    attribute5: '属性5',
    logicalPropertyMaintenance: '逻辑属性维护'
  },
  logicfunc: { /* 自动化逻辑属性 */
    automatedLogicalPropertyConfiguration: '自动化逻辑属性配置',
    logicalProgram: '逻辑程序',
    schedulerCode: '调度程序代码',
    schedulerDescription: '调度程序描述',
    setControllers: '控制器集合',
    subproject: '子项目',
    value: '值',
    attribute1: '属性1',
    attribute2: '属性2',
    attribute3: '属性3',
    attribute4: '属性4',
    attribute5: '属性5',
    isEnabled: '是否有效',
    attributeGroup: '属性组',
    groupCode: '组编码',
    groupDescription: '组描述',
    subprojectDescription: '子项目描述'
  },
  modmain: { /* 主流程图模板维护 */
    templateCode: '模板代码：',
    templateDescription: '模板描述：',
    templateCodet: '模板代码',
    templateDescriptiont: '模板描述',
    programDriven: '程序驱动'
  },
  modfunctionm: { /* 函数维护 */
    functionCode: '函数编码：',
    functionCodet: '函数编码',
    funcname: '函数名：',
    funcnamet: '函数名',
    functionDescription: '函数描述',
    csprojCode: '项目编码',
    csprojCodet: '项目编码',
    functionversion: '函数版本',
    functionversiondes: '版本说明'
  },
  modcsproj: { /* 工程维护 */
    csprojCode: '项目编码：',
    csprojCodet: '项目编码',
    csprojdes: '项目描述：',
    csprojdest: '项目描述',
    csprojtargetframework: '目标框架',
    csprojreference: '项目引用',
    csprojversion: '项目版本',
    csprojversiondes: '版本说明',
    csprojpathWin: '文件路径Win',
    csprojpathLinux: '文件路径Linux'
  },
  timermain: {
    timerMainCode: '主TIMER事件编码',
    timerMainDes: '主TIMER事件描述'
  },
  mqmain: {
    eventMqMainCode: 'MQ事件编码',
    station: '工位',
    eventModMq: 'MQ事件模版',
    eventModMqSub: 'MQ子事件模版',
    eventMqMainDes: 'MQ事件描述',
    clientIdList: '实例集合',
    monitorType: '监控类型'
  },
  mainmain: { /* 主流程图维护 */
    mainProcessCode: '主流程编码：',
    mainProcessDescription: '主流程描述：',
    basicAttribute: '基础属性',
    processTemplate: '流程模板',
    station: '工位',
    mainProcessCodet: '主流程编码',
    mainProcessDescriptiont: '主流程描述',
    taskNumberPrefix: '任务编号前缀',
    programProperties: '程序属性',
    processFlow: '流程图标',
    examplesCollections: '实例集合',
    triggerPoint: '触发点位',
    triggerPointValue: '触发点位值',
    taskTriggeringMode: '触发任务方式',
    pollingTime: '轮询时间',
    conditionsSet: '条件组',
    conditionGroupDescription: '条件组描述',
    conditionsDescribed: '条件描述',
    monitoringPoint: '监控点位',
    setUpInstructions: '成立说明',
    processCodet: '流程编码',
    processDescriptiont: '流程描述',
    taskInfo: '任务信息',
    view: '查看',
    dataException: '初始化数据异常',
    selectProduction: '请选择产线与工位',
    taskNumber: '任务编号',
    creationDate: '创建时间',
    detailInfo: '详细信息',
    pendEvents: '待处理事件',
    strDrawFlow: '拼命绘制流程图中',
    attrMomitor: '属性监控',
    abnormalData: '初始化模式数据异常',
    amplify: '放大',
    reduce: '缩小',
    sureDelete: '确定要删除当前选中',
    step: '项步骤吗?',
    cancelledDeletion: '已取消删除',

    log: '日志',
    attr: '基础属性',
    inputParams: '输入参数',
    outParams: '输出参数',
    subprocess: '所属子流程',
    stepDes: '步骤描述',
    sort: '排序',
    stepType: '步骤类型',
    startStep: '开始步骤',
    endStep: '结束步骤',
    generalSteps: '常规步骤',
    methodName: '方法名',
    nextOK: 'OK时下一个步骤',
    stepToNg: 'NG对应的步骤',
    afterLoop: '循环结束后下一步',
    selectNext1: '选择1下一步步骤',
    selectNext2: '选择2下一步步骤',
    selectNext3: '选择3下一步步骤',
    selectNext4: '选择4下一步步骤',
    selectNext5: '选择5下一步步骤',
    cancelNext: '取消下一步步骤',
    retryCount: '重试次数',
    limiTime: '限制时间(ms)',
    colorStateOK: 'OK状态时颜色',
    colorStateNG: 'NG状态时颜色',
    colorStateCancel: '取消状态时颜色',
    colorStateRetry: '重试状态时颜色',
    initColor: '初始颜色',
    code: '代码',
    conditionGroup: '条件组',
    notHave: '无',
    endAll: '结束全部',
    logQuery: '日志查询',
    logType: '日志类型',
    logCode: '日志代码',
    date: '日期',
    logInfo: '日志消息',

    stepJump: '步骤跳转',
    radio: '单选',
    index: '序号',
    stepName: '步骤名称',
    selectFlow: '请先选择流程',
    jumpSuccessful: '跳转成功',

    subDes: '子流程描述',
    processType: '流程类型',
    startSub: '开始子流程',
    endSub: '结束子流程',
    regularSub: '常规子流程',
    nextSub: '下一个子流程',
    subEnd: '子流程结束',
    afterEnd: '当前子流程结束后结束整个流程',
    subFunctionDll: '子流程FunctionDll',
    subAttr: '子流程程序属性',
    controlIcon: '控件图标',
    controlWidth: '控件宽度',
    controlHeight: '控件高度',
    colorStateWait: '等待状态时颜色',
    cannotBeEmpty: '不能为空',
    enterNumber: '请输入数字值',
    enterSub: '请输入子流程编码',
    enterDes: '请输入子流程描述',
    nextSelect: '请选择下一个子流程',
    enterFunction: '请输入子流程FunctionDll',
    addSub: '子流程新增成功',
    addError: '子流程新增异常',
    editSuccess: '子流程修改成功',
    editError: '子流程修改异常'

  },
  taskList: { /* 任务列表*/
    TaskSource: '任务来源:',
    TaskNumber: '任务号',
    NumberOfTasks: '任务数量',
    UrgentOrder: '急单',
    ScheduleDate: '计划日期',
    TargetCuttingMachine: '目标切割机',
    CutType: '切割类型',
    TaskCreationTime: '任务创建时间',
    CrownBlockAutomatic: '天车自动',
    CrownBlockStatus: '天车状态',
    CuttingStatus: '切割状态',
    WhetherToCutOrNot: '是否切割',
    SortOrNot: '是否分拣',
    ValidOrNot: '是否有效',
    SteelPlateModel: '钢板型号',
    DXFcode: 'DXF 编码',
    DXFFiles: 'DXF 文件:',
    NCcode: 'NC 编码',
    NCFiles: 'NC 文件',
    TaskStatus: '任务状态',
    validIdentification: '有效标识:'
  },
  cuttingZone: { // 切割区
    CacheBit: '缓存位',
    TaskNumber: '任务号',
    SteelPlateModel: '钢板型号',
    SteelPlateSource: '钢板来源',
    CacheStatus: '缓存状态',
    DelayTime: '到缓时间',
    ManualOperation: '手工操作',
    CuttingPosition: '切割位',
    CuttingMachineStatus: '切割机状态',
    cutNCName: '切割文件名称',
    PROGRAMNO: '程序号',
    schedule: '进度',
    DeviceName: '设备名称',
    AlarmClassification: '报警分类',
    AlarmLevel: '报警级别',
    AlarmMessage: '报警信息',
    AlarmOccurrenceTime: '报警发生时间',
    startTime: '开始时间',
    endTime: '结束时间',
    PlannedCuttingTime: '计划切割时间',
    ProgramUploadTime: '程序上传时间',
    CurrentWorkstation: '当前工位',
    TargetWorkstation: '目标工位',
    StockCode: '库位号',
    StockCount: '库存量',
    MaterialModel: '物料型号',
    ExecutionOrder: '执行顺序',
    AdjustTheOrder: '调整顺序',
    WarehousingTaskNumber: '入库任务号',
    WarehousingTime: '入库时间',
    LocationSorting: '库位排序',
    ZAxisCoordinates: 'Z轴坐标',
    IsItLocked: '是否锁定',
    LockTaskNumber: '锁定任务号'
  },
  sortingArea: { // 分拣区
    SortingStation: '分拣工位',
    SortType: '分拣类型',
    PlannedQuantity: '计划数量',
    ActualQuantity: '实际数量',
    SheetName: '图纸名称',
    MaterialFrameNumber: '料框号',
    MaterialFrameParts: '料框零件',
    Number: '数量',
    Status: '状态',
    MaterialFrameDetails: '料框明细',
    AGVStatus: 'AGV状态',
    StationInspectionStatus: '过站检查状态',
    WorkReportingStatus: '报工状态',
    AbnormalWorkReporting: '报工异常',
    TransitDetails: '过站明细',
    ProductionTaskNumber: '生产任务号',
    CurrentStationNumber: '当前工位号',
    SourceStaus: '来源任务号',
    Cutterbar: '切割机',
    arriveDate: '到达时间',
    leaveDate: '离开时间',
    StationConsumptionTime: '工位消耗时间',
    PalletNumber: '托盘号',
    ReportWorkOrNot: '是否报工',
    MaterialFrameTaskNumber: '料框任务号',
    PartLength: '零件长',
    PartWidth: '零件宽',
    PartType: '零件类型',
    PartThickness: '零件厚',
    PartWeight: '零件重',
    PlannedSortingTime: '计划分拣时间',
    SortingStationNumber: '分拣工位号',
    PartNumber: '零件编号',
    PartBarcodeNumber: '零件条码号',
    PartDrawingNumber: '零件图号',
    PlannedTimeSpent: '计划花费时间/s',
    CurrentSortingStation: '当前分拣工位',
    TargetTransmissionStation: '目标传输工位',
    CompulsoryRelease: '强制放行',
    MandatoryReportingInterface: '强制上报接口',
    IsAutomaticSortingCompleted: '是否自动分拣完成',
    CurrentSortingDXFDrawing: '当前分拣DXF图纸',
    CurrentSortingSteelPlateModel: '当前分拣钢板型号',
    ForceComplete: '强制完成',
    WorkReportingInterfaceAddress: '报工接口地址',
    DXFFileName: 'DXF文件名称',
    GNCFileName: 'GNC文件名称',
    PersonInChargeOfWorkReporting: '报工责任人',
    ClickToReportWork: '点击报工'
  },
  equipment: {
    stationCode: '工位号',
    deviceCode: '设备编号',
    DeviceName: '设备名称',
    PartNumber: '零件编号',
    PartName: '零件名称',
    TheoreticalLifespan: '理论寿命',
    ServiceLife: '使用寿命',
    Status: '状态',
    PartDescription: '零件描述',
    OriginalPartNumber: '原零件号',
    OriginalPartName: '原零件名称',
    PartNumberAfterReplacement: '更换后零件号',
    PartNameAfterReplacement: '更换后零件名称',
    ReplacementTime: '更换时间'
  },
  mfTable: { // 机型基础
    creator: '创建者',
    creationTime: '创建时间',
    ModifiedBy: '修改者',
    time: '时间',
    link: '链接',
    mainMaterialCode: '物料编码',
    mainMaterialDescription: '物料描述',
    partDrawingNumber: '零件图号',
    partMaterialNumber: '零件物料号',
    model_type: '型号',
    length: '长',
    width: '宽',
    thick: '厚',
    height: '高',
    weight: '重',
    materialQuality: '材质',
    cuttingMachine: '材质(切割机)',
    dataSources: '数据来源',
    cuttingXCoordinate: '切割x坐标',
    cuttingyCoordinate: '切割y坐标',
    enableFlag: '有效标识：'
  },
  smaterialbox: {// 分拣箱基础
    creator: '创建者',
    creationTime: '创建时间',
    modifiedBy: '修改者',
    time: '时间',
    materialFrameCoding: '料框编码',
    materialFrameName: '料框名称',
    materialFrameType: '料框类型',
    AGVLocationCode: 'AGV位置码'
  },
  qualityData: {// 质量数据采集
    creator: '创建者',
    creationTime: '创建时间',
    modifiedBy: '修改者',
    time: '时间',
    stationId: '工位ID',
    sourceOfQualityData: '质量数据来源',
    collectionItems: '采集项目',
    markOfConformity: '合格标志',
    groupNumber: '组号',
    groupDescription: '组描述',
    positionOfColumn: '列位置',
    columnSorting: '列排序',
    measurementObject: '测量对象',
    entryName: '项目名称',
    collectionProjectUnit: '采集项目单位',
    standardValue: '标准值',
    lowerLimitingValue: '下限值',
    upperLimitValue: '上限值',
    saveOrNot: '是否保存'
  },
  taskTable: { // 生产任务表
    taskNumber: '任务号',
    taskSource: '任务来源',
    serialNumber: '序列号',
    batchNumber: '批次号',
    productionMode: '制程分类',
    taskType: '任务类型',
    materialCode: '物料编码',
    materialDescription: '物料描述',
    partDrawingNumber: '零件图号',
    partMaterialNumber: '零件物料号',
    modelY: '型号',
    length: '长',
    width: '宽',
    thick: '厚',
    height: '高',
    weight: '重',
    materialQuality: '材质',
    cuttingMachine: '材质(切割机)',
    xCoordinate: 'x坐标',
    yCoordinate: 'y坐标',
    DXFFileName: 'DXF文件名称',
    DXFFileURLPath: 'DXF路径',
    JSONFileName: 'JSON文件名称',
    JSONFileURLPath: 'JSON路径',
    JSONFileURLTxt: 'JSON文本',
    JSONData: 'JSON数据',
    NCFileName: 'NC文件名称',
    NCFileURLPath: 'NC路径',
    scheduledTime: '切割计划时间',
    sort: '任务排序',
    taskStatus: '任务状态',
    taskMessage: '任务消息',
    cutterbar: '切割机',
    cutterbarType: '切割机类型',
    cuttingPlanTime: '切割计划时间',
    shotBlastingOrNot: '是否已抛丸',
    automaticCrownBlockOrNot: '天车状态',
    whetherToAutotheboard: '是否自动取板',
    whetherToSprayCode: '是否喷码',
    whetherToAutomaticallyCut: '是否切割',
    automaticSortingOrNot: '是否分拣',
    isTheCarCentered: '横移小车对中',
    onSiteSiteMaterials: '场内/外物料',
    stationPosition: '工位位置',
    reportWorkOrNot: '是否完成报工',
    area: '面次', // 面次
    NoBowPlate: '是否首板'// 是否首板
  },
  surplusMaterialTable: {// 生产任务余料表
    description: '描述',
    mainTaskID: '主任务ID',
    plusTaskNumber: '余料编号',
    plusRemnantMat: '余料材质',
    plusRemnantThick: '余料厚度',
    plusRemnantLength: '余料长度',
    plusRemnantWidth: '余料宽度',
    plusRemnantWeight: '余料重量'
  },
  eventRecordForm: { // 事件记录表
    mainTaskID: '主任务ID',
    user: '用户',
    eventType: '事件类型',
    eventName: '事件名称',
    sendContent: '发送内容',
    acceptContent: '接受内容',
    messageCODE: '消息CODE',
    messageContent: '消息内容',
    eventStatus: '事件状态',
    startTime: '开始时间',
    endTime: '结束时间',
    timeConsuming: '消耗时间(秒)',
    remake: '备注',
    condition: '条件',
    timeoutLimit: '超时限制(ms)',
    lastUpdateTime: '最后更新时间'
  },
  sortingResults: { // 分拣解析结果
    mainTaskID: '主任务ID',
    stationCode: '工位号',
    partBarcodeNumber: '零件条码',
    robotCoding: '机器人编码',
    partCode: '零件编码',
    partDrawingNumber: '零件图号',
    partMaterialNumber: '零件物料号',
    sortSequenceNumber: '分拣顺序号',
    nedCuttingTime: '切割时间',
    routingCode: '工艺路线编码',
    sprayCodeXCoordinate: '喷码顶点X坐标',
    sprayCodeYCoordinate: '喷码顶点Y坐标',
    PartType: '零件类型',
    sortResultCode: '分拣结果代码',
    exceptionInformation: '消息/异常信息',
    length: '长',
    width: '宽',
    NCFileName: 'NC 文件名称',
    thick: '厚',
    weight: '重',
    nextRouting: '零件加工工序',
    reservedPartProperties: '零件配送地点',
    timeRequiredForSorting: '分拣所需时间(毫秒)',
    startTime: '开始时间',
    endTime: '结束时间',
    sortStartTime: '分拣开始时间',
    sortEndTime: '分拣结束时间'
  },
  sortingBoxResults: { // 料框结果
    materialFramePosition: '料框位置',
    materialFrameBarcode: '料框条码',
    taskNumber: '任务号',
    partBarcodeNumber: '零件条码',
    partCode: '零件编码',
    partType: '零件类型',
    resultCode: '结果代码',
    stackingMessage: '码垛消息/异常',
    stackingTime: '码垛时间',
    stackingStartTime: '码垛开始时间',
    stackingEndTime: '码垛结束时间'
  },
  realTimeTable: { // 工位MIS实时状态表
    username: '用户名',
    arriveDate: '到达时间',
    leaveDate: '离开时间',
    stationCode: '工位号',
    stationDescription: '工位描述',
    stationProperties: '工位属性',
    stationStatus: '工位状态',
    taskNumber: '任务号',
    taskSource: '任务来源',
    serialNumber: '序列号',
    batchNumber: '批次号',
    palletNumber: '托盘号',
    palletUsageTimes: '使用次数',
    maximumPalletUsage: '最高使用次数',
    trayAlarmIdentification: '托盘报警标识',
    mainMaterialCode: '物料编码',
    mainMaterialDescription: '物料描述',
    partDrawingNumber: '零件图号',
    partMaterialNumber: '零件物料号',
    model_type: '型号',
    length: '长',
    width: '宽',
    thick: '厚',
    weight: '重',
    materialQuality: '材质',
    cuttingMachine: '材质(切割机)',
    uptime: '上线时间',
    workpieceInProcessTime: '工件在制时间(秒)',
    idealBeat: '理想节拍(秒)',
    actualBeat: '实际节拍(秒)',
    shifCode: '班次代码',
    shifDes: '班次描述',
    sourceStationNumber: '来源工位号',
    stationErrorMessage: '工位错误信息',
    DXFName: 'DXF名称',
    JSONName: 'JSON名称',
    NCName: 'NC名称'
  },
  wmsCbTable: { // WMS天车基础表
    libraryArea: '库区域',
    crownBlockCode: '天车编码',
    processName: '流程名称',
    subProcess: '子流程',
    crownBlockDescription: '天车描述',
    crownBlockNumber: '天车序号',
    crownBlockAttributes: '天车属性',
    crownBlockType: '天车类型',
    groupCollection: '管辖库位组集合',
    originXCoordinate: '原点X坐标',
    originYCoordinate: '原点Y坐标',
    originZCoordinate: '原点Z坐标',
    farPointXCoordinate: '远点X坐标',
    farPointYCoordinate: '远点Y坐标',
    farPointZCoordinate: '远点Z坐标',
    safeDistance: '安全距离',
    warehouseLocationGroupCode: '库位组编码',
    locationGroupDescription: '库位组描述',
    warehouseLocationNumber: '库位号',
    warehouseLocationDescription: '库位描述',
    inventoryLocationSorting: '库位排序',
    manageInventoryOrNot: '是否管理库存',
    isTheModelFixed: '是否固定型号',
    modelID: '型号ID',
    xCoordinate: 'X坐标',
    YCoordinate: 'Y坐标',
    ZCoordinate: 'Z坐标',
    isTheZAxisDefinedValue: 'Z轴是否为定义值',
    zAxisDynamicCalculationMethod: 'Z轴动态计算方法',
    inventory: '库存量',
    minimumInventory: '最小库存',
    maximumInventory: '最大库存',
    loopCode: '循环代码',
    maximumLimitExpirationTime: '最大限定超期时间(分钟)',
    warehouseLocationStatus: '库位状态',
    locationType: '库位类型',
    taskNumber: '任务号',
    taskSource: '任务来源',
    serialNumber: '序列号',
    batchNumber: '批次号',
    ZAxisCoordinates: 'Z轴坐标',
    isInventoryLocked: '库存是否锁定',
    warehousingMethod: '入库方式',
    taskType: '任务类型',
    findTheYCoordinate: '寻中Y坐标',
    isTheSearchCompleted: '是否完成寻中',
    warehousingTime: '存放天数',
    isInventoryAlarm: '是否库存超期报警',
    mainTaskID: '任务ID',
    model_type: '型号',
    locationID: '库位ID',
    taskExecutionStatus: '任务执行状态',
    taskExecutionErrorMessage: '任务执行错误信息',
    startingStorageLocation: '起始库位',
    targetInventoryLocation: '目标库位',
    taskStartTime: '任务开始时间',
    taskEndTime: '任务结束时间',
    timeConsuming: '消耗时间(秒)',
    isTheModelChecked: '是否检查型号',
    checkTheRollerTableTime: '检查辊道时间',
    checkModelTime: '检查型号时间',
    checkModelResults: '检查型号结果',
    crownBlockTaskType: '天车任务类型',
    stepCode: '步骤代码',
    stepName: '步骤名称',
    stepStatus: '步骤状态',
    restrictionStepID: '限制步骤ID',
    restrictionStepName: '限制步骤名称',
    restrictionStepStatus: '限制步骤状态',
    isTheRollerTableChecked: '是否检查辊道'
  },
  meStationQuality: {
    stationCode: '工位号',
    taskNumber: '任务号',
    serialNumber: '序列号',
    groupNumber: '组号',
    groupDescription: '组描述',
    positionOfColumn: '列位置',
    collectionItems: '采集项目',
    columnSorting: '列排序',
    measurementObject: '测量对象',
    entryName: '采集项目名称',
    collectionProjectUnit: '采集项目单位',
    standardValue: '标准值',
    lowerLimitingValue: '下限值',
    upperLimitValue: '上限值',
    collectValues: '子合格标志',
    retrospectiveTime: '追溯时间'

  },
  center: { /* 中心维护 */
    centreNumberDescription: '中心编号/描述：',
    centreCode: '中心编码',
    centreDescription: '中心描述',
    mainCard: '主网卡',
    attachCard1: '附网卡1',
    attachCard2: '附网卡2',
    attachCard3: '附网卡3'
  },
  server: { /* 服务维护 */
    centre: '中心',
    serviceTagDescription: '服务编号/ 描述：',
    serviceCode: '服务编码',
    serviceDescription: '服务描述',
    mainCard: '主网卡',
    attachCard1: '附网卡1',
    attachCard2: '附网卡2',
    attachCard3: '附网卡3',
    esbServer: 'ESB服务器',
    system: '系统'
  },
  cell: { /* 单元维护 */
    cellNameDescription: '单元名称/描述：',
    serve: '服务',
    cellName: '单元名称',
    cellDescription: '单元描述',
    mirrorName: '镜像名称',
    webPort: 'Web端口',
    mqttPort: 'Mqtt端口',
    opcUaPort: 'OPC UA端口',
    cellPort: '单元端口',
    startOpcUa: '启动OPC UA'
  },
  interf: { /* 接口维护 */
    interfaceCodeDescription: '接口编码/ 描述：',
    interfaceCode: '接口代码',
    interfaceDescription: '接口描述',
    sourceSystem: '来源系统',
    targetSystem: '目标系统',
    interfaceMeans: '接口方式',
    testAddress: '测试地址',
    officialAddress: '正式地址',
    testReturnValue: '测试返回值',
    interfaceNumber: '接口编号',
    productionAddress: '生产地址',
    remark: '备注',
    cell: '单元',
    parameterValue: '传参值'
  },
  file: { /* 文件管理 */
    autoDesk: '文件夹目录',
    fileName: '文件名称',
    fileSize: '文件大小'
  },
  celldeploy: { /* 单元部署 */
    serve: '服务：',
    uploadMirroring: '上传镜像',
    installAll: '全部安装',
    installSelected: '选择安装',
    unitName: '单元名称',
    unitDescription: '单元描述',
    cellMirrorName: '单元镜像名称',
    installMirrorName: '安装时镜像名称',
    serviceMirroringInstall: '服务镜像安装'
  },
  cellprogupd: { /* 单元程序更新 */
    serve: '服务：',
    massUpdate: '批量更新',
    selectiveupdate: '选择更新',
    updateObject: '更新对象',
    updateMode: '更新方式',
    file: '文件',
    serialNumber: '序号',
    cell: '单元',
    procedure: '程序',
    fileName: '文件名称',
    fileType: '文件类型',
    fileSize: '文件大小'
  },
  progbatchupd: { /* 单元程序更新批量 */
    serve: '服务：',
    massUpdate: '批量更新',
    selectiveupdate: '选择更新',
    updateObject: '更新对象',
    updateMode: '更新方式',
    file: '文件',
    serialNumber: '序号',
    cell: '单元',
    procedure: '程序',
    fileName: '文件名称',
    fileType: '文件类型',
    fileSize: '文件大小'
  },
  /** ************************************************************/
  /* MES定义*/

  /** ************************************************************/
  /* PMC定义*/
  pmcquery: { /* 查询条件 */
    creationDate: '时间范围：',
    workCenterCode: '车间：',
    prodLineCode: '产线：',
    stationCode: '工位：',
    makeOrder: '订单：',
    vin: 'VIN：',
    vinFlag: 'VIN最终标识：',
    workStatus: '生产状态：',
    setSign: '拉入/出状态：',
    zkName: '下发订单类型：',
    lastMakeOrder: '最后订单号：',
    stationDeviceType: '设备类型：',
    deviceSectionCode: '工位与拧紧枪匹配：',
    qualityFrom: '数据来源：',
    screenSetType: '大屏类型：',
    andonType: '安灯类型：',
    andonCardStatus: '工位牌状态：',
    andonControlName: '工位控制名称：',
    happenDate: '发生时间：',
    resetFlag: '是否复位：',
    andonVoiceSerial: '设备序列号：',
    andonVoiceDes: '音响名称：',
    andonMusicDes: '音乐名称：',
    enableFlag: '有效标识：'
  },
  flowonline: { /* 线首 */
    creationDate: '创建时间',
    flowOnlineId: 'ID',
    workCenterCode: '车间',
    prodLineCode: '产线',
    stationCode: '工位',
    makeOrder: '订单',
    serialNum: '工件编号',
    dms: 'DMS',
    itemProject: '行项目',
    vin: 'VIN',
    smallModelType: '型号',
    mainMaterialCode: '物料编号',
    materialColor: '颜色',
    materialSize: '尺寸',
    shaftProcNum: '拧紧程序号',
    staffId: '操作者',
    palletNum: '托盘号',
    engineNum: '发动机',
    driverWay: '驱动形式',
    publishNumber: '发布顺序号',
    vinFlag: 'VIN最终标识',
    repairFlag: '返修标识',
    enableFlag: '有效标识'
  },
  stationmo: { /* 工位生产订单 */
    creationDate: '创建时间',
    stationMoId: 'ID',
    prodLineCode: '产线',
    stationCode: '工位',
    makeOrder: '订单',
    targetMakeOrder: '目标订单',
    dms: 'DMS',
    itemProject: '行项目',
    moWorkOrder: '生产订单顺序',
    workStatus: '生产状态',
    setSign: '拉入/出状态',
    setDate: '时间',
    setMarks: '备注',
    preSetOutBut: '预约拉出',
    cancelPreSetOutBut: '取消预约拉出',
    setOutBut: '直接拉出',
    preSetInBut: '预约拉入',
    cancelPreSetInBut: '取消预约拉入',
    setInBut: '直接拉入'
  },
  modownstatus: { /* 下发订单状态 */
    creationDate: '创建时间',
    lastUpdateDate: '修改时间',
    zkName: '下发订单类型',
    lastMakeOrder: '最后订单号'
  },
  stationflow: { /* 过站 */
    creationDate: '创建时间',
    stationFlowId: 'ID',
    workCenterCode: '车间',
    prodLineCode: '产线',
    stationCode: '工位',
    stationDes: '工位描述',
    proceduceCode: '报工工序号',
    proceduceDes: '报工工序描述',
    serialType: '条码类型',
    serialNum: '工件编号',
    palletNum: '托盘号',
    staffId: '操作者',
    makeOrder: '订单号',
    dms: 'DMS',
    itemProject: '行项目',
    vin: 'VIN',
    smallModelType: '型号',
    mainMaterialCode: '物料编号',
    materialColor: '颜色',
    materialSize: '尺寸',
    shaftProcNum: '拧紧程序号',
    repair_flag: '返修标识',
    qualitySign: '总合格标志',
    dataCollectWay: '数据采集方式',
    arriveDate: '到达时间',
    leaveDate: '离开时间',
    costTime: '消耗时间',
    shifCode: '班次代码',
    shifDes: '班次描述',
    setSign: '拉入/出状态',
    checkStatus: '过站校验状态',
    checkCode: '过站校验代码',
    checkMsg: '过站校验描述',
    flowStautsCode: '过站状态',
    assemblyFinishFlag: '是否校验合件完成',
    mesAviFlag: '是否上传MES',
    upFlag: '上传标识',
    upNgCode: '上传错误代码',
    upNgMsg: '上传错误消息',
    vinAviFlag: '是否上传VIN打刻',
    downVinFlag: '下发标识(VIN打刻)',
    downVinNgCode: '下发错误代码(VIN打刻)',
    downVinNgMsg: '下发错误消息(VIN打刻)',
    jzAviFlag: '是否上传油液加注',
    downJzFlag: '下发标识(油液加注)',
    downJzNgCode: '下发错误代码(油液加注)',
    downJzNgMsg: '下发错误消息(油液加注)',
    lesAviFlag: '是否上传LES',
    downLesFlag: '下发标识(LES)',
    downLesNgCode: '下发错误代码(LES)',
    downLesNgMsg: '下发错误消息(LES)',
    downAgvFlag: '下发标识(AGV)',
    downAgvNgCode: '下发错误代码(AGV)',
    downAgvNgMsg: '下发错误消息(AGV)',
    enableFlag: '有效标识'
  },
  meStationFlow: {
    stationCode: '工位号',
    stationDes: '工位描述',
    taskNumber: '任务号',
    taskSource: '任务来源',
    serialNumber: '序列号',
    batchNumber: '批次号',
    palletNum: '托盘号',
    mainMaterialCode: '物料编码',
    mainMaterialDescription: '物料描述',
    partDrawingNumber: '零件图号',
    partMaterialNumber: '零件物料号',
    model_type: '型号',
    length: '长',
    width: '宽',
    thick: '厚',
    weight: '重',
    materialQuality: '材质',
    cuttingMachine: '材质(切割机)',
    releaseMethod: '放行方式',
    releaseUser: '放行人',
    releaseInstructions: '放行说明',
    releaseTime: '放行时间',
    arriveDate: '到达时间',
    leaveDate: '离开时间',
    costTime: '消耗时间(单位秒)',
    shifCode: '班次代码',
    shifDes: '班次描述',
    onlineWorkstationIdentification: '上线工位标识',
    offlineWorkstationIdentification: '下线工位标识',
    tagQualitySignId: '合格标志',
    reportWorkOrNot: '是否完成报工',
    workReportInspectionInformation: '报工检查信息',
    upFlag: '上传标识',
    upNgCode: '上传错误代码',
    upNgMsg: '上传错误消息'
  },
  stationstatus: { /* 当前工位实时工件信息 */
    creationDate: '创建时间',
    stationStatusId: 'ID',
    workCenterCode: '车间',
    prodLineCode: '产线',
    stationCode: '工位',
    stationDes: '工位描述',
    stationStatus: '工位状态',
    emptyBanFlag: '是否为空板',
    serialNum: '工件编号',
    palletNum: '托盘号',
    staffId: '操作者',
    makeOrder: '订单号',
    dms: 'DMS',
    itemProject: '行项目',
    vin: 'VIN',
    smallModelType: '型号',
    mainMaterialCode: '物料编号',
    materialColor: '颜色',
    materialSize: '尺寸',
    shaftProcNum: '拧紧程序号',
    qualitySign: '总合格标志',
    setSign: '拉入/出状态',
    checkStatus: '过站校验状态',
    checkCode: '过站校验代码',
    checkMsg: '过站校验描述',
    engineNum: '发动机',
    driverWay: '驱动形式',
    statusWay: '状态计算方式',
    lineSectionCode: '产线分段编码',
    allowWay: '允许信息来源'
  },
  setinout: { /* 拉入拉出履历 */
    creationDate: '创建时间',
    setReportId: 'ID',
    workCenterCode: '车间',
    prodLineCode: '产线',
    stationCode: '工位',
    stationDes: '工位描述',
    setType: '拉入/出状态',
    serialNum: '工件编号',
    palletNum: '托盘号',
    staffId: '操作者',
    makeOrder: '订单号',
    dms: 'DMS',
    itemProject: '行项目',
    vin: 'VIN',
    smallModelType: '型号',
    mainMaterialCode: '物料编号',
    materialColor: '颜色',
    materialSize: '尺寸',
    shaftProcNum: '拧紧程序号',
    setDate: '时间',
    setMarks: '备注',
    preSetInBut: '预约拉入',
    setInBut: '直接拉入'
  },
  stationdevice: { /* 工位设备对应关系 */
    creationDate: '创建时间',
    stationDeviceId: 'ID',
    workCenterCode: '车间',
    prodLineCode: '产线',
    stationCode: '工位',
    stationDeviceType: '设备类型',
    deviceCode: '设备编号',
    deviceDes: '设备描述',
    deviceSectionCode: '工位与拧紧枪匹配',
    enableFlag: '有效标识'
  },
  recipequality: { /* 工位数据采集定义 */
    creationDate: '创建时间',
    qualityId: 'ID',
    stationId: '工位',
    qualityFrom: '数据来源',
    tagId: '采集项目',
    tagQualitySignId: '合格标志',
    groupOrder: '组号',
    groupName: '组描述',
    tagColOrder: '列位置',
    tagColInnerOrder: '排序',
    qualityFor: '测量对象',
    tagDes: '采集项目名称',
    tagUom: '采集项目单位',
    theoryValue: '标准值',
    downLimit: '下限值',
    upperLimit: '上限值',
    saveOrNot: '数据是否保存',
    enableFlag: '有效标识'
  },
  screenset: { /* 大屏基础 */
    creationDate: '创建时间',
    screenSetId: 'ID',
    screenSetType: '大屏类型',
    screenSetCode: '大屏编码',
    screenSetDes: '大屏描述',
    screenSetUrl: '页面路由',
    screenSetUrlDes: '页面路由描述',
    orderNum: '顺序号',
    switchTime: '切换时长',
    enableFlag: '有效标识'
  },
  screensetcontent: { /* 大屏内容 */
    creationDate: '创建时间',
    prodLineCode: '产线',
    todayPlan: '今日计划',
    currentOffline: '当前下线',
    finishRate: '完成率',
    jphActual: 'JPH实绩',
    deviceMobility: '设备可动率',
    todayStop: '今日停线',
    aboveShow: '上面显示',
    belowShow: '下面显示',
    backgroundImage: '背景图片',
    enableFlag: '有效标识'
  },
  andonbtn: { /* 安灯按钮基础 */
    creationDate: '创建时间',
    andonBtnId: 'ID',
    stationId: '工位',
    andonType: '安灯类型',
    andonDes: '安灯描述',
    andonColor: '安灯按钮颜色',
    btnLevel: '优先级',
    action: '动作',
    btnTagId: '按钮TAG',
    lineStopTagId: '产线停线TAG',
    lineStopApi: '产线停线API',
    andonMusicId: '音响音乐',
    andonVoiceIdList: '音响',
    andonLimitTime: '响应超时时间',
    andonBtnStatus: '当前按钮状态',
    attribute1: '属性1',
    attribute2: '属性2',
    attribute3: '属性3',
    enableFlag: '有效标识'
  },
  andoncard: { /* 安灯工位牌基础 */
    creationDate: '创建时间',
    andonCardId: 'ID',
    stationId: '工位',
    andonCardType: '工位牌类型',
    andonCardColor: '颜色',
    cardTagId: '工位牌状态TAG',
    andonCardStatus: '工位牌状态',
    andonCardCode: '工位牌编码',
    enableFlag: '有效标识'
  },
  andoncontrol: { /* 安灯工位状态控制基础 */
    creationDate: '创建时间',
    andonControlId: 'ID',
    stationId: '工位',
    andonControlName: '工位控制名称',
    andonCardValue: '工位牌控制值',
    andonCardTagId: '工位牌状态TAG',
    andonMusicId: '音响音乐',
    lineStopTagId: '产线停线TAG',
    lineStopApi: '产线停线API',
    attribute1: '属性1',
    attribute2: '属性2',
    attribute3: '属性3',
    enableFlag: '有效标识'
  },
  andonevent: { /* 安灯事件 */
    creationDate: '创建时间',
    andonEventId: 'ID',
    workCenterCode: '车间',
    prodLineCode: '产线',
    stationName: '工位名称',
    stationCode: '工位',
    stationDes: '工位描述',
    lineSectionCode: '产线分段编码',
    andonBtnId: '安灯按钮',
    andonType: '安灯类型',
    andonDes: '安灯描述',
    andonLimitTime: '响应超时时间',
    happenDate: '发生时间',
    resetDate: '复位时间',
    resetFlag: '是否复位',
    costTime: '消耗时间',
    overTimeFlag: '是否超时',
    enableFlag: '有效标识'
  },
  andonvoice: { /* 安灯音响基础 */
    creationDate: '创建时间',
    andonVoiceId: 'ID',
    andonVoiceSerial: '设备序列号',
    andonVoiceDes: '音响名称',
    andonVoiceAttr: '音响属性',
    btnTagId: '按钮TAG',
    btnLevel: '优先级',
    enableFlag: '有效标识'
  },
  andonmusic: { /* 安灯音响音乐 */
    creationDate: '创建时间',
    andonMusicId: 'ID',
    andonMusicDes: '音乐名称',
    andonMusicPath: '音乐路径',
    andonMusicAttr: '音乐属性',
    enableFlag: '有效标识'
  },
  haCarTypeRoute: { /* 焊装车型工艺路线维护 */
    haCarTypeDes: '白车身号：',
    productionLine: '产线编码：',
    productionLinet: '产线编码',
    carType: '白车身号',
    onlineStation: '上线工位',
    routeStation: '工艺路线',
    attribute1: '属性1',
    attribute2: '属性2',
    attribute3: '属性3'
  },

  /** ************************************************************/
  /* PCB定义*/

  /** ************************************************************/
  /* DCS定义*/

  // 越南包装项目定义
  vie: {
    // 配方维护
    partNum: '料号',
    version: '版本',
    setType: 'SET类型',
    innerType: '厂内码类型',
    pcsType: 'PCS类型',
    plateLen: '板长(mm)',
    plateWid: '板宽(mm)',
    plateThi: '板厚(mm)',
    plateWei: '板重(g)',
    setCodeLen: 'SET条码长度',
    innerCodeLen: '厂内码长度',
    pcsCodeLen: 'PCS条码长度',
    setCase: 'SET大小写',
    innerCase: '厂内码大小写',
    pcsCase: 'PCS大小写',
    index: '序号',

    ordnumTruBatchRule: '订单号截取批次规则',
    cusPartNumIntRule: '客户料号截取料号规则',
    setTruBatchRule: 'SET截取批次规则',
    setPartNumIntRule: 'SET截取料号规则',
    setIntCycRule: 'SET截取周期规则',
    pcsTruBatchRule: 'PCS截取批次规则',
    pcsPartNumIntRule: 'PCS截取料号规则',
    pcsIntSeriaRule: 'PCS截取序号规则',
    pcsNumStartNum: 'PCS序号开始数字',
    pcsNumValadd: 'PCS序号递增值',
    // 工单管理
    OriginLotNum: '原始批号',
    taskSource: '任务来源',
    taskStatus: '任务状态',
    orderNum: '订单号',
    task_type: '任务类型',
    task_status: '任务状态',
    cusPartNum: '客户料号',
    planQuantity: '计划数量',
    singlePackQua: '单包数量',
    planTime: '计划时间',
    orderStatus: '订单状态',
    customCode1: '客户编码',
    orderType: '订单类型',
    okFinishe: 'OK完工',
    ngFinishe: 'NG完工',
    cycle: '周期',
    cutBatchNum: '截取批号',
    intPartNumb: '截取料号',
    taskStarTime: '任务开始时间',
    taskEndTime: '任务结束时间',
    taskUserTime: '任务消耗时间',
    operator: '操作者',
    confirmIssuance: '开始生产',
    LWHTHK: '长/宽/高/厚',
    plantCode: '工厂编码',
    salesOrder: '销售订单号',
    salesItem: '销售订单行',
    salesOrg: '销售组织',
    salesType: '销售类型',
    customPn: '终端客户PN',
    customPo: '终端客户订单',
    customCode: '终端客户编码',
    customName: '终端客户名称',
    splitLot: '截取批次号',
    splitModel: '截取料号',
    attribute1: '预留属性1',
    attribute2: '预留属性2',
    attribute3: '预留属性3',

    xoutNum: 'XOUT数量',
    xoutType: 'XOUT 类型',
    quaOfOkBoard: '放板OK数量',
    quaOfNgBoard: '放板NG数量',
    passRate: '合格率',
    numOfComBoards: '收板完成数量',
    packComplete: '打包完成',
    orderStarTime: '订单开始时间',
    orderUseTime: '订单耗时',

    setOrder: 'SET顺序',
    judgingTheResult: '判断结果',
    setCode: 'SET条码',
    setInnerCode: '厂内码',
    rotationDirection: '旋转方向',
    setLevel: 'SET等级',
    stackingPosition: '堆叠位置',
    setFront: 'SET正面',
    setOpposite: 'SET反面',
    sortingError: '分选错误',
    packOrder: '打包顺序',
    packCode: '打包条码',
    setNum: 'SET数量',
    IsItATrunk: '是否尾箱',
    packError: '打包异常',
    cus2D1: '客户2D1',
    cus2D2: '客户2D2',
    inner2D: '厂内2D',
    xout_mapping_result: 'Mapping',
    InnerOrder: '厂内码顺序',
    InnerLevel: '厂内码等级',
    InnerFront: '厂内码正面',
    InnerOpposite: '厂内码反面',

    equAllPLCIss: '设备允许PLC下发',
    triColorLight: '三色灯',
    lineScan: '线扫',
    printer: '打印机',
    employeeID: '员工名称',
    name: '姓名',
    department: '部门',
    classes: '班次',
    login: '登 入',
    logOut: '登 出',
    viewDetails: '查看明细',
    NGmulCod: '重码',
    OKPosition: 'OK位',
    NGPosition: 'NG位',
    rotate: '旋转',
    NORotate: '不旋转',
    Yes: '是',
    NO: '否',
    cancel: '取 消',
    close: '关 闭',
    employeeLogin: '员工登录',
    messageAlert: '消息提醒',
    workOrder: '工单管理',
    run: '运行',
    alarm: '警报',
    stop: '停止',
    preserve: '保养',
    mecFai: '机故',
    repair: '维修状态',
    queryException: '查询异常',
    loginSuccess: '登录成功',
    operationException: '操作异常',
    AreYouSureToLogOut: '确定登出当前员工吗？',
    AreYouSureDelete: '确定删除本条数据吗？',
    prompt: '提示',
    determine: '确定',
    qualifiedQuantity: '合格数量',
    unqualifiedQuantity: '不合格数量',
    quantity: '数量',
    cnneSuccess: '连接成功',
    cnneFailed: '连接失败',
    connDisconRecon: '连接断开，正在重连。。。',
    PLCCommunInter: 'PLC通讯中断',
    lineScanCommunInter: '线扫通讯中断',
    printerCommunInter: '打印机通讯中断',
    MESCommunInter: 'MES通讯中断',
    pleaseStartMonitor: '请启动监控',
    noIDFound: '未查询到id',
    save: '保存',
    TotalNumberOfPackages: '总包数',
    TotalNumberOfPieces: '总颗数',
    MaximumNumberOfFullPackages: '最大满包数',
    TrayMaterialCode: 'Tray盘物料编码',
    TrayDiskCapacity: 'Tray盘容积',
    SortingComparisonRuleSetting: '分选对比规则设定',
    // 包装set表
    pileBarcode: '包装条码',
    arrayIndex: '订单SET顺席',
    boardSn: '线扫流水号',
    arrayLevel: '线扫SET等级',
    arrayFrontLevel: '线扫正面SET等级',
    arrayBackLevel: '线扫反面SET等级',
    setFrontReadingCodeTime: 'SET正面读码时间',
    setReverseCodeReadingTime: 'SET反面读码时间',
    arrayMark: '线扫光学点检测结果',
    arrayBdCount: '线扫SET下PCS数量',
    boardResult: '板件判断结果',
    depositPosition: '堆叠位置',
    xoutFlag: '是否为XOUT分选',
    xoutSetNum: 'XOUT设定数量',
    xoutActNum: 'XOUT实际数量',
    arrayNgCode: 'SET分选NG代码',
    arrayNgMsg: 'SET分选NG描述',
    arrayFrontInfo: 'SET正面线扫数据',
    arrayBackInfo: 'SET反面线扫数据',
    arrayInnerLevel: '线扫厂内码等级',
    arrayInnerBdCount: '线扫厂内码下PCS数量',
    arrayInnerNgCode: '厂内码分选NG代码',
    arrayInnerNgMsg: '厂内码分选NG描述',
    arrayInnerFrontInfo: '厂内码正面线扫数据',
    arrayInnerBackInfo: '厂内码反面线扫数据',
    upFlag: '上传标识',
    upNgCode: '上传错误代码',
    pileUseFlag: '是否被打包使用',
    unbindFlag: '是否解绑',
    unbindUser: '解绑人',
    unbindTime: '解绑时间',
    unbindWay: '解绑方式说明',
    pileIndex: '订单中打包序号',
    arrayCount: '打包中SET总数',
    pileWeight: '打包重量',
    pileUser: '打包人员',
    pcArraylist: 'PC计划SET集合',
    plcArraylist: 'PLC反馈SET集合',
    pileStatus: '打包状态',
    pileFrontLevel: '线扫正面pile等级',
    pileBackLevel: '线扫反面pile等级',
    pileFrontReadingCodeTime: 'pile正面读码时间',
    pileReverseCodeReadingTime: 'pile反面读码时间',
    pilengCode: '打包验证NG代码',
    pile_ng_msg: '打包验证NG描述',
    dailyPassRate: '当日合格率',
    dailyOKQuantity: '当日OK数量',
    dailyNGQuantity: '当日NG数量',
    dailyHourProduct: '当日小时生产峰值',
    dailyHourProductVal: '当日小时生产谷值',
    dailyHourProductAve: '当日小时生产平均值',
    arrayInnerCount: '打包中厂内码总数',
    pcArrayInnerlist: 'PC计划厂内码集合',
    plcArrayInnerlist: 'PLC反馈厂内码集合',
    form: '从',
    start: '开始',
    locat: '位置',
    cut: '截取',
    charac: '个字符',
    formulaMainten: '配方维护',
    cannotBeEmptyOrAPositiveInteger: '不能为空或正整数',
    otherValuesCannotBeEmpty: '其他值不能为空',
    editSuccess: '修改成功',
    effective: '有效',
    invalid: '无效',
    what: '吗？',
    changeTo: '确定要将有效标识修改为',
    Cancellation: '任务取消',
    TaskInit: '任务初始化',
    scan: '扫描',
    scanOrdNumMESTask: '扫描Lot No获取MES任务：',
    select: '选择',
    success: '成功',
    PleaseSelect: '请至少选择一项!',
    conSel: '确认选中的',
    Piece: '条数据?',
    SuccessIss: '下发成功',
    SuccessGetStripInspectData: '下载Mapping结果成功',
    pcsCode: 'PCS条码',
    PCSReverse: 'PCS反面线扫数据',
    PCSFront: 'PCS正面面线扫数据',
    PCSOrder: 'PCS顺序',
    PCSLevel: 'PCS等级',
    LineScanPCSResults: '线扫PCS光学点检测结果',
    PCSSortingNGCode: 'PCS分选NG代码',
    PCSSortingNGMsg: 'PCS分选NG描述',
    PCSStatus: 'PCS状态',
    whetherXOUT: '是否XOUT',
    time: '时间',
    setStatus: 'SET状态',
    InnerStatus: '厂内码状态',
    export: '导出',
    setOutside: 'set记录表',
    packInformation: '包装配方维护信息',

    // 解绑功能相关
    unbind: '解绑',
    manual: '手动',
    masterLot: '母批状态', // 母批状态
    targetQuantity: '目标数量',
    completed: '完成数量', // 完成数量
    NGQuantity: 'NG数量', // NG数量
    checkplans: '首检计划数量', // 首检计划数量
    checksCompleted: '首检完成数量', // 首检完成数量
    platingplates: '陪镀板数量', // 陪镀板数量
    plates: '分盘数量', // 分盘数量
    area: '面次', // 面次
    MaxNumber: 'Tray盘最大使用次数', // Tray盘最大使用次数
    orderParameters: '工单参数集合', // 工单参数集合
    selectedTaskData: '确认删除选中的任务数据?',
    confirmUnbindAllSetTips: '确定解绑此条Pile记录下所有的Set数据吗？',
    confirmUnbindAllBdTips: '确定解绑此条Set记录下所有的Board数据吗？',
    BoardNumber: '板子序号', // 板子序号
    BarCode: '板子条码', // 板子条码
    Features: '功能对比'
  },
  interfaceLogs: {
    time: '时间',
    interName: '接口名称',
    interDes: '接口描述',
    sourceSystem: '来源系统',
    requestParams: '请求参数',
    responseParams: '响应参数',
    successFlag: '是否成功',
    ESBinterName: 'ESB接口名称',
    ESBinterDes: 'ESB接口描述',
    ESBinterAddress: 'ESB接口地址',
    targetSystem: '目标系统',
    interMeans: '接口方式',
    startTime: '开始时间',
    endTime: '结束时间',
    costTime: '请求时长',
    InterMessages: '接口消息',
    parasList: '定制化参数',
    requestInfo: '请求消息',
    remark: '备注',
    total: '总数量',
    pre: '上一页',
    next: '下一页',
    current: '当前第',
    page: '页',
    paramsDetails: '参数详情',
    pleaseEnter: '请输入内容',
    Topped: '已置顶',
    bottomSet: '已置底',
    ExportFailed: '导出失败',
    serialNumber: '序号'
  },
  scadaAlarmReport: {
    title: 'SCADA报警报表',
    prodLine: '产线',
    station: '工位',
    instanceCode: '实例编号',
    alarmCode: '报警代码',
    alarmDesc: '报警描述',
    resetFlag: '是否复位',
    resetYes: '已复位',
    resetNo: '待复位',
    time: '时间',
    startTime: '开始时间',
    endTime: '结束时间',
    instanceDesc: '实例描述',
    alarmLevel: '报警级别',
    alarmTime: '报警时间',
    resetTime: '复位时间',
    isSimulated: '是否模拟',
    simulatedYes: '是',
    simulatedNo: '否',
    tag: 'Tag',
    export: '导出',
    search: '搜索',
    reset: '重置',
    totalCount: '总数量',
    currentPage: '当前第',
    previousPage: '上一页',
    nextPage: '下一页',
    page: '页',
    top: '已置顶',
    bottom: '已置底',
    selectProdLineAndStation: '请选择产线与工位',
    queryException: '查询异常',
    exportFailed: '导出失败'
  },
  messageReport: {
    cim_from: '消息来源',
    finish_flag: '完成标识',
    screen_control: '显示方式',
    screen_code: '代码',
    cim_msg: '消息',
    selectStation: '请选择工位'
  },
  diagnosis: {
    client_code: '实例',
    link_status: '通讯状态',
    link_message: '通讯信息',
    creationDate: '时间范围',
    table: '表格形式',
    simulated_flag: '模拟标识',
    curved: '曲线形式',
    abnormal: '异常统计',
    link_normal: '通讯正常',
    link_abnormal: '通讯异常',
    scadaFaild: 'Scada实例基础数据查询失败',
    scadaError: 'Scada实例基础数据查询超时或者SQL错误',
    link_analysis: '通讯状态散点图分析',
    sampleSize: '样本数量',
    statusValue: '状态值',
    abnormalStatistics: '通讯异常频次统计',
    frequency: '断网频次',
    exception: '查询selCellIP异常',
    instance: '请选择实例',
    UnableNumber: '未获取到单元IP与端口号',
    NoData: '已到顶无数据',
    scadaQueryFailed: 'Scada通讯报表查询失败:',
    scadaSqlError: 'Scada通讯报表查询超时或者SQL错误',
    serialNumber: '序号:',
    time: '时间:',
    messageDes: '消息描述:',
    NoDataAvailable: '当前无数据'
  },
  proMonitor: {
    processTasks: '流程任务:',
    total: '总数量:',
    dayQuantity: '当日数量:',
    dayNormal: '当日正常:',
    dayAbnormal: '当日异常',
    cancel: '取消流程',
    view: '查看',
    time: '时间：',
    info: '信息：',
    step: '步骤：',
    log: '日志：',
    queryException: '查询异常',
    cancelSucess: '取消成功:',
    abnormal: '异常:'
  },
  header: {
    user: '当前用户：',
    onlineDuration: '在线时长：',
    min: '分钟',
    signOut: '退出登录',
    lockScreen: '锁屏',
    screen: '全屏缩放',
    stationSelect: '工位选择',
    greenNewEnergy: '绿色环保新能源',
    loginPassword: '请输入登陆密码',
    unlock: '解锁',
    exitSystem: '确定注销并退出系统吗？',
    passwordUnlock: '请输入登陆密码解锁',
    passwordError: '解锁密码错误，请输入登陆密码解锁',
    unlockingSuccessful: '解锁成功',
    error: '错误',
    usernameRequire: '用户名不能为空',
    passwordRequire: '密码不能为空',
    roleRequire: '系统角色不能为空',
    codeRequire: '验证码不能为空'
  },
  hmiMain: {
    onLine: '在线模式', // 在线
    offline: '脱机模式', // 脱机
    prot1: '工位1', // 端口1
    prot2: '工位2', // 端口2
    protAgv1: 'AGV(1)', // AGV1
    protAgv2: 'AGV(2)', // AGV2
    enable: '启用', // 启用
    disabled: '禁用', // 禁用
    manual: '手动画面', // 手动画面
    eapPing: 'PING网络测试', // Ping-EAP
    reOnline: '收板机在线', // 收板机在线
    CCD: '板件CCD',
    PlateCCD2: '板件CCD2',
    CCD1: '载具CCD1',
    CCD2: '载具CCD2',
    DownStatus: '下游',
    upStatus: '上游',
    petName: '昵称',
    processQuantity: '在制数量',
    importTask: '进片任务',
    productionTask: '出片任务',
    vehicleCode: '载具码',
    plannedQuantity: '计划数量',
    okQuantity: 'OK数量',
    noReadQuantity: 'NoRead数量',
    shortQuantity: '少片数量',
    wipManualReport: '扫描补报',
    leftNoReadCount: 'NoRead数量',
    cancelWip: 'NoRead补报',
    endWip: '结束补报',
    entranNum: '入口数量',
    exportNum: '出口数量',
    productionTime: '生产时间',
    occurrenTime: '发生时间',
    alarmLevel: '报警级别',
    alarmMsg: '报警消息',
    parameterName: '参数名称',
    company: '单位',
    setValue: '设定值',
    actualValue: '实际值',

    employee: '员工号', // 员工号
    name: '姓名', // 姓名
    department: '部门', // 部门
    status: '机台状态', // 机台状态
    EAP: 'EAP远程', // EAP远程
    AIS: 'AIS本地', // AIS本地
    production: '生产模式', // 生产模式
    plate: '板件模式', // 板件模式
    panel: '有Panel', // 有Panel
    NoPanel: '无Panel', // 无Panel
    more: '更多', // 更多
    work: '作业端口', // 作业端口
    Login: '登 入', // 登入
    LogOut: '登 出', // 登出
    prot1Step: '端口1步序', // 端口1步序
    prot1info: '端口1消息', // 端口1消息
    forced: '强 制 退 载 具', // 强制退载具
    prot2Step: '端口2步序', // 端口2步序
    prot2info: '端口2消息', // 端口2消息
    portStatus: '端口状态', // 端口状态
    carrierStatus: '载具状态', // 载具状态
    firstEdition: '是否首板', // 是否首板
    complete: '完工/计划', // 完工/计划
    firstPlan: '首板完工/计划', // 首板完工/计划
    workMode: '作业模式', // 作业模式
    LWH: '长/宽/高', // 长宽高
    vehicle: '载具/天盖', // 载具/天盖
    Tray: 'Tray盘码', // Tray盘码
    plateCode: '板件码', // 板件码
    fmale: '母批号', // 母批号
    blowOff: '放口', // 放口,
    plan: '计划', // 计划
    put: '已放(OK/ALL)', // 已放
    putStatus: '放状态', // 放状态
    panelStatus: '状态', // 板件状态
    received: '已收(OK/ALL)', // 已收,
    close: '收口', // 收口
    receivingState: '收状态', // 收状态
    SupportBrowning: '配套棕化', // 配套棕化
    SupportPunching: '配套冲孔', // 配套冲孔
    serial: '序号', // 序号
    firstPiece: '首件', // 首件
    abnormal: '异常', // 异常
    time: '时间', // 时间
    sideboard: '陪板', // 陪板
    vehicleInput: '载具输入', // 载具输入
    manualBatch: '手工批次上报', // 手工批次上报
    localTask: '本地任务增加', // 本地任务增加
    firstCheck: '首检判定', // 首检判定
    panelInput: 'Panel输入', // Panel输入
    panelJudge: 'Panel判定', // Panel判定
    confirmation: '切批确认', // 切批确认
    CIMMessage: 'CIM消息', // CIM消息
    dailyOnline: '日在线生产', // 日在线生产
    dailyOffLine: '日脱机生产', // 日脱机生产
    procedure: '获取当前登录信息异常', // 获取当前登录信息异常
    side: '正面', // 正面
    reverse: '反面', // 反面
    productionBoard: '生产板', // 生产板
    dummy: 'Dummy',
    have: '有', // 有
    isNo: '无', // 无
    logoutSuccess: '登出成功', // 登出成功
    workOrderEntry: '工单输入', // 工单输入
    clickOK: '批次需要切换主制程配方,请确认切换完成后点击确定', // 批次需要切换主制程配方,请确认切换完成后点击确定
    palletInput: 'Pallet/Tray输入', // Pallet/Tray输入
    mixedBatch: '混批确认', // 混批确认
    forValidation: '人工扫描任务并提交到EAP验证', // 人工扫描任务并提交到EAP验证
    forFinish: '手动结批上报', // 手动结批上报
    forFinish2: '手动确认WIP', // 手动确认WIP
    Identify: '确定对端口', // 确定对端口
    mandatory: '进行强制退载具操作吗？', // 进行强制退载具操作吗？
    machineProduction: '当前机台生产中,不允许状态修改', // 当前机台生产中,不允许状态修改
    cancelFlowchart: '取消流程图成功', // 取消流程图成功
    cancelException: '取消流程图异常', // 取消流程图异常
    abnormalError: '查询点位数据异常：', // 查询点位数据异常
    EAPCommunication: 'EAP通讯中断', // EAP通讯中断
    PLCCommunication: 'PLC通讯中断', // PLC通讯中断
    CCDinterrupted: '板件读码CCD通讯中断', // 板件读码CCD通讯中断
    CCD1Interrupted: '载具1读码CCD通讯中断', // 载具1读码CCD通讯中断
    CCD2Interrupted: '载具2读码CCD通讯中断', // 载具2读码CCD通讯中断
    startTime: '开始时间', // 开始时间
    endTime: '结束时间', // 结束时间,
    implement: '实放',
    implementUnLoad: '实收',
    scanConfirmation: '工单扫描确认',
    inSeconds: '秒后接口超时',
    BOXInput: 'BOX输入',
    deviceInit: '初始化',
    deviceRun: '运行',
    deviceStop: '停止',
    deviceIdle: '待机',
    deviceDown: '停机',
    devicePm: '保养'
  },
  workOrder: {
    workOrderNumber: '工单号',
    processCode: '制程代码',
    productUse: '产品用途',
    batchNumber: '批号简码',
    materialNumber: '物料号',
    NumbeOfPlates: '板件数量',
    platSe: '板序',
    flip: '翻转',
    parameterInfo: '参数信息'
  },
  productionTasks: {
    masterStatus: '母批状态',
    subbatchState: '子批状态',
    methodCompletion: '完工方式',
    normalFinish: '正常完板',
    lessBoard: '少板完板',
    boardsFinish: '多板完板',
    ForcedFinish: '强制完板',
    viewPNL: '查看PNL',
    PNLlist: 'PNL列表',
    deleteTask: '删除任务',
    FinishedCondition: '完工状态'
  },
  stationParameter: {
    stationConfiguration: '工位参数配置',
    theSettings: '未查询到对应IP,请检查设置',
    theReceiver: '未查询到收板机IP,请设置收板机IP',
    notEnabled: '获取收扳机监控未开启',
    interfaceCode: '接口编码',
    interDes: '接口描述',
    NGDegree: 'NG次数',
    synchronization: '同步',
    continuation: '续传',
    enable: '启用',
    cutOnline: '放板机不在线，无法切在线',
    onlineMode: '在线模式下不可操作'
  },
  // 广合
  guanghe: {
    parmas: '参数', // 参数
    finish: '完成', // 完成
    plateNG: '板件NG数量', // 板件NG数量
    trayNG: '托盘NG数量', // 托盘NG数量
    trayOK: '托盘OK数量', // 托盘OK数量
    panelNG: '面板NG数量', // 面板NG数量
    panelOK: '面板OK数量', // 面板OK数量
    deviceOee: '设备OEE', // 设备OEE
    OkQuantity: 'OK数量', // OK数量
    NGQuantity: 'NG数量', // NG数量
    total: '总量', // 总量
    NGPass: 'NG_PASS数量', // NG_PASS数量
    OfflineCodeReading: '离线读码量', // 离线读码量
    OnlineCodeReading: '在线读码量', // 在线读码量
    readBitRate: '读码率', // 读码率
    port1Return: '端口1退载具',
    port2Return: '端口2退载具',
    port3Return: '端口3退载具',
    port4Return: '端口4退载具',
    exitSystem: '退出系统',
    logout: '员工登出',
    energyDetail: '能源信息明细',
    timeTaskData: '时间统任务数据'
  },
  // 定颖
  dy: {
    auto: '自动',
    offLine: '离线模式',
    local: 'Local模式',
    semiAuto: '半自动模式',
    model: '模式',
    Semi_Auto: 'Semi-Auto模式',
    Auto: 'Auto模式'
  },
  // 维信 / 护士
  wx: {
    recipeName: '配方名称',
    downRecipe: '扫码下载配方：',
    editParameters: '是否修改参数：',
    import: '导入',
    export: '导出',
    equipSelfTest: '设备自检',
    recipeType: '配方类型',
    recipeDesc: '配方描述',
    lotNo: '批次条码',
    version: '版本号',
    recipeOperation: '配方操作',
    down: '下载配方',
    upload: '上传配方',
    distributed: '下发设备',
    equipSelfTestInfo: '设备自检信息',
    waitUpload: '已存在上传任务，请等待上传完成',
    uploadFormula: '触发上传配方流程',
    confirmDelete: '确认删除选中的',
    articleData: '条数据',
    SuccessfullyMes: '上传mes成功',
    selected: '确认上传选中的',
    uploadTask: '已存在上传任务，请等待上传完成',
    trigger: '触发上传配方流程',
    confirmRecipe: '确认上传配方?',
    alreadyUploadTask: '已存在上传任务，请等待上传完成',
    confirmDownload: '确认从上层系统下载配方',
    pleaseWait: '已存在下载任务，请等下载完成',
    triggerDownload: '触发下载配方流程',
    IssuedEquipment: '确认下发配方到设备?',
    alreadyExistsTask: '已存在下发任务，请等下发完成',
    SuccessfullyIssued: '下发配方到设备成功',
    selectOne: '请选择一条数据!',
    exportName: '确定导出配方名称为',
    recipeDownload: '配方下载',

    comfirmReset: '确认要复位流程吗？',
    resetSuccess: '流程复位成功',
    currently: '当前未获取到物料，请先去维护物料',
    queryFailed: '流程复位失败',
    failedMaterial: '未能根据当前物料号',
    foundRecipe: '查询到相关配方信息',
    pleaseScan: '请先扫描或输入工单号',
    reset: '复位流程',
    lotNum: '工单号',
    batchCheck: '批次校验',
    employeeID: '员工号',
    productInfo: '配方信息',
    alarmMsg: '报警信息',
    alarmTime: '报警时间',
    alarmLevel: '报警级别',
    alarmCode: '报警代码',
    alarmDesc: '报警描述',
    feedbackInfo: '反馈信息',
    userInfo: '用户登录',
    equipmentInfo: '设备自检',
    productResult: '产品登录',
    otherInfo: '其他信息',
    panelInfo: '扫码结果',
    paramInfo: '参数上传',
    statusInfo: '状态上传',
    producrParameter: '实时参数',
    projectName: '参数名称',
    currentValue: '当前值',
    state: '状态',
    unit: '单位',
    upperLimit: '上限',
    lowerLimit: '下限',
    modifyFormula: '修改配方',
    thickness: '塞孔机板厚',
    modifyParams: '是否修改参数',
    parameterCode: '参数编码',
    parameterDesc: '参数描述',
    parameterValue: '参数值',
    cancel: '取消',
    confirm: '确定下发',
    plcHeartbeat: 'PLC心跳',
    eapHeartbeat: 'EAP心跳',
    upstream: '上游',
    downstream: '下游',
    electricity: '电量',

    productionMode: '量产模式',
    firstArticleMode: '首件模式',
    dummyMode: 'Dummy模式',
    stationCode: '工位号',
    capacity: '产能',
    readBitRate: '读码率',
    onDuty: '在岗',
    leave: '脱岗',
    enterCode: '请输入物料编码',
    enterDesc: '请输入物料编码',
    hole: '选择填孔+',
    selectSupface: '选择表面+',

    materialMaintenance: '物料维护',
    equipment: '设备号',
    scanDownload: '扫码下载配方：',
    addParams: '新增参数',
    scadaRequest: '发起Scada请求配方,配方下载中',
    scadaError: '发起Scada请求配方,配方下载出现异常',
    uploadSuccessful: '上传成功',
    downLoadRecipe: '确认下发该配方吗',
    subDetail: '子配方维护详情',
    modificationFailed: '修改失败'

  },
  // 福建瑞闽
  fjrm: {
    wasteBoxCode: '废料框编码',
    wasteBoxDes: '废料框描述',
    height: '高度',
    rgvCode: 'RGV编码',
    rgvDes: 'RGV描述',
    wharfCode: '码头编码',
    wharfDes: '码头描述',
    wharfType: '码头类型',
    locationX: 'X坐标',
    locationY: 'Y坐标',
    locationZ: 'Z坐标',
    wharfOrder: '码头排序',
    wharfTag: '码头点位',
    lockFlag: '是否锁定',
    lotNo: '批次号',
    si: 'si',
    fe: 'fe',
    cu: 'cu',
    mn: 'mn',
    mg: 'mg',
    ni: 'ni',
    zn: 'zn',
    ti: 'ti',
    cr: 'cr',
    na: 'na',
    ca: 'ca',
    taskNum: '任务号',
    taskFrom: '任务来源',
    taskWay: '任务方式',
    taskType: '任务类型',
    wareHouse: '库区域',
    fromStockCode: '起始库位',
    toStockCode: '目标库位',
    lotNum: '批次号',
    materialCode: '物料编码',
    width: '总重量',
    errorMin: '最小值',
    errorMax: '最大值',
    executeWidth: '执行总重量',
    taskOrder: '任务排序',
    taskStatus: '任务状态',
    enableFlag: '有效标识'
  }
}
