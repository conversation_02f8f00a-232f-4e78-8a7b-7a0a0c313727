<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.center.centreNumberDescription')">
                <!-- 中心编号/描述： -->
                <el-input v-model="query.centerCodeDes" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.center.centreCode')" prop="center_code">
            <!-- 中心编码 -->
            <el-input v-model="form.center_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.center.centreDescription')" prop="center_des">
            <!-- 中心描述 -->
            <el-input v-model="form.center_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.center.mainCard')" prop="center_host_1">
            <!-- 主网卡 -->
            <el-input v-model="form.center_host_1" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.center.attachCard1')" prop="center_host_2">
            <!-- 附网卡1 -->
            <el-input v-model="form.center_host_2" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.center.attachCard2')" prop="center_host_3">
            <!-- 附网卡2 -->
            <el-input v-model="form.center_host_3" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.center.attachCard3')" prop="center_host_4">
            <!-- 附网卡3 -->
            <el-input v-model="form.center_host_4" />
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="center_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="center_code" min-width="120" :label="$t('lang_pack.center.centreCode')" />
            <!-- 中心编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="center_des" min-width="120" :label="$t('lang_pack.center.centreDescription')" />
            <!-- 中心描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="center_host_1" width="120" :label="$t('lang_pack.center.mainCard')" />
            <!-- 主网卡 -->
            <el-table-column  :show-overflow-tooltip="true" prop="center_host_2" width="120" :label="$t('lang_pack.center.attachCard1')" />
            <!-- 附网卡1 -->
            <el-table-column  :show-overflow-tooltip="true" prop="center_host_3" width="120" :label="$t('lang_pack.center.attachCard2')" />
            <!-- 附网卡2 -->
            <el-table-column  :show-overflow-tooltip="true" prop="center_host_4" width="100" :label="$t('lang_pack.center.attachCard3')" />
            <!-- 附网卡3 -->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudCenter from '@/api/core/center/center'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  center_id: '',
  center_code: '',
  center_des: '',
  center_host_1: '',
  center_host_2: '',
  center_host_3: '',
  center_host_4: ''
}
export default {
  name: 'SYS_CENTER',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '中心维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'center_id',
      // 排序
      sort: ['center_id asc'],
      // CRUD Method
      crudMethod: { ...crudCenter },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_core_center:add'],
        edit: ['admin', 'sys_core_center:edit'],
        del: ['admin', 'sys_core_center:del'],
        down: ['admin', 'sys_core_center:down']
      },
      rules: {
        center_code: [{ required: true, message: '请输入中心编码', trigger: 'blur' }],
        center_host_1: [{ required: true, message: '请输入主网卡', trigger: 'blur' }]
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {}
}
</script>