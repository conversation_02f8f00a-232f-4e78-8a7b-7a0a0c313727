import request from '@/utils/request'

// 查询用户
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserSel',
    method: 'post',
    data
  })
}
// 新增用户
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserIns',
    method: 'post',
    data
  })
}
// 修改用户
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserUpd',
    method: 'post',
    data
  })
}
// 删除用户
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserDel',
    method: 'post',
    data
  })
}
// 导出
export function down(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserDown',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 修改用户密码
export function updatePass(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserUpdatePass',
    method: 'post',
    data
  })
}
// 修改用户信息
export function updateDtl(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysUserUpdateDtl',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, down, updatePass, updateDtl }

