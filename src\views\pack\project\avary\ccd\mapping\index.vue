<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="料号">
                <el-input v-model="query.productNo" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批号">
                <el-input v-model="query.lotNo" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="厂内码">
                <el-input v-model="query.barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="数据来源">
                <el-input v-model="query.dataFrom" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="下载时间">
                <el-input v-model="query.itemDateVal" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="上传时间">
                <el-input v-model="query.uploadDateVal" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation
        show=""
        :permission="permission"
      >
        <template slot="left">
          <el-button
            v-loading="downloadLoading"
            :disabled="downloadLoading"
            type="primary"
            icon="el-icon-upload2"
            @click="exportExecl"
          >{{ $t('lang_pack.vie.export') }}</el-button>
          <el-date-picker
            v-model="exportDatePeriod"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('view.form.timePeriodStart')"
            :end-placeholder="$t('view.form.timePeriodEnd')"
            value-format="yyyy-MM-dd"
          />
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.enable_flag"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-value="Y"
                  inactive-value="N"
                  disabled
                />
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="下载时间" width="150" align="center" prop="item_date" />
            <el-table-column :show-overflow-tooltip="true" label="上传时间" width="130" align="center" prop="upload_date_val" />
            <el-table-column :show-overflow-tooltip="true" label="料号" width="130" align="center" prop="product_no" />
            <el-table-column :show-overflow-tooltip="true" label="料号版本" width="130" align="center" prop="product_rev" />
            <el-table-column :show-overflow-tooltip="true" label="批号" width="200" align="center" prop="lot_no" />
            <el-table-column :show-overflow-tooltip="true" label="厂内码" width="200" align="center" prop="barcode" />
            <el-table-column :show-overflow-tooltip="true" label="mapping结果" width="130" align="center" prop="bad_mark" />
            <el-table-column :show-overflow-tooltip="true" label="数据来源" width="100" align="center" prop="data_from" />
            <el-table-column :show-overflow-tooltip="true" label="制程代号" width="130" align="center" prop="process_no" />
            <el-table-column :show-overflow-tooltip="true" label="2Dmapping批号" width="200" align="center" prop="twodlot_no" />
            <el-table-column :show-overflow-tooltip="true" label="原始母批" width="200" align="center" prop="parentlot_no" />
            <el-table-column :show-overflow-tooltip="true" label="重工数" width="100" align="center" prop="reworknum" />
            <el-table-column :show-overflow-tooltip="true" label="旋转角度" width="100" align="center" prop="angle" />
            <el-table-column :show-overflow-tooltip="true" label="X,Y（翻转）" width="100" align="center" prop="flip_type" />
            <el-table-column :show-overflow-tooltip="true" label="Each Piece（Strip）X" width="100" align="center" prop="stripeachpiecex" />
            <el-table-column :show-overflow-tooltip="true" label="Each Piece（Strip）Y" width="100" align="center" prop="stripeachpiecey" />
            <el-table-column :show-overflow-tooltip="true" label="不良数据结果" width="200" align="center" prop="err_type" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import api from '@/api/pack/project/avary/ccd/mappingResultDetail'
import apiExportExcel from '@/api/core/biz/exportExcel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { downloadFile } from '@/utils/index'

const defaultForm = {
  id: '',
  code: '',
  desc: '',
  clientId: null,
  enable_flag: 'Y'
}
export default {
  name: 'PACK_CCD_MAPPING',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.dcsAlarm'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['item_date_val desc'],
      // CRUD Method
      crudMethod: { ...api },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'a_pack_get_strip_inspect_data:add'],
        edit: ['edit', 'a_pack_get_strip_inspect_data:edit'],
        del: ['del', 'a_pack_get_strip_inspect_data:del']
      },
      downloadLoading: false,
      exportDatePeriod: []
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
  },
  methods: {
    exportExecl() {
      if (!this.exportDatePeriod || this.exportDatePeriod.length === 0) {
        this.$message.error('请选择导出时间段（下载时间）')
        return
      }
      this.downloadLoading = true
      const bodyData = {
        name: '2D下载Mapping信息',
        conn: 'a_pack_get_strip_inspect_data',
        columns: {},
        date_period: this.exportDatePeriod
      }
      let columnIndex = 1
      const columns = Object.assign({}, this.$t('view.field.mappingResultRecord'))
      for (const key in columns) {
        columns[key] = columnIndex.toString().padStart(3, '0') + '-' + columns[key]
        columnIndex++
      }
      bodyData.columns = columns
      // 如果入参的条件里面没有值，那么就把入参条件给删掉，传空对象
      Object.keys(bodyData).forEach((key) => {
        if (
          bodyData[key] === '' ||
          (Array.isArray(bodyData[key]) && bodyData[key].length === 0)
        ) {
          delete bodyData[key]
        }
      })
      apiExportExcel(bodyData)
        .then((res) => {
          if (res.type && res.type.indexOf('application/json') > -1) {
            new Blob([res]).text().then((text) => {
              const resJson = JSON.parse(text)
              this.$message.error(resJson.msg)
            })
            this.downloadLoading = false
            return
          }
          downloadFile(res, '2D下载Mapping信息', 'xlsx')
          this.downloadLoading = false
        })
        .catch(() => {
          this.downloadLoading = false
        })
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
