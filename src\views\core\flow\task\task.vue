<template>
  <el-container>
    <el-header style="height: 0px;padding:0px;text-align:left">
      <div class="flow_header">
        {{ flow_task.flow_main_des }}
        <el-popover placement="bottom" width="400" trigger="hover">
          <el-form ref="form" label-width="80px" label-position="left" size="small">
            <el-form-item :label="$t('lang_pack.taskScheduling.taskName') + ':'">
              {{ flow_task.flow_main_des }}
            </el-form-item>
            <el-form-item :label="$t('lang_pack.mainmain.taskNumber') + ':'">
              {{ flow_task.task_num }}
            </el-form-item>
            <el-form-item :label="$t('lang_pack.mainmain.creationDate') + ':'">
              {{ flow_task.start_date }}
            </el-form-item>
            <el-form-item :label="$t('lang_pack.mainmain.taskInfo') + ':'">
              {{ flow_task.task_info }}
            </el-form-item>
          </el-form>
          <i id="zoomOut" slot="reference" class="el-icon-info" style="cursor: pointer;font-size:20px;" :title="$t('lang_pack.mainmain.detailInfo')" />
        </el-popover>
        <span v-if="btnRemind" class="flashing" @click="handleCloseRemind">
          <i class="el-icon-message-solid" style="cursor: pointer;" :title="$t('lang_pack.mainmain.pendEvents')" />
        </span>
        <span style="float:right;margin-right:20px;font-size:20px;width:30px;text-align:center;cursor: pointer;" @click="$emit('closeDrawer')">
          <i class="el-icon-close" />
        </span>
      </div>
    </el-header>
    <el-main>
      <el-scrollbar ref="scrollbar" :width="'100%'" :height="height">
        <Flowchart ref="chart" v-loading="loading" :nodes="nodes" :connections="connections" :width="'100%'" :height="height" :readonly="true" :element-loading-text="$t('lang_pack.mainmain.strDrawFlow')" @editnode="handleEditNode" @openTagMonitor="openTagMonitor" />
      </el-scrollbar>
      <RcsFlowSub ref="rcsFlowSub" :readonly="true" :visible.sync="rcsFlowSubVisible" :node-form.sync="nodeForm.target" />
      <RcsFlowStep ref="rcsFlowStep" :nodes="nodes" :flow_main_id="flow_main_id" :readonly="true" :visible.sync="rcsFlowSubStepVisible" :node-form.sync="nodeForm.target" :cel_api="cel_api" :flow_mod_main_id="flow_task.flow_mod_main_id" :me_flow_task_id="flow_task.me_flow_task_id" />
      <el-dialog :title="step_mod_des + $t('lang_pack.mainmain.attrMomitor')" :visible.sync="tagMonitorDialogVisible" top="10px" width="60%" class="abow_dialog" append-to-body :show-close="true" :close-on-press-escape="false" :close-on-click-modal="true">
        <Monitor v-if="tagMonitorDialogVisible" ref="Monitor" :cel_api="cel_api" :mqtt_url="mqtt_url" :step_mod_id="step_mod_id" :flow_main_id="flow_task.flow_main_id" />
      </el-dialog>
    </el-main>
  </el-container>
</template>
<script>
/* eslint-disable no-unused-vars */

import axios from 'axios'
import Cookies from 'js-cookie'
import Flowchart from '@/components/FlowChart/index'
import RcsFlowSub from '@/views/core/flow/task/sub'
import RcsFlowStep from '@/views/core/flow/task/step'
import Monitor from '@/views/core/flow/task/monitor'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
import { sel } from '@/api/core/flow/rcsFlowModMain'
export default {
  components: {
    Flowchart,
    RcsFlowSub,
    RcsFlowStep,
    Monitor
  },
  props: {
    flow_task: {
      type: Object,
      default: -1
    },
    cel_api: {
      type: String,
      default: ''
    },
    mqtt_url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight,
      nodes: [],
      connections: [],
      nodeForm: { target: null },
      connectionForm: { target: null, operation: null },
      loading: false,
      dialogLabel: false,
      flow_label: '',
      flowLabelPlaceholder: '',
      rcsFlowSubVisible: false,
      rcsFlowSubStepVisible: false,
      scrollLeft: 0,
      scrollTop: 0,
      flow_main_id: 4,
      flow_mod_main_id: 2,
      flow_mod_main_des: '',
      timer: '',
      tagMonitorDialogVisible: false,
      step_mod_id: '',
      step_mod_des: '',
      btnRemind: false,
      btnRemindEnable: true
    }
  },
  mounted() {
    const that = this
    this.timer = setInterval(this.getFlowStatus, 3000)
    this.handleScroll()
    window.onresize = () => {
      return (() => {
        that.height = document.documentElement.clientHeight
      })()
    }
  },
  created() {
    this.LoadFlowChart()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    handleScroll() {
      const scrollbarEl = this.$refs.scrollbar.wrap
      scrollbarEl.onscroll = () => {
        this.scrollLeft = scrollbarEl.scrollLeft
        this.scrollTop = scrollbarEl.scrollTop
      }
    },
    handleEditNode(node, thisH,flowPathId) {
      this.nodeForm.target = node
      if (node.type === 'sub') {
        this.$refs.rcsFlowSub.editNode(node, thisH, this.flow_mod_main_id)
        this.rcsFlowSubVisible = true
      } else if (node.type === 'step' || node.type === 'judge') {
        this.$refs.rcsFlowStep.editNode(node, thisH, this.flow_mod_main_id,flowPathId)
        this.rcsFlowSubStepVisible = true
      }
    },
    LoadFlowChart() {
      this.loading = true
      sel({
        user_name: Cookies.get('userName'),
        flow_mod_main_id: this.flow_task.flow_mod_main_id
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.flow_mod_main_des = defaultQuery.data[0].flow_mod_main_des
              if (defaultQuery.data[0].flow_mod_chart !== '') {
                const flow_chart = JSON.parse(defaultQuery.data[0].flow_mod_chart)
                console.log(flow_chart)
                this.nodes = flow_chart.nodes
                this.connections = flow_chart.connections
                this.getFlowStatus()
              } else {
                this.nodes = []
                this.connections = []
              }
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.mainmain.abnormalData'),
            type: 'error'
          })
          this.nodes = []
          this.connections = []
          this.loading = false
        })
    },
    confirmDialogClose() {
      if (this.btnRemindEnable) {
        this.btnRemind = true
      }
    },
    handleCloseRemind() {
      this.btnRemind = false
      this.getFlowStatus()
    },
    getFlowStatus() {
      var method = '/cell/core/flow/CoreFlowTaskStepStatusSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cel_api.split(':')[1] + method
        // path = 'http://*************:' +8089  + method
      } else {
        path = 'http://' + this.cel_api + method
        // path = 'http://*************:' +8089  + method
      }
      const data = {
        me_flow_task_id: this.flow_task.me_flow_task_id
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0) {
            const flowStatusList = JSON.parse(defaultQuery.data.result)
            for (let index = 0; index < flowStatusList.sub.length; index++) {
              const color = flowStatusList.sub[index].color
              this.nodes.filter(item => item.subId_stepId === flowStatusList.sub[index].flow_mod_sub_id && item.type === 'sub')[0].color = color
            }
            for (let index = 0; index < flowStatusList.step.length; index++) {
              const color = flowStatusList.step[index].color
              this.nodes.filter(item => item.subId_stepId === flowStatusList.step[index].step_mod_id && (item.type === 'step' || item.type === 'judge'))[0].color = color
            }
          } else {
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.stepLogData = []
          this.$message({ message:this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    },
    openTagMonitor(id, name) {
      this.step_mod_id = id
      this.step_mod_des = name
      this.tagMonitorDialogVisible = true
    }
  }
}
</script>
<style scoped>
.flow_header {
  width: 100%;
  color: #000000;
  background-color: rgb(223, 228, 237, 0.5);
  position: fixed;
  z-index: 999;
  font-size: 18px;
  line-height: 35px;
  padding-left: 10px;
}
.el-header {
  background-color: #f6f6f6;
  color: #333;
  font-weight: 600;
  font-size: 30px;
  text-align: center;
  line-height: 60px;
}

.el-main {
  background-color: #f1f1f1;
  color: #333;
  padding: 0px;
}
@keyframes hzfirst {
  from {
    background-color: #d27732;
  }
  to {
    background-color: rgba(253, 225, 148, 0.61);
  }
}
.flashing {
  text-align: center;
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  /*设置圆角*/
  border-radius: 50px;
  /*默认背景颜色*/
  background-color: #d27732;
  /*动画元素名-动画整个过程的时间*/
  animation: hzfirst 1.5s;
  /*动画次数*/
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite; /*Safari and Chrome*/
}
</style>
