<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <!--查询条件-->
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
          <el-form ref="query" :inline="true" size="mini" style="margin: 0; padding: 0">
            <el-form-item label="实例" style="margin: 0px 0px 5px 0px">
              <el-select v-model="query.client_code" filterable size="mini" style="width: 300px" @change="clientCodeSelect">
                <el-option v-for="(item,index) in scadaClientLov" :key="index" :label="item.client_des" :value="item.client_code">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.client_code }}</span>
                  <span style="float: right">{{ item.client_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签组" style="margin: 0px 0px 5px 0px">
              <el-select v-model="query.tag_group_code" clearable size="mini" style="width: 200px" @change="tagGroupSelect">
                <el-option v-for="item in scadaTagGroupLov" :key="item.tag_group_code" :label="item.tag_group_des" :value="item.tag_group_code">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.tag_group_code }}</span>
                  <span style="float: right">{{ item.tag_group_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签" style="margin: 0px 0px 5px 0px">
              <el-select v-model="query.tag_id" clearable size="mini" style="width: 200px">
                <el-option v-for="item in scadaTagLov" :key="item.tag_id" :label="item.tag_des" :value="item.tag_id">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.tag_id }}</span>
                  <span style="float: right">{{ item.tag_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围" style="margin: 0px 0px 5px 0px">
              <el-date-picker
                ref="timepicker"
                v-model="query.create_time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 350px"
                align="right"
                :picker-options="pickerOptions"
              />
            </el-form-item>
            <el-form-item style="margin: 0;float:right">
              <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="btnQuery('search','','')">搜索
              </el-button>
              <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="btnQuery('reset','','')">重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!--查询结果-->
        <el-card shadow="always" style="margin-top: 10px">
          <el-tabs v-model="tabValue" :tab-position="tabPosition" style="height:500px;" @tab-click="tabSelect">
            <el-tab-pane name="tableTab"><span slot="label"><i class="el-icon-s-grid" /> 表格形式</span>
              <el-card>
                <el-table
                  ref="tableReport"
                  v-loading="loadingReport"
                  :data="dataReport"
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F4F7', color: '#757575' }"
                  border
                  height="450px"
                  :highlight-current-row="true"
                >
                  <el-table-column  type="expand">
                    <template slot-scope="scope">
                      <el-table
                        ref="tableCReport"
                        :data="scope.row.read_list"
                        style="width: 100%;"
                        :header-cell-style="{ background: '#F1F4F7', color: '#757575' }"
                        border
                        height="250px"
                        :highlight-current-row="true"
                      >
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="tag_id" label="tagId" />
                        <el-table-column  :show-overflow-tooltip="true" width="200" prop="tag_only_key" label="标签唯一键" />
                        <el-table-column  :show-overflow-tooltip="true" width="200" prop="tag_des" label="标签描述" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="150" prop="read_val" label="读取值" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="down_limit" label="下限" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="upper_limit" label="上限" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="data_type" label="数据类型" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="block_name" label="区域" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="block_addr" label="区域地址" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="start_address" label="起始地址" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="data_length" label="数据长度" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="data_bit" label="位" />
                        <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="opc_addr" label="OPC地址" />
                      </el-table>
                    </template>
                  </el-table-column>
                  <el-table-column  v-if="1 == 1" width="200" prop="id" label="id" />
                  <el-table-column  :show-overflow-tooltip="true" width="200" prop="create_time" label="时间" />
                  <el-table-column  :show-overflow-tooltip="true" width="150" prop="device_code" label="实例编码" />
                  <el-table-column  :show-overflow-tooltip="true" width="200" prop="device_des" label="实例描述" />
                  <el-table-column  :show-overflow-tooltip="true" width="150" prop="group_code" label="标签组编码" />
                  <el-table-column  :show-overflow-tooltip="true" width="150" prop="group_des" label="标签组描述" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="fastpic_time" label="快照时间(s)" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="simulated_flag" label="模拟标识" />
                </el-table>
                <el-row :gutter="20">
                  <el-col :span="4" :offset="10">
                    <div style="margin-top: 5px">
                      <el-button-group>
                        <el-button type="primary" icon="el-icon-arrow-left" @click="pageQuery('pre')">上一页</el-button>
                        <el-button type="primary" @click="pageQuery('next')">下一页<i class="el-icon-arrow-right el-icon--right" /></el-button>
                      </el-button-group>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-tab-pane>
            <el-tab-pane name="chartTab"><span slot="label"><i class="el-icon-s-data" /> 曲线形式</span>
              <el-card>
                <ECharts ref="chartReport" v-loading="loadingChartReport" :options="chartOptions" style="width: 1200px;height:450px;" />
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<!--JS-->
<script>
// 导入
import { selCellIP } from '@/api/core/center/cell'
import { InitTimePickRange, ScadaClientSelect, ScadaTagGroupSelect, ScadaTagSelect } from '@/api/core/scada/report'
import ECharts from 'vue-echarts'
import Cookies from 'js-cookie'
import axios from 'axios'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'

// 执行
export default {
  name: 'REPORT_SCADA_FASTPIC',
  // 1.初始化组件
  components: {
    ECharts
  },
  // 2.初始化参数设置
  data() {
    return {
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      query: {
        client_code: '',
        tag_group_code: '',
        tag_id: '',
        create_time: null
      },
      scadaClientLov: [],
      scadaTagGroupLov: [],
      scadaTagLov: [],
      tabPosition: 'left', // Tab方向
      tabValue: 'tableTab', // 当前Tab选择
      dataReport: [], // 表格数据
      loadingReport: false, // 表格是否正在加载
      loadingChartReport: false,
      tableSize: 100, // 默认表格单次查询100行数据
      chartOptions: {},
      pickerOptions: {}
    }
  },
  // 3.页面创建时加载
  created: function() {
    // 初始化日期选择器快捷选项
    this.pickerOptions = createDatePickerShortcuts(this.$i18n)

    // Scada实例查询
    ScadaClientSelect().then((res) => {
      var result = JSON.parse(JSON.stringify(res))
      if (result.hasOwnProperty('error')) {
        this.$message({
          message: 'Scada实例基础数据查询失败:' + result.error,
          type: 'error'
        })
        this.scadaClientLov = []
        return
      }
      this.scadaClientLov = result.data
    }).catch(() => {
      this.$message({
        message: 'Scada实例基础数据查询超时或者SQL错误',
        type: 'error'
      })
    })
    // 自动加载开始时间结束时间(默认7天以内)
    this.query.create_time = InitTimePickRange(3)
  },
  // 5.页面渲染
  mounted() {
    // 曲线图
    this.chartOptions = {
      title: {
        text: '标签数据曲线分析',
        subtext: '',
        left: 'center'
      },
      dataZoom: [{
        type: 'inside'
      }, {
        type: 'slider'
      }],
      tooltip: {
        showDelay: 0,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        name: '时间',
        type: 'category',
        data: []
      },
      yAxis: {
        name: '标签值',
        type: 'value'
      },
      series: [{
        type: 'line',
        data: [],
        itemStyle: {
          normal: {
            color: 'blue'
          }
        }
      }]
    }
  },
  // 4.页面执行方法事件
  methods: {
    // select change方法
    // 0.1 实例选择
    clientCodeSelect() {
      this.query.tag_group_code = ''
      this.query.tag_id = ''
      this.scadaTagGroupLov = []
      this.scadaTagLov = []
      if (this.query.client_code === '') {
        return
      }
      ScadaTagGroupSelect(this.query.client_code).then((res) => {
        var result = JSON.parse(JSON.stringify(res))
        if (result.hasOwnProperty('error')) {
          this.$message({
            message: 'Scada实例标签组数据查询失败:' + result.error,
            type: 'error'
          })
          return
        }
        this.scadaTagGroupLov = result.data
      }).catch(() => {
        this.$message({
          message: 'Scada实例标签组数据查询超时或者SQL错误',
          type: 'error'
        })
      })
      this.getCellIp()
    },
    getCellIp() {
      const cell_id = this.scadaClientLov.filter(a => a.client_code === this.query.client_code)[0].cell_id
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询selCellIP异常', type: 'error' })
        })
    },
    // 0.2 标签组选择
    tagGroupSelect() {
      this.query.tag_id = ''
      this.scadaTagLov = []
      if (this.query.client_code === '' || this.query.tag_group_code === '') {
        return
      }
      ScadaTagSelect(this.query.client_code, this.query.tag_group_code).then((res) => {
        var result = JSON.parse(JSON.stringify(res))
        if (result.hasOwnProperty('error')) {
          this.$message({
            message: 'Scada实例标签数据查询失败:' + result.error,
            type: 'error'
          })
          return
        }
        this.scadaTagLov = result.data
      }).catch(() => {
        this.$message({
          message: 'Scada实例标签数据查询超时或者SQL错误',
          type: 'error'
        })
      })
    },
    // 4.1 查询方法
    btnQuery(code, pageDirct, pageId) {
      if (code === 'reset') { // 重置
        this.query.client_code = ''
        this.query.tag_group_code = ''
        this.query.tag_id = ''
        this.scadaTagGroupLov = []
        this.scadaTagLov = []
        this.query.create_time = InitTimePickRange(3)
      } else {
        if (this.query.client_code === '') {
          this.$message({
            message: '请选择实例',
            type: 'info'
          })
          return
        }
        var start_time = this.query.create_time === null ? '' : this.query.create_time[0]
        var end_time = this.query.create_time === null ? '' : this.query.create_time[1]
        this.loadingReport = true
        if (this.cellIp === '' || this.webapiPort === '') {
          this.$message({
            message: '未获取到单元IP与端口号',
            type: 'info'
          })
          return
        }
        var method = '/cell/core/scada/CoreScadaFastPicReportSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        // A:表格刷新
        const data = {
          device_code: this.query.client_code,
          group_code: this.query.tag_group_code,
          start_time: start_time,
          end_time: end_time,
          tableSize: this.tableSize,
          page_dirct: pageDirct,
          page_id: pageId
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0) {
              if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                if (pageId !== '') {
                  this.$message({
                    message: '已到顶无数据',
                    type: 'info'
                  })
                } else {
                  this.dataReport = []
                }
                this.loadingReport = false
                return
              }
              this.dataReport = defaultQuery.data.data
              this.loadingReport = false
            } else {
              this.$message({
                message: 'Scada数据快照报表查询失败:' + defaultQuery.data.msg,
                type: 'error'
              })
              this.dataReport = []
              this.loadingReport = false
            }
          })
          .catch(ex => {
            this.loadingReport = false
            this.$message({
              message: 'Scada数据快照报表查询超时或者SQL错误',
              type: 'error'
            })
          })
        // B:曲线刷新
        if (code === 'search') {
          this.loadingChartReport = true
          if (this.query.tag_group_code === '' || this.query.tag_id === '') {
            this.chartOptions = {}
            this.loadingChartReport = false
            return
          }
          const data1 = {
            device_code: this.query.client_code,
            group_code: this.query.tag_group_code,
            start_time: start_time,
            end_time: end_time,
            tableSize: '',
            page_dirct: '',
            page_id: ''
          }
          axios
            .post(path, data1, {
              headers: {
                'Content-Type': 'application/json'
              }
            })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.data.code === 0) {
                if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                  this.chartOptions = {}
                  this.loadingChartReport = false
                  return
                }
                var arrayData = defaultQuery.data.data
                var xList = []
                var yList = []
                var upperLimit = -1
                var downLimit = -1
                var tagDes = ''
                for (var i = 0; i < arrayData.length; i++) {
                  var create_time = arrayData[i].create_time
                  var read_list = arrayData[i].read_list
                  var tagId2 = this.query.tag_id
                  var itemJson = read_list.filter(function(item) { return item.tag_id === tagId2 })
                  if (itemJson != null && itemJson.length > 0) {
                    var read_val = itemJson[0].read_val
                    if (read_val !== '' && read_val !== undefined) {
                      var tValue = parseFloat(read_val)
                      if (!isNaN(tValue)) {
                        xList.push(create_time)
                        yList.push(tValue)
                        if (tagDes === '') {
                          tagDes = itemJson[0].tag_des
                          upperLimit = itemJson[0].upper_limit
                          downLimit = itemJson[0].down_limit
                        }
                      }
                    }
                  }
                }
                // 曲线图
                this.chartOptions = {
                  title: {
                    text: '标签数据曲线分析',
                    subtext: tagDes,
                    left: 'center'
                  },
                  dataZoom: [{
                    type: 'inside'
                  }, {
                    type: 'slider'
                  }],
                  tooltip: {
                    showDelay: 0,
                    trigger: 'axis',
                    axisPointer: {
                      type: 'shadow'
                    }
                  },
                  xAxis: {
                    name: '时间',
                    type: 'category',
                    data: xList
                  },
                  yAxis: {
                    name: '标签值',
                    type: 'value'
                  },
                  series: [{
                    type: 'line',
                    data: yList,
                    itemStyle: {
                      normal: {
                        color: 'blue'
                      }
                    },
                    markPoint: {
                      data: [
                        { type: 'max', name: 'Max' },
                        { type: 'min', name: 'Min' }
                      ]
                    },
                    markLine: {
                      silent: true,
                      lineStyle: {
                        color: '#FC7D02'
                      },
                      data: [{
                        yAxis: downLimit
                      },
                      {
                        yAxis: upperLimit
                      }
                      ]
                    }
                  }]
                }
                this.loadingChartReport = false
              } else {
                this.$message({
                  message: 'Scada数据快照报表查询失败:' + defaultQuery.data.msg,
                  type: 'error'
                })
                this.chartOptions = {}
                this.loadingChartReport = false
              }
            }).catch(() => {
              this.loadingChartReport = false
              this.$message({
                message: 'Scada数据快照报表查询超时或者SQL错误',
                type: 'error'
              })
            })
        }
      }
    },
    // 4.2 Tab切换
    tabSelect(tab, event) {},
    // 4.3 上一页或者下一页翻页
    pageQuery(pageDirct) {
      if (this.dataReport == null || this.dataReport.length <= 0) {
        this.$message({
          message: '当前无数据',
          type: 'info'
        })
        return
      }
      if (pageDirct === 'pre') {
        this.btnQuery('tablePage', pageDirct, this.dataReport[0].id)
      } else {
        this.btnQuery('tablePage', pageDirct, this.dataReport[this.dataReport.length - 1].id)
      }
    }
  }
}
</script>
<!--CSS-->
<style lang="scss" >
.box-card {min-height: calc(100vh);padding: 10px;}
:focus {outline: 0;}
.el-card__body {padding: 10px;}
</style>
