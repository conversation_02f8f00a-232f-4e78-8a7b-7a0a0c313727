<template>
  <el-row :gutter="20" class="el-row">
    <el-col :span="24">
      <!--标签组明细-->
      <el-drawer append-to-body :wrapper-closable="false" :title="dialogTitleGroupFrom" :visible.sync="dialogVisbleSyncGroupFrom" size="650px" @closed="drawerClose">
        <el-form ref="formGroup" class="el-form-wrap" :model="formGroup" :rules="rulesGroup" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.logicattr.childCode')" prop="logic_attr_item_code">
            <!-- 子编码 -->
            <el-input v-model="formGroup.logic_attr_item_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.childDescription')" prop="logic_attr_item_des">
            <!-- 子描述 -->
            <el-input v-model="formGroup.logic_attr_item_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.attribute1')" prop="attribute1">
            <!-- 属性1 -->
            <el-input v-model="formGroup.attribute1" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.attribute2')" prop="attribute2">
            <!-- 属性2 -->
            <el-input v-model="formGroup.attribute2" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.attribute3')" prop="attribute3">
            <!-- 属性3 -->
            <el-input v-model="formGroup.attribute3" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.attribute4')" prop="attribute4">
            <!-- 属性4 -->
            <el-input v-model="formGroup.attribute4" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.attribute5')" prop="attribute5">
            <!-- 属性5 -->
            <el-input v-model="formGroup.attribute5" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="formGroup.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromGroupFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormGroupFromSubmit('formGroup')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="tableGroup" v-loading="listLoadingGroupTable" size="small" :data="tableDataGroupTable" style="width: 100%" :height="height" highlight-current-row @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="logic_attr_item_code" :label="$t('lang_pack.logicattr.childCode')" sortable="custom" />
        <!-- 子编码 -->
        <el-table-column  :show-overflow-tooltip="true" prop="logic_attr_item_des" :label="$t('lang_pack.logicattr.childDescription')" sortable="custom" />
        <!-- 子描述 -->

        <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100" sortable="custom">
          <!-- 有效标识 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toGroupTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toGroupTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>

      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageGroupTable.size"
        :total="pageGroupTable.total"
        :current-page.sync="pageGroupTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeGroupTableChangeHandler($event)"
        @current-change="toPageGroupTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>

<script>
import Cookies from 'js-cookie'
import { selLogicAttrItem, insLogicAttrItem, updLogicAttrItem, delLogicAttrItem } from '@/api/core/logic/logicAttrItem'

export default {
  name: 'ATTRITEM',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      query: {
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'logic_attr_item_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      // 子
      // FROM:弹窗(新增、修改明细)
      dialogVisbleSyncGroupFrom: false,
      dialogTitleGroupFrom: '',

      // 新增&修改(初始化数据)
      formGroup: {
        logic_attr_item_id: '',
        logic_attr_group_id: '',
        logic_attr_item_code: '',
        logic_attr_item_des: '',
        attribute1: '',
        attribute2: '',
        attribute3: '',
        attribute4: '',
        attribute5: '',
        enable_flag: 'Y'
      },
      rulesGroup: {
        // 提交验证规则
        logic_attr_item_code: [{ required: true, message: '请输入逻辑属性子编码', trigger: 'blur' }]
      },
      // Table
      listLoadingGroupTable: false,
      popGroupTableBut: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataGroupTable: [],

      pageGroupTable: {
        // 页码
        page: 0,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      itemCodeDes: ''
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // 查询
    this.toButQuery()
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['formGroup'].resetFields()
    },
    // 子
    toButAdd() {
      // 新增
      this.formGroup.logic_attr_item_id = ''
      this.formGroup.logic_attr_item_code = ''
      this.formGroup.logic_attr_item_des = ''
      this.formGroup.attribute1 = ''
      this.formGroup.attribute2 = ''
      this.formGroup.attribute3 = ''
      this.formGroup.attribute4 = ''
      this.formGroup.attribute5 = ''
      this.formGroup.enable_flag = 'Y'
      this.dialogVisbleSyncGroupFrom = true // 新增弹出框
      this.dialogTitleGroupFrom = '新增逻辑属性'
    },

    toButQuery(LogicAttrGroupId, queryContent) {
      // 查询
      if (LogicAttrGroupId === undefined) {
        this.formGroup.logic_attr_group_id = this.$parent.$attrs.logicattrgroupid
      } else {
        this.formGroup.logic_attr_group_id = LogicAttrGroupId
      }
      if (queryContent !== undefined) {
        this.itemCodeDes = queryContent
      }
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName'),
        itemCodeDes: this.itemCodeDes,
        logic_attr_group_id: this.formGroup.logic_attr_group_id,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      this.listLoadingGroupTable = true
      selLogicAttrItem(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataGroupTable = defaultQuery.data
            } else {
              this.tableDataGroupTable = []
            }
            this.pageGroupTable.total = defaultQuery.count
            this.listLoadingGroupTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    toFromGroupFromCancel() {
      // 取消
      this.dialogVisbleSyncGroupFrom = false // 弹出框隐藏
    },
    toFormGroupFromSubmit() {
      // 确定(修改)
      this.$refs['formGroup'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            logic_attr_item_id: this.formGroup.logic_attr_item_id,
            logic_attr_group_id: this.formGroup.logic_attr_group_id,
            logic_attr_item_code: this.formGroup.logic_attr_item_code,
            logic_attr_item_des: this.formGroup.logic_attr_item_des,
            attribute1: this.formGroup.attribute1,
            attribute2: this.formGroup.attribute2,
            attribute3: this.formGroup.attribute3,
            attribute4: this.formGroup.attribute4,
            attribute5: this.formGroup.attribute5,
            enable_flag: this.formGroup.enable_flag
          }
          // 新增
          if (this.formGroup.logic_attr_item_id === undefined || this.formGroup.logic_attr_item_id.length <= 0) {
            insLogicAttrItem(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  this.dialogVisbleSyncGroupFrom = false
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updLogicAttrItem(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  this.dialogVisbleSyncGroupFrom = false
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },

    toGroupTableButEdit(data) {
      // Table编辑(单笔)
      this.formGroup.logic_attr_item_id = data.logic_attr_item_id
      this.formGroup.logic_attr_group_id = data.logic_attr_group_id
      this.formGroup.logic_attr_item_code = data.logic_attr_item_code
      this.formGroup.logic_attr_item_des = data.logic_attr_item_des
      this.formGroup.attribute1 = data.attribute1
      this.formGroup.attribute2 = data.attribute2
      this.formGroup.attribute3 = data.attribute3
      this.formGroup.attribute4 = data.attribute4
      this.formGroup.attribute5 = data.attribute5
      this.formGroup.enable_flag = data.enable_flag
      this.dialogVisbleSyncGroupFrom = true // 修改弹出框
      this.dialogTitleGroupFrom = '修改逻辑属性'
    },
    toGroupTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除逻辑属性子编码${data.logic_attr_item_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            logic_attr_item_id: data.logic_attr_item_id
          }
          delLogicAttrItem(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                // 查询
                this.toButQuery()
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeGroupTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageGroupTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'logic_attr_item_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-drawer {
  overflow-y: scroll;
}
</style>
