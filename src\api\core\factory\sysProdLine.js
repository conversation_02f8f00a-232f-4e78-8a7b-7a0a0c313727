import request from '@/utils/request'

//查询生产线
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryProdLineSel',
    method: 'post',
    data
  })
}
//新增生产线
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryProdLineIns',
    method: 'post',
    data
  })
}
//修改生产线
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryProdLineUpd',
    method: 'post',
    data
  })
}
//删除生产线
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryProdLineDel',
    method: 'post',
    data
  })
}

//查询生产线LOV
export function lovProdLine(data) {
  return request({
    url: 'aisEsbWeb/core/factory/CoreFactoryProdLineLov',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, lovProdLine}
