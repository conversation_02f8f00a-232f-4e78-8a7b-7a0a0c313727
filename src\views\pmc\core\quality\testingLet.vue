<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" class="wrapCard" shadow="never">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-9 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="车辆VIN：">  <!-- 车辆VIN： -->
                <el-input v-model="query.vin" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-3 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions :column="2" size="small" border>
                  <el-descriptions-item label="上报结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.report_result }}</el-descriptions-item>
                  <el-descriptions-item label="车辆排放阶段" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.chk_section }}</el-descriptions-item>
                  <el-descriptions-item label="车辆信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.vehicle_data }}</el-descriptions-item>
                  <el-descriptions-item label="环境参数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.envir_data }}</el-descriptions-item>
                  <el-descriptions-item label="检测信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.test_data }}</el-descriptions-item>
                  <el-descriptions-item label="OBD数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.obd_data }}</el-descriptions-item>
                  <el-descriptions-item label="结果数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.result_data }}</el-descriptions-item>
                  <el-descriptions-item label="设备数据" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.device_data }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="vin" label="VIN号" />  <!-- 车辆VIN -->
            <el-table-column  :show-overflow-tooltip="true" prop="obd_chk_date" label="OBD检测日期" />  <!-- OBD检测日期 -->
            <el-table-column  :show-overflow-tooltip="true" prop="dis_chk_date" label="排放检测日期" />  <!-- 排放检测日期 -->
            <el-table-column  :show-overflow-tooltip="true" prop="obd_chk_time" label="OBD检测时间" />  <!-- OBD检测时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="dis_chk_time" label="排放检测时间" />  <!-- 排放检测时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="obd_result" label="OBD检测结果" />  <!-- OBD检测结果 -->
            <el-table-column  :show-overflow-tooltip="true" prop="dis_result" label="排放检测结果" />  <!-- 排放检测结果 -->
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import getTestingLet from '@/api/pmc/quality/sysTestingLet'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
const defaultForm = {

}
export default {
  name: 'playTime',
  components: { crudOperation, rrOperation, udOperation },
  cruds() {
    return CRUD({
      title: '测量结果',
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'vin',
      // // 排序
      sort: ['vin asc'],
      // CRUD Method
      crudMethod: { ...getTestingLet },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据字典
  dicts: ['ENGRAVING_TYPE'],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'sys_function:add'],
        edit: ['admin', 'sys_function:edit'],
        del: ['admin', 'sys_function:del'],
        down: ['admin', 'sys_function:down']
      }
    }
  },
  created() {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
    color: #fff;
    background-color: #1473c5;
    border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
    background: #438fd1;
    border-color: #438fd1;
    color: #fff;
}
.labelIline{
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label{
    white-space: nowrap;
  }
}
::v-deep .marginL{
  margin-left: 10px;
}
::v-deep .el-descriptions-item__label.is-bordered-label{
  width: 100px;
}
</style>
