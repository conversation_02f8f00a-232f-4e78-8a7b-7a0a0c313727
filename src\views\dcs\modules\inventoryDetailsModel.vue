<template>
  <el-card shadow="always" style="margin-top: 10px">
    <!--工具栏-->
    <crudOperation show="" :permission="permission" />
    <el-row :gutter="20">
      <el-col :span="24">
        <el-dialog title="库存明细" width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              :label="$t('lang_pack.wmsCbTable.taskNumber')"
              min-width="80"
              align="center"
            />
            <!-- 序列号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              :label="$t('lang_pack.wmsCbTable.serialNumber')"
              min-width="80"
              align="center"
            />
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              :label="$t('lang_pack.wmsCbTable.taskType')"
              min-width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 库位排序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              min-width="100"
              align="center"
              prop="stock_index"
              :label="$t('lang_pack.cuttingZone.LocationSorting')"
            />
            <!-- Z轴坐标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              min-width="100"
              align="center"
              prop="location_z"
              :label="$t('lang_pack.cuttingZone.ZAxisCoordinates')"
            />
            <!-- 是否锁定 -->
            <el-table-column
              :show-overflow-tooltip="true"
              min-width="100"
              align="center"
              prop="lock_flag"
              :label="$t('lang_pack.cuttingZone.IsItLocked')"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.lock_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 是否库存超期报警 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="alarm_flag"
              :label="$t('lang_pack.wmsCbTable.isInventoryAlarm')"
              min-width="110"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.alarm_flag == 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 入库时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              min-width="100"
              align="center"
              prop="stock_d_time"
              :label="$t('lang_pack.cuttingZone.WarehousingTime')"
            />
          </el-table>
          <pagination />
        </el-dialog>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import crudWmsMeStock from '@/api/dcs/core/wmsCbTable/wmsMeStock'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {}
export default {
  name: 'INVENTORYDETAILS',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '库存明细',
      // 唯一字段
      idField: 'stock_d_id',
      // 排序
      sort: ['stock_d_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMeStock },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: false
      },
      query: {
        stock_id: this.propsData.stock_id
      }
    })
  },
  props: {
    stock_id: {
      type: Number
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      dialogVisible: false
    }
  },
  dicts: ['TASK_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 400
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('ok')
    }
  }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
</style>
