<template>
  <!--班次工作时间-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="160px" :inline="true">
        <div class="uploadStyle">
          <el-form-item>
            <div class="uploadStyleone">
              <el-upload ref="upload" :multiple="true" class="upload-demo" action="" drag="" :limit="uploadLimit" :accept="uploadAccept" :on-change="handleImport" :auto-upload="false" :http-request="uploadFile" :on-progress="progressA" :file-list="fileList" name="file">
                <i class="el-icon-upload" />
                <div class="el-upload__text">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
                <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
              </el-upload>
              <div class="btnCenter">
                <el-button type="primary" size="small" icon="el-icon-check" @click="toButDrawerUpload">上传</el-button>
              </div>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="开始时间" prop="start_time">
          <el-time-picker v-model="form.start_time" placeholder="选择时间" format="HH:mm:ss" value-format="HH:mm:ss" />
        </el-form-item>
        <el-form-item label="结束时间" prop="end_time">
          <el-time-picker v-model="form.end_time" placeholder="选择时间" format="HH:mm:ss" value-format="HH:mm:ss" />
        </el-form-item>
        <el-form-item label="音乐类型" prop="music_type">
          <el-input v-model="form.music_type" :disabled="true" />
        </el-form-item>
        <el-form-item label="音乐名称" prop="music_name">
          <el-input v-model="form.music_name" :disabled="true" />
        </el-form-item>
        <el-form-item label="音乐路径" prop="music_path">
          <el-input v-model="form.music_path" :disabled="true" />
        </el-form-item>
        <el-form-item label="音乐播放时间(秒)" prop="music_play_times">
          <el-input v-model="form.music_play_times" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" />
        </el-form-item>
        <el-form-item label="是否工作">
          <el-radio-group v-model="form.work_flag">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否播放音乐">
          <el-radio-group v-model="form.music_flag">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="有效标识">
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"  highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="shift_work_id" label="shift_work_id" />

          <el-table-column  :show-overflow-tooltip="true" prop="start_time" label="开始时间" />
          <el-table-column  :show-overflow-tooltip="true" prop="end_time" label="结束时间" />
          <el-table-column  label="是否工作" align="center" prop="work_flag">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column  label="是否播放音乐" align="center" prop="music_flag" width="120">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="music_type" label="音乐类型" />
          <el-table-column  :show-overflow-tooltip="true" prop="music_name" label="音乐名称" />
          <el-table-column  :show-overflow-tooltip="true" prop="music_path" label="音乐路径" />
          <el-table-column  :show-overflow-tooltip="true" prop="music_play_times" label="音乐播放时间(秒)" width="120" />
          <el-table-column  :show-overflow-tooltip="true" prop="remarks" label="备注" />

          <el-table-column  label="有效标识" align="center" prop="enable_flag" width="100">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>

          <el-table-column  label="操作" width="115" align="center" fixed="right">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import axios from 'axios'
import crudShiftWork from '@/api/mes/core/shiftWork'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  shift_work_id: '',
  shift_id: '',
  start_time: '',
  end_time: '',
  work_flag: 'N',
  music_flag: 'N',
  music_type: '',
  music_name: '',
  music_path: '',
  music_play_times: '',
  remarks: '',
  enable_flag: 'Y'
}
export default {
  name: 'ShiftWork',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    shift_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '班次工作时间',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'shift_work_id',
      // 排序
      sort: ['shift_work_id asc'],
      // CRUD Method
      crudMethod: { ...crudShiftWork },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_fmod_shift_work:add'],
        edit: ['admin', 'sys_fmod_shift_work:edit'],
        del: ['admin', 'sys_fmod_shift_work:del'],
        down: ['admin', 'sys_fmod_shift_work:down']
      },
      rules: {
        // 提交验证规则
        music_name: [{ required: true, message: '请选择文件', trigger: 'blur' }]
      },
      // 上传
      uploadLimit: 1,
      uploadAccept: '.MP3,.wav',
      fileList: []
    }
  },
  watch: {
    shift_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.shift_id = this.shift_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.shift_id = this.shift_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.shift_id = this.shift_id
      return true
    },

    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      console.log('handleImport')
      console.log(file.name)
      console.log(file.path)

      this.fileList = fileList
      if (this.fileList.length === 1) {
        this.form.music_type = file.name.substring(file.name.lastIndexOf('.') + 1)
        this.form.music_name = file.name
        this.form.music_path = file.path
      } else {
        this.form.music_type = ''
        this.form.music_name = ''
        this.form.music_path = ''
      }
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.append('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      this.fileData = new FormData()
      this.fileData.append('file_path', '')
      this.$refs.upload.submit()

      // 配置路径
      var method = '/core/file/CoreFileUpload'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            console.log('文件上传成功')
            console.log('defaultQuery数据')
            console.log(defaultQuery)
            const fileInfo = JSON.parse(defaultQuery.data.result)
            console.log('result数据')
            console.log(fileInfo)
            console.log(fileInfo.file_dir_path)

            this.form.music_path = fileInfo.file_dir_path

            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
    }
  }
}
</script>
<style lang="less" scoped>
.uploadStyle {
  width: 100% !important;
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}
.el-date-editor.el-input {
  width: 100% !important;
}
.btnCenter {
  text-align: center;
}
.el-card {
  border: 0 !important;
  overflow: inherit;
}
</style>
