<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号：">
                <!-- 任务号： -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码:">
                <!-- 物料编码 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务类型:">
                <!-- 任务类型 -->
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.APS_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务状态:">
                <!-- 任务状态 -->
                <el-select v-model="query.task_status" clearable filterable>
                  <el-option
                    v-for="item in dict.PROD_TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.fjrm.taskFrom')" prop="task_from">
                <!-- 任务来源 -->
                <el-select v-model="form.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.DATA_SOURCES"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.taskWay')" prop="task_way">
                <!-- 任务方式 -->
                <el-select v-model="form.task_way" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_WAY"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.taskType')" prop="task_type">
                <!-- 任务类型 -->
                <el-select v-model="form.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.APS_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.wareHouse')" prop="ware_house">
                <!-- 库区域 -->
                <el-select v-model="form.ware_house" clearable filterable>
                  <el-option
                    v-for="item in dict.WARE_HOUSE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.fromStockCode')" prop="from_stock_code">
                <!-- 起始库位 -->
                <el-select v-model="form.from_stock_code" clearable filterable>
                  <el-option
                    v-for="item in stockDataTable"
                    :key="item.stock_code"
                    :label="item.stock_des"
                    :value="item.stock_code"
                  >
                    <span style="float: left">{{ item.stock_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.toStockCode')" prop="to_stock_code">
                <!-- 目标库位 -->
                <el-select v-model="form.to_stock_code" clearable filterable>
                  <el-option
                    v-for="item in stockDataTable"
                    :key="item.stock_code"
                    :label="item.stock_des"
                    :value="item.stock_code"
                  >
                    <span style="float: left">{{ item.stock_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.lotNum')" prop="lot_num">
                <!-- 批次号 -->
                <el-input v-model="form.lot_num" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.materialCode')" prop="material_code">
                <!-- 物料编码 -->
                <el-input v-model="form.material_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.width')" prop="width">
                <!-- 总重量(KG) -->
                <el-input v-model.number="form.width" type="number" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.errorMin')" prop="error_min">
                <!-- 化学成分异常最小值 -->
                <el-input v-model.number="form.error_min" type="number" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.errorMax')" prop="error_max">
                <!-- 化学成分异常最大值 -->
                <el-input v-model.number="form.error_max" type="number" />
              </el-form-item>

              <!--任务排序
              <el-form-item :label="$t('lang_pack.fjrm.taskOrder')" prop="task_order">
                <el-input v-model.number="form.task_order" type="number" />
              </el-form-item>-->
              <el-form-item :label="$t('lang_pack.fjrm.wharfCode')" prop="wharf_code">
                <!-- 码头编码 -->
                <el-input v-model="form.wharf_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.wasteBoxCode')" prop="waste_box_code">
                <!-- 废料框编码 -->
                <el-input v-model="form.waste_box_code" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed />

            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              :label="$t('lang_pack.fjrm.taskNum')"
              width="180"
              align="center"
            />
            <!-- 任务状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_status"
              :label="$t('lang_pack.fjrm.taskStatus')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <!-- 排序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_order"
              :label="$t('lang_pack.fjrm.taskOrder')"
              width="80"
              align="center"
            />

            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.fjrm.taskFrom')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 任务方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_way"
              :label="$t('lang_pack.fjrm.taskWay')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_WAY[scope.row.task_way] }}
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              :label="$t('lang_pack.fjrm.taskType')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.APS_TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 库区域 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ware_house"
              :label="$t('lang_pack.fjrm.wareHouse')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
              </template>
            </el-table-column>
            <!-- 起始库位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_stock_code"
              :label="$t('lang_pack.fjrm.fromStockCode')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStockDes(scope.row.from_stock_code) }}
              </template>
            </el-table-column>
            <!-- 目标库位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_stock_code"
              :label="$t('lang_pack.fjrm.toStockCode')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStockDes(scope.row.to_stock_code) }}
              </template>
            </el-table-column>
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('lang_pack.fjrm.lotNum')"
              width="80"
              align="center"
            />
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.fjrm.materialCode')"
              width="80"
              align="center"
            />
            <!-- 总重量(KG) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="width"
              :label="$t('lang_pack.fjrm.width')"
              width="80"
              align="center"
            />
            <!-- 化学成分异常最小值 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="error_min"
              :label="$t('lang_pack.fjrm.errorMin')"
              width="80"
              align="center"
            />
            <!-- 化学成分异常最大值 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="error_max"
              :label="$t('lang_pack.fjrm.errorMax')"
              width="80"
              align="center"
            />
            <!-- 码头编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wharf_code"
              :label="$t('lang_pack.fjrm.wharfCode')"
              width="80"
              align="center"
            />
            <!-- 废料框编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_code"
              :label="$t('lang_pack.fjrm.wasteBoxCode')"
              width="150"
              align="center"
            />

            <!-- 拆分源任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_task_num"
              label="拆分源任务号"
              width="150"
              align="center"
            />
            <!-- 拆分总重量(KG) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_width"
              label="拆分总重量"
              width="150"
              align="center"
            />

            <el-table-column
              :label="$t('lang_pack.fjrm.lockFlag')"
              align="center"
              width="120"
              prop="lock_flag"
            >
              <!-- 是否锁定 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.lock_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" />
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="180"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" :delete-del="false" :delete-edit="false">
                  <template slot="right">
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      :disabled="scope.row.task_status != 'PLAN'"
                      @click="taskStatusExecute(scope.row.task_id,'WORK')"
                    >执行</el-button>
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      :disabled="scope.row.task_status != 'PLAN' "
                      @click="taskStatusExecute(scope.row.task_id,'CANCEL')"
                    >取消</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudTask from '@/api/dcs/project/fjrm/intefTask/intefTask'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  task_id: '',
  task_num: '',
  task_from: '',
  task_way: '',
  task_type: '',
  ware_house: '',
  from_stock_code: '',
  to_stock_code: '',
  lot_num: '',
  material_code: '',
  width: '',
  error_min: '',
  error_max: '',
  task_order: '',
  task_status: '',
  wharf_code: '',
  waste_box_code: '',
  split_task_num: '',
  split_width: '',
  lock_flag: 'N'
}
export default {
  name: 'INTEF_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'task_id',
      // 排序
      sort: ['task_id desc'],
      // CRUD Method
      crudMethod: { ...crudTask },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'dcs_wms_intef_task :add']
      },
      rules: {
        task_from: [{ required: true, message: '请选择任务来源', trigger: 'blur' }],
        task_way: [{ required: true, message: '请选择任务方式', trigger: 'blur' }],
        task_type: [{ required: true, message: '请选择任务类型', trigger: 'blur' }]
      },

      // 库位
      stockDataTable: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'APS_TASK_TYPE', 'DATA_SOURCES', 'TASK_WAY', 'WARE_HOUSE', 'PROD_TASK_STATUS'],
  mounted: function() {
    const that = this
    that.crud.props.searchToggle = false // 默认先隐藏
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 300
    }
  },
  created: function() {
    this.getSelStock()
  },
  methods: {

    // 库位
    getSelStock() {
      const query = {
        user_name: Cookies.get('userName'),
        ware_house: '',
        exclude_stock_code: '',
        enable_flag: 'Y'
      }
      crudTask.stockSel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.count === 0) {
            this.stockDataTable = []
          } else {
            this.stockDataTable = defaultQuery.data
          }
        } else {
          this.stockDataTable = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    getStockDes(stock_code) {
      var item = this.stockDataTable.find(item => item.stock_code === stock_code)
      if (item !== undefined) {
        return item.stock_des
      }
      return stock_code
    },

    // 执行
    taskStatusExecute(taskId, taskStatus) {
      this.$confirm(`确定要执行当前任务吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            task_id: taskId,
            task_status: taskStatus,
            user_name: Cookies.get('userName')
          }
          crudTask.taskStatusUpd(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('执行成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '执行失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '执行失败',
              type: 'error'
            })
          })
        })
        .catch(() => {})
    }

  }
}
</script>
<style scoped lang="less">
.app-container {
    .inbOutNum {
        margin: 0 5px;
        color: #409eff;
        cursor: pointer;
    }

    .hover-row {
        td {
            .el-dropdown {
                .el-button--text {
                    color: #fff;
                }
            }

            .inbOutNum {
                color: #fff;
            }
        }
    }

    .current-row {
        td {
            .el-dropdown {
                .el-button--text {
                    color: #fff;
                }
            }

            .inbOutNum {
                color: #fff;
            }
        }
    }

    ::v-deep .el-dialog {
        margin-top: 10vh !important;
    }
}
</style>
