<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="modalTitle" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <el-table
      ref="table"
      v-loading="crud.loading"
      border
      size="small"
      :data="crud.data"
      style="width: 100%"
      :cell-style="crud.cellStyle"
      :height="150"
      highlight-current-row
      @header-dragend="crud.tableHeaderDragend()"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :show-overflow-tooltip="true" prop="task_num" label="任务号" />
      <el-table-column :show-overflow-tooltip="true" prop="project_code" label="项目编码" />
      <el-table-column :show-overflow-tooltip="true" prop="material_code" label="零件编号" />
    </el-table>
    <div class="header">天车信息</div>
    <div class="car-info">
      <el-descriptions title="" :column="4" border class="left">
        <el-descriptions-item label="抓斗位置" label-class-name="my-label" content-class-name="my-content">kooriookami</el-descriptions-item>
        <el-descriptions-item label="升降速度" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="1X倾斜角度" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="1Y倾斜角度" label-class-name="my-label" content-class-name="my-content">18100000000</el-descriptions-item>
        <el-descriptions-item label="2X倾斜角度" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="2Y倾斜角度" label-class-name="my-label" content-class-name="my-content">18100000000</el-descriptions-item>
        <el-descriptions-item label="大车位置" label-class-name="my-label" content-class-name="my-content">18100000000</el-descriptions-item>
        <el-descriptions-item label="大车速度" label-class-name="my-label" content-class-name="my-content">18100000000</el-descriptions-item>
        <el-descriptions-item label="小车位置" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="小车速度" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="上升到位" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="上升减速" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="出库使能" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="入库使能" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="高度校准" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
        <el-descriptions-item label="出空框使能" label-class-name="my-label" content-class-name="my-content">苏州市</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="" :column="3" border class="right">
        <el-descriptions-item label="吊具开到位东南" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具关到位东南" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具落到位东南" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具开到位东北" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具关到位东北" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具落到位东北" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具开到位西北" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具关到位西北" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具落到位西北" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具开到位西南" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具关到位西南" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
        <el-descriptions-item label="吊具落到位西南" label-class-name="my-label" content-class-name="my-content"><el-radio v-model="value" /></el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="sports-position-info">
      <div class="sports-info">
        <div class="header">运动信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr>
                <th />
                <th>指令</th>
                <th>允许</th>
                <th>执行</th>
                <th>故障</th>
                <th>完成</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>初始化</th>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
              </tr>
              <tr>
                <th>前移车</th>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
              </tr>
              <tr>
                <th>抓框</th>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
              </tr>
              <tr>
                <th>后移车</th>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
              </tr>
              <tr>
                <th>放框</th>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
              </tr>
              <tr>
                <th>吊具旋转</th>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
                <td><el-radio v-model="value" /></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="position-info">
        <div class="header">位置信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr>
                <th />
                <th>实际</th>
                <th>目标</th>
                <th>▲</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>X:</th>
                <td>6.67 m</td>
                <td>6.66 m</td>
                <td>0.01 m</td>
              </tr>
              <tr>
                <th>Y:</th>
                <td>12.31 m</td>
                <td>12.31 m</td>
                <td>0.00 m</td>
              </tr>
              <tr>
                <th>Z:</th>
                <td>7.26 m</td>
                <td>7.26 m</td>
                <td>-0.00 m</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="control-info">
        <div class="header">控制信息</div>
        <div class="mode">
          <span>模式切换：</span>
          <el-button> 自&emsp;动</el-button>
          <el-button> 等待</el-button>
          <el-button> 检修</el-button>
        </div>
        <div class="operation">
          <span>操作按钮：</span>
          <el-button> 前移车</el-button>
          <el-button> 抓框</el-button>
          <el-button> 后移车</el-button>
          <el-button> 放框</el-button>
        </div>
        <div class="input-info">
          <span>&emsp;&emsp;&emsp;&nbsp;Y：</span>
          <el-input v-model="inputValue" />
          <span>&emsp;&emsp;&nbsp;Z：</span>
          <el-input v-model="inputValue" />
        </div>
        <div class="input-info">
          <span>&emsp;&nbsp;X高位：</span>
          <el-input />
          <span>X低位：</span>
          <el-input />
        </div>
        <div class="manual-stack">
          <span>手动倒垛：</span>
          <el-input />
          <svg-icon icon-class="right" />
          <el-input />
          <el-button> 确定</el-button>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="control-btn">
        <el-button type="primary" class="A">东行车 当前模式：等待</el-button>
        <el-button type="primary" class="B">紧急停止</el-button>
        <el-button type="primary" class="C">报警复位</el-button>
        <el-button type="primary" class="D">上总电</el-button>
        <el-button type="primary" class="E">断总电</el-button>
        <el-button type="primary" class="H">继续作业</el-button>
        <el-button type="primary" class="F">开照明</el-button>
        <el-button type="primary" class="G">关照明</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import Cookies from 'js-cookie'
import crudWmsFjTask from '@/api/dcs/project/wms/wmsFjTask'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
const defaultForm = {

}
export default {
  name: 'CROWBLOCKDETAIL',
  cruds() {
    return CRUD({
      title: '天车任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'fj_task_id',
      // 排序
      sort: ['fj_task_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsFjTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      modalTitle: '查看明细',
      modalWidth: '75%',
      value: false,
      inputValue: ''
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
      console.log('111')
    },
    handleClose(done) {
      done()
    }
  }

}
</script>
<style scoped lang="less">
::v-deep .el-dialog{
  margin-top: 5vh !important;
}
 .header{
    display: flex;
    align-items: center;
    margin: 10px 0;
    color: #ffffff;
    font-size: 20px;
    font-family: YouSheBiaoTiHei;
    color: #00ffff;
 }
 .my-label {
    background: #E1F3D8;
  }
  .my-content {
    background: #FDE2E2;
  }
  .car-info{
    display: flex;
    justify-content: space-between;
    .left{
        width: 57%;
    }
    .right{
        width: 42%;
    }
    ::v-deep .my-label{
        background-color: #031850 !important;
        color: #00ffff !important;
        border: 1px solid #00ffff!important;
        font-size: 14px;
        text-align: center;
    }
    ::v-deep .my-content{
        border: 1px solid #00ffff!important;
        background-color: #072578 !important;
        color: #21b1ee!important;
        text-align: center;
        font-size: 14px;
    }
    ::v-deep .el-radio__inner{
        width: 16px;
        height: 16px;
    }
  }
  .sports-position-info{
    display: flex;
    justify-content: space-between;
    .sports-info{
        width: 33%;
    }
    .position-info{
        width: 33%;
    }
    .control-info{
        width: 33%;
        .mode{
          color: #00ffff;
          font-size: 16px;
          display: flex;
          align-items: center;
          ::v-deep .el-button{
            border: 2px solid #57d6f6;
            background-color: #084aa0;
            color: #00ffff;
          }
        }
        .input-info{
          margin-top: 10px;
          span{
            color: #00ffff;
            font-size: 16px;
          }
          ::v-deep .el-input{
            width: 33% !important;
            height: 30px;
            .el-input__inner{
              border: 2px solid #57d6f6;
              background-color: #072578;
              color: #57d6f6;
              font-size: 16px;
            }
          }
        }
        .operation{
          margin-top: 10px;
          display: flex;
          align-items: center;
          span{
            color: #00ffff;
            font-size: 16px;
          }
          ::v-deep .el-button{
            border: 2px solid #57d6f6;
            background-color: #084aa0;
            color: #00ffff;
          }
        }
        .manual-stack{
          margin-top: 10px;
          display: flex;
          align-items: center;
          span{
            color: #00ffff;
            font-size: 16px;
          }
          .svg-icon{
            font-size: 20px;
            margin: 0 5px;
          }
          ::v-deep .el-input{
            width: 30% !important;
            height: 30px;
            .el-input__inner{
              border: 2px solid #57d6f6;
              background-color: #072578;
              color: #57d6f6;
              font-size: 16px;
            }
          }
          ::v-deep .el-button{
            border: 2px solid #57d6f6;
            background-color: #084aa0;
            color: #00ffff;
            margin-left: 5px;
          }
        }
    }
  }
  .table-wrapper{
    table {
        width: 100%;
        table-layout: auto;
        thead{
        th{
            white-space: nowrap;
        }
    }
    }
    tr{
        height: 25px;
    }
    th,td{
        font-size: 18px;
        text-align: center;
        ::v-deep .el-radio__inner{
            width: 16px;
            height: 16px;
            margin-left: 5px;
        }
    }
    th{
        color: #00ffff;
    }
    td{
        color: #fff;
    }
  }
  .footer{
    .control-btn{
      padding: 20px;
      width: 28%;
      background-color: #1b5dae;
      margin: 0 auto;
      border-radius: 3%;
      box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
      .el-button{
          font-size: 20px;
          margin: 5px 0;
      }
      .A{
          background: #346250;
      }
      .B{
          background: #ff0000;
      }
      .C,.D,.F,.H,.E,.G{
          border: 2px solid #57d6f6;
          background-color: #084aa0;
      }
      .E,.G{
          color: #ff0000;
      }
      .D,.E,.F,.G{
          margin-left: 20px;
      }
    }
  }
</style>
