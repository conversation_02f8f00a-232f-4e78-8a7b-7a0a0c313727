<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item
                :label="$t('lang_pack.haCarTypeRoute.productionLine')"
              >
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_code">
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_code"
                    :label="item.prod_line_code + ' ' + item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item
                :label="$t('lang_pack.haCarTypeRoute.haCarTypeDes')"
              >
                <!-- 白车身号： -->
                <el-input v-model="query.car_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentification')"
              >
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="100px"
              :inline="true"
            >
              <!--表：sys_fmod_prod_line-->
              <el-form-item
                :label="$t('lang_pack.haCarTypeRoute.productionLinet')"
                prop="prod_line_code"
              >
                <!-- 产线 -->
                <el-select
                  v-model="form.prod_line_code"
                  size="small"
                  placeholder="请选择产线"
                  @change="changeProdLine"
                >
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_code"
                    :label="item.prod_line_code + ' ' + item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.haCarTypeRoute.carType')"
                prop="car_type"
              >
                <!-- 白车身号 -->
                <el-input v-model="form.car_type" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.haCarTypeRoute.onlineStation')"
                prop="online_station"
              >
                <!-- 上线工位 -->
                <el-select
                  v-model="form.online_station"
                  size="small"
                  placeholder="请选择上线工位"
                >
                  <el-option
                    v-for="item in onLineStationData"
                    :key="item.station_code"
                    :label="item.station_code"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.haCarTypeRoute.routeStation')"
                prop="route_station"
              >
                <el-select
                  v-model="form.route_station"
                  placeholder="请选择工艺路线"
                  filterable
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in routeStationData"
                    :key="item.station_code"
                    :label="item.station_code"
                    :value="item.station_code + ''"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentificationt')"
                class="formItemStyle"
              >
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="crud.cancelCU"
                >{{ $t("lang_pack.commonPage.cancel") }}</el-button
              >
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
                >{{ $t("lang_pack.commonPage.confirm") }}</el-button
              >
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            border
            ref="table"
            v-loading="crud.loading"
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed="left" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_line_code"
              :label="$t('lang_pack.haCarTypeRoute.productionLinet')"
              min-width="100"
              align="center"
            />
            <!-- 白车身号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="car_type"
              :label="$t('lang_pack.haCarTypeRoute.carType')"
              min-width="100"
              align="center"
            />
            <!-- 白车身号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="online_station"
              :label="$t('lang_pack.haCarTypeRoute.onlineStation')"
              min-width="100"
              align="center"
            />
            <!-- 上线工位 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="route_station"
              :label="$t('lang_pack.haCarTypeRoute.routeStation')"
            />
            <!-- 工艺路线 -->
            <el-table-column
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              align="center"
              prop="enable_flag"
              width="100"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :disabled-dle="false"
                />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import crudRoute from "@/api/pmc/hacarTypeRoute";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import udOperation from "@crud/UD.operation";
import pagination from "@crud/Pagination";
const defaultForm = {
  car_route_id: "",
  prod_line_code: "",
  car_type: "",
  online_station: "",
  route_station: "",
  enable_flag: "Y",
  attribute1: "",
  attribute2: "",
  attribute3: "",
};
export default {
  name: "CARTYPEROUTE",
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: "车型工艺路线管理",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "car_route_id",
      // 排序
      sort: ["car_route_id asc"],
      // CRUD Method
      crudMethod: { ...crudRoute },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true,
      },
    });
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ["admin", "d_pmc_fmod_ha_car_route:add"],
        edit: ["admin", "d_pmc_fmod_ha_car_route:edit"],
        del: ["admin", "d_pmc_fmod_ha_car_route:del"],
        reset: ["admin", "d_pmc_fmod_ha_car_route:reset"],
      },
      rules: {
        car_type: [
          { required: true, message: "请输入白车身号", trigger: "blur" },
        ],
        online_station: [
          { required: true, message: "请选择上线工位", trigger: "blur" },
        ],
        route_station: [
          { required: true, message: "请选择工艺路线", trigger: "blur" },
        ],
      },
      // 产线数据
      currentProdLineCode: "", // 当前产线(单笔)
      prodLineData: [],
      onLineStationData: [],
      routeStationData: [],
      dialogInterfParasVisible: false,
    };
  },
  // 数据字典
  dicts: ["ENABLE_FLAG", "WHETHER_FLAG", "DATA_COLLECT_TYPE"],
  mounted() {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270;
    };
  },
  created() {
    // 加载 产线LOV
    const query = {
      userID: Cookies.get("userName"),
      work_center_code: "HA",
    };
    crudRoute
      .haLineCodeSel(query)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res));
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data;
          }
        }
      })
      .catch(() => {
        this.$message({
          message: "查询异常",
          type: "error",
        });
      });
  },
  methods: {
    // 更改产线
    changeProdLine(val) {
      this.currentProdLineCode = val; // 当前产线
      // 加载 工位LOV
      this.queryStation();
    },
    // 工位LOV
    queryStation() {
      this.form.online_station = "";
      this.form.route_station = "";
      this.onLineStationData = [];
      this.routeStationData = [];
      const query = {
        userID: Cookies.get("userName"),
        prod_line_code: this.currentProdLineCode,
        station_attr: "Y",
        enable_flag: "Y",
      };
      crudRoute
        .haLineStationSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.onLineStationData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });

      const query2 = {
        userID: Cookies.get("userName"),
        prod_line_code: this.currentProdLineCode,
        enable_flag: "Y",
      };
      crudRoute
        .haLineStationSel(query2)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.routeStationData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },

    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.route_station =
        crud.form.route_station === ""
          ? ""
          : crud.form.route_station.split(",");
      return true;
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.route_station =
        this.form.route_station === "" ? "" : this.form.route_station.join(",");
      return true;
    },
  },
};
</script>

