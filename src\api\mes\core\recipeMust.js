import request from '@/utils/request'

// 产品型号对应必需配方类型信息查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMustSel',
    method: 'post',
    data
  })
}
// 产品型号对应必需配方类型信息新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMustIns',
    method: 'post',
    data
  })
}
// 产品型号对应必需配方类型信息删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMustDel',
    method: 'post',
    data
  })
}

// 配方类型树查询
export function treeRecipeMust(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMustTree',
    method: 'post',
    data
  })
}

export default { sel, add, del, treeRecipeMust }
