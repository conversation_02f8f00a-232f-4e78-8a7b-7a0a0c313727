import request from '@/utils/request'
import Cookies from 'js-cookie'

// 获取 验证码
export function getSysCodeImg() {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysCode',
    method: 'get'
  })
}
// 白名单登录
export function sysWhiteLogin(username, password, uuid) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysLogin?skip=true',
    method: 'post',
    data: {
      username,
      password,
      uuid
    }
  })
}
export function sysWhiteLoginWx(username, password, uuid, rolename) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/WxSysLogin',
    method: 'post',
    data: {
      username,
      password,
      uuid
    }
  })
}
export function getLoginLog(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysLoginIns',
    method: 'post',
    data
  })
}
// 登录
export function sysLogin(username, password, code, uuid) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysLogin?skip=true',
    method: 'post',
    data: {
      username,
      password,
      code,
      uuid
    }
  })
}
// 白名单登录
export function sysWhiteLoginOfVerify(username, password, uuid) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysLogin' + (Cookies.get('userName') == null ? '?skip=true' : '?userCode=' + encodeURIComponent(username)),
    method: 'post',
    data: {
      username,
      password,
      uuid
    }
  })
}
// 登录
export function sysLoginOfVerify(username, password, code, uuid) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysLogin' + (Cookies.get('userName') == null ? '?skip=true' : '?userCode=' + encodeURIComponent(username)),
    method: 'post',
    data: {
      username,
      password,
      code,
      uuid
    }
  })
}
// 获取当前登录用户信息(刷新)
export function getSysInfo(userId) {
  const params = {
    userId
  }
  return request({
    url: 'aisEsbWeb/core/system/CoreSysInfo',
    method: 'get',
    params
  })
}
// 退出登录
export function logout() {
  return request({
    url: 'aisEsbWeb/core/system/Logout',
    method: 'delete'
  })
}
