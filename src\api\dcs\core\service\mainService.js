import request from '@/utils/request'

// 查询主界面维护
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/base/select-section',
    method: 'post',
    data
  })
}

// 新增主界面维护
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/base/insert-section',
    method: 'post',
    data
  })
}

// 修改主界面维护
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/base/update-section',
    method: 'put',
    data
  })
}

// 删除主界面维护
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/base/delete-section',
    method: 'delete',
    data
  })
}

// 查询点位
export function queryPonit(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/base/select-point',
    method: 'post',
    data
  })
}

// 保存点位
export function savePonit(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/base/save-point',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, queryPonit, savePonit }

