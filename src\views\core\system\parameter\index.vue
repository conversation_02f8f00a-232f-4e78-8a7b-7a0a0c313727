<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.systemParameter.parametricDescription')">  <!-- 参数/描述： -->
                <el-input v-model="query.parameterCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.systemParameter.cell')">  <!-- 单元： -->
                <el-select v-model="query.cell_id" filterable clearable="">
                  <el-option v-for="item in cellData" :key="item.cell_id" :label="item.cell_container_name + ' ' + item.cell_container_des" :value="item.cell_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">  <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.systemParameter.cell')" prop="cell_id">  <!-- 单元 -->
            <el-select v-model="form.cell_id" filterable>
              <el-option v-for="item in cellData" :key="item.cell_id" :label="item.cell_container_name + ' ' + item.cell_container_des" :value="item.cell_id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.systemParameter.parameterCode')" prop="parameter_code">  <!-- 参数编码 -->
            <el-input v-model="form.parameter_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.systemParameter.parameterDescription')" prop="parameter_des">  <!-- 参数描述 -->
            <el-input v-model="form.parameter_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.systemParameter.parameterValue')" prop="parameter_val">  <!-- 参数值 -->
            <el-input v-model="form.parameter_val" />
          </el-form-item>
          <!--快速编码：ENABLE_FLAG-->
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">  <!-- 有效标识 -->
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="parameter_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="cell_id" :label="$t('lang_pack.systemParameter.cell')">  <!-- 单元 -->
              <template slot-scope="scope">
                {{ getCelDes(scope.row.cell_id) }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_code" :label="$t('lang_pack.systemParameter.parameterNumber')" />  <!-- 参数编号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_des" :label="$t('lang_pack.systemParameter.parameterdescription')" />  <!-- 参数描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_val" :label="$t('lang_pack.systemParameter.parameterValue')" />  <!-- 参数值 -->
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">  <!-- 有效标识 -->
              <template slot-scope="scope">
                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">  <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudParameter from '@/api/core/system/sysParameter'
import { sel as selCell } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  parameter_id: '',
  cell_id: 0,
  parameter_code: '',
  parameter_des: '',
  parameter_val: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_PARAMETER',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '系统参数',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'parameter_id',
      // 排序
      sort: ['parameter_id asc'],
      // CRUD Method
      crudMethod: { ...crudParameter },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      },
      // 设置默认分页大小为20条每页
      props: {
        pageSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_parameter:add'],
        edit: ['admin', 'sys_parameter:edit'],
        del: ['admin', 'sys_parameter:del'],
        down: ['admin', 'sys_parameter:down']
      },
      rules: {
        // 提交验证规则
        parameter_code: [{ required: true, message: '请输入系统参数编码', trigger: 'blur' }],
        parameter_val: [{ required: true, message: '请输入系统参数值', trigger: 'blur' }]
      },
      cellData: []
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    selCell(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.cellData = defaultQuery.data
            this.cellData.push({ cell_id: 0, cell_container_name: '', cell_container_des: '通用参数' })
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 获取单元的中文描述
    getCelDes(cell_id) {
      var item = this.cellData.find(item => item.cell_id === cell_id)
      if (item !== undefined) {
        return item.cell_container_name + ' ' + item.cell_container_des
      }
      return cell_id
    }
  }
}
</script>
