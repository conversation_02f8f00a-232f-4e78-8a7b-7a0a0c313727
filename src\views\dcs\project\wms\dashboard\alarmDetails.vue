<template>
  <el-container id="bigScreen" :style="'background-image:url(' +mainBackground +')'">
    <el-header :style="'background-image:url(' + headerBackground +');background-size:100% 100%;width:100%;height:80px'">
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7" style="display: flex;align-items: center;">
          <img
            :src="headerLogo"
            style="width: 500px; height: 70px; float: left; ;margin-top: 10px;margin-left: -20px;"
          >
          <!-- <span class="logoTitle">中冶(上海)钢结构科技有限公司</span> -->
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 5px">
          <span class="title">程控行车设备状态看板</span>
        </el-col>
        <el-col :span="7" style="text-align: right; padding-top: 20px; padding-right: 10px"><span class="time"> {{ DateTime[0] + ' ' + DateTime[1] }} </span><span class="time time1"> {{ DateTime[2] }} </span></el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20" class="dcs-alarm-dashboard-main-hint">
        <div>限位显示</div>
        <div class="green-light" /><span class="light-explain">正常</span>
        <div class="yellow-light" /><span class="light-explain">触发</span>
        <el-button type="primary" @click="$router.push('/dcs/project/wms/dashboard/index')">返回主页</el-button>
      </el-row>
      <div class="dcs-alarm-dashboard-main-content">
        <div v-for="(item,index) in configData.limitPointData" :key="index" class="dcs-alarm-dashboard-main-content-item">
          <div :class="{'green-light':item.tags.tag_value !== '0','yellow-light':item.tags.tag_value === '0'}" /><span class="light-explain">{{ item.tags.tag_des }}</span>
        </div>
      </div>
      <el-row :gutter="20" class="dcs-alarm-dashboard-main-hint">
        <div>故障显示</div>
        <div class="green-light" /><span class="light-explain">正常</span>
        <div class="red-light" /><span class="light-explain">故障</span>
      </el-row>
      <div class="dcs-alarm-dashboard-main-content">
        <div v-for="(item,index) in configData.faultPointData" :key="index" class="dcs-alarm-dashboard-main-content-item">
          <div :class="{'green-light':item.tags.tag_value !== '1','red-light':item.tags.tag_value === '1'}" /><span class="light-explain">{{ item.tags.tag_des }}</span>
        </div>
      </div>
    </el-main>
  </el-container>
</template>
<script>
import Paho from 'paho-mqtt'
import Cookies from 'js-cookie'
import autofit from 'autofit.js'
import headerBackground from '@/assets/images/dcs/wms_header.png'
import mainBackground from '@/assets/images/dcs/wms_backGround.png'
import headerLogo from '@/assets/images/dcs/wms_logo.png'
import { getFormatDate } from '@/utils/index.js'
import configData from '@/api/dcs/project/wms/alarmDetails.json'
import { selCellIP } from '@/api/core/center/cell'
import { CoreScadaReadTag } from '@/api/hmi/mainIndex'
export default {
  name: 'ALARM_DASHBOARD',
  data() {
    return {
      headerBackground,
      mainBackground,
      headerLogo,
      configData,
      timer: null,
      DateTime: [],
      tagKeysMap: {},
      scadaClient: {},
      clients: {},
      mqttClient: {},
      triggerMonitors: {}
    }
  },
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
  },
  created() {
    this.init()
    this.DateTime = getFormatDate().split(' ')
    this.timer = setInterval(() => {
      this.DateTime = getFormatDate().split(' ')
    }, 1000)
  },
  beforeDestroy() {
    autofit.off()
    this.timer && clearInterval(this.timer)
    for (const key in this.clients) {
      this.clients[key] && this.clients[key].disconnect()
    }
  },
  methods: {
    init() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.getScadaPointdata(ipInfo)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    getScadaPointdata(ipInfo) {
      Object.keys(this.configData).forEach(temp => {
        this.configData[temp].forEach(item => {
          const scadaTopic = item.scadaClient
          const cellPort = 8089
          if (this.scadaClient[scadaTopic] == null) {
            this.scadaClient[scadaTopic] = []
          }
          if (this.tagKeysMap[cellPort] == null) {
            this.tagKeysMap[cellPort] = []
          }
          this.triggerMonitors[item.tags.tag_key] = item
          this.scadaClient[scadaTopic].push({ 'tag_key': item.tags.tag_key, 'client': item.scadaClient })
          this.tagKeysMap[cellPort].push({ 'tag_key': item.tags.tag_key })
        })
      })
      for (const key in this.tagKeysMap) {
        const cellUrl = `http://${ipInfo.ip}:${ipInfo.webapi_port}/`
        CoreScadaReadTag(cellUrl, this.tagKeysMap[key])
          .then(res => {
            if (res.code === 0 && res.data) {
              for (const i in res.data) {
                const item = res.data[i]
                const k = item.tag_key
                const v = item.tag_value
                this.setContent(k, v)
              }
            }
          })
      }
      for (const key in this.scadaClient) {
        const port = parseInt(ipInfo.mqtt_port)
        this.connectMQTT(ipInfo.ip, port, (c) => {
          this.clients[key] = c
          // 发布订阅
          this.scadaClient[key].forEach(item => {
            c.subscribe(`${item.client}/${item.tag_key}`, {
              onSuccess: () => {
                console.log('订阅成功：', item.client + '/' + item.tag_key)
              },
              onFailure: (responseObject) => {
                console.log('订阅失败：', item.client + '/' + item.tag_key, responseObject.errorMessage)
              }
            })
          })
        })
      }
    },
    setContent(k, v) {
      if (v === undefined || v === null) return
      Object.keys(this.configData).forEach(temp => {
        this.configData[temp].forEach(item => {
          if (item.tags.tag_key === k) {
            item.tags.tag_value = v
          }
        })
      })
    },
    connectMQTT(host, port, onConnected) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      this.mqttClient = mqttClient
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          this.setContent(data.TagKey, data.TagNewValue)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }
}
</script>
<style lang="scss" scoped>
@font-face {
  font-family: light;
  src: url('../../../../../assets/fonts/LCD2B___.TTF');
}
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('../../../../../assets/fonts/YouSheBiaoTiHei.ttf');
}
// wms大屏的数据
#bigScreen{
  background-size:100% 100%;width:100%;height:100%;
  .el-header{
      .logoTitle{
        font-family: YouSheBiaoTiHei;
          color: #fff;
          font-size: 30px;
          font-weight: 600;
      }
      .title{
            font-size: 40px;
            font-weight: 600;
            letter-spacing: 5px;
            background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
            -webkit-background-clip: text;
            color: transparent;
      }
      .time{
          color: #fff;
          font-size: 30px;
          font-family: light;
      }
      .time1{
            color: #fff;
            font-size: 36px;
            font-family: YouSheBiaoTiHei;
        }
  }
  .dcs-alarm-dashboard-main-hint {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: center;
    margin: 20px 0;
        div {
            margin-left: 50px;
            color: #fff;
            font-family: YouSheBiaoTiHei;
            font-size: 34px;
        }
        .el-button{
            position: absolute;
            right: 10px;
            cursor: pointer;
            font-size: 34px;
            font-family: YouSheBiaoTiHei;
        }
    }
    .dcs-alarm-dashboard-main-content {
        display: flex;
        flex-wrap: wrap;
        .dcs-alarm-dashboard-main-content-item {
            flex: 0 0 20%;
            text-align: center;
            box-sizing: border-box;
            padding: 15px;
            display: flex;
            align-items: center;

        }
    }
    /* 小绿灯的样式 */
    .green-light {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #75f94c;
        animation: greenPulse 2s infinite;
    }

    /* 小红灯的样式 */
    .red-light {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: red;
        animation: redPulse 2s infinite;
    }
    /* 小黄灯的样式 */
    .yellow-light {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: rgb(247, 192, 27);
        animation: yellowPulse 2s infinite;
    }
    /* 小灯的解释 */
    .light-explain {
        font-family: YouSheBiaoTiHei;
        font-size: 36px;
        color: white;
        margin-left: 10px;
    }
    /* 小绿灯的动画 */
    @keyframes greenPulse {
        0% {
            box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7);
        }

        50% {
            box-shadow: 0 0 0 15px rgba(0, 255, 0, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
        }
    }
    /* 小红灯的动画 */
    @keyframes redPulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
        }

        50% {
            box-shadow: 0 0 0 15px rgba(255, 0, 0, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
        }
    }
    @keyframes yellowPulse {
        0% {
            box-shadow: 0 0 0 0 rgb(244, 182, 0,0.7);
        }

        50% {
            box-shadow: 0 0 0 15px rgba(255, 0, 0, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
        }
    }
}
</style>
