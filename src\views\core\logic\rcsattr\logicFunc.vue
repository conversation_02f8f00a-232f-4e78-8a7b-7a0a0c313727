<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-drawer append-to-body :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="450px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="130px" :inline="true">
          <el-form-item :label="$t('lang_pack.logicfunc.logicalProgram')" prop="function_id">
            <!-- 逻辑程序 -->
            <el-select v-model="form.function_id" clearable filterable>
              <el-option v-for="item in functionData" :key="item.function_id" :label="item.function_des" :value="item.function_id">
                <span>{{ item.function_code }}</span>
                <span>{{ item.function_des }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.schedulerCode')" prop="func_code">
            <!-- 调度程序代码 -->
            <el-input v-model="form.func_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.schedulerDescription')" prop="func_des">
            <!-- 调度程序描述 -->
            <el-input v-model="form.func_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicfunc.setControllers')">
            <!-- 控制器集合 -->
            <el-select v-model="form.client_id_list" multiple>
              <el-option v-for="item in clientIdListOptions" :key="item.client_id + ''" :label="item.client_des" :value="item.client_id + ''">
                <span>{{ item.client_code }}</span>
                <span>{{ item.client_des }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :stripe="true" height="400px" :highlight-current-row="true" @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="func_code" :label="$t('lang_pack.logicfunc.schedulerCode')" sortable="custom" />
        <!-- 调度程序代码 -->
        <el-table-column  :show-overflow-tooltip="true" prop="func_des" :label="$t('lang_pack.logicfunc.schedulerDescription')" sortable="custom" />
        <!-- 调度程序描述 -->
        <el-table-column  :show-overflow-tooltip="true" prop="function_id" :label="$t('lang_pack.logicfunc.logicalProgram')" />
        <!-- 逻辑程序 -->
        <el-table-column  :show-overflow-tooltip="true" prop="client_id_list" width="100" :label="$t('lang_pack.logicfunc.setControllers')" />
        <!-- 控制器集合 -->

        <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
          <!-- 有效标识 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import { selScadaClient } from '@/api/core/scada/client'
import { lovFunction } from '@/api/core/system/sysFunction'
import { selLogicFunc, insLogicFunc, updLogicFunc, delLogicFunc } from '@/api/core/logic/logicFunc'

export default {
  name: 'FUNC',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      form: {
        logic_func_id: '',
        function_id: '',
        func_code: '',
        func_des: '',
        client_id_list: [],
        enable_flag: 'Y'
      },
      rules: {
        func_code: [{ required: true, message: '请输入调度程序代码', trigger: 'blur' }]
      },

      // Table
      listLoadingTable: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],
      query: {
        funcCodeDes: '',
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'logic_func_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      // LOV
      clientIdListOptions: [],
      functionData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // 查询
    this.toButQuery()
    // 实例LOV
    const query = {
      sort: 'client_id',
      user_name: Cookies.get('userName')
    }
    selScadaClient(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.clientIdListOptions = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })

    const query1 = {
      user_name: Cookies.get('userName'),
      menu_type_code: 'LOGIC'
    }
    // 从后台获取到对象数组
    lovFunction(query1)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.functionData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 按钮：
    toButQuery(queryContent) {
      if (queryContent !== undefined) {
        this.query.funcCodeDes = queryContent
      }
      const query = {
        user_name: Cookies.get('userName'),
        funcCodeDes: this.query.funcCodeDes,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }

      this.listLoadingTable = true
      selLogicFunc(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toButAdd() {
      this.form.logic_func_id = ''
      this.form.function_id = ''
      this.form.func_code = ''
      this.form.func_des = ''
      this.form.client_id_list = ''
      this.form.enable_flag = 'Y'

      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增自动化逻辑'
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.logic_func_id = data.logic_func_id
      this.form.function_id = data.function_id
      this.form.func_code = data.func_code
      this.form.func_des = data.func_des
      this.form.client_id_list = data.client_id_list === '' ? '' : data.client_id_list.split(',')
      this.form.enable_flag = data.enable_flag

      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改自动化逻辑'
    },
    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            logic_func_id: this.form.logic_func_id,
            function_id: this.form.function_id,
            func_code: this.form.func_code,
            func_des: this.form.func_des,
            client_id_list: this.form.client_id_list === '' ? '' : this.form.client_id_list.join(','),
            enable_flag: this.form.enable_flag
          }
          const that = this
          // 新增
          if (this.form.logic_func_id === undefined || this.form.logic_func_id.length <= 0) {
            insLogicFunc(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  that.$emit('AddTreeNode', defaultQuery.result, this.form.func_code, this.form.func_des)
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updLogicFunc(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  that.$emit('EditTreeNode', this.form.logic_func_id, this.form.func_code, this.form.func_des)
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除${data.func_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            logic_func_id: data.logic_func_id
          }
          const that = this
          delLogicFunc(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                that.$emit('DelTreeNode', data.logic_func_id)
                // 查询
                this.toButQuery()
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'logic_func_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-drawer {
  overflow-y: scroll;
}
</style>
