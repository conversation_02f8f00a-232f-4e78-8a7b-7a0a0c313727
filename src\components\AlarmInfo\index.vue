<template>
    <el-dialog title="报警信息状态" :visible.sync="dialogVisible" :append-to-body="true" width="60%" :before-close="handleClose">
      <!-- 时间/消息/操作 -->
        <el-card shadow="never" class="wrapCard" style="margin-top: 10px;">
            <el-table border ref="table" size="small" :data="alarmData" style="width: 100%"
            :height="height" :highlight-current-row="true">
                <el-table-column  :show-overflow-tooltip="true" min-width="80" align='center' prop="task_num"
                    label="时间" />
                <el-table-column :show-overflow-tooltip="true" min-width="80" align='center' prop="model_type"
                    label="消息" />
                <el-table-column :show-overflow-tooltip="true" align="center" min-width="120" :label="$t('lang_pack.commonPage.operate')">
                    <template slot-scope="scope">
                        <el-button slot="reference" type="text" :disabled="!scope.row.task_num" size="small">清除异常</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!--分页组件-->
            <el-pagination
                :page-size.sync="pageTable.size"
                :total="pageTable.total"
                :current-page.sync="pageTable.page"
                :page-sizes="[20, 30, 40, 50, 100]"
                style="margin-top: 8px; float: right"
                layout="total, prev, pager, next, sizes"
                @size-change="toSizeTableChangeHandler($event)"
                @current-change="toPageTableChangeHandler"
                />
        </el-card>
    </el-dialog>
</template>
<script>
import crudTask from '@/api/dcs/core/aps/task'
export default {
    name:'AlarmInfo',
    data(){
        return {
            query: {
                tablePage: 1, //当前页
                tableSize: 20, //每页数据条数
            },
            pageTable: {
                // 页码
                page: 1,
                // 每页数据条数
                size: 20,
                // 总数据条数
                total: 0,
            },
            dialogVisible:false,
            height:document.documentElement.clientHeight - 450,
            alarmData:[],
            alarmInfoFlag:false,
        }
    },
    created(){
        // this.getAlarmData()
    },
    methods:{
        getAlarmData(){
            const query = {
                    page: this.query.tablePage, //当前页
                    size: this.query.tableSize, //每页数据条数
                };
            crudTask.sel(query).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res));
                if (defaultQuery.code == 0) {
                    if (defaultQuery.count == 0) {
                        this.alarmData = [];
                    } else {
                        this.alarmData = defaultQuery.data;
                    }
                } else {
                    this.alarmData = [];
                    this.$message({
                        message: defaultQuery.msg,
                        type: "error",
                    });
                }
                this.pageTable.total = defaultQuery.count;
                this.$emit('ok',this.pageTable.total)
            })
            .catch(() => {
                this.$message({
                    message: "查询异常",
                    type: "error",
                });
            })
        },
        //Page:分页
        toSizeTableChangeHandler(val) {
            //页大小改变
            this.query.tableSize = val;
            //查询
            this.getAlarmData()
        },
        toPageTableChangeHandler(val) {
            //页数改变
            this.query.tablePage = val;
            //查询
            this.getAlarmData()
        },
        handleClose(){
            this.dialogVisible = false
        },
    }
}
</script>