import request from '@/utils/request'
// 查询当天订单查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel',
    method: 'post',
    data
  })
}
// 查询当天订单查询
export function PlanMoSel1(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel1',
    method: 'post',
    data
  })
}
// 一次下线合格率
export function planMoSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel2',
    method: 'post',
    data
  })
}
// 当日每小时产量
export function planMoMonthSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel3',
    method: 'post',
    data
  })
}
// 工位不良报警数TOP5
export function PlanMoSel5(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel5',
    method: 'post',
    data
  })
}
// 当月可动率
export function PlanMoSel6(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel6',
    method: 'post',
    data
  })
}
// 当月一次下线合格率
export function PlanMoSel7(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel7',
    method: 'post',
    data
  })
}
// 当月产量
export function PlanMoSel8(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel8',
    method: 'post',
    data
  })
}
// 查询一次性合格数数量
export function PlanMoSel9(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel9',
    method: 'post',
    data
  })
}
// 查询一次性合格数数量
export function PlanMoSel4(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSel4',
    method: 'post',
    data
  })
}
// 查询一次性合格数数量
export function MesSHApsPlanMoSelDelete(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSHApsPlanMoSelDelete',
    method: 'post',
    data
  })
}
export default { sel, PlanMoSel1, planMoSel, planMoMonthSel, PlanMoSel9, PlanMoSel5, PlanMoSel6, PlanMoSel7, PlanMoSel8, PlanMoSel4, MesSHApsPlanMoSelDelete }
