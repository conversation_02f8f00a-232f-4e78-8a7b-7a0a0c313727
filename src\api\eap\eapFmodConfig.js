import request from '@/utils/request'

// 查询工位配置信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapConfigSel',
    method: 'post',
    data
  })
}
// 新增工位配置信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapConfigIns',
    method: 'post',
    data
  })
}
// 修改工位配置信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapConfigUpd',
    method: 'post',
    data
  })
}
// 修改工位配置信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapConfigEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除工位配置信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapConfigDel',
    method: 'post',
    data
  })
}
// 工位配置信息查询
export function selStationConfig(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapStationConfigSel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag, selStationConfig }

