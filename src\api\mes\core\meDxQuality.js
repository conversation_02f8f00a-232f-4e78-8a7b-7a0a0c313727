import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb//mes/core/MesMeDxQualitySel',
    method: 'post',
    data
  })
}
// 导出
export function down(data) {
  return request({
    url: 'aisEsbWeb//mes/core/MesMeDxQualityDown',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 插入导出事件
export function exportEventInsert(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeDxQualityExportEventInsert',
    method: 'post',
    data
  })
}

export default { sel, down, exportEventInsert }

