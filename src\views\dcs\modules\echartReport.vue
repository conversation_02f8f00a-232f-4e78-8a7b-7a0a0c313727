<template>
    <el-row>
      <el-col>
        <ECharts ref="chart3" :options="orgOptions1" style="height:100%;" />
      </el-col>
    </el-row>
  </template>
  <script>
  import ECharts from 'vue-echarts'
  
  export default {
    components: {
      ECharts
    },
    data() {
      return {
        orgOptions: {},
        orgOptions1: {},
        orgOptions2: {}
      }
    },
    created() {},
    mounted() {
      this.orgOptions = {
        color: ['#495ce4'],
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true,
            areaStyle: {}
          }
        ]
      }
      this.orgOptions1 = {
        backgroundColor: '', // 背景颜色透明
        tooltip: {},
        dataset: {
          source: [
            ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            ['周一', 43, 85, 93, 55],
            ['周二', 83, 100, 55, 93],
            ['周三', 86, 65, 90, 93],
            ['周四', 72, 53, 39, 93],
            ['周五', 72, 53, 39, 93],
            ['周六', 72, 53, 39, 93],
            ['周日', 72, 53, 39, 93],
          ]
        },
        xAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            textStyle: {
              // 文字样式
              color: '#333333',
              fontSize: 12
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(191, 191, 191, 0)' // 更改坐标轴颜色
            }
          }
        },
        yAxis: {
          show: true,
          splitLine: { show: false },
          // 隐藏坐标线
          axisLine: {
            show: false
          },
          // 隐藏刻度线
          axisTick: {
            show: false
          }
        },
        grid: {
          top: '5%',
          left: '5%',
          right: '0%',
          bottom: '32%',
          containLabel: true
        },
        series: [
          {
            type: 'bar',
            barWidth: '25%',
            itemStyle: {
              barBorderRadius: [25, 25, 0, 0]
            },
            color: {
              type: 'linear',
              // x=0,y=1,柱子的颜色在垂直方向渐变
              x: 0,
              y: 1,
              colorStops: [
                // 0%处的颜色
                {
                  offset: 1,
                  color: '#b6ceff'
                },
                // 50%处的颜色
                {
                  offset: 0.5,
                  color: '#6e99f3'
                },
                // 100%处的颜色
                {
                  offset: 0,
                  color: '#6e99f3'
                }
              ],
              global: false // 缺省为 false
            }
          }
        ]
      }
    },
    methods: {}
  }
  </script>
  <style lang="scss" scoped>
  .echarts{
    width: 100%;
    height: 255px !important;
  }
  </style>
  