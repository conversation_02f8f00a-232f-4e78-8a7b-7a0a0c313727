import request from '@/utils/request'

// 查询WMS天车基础表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarSelect',
    method: 'post',
    data
  })
}
// 新增WMS天车基础表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarInsert',
    method: 'post',
    data
  })
}
// 修改WMS天车基础表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarUpdate',
    method: 'post',
    data
  })
}
// 删除WMS天车基础表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodCarDelete',
    method: 'post',
    data
  })
}

// 修改WMS天车基础表--修改有效标识
export function editEnableFlag(data) {
  return request({
      url: 'aisEsbWeb/dcs/core/DcsApsFmodCarEnableFlagUpdate',
      method: 'post',
      data
  })
}

export default { sel, add, edit, del ,editEnableFlag}

