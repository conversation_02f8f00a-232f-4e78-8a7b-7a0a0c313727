# https://www.cnblogs.com/tkqq000/p/12669780.html 跨域
# https://blog.csdn.net/qq_33443033/article/details/83010122 VUE跨域
# https://www.pianshen.com/article/8963408883/ 跨域
# https://www.cnblogs.com/zzl521/p/12702018.html CORS跨域
# https://www.cnblogs.com/eye-like/p/13305801.html 跨域

ENV = 'production'

# 如果使用 Nginx 代理后端接口，那么此处需要改为 '/'，文件查看 Docker 部署篇，Nginx 配置
# 接口地址，注意协议，如果你没有配置 ssl，需要将 https 改为 http
#VUE_APP_BASE_API  = 'https://el-admin.xin'
#VUE_APP_BASE_API  = 'http://localhost:8090'
VUE_APP_BASE_API  = '/'

# 如果接口是 http 形式， wss 需要改为 ws
#VUE_APP_WS_API = 'wss://el-admin.xin'
#VUE_APP_WS_API = 'ws://localhost:8090'
