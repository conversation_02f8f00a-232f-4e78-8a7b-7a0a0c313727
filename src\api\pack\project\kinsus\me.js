import request from '@/utils/request'

// 关闭计划
export function closePlan(data) {
  return request({
    url: 'aisEsbWeb/pack/project/kinsus/op/ClosePlan',
    method: 'post',
    data
  })
}

// 执行计划
export function executePlan(data) {
  return request({
    url: 'aisEsbWeb/special/events/ExecutePlan',
    method: 'post',
    data
  })
}

// 删除多笔记录
export function deleteMultipleRecords(data) {
  return request({
    url: 'aisEsbWeb/special/events/DeleteMultipleRecords',
    method: 'post',
    data: data
  })
}

// 根据lot获取包装产品信息
export function getPackProductInfoByLot(lot) {
  return request({
    url: 'aisEsbWeb/special/events/GetPackProductInfoByLot',
    method: 'post',
    data: {
      lot_num: lot
    }
  })
}

// 请求打印
export function requestPrint(data) {
  return request({
    url: 'aisEsbWeb/pack/project/kinsus/op/RequestPrint',
    method: 'post',
    data
  })
}

export default { closePlan, deleteMultipleRecords, executePlan, getPackProductInfoByLot, requestPrint }

