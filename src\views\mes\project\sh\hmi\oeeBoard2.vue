<template>
  <div id="#bigScreen" class="mesContainer">
    <div class="header">
      <img src="@/assets/images/sh/headerLeft.png" alt="">
      <span>产线当日统计报表</span>
      <img src="@/assets/images/sh/headerRight.png" alt="">
    </div>

    <div style="display: flex;margin-top: 20px;">
      <div id="onlineDom" style="width: 50%;height: 480px" />
      <div id="offlineDom" style="width: 50%;height: 480px" />
    </div>
    <div style="margin-top: 20px;display: flex;">
      <div id="hoursDom" style="width: 50%;height: 500px" />
      <div id="qualifiedDom" style="width: 50%;height: 500px" />
    </div>

  </div>
</template>
<script>
import { PlanMoSel1, planMoSel, planMoMonthSel, PlanMoSel5 } from '@/api/mes/project/sh/shProductionScreen'
import autofit from 'autofit.js'
// import { ref } from 'vue'
export default {
  name: 'productionBoard',
  data() {
    return {
      hoursDom: null,
      qualifiedDom: null,
      onlineDom: null,
      offlineDom: null,
      mo_offline_count3: null,
      qualified3: null,
      productTimer: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getHoursProduction() // 获取当前小时产量
      this.getQualifiedProduction() // 获取当前产品一次性合格产量
      this.getOnlineNum() // 获取计划数/上线数占比
      this.getOfflineNum() // 获取计划数/下线数占比
      this.getDailyProduction()
      this.getDailyProduction2()
    })
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    this.productTimer = setInterval(() => {
      this.getHoursProduction() // 获取当前小时产量
      this.getQualifiedProduction() // 获取当前产品一次性合格产量
    }, 1000 * 2)
    this.productTimer = setInterval(() => {
      this.getOnlineNum() // 获取计划数/上线数占比
      this.getOfflineNum() // 获取计划数/下线数占比
      this.getDailyProduction()
      this.getDailyProduction2()
    }, 1000 * 10)
  },
  created() {
  },
  beforeDestroy() {

  },
  methods: {
    getDailyProduction2() {
      const query = {}
      planMoSel(query).then(res => {
        if (res.code === 0 && res.data !== '') {
          this.qualified3 = res.data[0].qualified3
          const offlineData = [
            { value: res.data[0].qualified1, name: '合格数', label: { fontSize: 28 }},
            { value: res.data[0].qualified2, name: '下线数', label: { fontSize: 28 }}
          ]
          this.getOfflineNum(offlineData)
          return
        }
        this.qualified3 = 0
        const offlineData = [
          { value: 0, name: '合格数', label: { fontSize: 28 }},
          { value: 0, name: '下线数', label: { fontSize: 28 }}
        ]
        this.getOfflineNum(offlineData)
      }).catch(err => {
        this.offlineDom = null
        this.$message({
          type: 'error',
          message: err.msg
        })
      })
    },
    getDailyProduction() {
      const query = {}
      PlanMoSel1(query).then(res => {
        if (res.code === 0 && res.data !== '') {
          this.mo_offline_count3 = res.data[0].mo_offline_count3
          const onlineData = [
            { value: res.data[0].mo_offline_count, name: '下线数', label: { fontSize: 28 }},
            { value: res.data[0].start_date2, name: '基准数', label: { fontSize: 28 }}
          ]
          this.getOnlineNum(onlineData)
          return
        }
        this.mo_offline_count3 = 0
        const onlineData = [
          { value: 0, name: '下线数', label: { fontSize: 28 }},
          { value: 0, name: '基准数', label: { fontSize: 28 }}
        ]
        this.getOnlineNum(onlineData)
      }).catch(err => {
        this.onlineDom = null
        this.$message({
          type: 'error',
          message: err.msg
        })
      })
    },
    getOnlineNum(data) {
      var that = this
      this.onlineDom = this.$echarts.init(document.getElementById('onlineDom'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        color: ['#FCCA00', '#16C734'],
        title: {
          text: '可动率:' + this.mo_offline_count3 + '%',
          textStyle: {
            color: '#fff',
            fontSize: 60
          }
        },
        legend: {
          x: 'center',
          y: 'top',
          textStyle: {
            color: '#fff',
            fontFamily: 'Alibaba PuHuiTi',
            fontSize: 24
          },
          itemWidth: 20, // 修改图例标识的宽度
          itemHeight: 20, // 修改icon图形大小
          itemGap: 24, // 修改间距
          data: ['下线数1', '基准数1']
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '70%',
            center: ['50%', '60%'],
            label: {
              normal: {
                show: false,
                formatter: '{b}: {c}({d}%)'
              }
            },
            data
          }
        ]
      }
      this.onlineDom.setOption(option)
      window.addEventListener('resize', function() {
        that.onlineDom.resize()
      })
    },
    getOfflineNum(data) {
      var that = this
      this.offlineDom = this.$echarts.init(document.getElementById('offlineDom'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        color: ['#FCCA00', '#16C734'],
        title: {
          text: '一次交检合格率:' + this.qualified3 + '%',
          textStyle: {
            color: '#fff',
            fontSize: 60
          }
        },
        legend: {
          x: 'center',
          y: 'top',
          textStyle: {
            color: '#fff',
            fontFamily: 'Alibaba PuHuiTi',
            fontSize: 24
          },
          itemWidth: 20, // 修改图例标识的宽度
          itemHeight: 20, // 修改icon图形大小
          itemGap: 24, // 修改间距
          data: ['合格数1', '下线数1']
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '70%',
            center: ['50%', '60%'],
            label: {
              normal: {
                show: false,
                formatter: '{b}: {c}({d}%)'
              }
            },
            data
          }
        ]
      }
      this.offlineDom.setOption(option)
      window.addEventListener('resize', function() {
        that.offlineDom.resize()
      })
    },
    getQualifiedProduction() {
      const query = {}
      var series = []
      PlanMoSel5(query).then(res => {
        if (res.code === 0 && res.data !== '') {
          series = [
            {
              name: '报警次数',
              type: 'bar',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: res.data.map(e => { return e.count }),
              lineStyle: {// 设置线条颜色
                color: '#5891E6' // 折线线条颜色

              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#34436C' // 设置线条上点的颜色（和图例的颜色）

                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#5891E6',
                fontSize: 50
              }
            }

          ]
          const data = {
            dom: 'qualifiedDom',
            color: ['#223871', '#265A5C'],
            title: '工位不良报警数TOP5',
            legendData: ['报警次数1'],
            xAxisData: res.data.map(e => { return e.station_code }),
            series
          }
          this.getEchartsData(data)
        } else {
          series = [
            {
              name: '报警次数',
              type: 'bar',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0],
              lineStyle: {// 设置线条颜色
                color: '#5891E6' // 折线线条颜色

              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#34436C' // 设置线条上点的颜色（和图例的颜色）

                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#5891E6',
                fontSize: 50
              }
            }

          ]
          const data = {
            dom: 'qualifiedDom',
            color: ['#223871'],
            title: '工位不良报警数TOP5',
            legendData: ['报警次数1'],
            xAxisData: [0, 0, 0, 0, 0],
            series
          }
          this.getEchartsData(data)
        }
      })
    },
    getHoursProduction() {
      const query = {}
      var series = []
      planMoMonthSel(query).then(res => {
        if (res.code === 0 && res.data !== '') {
          series = [
            {
              name: '下线数',
              type: 'line',
              areaStyle: {},
              data: res.data.map(e => { return e.count }),
              lineStyle: {// 设置线条颜色
                color: '#1DEEFF' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#285671' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#1DEEFF',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'hoursDom',
            color: ['#1DEEFF'],
            title: '每小时产量',
            legendData: ['下线数1'],
            xAxisData: res.data.map(e => { return e.time }),
            series
          }
          this.getEchartsData(data)
        } else {
          series = [
            {
              name: '下线数',
              type: 'line',
              areaStyle: {},
              data: [0, 0, 0, 0, 0],
              lineStyle: {// 设置线条颜色
                color: '#1DEEFF' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#285671' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#1DEEFF',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'hoursDom',
            color: ['#1DEEFF'],
            title: '每小时产量',
            legendData: ['下线数1'],
            xAxisData: [0, 0, 0, 0, 0],
            series
          }
          this.getEchartsData(data)
        }
      }).catch(err => {
        this.hoursDom = null
        this.$message({
          type: 'error',
          message: err.msg
        })
      })
    },
    getEchartsData(result) {
      var that = this
      this[result.dom] = this.$echarts.init(document.getElementById(result.dom))
      var option = {
        color: result.color,
        title: {
          text: result.title,
          textStyle: {
            color: '#fff',
            fontSize: 60
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
              color: '#fff'
            }
          }
        },
        legend: {
          x: 'center',
          y: 'bottom',
          textStyle: {
            color: '#fff',
            fontFamily: 'Alibaba PuHuiTi',
            fontSize: 42
          },
          icon: 'circle',
          itemWidth: 20, // 修改图例标识的宽度
          itemHeight: 20, // 修改icon图形大小
          itemGap: 24, // 修改间距
          data: result.legendData
        },
        grid: {
          top: '24%',
          left: '3%',
          right: '4%',
          bottom: '12%',
          containLabel: true
        },
        xAxis: [
          {
            axisLabel: {
              textStyle: {
                fontSize: 27,
                color: '#fff'
              }
            },
            axisLine: {
              onZero: false
            },
            type: 'category',
            boundaryGap: true,
            data: result.xAxisData
          }
        ],
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 30,
              color: '#fff'
            }
          }
        },
        series: result.series
      }
      this[result.dom].setOption(option)
      window.addEventListener('resize', function() {
        that[result.dom].resize()
      })
    }
  }
}
</script>
  <style lang="less" scoped>
  .mesContainer{
      background: #2B304D;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      transform-origin: 0 0;
      padding: 0 15px;
      .header{
          width: 100%;
          height: 100px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          span{
              font-size: 80px;
              color: #fff;
              font-weight: 600;
          }
      }
  }
  </style>
