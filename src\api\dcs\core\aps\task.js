import request from '@/utils/request'

// 查询生产任务
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskSelect',
    method: 'post',
    data
  })
}
// 新增生产任务
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsIntefProducTaskInsert',
    method: 'post',
    data
  })
}
// 修改生产任务
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsIntefProducTaskUpdate',
    method: 'post',
    data
  })
}
// 删除生产任务
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsIntefProducTaskDelete',
    method: 'post',
    data
  })
}
// 修改生产任务--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelEnableFlagUpdate',
    method: 'post',
    data
  })
}
// 导出生产任务
export function down(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsIntefProducTaskExportTemplate',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 上移
export function taskUp(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskUp',
    method: 'post',
    data
  })
}
// 下移
export function taskDown(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskDown',
    method: 'post',
    data
  })
}
// 确认发布
export function taskPublish(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskManualPublish',
    method: 'post',
    data
  })
}
// 导出报工文件
export function exportBg(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsIntefProducExportBg',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 手动报工
export function operationBg(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsIntefProducManualOperationBg',
    method: 'post',
    data
  })
}
// 查询切割机
export function selStation(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/FactoryStationSel',
    method: 'post',
    data
  })
}
// 批量解析文件
export function FjSendTask(data) {
  return request({
    url: 'aisEsbApi/dcs/core/interf/fj/send/DcsCoreFjSendTask',
    method: 'post',
    data
  })
}

// 获取钢板型号
export function getFmodModel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelSelectOnlyForTaskInsert',
    method: 'post',
    data
  })
}

// 获取报警信息
export function getRcsEvent(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/bDcsFlowEventTraySelect',
    method: 'post',
    data
  })
}

// 修改报警处理标识
export function editHandleFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/bDcsFlowEventTrayUpdate',
    method: 'post',
    data
  })
}
// 任务撤回功能
export function taskWithdraw(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsTaskWithdraw',
    method: 'post',
    data
  })
}
export function updateEventStatus(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/updateEventStatus',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag, down, taskUp, taskDown, taskPublish, exportBg, operationBg, selStation, FjSendTask, getFmodModel, getRcsEvent, editHandleFlag, taskWithdraw, updateEventStatus }

