<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.batchNo') + ': '" label-width="120px">
                <el-input v-model="query.group_lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.subBatchNo') + ': '" label-width="120px">
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.subSimplifiedCode') + ': '" label-width="120px">
                <el-input v-model="query.lot_short_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.materialNo') + ': '" label-width="120px">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.carrierBarcode') + ': '" label-width="120px">
                <el-input v-model="query.pallet_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.taskSource') + ': '" label-width="120px">
                <el-select
                  v-model="query.task_from"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['AIS','EAP','MES']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.station') + ': '" label-width="120px">
                <el-select
                  v-model="query.station_id"
                  filterable
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.portNo') + ': '" label-width="120px">
                <el-input v-model="query.port_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.boardBarcode') + ': '" label-width="120px">
                <el-input v-model="query.panel_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.boardStatus') + ': '" label-width="120px">
                <el-select
                  v-model="query.panel_status"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['OK','NG','NG_PASS']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.boardErrorCode') + ': '" label-width="120px">
                <el-select
                  v-model="query.panel_ng_code"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option
                    v-for="item in [{ 'label': $t('view.enum.boardErrorCode.normal'), 'value': 0 },
                                    { 'label': $t('view.enum.boardErrorCode.mixedBoard'), 'value': 1 },
                                    { 'label': $t('view.enum.boardErrorCode.readFailureBoard'), 'value': 2 },
                                    { 'label': $t('view.enum.boardErrorCode.duplicateCode'), 'value': 3 },
                                    { 'label': $t('view.enum.boardErrorCode.forcedPass'), 'value': 4 },
                                    { 'label': $t('view.enum.boardErrorCode.otherDefined'), 'value': 5 }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.isFirstCheck') + ': '" label-width="120px">
                <el-select
                  v-model="query.inspect_flag"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['N','Y']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.isDummy') + ': '" label-width="120px">
                <el-select
                  v-model="query.dummy_flag"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['N','Y']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.manualProcessingBoard') + ': '" label-width="120px">
                <el-select
                  v-model="query.manual_judge_code"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option
                    v-for="item in [{ 'label': $t('view.enum.manualProcessingBoard.manualInputMode'), 'value': 1 },
                                    { 'label': $t('view.enum.manualProcessingBoard.reread'), 'value': 2 },
                                    { 'label': $t('view.enum.manualProcessingBoard.forcedPass'), 'value': 3 }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.hasPanelMode') + ': '" label-width="120px">
                <el-select
                  v-model="query.panel_flag"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['N','Y']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.operator') + ': '" label-width="120px">
                <el-input v-model="query.user_name_q" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.rooftopBarcode') + ': '" label-width="120px">
                <el-input v-model="query.tray_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.orientation') + ': '" label-width="120px">
                <el-select
                  v-model="query.face_code"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option
                    v-for="item in [{ 'label': $t('view.form.orientationToFront'), 'value': 0 },
                                    { 'label': $t('view.form.orientationToBack'), 'value': 1 }]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.isOffline') + ': '" label-width="120px">
                <el-select
                  v-model="query.offline_flag"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['N','Y']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.timePeriod') + ': '" label-width="120px">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:100%"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button type="primary" icon="el-icon-upload2" :disabled="crud.data.length <= 0" :loading="downloadLoading" @click="exportExecl">{{ $t('view.button.export') }}</el-button>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('view.button.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('view.button.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column :show-overflow-tooltip="true" prop="item_date" width="130" :label="$t('view.table.time')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_num" width="100" :label="$t('view.table.subTaskNo')" />
            <el-table-column :show-overflow-tooltip="true" prop="port_code" width="100" :label="$t('view.table.portNo')" />
            <el-table-column :show-overflow-tooltip="true" prop="tray_barcode" width="120" :label="$t('view.table.carrierBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_num" width="120" :label="$t('view.table.rooftopBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_barcode" width="120" :label="$t('view.table.boardBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_status" width="100" :label="$t('view.table.boardStatus')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_code" width="150" :label="$t('view.table.ngBoardErrorCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_msg" width="150" :label="$t('view.table.ngBoardErrorDescription')" />
            <el-table-column :show-overflow-tooltip="true" prop="inspect_flag" width="150" :label="$t('view.table.isFirstCheckBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="task_from" width="100" :label="$t('view.table.taskSource')" />
            <el-table-column :show-overflow-tooltip="true" prop="group_lot_num" width="100" :label="$t('view.table.motherBatch')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_short_num" width="100" :label="$t('view.table.subTaskSimplifiedCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_index" width="100" :label="$t('view.table.subTaskSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="material_code" width="100" :label="$t('view.table.materialNo')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_type" width="100" :label="$t('view.table.carrierType')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_level" width="100" :label="$t('view.table.layer')" />
            <el-table-column :show-overflow-tooltip="true" prop="fp_index" width="100" :label="$t('view.table.subDiskSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_length" width="100" :label="$t('view.table.boardLength')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_width" width="100" :label="$t('view.table.boardWidth')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_tickness" width="100" :label="$t('view.table.boardThickness')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_index" width="100" :label="$t('view.table.boardSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="dummy_flag" width="120" :label="$t('view.table.isDummyBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="manual_judge_code" width="100" :label="$t('view.table.manualProcessingBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_flag" width="120" :label="$t('view.table.isPanelMode')" />
            <el-table-column :show-overflow-tooltip="true" prop="user_name" width="100" :label="$t('view.table.operator')" />
            <el-table-column :show-overflow-tooltip="true" prop="eap_flag" width="100" :label="$t('view.table.isEAP')" />
            <el-table-column :show-overflow-tooltip="true" prop="face_code" width="100" :label="$t('view.table.orientation')" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >
                {{ $t('view.pagination.total') }}: {{ page.total }}</el-button>
              <el-button
                type="primary"
              >
                {{ $t('view.pagination.current') }}{{ nowPageIndex }}{{ $t('view.pagination.unit') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >
                &lt;&nbsp;{{ $t('view.pagination.previous') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >
                {{ $t('view.pagination.next') }}&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/index'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import { sel as selStation } from '@/api/core/factory/sysStation'
import crudEapStationFlow from '@/api/eap/eapStationFlow'
const defaultForm = {

}
export default {
  name: 'EapStationFlow',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.boardPractice'),
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'station_flow_id',
      // // 排序
      sort: ['station_flow_id asc'],
      // CRUD Method
      crudMethod: { ...crudEapStationFlow },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      stationData: [],
      height: document.documentElement.clientHeight - 330,
      downloadLoading: false,
      permission: {
        add: ['admin', 'eap_station_flow:add'],
        edit: ['admin', 'eap_station_flow:edit'],
        del: ['admin', 'eap_station_flow:del'],
        down: ['admin', 'eap_station_flow:down']
      }
    }
  },
  created() {
    this.getStationList()
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 330
    }
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('view.dialog.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('view.dialog.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    getStationList() {
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y'
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('view.dialog.queryException'),
            type: 'error'
          })
        })
    },
    exportExecl() {
      this.downloadLoading = true
      var query = {
        'sort': 'station_flow_id asc',
        userName: Cookies.get('userName')
      }
      var result = {}
      for (const key in this.query) {
        if (key !== 'tableSize') {
          result[key] = this.query[key]
        }
      }
      query = Object.assign(query, result)
      crudEapStationFlow.down(query).then(res => {
        downloadFile(res, this.$t('view.title.boardPractice') + '_' + this.$t('view.button.export'), 'xlsx')
        this.downloadLoading = false
      }).catch(() => {
        this.downloadLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
  color: #fff;
  background-color: #1473c5;
  border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
  background: #438fd1;
  border-color: #438fd1;
  color: #fff;
}
.labelIline {
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label {
    white-space: nowrap;
  }
}
::v-deep .wrapElFormFirst {
  .el-form-item__label {
    white-space: nowrap;
    margin: 0;
    width: 80px;
  }
}
::v-deep .el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 235px;
}
::v-deep .el-date-editor .el-range-separator {
  width: 12%;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  width: 130px;
}
</style>
