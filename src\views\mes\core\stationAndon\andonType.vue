<template>
  <el-row
    :gutter="20"
    style="padding-top: 10px; margin-left: 0px; margin-right: 0px"
  >
    <el-col :span="6">
      <el-card class="box-card1" shadow="always">
        <div
          slot="header"
          class="clearfix"
          style="font-size: 14px; color: #757575; font-weight: 700"
        >
          <span>产线</span>
        </div>
        <el-tree
          :data="treeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          :highlight-current="true"
          :render-content="handleTreeRenderContent"
          @node-click="handleNodeClick"
        />
      </el-card>
    </el-col>
    <el-col :span="18" style="padding-left: 0px">
      <el-card v-if="MesAndonTypeShow" class="box-card1" shadow="always">
        <div
          slot="header"
          class="clearfix"
          style="
            font-size: 14px;
            color: #757575;
            font-weight: 700;
            text-align: right;
          "
        >
          <span style="float: left">安灯大类型维护</span>
          <el-input
            v-model="queryHeader.andon_type_code_des"
            :placeholder="contentPlaceholder"
            class="filter-item"
            size="mini"
            style="width: 150px"
          />
          <el-button
            class="filter-item"
            size="mini"
            type="primary"
            icon="el-icon-search"
            style="margin-top: 3px"
            @click="handleQuery"
          >搜索</el-button>
          <el-button
            v-if="true"
            class="filter-item"
            size="mini"
            type="primary"
            icon="el-icon-plus"
            style="margin-top: 3px; margin-left: 5px"
            plain
            @click="handleAdd"
          >
            新增
          </el-button>
        </div>
        <el-table
          ref="table"
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :stripe="true"
          height="400px"
          :highlight-current-row="true"
          @sort-change="sortChage"
        >
          <el-table-column
            :show-overflow-tooltip="true"
            prop="andon_type_code"
            min-width="100"
            label="安灯大类型编码"
            sortable="custom"
          />
          <el-table-column
            :show-overflow-tooltip="true"
            prop="andon_type_des"
            min-width="100"
            label="安灯大类型描述"
            sortable="custom"
          />
          <el-table-column
            :show-overflow-tooltip="true"
            prop="andon_type_cycle"
            min-width="100"
            label="循环时间"
            sortable="custom"
          />

          <el-table-column
            label="有效标识"
            align="center"
            prop="enable_flag"
            width="100"
          >
            <template
              slot-scope="scope"
            ><!--取到当前单元格-->
              {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
            </template>
          </el-table-column>
          <!-- Table单条操作-->
          <el-table-column
            label="操作"
            width="125"
            align="center"
            fixed="right"
            prop="tableButton"
          >
            <template slot-scope="scope">
              <el-link
                icon="el-icon-edit"
                type="primary"
                style="color: #0072d6; font-size: 12px"
                @click="handleTableEdit(scope.row)"
              >编辑</el-link>
              &nbsp;&nbsp;
              <el-link
                icon="el-icon-delete"
                type="primary"
                style="color: #0072d6; font-size: 12px"
                @click="handleTableDel(scope.row)"
              >删除</el-link>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <!--<pagination />-->
        <el-pagination
          :page-size.sync="pageTable.size"
          :total="pageTable.total"
          :current-page.sync="pageTable.page"
          :page-sizes="[20, 30, 40, 50, 100]"
          style="margin-top: 8px; float: right"
          layout="total, prev, pager, next, sizes"
          @size-change="toSizeTableChangeHandler($event)"
          @current-change="toPageTableChangeHandler"
        />

        <el-drawer
          append-to-body
          :title="dialogTitle"
          :visible.sync="dialogVisbleSync"
          size="450px"
          @closed="drawerClosed"
        >
          <!--<el-form ref="form" :inline="true" :model="form" :rules="rules" size="small" label-width="100px">-->
          <el-form
            ref="form"
            :inline="false"
            :model="form"
            :rules="rules"
            size="small"
            label-width="110px"
          >
            <el-form-item label="安灯大类型编码" prop="andon_type_code">
              <el-input v-model="form.andon_type_code" style="width: 90%" />
            </el-form-item>
            <el-form-item label="安灯大类型描述" prop="andon_type_des">
              <el-input v-model="form.andon_type_des" style="width: 90%" />
            </el-form-item>
            <el-form-item label="循环时间" prop="andon_type_cycle">
              <el-input
                v-model.number="form.andon_type_cycle"
                style="width: 90%"
              />
            </el-form-item>
            <el-form-item label="有效标识">
              <el-radio-group
                v-model="form.enable_flag"
                :disabled="false"
                style="width: 90%"
              >
                <el-radio label="Y">有效</el-radio>
                <el-radio label="N">失效</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <el-divider />
          <div style="text-align: center; margin-bottom: 10px">
            <el-button
              size="small"
              icon="el-icon-close"
              plain
              @click="handleFromCancel"
            >取消</el-button>
            <el-button
              type="primary"
              size="small"
              icon="el-icon-check"
              @click="handleFormSubmit('form')"
            >确认</el-button>
          </div>
          <!--</el-dialog>-->
        </el-drawer>
      </el-card>
      <MesAndonStationType
        v-if="MesAndonStationTypeShow"
        ref="MesAndonStationType"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import {
  queryAllMesAndonTypeTree,
  queryAllMesAndonType,
  insMesAndonType,
  updMesAndonType,
  delMesAndonType
} from '@/api/mes/core/andonType'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import MesAndonStationType from '@/views/mes/core/stationAndon/andonStationType'

export default {
  name: 'MES_ANDON_TYPE',
  components: { MesAndonStationType },
  cruds() {
    return CRUD({ title: 'Tag', queryOnPresenterCreated: false })
  },
  mixins: [presenter(), header(), form(null), crud()],

  // Table 内按钮状态(组/明细)
  props: {},

  // 数据模型
  data() {
    var checkAndonTypeCycle = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('请输入循环时间'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',
      contentPlaceholder: '安灯大类型编码/描述',
      queryHeader: {
        andon_type_code_des: ''
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentNode: [],
      currentProdLineId: '',
      tableLoading: false,
      tableData: [],
      selectedTable: [], // 已选择项
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },
      query: {
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'andon_type_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      dialogVisbleSync: false,
      dialogTitle: '安灯大类型维护',
      form: {
        andon_type_id: '',
        prod_line_id: '',
        andon_type_code: '',
        andon_type_des: '',
        andon_type_cycle: '',
        enable_flag: ''
      },
      rules: {
        // 提交验证规则
        andon_type_code: [
          { required: true, message: '请输入安灯大类型编码', trigger: 'blur' }
        ],
        andon_type_des: [
          { required: true, message: '请输入安灯大类型描述', trigger: 'blur' }
        ],
        andon_type_cycle: [
          { required: true, validator: checkAndonTypeCycle, trigger: 'blur' }
        ]
      },
      MesAndonTypeShow: true,
      MesAndonStationTypeShow: false
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  created: function() {
    queryAllMesAndonTypeTree({})
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.length > 0) {
          this.treeData = defaultQuery.data
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    handleNodeClick(data) {
      this.currentNode = data
      this.currentProdLineId = data.prod_line_id
      if (data.level === 1) {
        if (!this.MesAndonTypeShow) {
          this.MesAndonStationTypeShow = false
          this.MesAndonTypeShow = true
        }
        this.handleQuery()
      } else if (data.level === 2) {
        if (!this.MesAndonStationTypeShow) {
          this.MesAndonTypeShow = false
          this.MesAndonStationTypeShow = true
        }
        this.$nextTick((x) => {
          this.$refs.MesAndonStationType.parentQuery(data.andon_type_id)
        })
      }
    },
    handleAdd() {
      if (this.currentProdLineId === '') {
        this.$message({
          message: '请先选择产线',
          type: 'info'
        })
        return
      }
      this.form.andon_type_id = ''
      this.form.prod_line_id = this.currentProdLineId
      this.form.andon_type_code = ''
      this.form.andon_type_des = ''
      this.form.andon_type_cycle = ''
      this.form.enable_flag = 'Y'
      this.dialogTitle = '新增安灯大类型'
      this.dialogVisbleSync = true
    },
    handleQuery() {
      if (this.currentProdLineId === '') {
        this.$message({
          message: '请先选择产线',
          type: 'info'
        })
        return
      }
      const query = {
        prod_line_id: this.currentProdLineId,
        andon_type_code_des: this.queryHeader.andon_type_code_des,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }

      this.tableLoading = true
      queryAllMesAndonType(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableData = defaultQuery.data
            } else {
              this.tableData = []
            }
            this.pageTable.total = defaultQuery.count
            this.tableLoading = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleTableEdit(data) {
      this.form.andon_type_id = data.andon_type_id
      this.form.prod_line_id = data.prod_line_id
      this.form.andon_type_code = data.andon_type_code
      this.form.andon_type_des = data.andon_type_des
      this.form.andon_type_cycle = data.andon_type_cycle
      this.form.enable_flag = data.enable_flag
      this.dialogTitle = '编辑安灯大类型'
      this.dialogVisbleSync = true
    },
    handleTableDel(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除${data.andon_type_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            id: data.andon_type_id
          }

          delMesAndonType(del)
            .then((res) => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                this.handleAndonTypeDelTreeNode(data.andon_type_id)
                // 查询
                this.handleQuery()
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: 'error'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    handleFormSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            andon_type_id: this.form.andon_type_id,
            prod_line_id: this.form.prod_line_id,
            andon_type_code: this.form.andon_type_code,
            andon_type_des: this.form.andon_type_des,
            andon_type_cycle: this.form.andon_type_cycle,
            enable_flag: this.form.enable_flag
          }
          // 新增
          if (
            this.form.andon_type_id === undefined ||
            this.form.andon_type_id.length <= 0
          ) {
            insMesAndonType(save)
              .then((res) => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  this.dialogVisbleSync = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  this.handleAndonTypeAddTreeNode(
                    this.form.prod_line_id,
                    defaultQuery.result,
                    this.form.andon_type_code,
                    this.form.andon_type_des
                  )
                  // 查询
                  this.handleQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updMesAndonType(save)
              .then((res) => {
                const defaultQuery = JSON.parse(JSON.stringify(res))

                if (defaultQuery.code === 0) {
                  this.dialogVisbleSync = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  this.handleAndonTypeEditTreeNode(
                    this.form.andon_type_id,
                    this.form.andon_type_code,
                    this.form.andon_type_des
                  )
                  // 查询
                  this.handleQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    handleFromCancel() {
      // 取消
      this.dialogVisbleSync = false // 弹出框隐藏
    },
    drawerClosed() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val

      // 查询
      this.handleQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val

      // 查询
      this.handleQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'andon_type_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.handleQuery()
    },
    handleTreeRenderContent(h, { node, data, store }) {
      if (node.level === 1) {
        return (
          <span class='custom-tree-node'>
            <span>
              <i class='el-icon-share'></i> {node.label}
            </span>
          </span>
        )
      } else if (node.level === 2) {
        return (
          <span class='custom-tree-node'>
            <span>
              <i class='el-icon-setting'></i> {node.label}
            </span>
          </span>
        )
      } else {
        return (
          <span class='custom-tree-node'>
            <span>
              <i class='el-icon-document'></i> {node.label}
            </span>
          </span>
        )
      }
    },
    handleAndonTypeAddTreeNode(
      prod_line_id,
      andon_type_id,
      andon_type_code,
      andon_type_des
    ) {
      if (!this.currentNode.children) {
        this.$set(this.currentNode, 'children', [])
      }
      this.currentNode.children.push({
        level: 2,
        prod_line_id: prod_line_id,
        andon_type_id: andon_type_id,
        andon_type_code: andon_type_code,
        andon_type_des: andon_type_des,
        label: '[' + andon_type_code + '] ' + andon_type_des
      })
    },
    handleAndonTypeEditTreeNode(
      andon_type_id,
      andon_type_code,
      andon_type_des
    ) {
      const items = this.currentNode.children.filter(
        (item) => item.andon_type_id.toString() === andon_type_id.toString()
      )[0]
      items.andon_type_code = andon_type_code
      items.andon_type_des = andon_type_des
      items.label = '[' + andon_type_code + '] ' + andon_type_des
    },
    handleAndonTypeDelTreeNode(andon_type_id) {
      const index = this.currentNode.children.findIndex(
        (item) => item.andon_type_id.toString() === andon_type_id.toString()
      )
      this.currentNode.children.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card1 {
  min-height: calc(100vh - 30px);
  .el-card__header {
    padding: 0px 20px;
    height: 40px;
    line-height: 40px;
  }
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}

.box-card2 {
  .el-card__header {
    padding: 0px 20px;
    height: 40px;
    line-height: 40px;
  }
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}
.el-drawer {
  overflow-y: scroll;
}
.inputSearch {
    display: flex;
    align-items: center;
    margin-right: 15px;
    .inputItem {
      .el-input__inner {
        width: 150px;
        border-radius: 4px 0 0 4px !important;
      }
    }
    .buttonItem {
      border-radius: 0 0.25rem 0.25rem 0;
      margin-left: -5px;
      z-index: 9;
    }
  }
</style>
