<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="版本号：">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="条码规则编码：">
                <el-input v-model="query.barcode_rule_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="条码规则描述：">
                <el-input v-model="query.barcode_rule_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-document" plain round @click="recipeDrawerVisible = true">
            配方
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="配方" prop="recipe_id">
            <el-select v-model="form.recipe_id" filterable clearable>
              <el-option v-for="item in recipeList" :key="item.recipe_id" :label="item.recipe_name + ' ' + item.recipe_version" :value="item.recipe_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="条码规则编码" prop="barcode_rule_code">
            <el-input v-model="form.barcode_rule_code" />
          </el-form-item>
          <el-form-item label="条码规则描述" prop="barcode_rule_des">
            <el-input v-model="form.barcode_rule_des" />
          </el-form-item>
          <el-form-item label="条码长度" prop="barcode_length">
            <el-input v-model.number="form.barcode_length" />
          </el-form-item>
          <el-form-item label="增量值" prop="num_increase">
            <el-input v-model.number="form.num_increase" />
          </el-form-item>
          <el-form-item label="当前值" prop="next_num">
            <el-input v-model.number="form.next_num" />
          </el-form-item>
          <el-form-item label="是否复位" prop="change_recov_flag">
            <el-select v-model="form.change_recov_flag">
              <el-option v-for="item in [{ label: '是', id: 'Y' }, { label: '否', id: 'N' }]" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="复位值" prop="recov_next_num">
            <el-input v-model.number="form.recov_next_num" :disabled="form.change_recov_flag !== 'Y'" />
          </el-form-item>
          <el-form-item label="固定条件1" prop="fix_field1">
            <el-input v-model="form.fix_field1" />
          </el-form-item>
          <el-form-item label="固定条件2" prop="fix_field2">
            <el-input v-model="form.fix_field2" />
          </el-form-item>
          <el-form-item label="固定条件3" prop="fix_field3">
            <el-input v-model="form.fix_field3" />
          </el-form-item>
          <el-form-item label="固定条件4" prop="fix_field4">
            <el-input v-model="form.fix_field4" />
          </el-form-item>
          <el-form-item label="固定条件5" prop="fix_field5">
            <el-input v-model="form.fix_field5" />
          </el-form-item>
          <el-form-item label="固定条件6" prop="fix_field6">
            <el-input v-model="form.fix_field6" />
          </el-form-item>
          <el-form-item label="固定条件7" prop="fix_field7">
            <el-input v-model="form.fix_field7" />
          </el-form-item>
          <el-form-item label="固定条件8" prop="fix_field8">
            <el-input v-model="form.fix_field8" />
          </el-form-item>
          <el-form-item label="变化条件类型" prop="change_field_type">
            <el-input v-model="form.change_field_type" />
          </el-form-item>
          <el-form-item label="条码附属固定值1" prop="attr_field1">
            <el-input v-model="form.attr_field1" />
          </el-form-item>
          <el-form-item label="条码附属固定值2" prop="attr_field2">
            <el-input v-model="form.attr_field2" />
          </el-form-item>
          <el-form-item label="条码附属固定值3" prop="attr_field3">
            <el-input v-model="form.attr_field3" />
          </el-form-item>
          <el-form-item label="条码附属固定值4" prop="attr_field4">
            <el-input v-model="form.attr_field4" />
          </el-form-item>
          <el-form-item label="条码附属固定值5" prop="attr_field5">
            <el-input v-model="form.attr_field5" />
          </el-form-item>
          <el-form-item label="条码附属固定值6" prop="attr_field6">
            <el-input v-model="form.attr_field6" />
          </el-form-item>
          <el-form-item label="条码附属固定值7" prop="attr_field7">
            <el-input v-model="form.attr_field7" />
          </el-form-item>
          <el-form-item label="条码附属固定值8" prop="attr_field8">
            <el-input v-model="form.attr_field8" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div class="ruleBottom">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-drawer append-to-body :wrapper-closable="false" title="配方信息" :visible.sync="recipeDrawerVisible" size="70%" @closed="getRecipeList()">
        <recipe v-if="recipeDrawerVisible" ref="recipe" />
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="配方ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_id }}</el-descriptions-item>
                  <el-descriptions-item label="配方类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_type }}</el-descriptions-item>
                  <el-descriptions-item label="配方描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_name }}</el-descriptions-item>
                  <el-descriptions-item label="版本号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recipe_version }}</el-descriptions-item>
                  <el-descriptions-item label="责任人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.liable_person }}</el-descriptions-item>
                  <el-descriptions-item label="更新说明" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.update_remark }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.enable_flag }}</el-descriptions-item>
                  <el-descriptions-item label="条码规则ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.barcode_rule_id }}</el-descriptions-item>
                  <el-descriptions-item label="条码规则编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.barcode_rule_code }}</el-descriptions-item>
                  <el-descriptions-item label="条码规则描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.barcode_rule_des }}</el-descriptions-item>
                  <el-descriptions-item label="条码长度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.barcode_length }}</el-descriptions-item>
                  <el-descriptions-item label="增量值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.num_increase }}</el-descriptions-item>
                  <el-descriptions-item label="当前值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.next_num }}</el-descriptions-item>
                  <el-descriptions-item label="是否复位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.change_recov_flag }}</el-descriptions-item>
                  <el-descriptions-item label="复位值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recov_next_num }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field1 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field2 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field3 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件4" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field4 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件5" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field5 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件6" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field6 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件7" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field7 }}</el-descriptions-item>
                  <el-descriptions-item label="固定条件8" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fix_field8 }}</el-descriptions-item>
                  <el-descriptions-item label="变化条件类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.change_field_type }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field1 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field2 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field3 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值4" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field4 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值5" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field5 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值6" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field6 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值7" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field7 }}</el-descriptions-item>
                  <el-descriptions-item label="条码附属固定值8" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attr_field8 }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_name" min-width="100" label="配方描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_version" min-width="100" label="版本号" />
            <el-table-column  :show-overflow-tooltip="true" prop="liable_person" min-width="100" label="责任人" />
            <el-table-column  :show-overflow-tooltip="true" prop="barcode_rule_code" min-width="100" label="条码规则编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="barcode_rule_des" min-width="100" label="条码规则描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="barcode_length" min-width="100" label="条码长度" />
            <el-table-column  :show-overflow-tooltip="true" prop="num_increase" min-width="100" label="增量值" />
            <el-table-column  :show-overflow-tooltip="true" prop="next_num" min-width="100" label="当前值" />
            <el-table-column  :show-overflow-tooltip="true" prop="change_recov_flag" min-width="100" label="是否复位" />
            <el-table-column  :show-overflow-tooltip="true" prop="recov_next_num" min-width="100" label="复位值" />
            <el-table-column  :show-overflow-tooltip="true" prop="change_field_type" min-width="100" label="变化条件类型" />
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column  label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudBarcodeRule from '@/api/mes/core/barcodeRule'
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import recipe from '@/views/mes/core/recipe/recipe'
const defaultForm = {
  recipe_id: '',
  barcode_rule_id: '',
  barcode_rule_code: '',
  barcode_rule_des: '',
  barcode_length: '',
  num_increase: '',
  next_num: '',
  change_recov_flag: 'N',
  recov_next_num: '0',
  fix_field1: '',
  fix_field2: '',
  fix_field3: '',
  fix_field4: '',
  fix_field5: '',
  fix_field6: '',
  fix_field7: '',
  fix_field8: '',
  change_field_type: '',
  attr_field1: '',
  attr_field2: '',
  attr_field3: '',
  attr_field4: '',
  attr_field5: '',
  attr_field6: '',
  attr_field7: '',
  attr_field8: '',
  enable_flag: 'Y'
}
export default {
  name: 'MES_RECIPE_BARCODE_RULE',
  components: { crudOperation, rrOperation, udOperation, pagination, recipe },
  cruds() {
    return CRUD({
      title: '条码生成规则信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'barcode_rule_id',
      // 排序
      sort: ['barcode_rule_id asc'],
      // CRUD Method
      crudMethod: { ...crudBarcodeRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('不能为空'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    var checkRecovNextNum = (rule, value, callback) => {
      if (this.form.change_recov_flag === 'Y') {
        if (value.length === 0) {
          return callback(new Error('不能为空'))
        }
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_recipe_barcode_rule:add'],
        edit: ['admin', 'mes_recipe_barcode_rule:edit'],
        del: ['admin', 'mes_recipe_barcode_rule:del'],
        down: ['admin', 'mes_recipe_barcode_rule:down']
      },
      rules: {
        recipe_id: [{ required: true, message: '请选择配方', trigger: 'blur' }],
        barcode_rule_code: [{ required: true, message: '请输入条码规则编码', trigger: 'blur' }],
        barcode_rule_des: [{ required: true, message: '请输入条码规则描述', trigger: 'blur' }],
        barcode_length: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        num_increase: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        next_num: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        recov_next_num: [{ required: true, validator: checkRecovNextNum, trigger: 'blur' }]
      },
      recipeDrawerVisible: false,
      recipeList: []
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.getRecipeList()
  },
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将条码规则为【' + data.barcode_rule_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudBarcodeRule
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              barcode_rule_id: data.barcode_rule_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    getRecipeList() {
      this.recipeList = []
      crudRecipe
        .sel({
          user_name: Cookies.get('userName'),
          recipe_type: 'CODERULE',
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.recipeList = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
.ruleBottom {
  text-align: center;
  margin: 40px 0;
}
</style>
