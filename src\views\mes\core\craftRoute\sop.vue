<template>
  <el-row :gutter="20">
    <el-col :span="12" style="width:610px">
      <SopChart
        ref="chart"
        v-loading="loading"
        :nodes="nodes"
        :connections="connections"
        :width="'370'"
        :height="'360'"
        :readonly="false"
        element-loading-text="拼命绘制流程图中"
        @editnode="handleEditNode"
        @dblclick="handleDblClick"
        @editconnection="handleEditConnection"
        @save="handleChartSave"
        @delnode="handleDelNode"
      />
    </el-col>

    <el-col :span="12">

      <el-descriptions :column="1" border style="margin-top:10px;">
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-tickets" />
            默认直径
          </template>
          <el-input v-model.number="diameter" style="width:100px;" />
          <el-button-group style="float:right;">
            <el-button type="primary" icon="el-icon-plus" @click="batchAdd()">批量添加</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="add()">添加</el-button>
            <el-button type="primary" icon="el-icon-edit" @click="save()">保存</el-button>
          </el-button-group>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-tickets" />
            编号
          </template>
          {{ nodeForm.id }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-tickets" />
            序号
          </template>
          <el-input v-model="nodeForm.index" />
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-tickets" />
            X轴
          </template>
          <el-input v-model="nodeForm.x" />
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-tickets" />
            Y轴
          </template>
          <el-input v-model="nodeForm.y" />
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-tickets" />
            直径
          </template>
          <el-input v-model.number="nodeForm.width" />
        </el-descriptions-item>
      </el-descriptions>
    </el-col>
  </el-row>
</template>
<script>
/* eslint-disable no-unused-vars */

import crudrecipeCrPdure from '@/api/mes/core/recipeCrPdure'
import { selTagColOrder } from '@/api/mes/core/recipeCrPdureQuality'
import Cookies from 'js-cookie'
import SopChart from '@/components/SopChart/index'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
export default {
  components: {
    SopChart
  },
  props: {
    proceduce_id: {
      type: [String, Number],
      default: -1
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 60,
      diameter: 15,
      nodes: [],
      connections: [],
      nodeForm: { },
      thisH: null,
      loading: false
    }
  },
  watch: {
    nodeForm: {
      immediate: true,
      deep: true,
      handler() {
        this.handleRefreshChart()
      }
    }
  },
  mounted() {
  },
  created() {
    this.LoadSopChart()
  },
  methods: {
    batchAdd() {
      const query = {
        user_name: Cookies.get('userName'),
        proceduce_id: this.proceduce_id
      }
      selTagColOrder(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              let _id = +new Date()
              let x = 20
              let y = this.diameter + 10
              defaultQuery.data.forEach(element => {
                this.$refs.chart.add({
                  id: _id,
                  x: x,
                  y: y,
                  type: 'circle',
                  index: element.tag_col_order,
                  width: this.diameter,
                  height: this.diameter,
                  color: '#C8CACC',
                  describe: '圆圈',
                  stroke: '#FFFFFF',
                  strokeWidth: '2px',
                  status: 'WAIT'
                })
                _id++
                x = x + (this.diameter * 2)
                if (x >= 500) {
                  x = 20
                  y = y + (this.diameter * 2)
                }
              })
            }
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'warning' })
          }
        })
        .catch(() => {
          this.$message({
            message: '保存异常',
            type: 'error'
          })
        })
    },
    add() {
      this.$refs.chart.add({
        id: +new Date(),
        x: 50,
        y: 50,
        type: 'circle',
        index: 1,
        width: this.diameter,
        height: this.diameter,
        color: '#C8CACC',
        describe: '圆圈',
        stroke: '#FFFFFF',
        strokeWidth: '2px',
        status: 'WAIT'
      })
    },
    save() {
      this.$refs.chart.save()
    },
    handleDblClick(position) {},
    handleDelNode(type, id) {

    },
    async handleChartSave(nodes, connections) {
      const save = {
        user_name: Cookies.get('userName'),
        proceduce_id: this.proceduce_id,
        pdure_chart: '{"nodes":' + JSON.stringify(nodes) + ',"connections":' + JSON.stringify(connections) + '}'
      }
      // 修改
      crudrecipeCrPdure.saveImage(save)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '保存成功', type: 'success' })
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'warning' })
          }
        })
        .catch(() => {
          this.$message({
            message: '保存异常',
            type: 'error'
          })
        })
    },
    handleRefreshChart() {
      if (this.$refs.chart !== undefined) {
        this.$refs.chart.init(this.nodeForm, this.thisH)
      }
    },
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
    handleEditConnection(connection) {
      // this.connectionForm.target = connection;
      // this.connectionDialogVisible = true;
    },
    LoadSopChart() {
      this.loading = true
      crudrecipeCrPdure.sel({
        user_name: Cookies.get('userName'),
        proceduce_id: this.proceduce_id
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              if (defaultQuery.data[0].pdure_chart !== '') {
                const flow_chart = JSON.parse(defaultQuery.data[0].pdure_chart)
                this.nodes = flow_chart.nodes
                this.nodes.filter(item => item.type === 'image')[0].id = +new Date()
                console.log(this.nodes)
                this.connections = flow_chart.connections
              } else {
                this.nodes = [{
                  id: +new Date(),
                  x: 0,
                  y: 0,
                  type: 'image',
                  width: 370,
                  height: 360,
                  describe: '作业指导图片',
                  strokeWidth: 1,
                  href: this.proceduce_id
                }]
                this.connections = []
              }
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
          this.nodes = [{
            id: +new Date(),
            x: 0,
            y: 0,
            type: 'image',
            width: 370,
            height: 360,
            describe: '作业指导图片',
            strokeWidth: 1,
            href: this.proceduce_id
          }]
          this.connections = []
          this.loading = false
        })
    }
  }
}
</script>
