<template>
  <div>
    <div class="menuAttribute" :style="{'right':showRihgtBar ? '0.3%':'-0.3%'}">
      <div class="arrowRigltBar">
        <img
          src="@/assets/images/newHmiImages/right.png"
          :class="{ 'iconRotateTime': showRihgtBar }"
          @click="openCollapse"
        >
      </div>
      <div class="mentuContent" :class="{'menuAnimationOpen':showRihgtBar,'menuAnimationClose':!showRihgtBar}">
        <div class="box">
          <div class="left">
            <el-button v-show="currentMenu.length !== 0" class="filter-item" size="medium" type="primary" icon="el-icon-arrow-up" plain @click="marginTop !== 0 ? (marginTop += 20) : 0" />
            <div class="content">
              <div class="contentChild" :style="{'margin-top': +marginTop + 'px'}">
                <el-button v-for="(item,index) in currentMenu" :key="item.menu_item_id" class="menuBtns" :class="{'menuActive':index === activeIndex }" @click="jumpMenu(item,index)">{{ item.menu_item_des }}</el-button>
              </div>
            </div>
            <el-button v-show="currentMenu.length !== 0" class="filter-item" style="margin-top: 5px;" size="medium" type="primary" icon="el-icon-arrow-down" plain @click="chnagePX('down')" />
          </div>
          <div class="right">
            <el-button v-for="(item,index) in updatedControlData" :key="index" :style="{'margin-left':index % 2 !== 0 ? '5px' :''}" @click="handleTrigger(item)">{{ item.name }}</el-button>
          </div>
        </div>
      </div>
    </div>

    <el-drawer
      append-to-body
      :wrapper-closable="false"
      :direction="direction"
      :title="pathData.name"
      :visible="visible"
      :before-close="beforeClose"
      size="90%"
    >
      <elFrame v-if="elementFlag" ref="fIframe" :name="pathData.name" :src="pathData.function_path + '?prod_line_id=' + currentStation.prod_line_id + '&station_id=' + currentStation.station_id + '&station_code=' + currentStation.station_code +'&cell_id='+currentStation.cell_id" />
    </el-drawer>
  </div>
</template>
<script>
import elFrame from './iframe.vue'
export default {
  components: { elFrame },
  props: {
    currentStation: {
      type: Object,
      require: true,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: ''
    },
    currentMenu: {
      type: Array,
      require: true,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      showContent: false,
      activeIndex: -1,
      visible: false,
      elementFlag: false,
      direction: 'ltr',
      pathData: {
        function_path: '',
        name: ''
      },
      marginTop: 0,
      showRihgtBar: false
    }
  },
  computed: {
    updatedControlData() {
      return [
        // key 代表调用的方法名
        { name: this.$t('lang_pack.vie.employeeLogin'), key: 'openUserLogin' }, // 员工登录
        { name: this.$t('lang_pack.guanghe.logout'), key: 'handleUserLogout' }, // 员工登出
        { name: this.$t('lang_pack.guanghe.exitSystem'), key: 'open' }, // 退出系统
        { name: this.$t('lang_pack.guanghe.port1Return'), key: 'handleWrite', tag_key: `${this.currentStation.station_id === '1' ? 'LoadPlc' : this.currentStation.station_id === '2' ? 'UnLoadPlc' : 'LoadPlc'}/AisReqControl1/AisPortBackRequest`, tag_value: '1' }, // 端口1退载具
        { name: this.$t('lang_pack.guanghe.port2Return'), key: 'handleWrite', tag_key: `${this.currentStation.station_id === '1' ? 'LoadPlc' : this.currentStation.station_id === '2' ? 'UnLoadPlc' : 'LoadPlc'}/AisReqControl2/AisPortBackRequest`, tag_value: '1' }, // 端口2退载具
        { name: this.$t('lang_pack.guanghe.port3Return'), key: 'handleWrite', tag_key: `${this.currentStation.station_id === '1' ? 'LoadPlc' : this.currentStation.station_id === '2' ? 'UnLoadPlc' : 'LoadPlc'}/AisReqControl3/AisPortBackRequest`, tag_value: '1' }, // 端口3退载具
        { name: this.$t('lang_pack.guanghe.port4Return'), key: 'handleWrite', tag_key: `${this.currentStation.station_id === '1' ? 'LoadPlc' : this.currentStation.station_id === '2' ? 'UnLoadPlc' : 'LoadPlc'}/AisReqControl4/AisPortBackRequest`, tag_value: '1' } // 端口4退载具
      ]
    }
  },
  mounted() {
    document.querySelector('.mentuContent').style.display = 'none'
  },
  methods: {
    suspensionFlag() {
      this.showContent = !this.showContent
    },
    handleTrigger(item) {
      const menuData = ['open'] // 这里的值代表直接在当前页面调用
      if (menuData.includes(item.key)) {
        this[item.key]()
        return
      }
      this.$emit('attrKey', item)
    },
    beforeClose() {
      this.visible = false
    },
    jumpMenu(item, index) {
      this.activeIndex = index
      this.visible = true
      this.elementFlag = true
      this.$nextTick(() => {
        this.pathData.name = item.menu_item_des
        this.pathData.function_path = item.function_path
      })
      // this.$emit('ok',item)
    },
    chnagePX(type) {
      if (type === 'down') {
        this.marginTop = this.marginTop - 20
      }
    },
    openCollapse() {
      this.showRihgtBar = !this.showRihgtBar
      if (this.showRihgtBar) {
        document.querySelector('.mentuContent').style.display = 'block'
      }
    },
    open() {
      this.$confirm(this.$t('lang_pack.header.exitSystem'), this.$t('lang_pack.Prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
        type: 'warning'
      }).then(() => {
        this.$router.push('/whiteLogin')
      })
    }
  }
}
</script>

  <style lang="less" scoped>
  .menuAttribute{
    position: fixed;
    top: 36%;  /* 距离顶部设置 50% */
    right: -0.3%;
    margin-top: -50px; /* 减去高度自身一半 */
    margin-left: -50px; /* 减去宽度自身一半 */
    height: 500px;
    display: flex;
    align-items: center;
    transition: 1s;
    .arrowRigltBar{
      // width: 30px;
      // height: 50px;
      // background: #424d8c;
      // border: 1px solid #0ee50e;
      // border-radius: 5px;
      cursor: pointer;
      img{
        width: 100%;
      }
    }
    .mentuContent{
      height: 100%;
      background:#3D4FB5;
      border-radius: 5px;
      padding: 5px;
      .box{
        width: 100%;
        display: flex;
        justify-content: space-between;
        .left{
          width: 34%;
          height: 100%;
          .filter-item{
            background: #EE822F;
            border-color: 1px solid #fff;
            color: #fff;
            font-size: 18px;
            width: 90%;
          }
          .content{
            height: 400px;
            overflow: hidden;
            .contentChild{
              height: 400%;
              button{
                width: 90%;
                margin: 0;
                border-color: 1px solid #fff;
                background: #EE822F;
                color: #fff;
                font-size: 18px;
                margin-top: 5px;
              }
              .menuActive{
                background: #0ee50e;
              }
            }
          }

        }
        .right{
          width: 66%;
          height: 100%;
          border: 1px solid #fff;
          background: #EE822F;
          padding: 5px 0 0 8px;
          button{
            background: #91ACE0;
            width: 47%;
            margin:0 0 5px 0;
            color: #fff;
            font-size: 18px;
          }
        }
      }
    }
    .menuAnimationOpen{
      animation: collapseAnimation 2s forwards;
    }
    .menuAnimationClose{
      animation: expandAnimation 2s forwards;
    }
    .iconRotateTime {
      -moz-transform: rotate(180deg);
      -webkit-transform: rotate(180deg);
    }
  }
  @keyframes  collapseAnimation{
    0% {
      width: 0;
      opacity: 0;
    }
    100% {
      width: 470px;
      opacity: 0.8;
    }
}
@keyframes expandAnimation{
    0% {
      width: 470px;
      opacity: 1;
    }
    100% {
      width: 0;
      opacity: 0;
    }
}

::v-deep .el-drawer__header{
  padding: 10px !important;
  font-size: 22px;
  margin-bottom:0px !important;
  .el-dialog__close{
    font-size: 22px;
  }
}
</style>
