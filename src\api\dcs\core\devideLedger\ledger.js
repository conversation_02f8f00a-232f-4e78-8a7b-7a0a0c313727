import request from '@/utils/request'

// 查询设备台账
export function sel(data) {
  return request({
    url: '/aisEsbWeb/dcs/core/DcsApsFmodEquipSel',
    method: 'post',
    data
  })
}
// 新增设备台账
export function add(data) {
  return request({
    url: '/aisEsbWeb/dcs/core/DcsApsFmodEquipIns',
    method: 'post',
    data
  })
}
// 修改设备台账
export function edit(data) {
  return request({
    url: '/aisEsbWeb/dcs/core/DcsApsFmodEquipUpd',
    method: 'post',
    data
  })
}
// 删除设备台账
export function del(data) {
  return request({
    url: '/aisEsbWeb/dcs/core/DcsApsFmodEquipDel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del }

