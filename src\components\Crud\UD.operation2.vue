<template>
  <div>
    <!--左侧插槽-->
    <slot name="left" />
    <!-- @click="crud.toEdit(data)" -->
    <el-button v-if="deleteEdit" v-permission="permission.edit" :loading="crud.status.cu === 2" :disabled="disabledEdit" size="small" type="text" @click="edit">{{ $t('lang_pack.commonPage.edit') }}</el-button>  <!-- 编辑 -->
    <LoginModal ref="LoginModal" @editPopup="crud.toEdit(data)" />
    <el-popover v-model="pop" v-permission="permission.del" placement="top" width="180" trigger="manual" @show="onPopoverShow" @hide="onPopoverHide">
      <p>{{ $t('lang_pack.vie.AreYouSureDelete') }}</p>
      <div style="text-align: right; margin: 0">
        <el-button size="small" type="text" @click="doCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
        <el-button :loading="crud.dataStatus[crud.getDataId(data)].delete === 2" type="primary" size="small" @click="crud.doDelete(data)">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确定 -->
      </div>
      <el-button v-if="deleteDel" slot="reference" :disabled="disabledDle" type="text" size="small" @click="toDelete">{{ $t('lang_pack.commonPage.remove') }}</el-button>  <!-- 删除 -->
    </el-popover>
    <!--右侧插槽-->
    <slot name="right" />
  </div>
</template>
<script>
import CRUD, { crud } from '@crud/crud'
import { sel as selHmiInfo } from '@/api/hmi/main'
import LoginModal from '@/views/core/hmi/LoginModal.vue'
import Cookies from 'js-cookie'
export default {
  components: {
    LoginModal
  },
  mixins: [crud()],
  props: {
    data: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    disabledEdit: {
      type: Boolean,
      default: false
    },
    disabledDle: {
      type: Boolean,
      default: false
    },
    msg: {
      type: String,
      default: ''
    },
    deleteEdit: {
      type: Boolean,
      default: true
    },
    deleteDel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      show: true,
      pop: false
    }
  },
  methods: {
    edit() {
      const query = {
        user_name: Cookies.get('userName'),
        userID: Cookies.get('userId')
      }
      selHmiInfo(query).then(res => {
        console.log(res.menu)
        if (res.menu.some((element) => element.menu_item_id === 85)) {
          this.crud.toEdit(this.data)
        } else {
          this.$refs.LoginModal.open()
        }
      })
    },
    doCancel() {
      this.pop = false
      this.crud.cancelDelete(this.data)
    },
    toDelete() {
      this.pop = true
    },
    [CRUD.HOOK.afterDelete](crud, data) {
      if (data === this.data) {
        this.pop = false
      }
    },
    onPopoverShow() {
      setTimeout(() => {
        document.addEventListener('click', this.handleDocumentClick)
      }, 0)
    },
    onPopoverHide() {
      document.removeEventListener('click', this.handleDocumentClick)
    },
    handleDocumentClick(event) {
      this.pop = false
    }
  }
}
</script>
