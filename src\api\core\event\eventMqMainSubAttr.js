import request from '@/utils/request'

// 查询ALL
export function selAll(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreMqMainAllSubAttrSel',
    method: 'post',
    data
  })
}
// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreMqMainSubAttrSel',
    method: 'post',
    data
  })
}

// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreMqMainSubAttrIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreMqMainSubAttrUpd',
    method: 'post',
    data
  })
}
// 保存
export function saveAttr(data) {
  return request({
    url: 'aisEsbWeb/core/event/CoreMqMainSubAttrSave',
    method: 'post',
    data
  })
}

export default { sel, add, edit, selAll, saveAttr }

