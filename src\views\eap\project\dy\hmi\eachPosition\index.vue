<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.hmiMain.plateCode') + ':'">
                <!-- 板件条码 -->
                <el-input v-model="query.panel_barcode" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item style="margin: 0; float: right">
              <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="toButQuery('reset');sendCode()">{{ $t('view.button.search') }}</el-button>
              <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="toButResetQuery">{{ $t('view.button.reset') }}</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <!-- <crudOperation show=""  /> -->
      <el-row :gutter="20">

        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="listLoadingTable"
            border
            size="small"
            :data="tableDataTable"
            style="width: 100%"
            :cell-style="cellStyle"
            :height="height"
            :highlight-current-row="true"
          >
            <!-- 时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_date"
              :label="$t('view.table.time')"
              align="center"
            />
            <!-- 条码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="panel_barcode"
              :label="$t('view.table.boardBarcode')"
              align="center"
            />
            <!-- 暂存代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="zc_code"
              :label="$t('view.table.ngBoardErrorCode')"
              align="center"
            />
            <!-- 暂存原因 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="zc_code_msg"
              :label="$t('view.table.ngBoardErrorDescription')"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <!--<pagination />-->
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { eapMeEachPositionSelect } from '@/api/eap/eapApsPlanReport'
import Cookies from 'js-cookie'
export default {
  name: 'EachPosition',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      query: {
        panel_barcode: ''
      },
      listLoadingTable: false,
      tableDataTable: [],
      delayCount: 0
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
  },
  methods: {
    // 单元格样式控制
    cellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec'
    },
    toButQuery(op) {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName'),
        panel_barcode: this.query.panel_barcode,
        station_code: this.$route.query.station_code
      }
      this.listLoadingTable = true

      // 倒计时功能
      this.delayCount = 15
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      eapMeEachPositionSelect(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.tableDataTable = defaultQuery.data
          } else {
            this.tableDataTable = []
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    toButResetQuery() {
      // 重置
      this.query.panel_barcode = ''
      this.toButQuery()
    }

  }
}
</script>
