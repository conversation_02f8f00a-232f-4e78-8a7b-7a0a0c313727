import request from '@/utils/request'

// 查询安灯按钮基础
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonBtnSel',
    method: 'post',
    data
  })
}
// 新增安灯按钮基础
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonBtnIns',
    method: 'post',
    data
  })
}
// 修改安灯按钮基础
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonBtnUpd',
    method: 'post',
    data
  })
}
// 删除安灯按钮基础
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonBtnDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

