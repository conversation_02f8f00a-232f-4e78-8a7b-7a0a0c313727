<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-drawer append-to-body :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="450px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <!--表：rcs_logic_attr_group-->
          <el-form-item :label="$t('lang_pack.logicfunc.attributeGroup')" prop="logic_attr_group_id" display:none>
            <!-- 属性组 -->
            <el-select v-model="form.logic_attr_group_id" clearable filterable>
              <el-option v-for="item in logicAttrGroupData" :key="item.logic_attr_group_id" :label="item.logic_attr_group_des" :value="item.logic_attr_group_id">
                <span>{{ item.logic_attr_group_code }}</span>
                <span>{{ item.logic_attr_group_des }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :stripe="true" height="400px" :highlight-current-row="true" @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="logic_func_g_code" :label="$t('lang_pack.logicfunc.groupCode')" sortable="custom" />
        <!-- 组编码 -->
        <el-table-column  :show-overflow-tooltip="true" prop="logic_func_g_des" :label="$t('lang_pack.logicfunc.groupDescription')" sortable="custom" />
        <!-- 组描述 -->

        <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import { logicAttrGroupLov } from '@/api/core/logic/logicAttrGroup'
import { selLogicFuncGroup, insLogicFuncGroup, updLogicFuncGroup, delLogicFuncGroup } from '@/api/core/logic/logicFuncG'

export default {
  name: 'FUNCG',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      form: {
        logic_func_g_id: '',
        logic_func_id: '',
        logic_attr_group_id: '',
        enable_flag: 'Y'
      },
      rules: {
        logic_attr_group_id: [{ required: true, message: '请选择属性组', trigger: 'blur' }]
      },

      // Table
      listLoadingTable: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],

      query: {
        logicFuncGCodeDes: '',
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'logic_func_g_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      },

      logicAttrGroupData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // 查询
    this.toButQuery()

    // 属性组LOV
    logicAttrGroupLov({})
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.logicAttrGroupData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 按钮：
    toButQuery(logic_func_id, queryContent) {
      if (logic_func_id === undefined) {
        this.form.logic_func_id = this.$parent.$attrs.logicfuncid
      } else {
        this.form.logic_func_id = logic_func_id
      }
      if (queryContent === undefined) {
        this.query.logicFuncGCodeDes = ''
      } else {
        this.query.logicFuncGCodeDes = queryContent
      }
      const query = {
        user_name: Cookies.get('userName'),
        logicFuncGCodeDes: this.query.logicFuncGCodeDes,
        logic_func_id: this.form.logic_func_id,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      this.listLoadingTable = true
      selLogicFuncGroup(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.count > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toButAdd() {
      this.form.logic_func_g_id = ''
      this.form.logic_attr_group_id = ''
      this.form.enable_flag = 'Y'
      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增属性组'
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.logic_func_g_id = data.logic_func_g_id
      this.form.logic_func_id = data.logic_func_id
      this.form.logic_attr_group_id = data.logic_attr_group_id
      this.form.enable_flag = data.enable_flag
      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改属性组'
    },

    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            logic_func_g_id: this.form.logic_func_g_id,
            logic_func_id: this.form.logic_func_id,
            logic_attr_group_id: this.form.logic_attr_group_id,
            enable_flag: this.form.enable_flag
          }
          const that = this
          var logic_attr_group_des = this.logicAttrGroupData.filter(item => item.logic_attr_group_id + '' === this.form.logic_attr_group_id + '')[0].logic_attr_group_des
          // 新增
          if (this.form.logic_func_g_id === undefined || this.form.logic_func_g_id.length <= 0) {
            insLogicFuncGroup(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  that.$emit('AddTreeNode', this.form.logic_func_id, defaultQuery.result, logic_attr_group_des, this.form.logic_attr_group_id)
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updLogicFuncGroup(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  that.$emit('EditTreeNode', this.form.logic_func_g_id, logic_attr_group_des, this.form.logic_attr_group_id)
                  // 查询
                  this.toButQuery()
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除${data.logic_func_g_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            logic_func_g_id: data.logic_func_g_id
          }
          const that = this
          delLogicFuncGroup(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                that.$emit('DelTreeNode', data.logic_func_id)
                // 查询
                this.toButQuery()
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'logic_func_g_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}

.el-drawer {
  overflow-y: scroll;
}
</style>
