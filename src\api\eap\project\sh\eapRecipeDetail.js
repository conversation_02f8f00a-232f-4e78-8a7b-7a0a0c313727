import request from '@/utils/request'

// 查询配方维护详情信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/EapRecipeDetailSel',
    method: 'post',
    data
  })
}
// 新增配方维护详情信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/EapRecipeDetailIns',
    method: 'post',
    data
  })
}
// 修改配方维护详情信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/EapRecipeDetailUpd',
    method: 'post',
    data
  })
}
// 删除配方维护详情信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/EapRecipeDetailDel',
    method: 'post',
    data
  })
}
// 有效标识更新
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/EapRecipeDetailEnableFlagUpdate',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

