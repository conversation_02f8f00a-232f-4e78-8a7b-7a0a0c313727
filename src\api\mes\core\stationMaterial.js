import request from '@/utils/request'

// 查询过站物料数据信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/queryStationMaterialList',
    method: 'post',
    data
  })
}

export function exportEventInsert(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesStationMaterialExportEventInsert',
    method: 'post',
    data
  })
}

// 解除绑定
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeStationMaterialDel',
    method: 'post',
    data
  })
}
// 替换物料
export function materialUpd(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeStationMaterialUpd',
    method: 'post',
    data
  })
}
// 物料工件绑定
export function materialBind(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeStationMaterialBind',
    method: 'post',
    data
  })
}
export default { sel, exportEventInsert, del, materialUpd, materialBind }
