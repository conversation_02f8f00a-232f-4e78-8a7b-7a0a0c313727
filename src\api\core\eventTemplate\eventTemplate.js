import request from '@/utils/request'

const conn = 'event-templates'
const context = 'aisEsbWeb/' + conn

export function sel(params) {
  delete params.user_name
  return request({
    url: context,
    method: 'get',
    params
  })
}

export function edit(data, params) {
  delete data.user_name
  return request({
    url: context + '/' + data.originId,
    method: 'patch',
    data,
    params
  })
}

export function add(data, params) {
  return request({
    url: context,
    method: 'post',
    data,
    params
  })
}

export function del(data, params) {
  return request({
    url: context + '/' + (data.id || data.ids),
    method: 'delete',
    data,
    params
  })
}

export default { add, del, edit, sel }
