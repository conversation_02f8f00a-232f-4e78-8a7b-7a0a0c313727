<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="天车编码:">
                                <!-- 天车编码 -->
                                <el-input v-model="query.car_code" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="库区域:">
                                <!-- 库区域 -->
                                <el-select v-model="query.ware_house" clearable filterable>
                                    <el-option v-for="item in dict.WARE_HOUSE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="天车任务类型:">
                                <!-- 天车任务类型 -->
                                <el-select v-model="query.car_task_type" clearable filterable>
                                    <el-option v-for="item in dict.CAR_TASK_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.libraryArea')" prop="ware_house">
                                <!-- 库区域 -->
                                <el-input v-model="form.ware_house" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockCode')" prop="car_code">
                                <!-- 天车编码 -->
                                <el-input v-model="form.car_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockTaskType')" prop="car_task_type">
                                <!-- 天车任务类型 -->
                                <el-select v-model="query.car_task_type" clearable filterable>
                                    <el-option v-for="item in dict.CAR_TASK_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.stepCode')" prop="step_code">
                                <!-- 步骤代码 -->
                                <el-input v-model="form.step_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.stepName')" prop="step_name">
                                <!-- 步骤名称 -->
                                <el-input v-model="form.step_name" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.xCoordinate')" prop="location_x">
                                <!-- X坐标 -->
                                <el-input v-model="form.location_x" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.YCoordinate')" prop="location_y">
                                <!-- Y坐标 -->
                                <el-input v-model="form.location_y" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.ZCoordinate')" prop="location_z">
                                <!-- Z坐标 -->
                                <el-input v-model="form.location_z" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.stepStatus')" prop="step_status">
                                <!-- 步骤状态 -->
                                <el-input v-model="form.step_status" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.restrictionStepName')" prop="step_start_limit_step">
                                <!-- 限制步骤名称 -->
                                <el-input v-model="form.step_start_limit_step" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.restrictionStepStatus')" prop="step_start_limit_status">
                                <!-- 限制步骤状态 -->
                                <el-input v-model="form.step_start_limit_status" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.isTheRollerTableChecked')" prop="need_check_gd_flag">
                                <!-- 是否检查辊道 -->
                                <el-radio-group v-model="form.need_check_gd_flag">
                                    <el-radio label="0">是</el-radio>
                                    <el-radio label="1">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.isTheModelChecked')" prop="need_check_model_flag">
                                <!-- 是否检查型号 -->
                                <el-radio-group v-model="form.need_check_model_flag">
                                    <el-radio label="0">是</el-radio>
                                    <el-radio label="1">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()"  ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <!-- <el-table-column type="selection" width="55" /> -->
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                                <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                                <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                                <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_route_id }}</el-descriptions-item>
                                <el-descriptions-item label="库区域" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.ware_house }}</el-descriptions-item>
                                <el-descriptions-item label="天车编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_code }}</el-descriptions-item>
                                <el-descriptions-item label="天车任务类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{dict.label.CAR_TASK_TYPE[props.row.car_task_type]}}</el-descriptions-item>
                                <el-descriptions-item label=" 步骤代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_code }}</el-descriptions-item>
                                <el-descriptions-item label="步骤名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_name}}</el-descriptions-item>
                                <el-descriptions-item label="X坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_x}}</el-descriptions-item>
                                <el-descriptions-item label="Y坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_y }}</el-descriptions-item>
                                <el-descriptions-item label="Z坐标" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.location_z }}</el-descriptions-item>
                                <el-descriptions-item label="步骤状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_status }}</el-descriptions-item>
                                <el-descriptions-item label="限制步骤ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_start_limit_id }}</el-descriptions-item>
                                <el-descriptions-item label="限制步骤名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_start_limit_step }}</el-descriptions-item>
                                <el-descriptions-item label="限制步骤状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.step_start_limit_status }}</el-descriptions-item>
                                <el-descriptions-item label="是否需要检查辊道" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_gd_flag ===0 ? '是':'否' }}</el-descriptions-item>
                                <el-descriptions-item label="是否需要检查型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_model_flag ===0 ? '是':'否' }}</el-descriptions-item>
                                <el-descriptions-item label="步骤约束信号点TagID集合" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.if_tags_list}}</el-descriptions-item>
                                <el-descriptions-item label="步骤约束信号点TagID对应值集合" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.if_tags_value }}</el-descriptions-item>
                                <el-descriptions-item label="天车调度任务ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_task_id }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 库区域 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="ware_house"
                            :label="$t('lang_pack.wmsCbTable.libraryArea')" width="120" align='center'/>
                        <!-- 天车编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_code"
                            :label="$t('lang_pack.wmsCbTable.crownBlockCode')" width="120" align='center'/>
                        <!-- 天车任务类型 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_task_type"
                            :label="$t('lang_pack.wmsCbTable.crownBlockTaskType')" width="120" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.CAR_TASK_TYPE[scope.row.car_task_type] }}
                            </template>
                        </el-table-column>
                        <!-- 步骤代码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="step_code"
                            :label="$t('lang_pack.wmsCbTable.stepCode')" width="120" align='center'/>
                        <!-- 步骤名称 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="step_name"
                            :label="$t('lang_pack.wmsCbTable.stepName')" width="120" align='center'/>
                        <!-- X坐标 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="location_x"
                            :label="$t('lang_pack.wmsCbTable.xCoordinate')" width="120" align='center'/>
                        <!-- Y坐标 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="location_y"
                            :label="$t('lang_pack.wmsCbTable.YCoordinate')" width="120" align='center'/>
                        <!-- Z坐标 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="location_z"
                            :label="$t('lang_pack.wmsCbTable.ZCoordinate')" width="120" align='center'/>
                        <!-- 步骤状态 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="step_status"
                            :label="$t('lang_pack.wmsCbTable.stepStatus')" width="120" align='center'/>
                        <!-- 限制步骤名称 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="step_start_limit_step"
                            :label="$t('lang_pack.wmsCbTable.restrictionStepName')" width="120" align='center'/>
                        <!-- 限制步骤状态 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="step_start_limit_status"
                            :label="$t('lang_pack.wmsCbTable.restrictionStepStatus')" width="140" align='center'/>
                        <!-- 是否检查辊道 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="need_check_gd_flag"
                            :label="$t('lang_pack.wmsCbTable.isTheRollerTableChecked')" width="140" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.need_check_gd_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <!-- 是否检查型号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="need_check_model_flag"
                            :label="$t('lang_pack.wmsCbTable.isTheModelChecked')" width="140" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.need_check_model_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudWmsLockCarRoute from '@/api/dcs/core/wmsCbTable/wmsLockCarRoute'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    car_route_id: '',
    ware_house: '',
    car_code: '',
    car_task_type: '',
    step_code: '',
    step_name: '',
    location_x: '',
    location_y: '',
    location_z: '',
    step_start_limit_id: '',
    step_start_limit_step: '',
    step_start_limit_status: '',
    need_check_gd_flag: '1',
    need_check_model_flag: '1',
    enable_flag: 'Y'
}
export default {
    name: 'wmsLockCarRoute',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: 'WMS调度任务路线表',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'car_route_id',
            // 排序
            sort: ['car_route_id asc'],
            // CRUD Method
            crudMethod: { ...crudWmsLockCarRoute },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                ware_house: [{ required: true, message: '请选择库区域', trigger: 'blur' }],
                car_code: [{ required: true, message: '请选择天车编码', trigger: 'blur' }],
                car_task_type: [{ required: true, message: '请选择天车任务类型', trigger: 'blur' }],
                step_code: [{ required: true, message: '请选择步骤代码', trigger: 'blur' }],
                step_name: [{ required: true, message: '请选择步骤名称', trigger: 'blur' }],
                location_x: [{ required: true, message: '请选择X坐标', trigger: 'blur' }],
                location_y: [{ required: true, message: '请选择Y坐标', trigger: 'blur' }],
                location_z: [{ required: true, message: '请选择Z坐标', trigger: 'blur' }],
                step_status: [{ required: true, message: '请选择步骤状态', trigger: 'blur' }],
                need_check_gd_flag: [{ required: true, message: '请选择是否检查辊道', trigger: 'blur' }],
                need_check_model_flag: [{ required: true, message: '请选择是否检查型号', trigger: 'blur' }],
            },
        }
    },
    dicts: ['CAR_TASK_TYPE','WARE_HOUSE',],
    mounted: function () {
        this.$refs.table.doLayout()
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
    }
}
</script>
  