import request from '@/utils/request'

// 查询质量数据采集配置表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodQualitySel',
    method: 'post',
    data
  })
}
// 新增质量数据采集配置表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodQualityIns',
    method: 'post',
    data
  })
}
// 修改质量数据采集配置表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodQualityUpd',
    method: 'post',
    data
  })
}
// 删除质量数据采集配置表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodQualityDel',
    method: 'post',
    data
  })
}

// 修改质量数据采集配置表--修改有效标识
export function editEnableFlag(data) {
  return request({
      url: 'aisEsbWeb/dcs/core/DcsApsFmodQualityEnableFlagUpdate',
      method: 'post',
      data
  })
}
export default { sel, add, edit, del ,editEnableFlag}

