<template>
  <div class="workstationScreen">
    <el-card class="cardStyle">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="12">
          <div class="carInfo">
            <div class="carInfoText">
              <el-descriptions column="1">
                <el-descriptions-item label="VIN">LVBV3JBB1MY434777</el-descriptions-item>
                <el-descriptions-item label="DMS">152972597</el-descriptions-item>
                <el-descriptions-item label="车型">牵引车</el-descriptions-item>
                <el-descriptions-item label="驾驶室颜色">红色</el-descriptions-item>
                <el-descriptions-item label="高低宽窄">10-20-30-40</el-descriptions-item>
                <el-descriptions-item label="发动机">占位内容</el-descriptions-item>
                <el-descriptions-item label="驱动形式">占位内容</el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="carInfoImg">
              <img src="@/assets/images/avatar.png" alt="">
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="carInfoTable">
            <el-table
              :data="carPlayData"
              style="width: 100%"
            >
              <el-table-column 
                prop="order"
                label="序号"
                width="180"
              />
              <el-table-column 
                prop="code"
                label="车型代码"
                width="180"
              />
              <el-table-column 
                prop="vin"
                label="VIN"
              />
              <el-table-column 
                prop="status"
                label="状态"
              />
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="12">
          <div class="carInfoTable">
            <el-table
              :data="carInfoData"
              style="width: 100%"
            >
              <el-table-column 
                prop="order"
                label="序号"
                width="180"
              />
              <el-table-column 
                prop="imgcode"
                label="图号"
                width="180"
              />
              <el-table-column 
                prop="name"
                label="零件名称"
              />
              <el-table-column 
                prop="usenumber"
                label="用量"
              />
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="carInfoVideo">
            <img src="@/assets/images/loginleft.gif" alt="">
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

export default {
  name: 'workstationScreen',
  components: {
  },
  data() {
    return {
      carPlayData: [{
        order: '001',
        code: 'TMLL1545686',
        vin: 'LVBV3JBB1MY434777',
        status: '已完成'
      }, {
        order: '002',
        code: 'TMLL1545686',
        vin: 'LVBV3JBB1MY434777',
        status: '进行中'
      }, {
        order: '003',
        code: 'TMLL1545686',
        vin: 'LVBV3JBB1MY434777',
        status: '待生产'
      }, {
        order: '004',
        code: 'TMLL1545686',
        vin: 'LVBV3JBB1MY434777',
        status: '待生产'
      }, {
        order: '005',
        code: 'TMLL1545686',
        vin: 'LVBV3JBB1MY434777',
        status: '待生产'
      }],
      carInfoData: [{
        order: '001',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '002',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '003',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '004',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '005',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '006',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '007',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '008',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '009',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }, {
        order: '010',
        imgcode: 'TMLL1545686',
        name: '下推力杆连接螺栓',
        usenumber: 1
      }]
    }
  },
  created() {

  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.cardStyle{
  margin: 10px;
}
.carInfo{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
::v-deep .carInfoTable{
  .el-table--small .el-table__cell {
    padding: 4px 0;
}
}
.carInfoVideo{
  height: 430px;
  img{
    width: 100%;
    height: 100%;
  }
}

</style>
