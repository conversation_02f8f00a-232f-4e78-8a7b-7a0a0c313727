<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="型材编号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="材质:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="位置:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="500px"
      >
        <el-form
          ref="form"
          class="el-form-wrap el-form-column"
          :model="form"
          :rules="rules"
          size="small"
          label-width="145px"
          :inline="true"
        >
          <el-form-item label="型材编号" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入余料类型" />
          </el-form-item>
          <el-form-item label="规格(mm)" prop="task_from">
            <el-input v-model="form.gg" style="width: 110px;" placeholder="长" />
            <el-input v-model="form.gg" style="width: 110px;" placeholder="宽" />
            <el-input v-model="form.gg" style="width: 110px;" placeholder="厚" />
          </el-form-item>
          <el-form-item label="重量" prop="task_from">
            <el-input v-model="form.zl" placeholder="请输入重量" />
          </el-form-item>
          <el-form-item label="材质" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入材质" />
          </el-form-item>
          <el-form-item label="备注" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入备注" />
          </el-form-item>
          <el-form-item label="存放位置" prop="task_from">
            <el-input v-model="form.ylwz" placeholder="请输入存放人员" />
          </el-form-item>
          <el-form-item label="操作人员" prop="task_from">
            <el-input v-model="form.yllx" placeholder="请输入操作人员" />
          </el-form-item>
          <el-form-item label="来源" prop="task_from">
            <el-input v-model="form.ly" placeholder="请输入型材来源" />
          </el-form-item>
          <el-divider />
          <div style="text-align: center">
            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
              $t('lang_pack.commonPage.cancel') }}</el-button>
            <!-- 取消 -->
            <el-button
              type="primary"
              size="small"
              icon="el-icon-check"
              :loading="crud.status.cu === 2"
              @click="crud.submitCU"
            >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
            <!-- 确认 -->
          </div>
        </el-form>
      </el-drawer>
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="tableData"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="ylbh" label="型材编号" />
            <el-table-column :show-overflow-tooltip="true" prop="gg" label="规格(mm)" />
            <el-table-column :show-overflow-tooltip="true" prop="cz" label="材质" />
            <el-table-column :show-overflow-tooltip="true" prop="zl" label="重量(kg)" />
            <el-table-column :show-overflow-tooltip="true" prop="wz" label="位置" />
            <el-table-column :show-overflow-tooltip="true" prop="sj" label="入库时间" />
            <el-table-column :show-overflow-tooltip="true" prop="ly" label="来源" />
            <el-table-column :show-overflow-tooltip="true" prop="bz" label="备注" />
            <el-table-column :show-overflow-tooltip="true" prop="czry" label="操作人员" />
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  @click="openInventory(scope.row)"
                >型材出库</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >删除</el-button>
              </template></el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-dialog title="型材出库" width="60%" :before-close="handleClose" :close-on-click-modal="false" :visible.sync="dialogVisible">
      <el-card ref="queryCard" shadow="never" class="wrapCard">
        <el-form ref="query" :inline="true" size="small">
          <div class="wrapElForm">
            <div class="wrapElFormFirst col-md-9 col-12">
              <div class="formChild col-md-4 col-12">
                <el-form-item label="数量：">
                  <!-- 数量 -->
                  <el-input v-model.number="orderNumber" type="number" />
                </el-form-item>
              </div>
            </div>
            <div class="wrapElFormSecond formChild col-md-3 col-12">
              <el-form-item>
                <el-button class="filter-item" size="small" type="primary" @click="handleOk">数量确认</el-button>
                <el-button class="filter-item" size="small" type="primary" @click="handleAdd">新增一行</el-button>
              </el-form-item>
            </div>
          </div>
        </el-form>
        <el-form ref="baseForm" class="base-form" :model="baseForm" :rules="rules" auto-complete="on">
          <el-table
            ref="table1"
            border
            size="small"
            :data="baseForm.dataList"
            style="width: 100%"
            :height="profileHeight"
            :highlight-current-row="true"
          >
            <el-table-column align="center" prop="xcbh" label="型材编号">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.xcbh'" :rules="rules.xcbh" class="all">
                  <el-input v-model="scope.row.xcbh" placeholder="请输入型材编号" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.xcbh`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="xcxx" label="型材信息">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.xcxx'" :rules="rules.xcxx" class="all">
                  <el-input v-model="scope.row.xcxx" placeholder="请输入型材信息" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.xcxx`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="scx" label="生产线">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.scx'" :rules="rules.scx" class="all">
                  <el-input v-model="scope.row.scx" placeholder="请输入生产线" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.scx`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="sxsx" label="上线顺序">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.sxsx'" :rules="rules.sxsx" class="all">
                  <el-input v-model.number="scope.row.sxsx" type="number" placeholder="请输入上线顺序" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.sxsx`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="czry" label="操作人员">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.czry'" :rules="rules.czry" class="all">
                  <el-input v-model="scope.row.czry" placeholder="请输入操作人员" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.czry`)" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="bz" label="备注">
              <template slot-scope="scope">
                <el-form-item :prop="'dataList.'+scope.$index+'.bz'" :rules="rules.bz" class="all">
                  <el-input v-model="scope.row.bz" placeholder="请输入备注" clearable @focus="$refs.baseForm.clearValidate(`demoList.${scope.$index}.bz`)" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary">重置</el-button>
        <el-button type="primary">提交并同步到预处理线</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import crudWmsMapStockCell from '@/api/dcs/project/wms/wmsMapStockCell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_PROFILE_MANAGE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '型材信息管理',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapStockCell },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      profileHeight: document.documentElement.clientHeight - 400,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      tableData: [
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' },
        { ylbh: 'YL2025001', gg: '20*8000*800', cz: 'Q235B', zl: '235.6', wz: 'A跨-A001', ly: '30', sj: '2025-05-14 18:08:10', bz: '0', czry: '张三' }
      ],
      rules: {
        task_from: [{ required: true, message: '请选择任务来源', trigger: 'change' }],
        xcbh: [{ required: true, message: '请输入型材编号', trigger: 'change' }],
        xcxx: [{ required: true, message: '请输入型材信息', trigger: 'change' }],
        scx: [{ required: true, message: '请输入生产线', trigger: 'change' }],
        sxsx: [{ required: true, message: '请输入上线顺序', trigger: 'change' }],
        czry: [{ required: true, message: '请输入操作人员', trigger: 'change' }],
        bz: [{ required: true, message: '请输入备注', trigger: 'change' }]
      },
      baseForm: {
        dataList: []
      },
      dialogVisible: false,
      orderNumber: 0 // 数量
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
      that.profileHeight = document.documentElement.clientHeight - 400
    }
  },
  created: function() {
  },
  methods: {
    // 数量确认，去循环
    handleOk() {
      if (this.orderNumber <= 0) {
        this.$message({ type: 'error', message: '请输入大于0的数量' })
        return
      }
      this.baseForm.dataList = []
      for (let i = 0; i < this.orderNumber; i++) {
        this.baseForm.dataList.push(
          { xcbh: '', xcxx: '', scx: '', sxsx: i + 1, czry: '', bz: '' }
        )
      }
    },
    // 新增一行
    handleAdd() {
      this.orderNumber++
      this.baseForm.dataList.push(
        { xcbh: '', xcxx: '', scx: '', sxsx: this.orderNumbe, czry: '', bz: '' }
      )
    },
    handleClose() {
      this.dialogVisible = false
    },
    openInventory(row) {
      this.dialogVisible = true
    }
  }
}
</script>

    <style>
    .table-descriptions-label {
    width: 150px;
    }
    .table-descriptions-content {
    width: 150px;
    }
    </style>
