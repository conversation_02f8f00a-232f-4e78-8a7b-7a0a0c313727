import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeCarQualitySel',
    method: 'post',
    data
  })
}
// 查询
export function selDetail(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMePackMzQualitySel',
    method: 'post',
    data
  })
}
// 导出
export function down(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMePackQualityDown',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 替换模组条码
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMePackQualityUpd',
    method: 'post',
    data
  })
}
// 解除绑定
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMePackQualityDel',
    method: 'post',
    data
  })
}
// 重新上传
export function reupload(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMePackQualityReupload',
    method: 'post',
    data
  })
}

// 插入导出事件
export function exportEventInsert(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeCarQualityDown',
    method: 'post',
    data
  })
}

// 插入导出PACK模组电芯事件
export function exportEventInsert2(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMePackQualityExportEventInsert2',
    method: 'post',
    data
  })
}
export function delRel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeCarPackDelRel',
    method: 'post',
    data
  })
}

export default { sel, selDetail, edit, del, down, reupload, exportEventInsert, exportEventInsert2, delRel }

