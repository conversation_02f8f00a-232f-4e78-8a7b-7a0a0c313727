<template>
  <!--PACK电芯-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="770px">
      <el-form ref="form" class="el-form-wrap" :rules="rules" :model="form" size="small" label-width="180px" :inline="true">
        <el-form-item label="项目编码" prop="item_code">
          <el-input v-model="form.item_code" />
        </el-form-item>
        <el-form-item label="项目描述" prop="item_des">
          <el-input v-model="form.item_des" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model.number="form.unit" />
        </el-form-item>
        <el-form-item label="灰色值" prop="gray_value">
          <el-input v-model.number="form.gray_value" />
        </el-form-item>
        <el-form-item label="绿色值" prop="green_value">
          <el-input v-model.number="form.green_value" />
        </el-form-item>
        <el-form-item label="红色值" prop="red_value">
          <el-input v-model.number="form.red_value" />
        </el-form-item>
        <el-form-item label="排序" prop="tag_index">
          <el-input v-model.number="form.tag_index" />
        </el-form-item>
        <el-form-item label="工序显示照片名称" prop="pic_name">
          <el-input v-model="form.pic_name" />
        </el-form-item>
        <el-form-item label="工序显示照片路径" prop="pic_path">
          <el-input v-model="form.pic_path" readonly="readonly">
            <el-button slot="append" icon="el-icon-upload" @click="handleUpload('pic_path')" />
          </el-input>
        </el-form-item>
        <el-form-item v-show="isPlay" :label="$t('lang_pack.mainmain.triggerPoint')" prop="tag_id">
          <!-- 触发点位 -->
          <el-input v-model.number="form.tag_id" readonly="readonly">
            <div slot="append">
              <el-popover v-model="customPopover" placement="left" width="650">
                <tagSelect ref="tagSelect" :client-id-list="form.client_id_list" :tag-id="form.tag_id" @chooseTag="handleChooseTag" />
                <el-button slot="reference">选择</el-button>
              </el-popover>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="显示方式" prop="display_type">
          <el-select v-model="form.display_type" clearable @change="displayItem">
            <el-option v-for="item in dict.EAP_DISPLAY_TYPE" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="有效标识" prop="enable_flag">
          <el-select v-model="form.enable_flag" clearable>
            <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
      </div>
    </el-drawer>
    <el-drawer append-to-body :wrapper-closable="false" :title="uploadDrawerTitle" :visible.sync="uploadDrawerVisbleSync" size="390px" @closed="handleUploadDrawerClose">
      <el-upload
        ref="upload"
        name="file"
        :multiple="false"
        action=""
        drag=""
        :limit="uploadLimit"
        :on-change="handleUploadOnChange"
        :http-request="handleUploadHttpRequest"
        :accept="uploadAccept"
        :auto-upload="false"
        :file-list="fileList"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
      </el-upload>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="handleUploadDrawerCancel">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" @click="handleUploadFile">上传</el-button>
      </div>
    </el-drawer>
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="478" highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="tag_id" label="标签ID" />
          <el-table-column  :show-overflow-tooltip="true" prop="item_code" label="项目编码" />
          <el-table-column  :show-overflow-tooltip="true" prop="item_des" label="项目描述" />
          <el-table-column  :show-overflow-tooltip="true" prop="display_type" label="显示方式" />
          <el-table-column  :show-overflow-tooltip="true" prop="creation_date" label="创建时间" />
          <el-table-column  :show-overflow-tooltip="true" prop="unit" label="单位" />
          <el-table-column  label="是否有效" align="center" prop="enable_flag">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>
          <el-table-column  label="操作" width="115" align="center" fixed="right">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import axios from 'axios'
import eapScreenConfigDetail from '@/api/eap/core/eapScreenConfigDetail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  screen_config_detail_id: '',
  item_code: '',
  item_des: '',
  display_type: '',
  unit: '',
  gray_value: 0,
  green_value: 0,
  red_value: 0,
  tag_index: '',
  pic_name: '',
  pic_path: '',
  client_id_list: '',
  tag_id: '',
  enable_flag: 'Y'

}
export default {
  name: 'EAP_SCREEN_CONFIG_DETAIL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    screen_config_detail_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '子大屏配置详情',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'screen_config_d_id',
      // 排序
      sort: ['tag_index asc'],
      // CRUD Method
      crudMethod: { ...eapScreenConfigDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EAP_DISPLAY_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_eap_fmod_screen_deploy_d:add'],
        edit: ['admin', 'c_eap_fmod_screen_deploy_d:edit'],
        del: ['admin', 'c_eap_fmod_screen_deploy_d:del'],
        down: ['admin', 'c_eap_fmod_screen_deploy_d:down']
      },
      customPopover: false,
      tag_id: '',
      rules: {
        tag_id: [{ required: true, message: '请选择采集项目标签', trigger: 'blur' }],
        item_code: [{ required: true, message: '请填写项目编码', trigger: 'blur' }],
        item_des: [{ required: true, message: '请填写项目描述', trigger: 'blur' }],
        pic_path: [{ required: false, message: '请填写工序显示照片路径', trigger: 'blur' }],
        tag_index: [{ required: true, type: 'number', message: '排序必须为数字值', trigger: 'blur' }],
        unit: [{ required: false, message: '请填写单位', trigger: 'blur' }],
        gray_value: [{ required: false, type: 'number', message: '灰色值必填和必须为数字值' }],
        green_value: [{ required: false, type: 'number', message: '绿色值必填和必须为数字值' }],
        red_value: [{ required: false, type: 'number', message: '红色值必填和必须为数字值' }],
        display_type: [{ required: true, message: '请选择显示方式', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      },
      isPlay: true,
      // 文件上传
      currentUploadType: '',
      currentFilePath: '',
      uploadDrawerTitle: '文件上传',
      uploadDrawerVisbleSync: false,
      uploadLimit: 1,
      uploadAccept: '*.*',
      fileList: []
    }
  },
  watch: {
    screen_config_detail_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.screen_config_id = this.screen_config_detail_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
    displayItem(val) {
      console.log(val)
      if (val === 'label' || val === 'pic') {
        this.form.tag_id = 0
        this.query.tag_id = this.form.tag_id
        this.isPlay = false
      } else {
        this.isPlay = true
      }
      if (val === 'pic') {
        if (this.form.pic_path === '') {
          this.rules.pic_path[0].required = true
        }
      } else {
        this.form.pic_name = ''
        this.form.pic_path = ''
        this.rules.pic_path[0].required = false
      }
      if (val === 'status') {
        this.rules.gray_value[0].required = true
        this.rules.green_value[0].required = true
        this.rules.red_value[0].required = true
      } else {
        this.form.gray_value = 0
        this.form.green_value = 0
        this.form.red_value = 0
        this.rules.gray_value[0].required = false
        this.rules.green_value[0].required = false
        this.rules.red_value[0].required = false
      }
    },
    // 触发点位
    handleChooseTag(tagId) {
      this.form.tag_id = tagId
      this.customPopover = false
    },
    handleUploadDrawerClose() {
      this.fileList = []
    },
    handleUpload(type) {
      this.currentUploadType = type
      if (type === 'pic_path') {
        this.uploadDrawerTitle = '图片文件上传'
        this.currentFilePath = 'pic_file'
        this.uploadAccept = '.jpg,.jpeg,.png,.gif,.JPG,.JPEG,.GIF'
      }
      this.uploadDrawerVisbleSync = true
    },
    handleUploadDrawerCancel() {
      this.uploadDrawerVisbleSync = false
    },
    // 导入文件时将文件存入数组中
    handleUploadOnChange(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequest(file) {
      this.fileData.append('file', file.file)
    },
    // 处理上传文件
    handleUploadFile() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      this.fileData = new FormData()
      this.fileData.append('file_path', this.currentFilePath)
      this.$refs.upload.submit()

      // 配置路径
      var method = 'core/file/CoreFileUpload'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method

      const loading = this.$loading({
        lock: true,
        text: '上传文件处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            const fileInfo = JSON.parse(defaultQuery.data.result)
            if (this.currentUploadType === 'pic_path') {
              this.form.pic_name = fileInfo.file_name
              this.form.pic_path = fileInfo.file_dir_path
            }
            this.uploadDrawerVisbleSync = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
        .catch(er => {
          loading.close()
          this.$message({
            message: '上传文件异常：' + er,
            type: 'error'
          })
        })
    },
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.screen_config_id = this.screen_config_detail_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.screen_config_id = this.screen_config_detail_id
      return true
    }
  }
}
</script>
