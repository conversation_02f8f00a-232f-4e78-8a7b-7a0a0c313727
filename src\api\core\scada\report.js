import request from '@/utils/request'

// 默认设定时间范围(参数为天数)
export function InitTimePickRange(days) {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * days)
  var start2 = new Date(start.getTime() - start.getTimezoneOffset() * 60000).toISOString().replace('T', ' ').replace(/\..+$/, '')
  var end2 = new Date(end.getTime() - end.getTimezoneOffset() * 60000).toISOString().replace('T', ' ').replace(/\..+$/, '')
  return [start2, end2]
}

// Scada实例查询
export function ScadaClientSelect() {
  var dataParas = {}
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaClientBaseSelect',
    method: 'post',
    data: dataParas
  })
}

// Scada实例对应组查询
export function ScadaTagGroupSelect(device_code) {
  var dataParas = {}
  dataParas.device_code = device_code
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagGroupBaseSelect',
    method: 'post',
    data: dataParas
  })
}

// Scada实例对应标签查询
export function ScadaTagSelect(device_code, group_code) {
  var dataParas = {}
  dataParas.device_code = device_code
  dataParas.group_code = group_code
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagBaseSelect',
    method: 'post',
    data: dataParas
  })
}

// Link查询
export function ScadaLinkReportSelect(device_code, link_status, start_time, end_time, tableSize, page_dirct, page_id) {
  var dataParas = {}
  dataParas.device_code = device_code
  dataParas.link_status = link_status
  dataParas.start_time = start_time
  dataParas.end_time = end_time
  dataParas.tableSize = tableSize
  dataParas.page_dirct = page_dirct
  dataParas.page_id = page_id
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaLinkReportSelect',
    method: 'post',
    data: dataParas
  })
}

// TagRead查询
export function ScadaTagReadReportSelect(device_code, read_status, start_time, end_time, tableSize, page_dirct, page_id) {
  var dataParas = {}
  dataParas.device_code = device_code
  dataParas.read_status = read_status
  dataParas.start_time = start_time
  dataParas.end_time = end_time
  dataParas.tableSize = tableSize
  dataParas.page_dirct = page_dirct
  dataParas.page_id = page_id
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagReadReportSelect',
    method: 'post',
    data: dataParas
  })
}

// TagWrite查询
export function ScadaTagWriteReportSelect(device_code, write_status, start_time, end_time, tableSize, page_dirct, page_id) {
  var dataParas = {}
  dataParas.device_code = device_code
  dataParas.write_status = write_status
  dataParas.start_time = start_time
  dataParas.end_time = end_time
  dataParas.tableSize = tableSize
  dataParas.page_dirct = page_dirct
  dataParas.page_id = page_id
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagWriteReportSelect',
    method: 'post',
    data: dataParas
  })
}

// fastPic查询
export function ScadaFastPicReportSelect(device_code, group_code, start_time, end_time, tableSize, page_dirct, page_id) {
  var dataParas = {}
  dataParas.device_code = device_code
  dataParas.group_code = group_code
  dataParas.start_time = start_time
  dataParas.end_time = end_time
  dataParas.tableSize = tableSize
  dataParas.page_dirct = page_dirct
  dataParas.page_id = page_id
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaFastPicReportSelect',
    method: 'post',
    data: dataParas
  })
}

// tagChange查询
export function ScadaTagChangeReportSelect(device_code, tag_id, tag_alarm_flag, start_time, end_time, tableSize, page_dirct, page_id) {
  var dataParas = {}
  dataParas.device_code = device_code
  dataParas.tag_id = tag_id
  dataParas.tag_alarm_flag = tag_alarm_flag
  dataParas.start_time = start_time
  dataParas.end_time = end_time
  dataParas.tableSize = tableSize
  dataParas.page_dirct = page_dirct
  dataParas.page_id = page_id
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagChangeReportSelect',
    method: 'post',
    data: dataParas
  })
}

export default {
  InitTimePickRange,
  ScadaClientSelect,
  ScadaTagGroupSelect,
  ScadaTagSelect,
  ScadaLinkReportSelect,
  ScadaTagReadReportSelect,
  ScadaTagWriteReportSelect,
  ScadaFastPicReportSelect,
  ScadaTagChangeReportSelect
}
