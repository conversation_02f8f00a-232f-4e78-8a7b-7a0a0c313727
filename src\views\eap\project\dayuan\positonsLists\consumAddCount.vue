<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard">
      <h2>水电消耗统计</h2>
      <el-form :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="操作日期：">
                <div class="block">
                  <el-date-picker
                    v-model="item_date"
                    type="daterange"
                    range-separator="~"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item flRight serachStyle" size="small" type="primary" icon="el-icon-search" @click="toButtableTagQuery(1)">搜索</el-button>
                <el-button class="filter-item flRight" size="small" icon="el-icon-refresh-left" @click="toButResetQuery">重置</el-button>
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="tableTagLoading"
            size="small"
            :data="consumList"
            style="width: 100%"
            height="448"
            max-height="448"
            highlight-current-row
          >
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="record_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="record_date" label="记录时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="record_type" label="记录类型">
              <template slot-scope="scope">
                {{ dict.label.EAP_DY_RECORD_TYPE[scope.row.record_type] }}
                <!-- <span>{{ scope.row.record_type == 'WATER'?'水消耗' :'电消耗' }}</span> -->
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="quantity" label="数量" />
            <el-table-column  :show-overflow-tooltip="true" prop="unit" label="单位" />
            <el-table-column  :show-overflow-tooltip="true" prop="shift" label="班次" />
          </el-table>
          <el-pagination
            :page-size.sync="tagTablePage.size"
            :total="tagTablePage.total"
            :current-page.sync="tagTablePage.page"
            :page-sizes="[10, 30, 50]"
            style="margin-top: 8px; float: right"
            layout="total, prev, pager, next, sizes"
            @size-change="sizeChangeHandler($event)"
            @current-change="currentChangeHandler"
          />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { sel as selConsumed } from '@/api/eap/project/dayuan/eapDyConsumptionCapacityStat'
export default {
  name: 'EAP_DY_CONSUM_PTION_CAPACITY_STAT',
  components: { },
  dicts: ['EAP_DY_RECORD_TYPE'],
  data() {
    return {
      item_date: [],
      height: document.documentElement.clientHeight - 200,
      consumList: [],
      tableTagLoading: false,
      record_types: 'WATER,PUREWATER,ELECTRICITY',
      tagTablePage: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 10,
        // 总数据条数
        total: 0
      }

    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.SetselConsumed()
  },
  methods: {
    toButResetQuery() {
      this.item_date = []
      this.tagTablePage.page = 1
      this.toButtableTagQuery(1)
    },
    toButtableTagQuery(page) {
      console.log(this.item_date)
      this.SetselConsumed(page)
    },
    SetselConsumed(page) {
      var query = {
        item_date: this.item_date,
        record_types: this.record_types,
        tablePage: page, // 当前页
        tableSize: this.tagTablePage.size // 每页数据条数
      }
      this.tableTagLoading = true
      selConsumed(query)
        .then(res => {
          if (res.count > 0) {
            this.tagTablePage.total = res.count
            this.consumList = res.data
          } else {
            this.consumList = []
          }
          this.tableTagLoading = false
        })
        .catch(() => {
          this.$message({
            message: '获取生产片数统计查询异常',
            type: 'error'
          })
          this.consumList = []
          this.tableTagLoading = false
        })
    },
    // Page:分页
    sizeChangeHandler(size) {
      // 查询
      this.tagTablePage.size = size
      // 当出现  当前页  *  条数 > 总条数   的情况时不调用接口
      if (this.tagTablePage.page * size > this.tagTablePage.total) return
      this.toButtableTagQuery(this.tagTablePage.page)
    },
    currentChangeHandler(page) {
      // 查询
      this.toButtableTagQuery(page)
    }
  }
}
</script>
<style lang="less" scoped>

</style>
