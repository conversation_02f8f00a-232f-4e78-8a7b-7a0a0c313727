import request from '@/utils/request'

// 查询废料框基础表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWasteBoxSelect',
    method: 'post',
    data
  })
}
// 新增废料框基础表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWasteBoxInsert',
    method: 'post',
    data
  })
}
// 修改废料框基础表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWasteBoxUpdate',
    method: 'post',
    data
  })
}
// 删除废料框基础表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWasteBoxDelete',
    method: 'post',
    data
  })
}

// 修改废料框基础表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWasteBoxEnableFlagUpdate',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

