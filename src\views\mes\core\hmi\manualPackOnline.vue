<template>
  <div class="app-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="24">
          <div class="statuStyle">
            <p><span :class="monitorData.PlcS_RequestSignal.value === '1' ? 'commonsatu statuOne' : 'commonsatu statuZero'" /><span>允许扫码</span></p>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="wrapCard CardTwo" style="margin-top: 10px;" :style="{'height':height + 'px'}">
      <div class="scanStyle" style="margin-top:50px; ">
        <!-- <div class="orderButton">
          <el-button class="buttonone" size="large" type="primary" @click="getOrder">选择订单</el-button>
        </div> -->
        <div class="wrapDes">
          <el-descriptions :column="2">
            <el-descriptions-item label="订单号">{{ currentOrderInfo.make_order }}</el-descriptions-item>
            <el-descriptions-item label="机型">{{ currentOrderInfo.small_model_type }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div class="scanStyle" style="margin-top:50px; ">
        <span style="margin-left: 25px;">当前已扫车辆码：</span>
        <div class="wrapimin">
          <el-input v-model="monitorData.BarS_GetBarCodeResult.value" type="text" size="large" placeholder="请输入内容" />
        </div>
      </div>
      <div class="scanStyle" style="margin-top:100px; ">
        <span>绑定生成的PACK码：</span>
        <div class="wrapimin">
          <el-input v-model="monitorData.MesQ_ModuleBlockCode.value" type="text" size="large" placeholder="请输入内容" />
        </div>
      </div>
      <div class="printPack">
        <el-button type="primary" @click="printPackCode">重新打印当前PACK码</el-button>
      </div>
    </el-card>
    <el-drawer title="选择订单" :visible.sync="chooseOrder" direction="rtl" size="85%">
      <el-table border :data="radioArr" style="width: 100%" height="550" highlight-current-row @header-dragend="crud.tableHeaderDragend()" @row-click="singleElection">
        <el-table-column align="center" width="55" label="选择">
          <template slot-scope="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio v-model="templateSelection" class="radio" :label="scope.row.mo_id">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="mo_from" label="订单来源" width="80" />
        <el-table-column align="center" prop="make_order" label="订单号" width="150" />
        <el-table-column align="center" prop="product_batch" label="订单批号" width="80" />
        <el-table-column align="center" prop="small_model_type" label="产品型号" width="250" />
        <el-table-column align="center" prop="mo_plan_count" label="订单计划数量" />
        <el-table-column align="center" prop="mo_finish_count" label="实际完成数量" />
        <el-table-column align="center" prop="mo_order_by" label="订单排序" width="80" />
        <el-table-column align="center" prop="plan_start_time" label="计划开始时间" :formatter="formatDate" />
        <el-table-column align="center" prop="plan_end_time" label="计划结束时间" :formatter="formatDate" />
        <el-table-column align="center" prop="start_date" label="订单启动时间" :formatter="formatDate" />
        <el-table-column align="center" prop="finish_date" label="订单完成时间" :formatter="formatDate" />
        <el-table-column align="center" prop="mo_custom_des" label="订单来源客户描述" />
      </el-table>
    </el-drawer>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { chooseOrderInfo, getOrderInfo, showOrderInfo } from '@/api/hmi/orderinfo.js'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'manualPackOnline',
  data() {
    return {
      height: document.documentElement.clientHeight - 90,
      monitorData: {
        // PLC请求信号
        PlcS_RequestSignal: { client_code: 'OP3000', group_code: 'PlcStatus', tag_code: 'PlcS_RequestSignal', value: '' },
        // PACK码
        MesQ_ModuleBlockCode: { client_code: 'OP3000', group_code: 'MesRecipe', tag_code: 'MesQ_ModuleBlockCode', value: '' },
        // Bar扫描条码结果
        BarS_GetBarCodeResult: { client_code: 'OP3001', group_code: 'BarStatus', tag_code: 'BarS_GetBarCodeResult', value: '' }
      },
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      currentOrderInfo: {
        make_order: '',
        small_model_type: ''
      },
      chooseOrder: false,
      templateSelection: '',
      radioArr: []
    }
  },
  mounted: function() {
    this.toStartWatch()
    this.initOrderDes()
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 90
    }
  },
  methods: {
    getOrder() {
      // var $this = this
      this.chooseOrder = true
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.$route.query.prod_line_id,
        station_id: this.$route.query.station_id
      }
      chooseOrderInfo(query)
        .then(res => {
          if (res.code !== 0 && res.count < 0) {
            this.$message({
              message: '选择订单异常',
              type: 'error'
            })
            return
          }
          this.radioArr = res.data
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    // 单选---选择订单
    singleElection(row) {
      this.chooseOrder = false
      this.templateSelection = row.mo_id
      var getQuery = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        make_order: row.make_order,
        station_code: this.$route.query.station_code,
        mo_id: row.mo_id
      }
      getOrderInfo(getQuery)
        .then(() => {
          var showQuery = {
            userName: Cookies.get('userName'),
            station_id: this.$route.query.station_id
          }
          showOrderInfo(showQuery)
            .then(res => {
              const result = JSON.parse(res.result)
              if (result.mo_list.length === 0) {
                this.$message({
                  message: '当前订单数据为空',
                  type: 'warning'
                })
                this.currentOrderInfo.make_order = ''
                this.currentOrderInfo.small_model_type = ''
              } else {
                this.currentOrderInfo.make_order = result.mo_list[0].make_order
                this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
              }
            })
            .catch(() => {
              this.$message({
                message: '展示信息查询异常',
                type: 'error'
              })
            })
        })
        .catch(() => {
          this.$message({
            message: '获取信息查询异常',
            type: 'error'
          })
        })
    },
    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate() + ' ' + dt.getHours() + ':' + dt.getMinutes() + ':' + dt.getSeconds()
      }
    },
    initOrderDes() {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.$route.query.prod_line_id,
        station_id: this.$route.query.station_id
      }
      chooseOrderInfo(query)
        .then(res => {
          if (res.data.length > 0) {
            this.templateSelection = res.data[0].mo_id
            var initshowQuery = {
              userName: Cookies.get('userName'),
              prod_line_id: this.$route.query.prod_line_id,
              station_id: this.$route.query.station_id
            }
            showOrderInfo(initshowQuery)
              .then(res => {
                const result = JSON.parse(res.result)
                if (result.mo_list.length > 0) {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                }
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          }
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    printPackCode() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'OP3002/PrintStatus/PrintS_StartPrint',
        TagValue: 'Print.Module.Gx.Mz01@1@' + this.monitorData.MesQ_ModuleBlockCode.value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/OP3002'
      this.sendMessage(topic, sendStr)
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          //   var connectUrl = 'ws://*************:8083' + '/mqtt'
          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // 获取Tag值
            this.GetTagValue()
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            var jsonData = JSON.parse(message)
            if (jsonData == null) return
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 从后台REDIS获取数据
    GetTagValue() {
      // 读取Tag集合(Key)
      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                Object.keys(this.monitorData).forEach(key => {
                  var client_code = this.monitorData[key].client_code
                  var group_code = this.monitorData[key].group_code
                  var tag_code = this.monitorData[key].tag_code
                  var tag_key = client_code + '/' + group_code + '/' + tag_code
                  const item = result.filter(item => item.tag_key === tag_key)
                  if (item.length > 0) {
                    this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                  }
                })
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.statuStyle {
justify-content: space-around;
p {
  display: flex;
  align-items: center;
  color: #333333;
  margin: 0;
  margin-bottom: 10px;
  span {
    display: flex;
    align-items: center;
  }
  .commonsatu {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    display: block;
  }
  .statuOne {
    background-color: #13ce66;
  }
  .statuZero {
    background-color: #cccccc;
  }
  .statuTwo {
    background-color: #ff4949;
  }
  .statuSecond {
    background-color: #cccccc;
  }
}
}
.scanStyle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .wrapimin{
    position: relative;
    width: 100%;
    img{
        position: absolute;
        right: 7px;
        top: -3px;
        width: 45px;
        z-index: 2;
    }
  }
  .orderButton {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 15px;
    .buttonone {
      width: 90px;
      height: 30px;
      margin-top: -10px;
      margin-bottom: 5px !important;
      position: relative;
      color: #ffffff;
      background: linear-gradient(50deg, #bad1ff, #79a0f1);
      border-radius: 25px;
      text-align: center;
      font-size: 12px;
      box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      user-select: none;
      border: 0;
    }
    .buttonone:active {
      font-size: 14px;
      box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
    }
  }
  ::v-deep .wrapDes{
    margin-left: 30px;
    .el-descriptions-item__container .el-descriptions-item__label{
      font-size: 18px;
    }
    .el-descriptions-item__content{
      font-size: 18px;
    }
  }
}
.printPack{
    display: flex;
    justify-content: center;
    margin-top: 50px;
    .el-button{
        font-size: 20px;
    }
}
</style>
