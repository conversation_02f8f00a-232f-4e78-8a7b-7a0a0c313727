<template>
  <!--电芯排废-->
  <el-card shadow="never">
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <el-form ref="form" class="el-form-wrap" :model="model" :rules="model.rules" size="small">
          <!--表格渲染-->
          <el-table
            ref="table"
            border
            size="small"
            :data="model.tableDataTable"
            style="width: 100%"
            :header-cell-style="{ background: '#eef1f6', color: '#545559' }"
            height="478"
            max-height="678"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @row-click="handleRowClick"
          >
            <!-- Table单条操作-->
            <el-table-column label="操作" align="center" fixed="left" width="70">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="toTableButDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="数据来源" width="120px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.quality_from'" :rules="model.rules.quality_from" style="width:100%">
                  <el-select v-model="scope.row.quality_from" clearable>
                    <el-option v-for="item in [{ label: 'SCADA', id: 'SCADA' }, { label: 'WEBSERVICE', id: 'WEBSERVICE' }]" :key="item.id" :label="item.label" :value="item.id" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="设定值" width="180px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_set_id'" :rules="model.rules.tag_set_id" style="width:100%">
                  <el-input v-model.number="scope.row.tag_set_id" readonly="readonly" width="120px">
                    <el-button slot="append" @click="openSelectTag">选择</el-button>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="标准值" width="180px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_actual_id'" :rules="model.rules.tag_actual_id" style="width:100%">
                  <el-input v-model.number="scope.row.tag_actual_id" readonly="readonly" width="120px">
                    <el-button slot="append" @click="openSelectTag2">选择</el-button>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="采集项目名称" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_des'" style="width:100%">
                  <el-input v-model="scope.row.tag_des" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="采集项目单位" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_uom'" style="width:100%">
                  <el-input v-model="scope.row.tag_uom" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="下限值" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.down_limit'" style="width:100%">
                  <el-input v-model="scope.row.down_limit" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="上限值" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.upper_limit'" style="width:100%">
                  <el-input v-model="scope.row.upper_limit" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="有效标识" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.enable_flag'" style="width:100%">
                  <el-select v-model="scope.row.enable_flag" clearable>
                    <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-col>
    </el-row>
    <el-dialog :fullscreen="false" top="10px" :modal-append-to-body="true" :append-to-body="true" title="设定值" custom-class="step-attr-dialog" :visible.sync="customPopover1">
      <tagSelect ref="tagSelect" client-id-list="" :tag-id="currentRow.tag_set_id" @chooseTag="handleChooseTag1" />
    </el-dialog>
    <el-dialog :fullscreen="false" top="10px" :modal-append-to-body="true" :append-to-body="true" title="实际值" custom-class="step-attr-dialog" :visible.sync="customPopover2">
      <tagSelect ref="tagSelect" client-id-list="" :tag-id="currentRow.tag_actual_id" @chooseTag="handleChooseTag2" />
    </el-dialog>

    <div style="text-align: center;margin-top:10px">
      <el-button type="primary" size="small" icon="el-icon-plus" @click="toButAdd">新增行</el-button>
      <!-- 取消 -->
      <el-button type="primary" size="small" icon="el-icon-check" :loading="saveBtnLoading" :disabled="model.tableDataTable.length === 0" @click="saveData">保存</el-button>
      <!-- 确认 -->
    </div>
  </el-card>
</template>

<script>
import { batchAdd } from '@/api/eap/core/recipeQuality'
import Cookies from 'js-cookie'
export default {
  name: 'qualityBatch',
  props: {
    proceduce_id: {
      type: [String, Number],
      default: -1
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      saveBtnLoading: false,
      model: {
        rules: {
          quality_from: [{ required: true, message: '请选择数据来源', trigger: 'blur' }],
          tag_set_id: [{ required: true, message: '请选择设定值标签', trigger: 'blur' }],
          tag_actual_id: [{ required: true, message: '请选择实际值标签', trigger: 'blur' }]
        },
        tableDataTable: []
      },
      customPopover1: false,
      customPopover2: false,
      currentRow: {
        tag_set_id: ''
      }
    }
  },
  // 数据字典
  dicts: ['CONTROL_TYPE', 'ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.toButAdd()
  },
  methods: {
    // 保存
    saveData() {
      this.$refs['form'].validate((valid, model) => {
        if (valid) {
          const save = {
            user_name: Cookies.get('userName'),
            qualityList: this.model.tableDataTable
          }
          this.saveBtnLoading = true
          batchAdd(save)
            .then(res => {
              this.saveBtnLoading = false
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: '保存成功！', type: 'success' })
                this.$emit('updateVisible', false)
              } else {
                this.$message({ message: '保存失败：' + defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              this.saveBtnLoading = false
              this.$message({
                message: '保存异常',
                type: 'error'
              })
            })
        }
      })
    },
    toTableButDelete(row) {
      console.info(row.seq)
      this.model.tableDataTable.splice(row.seq, 1)
      this.refreshSeq()
    },
    toButAdd() {
      this.model.tableDataTable.push({
        seq: this.model.tableDataTable.length,
        quality_id: '',
        proceduce_id: this.proceduce_id,
        quality_from: '',
        tag_set_id: '',
        tag_actual_id: '',
        tag_des: '',
        tag_uom: '',
        theory_value: '',
        down_limit: '',
        upper_limit: '',
        enable_flag: 'Y'
      })
    },
    refreshSeq() {
      for (let i = 0; i < this.model.tableDataTable.length; i++) {
        this.model.tableDataTable[i].seq = i
      }
    },
    handleRowClick(row, column, event) {
      this.currentRow = row
    },
    handleChooseTag1(tagId) {
      this.currentRow.tag_set_id = tagId
      this.customPopover1 = false
    },
    openSelectTag() {
      this.customPopover1 = true
    },
    openSelectTag2() {
      this.customPopover2 = true
    },
    handleChooseTag2(tagId) {
      this.currentRow.tag_actual_id = tagId
      this.customPopover2 = false
    }
  }
}
</script>
