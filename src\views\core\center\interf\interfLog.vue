<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <!-- 接口名称 -->
              <el-form-item :label="$t('view.form.interfaceName')+':'">
                <el-input v-model="query.esb_interf_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 接口描述： -->
              <el-form-item :label="$t('view.form.interfaceDescription')+':'">
                <el-input v-model="query.esb_interf_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('view.form.timePeriod') + ':'">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:100%"
                    :picker-options="pickerOptions"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 来源系统： -->
              <el-form-item :label="$t('view.form.sourceSystem')+':'">
                <el-input v-model="query.data_from_sys" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 目标系统： -->
              <el-form-item :label="$t('view.form.targetSystem')+':'">
                <el-input v-model="query.data_to_sys" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 请求参数： -->
              <el-form-item :label="$t('view.form.requestParameters')+':'">
                <el-input v-model="query.request_paras" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 响应参数： -->
              <el-form-item :label="$t('view.form.responseParameters')+':'">
                <el-input v-model="query.response_paras" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 是否成功： -->
              <el-form-item :label="$t('view.form.isSuccess')+':'">
                <el-select
                  v-model="query.success_flag"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['Y','N']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <!-- 接口方式： -->
              <el-form-item :label="$t('view.form.interfaceMethod')+':'">
                <el-select
                  v-model="query.esb_interf_way"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['WebApi','WebService','MQ']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button
                  class="filter-item"
                  size="small"
                  type="primary"
                  icon="el-icon-download"
                  :disabled="crud.data.length <= 0"
                  @click="exportExecl"
                >{{ $t('view.button.export') }}</el-button>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('view.button.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('view.button.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="esb_interf_code" width="150" :label="$t('view.table.esbInterfaceName')" />
            <el-table-column :show-overflow-tooltip="true" prop="esb_interf_des" width="200" :label="$t('view.table.esbInterfaceDescription')" />
            <el-table-column :show-overflow-tooltip="true" prop="esb_interf_way" width="100" :label="$t('view.table.interfaceMethod')" />
            <el-table-column :show-overflow-tooltip="true" prop="esb_intef_url" width="150" :label="$t('view.table.esbInterfaceAddress')" />
            <el-table-column :show-overflow-tooltip="true" prop="data_from_sys" width="120" :label="$t('view.table.sourceSystem')" />
            <el-table-column :show-overflow-tooltip="true" prop="data_to_sys" width="120" :label="$t('view.table.targetSystem')" />
            <el-table-column :show-overflow-tooltip="true" prop="start_date" width="150" :label="$t('view.table.startTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="end_date" width="150" :label="$t('view.table.endTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="cost_time" width="120" :label="$t('view.table.requestDuration')" />
            <el-table-column :show-overflow-tooltip="true" prop="success_flag" width="120" :label="$t('view.table.isSuccess')" />
            <el-table-column :show-overflow-tooltip="true" prop="message" width="130" :label="$t('view.table.interfaceMessage')" />
            <el-table-column :show-overflow-tooltip="true" prop="request_paras" width="120" :label="$t('view.table.requestParameters')">
              <template slot-scope="scope">
                <el-button type="primary" plain @click="openEditInterfParas(scope.row.request_paras)">{{ $t('view.button.view') }}</el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="response_paras" width="130" :label="$t('view.table.responseParameters')">
              <template slot-scope="scope">
                <el-button type="primary" plain @click="openEditInterfParas(scope.row.response_paras)">{{ $t('view.button.view') }}</el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="paras_list" width="150" :label="$t('view.table.customizedParameters')" />
            <el-table-column :show-overflow-tooltip="true" prop="request_info" width="150" :label="$t('view.table.requestMessage')" />
            <el-table-column :show-overflow-tooltip="true" prop="remarks" width="150" :label="$t('view.table.remark')" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >
                {{ $t('view.pagination.total') }}: {{ page.total }}</el-button>
              <el-button
                type="primary"
              >
                {{ $t('view.pagination.current') }}{{ nowPageIndex }}{{ $t('view.pagination.unit') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >
                &lt;&nbsp;{{ $t('view.pagination.previous') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >
                {{ $t('view.pagination.next') }}&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-dialog :title="$t('lang_pack.interfaceLogs.paramsDetails')" width="50%" top="20px" :visible.sync="dialogInterfParasVisible" :append-to-body="true" :close-on-click-modal="false">
      <el-input v-model="interf_paras" type="textarea" :rows="20" :placeholder="$t('view.enum.placeholder.pleaseEnter')" />
      <span style="color:red;">{{ jsonErrorMsg }}</span>
    </el-dialog>
    <download-excel v-if="downloadFlag" class="export-excel-wrapper" :data="json_data" :fields="json_fields" :name="$t('view.title.interfaceLog') + '_' + $t('view.button.export')" />
  </div>
</template>

<script>
import crudCoreEsbInterfLog from '@/api/core/center/sysCoreEsbInterfLog'
import { down } from '@/api/core/center/interf'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'
const defaultForm = {

}
export default {
  name: 'SYS_INTERF_LOG',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.interfaceLog'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: '_id',
      // 排序
      sort: ['item_date desc'],
      // CRUD Method
      crudMethod: { ...crudCoreEsbInterfLog },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        tableSize: 40,
        currentPage: 1
      }
    })
  },
  // 数据字典
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    const d = {
      height: document.documentElement.clientHeight - 350,
      permission: {
        add: ['admin', 'sys_core_esb_interf_log:add'],
        edit: ['admin', 'sys_core_esb_interf_log:edit'],
        del: ['admin', 'sys_core_esb_interf_log:del'],
        down: ['admin', 'sys_core_esb_interf_log:down']
      },
      nowPageIndex: 1, // 当前页数
      pageList: [],
      dialogInterfParasVisible: false,
      json_fields: {},
      json_data: [],
      downloadFlag: false,
      interf_paras: '',
      jsonErrorMsg: '',
      pickerOptions: {}
    }
    const serialNumber = this.$t('view.table.serialNumber')
    const esbInterfaceName = this.$t('view.table.esbInterfaceName')
    const esbInterfaceDescription = this.$t('view.table.esbInterfaceDescription')
    const interfaceMethod = this.$t('view.table.interfaceMethod')
    const esbInterfaceAddress = this.$t('view.table.esbInterfaceAddress')
    const sourceSystem = this.$t('view.table.sourceSystem')
    const targetSystem = this.$t('view.table.targetSystem')
    const startTime = this.$t('view.table.startTime')
    const endTime = this.$t('view.table.endTime')
    const requestDuration = this.$t('view.table.requestDuration')
    const isSuccess = this.$t('view.table.isSuccess')
    const interfaceMessage = this.$t('view.table.interfaceMessage')
    const requestParameters = this.$t('view.table.requestParameters')
    const responseParameters = this.$t('view.table.responseParameters')
    const customizedParameters = this.$t('view.table.customizedParameters')
    const requestMessage = this.$t('view.table.requestMessage')
    const remark = this.$t('view.table.remark')
    d.json_fields[serialNumber] = 'index'
    d.json_fields[esbInterfaceName] = 'esb_interf_code'
    d.json_fields[esbInterfaceDescription] = 'esb_interf_des'
    d.json_fields[interfaceMethod] = 'esb_interf_way'
    d.json_fields[esbInterfaceAddress] = 'esb_intef_url'
    d.json_fields[sourceSystem] = 'data_from_sys'
    d.json_fields[targetSystem] = 'data_to_sys'
    d.json_fields[startTime] = 'start_date'
    d.json_fields[endTime] = 'end_date'
    d.json_fields[requestDuration] = 'cost_time'
    d.json_fields[isSuccess] = 'success_flag'
    d.json_fields[interfaceMessage] = 'message'
    d.json_fields[requestParameters] = 'request_paras'
    d.json_fields[responseParameters] = 'response_paras'
    d.json_fields[customizedParameters] = 'paras_list'
    d.json_fields[requestMessage] = 'request_info'
    d.json_fields[remark] = 'remarks'
    return d
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 350
    }
  },
  created: function() {
    // Initialize date picker shortcuts
    this.pickerOptions = createDatePickerShortcuts(this.$i18n);

    const dynamicFields = [
      { key: this.$t('view.table.serialNumber'), value: 'index' },
      { key: this.$t('view.table.esbInterfaceName'), value: 'esb_interf_code' },
      { key: this.$t('view.table.esbInterfaceDescription'), value: 'esb_interf_des' },
      { key: this.$t('view.table.interfaceMethod'), value: 'esb_interf_way' },
      { key: this.$t('view.table.esbInterfaceAddress'), value: 'esb_intef_url' },
      { key: this.$t('view.table.sourceSystem'), value: 'data_from_sys' },
      { key: this.$t('view.table.targetSystem'), value: 'data_to_sys' },
      { key: this.$t('view.table.startTime'), value: 'start_date' },
      { key: this.$t('view.table.endTime'), value: 'end_date' },
      { key: this.$t('view.table.requestDuration'), value: 'cost_time' },
      { key: this.$t('view.table.isSuccess'), value: 'success_flag' },
      { key: this.$t('view.table.interfaceMessage'), value: 'message' },
      { key: this.$t('view.table.requestParameters'), value: 'request_paras' },
      { key: this.$t('view.table.responseParameters'), value: 'response_paras' },
      { key: this.$t('view.table.customizedParameters'), value: 'paras_list' },
      { key: this.$t('view.table.requestMessage'), value: 'request_info' },
      { key: this.$t('view.table.remark'), value: 'remarks' }
    ]
    dynamicFields.forEach(field => {
      this.$set(this.json_fields, field.key, field.value)
    })
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('view.dialog.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.currentPage = this.nowPageIndex
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('view.dialog.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.currentPage = this.nowPageIndex
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    openEditInterfParas(paras) {
      this.interf_paras = '{}'
      this.jsonErrorMsg = ''
      if (paras !== '') {
        try {
          var _json = JSON.parse(paras)

          if (typeof _json === 'object' && _json) {
            this.interf_paras = JSON.stringify(_json, null, 4)
          }
        } catch (ex) {
          this.jsonErrorMsg = ex
          this.interf_paras = paras
        }
      }
      this.dialogInterfParasVisible = true
    },
    // 数据导出
    exportExecl() {
      const query = {
        item_date: this.query.item_date,
        esb_interf_code: this.query.esb_interf_code,
        esb_interf_des: this.query.esb_interf_des,
        data_from_sys: this.query.data_from_sys,
        data_to_sys: this.query.data_to_sys,
        request_paras: this.query.request_paras,
        response_paras: this.query.response_paras,
        success_flag: this.query.success_flag,
        esb_interf_way: this.query.esb_interf_way
      }
      down(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.json_data = defaultQuery.data || []
          this.json_data = [
            {
              esb_interf_code: '1',
              esb_interf_des: '2',
              esb_interf_way: '1',
              esb_intef_url: '2',
              data_from_sys: '1',
              data_to_sys: '2',
              start_date: '1',
              end_date: '2',
              cost_time: '1',
              success_flag: '2',
              message: '1',
              request_paras: '2',
              response_paras: '1',
              paras_list: '2',
              request_info: '1',
              remarks: '2'
            }
          ]
          this.json_data.forEach((e, index) => {
            e.index = index + 1
          })
          this.downloadFlag = true
          this.$nextTick(() => {
            const dom = document.querySelector('.export-excel-wrapper')
            dom.style.display = 'none'
            dom.click()
          })
        } else {
          this.$message.warning(this.$t('view.dialog.exportFailed'))
        }
      })
        .catch((err) => {
          this.$message.warning(this.$t('view.dialog.exportFailed') + err)
        })
    }
  }
}
</script>

<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
::v-deep .el-textarea__inner {
  color: #f8c555;
  background-color: #2d2d2d;
}
</style>
