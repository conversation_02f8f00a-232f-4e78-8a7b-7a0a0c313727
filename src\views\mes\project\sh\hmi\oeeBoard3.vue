<template>
  <div id="#bigScreen" class="mesContainer">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :default-time="['00:00:00', '23:59:59']"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
            <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button
                  class="filter-item"
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  @click="handleOk"
                >{{ $t("lang_pack.commonPage.search") }}</el-button>
                <!-- 搜索 -->
                <el-button
                  v-if="crud.optShow.reset"
                  class="filter-item"
                  size="small"
                  icon="el-icon-refresh-left"
                  @click="resetQuery()"
                >{{ $t("lang_pack.commonPage.reset") }}</el-button>
                <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    <div class="header">
      <img src="@/assets/images/sh/headerLeft.png" alt="">
      <span>产线当月统计报表</span>
      <img src="@/assets/images/sh/headerRight.png" alt="">
    </div>
    <div style="margin-top: 20px;display: flex;">
      <div id="dailyDom" style="width: 50%;height: 480px" />
      <div id="monthlyDom" style="width: 50%;height: 480px" />
    </div>
    <div style="margin-top: 20px;display: flex;">
      <div id="planNgDom" style="width: 50%;height: 480px" />
      <div id="qualifiedDom" style="width: 50%;height: 480px" />
    </div>
  </div>
</template>
<script>
import { PlanMoSel6, PlanMoSel7, PlanMoSel8, PlanMoSel9 } from '@/api/mes/project/sh/shProductionScreen'
import autofit from 'autofit.js'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {}
export default {
  name: 'productionBoard',
  components: { crudOperation },
    cruds() {
    return CRUD({
      title: '过站物料数据',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_trace_id',
      // 排序
      sort: ['material_trace_id desc'],
      // CRUD Method
      crudMethod: { ...PlanMoSel6 },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
      }
    })
  },
  // 数据字典
  dicts: ['QUALIFIED_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  data() {
    return {
      height: document.documentElement.clientHeight - 390,
      permission: {
        add: ['admin', 'mes_me_station_material:add'],
        edit: ['admin', 'mes_me_station_material:edit'],
        del: ['admin', 'mes_me_station_material:del'],
        down: ['admin', 'mes_me_station_material:down']
      },
      dailyDom: null,
      monthlyDom: null,
      planNgDom: null,
      qualifiedDom: null,
      productTimer: null,
      timer: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getDailyProduction() // 获取当前日产量
      this.getmonthlyProduction() // 获取当前月产量
      this.getPlanNgProduction() // 计划ng数量
      this.getQualifiedProduction() // 计划ng数量
    })
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    // this.productTimer = setInterval(() => {
    //   this.getDailyProduction() // 获取当前日产量
    //   this.getmonthlyProduction() // 获取当前月产量
    //   this.getPlanNgProduction() // 计划ng数量
    //   this.getQualifiedProduction() // 计划ng数量
    // }, 1000)
  },
  created() {
  },
  beforeDestroy() {

  },
  methods: {
    handleOk(){
      this.getDailyProduction() // 获取当前日产量
      this.getmonthlyProduction() // 获取当前月产量
      this.getPlanNgProduction() // 计划ng数量
      this.getQualifiedProduction() // 计划ng数量
    },
    getPlanNgProduction() {
      var series = []
      PlanMoSel8(this.query).then(res => {
        if (res.code === 0 && res.data !== '') {
          series = [
            {
              name: '下线数',
              type: 'line',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: res.data.map(e => { return e.count }),
              lineStyle: {// 设置线条颜色
                color: '#005AFF' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#223871' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#005AFF',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'planNgDom',
            color: ['#34436C'],
            title: '当月产量',
            legendData: ['下线数1'],
            xAxisData: res.data.map(e => { return e.time }),
            series
          }
          this.getEchartsData(data)
        } else {
          series = [
            {
              name: '下线数',
              type: 'line',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0],
              lineStyle: {// 设置线条颜色
                color: '#005AFF' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#223871' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#005AFF',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'planNgDom',
            color: ['#34436C'],
            title: '当月产量',
            legendData: ['下线数1'],
            xAxisData: [0, 0, 0, 0, 0],
            series
          }
          this.getEchartsData(data)
        }
      })
    },
    getmonthlyProduction() {
      var series = []
      PlanMoSel7(this.query).then(res => {
        if (res.code === 0 && res.data !== '') {
          series = [

            {
              name: '合格率',
              type: 'line',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: res.data.map(e => { return e.count }),
              lineStyle: {// 设置线条颜色
                color: '#35E00A' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#265A5C' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}' + '%', // 显示数值
                color: '#35E00A',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'monthlyDom',
            color: ['#265A5C'],
            title: '当月一次交检合格率',
            legendData: ['下线数1'],
            xAxisData: res.data.map(e => { return e.time }),
            series
          }
          this.getEchartsData(data)
        } else {
          series = [

            {
              name: '合格率',
              type: 'line',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0],
              lineStyle: {// 设置线条颜色
                color: '#35E00A' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#265A5C' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}' + '%', // 显示数值
                color: '#35E00A',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'monthlyDom',
            color: ['#265A5C'],
            title: '当月一次交检合格率',
            legendData: ['下线数1'],
            xAxisData: [0, 0, 0, 0, 0],
            series
          }
          this.getEchartsData(data)
        }
      })
    },
    getDailyProduction() {
      var series = []
      PlanMoSel6(this.query).then(res => {
        if (res.code === 0 && res.data !== '') {
          series = [
            {
              name: '可动率',
              type: 'line',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: res.data.map(e => { return e.count }),
              lineStyle: {// 设置线条颜色
                color: '#35E00A' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#265A5C' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}' + '%', // 显示数值
                color: '#35E00A',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'dailyDom',
            color: ['#223871', '#265A5C'],
            title: '当月可动率',
            legendData: ['可动率1'],
            xAxisData: res.data.map(e => { return e.time }),
            series
          }
          this.getEchartsData(data)
        } else {
          series = [
            {
              name: '可动率',
              type: 'line',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0],
              lineStyle: {// 设置线条颜色
                color: '#35E00A' // 折线线条颜色
              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#265A5C' // 设置线条上点的颜色（和图例的颜色）
                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}' + '%', // 显示数值
                color: '#35E00A',
                fontSize: 50
              }
            }
          ]
          const data = {
            dom: 'dailyDom',
            color: ['#223871', '#265A5C'],
            title: '当月可动率',
            legendData: ['可动率1'],
            xAxisData: [0, 0, 0, 0, 0],
            series
          }
          this.getEchartsData(data)
        }
      })
    },
    getQualifiedProduction() {
      var series = []
      PlanMoSel9(this.query).then(res => {
        if (res.code === 0 && res.data !== '') {
          series = [
            {
              name: '报警次数',
              type: 'bar',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: res.data.map(e => { return e.count }),
              lineStyle: {// 设置线条颜色
                color: '#5891E6' // 折线线条颜色

              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#34436C' // 设置线条上点的颜色（和图例的颜色）

                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#5891E6',
                fontSize: 50
              }
            }

          ]
          const data = {
            dom: 'qualifiedDom',
            color: ['#223871', '#265A5C'],
            title: '当月工位不良报警数TOP5',
            legendData: ['报警次数1'],
            xAxisData: res.data.map(e => { return e.station_code }),
            series
          }
          this.getEchartsData(data)
        } else {
          series = [
            {
              name: '报警次数',
              type: 'bar',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [0, 0, 0, 0, 0],
              lineStyle: {// 设置线条颜色
                color: '#5891E6' // 折线线条颜色

              },
              itemStyle: {// 设置端点颜色
                normal: {
                  color: '#34436C' // 设置线条上点的颜色（和图例的颜色）

                }
              },
              label: {
                show: true,
                position: 'top', // 数值显示在拐点上方
                formatter: '{c}', // 显示数值
                color: '#5891E6',
                fontSize: 50
              }
            }

          ]
          const data = {
            dom: 'qualifiedDom',
            color: ['#223871', '#265A5C'],
            title: '工位不良报警数TOP5',
            legendData: ['报警次数1'],
            xAxisData: [0, 0, 0, 0, 0],
            series
          }
          this.getEchartsData(data)
        }
      })
    },
    getEchartsData(result) {
      var that = this
      this[result.dom] = this.$echarts.init(document.getElementById(result.dom))
      var option = {
        color: result.color,
        title: {
          text: result.title,
          textStyle: {
            color: '#fff',
            fontSize: 60
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
              color: '#fff'
            }
          }
        },
        legend: {
          x: 'center',
          y: 'bottom',
          textStyle: {
            color: '#fff',
            fontFamily: 'Alibaba PuHuiTi',
            fontSize: 42
          },
          icon: 'circle',
          itemWidth: 20, // 修改图例标识的宽度
          itemHeight: 20, // 修改icon图形大小
          itemGap: 24, // 修改间距
          data: result.legendData
        },
        grid: {
          top: '24%',
          left: '3%',
          right: '4%',
          bottom: '12%',
          containLabel: true
        },
        xAxis: [
          {
            axisLabel: {
              textStyle: {
                fontSize: 40,
                color: '#fff'
              }
            },
            axisLine: {
              onZero: false
            },
            type: 'category',
            boundaryGap: true,
            data: result.xAxisData
          }
        ],
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 30,
              color: '#fff'
            }
          }
        },
        series: result.series
      }
      this[result.dom].setOption(option)
      window.addEventListener('resize', function() {
        that[result.dom].resize()
      })
    }
  }
}
</script>
  <style lang="less" scoped>
  .mesContainer{
      background: #2B304D;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      transform-origin: 0 0;
      padding: 0 15px;
      .header{
          width: 100%;
          height: 100px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          span{
              font-size: 80px;
              color: #fff;
              font-weight: 600;
          }
      }
  }
  </style>
