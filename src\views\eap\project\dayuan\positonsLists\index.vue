<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard">
      <el-tabs v-model="activeName">
        <el-tab-pane label="药水添加" name="first"><potionsAddRecord /><potionsAddCount /></el-tab-pane>
        <el-tab-pane label="水电消耗" name="second"><consumeRecord /><consumAddCount /></el-tab-pane>
        <el-tab-pane label="生产片数" name="third"><productedRecord /><productedAddCount /></el-tab-pane>
      </el-tabs>
    </el-card>

  </div>
</template>

<script>
import potionsAddRecord from './potionsAddRecord'
import potionsAddCount from './potionsAddCount'
import consumeRecord from './consumeRecord'
import consumAddCount from './consumAddCount'
import productedRecord from './productedRecord'
import productedAddCount from './productedAddCount'
export default {
  name: 'EAP_DY_POTIOND_RECORD',
  components: { potionsAddRecord, potionsAddCount, consumeRecord, consumAddCount, productedRecord, productedAddCount },
  props: {},
  // 数据模型
  data() {
    return {
      activeName: 'first'
    }
  },
  watch: {},
  mounted() {

  },
  created: function() {
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>

</style>
