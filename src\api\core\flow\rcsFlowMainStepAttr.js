import request from '@/utils/request'

// 查询
export function selAll(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainAllStepAttrSel',
    method: 'post',
    data
  })
}
// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrSel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrDel',
    method: 'post',
    data
  })
}
// 保存
export function saveAttr(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrSave',
    method: 'post',
    data
  })
}
// 查询属性监控数据
export function selAttrMonitor(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrMonitorSel',
    method: 'post',
    data
  })
}
// 自动匹配流程属性
export function autoMatchAttr(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowMainStepAttrAutoMatch',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, selAll, saveAttr, selAttrMonitor, autoMatchAttr }

