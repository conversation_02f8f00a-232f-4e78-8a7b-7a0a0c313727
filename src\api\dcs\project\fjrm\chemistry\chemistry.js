import request from '@/utils/request'

// 查询化学成分基础表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodChemistrySelect',
    method: 'post',
    data
  })
}
// 新增化学成分基础表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodChemistryInsert',
    method: 'post',
    data
  })
}
// 修改化学成分基础表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodChemistryUpdate',
    method: 'post',
    data
  })
}
// 删除化学成分基础表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodChemistryDelete',
    method: 'post',
    data
  })
}

// 修改化学成分基础表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodChemistryEnableFlagUpdate',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

