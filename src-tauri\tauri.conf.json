{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeBuildCommand": "npm run build:prod", "beforeDevCommand": "", "devPath": "../dist", "distDir": "http://127.0.0.1:9090/whiteLogin"}, "package": {"productName": "AIS Work", "version": "0.1.0"}, "tauri": {"allowlist": {"all": true, "http": {"all": true, "request": true, "scope": ["http://**", "https://**"]}}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/icon.ico"], "identifier": "com.JHInfo.dev", "longDescription": "", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "resources": [], "shortDescription": "", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "height": 768, "resizable": true, "title": "AIS", "width": 1024, "maximized": true}]}}