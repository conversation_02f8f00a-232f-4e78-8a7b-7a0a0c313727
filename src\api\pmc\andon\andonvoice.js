import request from '@/utils/request'

// 查询安灯音响基础
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonVoiceSel',
    method: 'post',
    data
  })
}
// 新增安灯音响基础
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonVoiceIns',
    method: 'post',
    data
  })
}
// 修改安灯音响基础
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonVoiceUpd',
    method: 'post',
    data
  })
}
// 删除安灯音响基础
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcAndonVoiceDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

