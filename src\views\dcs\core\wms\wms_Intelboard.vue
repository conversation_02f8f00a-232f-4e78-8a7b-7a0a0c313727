<template>
  <el-container style="height: 100%">
    <el-header
      :style="
        'background-image:url(' +
          headerBackground +
          ');background-size:100% 100%;width:100%;height:90px'
      "
    >
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7">
          <img
            :src="headerLogo"
            style="width: 150px; height: 30px; float: left; margin-left: 210px;margin-top: 5px;"
          >
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 25px">
          <span class="title">智能下料车间-智能行车</span>
        </el-col>
        <el-col
          :span="7"
          style="text-align: center; padding-top: 10px; padding-right: 150px"
        ><span class="time"> {{ nowDate }}{{ time }}{{ week }} </span></el-col>
      </el-row>
      <div style="display: flex;justify-content: left;align-items: center;color: #fff;">
        <img
          :src="monitorInit"
          style="width: 30px"
        >
        <span class="dataInfo">无</span>
        <img
          :src="monitorGreen"
          style="width: 30px;margin-left: 20px;"
        >
        <span class="dataInfo">正常</span>
        <img
          :src="monitorRed"
          style="width: 30px;margin-left: 20px;"
        >
        <span class="dataInfo">异常</span>
      </div>
    </el-header>
    <el-main>
      <el-row v-for="(item,index) in TagKey" :key="index" :gutter="20">
        <el-col v-for="(itemArr,i) in item.keyValue" :key="i" :span="itemArr.span" :style="{'margin-top':index=== 1 ? '10px' :''}">
          <el-card class="cardTextBox">
            <div slot="header" class="wrapTextSelect">
              <span>{{ itemArr.name }}</span>
            </div>
            <div v-if="index === 0 && i === 0 " class="PlcStatusInfo">
              <template v-for="itemKey in itemArr.key">
                <div class="PlcStatusValue">
                  <img
                    :id="`img_signal_${itemKey.key.split('Status/')[1]}`"
                    :src=" itemKey.tagValue === '1' ? monitorGreen : monitorInit"
                    style="width: 30px"
                  >
                  <span class="dataInfo">{{ itemKey.label }}</span>
                </div>
              </template>
            </div>
            <div v-if="index === 0 && i === 1" style="display: flex;">
              <div class="PlcStatusInfo" style="width: 50%;">
                <template v-for="itemKey in itemArr.key">
                  <div class="PlcStatuspoint">
                    <img
                      :id="`img_signal_${itemKey.key.split('Status/')[1]}`"
                      :src="itemKey.tagValue === '1' ? monitorGreen : monitorInit"
                      style="width: 30px"
                    >
                    <span class="dataInfo">{{ itemKey.label }}</span>
                  </div>
                </template>
              </div>
              <div class="PlcStatusInfo" style="width: 50%;">
                <div class="RCSStatusValue">
                  <span class="dataInfo dataInfoName">称重值：</span>
                  <span class="dataInfo">{{ resultTagKey.Weight }}</span>
                </div>
                <div class="RCSStatusValue">
                  <span class="dataInfo dataInfoName">真空度：</span>
                  <span class="dataInfo">{{ resultTagKey.Vacuum }}</span>
                </div>
                <div class="RCSStatusValue">
                  <span class="dataInfo dataInfoName">大车实时位置：</span>
                  <span class="dataInfo">{{ resultTagKey.X_real_time }}</span>
                </div>
                <div class="RCSStatusValue">
                  <span class="dataInfo dataInfoName">小车实时位置：</span>
                  <span class="dataInfo">{{ resultTagKey.Y_real_time }}</span>
                </div>
                <div class="RCSStatusValue">
                  <span class="dataInfo dataInfoName">起升实时位置：</span>
                  <span class="dataInfo">{{ resultTagKey.Z_real_time }}</span>
                </div>
              </div>
            </div>
            <div v-if="index === 1 " :style="{'margin-bottom' : (i === 0 || i === 1) ? '30px' : ''}">
              <template v-for="(itemKey,k) in itemArr.key">
                <div v-if="i !== 2" class="WMSStatusInfo">
                  <span class="dataInfo dataInfoName">{{ itemKey.label }}：</span>
                  <span class="dataInfo">{{ itemKey.tagValue }}</span>
                </div>
                <img
                  v-if="i === 2"
                  :src="itemKey.imgUrl"
                  style="width: 730px;height: 445px;"
                >
              </template>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
  </el-container>
</template>
<script>
import { getIconPathData } from '@/components/wmsDashboard/icon'
import headerBackground from '@/assets/images/dcs/header1.png'
import headerLogo from '@/assets/images/dcs/xcmg.png'
import xcmgGroup from '@/assets/images/dcs/xcmgGroup1.jpg'
import monitorInit from '@/assets/images/dcs/Init.png'
import monitorGreen from '@/assets/images/dcs/green.png'
import monitorRed from '@/assets/images/dcs/red.png'
import axios from 'axios'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
import { lovCell, selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  data() {
    return {
      imgData: getIconPathData(),
      headerBackground: headerBackground,
      headerLogo: headerLogo,
      xcmgGroup: xcmgGroup,
      monitorInit: monitorInit,
      monitorGreen: monitorGreen,
      monitorRed: monitorRed,
      diameter: 15,
      nodeForm: {},
      nowDate: '',
      time: '',
      week: '',
      timer1: null,
      timerLoad: null,
      // MQTT
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
      'ScadaWeb_' +
      Cookies.get('userId') +
      '_' +
      Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      TagKey: [
        {
          keyValue: [
            {
              name: '共有条件',
              span: 14,
              key: [
                { label: '自动运行', key: 'SlCarPlc01/PlcStatus/PlcAuto', tagValue: '0' },
                { label: '手动运行', key: 'SlCarPlc01/PlcStatus/PlcManual', tagValue: '0' },
                { label: '安全区域内', key: 'SlCarPlc01/PlcStatus/PlcTag008', tagValue: '0' },
                { label: '机旁柜暂停', key: 'SlCarPlc01/PlcStatus/PlcTag009', tagValue: '0' },
                { label: '自动暂停', key: 'SlCarPlc01/PlcStatus/PlcTag010', tagValue: '0' },
                { label: '中控暂停', key: 'SlCarPlc01/PlcStatus/PlcTag011', tagValue: '0' },
                { label: '吸附完成', key: 'SlCarPlc01/PlcStatus/AdsorptionFinish', tagValue: '0' },
                { label: '车上电柜急停', key: 'SlCarPlc01/PlcStatus/PlcTag001', tagValue: '0' },
                { label: '机旁柜急停', key: 'SlCarPlc01/PlcStatus/PlcTag002', tagValue: '0' },
                { label: '遥控器急停', key: 'SlCarPlc01/PlcStatus/PlcTag003', tagValue: '0' },
                { label: '吊具宽度调整完成', key: 'SlCarPlc01/PlcStatus/PlcTag004', tagValue: '0' },
                { label: '吊具吸盘升降完成', key: 'SlCarPlc01/PlcStatus/PlcTag005', tagValue: '0' },
                { label: '吊具有板', key: 'SlCarPlc01/PlcStatus/HangingPlate', tagValue: '0' },
                { label: '行车空闲', key: 'SlCarPlc01/PlcStatus/PlcEmptyTask', tagValue: '0' },
                { label: '大车安全门已关闭', key: 'SlCarPlc01/PlcStatus/PlcTag006', tagValue: '0' },
                { label: '围栏安全门已关闭', key: 'SlCarPlc01/PlcStatus/PlcTag007', tagValue: '0' },
                { label: '动作步骤', key: 'SlCarPlc01/PlcStatus/Steps', tagValue: '0' },
                { label: '设备状态', key: 'SlCarPlc01/PlcStatus/Status', tagValue: '0' },
                { label: 'Z轴最高位', key: 'SlCarPlc01/PlcStatus/Z_Highest', tagValue: '0' },
                { label: '释放完成', key: 'SlCarPlc01/PlcStatus/ReleaseFinish', tagValue: '0' },
                { label: '手动天车闯入', key: 'SlCarPlc01/PlcStatus/ManualBreak', tagValue: '0' }
              ]
            },
            {
              name: '状态点位',
              span: 10,
              key: [
                { label: '重新启用自动', key: 'SlCarPlc01/DcsStatus/RcsTag001', tagValue: '0' },
                { label: '故障复位', key: 'SlCarPlc01/DcsStatus/FaultReset', tagValue: '0' },
                { label: '设备自检', key: 'SlCarPlc01/DcsStatus/DeviceSelfTest', tagValue: '0' },
                { label: '大车激光值确认', key: 'SlCarPlc01/DcsStatus/RcsTag002', tagValue: '0' },
                { label: '小车激光值确', key: 'SlCarPlc01/DcsStatus/RcsTag003', tagValue: '0' },
                { label: '吸板任务完成', key: 'SlCarPlc01/PlcStatus/PlcFinishChaQu', tagValue: '0' },
                { label: '放板任务完成', key: 'SlCarPlc01/PlcStatus/PlcFinishFangZhi', tagValue: '0' },
                { label: '避让任务完成', key: 'SlCarPlc01/PlcStatus/PlcFinishBiRang', tagValue: '0' },
                { label: '寻中任务完成', key: 'SlCarPlc01/PlcStatus/PlcFinishXz', tagValue: '0' }
              ]
            }
          ]
        },
        {
          keyValue: [
            {
              name: 'WMS下方任务',
              span: 7,
              key: [
                { label: 'RCS写入X坐标', key: 'SlCarPlc01/DcsStatus/RcsWriteX', tagValue: '0' },
                { label: 'RCS写入Y坐标', key: 'SlCarPlc01/DcsStatus/RcsWriteY', tagValue: '0' },
                { label: 'RCS写入Z坐标', key: 'SlCarPlc01/DcsStatus/RcsWriteZ', tagValue: '0' },
                { label: 'RCS写入R任务类型', key: 'SlCarPlc01/DcsStatus/RcsWriteR', tagValue: '0' },
                { label: 'RCS写入A位置代码', key: 'SlCarPlc01/DcsStatus/RcsWriteA', tagValue: '0' },
                { label: 'RCS写入任务启动', key: 'SlCarPlc01/DcsStatus/RcsStartTask', tagValue: '0' },
                { label: 'RCS写入天车急停', key: 'SlCarPlc01/DcsStatus/RcsTellStop', tagValue: '0' },
                { label: 'RCS写入任务号', key: 'SlCarPlc01/DcsStatus/RcsWriteTask', tagValue: '0' },
                { label: 'RCS写入钢板型号', key: 'SlCarPlc01/DcsStatus/RcsWriteModelType', tagValue: '0' },
                { label: 'RCS写入钢板长', key: 'SlCarPlc01/DcsStatus/RcsWriteLength', tagValue: '0' },
                { label: 'RCS写入钢板宽', key: 'SlCarPlc01/DcsStatus/RcsWriteWidth', tagValue: '0' },
                { label: 'RCS写入钢板厚', key: 'SlCarPlc01/DcsStatus/RcsWriteHeight', tagValue: '0' },
                { label: 'RCS写入钢板重', key: 'SlCarPlc01/DcsStatus/RcsWriteWeight', tagValue: '0' }
              ]
            },
            {
              name: '天车接受任务',
              span: 7,
              key: [
                { label: '执行任务.X', key: 'SlCarPlc01/PlcStatus/PlcTimeX', tagValue: '0' },
                { label: '执行任务.Y', key: 'SlCarPlc01/PlcStatus/PlcTimeY', tagValue: '0' },
                { label: '执行任务.Z', key: 'SlCarPlc01/PlcStatus/PlcTimeZ', tagValue: '0' },
                { label: '执行任务.R', key: 'SlCarPlc01/PlcStatus/PlcTimeR', tagValue: '0' },
                { label: '执行任务.A', key: 'SlCarPlc01/PlcStatus/PlcTimeA', tagValue: '0' },
                { label: '执行任务.START', key: 'SlCarPlc01/PlcStatus/PlcReadStartTask', tagValue: '0' },
                { label: '接收任务.X', key: 'SlCarPlc01/PlcStatus/AcceptTaskX', tagValue: '0' },
                { label: '接收任务.Y', key: 'SlCarPlc01/PlcStatus/AcceptTaskY', tagValue: '0' },
                { label: '接收任务.Z', key: 'SlCarPlc01/PlcStatus/AcceptTaskZ', tagValue: '0' },
                { label: '接收任务.R', key: 'SlCarPlc01/PlcStatus/AcceptTaskR', tagValue: '0' },
                { label: '接收任务.A', key: 'SlCarPlc01/PlcStatus/AcceptTaskA', tagValue: '0' },
                { label: '接收任务.START', key: 'SlCarPlc01/PlcStatus/AcceptTaskStart', tagValue: '0' },
                { label: '目标库位入库钢板数量', key: 'SlCarPlc01/DcsStatus/RcsTag004', tagValue: '0' }
              ]
            },
            {
              name: 'XCMG',
              span: 10,
              key: [
                { imgUrl: xcmgGroup }
              ]
            }
          ]
        }
      ],
      resultTagKey: {
        Weight: '',
        Vacuum: '',
        X_real_time: '',
        Y_real_time: '',
        Z_real_time: ''
      }
    }
  },
  mounted() {
    this.currentTime()
    this.timer1 = setInterval(this.workLocation, 1000)
    this.timerLoad = setInterval(() => {
      location.reload()
    }, 1000 * 60 * 10)
    this.toStartWatch()
  },
  beforeDestory() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.timerLoad) {
      clearInterval(this.timerLoad)
    }
    // 离开此页面时销毁mqtt链接
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  created() {
  },
  methods: {
    currentTime() {
      setInterval(this.formatDate, 1000)
    },
    formatDate() {
      const date = new Date()
      const hours = date.getHours()
      const minuter =
    date.getMinutes() > 9 ? date.getMinutes() : '0' + date.getMinutes()
      const seconds =
    date.getSeconds() > 9 ? date.getSeconds() : '0' + date.getSeconds()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const d = date.getDate()
      const day = date.getDay()
      const week = [
        '星期日',
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六'
      ]

      this.nowDate = year + '-' + month + '-' + d + ' '
      this.time = hours + ':' + minuter + ':' + seconds + ' '
      this.week = week[day]
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 获取连接地址
      // 'ws://***************:8090/mqtt'
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.GetTagValue(result.ip, result.webapi_port)
          //  var connectUrl ="ws://*************:8083/mqtt";
          // connectUrl=MQTT_SERVICE;
          console.log('拼接URL：' + connectUrl)
          // mqtt连接
          // this.clientMqtt = mqtt.connect(MQTT_SERVICE, this.optionsMqtt); //开启连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', (e) => {
            this.mqttConnStatus = true
            this.topicSubscribe('SCADA_CHANGE/SlCarPlc01/PlcStatus/Weight')
            this.topicSubscribe('SCADA_CHANGE/SlCarPlc01/PlcStatus/Vacuum')
            this.topicSubscribe('SCADA_CHANGE/SlCarPlc01/PlcStatus/X_real-time')
            this.topicSubscribe('SCADA_CHANGE/SlCarPlc01/PlcStatus/Y_real-time')
            this.topicSubscribe('SCADA_CHANGE/SlCarPlc01/PlcStatus/Z_real-time')
            this.TagKey.forEach(item => {
              item.keyValue.forEach(subItem => {
                subItem.key.forEach(keyItem => {
                  if (keyItem.key) {
                    this.topicSubscribe('SCADA_CHANGE/' + keyItem.key)
                  }
                })
              })
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', (error) => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })

            clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', (error) => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', (error) => {
            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          this.clientMqtt.on('close', () => {
            this.clientMqtt.end()

            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())

            const res = JSON.parse(message.toString())

            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    GetTagValue(ip, port) {
      var readTagArray = []
      this.TagKey.forEach(item => {
        item.keyValue.forEach(subItem => {
          subItem.key.forEach(keyItem => {
            if (keyItem.key) {
              readTagArray.push({ tag_key: keyItem.key })
            }
          })
        })
      })
      const result = [
        { tag_key: 'SlCarPlc01/PlcStatus/Weight' },
        { tag_key: 'SlCarPlc01/PlcStatus/Vacuum' },
        { tag_key: 'SlCarPlc01/PlcStatus/X_real-time' },
        { tag_key: 'SlCarPlc01/PlcStatus/Y_real-time' },
        { tag_key: 'SlCarPlc01/PlcStatus/Z_real-time' }
      ]
      readTagArray = readTagArray.concat(result)
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + ip + ':' + port + method
        // path = 'http://*************:8089' + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()

                  if (tagKey === 'SlCarPlc01/PlcStatus/Weight') {
                    this.resultTagKey.Weight = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/Vacuum') {
                    this.resultTagKey.Vacuum = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/X_real-time') {
                    this.resultTagKey.X_real_time = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/Y_real-time') {
                    this.resultTagKey.Y_real_time = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/Z_real-time') {
                    this.resultTagKey.Z_real_time = tagValue
                  }
                  for (let i = 0; i < this.TagKey.length; i++) {
                    const item = this.TagKey[i]
                    for (let j = 0; j < item.keyValue.length; j++) {
                      const subItem = item.keyValue[j]
                      for (let k = 0; k < subItem.key.length; k++) {
                        const keyItem = subItem.key[k]
                        if (keyItem.key === tagKey) {
                          keyItem.tagValue = tagValue
                          break // 终止循环
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },

    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_STATUS/') >= 0 || channel.indexOf('SCADA_BEAT/') >= 0 || channel.indexOf('SCADA_MSG/') >= 0) {

      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue
      for (let i = 0; i < this.TagKey.length; i++) {
        const item = this.TagKey[i]
        for (let j = 0; j < item.keyValue.length; j++) {
          const subItem = item.keyValue[j]
          for (let k = 0; k < subItem.key.length; k++) {
            const keyItem = subItem.key[k]
            if (keyItem.key === TagKey) {
              keyItem.tagValue = TagNewValue
              break // 终止循环
            }
          }
        }
      }
      if (TagKey === 'SlCarPlc01/PlcStatus/Weight') {
        this.resultTagKey.Weight = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/Vacuum') {
        this.resultTagKey.Vacuum = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/X_real-time') {
        this.resultTagKey.X_real_time = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/Y_real-time') {
        this.resultTagKey.Y_real_time = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/Z_real-time') {
        this.resultTagKey.Z_real_time = TagNewValue
      }
    }
  }
}
</script>

<style scoped lang="less">
.el-header {
background-color: #161522;
text-align: center;
}

.el-header .title {
font-size: 25px;
letter-spacing: 5px;
background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
-webkit-background-clip: text;
color: transparent;
font-weight: 600;
}
.el-header .time {
font-size: 20px;
background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
-webkit-background-clip: text;
color: transparent;
font-weight: 600;
}
.el-main {
background-color: #161522;
padding-top: 5px;
overflow: hidden;
}
::v-deep .el-main .cardTextBox{

  color:#fff;
  border:1px solid #808183;

  background: linear-gradient(to left, #b7b8bc, #b7b8bc)  left top no-repeat, linear-gradient(to bottom, #b7b8bc, #b7b8bc) left top no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) right top no-repeat, linear-gradient(to bottom, #b7b8bc, #b7b8bc) right top no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) left bottom no-repeat, linear-gradient(to bottom, #b7b8bc, #b7b8bc) left bottom no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) right bottom no-repeat, linear-gradient(to left, #b7b8bc, #b7b8bc) right bottom no-repeat;
  background-size: 2px 12px, 12px 2px, 2px 12px, 12px 2px;
  background-color:#232526;
  .el-card__header{
    padding:10px !important;
    font-size:20px;
    font-weight:600;
    border-bottom:1px solid #808183;
  }
  .dataInfo {
    font-size: 20px;
    background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
    -webkit-background-clip: text;
    margin-left: 5px;
  }
  .PlcStatusInfo{
    display: flex;
    overflow-x: hidden;
    flex-wrap: wrap;
    .PlcStatusValue{
      width:20%;
      display:flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .PlcStatuspoint{
      width:50%;
      display:flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .RCSStatusValue{
      width:100%;
      display: flex;
      .dataInfoName{
        width: 50%;
        display: block;
        text-align: right;
      }

    }
  }
  .WMSStatusInfo{
    display: flex;
    margin-bottom: 10px;
      .dataInfoName{
        width: 220px;
        display: block;
        text-align: right;
      }
    }

}
</style>
