<template>
  <div class="box-card">
    <el-card shadow="always" style="border: 0px solid #d2dbe4">
      <el-form ref="query" :inline="true" size="mini" style="margin: 0; padding: 0" label-width="70px">
        <el-row>
          <el-form-item label="产线" style="margin: 0px 0px 5px 0px">
            <el-select v-model="query.prod_line_code" clearable style="width: 200px" value-key="prod_line_id"
              @change="handleProdLineChange">
              <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_des" :value="item">
                <span style="float: left">{{ item.prod_line_des }}</span>
                <span style="
                    float: right;
                    margin-left: 20px;
                    color: #8492a6;
                    font-size: 13px;
                  ">{{ item.prod_line_code }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机型" style="margin: 0px 0px 5px 0px">
            <el-select v-model="query.small_model_type" filterable clearable style="width: 120px">
              <el-option v-for="item in smallModelTypeData" :key="item.small_type_id" :label="item.small_model_type"
                :value="item.small_model_type">
                <!-- <span style="float: left">{{ item.small_model_type }}</span>
                <span
                  style="
                    float: right;
                    margin-left: 20px;
                    color: #8492a6;
                    font-size: 13px;
                  "
                >{{ item.model_code }}</span> -->
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工位号" style="margin: 0px 0px 5px 0px">
            <el-select v-model="query.station_code" filterable clearable style="width: 200px" value-key="station_code"
              @change="getQulityforData">
              <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_code" :value="item">
                <span style="float: left">{{ item.station_code }}</span>
                <span style="
                    float: right;
                    margin-left: 20px;
                    color: #8492a6;
                    font-size: 13px;
                  ">{{ item.station_des }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="控制项目：">
            <el-select v-model="query.tag_des" filterable clearable style="width: 200px">
              <el-option v-for="item in tagList" :key="item.tag_des" :label="item.tag_des" :value="item.tag_des" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="控制项目" style="margin: 0px 0px 5px 0px">
            <el-select
              v-model="query.quality_for"
              clearable
              style="width: 200px"
              value-key="tag_des"
            >
              <el-option
                v-for="item in qulityforData"
                :key="item.tag_des"
                :label="item.tag_des"
                :value="item"
              >
                <span style="float: left">{{ item.tag_des }}</span>
                <span
                  style="
                    float: right;
                    margin-left: 20px;
                    color: #8492a6;
                    font-size: 13px;
                  "
                >{{ item.tag_des }}</span>
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="查询时间" style="margin: 0px 0px 5px 0px">
            <el-date-picker v-model="query.leave_date" type="datetimerange" :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss" range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期"
              style="width: 350px" align="right" />
          </el-form-item>
        </el-row>
        <el-row class="el-row">
          <el-col :span="4">
            <el-card class="box-card1" shadow="always" style="margin: 0px 5px 0px 0px">
              <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
                <el-checkbox ref="option1" v-model="option1" @change="handleCheckedChange1">取样间隔时间设定</el-checkbox>
              </div>
              <el-form-item label="间隔分钟" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
              <el-form-item label="共取次数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card class="box-card1" shadow="always" style="margin: 0px 5px 0px 0px">
              <div slot="header" class="clearfix" style="font-size: 8px; color: #757575; font-weight: 400">
                <el-checkbox ref="option2" v-model="option2" @change="handleCheckedChange2">取样间隔数量设定</el-checkbox>
              </div>
              <el-form-item label="间隔条数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
              <el-form-item label="共取次数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card class="box-card1" shadow="always" style="margin: 0px 5px 0px 0px">
              <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
                <el-checkbox ref="option3" v-model="option3" @change="handleCheckedChange3">取样前N条设定</el-checkbox>
              </div>
              <el-form-item label="开始条数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
              <el-form-item label="前N条数据" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card class="box-card1" shadow="always" style="margin: 0px 5px 0px 0px">
              <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
                <el-checkbox ref="option4" v-model="option4" @change="handleCheckedChange4">取样后N条设定</el-checkbox>
              </div>
              <el-form-item label="开始条数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
              <el-form-item label="后N条数据" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card class="box-card1" shadow="always">
              <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
                <el-checkbox ref="option5" v-model="option5" @change="handleCheckedChange5">取样开始位置设定</el-checkbox>
              </div>
              <el-form-item label="开始条数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
              <el-form-item label="间隔数量" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
              <el-form-item label="共取次数" label-width="100px" style="margin: 5px 5px 5px 5px">
                <el-input-number v-model="query.attr3" clearable size="mini" :min="1" :max="99999" style="width: 100px" />
              </el-form-item>
            </el-card>
          </el-col>
        </el-row>

        <el-button style="margin: 5px 5px 5px 5px" class="filter-item" size="mini" type="primary" icon="el-icon-search"
          @click="handleQuery()">查询</el-button>
        <!-- <el-button class="filter-item" size="mini" type="primary" icon="el-icon-download" style="margin: 0px 0px 5px 0px"
          @click="export2Excel">导出</el-button> -->
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <el-tabs v-model="tabValue" style="height: 400px">
        <el-tab-pane name="tableTab"><span slot="label"><i class="el-icon-s-grid" /> 表格形式</span>
          <el-card class="tables">
            <el-table ref="table" v-loading="tableLoading" :data="tableData" style="width: 100%"
              :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" height="350px"
              :highlight-current-row="true">
              <el-table-column :show-overflow-tooltip="true" prop="prod_line_code" width="120" label="产线编码"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="prod_line_des" width="200" label="产线描述"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="station_code" width="120" label="工位编号"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="station_des" width="200" label="工位描述"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="small_model_type" width="200" label="机型"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="tag_des" width="130" label="控制项目" sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="tag_value" width="130" label="采集值" sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="upper_limit" width="130" label="上限值"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="down_limit" width="130" label="下限值"
                sortable="custom" />
              <el-table-column :show-overflow-tooltip="true" prop="trace_d_time" width="130" label="采集时间"
                sortable="custom" />
            </el-table>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="chartTab"><span slot="label"><i class="el-icon-s-data" /> 趋势图</span>
          <ECharts id="myChart" ref="cpk_chart2" :options="orgOptions2" style="width: 1200px; height: 400px" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <el-form ref="result" :inline="true" size="mini" style="margin: 0; padding: 0" label-width="70px">
        <el-form-item label="规则中心(C)" label-width="100px" style="margin: 0">
          <el-input v-model="result.C" clearable size="mini" style="width: 120px" />
        </el-form-item>
        <el-form-item label="平均值(X)" label-width="100px" style="margin: 0">
          <el-input v-model="result.X" clearable size="mini" style="width: 120px" />
        </el-form-item>
        <el-form-item label="规格公差(T)" label-width="100px" style="margin: 0">
          <el-input v-model="result.T" clearable size="mini" style="width: 120px" />
        </el-form-item>
        <el-form-item label="标准差(δ)" label-width="100px" style="margin: 0">
          <el-input v-model="result.δ" clearable size="mini" style="width: 120px" />
        </el-form-item>
        <el-form-item label="制程准确度(Ca)" label-width="120px" style="margin: 0">
          <el-input v-model="result.Ca" clearable size="mini" style="width: 120px" />
        </el-form-item>
        <el-form-item label="制程精密度(Cp)" label-width="100px" style="margin: 0">
          <el-input v-model="result.Cp" clearable size="mini" style="width: 120px" />
        </el-form-item>
        <el-form-item label="制程能力指数(Cpk)" label-width="120px" style="margin: 0">
          <el-input v-model="result.Cpk" clearable size="mini" style="width: 120px" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { mesQualitySpcAnalyze, mesQualitySpcTagList } from '@/api/mes/core/spcReport'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import ECharts from 'vue-echarts'
import ExcelJS from 'exceljs';    // 引入exceljs, 用于生成excel文件
import { saveAs } from 'file-saver' // 引入file-saver, 用于保存文件
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'

export default {
  name: 'MES_SPC_REPORT',
  components: {
    ECharts
  },
  data() {
    return {
      tabValue: 'tableTab',
      orgOptions1: {},
      orgOptions2: {},
      // 样本个数
      samples: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      // 样本取值
      values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      // 上限值
      upper_limit: 0,
      // 下限值
      down_limit: 0,
      tag_uom: '',
      prodLineData: [],
      smallModelTypeData: [],
      stationData: [],
      tableData: [],
      tableDataCatch: [],
      tagList: [],
      tableLoading: false,
      option1: true,
      option2: false,
      option3: false,
      option4: false,
      option5: false,
      customPopover1: false,
      query: {
        station_code: '',
        small_model_type: '',
        quality_for: '',
        selectedoption: 'option1',
        attr1: 1,
        attr2: 1,
        attr3: 1,
        leave_date: null
      },
      result: {
        C: 0.0,
        X: 0.0,
        T: 0.0,
        δ: 0.0,
        Ca: 0.0,
        Cp: 0.0,
        Cpk: 0.0,
        USL: 0.0,
        LSL: 0.0
      },
      // 时间选择器
      pickerOptions: {}
    }
  },
  mounted() {
    this.orgOptions2 = {
      color: ['#559AD3', '#99CC66', '#B4B4B4'],
      xAxis: {
        name: '样本数量（个）',
        type: 'category',
        data: this.samples
      },
      yAxis: {
        name: '样本采集值(' + this.tag_uom + ')',
        type: 'value'
      },
      series: [
        {
          data: this.values,
          type: 'line',
          smooth: true,
          markLine: {
            symbol: 'none', // 去掉警戒线最后面的箭头
            label: {
              position: 'end' // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
            },
            data: [
              {
                name: '平均线',
                lineStyle: {
                  // 警戒线的样式  ，虚实  颜色
                  type: 'solid',
                  color: '#FFCC66'
                },
                yAxis: this.result.X
              },
              {
                name: '最大值',
                lineStyle: {
                  // 警戒线的样式  ，虚实  颜色
                  type: 'solid',
                  color: '#FA3934'
                },
                yAxis: this.result.USL
              },
              {
                name: '最小值',
                lineStyle: {
                  // 警戒线的样式  ，虚实  颜色
                  type: 'solid',
                  color: '#FA3934'
                },
                yAxis: this.result.LSL
              }
            ]
          }
        }
      ]
    }
  },
  created: function () {
    // 初始化日期选择器快捷选项
    this.pickerOptions = createDatePickerShortcuts(this.$i18n)

    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    handleCheckedChange1(option) {
      if (this.option1 === true) {
        this.option2 = false
        this.option3 = false
        this.option4 = false
        this.option5 = false
        this.query.selectedoption = 'option1'
      }
    },
    handleCheckedChange2() {
      if (this.option2 === true) {
        this.option1 = false
        this.option3 = false
        this.option4 = false
        this.option5 = false
        this.query.selectedoption = 'option2'
      }
    },
    handleCheckedChange3() {
      if (this.option3 === true) {
        this.option2 = false
        this.option1 = false
        this.option4 = false
        this.option5 = false
        this.query.selectedoption = 'option3'
      }
    },
    handleCheckedChange4() {
      if (this.option4 === true) {
        this.option2 = false
        this.option3 = false
        this.option1 = false
        this.option5 = false
        this.query.selectedoption = 'option4'
      }
    },
    handleCheckedChange5() {
      if (this.option5 === true) {
        this.option2 = false
        this.option3 = false
        this.option4 = false
        this.option1 = false
        this.query.selectedoption = 'option5'
      }
    },
    handleProdLineChange(data) {
      const query = {
        userID: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: data.prod_line_id
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })

      selSmallModel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: data.prod_line_id
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.smallModelTypeData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    handleQuery() {
      if (this.query.station_code === '') {
        this.$message({
          message: '请选择工位号',
          type: 'info'
        })
        return
      }
      if (this.query.small_model_type === '') {
        this.$message({
          message: '请选择机型',
          type: 'info'
        })
        return
      }
      if (this.query.tag_des === '') {
        this.$message({
          message: '请选择控制项目',
          type: 'info'
        })
        return
      }
      if (this.query.station_code === '') {
        this.$message({
          message: '请选择工位号',
          type: 'info'
        })
        return
      }
      if (this.query.selectedoption === '') {
        this.$message({
          message: '请选择取样方式',
          type: 'info'
        })
        return
      }
      this.tableData = []
      this.shifts = []
      this.reality_Beat = []
      this.standard_Beat = []
      this.samples = []
      this.values = []
      const query = {
        prod_line_code: this.query.prod_line_code.prod_line_code,
        small_model_type: this.query.small_model_type,
        station_code: this.query.station_code.station_code,
        tag_des: this.query.tag_des,
        item_date: this.query.leave_date
      }

      this.tableLoading = true
      mesQualitySpcAnalyze(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code == 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataCatch = defaultQuery.data
              if (this.query.selectedoption === 'option1') {
                var i = 0 // 当前序号
                var j = 0 // 取值次数
                var lastTime = new Date()
                this.tableDataCatch.forEach((item) => {
                  if (j < this.query.attr2) {
                    var time = item.trace_d_time
                    var trace_d_time = new Date(time)
                    if (i === 0) {
                      lastTime = trace_d_time
                    }
                    var newdate = new Date(
                      lastTime + this.query.attr1 * i * 60 * 1000
                    )
                    if (trace_d_time.getTime() === newdate.getTime()) {
                      this.tableData.push(item)
                      j += 1
                    }
                  }
                  i += 1
                })
              } else if (this.query.selectedoption === 'option2') {
                var i = 0 // 当前序号
                var j = 0 // 取值次数
                this.tableDataCatch.forEach((item) => {
                  if (j < this.query.attr2) {
                    if (i % (this.query.attr1 + 1) === 0) {
                      this.tableData.push(item)
                      j += 1
                    }
                  }
                  i += 1
                })
              } else if (this.query.selectedoption === 'option3') {
                var i = 0 // 当前序号
                var j = 0 // 取值次数
                var k = this.query.attr1 - this.query.attr2
                if (k > 0) {
                  this.tableDataCatch.forEach((item) => {
                    if (j < this.query.attr2) {
                      if (i >= k && i < this.query.attr1) {
                        this.tableData.push(item)
                        j += 1
                      }
                    }
                    i += 1
                  })
                } else {
                  this.$message({
                    message: '参数设置异常',
                    type: 'error'
                  })
                }
              } else if (this.query.selectedoption === 'option4') {
                var i = 0 // 当前序号
                var j = 0 // 取值次数
                this.tableDataCatch.forEach((item) => {
                  if (j < this.query.attr2) {
                    if (i > this.query.attr1) {
                      this.tableData.push(item)
                      j += 1
                    }
                  }
                  i += 1
                })
              } else if (this.query.selectedoption === 'option5') {
                var i = 0 // 当前序号
                var j = 0 // 取值次数
                this.tableDataCatch.forEach((item) => {
                  if (j < this.query.attr3) {
                    if (
                      i >= this.query.attr1 &&
                      i % (this.query.attr2 + 1) === 0
                    ) {
                      this.tableData.push(item)
                      j += 1
                    }
                  }
                  i += 1
                })
              }

              // this.tableData = defaultQuery.data;
              var SUM = 0.0 // 采集值的和
              var S = 0.0 // 方差和
              var i = 0
              this.tableData.forEach((item) => {
                this.tag_uom = item.tag_uom
                this.result.USL = parseFloat(item.upper_limit)
                this.result.LSL = parseFloat(item.down_limit)
                SUM += parseFloat(item.tag_value)
                i += 1
                this.samples.push(i)
                this.values.push(item.tag_value)
              })
              this.result.C = ((this.result.USL + this.result.LSL) / 2).toFixed(
                2
              )
              this.result.X = (SUM / defaultQuery.data.length).toFixed(2)
              this.result.T = (this.result.USL - this.result.LSL).toFixed(2)
              this.tableData.forEach((item) => {
                S += Math.pow(parseFloat(item.tag_value) - this.result.X, 2)
              })
              this.result.δ = Math.sqrt(
                S / (defaultQuery.data.length - 1)
              ).toFixed(2)
              this.result.Ca = (
                ((this.result.X - this.result.C) * 2) /
                this.result.T
              ).toFixed(2)
              this.result.Cp = (
                (this.result.USL - this.result.LSL) /
                (this.result.δ * 6)
              ).toFixed(2)
              this.result.Cpk = (
                this.result.Cp * Math.abs(1 - this.result.Ca)
              ).toFixed(2)
              this.orgOptions2.xAxis.data = this.samples
              this.orgOptions2.series[0].data = this.values
              this.orgOptions2.series[0].markLine.data[0].yAxis = this.result.X
              this.orgOptions2.series[0].markLine.data[1].yAxis =
                this.result.USL
              this.orgOptions2.series[0].markLine.data[2].yAxis =
                this.result.LSL
              this.orgOptions2.yAxis.name = '样本采集值(' + this.tag_uom + ')'
            } else {
              this.tableData = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })

      this.tableLoading = false
    },
    // export2Excel() {
    //   this.tabValue = 'chartTab'
    //   const that = this
    //   require.ensure([], () => {
    //     const { export_json_to_excel } = require('@/components/excel/Export2Excel')
    //     const tHeader = [
    //       '产线编码',
    //       '产线描述',
    //       '工位编号',
    //       '工位描述',
    //       '机型',
    //       '控制项目',
    //       '采集值',
    //       '上限值',
    //       '下限值',
    //       '采集时间'
    //     ]
    //     const filterVal = [
    //       'prod_line_code',
    //       'prod_line_des',
    //       'station_code',
    //       'station_des',
    //       'small_model_type',
    //       'quality_for',
    //       'tag_value',
    //       'upper_limit',
    //       'down_limit',
    //       'trace_d_time'
    //     ]
    //     const list = this.tableData
    //     const data = this.formatJson(filterVal, list)
    //     export_json_to_excel(
    //       tHeader,
    //       data,
    //       'CPK分析表'
    //     )
    //   })
    // },
    base64ToBlob(base64Data, contentType) {
      contentType = contentType || '';
      const sliceSize = 1024;
      const byteCharacters = atob(base64Data);
      const byteArrays = [];

      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }

      return new Blob(byteArrays, { type: contentType });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]))
    },
    getQulityforData(data) {
      console.info(data)
      this.tagList = []
      mesQualitySpcTagList({
        enable_flag: 'Y',
        station_id: data.station_id
      })
        .then(res => {
          console.log(res)
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tagList = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询采集项目异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.box-card {
  // min-height: calc(100vh);
  padding: 10px;
}

.box-card1 {
  min-height: 30px;
  padding: 10px;
}

.el-row {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}
</style>
