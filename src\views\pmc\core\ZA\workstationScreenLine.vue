<template>
    <div class="workstationScreen">
        <el-card class="cardStyle">
            <div class="headerStyle">
                <div class="logoStyle"><img src="@/assets/images/qyc1.png" alt=""><span>{{ this.$route.query.station_code }}</span></div>
                <div class="orderInfo" v-if="orderData.length > 0">
                    <p>{{orderData[0].orderProd}} 加注异常AGV停线</p>
                </div>
                <div class="wrapmarquee">
                    <marquee loop="infinite">
                        <div class="wraptext">
                        <p v-for="(item,index) in emergency" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                        </div>
                    </marquee>
                </div>
                <div class="headerL">
                    <p class="pImg"><img src="@/assets/images/log.png" alt=""></p>
                    <p class="pTime">{{ nowDateTime }} {{ nowDateWeek }}</p>
                </div>
            </div>
        </el-card>
        <el-card class="cardStyle" >
            <el-row :gutter="24" class="elRowStyle elRowStyleFlex elRowStyleNmargin linePie">
                <el-col :span="7" >
                    <el-card class="cardStyle" >
                        <div id="comPreLine" style="width: 100%;height: 340px" />
                    </el-card>
                </el-col>
                <el-col :span="6"  >
                    <el-card class="cardStyle" style="margin: 0 10px !important;">
                        <div id="circumAB" style="width: 100%;height: 340px" />
                    </el-card>
                </el-col>
                <el-col :span="11" >
                    <el-card class="cardStyle" >
                        <div id="passRate" style="width: 100%;height: 340px" />
                    </el-card>
                </el-col>
            </el-row>
        </el-card>
        <el-card class="cardStyle"  style="margin: 10px;padding: 10px 0;">
            <el-row :gutter="24" class="elRowStyle elRowStyleFlex elRowStyleNmargin">
                <el-col :span="24" v-if="qualityData.length > 0">
                    <el-col :span="8" v-for="item in qualityData" :key="item.id" class="detail">
                        <div v-if="item.source && item.faultDesc">(<span>{{ item.source == 'MES' ? 'BIR' : item.source == 'PMS' ? 'FIR' : 'AIR'}}-{{ item.faultLevel }}</span>){{item.faultDesc}}</div>
                        <div v-else>暂无缺陷数据</div>
                        <img :src=" item.picture ? ('http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/aqmisDefectInfo/display?path=' + item.picture) : require('@/assets/images/qyc1.png')" alt="">
                    </el-col>
                    </el-col>
                    <el-col :span="24" v-else>
                    <el-col :span="8" class="detail">
                        <div>暂无缺陷数据</div>
                        <img src="@/assets/images/qyc1.png" alt="">
                    </el-col>
                </el-col>
            </el-row>
        </el-card>
        <el-card class="cardStyle"  style="margin: 10px;">
            <div class="headerStyle">
                <div class="wrapmarquee">
                    <marquee loop="infinite">
                        <div class="wraptext">
                            <!-- <p v-for="(item,index) in qualityCulture" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p> -->
                            <p style="color: red;">{{ description }}</p>
                        </div>
                    </marquee>
                </div>
            </div>
        </el-card>
    </div>
</template>
<script>
import { getStationTime,getAqmisDefectInfo ,getOilStop,getStationEmergency,getProblemTrendsChart,getABClassifiy,getFirstPassYield,getBigScreenFooter} from '@/api/pmc/sysworkstationScreen'
export default {
    name:'workstationScreenLine',
    data(){
        return {
            nowDateTime: '', // 当前日期
            nowDateWeek: '', // 周末
            timer:null,
            timer2:null,
            timer3:null,
            comPreLine:null,
            circumAB:null,
            passRate:null,
            orderData:[],
            emergency: [],
            work_center_code: 'ZA',
            qualityData:[],
            qualityPageNo:1,
            description:'',
        }
    },
    created(){
        // 加载工位屏时间
        this.initstationTime()
        this.timer = setInterval(() => {
            this.initstationTime()
        }, 1000)
    },
    mounted(){
        // 加载工位屏紧急事件
        this.initStationEmergency()
        this.getOrderInfo()
        this.getComPreLine() //获取问题项趋势图
        this.getCircumData()//获取周度问题
        this.getPassRate()//获取合格率
        this.qualityInfo() //质量缺陷
        this.getFooterData() //获取底部大屏文字
        this.timer = setInterval(()=>{
            this.initStationEmergency()
            this.qualityInfo()
        },15000)
        this.timer2 = setInterval(()=>{
            this.getOrderInfo()
            this.getFooterData()
        },1000 * 60)
        this.timer3 = setInterval(()=>{
            this.getComPreLine()
            this.getCircumData()
            this.getPassRate()
        },1000 * 60 * 60)
        var that = this
        window.addEventListener('resize', function() {
            that.comPreLine.resize()
            that.circumAB.resize()
            that.passRate.resize()
      })
    },
    beforeCreate(){
        clearInterval(this.timer)
        clearInterval(this.timer2)
        clearInterval(this.timer3)
    },
    methods:{
        // 加载工位屏紧急事件
        initStationEmergency() {
        var query = {
            station_code: this.$route.query.station_code,
            work_center_code: this.work_center_code
        }
            getStationEmergency(query)
                .then(res => {
                if (res.code !== 0 && res.count < 0) {
                    console.log('请求数据异常')
                    return
                }
                // 紧急事件
                this.emergency = res.data[0].andon_list
                if (this.emergency === null || this.emergency.length === 0) {
                    this.emergency = []
                }
                // 紧急事件
                })
                .catch(() => {
                    console.log('请求数据为空')
            })
        },
        getOrderInfo(){
            const query = `queryMode=page&pageNo=1&limit=1&status=0&station=${this.$route.query.station_code}`
            getOilStop(query).then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                this.orderData = res.data
                if (this.orderData === null || this.orderData.length === 0) {
                    this.orderData = []
                }
            })
            .catch(() => {
                console.log('请求数据为空')
            })
        },
        initstationTime() {
            var query = {
                station_code: this.$route.query.station_code
            }
            getStationTime(query).then(res => {
                if (res.code !== 0 && res.count < 0) {
                    console.log('请求数据异常')
                    return
                }
                    this.nowDateTime = res.data[0].sysdate
                    this.nowDateWeek = res.data[0].sysweek
                })
                .catch(() => {
                    console.log('请求数据为空')
            })
        },
        getComPreLine(){
            const query = {
                station:this.$route.query.station_code,
            }
            getProblemTrendsChart(query).then(res=>{
                if(res.code == '200' && res.data.data.length > 0){
                    this.comPreLine = this.$echarts.init(document.getElementById('comPreLine'))
                    let maxCount = Math.max.apply(null,res.data.data.map(function(obj){ //找出最大值
                        return obj.count 
                    }))
                        const  option = {
                            title: {
                                text: res.data.teamName + '百台问题项趋势图',
                                left: 'center',
                                top:'3%',
                                // 文字颜色
                                textStyle: {
                                    color: '#000',
                                    fontSize:16
                                }
                            },
                            legend: {
                                itemWidth: 10,
                                itemHeight: 10,
                                itemGap: 20,
                                textStyle: {
                                    color: '#000', 
                                    fontFamily: 'Alibaba PuHuiTi', 
                                    fontSize: 16, 
                                    fontWeight: 400, 
                                },
                                selectedMode: false
                            },
                            grid: {
                                left: "3%",
                                right: "3%",
                                bottom: "3%",
                                containLabel: true,
                            },
                            xAxis: [
                                {
                                type: 'category',
                                axisLabel: {
                                    textStyle:{
                                        fontSize:16
                                    },
                                },
                                axisLine: {
                                    show: true,
                                },
                                axisTick: {
                                    show: false
                                },
                                splitLine: {
                                    show: false,
                                },
                                data: res.data.data.map(e=>{ return e.month}),
                                }
                            ],
                            yAxis: {
                                type: 'value',
                                min:0,
                                max:maxCount > 5 ? maxCount : 5,
                                minInterval:1,
                                axisLine:{
                                    show:false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLabel:{
                                    show:true,
                                    textStyle:{
                                        fontSize:16,
                                    }
                                }
                            },
                            series: [
                                {   
                                    data: res.data.data.map(e=>{return e.count}),
                                    type: "line",
                                    symbol: "true",
                                    symbolSize: 2, //标记的大小
                                    itemStyle: {
                                        normal: {
                                            color: '#2E65BF',//拐点颜色
                                            label: {
                                                show: true, //开启显示
                                                color: '#000',
                                                position: 'top', //在上方显示
                                                fontSize:16,
                                            },
                                        },
                                    },
                                }
                        
                            ]
                        };
                        this.comPreLine.setOption(option)
                }else{
                    this.comPreLine = null
                    console.log('请求数据异常')
                }
            }).catch((ex)=>{
                this.comPreLine = null
                console.log('请求数据为空')
            })
           
        },
        getCircumData(){
            const query = {
                station:this.$route.query.station_code,
            }
            getABClassifiy(query).then(res=>{
                if(res.code === '200' && res.data.length > 0){
                    res.data = res.data.reverse()
                    this.circumAB = this.$echarts.init(document.getElementById('circumAB'))
                    let maxCount = Math.max.apply(null,res.data.map(function(obj){ //找出最大值
                        return obj.count
                    }))
                        let option = {
                            title: {
                                text: '周度AB类问题柱状图',
                                left: 'center',
                                top:'3%',
                                // 文字颜色
                                textStyle: {
                                    color: '#000',
                                    fontSize:16
                                }
                            },
                            legend: {
                                top: 0,
                                right: '4%',
                                itemWidth: 40,
                                itemHeight: 25, 
                                borderRadius: 4,
                                textStyle: {
                                    color: '#000', 
                                    fontFamily: 'Alibaba PuHuiTi', 
                                    fontSize: 16, 
                                },
                            },
                            tooltip: {
                                //   trigger: "item", //默认效果
                                //柱状图加阴影
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow',
                                    label: {
                                        show: true,
                                    },
                                },
                            },
                            grid: {
                                left: "3%",
                                right: "3%",
                                bottom: "3%",
                                containLabel: true,
                            },
                            color: ['#ED7D31'],
                            xAxis: {
                                type: 'category',
                                data: res.data.map(e=>{return e.week}),
                                axisLine:{
                                    show:false,
                                    
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel:{
                                    textStyle:{
                                        fontSize:16,
                                    }
                                }
                            },
                            yAxis: {
                                type: 'value',
                                minInterval:1,
                                min:0,
                                max:maxCount > 5 ? maxCount : 5,
                                axisLine:{
                                        show:false
                                        },
                                axisTick: {
                                    show: false
                                },
                                axisLabel:{
                                    textStyle:{
                                        fontSize:16,
                                    }
                                }
                            },
                            series: [
                                {
                                    name: '',
                                    data: res.data.map(e=>{return e.count}),
                                    barWidth: 30,
                                    type: 'bar',
                                    itemStyle: {
                                    normal: {
                                        label: {
                                            show: true, //开启显示
                                            position: 'top', //顶部显示
                                            textStyle: {
                                                //数值样式
                                                    color: 'black',
                                                    fontSize: 16,
                                                },
                                            },
                                        },
                                    },
                                },
                            ],
                        };
                    this.circumAB.setOption(option)
                }else{
                    this.circumAB  = null
                    console.log('请求数据异常')
                }
            }).catch((ex)=>{
                this.circumAB  = null
                console.log('请求数据为空')
            })
            
        },
        getPassRate(){
            const query = {
                station:this.$route.query.station_code,
            }
            getFirstPassYield(query).then(res=>{
                if(res.code === '200' && res.data.length > 0){
                    res.data = res.data.reverse()
                    this.passRate = this.$echarts.init(document.getElementById('passRate'))
                    let maxCount = Math.max.apply(null,res.data.map(function(obj){ //找出最大值
                        return Math.max(obj.count,obj.allCount)
                    }))
                    res.data.map(e=>{
                        e.impact = e.impact.split('%')[0]
                    })
                        let series = [
                            {
                                name:'该工位问题项数',
                                type: 'bar',
                                barWidth: 20,
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: true, //开启显示
                                            position: 'top', //顶部显示
                                            textStyle: {
                                                //数值样式
                                                color: 'black',
                                                fontSize: 16,
                                            },
                                        },
                                        color: '#4472C4',
                                    },
                                },
                                data:res.data.map(e=>{return e.count})
                            },
                            {
                                name:'质量门问题总项数',
                                type: 'bar',
                                barWidth: 20,
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: true, //开启显示
                                            position: 'top', //顶部显示
                                            textStyle: {
                                                //数值样式
                                                color: 'black',
                                                fontSize: 16,
                                            },
                                        },
                                        color: '#ED7D31',
                                    },
                                },
                                data:res.data.map(e=>{return e.allCount})
                            },
                            {
                                name: '影响值',
                                type: 'line',
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                symbol: 'circle', //标记的图形为实心圆
                                symbolSize: 2, //标记的大小
                                itemStyle: {
                                        normal: {
                                            color: '#9E9E9D',//拐点颜色
                                            label: {
                                                show: true, //开启显示
                                                color: '#000',
                                                position: 'left', //在上显示
                                                fontSize:16,
                                                formatter: function (res) {
                                                    if (res.value) {
                                                        return res.value + '%'
                                                    } else {
                                                        return 0
                                                    }
                                                },
                                            },
                                        },
                                    },
                                data: res.data.map(e=>{return e.impact}),
                            },
                        ]
                        const  option = {
                            grid: {
                                left: "3%",
                                right: "3%",
                                bottom: "3%",
                                containLabel: true,
                            },
                            title: {
                                text: '一次交检合格率影响值',
                                left: 'center',
                                left:'3%',
                                top:'3%',
                                // 文字颜色
                                textStyle: {
                                    color: '#000',
                                    fontSize:16
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow',
                                    label: {
                                        show: true,
                                    },
                                },
                            },
                            legend: {
                                right:'1%',
                                top:'2%',
                                itemGap:10,
                                textStyle:{
                                    fontSize:12,
                                }
                            },
                            xAxis: {
                                type: 'category',
                                data: res.data.map(e=>{return e.week}),
                                axisLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: '#393939', //X轴文字颜色
                                        fontSize:16,
                                    },
                                },
                            },
                            yAxis: [
                                {
                                    type: 'value',
                                    min:0,
                                    max:maxCount > 5 ? maxCount : 5,
                                    minInterval:1,
                                    name: '',
                                    nameTextStyle: {
                                        color: '#393939',
                                    },
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#eeeeee',
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    axisLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,
                                        textStyle: {
                                            color: '#393939',
                                        },
                                    },
                                },
                                {
                                    type: 'value',
                                    minInterval:1,
                                    name: '(%)',
                                    min:0,
                                    max:100,
                                    nameTextStyle: {
                                        color: '#393939',
                                        padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                                    },
                                    position: 'right',
                                    splitLine: {
                                        show: false,
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    axisLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,
                                        textStyle: {
                                            color: '#393939',
                                        },
                                    },
                                },
                                {
                                    type: 'value',
                                    gridIndex: 0,
                                    min: 50,
                                    max: 100,
                                    splitNumber: 8,
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLine: {
                                        show: false,
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: false,
                                    },
                                    splitArea: {
                                        show: false,
                                        areaStyle: {
                                            color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)'],
                                        },
                                    },
                                },
                            ],
                            series,
                        };
                        this.passRate.setOption(option)
                }else{
                    this.passRate  = null
                    console.log('请求数据异常')
                }
            }).catch((ex)=>{
                this.passRate  = null
                console.log('请求数据为空')
            })
            
        },
        qualityInfo(){
            const query = `queryMode=page&pageNo=${this.qualityPageNo}&limit=3&stationCode=${this.$route.query.station_code}&notEqualStatus=${this.$route.query.notEqualStatus}`
            getAqmisDefectInfo(query).then(res=>{
                if (res.code != 200) {
                    console.log('请求数据异常')
                    return
                }
                if(this.qualityPageNo * 3 >=  res.total){
                    this.qualityPageNo = 1
                }else{
                    this.qualityPageNo ++
                }
                this.qualityData = res.data
                if (this.qualityData === null || this.qualityData.length === 0) {
                    this.qualityData = []
                }
            })
            .catch(() => {
                console.log('请求数据为空')
            })
        },
        getFooterData(){
            const query = `code=TheFirst`
            getBigScreenFooter(query).then(res=>{
                if(res.code === '200' && res.data.length > 0){
                    this.description = res.data[0].description
                }else{
                    this.description = ''
                }
            }).catch(ex=>{
                this.description = ''
                console.log('请求数据为空')
            })

        }
    }
}
</script>
<style lang="less" scoped>
    .workstationScreen{
       ::v-deep .el-card__body{
            padding: 0 15px !important;
        }
        ::v-deep .linePie{
            .el-col{
                padding: 0 !important;
                margin: 0 !important;
            }
            .el-card__body{
                background: transparent !important;
                padding: 0 !important;
            }
            .cardStyle {
                margin: 0 !important;
            }
        }
        .cardStyle{
            margin: 10px;
            padding: 0;
            border: 0;
            .headerStyle{
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px;
                .logoStyle{
                        display: flex;
                        align-items: center;
                    img{
                        width: 60px;
                        margin-right: 10px;
                    }
                    span{
                        font-weight: 700;
                        font-size: 24px;
                    }
                }
                .headerL{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    p{
                        margin: 0;
                    }
                    .pImg{
                        width: 60px;
                        img{
                            width: 100%;
                            }
                    }
                    .pTime{
                        margin-top: 5px !important;
                        font-weight: 700;
                    }
                }
                .wrapmarquee{
                    width: 100%;
                    flex: 1;
                    .wraptext{
                        display: flex;
                        align-items: center;
                        p{
                            color: #ffba00;
                            font-size: 40px;
                            font-weight: 700;
                            margin: 0;
                            margin-right: 25px;
                            display: flex;
                            align-items: center;
                        }
                    }
                }
                .redactive{
                    color: red !important;
                }
                .orderInfo{
                    display: flex;
                    justify-content: left;
                    flex-direction: column;
                    p{
                    margin: 0 10px;
                    color: red;
                    font-weight: 700;
                    font-size:30px;
                    }
                }
            }
            .detail{
                display: flex;
                height: 150px;
                div{
                    padding: 5px;
                    font-weight: 700;
                    font-size: 20px;
                    color: red;
                    border: 1px #000 solid;
                    width: 50%;
                    white-space: wrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    span{
                        color: #0270bd;
                    }
                }
                img{
                    width: 50%;
                    height: 100%;
                }
            }
        }

    }
</style>