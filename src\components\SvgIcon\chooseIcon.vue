<template>
  <el-scrollbar id="icon_scrollbar" style="width: 250px; height: 200px">
    <div class="el-avatar" @click="chooseIcon('circle')">
      <svg-icon icon-class="circle" />
    </div>
    <div class="el-avatar" @click="chooseIcon('car')">
      <svg-icon icon-class="car" />
    </div>
    <div class="el-avatar" @click="chooseIcon('menu2')">
      <svg-icon icon-class="menu2" />
    </div>
    <div class="el-avatar" @click="chooseIcon('sucai')">
      <svg-icon icon-class="sucai" />
    </div>
    <div class="el-avatar" @click="chooseIcon('gongjuxiang')">
      <svg-icon icon-class="gongjuxiang" />
    </div>
    <div class="el-avatar" @click="chooseIcon('huizhuan')">
      <svg-icon icon-class="huizhuan" />
    </div>
    <div class="el-avatar" @click="chooseIcon('yidong')">
      <svg-icon icon-class="yidong" />
    </div>
    <div class="el-avatar" @click="chooseIcon('xinzeng')">
      <svg-icon icon-class="xinzeng" />
    </div>
    <div class="el-avatar" @click="chooseIcon('diaogou')">
      <svg-icon icon-class="diaogou" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dianyuan')">
      <svg-icon icon-class="dianyuan" />
    </div>
    <div class="el-avatar" @click="chooseIcon('weizhi')">
      <svg-icon icon-class="weizhi" />
    </div>
    <div class="el-avatar" @click="chooseIcon('xiangmu')">
      <svg-icon icon-class="xiangmu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('jixie')">
      <svg-icon icon-class="jixie" />
    </div>
    <div class="el-avatar" @click="chooseIcon('ruanjian')">
      <svg-icon icon-class="ruanjian" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dianqi')">
      <svg-icon icon-class="dianqi" />
    </div>
    <div class="el-avatar" @click="chooseIcon('wangluo')">
      <svg-icon icon-class="wangluo" />
    </div>
    <div class="el-avatar" @click="chooseIcon('yunyingguanli')">
      <svg-icon icon-class="yunyingguanli" />
    </div>
    <div class="el-avatar" @click="chooseIcon('jiankong')">
      <svg-icon icon-class="jiankong" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dashuju')">
      <svg-icon icon-class="dashuju" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('shujukong')">
      <svg-icon icon-class="shujukong" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('chuangxin')">
      <svg-icon icon-class="chuangxin" />
    </div>
    <div class="el-avatar" @click="chooseIcon('goc')">
      <svg-icon icon-class="goc" />
    </div>
    <div class="el-avatar" @click="chooseIcon('yunweiguanli')">
      <svg-icon icon-class="yunweiguanli" />
    </div>
    <div class="el-avatar" @click="chooseIcon('yanfaxietong')">
      <svg-icon icon-class="yanfaxietong" />
    </div>
    <div class="el-avatar" @click="chooseIcon('gengduo')">
      <svg-icon icon-class="gengduo" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dingdan')">
      <svg-icon icon-class="dingdan" />
    </div>
    <div class="el-avatar" @click="chooseIcon('peifang')">
      <svg-icon icon-class="peifang" />
    </div>
    <div class="el-avatar" @click="chooseIcon('gongyi')">
      <svg-icon icon-class="gongyi" />
    </div>
    <div class="el-avatar" @click="chooseIcon('tiaoma')">
      <svg-icon icon-class="tiaoma" />
    </div>
    <div class="el-avatar" @click="chooseIcon('wuliao')">
      <svg-icon icon-class="wuliao" />
    </div>
    <div class="el-avatar" @click="chooseIcon('mozu')">
      <svg-icon icon-class="mozu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('guize')">
      <svg-icon icon-class="guize" />
    </div>
    <div class="el-avatar" @click="chooseIcon('pack')">
      <svg-icon icon-class="pack" />
    </div>
    <div class="el-avatar" @click="chooseIcon('wenjian')">
      <svg-icon icon-class="wenjian" />
    </div>
    <div class="el-avatar" @click="chooseIcon('bushu')">
      <svg-icon icon-class="bushu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('chengxu')">
      <svg-icon icon-class="chengxu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('piliang')">
      <svg-icon icon-class="piliang" />
    </div>
    <div class="el-avatar" @click="chooseIcon('jiekou')">
      <svg-icon icon-class="jiekou" />
    </div>
    <div class="el-avatar" @click="chooseIcon('zhongxin')">
      <svg-icon icon-class="zhongxin" />
    </div>
    <div class="el-avatar" @click="chooseIcon('fuwu')">
      <svg-icon icon-class="fuwu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('danyuan')">
      <svg-icon icon-class="danyuan" />
    </div>
    <div class="el-avatar" @click="chooseIcon('gongwei')">
      <svg-icon icon-class="gongwei" />
    </div>
    <div class="el-avatar" @click="chooseIcon('chanxian')">
      <svg-icon icon-class="chanxian" />
    </div>
    <div class="el-avatar" @click="chooseIcon('zhuliucheng')">
      <svg-icon icon-class="zhuliucheng" />
    </div>
    <div class="el-avatar" @click="chooseIcon('luoji')">
      <svg-icon icon-class="luoji" />
    </div>
    <div class="el-avatar" @click="chooseIcon('zidonghua')">
      <svg-icon icon-class="zidonghua" />
    </div>
    <div class="el-avatar" @click="chooseIcon('shili')">
      <svg-icon icon-class="shili" />
    </div>
    <div class="el-avatar" @click="chooseIcon('kafka')">
      <svg-icon icon-class="kafka" />
    </div>
    <div class="el-avatar" @click="chooseIcon('xinghao')">
      <svg-icon icon-class="xinghao" />
    </div>
    <div class="el-avatar" @click="chooseIcon('biaoqian')">
      <svg-icon icon-class="biaoqian" />
    </div>
    <div class="el-avatar" @click="chooseIcon('banci')">
      <svg-icon icon-class="banci" />
    </div>
    <div class="el-avatar" @click="chooseIcon('gongyingshang')">
      <svg-icon icon-class="gongyingshang" />
    </div>
    <div class="el-avatar" @click="chooseIcon('renwu')">
      <svg-icon icon-class="renwu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('cuowu')">
      <svg-icon icon-class="cuowu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('alipay')">
      <svg-icon icon-class="alipay" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('anq')">
      <svg-icon icon-class="anq" />
    </div>
    <div class="el-avatar" @click="chooseIcon('app')">
      <svg-icon icon-class="app" />
    </div>
    <div class="el-avatar" @click="chooseIcon('backup')">
      <svg-icon icon-class="backup" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('chain')">
      <svg-icon icon-class="chain" />
    </div>
    <div class="el-avatar" @click="chooseIcon('chart')">
      <svg-icon icon-class="chart" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('codeConsole')">
      <svg-icon icon-class="codeConsole" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('dashboard')">
      <svg-icon icon-class="dashboard" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('database')">
      <svg-icon icon-class="database" />
    </div> -->
    <!-- <div class="el-avatar" @click="chooseIcon('date')">
      <svg-icon icon-class="date" />
    </div> -->
    <!-- <div class="el-avatar" @click="chooseIcon('deploy')">
      <svg-icon icon-class="deploy" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dept')">
      <svg-icon icon-class="dept" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dev')">
      <svg-icon icon-class="dev" />
    </div>
    <div class="el-avatar" @click="chooseIcon('develop')">
      <svg-icon icon-class="develop" />
    </div>
    <div class="el-avatar" @click="chooseIcon('dictionary')">
      <svg-icon icon-class="dictionary" />
    </div>
    <div class="el-avatar" @click="chooseIcon('doc')">
      <svg-icon icon-class="doc" />
    </div>
    <div class="el-avatar" @click="chooseIcon('download')">
      <svg-icon icon-class="download" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('edit')">
      <svg-icon icon-class="edit" />
    </div>
    <div class="el-avatar" @click="chooseIcon('education')">
      <svg-icon icon-class="education" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('email')">
      <svg-icon icon-class="email" />
    </div>
    <div class="el-avatar" @click="chooseIcon('error')">
      <svg-icon icon-class="error" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('exit-fullscreen')">
      <svg-icon icon-class="exit-fullscreen" />
    </div>
    <div class="el-avatar" @click="chooseIcon('fullscreen')">
      <svg-icon icon-class="fullscreen" />
    </div>
    <div class="el-avatar" @click="chooseIcon('fwb')">
      <svg-icon icon-class="fwb" />
    </div>
    <div class="el-avatar" @click="chooseIcon('github')">
      <svg-icon icon-class="github" />
    </div>
    <div class="el-avatar" @click="chooseIcon('gonggao')">
      <svg-icon icon-class="gonggao" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('icon')">
      <svg-icon icon-class="icon" />
    </div>
    <div class="el-avatar" @click="chooseIcon('image')">
      <svg-icon icon-class="image" />
    </div>
    <div class="el-avatar" @click="chooseIcon('index')">
      <svg-icon icon-class="index" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('international')">
      <svg-icon icon-class="international" />
    </div>
    <div class="el-avatar" @click="chooseIcon('ipvisits')">
      <svg-icon icon-class="ipvisits" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('java')">
      <svg-icon icon-class="java" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('link')">
      <svg-icon icon-class="link" />
    </div>
    <div class="el-avatar" @click="chooseIcon('list')">
      <svg-icon icon-class="list" />
    </div>
    <div class="el-avatar" @click="chooseIcon('lock')">
      <svg-icon icon-class="lock" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('log')">
      <svg-icon icon-class="log" />
    </div>
    <div class="el-avatar" @click="chooseIcon('login')">
      <svg-icon icon-class="login" />
    </div>
    <div class="el-avatar" @click="chooseIcon('markdown')">
      <svg-icon icon-class="markdown" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('menu')">
      <svg-icon icon-class="menu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('message')">
      <svg-icon icon-class="message" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('mnt')">
      <svg-icon icon-class="mnt" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('money')">
      <svg-icon icon-class="money" />
    </div>
    <div class="el-avatar" @click="chooseIcon('monitor')">
      <svg-icon icon-class="monitor" />
    </div>
    <div class="el-avatar" @click="chooseIcon('nested')">
      <svg-icon icon-class="nested" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('password')">
      <svg-icon icon-class="password" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('people')">
      <svg-icon icon-class="people" />
    </div>
    <div class="el-avatar" @click="chooseIcon('peoples')">
      <svg-icon icon-class="peoples" />
    </div>
    <div class="el-avatar" @click="chooseIcon('permission')">
      <svg-icon icon-class="permission" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('phone')">
      <svg-icon icon-class="phone" />
    </div>
    <div class="el-avatar" @click="chooseIcon('qiniu')">
      <svg-icon icon-class="qiniu" />
    </div>
    <div class="el-avatar" @click="chooseIcon('redis')">
      <svg-icon icon-class="redis" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('role')">
      <svg-icon icon-class="role" />
    </div>
    <div class="el-avatar" @click="chooseIcon('search')">
      <svg-icon icon-class="search" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('server')">
      <svg-icon icon-class="server" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('shopping')">
      <svg-icon icon-class="shopping" />
    </div>
    <div class="el-avatar" @click="chooseIcon('size')">
      <svg-icon icon-class="size" />
    </div>
    <div class="el-avatar" @click="chooseIcon('skill')">
      <svg-icon icon-class="skill" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('source')">
      <svg-icon icon-class="source" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('sqlMonitor')">
      <svg-icon icon-class="sqlMonitor" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('steve-Jobs')">
      <svg-icon icon-class="steve-Jobs" />
    </div> -->
    <!-- <div class="el-avatar" @click="chooseIcon('swagger')">
      <svg-icon icon-class="swagger" />
    </div>
    <div class="el-avatar" @click="chooseIcon('system')">
      <svg-icon icon-class="system" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('system1')">
      <svg-icon icon-class="system1" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('sys-tools')">
      <svg-icon icon-class="sys-tools" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('tab')">
      <svg-icon icon-class="tab" />
    </div>
    <div class="el-avatar" @click="chooseIcon('theme')">
      <svg-icon icon-class="theme" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('timing')">
      <svg-icon icon-class="timing" />
    </div>
    <div class="el-avatar" @click="chooseIcon('tools')">
      <svg-icon icon-class="tools" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('tree')">
      <svg-icon icon-class="tree" />
    </div>
    <div class="el-avatar" @click="chooseIcon('tree-table')">
      <svg-icon icon-class="tree-table" />
    </div>
    <div class="el-avatar" @click="chooseIcon('unlock')">
      <svg-icon icon-class="unlock" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('user')">
      <svg-icon icon-class="user" />
    </div>
    <div class="el-avatar" @click="chooseIcon('user1')">
      <svg-icon icon-class="user1" />
    </div>
    <div class="el-avatar" @click="chooseIcon('validCode')">
      <svg-icon icon-class="validCode" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('visits')">
      <svg-icon icon-class="visits" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('web')">
      <svg-icon icon-class="web" />
    </div> -->
    <div class="el-avatar" @click="chooseIcon('wechat')">
      <svg-icon icon-class="wechat" />
    </div>
    <!-- <div class="el-avatar" @click="chooseIcon('weixin')">
      <svg-icon icon-class="weixin" />
    </div>
    <div class="el-avatar" @click="chooseIcon('zujian')">
      <svg-icon icon-class="zujian" />
    </div> -->
  </el-scrollbar>
</template>
<script>
export default {
  // 数据模型
  data() {
    return {}
  },
  created: function() {
    // console.log(this.imgPath)
  },
  methods: {
    chooseIcon(id) {
      this.$emit('chooseIcon', id)
    }
  }
}
</script>
<style lang="scss">
#icon_scrollbar {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .el-avatar {
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #dd4b39;
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 15px;
    cursor: pointer;
    margin-bottom: 5px;
  }
}
</style>
