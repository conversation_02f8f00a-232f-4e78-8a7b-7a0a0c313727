<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="报警内容:">
                <el-input v-model="query.alarm_desc" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'有效',value:'Y'},{id:'2',label:'无效',value:'N'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="create_date" label="报警时间" />
            <el-table-column :show-overflow-tooltip="true" prop="alarm_code" label="报警代码" />
            <el-table-column :show-overflow-tooltip="true" prop="alarm_desc" label="报警内容" />
            <el-table-column :show-overflow-tooltip="true" prop="alarm_content" label="报警设备" />
            <el-table-column :show-overflow-tooltip="true" prop="enable_flag" label="有效标识">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsDashboard from '@/api/dcs/project/wms/wmsDashboard'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_ALARM_HISTORY',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '报警信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'alarm_id',
      // 排序
      sort: ['alarm_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsDashboard },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_fmod_alarm_history:add'],
        edit: ['admin', 'b_dcs_fmod_alarm_history:edit'],
        del: ['admin', 'b_dcs_fmod_alarm_history:del']
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {

  }
}
</script>

  <style>
  .table-descriptions-label {
    width: 150px;
  }
  .table-descriptions-content {
    width: 150px;
  }
  </style>
