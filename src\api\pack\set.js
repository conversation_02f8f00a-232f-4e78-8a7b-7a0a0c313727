import request from '@/utils/request'
// 查询包装set记录表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pack/core/packArrayBDSelect',
    method: 'post',
    data
  })
}

// 根据array_id启用set
export function enable(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackCoreOpEnableArrayByArrayId',
    method: 'post',
    data
  })
}

// 根据array_id禁用set
export function disable(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackCoreOpDisableArrayByArrayId',
    method: 'post',
    data
  })
}

// 根据array_barcode解绑包装set下的所有board
export function unbind(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackCoreOpUnbindArrayByArrayBarCode',
    method: 'post',
    data
  })
}

export default { sel, disable, unbind }
