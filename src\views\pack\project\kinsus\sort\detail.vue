<template>
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
        <el-form-item label="规则名称222" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="规则代码222" prop="code">
          <el-select v-model="form.code" allow-create clearable filterable>
            <el-option v-for="item in dict.SORT_RULE_CODES" :key="item.value" :label="item.label" :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="CCD对比功能选择" prop="boardType" display:none>
          <el-select v-model="form.boardType" clearable filterable>
            <el-option v-for="item in boardTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="源字段2222" prop="srcKey">
          <el-select v-model="form.srcKey" allow-create clearable filterable>
            <el-option v-for="item in dict.CCD_MESSAGE_FIELDS" :key="item.value" :label="item.label" :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="截取起始222" prop="srcSplitIndex">
          <el-input-number v-model="form.srcSplitIndex" :min="0" :max="99999" :step="1" clearable />
        </el-form-item>
        <el-form-item label="截取长度222" prop="srcSplitLength">
          <el-input-number v-model="form.srcSplitLength" :min="0" :max="99999" :step="1" clearable />
        </el-form-item>
        <el-form-item label="从右往左222" prop="srcSplitReverse">
          <el-switch v-model="form.srcSplitReverse" active-color="#13ce66" inactive-color="#ff4949" active-value="true" inactive-value="false" />
        </el-form-item>
        <el-form-item label="比对函数222" prop="compareFunc">
          <el-select v-model="form.compareFunc" clearable filterable>
            <el-option v-for="item in compareFuncs" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标字段222" prop="dstKey">
          <el-select v-model="form.dstKey" allow-create clearable filterable>
            <el-option v-for="item in dict.CUST_FIELDS" :key="item.value" :label="item.label" :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="截取起始" prop="dstSplitIndex">
          <el-input-number v-model="form.dstSplitIndex" :min="0" :max="99999" :step="1" :step-strictly="true" />
        </el-form-item>
        <el-form-item label="截取长度" prop="dstSplitLength">
          <el-input-number v-model="form.dstSplitLength" :min="0" :max="99999" :step="1" :step-strictly="true" />
        </el-form-item> -->
        <!-- <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
        </el-form-item> -->
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
      </div>
    </el-drawer>
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <el-table
          ref="table"
          v-loading="loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          :header-cell-style="headerCellStyle"
          :height="height"
          :highlight-current-row="true"
        >
          <!-- <el-table-column
            :show-overflow-tooltip="true"
            prop="enable_flag"
            :label="$t('lang_pack.commonPage.validIdentificationt')"
            width="130"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
            </template>
          </el-table-column> -->
          <el-table-column :show-overflow-tooltip="true" label="规则名称333" width="130" align="center" prop="name" />
          <el-table-column :show-overflow-tooltip="true" label="规则代码333" width="130" align="center" prop="code" />
          <el-table-column :show-overflow-tooltip="true" label="CCD对比功能选择" width="130" align="center" prop="boardType">
            <template slot-scope="scope">
              {{ boardTypes.find(item => item.value === scope.row.boardType).label }}
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" label="源字段333" width="200" align="center" prop="srcKey" />
          <el-table-column label="截取起始333" width="90" align="center" prop="srcSplitIndex" />
          <el-table-column label="截取长度333" width="90" align="center" prop="srcSplitLength" />
          <el-table-column label="从右往左333" width="90" align="center" prop="srcSplitReverse">
            <template slot-scope="scope">
              {{ (scope.row.srcSplitReverse === 1 || scope.row.srcSplitReverse === 'true') ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="比对函数333" width="100" align="center" prop="compareFunc">
            <template slot-scope="scope">
              {{ compareFuncs.find(item => item.value === scope.row.compareFunc).label }}
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" label="目标字段" width="200" align="center" prop="dstKey" />
          <!-- <el-table-column label="截取起始" width="90" align="center" prop="dstSplitIndex" />
          <el-table-column label="截取长度" width="90" align="center" prop="dstSplitLength" /> -->
          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import udOperation from '@crud/UD.operation'
const defaultForm = {
  id: '', // ID
  name: '', // 规则名称
  code: '', // 规则代码
  boardType: 'SET', // 板件类型
  compareFunc: '=', // 比对函数
  srcKey: '', // 源字段
  srcSplitIndex: 0, // 截取起始
  srcSplitLength: 0, // 截取长度
  srcSplitReverse: false, // 是否反转/从右往左
  dstKey: '', // 目标字段
  dstSplitIndex: 0, // 截取起始
  dstSplitLength: 0, // 截取长度
  dstSplitReverse: false // 是否反转/从右往左
}
export default {
  name: 'PACK_SORT_SPLIT_RULE_DETAIL',
  components: { udOperation },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.parkSortSplitRule') + '明细',
      // 登录用户
      userName: Cookies.get('userName'),
      // 菜单组ID
      query: { id: '' },
      // 唯一字段
      idField: 'id',
      // 排序
      sort: ['index asc'],
      crudMethod: {
        sel: (params) => {
          const data = this.propsData.list
          return new Promise((resolve, reject) => {
            resolve({ data: data })
          })
        },
        add: (form) => {
          const parentForm = this.propsData.parentForm
          const parentCrud = this.propsData.parentCrud
          const content = parentForm.content != null && parentForm.content !== '' ? JSON.parse(parentForm.content) : []
          const src = `${form.srcKey},${form.srcSplitIndex},${form.srcSplitLength},${form.srcSplitReverse}`
          const dst = `${form.dstKey},${form.dstSplitIndex},${form.dstSplitLength},${form.dstSplitReverse}`
          const contentItem = `${form.name};${form.code};${form.boardType};${form.compareFunc};${src};${dst}`
          content.push(contentItem)
          const contentJSON = JSON.stringify(content)
          const newForm = {
            id: parentForm.id,
            name: parentForm.name,
            content: contentJSON
          }
          const promise = parentCrud.crudMethod.edit(newForm)
          promise.then((res) => {
            if (res.code === 0) {
              parentForm.content = contentJSON
              parentCrud.toQuery()
            }
          })
          return promise
        },
        del: (form) => {
          if (form.ids) {
            const index = parseInt(form.ids)
            const data = this.propsData.list
            const parentForm = this.propsData.parentForm
            const parentCrud = this.propsData.parentCrud
            if (parentForm.id) {
              if (data[index]) {
                const content = JSON.parse(parentForm.content)
                content.splice(index, 1)
                const contentJSON = JSON.stringify(content)
                const newForm = {
                  id: parentForm.id,
                  name: parentForm.name,
                  content: contentJSON
                }
                const promise = parentCrud.crudMethod.edit(newForm)
                promise.then((res) => {
                  if (res.code === 0) {
                    data.splice(index, 1)
                    parentForm.content = contentJSON
                    parentCrud.toQuery()
                  }
                })
                return promise
              }
            }
          }
          return new Promise((resolve, reject) => {
            resolve({ code: 0 })
          })
        },
        edit: (form) => {
          const parentForm = this.propsData.parentForm
          const parentCrud = this.propsData.parentCrud
          if (parentForm.id) {
            const index = parseInt(form.id)
            const content = JSON.parse(parentForm.content)
            content.splice(index, 1)
            const src = `${form.srcKey},${form.srcSplitIndex},${form.srcSplitLength},${form.srcSplitReverse}`
            const dst = `${form.dstKey},${form.dstSplitIndex},${form.dstSplitLength},${form.dstSplitReverse}`
            const contentItem = `${form.name};${form.code};${form.boardType};${form.compareFunc};${src};${dst}`
            content.push(contentItem)
            const contentJSON = JSON.stringify(content)
            const newForm = {
              id: parentForm.id,
              name: parentForm.name,
              content: contentJSON
            }
            const promise = parentCrud.crudMethod.edit(newForm)
            promise.then((res) => {
              if (res.code === 0) {
                parentForm.content = contentJSON
                parentCrud.toQuery()
              }
            })
            return promise
          }
          return new Promise((resolve, reject) => {
            resolve({ code: 0 })
          })
        }
      },
      // 按钮显示
      optShow: {
        // add: true,
        edit: true,
        del: true
        // reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  props: {
    loading: {
      type: Boolean,
      default: () => false
    },
    permission: {
      type: Object,
      default: () => {}
    },
    parentCrud: {
      type: Object,
      default: () => {}
    },
    parentForm: {
      type: Object,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      boardTypes: [
        // { label: 'SET', value: 'Set' },
        // { label: 'PCS', value: 'Pcs' },
        { label: 'SET', value: 'SET' },
        { label: 'SET', value: 'PCS' }
      ],
      compareFuncs: [
        { label: '等于', value: '=' },
        { label: '不等于', value: '!=' },
        { label: '大于', value: '>' },
        { label: '大于等于', value: '>=' },
        { label: '小于', value: '<' },
        { label: '小于等于', value: '<=' },
        { label: '包含', value: 'contains' },
        { label: '不包含', value: 'not_contains' }
      ],
      srcProperties: ['srcKey', 'srcSplitIndex', 'srcSplitLength', 'srcSplitReverse'],
      dstProperties: ['dstKey', 'dstSplitIndex', 'dstSplitLength', 'dstSplitReverse'],
      rules: {
        name: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        boardType: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        srcKey: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        compareFunc: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        dstKey: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      }
    }
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['SORT_RULE_CODES', 'CCD_MESSAGE_FIELDS', 'CUST_FIELDS'],
  methods: {
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      return (this.srcProperties.includes(column.property) && 'background:#FFC000 !important') || // 源字段
              (this.dstProperties.includes(column.property) && 'background:#00B0F0 !important') || // 目标字段
              ''
    }
  }
}
</script>
