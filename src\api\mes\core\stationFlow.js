import request from '@/utils/request'

// 查询过站信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/queryStationFlowList',
    method: 'post',
    data
  })
}

export function exportEventInsert(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesStationFlowExportEventInsert',
    method: 'post',
    data
  })
}

export function exportEventInsertSH(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesStationFlowExportEventInsertSH',
    method: 'post',
    data
  })
}
export default { sel, exportEventInsert, exportEventInsertSH }
