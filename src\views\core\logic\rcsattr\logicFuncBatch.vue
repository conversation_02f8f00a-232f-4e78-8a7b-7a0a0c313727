<template>
  <el-row :gutter="20" style="padding: 10px">
    <el-col :span="24">
      <el-button size="small" type="primary" icon="el-icon-check" style="margin-left: 10px" :disabled="disabledSaveData" @click="handleSaveData">保存</el-button>
      <hr style="border: 0px; height: 0.5px; background-color: #a2b5c7 !important">
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :header-cell-style="{ background: '#eef1f6', color: '#545559' }" border :cell-style="getCellStyle" height="350px" @row-click="rowClick">
        <el-table-column  label="#" type="index" align="center" width="50" />
        <el-table-column  prop="logic_func_item_id" :label="$t('lang_pack.logicfunc.subproject')">  <!-- 子项目 -->
          <template slot-scope="scope">
            <el-select v-model="scope.row.logic_func_item_id" clearable filterable>
              <el-option v-for="item in logicAttrItemData" :key="item.logic_attr_item_id" :label="item.logic_attr_item_des" :value="item.logic_attr_item_id">
                <span style="float: left">{{ item.logic_attr_item_code }}</span>
                <span
                  style="
                    float: right;
                    color: #8492a6;
                    font-size: 13px;
                    margin-left: 20px;
                  "
                >{{ item.logic_attr_item_des }}</span>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <!--选择Tab-->
        <el-table-column  prop="logic_func_i_value" :label="$t('lang_pack.logicfunc.value')">  <!-- 值 -->
          <template slot-scope="scope">
            <el-popover :key="scope.row.id" placement="bottom" width="350" :v-model="customPopoverList['customPopover' + scope.row.id]" @show="currentRowData = scope.row">
              <div id="customPopoverDiv" style="height: 250px">
                <el-scrollbar style="height: 100%">
                  <el-tree :data="treeData" :props="defaultProps" :highlight-current="true" :render-content="renderContent"> ></el-tree>
                </el-scrollbar>
              </div>
              <el-input slot="reference" v-model="scope.row.logic_func_i_value" />
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column  prop="attribute1" :label="$t('lang_pack.logicfunc.attribute1')">  <!-- 属性1 -->
          <template slot-scope="scope">
            <el-input v-model="scope.row.attribute1" />
          </template>
        </el-table-column>
        <el-table-column  prop="attribute2" :label="$t('lang_pack.logicfunc.attribute2')">  <!-- 属性2 -->
          <template slot-scope="scope">
            <el-input v-model="scope.row.attribute2" />
          </template>
        </el-table-column>
        <el-table-column  prop="attribute3" :label="$t('lang_pack.logicfunc.attribute3')">  <!-- 属性3 -->
          <template slot-scope="scope">
            <el-input v-model="scope.row.attribute3" />
          </template>
        </el-table-column>
        <el-table-column  prop="attribute4" :label="$t('lang_pack.logicfunc.attribute4')">  <!-- 属性4 -->
          <template slot-scope="scope">
            <el-input v-model="scope.row.attribute4" />
          </template>
        </el-table-column>
        <el-table-column  prop="attribute5" :label="$t('lang_pack.logicfunc.attribute5')">  <!-- 属性5 -->
          <template slot-scope="scope">
            <el-input v-model="scope.row.attribute5" />
          </template>
        </el-table-column>
        <el-table-column  :label="$t('lang_pack.logicfunc.isEnabled')" align="center" prop="enable_flag" width="105">  <!-- 是否有效 -->
          <template
            slot-scope="scope"
          ><!--取到当前单元格-->
            <el-switch v-model="scope.row.enable_flag" :disabled="false" active-value="Y" inactive-value="N" active-color="#409EFF" inactive-color="#F56C6C" />
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="left">  <!-- 操作 -->
          <template slot-scope="scope">
            <div>
              <el-button size="small" type="primary" icon="el-icon-plus" circle @click="handleAddRow" />
              <el-button type="danger" icon="el-icon-delete" size="small" circle @click="handleDelRow(scope.row)" />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </el-row>
</template>
<script>
import { logicAttrItemLov } from '@/api/core/logic/logicAttrItem'
import { scadaClientTree } from '@/api/core/scada/client'
import { insRcsLogicFuncItem } from '@/api/core/logic/logicFuncI'

export default {
  name: 'HelloWorld',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',
      disabledSaveData: false,
      popoverTagDes: false,
      checkedTagDes: false,
      listLoadingTable: false,
      tableDataTable: [],
      count: 0,
      template: {
        id: 0,
        logic_func_i_id: '',
        logic_func_g_id: this.$parent.$attrs.LogicFuncGId,
        logic_func_item_id: '',
        logic_func_i_value: '',
        attribute1: '',
        attribute2: '',
        attribute3: '',
        attribute4: '',
        attribute5: '',
        enable_flag: 'Y'
      },
      logicAttrItemData: [],
      customPopoverList: {
        customPopover0: false,
        customPopover1: false,
        customPopover2: false,
        customPopover3: false,
        customPopover4: false
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      errorData: [],
      currentRowData: []
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  created: function() {
    this.handleAddRow()
    const query = {
      userID: '-1',
      logic_attr_group_id: this.$parent.$attrs.LogicAttrGroupId
    }
    this.logicAttrItemData = []
    // 从后台获取到对象数组
    logicAttrItemLov(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))

        if (defaultQuery.code == 0) {
          if (defaultQuery.data.length > 0) {
            this.logicAttrItemData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    scadaClientTree({
      logic_func_g_id: this.$parent.$attrs.LogicFuncGId,
      isShowTag: 'Y'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.length > 0) {
          this.treeData = []
          var a = {}
          a.level = 1
          a.label = '实例'
          a.children = defaultQuery.data
          this.treeData.push(a)
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    rowClick(row, column, event) {
      const errorinfo = this.errorData.filter(item => item.id === row.id)
      if (errorinfo.length > 0) {
        this.$message({
          message: errorinfo[0].msg,
          type: 'error'
        })
      }
    },
    getCellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec'
    },
    handleAddRow() {
      this.count++
      this.tableDataTable.push({
        id: this.count,
        logic_func_i_id: '',
        logic_func_g_id: this.$parent.$attrs.LogicFuncGId,
        logic_func_item_id: '',
        logic_func_i_value: '',
        attribute1: '',
        attribute2: '',
        attribute3: '',
        attribute4: '',
        attribute5: '',
        enable_flag: 'Y'
      })
    },
    handleDelRow(row) {
      if (this.tableDataTable.length === 1) {
        this.$message({
          message: '至少要留一个，你说是吧！',
          type: 'info'
        })
      } else {
        const index = this.tableDataTable.findIndex(d => d.id === row.id)
        this.tableDataTable.splice(index, 1)
      }
    },
    handleSaveData() {
      var rowcount = this.tableDataTable.length
      var index = 0
      this.disabledSaveData = true
      this.listLoadingTable = true
      this.errorData = []
      var successData = []
      this.tableDataTable.forEach(item => {
        const save = {
          userID: '-1',
          logic_func_i_id: item.logic_func_i_id,
          logic_func_g_id: item.logic_func_g_id,
          logic_func_item_id: item.logic_func_item_id,
          logic_func_i_value: item.logic_func_i_value,
          attribute1: item.attribute1,
          attribute2: item.attribute2,
          attribute3: item.attribute3,
          attribute4: item.attribute4,
          attribute5: item.attribute5,
          enable_flag: item.enable_flag
        }
        insRcsLogicFuncItem(save)
          .then(res => {
            index++
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code == 0) {
              successData.push({ id: item.id, msg: '' })
            } else {
              this.errorData.push({ id: item.id, msg: defaultQuery.msg })
            }
            if (rowcount === index) {
              this.disabledSaveData = false
              this.listLoadingTable = false
              if (this.errorData.length === 0) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                })
                this.$emit('RefreshTagTable')
              } else {
                this.$message({
                  message: '保存操作共' + this.tableDataTable.length + '条数据，成功' + successData.length + '条，失败' + this.errorData.length + '条',
                  type: 'info'
                })
                successData.forEach(item => {
                  const index = this.tableDataTable.findIndex(d => d.id === item.id)
                  this.tableDataTable.splice(index, 1)
                })
              }
            }
          })
          .catch(() => {
            if (rowcount === index) {
              this.disabledSaveData = false
              this.listLoadingTable = false
            }
            this.$message({
              message: '新增异常',
              type: 'error'
            })
          })
      })
    },

    chooseTag(data) {
      this.currentRowData.logic_func_i_value = data.tag_id
      this.customPopoverList['customPopover' + data.id] = false
    },
    renderContent(h, { node, data, store }) {
      if (data.level === 4) {
        return (
          <span class='custom-tree-node'>
            <span>{node.label}</span>
            <span>
              <el-button size='mini' type='text' on-click={() => this.chooseTag(data)}>
                选择
              </el-button>
            </span>
          </span>
        )
      } else {
        return (
          <span class='custom-tree-node'>
            <span>{node.label}</span>
          </span>
        )
      }
    }
  }
}
</script>

<style lang="scss">
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
#customPopoverDiv {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
}
</style>
