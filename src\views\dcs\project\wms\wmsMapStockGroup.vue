<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="库位区域:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="700px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="180px"
              :inline="true"
            >
              <el-form-item label="库位区域" prop="stock_group_code">
                <el-input v-model="form.stock_group_code" />
              </el-form-item>
              <el-form-item label="库位组起始X坐标" prop="start_position_x">
                <el-input v-model="form.start_position_x" />
              </el-form-item>
              <el-form-item label="库位组终点X坐标" prop="end_position_x">
                <el-input v-model="form.end_position_x" />
              </el-form-item>
              <el-form-item label="库位组起始Y坐标" prop="start_position_y">
                <el-input v-model="form.start_position_y" />
              </el-form-item>
              <el-form-item label="库位组终点Y坐标" prop="end_position_y">
                <el-input v-model="form.end_position_y" />
              </el-form-item>
              <el-form-item label="库位组起始Z坐标" prop="start_position_z">
                <el-input v-model="form.start_position_z" />
              </el-form-item>
              <el-form-item label="像素单格长度" prop="map_cell_length">
                <el-input v-model="form.map_cell_length" />
              </el-form-item>
              <el-form-item label="像素单格宽度" prop="map_cell_width">
                <el-input v-model="form.map_cell_width" />
              </el-form-item>
              <el-form-item label="X起始放置顺序" prop="x_start_type">
                <el-select v-model="form.x_start_type" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'从左到右',value:'LEFT'},{id:'2',label:'从右到左',value:'RIGHT'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="X坐标计算方式" prop="x_math_type">
                <el-select v-model="form.x_math_type" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'增加方式',value:'PLUS'},{id:'2',label:'减少方式',value:'MINUS'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Y起始放置顺序" prop="y_start_type">
                <el-select v-model="form.y_start_type" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'从上到下',value:'UP'},{id:'2',label:'从下到上',value:'DOWN'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="Y坐标计算方式" prop="y_math_type">
                <el-select v-model="form.y_math_type" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'增加方式',value:'PLUS'},{id:'2',label:'减少方式',value:'MINUS'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="库位最大高度" prop="stock_max_height">
                <el-input v-model="form.stock_max_height" />
              </el-form-item>
              <el-form-item label="板子之间安全长度" prop="safe_length">
                <el-input v-model="form.safe_length" />
              </el-form-item>
              <el-form-item label="板子之间安全宽度" prop="safe_width">
                <el-input v-model="form.safe_width" />
              </el-form-item>
              <el-form-item label="堆叠方式" prop="dd_way">
                <el-select v-model="form.dd_way" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'按尺寸',value:'SIZE'},{id:'2',label:'按物料',value:'MATERIAL'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="stock_group_code" label="库位区域" />
            <el-table-column :show-overflow-tooltip="true" prop="start_position_x" width="140" label="库位组起始X坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="end_position_x" width="140" label="库位组终点X坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="start_position_y" width="140" label="库位组起始Y坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="end_position_y" width="140" label="库位组终点Y坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="start_position_z" width="140" label="库位组起始Z坐标" />
            <el-table-column :show-overflow-tooltip="true" prop="map_cell_length" width="140" label="像素单格长度" />
            <el-table-column :show-overflow-tooltip="true" prop="map_cell_width" width="130" label="像素单格宽度" />
            <el-table-column :show-overflow-tooltip="true" prop="x_start_type" width="130" label="X起始放置顺序">
              <template slot-scope="scope">
                <!--LEFT从左到右,RIGHT从右到左-->
                {{ scope.row.x_start_type == 'LEFT' ? '从左到右' : '从右到左' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="x_math_type" width="130" label="X坐标计算方式">
              <template slot-scope="scope">
                <!--PLUS增加方式,MINUS减少方式-->
                {{ scope.row.x_math_type == 'PLUS' ? '增加方式' : '减少方式' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="y_start_type" width="130" label="Y起始放置顺序">
              <template slot-scope="scope">
                <!--UP从上到下,DOWN从下到上-->
                {{ scope.row.y_start_type == 'UP' ? '从上到下' : '从下到上' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="y_math_type" width="130" label="Y坐标计算方式">
              <template slot-scope="scope">
                <!--PLUS增加方式,MINUS减少方式-->
                {{ scope.row.y_math_type == 'PLUS' ? '增加方式' : '减少方式' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="stock_max_height" width="130" label="库位最大高度" />
            <el-table-column :show-overflow-tooltip="true" prop="safe_length" width="130" label="板子之间安全长度" />
            <el-table-column :show-overflow-tooltip="true" prop="safe_width" width="130" label="板子之间安全宽度" />
            <el-table-column :show-overflow-tooltip="true" prop="dd_way" label="堆叠方式">
              <template slot-scope="scope">
                <!--SIZE按尺寸,MATERIAL按物料-->
                {{ scope.row.dd_way == 'MATERIAL' ? '按物料' : '按尺寸' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="80" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :delete-del="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <selectModal ref="selectModal" :dict="dict" />
  </div>
</template>

<script>
import crudWmsMapMeStockGroup from '@/api/dcs/project/wms/wmsMapStockGroup'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import selectModal from '@/components/selectModal'
const defaultForm = {
  stock_group_code: '',
  start_position_x: '',
  end_position_x: '',
  start_position_y: '',
  end_position_y: '',
  start_position_z: '',
  map_cell_length: '',
  map_cell_width: '',
  x_start_type: 'LEFT',
  x_math_type: 'PLUS',
  y_start_type: 'UP',
  y_math_type: 'PLUS',
  stock_max_height: '',
  safe_length: '',
  safe_width: '',
  dd_way: 'SIZE'
}
export default {
  name: 'WMS_MAP_ME_STOCK_GROUP',
  components: { crudOperation, udOperation, rrOperation, pagination, selectModal },
  cruds() {
    return CRUD({
      title: 'WMS库区基础信息配置',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'stock_group_id',
      // 排序
      sort: ['stock_group_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapMeStockGroup },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var isNumber = (rule, value, callback) => {
      value = Number(value) // 转换为数字
      if (typeof value === 'number' && !isNaN(value)) { // 判断是否为数字
        if (value < 0) {
          callback(new Error('不能小于等于0'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_me_stock:add'],
        edit: ['admin', 'b_dcs_wms_map_me_stock:edit'],
        del: ['admin', 'b_dcs_wms_map_me_stock:del']
      },
      rules: {
        stock_group_code: [{ required: true, message: '请输入必填项', trigger: 'blur' }],
        start_position_x: [{ validator: isNumber, required: true, message: '请输入大于等于0的整数', trigger: 'blur' }],
        end_position_x: [{ validator: isNumber, required: true, message: '请输入大于等于0的整数', trigger: 'blur' }],
        start_position_y: [{ validator: isNumber, required: true, message: '请输入大于等于0的整数', trigger: 'blur' }],
        end_position_y: [{ validator: isNumber, required: true, message: '请输入大于等于0的整数', trigger: 'blur' }],
        start_position_z: [{ validator: isNumber, required: true, message: '请输入大于等于0的整数', trigger: 'blur' }],
        x_start_type: [{ required: true, message: '请输入必填项', trigger: 'blur' }],
        x_math_type: [{ required: true, message: '请输入必填项', trigger: 'blur' }],
        y_start_type: [{ required: true, message: '请输入必填项', trigger: 'blur' }],
        y_math_type: [{ required: true, message: '请输入必填项', trigger: 'blur' }],
        dd_way: [{ required: true, message: '请输入必填项', trigger: 'blur' }]
      }
    }
  },
  dicts: ['TASK_TYPE', 'TASK_WAY', 'LOCK_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    viewDetail(data) {
      if (!data.stock_id) {
        this.$message({ type: 'error', message: '未获取到库位id' })
        return
      }
      this.$refs.selectModal.open({
        type: 'ckkcmx',
        checkType: '',
        search: {
          stock_id: data.stock_id,
          sort: 'stock_d_id desc',
          user_name: Cookies.get('userName')
        }
      })
    }
  }
}
</script>
