<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <!--表格渲染-->
      <el-table ref="table" v-loading="listLoadingTable" border size="small" :data="tableDataTable" style="width: 100%" :height="height" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
        <el-table-column :show-overflow-tooltip="true" prop="label" :label="$t('lang_pack.file.fileName')" sortable="custom" />
        <!-- 文件名称 -->
        <el-table-column :show-overflow-tooltip="true" prop="size" :label="$t('lang_pack.file.fileSize')" sortable="custom" />
        <!-- 文件大小 -->

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButDownload(scope.row)">下载</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </el-row>
</template>
<script>
import { dirFileTree, fileDownload, fileDel } from '@/api/dcs/project/xg/file'

export default {
  name: 'FILEITME',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      // Table
      listLoadingTable: false,
      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],
      // 文件夹目录
      currentFilePath: ''
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.toButQuery()
  },

  methods: {
    // 按钮：
    toButQuery(fileName, filePath) {
      this.currentFilePath = filePath
      this.listLoadingTable = true

      const query = {
        type: 'file',
        file_name: fileName,
        file_path: filePath
      }
      dirFileTree(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.length > 0) {
            this.tableDataTable = defaultQuery.data
          } else {
            this.tableDataTable = []
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    // 下载
    toTableButDownload(data) {
      // Table下载(单笔)
      this.$confirm(`将下载文件${data.label}到本地，是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const download = {
            file_name: data.label,
            file_path: data.path
          }
          fileDownload(download)
            .then(res => {
              const link = document.createElement('a')
              const blob = new Blob([res], { type: 'blob' })
              link.href = URL.createObjectURL(blob)
              link.style.display = 'none'
              link.style.target = '_blank'
              link.href = URL.createObjectURL(blob)
              console.warn(link.href)
              link.download = data.label // 下载的文件名
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
            })
            .catch(() => {
              this.$message({
                message: '下载异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // 删除
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除文件${data.label}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            file_name: data.label,
            file_path: data.path
          }
          fileDel(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })

                // 查询
                this.toButQuery('', this.currentFilePath)
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    }
  }
}
</script>
