
// function render(g, node, isSelected) {
//   if (node.type === 'image') {
//     g.append('circle')
//       .attr('cx', node.x)
//       .attr('cy', node.y)
//       .attr('stroke', '#445ddb')
//       .attr('r', node.width)
//       .style('stroke-width', '3px')
//       // .attr('href',  node.href)
//   } 
// }

// export default render

function render(g, node, isSelected) {
  if (node.type === 'img') {
    g.append('image')
      .attr('x', node.x)
      .attr('y', node.y)
      .attr('preserveAspectRatio', 'none')
      .attr('vector-effect', 'non-scaling-stroke')
      .style('width', node.width + 'px')
      .style('height', node.height + 'px')
      .style('borderRadius', '50%')
      .attr('href', node.href)
  } else if (node.type === 'circle') {
    const borderColor = isSelected ? '#445ddb' : node.stroke
    const borderWidth = isSelected ? '3px' : node.strokeWidth
    // g.append('circle')
    //   .attr('cx', node.x)
    //   .attr('cy', node.y)
    //   .attr('r', node.width)
    //   .style('stroke-width', borderWidth)
    // g.append('image')
    // .attr('width', node.width + 'px')
    // .attr('height', node.height + 'px')
    // .attr('href',  node.href)
    // if (node.status === 'PLAN') {
    //   g.classed('commodity-sign-wrap', true)
    //   g.attr('fill', '#F9F500')
    // } else if (node.status === 'OK') {
    //   g.attr('fill', '#0EAD00')
    // } else if (node.status === 'WAIT') {
    //   g.attr('fill', '#C8CACC')
    // } else {
    //   g.attr('fill', '#FF1418')
    }

  //   g.append('text')
  //     .attr('x', node.x)
  //     .attr('y', node.y + 6)
  //     .style('fill', '#2D333D')
  //     .attr('class', 'unselectable')
  //     .attr('text-anchor', 'middle')
  //     .text(() => node.index)
  //     .style('font-size', '14px')
  //     .style('font-weight', 'bold')
  // }
}

export default render
