<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="产线：">
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_id" placeholder="请选择产线">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="产线代码/描述：">
                <el-input v-model="query.prod_line_code_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="机型：">
                <el-input v-model="query.small_model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="150px" :inline="true">
          <!--表：sys_fmod_prod_line-->
          <el-form-item label="产线" prop="reciprod_line_idpe_id">
            <el-select v-model="form.prod_line_id" filterable clearable>
              <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
          </el-form-item>

          <el-form-item label="机型" prop="small_model_type">
            <el-input v-model="form.small_model_type" />
          </el-form-item>
          <el-form-item label="机型代码" prop="model_code">
            <el-input v-model="form.model_code" />
          </el-form-item>
          <!--快速编码：MATERIAL_TYPE-->
          <el-form-item label="零件类型" prop="material_type">
            <el-select v-model="form.material_type" clearable filterable>
              <el-option v-for="item in dict.MATERIAL_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="产品编码" prop="main_material_code">
            <el-input v-model="form.main_material_code" />
          </el-form-item>
          <el-form-item label="产品描述" prop="main_material_des">
            <el-input v-model="form.main_material_des" />
          </el-form-item>
          <el-form-item label="换模时间(分)" prop="change_model_times">
            <el-input v-model="form.change_model_times" />
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <!--快速编码：ENABLE_FLAG-->
          <el-form-item label="有效标识">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="配方类型" prop="small_model_type">
            <el-tree ref="treeSmallModel" :data="dataRecipeTypeTable" :props="dataRecipeTypeProps" :highlight-current="true" check-strictly accordion show-checkbox node-key="fastcode_code" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>

      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler" @row-click="handleRowClick">
              <el-table-column  type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  v-if="1 == 0" width="10" prop="small_type_id" label="id" />

              <el-table-column  :show-overflow-tooltip="true" prop="prod_line_code_des" label="产线" />
              <el-table-column  :show-overflow-tooltip="true" prop="small_model_type" label="机型" />
              <el-table-column  :show-overflow-tooltip="true" prop="model_code" label="机型代码" />
              <el-table-column  label="零件类型" align="center" prop="material_type" width="100">
                <!-- 零件类型 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.MATERIAL_TYPE[scope.row.material_type] }}
                </template>
              </el-table-column>

              <el-table-column  :show-overflow-tooltip="true" prop="main_material_code" label="产品编码" />
              <el-table-column  :show-overflow-tooltip="true" prop="main_material_des" label="产品描述" />
              <el-table-column  :show-overflow-tooltip="true" prop="change_model_times" label="换模时间" />

              <el-table-column  label="有效标识" align="center" prop="enable_flag" width="120">
                <!-- 有效标识 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
                </template>
              </el-table-column>

              <!-- Table单条操作-->
              <el-table-column  label="操作" width="115" align="center" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <smallModelRecipe ref="smallModelRecipe" class="tableFirst" :small_type_id="currentSmallTypeId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudSmallModel from '@/api/mes/core/smallModel'
import smallModelRecipe from './smallModelRecipe'
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import { treeRecipeMust } from '@/api/mes/core/recipeMust'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  small_type_id: '',
  prod_line_id: '',
  small_model_type: '',
  model_code: '0',
  material_type: '',
  main_material_code: '',
  main_material_des: '',
  change_model_times: '0',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: '',
  recipe_type: ''
}
export default {
  name: 'MES_SMALL_MODEL',
  components: { crudOperation, rrOperation, udOperation, pagination, smallModelRecipe },
  cruds() {
    return CRUD({
      title: '机型维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'small_type_id',
      // 排序
      sort: ['small_type_id asc'],
      // CRUD Method
      crudMethod: { ...crudSmallModel },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'c_mes_fmod_small_model:add'],
        edit: ['admin', 'c_mes_fmod_small_model:edit'],
        del: ['admin', 'c_mes_fmod_small_model:del'],
        down: ['admin', 'c_mes_fmod_small_model:down']
      },
      rules: {
        // 提交验证规则
        small_model_type: [{ required: true, message: '请输入机型', trigger: 'blur' }]
      },
      currentSmallTypeId: 0,
      prodLineData: [],
      dataRecipeTypeTable: [], // 配方类型数据
      dataRecipeTypeProps: {
        fastcode_des: 'fastcode_des',
        children: 'children',
        label: 'label'
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'MATERIAL_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {
    this.getSmallType('')

    // 加载 产线LOV
    const query = {
      userID: Cookies.get('userName')
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    // 编辑 - 之后
    [CRUD.HOOK.afterToEdit](crud) {
      this.getSmallType(crud.form.small_type_id)
      return true
    },
    // 编辑取消 - 之后
    [CRUD.HOOK.afterEditCancel](crud) {
      this.$refs.treeSmallModel.setCheckedKeys([])
      return true
    },
    // 提交 - 之前
    [CRUD.HOOK.beforeSubmit](crud) {
      const data = this.$refs.treeSmallModel.getCheckedNodes()
      if (data.length > 0) {
        var recipeMusts = []
        this.$refs.treeSmallModel.getCheckedNodes().forEach(item => {
          recipeMusts.push(item.fastcode_code)
        })
        crud.form.recipe_type = recipeMusts.join(',')
      }
      return true
    },
    // 提交 - 之后
    [CRUD.HOOK.afterSubmit](crud) {
      this.$refs.treeSmallModel.setCheckedKeys([])
      return true
    },

    // 配方类型 LOV
    getSmallType(smallTypeId) {
      // 查询所有的配方类型
      const query = {
        userID: Cookies.get('userName'),
        small_type_id: smallTypeId
      }
      // 从后台获取到对象数组
      treeRecipeMust(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          if (smallTypeId === '') {
            if (defaultQuery.data.length > 0) {
              this.dataRecipeTypeTable = defaultQuery.data
            }
          } else {
            this.$refs.treeSmallModel.setCheckedKeys([])
            if (defaultQuery.checked.length > 0) {
              var ids = []
              defaultQuery.checked.forEach(item => {
                if (item.fastcode_code !== '' && item.fastcode_code !== undefined && item.fastcode_code !== null) {
                  ids.push(item.fastcode_code)
                }
              })
              this.$refs.treeSmallModel.setCheckedKeys(ids)
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    handleRowClick(row, column, event) {
      this.currentSmallTypeId = row.small_type_id
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  min-height: calc(100vh - 30px);
  .el-card__header {
    padding: 0px 20px;
    height: 40px;
    line-height: 40px;
  }
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}

.el-drawer {
  overflow-y: scroll;
}
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
</style>
