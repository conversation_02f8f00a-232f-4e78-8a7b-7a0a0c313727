<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-descriptions :title="$t('lang_pack.stationParameter.stationConfiguration')" class="margin-top" :column="2" border>
        <el-descriptions-item
          v-for="(item, index) in StationConfigData"
          :key="index"
          label-class-name="table-descriptions-label"
          content-class-name="table-descriptions-content"
        >
          <template slot="label">
            <i class="el-icon-tickets" />
            {{ item.config_des }}
          </template>
          <template v-if="item.data_type === 'Bool'">
            <el-switch
              v-model="item.tag_value"
              active-value="1"
              inactive-value="0"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="handleBoolWrite(item)"
            />
          </template>
          <template v-else>
            <el-button size="small" type="primary" plain @click="openTagWrite(item)">{{ $t('lang_pack.monitor.write') }}</el-button>
          </template>
          &nbsp;&nbsp;&nbsp;&nbsp;{{ item.tag_value }}
        </el-descriptions-item>
      </el-descriptions>
      <el-dialog :title="currentRow.config_des" width="450px" :visible.sync="tagWriteDialogVisible" append-to-body>
        <el-input ref="tagWriteValue" v-model="tagWriteValue" clearable size="medium" style="width: 100%" />
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible = false">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite('String')">{{ $t('lang_pack.monitor.labelValue') }}</el-button>
        </div>
      </el-dialog>
      <el-dialog :title="currentRow.config_des" width="550px" :visible.sync="tagWriteDialogVisible1" append-to-body>
        <template v-for="(item, index) in data1">
          <div :key="index" style="margin-bottom:5px;">
            <el-input v-model="item.key" clearable size="medium" style="width: 170px">
              <template slot="prepend">Key</template>
            </el-input>
            <el-input v-model="item.value" clearable size="medium" style="width: 170px">
              <template slot="prepend">Value</template>
            </el-input>
            <el-button-group>
              <el-button type="primary" icon="el-icon-edit" @click="handleAddData1()">{{ $t('lang_pack.commonPage.add') }}</el-button>
              <el-button type="primary" icon="el-icon-delete" @click="handleDelData1(item.id)">{{ $t('lang_pack.commonPage.remove') }}</el-button>
            </el-button-group>
          </div>
        </template>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible1 = false">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite('Json')">{{ $t('lang_pack.monitor.write') }}</el-button>
        </div>
      </el-dialog>
      <el-dialog :title="currentRow.config_des" width="700px" :visible.sync="tagWriteDialogVisible2" append-to-body>
        <el-table
          border
          :data="data2"
          style="width: 100%"
          height="350px"
          :stripe="true"
          :header-cell-style="{ background: '#F1F4F7', color: '#757575' }"
          @header-dragend="crud.tableHeaderDragend()"
        >
          <el-table-column :label="$t('lang_pack.commonPage.operate')" width="100" align="center">
            <template slot-scope="scope">
              <div>
                <el-button size="small" type="primary" icon="el-icon-plus" circle @click="handleAddData2" />
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="small"
                  circle
                  @click="handleDelData2(scope.row.id)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.stationParameter.interfaceCode')" prop="func_code">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.func_code" placement="top" effect="light">
                <el-input v-model="scope.row.func_code" clearable size="mini" />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.stationParameter.interDes')" prop="func_des">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.func_des" placement="top" effect="light">
                <el-input v-model="scope.row.func_des" clearable size="mini" />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.stationParameter.NGDegree')" prop="func_ng_count">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.func_ng_count" placement="top" effect="light">
                <el-input v-model="scope.row.func_ng_count" clearable size="mini" />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.stationParameter.synchronization')" width="70">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.func_sync"
                active-value="Y"
                inactive-value="N"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.stationParameter.continuation')" width="70">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.func_continue"
                active-value="Y"
                inactive-value="N"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('lang_pack.stationParameter.enable')" width="70">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enable_flag"
                active-value="Y"
                inactive-value="N"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </template>
          </el-table-column>
        </el-table>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="tagWriteDialogVisible2 = false">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleTagWrite('Interf')">{{ $t('lang_pack.monitor.write') }}</el-button>
        </div>
      </el-dialog>
      <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="warningMsgDialogVisible" :close-on-click-modal="false">
        <span>{{ $t('lang_pack.stationParameter.cutOnline') }}</span>
      </el-dialog>
      <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="resetDialogFlag" :close-on-click-modal="false">
        <span>{{ $t('lang_pack.stationParameter.onlineMode') }}</span>
      </el-dialog>
      <el-dialog :visible.sync="planListShow" :close-on-click-modal="false" :title="title" width="80%" :before-close="handlePlanClose">
        <planList v-if="planListShow" ref="planList"  />
      </el-dialog>  
    </el-card>
  </div>
</template>

<script>
import { sel as selStationIP } from '@/api/core/factory/sysStation'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import { selStationConfig } from '@/api/eap/eapFmodConfig'
import { selCellIP } from '@/api/core/center/cell'
import planList from '@/views/eap/core/plan/PlanList'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'STATION_SET_HMI',
  components: {planList},
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      station_id: '',
      cell_id: '',
      StationConfigData: [
        // { config_des: 'Panel模式(1有/0无)', client_code: '', group_code: '', tag_code: '', data_type: 'Bool', tag_key: 'A/B/C', tag_value: '0' },
        // { config_des: '设备模式', client_code: '', group_code: '', tag_code: '', data_type: 'String', tag_key: 'A/B/C', tag_value: '123' },
        // { config_des: '下发参数', client_code: '', group_code: '', tag_code: '', data_type: 'Json', tag_key: 'A/B/C', tag_value: '{"a":"123","b":"33"}' }
        // { config_des: '接口配置', client_code: '', group_code: '', tag_code: '', data_type: 'Interf', tag_key: 'A/B/C', tag_value: '[]' }
      ],
      data1: [],
      data2: [],
      currentRow: {},
      tagWriteDialogVisible: false,
      tagWriteValue: '',
      tagWriteDialogVisible1: false,
      tagWriteDialogVisible2: false,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip      
      planListShow:false,
      onofflineItem:null,
      optionsChangeMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8) + '1', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      triggerValue: '',
      boardValue: '',
      warningMsgDialogVisible: false,
      resetDialogFlag: false

    }
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
    if (this.clientChangeMqtt) {
      this.clientChangeMqtt.end()
      this.mqttChangeStatus = false
    }
  },
  // 数据字典
  dicts: ['EAP_MACHINE_NAME'],
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {
    this.station_id = this.$route.query.station_id
    this.cell_id = this.$route.query.cell_id
    this.getCellIp()
    // 获取收扳机的IP
    var queryParameter = {
      userName: Cookies.get('userName'),
      parameter_code: 'SYNC_LOAD_UNLOAD_MODE',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.triggerValue = defaultQuery.data[0].parameter_val
          }
        }
      }).catch(() => {
        this.$message({
          message: this.$t('view.dialog.queryException'),
          type: 'error'
        })
      })
  },
  methods: {
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.getStationConfigData()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('view.dialog.queryException'), type: 'error' })
        })
    },
    getStationConfigData() {
      this.StationConfigData = []
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.station_id
      }
      selStationConfig(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.StationConfigData = JSON.parse(defaultQuery.result)
            this.getTagValue()
            this.toStartWatch()
            // 先判断是否启动mqtt
            if (this.triggerValue == 'Y') {
              this.changeTrigger()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('view.dialog.queryException'),
            type: 'error'
          })
        })
    },
    getTagValue() {
      var readTagArray = []
      for (var i = 0; i < this.StationConfigData.length; i++) {
        var readTag = {}
        readTag.tag_key = this.StationConfigData[i].tag_key
        readTagArray.push(readTag)
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              for (var i = 0; i < result.length; i++) {
                var tag_key = result[i].tag_key
                var tag_value = result[i].tag_value === undefined ? '' : result[i].tag_value
                this.StationConfigData.filter(item => item.tag_key === tag_key)[0].tag_value = tag_value
              }
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
        })
    },
    openTagWrite(item) {
      this.currentRow = item
      if (item.data_type === 'String') {
        this.tagWriteValue = ''
        this.$nextTick(x => {
          this.$refs.tagWriteValue.focus()
        })
        this.tagWriteDialogVisible = true
      } else if (item.data_type === 'Json') {
        if (item.tag_value === '') {
          this.data1 = [{ id: +new Date(), key: '', value: '' }]
        } else {
          this.data1 = []
          const data = JSON.parse(item.tag_value)
          var index = 0
          Object.keys(data).forEach(key => {
            index++
            this.data1.push({ id: +new Date() + index, key: key, value: data[key] })
            console.log(this.data1)
          })
        }
        this.tagWriteDialogVisible1 = true
      } else if (item.data_type === 'Interf') {
        if (item.tag_value === '') {
          this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
        } else {
          this.data2 = []
          const data = JSON.parse(item.tag_value)
          if (data.length === 0) {
            this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
          } else {
            this.data2 = data
          }
        }
        this.tagWriteDialogVisible2 = true
      }
    },
    handleAddData1() {
      this.data1.push({ id: +new Date(), key: '', value: '' })
    },
    handleDelData1(id) {
      if (this.data1.length === 1) {
        this.data1.push({ id: +new Date(), key: '', value: '' })
      }
      const index = this.data1.findIndex(item => item.id === id)
      this.data1.splice(index, 1)
    },
    handleAddData2() {
      this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
    },
    handleDelData2(id) {
      if (this.data2.length === 1) {
        this.data2.push({ id: +new Date(), func_code: '', func_des: '', func_sync: 'Y', func_ng_count: '0', func_continue: 'Y', enable_flag: 'Y' })
      }
      const index = this.data2.findIndex(item => item.id === id)
      this.data2.splice(index, 1)
    },

    handleTagWrite(type) {
      var sendJson = {}
      var rowJson = []
      if (type === 'String') {
        var newRow = {
          TagKey: this.currentRow.tag_key,
          TagValue: this.tagWriteValue
        }
        rowJson.push(newRow)
      } else if (type === 'Json') {
        var data = {}
        this.data1.forEach(item => {
          if (item.key !== '') {
            data[item.key] = item.value
          }
        })
        rowJson.push({
          TagKey: this.currentRow.tag_key,
          TagValue: JSON.stringify(data)
        })
      } else if (type === 'Interf') {
        console.log(JSON.stringify(this.data2))
        rowJson.push({
          TagKey: this.currentRow.tag_key,
          TagValue: JSON.stringify(this.data2)
        })
      }
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.currentRow.tag_key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },
    handleBoolWrite(item) {
      if(this.dict.EAP_MACHINE_NAME.length > 0 && item.tag_value === '1' && (item.tag_key === 'LoadPlc/PlcConfig/OnOffLine' ||item.tag_key === 'UnLoadPlc/PlcConfig/OnOffLine')){ //这个是在快速编码里面设置的参数，广芯特有
        item.tag_value = item.tag_value === '1' ? '0' : '1'
        this.planListShow = true
        this.onofflineItem=item
        return
      }
      this.handleOnOffLineSwitch(item)
    },
    handleOnOffLineSwitch(item){
       // 说明是收板机模式
       if (item.tag_key === 'UnLoadPlc/PlcConfig/OnOffLine') {
        if (this.triggerValue === 'Y' && this.cell_id === '2' && item.tag_value !== '0' && this.boardValue !== '1') {
          item.tag_value = item.tag_value === '1' ? '0' : '1'
          this.warningMsgDialogVisible = true
          return false
        }
      }
      // 复位信号的点位  判断放收如果在线的情况下，复位按钮不能点击
      const ArrPlcConfig = ['LoadPlc/PlcConfig/FuWei', 'UnLoadPlc/PlcConfig/FuWei']
      if (ArrPlcConfig.includes(item.tag_key) && this.StationConfigData.some(e => e.tag_key.includes(item.client_code + '/PlcConfig/OnOffLine') && e.tag_value === '1')) {
        item.tag_value = item.tag_value === '1' ? '0' : '1'
        this.resetDialogFlag = true
        return
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: item.tag_key,
        TagValue: item.tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + item.tag_key.split('/')[0]
      this.sendMessage(topic, sendStr)
      // 说明是放板机模式
      if (item.tag_key === 'LoadPlc/PlcConfig/OnOffLine') {
        this.triggerValue === 'Y' && this.cell_id === '1' && item.tag_value === '0' && this.sendChangeMessage()
      }
      // 在线切离线
      console.log(item.tag_key + ':' + item.tag_value)
      // 判断离线的时候也要取消流程图
      // if (item.tag_key.indexOf('OnOffLine') >= 0 && item.tag_value === '0') {  目前这个是只有在线切离线的时候会取消所有的流程图
      if (item.tag_key.indexOf('OnOffLine') >= 0) {
        this.cannelFlowChart()
      }
      // 端口1状态
      else if (item.tag_key.indexOf('PortOn1') >= 0 && item.tag_value === '0') {
        var method = '/cell/core/flow/CoreFlowTaskListSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        const data = {
          station_id: this.station_id
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
              if (defaultQuery.data.data != null && defaultQuery.data.data.length > 0) {
                const flowTaskList = defaultQuery.data.data.filter(item => item.flow_main_des.indexOf('PORT1') >= 0)
                if (flowTaskList != null && flowTaskList.length > 0) {
                  flowTaskList.forEach(val => {
                    var method = '/cell/core/flow/CoreFlowCancel'
                    var path = ''
                    if (process.env.NODE_ENV === 'development') {
                      path = 'http://localhost:' + this.webapiPort + method
                    } else {
                      path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                    }
                    const data = {
                      me_flow_task_id: val.me_flow_task_id,
                      flow_task_status: 'AUTO_CANCEL'
                    }
                    axios
                      .post(path, data, {
                        headers: {
                          'Content-Type': 'application/json'
                        }
                      })
                      .then(res => {
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.data.code === 0) {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                        }
                      })
                      .catch(ex => {
                        this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                      })
                  })
                }
              }
            }
          })
          .catch(ex => {
            this.flowTaskData = []
            this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
          })
      }
      // 端口2状态
      else if (item.tag_key.indexOf('PortOn2') >= 0 && item.tag_value === '0') {
        var method = '/cell/core/flow/CoreFlowTaskListSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        const data = {
          station_id: this.station_id
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
              if (defaultQuery.data.data != null && defaultQuery.data.data.length > 0) {
                const flowTaskList = defaultQuery.data.data.filter(item => item.flow_main_des.indexOf('PORT2') >= 0)
                if (flowTaskList != null && flowTaskList.length > 0) {
                  flowTaskList.forEach(val => {
                    var method = '/cell/core/flow/CoreFlowCancel'
                    var path = ''
                    if (process.env.NODE_ENV === 'development') {
                      path = 'http://localhost:' + this.webapiPort + method
                    } else {
                      path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                    }
                    const data = {
                      me_flow_task_id: val.me_flow_task_id,
                      flow_task_status: 'AUTO_CANCEL'
                    }
                    axios
                      .post(path, data, {
                        headers: {
                          'Content-Type': 'application/json'
                        }
                      })
                      .then(res => {
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.data.code === 0) {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                        }
                      })
                      .catch(ex => {
                        this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                      })
                  })
                }
              }
            }
          })
          .catch(ex => {
            this.flowTaskData = []
            this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
          })
      }
      // 端口2状态
      else if (item.tag_key.indexOf('PortOn3') >= 0 && item.tag_value === '0') {
        var method = '/cell/core/flow/CoreFlowTaskListSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        const data = {
          station_id: this.station_id
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
              if (defaultQuery.data.data != null && defaultQuery.data.data.length > 0) {
                const flowTaskList = defaultQuery.data.data.filter(item => item.flow_main_des.indexOf('PORT3') >= 0)
                if (flowTaskList != null && flowTaskList.length > 0) {
                  flowTaskList.forEach(val => {
                    var method = '/cell/core/flow/CoreFlowCancel'
                    var path = ''
                    if (process.env.NODE_ENV === 'development') {
                      path = 'http://localhost:' + this.webapiPort + method
                    } else {
                      path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                    }
                    const data = {
                      me_flow_task_id: val.me_flow_task_id,
                      flow_task_status: 'AUTO_CANCEL'
                    }
                    axios
                      .post(path, data, {
                        headers: {
                          'Content-Type': 'application/json'
                        }
                      })
                      .then(res => {
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.data.code === 0) {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                        }
                      })
                      .catch(ex => {
                        this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                      })
                  })
                }
              }
            }
          })
          .catch(ex => {
            this.flowTaskData = []
            this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
          })
      }
      // 端口4状态
      else if (item.tag_key.indexOf('PortOn4') >= 0 && item.tag_value === '0') {
        var method = '/cell/core/flow/CoreFlowTaskListSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }
        const data = {
          station_id: this.station_id
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
              if (defaultQuery.data.data != null && defaultQuery.data.data.length > 0) {
                const flowTaskList = defaultQuery.data.data.filter(item => item.flow_main_des.indexOf('PORT4') >= 0)
                if (flowTaskList != null && flowTaskList.length > 0) {
                  flowTaskList.forEach(val => {
                    var method = '/cell/core/flow/CoreFlowCancel'
                    var path = ''
                    if (process.env.NODE_ENV === 'development') {
                      path = 'http://localhost:' + this.webapiPort + method
                    } else {
                      path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                    }
                    const data = {
                      me_flow_task_id: val.me_flow_task_id,
                      flow_task_status: 'AUTO_CANCEL'
                    }
                    axios
                      .post(path, data, {
                        headers: {
                          'Content-Type': 'application/json'
                        }
                      })
                      .then(res => {
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.data.code === 0) {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                        }
                      })
                      .catch(ex => {
                        this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                      })
                  })
                }
              }
            }
          })
          .catch(ex => {
            this.flowTaskData = []
            this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
          })
      }
    },
    cannelFlowChart() {
      var method = '/cell/core/flow/CoreFlowTaskListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      const data = {
        station_id: this.station_id
      }
      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
            const flowTaskList = defaultQuery.data.data
            if (flowTaskList != null && flowTaskList.length > 0) {
              flowTaskList.forEach(val => {
                var method = '/cell/core/flow/CoreFlowCancel'
                var path = ''
                if (process.env.NODE_ENV === 'development') {
                  path = 'http://localhost:' + this.webapiPort + method
                } else {
                  path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                }
                const data = {
                  me_flow_task_id: val.me_flow_task_id,
                  flow_task_status: 'AUTO_CANCEL'
                }
                axios
                  .post(path, data, {
                    headers: {
                      'Content-Type': 'application/json'
                    }
                  })
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.data.code === 0) {
                      this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                    }
                  })
                  .catch(ex => {
                    this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                  })
              })
            }
          }
        })
        .catch(ex => {
          this.flowTaskData = []
          this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    changeTrigger() {
      const resultObj = this.StationConfigData.filter(item => item.tag_key.indexOf('LoadPlc/PlcConfig/OnOffLine') > -1)[0] || {}
      if (!Object.keys(resultObj).length) {
        return
      }
      var query = {
        userName: Cookies.get('userName')
      }
      var clientCode = resultObj.client_code.indexOf('UnLoadPlc') > -1 ? 'LoadPlc' : 'UnLoadPlc'
      var readTagArray = []
      var readTag = {}
      readTag.tag_key = clientCode + '/PlcConfig/OnOffLine'
      readTagArray.push(readTag)
      // 在放板机这里获取收扳机的ip地址
      selStationIP(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            // 找出放板机对应的ip地址
            const result = defaultQuery.data.filter(item => item.cell_id == this.cell_id)[0] || {}
            // 判断没有 UnLoad说明没有收扳机的IP
            if (!Object.keys(result).length) {
              this.$message({ message: this.$t('lang_pack.stationParameter.theSettings'), type: 'error' })
              return
            }
            // 收板机的时候才调用
            if (resultObj.client_code.indexOf('UnLoadPlc') > -1) {
              var method = '/cell/core/scada/CoreScadaReadTag'
              var path = 'http://' + result.attribute3.split(':')[0] + ':' + this.webapiPort + method
              axios
                .post(path, readTagArray, {
                  headers: {
                    'Content-Type': 'application/json'
                  }
                })
                .then(results => {
                  const defaultQuerys = JSON.parse(JSON.stringify(results.data))
                  if (defaultQuerys.code === 0) {
                    if (defaultQuerys.data.length > 0) {
                      var resultData = defaultQuerys.data
                      this.boardValue = resultData[0].tag_value
                    }
                  }
                })
                .catch(ex => {
                  this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
                })
            }
            if (this.mqttChangeStatus) {
              this.clientChangeMqtt.end()
              this.mqttChangeStatus = false
            }
            var connectUrl = 'ws://' + result.attribute3 + '/mqtt'
            // mqtt连接
            this.clientChangeMqtt = mqtt.connect(connectUrl, this.optionsChangeMqtt) // 开启连接
            this.clientChangeMqtt.on('connect', e => {
              this.mqttChangeStatus = true
              // 当MQTT连接成功后，注册CLIENT相关TOPIC
              this.topicSubscribeChange('SCADA_STATUS/' + clientCode)
              this.topicSubscribeChange('SCADA_BEAT/' + clientCode)
              this.topicSubscribeChange('SCADA_CHANGE/' + readTag.tag_key)
              // MQTT连接失败
              this.clientChangeMqtt.on('error', () => {
                this.$message({
                  message: this.$t('lang_pack.vie.cnneFailed'),
                  type: 'error'
                })
                this.clientChangeMqtt.end()
              })
              // 断开发起重连(异常)
              this.clientChangeMqtt.on('reconnect', () => {
                this.$message({
                  message: this.$t('lang_pack.vie.connDisconRecon'),
                  type: 'error'
                })
              })
              this.clientChangeMqtt.on('disconnect', () => {})
              this.clientChangeMqtt.on('close', () => {})
              // 接收消息处理
              this.clientChangeMqtt.on('message', (topic, message) => {
                // 解析传过来的数据
                var jsonData = JSON.parse(message)
                if (jsonData == null) return
                if (topic.indexOf('SCADA_CHANGE/') >= 0) {
                  if (readTag.tag_key === jsonData.TagKey) {
                    this.boardValue = jsonData.TagNewValue
                    if (jsonData.TagNewValue === '0' && this.cell_id == '2') {
                      resultObj.tag_value = '0'
                    }
                    this.cell_id == '2' && this.cannelFlowChart()
                  }
                }
              })
            })
          } else {
            // 在工厂建模模块，工位维护里面
            this.$message({ message: this.$t('lang_pack.stationParameter.theReceiver'), type: 'error' })
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribeChange(topic) {
      if (!this.mqttChangeStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientChangeMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttChangeStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    sendChangeMessage() {
      if (!this.mqttChangeStatus) {
        this.$message({
          message: this.$t('lang_pack.stationParameter.notEnabled'),
          type: 'error'
        })
        return
      }
      // 这个是收板机plc的在线离线点位
      var tag_key = 'UnLoadPlc/PlcConfig/OnOffLine'
      // 再写入
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: '0'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var msg = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/UnLoadPlc'
      // qos消息发布服务质量
      this.clientChangeMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }

      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://*************:8083'  + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        for (var i = 0; i < this.StationConfigData.length; i++) {
          // 订阅主题
          this.topicSubscribe('SCADA_CHANGE/' + this.StationConfigData[i].tag_key)
        }
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          var TagKey = jsonData.TagKey
          var TagNewValue = jsonData.TagNewValue
          const tagInfo = this.StationConfigData.filter(item => item.tag_key === TagKey)
          if (tagInfo.length > 0) {
            tagInfo[0].tag_value = TagNewValue
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    handlePlanClose(done){
      this.$confirm('确认处理完成？')
        .then(_ => {
          done();
          this.onofflineItem.tag_value = this.onofflineItem.tag_value === '0' ? '1' : '0'
          this.handleOnOffLineSwitch(this.onofflineItem);
        })
        .catch(_ => {});
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 550px;
  color: #333333;
}

.table-descriptions-content {
  min-width: 150px;
}
</style>
