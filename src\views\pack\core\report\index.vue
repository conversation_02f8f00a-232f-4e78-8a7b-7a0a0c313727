<template>
  <div class="app-container">
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col :span="24">
        <el-card
          class="box-card1"
          shadow="always"
        >
          <div class="analysisValue">
            <div
              v-for="(item, index) in reportData"
              :key="index"
              class="reportVal"
            >
              <span class="val">{{ item.val }}</span>
              <span class="name">{{ item.name }}</span>
            </div>
          </div>
        </el-card>
        <el-card
          v-if="crud.props.searchToggle"
          class="box-card1"
          shadow="always"
          style="margin-top: 5px"
        >
          <el-form
            ref="query"
            :inline="true"
            size="small"
            label-width="100px"
          >
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-8 col-12">
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('view.field.plan.lotNum') + ':'">
                    <el-input
                      v-model="query.lot_num"
                      clearable
                      size="small"
                    />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                    <!-- 料号 -->
                    <el-input
                      v-model="query.model_type"
                      clearable
                      size="small"
                    />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.vie.setStatus') + ':'">
                    <!-- SET状态 -->
                    <el-select v-model="query.array_status" clearable filterable>
                      <el-option
                        v-for="item in [{id:'0',label:'OK',value:'OK'},{id:'1',label:'NG',value:'NG'}]"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.vie.task_type') + ':'">
                    <!-- 任务类型 -->
                    <el-select
                      v-model="query.task_type"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in dict.TASK_TYPE"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      >
                        <span style="float: left">{{ item.label }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                        }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-2 col-12">
                <el-form-item>
                  <rrOperation />
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card
          class="box-card1"
          shadow="always"
          style="margin-top: 5px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!--工具栏-->
              <crudOperation
                show=""
                :permission="permission"
              >
                <template slot="left">
                  <el-button
                    v-loading="downloadLoading"
                    type="primary"
                    icon="el-icon-upload2"
                    @click="exportExecl"
                  >{{ $t('lang_pack.vie.export') }}</el-button>
                  <el-date-picker
                    v-model="exportDatePeriod"
                    type="daterange"
                    range-separator="~"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd"
                  />
                </template>
              </crudOperation>
              <el-table
                ref="table"
                v-loading="crud.loading"
                border
                size="small"
                :data="crud.data"
                style="width: 100%"
                :cell-style="crud.cellStyle"
                :height="height"
                :highlight-current-row="true"
                @header-dragend="crud.tableHeaderDragend()"
                @selection-change="crud.selectionChangeHandler"
              >
                <el-table-column
                  type="selection"
                  width="55"
                />
                <!-- 时间 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  :label="$t('lang_pack.vie.time')"
                  width="140"
                  align="center"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="lot_num"
                  :label="$t('view.field.plan.lotNum')"
                  width="120"
                  align="center"
                />
                <!-- SET顺序 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_index"
                  :label="$t('lang_pack.vie.setOrder')"
                  width="80"
                  align="center"
                />
                <!-- 线扫流水号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="board_sn"
                  :label="$t('lang_pack.vie.boardSn')"
                  width="130"
                  align="center"
                />
                <!-- 包装条码 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_barcode"
                  :label="$t('lang_pack.vie.pileBarcode')"
                  width="120"
                  align="center"
                />
                <!-- SET条码 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_barcode"
                  :label="$t('lang_pack.vie.setCode')"
                  width="120"
                  align="center"
                />
                <!-- SET状态 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_status"
                  :label="$t('lang_pack.vie.setStatus')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    <el-button
                      size="small"
                      type="primary"
                      :class="scope.row.array_status == 'OK' ? 'btnGreen' : 'btnRed'"
                    >
                      {{ scope.row.array_status }}
                    </el-button>
                  </template>
                </el-table-column>
                <!-- SET分选NG代码 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_ng_code"
                  :label="$t('lang_pack.vie.arrayNgCode')"
                  width="120"
                  align="center"
                />
                <!-- SET分选NG描述 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_ng_msg"
                  :label="$t('lang_pack.vie.arrayNgMsg')"
                  width="120"
                  align="center"
                />
                <!-- 线扫SET等级 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_level"
                  :label="$t('lang_pack.vie.arrayLevel')"
                  width="120"
                  align="center"
                />
                <!-- 线扫光学点检测结果 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_mark"
                  :label="$t('lang_pack.vie.arrayMark')"
                  width="140"
                  align="center"
                />
                <!-- 线扫SET下PCS数量 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_bd_count"
                  :label="$t('lang_pack.vie.arrayBdCount')"
                  width="140"
                  align="center"
                />
                <!-- 板件判断结果 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="board_result"
                  :label="$t('lang_pack.vie.boardResult')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    <el-button
                      size="small"
                      type="primary"
                      :class="scope.row.board_result == '1' ? 'btnGreen' : (scope.row.board_result == '2' ? 'btnYellow' : (scope.row.board_result == '3' ? 'btnRed' : (scope.row.board_result == '4' ? 'btnRed' : 'btnRed')))"
                    >
                      {{ scope.row.board_result == '1' ? 'OK' : (scope.row.board_result == '2' ? 'XOUT' : (scope.row.board_result == '3' ? 'NG' : (scope.row.board_result == '4' ? $t('lang_pack.vie.NGmulCod') : 'NG'))) }}
                    </el-button>
                  </template>
                </el-table-column>
                <!-- 旋转方向 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="board_turn"
                  :label="$t('lang_pack.vie.rotationDirection')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.board_turn === '1' ? $t('lang_pack.vie.rotate') :
                      $t('lang_pack.vie.NORotate') }}
                  </template>
                </el-table-column>
                <!-- 堆叠位置 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="deposit_position"
                  :label="$t('lang_pack.vie.stackingPosition')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    <!-- {{
                      scope.row.deposit_position === 1 ? $t('lang_pack.vie.OKPosition') :
                      scope.row.deposit_position === 2 ? $t('lang_pack.vie.NGPosition') :
                      scope.row.deposit_position
                    }} -->
                    {{ dict.label.DEPOSIT_POSITION[scope.row.deposit_position] }}
                  </template>
                </el-table-column>
                <!-- 是否为XOUT分选 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="xout_flag"
                  :label="$t('lang_pack.vie.xoutFlag')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.xout_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- XOUT设定数量 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="xout_set_num"
                  :label="$t('lang_pack.vie.xoutSetNum')"
                  width="120"
                  align="center"
                />
                <!-- XOUT实际数量 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="xout_act_num"
                  :label="$t('lang_pack.vie.xoutActNum')"
                  width="120"
                  align="center"
                />
                <!-- SET正面线扫数据 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_front_info"
                  :label="$t('lang_pack.vie.arrayFrontInfo')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag
                      style="cursor:pointer"
                      :disabled="!scope.row.array_id"
                      @click="viewSetData(scope.row, 'array_front_info', $t('lang_pack.vie.setFront'))"
                    >{{ $t('view.button.view') }}</el-tag>
                  </template>
                </el-table-column>
                <!-- SET反面线扫数据 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_back_info"
                  :label="$t('lang_pack.vie.arrayBackInfo')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag
                      style="cursor:pointer"
                      :disabled="!scope.row.array_id"
                      @click="viewSetData(scope.row, 'array_back_info', $t('lang_pack.vie.setOpposite'))"
                    >{{ $t('view.button.view') }}</el-tag>
                  </template>
                </el-table-column>
                <!-- 任务类型 -->
                <el-table-column
                  prop="task_type"
                  :label="$t('lang_pack.vie.orderType')"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ dict.label.TASK_TYPE[scope.row.task_type] }}
                  </template>
                </el-table-column>
                <!-- 料号 -->
                <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="model_type"
                  :label="$t('lang_pack.vie.partNum')"
                  width="130"
                  align="center"
                /> -->
                <!-- 版本 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="model_version"
                  :label="$t('lang_pack.vie.version')"
                  width="120"
                  align="center"
                />
                <!-- SET类型 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="array_type"
                  :label="$t('lang_pack.vie.setType')"
                  width="130"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ dict.label.QR_TYPE[scope.row.array_type] }}
                  </template>
                </el-table-column>
                <!-- PCS类型 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="bd_type"
                  :label="$t('lang_pack.vie.pcsType')"
                  width="130"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ dict.label.QR_TYPE[scope.row.bd_type] }}
                  </template>
                </el-table-column>
                <!-- 板长 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="m_length"
                  :label="$t('lang_pack.vie.plateLen')"
                  width="130"
                  align="center"
                />
                <!-- 板宽 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="m_width"
                  :label="$t('lang_pack.vie.plateWid')"
                  width="130"
                  align="center"
                />
                <!-- 板厚 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="m_tickness"
                  :label="$t('lang_pack.vie.plateThi')"
                  width="130"
                  align="center"
                />
                <!-- 板重 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="m_weight"
                  :label="$t('lang_pack.vie.plateWei')"
                  width="130"
                  align="center"
                />
                <!-- 周期 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="cycle_period"
                  :label="$t('lang_pack.vie.cycle')"
                  width="130"
                  align="center"
                />
                <!-- 批号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="batch_no"
                  :label="$t('view.field.jobOrder.lot')"
                  width="130"
                  align="center"
                />
                <!-- 料号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="model_type"
                  :label="$t('view.field.jobOrder.model')"
                  width="130"
                  align="center"
                />
                <!-- 镭射批号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="laser_batch_no"
                  :label="$t('view.field.jobOrder.laserBatchNo')"
                  width="130"
                  align="center"
                />
                <!-- 排版数 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="typesetting_no"
                  :label="$t('view.field.jobOrder.typesettingNumber')"
                  width="130"
                  align="center"
                />
                <!-- BD板件条码集 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="bd_barcodes"
                  :label="$t('view.field.jobOrder.bdBarcodes')"
                  width="130"
                  align="center"
                />
                <!-- XOUT位置集 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="xout_positions"
                  :label="$t('view.field.jobOrder.xoutPositions')"
                  width="130"
                  align="center"
                />
                <!-- PCS等级集 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="bd_levels"
                  :label="$t('view.field.jobOrder.bdLevels')"
                  width="130"
                  align="center"
                />
                <!-- PCS等级判定集 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="bd_level_judgments"
                  :label="$t('view.field.jobOrder.bdLevelJudgments')"
                  width="130"
                  align="center"
                />
                <!-- 客户料号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="customer_mn"
                  :label="$t('view.field.jobOrder.customerMaterialNo')"
                  width="130"
                  align="center"
                />
                <!-- UL号 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="ul_code"
                  :label="$t('view.field.jobOrder.ul')"
                  width="130"
                  align="center"
                />
                <!-- 截取批次号 -->
                <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="split_lot"
                  :label="$t('lang_pack.vie.splitLot')"
                  width="120"
                  align="center"
                /> -->
                <!-- 截取料号 -->
                <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="split_model"
                  :label="$t('lang_pack.vie.splitModel')"
                  width="120"
                  align="center"
                /> -->
                <!-- 是否被打包使用 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="pile_use_flag"
                  :label="$t('lang_pack.vie.pileUseFlag')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.pile_use_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- 有效标识 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="enable_flag"
                  :label="$t('lang_pack.commonPage.validIdentificationt')"
                  width="130"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- 是否解绑 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="unbind_flag"
                  :label="$t('lang_pack.vie.unbindFlag')"
                  width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                    <!--取到当前单元格-->
                    {{ scope.row.unbind_flag === 'Y' ? $t('lang_pack.vie.Yes') :
                      $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!-- 解绑时间 -->
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="unbind_time"
                  :label="$t('lang_pack.vie.unbindTime')"
                  width="120"
                  align="center"
                />
                <el-table-column
                  :label="$t('lang_pack.commonPage.operate')"
                  align="center"
                  fixed="left"
                >
                  <!-- 操作 -->
                  <template slot-scope="scope">
                    <el-button
                      v-if="canUnbind(scope.row)"
                      slot="reference"
                      type="text"
                      size="small"
                      @click="unbind(scope.row)"
                    >
                      {{ $t('lang_pack.vie.unbind') }}
                    </el-button> <!-- 解绑 -->
                    <el-button
                      v-if="canDisable(scope.row)"
                      slot="reference"
                      type="text"
                      size="small"
                      @click="disable(scope.row)"
                    >
                      {{ $t('view.button.disable') }}
                    </el-button> <!-- 禁用 -->
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      @click="viewsDetails(scope.row)"
                    >{{ $t('lang_pack.vie.viewDetails')
                    }}</el-button> <!-- 查看明细 -->
                  </template>
                </el-table-column>
              </el-table>
              <!--分页组件-->
              <pagination />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      title="jsonTitle"
      width="50%"
      :visible.sync="tableDialogVisible"
    >
      <!-- <el-input v-model="jsonStr" autosize type="textarea" placeholder="Please input"  style="max-height: 450px;overflow: auto;"/> -->
    </el-dialog>
    <el-dialog
      :title="jsonTitle"
      width="50%"
      :visible.sync="jsonDialogVisible"
    >
      <el-input
        v-model="jsonStr"
        autosize
        type="textarea"
        :rows="20"
        :placeholder="$t('lang_pack.interfaceLogs.pleaseEnter')"
        style="max-height: 450px;overflow: auto;"
      />
      <span style="color:red;">{{ jsonErrorMsg }}</span>
    </el-dialog>
    <detail
      v-if="tableDialogVisible"
      ref="tableDialog"
      :array_id="array_id"
      @ok="tableDialogVisible = false"
    />
  </div>
</template>
<script>
import { downloadFile } from '@/utils/index'
import detail from './modules/setIndex'
import Cookies from 'js-cookie'
import crudSet from '@/api/pack/set'
import crudReport from '@/api/pack/report'
import { InfoSelect } from '@/api/pack/task'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  model_type: ''
}
export default {
  name: 'REPORTINDEX',
  components: { crudOperation, rrOperation, pagination, detail },
  cruds() {
    return CRUD({
      title: 'SET记录表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'array_id',
      // 排序
      sort: ['array_id desc'],
      // CRUD Method
      crudMethod: { ...crudReport },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['ENABLE_FLAG', 'TASK_TYPE', 'QR_TYPE', 'DEPOSIT_POSITION'],
  data() {
    return {
      height: document.documentElement.clientHeight - 348,
      permission: {
        add: ['admin', 'a_pack_me_array:add'],
        edit: ['edit', 'a_pack_me_array:edit'],
        del: ['del', 'a_pack_me_array:del']
      },
      reportData: [
        {
          val: '100%',
          name: this.$t('lang_pack.vie.dailyPassRate'),
          key: 'qualificationRate'
        },
        { val: 0, name: this.$t('lang_pack.vie.dailyOKQuantity'), key: 'ok' },
        { val: 0, name: this.$t('lang_pack.vie.dailyNGQuantity'), key: 'ng' },
        { val: 0, name: this.$t('lang_pack.vie.dailyHourProduct'), key: 'max' },
        {
          val: 0,
          name: this.$t('lang_pack.vie.dailyHourProductVal'),
          key: 'min'
        },
        {
          val: 0,
          name: this.$t('lang_pack.vie.dailyHourProductAve'),
          key: 'avg'
        }
      ],
      jsonStr: '',
      jsonErrorMsg: '',
      jsonDialogVisible: false,
      tableDialogVisible: false,
      jsonTitle: '',
      array_id: '',
      downloadLoading: false,
      timer: null,
      exportDatePeriod: []
    }
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 348
    }
    this.getPackIndex()
    this.timer = setInterval(() => {
      this.getPackIndex()
      this.crud.refresh()
    }, 1000 * 30)
  },
  methods: {
    getPackIndex() {
      crudReport
        .packIndex({})
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const obj = defaultQuery.data
            this.reportData.forEach((item) => {
              const objKey = item.key
              if (obj[objKey]) {
                item.val =
                  objKey === 'qualificationRate'
                    ? `${obj[objKey]}%`
                    : obj[objKey]
              }
            })
          } else {
            this.reportData.map((item) => {
              return (item.val = item.key === 'qualificationRate' ? '100%' : 0)
            })
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          this.reportData.map((item) => {
            return (item.val = item.key === 'qualificationRate' ? '100%' : 0)
          })
          this.$message({
            message: err.msg,
            type: 'error'
          })
        })
    },
    viewsDetails(row) {
      this.array_id = row.array_id
      this.tableDialogVisible = true
      this.$nextTick(() => {
        this.$refs.tableDialog.dialogVisible = true
      })
    },
    viewSetData(row, dataKey, title) {
      const query = {
        array_id: row.array_id
      }
      this.jsonTitle = title
      this.jsonStr = '{}'
      this.jsonErrorMsg = ''
      InfoSelect(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.jsonStr = JSON.stringify(JSON.parse(defaultQuery.data[0][dataKey]), null, 4)
            this.jsonDialogVisible = true
          } else {
            this.jsonStr = ''
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch((ex) => {
          this.jsonStr = ''
          this.jsonErrorMsg = ex
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    exportExecl() {
      const array_ids = this.crud.selections.map((e) => e.array_id)
      this.downloadLoading = true
      const query = {
        array_ids,
        item_date: this.exportDatePeriod,
        lot_num: this.query.lot_num,
        model_type: this.query.model_type,
        model_version: this.query.model_version,
        task_type: this.query.task_type,
        columns: {}
      }
      let columnIndex = 1
      const columns = Object.assign({}, this.$t('view.field.setRecord'))
      for (const key in columns) {
        columns[key] = columnIndex.toString().padStart(3, '0') + '-' + columns[key]
        columnIndex++
      }
      query.columns = columns
      // 如果入参的条件里面没有值，那么就把入参条件给删掉，传空对象
      Object.keys(query).forEach((key) => {
        if (
          query[key] === '' ||
          (Array.isArray(query[key]) && query[key].length === 0)
        ) {
          delete query[key]
        }
      })
      crudReport
        .down(query)
        .then((res) => {
          downloadFile(res, this.$t('lang_pack.vie.setOutside'), 'xlsx')
          this.downloadLoading = false
        })
        .catch(() => {
          this.downloadLoading = false
        })
    },
    canDisable(item) {
      return item.enable_flag === 'Y' && item.unbind_flag !== 'Y'
    },
    disable(item) {
      this.$confirm(this.$t('view.dialog.confirmToDisableThisSETRecord'), '提示', {
        confirmButtonText: this.$t('view.button.confirm'),
        cancelButtonText: this.$t('view.button.cancel'),
        type: 'warning'
      })
        .then(() => {
          crudSet
            .disable({
              user_name: Cookies.get('userName'),
              array_id: item.array_id
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
                this.crud.toQuery()
              } else {
                this.$message({
                  message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + res.msg,
                  type: 'error'
                })
              }
            })
            .catch((ex) => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    },
    // enable(item) {
    //   this.$confirm(this.$t('view.dialog.confirmToEnableThisSETRecord'), '提示', {
    //     confirmButtonText: this.$t('view.button.confirm'),
    //     cancelButtonText: this.$t('view.button.cancel'),
    //     type: 'warning'
    //   })
    //     .then(() => {
    //       crudSet
    //         .enable({
    //           user_name: Cookies.get('userName'),
    //           array_id: item.array_id
    //         })
    //         .then((res) => {
    //           if (res.code === 0) {
    //             this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
    //             this.crud.toQuery()
    //           } else {
    //             this.$message({
    //               message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + res.msg,
    //               type: 'error'
    //             })
    //           }
    //         })
    //         .catch((ex) => {
    //           this.$message({ message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + ex, type: 'error' })
    //         })
    //     })
    //     .catch(() => {})
    // },
    canUnbind(item) {
      return item.enable_flag === 'Y' && item.unbind_flag !== 'Y' && item.array_barcode != null && item.array_barcode !== ''
    },
    unbind(item) {
      this.$confirm(this.$t('lang_pack.vie.confirmUnbindAllBdTips'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudSet
            .unbind({
              user_name: Cookies.get('userName'),
              array_barcode: item.array_barcode,
              unbind_way: this.$t('lang_pack.vie.manual')
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
                this.crud.toQuery()
              } else {
                this.$message({
                  message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + res.msg,
                  type: 'error'
                })
              }
            })
            .catch((ex) => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationFailed') + ': ' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style scoped lang="less">
.app-container {
  .analysisValue {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .reportVal {
      display: flex;
      flex-direction: column;
      text-align: center;
      width: 10%;
      background: #4874cb;
      border-radius: 8px;
      color: #fff;

      .val {
        font-size: 18px;
        margin: 10px 0;
      }

      .name {
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
  }

  .btnGreen {
    background-color: #0d0 !important;
    border: none !important;
  }

  .btnRed {
    background-color: #ee1216 !important;
    border: none !important;
  }

  .btnYellow {
    background-color: #daea05 !important;
    border: none !important;
  }
}
</style>
