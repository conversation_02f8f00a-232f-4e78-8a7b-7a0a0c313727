<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <CarMainChart
          ref="chart"
          v-loading="loading"
          :nodes="nodes"
          :connections="connections"
          :width="'1651'"
          :height="'313'"
          :readonly="false"
          element-loading-text="拼命绘制流程图中"
          @editnode="handleEditNode"
          @ok="handleCutNode"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import CarMainChart from '@/components/CarMainAll/index'
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import mqtt from 'mqtt'
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'carMainIndex',
  components: {
    CarMainChart
  },
  props: {
    task_num: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      fullHeight: 0,
      diameter: 15,
      connections: [],
      thisH: null,
      loading: false,
      nodeForm: {},
      timer: null,
      // MQTT
      // MQTT配置全局变量
      clientMqtt_8083: null,
      clientMqtt_8084: null,
      clientMqtt_8085: null,
      mqttConnStatus_8083: false, // 8083MQTT连接状态(true、false)
      mqttConnStatus_8084: false, // 8084MQTT连接状态(true、false)
      mqttConnStatus_8085: false, // 8085MQTT连接状态(true、false)
      optionsMqtt8083: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
            'ScadaWeb_' +
            Cookies.get('userId') +
            '_' +
            Math.random().toString(16).substr(2, 8) + '8083', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt8084: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
            'ScadaWeb_' +
            Cookies.get('userId') +
            '_' +
            Math.random().toString(16).substr(2, 8) + '8084', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt8085: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
            'ScadaWeb_' +
            Cookies.get('userId') +
            '_' +
            Math.random().toString(16).substr(2, 8) + '8085', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 判断工位是手动还是自动
      stationKeyList: [
        'SlTransGSlPlc/PlcStatusG2/PlcS_Manual-Auto_1', // G2
        'QgTransGPmPlc/PlcStatusG3/PlcS_Manual-Auto_1', // G3
        'QgTransGCutPlc01/PlcStatusG5/PlcS_Manual-Auto_1', // G5
        'QgTransGHcPlc/PlcStatusG4/PlcS_Manual-Auto_1', // G4
        'QgTransGCutPlc01/PlcStatusG6/PlcS_Manual-Auto_1', // G6
        'QgTransGCutPlc02/PlcStatusG7/PlcS_Manual-Auto_1', // G7
        'QgTransGHcPlc/PlcStatusG8/PlcS_Manual-Auto_1', // G8
        'QgRgvPlc01/PlcStatus/PlcS_Manul-Auto', // H1
        'QgTransGCutPlc02/PlcStatusG9/PlcS_Manual-Auto_1', // G9
        'QgTransGCutPlc02/PlcStatusG10/PlcS_Manual-Auto_1', // G10
        'QgTransGHcPlc/PlcStatusG11/PlcS_Manual-Auto_1', // G11
        'FjTransGPlc01/PlcStatusG12/PlcS_Manual-Auto_1', // G12
        'FjTransGPlc01/PlcStatusG13/PlcS_Manual-Auto_1', // G13
        'FjTransGPlc01/PlcStatusG14/PlcS_Manual-Auto_1', // G14
        'FjTransGPlc01/PlcStatusG15/PlcS_Manual-Auto_1', // G15
        'FjTransGPlc02/PlcStatusG16/PlcS_Manual-Auto_1'// G16
      ],
      nodes: [
        {
          type: 'rect',
          x: 10,
          y: 20,
          borderColor: '#519bef',
          bgColor: '#00479D',
          width: 20,
          height: 20,
          strokeWidth: 2,
          title: '工位有任务'
        },
        {
          type: 'rect',
          x: 10,
          y: 70,
          borderColor: '',
          bgColor: '#6E9ACF',
          width: 20,
          height: 20,
          strokeWidth: '',
          title: '工位无任务'
        },
        {
          type: 'icon',
          x: 10,
          y: 120,
          width: 25,
          height: 25,
          title: '工位自动',
          href: require('@/assets/images/dcs/dcs_automatic.png')
        },
        {
          type: 'icon',
          x: 10,
          y: 170,
          width: 25,
          height: 25,
          title: '工位手动',
          href: require('@/assets/images/dcs/dcs_anual.png')
        },
        {
          type: 'icon',
          x: 10,
          y: 220,
          key: 'tray',
          width: 80,
          height: 80,
          title: '托盘到位',
          href: require('@/assets/images/dcs/dcs_tray.png')
        },
        // {
        //   type:'code',
        //   x:200,
        //   y:100,
        //   borderColor:'#ffffff',
        //   bgColor:'#1fa3e3',
        //   width:80,
        //   height:30,
        //   strokeWidth:3,
        //   title:'G1',
        //   goStationCode:'G2'
        // },
        {
          type: 'code',
          x: 50,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 170,
          height: 80,
          strokeWidth: 3,
          title: 'G2',
          goStationCode: 'G3',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 210,
          y: 200,
          width: 60,
          height: 80,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/computer.png')
        },
        {
          type: 'code',
          x: 290,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G3',
          goStationCode: 'G4',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 510,
          y: 140,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G5',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          direction: 'left',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 560,
          y: 130,
          width: 60,
          height: 80,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/computer2.png')
        },
        {
          type: 'code',
          x: 510,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G4',
          goStationCode: 'G5,G6,G7,G8,G9,G10,G11,G12',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 510,
          y: 440,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G6',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          direction: 'left',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 560,
          y: 430,
          width: 60,
          height: 80,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/computer2.png')
        },
        {
          type: 'code',
          x: 510,
          y: 590,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G7',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          direction: 'left',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 510,
          y: 730,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G8',
          goStationCode: 'G5,G6,G7,G9,G10,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          direction: 'left',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 730,
          y: 200,
          width: 170,
          height: 560,
          title: '',
          href: require('@/assets/images/dcs/dcs_aisle.png')
        },
        // {
        //   type:'code',
        //   x:685,
        //   y:100,
        //   borderColor:'#ffffff',
        //   bgColor:'#1fa3e3',
        //   width:80,
        //   height:30,
        //   strokeWidth:3,
        //   title:'G18',
        //   goStationCode:'G1'
        // },
        {
          type: 'squ',
          x: 750,
          y: 140,
          borderColor: '',
          bgColor: '#BBBBBB',
          width: 5,
          height: 675,
          strokeWidth: '',
          title: ''
        },
        {
          type: 'squ',
          x: 935,
          y: 140,
          borderColor: '',
          bgColor: '#BBBBBB',
          width: 5,
          height: 675,
          strokeWidth: '',
          title: ''
        },
        {
          type: 'squ',
          x: 1430,
          y: 150,
          borderColor: '',
          bgColor: '#BBBBBB',
          width: 5,
          height: 500,
          strokeWidth: '',
          title: ''
        },
        {
          type: 'squ',
          x: 1610,
          y: 150,
          borderColor: '',
          bgColor: '#BBBBBB',
          width: 5,
          height: 500,
          strokeWidth: '',
          title: ''
        },
        {
          type: 'icon',
          x: 635,
          y: 510,
          width: 180,
          height: 35,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/move_car.png')
        },
        // {
        //   type: 'icon',
        //   x: 640,
        //   y: 475,
        //   width: 180,
        //   height: 90,
        //   title: '',
        //   href: require('@/assets/images/dcs/dcs_tray.png')
        // },
        {
          type: 'code',
          x: 765,
          y: 490,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 180,
          height: 20,
          strokeWidth: 3,
          title: 'H1',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          InTask: '',
          Location: '',
          href: ''
        },
        {
          type: 'code',
          x: 1000,
          y: 140,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G9',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 1000,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G13',
          goStationCode: 'G14',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 1000,
          y: 440,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G10',
          goStationCode: 'G8,G11,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 1000,
          y: 590,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G11',
          goStationCode: 'G5,G6,G12,G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 1000,
          y: 730,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G12',
          goStationCode: 'G13',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 1230,
          y: 40,
          width: 70,
          height: 100,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car2.png')
        },
        {
          type: 'icon',
          x: 1500,
          y: 40,
          width: 70,
          height: 100,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car3.png')
        },
        {
          type: 'icon',
          x: 1125,
          y: 210,
          width: 70,
          height: 70,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car5.png')
        },
        {
          type: 'icon',
          x: 1200,
          y: 220,
          width: 90,
          height: 60,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car4.png')
        },
        {
          type: 'icon',
          x: 1530,
          y: 200,
          width: 100,
          height: 80,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car10.png')
        },
        {
          type: 'icon',
          x: 1100,
          y: 140,
          width: 620,
          height: 140,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_track.png')
        },
        {
          type: 'icon',
          x: 1100,
          y: 380,
          width: 620,
          height: 280,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_track2.png')
        },
        {
          type: 'code',
          x: 1220,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G14',
          goStationCode: 'G15',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 1120,
          y: 320,
          width: 50,
          height: 50,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car6.png')
        },
        {
          type: 'code',
          x: 1440,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#1fa3e3',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G15',
          goStationCode: 'G16',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'code',
          x: 1680,
          y: 290,
          borderColor: '#ffffff',
          bgColor: '#005ee2',
          width: 160,
          height: 80,
          strokeWidth: 3,
          title: 'G16',
          goStationCode: 'G17',
          manual_Auto_Flag: '',
          pallet_Flag: '',
          pallet_Num: '',
          SeizeSeat: '',
          Deceleration: '',
          InPlace: '',
          href: require('@/assets/images/dcs/dcs_car.png')
        },
        {
          type: 'icon',
          x: 1180,
          y: 490,
          width: 100,
          height: 100,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car7.png')
        },
        {
          type: 'icon',
          x: 1570,
          y: 490,
          width: 100,
          height: 100,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car8.png')
        },
        {
          type: 'icon',
          x: 1340,
          y: 660,
          width: 50,
          height: 50,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car6.png')
        },
        {
          type: 'icon',
          x: 1520,
          y: 660,
          width: 50,
          height: 50,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car9.png')
        },
        {
          type: 'icon',
          x: 1305,
          y: 400,
          width: 195,
          height: 90,
          title: '',
          key: 'shadow',
          href: require('@/assets/images/dcs/dcs_car11.png')
        }
        // {
        //   type:'code',
        //   x:1290,
        //   y:510,
        //   borderColor:'#ffffff',
        //   bgColor:'#1fa3e3',
        //   width:160,
        //   height:80,
        //   strokeWidth:3,
        //   title:'G17',
        //   goStationCode:'G18'
        // },
      ],
      pointData: [
        {
          'cell_id': '1',
          point: [
            { key: 'SlTransGSlPlc/PlcAlarmG2/G2_SeizeSeat', title: 'G2' },
            { key: 'SlTransGSlPlc/PlcAlarmG2/G2_Deceleration', title: 'G2' },
            { key: 'SlTransGSlPlc/PlcAlarmG2/G2_InPlace', title: 'G2' },
            { key: 'QgRgvPlc01/PlcStatus/PlcS_InTask', title: 'H1' },
            { key: 'QgRgvPlc01/PlcStatus/Location', title: 'H1' }
          ]
        },
        {
          'cell_id': '2',
          point: [
            { key: 'QgTransGPmPlc/PlcAlarmG3/G3_SeizeSeat', title: 'G3' },
            { key: 'QgTransGPmPlc/PlcAlarmG3/G3_Deceleration', title: 'G3' },
            { key: 'QgTransGPmPlc/PlcAlarmG3/G3_InPlace', title: 'G3' },
            { key: 'QgTransGCutPlc01/PlcAlarmG5/G5_SeizeSeat', title: 'G5' },
            { key: 'QgTransGCutPlc01/PlcAlarmG5/G5_Deceleration', title: 'G5' },
            { key: 'QgTransGCutPlc01/PlcAlarmG5/G5_InPlace', title: 'G5' },
            { key: 'QgTransGHcPlc/PlcAlarmG4/G4_SeizeSeat', title: 'G4' },
            { key: 'QgTransGHcPlc/PlcAlarmG4/G4_Deceleration', title: 'G4' },
            { key: 'QgTransGHcPlc/PlcAlarmG4/G4_InPlace', title: 'G4' },
            { key: 'QgTransGCutPlc01/PlcAlarmG6/G6_SeizeSeat', title: 'G6' },
            { key: 'QgTransGCutPlc01/PlcAlarmG6/G6_Deceleration', title: 'G6' },
            { key: 'QgTransGCutPlc01/PlcAlarmG6/G6_InPlace', title: 'G6' },
            { key: 'QgTransGHcPlc/PlcAlarmG8/G8_SeizeSeat', title: 'G8' },
            { key: 'QgTransGHcPlc/PlcAlarmG8/G8_Deceleration', title: 'G8' },
            { key: 'QgTransGHcPlc/PlcAlarmG8/G8_InPlace', title: 'G8' },
            { key: 'QgTransGHcPlc/PlcAlarmG11/G11_SeizeSeat', title: 'G11' },
            { key: 'QgTransGHcPlc/PlcAlarmG11/G11_Deceleration', title: 'G11' },
            { key: 'QgTransGHcPlc/PlcAlarmG11/G11_InPlace', title: 'G11' }
          ]
        },
        {
          'cell_id': '3',
          point: [
            { key: 'FjTransGPlc01/PlcAlarmG12/G12_SeizeSeat', title: 'G12' },
            { key: 'FjTransGPlc01/PlcAlarmG12/G12_Deceleration', title: 'G12' },
            { key: 'FjTransGPlc01/PlcAlarmG12/G12_InPlace', title: 'G12' },
            { key: 'FjTransGPlc01/PlcAlarmG13/G13_SeizeSeat', title: 'G13' },
            { key: 'FjTransGPlc01/PlcAlarmG13/G13_Deceleration', title: 'G13' },
            { key: 'FjTransGPlc01/PlcAlarmG13/G13_InPlace', title: 'G13' },
            { key: 'FjTransGPlc01/PlcAlarmG14/G14_SeizeSeat', title: 'G14' },
            { key: 'FjTransGPlc01/PlcAlarmG14/G14_Deceleration', title: 'G14' },
            { key: 'FjTransGPlc01/PlcAlarmG14/G14_InPlace', title: 'G14' },
            { key: 'FjTransGPlc01/PlcAlarmG15/G15_SeizeSeat', title: 'G15' },
            { key: 'FjTransGPlc01/PlcAlarmG15/G15_Deceleration', title: 'G15' },
            { key: 'FjTransGPlc01/PlcAlarmG15/G15_InPlace', title: 'G15' },
            { key: 'FjTransGPlc02/PlcAlarmG16/G16_SeizeSeat', title: 'G16' },
            { key: 'FjTransGPlc02/PlcAlarmG16/G16_Deceleration', title: 'G16' },
            { key: 'FjTransGPlc02/PlcAlarmG16/G16_InPlace', title: 'G16' }
          ]
        }
      ],
      stationArrFlag: true
    }
  },
  watch: {
    fullHeight: {
      immediate: true,
      handler() {
        this.nodes.map(item => {
          if (item.type === 'code' || (item.type === 'icon' && item.title === '')) {
            item.y = item.y + this.fullHeight
          }
        })
      }
    }
    // task_num:{
    //   immediate: true,
    //   deep: true,
    //   handler() {
    //     this.nodes.map(item=>{
    //       this.getHighlight(item)
    //     })
    //   }
    // }
  },
  mounted() {
    var that = this
    this.getPointData()
    this.timer = setInterval(() => {
      this.getPointData()
    }, 5000)
    window.onresize = () => {
      if (document.documentElement.clientHeight >= 1000) {
        that.fullHeight = 100
      } else {
        that.fullHeight = -100
      }
    }
  },
  created() {
    Promise.all(this.pointData.map(e => {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: e.cell_id,
        current_ip: window.location.hostname
      }
      return selCellIP(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res.result))
        const result = JSON.parse(defaultQuery)
        if (result === '' || result === undefined || result == null) {
          return null
        }
        return { ip: result.ip, port: result.webapi_port }
      })
    })).then(results => {
      // 处理所有结果
      results.forEach((result, index) => {
        if (result) {
          this.getTagValue(result.ip, result.port, index)
        }
      })
    }).catch(error => {
      // 处理错误
      console.error('An error occurred:', error)
    })
  },
  beforeDestory() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    // 离开此页面时销毁mqtt链接
    if (this.clientMqtt_8083) {
      this.clientMqtt_8083.end()
      this.mqttConnStatus_8083 = false
    }
    if (this.clientMqtt_8084) {
      this.clientMqtt_8084.end()
      this.mqttConnStatus_8084 = false
    }
    if (this.clientMqtt_8085) {
      this.clientMqtt_8085.end()
      this.mqttConnStatus_8085 = false
    }
  },
  methods: {
    selCellIP(query) {
      return new Promise((resolve, reject) => {
        // 模拟异步操作
        setTimeout(() => {
          resolve({ result: { ip: '***********', webapi_port: '8080' }})
        }, 1000)
      })
    },
    getTagValue(ip, port, index) {
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + ip + ':' + port + method
        // path = 'http://*************:8089' + method
      }
      const readTagArray = []
      this.pointData[index].point.forEach(item => {
        readTagArray.push({ tag_key: item.key })
      })
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach(i => {
                this.pointData[index].point.forEach(k => {
                  if (i.tag_key === k.key) {
                    const value = i.tag_key.split('_')[1] ? i.tag_key.split('_')[1] : 'Location'
                    this.nodes.find(d => d.title === k.title)[value] = i.tag_value
                  }
                })
              })
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    getPointData() {
      crudMainPage.stationMinStatus({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.nodes.map(item => {
              const obj = defaultQuery.data.find(e => e.station_code === item.title)
              item = Object.assign(item, obj)
            })
            // 从这里拿到三个单元的端口加IP
            if (this.stationArrFlag) {
              this.stationArrFlag = false
              var uniqueArr = defaultQuery.data.reduce((acc, current) => {
                const x = acc.find(item => item.cell_mqtt_port === current.cell_mqtt_port && item.server_host_1 === current.server_host_1)
                if (!x) {
                  return acc.concat([current])
                } else {
                  return acc
                }
              }, [])
              // 获取scada每个mqtt的点位 的ip + prot ,port一致的，ip也一致
              var result = uniqueArr.map(item => ({ cell_mqtt_port: item.cell_mqtt_port, server_host_1: item.server_host_1 }))
              this.$nextTick(() => {
                for (let i = 0; i < result.length; i++) {
                  this[`toStartWatch_${result[i].cell_mqtt_port}`](result[i].server_host_1, result[i].cell_mqtt_port)
                }
              })
            }
            this.connections = []
          }
        } else {
          this.$message({
            message: defaultQuery.msg || '获取点位异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.$message({
            message: '获取点位异常',
            type: 'error'
          })
        })
    },
    handleCutNode(data) {
      this.$emit('ok', data)
    },
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
    getHighlight(item) {
      if (this.task_num && this.task_num === item.task_num) {
        item.status = 'LOCKSTATUS'
        this.$refs.chart.renderNode(item, false)
      } else {
        item.status = ''
        this.$refs.chart.renderNode(item, false)
      }
    },
    // ----------------------------------【MQTT】----------------------------------
    // 8083启动监控
    toStartWatch_8083(ip, port) {
      var connectUrl = 'ws://' + ip + ':' + port + '/mqtt'
      // this.GetTagValue(result.ip,result.webapi_port)
      // var connectUrl ="ws://*************:8083/mqtt";
      // connectUrl=MQTT_SERVICE;
      console.log('拼接URL：' + connectUrl)
      this.clientMqtt_8083 = mqtt.connect(connectUrl, this.optionsMqtt8083) // 开启连接
      this.clientMqtt_8083.on('connect', (e) => {
        this.mqttConnStatus_8083 = true
        // 8083端口的点位只有一个
        const Arr = [
          // 判断占位
          'SlTransGSlPlc/PlcAlarmG2/G2_SeizeSeat',
          // 判断减速
          'SlTransGSlPlc/PlcAlarmG2/G2_Deceleration',
          // 判断到位
          'SlTransGSlPlc/PlcAlarmG2/G2_InPlace',
          // 判断手动自动点位
          'SlTransGSlPlc/PlcStatusG2/PlcS_Manual-Auto_1', // G2
          // 判断托盘是否到位的点位
          'SlTransGSlPlc/PlcStatusG2/PlcS_PalletOver', // G2
          // 托盘号点位
          'SlTransGSlPlc/PlcStatusG2/PlcS_PalletNum', // G2
          // 有无任务点位
          'QgRgvPlc01/PlcStatus/PlcS_InTask', // H1
          // 横移车目标工位点位
          'QgRgvPlc01/PlcStatus/Location'//  H1
        ]
        for (let i = 0; i < Arr.length; i++) {
          this.topicSubscribe_8083('SCADA_CHANGE/' + Arr[i])
        }
      })
      // MQTT连接失败
      this.clientMqtt_8083.on('error', (error) => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })

        this.clientMqtt_8083.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt_8083.on('reconnect', (error) => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt_8083.on('disconnect', (error) => {
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      this.clientMqtt_8083.on('close', () => {
        this.clientMqtt_8083.end()
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt_8083.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    // 8084启动监控
    toStartWatch_8084(ip, port) {
      var connectUrl = 'ws://' + ip + ':' + port + '/mqtt'
      // this.GetTagValue(result.ip,result.webapi_port)
      // var connectUrl ="ws://*************:8083/mqtt";
      console.log('拼接URL：' + connectUrl)
      this.clientMqtt_8084 = mqtt.connect(connectUrl, this.optionsMqtt8084) // 开启连接
      this.clientMqtt_8084.on('connect', (e) => {
        this.mqttConnStatus_8084 = true
        const Arr = [
          // 判断占位
          'QgTransGPmPlc/PlcAlarmG3/G3_SeizeSeat', // G3
          'QgTransGCutPlc01/PlcAlarmG5/G5_SeizeSeat', // G5
          'QgTransGHcPlc/PlcAlarmG4/G4_SeizeSeat', // G4
          'QgTransGCutPlc01/PlcAlarmG6/G6_SeizeSeat', // G6
          'QgTransGHcPlc/PlcAlarmG8/G8_SeizeSeat', // G8
          'QgTransGHcPlc/PlcAlarmG11/G11_SeizeSeat', // G11
          // 判断减速
          'QgTransGPmPlc/PlcAlarmG3/G3_Deceleration', // G3
          'QgTransGCutPlc01/PlcAlarmG5/G5_Deceleration', // G5
          'QgTransGHcPlc/PlcAlarmG4/G4_Deceleration', // G4
          'QgTransGCutPlc01/PlcAlarmG6/G6_Deceleration', // G6
          'QgTransGHcPlc/PlcAlarmG8/G8_Deceleration', // G8
          'QgTransGHcPlc/PlcAlarmG11/G11_Deceleration', // G11
          // 判断到位
          'QgTransGPmPlc/PlcAlarmG3/G3_InPlace', // G3
          'QgTransGCutPlc01/PlcAlarmG5/G5_InPlace', // G5
          'QgTransGHcPlc/PlcAlarmG4/G4_InPlace', // G4
          'QgTransGCutPlc01/PlcAlarmG6/G6_InPlace', // G6
          'QgTransGHcPlc/PlcAlarmG8/G8_InPlace', // G8
          'QgTransGHcPlc/PlcAlarmG11/G11_InPlace', // G11
          // 判断手动自动点位
          'QgTransGPmPlc/PlcStatusG3/PlcS_Manual-Auto_1', // G3
          'QgTransGCutPlc01/PlcStatusG5/PlcS_Manual-Auto_1', // G5
          'QgTransGHcPlc/PlcStatusG4/PlcS_Manual-Auto_1', // G4
          'QgTransGCutPlc01/PlcStatusG6/PlcS_Manual-Auto_1', // G6
          'QgTransGCutPlc02/PlcStatusG7/PlcS_Manual-Auto_1', // G7
          'QgTransGHcPlc/PlcStatusG8/PlcS_Manual-Auto_1', // G8
          'QgRgvPlc01/PlcStatus/PlcS_Manul-Auto', // H1
          'QgTransGCutPlc02/PlcStatusG9/PlcS_Manual-Auto_1', // G9
          'QgTransGCutPlc02/PlcStatusG10/PlcS_Manual-Auto_1', // G10
          'QgTransGHcPlc/PlcStatusG11/PlcS_Manual-Auto_1', // G11
          // 判断托盘是否到位的点位
          'QgTransGPmPlc/PlcStatusG3/PlcS_PalletOver', // G3
          'QgTransGHcPlc/PlcStatusG4/PlcS_PalletOver', // G4
          'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletOver', // G5
          'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletOver', // G6
          'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletOver', // G7
          'QgTransGHcPlc/PlcStatusG8/PlcS_PalletOver', // G8
          'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletOver', // G9
          'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletOver', // G10
          'QgTransGHcPlc/PlcStatusG11/PlcS_PalletOver', // G11
          'QgRgvPlc01/PlcStatus/PlcS_PalletOn', // H1
          // 托盘号点位  H1没有托盘号
          'QgTransGPmPlc/PlcStatusG3/PlcS_PalletNum', // G3
          'QgTransGHcPlc/PlcStatusG4/PlcS_PalletNum', // G4
          'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletNum', // G5
          'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletNum', // G6
          'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletNum', // G7
          'QgTransGHcPlc/PlcStatusG8/PlcS_PalletNum', // G8
          'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletNum', // G9
          'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletNum', // G10
          'QgTransGHcPlc/PlcStatusG11/PlcS_PalletNum'// G11
        ]
        for (let i = 0; i < Arr.length; i++) {
          this.topicSubscribe_8084('SCADA_CHANGE/' + Arr[i])
        }
      })
      // MQTT连接失败
      this.clientMqtt_8084.on('error', (error) => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })

        this.clientMqtt_8084.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt_8084.on('reconnect', (error) => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt_8084.on('disconnect', (error) => {
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      this.clientMqtt_8084.on('close', () => {
        this.clientMqtt_8084.end()
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt_8084.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    toStartWatch_8085(ip, port) {
      var connectUrl = 'ws://' + ip + ':' + port + '/mqtt'
      // this.GetTagValue(result.ip,result.webapi_port)
      // var connectUrl ="ws://*************:8083/mqtt";
      console.log('拼接URL：' + connectUrl)
      this.clientMqtt_8085 = mqtt.connect(connectUrl, this.optionsMqtt8085) // 开启连接
      this.clientMqtt_8085.on('connect', (e) => {
        this.mqttConnStatus_8085 = true
        const Arr = [
          // 判断占位
          'FjTransGPlc01/PlcAlarmG12/G12_SeizeSeat', // G12
          'FjTransGPlc01/PlcAlarmG13/G13_SeizeSeat', // G13
          'FjTransGPlc01/PlcAlarmG14/G14_SeizeSeat', // G14
          'FjTransGPlc01/PlcAlarmG15/G15_SeizeSeat', // G15
          'FjTransGPlc02/PlcAlarmG16/G16_SeizeSeat', // G16
          // 判断减速
          'FjTransGPlc01/PlcAlarmG12/G12_Deceleration', // G12
          'FjTransGPlc01/PlcAlarmG13/G13_Deceleration', // G13
          'FjTransGPlc01/PlcAlarmG14/G14_Deceleration', // G14
          'FjTransGPlc01/PlcAlarmG15/G15_Deceleration', // G15
          'FjTransGPlc02/PlcAlarmG16/G16_Deceleration', // G16
          // 判断到位
          'FjTransGPlc01/PlcAlarmG12/G12_InPlace', // G12
          'FjTransGPlc01/PlcAlarmG13/G13_InPlace', // G13
          'FjTransGPlc01/PlcAlarmG14/G14_InPlace', // G14
          'FjTransGPlc01/PlcAlarmG15/G15_InPlace', // G15
          'FjTransGPlc02/PlcAlarmG16/G16_InPlace', // G16
          // 判断手动自动点位
          'FjTransGPlc01/PlcStatusG12/PlcS_Manual-Auto_1', // G12
          'FjTransGPlc01/PlcStatusG13/PlcS_Manual-Auto_1', // G13
          'FjTransGPlc01/PlcStatusG14/PlcS_Manual-Auto_1', // G14
          'FjTransGPlc01/PlcStatusG15/PlcS_Manual-Auto_1', // G15
          'FjTransGPlc02/PlcStatusG16/PlcS_Manual-Auto_1', // G16
          // 判断托盘是否到位的点位
          'FjTransGPlc01/PlcStatusG12/PlcS_PalletOver', // G12
          'FjTransGPlc01/PlcStatusG13/PlcS_PalletOver', // G13
          'FjTransGPlc01/PlcStatusG14/PlcS_PalletOver', // G14
          'FjTransGPlc01/PlcStatusG15/PlcS_PalletOver', // G15
          'FjTransGPlc02/PlcStatusG16/PlcS_PalletOver', // G16
          // 托盘号点位  H1没有托盘号
          'FjTransGPlc01/PlcStatusG12/PlcS_PalletNum', // G12
          'FjTransGPlc01/PlcStatusG13/PlcS_PalletNum', // G13
          'FjTransGPlc01/PlcStatusG14/PlcS_PalletNum', // G14
          'FjTransGPlc01/PlcStatusG15/PlcS_PalletNum', // G15
          'FjTransGPlc02/PlcStatusG16/PlcS_PalletNum'// G16
        ]
        for (let i = 0; i < Arr.length; i++) {
          this.topicSubscribe_8085('SCADA_CHANGE/' + Arr[i])
        }
      })
      // MQTT连接失败
      this.clientMqtt_8085.on('error', (error) => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })

        this.clientMqtt_8085.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt_8085.on('reconnect', (error) => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt_8085.on('disconnect', (error) => {
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      this.clientMqtt_8085.on('close', () => {
        this.clientMqtt_8085.end()
        this.$message({
          message: '服务连接断开',
          type: 'error'
        })
      })
      // 接收消息处理
      this.clientMqtt_8085.on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue
      // 有无任务点位
      const tagTask = {
        'QgRgvPlc01/PlcStatus/PlcS_InTask': 'H1'
      }
      // 横移车目标工位
      const tagLocation = {
        'QgRgvPlc01/PlcStatus/Location': 'H1'
      }
      // 判断占位
      const tagToSeat = {
        'SlTransGSlPlc/PlcAlarmG2/G2_SeizeSeat': 'G2', // G2
        'QgTransGPmPlc/PlcAlarmG3/G3_SeizeSeat': 'G3', // G3
        'QgTransGCutPlc01/PlcAlarmG5/G5_SeizeSeat': 'G5', // G5
        'QgTransGHcPlc/PlcAlarmG4/G4_SeizeSeat': 'G4', // G4
        'QgTransGCutPlc01/PlcAlarmG6/G6_SeizeSeat': 'G6', // G6
        'QgTransGHcPlc/PlcAlarmG8/G8_SeizeSeat': 'G8', // G8
        'QgTransGHcPlc/PlcAlarmG11/G11_SeizeSeat': 'G11', // G11
        'FjTransGPlc01/PlcAlarmG12/G12_SeizeSeat': 'G12', // G12
        'FjTransGPlc01/PlcAlarmG13/G13_SeizeSeat': 'G13', // G13
        'FjTransGPlc01/PlcAlarmG14/G14_SeizeSeat': 'G14', // G14
        'FjTransGPlc01/PlcAlarmG15/G15_SeizeSeat': 'G15', // G15
        'FjTransGPlc02/PlcAlarmG16/G16_SeizeSeat': 'G16' // G16
      }
      // 判断减速
      const tagToDecele = {
        'SlTransGSlPlc/PlcAlarmG2/G2_Deceleration': 'G2',
        'QgTransGPmPlc/PlcAlarmG3/G3_Deceleration': 'G3', // G3
        'QgTransGCutPlc01/PlcAlarmG5/G5_Deceleration': 'G5', // G5
        'QgTransGHcPlc/PlcAlarmG4/G4_Deceleration': 'G4', // G4
        'QgTransGCutPlc01/PlcAlarmG6/G6_Deceleration': 'G6', // G6
        'QgTransGHcPlc/PlcAlarmG8/G8_Deceleration': 'G8', // G8
        'QgTransGHcPlc/PlcAlarmG11/G11_Deceleration': 'G11', // G11
        'FjTransGPlc01/PlcAlarmG12/G12_Deceleration': 'G12', // G12
        'FjTransGPlc01/PlcAlarmG13/G13_Deceleration': 'G13', // G13
        'FjTransGPlc01/PlcAlarmG14/G14_Deceleration': 'G14', // G14
        'FjTransGPlc01/PlcAlarmG15/G15_Deceleration': 'G15', // G15
        'FjTransGPlc02/PlcAlarmG16/G16_Deceleration': 'G16' // G16
      }
      // 判断到位
      const tagToInplace = {
        'SlTransGSlPlc/PlcAlarmG2/G2_InPlace': 'G2',
        'QgTransGPmPlc/PlcAlarmG3/G3_InPlace': 'G3', // G3
        'QgTransGCutPlc01/PlcAlarmG5/G5_InPlace': 'G5', // G5
        'QgTransGHcPlc/PlcAlarmG4/G4_InPlace': 'G4', // G4
        'QgTransGCutPlc01/PlcAlarmG6/G6_InPlace': 'G6', // G6
        'QgTransGHcPlc/PlcAlarmG8/G8_InPlace': 'G8', // G8
        'QgTransGHcPlc/PlcAlarmG11/G11_InPlace': 'G11', // G11
        'FjTransGPlc01/PlcAlarmG12/G12_InPlace': 'G12', // G12
        'FjTransGPlc01/PlcAlarmG13/G13_InPlace': 'G13', // G13
        'FjTransGPlc01/PlcAlarmG14/G14_InPlace': 'G14', // G14
        'FjTransGPlc01/PlcAlarmG15/G15_InPlace': 'G15', // G15
        'FjTransGPlc02/PlcAlarmG16/G16_InPlace': 'G16' // G16
      }
      const tagToNode = {
        // 判断手动自动
        'SlTransGSlPlc/PlcStatusG2/PlcS_Manual-Auto_1': 'G2',
        'QgTransGPmPlc/PlcStatusG3/PlcS_Manual-Auto_1': 'G3',
        'QgTransGCutPlc01/PlcStatusG5/PlcS_Manual-Auto_1': 'G5',
        'QgTransGHcPlc/PlcStatusG4/PlcS_Manual-Auto_1': 'G4',
        'QgTransGCutPlc01/PlcStatusG6/PlcS_Manual-Auto_1': 'G6',
        'QgTransGCutPlc02/PlcStatusG7/PlcS_Manual-Auto_1': 'G7',
        'QgTransGHcPlc/PlcStatusG8/PlcS_Manual-Auto_1': 'G8',
        'QgRgvPlc01/PlcStatus/PlcS_Manul-Auto': 'H1',
        'QgTransGCutPlc02/PlcStatusG9/PlcS_Manual-Auto_1': 'G9',
        'FjTransGPlc01/PlcStatusG13/PlcS_Manual-Auto_1': 'G13',
        'QgTransGCutPlc02/PlcStatusG10/PlcS_Manual-Auto_1': 'G10',
        'QgTransGHcPlc/PlcStatusG11/PlcS_Manual-Auto_1': 'G11',
        'FjTransGPlc01/PlcStatusG12/PlcS_Manual-Auto_1': 'G12',
        'FjTransGPlc01/PlcStatusG14/PlcS_Manual-Auto_1': 'G14',
        'FjTransGPlc01/PlcStatusG15/PlcS_Manual-Auto_1': 'G15',
        'FjTransGPlc02/PlcStatusG16/PlcS_Manual-Auto_1': 'G16'
      }
      const tagToNodeOver = {
        // 判断托盘是否到位的点位
        'SlTransGSlPlc/PlcStatusG2/PlcS_PalletOver': 'G2',
        'QgTransGPmPlc/PlcStatusG3/PlcS_PalletOver': 'G3',
        'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletOver': 'G5',
        'QgTransGHcPlc/PlcStatusG4/PlcS_PalletOver': 'G4',
        'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletOver': 'G6',
        'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletOver': 'G7',
        'QgTransGHcPlc/PlcStatusG8/PlcS_PalletOver': 'G8',
        'QgRgvPlc01/PlcStatus/PlcS_PalletOn': 'H1',
        'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletOver': 'G9',
        'FjTransGPlc01/PlcStatusG13/PlcS_PalletOver': 'G13',
        'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletOver': 'G10',
        'QgTransGHcPlc/PlcStatusG11/PlcS_PalletOver': 'G11',
        'FjTransGPlc01/PlcStatusG12/PlcS_PalletOver': 'G12',
        'FjTransGPlc01/PlcStatusG14/PlcS_PalletOver': 'G14',
        'FjTransGPlc01/PlcStatusG15/PlcS_PalletOver': 'G15',
        'FjTransGPlc02/PlcStatusG16/PlcS_PalletOver': 'G16'
      }
      const tagToNodeNum = {
        // 托盘号点位  H1没有托盘号
        'SlTransGSlPlc/PlcStatusG2/PlcS_PalletNum': 'G2',
        'QgTransGPmPlc/PlcStatusG3/PlcS_PalletNum': 'G3',
        'QgTransGCutPlc01/PlcStatusG5/PlcS_PalletNum': 'G5',
        'QgTransGHcPlc/PlcStatusG4/PlcS_PalletNum': 'G4',
        'QgTransGCutPlc01/PlcStatusG6/PlcS_PalletNum': 'G6',
        'QgTransGCutPlc02/PlcStatusG7/PlcS_PalletNum': 'G7',
        'QgTransGHcPlc/PlcStatusG8/PlcS_PalletNum': 'G8',
        'QgTransGCutPlc02/PlcStatusG9/PlcS_PalletNum': 'G9',
        'FjTransGPlc01/PlcStatusG13/PlcS_PalletNum': 'G13',
        'QgTransGCutPlc02/PlcStatusG10/PlcS_PalletNum': 'G10',
        'QgTransGHcPlc/PlcStatusG11/PlcS_PalletNum': 'G11',
        'FjTransGPlc01/PlcStatusG12/PlcS_PalletNum': 'G12',
        'FjTransGPlc01/PlcStatusG14/PlcS_PalletNum': 'G14',
        'FjTransGPlc01/PlcStatusG15/PlcS_PalletNum': 'G15',
        'FjTransGPlc02/PlcStatusG16/PlcS_PalletNum': 'G16'
      }
      // for (const key in tagToNode) {
      //   if (TagKey === key) {
      //     const nodeTitle = tagToNode[key]
      //     this.nodes.find(e => e.title === nodeTitle).manual_Auto_Flag = TagNewValue || ''
      //     break
      //   }
      // }
      // for (const key in tagToNodeOver) {
      //   if (TagKey === key) {
      //     const nodeTitle = tagToNodeOver[key]
      //     this.nodes.find(e => e.title === nodeTitle).pallet_Flag = TagNewValue || ''
      //     break
      //   }
      // }
      // for (const key in tagToNodeNum) {
      //   if (TagKey === key) {
      //     const nodeTitle = tagToNodeOver[key]
      //     this.nodes.find(e => e.title === nodeTitle).pallet_Num = TagNewValue || ''
      //     break
      //   }
      // }
      // for (const key in tagToSeat) {
      //   if (TagKey === key) {
      //     const nodeTitle = tagToSeat[key]
      //     if (this.nodes.find(e => e.title === nodeTitle).SeizeSeat) {
      //       this.nodes.find(e => e.title === nodeTitle).SeizeSeat = TagNewValue || ''
      //     }
      //     break
      //   }
      // }
      this.updateNodeProperty(tagTask, 'InTask', TagKey, TagNewValue)
      this.updateNodeProperty(tagLocation, 'Location', TagKey, TagNewValue)
      this.updateNodeProperty(tagToNode, 'manual_Auto_Flag', TagKey, TagNewValue)
      this.updateNodeProperty(tagToNodeOver, 'pallet_Flag', TagKey, TagNewValue)
      this.updateNodeProperty(tagToNodeNum, 'pallet_Num', TagKey, TagNewValue)
      this.updateNodeProperty(tagToSeat, 'SeizeSeat', TagKey, TagNewValue)
      this.updateNodeProperty(tagToDecele, 'Deceleration', TagKey, TagNewValue)
      this.updateNodeProperty(tagToInplace, 'InPlace', TagKey, TagNewValue)
    },
    updateNodeProperty(tagToNodeMap, nodeProperty, TagKey, TagNewValue) {
      const key = Object.keys(tagToNodeMap).find(key => key === TagKey)
      if (key) {
        const nodeTitle = tagToNodeMap[key]
        const node = this.nodes.find(e => e.title === nodeTitle)
        if (node.hasOwnProperty(nodeProperty)) {
          node[nodeProperty] = TagNewValue || ''
        }
      }
    },
    // 8083端口订阅主题函数
    topicSubscribe_8083(topic) {
      if (!this.mqttConnStatus_8083) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt_8083.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus_8083) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 8084端口订阅主题函数
    topicSubscribe_8084(topic) {
      if (!this.mqttConnStatus_8084) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt_8084.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus_8084) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 8084端口订阅主题函数
    topicSubscribe_8085(topic) {
      if (!this.mqttConnStatus_8085) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt_8085.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus_8085) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>

  <style lange="less" scoped>
  .app-container{
     padding: 0 !important;
     width: 100%;
     height: 100%;
  }
  </style>
