<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="托盘编码:">
                                <!-- 托盘编码 -->
                                <el-input v-model="query.pallet_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="托盘类型:">
                                <!-- 托盘类型 -->
                                <fastCode fastcode_group_code="PALLET_TYPE" :fastcode_code.sync="query.pallet_type" control_type="select" size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="有效标识：">
                                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />
            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item label="托盘编号" prop="pallet_num">
                                <!-- 托盘编号 -->
                                <el-input v-model="form.pallet_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="托盘类型" prop="pallet_type">
                                <!-- 托盘类型 -->
                                <fastCode fastcode_group_code="PALLET_TYPE" :fastcode_code.sync="form.pallet_type" control_type="select" size="small" />
                            </el-form-item>
                            <el-form-item label="维修值" prop="maintenance_value">
                                <!--  维修值-->
                                <el-input v-model="form.maintenance_value" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.recipequality.enableFlag')" prop="enable_flag">
                                <!-- 有效标识 -->
                                    <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>
                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" fixed/>
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="5" size="small" border>
                                <el-descriptions-item label="托盘编号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{props.row.pallet_num}}</el-descriptions-item>
                                <el-descriptions-item label="托盘类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.PALLET_TYPE[props.row.pallet_type] }}</el-descriptions-item>
                                <el-descriptions-item label="当前使用次数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.usage_times }}</el-descriptions-item>
                                <el-descriptions-item label="已维修次数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.maintenance_times }}</el-descriptions-item>
                                <el-descriptions-item label="维修值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.maintenance_value }}</el-descriptions-item>
                                <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <!-- 托盘编号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="pallet_num"
                            label="托盘编号" width="220" align='center'/>
                        <!-- 托盘类型 -->
                        <el-table-column :show-overflow-tooltip="true" prop="pallet_type"
                            label="托盘类型" width="220" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.PALLET_TYPE[scope.row.pallet_type] }}
                            </template>
                        </el-table-column>
                        <!-- 当前使用次数 -->
                        <el-table-column :show-overflow-tooltip="true" prop="usage_times"
                            label="当前使用次数" width="220" align='center'/>
                        <!-- 已维修次数 -->
                        <el-table-column :show-overflow-tooltip="true" prop="maintenance_times"
                            label="已维修次数" width="220" align='center' />
                        <!-- 维修值 -->
                        <el-table-column :show-overflow-tooltip="true" prop="maintenance_value"
                            label="维修值" width="220" align='center' />
                        <el-table-column :label="$t('lang_pack.recipequality.enableFlag')" align="center" width="100"
                            prop="enable_flag">
                            <!-- 有效标识 -->
                            <template slot-scope="scope">
                                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                            </template>
                        </el-table-column>
                        <!-- Table单条操作-->
                        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center"  fixed="right">
                            <!-- 操作 -->
                            <template slot-scope="scope">
                                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudTaskFmodPallet from '@/api/dcs/core/aps/taskFmodPallet'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    created_by:'',
    creation_date:'',
    enable_flag:'Y',
    last_update_date:'',
    last_updated_by:'',
    maintenance_times:'',
    maintenance_value:'',
    pallet_id:'',
    pallet_num:'',
    pallet_type:'',
    usage_times:'',
}
export default {
    name: 'WEB_TASK_FMOD_PALLET',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '托盘管理',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'pallet_id',
            // 排序
            sort: ['pallet_id asc'],
            // CRUD Method
            crudMethod: { ...crudTaskFmodPallet },
            // 按钮显示
            optShow: {
                add: true,
                edit: true,
                del: true,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'TASK_FMOD_PALLET:add'],
                edit: ['admin', 'TASK_FMOD_PALLET:edit'],
                del: ['admin', 'TASK_FMOD_PALLET:del']
            },
            rules: {
                pallet_num: [{ required: true, message: '请选择托盘编码', trigger: 'blur' }],
                pallet_type: [{ required: true, message: '请选择托盘类型', trigger: 'blur' }],
                pallet_type: [{ required: true, message: '请选择维修值', trigger: 'blur' }],
                enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }],
            },
        }
    },
    // 数据字典
    dicts: ['ENABLE_FLAG','PALLET_TYPE'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
    }
}
</script>
  