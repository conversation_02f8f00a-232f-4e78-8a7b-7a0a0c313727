<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          载具号
        </template>
        <el-input ref="webPalletNum" v-model="webPalletNum" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          批次信息
        </template>
        <el-table ref="table1" style="width: 100%;margin-top:10px;" :data="lot_list" :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" :highlight-current-row="true">
          <el-table-column type="index" width="50" align="center" label="#" />
          <el-table-column :show-overflow-tooltip="true" label="批次号" prop="lot_num">
            <template slot-scope="scope">
              <el-input :ref="'mark'+scope.$index" v-model="scope.row.lot_num" clearable size="mini" style="width: 100%" @keyup.enter.native="nextFocus(scope.$index)" />
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" label="数量" prop="qty">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty" clearable size="mini" style="width: 100%" />
            </template>
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo">上报</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
  },
  // 数据模型
  data() {
    return {
      lot_list: [{ id: 1, lot_num: '', qty: '0' }, { id: 2, lot_num: '', qty: '0' }, { id: 3, lot_num: '', qty: '0' }, { id: 4, lot_num: '', qty: '0' }, { id: 5, lot_num: '', qty: '0' }],
      webPalletNum: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webPalletNum.focus()
    })
  },
  methods: {
    // 自动聚焦下到一行
    nextFocus(index) {
      if (index >= 4) return
      this.$refs['mark' + (index + 1)].select()
      this.$refs['mark' + (index + 1)].focus()
    },
    handleSendInfo() {
      var lot_list2 = []
      this.lot_list.forEach(item => {
        if (item.lot_num !== '' && item.qty !== '') {
          lot_list2.push({ lot_id: item.lot_num.toString(), lot_count: item.qty.toString() })
        }
      })
      if (lot_list2.length === 0 && this.webPalletNum==='') {
        this.$message({ message: '请正确输入批次信息或者载具信息', type: 'info' })
        return
      }
      this.$emit('sendMessage', this.webPalletNum, lot_list2)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
