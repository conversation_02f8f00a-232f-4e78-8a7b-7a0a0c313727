<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-drawer append-to-body :wrapper-closable="false" :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="450px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
          <el-form-item label="标签组编码" prop="tag_group_code">
            <el-input v-model="form.tag_group_code" />
          </el-form-item>
          <el-form-item label="标签组描述" prop="tag_group_des">
            <el-input v-model="form.tag_group_des" />
          </el-form-item>
          <el-form-item label="快照时间(ms)" prop="fastpic_time">
            <el-input v-model="form.fastpic_time" />
          </el-form-item>
          <el-form-item label="是否快照">
            <el-radio-group v-model="form.fastpic_flag">
              <el-radio v-for="item in dict.WHETHER_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="有效标识">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">确认</el-button>
        </div>
      </el-drawer>

      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" size="small" :data="tableDataTable" style="width: 100%" :height="height" highlight-current-row @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="tag_group_code" min-width="100" label="组编码" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="tag_group_des" min-width="100" label="组描述" sortable="custom" />
        <el-table-column  :show-overflow-tooltip="true" prop="fastpic_time" width="120" label="快照时间(ms)" sortable="custom" />
        <el-table-column  label="是否快照" align="center" prop="fastpic_flag" width="100" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.WHETHER_FLAG[scope.row.fastpic_flag] }}
          </template>
        </el-table-column>

        <el-table-column  label="有效标识" align="center" prop="enable_flag" width="100" sortable="custom">
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column  label="操作" width="125" align="center">
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import { selScadaTagGroup, insScadaTagGroup, updScadaTagGroup, delScadaTagGroup } from '@/api/core/scada/tagGroup'

export default {
  name: 'TAGGROUP',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 230,
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      // Table
      listLoadingTable: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],

      form: {
        tag_group_id: '',
        client_id: '',
        tag_group_code: '',
        tag_group_des: '',
        fastpic_flag: 'N',
        fastpic_time: '0',
        enable_flag: 'Y'
      },
      rules: {
        // 提交验证规则
        tag_group_code: [{ required: true, message: '请输入标签组编码', trigger: 'blur' }],
        tag_group_des: [{ required: true, message: '请输入标签组描述', trigger: 'blur' }]
      },
      tag_group_code_des: '',

      query: {
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'tag_group_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      pageTable: {
        // 页码
        page: 1,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WHETHER_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 230
    }
  },
  created: function() {
    // 查询
    this.toButQuery()
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 按钮：
    toButQuery(clientId, queryContent) {
      if (clientId === undefined) {
        this.form.client_id = this.$parent.$attrs.clientid
      } else {
        this.form.client_id = clientId
      }
      if (queryContent !== undefined) {
        this.tag_group_code_des = queryContent
      }
      this.listLoadingTable = true
      const query = {
        user_name: Cookies.get('userName'),
        client_id: this.form.client_id,
        tag_group_code_des: this.tag_group_code_des,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      selScadaTagGroup(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toButAdd() {
      this.form.tag_group_id = ''
      this.form.tag_group_code = ''
      this.form.tag_group_des = ''
      this.form.fastpic_flag = 'N'
      this.form.fastpic_time = '0'
      this.form.enable_flag = 'Y'
      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增标签组'
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.tag_group_id = data.tag_group_id
      this.form.client_id = data.client_id
      this.form.tag_group_code = data.tag_group_code
      this.form.tag_group_des = data.tag_group_des
      this.form.fastpic_flag = data.fastpic_flag
      this.form.fastpic_time = data.fastpic_time
      this.form.enable_flag = data.enable_flag
      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改标签组'
    },
    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            tag_group_id: this.form.tag_group_id,
            client_id: this.form.client_id,
            tag_group_code: this.form.tag_group_code,
            tag_group_des: this.form.tag_group_des,
            fastpic_flag: this.form.fastpic_flag,
            fastpic_time: this.form.fastpic_time,
            enable_flag: this.form.enable_flag
          }
          const that = this
          // 新增
          if (this.form.tag_group_id === undefined || this.form.tag_group_id.length <= 0) {
            insScadaTagGroup(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  that.$emit('AddTreeNode', this.form.client_id, defaultQuery.result, this.form.tag_group_code, this.form.tag_group_des)
                  this.dialogVisbleSyncFrom = false
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updScadaTagGroup(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  that.$emit('EditTreeNode', this.form.client_id, this.form.tag_group_id, this.form.tag_group_code, this.form.tag_group_des)
                  this.dialogVisbleSyncFrom = false
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除标签组编码${data.tag_group_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            tag_group_id: data.tag_group_id
          }
          const that = this
          delScadaTagGroup(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                that.$emit('DelTreeNode', data.client_id, data.tag_group_id)
                // 查询
                this.toButQuery()
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.query.tableSize = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.query.tablePage = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'tag_group_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
</style>
