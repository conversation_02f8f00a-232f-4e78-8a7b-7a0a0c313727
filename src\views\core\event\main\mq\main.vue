<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.mqmain.eventMqMainCode')">
                <!-- MQ事件代码： -->
                <el-input
                  v-model="query.event_mq_main_code"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item
                :label="$t('lang_pack.mqmain.eventMqMainDes')"
              >
                <!-- MQ事件描述： -->
                <el-input
                  v-model="query.event_mq_main_des"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentification')"
              >
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="450px"
      >
        <el-form
          ref="form"
          class="el-form-wrap el-form-column"
          :model="form"
          :rules="rules"
          size="small"
          label-width="200px"
          :inline="true"
        >
          <el-form-item :label="$t('lang_pack.mqmain.eventModMq')" prop="event_mod_mq_id">
            <!--MQ事件模版 -->
            <el-select v-model="form.event_mod_mq_id" filterable clearable>
              <el-option v-for="item in eventModMqs" :key="item.event_mod_mq_id" :label="item.event_mod_mq_code + ' ' + item.event_mod_mq_des" :value="item.event_mod_mq_id" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.mainmain.station')" prop="station_id">
            <!-- 工位 -->
            <el-select v-model="form.station_id" filterable clearable>
              <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.mqmain.eventMqMainCode')" prop="event_mq_main_code">
            <!-- MQ事件编码 -->
            <el-input v-model="form.event_mq_main_code" />
          </el-form-item>

          <el-form-item :label="$t('lang_pack.mqmain.eventMqMainDes')" prop="event_mq_main_des">
            <!-- MQ事件描述 -->
            <el-input v-model="form.event_mq_main_des" />
          </el-form-item>

          <el-form-item :label="$t('lang_pack.mainmain.examplesCollections')" prop="client_id_list">
            <el-input v-model="form.client_id_list" readonly="readonly">
              <div slot="append">
                <el-button slot="reference" @click="$refs.popupDialog.open(form.client_id_list)">选择</el-button>
              </div>
            </el-input>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button
            size="small"
            icon="el-icon-close"
            plain
            @click="crud.cancelCU"
          >{{ $t("lang_pack.commonPage.cancel") }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              :height="height"
              :highlight-current-row="true"
              @header-dragend="crud.tableHeaderDragend()"
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="event_mq_main_id" label="id" />
              <!-- 工位 -->
              <el-table-column :show-overflow-tooltip="true" prop="station_id" :label="$t('lang_pack.mqmain.station')" width="130">
                <template slot-scope="scope">
                  {{ getStationDes(scope.row.station_id) }}
                </template>
              </el-table-column>
              <!-- 主MQ事件编码 -->
              <el-table-column :show-overflow-tooltip="true" prop="event_mq_main_code" :label="$t('lang_pack.mqmain.eventMqMainCode')" width="130" />
              <!-- 主MQ事件描述 -->
              <el-table-column :show-overflow-tooltip="true" prop="event_mq_main_des" :label="$t('lang_pack.mqmain.eventMqMainDes')" width="130" />
              <!-- 关联SCADA实例ID集合 -->
              <el-table-column :show-overflow-tooltip="true" prop="client_id_list" :label="$t('lang_pack.mqmain.clientIdList')" width="130" />
              <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
                <!-- 有效标识 -->
                <template slot-scope="scope">
                  {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('lang_pack.commonPage.operate')"
                align="center"
                fixed="right"
                width="140"
              >
                <!-- 操作 -->
                <template slot-scope="scope">
                  <udOperation
                    :data="scope.row"
                    :permission="permission"
                    :disabled-dle="false"
                  >
                    <template slot="right">
                      <el-button
                        slot="reference"
                        type="text"
                        size="small"

                        @click="$refs.subItem && $refs.subItem.crud.toAdd()"
                      >{{
                        $t("lang_pack.maintenanceMenu.addSubmenu")
                      }}</el-button>
                      <!-- 新增子菜单 -->
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
            <!-- 实例弹窗的公用组件 -->
            <popupDialog ref="popupDialog" :obj.sync="exampleObj" @updateAdd="updateAdd" />
          </el-col>
        </el-row>

        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <subItem
              ref="subItem"
              :client_id_list="currentClientIdList"
              class="taIbleFirst box-card1"
              :readonly=true
              :event_mq_main_id="this.form.event_mq_main_id"
              :event_mod_mq_id="this.form.event_mod_mq_id"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudEventMqMain from '@/api/core/event/eventMqMain'
import { sel as selMqMod } from '@/api/core/event/eventMqModMain'
import { sel as selStation } from '@/api/core/factory/sysStation'
import subItem from './subItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import popupDialog from '@/components/popupDialog/index'

const defaultForm = {
  event_mq_main_id: 0,
  event_mod_mq_id: 0,
  station_id: 0,
  event_mq_main_code: '',
  event_mq_main_des: '',
  client_id_list: '',
  enable_flag: 'Y'
}
export default {
  name: 'EVENT_MQ_MAIN',
  components: { crudOperation, rrOperation, udOperation, pagination, subItem, popupDialog },
  cruds() {
    return CRUD({
      title: 'MQ事件',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'event_mq_main_id',
      // 排序
      sort: ['event_mq_main_id asc'],
      // CRUD Method
      crudMethod: { ...crudEventMqMain },
      query: { msg: 'YES' },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {},
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'rcs_event_mq:add'],
        edit: ['admin', 'rcs_event_mq:edit'],
        del: ['admin', 'rcs_event_mq:del'],
        down: ['admin', 'rcs_event_mq:down']
      },
      rules: {
        event_mod_mq_id: [{ required: true, message: '请选择MQ事件模版', trigger: 'blur' }],
        station_id: [{ required: true, message: '请输入工位', trigger: 'blur' }],
        event_mq_main_code: [{ required: true, message: '请输入主MQ事件编码', trigger: 'blur' }],
        event_mq_main_des: [{ required: true, message: '请输入MQ事件描述', trigger: 'blur' }],
        client_id_list: [{ required: true, message: '请选择实例集合', trigger: 'blur' }]
      },
      stationData: [],
      eventModMqs: [],
      customPopover: false,
      currentClientIdList: '',
      exampleObj: {
        title: '实例集合',
        tableLable: [
          { prop: 'client_des', label: '描述' }
        ]
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    this.getModMqs({
      user_name: Cookies.get('userName'),
      sort: 'client_id',
      enable_flag: 'Y' })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {
    const query = {
      user_name: Cookies.get('userName'),
      sort: 'client_id',
      enable_flag: 'Y'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    getModMqs(query) {
      selMqMod(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.eventModMqs = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.client_id_list = crud.form.client_id_list === '' ? '' : (crud.form.client_id_list.split(',')).toString()
      return true
    },

    updateAdd(value) {
      const list = []
      if (value.length > 0) {
        value.forEach((e) => {
          list.push(e.client_id)
        })
      }
      this.form.client_id_list = list.toString()
    },

    handleRowClick(row, column, event) {
      this.currentClientIdList = row.client_id_list
      this.form.event_mq_main_id = row.event_mq_main_id
      this.form.event_mod_mq_id = row.event_mod_mq_id
    },
    // 获取角色的中文描述
    getStationDes(station_id) {
      var item = this.stationData.find(item => item.station_id === station_id)
      if (item !== undefined) {
        return item.station_code + ' ' + item.station_des
      }
      return station_id
    }

  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
