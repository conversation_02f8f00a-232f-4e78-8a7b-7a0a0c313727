<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.taskTable.batchNumber') + ':'">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <!-- 制程分类 -->
              <el-form-item :label="$t('lang_pack.taskTable.productionMode') + ':'">
                <el-select v-model="query.production_mode" clearable>
                  <el-option v-for="item in productionMode" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>

          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item style="margin: 0; float: right">
              <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="toButQuery('reset');sendCode()">{{ $t('view.button.search') }}</el-button>
              <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="toButResetQuery">{{ $t('view.button.reset') }}</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <!-- <crudOperation show=""  /> -->
      <el-row :gutter="20">

        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="listLoadingTable"
            border
            size="small"
            :data="tableDataTable"
            style="width: 100%"
            :cell-style="cellStyle"
            :height="height"
            :highlight-current-row="true"
          >
            <!-- 工单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_id"
              :label="$t('lang_pack.workOrder.workOrderNumber')"
              align="center"
            />
            <!-- 制程代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="process_code"
              :label="$t('lang_pack.workOrder.processCode')"
              align="center"
            />
            <!-- 产品用途 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="use_in_name"
              :label="$t('lang_pack.workOrder.productUse')"
              align="center"
            />
            <!-- 批号简码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_short_id"
              :label="$t('lang_pack.workOrder.batchNumber')"
              align="center"
            />
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_id"
              :label="$t('lang_pack.workOrder.materialNumber')"
              align="center"
            />
            <!-- 板件数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pnl_count"
              :label="$t('lang_pack.workOrder.NumbeOfPlates')"
              align="center"
            />
            <!-- 板序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="prod_ver"
              :label="$t('lang_pack.workOrder.platSe')"
              align="center"
            />
            <!-- 板长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="panel_length"
              :label="$t('view.table.boardLength')"
              align="center"
            />
            <!-- 板宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="panel_width"
              :label="$t('view.table.boardWidth')"
              align="center"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="panel_tickness"
              :label="$t('view.table.boardThickness')"
              align="center"
            />
            <!-- 翻转 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="panel_fz"
              :label="$t('lang_pack.workOrder.flip')"
              align="center"
            />
            <!-- 参数信息 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_infos"
              :label="$t('lang_pack.workOrder.parameterInfo')"
              align="center"
            />
          </el-table>
          <!--分页组件-->
          <!--<pagination />-->
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { sel } from '@/api/eap/project/dy/eapDyParams'
import Cookies from 'js-cookie'
export default {
  name: 'ProdOrder',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      query: {
        lot_num: '',
        production_mode: ''
      },
      listLoadingTable: false,
      tableDataTable: [],
      productionMode: [{ 'label': 'Default', 'value': 'Default' },
        { 'label': 'Inner', 'value': 'Inner' },
        { 'label': 'Outer', 'value': 'Outer' }],
      delayCount: 0
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
  },
  methods: {
    // 单元格样式控制
    cellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec'
    },
    toButQuery(op) {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName'),
        lot_num: this.query.lot_num,
        production_mode: this.query.production_mode,
        station_id: this.$route.query.station_id
      }
      this.listLoadingTable = true

      // 倒计时功能
      this.delayCount = 15
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      sel(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.tableDataTable = defaultQuery.data
          } else {
            this.tableDataTable = []
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    toButResetQuery() {
      // 重置
      this.query.lot_num = ''
      this.production_mode = ''
      this.toButQuery()
    }

  }
}
</script>
