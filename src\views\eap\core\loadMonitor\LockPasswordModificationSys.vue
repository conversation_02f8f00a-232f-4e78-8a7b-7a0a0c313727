<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          锁机密码
        </template>
        <el-input ref="LockPasswor" v-model="LockPasswor" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo">确认输入</el-button>
    </div>
  </div>
</template>

<script>
import { sel } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
export default {
  name: 'roleCheck',
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null,
      paramsCode: {
        type: String,
        default: ''
      }
    },
    paramsCode: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      LockPasswor: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.LockPasswor.focus()
    })
  },
  methods: {
    handleSendInfo() {
      var checkFlag = true
      var panelBarCode = ''
      if (this.LockPasswor === '') {
        this.$message({ message: '请输入密码', type: 'info' })
        return
      }
      // 查询系统参数
      const query = {
        user_name: Cookies.get('userName'),
        parameter_code: this.paramsCode ? this.paramsCode : 'LockPassword',
        enable_flag: 'Y'
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              // console.log(this.$parent.$parent.LockPasswordModification.client_code)
              // console.log(this.$parent.$parent.currentStation.station_code)
              // console.log(this.$parent.$parent.LockPasswordModification.PasswordSetting)
              const tagKey = this.$parent.$parent.LockPasswordModification.client_code + '/' + this.$parent.$parent.LockPasswordModification.PasswordSetting
              const parasInfo = defaultQuery.data[0]
              var hmiPwd = parasInfo.parameter_val
              if (hmiPwd !== this.LockPasswor) {
                this.LockPasswor = ''
                this.$notify.error({
                  title: '提示',
                  message: '密码错误',
                  duration: 1000
                })
                return
              }
              setTimeout(() => {
                this.$notify.success({
                  title: '验证成功',
                  duration: 1000
                })
                this.LockPasswor = ''
                this.$emit('roleCheck', this.role_func_code, 'OK')
              }, 500)
              var sendJson = {}
              var rowJson = []
              var newRow = {
                TagKey: tagKey,
                TagValue: '2'
              }
              rowJson.push(newRow)
              sendJson.Data = rowJson
              sendJson.ClientName = 'SCADA_WEB'
              var sendStr = JSON.stringify(sendJson)
              var topic = 'SCADA_WRITE/' + tagKey.split('/')[0]
              this.$emit('sendMessage', topic, sendStr, panelBarCode)
              return
            } else {
              this.LockPasswor = ''
              this.$message({ message: '系统未维护解除Remote模式密码', type: 'error' })
              return
            }
          } else {
            this.LockPasswor = ''
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.LockPasswor = ''
          this.$message({ message: '获取当前解除Remote模式密码信息异常', type: 'error' })
          return
        })
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
