<template>
  <div>
    <!--工具栏-->
    <crudOperation v-if="!readonly" show="" :permission="permission" />
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px" :inline="false">
        <el-form-item label="属性组代码" prop="timer_mod_attr_group_code">
          <el-input v-model="form.timer_mod_attr_group_code" />
        </el-form-item>
        <el-form-item label="属性组描述" prop="timer_mod_attr_group_des">
          <el-input v-model="form.timer_mod_attr_group_des" />
        </el-form-item>
        <el-form-item label="有效标识" prop="enable_flag">
          <el-radio-group v-model="form.enable_flag">
            <el-radio label="Y">有效</el-radio>
            <el-radio label="N">失效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          highlight-current-row
          height="250px"
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
          @row-click="handleRowClick"
        >
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="timer_mod_attr_group_id" label="id" />
          <el-table-column v-if="1 == 0" width="10" prop="event_mod_timer_id" label="event_mod_timer_id" />
          <el-table-column :show-overflow-tooltip="true" prop="timer_mod_attr_group_code" label="属性组代码" />
          <el-table-column :show-overflow-tooltip="true" prop="timer_mod_attr_group_des" label="属性组描述" />
          <el-table-column label="有效标识" prop="enable_flag">
            <template slot-scope="scope">
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>
          <el-table-column v-if="!readonly" label="操作" width="100" align="center">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
    <attrItem ref="attrItem" :readonly="readonly" :event_timer_main_id="this.event_timer_main_id" 
    :client_id_list="client_id_list" :timer_mod_attr_group_id="this.timer_mod_attr_group_id" />
  </div>
</template>

<script>
import crudEventModAttrGroup from '@/api/core/event/eventTimerModAttrGroup'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import attrItem from './attr-item'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  timer_mod_attr_group_id: 0,
  event_mod_timer_id: 0,
  timer_mod_attr_group_code: '',
  timer_mod_attr_group_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'EVENT_TIMER_MOD_ATTR_GROUP',
  components: { attrItem, crudOperation, udOperation, pagination },
  props: {
    event_mod_timer_id: {
      type: [String, Number],
      default: -1
    },
    
    timer_mod_attr_group_id: {
      type: [String, Number],
      default: -1
    },
    event_timer_main_id: {
      type: [String, Number],
      default: -1
    },

    client_id_list:{
      type: [String, Number],
      default: -1
    },
    
    readonly: {
      type: Boolean,
      default: false
    }
  },
  cruds() {
    return CRUD({
      title: '属性组',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'timer_mod_attr_group_id',
      // 排序
      sort: ['timer_mod_attr_group_id asc'],
      // CRUD Method
      crudMethod: { ...crudEventModAttrGroup },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      attrItemShow: false,
      permission: {
        add: ['admin', 'rcs_event_mod_timer_attr_group:add'],
        edit: ['admin', 'rcs_event_mod_timer_attr_group:edit'],
        del: ['admin', 'rcs_event_mod_timer_attr_group:del']
      },
      rules: {
        timer_mod_attr_group_code: [{ required: true, message: '请输入属性组代码', trigger: 'blur' }],
        timer_mod_attr_group_des: [{ required: true, message: '请输入属性组描述', trigger: 'blur' }]
      }
    }
  },
  watch: {
    event_mod_timer_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.event_mod_timer_id = this.event_mod_timer_id
        this.timer_mod_attr_group_id = 0
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    handleRowClick(row, column, event) {
      this.timer_mod_attr_group_id = row.timer_mod_attr_group_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.event_mod_timer_id = this.event_mod_timer_id
      return true
    }
  }
}
</script>
