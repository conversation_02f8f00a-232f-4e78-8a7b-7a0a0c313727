import request from '@/utils/request'

// 载具上报
export function carrierIDReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/interf/send/CarrierIDReportOfWeb',
    method: 'post',
    data
  })
}

// Ping
export function eapPing(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/interf/send/EapPing',
    method: 'post',
    data
  })
}

// MGV与AGV模式切换
export function dockingModeChangeRequest(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/interf/send/DockingModeChangeRequest',
    method: 'post',
    data
  })
}

// Mode切换
export function cimModeChangeReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/interf/send/CimModeChangeReport',
    method: 'post',
    data
  })
}

// 补报WIP
export function eapDyPlanManualWipSel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/plan/EapDyPlanManualWipSel',
    method: 'post',
    data
  })
}

// 过站信息查询
export function eapDyApsPlanStationFlowSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/plan/EapDyApsPlanStationFlowSelect',
    method: 'post',
    data
  })
}

// 手工扫描板件条码
export function eapDyUnLoadPlanPanelCheckAndSaveNew(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/plan/EapDyUnLoadPlanPanelCheckAndSaveNew',
    method: 'post',
    data
  })
}

// 手工提交WIP
export function eapDyUnLoadPlanWipManualReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/dy/plan/EapDyUnLoadPlanWipManualReport',
    method: 'post',
    data
  })
}

export default { carrierIDReport, eapPing, dockingModeChangeRequest, cimModeChangeReport, eapDyPlanManualWipSel, eapDyApsPlanStationFlowSelect, eapDyUnLoadPlanPanelCheckAndSaveNew, eapDyUnLoadPlanWipManualReport }
