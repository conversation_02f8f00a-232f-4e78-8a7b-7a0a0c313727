<template>
  <!--模组子维护-->
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <h2>药水统计</h2>
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="药水名称：">
                <el-input v-model="query.potions_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never">
      <el-row :gutter="20">
        <el-col :span="24" class="elTableItem">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="448" max-height="448" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="potions_record_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="potions_name" label="药水名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="dosage" label="用量" />
            <el-table-column  :show-overflow-tooltip="true" prop="num" label="次数" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import eapDyPotionsRecordStat from '@/api/eap/project/dayuan/eapDyPotionsRecordStat'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
const defaultForm = {
  potions_name: ''
}
export default {
  name: 'EAP_DY_POTIONS_RECOED_STAT',
  components: { rrOperation },
  cruds() {
    return CRUD({
      title: '药水统计详情',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'potions_record_id',
      // 排序
      sort: ['potions_record_id asc'],
      // CRUD Method
      crudMethod: { ...eapDyPotionsRecordStat },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
  }
}
</script>
<style lang="less" scoped>
</style>
