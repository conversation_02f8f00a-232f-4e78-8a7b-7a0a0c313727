<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard" :style="{height:height + 'px'}">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="生产线：">
                <el-select
                  v-model="query.prod_line_code"
                  filterable
                  clearable
                  @change="prodLineChange"
                >
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_id"
                    :label="item.prod_line_code + ' ' + item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="工位：">
                <el-select
                  v-model="query.station_code"
                  filterable
                  clearable
                  @change="stationChange"
                >
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_id"
                    :label="item.station_code + ' ' + item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="工件编号：">
                <el-input v-model="query.serial_num" clearable />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="物料号：">
                <el-input v-model="query.exact_barcode" clearable />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="wrapElFormSecond formChild col-md-2 col-12">
              <el-form-item>
                <!-- <rrOperation /> -->
                <span class="wrapRRItem">
                  <el-button
                    class="filter-item"
                    size="small"
                    type="primary"
                    @click="materialBind"
                  >确认绑定</el-button>
                </span>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import crudStationMaterial from '@/api/mes/core/stationMaterial'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
export default {
  name: 'MES_ME_WORK_MATERIAL_BIND',
  data() {
    return {
      height: document.documentElement.clientHeight - 70,
      query: {
        prod_line_code: '',
        station_code: '',
        serial_num: '',
        exact_barcode: '',
        station_des: ''
      },
      prodLineData: [],
      stationData: []
    }
  },
  created() {
    selProdLine({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    })
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    prodLineChange(val) {
      const lineItem = this.prodLineData.find((item) => item.prod_line_code === val)
      if (lineItem) {
        this.getStationList(lineItem.prod_line_id)
      }
    },
    getStationList(prod_line_id) {
      this.stationData = []
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: prod_line_id
      }).then(res => {
        if (res.code === 0) {
          if (res.data && res.data.length) {
            this.stationData = res.data
          }
        }
      })
    },
    stationChange(val) {
      const lineItem = this.stationData.find((item) => item.station_code === val)
      if (lineItem) {
        this.query.station_des = lineItem.station_des
      }
    },
    materialBind() {
      for (const key in this.query) {
        if (!this.query[key]) {
          this.$message({ type: 'warning', message: '产线、工位、工件编号、物料号，不能为空' })
          return
        }
      }
      crudStationMaterial.materialBind(this.query).then(res => {
        if (res.code === 0) {
          this.$message({ type: 'success', message: '物料绑定工件编号成功' })
          return
        }
        this.$message({ type: 'warning', message: '物料绑定工件编号失败：' + res.msg })
      }).catch(err => {
        this.$message({ type: 'warning', message: '物料绑定工件编号失败：' + err.msg })
      })
    }
  }
}
</script>
