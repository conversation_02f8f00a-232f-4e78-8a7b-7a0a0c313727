<template>
    <div class="app-container">
      <el-card ref="queryCard" shadow="never" class="wrapCard">
        <el-form ref="query" :inline="true" size="small" label-width="100px">
          <div class="wrapElForm">
            <div class="wrapElFormFirst col-md-9 col-12">

              <div class="formChild col-md-4 col-14">
                <el-form-item label="产线：">
                  <!-- 产线： -->
                  <el-select v-model="query.prod_line_id" filterable clearable size="small">
                    <el-option v-for="item in prodLineData" :key="item.prod_line_id"
                      :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
                  </el-select>
                </el-form-item>
              </div>
              <div class="formChild col-md-4 col-12">
                <el-form-item label="工位：">
                  <!-- 工位： -->
                  <el-select v-model="query.station_code" filterable clearable>
                    <el-option v-for="item in stationData" :key="item.station_id"
                      :label="item.station_code + ' ' + item.station_des" :value="item.station_code" />
                  </el-select>
                </el-form-item>
              </div>
              <div class="formChild col-md-4 col-12">
                <el-form-item label="时间：">
                    <div class="block">
                    <el-date-picker v-model="query.item_date" type="datetimerange" size="small" align="right" unlink-panels
                        range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions" />
                    </div>
                </el-form-item>
              </div>
              <div class="formChild col-md-4 col-12">
                <el-form-item label="数据来源：">
                  <el-input v-model="query.msg_from" clearable size="small" />
                </el-form-item>
              </div>
              <div class="formChild col-md-4 col-12">
                <el-form-item label="数据目标：">
                  <el-input v-model="query.msg_to" clearable size="small" />
                </el-form-item>
              </div>

            </div>
            <div class="wrapElFormSecond formChild col-md-2 col-12">
              <el-form-item>
                <span class="wrapRRItem">
                  <el-button class="filter-item" size="small" type="primary" icon="el-icon-download" @click="exportExecl"
                      :disabled="tableData.length <= 0">导出</el-button>
                  <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{
                    $t('lang_pack.commonPage.search') }}</el-button> <!-- 搜索 -->
                  <el-button class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{
                    $t('lang_pack.commonPage.reset') }}</el-button> <!-- 重置 -->
                </span>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-card>

      <el-card shadow="never" style="margin-top: 10px">
        <!--工具栏-->
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%"
              :cell-style="cellStyle" highlight-current-row :height="height">
              <el-table-column  :show-overflow-tooltip="true" prop="item_date" label="时间" align="center" width="200"/>
              <el-table-column  :show-overflow-tooltip="true" prop="station_code" label="工位" align="center" width="130"/>
              <el-table-column  :show-overflow-tooltip="true" prop="client_des" label="实例描述" align="center" width="140"/>
              <el-table-column  :show-overflow-tooltip="true" prop="msg_device_id" label="device_id" align="center" width="120"/>
              <el-table-column  :show-overflow-tooltip="true" prop="msg_trans_id" label="事务ID" align="center" width="120"/>
              <el-table-column  :show-overflow-tooltip="true" prop="msg_from" label="数据来源" align="center" width="120"/>
              <el-table-column  :show-overflow-tooltip="true" prop="msg_to" label="数据目标" align="center" width="120"/>
              <el-table-column  :show-overflow-tooltip="true" prop="msg_sf" label="SF" align="center" width="120"/>
              <el-table-column  :show-overflow-tooltip="true" prop="msg_sml" label="SML" align="center"/>
              <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" width="140" fixed="right">
                <template slot-scope="scope">
                  <el-button type="primary" style="margin-left:20px" plain @click="openEditInterfParas(scope.row.msg_sml)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div style="margin-top: 5px;text-align:right;">
              <el-button-group>
                <el-button type="primary">总数量：{{ tableTotal }}</el-button>
                <el-button type="primary">当前第{{ nowPageIndex }}页</el-button>
                <el-button type="primary" @click="pageQuery('pre')">&lt;&nbsp;上一页</el-button>
                <el-button type="primary" @click="pageQuery('next')">下一页&nbsp;&gt;</el-button>
              </el-button-group>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-dialog title="SML参数详情" width="50%" top="20px" :visible.sync="dialogInterfParasVisible" :close-on-click-modal="false">
        <el-input v-model="interf_paras" type="textarea" :rows="20" placeholder="请输入内容" />
      </el-dialog>
      <download-excel class="export-excel-wrapper" v-if="downloadFlag" :data="json_data" :fields="json_fields" name="单元数据导出"></download-excel>
    </div>
  </template>
  <script>

  import exportExecl from '@/utils/manage'
  import crudCellService from '@/api/core/factory/sysProdLine'
  import { sel as selStation } from '@/api/core/factory/sysStation'
  import { selCellIP } from '@/api/core/center/cell'
  import Cookies from 'js-cookie'
  import axios from 'axios'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'
  const queryDefault = {
    prod_line_id: '',
    station_code: '',
    item_date: null,
    client_des: '',
    msg_from:'',
    msg_to:'',
    page_dirct: '',
    page_id: '',
    tableSize: 20,
    tablePage: 1
  }
  export default {
    name: 'CELL_SERVICE',
    // 数据模型
    data() {
      return {
        height: document.documentElement.clientHeight - 240,
        query: { ...queryDefault },
        tableLoading: false,
        tableData: [],
        tableTotal: 0,
        nowPageIndex: 1, // 当前页数
        pageList: [],
        prodLineData: [],
        stationData: [],
        currentRow: {},
        mainchartShow: false,
        editFlowChart: false,
        cellIp: '', // 单元IP
        webapiPort: '', // 单元API端口号
        mqttPort: '', // MQTT端口号
        json_fields: {
          '序号': 'index',
          '时间':'item_date',
          '工位': 'station_code',
          '实例描述': 'client_des',
          'device_id': 'msg_device_id',
          '事务ID': 'msg_trans_id',
          '数据来源': 'msg_from',
          '数据目标': 'msg_to',
          'SF': 'msg_sf',
          'SML': 'msg_sml',
        },
        json_data: [],
        downloadFlag:false,
        dialogInterfParasVisible: false,
        interf_paras: '',
        jsonErrorMsg: '',
        pickerOptions: {}
      }
    },
    watch: {
      'query.prod_line_id': {
        handler() {
          this.getStationData()
        }
      },
      'query.station_code': {
        handler() {
          this.getCellIp()
        }
      }
    },

    mounted() {
      const that = this
      window.onresize = function temp() {
        that.height = document.documentElement.clientHeight - 240
      }
    },
    created() {
        // 初始化日期选择器快捷选项
        this.pickerOptions = createDatePickerShortcuts(this.$i18n)

        crudCellService
        .sel({
          user_name: Cookies.get('userName'),
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.prodLineData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    },
    methods: {
      cellStyle() {
        return 'border:0px;border-bottom:1px solid #dfe6ec'
      },
      getStationData() {
        this.query.station_id = ''
        this.stationData = []
        const query = {
          user_name: Cookies.get('userName'),
          enable_flag: 'Y',
          prod_line_id: this.query.prod_line_id
        }
        selStation(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.stationData = defaultQuery.data
              }
            }
          })
          .catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
      },
      getCellIp() {
        var cellId = 0
        const stationInfo = this.stationData.filter(item => item.station_code === this.query.station_code)
        if (stationInfo.length > 0) {
          cellId = stationInfo[0].cell_id
        }
        const query = {
          user_name: Cookies.get('userName'),
          cell_id: cellId,
          current_ip: window.location.hostname
        }
        selCellIP(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              const ipInfo = JSON.parse(defaultQuery.result)
              this.cellIp = ipInfo.ip
              this.webapiPort = ipInfo.webapi_port
              this.mqttPort = ipInfo.mqtt_port
            } else {
              this.$message({ message: defaultQuery.msg, type: 'error' })
            }
          })
          .catch(() => {
            this.$message({ message: '查询异常', type: 'error' })
          })
      },
      resetQuery() {
        this.query = { ...queryDefault }
        this.nowPageIndex = 1
        this.pageList = []
        this.cellIp = ''
        this.webapiPort = ''
        this.tableData = []
      },
      toQuery() {
        if (this.query.prod_line_id === '' || this.query.station_code === '') {
          this.$message({ message: '请选择产线与工位', type: 'info' })
          return
        }
        var method = '/eap/interf/core/EapInterfCoreSecsLogSel'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
          // path = 'http://*************:' + 8089 + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
          // path = 'http://*************:' + 8089 + method
        }
        this.tableLoading = true
        this.tableData = []
        const data = {
          item_date: this.query.item_date,
          station_code: this.query.station_code,
          msg_from:this.query.msg_from,
          msg_to:this.query.msg_to,
          tableSize: this.query.tableSize,
          tablePage: this.nowPageIndex,
          page_dirct: this.query.page_dirct,
          page_id: this.query.page_id
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            this.tableLoading = false
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0) {
              if (defaultQuery.data.count > 0) {
                this.tableData = defaultQuery.data.data
              }
              this.tableTotal = defaultQuery.data.count
            } else {
              this.$message({ message: defaultQuery.data.msg, type: 'warning' })
            }
          })
          .catch(ex => {
            this.tableLoading = false
            this.$message({ message: '查询异常：' + ex, type: 'error' })
          })
      },
      pageQuery(pageDirct) {
        if (pageDirct === 'pre') {
          if (this.nowPageIndex <= 1) {
            this.nowPageIndex = 1
            this.$message({
              message: '已置顶',
              type: 'info'
            })
            return
          }
          this.nowPageIndex = this.nowPageIndex - 1
          const preId = this.pageList[this.nowPageIndex]
          this.query.page_dirct = pageDirct
          this.query.page_id = preId
          this.toQuery()
        } else {
          const total_page = this.tableTotal === 0 ? 1 : Math.ceil(this.tableTotal / this.query.tableSize)
          if (total_page === 1 || this.nowPageIndex >= total_page) {
            this.$message({
              message: '已置底',
              type: 'info'
            })
            return
          }
          const preId = this.tableData[0].id
          this.pageList[this.nowPageIndex] = preId
          this.nowPageIndex = this.nowPageIndex + 1
          this.query.page_dirct = pageDirct
          this.query.page_id = this.tableData[this.tableData.length - 1].id
          this.toQuery()
        }
      },
      // 数据导出
       exportExecl() {
        var method = '/eap/interf/core/EapInterfCoreSecsLogToExcel'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
          // path = 'http://*************:' + 8089 + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
          // path = 'http://*************:' + 8089 + method
        }
        const data = {
          item_date: this.query.item_date,
          station_code: this.query.station_code,
          msg_from:this.query.msg_from,
          msg_to:this.query.msg_to,
          page_dirct: this.query.page_dirct,
          page_id: this.query.page_id
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then( res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0) {
                this.json_data = defaultQuery.data.data || []
                this.json_data.forEach((e,index)=>{
                    e.index = index + 1
                })
                this.downloadFlag = true
                this.$nextTick(()=>{
                  const dom = document.querySelector('.export-excel-wrapper')
                  dom.click();
                })
            }else{
              this.$message.warning('导出失败' + err)
            }
          })
          .catch((err) => {
            this.$message.warning('导出失败' + err)
          })
      },
      openEditInterfParas(paras) {
        this.interf_paras = '{}'
        this.jsonErrorMsg = ''
        if (paras !== '') {
          try {
            var _json = JSON.parse(paras)
            if (typeof _json === 'object' && _json) {
              this.interf_paras = JSON.stringify(_json, null, 4)
            }
          } catch (ex) {
            this.jsonErrorMsg = ex
            this.interf_paras = paras
          }
        }
        this.dialogInterfParasVisible = true
      }
    }
  }
  </script>

  <style scoped>
  .export-excel-wrapper{
    display: none;
  }
  </style>
