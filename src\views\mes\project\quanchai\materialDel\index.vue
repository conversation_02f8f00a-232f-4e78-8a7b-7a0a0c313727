<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width: 100%"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="打印条码">
                <el-input v-model="query.print_barcode" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料二维码：">
                <el-input v-model="query.exact_barcode" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-close" plain round :disabled="crud.selections.length <=0" @click="unbind">
            解绑
          </el-button>
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
@header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="item_date" min-width="150" label="时间" />
            <el-table-column :show-overflow-tooltip="true" prop="station_code" min-width="100" label="工位号" />
            <el-table-column :show-overflow-tooltip="true" prop="station_docs.print_barcode" min-width="220" label="打印条码" />
            <el-table-column :show-overflow-tooltip="true" prop="serial_num" min-width="220" label="工件编码" />
            <el-table-column :show-overflow-tooltip="true" prop="exact_barcode" min-width="220" label="物料二维码" />
            <el-table-column :show-overflow-tooltip="true" prop="material_des" min-width="220" label="物料名称" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <!-- <el-button
                type="primary"
              >总数量：{{ page.total }}</el-button> -->
              <el-button
                type="primary"
              >当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudStationMaterial from '@/api/mes/core/stationMaterial'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {

}
export default {
  name: 'MES_MATERIAL_DEL',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: '物料解绑',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_trace_id',
      // 排序
      sort: ['material_trace_id desc'],
      // CRUD Method
      crudMethod: { ...crudStationMaterial },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        del: ['admin', 'c_mes_me_station_material:del']
      },
      nowPageIndex: 1, // 当前页数
      pageList: [],
      detailDialogVisible: false,
      currentRowData: {},
      exportId: '',
      timer: ''
    }
  },
  computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime = start.getFullYear() + '-' + ((start.getMonth() + 1) >= 10 ? (start.getMonth() + 1) : '0' + (start.getMonth() + 1)) + '-' + (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) + ' 00:00:00'
      const defalutEndTime = end.getFullYear() + '-' + ((end.getMonth() + 1) >= 10 ? (end.getMonth() + 1) : '0' + (end.getMonth() + 1)) + '-' + (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) + ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.crud.query.item_date = this.timeDefault
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y',
      station_group_code: 'R2'
    }
  },
  methods: {
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    unbind() {
      this.$confirm(`确认解绑选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var idList = []
          this.crud.selections.forEach((item) => {
            idList.push(item.material_trace_id)
          })
          crudStationMaterial
            .del({
              idList: idList
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
