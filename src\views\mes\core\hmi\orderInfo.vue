<template>
  <div class="orderinfo-container">
    <!-- <el-card>
      <div>
        <h4>模组配方表格</h4>
        <el-table border @header-dragend="crud.tableHeaderDragend()" :data="recipeTableData" style="width: 100%" :highlight-current-row="true">
          <el-table-column  label="模组编号" prop="mz_num" />
          <el-table-column  label="模组名称" prop="mz_name" />
          <el-table-column  label="模组电芯单双列" prop="dx_column" />
          <el-table-column  label="模组电芯层数" prop="dx_layer_num" />
          <el-table-column  label="模组电芯数量" prop="dx_count" />
          <el-table-column  label="模组P数" prop="mz_p_count" />
          <el-table-column  label="模组S数" prop="mz_s_count" />
          <el-table-column  label="模组类型" prop="mz_type" />
          <el-table-column  label="高档位电芯电压下限v" prop="dx_pressure_high_down_limit" />
          <el-table-column  label="高档位电芯电压上限v" prop="dx_pressure_high_upper_limit" />
          <el-table-column  label="低档位电芯电压下限v" prop="dx_pressure_low_down_limit" />
          <el-table-column  label="低档位电芯电压上限v" prop="dx_pressure_low_upper_limit" />
          <el-table-column  label="电芯内阻下限m" prop="dx_neizu_down_limit" />
          <el-table-column  label="电芯内阻上限m" prop="dx_neizu_upper_limit" />
          <el-table-column  label="电芯K值下限" prop="dx_kvalue_down_limit" />
          <el-table-column  label="电芯K值上限" prop="dx_kvalue_upper_limit" />
          <el-table-column  label="电芯电容下限" prop="dx_container_down_limit" />
          <el-table-column  label="电芯电容上限" prop="dx_container_upper_limit" />
          <el-table-column  label="模组双电芯电容下限" prop="mz_dd_container_down_limit" />
          <el-table-column  label="模组双电芯电容上限" prop="mz_dd_container_upper_limit" />
        </el-table>
      </div>
    </el-card> -->
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="12">
          <el-card class="cardFirst">
            <!-- <div slot="header" class="clearfix">
              <span>订单模组配方</span>
            </div> -->
            <div class="orderrecipe">
              <div class="orderButton">
                <el-button class="buttonone" size="large" type="primary" @click="getOrder">选择订单</el-button>
                <!-- <el-button class="buttonTwo" size="large" type="primary" @click="orderDetail = true">查看订单明细</el-button> -->
              </div>
              <div class="wrapDes">
                <el-descriptions :column="2">
                  <el-descriptions-item label="订单号">{{ currentOrderInfo.make_order }}</el-descriptions-item>
                  <el-descriptions-item label="计划数量">{{ currentOrderInfo.mo_plan_count }}</el-descriptions-item>
                  <el-descriptions-item label="机型">{{ currentOrderInfo.small_model_type }}</el-descriptions-item>
                  <el-descriptions-item label="完成数量">{{ currentOrderInfo.mo_finish_count }}</el-descriptions-item>
                  <el-descriptions-item label="模组配方">{{ currentOrderInfo.recipe }}</el-descriptions-item>
                  <el-descriptions-item label="电芯排废率">{{ currentOrderInfo.mo_scrap_rate }}</el-descriptions-item>
                  <el-descriptions-item label="开始时间">{{ currentOrderInfo.plan_start_time }}</el-descriptions-item>
                  <el-descriptions-item label="结束时间">{{ currentOrderInfo.plan_end_time }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>

            <!-- <div>
              <h4>模组电芯配方表格</h4>
              <el-table border @header-dragend="crud.tableHeaderDragend()" height="100" :data="batteriesTableData" style="width: 100%" :highlight-current-row="true">
                <el-table-column  label="电芯序号" prop="dx_num" />
                <el-table-column  label="线体选择" prop="line_num" />
                <el-table-column  label="电芯方向" prop="dx_direct" />
                <el-table-column  label="电芯档位" prop="dx_gear" />
              </el-table>
            </div> -->
          </el-card>
          <el-card>
            <!-- <div class="moreButton">
              <el-button class="" size="large" type="default">电芯配方</el-button>
              <el-button class="" size="large" type="default" @click="orderDetail = true">数据范围</el-button>
              <el-button class="" size="large" type="default" @click="orderDetail = true">条码NG规则</el-button>
            </div> -->
            <el-tabs type="border-card">
              <el-tab-pane label="电芯配方">
                <el-table border @header-dragend="crud.tableHeaderDragend()" height="360" :data="batteriesTableData" style="width: 100%" :highlight-current-row="true">
                  <el-table-column  label="电芯序号" prop="dx_num" />
                  <el-table-column  label="线体选择" prop="line_num" />
                  <el-table-column  label="电芯方向" prop="dx_direct" />
                  <el-table-column  label="电芯档位" prop="dx_gear" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="数据范围">
                <el-table border @header-dragend="crud.tableHeaderDragend()" height="360" :data="limitList" style="width: 100%" :highlight-current-row="true">
                  <el-table-column  label="范围值约束编码" prop="mz_limit_code" />
                  <el-table-column  label="范围值约束描述" prop="mz_limit_des" />
                  <el-table-column  label="上限" prop="upper_limit" />
                  <el-table-column  label="下限" prop="down_limit" />
                  <el-table-column  label="排废槽号" prop="ng_rack_code" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="条码NG规则">
                <el-table border @header-dragend="crud.tableHeaderDragend()" height="360" :data="dxbarList" style="width: 100%" :highlight-current-row="true">
                  <el-table-column  label="电芯条码排废名称" prop="dxbar_ng_name" width="120" />
                  <el-table-column  label="条码起始位置" prop="start_index" />
                  <el-table-column  label="条码结束位置" prop="end_index" />
                  <el-table-column  label="NG方式" prop="ng_way" />
                  <el-table-column  label="值集合" prop="value_list" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="cardFirst">
            <!-- <div slot="header" class="clearfix">
              <span>电芯扫描</span>
            </div> -->
            <!-- <div class="scanStyle">
              <span>料框码：</span>
              <el-input v-model="scanone" size="large" placeholder="请输入内容" :disabled="true" />
              <el-button size="large" type="primary">扫描</el-button>
            </div> -->
            <div class="statuStyle">
              <p>
                <span v-show="online == '0'"><span class="commonsatu statuZero" /><span>申请上线</span></span>
                <span v-show="online == '1'"><span class="commonsatu statuOne" /><span>申请上线</span></span>
              </p>
              <p>
                <span v-show="material == '0'"><span class="commonsatu statuSecond" /><span>缺料提醒</span></span>
                <span v-show="material == '1'"><span class="commonsatu statuTwo" /><span>缺料提醒</span></span>
              </p>
              <p class="needStyle">
                <span>需求档位</span><span class="neddNumber">{{ needGear }}</span>
              </p>
            </div>
          </el-card>
          <el-card>
            <!-- <div slot="header" class="clearfix">
              <span>扫描结果显示如下</span>
            </div> -->
            <div class="scanStyle">
              <span>电芯码：</span>
              <div class="wrapimin">
                <el-input v-model="scantwo" type="text" size="large" placeholder="请输入内容" />
                <img :src="keyboard" @click="showKeyboard">
              </div>
              <el-button class="scanBtn" size="large" type="primary" @click="ManualDxScan">扫描</el-button>
            </div>
            <el-table border @header-dragend="crud.tableHeaderDragend()" :data="orderTableData" style="width: 100%" height="460" :highlight-current-row="true">
              <el-table-column  label="序号" prop="item_index" />
              <el-table-column  label="时间" prop="item_date" />
              <el-table-column  label="电芯码" prop="dx_barcode" />
              <el-table-column  label="错误代码" prop="dx_ng_code">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.dx_ng_code === 0 ? 'success' : 'danger'" disable-transitions>
                    {{ scope.row.dx_ng_code }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column  label="错误描述" width="200" prop="dx_ng_msg" />
              <el-table-column  label="档位" prop="dx_gear" />
              <el-table-column  label="批次号" prop="dx_ori_batch" />
              <el-table-column  label="容量" prop="dx_ori_capacity" />
              <el-table-column  label="电压" prop="dx_ori_ocv4_pressure" />
              <el-table-column  label="内阻" prop="dx_ori_ocr4_air" />
              <el-table-column  label="K值" prop="dx_ori_k_value" />
              <el-table-column  label="Dcir内阻" prop="dx_ori_dcir" />
              <el-table-column  label="厚度" prop="dx_ori_thickness" />
              <el-table-column  label="ocv4时间" prop="dx_ori_ocv4_time" />
            </el-table>
          </el-card>
          <!-- <el-card class="box-card1" /> -->
        </el-col>
      </el-row>
    </el-card>
    <el-drawer title="选择订单" :visible.sync="chooseOrder" direction="rtl" size="85%">
      <el-table border @header-dragend="crud.tableHeaderDragend()" :data="radioArr" style="width: 100%" height="550" highlight-current-row @row-click="singleElection">
        <el-table-column  align="center" width="55" label="选择">
          <template slot-scope="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio v-model="templateSelection" class="radio" :label="scope.row.mo_id">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column  align="center" prop="mo_from" label="订单来源" width="80" />
        <el-table-column  align="center" prop="make_order" label="订单号" width="150" />
        <el-table-column  align="center" prop="product_batch" label="订单批号" width="80" />
        <el-table-column  align="center" prop="small_model_type" label="产品型号" width="250" />
        <el-table-column  align="center" prop="mo_plan_count" label="订单计划数量" width="80" />
        <el-table-column  align="center" prop="mo_finish_count" label="实际完成数量" width="80" />
        <el-table-column  align="center" prop="mo_order_by" label="订单排序" width="80" />
        <el-table-column  align="center" prop="plan_start_time" label="计划开始时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="plan_end_time" label="计划结束时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="start_date" label="订单启动时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="finish_date" label="订单完成时间" :formatter="formatDate" width="80" />
        <el-table-column  align="center" prop="mo_custom_des" label="订单来源客户描述" width="80" />
      </el-table>
    </el-drawer>
    <el-drawer title="订单明细" :visible.sync="orderDetail" direction="rtl" size="50%" />
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import { chooseOrderInfo, getOrderInfo, showOrderInfo, gxDxOnlineCheck, showtelecomInfo } from '@/api/hmi/orderinfo.js'
import { sel } from '@/api/core/system/sysParameter'
import { selCellIP } from '@/api/core/center/cell'

import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
import { MessageBox } from 'element-ui'
export default {
  name: 'orderInfo',
  components: {
    SimpleKeyboard
  },
  data() {
    return {
      currentOrderInfo: {
        make_order: '',
        mo_plan_count: '',
        small_model_type: '',
        mo_finish_count: '',
        recipe: '',
        mo_scrap_rate: '',
        plan_start_time: '',
        plan_end_time: ''
      },
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      // scanone: '',
      scantwo: '',
      chooseOrder: false,
      orderDetail: false,
      choose_make_order: '',
      choose_mo_id: '',
      iframeStaionId: '',
      iframeprodLineId: '',
      iframeStaionCode: '',
      online: 0,
      material: 0,
      needGear: 1,
      radioArr: [],
      // 当前选择的行的id
      templateSelection: '',
      recipeTableData: [],
      batteriesTableData: [],
      dxbarList: [],
      limitList: [],
      orderTableData: [],
      dlgObject: null,

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      connectUrl: '', // MQTT连接地址
      tagOnlyKey: ''
    }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  created() {

  },
  mounted() {
    // setInterval(function() {
    //   this.toGxDxOnlineCheck()
    // }, 1000)
    this.iframeprodLineId = this.$route.query.prod_line_id
    this.iframeStaionId = this.$route.query.station_id
    this.iframeStaionCode = this.$route.query.station_code
    // console.log(this.iframeprodLineId, this.iframeStaionId, this.iframeStaionCode)
    this.initshowtelecomInfo()
    // 启动监控
    this.toStartWatch()
    this.initOrderDes()
    // var $this = this
    // window.addEventListener('message', function(e) {
    //   // 截取字符prod_line_code
    //   // const params = e.currentTarget.location.search.substr(1, 14)
    //   // const arriframe = params.split('=')
    //   // this.iframeprodLineId = arriframe[1]
    //   // // 截取字符station_id
    //   // const paramsone = e.currentTarget.location.search.substr(16)
    //   // const arriframeone = paramsone.split('=')
    //   // this.iframeStaionId = arriframeone[1]
    //   console.log(e.currentTarget.location.search)
    //   const params = e.currentTarget.location.search.substr(1)
    //   const arriframe = params.split('&')
    //   const arrOne = arriframe[0].split('=')
    //   const arrTwo = arriframe[1].split('=')
    //   $this.iframeprodLineId = arrOne[1]
    //   $this.iframeStaionId = arrTwo[1]
    //   console.log($this)
    // })
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellId = result.cell_id
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          if (process.env.NODE_ENV === 'development') {
            this.connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          } else {
            this.connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          }
          console.log('拼接URL：' + this.connectUrl)

          // 获取系统参数信息
          var queryParameter = {
            userName: Cookies.get('userName'),
            cell_id: this.cellId,
            parameter_code: 'HMI_DX_ONLINE_MQTT',
            enable_flag: 'Y'
          }
          sel(queryParameter)
            .then(res => {
              console.log('获取系统参数1')
              const defaultQuery = JSON.parse(JSON.stringify(res))

              console.log('获取系统参数2')
              console.log(defaultQuery)

              console.log('defaultQuery行数：' + defaultQuery.data.length)
              if (defaultQuery.code === 0) {
                if (defaultQuery.data.length <= 0) {
                  this.$message({
                    message: '请先维护单元订阅主题系统参数',
                    type: 'error'
                  })
                  return
                }

                // 监控Tag
                this.tagOnlyKey = defaultQuery.data[0].parameter_val
                // console.log('tagOnlyKey值：' + this.tagOnlyKey)
                // Tag点位集合
                var newClientTagGroupList = []
                var newClientTagList = this.tagOnlyKey.split(',')

                // mqtt连接
                this.clientMqtt = mqtt.connect(this.connectUrl, this.optionsMqtt) // 开启连接
                this.clientMqtt.on('connect', e => {
                  this.mqttConnStatus = true
                  // 当MQTT连接成功后，注册CLIENT相关TOPIC
                  // 订阅主题
                  // var topic_clientStatus = 'SCADA_STATUS/OP1010'
                  // 获取Tag值
                  if (newClientTagList.length > 0) {
                    this.GetTagValue(newClientTagList)
                    // MesSleep(100);
                  }
                  // 订阅Tag组
                  if (newClientTagList.length > 0) {
                    for (var i = 0; i < newClientTagList.length; i++) {
                      var tagTopicArray = newClientTagList[i].toString().split('/')
                      var client_code = tagTopicArray[0]
                      var tag_group_code = tagTopicArray[1]

                      var clientGroupMultyKey = 'SCADA_CHANGE/' + client_code + '/' + tag_group_code
                      if (newClientTagGroupList.indexOf(clientGroupMultyKey) < 0) {
                        newClientTagGroupList.push(clientGroupMultyKey)
                        this.topicSubscribe(clientGroupMultyKey)
                      }
                    }
                  }

                  this.$message({
                    message: '连接成功',
                    type: 'success'
                  })
                })

                // MQTT连接失败
                this.clientMqtt.on('error', () => {
                  this.$message({
                    message: '连接失败',
                    type: 'error'
                  })
                  this.clientMqtt.end()
                })
                // 断开发起重连(异常)
                this.clientMqtt.on('reconnect', () => {
                  this.$message({
                    message: '连接断开，正在重连。。。',
                    type: 'error'
                  })
                })
                this.clientMqtt.on('disconnect', () => {
                  // this.$message({
                  //  message: '服务连接断开',
                  //  type: 'error'
                  // })
                })
                this.clientMqtt.on('close', () => {
                  // this.clientMqtt.end()
                  // this.$message({
                  //  message: '服务连接断开',
                  //  type: 'error'
                  // })
                })
                // 接收消息处理
                this.clientMqtt.on('message', (topic, message) => {
                  // console.log('MQTT收到来自', topic, '的消息', message.toString())
                  // const res = JSON.parse(message.toString())
                  // 解析传过来的数据
                  this.mqttUpdateTable(topic, message)
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '查询异常',
                type: 'error'
              })
            })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, error => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // this.clientMqtt.disconnect()
      // this.clientMqtt = null
      this.clientMqtt.end()
      this.mqttConnStatus = false
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < newTagList.length; i++) {
        var readTag = {}
        readTag.tag_key = newTagList[i].toString()
        readTagArray.push(readTag)
      }
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
      }
      console.log('环境process.env.NODE_ENV：' + process.env.NODE_ENV)
      console.log('调用接口：' + path)

      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');

              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()

                  if (tagKey === 'OP1010/PlcStatus/PlcS_RequestSignal') {
                    this.online = tagValue
                    if (tagValue === '1') {
                      this.orderTableData = []
                    }
                  } else if (tagKey === 'OP1012/PlcStatus/PlcS_RequestSignal') {
                    this.material = tagValue
                  } else if (tagKey === 'OP1012/PlcStatus/PlcS_LineStarvingRemind') {
                    this.needGear = tagValue
                  } else if (tagKey === 'OP1013/BarStatus/BarS_GetBarCodeResult') {
                    this.scantwo = tagValue
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      // console.log('MQTT收到来自', channel, '的消息', message.toString())
      // var jsonData = JSON.parse(message)
      // var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        var TagKey = jsonData.TagKey
        // var TagCode = jsonData.TagCode
        // var TagOldValue = jsonData.TagOldValue
        var TagNewValue = jsonData.TagNewValue
        if (TagKey === 'OP1010/PlcStatus/PlcS_RequestSignal') {
          this.online = TagNewValue
        } else if (TagKey === 'OP1012/PlcStatus/PlcS_RequestSignal') {
          this.material = TagNewValue
        } else if (TagKey === 'OP1012/PlcStatus/PlcS_LineStarvingRemind') {
          this.needGear = TagNewValue
        } else if (TagKey === 'OP1013/BarStatus/BarS_GetBarCodeResult') {
          this.scantwo = TagNewValue
          this.toGxDxOnlineCheck()
        }
      }
    },

    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate() + ' ' + dt.getHours() + ':' + dt.getMinutes() + ':' + dt.getSeconds()
      }
    },
    // handleClick(tab, event) {
    //         console.log(tab, event);
    //       },
    initOrderDes() {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_id: this.iframeStaionId
      }
      chooseOrderInfo(query)
        .then(res => {
          // console.log(res)
          // console.log(res.data[0].mo_flag)

          if (res.data[0].mo_flag > 0) {
            this.templateSelection = res.data[0].mo_id
            var initshowQuery = {
              userName: Cookies.get('userName'),
              prod_line_id: this.iframeprodLineId,
              station_id: this.iframeStaionId
            }
            showOrderInfo(initshowQuery)
              .then(res => {
                const result = JSON.parse(res.result)
                // console.log(result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
                }
                // this.recipeTableData = result.mz_list
                this.batteriesTableData = result.mzd_list
                this.dxbarList = result.dxbar_list
                this.limitList = result.limit_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          } else {
            var initshowQueryone = {
              userName: Cookies.get('userName'),
              prod_line_id: this.iframeprodLineId,
              station_id: this.iframeStaionId
            }
            showOrderInfo(initshowQueryone)
              .then(res => {
                const result = JSON.parse(res.result)
                // console.log(result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order = result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
                }
                this.batteriesTableData = result.mzd_list
                this.dxbarList = result.dxbar_list
                this.limitList = result.limit_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          }
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    getOrder() {
      // var $this = this
      this.chooseOrder = true
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_id: this.iframeStaionId
      }
      chooseOrderInfo(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            this.$message({
              message: '选择订单异常',
              type: 'error'
            })
            return
          }
          this.radioArr = res.data
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    // 单选---选择订单
    singleElection(row) {
      this.chooseOrder = false
      this.templateSelection = row.mo_id
      this.choose_make_order = row.make_order
      this.choose_mo_id = row.mo_id
      var getQuery = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId,
        make_order: this.choose_make_order,
        station_code: this.iframeStaionCode,
        mo_id: this.choose_mo_id
      }
      getOrderInfo(getQuery)
        .then(() => {
          var showQuery = {
            userName: Cookies.get('userName'),
            station_id: this.iframeStaionId
          }
          showOrderInfo(showQuery)
            .then(res => {
              const result = JSON.parse(res.result)
              if (result.mo_list.length === 0) {
                this.$message({
                  message: '当前订单数据为空',
                  type: 'warning'
                })
                this.currentOrderInfo.make_order = ''
                this.currentOrderInfo.mo_plan_count = ''
                this.currentOrderInfo.small_model_type = ''
                this.currentOrderInfo.mo_finish_count = ''
                this.currentOrderInfo.recipe = ''
                this.currentOrderInfo.mo_scrap_rate = ''
                this.currentOrderInfo.plan_start_time = ''
                this.currentOrderInfo.plan_end_time = ''
              } else {
                this.currentOrderInfo.make_order = result.mo_list[0].make_order
                this.currentOrderInfo.mo_plan_count = result.mo_list[0].mo_plan_count
                this.currentOrderInfo.small_model_type = result.mo_list[0].small_model_type
                this.currentOrderInfo.mo_finish_count = result.mo_list[0].mo_finish_count
                this.currentOrderInfo.recipe = result.mo_list[0].recipe
                this.currentOrderInfo.mo_scrap_rate = result.mo_list[0].mo_scrap_rate
                this.currentOrderInfo.plan_start_time = result.mo_list[0].plan_start_time
                this.currentOrderInfo.plan_end_time = result.mo_list[0].plan_end_time
              }
              this.batteriesTableData = result.mzd_list
              this.dxbarList = result.dxbar_list
              this.limitList = result.limit_list
            })
            .catch(() => {
              this.$message({
                message: '展示信息查询异常',
                type: 'error'
              })
            })
        })
        .catch(() => {
          this.$message({
            message: '获取信息查询异常',
            type: 'error'
          })
        })
    },
    toGxDxOnlineCheck() {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_code: this.iframeStaionCode,
        dx_barcode: this.scantwo
      }
      gxDxOnlineCheck(query)
        .then(res => {
          console.log(res.code)
          if (res.code !== 0) {
            setTimeout(() => {
              if (this.dlgObject) {
                MessageBox.close()
                this.dlgObject = null
              }
              this.dlgObject = MessageBox.confirm(res.msg, '提示', {
                confirmButtonText: '确定',
                type: 'warning',
                center: true,
                showCancelButton: true
              })
            }, 0)
          } else {
            setTimeout(() => {
              if (this.dlgObject) {
                MessageBox.close()
                this.dlgObject = null
              }
            }, 0)
          }

          this.initshowtelecomInfo()
        })
        .catch(() => {
          this.$message.closeAll()
          this.$message({
            message: '电芯上线校验异常',
            type: 'error'
          })
        })
    },
    initshowtelecomInfo() {
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId
      }
      showtelecomInfo(query)
        .then(res => {
          if (res.code !== 0) {
            this.$message.closeAll()
            this.$message({
              message: '查询电芯上线明细异常',
              type: 'error'
            })
          }
          this.orderTableData = res.data
        })
        .catch(() => {
          this.$message.closeAll()
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    ManualDxScan() {
      if (this.scantwo === '') {
        this.$message.closeAll()
        this.$message({
          message: '输入电芯条码不能为空',
          type: 'warning'
        })
      }
      this.toGxDxOnlineCheck()
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // console.log(1)
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
            // console.log(inputDom.type, i);
          }
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  height: calc(100vh - 80px);
}
.orderButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 15px;
  button {
    margin-left: 0 !important;
  }
  .buttonone {
    width: 90px;
    height: 30px;
    margin-top: -10px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonone:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .buttonTwo {
    margin-top: 5px !important;
    width: 90px;
    height: 30px;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .buttonTwo:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
}
// .wrapDes {
//   margin-top: 30px;
// }
.stepStyle {
  padding: 10px 0;
  padding-bottom: 30px;
}
.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
}
.el-menu {
  display: flex;
  justify-content: space-between;
  overflow: auto;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: #ffffff !important;
}
::v-deep .el-menu--horizontal > .el-menu-item {
  flex: 1;
  text-align: center;
  margin: 0 5px;
}
.flagActive {
  background-color: #e8efff;
}
.orderrecipe {
  display: flex;
  align-items: center;
}
.cardFirst {
  margin-bottom: 10px;
}
.statuStyle {
  display: flex;
  justify-content: space-around;
  p {
    display: flex;
    align-items: center;
    color: #333333;
    margin: 0;
    margin-bottom: 10px;
    span {
      display: flex;
      align-items: center;
    }
    .commonsatu {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: block;
    }
    .statuOne {
      background-color: #13ce66;
    }
    .statuZero {
      background-color: #cccccc;
    }
    .statuTwo {
      background-color: #ff4949;
    }
    .statuSecond {
      background-color: #cccccc;
    }
  }
}
.needStyle {
  display: flex !important;
  flex-direction: column;
  .neddNumber {
    margin-top: 15px;
    font-weight: 700;
  }
}
::v-deep .el-card__header {
  text-align: center;
  background: #79a0f1;
  color: #ffffff;
  padding: 10px 0;
}
::v-deep .el-tabs--border-card > .el-tabs__header {
  background-color: #ffffff;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333 !important;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #ffffff !important;
    background: #79a0f1;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #333333;
}
::v-deep .el-table th {
  background-color: #e8efff !important;
}
::v-deep .el-table__body tr.current-row > td {
  background-color: #79a0f1 !important;
  color: #ffffff;
}
::v-deep .el-input__inner{
  padding-right: 40px;
}
.keyboard-mask{
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 99;
    left: 0;
    right: 0;
}
.wrapimin{
  position: relative;
  width: 100%;
  img{
    position: absolute;
    right: 7px;
    top: -3px;
    width: 45px;
    z-index: 2;
  }
}
</style>
