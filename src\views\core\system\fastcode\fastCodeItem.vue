<template>
  <!--子快速编码明细-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
        <el-form-item :label="$t('lang_pack.fastCode.childCode')" prop="fastcode_code" display:none>
          <!-- 子编码 -->
          <el-input v-model="form.fastcode_code" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.fastCode.childDescription')" prop="fastcode_des">
          <!-- 子描述 -->
          <el-input v-model="form.fastcode_des" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.fastCode.order')" prop="fastcode_order">
          <!-- 顺序 -->
          <el-input v-model="form.fastcode_order" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
          <!-- 有效标识 -->
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
        <!-- 确认 -->
      </div>
    </el-drawer>
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"  highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="fastcode_id" label="id" />
          <el-table-column  v-if="1 == 0" width="10" prop="fastcode_group_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="fastcode_code" :label="$t('lang_pack.fastCode.childCode')" />
          <!-- 子编码 -->
          <el-table-column  :show-overflow-tooltip="true" prop="fastcode_des" :label="$t('lang_pack.fastCode.childDescription')" />
          <!-- 子描述 -->
          <el-table-column  :show-overflow-tooltip="true" prop="fastcode_order" width="100" :label="$t('lang_pack.fastCode.order')" />
          <!-- 顺序 -->

          <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
            <!-- 有效标识 -->
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
            <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import crudFastCodeItem from '@/api/core/system/sysFastcodeItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  fastcode_id: '',
  fastcode_group_id: '',
  fastcode_code: '',
  fastcode_des: '',
  fastcode_order: '',
  enable_flag: 'Y'
}
export default {
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    fastcode_group_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '快速编码明细',
      // 登录用户
      userName: Cookies.get('userName'),
      // 菜单组ID
      query: { fastcode_group_id: '' },
      // 唯一字段
      idField: 'fastcode_id',
      // 排序
      sort: ['fastcode_id asc'],
      // CRUD Method
      crudMethod: { ...crudFastCodeItem },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_fastcode:add'],
        edit: ['admin', 'sys_fastcode:edit'],
        del: ['admin', 'sys_fastcode:del'],
        down: ['admin', 'sys_fastcode:down']
      },
      rules: {
        // 提交验证规则
        fastcode_code: [{ required: true, message: '请输入子编码', trigger: 'blur' }],
        fastcode_order: [{ required: true, message: '请输入顺序', trigger: 'blur' }]
      },
      customPopover: false
    }
  },
  watch: {
    fastcode_group_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.fastcode_group_id = this.fastcode_group_id
        this.crud.toQuery()
      }
    }
  },

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.fastcode_group_id = this.fastcode_group_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.fastcode_group_id = this.fastcode_group_id
      return true
    }
  }
}
</script>
<style lang="less" scoped>
.el-table {
  border-radius: 10px;
}
.el-card {
  border: 0 !important;
  overflow: inherit;
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
