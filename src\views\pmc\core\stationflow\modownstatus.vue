<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.lastMakeOrder')" width="150">
                <!-- 最后订单号： -->
                <el-input v-model="query.last_make_order" clearable size="small" />
              </el-form-item>
            </div>

            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.zkName')" width="200">
                <!-- 下发订单类型： -->
                <el-select v-model="query.zk_name" clearable filterable>
                  <el-option v-for="item in dict.ZK_NAME" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
              <el-form-item :label="$t('lang_pack.modownstatus.zkName')">
                <!-- 下发订单类型 -->
                <el-select v-model="form.zk_name" clearable filterable>
                  <el-option v-for="item in dict.ZK_NAME" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.modownstatus.lastMakeOrder')" prop="last_make_order">
                <!-- 最后订单号 -->
                <el-input v-model="form.last_make_order" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" :label="$t('lang_pack.modownstatus.creationDate')" />
            <!-- 创建时间 -->
            <el-table-column  :show-overflow-tooltip="true" prop="last_update_date" :label="$t('lang_pack.modownstatus.lastUpdateDate')" />
            <!-- 修改时间 -->

            <el-table-column  :label="$t('lang_pack.modownstatus.zkName')" align="center" prop="zk_name">
              <!-- 下发订单类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ZK_NAME[scope.row.zk_name] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="last_make_order" :label="$t('lang_pack.modownstatus.lastMakeOrder')" />
            <!-- 最后订单号 -->

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudModownstatus from '@/api/pmc/stationflow/modownstatus'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  creation_date: '',
  zk_name: '',
  last_make_order: ''
}
export default {
  name: 'MODOWNSTATUS',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '下发订单状态',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'zk_name',
      // 排序
      sort: ['zk_name asc'],
      // CRUD Method
      crudMethod: { ...crudModownstatus },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'pmc_me_mo_down_status:add'],
        edit: ['admin', 'pmc_me_mo_down_status:edit'],
        del: ['admin', 'pmc_me_mo_down_status:del']
      },
      rules: {
        zk_name: [{ required: true, message: '请输入下发订单类型', trigger: 'blur' }],
        last_make_order: [{ required: true, message: '请输入最后订单号', trigger: 'blur' }]
      }
    }
  },
  // 数据字典
  dicts: ['ZK_NAME'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {}
}
</script>
