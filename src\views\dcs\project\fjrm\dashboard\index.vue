<template>
  <el-container class="container-zlrm">
    <el-header>
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7" style="display: flex;align-items: center;">
          <img
            :src="headerLogo"
            style="width: 300px; height: 90px; float: left; ;margin-top: 10px;margin-left: 10px;"
          >
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 5px;height: 100%;line-height: 100px;">
          <span class="title">中铝瑞闽废料二期立体智能仓库</span>
        </el-col>
        <el-col :span="7" style="text-align: right; padding-right: 10px;line-height: 100px;"><span class="time"> {{ DateTime[0] + ' ' + DateTime[1] }} </span><span class="time time1"> {{ DateTime[2] }} </span></el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="car-box">
            <div class="car-box-left">
              <p>06</p>
              <p>05</p>
              <p>04</p>
              <p>03</p>
              <p>02</p>
              <p>01</p>
            </div>
            <div class="car-box-right">
              <MainChart
                ref="chart"
                v-loading="loading"
                :nodes="nodes"
                :width="'1870'"
                :height="'536'"
                :readonly="false"
                element-loading-text="拼命绘制流程图中"
              />
            </div>
          </div>
          <div class="car-gra">
            <div class="car-gra-left" />
            <div class="car-gra-right">
              <span v-for="(item,index) in 24" :key="index">{{ index > 8 ? `${index+1}` : `0${index+1}` }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-main>
    <div class="footer">
      <div class="box footer-left">
        <div class="header"><svg-icon icon-class="stock" />库存信息</div>
        <!-- <div class="image"><img src="@/assets/images/fjrm/zsCss.png" alt=""></div> -->
        <div class="table-wrapper">
          <table>
            <thead>
              <tr class="event">
                <th>一层料框数量</th>
                <td>118</td>
                <th>一层饱和度</th>
                <td>98%</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>二层料框数量</th>
                <td>110</td>
                <th>二层饱和度</th>
                <td>91%</td>
              </tr>
              <tr class="event">
                <th>三层料框数量</th>
                <td>78</td>
                <th>三层饱和度</th>
                <td>65%</td>
              </tr>
              <tr>
                <th>四层料框数量</th>
                <td>12</td>
                <th>四层饱和度</th>
                <td>10%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="box footer-centet">
        <div class="header"><svg-icon icon-class="rmCar" />平板小车信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr class="event">
                <th>行车</th>
                <th>联锁状态</th>
                <th>按钮</th>
                <th>控制按钮</th>
                <th>位置状态</th>
                <th>故障复位</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1#平板小车</td>
                <td style="color: red;">未联锁</td>
                <td>
                  <el-switch active-color="#13ce66" inactive-color="#ff4949" />
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px">出库</el-tag>
                  <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;">详</el-button>
                </td>
                <td style="color: aqua;">库内到位</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="trainDetail">复位</el-tag></td>
              </tr>
              <tr class="event">
                <td>2#平板小车</td>
                <td style="color: red;">未联锁</td>
                <td>
                  <el-switch active-color="#13ce66" inactive-color="#ff4949" />
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px">出库</el-tag>
                  <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;">详</el-button>
                </td>
                <td style="color: aqua;">库内到位</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;">复位</el-tag></td>
              </tr>
              <tr>
                <td>3#平板小车</td>
                <td style="color: red;">未联锁</td>
                <td>
                  <el-switch active-color="#13ce66" inactive-color="#ff4949" />
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px">出库</el-tag>
                  <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;">详</el-button>
                </td>
                <td style="color: aqua;">库内到位</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;">复位</el-tag></td>
              </tr>
              <tr class="event">
                <td>4#平板小车</td>
                <td style="color: red;">未联锁</td>
                <td>
                  <el-switch active-color="#13ce66" inactive-color="#ff4949" />
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px">出库</el-tag>
                  <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;">详</el-button>
                </td>
                <td style="color: aqua;">库内到位</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;">复位</el-tag></td>
              </tr>
              <tr>
                <td>5#平板小车</td>
                <td style="color: red;">未联锁</td>
                <td>
                  <el-switch active-color="#13ce66" inactive-color="#ff4949" />
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="stockDetail">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="smallCarDetail">出库</el-tag>
                  <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;" @click="crownBlockDetail">详</el-button>
                </td>
                <td style="color: aqua;">库内到位</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;">复位</el-tag></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="box footer-right">
        <div class="header"><svg-icon icon-class="xingChe" />行车作业信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr class="event">
                <th>行车模式</th>
                <td>东行车</td>
                <th>西行车</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>当前模式</th>
                <td>等待</td>
                <th>等待</th>
              </tr>
              <tr class="event">
                <th>运动类型</th>
                <td>6</td>
                <th>6</th>
              </tr>
              <tr>
                <th>作业ID</th>
                <td>34858</td>
                <th>34824</th>
              </tr>
              <tr class="event">
                <th>源位置</th>
                <td>CR01</td>
                <th>CR02</th>
              </tr>
              <tr>
                <th>目标位置</th>
                <td>CR01</td>
                <th>CR02</th>
              </tr>
              <tr class="event">
                <th>作业状态</th>
                <td>执行完成</td>
                <th>执行失败</th>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <selectModal ref="paneal" class="fjTable" />
    <stackIndex ref="stackIndex" class="fjTable" />
    <smallCarDetail ref="smallCarDetail" class="fjTable" />
    <crownBlockDetail ref="crownBlockDetail" class="fjTable" />
  </el-container>
</template>
<script>
import Cookies from 'js-cookie'
import selectModal from '@/components/selectModal'
import crownBlock from '@/api/dcs/project/fjrm/crownBlock.json'
import { getFormatDate } from '@/utils/index.js'
import headerLogo from '@/assets/images/fjrm/logo.png'
import MainChart from './wmsDashboard/index'
import stackIndex from '../module/stackIndex.vue'
import smallCarDetail from '../module/smallCarDetail.vue'
import crownBlockDetail from '../module/crownBlockDetail.vue'
export default {
  name: 'HelloWorld',
  components: { selectModal, stackIndex, smallCarDetail, crownBlockDetail, MainChart },
  data() {
    return {
      headerLogo,
      DateTime: [],
      loading: false,
      nodes: crownBlock
    }
  },
  created() {
    this.DateTime = getFormatDate().split(' ')
    this.timer = setInterval(() => {
      this.DateTime = getFormatDate().split(' ')
    }, 1000)
  },
  methods: {
    trainDetail() {
      const search = {
        page: 1,
        size: 20,
        sort: 'function_id asc',
        user_name: Cookies.get('userName')
      }
      this.$refs.paneal.open({
        type: 'pljh',
        checkType: '',
        search
      })
    },
    stockDetail() {
      this.$refs.stackIndex.open()
    },
    smallCarDetail() {
      this.$refs.smallCarDetail.open()
      this.$refs.smallCarDetail.modalTitle = '8#平板小车详情页面-小车编号8'
    },
    crownBlockDetail() {
      this.$refs.crownBlockDetail.open()
    }
  }
}
</script>
<style scoped  lang="scss">
@import '~@/assets/styles/fjrm/index.scss';
</style>
<style>
.fjTable .el-card{
  background-color: transparent!important;
  border: none!important;
}
.fjTable .el-table{
  background-color: transparent!important;
  border: none!important;
}
.fjTable .el-table th{
  background-color: #011c39 !important;
  color: aqua!important;
}
body .fjTable .el-table th.gutter{
    display: table-cell!important;
  }
.fjTable .el-table__body-wrapper::-webkit-scrollbar {
  width: 3px !important;
  height: 3px !important;
  background-color: rgb(18, 36, 80) !important;
  cursor: pointer !important;
}
.fjTable .el-scrollbar__wrap::-webkit-scrollbar {
  width: 3px!important;
  height: 3px!important;
}
.fjTable .el-table__body-wrapper::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: aqua !important;
  cursor: pointer !important;
}
.fjTable .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: transparent !important;
}
.fjTable .el-table__body tr.current-row>td{
  background-color: transparent !important;
}
.fjTable .el-table tr{
  background-color: #002E52 !important;
}
.fjTable .el-table tr:nth-child(even){
  background-color: #08192D !important;
}
.fjTable .el-table tr td{
  color: #fff!important;
}
.fjTable .el-table__body-wrapper{
  background-color: rgba(12, 35, 103,0.9) !important;
}

.fjTable .el-pagination .el-pagination__total{
color: aqua !important;
}

.fjTable .el-pagination button:disabled,.fjTable .el-pagination .btn-next,.fjTable .el-pagination .btn-prev{
  color: aqua !important;
  background-color: #193078 !important;
  border: none;
}
.fjTable .el-pager{
  background-color: #193078 !important;
}
.fjTable .el-pager li.active{
  color: aqua !important;
}
.fjTable .el-pager li{
  color: #fff!important;
  border: 1px none;
  border-right: nonde;
  background-color: transparent !important;
}

.fjTable .el-pagination__sizes .el-input__inner{
  background-color: #193078 !important;
  color: aqua!important;
  border: 1px solid aqua !important;
}
.fjTable .el-select .el-input .el-select__caret{
  color: aqua!important;
}

</style>
