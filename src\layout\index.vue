<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <!-- <navbar /> -->
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
      <!-- <right-panel v-if="themeStyle">
        <Themetings />
      </right-panel> -->
    </div>
    <!--  防止刷新后主题丢失  -->
    <Theme v-show="false" ref="theme" />
  </div>
</template>

<script>
import { sel } from '@/api/core/system/sysParameter'
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView, Themetings } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import Theme from '@/components/ThemePicker'
import Cookies from 'js-cookie'
export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    Theme,
    Themetings
  },
  data() {
    return {
      attrValue: [
        { key: 'sidebarStyle', val: 'dark' },//侧边栏
        { key: 'headerStyle', val: 'light' },//顶栏
        { key: 'theme', val: '#79A0F1' },//颜色详情
        { key: 'tagsView', val: true }, //显示标签
        { key: 'fixedHeader', val: true }, //固定头部
        { key: 'sidebarLogo', val: true },//显示logo
        { key: 'warningValue', val: true },//报警信息
        { key: 'userValue', val: '0' },//用户显示方式
      ]
    }
  },
  mixins: [ResizeMixin],
  computed: {

    ...mapState({
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader,
      themeStyle: state => state.settings.themeStyle,
      hmiBgStyle: state => state.settings.hmiBgStyle,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  mounted() {
    // if (Cookies.get('theme')) {
    //   this.$refs.theme.theme = Cookies.get('theme')
    //   this.$store.dispatch('settings/changeSetting', {
    //     key: 'theme',
    //     value: Cookies.get('theme')
    //   })
    // }
    // 这里是获取系统参数的一些配置
    const query = {
      user_name: Cookies.get('userName'),
      parameter_code: 'user_code',
      enable_flag: 'Y'
    }
    sel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data.length > 0) {
          const str = defaultQuery.data[0].parameter_val
          const Arr = str.split(',')
          if (Arr.length === 8) {
            const result = [
              { key: 'sidebarStyle', val: Arr[0] },
              { key: 'headerStyle', val: Arr[1] },
              { key: 'theme', val: Arr[2] },
              { key: 'tagsView', val: Arr[3] === 'true' },
              { key: 'fixedHeader', val: Arr[4] === 'true' },
              { key: 'sidebarLogo', val: Arr[5] === 'true' },
              { key: 'warningValue', val: Arr[6] === 'true' },
              { key: 'userValue', val: Arr[7] },
            ]
            for (let i = 0; i < result.length; i++) {
              this.$store.dispatch('settings/changeSetting', {
                key: result[i].key,
                value: result[i].val
              })
            }
            this.$store.state.settings.systemArr = result
            this.$store.state.settings.systemData = defaultQuery.data[0]
          }
        } else {
          for (let i = 0; i < this.attrValue.length; i++) {
            this.$store.dispatch('settings/changeSetting', {
              key: this.attrValue[i].key,
              value: this.attrValue[i].val
            })
          }
        }
      }
    })
    // 这里是获取系统语言设置
    // const params = {
    //   user_name: Cookies.get('userName'),
    //   parameter_code: 'language_code',
    //   enable_flag: 'Y'
    // }
    // sel(params).then(res => {
    //   const defaultQuery = JSON.parse(JSON.stringify(res))
    //   if (defaultQuery.code === 0) {
    //     if (defaultQuery.data.length > 0) {
    //       const code = defaultQuery.data[0].parameter_val
    //       this.$i18n.locale = code
    //       this.$store.state.settings.systemLanguageData = defaultQuery.data[0]
    //       localStorage.setItem('language', this.$i18n.locale)
    //     } else {
    //       this.$i18n.locale = 'zh-CN'
    //       localStorage.setItem('language', this.$i18n.locale)
    //     }
    //   }
    // })
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/mixin.scss';
@import '~@/assets/styles/variables.scss';

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
  padding: 0;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
