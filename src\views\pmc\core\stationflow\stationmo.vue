<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.makeOrder')">
                <!-- 生产订单： -->
                <el-input v-model="query.make_order" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workCenterCode')">
                <!-- 车间： -->
                <el-select
                  v-model="query.work_center_code"
                  clearable
                  filterable
                  @change="changeWorkCenterCode"
                >
                  <el-option
                    v-for="item in dict.WORK_CENTER_CODE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.prodLineCode')">
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_code" @change="changeProdLine">
                  <el-option
                    v-for="item in prodLineData"
                    :key="item.prod_line_code"
                    :label="item.prod_line_des"
                    :value="item.prod_line_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.stationCode')">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_code">
                  <el-option
                    v-for="item in stationData"
                    :key="item.station_code"
                    :label="item.station_des"
                    :value="item.station_code"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workStatus')">
                <!-- 生产状态： -->
                <el-select v-model="query.work_status" clearable filterable>
                  <el-option
                    v-for="item in dict.WORK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.setSign')">
                <!-- 拉入/出状态： -->
                <el-select v-model="query.set_sign" clearable filterable>
                  <el-option
                    v-for="item in dict.SET_SIGN"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.creationDate')">
                <!-- 时间范围： -->
                <div class="block">
                  <el-date-picker
                    v-model="query.creation_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            border
            @header-dragend="crud.tableHeaderDragend()"
            ref="table"
            v-loading="crud.loading"
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="creation_date"
              :label="$t('lang_pack.stationmo.creationDate')"
              width="140"
            />
            <!-- 创建时间 -->
            <el-table-column
              :label="$t('lang_pack.stationmo.prodLineCode')"
              align="center"
              prop="prod_line_code"
              width="100"
            >
              <!-- 生产线 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getProdLineDes(scope.row.prod_line_code) }}
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('lang_pack.stationmo.stationCode')"
              align="center"
              prop="station_code"
              width="100"
            >
              <!-- 工位号 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStationDes(scope.row.station_code) }}
              </template>
            </el-table-column>

            <el-table-column
              :show-overflow-tooltip="true"
              prop="make_order"
              :label="$t('lang_pack.stationmo.makeOrder')"
              width="100"
            />
            <!-- 订单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="dms"
              :label="$t('lang_pack.stationmo.dms')"
              width="140"
            />
            <!-- DMS号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="item_project"
              :label="$t('lang_pack.stationmo.itemProject')"
            />
            <!-- 行项目 -->

            <el-table-column
              :label="$t('lang_pack.stationmo.workStatus')"
              align="center"
              prop="work_status"
              width="100"
            >
              <!-- 生产状态 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WORK_STATUS[scope.row.work_status] }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.stationmo.setSign')"
              align="center"
              prop="set_sign"
              width="100"
            >
              <!-- 拉入/出状态 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.SET_SIGN[scope.row.set_sign] }}
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from "@/api/core/factory/sysProdLine";
import crudStation from "@/api/core/factory/sysStation";
import crudStationMo from "@/api/pmc/stationflow/stationmo";
import Cookies from "js-cookie";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import rrOperation from "@crud/RR.operation";
import crudOperation from "@crud/CRUD.operation";
import pagination from "@crud/Pagination";
const defaultForm = {};

export default {
  name: "MESTATIONMO",
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: "工位生产订单",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "station_mo_id",
      // 排序
      sort: ["prod_line_code,station_code,mo_work_order"],
      // CRUD Method
      crudMethod: { ...crudStationMo },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true,
      },
    });
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ["admin", "pmc_me_station_mo:add"],
        edit: ["admin", "pmc_me_station_mo:edit"],
        del: ["admin", "pmc_me_station_mo:del"],
      },
      // 车间数据
      currentWorkCenterCode: "", // 当前车间(单笔)
      // 产线数据
      currentProdLineCode: "", // 当前产线(单笔)
      prodLineData: [],
      // 工位数据
      stationData: [],
    };
  },
  // 数据字典
  dicts: ["WORK_CENTER_CODE", "WORK_STATUS", "SET_SIGN"],
  mounted: function () {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270;
    };
  },
  created: function () {},
  methods: {
    // 获取产线的中文描述
    getProdLineDes(prod_line_code) {
      var item = this.prodLineData.find((item) => item.prod_line_code === prod_line_code);
      if (item !== undefined) {
        return item.prod_line_des;
      }
      return prod_line_code;
    },
    // 获取工位的中文描述
    getStationDes(station_code) {
      var item = this.stationData.find((item) => item.station_code === station_code);
      if (item !== undefined) {
        return item.station_des;
      }
      return station_code;
    },

    // 更改车间
    changeWorkCenterCode(val) {
      this.currentWorkCenterCode = val; // 当前车间
      // 加载 产线LOV
      this.queryProdLine();
    },
    // 产线LOV
    queryProdLine() {
      const query = {
        work_center_code: this.currentWorkCenterCode,
        userID: Cookies.get("userName"),
      };
      lovProdLine(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.prodLineData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },
    // 更改产线
    changeProdLine(val) {
      this.currentProdLineCode = val; // 当前产线
      // 加载 工位LOV
      this.queryStation();
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get("userName"),
        prod_line_code: this.currentProdLineCode,
        enable_flag: "Y",
      };
      crudStation
        .lovStation(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data;
            }
          }
        })
        .catch(() => {
          this.$message({
            message: "查询异常",
            type: "error",
          });
        });
    },
    [CRUD.HOOK.beforeRefresh](crud) {
      var now = new Date();
      var monthn = now.getMonth() + 1;
      var monthnStart = now.getMonth();
      var yearn = now.getFullYear();
      var dayn = now.getDate();
      var h = now.getHours();
      var m = now.getMinutes();
      var s = now.getSeconds();
      var datetimeStart =
        yearn + "-" + monthnStart + "-" + dayn + " " + h + ":" + m + ":" + s;
      var datetimeEnd = yearn + "-" + monthn + "-" + dayn + " " + h + ":" + m + ":" + s;
      crud.query.creation_date = [
        // format：格式化日期函数；subDays：获得当前日期之前n天的日期
        datetimeStart,
        datetimeEnd,
      ];
    },
  },
};
</script>
