<template>
  <div class="orderinfo-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="12">
          <el-card class="cardFirst">
            <div class="orderrecipe">
              <div class="orderButton">
                <el-button
                  class="buttonone"
                  size="large"
                  type="primary"
                >订单信息</el-button>
              </div>
              <div class="wrapDes">
                <el-descriptions :column="2">
                  <el-descriptions-item label="订单号">{{
                    currentOrderInfo.make_order
                  }}</el-descriptions-item>
                  <el-descriptions-item label="计划数量">{{
                    currentOrderInfo.mo_plan_count
                  }}</el-descriptions-item>
                  <el-descriptions-item label="机型">{{
                    currentOrderInfo.small_model_type
                  }}</el-descriptions-item>
                  <el-descriptions-item label="完成数量">{{
                    currentOrderInfo.mo_finish_count
                  }}</el-descriptions-item>
                  <el-descriptions-item label="PACK配方">{{
                    currentOrderInfo.recipe
                  }}</el-descriptions-item>
                  <el-descriptions-item label="模组排废率">{{
                    currentOrderInfo.mo_scrap_rate
                  }}</el-descriptions-item>
                  <el-descriptions-item label="开始时间">{{
                    currentOrderInfo.plan_start_time
                  }}</el-descriptions-item>
                  <el-descriptions-item label="结束时间">{{
                    currentOrderInfo.plan_end_time
                  }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
          <el-card>
            <el-tabs type="border-card">
              <el-tab-pane label="PACK配方">
                <el-table
                  border
                  height="360"
                  :data="recipeTableData"
                  style="width: 100%"
                  :highlight-current-row="true"
                  @header-dragend="crud.tableHeaderDragend()"
                >
                  <el-table-column label="配方序号" prop="pack_id" />
                  <el-table-column label="配方名称" prop="pack_name" />
                  <el-table-column label="模组数量" prop="mz_count" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="模组配方">
                <el-table
                  border
                  height="360"
                  :data="mzsTableData"
                  style="width: 100%"
                  :highlight-current-row="true"
                  @header-dragend="crud.tableHeaderDragend()"
                >
                  <el-table-column label="模组型号" prop="mz_model_type" />
                  <el-table-column label="电芯档位" prop="dx_gear" />
                  <el-table-column
                    label="机型对应模组数量"
                    prop="mz_type_count"
                  />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="数据范围">
                <el-table
                  border
                  height="360"
                  :data="limitList"
                  style="width: 100%"
                  :highlight-current-row="true"
                  @header-dragend="crud.tableHeaderDragend()"
                >
                  <el-table-column
                    label="范围值约束编码"
                    prop="pack_limit_code"
                  />
                  <el-table-column
                    label="范围值约束描述"
                    prop="pack_limit_des"
                  />
                  <el-table-column label="上限" prop="upper_limit" />
                  <el-table-column label="下限" prop="down_limit" />
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="条码NG规则">
                <el-table
                  border
                  height="360"
                  :data="dxbarList"
                  style="width: 100%"
                  :highlight-current-row="true"
                  @header-dragend="crud.tableHeaderDragend()"
                >
                  <el-table-column
                    label="电芯条码排废名称"
                    prop="dxbar_ng_name"
                    width="120"
                  />
                  <el-table-column label="条码起始位置" prop="start_index" />
                  <el-table-column label="条码结束位置" prop="end_index" />
                  <el-table-column label="NG方式" prop="ng_way" />
                  <el-table-column label="值集合" prop="value_list" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="cardFirst">
            <el-row :gutter="20">
              <el-col :span="10">
                <span>模组完成/计划：</span><span style="font-size: 30px; font-weight: 900">{{
                  finishCountAndPlanCount
                }}</span>
                <el-tag v-if="mzScanFinish" type="success">已全部完成</el-tag>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 25px">
              <el-col :span="24">
                <div class="scanStyle">
                  <span>PACK码：</span>
                  <span>
                    {{ packBarcode }}
                  </span>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-card>
            <div class="scanStyle">
              <span>模组码：</span>
              <div class="wrapimin">
                <el-input
                  v-model="scantwo"
                  type="text"
                  size="large"
                  placeholder="请输入内容"
                />
                <img :src="keyboard" @click="showKeyboard">
              </div>
              <el-button
                class="scanBtn"
                size="large"
                type="primary"
                @click="PackMzScan"
              >扫描</el-button>
            </div>
            <el-table
              border
              :data="mzData"
              style="width: 100%"
              height="460"
              :cell-style="qualityTableRowClassName"
              :highlight-current-row="true"
              @header-dragend="crud.tableHeaderDragend()"
            >
              <el-table-column label="替换模组" prop="checked">
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="checked"
                    :true-label="scope.row.mz_index"
                    @change="handleUpdMz"
                  />
                </template>
              </el-table-column>
              <el-table-column label="序号" width="60" prop="mz_index" />
              <el-table-column label="NG标识" width="65" prop="check_ng_flag">
                <template slot-scope="scope">
                  <el-tag
                    :type="
                      scope.row.check_ng_flag === 'Y' ? 'danger' : 'success'
                    "
                    disable-transitions
                  >
                    {{ scope.row.check_ng_flag }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="NG描述" prop="check_ng_msg" width="70" />
              <el-table-column label="模组码" :show-overflow-tooltip="true" width="170" prop="mz_barcode" />
              <el-table-column label="最大容量" :show-overflow-tooltip="true" width="150" prop="mz_max_capacity" />
              <el-table-column label="最小容量" :show-overflow-tooltip="true" width="150" prop="mz_min_capacity" />
              <el-table-column label="时间" width="150" prop="creation_date" />
            </el-table>
          </el-card>
          <!-- <el-card class="box-card1" /> -->
        </el-col>
      </el-row>
    </el-card>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard
        :input="input"
        @onChange="onChange"
        @onKeyPress="onKeyPress"
      />
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import { chooseOrderInfo, showPackOrderInfo } from '@/api/hmi/orderinfo.js'
import {
  mesGxPackManualPackInfoSelect,
  mesGxPackManualSelect,
  mesGxPackManualMzCheck,
  mesGxPackManualOpClear,
  mesGxPackManualSave
} from '@/api/mes/project/manualPack.js'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import { selCellIP } from '@/api/core/center/cell'
import stationMonitor from '@/api/mes/core/hmi/stationMonitor'

import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'

export default {
  name: 'orderInfo',
  components: {
    SimpleKeyboard
  },
  data() {
    return {
      currentOrderInfo: {
        make_order: '',
        mo_plan_count: '',
        small_model_type: '',
        mo_finish_count: '',
        recipe: '',
        mo_scrap_rate: '',
        plan_start_time: '',
        plan_end_time: ''
      },
      checked: false,
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      // scanone: '',
      scantwo: '',
      chooseOrder: false,
      choose_make_order: '',
      choose_mo_id: '',
      iframeStaionId: '',
      iframeprodLineId: '',
      iframeStaionCode: '',
      allowScan: 0, // 允许扫描模组
      finishCountAndPlanCount: '0/0', // 模组完成数量/模组计划数量
      FinishScanMzCode: '0',

      packBarcode: '', // PACK码
      mzScanFinish: false,
      radioArr: [],
      newClientTagList: [],
      // 当前选择的行的id
      templateSelection: '',
      recipeTableData: [],
      mzsTableData: [],
      limitList: [],
      dxbarList: [],
      mzData: [],
      clientCode: 'OP3013',
      finishScanTag: 'OP3013/MesStatus/MesR_FinishScanMzCode',

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      connectUrl: '', // MQTT连接地址
      tagOnlyKey: '',
      repairDialogVisible: false,
      monitorData: {
        PlcS_DataReady: { client_code: this.$route.query.station_code, group_code: 'PlcStatus', tag_code: 'PlcS_DataReady', tag_des: 'PLC数据准备完成', value: '0' }
      },
      projectCode: ''
    }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  created() {},
  dicts: ['PLCS_DATA_READY'],
  mounted() {
    // 获取系统参数信息,判断是否是唐山国轩项目
    var queryParameter = {
      userName: Cookies.get('userName'),
      parameter_code: 'ProjectCode',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.projectCode = defaultQuery.data[0].parameter_val
            if (defaultQuery.data[0].parameter_val === 'TSGX') { this.clientCode = 'OP3050' }
            this.finishScanTag = 'OP3050/MesStatus/MesS_DataReady'
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
    this.iframeprodLineId = this.$route.query.prod_line_id
    this.iframeStaionId = this.$route.query.station_id
    this.iframeStaionCode = this.$route.query.station_code
    this.mzDataSelect()
    // 启动监控
    this.toStartWatch()
    this.initOrderDes()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    console.log('MQTT销毁链接')
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    qualityTableRowClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'mz_max_capacity' || column.property === 'mz_min_capacity') {
        return { color: '#000', fontWeight: 700, fontSize: '20px' }
      }
    },
    // 关闭窗口
    handleCloseDialog() {
      this.repairDialogVisible = false
    },

    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.iframeStaionId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellId = result.cell_id
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          this.getStationStatus()
          if (process.env.NODE_ENV === 'development') {
            this.connectUrl = 'ws://127.0.0.1:8083/mqtt'
          } else {
            this.connectUrl =
              'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          }
          console.log('拼接URL：' + this.connectUrl)
          // Tag点位集合
          var newClientTagGroupList = []

          // mqtt连接
          this.clientMqtt = mqtt.connect(this.connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', (e) => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // 获取Tag值
            this.GetTagValue(this.newClientTagList)
            // MesSleep(100);
            // 订阅Tag组
            if (this.newClientTagList.length > 0) {
              for (var i = 0; i < this.newClientTagList.length; i++) {
                var tagTopicArray = this.newClientTagList[i]
                  .toString()
                  .split('/')
                var client_code = tagTopicArray[0]
                var tag_group_code = tagTopicArray[1]

                var clientGroupMultyKey =
                  'SCADA_CHANGE/' + client_code + '/' + tag_group_code
                if (newClientTagGroupList.indexOf(clientGroupMultyKey) < 0) {
                  newClientTagGroupList.push(clientGroupMultyKey)
                  this.topicSubscribe(clientGroupMultyKey)
                }
              }
            }
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              if (this.aisMonitorMode === 'AIS-SERVER') {
                client_code = client_code + '_' + this.currentStation.station_code
              }
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })

            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', () => {
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          this.clientMqtt.on('close', () => {
            // this.clientMqtt.end()
            // this.$message({
            //  message: '服务连接断开',
            //  type: 'error'
            // })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())
            // const res = JSON.parse(message.toString())
            // 解析传过来的数据
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              var jsonData = JSON.parse(message)
              if (jsonData == null) return
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code = client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
            }
            this.newClientTagList.length > 0 && this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // this.clientMqtt.disconnect()
      // this.clientMqtt = null
      this.clientMqtt.end()
      this.mqttConnStatus = false
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      // 读取Tag集合(Key)
      var readTagArray = []
      for (var i = 0; i < newTagList.length; i++) {
        var readTag = {}
        readTag.tag_key = newTagList[i].toString()
        readTagArray.push(readTag)
      }
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      }
      console.log('环境process.env.NODE_ENV：' + process.env.NODE_ENV)
      console.log('调用接口：' + path)

      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');
              debugger

              if (result.length > 0) {
                Object.keys(this.monitorData).forEach(key => {
                  var client_code = this.monitorData[key].client_code
                  var group_code = this.monitorData[key].group_code
                  var tag_code = this.monitorData[key].tag_code
                  if (this.aisMonitorMode === 'AIS-SERVER') {
                    client_code = client_code + '_' + this.currentStation.station_code
                  }
                  var tag_key = client_code + '/' + group_code + '/' + tag_code
                  const item = result.filter(item => item.tag_key === tag_key)
                  if (item.length > 0) {
                    this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                  }
                })

                if (newTagList.length === 0) return
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  var type = tagValue.substr(3, 1)
                  if (type === 'P') {
                    this.packBarcode = tagValue
                    this.clearPackManualOp()
                    this.getFinishAndPlanCount()
                    this.mzDataSelect()
                    this.allowScan = 1
                    this.FinishScanMzCode = '0'
                    this.writeFinishScanMzCodeTag()
                  } else {
                    if (this.packBarcode === '') {
                      this.$message({
                        message: '请先扫描PACK码',
                        type: 'warning'
                      })
                      return
                    }
                    if (type !== 'M') {
                      this.$message({
                        message: '请扫描模组码',
                        type: 'warning'
                      })

                      return
                    } else {
                      // 说明是唐山国轩的
                      if (this.projectCode === 'TSGX' && this.monitorData.PlcS_DataReady.value !== '1') {
                        return
                      }
                      this.scantwo = tagValue
                      this.toGxMzOnlineCheck()
                      this.getFinishAndPlanCount()
                    }
                  }
                }
              }
            }
          }
        })
        .catch((ex) => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      console.log('MQTT收到来自', channel, '的消息', message.toString())
      // var jsonData = JSON.parse(message)
      // var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        var jsonData = JSON.parse(message)
        if (jsonData == null) return
        var TagKey = jsonData.TagKey
        // var TagCode = jsonData.TagCode
        // var TagOldValue = jsonData.TagOldValue
        var TagNewValue = jsonData.TagNewValue
        var type = TagNewValue.substr(3, 1)
        if (type === 'P') {
          this.packBarcode = TagNewValue
          this.clearPackManualOp()
          this.getFinishAndPlanCount()
          this.mzDataSelect()
          this.allowScan = 1
          this.FinishScanMzCode = '0'
          this.writeFinishScanMzCodeTag()
        } else {
          if (this.packBarcode === '') {
            this.$message({
              message: '请先扫描PACK码',
              type: 'warning'
            })
            return
          }
          if (type !== 'M') {
            this.$message({
              message: '请扫描模组码',
              type: 'warning'
            })
            return
          } else {
            // // 说明是唐山国轩的
            // if (this.projectCode === 'TSGX' && this.monitorData.PlcS_DataReady.value !== '1') {
            //   this.$message({ type: 'error', message: '当前工位pack涂胶超时' })
            //   return
            // }
            this.scantwo = TagNewValue
            this.toGxMzOnlineCheck()
            this.getFinishAndPlanCount()
          }
        }
      }
    },

    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return (
          dt.getFullYear() +
          '-' +
          (dt.getMonth() + 1) +
          '-' +
          dt.getDate() +
          ' ' +
          dt.getHours() +
          ':' +
          dt.getMinutes() +
          ':' +
          dt.getSeconds()
        )
      }
    },
    // handleClick(tab, event) {
    //         console.log(tab, event);
    //       },
    initOrderDes() {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.iframeprodLineId,
        station_id: this.iframeStaionId
      }
      chooseOrderInfo(query)
        .then((res) => {
          if (res.data[0].mo_flag > 0) {
            this.templateSelection = res.data[0].mo_id
            var initshowQuery = {
              userName: Cookies.get('userName'),
              prod_line_id: this.iframeprodLineId,
              station_id: this.iframeStaionId
            }
            showPackOrderInfo(initshowQuery)
              .then((res) => {
                const result = JSON.parse(res.result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order =
                    result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count =
                    result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type =
                    result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count =
                    result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate =
                    result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time =
                    result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time =
                    result.mo_list[0].plan_end_time
                }
                this.recipeTableData = result.pack_list
                this.mzsTableData = result.pack_mz_list
                this.limitList = result.limit_list
                this.dxbarList = result.dxbar_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          } else {
            var initshowQueryone = {
              userName: Cookies.get('userName'),
              prod_line_id: this.iframeprodLineId,
              station_id: this.iframeStaionId
            }
            showPackOrderInfo(initshowQueryone)
              .then((res) => {
                const result = JSON.parse(res.result)
                if (result.mo_list.length === 0) {
                  this.$message({
                    message: '当前订单数据为空',
                    type: 'warning'
                  })
                  this.currentOrderInfo.make_order = ''
                  this.currentOrderInfo.mo_plan_count = ''
                  this.currentOrderInfo.small_model_type = ''
                  this.currentOrderInfo.mo_finish_count = ''
                  this.currentOrderInfo.recipe = ''
                  this.currentOrderInfo.mo_scrap_rate = ''
                  this.currentOrderInfo.plan_start_time = ''
                  this.currentOrderInfo.plan_end_time = ''
                } else {
                  this.currentOrderInfo.make_order =
                    result.mo_list[0].make_order
                  this.currentOrderInfo.mo_plan_count =
                    result.mo_list[0].mo_plan_count
                  this.currentOrderInfo.small_model_type =
                    result.mo_list[0].small_model_type
                  this.currentOrderInfo.mo_finish_count =
                    result.mo_list[0].mo_finish_count
                  this.currentOrderInfo.recipe = result.mo_list[0].recipe
                  this.currentOrderInfo.mo_scrap_rate =
                    result.mo_list[0].mo_scrap_rate
                  this.currentOrderInfo.plan_start_time =
                    result.mo_list[0].plan_start_time
                  this.currentOrderInfo.plan_end_time =
                    result.mo_list[0].plan_end_time
                }
                this.recipeTableData = result.pack_list
                this.mzsTableData = result.pack_mz_list
                this.limitList = result.limit_list
                this.dxbarList = result.dxbar_list
              })
              .catch(() => {
                this.$message({
                  message: '展示信息查询异常',
                  type: 'error'
                })
              })
          }
        })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },

    toGxMzOnlineCheck() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        pack_barcode: this.packBarcode,
        mz_barcode: this.scantwo
      }
      mesGxPackManualMzCheck(query)
        .then((res) => {
          this.checked = false
          this.mzScanFinish = false
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            if (res.result === 'Y') {
              // 扫描完成
              this.savePackManual()
              this.allowScan = 0
              this.FinishScanMzCode = '1'
              this.writeFinishScanMzCodeTag()
              this.mzScanFinish = true
              this.$message({
                message: '模组已全部扫描完成',
                type: 'success',
                duration: 5000
              })
            }
          }
          this.mzDataSelect()
          this.getFinishAndPlanCount()
        })
        .catch(() => {
          this.$message({
            message: '模组上线校验异常',
            type: 'error'
          })
        })
    },
    mzDataSelect() {
      this.mzData = []
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode
      }
      mesGxPackManualSelect(query)
        .then((res) => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
            return
          }
          if (res.count > 0) {
            this.mzData = res.data
          }
        })
        .catch(() => {
          this.$message({
            message: '查询模组数据异常',
            type: 'error'
          })
        })
    },
    allowScanMz() {
      this.$confirm('是否清空数据并开始模组扫描？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.clearPackManualOp()
        this.getFinishAndPlanCount()
        this.mzDataSelect()
        this.allowScan = 1
        this.FinishScanMzCode = '0'
        this.writeFinishScanMzCodeTag()
      })
    },
    endScanMz() {
      this.$confirm('是否结束模组扫描并保存PACK与模组的关系？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.savePackManual()
        this.allowScan = 0
        this.FinishScanMzCode = '1'
        this.writeFinishScanMzCodeTag()
      })
    },
    PackMzScan() {
      if (this.scantwo === '') {
        this.$message({
          message: '输入模组条码不能为空',
          type: 'warning'
        })
        return
      }
      var type = this.scantwo.substr(3, 1)
      if (type === 'P') {
        this.packBarcode = this.scantwo
        this.clearPackManualOp()
        this.getFinishAndPlanCount()
        this.mzDataSelect()
        this.allowScan = 1
        this.FinishScanMzCode = '0'
        this.writeFinishScanMzCodeTag()
      }
      if (type === 'M') {
        if (this.packBarcode === '') {
          this.$message({
            message: '请先扫描PACK码',
            type: 'warning'
          })
          return
        }
        // 说明是唐山国轩的
        // if (this.projectCode === 'TSGX' && this.monitorData.PlcS_DataReady.value !== '1') {
        //   this.$message({ type: 'error', message: '当前工位pack涂胶超时' })
        //   return
        // }
        this.toGxMzOnlineCheck()
        this.getFinishAndPlanCount()
      } else {
        this.$message({
          message: '请扫描模组码',
          type: 'warning'
        })
        return
      }
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },
    // 查询当前工位作业状态监控配置
    getStationStatus() {
      if (this.iframeStaionId === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.iframeStaionId
      }
      stationMonitor
        .mesHmiStationStatusSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.newClientTagList = defaultQuery.result.split(',')
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: '查询mesHmiStationStatusSel异常',
            type: 'error'
          })
        })
    },
    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
            // console.log(inputDom.type, i);
          }
        }
      }
    },
    // 获取完成数量与计划数量
    getFinishAndPlanCount() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode,
        pack_barcode: this.packBarcode
      }
      mesGxPackManualPackInfoSelect(query)
        .then((res) => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
            this.packBarcode = ''
            return
          }
          const resultInfo = res.result.split(',')
          if (resultInfo[0] !== '' && resultInfo[0] != null) {
            this.packBarcode = resultInfo[0]
          }
          this.finishCountAndPlanCount = resultInfo[1]
          var list = this.finishCountAndPlanCount.split('/')
          if (list[0] === list[1] && list[1] > 0) {
            this.FinishScanMzCode = '1'
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 允许扫模组码时清空PACK配组临时表
    clearPackManualOp() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode
      }
      mesGxPackManualOpClear(query)
        .then((res) => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.$message({
            message: '清空PACK配组临时表异常',
            type: 'error'
          })
        })
    },
    // 结束扫模组码时保存PACK模组信息
    savePackManual() {
      var query = {
        userName: Cookies.get('userName'),
        station_code: this.iframeStaionCode
      }
      mesGxPackManualSave(query)
        .then((res) => {
          if (res.code !== 0) {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.$message({
            message: '保存PACK模组信息异常',
            type: 'error'
          })
        })
    },

    // 写入扫码完成信号
    writeFinishScanMzCodeTag() {
      var sendJson = {}
      var rowJson = []
      // 模组码
      var newRow = {
        TagKey: this.finishScanTag,
        TagValue: this.FinishScanMzCode
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.clientCode
      this.sendMessage(topic, sendStr)
      this.$message({
        message: '写入扫码完成信号成功!!!',
        type: 'success'
      })
    },
    handleUpdMz(val) {
      this.dxIndex = val
      if (!val) {
        this.dxIndex = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  height: calc(100vh - 80px);
}

.orderButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 15px;

  button {
    margin-left: 0 !important;
  }

  .buttonone {
    width: 90px;
    height: 30px;
    margin-top: -10px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff,
      -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }

  .buttonone:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1,
      -1px 4px 10px 1px #79a0f1;
  }
}

// .wrapDes {
//   margin-top: 30px;
// }
.stepStyle {
  padding: 10px 0;
  padding-bottom: 30px;
}

.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  margin-bottom: 20px;

  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }

  button {
    margin-left: 10px;
  }

  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff,
      -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }

  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1,
      -1px 4px 10px 1px #79a0f1;
  }
}

.el-menu {
  display: flex;
  justify-content: space-between;
  overflow: auto;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: #ffffff !important;
}

::v-deep .el-menu--horizontal > .el-menu-item {
  flex: 1;
  text-align: center;
  margin: 0 5px;
}

.flagActive {
  background-color: #e8efff;
}

.orderrecipe {
  display: flex;
  align-items: center;
}

.cardFirst {
  margin-bottom: 10px;
}

.statuStyle {
  justify-content: space-around;

  p {
    display: flex;
    align-items: center;
    color: #333333;
    margin: 0;
    margin-top: 10px;

    span {
      display: flex;
      align-items: center;
    }

    .commonsatu {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: block;
    }

    .statuOne {
      background-color: #13ce66;
    }

    .statuZero {
      background-color: #cccccc;
    }

    .statuTwo {
      background-color: #ff4949;
    }

    .statuSecond {
      background-color: #cccccc;
    }
  }
}

::v-deep .el-card__header {
  text-align: center;
  background: #79a0f1;
  color: #ffffff;
  padding: 10px 0;
}

::v-deep .el-tabs--border-card > .el-tabs__header {
  background-color: #ffffff;
}

::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333 !important;
}

::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #ffffff !important;
  background: #79a0f1;
}

::v-deep
  .el-tabs--border-card
  > .el-tabs__header
  .el-tabs__item:not(.is-disabled):hover {
  color: #333333;
}

::v-deep .el-table th {
  background-color: #e8efff !important;
}

::v-deep .el-table__body tr.current-row > td {
  background-color: #79a0f1 !important;
  color: #ffffff;
}

::v-deep .el-input__inner {
  padding-right: 40px;
}

.keyboard-mask {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99;
  left: 0;
  right: 0;
}

.wrapimin {
  position: relative;
  width: 100%;

  img {
    position: absolute;
    right: 7px;
    top: -3px;
    width: 45px;
    z-index: 2;
  }
}
</style>
