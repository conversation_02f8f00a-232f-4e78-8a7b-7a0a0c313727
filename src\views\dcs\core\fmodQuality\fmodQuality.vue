<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位：">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_code">
                    <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code" >
                        <span style="float: left">{{ item.station_code }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
                    </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
                <el-form-item label="有效标识：">
                    <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
                </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px"
              :inline="true">
              <!--表：sys_fmod_station-->
              <el-form-item :label="$t('lang_pack.recipequality.stationId')" prop="station_id">
                <!-- 工位号 -->
                <el-select v-model="form.station_id" size="small" placeholder="请选择工位">
                  <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des"
                    :value="item.station_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.qualityFrom')" prop="quality_from">
                <!-- 质量数据来源 -->
                <el-select v-model="form.quality_from" clearable filterable>
                  <el-option v-for="item in dict.QUALITY_FROM" :key="item.id" :label="item.label" :value="item.value" >
                      <span style="float: left">{{ item.label }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="采集项目" prop="tag_id">
                <!-- 采集项目 -->
                <el-input v-model.number="form.tag_id" readonly="readonly">
                  <div slot="append">
                    <el-popover v-model="customPopover1" placement="left" width="650">
                      <tagSelect ref="tagSelect" client-id-list="" :tag-id="form.tag_id" @chooseTag="handleChooseTag1" />
                      <el-button slot="reference">选择</el-button>
                    </el-popover>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item label="合格标志" prop="tag_quality_sign_id">
                <!-- 合格标志 -->
                <el-input v-model.number="form.tag_quality_sign_id" readonly="readonly">
                  <div slot="append">
                    <el-popover v-model="customPopover2" placement="left" width="650">
                      <tagSelect ref="tagSelect" client-id-list="" :tag-id="form.tag_quality_sign_id"
                        @chooseTag="handleChooseTag2" />
                      <el-button slot="reference">选择</el-button>
                    </el-popover>
                  </div>
                </el-input>
              </el-form-item>


              <el-form-item :label="$t('lang_pack.recipequality.groupOrder')" prop="group_order">
                <!-- 组号 -->
                <el-input type="number" v-model="form.group_order"></el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.groupName')" prop="group_name">
                <!-- 组描述 -->
                <el-input v-model="form.group_name" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.tagColOrder')" prop="tag_col_order">
                <!-- 列位置 -->
                <el-input type="number" v-model="form.tag_col_order" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.tagColInnerOrder')" prop="tag_col_inner_order">
                <!-- 排序 -->
                <el-input type="number" v-model="form.tag_col_inner_order" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.qualityFor')" prop="quality_for">
                <!-- 测量对象 -->
                <el-input v-model="form.quality_for" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.tagDes')" prop="tag_des">
                <!-- 采集项目名称 -->
                <el-input v-model="form.tag_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.tagUom')" prop="tag_uom">
                <!-- 采集项目单位 -->
                <el-input v-model="form.tag_uom" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.theoryValue')" prop="theory_value">
                <!-- 标准值 -->
                <el-input v-model="form.theory_value" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.downLimit')" prop="down_limit">
                <!-- 下限值 -->
                <el-input v-model="form.down_limit" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.upperLimit')" prop="upper_limit">
                <!-- 上限值 -->
                <el-input v-model="form.upper_limit" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.saveOrNot')" prop="quality_save_flag">
                <!-- 数据是否保存 -->
                  <fastCode fastcode_group_code="QUALITY_SAVE_FLAG" :fastcode_code.sync="form.quality_save_flag" control_type="radio" size="mini" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.recipequality.enableFlag')">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small"
            :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
            :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column type="selection" width="55" fixed  prop="quality_id"/>
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="creation_date"
              :label="$t('lang_pack.modownstatus.creationDate')" /> -->
              <el-table-column type="expand">
                <template slot-scope="props">
                    <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                    <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                    <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                    <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                    <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                    <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.quality_id }}</el-descriptions-item>
                    <el-descriptions-item label="工位ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{getStationDes(props.row.station_id)}}</el-descriptions-item>
                    <el-descriptions-item label="质量数据来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.QUALITY_FROM[props.row.quality_from]}}</el-descriptions-item>
                    <el-descriptions-item label="采集项目" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_id }}</el-descriptions-item>
                    <el-descriptions-item label="合格标志" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_quality_sign_id }}</el-descriptions-item>
                    <el-descriptions-item label="组号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.group_order }}</el-descriptions-item>
                    <el-descriptions-item label="组描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.group_name }}</el-descriptions-item>
                    <el-descriptions-item label="列位置" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_col_order }}</el-descriptions-item>
                    <el-descriptions-item label="列排序" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_col_inner_order }}</el-descriptions-item>
                    <el-descriptions-item label="测量对象" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.quality_for }}</el-descriptions-item>
                    <el-descriptions-item label="采集项目名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_des }}</el-descriptions-item>
                    <el-descriptions-item label="采集项目单位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tag_uom}}</el-descriptions-item>
                    <el-descriptions-item label="标准值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.theory_value }}</el-descriptions-item>
                    <el-descriptions-item label="下限值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.down_limit }}</el-descriptions-item>
                    <el-descriptions-item label="上限值" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.upper_limit }}</el-descriptions-item>
                    <el-descriptions-item label="当前质量数据是否保存" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{  dict.label.QUALITY_SAVE_FLAG[props.row.quality_save_flag]}}</el-descriptions-item>
                    <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                    </el-descriptions>
                </template>
              </el-table-column>
            <!-- 创建时间 -->
            <el-table-column :show-overflow-tooltip="true" :label="$t('lang_pack.recipequality.stationId')"
              prop="station_id" width="120" align='center'>
              <!-- 工位号 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStationDes(scope.row.station_id) }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" :label="$t('lang_pack.recipequality.qualityFrom')" width="100" align='center'
              prop="quality_from">
              <!-- 质量数据来源 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QUALITY_FROM[scope.row.quality_from] }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="tag_id"
              :label="$t('lang_pack.recipequality.tagId')" width="100" align='center'/>
            <!-- 采集项目 -->
            <el-table-column  :show-overflow-tooltip="true" prop="tag_quality_sign_id"
              :label="$t('lang_pack.recipequality.tagQualitySignId')" width="100" align='center'/>
            <!-- 合格标志 -->
            <el-table-column  :show-overflow-tooltip="true" prop="group_order"
              :label="$t('lang_pack.recipequality.groupOrder')" width="100" align='center'/>
            <!-- 组号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="group_name"
              :label="$t('lang_pack.recipequality.groupName')" width="80" align='center'/>
            <!-- 组描述 -->
            <el-table-column  :show-overflow-tooltip="true" prop="tag_col_order"
              :label="$t('lang_pack.recipequality.tagColOrder')" width="80" align='center'/>
            <!-- 列位置 -->
            <el-table-column  :show-overflow-tooltip="true" prop="tag_col_inner_order"
              :label="$t('lang_pack.recipequality.tagColInnerOrder')" width="80" align='center'/>
            <!-- 排序 -->
            <el-table-column  :show-overflow-tooltip="true" prop="quality_for"
              :label="$t('lang_pack.recipequality.qualityFor')" width="100" align='center'/>
            <!-- 测量对象 -->
            <el-table-column  :show-overflow-tooltip="true" prop="tag_des"
              :label="$t('lang_pack.recipequality.tagDes')" width="120" align='center'/>
            <!-- 采集项目名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="tag_uom"
              :label="$t('lang_pack.recipequality.tagUom')" width="120" align='center'/>
            <!-- 采集项目单位 -->
            <el-table-column  :show-overflow-tooltip="true" prop="theory_value"
              :label="$t('lang_pack.recipequality.theoryValue')" width="80" align='center'/>
            <!-- 标准值 -->
            <el-table-column  :show-overflow-tooltip="true" prop="down_limit"
              :label="$t('lang_pack.recipequality.downLimit')" width="80" align='center'/>
            <!-- 下限值 -->
            <el-table-column  :show-overflow-tooltip="true" prop="upper_limit"
              :label="$t('lang_pack.recipequality.upperLimit')" width="80" align='center'/>
            <!-- 上限值 -->
            <el-table-column  :label="$t('lang_pack.recipequality.enableFlag')" align="center"
              prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" prop="button" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
  
<script>
import crudFmodQuality from '@/api/dcs/core/fmodQuality/fmodQuality'
import crudStation from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  quality_id: '',
  station_id: '0',
  quality_from: '',
  tag_id: '',
  tag_quality_sign_id: '',
  group_order: '',
  group_name: '',
  tag_col_order: '',
  tag_col_inner_order: '',
  quality_for: '',
  tag_des: '',
  tag_uom: '',
  theory_value: '0',
  down_limit: '0',
  upper_limit: '0',
  quality_save_flag:'Y',
  enable_flag: 'Y'
}
export default {
  name: 'FMODQUALITY',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '质量数据采集配置',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'quality_id',
      // 排序
      sort: ['quality_id asc'],
      // CRUD Method
      crudMethod: { ...crudFmodQuality },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        quality_id: [{ required: true, message: '请选择质量数据采集配置ID', trigger: 'blur' }],
        station_id: [{ required: true, message: '请选择工位ID', trigger: 'blur' }],
        quality_from: [{ required: true, message: '请选择质量数据来源', trigger: 'blur' }],
        tag_id: [{ required: true, message: '请选择采集项目', trigger: 'blur' }],
        group_order: [{ required: true, message: '请选择组号', trigger: 'blur' }],
        group_name: [{ required: true, message: '请选择组描述', trigger: 'blur' }],
        tag_col_order: [{ required: true, message: '请选择列位置num', trigger: 'blur' }],
        tag_col_inner_order: [{ required: true, message: '请选择列位置内排序', trigger: 'blur' }],
        quality_for: [{ required: true, message: '请选择测量对象', trigger: 'blur' }],
        tag_des: [{ required: true, message: '请选择采集项目名称', trigger: 'blur' }],
        tag_uom: [{ required: true, message: '请选择采集项目单位', trigger: 'blur' }],
        quality_save_flag: [{ required: true, message: '请选择质量数据是否保存', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }],
      },
      customPopover1: false,
      customPopover2: false,
      // 车间数据
      currentWorkCenterCode: '', // 当前车间(单笔)
      // 产线数据
      currentProdLineCode: '', // 当前产线(单笔)
      prodLineData: [],
      // 工位数据
      stationData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG','QUALITY_SAVE_FLAG','QUALITY_FROM'],
  mounted: function () {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function () { 
    this.getStation()
  },
  methods: {
    // 获取工位的中文描述
    getStationDes(station_id) {
      var item = this.stationData.find(item => item.station_id === station_id)
      if (item !== undefined) {
        return item.station_des
      }
      return station_id
    },
    handleChooseTag1(tagId) {
      this.form.tag_id = tagId
      this.customPopover1 = false
    },
    handleChooseTag2(tagId) {
      this.form.tag_quality_sign_id = tagId
      this.customPopover2 = false
    },
    getStation(){
      const query = {
                userID: Cookies.get('userName')
            }
      crudStation.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if(defaultQuery.data.length > 0){
                            this.stationData = defaultQuery.data || []
                        }
                    }else{
                        this.stationData =[]
                        this.$message({
                            message: '工位查询异常',
                            type: 'error'
                        })
                    }
                })
                .catch(() => {
                    this.stationData =[]
                    this.$message({
                        message: '工位查询异常',
                        type: 'error'
                    })
            })
    },
    changeEnabled(data, val) {
            this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
        .then(() => {
          crudFmodQuality
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              quality_id: data.quality_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
  }
}
</script>
  