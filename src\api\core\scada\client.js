import request from '@/utils/request'

// 查询实例
export function selScadaClient(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaClientSel',
    method: 'post',
    data
  })
}
// 新增实例
export function insScadaClient(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaClientIns',
    method: 'post',
    data
  })
}
// 修改实例
export function updScadaClient(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaClientUpd',
    method: 'post',
    data
  })
}
// 删除实例
export function delScadaClient(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaClientDel',
    method: 'post',
    data
  })
}

// 查询实例(树)
export function scadaClientTree(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaClientTree',
    method: 'post',
    data
  })
}
export default { selScadaClient, insScadaClient, updScadaClient, delScadaClient,
  scadaClientTree }
