import request from '@/utils/request'

// 模组范围约束模板查询
export function mesRecipeMzLimitMustSel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMzLimitMustSel',
    method: 'post',
    data
  })
}

// 模组范围约束模板修改
export function mesRecipeMzLimitMustUpd(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMzLimitMustUpd',
    method: 'post',
    data
  })
}

// 模组范围约束模板修改
export function mesRecipeMzLimitMustEnableFlagUpd(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeMzLimitMustEnableFlagUpd',
    method: 'post',
    data
  })
}

export default { mesRecipeMzLimitMustSel, mesRecipeMzLimitMustUpd, mesRecipeMzLimitMustEnableFlagUpd }
