<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="切割计划编号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="钢板编号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="材质:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="规格:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="切割设备:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="状态：">
                <el-select v-model="query.lock_flag" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'是',value:'Y'},{id:'2',label:'否',value:'N'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="tableData"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="qgjhbh" label="切割计划编号" />
            <el-table-column :show-overflow-tooltip="true" prop="gbbh" label="钢板编号" />
            <el-table-column :show-overflow-tooltip="true" prop="cz" label="材质" />
            <el-table-column :show-overflow-tooltip="true" prop="gg" label="规格" />
            <el-table-column :show-overflow-tooltip="true" prop="sl" label="数量" />
            <el-table-column :show-overflow-tooltip="true" prop="qgsb" label="切割设备" />
            <el-table-column :show-overflow-tooltip="true" prop="sj" label="上料时间" />
            <el-table-column :show-overflow-tooltip="true" prop="zt" width="120" label="状态" />
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsMapStockCell from '@/api/dcs/project/wms/wmsMapStockCell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_INVENTORY_LIST',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '入库到货清单',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapStockCell },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      tableData: [
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '上料中' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '待执行' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '待执行' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '待执行' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '待执行' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '上料中' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '上料中' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '已切割' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '已切割' },
        { qgjhbh: 'CUT-20250514-001', gbbh: 'PL001', cz: 'AH36', gg: '20*8000*800', sl: '10', qgsb: '等离子切割#1', sj: '2025-05-14 18:08:10', zt: '已切割' }
      ]
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>

    <style>
    .table-descriptions-label {
    width: 150px;
    }
    .table-descriptions-content {
    width: 150px;
    }
    </style>
