import request from '@/utils/request'

// 查询程序菜单
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFunctionSel',
    method: 'post',
    data
  })
}
// 新增程序菜单
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFunctionIns',
    method: 'post',
    data
  })
}
// 修改程序菜单
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFunctionUpd',
    method: 'post',
    data
  })
}
// 删除程序菜单
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFunctionDel',
    method: 'post',
    data
  })
}

// 程序LOV查询
export function lovFunction(data) {
  return request({
    url: 'aisEsbWeb/core/system/SysFunctionLov',
    method: 'post',
    data
  })
}

// 程序IFrame嵌套查询
export function selIframe(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFunctionIframeSel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, selIframe, lovFunction }
