<template>
  <div>
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8">
            <div class="formChild col-md-4 ">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 ">
              <el-form-item label="设备编码/描述：">
                <el-input v-model="query.device_cod_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 ">
              <el-form-item label="物料编码/描述：">
                <el-input v-model="query.material_code_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :header-cell-style="{ lineHeight:'20px' }" :cell-style="crud.cellStyle" height="350" highlight-current-row @selection-change="crud.selectionChangeHandler">

            <el-table-column  :show-overflow-tooltip="true" prop="recipe_type" label="配方类型" />
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_name" label="配方描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="recipe_version" label="版本号" />
            <el-table-column  :show-overflow-tooltip="true" prop="device_code" label="设备编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="device_des" label="设备描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_code" label="物料编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_des" label="物料描述" />
            <el-table-column  label="操作" width="70" align="center" fixed="left">
              <template slot-scope="scope">
                <el-link class="linkItem" type="primary" @click="chooseRecipe(scope.row)">选择</el-link>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
          <el-dialog title="确认切换配方信息" append-to-body :visible.sync="repiceDetailDialogVisible" width="600px" top="100px">
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table1" size="small" :data="repiceDetailData" style="width: 100%" height="450" highlight-current-row>
              <el-table-column  :show-overflow-tooltip="true" prop="parameter_code" label="参数编码" />
              <el-table-column  :show-overflow-tooltip="true" prop="parameter_des" label="参数描述" />
              <el-table-column  :show-overflow-tooltip="true" prop="parameter_val" label="参数值" />
            </el-table>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="repiceDetailDialogVisible=false">取消</el-button>
              <el-button type="primary" size="small" icon="el-icon-check" :loading="confirmWriteRepiceBtnLoading" @click="confirmWriteRepice">确认</el-button>
            </div>
          </el-dialog>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import eapRecipe from '@/api/eap/core/eapRecipe'
import { sel as selRecipeDetail } from '@/api/eap/core/eapRecipeDetail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
const defaultForm = {
  recipe_id: '',
  recipe_name: '',
  recipe_version: '',
  device_code: '',
  device_des: '',
  material_code: '',
  material_des: '',
  recipe_type: '',
  enable_flag: 'Y'
}

export default {
  name: 'EAP_DEVICE_MONITOR_RECIPE',
  components: { rrOperation, pagination },
  props: {
    cell_ip: {
      type: String,
      default: ''
    },
    webapi_port: {
      type: String,
      default: ''
    }

  },
  cruds() {
    return CRUD({
      title: '配方信息',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id asc'],
      // CRUD Method
      crudMethod: { ...eapRecipe },
      query: { enable_flag: 'Y' },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: { },
      rules: { },
      repiceDetailDialogVisible: false,
      repiceDetailData: [],
      confirmWriteRepiceBtnLoading: false
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  watch: {
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {

  },
  methods: {
    chooseRecipe(row) {
      this.repiceDetailDialogVisible = true
      const query = {
        user_name: Cookies.get('userName'),
        recipe_id: row.recipe_id,
        enable_flag: 'Y'
      }
      selRecipeDetail(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.count > 0) {
              this.repiceDetailData = defaultQuery.data
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    confirmWriteRepice() {
      if (this.repiceDetailData.length <= 0) {
        this.$message({ message: '无配方数据，不能执行操作', type: 'info' })
        return
      }
      this.confirmWriteRepiceBtnLoading = true
      var writeData = []
      this.repiceDetailData.forEach(item => {
        if (item.client_code !== '') {
          var writeTag = {}
          writeTag.tag_key = item.tag_key
          writeTag.tag_value = item.parameter_val

          var writeItem = writeData.filter(a => a.client_code === item.client_code)
          if (writeItem.length > 0) {
            writeItem[0].tag_list.push(writeTag)
          } else {
            writeItem = {}
            writeItem.user_name = Cookies.get('userName')
            writeItem.client_code = item.client_code
            writeItem.isAsyn = true
            writeItem.tag_list = []
            writeItem.tag_list.push(writeTag)
            writeData.push(writeItem)
          }
        }
      })
      console.log(writeData)
      var method = '/cell/core/scada/CoreScadaWriteTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapi_port + method
      } else {
        path = 'http://' + this.cell_ip + ':' + this.webapi_port + method
      }
      axios
        .post(path, writeData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          this.confirmWriteRepiceBtnLoading = false
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            this.repiceDetailDialogVisible = false
            this.$message({ message: '写入配方成功', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(ex => {
          this.confirmWriteRepiceBtnLoading = false
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
