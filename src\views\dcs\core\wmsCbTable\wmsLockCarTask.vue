<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="天车编码:">
                                <!-- 天车编码 -->
                                <el-input v-model="query.car_code" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="库区域:">
                                <!-- 库区域 -->
                                <el-select v-model="query.ware_house" clearable filterable>
                                    <el-option v-for="item in dict.WARE_HOUSE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务号:">
                                <!-- 任务号 -->
                                <el-input v-model="query.task_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="型号:">
                                <!-- 型号 -->
                                <el-select v-model="form.model_type" clearable filterable>
                                    <el-option v-for="item in modelList" :key="item.model_id" :label="item.model_type"
                                        :value="item.model_type" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务开始时间:">
                                <!-- 任务开始时间 -->
                                <el-date-picker
                                    v-model="query.task_start_date"
                                    type="datetimerange"
                                    size="small"
                                    align="right"
                                    unlink-panels
                                    range-separator="~"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="任务结束时间:">
                                <!-- 任务结束时间 -->
                                <el-date-picker
                                    v-model="query.task_end_date"
                                    type="datetimerange"
                                    size="small"
                                    align="right"
                                    unlink-panels
                                    range-separator="~"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.wmsCbTable.loopCode')" prop="cycle_code">
                                <!-- 循环代码 -->
                                <el-input v-model="form.cycle_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.libraryArea')" prop="ware_house">
                                <!-- 库区域 -->
                                <el-input v-model="form.ware_house" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.crownBlockCode')" prop="car_code">
                                <!-- 天车编码 -->
                                <el-input v-model="form.car_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.mainTaskID')" prop="task_id">
                                <!-- 任务ID -->
                                <el-input v-model="form.task_id" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.modelID')" prop="model_id">
                                <!-- 型号ID -->
                                <el-input v-model="form.model_id" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.taskNumber')" prop="task_num">
                                <!-- 任务号 -->
                                <el-input v-model="form.task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.model_type')" prop="model_type">
                                <!-- 型号 -->
                                <el-input v-model="form.model_type" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.taskExecutionStatus')" prop="execute_status">
                                <!-- 任务执行状态 -->
                                <el-input v-model="form.execute_status" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.startingStorageLocation')" prop="from_stock_code">
                                <!-- 起始库位 -->
                                <el-input v-model="form.from_stock_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.targetInventoryLocation')" prop="to_stock_code">
                                <!-- 目标库位 -->
                                <el-input v-model="form.to_stock_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.timeConsuming')" prop="cost_time">
                                <!-- 消耗时间(秒) -->
                                <el-input v-model="form.cost_time" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.isTheModelChecked')" prop="check_model_flag">
                                <!-- 是否检查型号 -->
                                <el-radio-group v-model="form.check_model_flag">
                                    <el-radio label="0">是</el-radio>
                                    <el-radio label="1">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.wmsCbTable.checkModelResults')" prop="check_model_result">
                                <!-- 检查型号结果 -->
                                <el-input v-model="form.check_model_result" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" prop="car_task_id" fixed/>
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                <el-descriptions-item label="创建者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                                <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                                <el-descriptions-item label="修改者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                                <el-descriptions-item label="时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_task_id }}</el-descriptions-item>
                                <el-descriptions-item label="调度任务循环代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cycle_code }}</el-descriptions-item>
                                <el-descriptions-item label="天车编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.car_code }}</el-descriptions-item>
                                <el-descriptions-item label="库区域" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.ware_house }}</el-descriptions-item>
                                <el-descriptions-item label="任务ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_id }}</el-descriptions-item>
                                <el-descriptions-item label="型号ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_id }}</el-descriptions-item>
                                <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                                <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                                <el-descriptions-item label="库位ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_d_id }}</el-descriptions-item>
                                <el-descriptions-item label="任务执行状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.EXECUTE_STATUS[props.row.execute_status] }}</el-descriptions-item>
                                <el-descriptions-item label="任务执行错误信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.execute_error_msg }}</el-descriptions-item>
                                <el-descriptions-item label="起始库位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.from_stock_code }}</el-descriptions-item>
                                <el-descriptions-item label="目标库位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.to_stock_code }}</el-descriptions-item>
                                <el-descriptions-item label="任务开始时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_start_date }}</el-descriptions-item>
                                <el-descriptions-item label="任务结束时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_end_date }}</el-descriptions-item>
                                <el-descriptions-item label="消耗时间(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cost_time }}</el-descriptions-item>
                                <el-descriptions-item label="是否检查型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_flag == 'Y' ? '是':'否' }}</el-descriptions-item>
                                <el-descriptions-item label="检查辊道时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.finish_check_gd_date }}</el-descriptions-item>
                                <el-descriptions-item label="检查型号时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_date }}</el-descriptions-item>
                                <el-descriptions-item label="检查型号结果" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.check_model_result }}</el-descriptions-item>
                                <el-descriptions-item label="告之启动时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_start_date }}</el-descriptions-item>
                                <el-descriptions-item label="告之取消时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_cancel_date }}</el-descriptions-item>
                                <el-descriptions-item label="告之暂停时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_stop_date }}</el-descriptions-item>
                                <el-descriptions-item label="告之启动者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_start_by }}</el-descriptions-item>
                                <el-descriptions-item label="告之取消者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_cancel_by }}</el-descriptions-item>
                                <el-descriptions-item label="告之暂停者" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.tell_stop_by }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                            </el-table-column>
                        <!-- 库区域 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="ware_house"
                            :label="$t('lang_pack.wmsCbTable.libraryArea')" width="90" align='center'/>
                        <!-- 天车编码 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="car_code"
                            :label="$t('lang_pack.wmsCbTable.crownBlockCode')" width="80" align='center'/>
                        <!-- 任务ID -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_id"
                            :label="$t('lang_pack.wmsCbTable.mainTaskID')" width="80" align='center'/>
                        <!-- 型号ID -->
                        <el-table-column  :show-overflow-tooltip="true" prop="model_id"
                            :label="$t('lang_pack.wmsCbTable.modelID')" width="80" align='center'/>
                        <!-- 任务号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_num"
                            :label="$t('lang_pack.wmsCbTable.taskNumber')" width="80" align='center'/>
                        <!-- 型号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="model_type"
                            :label="$t('lang_pack.wmsCbTable.model_type')" width="80" align='center'/>
                        <!-- 任务执行状态 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="execute_status"
                            :label="$t('lang_pack.wmsCbTable.taskExecutionStatus')" width="100" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.EXECUTE_STATUS[scope.row.execute_status] }}
                            </template>
                        </el-table-column>
                        <!-- 起始库位 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="from_stock_code"
                            :label="$t('lang_pack.wmsCbTable.startingStorageLocation')" width="80" align='center'/>
                        <!-- 目标库位 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="to_stock_code"
                            :label="$t('lang_pack.wmsCbTable.targetInventoryLocation')" width="80" align='center'/>
                        <!-- 任务开始时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_start_date"
                            :label="$t('lang_pack.wmsCbTable.taskStartTime')" width="150" align='center'/>
                        <!-- 任务结束时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="task_end_date"
                            :label="$t('lang_pack.wmsCbTable.taskEndTime')" width="150" align='center'/>
                        <!-- 消耗时间(秒) -->
                        <el-table-column  :show-overflow-tooltip="true" prop="cost_time"
                            :label="$t('lang_pack.wmsCbTable.timeConsuming')" width="100" align='center'/>
                        <!-- 是否检查型号 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="check_model_flag"
                            :label="$t('lang_pack.wmsCbTable.isTheModelChecked')" width="100" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.check_model_flag == 'Y' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <!-- 检查辊道时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="finish_check_gd_date"
                            :label="$t('lang_pack.wmsCbTable.checkTheRollerTableTime')" width="140" align='center'/>
                         <!-- 检查型号时间 -->
                         <el-table-column  :show-overflow-tooltip="true" prop="check_model_date"
                            :label="$t('lang_pack.wmsCbTable.checkModelTime')" width="140" align='center'/>
                        <!-- 检查型号结果 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="check_model_result"
                            :label="$t('lang_pack.wmsCbTable.checkModelResults')" width="120" align='center'/>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import crudWmsLockCarTask from '@/api/dcs/core/wmsCbTable/wmsLockCarTask'
import crudFmodModel from '@/api/dcs/core/fmodModel/fmodModel'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    car_task_id: '',
    cycle_code: '',
    car_code: '',
    ware_house: '',
    model_id: '',
    task_num: '',
    model_type: '',
    stock_d_id: '',
    execute_status: '',
    execute_error_msg: '',
    from_stock_code: '',
    to_stock_code: '',
    task_start_date: '',
    task_end_date: '',
    cost_time: '',
    finish_check_gd_flag: '1',
    finish_check_gd_date: '',
    execute_error_msg: '',
    check_model_flag: '1',
    check_model_date: '',
    check_model_result: '',
    enable_flag: 'Y'
}
export default {
    name: 'WMSLOCKCAARTASK',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: 'WMS调度任务表',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'car_task_id',
            // 排序
            sort: ['car_task_id asc'],
            // CRUD Method
            crudMethod: { ...crudWmsLockCarTask },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                cycle_code: [{ required: true, message: '请选择调度任务循环代码', trigger: 'blur' }],
                ware_house: [{ required: true, message: '请选择库区域', trigger: 'blur' }],
                car_code: [{ required: true, message: '请选择天车编码', trigger: 'blur' }],
                task_id: [{ required: true, message: '请选择任务ID', trigger: 'blur' }],
                model_id: [{ required: true, message: '请选择型号ID', trigger: 'blur' }],
                task_num: [{ required: true, message: '请选择任务号', trigger: 'blur' }],
                model_type: [{ required: true, message: '请选择型号', trigger: 'blur' }],
                execute_status: [{ required: true, message: '请选择任务执行状态', trigger: 'blur' }],
                from_stock_code: [{ required: true, message: '请选择起始库位', trigger: 'blur' }],
                to_stock_code: [{ required: true, message: '请选择目标库位', trigger: 'blur' }],
            },
            modelList:[],
        }
    },
    dicts: ['ENABLE_FLAG','WARE_HOUSE','EXECUTE_STATUS'],
    mounted: function () {
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
        this.getModelType()
    },
    methods: {
        getModelType(){
            const query = {
                userID: Cookies.get('userName')
            }
            crudFmodModel.sel(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if(defaultQuery.data.length > 0){
                            this.modelList = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '型号查询异常',
                        type: 'error'
                    })
            })
        },
    }
}
</script>
