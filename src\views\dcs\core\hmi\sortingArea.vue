<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-card >
                    <div slot="header" class="wrapTextSelect">
                        <span>分拣任务信息</span>
                    </div>
                    <el-table border @header-dragend="tableHeaderDragend" ref="sortTable" style="width: 100%;"
                        v-loading="loading" :data="tableData" :row-key="row => row.id"
                        :highlight-current-row="highlightCurrentRow" :height="height">
                        <!-- 分拣工位 -->
                        <el-table-column  :show-overflow-tooltip="true" width="100" align="center" prop="station_code"
                            :label="$t('lang_pack.sortingArea.SortingStation')" />
                        <!-- 任务号 -->
                        <el-table-column :show-overflow-tooltip="true" width="100" align="center" prop="task_num"
                            :label="$t('lang_pack.cuttingZone.TaskNumber')" />
                        <!-- 钢板型号 -->
                        <el-table-column :show-overflow-tooltip="true" width="100" align="center" prop="model_type"
                            :label="$t('lang_pack.cuttingZone.SteelPlateModel')" />
                        <!-- 分拣类型 -->
                        <el-table-column :show-overflow-tooltip="true" width="100" align="center" prop="station_des"
                            :label="$t('lang_pack.sortingArea.SortType')" />
                        <!-- 计划数量 -->
                        <el-table-column :show-overflow-tooltip="true" width="100" align="center" prop="plan_count"
                            :label="$t('lang_pack.sortingArea.PlannedQuantity')" />
                        <!-- 实际数量 -->
                        <el-table-column :show-overflow-tooltip="true" width="100" align="center" prop="actual_count"
                            :label="$t('lang_pack.sortingArea.ActualQuantity')" />
                        <!-- 文件名称 -->
                        <el-table-column :show-overflow-tooltip="true" width="100" align="center" prop="material_draw"
                            label="文件名称" />
                        <!-- 手动操作 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" width="220"
                            :label="$t('lang_pack.cuttingZone.ManualOperation')" fixed="right">
                            <template slot-scope="scope">
                                <el-button slot="reference" :disabled="scope.row.station_code != 'G14'" type="text" size="small" @click="changeNum(scope.row)">手工录入</el-button>
                                <el-button slot="reference" type="text" size="small" @click="forceHandle(scope.row, '1')">强制放行</el-button>
                                <el-dropdown>
                                    <span class="el-button el-button--text el-button--small">更多<i class="el-icon-arrow-down el-icon--right"></i></span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item :disabled="!scope.row.task_num" @click.native="lookPartDetails(scope.row)">查看零件明细</el-dropdown-item>
                                        <el-dropdown-item @click.native="forceHandle(scope.row, '2')">强制分拣完成</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                <el-card  style="height: 250px;margin: 10px 0;">
                    <div slot="header" class="wrapTextSelect">
                        <span>流程管理</span>
                    </div>
                    <div class="wrapRowss">
                        <el-row :gutter="12" class="wrapRowItem row" :style="{overflowX:'auto'}">
                            <el-col  :span="24"
                                class=" nomargin">
                                <div class="flow_main" v-for="item in flowTaskData" :key="item.me_flow_task_id">
                                    <div class="flow_title">{{ item.flow_main_des }}</div>
                                    <el-dropdown trigger="click" placement="bottom" class="flow_menu">
                                        <span type="text" class="text">...</span>
                                        <el-dropdown-menu slot="dropdown">
                                            <span>
                                                <el-dropdown-item>取消流程</el-dropdown-item>
                                            </span>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                    <div class="flow_info overflowText">时间：{{ item.start_date }}</div>
                                    <el-tooltip class="item" effect="dark" :content="item.task_info" placement="top">
                                        <div class="flow_info overflowText">信息：{{ item.task_info }}</div>
                                    </el-tooltip>
                                    <div class="flow_info">步骤：{{ item.step_mod_des }}&nbsp;&nbsp;<el-button type="text"
                                            class="flow_button" @click="opendFlowTask(item)">查看</el-button></div>
                                    <el-tooltip class="item" effect="dark" :content="item.log_msg" placement="top">
                                        <div class="flow_info overflowText">日志：{{ item.log_msg }}</div>
                                    </el-tooltip>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card >
                    <div slot="header" class="wrapTextSelect">
                        <span>料框任务信息</span>
                    </div>
                    <el-table border @header-dragend="tableHeaderDragend" ref="table" v-loading="loading" :data="cutData"
                        :row-key="row => row.id" :highlight-current-row="highlightCurrentRow" :height="ordHeight">
                        <!-- 料框明细 -->
                        <el-table-column :show-overflow-tooltip="true" align="center"
                            :label="$t('lang_pack.sortingArea.MaterialFrameDetails')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small"  @click="lookMaterialDetails(scope.row)">查看料框明细</el-button>
                            </template>
                        </el-table-column>
                        <!-- 料框号 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="lkh"
                            :label="$t('lang_pack.sortingArea.MaterialFrameNumber')" />
                        <!-- 料框零件 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="lklj"
                            :label="$t('lang_pack.sortingArea.MaterialFrameParts')" />
                        <!-- 数量 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="sl"
                            :label="$t('lang_pack.sortingArea.Number')" />
                        <!-- 状态 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="zt"
                            :label="$t('lang_pack.sortingArea.Status')" />
                        <!-- AGV状态 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="AGVzt"
                            :label="$t('lang_pack.sortingArea.AGVStatus')" />
                        <!-- 手动操作 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" width="240" fixed="right"
                            :label="$t('lang_pack.cuttingZone.ManualOperation')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small">盘点</el-button>
                                <el-button slot="reference" type="text" size="small">人工清框</el-button>
                                <el-button slot="reference" type="text" size="small">AGV呼叫</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12" class="warehouse">
                <el-card>
                    <div slot="header" class="wrapTextSelect">
                        <span>线尾下线履历</span>
                    </div>
                    <el-table border ref="table" v-loading="loading"
                        :data="lineTailData"  :highlight-current-row="highlightCurrentRow"
                        :height="abnormalHeight">
                        <!-- 任务号 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="task_num"
                            :label="$t('lang_pack.taskList.TaskNumber')" />
                        <!-- 钢板型号 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="model_type"
                            :label="$t('lang_pack.taskList.SteelPlateModel')" />
                        <!-- 过站检查状态 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="quality_sign"
                            :label="$t('lang_pack.sortingArea.StationInspectionStatus')" />
                        <!-- 报工状态-->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="bg_flag"
                            :label="$t('lang_pack.sortingArea.WorkReportingStatus')" >
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ scope.row.bg_flag == 'Y' ? '已报工' : '未报工' }}
                            </template>
                        </el-table-column>
                        <!-- 报工异常 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="bg_check_msg"
                            :label="$t('lang_pack.sortingArea.AbnormalWorkReporting')" />
                        <!-- 过站明细 -->
                        <el-table-column :show-overflow-tooltip="true" align="center"
                            :label="$t('lang_pack.sortingArea.TransitDetails')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small" :disabled="!scope.row.task_num" @click="lookStationDetails(scope.row)">查看过站明细</el-button>
                            </template>
                        </el-table-column>
                        <!-- 手动操作 -->
                        <el-table-column :show-overflow-tooltip="true" align="center"
                            :label="$t('lang_pack.cuttingZone.ManualOperation')">
                            <template slot-scope="scope">
                                <el-button slot="reference" type="text" size="small" @click="forceHandle(scope.row, '3')">手动报工</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card>
                    <div slot="header" class="wrapTextSelect">
                        <span>设备异常信息</span>
                    </div>
                    <el-table border ref="table" v-loading="loading" :data="abnormalData" :row-key="row => row.id"
                        :highlight-current-row="highlightCurrentRow" :height="abnormalHeight">
                        <!-- 设备名称 -->
                        <el-table-column  :show-overflow-tooltip="true" align="center" prop="sbmc"
                            :label="$t('lang_pack.cuttingZone.DeviceName')" />
                        <!-- 报警分类 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="bjfl"
                            :label="$t('lang_pack.cuttingZone.AlarmClassification')" />
                        <!-- 报警级别 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="bjjb"
                            :label="$t('lang_pack.cuttingZone.AlarmLevel')" />
                        <!-- 报警信息 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="bjxx"
                            :label="$t('lang_pack.cuttingZone.AlarmMessage')" />
                        <!-- 报警发生时间 -->
                        <el-table-column :show-overflow-tooltip="true" align="center" prop="bjfssj"
                            :label="$t('lang_pack.cuttingZone.AlarmOccurrenceTime')" />
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
        <el-dialog title="更改实际数量" :visible.sync="changeNumVisible" width="40%" :before-close="handleClose">
            <el-form  ref="ruleForm" :model="formRules" :rules="rules" :inline="true" size="small" label-width="100px">
                <el-form-item label="录入数量:" prop="sorting_num">
                <!-- 录入数量 -->
                    <el-input type="number" v-model.number="formRules.sorting_num" clearable size="small" />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" :loading="sortLoading" @click="handleOk">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="tableTitle" :visible.sync="visible" width="30%" :before-close="handleClose">
            <el-form ref="query" :inline="true" :model="form" size="small" v-if="tableType == 1">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-12 col-12">
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.CurrentSortingStation')">
                                <!-- 当前分拣工位 -->
                                <el-input class="filter-item" v-model="form.dqfjgw" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.TargetTransmissionStation')">
                                <!-- 目标传输工位 -->
                                <el-input class="filter-item" v-model="form.mbcsgw" />
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-form>
            <el-form ref="query" :inline="true" :model="form" size="small" v-if="tableType == 2">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-12 col-12">
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.CurrentSortingStation')" class="fromP">
                                <!-- 当前分拣工位 -->
                                <el-input class="filter-item" v-model="form.dqfjgw" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.MandatoryReportingInterface')" class="fromP">
                                <!-- 强制上报接口 -->
                                <el-input class="filter-item" v-model="form.qzsbjk" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.IsAutomaticSortingCompleted')" class="fromP">
                                <!-- 是否自动分拣完成 -->
                                <el-input class="filter-item" v-model="form.sfzdfjwc" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.TargetTransmissionStation')" class="fromP">
                                <!-- 目标传输工位 -->
                                <el-input class="filter-item" v-model="form.mbcsgw" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item label="当前分拣JSON图纸" class="fromP">
                                <!-- 当前分拣JSON图纸 -->
                                <el-input class="filter-item" v-model="form.dqfjdxftz" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.CurrentSortingSteelPlateModel')" class="fromP">
                                <!-- 当前分拣钢板型号 -->
                                <el-input class="filter-item" v-model="form.dqfjgbxh" />
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-form>
            <el-form ref="query" :inline="true" :model="form" size="small" v-if="tableType == 3">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-12 col-12">
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.WorkReportingInterfaceAddress')" class="fromP">
                                <!-- 报工接口地址 -->
                                <el-input class="filter-item" v-model="form.dqfjgw" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.DXFFileName')" class="fromP">
                                <!-- DXF文件名称 -->
                                <el-input class="filter-item" v-model="form.mbcsgw" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.GNCFileName')" class="fromP">
                                <!-- GNCFileName -->
                                <el-input class="filter-item" v-model="form.mbcsgw" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-12 col-12">
                            <el-form-item :label="$t('lang_pack.sortingArea.PersonInChargeOfWorkReporting')" class="fromP">
                                <!-- 报工责任人 -->
                                <el-input class="filter-item" v-model="form.mbcsgw" />
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="handleOk">确 定</el-button>
            </span>
        </el-dialog>
        <selectModal ref="selectModal" :dict="dict"></selectModal>
        <selectModal ref="stationSelectModal" :dict="dict"></selectModal>
    </div>
</template>
<script>
import crudSortingArea from '@/api/dcs/core/hmi/sortingArea'
import Cookies from 'js-cookie'
import { selCellIP } from '@/api/core/center/cell'
import flowTask from '@/views/core/flow/task/task'
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import selectModal from '@/components/selectModal'
export default {
    name: 'SORTTING_AREA',
    components:{selectModal},
    data() {
        return {
            height: 0,
            ordHeight: 0,
            abnormalHeight: 0,
            historyHeight: document.documentElement.clientHeight - 270,
            loading: false,
            form: {
                dqfjgw:'10',
                mbcsgw:'20',
            },
            flowTaskData: [],
            highlightCurrentRow: false,
            tableData: [],
            cutData: [],
            lineTailData: [],
            abnormalData: [],
            width: '70%',
            title: '',
            type: '',
            tableTitle: '',
            tableType: '',
            visible: false,
            changeNumVisible:false,
            formRules:{
                part_type:'',
                sorting_num:''
            },
            sortLoading:false,
            rules:{
                part_type:[{ required: true, message: '请选择零件类型', trigger: 'blur' }],
                sorting_num: [{ required: true, message: '请录入数量', trigger: 'blur' }]
            },
            cellIp: '', // 单元IP
            webapiPort: '', // 单元API端口号
            mqttPort: '', // MQTT端口号
            flowTaskDialogVisible: false,
            currentFlowTask: {},
            timer:null,
        }
    },
    created() {
        this.getPower() //调整表格低分辨率下的几种情况
        this.getRecordResume()
        this.getSortingList()
        this.getCellIp() //获取流程管理
    },
    dicts: ['PART_TYPE'],
    mounted: function () {
        const that = this
        that.$refs.sortTable.doLayout()
        this.timer = setInterval(function () {
            // that.getFlowTaskData()
        }, 5000)
        window.onresize = function temp() {
            that.getPower() //调整表格低分辨率下的几种情况
        }
    },
    beforeDestroy(){
        if(this.timer){
            clearInterval(this.timer)
        }
    },
    methods: {
        getPower(){
            const height = document.documentElement.clientHeight;
            const conditions = [
                { max: 620, height: 430, ordHeight: 170, abnormalHeight: 430 },
                { max: 700, height: 450, ordHeight: 190, abnormalHeight: 480 },
                { max: 800, height: 480, ordHeight: 220, abnormalHeight: 540 },
                { max: 900, height: 530, ordHeight: 270, abnormalHeight: 590 },
            ];
            const condition = conditions.find((condition) => height <= condition.max);
            if (condition) {
                this.height = height - condition.height;
                this.ordHeight = height - condition.ordHeight;
                this.abnormalHeight = height - condition.abnormalHeight;
            } else {
                this.height = height - 700;
                this.ordHeight = height - 440;
                this.abnormalHeight = height - 700;
            }
        },
        // 更改实际数量
        changeNum(row){
            this.formRules = Object.assign(this.formRules,row)
            this.changeNumVisible = true
            this.$nextTick(()=>{
                this.$refs.ruleForm.resetFields()
            })
        },
        getRecordResume(){
            crudSortingArea.recordResume({}).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0 && defaultQuery.data.length >0) {
                    this.lineTailData = defaultQuery.data
                }
            })
            .catch((err) => {
                this.lineTailData = []
                this.$message({
                    message: '查询异常',
                    type: 'error'
                })
            })
        },
        getSortingList(){
            crudSortingArea.sortingList({}).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    this.tableData = defaultQuery.data
                } else {
                    this.tableData = []
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                }
            })
            .catch((err) => {
                this.tableData = []
                this.$message({
                    message: '查询异常',
                    type: 'error'
                })
            })
        },
        changeState(index, row) {

        },
        tableHeaderDragend(a,b,c,d) {
          this.$refs.sortTable.doLayout()
            // this.$nextTick(() => {
            //     dom.doLayout()
            // })
        },
        lookPartDetails(row){
            this.$refs.selectModal.open({
                type: 'ckljmx',
                checkType: '',
                search: {
                    mo_id:row.mo_id
                }
            })
        },
        lookStationDetails(row){
            this.$refs.stationSelectModal.open({
                type: 'ckgzmx',
                checkType: '',
                search: {
                    task_num:row.task_num
                }
            })
        },
        lookMaterialDetails(){

        },
        cancelTask() {
            this.$confirm(`确认取消当前任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {

                })
                .catch(() => { })
        },
        Reissue() {
            this.$confirm(`确认重新下发当前任务?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {

                })
                .catch(() => { })
        },
        handleClose() {
            this.visible = false
            this.changeNumVisible = false
        },
        transferHandle(record, type) {
            this.tableTitle = '缓存位到分拣区'
            this.visible = true
        },
        forceHandle(record, type) {
            const obj = {
                '1': () => { this.tableTitle = '强制放行' },
                '2': () => { this.tableTitle = '强制分拣完成' },
                '3': () => { this.tableTitle = '手动报工' },
            }
            this.tableType = type
            this.visible = true
            return (obj[type] || eval(() => this.tableTitle = ''))()
        },
        handleOk() {
            this.sortLoading = true
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    crudSortingArea.sortingNum(this.formRules).then(res=>{
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.code === 0) {
                            this.$message({
                                message: '录入成功',
                                type: 'success'
                            })
                            this.getSortingList()
                            this.handleClose()
                        }else{
                            this.$message({
                                message: defaultQuery.error,
                                type: 'error'
                            })
                        }
                    })
                    .catch((err) => {
                        this.$message({
                            message: '录入异常',
                            type: 'error'
                        })
                    })
                    .finally(()=>{
                        this.sortLoading = false
                    })
                } else {
                    return false;
                }
            });
        },
        getCellIp() {
            const query = {
                user_name: Cookies.get('userName'),
                cell_id: this.cellId,
                current_ip: window.location.hostname
            }
            selCellIP(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        const ipInfo = JSON.parse(defaultQuery.result)
                        this.cellIp = ipInfo.ip
                        this.webapiPort = ipInfo.webapi_port
                        this.mqttPort = ipInfo.mqtt_port
                        this.getFlowTaskData()
                    } else {
                        this.$message({ message: defaultQuery.msg, type: 'error' })
                    }
                })
                .catch(() => {
                    this.$message({ message: '查询异常', type: 'error' })
                })
        },
        getFlowTaskData() {
            const data = {
                line_section_code: 'SORT',
            }
            crudLoadAreaOper.flowTask(data)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        this.flowTaskData = defaultQuery.data || []
                    } else {
                        this.flowTaskData = []
                        this.$message({ message: defaultQuery.data.msg, type: 'warning' })
                    }
                })
                .catch(ex => {
                    this.flowTaskData = []
                    this.$message({ message: '查询异常：' + ex, type: 'error' })
                })
        },
        opendFlowTask(item) {
            this.currentFlowTask = item
            this.flowTaskDialogVisible = true
        }
    }

}
</script>
<style scoped lang="less">
.app-container {
    ::v-deep .el-card__header,
    ::v-deep .el-card__body {
        padding: 10px !important;
    }

    .inbOutNum {
        margin: 0 5px;
        color: #409eff;
        cursor: pointer;
    }

    .cuting {
        display: flex;

        p {
            width: 80px;
            background: #00479d;
            margin: 2px 3px;
            color: #fff;
            cursor: pointer;
            border: 1px solid #fff;
            border-radius: 3px;
        }
    }

    .wrapTextSelect {
        display: flex;
        justify-content: space-between;
        align-items: center;

    }

    .information {
        width: 80%;
        background-color: #0dade8;
        height: 30px;
        margin: 15px 0;
        font-size: 20px;
        text-align: center;
        color: #fff;
        border-radius: 10px;
        line-height: 30px;
    }

    .warehouse {
        ::v-deep .el-descriptions-item__cell {
            padding-bottom: 27px;

            .el-descriptions-item__label,
            .el-descriptions-item__content {
                color: #28b5ff;
                font-size: 20px;
            }
        }

        .btn {
            width: 100%;
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
        }

    }


    .flow_main {
        background-color: #f1f3ff;
        padding-top: 50px;
        border-radius: 5px;
        border-color: rgba(0, 0, 0, 0.09);
        box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
        transition: 0.2s;
    }

    .flow_main:hover {
        border-color: rgba(0, 0, 0, 0.09);
        box-shadow: 0 2px 8px rgb(191 200 220);
    }

    .flow_title {
        font-weight: bold;
        color: #ffffff;
        margin-top: -35px;
        //   position: fixed;
        cursor: pointer;
        background-image: url('~@/assets/images/label_bg.png');
        background-position: 0;
        background-repeat: no-repeat;
        height: 35px;
        line-height: 35px;
        padding-left: 28px;
        font-size: 13px;
        margin-left: -8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .flow_info {
        line-height: 25px;
        color: #333;
        font-size: 12px;
        padding: 0 10px;
    }

    .overflowText {
        // /* 1.溢出隐藏 */
        // overflow: hidden;
        // /* 2.用省略号来代替超出文本 */
        // text-overflow: ellipsis;
        // /* 3.设置盒子属性为-webkit-box  必须的 */
        // display: -webkit-box;
        // /* 4.-webkit-line-clamp 设置为2，表示超出2行的部分显示省略号，如果设置为3，那么就是超出3行部分显示省略号 */
        // -webkit-line-clamp: 2;
        // /* 5.字面意思：单词破坏：破坏英文单词的整体性，在英文单词还没有在一行完全展示时就换行  即一个单词可能会被分成两行展示 */
        // word-break: break-all;
        // /* 6.盒子实现多行显示的必要条件，文字是垂直展示，即文字是多行展示的情况下使用 */
        // -webkit-box-orient: vertical;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        width: 320px;
    }

    .flow_button {
        padding: 0;
        font-weight: 600;
        color: #00479d;
    }
    .flow_menu {
        float: right;
        margin-top: -55px;
        margin-right: 10px;
    }
    .flow_menu .text {
        color: #838585;
        font-weight: 600;
        font-size: 20px;
        height: 20px;
        cursor: pointer;
    }
    .hover-row{
        td{
            .el-dropdown{
                .el-button--text{
                    color:#fff;
                }
            }
            .inbOutNum{
                color:#fff;
            }
        }
    }
    .current-row{
        td{
            .el-dropdown{
                .el-button--text{
                    color:#fff;
                }
            }
            .inbOutNum{
                color:#fff;
            }
        }
    }
    .wrapRowss {
        background: #ffffff;
        border-radius: 4px;
        .wrapRowItem {
            .nomargin {
                margin-bottom: 0;
                padding: 6px;
                white-space:nowrap;
                .flow_main{
                    display:inline-block;
                    width:330px;
                    margin-left:10px;
                    overflow:hidden;
                }
            }
        }
        .wrapRowItem::-webkit-scrollbar {
            width: 10px;
            height: 10px;
            background-color: #ebeef5;
            cursor: pointer !important;
        }
        .wrapRowItem::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(255,255,255,.3);
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
            background-color: #f6f9ff;
            cursor: pointer !important;
        }
        .wrapRowItem::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,.2);
            box-shadow: inset 0 0 5px rgba(0,0,0,.2);
            border-radius: 3px;
            background: #fff;
            cursor: pointer !important;
        }
        .wrapRowItem:first-child {
            margin-top: 0;
        }

        .wrapRowItem:first-child {
            margin-top: 0;
        }
    }
}
</style>