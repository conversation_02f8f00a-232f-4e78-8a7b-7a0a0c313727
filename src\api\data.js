// qs 是一个增加了一些安全性的查询字符串解析和序列化字符串的库

import request from '@/utils/request'
import qs from 'qs'

export function initData(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}

export function getServer(url) {
  return request({
    // url: 'aisEsbWeb/monitor/getDetailedIndicators',
    url: 'http://'+ url + '/aisEsbWeb/monitor/getDetailedIndicators',
    method: 'get',
  })
}
export function getControlRelease(ip,url) {
  return request({
    // url:`aisEsbWeb${url}`,
    url:`http://${ip}/aisEsbWeb${url}`,
    method: 'get',
  })
}

export function download(url, params) {
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}
