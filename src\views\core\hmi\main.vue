<template>
  <el-container style="height:100%">
    <el-header>
      <div class="headerFirst">
        <div class="headerLogo">
          <div><img :src="logo" class="hmi-logo"></div>
          <div class="headerStaion">
            <span class="spanFisrt" @click="openStationChoose"><svg-icon icon-class="tree" />{{ $t('lang_pack.andonevent.prodLineCode') +'：' }}{{ currentStation.prod_line_code + ' ' + currentStation.prod_line_des }}<i class="el-icon-more arrowStyle" /></span>
            <span @click="openStationChoose"><svg-icon icon-class="monitor" />{{ $t('lang_pack.andonevent.stationCode') +'：' }}{{ currentStation.station_code + ' ' + currentStation.station_des }}<i class="el-icon-more arrowStyle" /></span>
          </div>
        </div>

        <div class="headerSecond">
          <div class="headerTwo">
            <span><svg-icon icon-class="people" />{{ $t('lang_pack.header.user') }}{{ user.nickName }}</span>
            <span><i class="el-icon-time" />{{ $t('lang_pack.header.onlineDuration') }}{{ onlineTime }}{{ $t('lang_pack.header.min') }}</span>
          </div>
          <div>
            <el-tooltip :content="$t('lang_pack.header.signOut')" effect="dark" placement="bottom">
              <span class="loginout" @click="open"><i class="el-icon-switch-button" /></span>
            </el-tooltip>
            <el-tooltip :content="$t('lang_pack.header.lockScreen')" effect="dark" placement="bottom">
              <div class="right-menu-item" style="cursor: pointer" @click="lockScreen">
                <i class="el-icon-lock" style="font-weight:700;" />
              </div>
            </el-tooltip>
            <el-tooltip :content="$t('lang_pack.header.screen')" effect="dark" placement="bottom">
              <screenfull id="screenfull" class="right-menu-item hover-effect" />
            </el-tooltip>
          </div>
        </div>
      </div>
      <el-dialog :modal="false" :title="$t('lang_pack.header.stationSelect')" :visible.sync="dialogVisible" width="80%" top="65px">
        <div style="width:100%">
          <div v-for="(item, index) in stationData" :key="index" style="margin-bottom:20px;">
            <el-tag :key="index" type="info" :disable-transitions="false" style="line-height:40px;height:40px;">
              {{ item.prod_line_code + ' ' + item.prod_line_des }}
            </el-tag>
            <div v-if="item.station_list.length > 0" style="margin-top:10px;">
              <el-tag v-for="(item1, index1) in item.station_list" :key="index1" :disable-transitions="false" style="margin:10px 10px 10px 0;line-height:50px;height:50px;cursor: pointer;" @click="handleStationChoose(item, item1)">
                {{ item1.station_code + ' ' + item1.station_des }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-dialog>
    </el-header>
    <el-main style="background-color: #f1f1f1">
      <div v-if="greenbgShow" class="greenStyle" :style="'background-image:url(' + greenbg + ');'">
        <div class="drop drop1">
          <h1 style="text-shadow: 0 5px rgb(0 0 0 / 30%);">{{ $t('lang_pack.header.greenNewEnergy') }}</h1>
          <div class="drop drop2" />
          <div class="drop drop3" />
          <div class="drop drop4" />
        </div>
      </div>
      <el-tabs id="hmiTabs" v-model="editableTabsValue1" :closable="true" @tab-remove="handleRemove">
        <el-tab-pane v-for="(item, index) in editableTabs1" :key="index" :label="item.title" :name="item.name" style="padding: 0px; background-color: #e6e8ee">
          <elFrame v-if="elFrameFlag" :name="item.name" :src="item.path + '?prod_line_id=' + currentStation.prod_line_id + '&station_id=' + currentStation.station_id + '&station_code=' + currentStation.station_code+'&cell_id='+currentStation.cell_id+'&projectCode='+projectCode" />
        </el-tab-pane>
      </el-tabs>
    </el-main>
    <el-footer style="height: 65px">
      <table style="width: 100%; border: 0px">
        <tr>
          <td style="width: 20px">
            <el-button
              v-show="hmiMenuData.length !== 0"
              class="filter-item"
              size="medium"
              type="primary"
              icon="el-icon-arrow-left"
              style="
                    height: 40px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
              plain
              @click="marginLeft !== 0 ? (marginLeft += 100) : 0"
            />
          </td>
          <td>
            <el-scrollbar ref="scrollbar" style="width: 100%">
              <div :style="'width: 400%;margin-left:' + marginLeft + 'px;'">
                <template v-for="(item, index) in hmiMenuData">
                  &nbsp;
                  <el-button
                    :key="item.id"
                    :class="{'footerButton': activeIndex === index}"
                    class="filter-item itemBtns"
                    size="medium"
                    type="primary"
                    :icon="item.menu_icon"
                    style="
                            height: 40px;
                            margin-top: 5px;
                            font-weight: bold;
                            margin-left: 0px;
                          "
                    plain
                    @click="handlesSelect(item, index, item)"
                  >
                    {{ item.current_menu_des === '' ? item.menu_des : item.current_menu_des }}
                  </el-button>
                </template>
              </div>
            </el-scrollbar>
          </td>
          <td style="width: 20px">
            <el-button
              v-show="hmiMenuData.length !== 0"
              class="filter-item"
              size="medium"
              type="primary"
              icon="el-icon-arrow-right"
              style="
                    height: 40px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
              plain
              @click="marginLeft -= 100"
            />
          </td>
        </tr>
      </table>
    </el-footer>
    <div v-if="lockShow" class="zhezhao">
      <el-form class="userInfo" @submit.native.prevent>
        <h3 class="title">{{ $t('lang_pack.SystemName') }}</h3>
        <el-form-item>
          <el-input
            v-model="userForm.newPw"
            :placeholder="$t('lang_pack.header.loginPassword')"
            type="password"
            auto-complete="off"
            show-password
            @keyup.enter.native="unLock()"
          >
            <div slot="prefix" style="margin-left: 3px">
              <i class="el-icon-lock" /></div></el-input>
          <span class="jianpan" @click="showKeyboard">
            <img :src="keyboard">
          </span>
        </el-form-item>
        <el-form-item class="wrapbtn">
          <!-- <el-button
          size="medium"
          type="warning"
          @click="logout"
        ><i class="el-icon-unlock" />退屏重新登录</el-button> -->
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width: 100%"
            @click="unLock"
          ><i class="el-icon-unlock" />{{ $t('lang_pack.header.unlock') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </el-container>
</template>

<script>
import md5 from 'js-md5'
import { sel } from '@/api/core/system/sysLogo'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import elFrame from '@/views/core/hmi/iframe'
import { sel as selHmiInfo } from '@/api/hmi/main'
import Cookies from 'js-cookie'
import { mapGetters } from 'vuex'
import keyboard from '@/assets/images/keyboard.png'
import greenbg from '@/assets/images/greenbg.jpg'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
import screenfull from '@/components/Screenfull/index.vue'
export default {
  name: 'layout',
  components: {
    elFrame,
    SimpleKeyboard,
    screenfull
  },
  data() {
    return {
      input: '',
      isShow: false,
      lockShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      greenbg: greenbg,
      greenbgShow: true,
      userForm: {
        newPw: '',
        user: '',
        isCover: true
        // isLock:this.$store.state.user.isLock,
      },
      loading: false,
      logo: '',
      dialogVisible: false,
      height: document.documentElement.clientHeight,
      hmiMenuData: [],
      editableTabsValue1: '0',
      editableTabs1: [],
      marginLeft: 0,
      marginLeft1: 0,
      stationData: [],
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      onlineTime: 0,
      timer: '',
      elFrameFlag: true,
      activeIndex: 0,
      projectCode: ''
    }
  },
  computed: {
    ...mapGetters(['user'])
  },
  watch: {},
  mounted: function() {
    this.timer = setInterval(this.getOnlineTime, 1000)
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight
    }
    window.addEventListener('message', this.handleMessage)
    // 获取系统参数信息
    var queryParameter = {
      userName: Cookies.get('userName'),
      parameter_code: 'ProjectCode',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0 && defaultQuery.data !== '') {
          this.projectCode = defaultQuery.data[0].parameter_val
        }
      }).catch(() => {})
    setTimeout(() => {
      // console.log(document.querySelectorAll('.itemBtns'))
      document.querySelectorAll('.itemBtns') && document.querySelectorAll('.itemBtns')[0].click()
    }, 1000)
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  created: function() {
    this.getLogo()
    this.handleRefresh('127.0.0.1', '8090')
    this.getOnlineTime()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    handleMessage(event) {
      if (event.data === 'whiteLogin') {
        this.$router.push('/whiteLogin')
      }
    },
    getOnlineTime() {
      if (Cookies.get('OnlineTime') !== undefined) {
        var dateBegin = new Date(Cookies.get('OnlineTime'))
        var dateEnd = new Date()
        var dateDiff = dateEnd.getTime() - dateBegin.getTime() // 时间差的毫秒数
        this.onlineTime = Math.floor(dateDiff / (60 * 1000)) // 计算相差分钟数
      }
    },
    getLogo() {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName')
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.logo = 'data:image/png;base64,' + defaultQuery.data[0].logo
            }
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: this.$t('lang_pack.vie.queryException')
          })
        })
    },
    openStationChoose() {
      this.dialogVisible = true
    },
    handleStationChoose(item, item1) {
      this.editableTabs1 = []
      this.currentStation = {
        prod_line_id: item.prod_line_id,
        prod_line_code: item.prod_line_code,
        prod_line_des: item.prod_line_des,
        station_id: item1.station_id,
        station_code: item1.station_code,
        station_des: item1.station_des,
        cell_id: item1.cell_id
      }
      this.dialogVisible = false
    },
    handleRefresh(line_code, station_code) {
      this.editableTabs1 = []
      this.hmiMenuData = []
      const query = {
        user_name: Cookies.get('userName'),
        userID: Cookies.get('userId')
      }
      selHmiInfo(query)
        .then(res => {
          this.stationData = res.station
          if (res.station.length > 0) {
            this.currentStation = {
              prod_line_id: this.stationData[0].prod_line_id,
              prod_line_code: this.stationData[0].prod_line_code,
              prod_line_des: this.stationData[0].prod_line_des,
              station_id: this.stationData[0].station_list[0].station_id,
              station_code: this.stationData[0].station_list[0].station_code,
              station_des: this.stationData[0].station_list[0].station_des,
              cell_id: this.stationData[0].station_list[0].cell_id
            }
          }
          const menuData = res.menu

          // this.hmiMenuData = defaultQuery.data;
          var dd = []
          for (let i = 0; i < menuData.length; i++) {
            const element = menuData[i]
            const id = element.menu_item_id
            const menu_des = element.menu_item_des
            const path = element.function_path
            const hmi_menu_ico = element.hmi_menu_ico

            var aa = {}
            aa.id = '1-' + id
            aa.menu_des = menu_des
            aa.button_type = 'primary'
            aa.current_menu_des = ''
            aa.menu_icon = hmi_menu_ico === '' ? 'el-icon-s-platform' : hmi_menu_ico
            aa.path = path // 'http://' + server_host + ':' + cell_port + path // http://localhost:8013/hmi
            dd.push(aa)
          }
          this.hmiMenuData = dd
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: this.$t('lang_pack.vie.queryException')
          })
        })
    },
    handlesSelect(item, index, parent) {
      this.activeIndex = index
      let obj = {}
      const TabName = 'Tab_' + item.id
      obj = this.editableTabs1.find(item => {
        return item.name === TabName
      })
      const currentMenu1 = this.editableTabs1.filter(item => item.button_type === 'warning')
      if (currentMenu1.length > 0) {
        currentMenu1[0].button_type = 'primary'
      }
      // 越南包装机 要在每次点击的时候都要重新刷新页面  固定的几个页面  其他也没不影响
      // const Arr = ['/packTaskManager', 'packStationSetReport', 'packStationPileReport']
      // if (Arr.includes(item.path)) {
      //   this.elFrameFlag = false
      //   this.$nextTick(() => {
      //     this.elFrameFlag = true
      //   })
      // }

      if (typeof obj !== 'undefined') {
        obj.button_type = 'warning'
        this.editableTabsValue1 = TabName
      } else {
        this.editableTabs1.push({
          title: item.menu_des,
          name: TabName,
          path: item.path,
          icon: item.menu_icon,
          button_type: 'warning'
        })
        this.editableTabsValue1 = TabName
      }
      const currentMenu = this.hmiMenuData.filter(item => item.button_type === 'warning')
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
        currentMenu[0].current_menu_des = ''
      }
      parent.current_menu_des = item.menu_des
      parent.button_type = 'warning'
      this.$emit('showCellNavbar', item.menu_des)
      this.greenbgShow = false
    },
    handleRemove(tabName) {
      for (var i = 0; i < this.editableTabs1.length; i++) {
        if (this.editableTabs1[i].name === tabName) {
          this.editableTabs1.splice(i, 1)
        }
      }
    },
    handlesOpenedMenu(item, index) {
      const currentMenu = this.editableTabs1.filter(item => item.button_type === 'warning')
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
      }
      item.button_type = 'warning'
      this.editableTabsValue1 = item.name
      this.$emit('showCellNavbar', item.title)
    },
    open() {
      this.$confirm(this.$t('lang_pack.header.exitSystem'), this.$t('lang_pack.Prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
        type: 'warning'
      }).then(() => {
        this.$router.push('/whiteLogin')
      })
    },
    // 锁屏：
    lockScreen() {
      this.lockShow = true
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
      // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
      // console.log(1)
      // 删除键的相应操作
      }
      if (button === '{bksp}') {
      // console.log(1)
      // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
        // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
          // console.log(inputDom.type, i);
          }
        }
      }
    },
    unLock() {
      const oldAuct = sessionStorage.getItem('lockPassword')
      sessionStorage.setItem('newlockPassword', md5(this.userForm.newPw))
      console.log(oldAuct, sessionStorage.getItem('newlockPassword'), '999990')
      if (this.userForm.newPw === '' || this.userForm.newPw === undefined) {
        this.$notify.error({
          title: this.$t('lang_pack.header.error'),
          message: this.$t('lang_pack.header.passwordUnlock'),
          duration: 1500
        })
        return
      } else if (md5(this.userForm.newPw) !== oldAuct) {
        this.userForm.newPw = ''
        this.$notify.error({
          title: this.$t('lang_pack.header.error'),
          message: this.$t('lang_pack.header.passwordError'),
          duration: 1500
        })
        return
      } else {
        setTimeout(() => {
          this.$notify.success({
            title: this.$t('lang_pack.header.unlockingSuccessful'),
            duration: 1500
          })
          this.lockShow = false
          this.isShow = false
          this.userForm.newPw = ''
        }, 500)
      }
    }
    // logout() {
    //   this.$store.dispatch('LogOut').then(() => {
    //     this.$router.push('/whitelLogin')
    //     location.reload()
    //   })
    // }
  }
}
</script>
<style lang="less" scoped>
.el-header {
  background-color: #79a0f1;
  z-index: 2;
  height: 50px !important;
}
.el-footer {
  background-color: #79a0f1;
  color: #333;
  line-height: 60px;
  padding: 0px;
  overflow: hidden;
  z-index: 2;
}
.el-main {
  background-color: #ffffff;
  color: #333;
  padding: 0px;
}
// 细化滚动条
.el-main::-webkit-scrollbar {
  width: 0px;
  height: 4px;
  background-color: #ebeef5;
  cursor: pointer !important;
}
.el-main::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f6f9ff;
}
.el-main::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}
body > .el-container {
  margin-bottom: 40px;
}
.hmi-logo {
  width: auto;
  height: 40px;
}
.right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #ffffff;
      vertical-align: text-bottom;
      margin-left: 10px;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }
    .headerFirst{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      .headerLogo{
        display: flex;
        align-items: center;
        .headerStaion{
          span{
          color: #ffffff;
          font-size: 12px;
          padding: 8px 10px;
          border-radius: 25px;
          background: rgba(0,0,0,.1);
          font-weight: normal;
          white-space: nowrap;
          cursor: pointer;
            .svg-icon{
              margin-right: 5px;
            }
          }
          .spanFisrt{
            margin-right: 6px;
            margin-left: 15px;
          }
        }
      }
    }
    .headerSecond{
      color: #ffffff;
      display: flex;
    align-items: center;
      span{
        margin-right: 10px;
        font-size: 12px;
        font-weight: normal;
        white-space: nowrap;
        .svg-icon{
          margin-right: 5px;
        }
      }
      .loginout{
        cursor: pointer;
        margin-right: 0 !important;
        font-size: 14px;
        i{
          font-weight: 700;
        }
      }
    }
    .headerTwo{
      display: flex;
      margin-top: 6px;
    }
    .el-icon-lock{
      font-size: 14px;
    }
    .el-icon-time{
      margin-right: 5px;
    }
    ::v-deep .right-menu-item.hover-effect{
      font-size: 14px;
    margin-left: 0;
    padding-right: 0;
    }
    .zhezhao{
      position: fixed;
      width: 100%;
      height: 100%;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 9;

    }
    .userInfo{
        position: fixed;
    bottom: 15%;
    right: 10%;
    border-radius: 10px;
    background: #ffffff;
    padding: 30px 25px;
    width: 380px;
    z-index: 13;
    .title {
  margin: 0 auto 40px auto;
  text-align: center;
  color: #18245e;
  font-size: 24px;
}
  }
  .wrapbtn{
    margin-top: 20px;
  }
  .keyboard-mask{
    position: fixed;
    bottom: 15%;
    left: 5%;
    z-index: 999;
}
::v-deep .hg-theme-default .hg-button.hg-standardBtn {
    width: 24px !important;
    // height: 51px;
}
.jianpan img{
      width: 35px;
    margin-top: 10px;
    margin-left: 10px;
    cursor: pointer;
}
.greenStyle{
  background-repeat: no-repeat;
  background-size: cover;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.drop {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    border: 1px solid rgba(255,255,255,0.2);
    position: absolute;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: block;
    margin: 0 auto;
    h1 {
      text-align: center;
    font-family: Arial;
    color: #FFF;
    font-size: 90px;
    padding: 20px 30px;
    text-transform: uppercase;
}
.drop1 {
    width: 47%;
    height: 150px;
    top: 56px;
    left: 0;
    right: 0;
    z-index: 2;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}
.drop2 {
    width: 40px;
    height: 40px;
    top: -60px;
    left: -80%;
    right: 0;
    z-index: 4;
}
.drop3 {
    width: 60px;
    height: 60px;
    bottom: -80px;
    right: 30px;
    z-index: 3;
}
.drop4 {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    top: -55px;
    right: 20px;
}
}
.arrowStyle{
  color: #ffffff;
  transform: rotate(90deg);
}
.footerButton{
  background-color: #1890ff !important;
  color: #fff !important;
  border: none;
}
</style>
<style lang="less">
#hmiTabs .el-tabs__header {
  margin: 0 0 0px;
  display: none;
}
</style>
