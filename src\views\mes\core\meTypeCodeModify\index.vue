<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard" :style="{height:height + 'px'}">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="类型：">
                <el-select
                  v-model="query.typeCode"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in typeCodeData"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="源码：">
                <el-input ref="source_code" v-model="query.source_code" clearable />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-8 col-12">
              <el-form-item label="新码：">
                <el-input v-model="query.code" clearable />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="wrapElFormSecond formChild col-md-2 col-12">
              <el-form-item>
                <!-- <rrOperation /> -->
                <span class="wrapRRItem">
                  <el-button
                    class="filter-item"
                    size="small"
                    type="primary"
                    @click="materialBind"
                  >确认修改</el-button>
                </span>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import { MesMeTypeCodeModify } from '@/api/mes/core/meTypeCodeModify'
export default {
  name: 'MES_TYPE_CODE_MODIFY',
  data() {
    return {
      height: document.documentElement.clientHeight - 70,
      query: {
        typeCode: '',
        source_code: '',
        code: ''
      },
      typeCodeData: [
        { label: '车辆码', value: 'car_code', id: 1 },
        { label: 'PACK码', value: 'pack_barcode', id: 2 },
        { label: '模组码', value: 'mz_barcode', id: 3 }
      ]
    }
  },
  created() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.source_code.focus()
    })
  },
  methods: {
    materialBind() {
      for (const key in this.query) {
        if (!this.query[key]) {
          this.$message({ type: 'warning', message: '类型、源码、新码，不能为空' })
          return
        }
      }
      MesMeTypeCodeModify(this.query).then(res => {
        if (res.code === 0) {
          this.query = {
            typeCode: '',
            source_code: '',
            code: ''
          }
          this.$message({ type: 'success', message: '修改成功' })
          return
        }
        this.$message({ type: 'error', message: '修改失败：' + res.msg })
      }).catch(err => {
        this.$message({ type: 'error', message: '修改失败：：' + err.msg })
      })
    }
  }
}
</script>
