import request from '@/utils/request'

const conn = 'a_pack_aps_tray_recipe'
const context = 'aisEsbWeb/core/mongo-data/' + conn

export function sel(params) {
  return request({
    url: context,
    method: 'get',
    params
  })
}

export function edit(data, params) {
  return request({
    url: context + '/' + data.id,
    method: 'patch',
    data,
    params
  })
}

export function add(data, params) {
  return request({
    url: context,
    method: 'post',
    data,
    params
  })
}

export function del(data, params) {
  return request({
    url: context + '/' + (data.id || data.ids),
    method: 'delete',
    data,
    params
  })
}

export default { add, del, edit, sel }
