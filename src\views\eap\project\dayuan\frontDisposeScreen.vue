<template>
  <div class="screen_container">
    <div class="screen_head">
      <h2>普诺威生产车间设备预警看板-{{ prodLineName }}</h2>
      <div class="imgtime">
        <img src="@/assets/images/time.png">
        <span class="wrapTime"><span class="showTime">{{ gettime }}</span><span class="showTime timeMargin">{{ week }}</span></span>
      </div>
    </div>
    <div class="mainbox">
      <div class="column">
        <div class="columnFirst">
          <div class="panelOne">
            <div class="title"><span class="dot" /><h2>输送/温度/电导率</h2></div>
            <div class="panelOneTable">
              <el-table
                :data="tableDataone"
                style="width: 100%"
                height="198"
              >
                <el-table-column 
                  prop="itemName"
                  label="设备输送统计"
                />
                <el-table-column 
                  prop="setValue"
                  label="设定值"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.setValue">{{ scope.row.setValue + scope.row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="scopeValue"
                  label="范围值"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.upperLimit && scope.row.downLimit">{{ scope.row.downLimit + "-" + scope.row.upperLimit + scope.row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="actualValue"
                  label="实际值"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.actualValue" :style="{'color':(scope.row.isOutRange ? 'red' : '#828fa9')}">{{ scope.row.actualValue + scope.row.unit }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="panelImg">
            <img :src="devicePicUrl" alt="">
          </div>
          <div class="panelOne">
            <div class="title"><span class="dot" /><h2>报警消息</h2></div>
            <div class="panelSelect">
              <div class="selects">
                <label>料号：</label>
                <el-input v-model="valueOne" clearable size="small" />
              </div>
              <div class="selects">
                <label>批次号：</label>
                <el-input v-model="valueTwo" clearable size="small" />
              </div>
              <el-button class="sureBtn" type="primary" @click="addDyWorkOrder">确认</el-button>
            </div>
            <div class="panelOneTable">
              <el-table
                :data="tableDatatwo"
                style="width: 100%"
                height="202"
              >
                <el-table-column 
                  prop="item_date"
                  width="150"
                  label="报警时间"
                  show-overflow-tooltip
                />
                <el-table-column 
                  prop="alarm_code"
                  width="100"
                  label="报警代码"
                />
                <el-table-column 
                  prop="alarm_des"
                  label="报警信息"
                />
              </el-table>
            </div>
          </div>
        </div>
        <div class="columnSecond">
          <div class="panelOne panelOneSecondBg">
            <div class="title"><span class="dot" /><h2>压力</h2></div>
            <div class="panelOneTable">
              <el-table
                :data="tableDatathree"
                style="width: 100%"
                height="679"
              >
                <el-table-column 
                  prop="itemName"
                  label="设备压力统计"
                />
                <el-table-column 
                  prop="setValue"
                  label="设定值"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.setValue">{{ scope.row.setValue + scope.row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="scopeValue"
                  label="范围值"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.upperLimit && scope.row.downLimit">{{ scope.row.downLimit + "-" + scope.row.upperLimit + scope.row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="actualValue"
                  label="实际值"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.actualValue" :style="{'color':scope.row.isOutRange ? 'red' : '#828fa9'}">{{ scope.row.actualValue + scope.row.unit }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { selTree as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import { selStationConfig } from '@/api/eap/core/eapScreenConfig'
import { add as addDyWorkOrder } from '@/api/eap/project/dayuan/eapDyWorkOrder'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'frontDisposeScreen',
  data() {
    return {
      timer1: '',
      timer2: '',
      timer3:'',
      gettime: '',
      week: '',
      tableDataone: [],
      tableDatatwo: [],
      tableDatathree: [],
      valueOne: '',
      valueTwo: '',
      timer: '',
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // G区域
      gArea: { },
      // H区域
      hArea: { },
      // I区域
      iArea: { },
      // j区域
      jArea: { },
      // k区域
      kArea: { },
      // L区域
      lArea: { },
      // M区域
      mArea: { },
      // N区域
      nArea: { },
      // 图片显示
      devicePicUrl: '',
      repiceDialogVisible: false,
      repiceData: [],
      repiceDetailData: [],
      alarmData: [],
      panel: '',
      jdeCode: '',
      scanError: '',
      prodLineName: '去膜线'
    }
  },
  beforeCreate() {
    document.querySelector('body').setAttribute('style', 'background-color:#01093a')
  },
  created: function() {
    this.getTime()
    this.timer1 = setInterval(() => {
      this.getTime()
    }, 1000)
    this.getStationData()
    this.getProdLineName()
  },
  mounted() {
    this.timer = setInterval(this.getAlarmData, 3000)
    this.timer2 = setInterval(this.assemblyTableData, 3000)
    this.timer3 = setInterval(this.getStationConfig, 3000)
  },
  // 离开此页面时销毁定时器
  beforeDestroy() {
    document.querySelector('body').removeAttribute('style')
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer)
    clearInterval(this.timer3)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    },
    tableRowClassName({ row, rowIndex }) {
      return { background: '#f00', color: '#000', fontWeight: 700 }
    },
    getStationData() {
      const query = {
        user_name: Cookies.get('userName')
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const stationData = JSON.parse(defaultQuery.result)
            if (stationData.length > 0) {
              this.currentStation = {
                prod_line_id: stationData[0].prod_line_id,
                prod_line_code: stationData[0].prod_line_code,
                prod_line_des: stationData[0].prod_line_des,
                station_id: stationData[0].station_list[0].station_id,
                station_code: stationData[0].station_list[0].station_code,
                station_des: stationData[0].station_list[0].station_des,
                cell_id: stationData[0].station_list[0].cell_id
              }
              this.getStationConfig()
              this.getCellIp()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getStationConfig() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      this.gArea = {},this.hArea = {},this.iArea = {},this.jArea = {},this.kArea = {},this.lArea = {},this.mArea = {},this.nArea = {}
      selStationConfig(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const StationConfig = JSON.parse(defaultQuery.result)
            console.log(StationConfig)
            if (StationConfig.length > 0) {
              this.gArea = StationConfig.filter(item => item.region_code === 'G')
              if (this.gArea.length > 0) {
                this.gArea = this.gArea[0]
              }
              this.hArea = StationConfig.filter(item => item.region_code === 'H')
              if (this.hArea.length > 0) {
                this.hArea = this.hArea[0]
              }
              this.iArea = StationConfig.filter(item => item.region_code === 'I')
              if (this.iArea.length > 0) {
                this.iArea = this.iArea[0]
              }
              this.jArea = StationConfig.filter(item => item.region_code === 'J')
              if (this.jArea.length > 0) {
                this.jArea = this.jArea[0]
              }
              this.kArea = StationConfig.filter(item => item.region_code === 'K')
              if (this.kArea.length > 0) {
                this.kArea = this.kArea[0]
              }
              this.lArea = StationConfig.filter(item => item.region_code === 'L')
              if (this.lArea.length > 0) {
                this.lArea = this.lArea[0]
              }
              this.mArea = StationConfig.filter(item => item.region_code === 'M')
              if (this.mArea.length > 0) {
                this.mArea = this.mArea[0]
              }
              this.nArea = StationConfig.filter(item => item.region_code === 'N')
              if (this.nArea.length > 0) {
                this.nArea = this.nArea[0]
              }
              const devicePic = StationConfig.filter(item => item.region_code === 'DevicePic')
              if (devicePic.length > 0) {
                if (devicePic[0].item_list.length > 0) {
                  this.devicePicUrl = (process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : '/') + 'aisEsbWeb/eap/core/EapScreenConfigImage/' + devicePic[0].item_list[0].screen_config_d_id + '?id=' + Math.random().toString(16).substr(2, 8)
                }
              }
              this.getTagValue()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getAlarmData()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getAlarmData() {
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      var queryData = {
        reset_flag: 'N'
      }
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
              this.tableDatatwo = defaultQuery.data ? defaultQuery.data : []
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      if (this.gArea.item_list !== undefined) {
        this.gArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.hArea.item_list !== undefined) {
        this.hArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.iArea.item_list !== undefined) {
        this.iArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.jArea.item_list !== undefined) {
        this.jArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.kArea.item_list !== undefined) {
        this.kArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.lArea.item_list !== undefined) {
        this.lArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.mArea.item_list !== undefined) {
        this.mArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      if (this.nArea.item_list !== undefined) {
        this.nArea.item_list.forEach(item => {
          if (item.tag_key !== '') {
            var readTag = {}
            readTag.tag_key = item.tag_key
            readTagArray.push(readTag)
          }
        })
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://localhost:8090' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              result.forEach(item => {
                const tag_value = item.tag_value === undefined ? '' : item.tag_value
                if (this.gArea.item_list !== undefined) {
                  const gAreaItem = this.gArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (gAreaItem.length > 0) {
                    gAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // gAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.hArea.item_list !== undefined) {
                  const hAreaItem = this.hArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (hAreaItem.length > 0) {
                    hAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // hAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.iArea.item_list !== undefined) {
                  const iAreaItem = this.iArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (iAreaItem.length > 0) {
                    iAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // iAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.jArea.item_list !== undefined) {
                  const jAreaItem = this.jArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (jAreaItem.length > 0) {
                    jAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // jAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.kArea.item_list !== undefined) {
                  const kAreaItem = this.kArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (kAreaItem.length > 0) {
                    kAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // kAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.lArea.item_list !== undefined) {
                  const lAreaItem = this.lArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (lAreaItem.length > 0) {
                    lAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // lAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.mArea.item_list !== undefined) {
                  const mAreaItem = this.mArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (mAreaItem.length > 0) {
                    mAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // mAreaItem[0].tag_value = tag_value
                  }
                }
                if (this.nArea.item_list !== undefined) {
                  const nAreaItem = this.nArea.item_list.filter(a => a.tag_key === item.tag_key)
                  if (nAreaItem.length > 0) {
                    nAreaItem.forEach(item => {
                      item.tag_value = tag_value
                    })
                    // nAreaItem[0].tag_value = tag_value
                  }
                }
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }

      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://broker.emqx.io:8083/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        if (this.gArea.item_list !== undefined) {
          this.gArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.hArea.item_list !== undefined) {
          this.hArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.iArea.item_list !== undefined) {
          this.iArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.jArea.item_list !== undefined) {
          this.jArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.kArea.item_list !== undefined) {
          this.kArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.lArea.item_list !== undefined) {
          this.lArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.mArea.item_list !== undefined) {
          this.mArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }
        if (this.nArea.item_list !== undefined) {
          this.nArea.item_list.forEach(item => {
            if (item.tag_key !== '') {
              this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
            }
          })
        }

        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        // 解析传过来的数据
        var jsonData = JSON.parse(message)
        if (jsonData == null) return

        if (topic.indexOf('SCADA_CHANGE/') >= 0) {
          if (this.gArea.item_list !== undefined) {
            const gAreaItem = this.gArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (gAreaItem.length > 0) {
              gAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // gAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.hArea.item_list !== undefined) {
            const hAreaItem = this.hArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (hAreaItem.length > 0) {
              hAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // hAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.iArea.item_list !== undefined) {
            const iAreaItem = this.iArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (iAreaItem.length > 0) {
              iAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // iAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.jArea.item_list !== undefined) {
            const jAreaItem = this.jArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (jAreaItem.length > 0) {
              jAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // jAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.kArea.item_list !== undefined) {
            const kAreaItem = this.kArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (kAreaItem.length > 0) {
              kAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // kAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.lArea.item_list !== undefined) {
            const lAreaItem = this.lArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (lAreaItem.length > 0) {
              lAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // lAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.mArea.item_list !== undefined) {
            const mAreaItem = this.mArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (mAreaItem.length > 0) {
              mAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // mAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
          if (this.nArea.item_list !== undefined) {
            const nAreaItem = this.nArea.item_list.filter(a => a.tag_key === jsonData.TagKey)
            if (nAreaItem.length > 0) {
              nAreaItem.forEach(item => {
                item.tag_value = jsonData.TagNewValue
              })
              // nAreaItem[0].tag_value = jsonData.TagNewValue
            }
          }
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          // console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          this.$message({ message: '操作成功！', type: 'success' })
        } else {
          this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    },
    // 组装表格数据
    assemblyTableData() {
      this.tableDataone = []
      this.tableDatathree = []
      this.gArea.item_list.forEach(item => {
        const d = {}
        d.itemName = item.item_des
        d.unit = item.unit
        d.actualValue = item.tag_value
        d.isOutRange = false
        const hAreaItem = this.hArea.item_list.find(a => a.item_code === item.item_code)
        if (hAreaItem) {
          d.setValue = hAreaItem.tag_value
        }
        const iAreaItem = this.iArea.item_list.find(a => a.item_code === item.item_code)
        if (iAreaItem) {
          d.upperLimit = iAreaItem.tag_value
        }
        const jAreaItem = this.jArea.item_list.find(a => a.item_code === item.item_code)
        if (jAreaItem) {
          d.downLimit = jAreaItem.tag_value
        }
        if ((d.actualValue && d.upperLimit && d.downLimit) && !(parseFloat(d.actualValue) <= parseFloat(d.upperLimit) && parseFloat(d.actualValue) >= parseFloat(d.downLimit))) {
          d.isOutRange = true
        }
        console.info(d.isOutRange)
        this.tableDataone.push(d)
      })

      this.kArea.item_list.forEach(item => {
        const d = {}
        d.itemName = item.item_des
        d.unit = item.unit
        d.actualValue = item.tag_value
        d.isOutRange = false
        const lAreaItem = this.lArea.item_list.find(a => a.item_code === item.item_code)
        if (lAreaItem) {
          d.setValue = lAreaItem.tag_value
        }
        const mAreaItem = this.mArea.item_list.find(a => a.item_code === item.item_code)
        if (mAreaItem) {
          d.upperLimit = mAreaItem.tag_value
        }
        const nAreaItem = this.nArea.item_list.find(a => a.item_code === item.item_code)
        if (nAreaItem) {
          d.downLimit = nAreaItem.tag_value
        }
        if ((d.actualValue && d.upperLimit && d.downLimit) && !(parseFloat(d.actualValue) <= parseFloat(d.upperLimit) && parseFloat(d.actualValue) >= parseFloat(d.downLimit))) {
          d.isOutRange = true
        }
        this.tableDatathree.push(d)
      })
    },
    // 新增工单信息
    addDyWorkOrder() {
      if (!this.valueOne) {
        this.$message({ message: '请输入料号', type: 'info' })
        return
      }
      if (!this.valueTwo) {
        this.$message({ message: '请输入批次号', type: 'info' })
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '正在新增工单信息，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const query = {
        user_name: Cookies.get('userName'),
        material_no: this.valueOne,
        batch_no: this.valueTwo,
        cell_ip: 'http://' + this.cellIp + ':' + this.webapiPort
      }
      addDyWorkOrder(query)
        .then(res => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '新增成功', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          loading.close()
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getProdLineName() {
      // 获取系统参数信息
      var queryParameter = {
        userName: Cookies.get('userName'),
        parameter_code: 'DY_PROD_LINE_NAME',
        enable_flag: 'Y'
      }
      selSysParameter(queryParameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.prodLineName = defaultQuery.data[0].parameter_val
          }
        }).catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.screen_container{
  padding: 10px;
  background: #01093a;
  height:calc(100vh);
   .screen_head{
      position: relative;
      background: url('~@/assets/images/pnwhbg.png') no-repeat;
      background-size: 100% 100%;
      height: 100px;
      display: flex;
      justify-content: space-between;
      padding: 0 25px;
      h2{
        margin: 0;
        height: 80px;
        line-height: 80px;
        color: #ffffff;
        font-weight: 700;
        font-size: 34px;
        background: linear-gradient(to bottom, hsl(0deg 0% 100%), hsl(204deg 92.4% 43.27%));
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .imgtime{
        line-height: 100px;
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        .wrapTime{
          margin-left: 10px;
          .timeMargin{
            margin-left: 10px;
          }
          .showTime{
              color: #00e2fd;
            }
        }

      }
  }
  .mainbox{
    padding-top: 10px;
    .column{
      display: flex;
      justify-content: space-between;
      width: 100%;
      .columnFirst{
        width: 59.5%;
        .panelOne{
          background: url('~@/assets/images/panelOne.png') no-repeat;
          background-size: 100% 100%;
          .title{
            display: flex;
            align-items: center;
            padding: 10px;
            .dot{
                width: 10px;
                height: 10px;
                background-color: #117ec8;
                border-radius: 50%;
                display: block;
                margin-right: 10px;
              }
              h2{
                color: #ffffff;
                font-size: 17px;
                margin: 0;
              }
          }
          .panelSelect{
            margin: 0 0 10px 10px;
            display: flex;
            .selects{
              display: flex;
              align-items: center;
              margin-right: 10px;
              label{
                color: #ffffff;
                font-size: 12px;
                white-space: nowrap;
              }
              :v-deep(.el-button--primary){
                background: linear-gradient(0deg, #03a9f4, #2196F3);
              }
            }
          }
        }
        .panelImg{
          height: 200px;
          img{
            width: 100%;
            height: -webkit-fill-available;
            object-fit: contain;
          }
        }
      }
      .columnSecond{
        .panelOne{
          background: url('~@/assets/images/panelOne.png') no-repeat;
          background-size: 100% 100%;
          .title{
            display: flex;
            align-items: center;
            padding: 10px;
            .dot{
                width: 10px;
                height: 10px;
                background-color: #117ec8;
                border-radius: 50%;
                display: block;
                margin-right: 10px;
              }
              h2{
                color: #ffffff;
                font-size: 17px;
                margin: 0;
              }
          }
        }
        .panelOneSecondBg{
          background: url('~@/assets/images/panelOneSecondBg.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .columnSecond{
        width: 39.5%;
      }
    }
  }
}
:deep(.el-input__inner){
  background: none !important;
  border: 1px solid #125396 !important;
  color: #ffffff !important;
}
:deep(.el-button--primary) {
    background-color: #0b4175;
    border-color: #0b4175;
}
:deep(.el-select-dropdown__list){
  background: #01093a !important;
}
:deep(.el-select-dropdown__item.hover){
  background-color: #0b4175 !important;
}
:deep(.el-select-dropdown__item){
  color: #b4cdff !important;
}
// 细化滚动条
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  // width: 0 !important;
  // height: 0 !important;
  background-color: #073bb3 !important;
}
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  box-shadow: inset 0 0 6px rgba(66, 10, 10, 0.3) !important;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
  background-color: #117ec8 !important;
  cursor: pointer !important;
}
:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: #59f3f1 !important;
  cursor: pointer !important;
}
:deep(.el-table){
  background: none !important;
  th{
    background: #0b4175 !important;
    color: #fff !important;
    font-size: 14px !important;
    font-weight: 700;
  }
  tr{
    background: #051f52;
    color: #828fa9 !important;
    font-size: 13px !important;
  }
}
:deep(.el-table__body tr.current-row > td){
    background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table__row:hover){
     background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background: none !important;
}
</style>
