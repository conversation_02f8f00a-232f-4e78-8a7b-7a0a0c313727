import request from '@/utils/request'

// 查询配方信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeSel',
    method: 'post',
    data
  })
}
// 新增配方信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeIns',
    method: 'post',
    data
  })
}
// 修改配方信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeUpd',
    method: 'post',
    data
  })
}
// 修改配方信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除配方信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesRecipeDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

