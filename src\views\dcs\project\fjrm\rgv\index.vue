<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="RGV编码:">
                <!-- RGV编码 -->
                <el-input v-model="query.rgv_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.fjrm.rgvCode')" prop="rgv_code">
                <!-- RGV编码 -->
                <el-input v-model="form.rgv_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.rgvDes')" prop="rgv_des">
                <!-- RGV描述 -->
                <el-input v-model="form.rgv_des" clearable size="small" />
              </el-form-item>

              <el-form-item label="RGV就绪点位" prop="ready_tag">
                <!-- 读取点位:RGV就绪:可用 空闲 -->
                <el-input v-model="form.ready_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="RGV到达" prop="reach_tag">
                <!-- 读取点位点位:RGV到达上料位置 -->
                <el-input v-model="form.reach_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="任务类型" prop="task_type_tag">
                <!-- 写入点位:任务类型(1空废料框入库 2空废料框出库 3满框入库 4满框出库) -->
                <el-input v-model="form.task_type_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="任务号" prop="task_num_tag">
                <!-- 写入点位:任务号 -->
                <el-input v-model="form.task_num_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="批次号" prop="lot_num_tag">
                <!-- 写入点位:批次号 -->
                <el-input v-model="form.lot_num_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="物料编码" prop="material_code_tag">
                <!-- 写入点位:物料编码 -->
                <el-input v-model="form.material_code_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="总重量" prop="width_tag">
                <!-- 写入点位:总重量(KG) -->
                <el-input v-model="form.width_tag" clearable size="small" />
              </el-form-item>

              <el-form-item label="废料框编码" prop="waste_box_tag">
                <!-- 写入点位:废料框编码 -->
                <el-input v-model="form.waste_box_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="通知RGV位置" prop="location_tag">
                <!-- 写入点位:通知RGV到取料位置 -->
                <el-input v-model="form.location_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="通知RGV离开" prop="leave_tag">
                <!-- 写入点位:通知RGV离开取料位置 -->
                <el-input v-model="form.leave_tag" clearable size="small" />
              </el-form-item>
              <el-form-item label="任务启动" prop="start_task_tag">
                <!-- 写入点位:任务启动 -->
                <el-input v-model="form.start_task_tag" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.enableFlag')" prop="enable_flag">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" prop="rgv_id" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->

            <!-- RGV编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="rgv_code"
              :label="$t('lang_pack.fjrm.rgvCode')"
              width="140"
              align="center"
            />
            <!-- RGV描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="rgv_des"
              :label="$t('lang_pack.fjrm.rgvDes')"
              width="140"
              align="center"
            />

            <!-- 读取点位:RGV就绪:可用 空闲 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ready_tag"
              label="RGV就绪点位"
              width="140"
              align="center"
            />
            <!-- 读取点位点位:RGV到达上料位置 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="reach_tag"
              label="RGV到达"
              width="140"
              align="center"
            />
            <!-- 写入点位:任务类型(1空废料框入库 2空废料框出库 3满框入库 4满框出库) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type_tag"
              label="任务类型"
              width="140"
              align="center"
            />
            <!-- 写入点位:任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num_tag"
              label="任务号"
              width="140"
              align="center"
            />
            <!-- 写入点位:批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num_tag"
              label="批次号"
              width="140"
              align="center"
            />
            <!-- 写入点位:物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code_tag"
              label="物料编码"
              width="140"
              align="center"
            />
            <!-- 写入点位:总重量(KG) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="width_tag"
              label="总重量"
              width="140"
              align="center"
            />

            <!-- 写入点位:废料框编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_tag"
              label="废料框编码"
              width="140"
              align="center"
            />
            <!-- 写入点位:通知RGV到取料位置 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="location_tag"
              label="通知RGV位置"
              width="140"
              align="center"
            />
            <!-- 写入点位:通知RGV离开取料位置 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="leave_tag"
              label="通知RGV离开"
              width="140"
              align="center"
            />
            <!-- 写入点位:任务启动 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="start_task_tag"
              label="任务启动"
              width="140"
              align="center"
            />
            <el-table-column
              :label="$t('lang_pack.fjrm.enableFlag')"
              align="center"
              width="120"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              prop="button"
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudRgv from '@/api/dcs/project/fjrm/rgv/rgv'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  rgv_id: '',
  rgv_code: '',
  rgv_des: '',
  enable_flag: 'Y',
  ready_tag: '',
  reach_tag: '',
  task_type_tag: '',
  task_num_tag: '',
  lot_num_tag: '',
  material_code_tag: '',
  width_tag: '',
  waste_box_tag: '',
  location_tag: '',
  leave_tag: '',
  start_task_tag: ''
}
export default {
  name: 'RGV',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: 'RGV基础表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'rgv_id',
      // 排序
      sort: ['rgv_id asc'],
      // CRUD Method
      crudMethod: { ...crudRgv },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'dcs_fmod_rgv:add'],
        edit: ['admin', 'dcs_fmod_rgv:edit'],
        del: ['admin', 'dcs_fmod_rgv:del']
      },
      rules: {
        rgv_code: [{ required: true, message: '请选择RGV编码', trigger: 'blur' }],
        rgv_des: [{ required: true, message: '请选择RGV描述', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudRgv
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              rgv_id: data.rgv_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
