import request from '@/utils/request'

// 查询质量数据信息
export function sel(data) {
    return request({
        url: 'aisEsbWeb/mes/core/SHqueryStationQualityList',
        method: 'post',
        data
    })
}

export function getTagList(data) {
    return request({
        url: 'aisEsbWeb/mes/core/getStationQualityTagList',
        method: 'post',
        data
    })
}

export function exportEventInsert(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesStationQualityExportEventInsert',
        method: 'post',
        data
    })
}

export default { sel, getTagList, exportEventInsert }