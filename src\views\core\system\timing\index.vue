<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.taskScheduling.taskName')">
                <!-- 任务名称： -->
                <el-input v-model="query.job_name" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
          <el-form-item :label="$t('lang_pack.taskScheduling.cellId')" prop="cell_id">
            <!-- 单元ID -->
            <el-select v-model="form.cell_id" filterable>
              <el-option v-for="item in cellData" :key="item.cell_id" :label="item.cell_container_name + ' ' + item.cell_container_des" :value="item.cell_id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.BeanName')" prop="bean_name">
            <!-- Bean名称 -->
            <el-input v-model="form.bean_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.cronExpression')" prop="cron_expression">
            <!-- Cron表达式 -->
            <el-input v-model="form.cron_expression" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.taskName')" prop="job_name">
            <!-- 任务名称 -->
            <el-input v-model="form.job_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.mannerExecution')" prop="method_name">
            <!-- 执行方法 -->
            <el-input v-model="form.method_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.failureSuspend')">
            <!-- 失败暂停 -->
            <el-radio-group v-model="form.pause_after_failure">
              <el-radio :label="0">是</el-radio>
              <el-radio :label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.parameter')" prop="params">
            <!-- 参数 -->
            <el-input v-model="form.params" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.successSuspendRemove')">
            <!-- 成功暂停/删除 -->
            <el-radio-group v-model="form.pause_after_success">
              <el-radio :label="0">是</el-radio>
              <el-radio :label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.remark')" prop="description">
            <!-- 备注 -->
            <el-input v-model="form.description" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.taskScheduling.taskStatus')">
            <!-- 任务状态 -->
            <el-radio-group v-model="form.is_pause">
              <el-radio :label="0">启用</el-radio>
              <el-radio :label="1">暂停</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" fixed="left" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="cell_id" :label="$t('lang_pack.taskScheduling.cellId')">
              <!-- 单元 -->
              <template slot-scope="scope">
                {{ getCelDes(scope.row.cell_id) }}
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="bean_name" :label="$t('lang_pack.taskScheduling.BeanName')" width="160" />
            <!-- Bean名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="job_name" :label="$t('lang_pack.taskScheduling.taskName')" width="160" />
            <!-- 任务名称 -->
            <el-table-column  :show-overflow-tooltip="true" prop="method_name" :label="$t('lang_pack.taskScheduling.mannerExecution')" width="160" />
            <!-- 执行方法 -->
            <el-table-column  :show-overflow-tooltip="true" prop="cron_expression" :label="$t('lang_pack.taskScheduling.cronExpression')" width="100" />
            <!-- Cron表达式 -->
            <el-table-column  :show-overflow-tooltip="true" prop="params" :label="$t('lang_pack.taskScheduling.parameter')" width="120" />
            <!-- 参数 -->
            <el-table-column  :show-overflow-tooltip="true" prop="description" :label="$t('lang_pack.taskScheduling.remark')" width="120" />
            <!-- 备注 -->
            <el-table-column  :label="$t('lang_pack.taskScheduling.status')" align="center" prop="is_pause" width="100">
              <!-- 状态 -->
              <template slot-scope="scope">
                <el-tag :type="scope.row.is_pause === 1 ? 'info' : 'success'">{{ scope.row.is_pause === 1 ? '暂停' : '启用' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" prop="pause_after_failure" :label="$t('lang_pack.taskScheduling.pauseAfterFailure')" width="180" />
            <!-- 失败后暂停 -->
            <el-table-column  :show-overflow-tooltip="true" prop="pause_after_success" :label="$t('lang_pack.taskScheduling.pauseAfterSuccessRemove')" width="220" />
            <!-- 成功后暂停/删除 -->

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudTiming from '@/api/core/system/sysTiming'
import { sel as selCell } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  job_id: '',
  bean_name: '',
  cron_expression: '',
  is_pause: 0,
  job_name: '',
  method_name: '',
  params: '',
  description: '',
  pause_after_failure: 0,
  pause_after_success: 0,
  cell_id: 0
}
export default {
  name: 'SYS_TIMING',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '任务调度',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'job_id',
      // 排序
      sort: ['job_id asc'],
      // CRUD Method
      crudMethod: { ...crudTiming },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_quartz_job:add'],
        edit: ['admin', 'sys_quartz_job:edit'],
        del: ['admin', 'sys_quartz_job:del'],
        reset: ['admin', 'sys_quartz_job:reset']
      },
      rules: {
        bean_name: [{ required: true, message: '请输入Bean名称', trigger: 'blur' }],
        cron_expression: [{ required: true, message: '请输入Cron表达式', trigger: 'blur' }],
        job_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        method_name: [{ required: true, message: '请输入方法名称', trigger: 'blur' }],
        cell_id: [{ required: true, message: '请输入单元ID', trigger: 'blur' }]
      },
      cellData: []
    }
  },
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {
    const query = {
      user_name: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    selCell(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.cellData = defaultQuery.data
            this.cellData.push({ cell_id: 0, cell_container_name: '', cell_container_des: '通用参数' })
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 获取单元的中文描述
    getCelDes(cell_id) {
      var item = this.cellData.find(item => item.cell_id === cell_id)
      if (item !== undefined) {
        return item.cell_container_name + ' ' + item.cell_container_des
      }
      return cell_id
    }
  }
}
</script>
