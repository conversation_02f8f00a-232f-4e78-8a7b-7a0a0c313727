<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <!--表：sys_fmod_prod_line-->
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产线：">
                <el-select v-model="query.prod_line_id">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="班次代码/名称：">
                <el-input v-model="query.shiftCodeName" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="160px" :inline="true">
          <!--表：sys_fmod_prod_line-->
          <el-form-item label="产线" prop="prod_line_id">
            <!-- 产线 -->
            <el-select v-model="form.prod_line_id" size="mini">
              <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
          </el-form-item>

          <el-form-item label="班次代码" prop="shift_code">
            <el-input v-model="form.shift_code" />
          </el-form-item>
          <el-form-item label="班次名称" prop="shift_name">
            <el-input v-model="form.shift_name" />
          </el-form-item>
          <el-form-item label="班次开始时间" prop="shift_start_time">
            <el-time-picker v-model="form.shift_start_time" placeholder="选择时间" format="HH:mm:ss" value-format="HH:mm:ss" />
          </el-form-item>
          <el-form-item label="班次结束时间" prop="shift_end_time">
            <el-time-picker v-model="form.shift_end_time" placeholder="选择时间" format="HH:mm:ss" value-format="HH:mm:ss" />
          </el-form-item>
          <el-form-item label="班次排序" prop="shift_order">
            <el-input v-model="form.shift_order" />
          </el-form-item>
          <el-form-item label="有效标识">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              :height="height"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column  type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  v-if="1 == 0" width="10" prop="shift_id" label="id" />
              <el-table-column  v-if="1 == 0" width="10" prop="prod_line_id" label="prod_line_id" />

              <el-table-column  :show-overflow-tooltip="true" prop="prod_line_code" label="产线代码" />
              <el-table-column  :show-overflow-tooltip="true" prop="prod_line_des" label="产线描述" />
              <el-table-column  :show-overflow-tooltip="true" prop="shift_code" label="班次代码" />
              <el-table-column  :show-overflow-tooltip="true" prop="shift_name" label="班次名称" />
              <el-table-column  :show-overflow-tooltip="true" prop="shift_start_time" label="班次开始时间" width="120" />
              <el-table-column  :show-overflow-tooltip="true" prop="shift_end_time" label="班次结束时间" width="120" />
              <el-table-column  :show-overflow-tooltip="true" prop="shift_order" label="班次排序" />
              <el-table-column  label="是否有效" align="center" prop="enable_flag">
                <!-- 有效标识 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
                </template>
              </el-table-column>
              <el-table-column  label="操作" width="175" align="center" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="$refs.shiftWorkItem && $refs.shiftWorkItem.crud.toAdd()">新增工作时间</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <shiftWorkItem ref="shiftWorkItem" class="tableFirst" :shift_id="currentShiftId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import crudShift from '@/api/mes/core/shift'
import shiftWorkItem from './shiftWorkItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  shift_id: '',
  prod_line_id: '',
  shift_code: '',
  shift_name: '',
  shift_start_time: '',
  shift_end_time: '',
  shift_order: '1',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_SHIFT',
  components: { crudOperation, rrOperation, udOperation, pagination, shiftWorkItem },
  props: {},
  cruds() {
    return CRUD({
      title: '班次',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'shift_id',
      // 排序
      sort: ['shift_id asc'],
      // CRUD Method
      crudMethod: { ...crudShift },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'sys_fmod_shift:add'],
        edit: ['admin', 'sys_fmod_shift:edit'],
        del: ['admin', 'sys_fmod_shift:del'],
        down: ['admin', 'sys_fmod_shift:down']
      },
      rules: {
        shift_code: [{ required: true, message: '请输入班次代码', trigger: 'blur' }]
      },
      currentShiftId: 0,
      // 产线数据
      prodLineData: []
    }
  },
  watch: {},
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {
    // 加载 产线LOV
    const query = {
      userID: Cookies.get('userName')
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    handleRowClick(row, column, event) {
      this.currentShiftId = row.shift_id
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.el-date-editor.el-input {
  width: 100%;
}
</style>
