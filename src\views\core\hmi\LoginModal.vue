<template>
  <el-dialog
    append-to-body
    title="权限校验"
    :visible.sync="isVisible"
    width="30%"
    center
  >
    <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="100px" class="demo-ruleForm">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="ruleForm.username" type="username" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="ruleForm.password" type="password" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitLogin('ruleForm')">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { sel as selEditPermission } from '@/api/eap/project/sh/EditPermission'
export default {
  data() {
    return {
      ruleForm: {
        username: '',
        password: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      isVisible: false
    }
  },
  methods: {
    open() {
      this.isVisible = true
    },
    submitLogin() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          selEditPermission(this.ruleForm).then(res => {
            if (!res.data) return this.$message.error('账号或密码错误！')
            if (res.data.findIndex(n => n.menu_item_id === 85) !== -1) {
              this.isVisible = false
              this.$emit('editPopup')
            } else {
              this.$message.error('暂无权限！')
            }
          })
        } else {
          console.log('error submit!!')
        }
      })
    }
  }
}
</script>

