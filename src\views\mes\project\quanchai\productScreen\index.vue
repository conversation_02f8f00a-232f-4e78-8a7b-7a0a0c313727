<template>
  <div class="screen_container">
    <div class="screen_head">
      <h2>全柴装三生产车间数字看板</h2>
      <div class="headLogo">
        <img class="logo" src="@/assets/images/qcLogo.png">
        <div class="imgtime"><img src="@/assets/images/time.png"><span class="showTime">{{ gettime }}</span><span class="showTime timeMargin">{{ week }}</span></div>
      </div>
    </div>
    <div class="mainbox">
      <div class="column">
        <div class="columnFirst">
          <div class="panel panelOne">
            <div class="panelHeight panelbgone">
              <p>理论节拍时间：<span>{{ beatTime }}</span></p>
              <p>实际节拍时间: <span>{{ actualBeat }}</span></p>
            </div>
            <div class="panelbgone">
              <div id="hourOutput" class="hourOutputone" />
            </div>
          </div>
          <div class="panel panelOne">
            <div class="panelHeight heightone panelbgone">
              <p><img src="@/assets/images/qc_sj.png" alt=""> 预计完成时间：<span class="sjstyle">{{ planCompletedTime }}</span></p>
              <p><img src="@/assets/images/qc_jx.png" alt=""> 当前在制机型：<span class="sjstyle">{{ smallModelType }}</span></p>
              <p><img src="@/assets/images/qc_sl.png" alt=""> 在制机型数量：<span class="sjstyle">{{ modelTypeNum }}</span></p>
            </div>
            <div class="panelbgone marginTen">
              <div id="indicatorr" class="indicatorrone" />
            </div>
            <div class="panelHeight heightone panelbgone heightOther">
              <div id="statistic" class="hourOutputone" />
            </div>
          </div>
          <div class="panel panelOne">
            <div class="panelHeight panelbgone">
              <p>目标平衡率：<span>{{ rateNumber }}</span></p>
              <p>实际平衡率: <span>{{ equilibriumRate }}</span></p>
            </div>
            <div class="panelbgone">
              <div id="rate" class="hourOutputone" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { selQcWorkshopBoardBeat, selQcWorkshopBoardOutput, selQcBottleneckProcess, selQcBeatTime } from '@/api/mes/project/mesQcMaterialWarehouseBoard'
import Cookies from 'js-cookie'
import tooltipShow from 'roc-tooltip-show'
export default {
  name: 'MES_QC_ANDON_SCREEN',
  data() {
    return {
      hourOutputOption: {
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#84ffff' }, // 设置颜色渐变
              { offset: 1, color: '#2962ff' }
            ]
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        grid: {
          right: '10%',
          left: '5%'
        },
        legend: {
          data: ['小时产量'],
          top: '5%',
          textStyle: {
            color: 'rgba(255, 255, 255, 1)',
            fontSize: 16
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              color: '#3fddff', // 刻度线标签颜色
              rotate: 60 // 主要是下面的代码-倾斜
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#878a88'
              }
            },
            // prettier-ignore
            data: []
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '产量',
            position: 'right',
            alignTicks: true,
            splitLine: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470C6'
              }
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '小时产量',
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              symbol: ['none', 'none'],
              position: 'insideTopCenter',
              itemStyle: { normal: { label: { show: true, textStyle: {
                fontWeight: 'normal',
                fontSize: 18,
                color: '#ffffff'
              }}}}
            },
            type: 'line',
            yAxisIndex: 0,
            symbol: 'circle',
            symbolSize: 20,
            itemStyle: { normal: { label: { show: true, textStyle: {
              fontWeight: 'normal',
              fontSize: 18,
              color: '#ffffff'
            }}}},
            data: []
          }
        ]
      },
      statisticOption: {
        title: {
          show: false,
          text: '',
          link: '',
          target: 'blank',
          textStyle: {
            color: '#fff',
            fontSize: 18,
            textShadowColor: '',
            textShadowBlur: 0
          },
          subtext: '',
          sublink: '',
          subtarget: 'blank',
          subtextStyle: {
            color: '#e0e0e0',
            fontSize: 12,
            textShadowColor: '',
            textShadowBlur: 0
          },
          itemGap: 8,
          left: 'auto',
          top: 'auto',
          right: 'auto',
          bottom: 'auto'
        },
        tooltip: {
          show: false
        },
        grid: {
          right: '5%',
          left: '5%',
          bottom: '0%'
        },
        legend: {
          show: true,
          type: 'plain',
          top: '12%',
          orient: 'horizontal',
          itemGap: 20,
          itemWidth: 20,
          itemHeight: 20,
          textStyle: {
            color: 'rgba(255, 255, 255, 1)',
            fontSize: 16
          },
          icon: 'triangle',
          data: [
            '当月目标产量',
            '当月累计产量'
          ]
        },
        xAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            data: [
              ''
            ],
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '当月目标产量',
            type: 'bar',
            barWidth: 16,
            stack: '1',
            label: {
              borderWidth: 10,
              distance: 20,
              align: 'center',
              verticalAlign: 'middle',
              borderRadius: 1,
              borderColor: '#2196f3',
              backgroundColor: '#2196f3',
              show: true,
              position: 'top',
              color: '#fff',
              fontSize: 12
            },
            itemStyle: {
              color: '#2196f3'
            },
            data: [
              {
                value: 60
              }
            ]
          },
          {
            name: '当月累计产量',
            type: 'bar',
            barWidth: 16,
            stack: '1',
            label: {
              borderWidth: 10,
              distance: 20,
              align: 'center',
              verticalAlign: 'middle',
              borderRadius: 1,
              borderColor: '#f06292',
              backgroundColor: '#f06292',
              show: true,
              position: 'top',
              color: '#fff',
              fontSize: 12
            },
            itemStyle: {
              color: '#f06292'
            },
            data: [
              {
                value: 40
              }
            ]
          }
        ]
      },
      rateOption: {
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#84ffff' }, // 设置颜色渐变
              { offset: 1, color: '#2962ff' }
            ]
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        grid: {
          right: '10%',
          left: '5%'
        },
        legend: {
          data: ['TOP5瓶频工序'],
          top: '5%',
          textStyle: {
            color: 'rgba(255, 255, 255, 1)',
            fontSize: 16
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              color: '#3fddff' // 刻度线标签颜色
            },
            // prettier-ignore
            data: []
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '时间/秒',
            position: 'right',
            alignTicks: true,
            splitLine: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470C6'
              }
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: 'TOP5瓶频工序',
            areaStyle: {// 覆盖区域的渐变色
              normal: {
                color: {
                  type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                  colorStops: [
                    {
                      offset: 0, color: 'rgba(29,73,193,1)' // 0% 处的颜色
                    },
                    {
                      offset: 1, color: 'rgba(12,24,99, 0.9)' // 100% 处的颜色
                    }
                  ],
                  global: false // 缺省为 false
                }
              }
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              symbol: ['none', 'none'],
              position: 'insideTopCenter',
              itemStyle: { normal: { label: { show: true, textStyle: {
                fontWeight: 'normal',
                fontSize: 18,
                color: '#ffffff'
              }}}}
            },
            type: 'bar',
            yAxisIndex: 0,
            symbol: 'circle',
            symbolSize: 20,
            itemStyle: { normal: { label: { show: true, textStyle: {
              fontWeight: 'normal',
              fontSize: 18,
              color: '#ffffff'
            }}}},
            data: []
          }
        ]
      },
      indicatorrOption: {
        color: ['#24e42f', '#03a9f4', '#ffc107'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          data: ['目标产量', '完成产量'],
          textStyle: {
            color: '#ffffff',
            fontSize: 16,
            fontWeight: 700
          },
          top: '10%'
        },
        series: [
          {
            name: '数据状态',
            type: 'pie',
            radius: '70%',
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold',
                color: '#ffffff'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: '50', name: '目标产量' },
              { value: '100', name: '完成产量' }
            ],
            top: '10%'
          }
        ]
      },
      indicatorr: null,
      hourOutput: null,
      statistic: null,
      rate: null,
      materialTableData: [],
      timer: '',
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: '',
      gettime: '',
      week: '',
      actualBeat: '',
      planCompletedTime: '---',
      smallModelType: '---',
      modelTypeNum: '---',
      equilibriumRate: '',
      beatTime: '',
      rateNumber: '',
      hourOutputName: [],
      hourOutputValue: [],
      bottleneckProcessName: [],
      bottleneckProcessValue: []
    }
  },
  created: function() {
    this.getTime()
    this.timer1 = setInterval(() => {
      this.getTime()
    }, 1000)
    this.initQcBeatTime()
    this.initEquiLibriumRate()
  },
  mounted() {
    this.echartsInit()
    this.initQcWorkshopBoardBeat()
    this.timer2 = setInterval(() => {
      this.initQcWorkshopBoardBeat()
    }, 10000)
    this.initQcWorkshopBoardOutput()
    this.timer3 = setInterval(() => {
      this.initQcWorkshopBoardOutput()
    }, 10000)
    this.initQcBottleneckProcess()
    this.timer4 = setInterval(() => {
      this.initQcBottleneckProcess()
    }, 10000)
  },
  // 离开此页面时销毁定时器
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
  },
  methods: {
    // 大屏左侧数据
    initQcWorkshopBoardBeat() {
      selQcWorkshopBoardBeat()
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.actualBeat = res.actualBeat

          this.planCompletedTime = res.planCompletedTime
          this.smallModelType = res.smallModelType
          this.modelTypeNum = res.modelTypeNum

          if (this.planCompletedTime === undefined) {
            this.planCompletedTime = '---'
          }
          if (this.smallModelType === undefined) {
            this.smallModelType = '---'
          }
          if (this.modelTypeNum === undefined) {
            this.modelTypeNum = '---'
          }

          console.log(this.planCompletedTime, this.smallModelType, this.modelTypeNum)
          this.hourOutputName = res.hourOutput.map(obj => obj.name)
          this.hourOutputValue = res.hourOutput.map(obj => obj.value)

          this.hourOutputOption.xAxis[0].data = this.hourOutputName
          this.hourOutputOption.series[0].data = this.hourOutputValue
          this.hourOutput.setOption(this.hourOutputOption)
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 大屏中间数据
    initQcWorkshopBoardOutput() {
      selQcWorkshopBoardOutput()
        .then(res => {
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.planCompletedTime = res.planCompletedTime
          this.smallModelType = res.smallModelType
          this.modelTypeNum = res.modelTypeNum
          this.indicatorrOption.series[0].data[0].value = res.dayPlanOutput
          this.indicatorrOption.series[0].data[1].value = res.dayOutput
          this.statisticOption.series[0].data[0].value = res.monthPlanOutput
          this.statisticOption.series[1].data[0].value = res.monthOutput
          this.indicatorr.setOption(this.indicatorrOption)
          this.statistic.setOption(this.statisticOption)
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 大屏右侧数据
    initQcBottleneckProcess() {
      selQcBottleneckProcess()
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.equilibriumRate = res.equilibriumRate
          this.bottleneckProcessName = res.bottleneckProcess.map(obj => obj.name)
          this.bottleneckProcessValue = res.bottleneckProcess.map(obj => obj.value)

          this.rateOption.xAxis[0].data = this.bottleneckProcessName
          this.rateOption.series[0].data = this.bottleneckProcessValue
          this.rate.setOption(this.rateOption)
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    initQcBeatTime() {
      // 获取系统参数信息
      var queryParameter = {
        userName: Cookies.get('userName'),
        parameter_code: 'MES_THEORY_BEAT_TIME',
        enable_flag: 'Y'
      }
      selQcBeatTime(queryParameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.beatTime = defaultQuery.data[0].parameter_val
          }
        }).catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    initEquiLibriumRate() {
      // 获取系统参数信息
      var queryParameter = {
        userName: Cookies.get('userName'),
        parameter_code: 'MES_PLAN_EQUILIBRIUM_RATE',
        enable_flag: 'Y'
      }
      selQcBeatTime(queryParameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.rateNumber = defaultQuery.data[0].parameter_val
          }
        }).catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    },
    // 初始化echarts
    echartsInit() {
      this.indicatorr = this.$echarts.init(document.getElementById('indicatorr'))
      this.hourOutput = this.$echarts.init(document.getElementById('hourOutput'))
      this.rate = this.$echarts.init(document.getElementById('rate'))
      this.statistic = this.$echarts.init(document.getElementById('statistic'))
      // 用法示例
      const options = {
        interval: 2000,
        loopSeries: false,
        seriesIndex: 0,
        updateData: null
      }
      tooltipShow(this.indicatorr, this.indicatorrOption, options)
      tooltipShow(this.rate, this.rateOption, options)
      window.addEventListener('resize', () => {
        this.indicatorr.resize()
        this.hourOutput.resize()
        this.rate.resize()
        this.statistic.resize()
      })
      this.indicatorr.setOption(this.indicatorrOption)
      this.hourOutput.setOption(this.hourOutputOption)
      this.rate.setOption(this.rateOption)
      this.statistic.setOption(this.statisticOption)
    }
  }
}
</script>

<style lang="less" scoped>
body{
  background: red;
}
.screen_container{
  background: url('~@/assets/images/wcbg.jpg') no-repeat #000;
  background-size: cover;
  height:calc(100vh);
  .screen_head{
    position: relative;
    background: url('~@/assets/images/headBg.png') no-repeat;
    background-size: 100% 100%;
    height: 140px;
    h2{
        margin: 0;
        height: 140px;
        line-height: 100px;
        text-align: center;
        color: #ffffff;
        font-weight: 700;
        font-size: 40px;
    }
    .headLogo{
      width: 100%;
      position: absolute;
      top: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      .logo{
       width: 100px;
       margin-left: 152px;
      }
      .imgtime{
        display: flex;
        align-items: center;
        img{
          width: 20px;
          margin-right: 5px;
        }
        .showTime{
          color: #00e2fd;
          font-size: 30px;
          font-weight: 700;
        }
        .timeMargin{
          margin-left: 10px;
        }
      }
    }
  }
  .mainbox{
    // min-width: 1024px;
    // max-width: 1920px;
    padding: 0.125rem 0.125rem 0;
    display: flex;
    .column{
      width: 100%;
      .columnFirst{
        display: flex;
        .panel{
          padding: 0 0.1875rem 0.1875rem;
          margin-bottom: 0.1875rem;
        }
        .panelOne{
          width: 33.333%;
          margin-right: 0.1875rem;
          .panelHeight{
            height: 320px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-around;
          }
          .heightOther{
            height: 220px;
          }
          .heightone{
            flex-direction: column;
            justify-content: center;
            p{
              margin: 20px 0 !important;
            }
          }
          .panelbgone{
            background: url('~@/assets/images/bordercenter.png') no-repeat;
            background-size: 100% 100%;
              p{
              font-size: 22px;
              color: #ffffff;
              font-weight: 700;
              margin: 10px 0;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-wrap: wrap;
              img{
                width: 36px;
                margin-right: 32px;
              }
                span{
                  color: #00ed1f;
                  font-size: 32px;
                  margin-left: 20px;
                }
            }
          }
          .hourOutputone{
            width: 100%;
            height: 600px;
          }
          .indicatorrone{
            width: 100%;
            height: 370px;
          }
        }
        .panelTwo{
          width: 70%;
        }
        .panelbgtwo{
          background: url('~@/assets/images/bordercenter.png') no-repeat;
            background-size: 100% 100%;
        }
      }
    }
  }
}
.marginTen{
  margin-bottom: 10px;
}
.sjstyle{
  width: 160px;
  display: block;
}
// 细化滚动条
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 0 !important;
  height: 0 !important;
}
:deep(.el-table){
  background: none !important;
  margin-top: 30px !important;
  th{
    background: #1daad6 !important;
    color: #fff !important;
    font-size: 16px !important;
  }
  tr{
    background: none;
    color: #fff !important;
    font-size: 28px !important;
  }
  .cell{
    line-height: 26px;
  }
}
:deep(.el-table__body tr.current-row > td){
    background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table__row:hover){
     background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background: none !important;
}
</style>
