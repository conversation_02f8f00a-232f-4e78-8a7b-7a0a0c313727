<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          工单号
        </template>
        <el-input ref="webLotNum" v-model="webLotNum" clearable size="mini" @input="handleInput($event,'工单号',24,'webLotNum')" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          简码
        </template>
        <el-input ref="webLotShortNum" v-model="webLotShortNum" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          载具码
        </template>
        <el-input ref="webPalletNum" v-model="webPalletNum" clearable size="mini" @input="handleInput($event,'载具码',12,'webPalletNum')" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          数量
        </template>
        <el-input ref="webLotPlanCount" v-model="webLotPlanCount" clearable size="mini" @input="handleInputIsNumber($event,'数量','webLotPlanCount')" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          板长
        </template>
        <el-input ref="webPanelLength" v-model="webPanelLength" clearable size="mini" @input="handleInputIsNumber($event,'板长','webPanelLength')" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          板宽
        </template>
        <el-input ref="webPanelWidth" v-model="webPanelWidth" clearable size="mini" @input="handleInputIsNumber($event,'板宽','webPanelWidth')" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          翻转
        </template>
        <el-input ref="webPanelFz" v-model="webPanelFz" clearable size="mini" @input="handleInputIsNumber($event,'翻转','webPanelFz')" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          料号
        </template>
        <el-input ref="webPanelLh" v-model="webPanelLh" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo">提 交</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    },
    DyCheckCode: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      webLotNum: '',
      webLotShortNum: '',
      webPalletNum: '',
      webLotPlanCount: '0',
      webPanelLength: '0',
      webPanelWidth: '0',
      webPanelFz: '0',
      webPanelLh: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webLotNum.focus()
    })
  },
  methods: {
    handleInput(e, label, val, attr) {
      if (this.DyCheckCode) {
        if (e.length === val) {
          this.$message({ type: 'info', message: `${label + '长度不能超过' + val + '位'}` })
          return
        }
        if (e.length > val) {
          this.$message({ type: 'error', message: `${label + '长度大于' + val + '位,已清空'}` })
          this[attr] = ''
          return
        }
      }
    },
    handleInputIsNumber(e, label, attr) {
      if (this.DyCheckCode) {
        const regex = /^\d+$/
        if (!regex.test(e)) {
          this.$message({ type: 'error', message: `${label + '只能输入数字'} ` })
          this[attr] = ''
        }
      }
    },
    handleSendInfo() {
      if (this.webLotNum === '') {
        this.$message({ message: '请输入工单号', type: 'info' })
        return
      }
      if (this.webLotShortNum === '') {
        this.$message({ message: '请输入工单简码', type: 'info' })
        return
      }
      if (this.webPalletNum === '') {
        this.$message({ message: '请输入载具码', type: 'info' })
        return
      }
      if (this.webLotPlanCount === '' || parseInt(this.webLotPlanCount) <= 0) {
        this.$message({ message: '请输入工单数量', type: 'info' })
        return
      }
      if (this.webPanelLength === '' || parseInt(this.webPanelLength) <= 0) {
        this.$message({ message: '请输入板长', type: 'info' })
        return
      }
      if (this.webPanelWidth === '' || parseInt(this.webPanelWidth) <= 0) {
        this.$message({ message: '请输入板宽', type: 'info' })
        return
      }
      if (this.webPanelLh === '') {
        this.$message({ message: '请输入料号', type: 'info' })
        return
      }
      if (this.webPanelFz === '') {
        this.webPanelFz = '0'
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebLotNum,
        TagValue: this.webLotNum + ',' + this.webLotShortNum + ',' + this.webPalletNum + ',' +
                  this.webLotPlanCount + ',' + this.webPanelLength + ',' + this.webPanelWidth + ',' + this.webPanelFz + ',' + this.webPanelLh
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebLotRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebLotNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
