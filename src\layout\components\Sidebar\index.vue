<template>
  <div :class="{ 'has-logo': showLogo,'sidebar-logo':getUserValue() === '1' }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <div v-if="!isCollapse" class="sysSearch">
      <el-autocomplete v-model="sysSearch" class="inline-input" placeholder="搜索菜单" suffix-icon="el-icon-search" :fetch-suggestions="querySearch" :trigger-on-focus="false" @select="handleSelect" />
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="variables.menuBg" :text-color="variables.menuText" :unique-opened="$store.state.settings.uniqueOpened" :active-text-color="variables.menuActiveText" :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="route in permission_routers" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
    <userInfo :collapse="isCollapse" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import UserInfo from './UserInfo'
import variables from '@/assets/styles/variables.scss'
import Cookies from 'js-cookie'

export default {
  components: { SidebarItem, Logo, UserInfo },
  data() {
    return {
      sysSearch: '',
      restaurants: []
    }
  },
  computed: {
    ...mapGetters(['permission_routers', 'sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  mounted() {
    this.permission_routers.forEach(item => {
      if (item.children !== undefined) {
        for (let i = 0; i < item.children.length; i++) {
          if (item.children[i].meta !== undefined) {
            this.restaurants[this.restaurants.length + 1] = { value: item.children[i].meta.title, path: item.path + '/' + item.children[i].path }
          }
        }
      }
    })
  },
  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return restaurant => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    handleSelect(item) {
      this.$router.push({ path: item.path.replace('//', '/') })
    },
    getUserValue(){
      return  this.$store.state.settings.userValue
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss">
.sysSearch {
  width: 90%;
  margin-left: 5%;
  .el-input--small .el-input__inner {
    border-radius: 15px;
    background-color: #5a6bd5;
    border: 2px solid #ffffff;
    font-size: 12px;
  }
  .el-input__suffix {
    right: 10px;
    color: #ffffff;
    font-size: 20px;
  }
}
.el-submenu__title i {
  color: #ffffff;
}
.sysSearch .el-input__inner {
  $placeholder: #ffffff;
  &::placeholder {
    color: $placeholder;
  }
  &::-webkit-input-placeholder {
    /* WebKit browsers 适配谷歌 */
    color: $placeholder;
  }
  &:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 适配火狐 */
    color: $placeholder;
  }
  &::-moz-placeholder {
    /* Mozilla Firefox 19+ 适配火狐 */
    color: $placeholder;
  }
  &:-ms-input-placeholder {
    /* Internet Explorer 10+  适配ie*/
    color: $placeholder;
  }
}
</style>