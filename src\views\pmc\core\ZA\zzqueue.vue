<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24" class="elnopadding">
          <div >
            <el-table
              :data="tableData"
              style="width: 100%"
              height="520"
              :row-style="{height:'45px'}"
              :cell-style="{padding:'0px'}"
            >
              <el-table-column 
                prop="orderProd"
                label="订单号"
                align="center"
              />
              <el-table-column 
                prop="vin"
                label="vin号"
                width="80"
                align="center"
              />
              <el-table-column 
                prop="offlineTime"
                label="下线日期"
                align="center"
              />
              <el-table-column 
                prop="secondStageStatus"
                width="70"
                align="center"
                label="静检">
                <template slot-scope="scope">
                  <span :class="scope.row.secondStageStatus == '2' ? 'activeGreen' : 'activeRed'"></span>
                </template>
              </el-table-column>
              <el-table-column 
                prop="firstStageStatus"
                label="四轮定位"
                align="center"
                width="110">
                <template slot-scope="scope">
                  <span :class="scope.row.firstStageStatus == '2' ? 'activeGreen' : 'activeRed'"></span>
                </template>
              </el-table-column>
              <el-table-column label="检验线" align="center">
                <el-table-column 
                  prop="thirdStagePerson"
                  label="人员"
                  width="100"
                  align="center"
                />
                <el-table-column 
                  prop="thirdStageStartTime"
                  label="时间"
                  align="center"
                />
              </el-table-column>
              <el-table-column label="终检" align="center">
                <el-table-column 
                  prop="forthStagePerson"
                  label="人员"
                  width="100"
                  align="center"
                />
                <el-table-column 
                  prop="forthStageStartTime"
                  label="时间"
                  align="center"
                />
              </el-table-column>
              <el-table-column 
                  prop="planStorageTime"
                  label="预计入库时间"
                  align="center"
                />
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle footerStyle">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <h2>当天生产线计划台数:<span>{{ objData.planNum }}</span>生产线下线数量台数:<span>{{ objData.offlinenNum }}</span>调试计划入库数:<span>{{ objData.debugPlanNum }}</span>调试PDI入库数:<span>{{ objData.debugPdiNum }}</span>调试终检数:<span>{{ objData.finalNum }}</span></h2>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getStationTime,getSelectVehicleDispatch,getSelectTodayPlanCount } from '@/api/pmc/sysTime'
import headlbg from '@/assets/images/headlbg.png'
export default {
  name: 'queue',
  data() {
    return {
      prod_line_des: '车辆派工计划',
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      headlbg: headlbg,
      tableData: [],
      timer:null,
      timer2:null,
      qualityPageNo:1,
      objData:{
        offlinenNum: "0", //生产线下线台数
        finalNum: "0", //调试终检数
        planNum: "0", //当天生产线计划台数
        debugPlanNum: "0", //调试计划入库数
        debugPdiNum: "0" //调试PDI入库数
      }
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.timer = setInterval(() => {
      this.initstationTime()
    }, 1000)
    this.getLargeScreen()//派工大屏数据
    this.getUnderlayData()//派工大屏底图蓝色区域数据
    this.timer2 = setInterval(() => {
      this.getLargeScreen()
      this.getUnderlayData()
    }, 5000)
  },
  beforeDestroy(){
    clearInterval(this.timer)
    clearInterval(this.timer2)
  },
  mounted() {
  },
  methods: {// 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    getLargeScreen(){
      const query = `queryMode=page&pageNo=${this.qualityPageNo}&limit=10`
      getSelectVehicleDispatch(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '请求异常'})
            return
        }
        if(this.qualityPageNo * 10 >=  res.total){
          this.qualityPageNo = 1
        }else{
          this.qualityPageNo ++
        }
        this.tableData = res.data
        if (!this.tableData) {
            this.tableData = []
        }
      })
      .catch(() => {
        this.tableData = []
        this.$message({type:'error',message: '请求异常',})
      })
    },
    getUnderlayData(){
      const query = {}
      getSelectTodayPlanCount(query).then(res=>{
        if (res.code != 200) {
          this.$message({type:'error',message: '请求异常'})
            return
        }
        const arr = ['null','undefined','']//判断返回的数据中是否有null、undefien、'',有的话赋值为0
        for(let key in res.data){
          if(arr.includes(res.data[key])){
            res.data[key] = '0'
          }
        }
        this.objData = Object.assign(this.objData,res.data)
      })
      .catch(() => {
        this.$message({type:'error',message: '请求异常',})
      })
    }
  }
}
</script>

<style lang="less" scoped>
// body{
//   background: #1d3a6a !important;
// }
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.wraptext p::before{
    content: '';
    width: 10px;
    height: 10px;
    background-color: #ff0000;
    display: block;
    border-radius: 50%;
    margin-right: 10px;
}
.cardheadbg{
  background-color: #031c45;
      // background: -webkit-gradient( linear, left bottom, left top, color-stop(0.02, #031c45), color-stop(0.51, #194998), color-stop(0.87, #031c45) );
}
.footerStyle{
  background-color: #0070c0;
  h2{
    margin: 0 10px;
    font-size: 24px;
    color: #fff;
    span{
      margin:0 12px;
      color: #fdfd21;
    }
  }
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .el-card{
  border-radius: 0;
}
::v-deep.workstationScreen{
   .el-table__header-wrapper .has-gutter{
    height: 50px;
  }
}

::v-deep .el-table th{
    // background-color: #6b88b8 !important;
    // color: #fff;
    font-size: 22px;
    font-weight: 700;
    // text-shadow: 2px 2px 4px #000;
}
::v-deep .el-table--enable-row-transition .el-table__body td{
  // background-color: #1d3a6a !important;
    // color: #fff;
    font-weight: 700;
    // text-shadow: 2px 2px 4px #000;
    text-align: center;
}
::v-deep .el-table th.el-table__cell{
  padding:0;
}
::v-deep .el-table__body{
  padding-bottom: 20px;
  td{
    .cell{
      line-height:24px;
      font-size:24px;
    }
  }
}
.activeGreen{
  width: 20px;
  height:20px;
  background:#17e317;
  display:block;
  border-radius:50%
  ;margin:0 auto;
}
.activeRed{
  width: 20px;
  height:20px;
  background:red;
  display:block;
  border-radius:50%
  ;margin:0 auto;
}
</style>
