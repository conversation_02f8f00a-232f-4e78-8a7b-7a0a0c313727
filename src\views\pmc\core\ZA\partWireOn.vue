<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardOne">
      <div class="headerStyle">
        <div class="logoStyle"><img src="@/assets/images/log.png" alt=""></div>
        <div class="headerC">
          <h2>零件线-{{ this.$route.query.station_code }}上线</h2>
        </div>
        <div class="headerL">
          {{ nowDateTime }} {{ nowDateWeek }}
        </div>
      </div>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <div class="wrapitem">
        <el-row :gutter="24" class="elRowStyle">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="carInfoTable">
                <el-table
                  :data="carInfoDataresult"
                  style="width: 100%"
                  class="partstyle"
                  height="600"
                  :row-class-name="tableRowClassName"
                >
                  <el-table-column
                    prop="seq"
                    label="序号"
                    width="120"
                  />
                  <el-table-column
                    prop="main_material_code"
                    label="车型编号"
                    width="390"
                  />
                  <el-table-column
                    prop="vin"
                    label="VIN"
                    width="390"
                  />
                  <el-table-column 
                    prop="make_order"
                    label="订单号"
                    width="270"
                  />
                  <el-table-column 
                    prop="work_status_name"
                    label="状态"
                  />
                </el-table>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getStationTime, getPmcBqPartStation } from '@/api/pmc/sysworkstationScreen'

export default {
  name: 'tireLineOn',
  data() {
    return {
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      carInfoDataresult: [],
      timer1: '',
      timer2: ''
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.timer1 = setInterval(() => {
      this.initstationTime()
    }, 1000)
    this.initPmcBqPartStation()
    this.timer2 = setInterval(() => {
      this.initPmcBqPartStation()
    }, 15000)
  },
  mounted() {

  },
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
  },
  methods: {
    tableRowClassName({ row }) {
      if (row.work_status_name === '已完成') {
        return 'row-success'
      }
      if (row.work_status_name === '进行中') {
        return 'row-warning'
      }
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 零件线数据
    initPmcBqPartStation() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getPmcBqPartStation(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.carInfoDataresult = res.data
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.elRowStyle{
  display: flex;
}
::v-deep .el-table__header{
  width: 100%;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ffba00;
  font-size: 40px;
  font-weight: 700;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.cardStyle{
  margin: 0;
  padding: 0;
  border: 0;
}
.cardOne{
  margin-bottom: 12px;
}
.headerStyle{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 15px;
  .logoStyle{
    width: 25%;
    display: flex;
    align-items: center;
    img{
      width: 130px;
    }
  }
  .headerC{
    h2{
      font-size: 56px;
      margin: 0;
    }
  }
  .headerL{
    width: 25%;
    font-size: 24px;
    text-align: right;
    font-weight: 700;
  }
}
::v-deep .el-table th {
    background-color: #ffffff !important;
    color: #000000;
    font-size: 34px;
    border-bottom: 1px solid #dfe6ec !important;
}
::v-deep .el-table__row td,::v-deep .el-table__row td{
    font-size: 30px;
    font-weight: 700;
    height: 25px;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background:#6b9ef9 !important;
  color: #ffffff !important;

}
::v-deep .el-card__body {
  display: flex;
    padding: 0px !important;
}
::v-deep .el-table .cell{
  line-height: 58px;
}
.greenactive{
  color: green !important;
}
::v-deep .row-success{
    color: #fff !important;
    background-color: #67c23a !important;
}
::v-deep .row-warning{
    color: #fff !important;
    background-color: #e6a23c !important;
}
.wrapitem{
  width: 100%;
  .itemone{
    width: 50%;
    .elRowStyleFlex{
      margin: 0;
      padding: 0;
      .el-col{
        padding-right: 0 !important;
      }
    }
  }
  .itemtwo{
    width: 50%;
    .elRowStyleFlex{
      margin: 0 !important;
      padding: 0 !important;
      .el-col{
        padding-right: 0 !important;
        padding-left: 0 !important;
      }
    }
  }
}
</style>
