<template>
    <div class="box-card">
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
            <el-form ref="query" :inline="true" size="mini" style="margin-top: 15px; padding: 0" label-width="70px">
                <el-row>
                    <el-form-item label="产线" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.prod_line_code" clearable style="width: 150px" value-key="prod_line_id"
                            @change="handleProdLineChange">
                            <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_des"
                                :value="item">
                                <span style="float: left">{{ item.prod_line_des }}</span>
                                <span style="
                                    float: right;
                                    margin-left: 20px;
                                    color: #8492a6;
                                    font-size: 13px;
                                ">{{ item.prod_line_code }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="机型" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.small_model_type" filterable clearable style="width: 130px">
                            <el-option v-for="item in smallModelTypeData" :key="item.small_type_id"
                                :label="item.small_model_type" :value="item.small_model_type">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="工位号" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.station_code" filterable clearable style="width: 150px"
                            value-key="station_code" @change="getQulityforData">
                            <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_code"
                                :value="item">
                                <span style="float: left">{{ item.station_code }}</span>
                                <span style="
                            float: right;
                            margin-left: 20px;
                            color: #8492a6;
                            font-size: 13px;
                        ">{{ item.station_des }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="控制项目" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.tag_des" filterable clearable style="width: 220px">
                            <el-option v-for="item in tagList" :key="item.tag_des" :label="item.tag_des"
                                :value="item.tag_des" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="查询时间" style="margin: 0px 0px 5px 0px">
                        <el-date-picker v-model="query.leave_date" type="datetimerange" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" range-separator="~" start-placeholder="开始日期"
                            end-placeholder="结束日期" style="width: 350px" align="right" />
                    </el-form-item>
                    <el-button style="margin: 0px 0px 5px 0px" class="filter-item" size="mini" type="primary"
                        icon="el-icon-search" @click="handleQuery()">查询</el-button>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <el-table ref="table" v-loading="tableLoading" :data="tableData" style="width: 100%"
                :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" :height="height"
                :highlight-current-row="true">
                <el-table-column :show-overflow-tooltip="true" prop="prod_line_code" width="120" label="产线编码" />
                <el-table-column :show-overflow-tooltip="true" prop="prod_line_des" width="240" label="产线描述" />
                <el-table-column :show-overflow-tooltip="true" prop="station_code" label="工位编号" />
                <el-table-column :show-overflow-tooltip="true" prop="station_des" width="300" label="工位描述" />
                <el-table-column :show-overflow-tooltip="true" prop="small_model_type" label="机型" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_des" width="130" label="控制项目" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_value" label="采集值" />
                <el-table-column :show-overflow-tooltip="true" prop="upper_limit" label="上限值" />
                <el-table-column :show-overflow-tooltip="true" prop="down_limit" label="下限值" />
                <el-table-column :show-overflow-tooltip="true" prop="trace_d_time" width="130" label="采集时间" />
            </el-table>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <div id="cpk_chart" style="width: 100%;height: calc(100vh - 620px)"></div>
        </el-card>
    </div>
</template>
<script>
import { mesQualitySpcAnalyze, mesQualitySpcTagList, meanStandardDeviation } from '@/api/mes/core/spcReport'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import ECharts from 'vue-echarts'
export default {
    name: 'MES_REPORT_BASIC',
    components: {
        ECharts
    },
    data() {
        return {
            height: document.documentElement.clientHeight - 485,
            prodLineData: [],
            smallModelTypeData: [],
            stationData: [],
            tableData: [],
            tableDataCatch: [],
            tagList: [],
            tableLoading: false,
            query: {
                station_code: '',
                small_model_type: '',
                quality_for: '',
                leave_date: null
            },
            // 时间选择器
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            meanStandardDom: null,
        }
    },
    created: function () {
        selProdLine({
            user_name: Cookies.get('userName'),
            enable_flag: 'Y'
        })
            .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.prodLineData = defaultQuery.data
                    }
                }
            })
            .catch(() => {
                this.$message({
                    message: '初始化模式数据异常',
                    type: 'error'
                })
            })
    },
    mounted() {
        this.getMeanTrend()
        window.addEventListener('resize', function () {
            that.meanStandardDom.resize()
        })
    },
    methods: {
        handleProdLineChange(data) {
            const query = {
                userID: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            }
            selStation(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.stationData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                })

                return
            selSmallModel({
                user_name: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            })
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.smallModelTypeData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '初始化模式数据异常',
                        type: 'error'
                    })
                })
        },
        handleQuery() {
            if (this.query.station_code === '') {
                this.$message({
                    message: '请选择工位号',
                    type: 'info'
                })
                return
            }
            if (this.query.small_model_type === '') {
                this.$message({
                    message: '请选择机型',
                    type: 'info'
                })
                return
            }
            if (this.query.tag_des === '') {
                this.$message({
                    message: '请选择控制项目',
                    type: 'info'
                })
                return
            }
            if (this.query.station_code === '') {
                this.$message({
                    message: '请选择工位号',
                    type: 'info'
                })
                return
            }
            this.tableData = []
            this.shifts = []
            this.reality_Beat = []
            this.standard_Beat = []
            const query = {
                prod_line_code: this.query.prod_line_code.prod_line_code,
                small_model_type: this.query.small_model_type,
                station_code: this.query.station_code.station_code,
                tag_des: this.query.tag_des,
                item_date: this.query.leave_date
            }

            this.tableLoading = true
            mesQualitySpcAnalyze(query)
                .then((res) => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code == 0) {
                        if (defaultQuery.data.length > 0) {
                            this.tableDataCatch = defaultQuery.data
                        } else {
                            this.tableData = []
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                })

            this.tableLoading = false
        },
        getQulityforData(data) {
            this.tagList = []
            mesQualitySpcTagList({
                enable_flag: 'Y',
                station_id: data.station_id
            })
                .then(res => {
                    console.log(res)
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.tagList = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询采集项目异常',
                        type: 'error'
                    })
                })
        },
        getMeanTrend() {
            this.meanStandardDom = this.$echarts.init(document.getElementById('cpk_chart'))
            meanStandardDeviation({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.meanStandardDeviation)
                        const xAxisData = []
                        const seriesData = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key])
                        }
                        const option = {
                            backgroundColor: 'white',
                            grid: {
                                top: '20%',
                                left: '5%',
                                right: '5%',
                                bottom: '8%',
                                containLabel: true
                            },
                            tooltip: {
                                trigger: 'axis',
                                borderWidth: 1,
                                axisPointer: {
                                    type: 'shadow'
                                },
                                extraCssText: 'z-index:2'

                            },
                            legend: [{
                                top: 'top',
                                left: 'center',
                                orient: 'horizontal',
                                data: ['均值'],
                                itemWidth: 15,
                                itemHeight: 10,
                                itemGap: 15,
                                borderRadius: 4,
                                textStyle: {
                                    color: '#000',
                                    fontFamily: 'Alibaba PuHuiTi',
                                    fontSize: 14,
                                    fontWeight: 400
                                }
                            }],
                            xAxis: {
                                type: 'category',
                                data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: '#393939' //X轴文字颜色
                                    }
                                }
                            },
                            yAxis: [

                                {
                                    type: 'value',
                                    name: '',
                                    nameTextStyle: {
                                        color: '#000',
                                        fontFamily: 'Alibaba PuHuiTi',
                                        fontSize: 14,
                                        fontWeight: 600
                                        // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                                    },
                                    nameGap: 30,  // 表现为上下位置
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#eeeeee'
                                        }
                                    },
                                    axisTick: {
                                        show: false
                                    },
                                    axisLabel: {
                                        color: '#393939',
                                        fontSize: 14
                                    },
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#eeeeee'
                                        }
                                    }

                                }

                            ],
                            series: [
                                {
                                    name: '均值',
                                    type: 'line',
                                    showAllSymbol: true, //显示所有图形。
                                    //标记的图形为实心圆
                                    symbolSize: 8, //标记的大小
                                    itemStyle: {
                                        //折线拐点标志的样式
                                        color: 'white',
                                        borderWidth: '2',
                                        borderColor: '#5470c6',
                                        normal: {
                                            color: '#5470c6'//拐点颜色
                                        }
                                    },
                                    lineStyle: {
                                        color: '#5470c6'
                                    },
                                    data: [0, 0, 0, 0, 0]
                                },
                            ]
                        }
                        this.meanStandardDom.setOption(option)
                    }
                } else {
                    this.meanStandardDom = null
                    this.$message({
                        type: 'warning',
                        message: defaultQuery.msg
                    })
                }
            }).catch((ex) => {
                this.meanStandardDom = null
                this.$message({
                    type: 'warning',
                    message: ex.msg
                })
            })
        },
        // 字符串转时间戳并且支持排序的方法
        sortObjectByTimestamp(obj) {
            var sortedArray = Object.entries(obj).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            });
            var sortedObj = {};
            for (var i = 0; i < sortedArray.length; i++) {
                var key = sortedArray[i][0];
                var value = sortedArray[i][1];
                sortedObj[key] = value;
            }
            return sortedObj;
        }
    }
}
</script>
