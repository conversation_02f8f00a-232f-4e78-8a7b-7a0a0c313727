import request from '@/utils/request'

// 查询标签
export function selScadaTag(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagSel',
    method: 'post',
    data
  })
}
// 新增标签
export function insScadaTag(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagIns',
    method: 'post',
    data
  })
}
// 修改标签
export function updScadaTag(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagUpd',
    method: 'post',
    data
  })
}
// 删除标签
export function delScadaTag(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagDel',
    method: 'post',
    data
  })
}

// 控制器标签查询
export function scadaTagList(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagList',
    method: 'post',
    data
  })
}

// TAG明细查询
export function scadaTagInfo(data) {
  return request({
    url: 'aisEsbWeb/core/scada/CoreScadaTagInfo',
    method: 'post',
    data
  })
}
export default { selScadaTag, insScadaTag, updScadaTag, delScadaTag,
  scadaTagList, scadaTagInfo }
