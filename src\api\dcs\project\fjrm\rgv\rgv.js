import request from '@/utils/request'

// 查询RGV基础表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodRgvSelect',
    method: 'post',
    data
  })
}
// 新增RGV基础表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodRgvInsert',
    method: 'post',
    data
  })
}
// 修改RGV基础表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodRgvUpdate',
    method: 'post',
    data
  })
}
// 删除RGV基础表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodRgvDelete',
    method: 'post',
    data
  })
}

// 修改RGV基础表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodRgvEnableFlagUpdate',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

