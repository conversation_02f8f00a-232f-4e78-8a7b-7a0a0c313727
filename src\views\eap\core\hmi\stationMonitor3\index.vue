<template>
  <div class="mesContainer">
    <el-card>
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="18">
          <table class="table3">
            <tbody>
              <tr class="trtitle">
                <td class="tdName"><span>工作编号</span></td>
                <td><span>xxxxxx</span></td>
                <td class="tdName"><span>订单号</span></td>
                <td><span> xxxxxx</span></td>
                <td class="tdName"><span>机型</span></td>
                <td><span>xxxxxx</span></td>
                <td class="tdName"><span>机型代码</span></td>
                <td><span>1</span></td>
                <td class="tdName"><span>工作标识</span></td>
                <td><span>正常</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName"><span>上线时间</span></td>
                <td><span>2022/11/29 9:15</span></td>
                <td class="tdName"><span>在制时间</span></td>
                <td><span>1m10s</span></td>
                <td class="tdName"><span>订单完工</span></td>
                <td><span>1/50</span></td>
                <td class="tdName"><span>实际/理想节拍</span></td>
                <td><span>1/1</span></td>
                <td class="tdName"><span>当前节拍</span></td>
                <td><span>1</span></td>
              </tr>
            </tbody>
          </table>
          <table class="table3">
            <tbody>
              <tr class="trtitle">
                <td class="tdName"><span>流程步骤</span></td>
                <td><span>xxxxxx</span></td>
                <td class="tdName"><span>步骤信息</span></td>
                <td><span>xxxxxx</span></td>
              </tr>
            </tbody>
          </table>
          <table class="table3">
            <tbody>
              <tr class="trtitle">
                <td class="wholetd">
                  <div class="wrappstyle">
                    <p><span class="wholeline wholelinegray" /><span>物料扫描</span></p>
                    <p><span class="wholeline wholelinegray" /><span>拧紧螺丝</span></p>
                  </div>
                </td>
                <td><span>xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</span></td>
                <td class="scan"><el-button type="warning">扫描</el-button></td>
              </tr>
            </tbody>
          </table>
          <div class="tableImg">
            <div class="showImg">
              <img src="@/assets/images/boss.gif" alt="">
            </div>
            <div class="wraptable">
              <el-table
                :data="tableData"
                style="width: 100%"
                height="120"
              >
                <el-table-column
                  :sortable="true"
                  prop="panel_index"
                  width="80"
                  label="物料号"
                />
                <el-table-column
                  :sortable="true"
                  prop="panel_barcode"
                  label="物料描述"
                />
                <el-table-column
                  :sortable="true"
                  prop="tray_barcode"
                  label="用量"
                />
                <el-table-column
                  :sortable="true"
                  prop="inspect_flag"
                  label="物料条码"
                />
                <el-table-column
                  :sortable="true"
                  prop="panel_status"
                  label="批次"
                  width="60"
                />
                <el-table-column
                  :sortable="true"
                  prop="item_date"
                  label="供应商"
                />
              </el-table>
              <table class="table3 nomargin">
                <tbody>
                  <tr class="trtitle">
                    <td class="orderwd">
                      <div class="wrappstyle">
                        <p><span>当前序号：1</span></p>
                        <p><span>套简号：1</span></p>
                        <p><span>程序号：1</span></p>
                      </div>
                    </td>
                    <td class="scan"><el-button type="warning">手工画面</el-button></td>
                  </tr>
                </tbody>
              </table>
              <el-table
                :data="tableData"
                style="width: 100%"
                height="120"
              >
                <el-table-column
                  :sortable="true"
                  prop="panel_index"
                  width="60"
                  label="序号"
                />
                <el-table-column
                  :sortable="true"
                  prop="panel_barcode"
                  label="测量对象"
                />
                <el-table-column
                  :sortable="true"
                  prop="tray_barcode"
                  label="项目"
                />
                <el-table-column
                  :sortable="true"
                  prop="inspect_flag"
                  label="采集值"
                />
                <el-table-column
                  :sortable="true"
                  prop="panel_status"
                  label="单位"
                  width="60"
                />
                <el-table-column
                  :sortable="true"
                  prop="item_date"
                  label="上下限"
                />
              </el-table>
            </div>
          </div>
          <table class="table3 daynoborder">
            <tbody>
              <tr class="trtitle">
                <td class="tdName riyueactive"><span>日累计</span></td>
                <td class="tdName"><span>0点-1点</span></td>
                <td class="tdName"><span>1点-2点</span></td>
                <td class="tdName"><span>2点-3点</span></td>
                <td class="tdName"><span>3点-4点</span></td>
                <td class="tdName"><span>4点-5点</span></td>
                <td class="tdName"><span>5点-6点</span></td>
                <td class="tdName"><span>6点-7点</span></td>
                <td class="tdName"><span>7点-8点</span></td>
                <td class="tdName"><span>8点-9点</span></td>
                <td class="tdName"><span>9点-10点</span></td>
                <td class="tdName"><span>10点-11点</span></td>
              </tr>
              <tr>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
              </tr>
            </tbody>
          </table>
          <table class="table3 daynoborder">
            <tbody>
              <tr class="trtitle">
                <td class="tdName riyueactive"><span>月累计</span></td>
                <td class="tdName"><span>11点-12点</span></td>
                <td class="tdName"><span>13点-14点</span></td>
                <td class="tdName"><span>14点-15点</span></td>
                <td class="tdName"><span>15点-16点</span></td>
                <td class="tdName"><span>16点-17点</span></td>
                <td class="tdName"><span>17点-18点</span></td>
                <td class="tdName"><span>18点-19点</span></td>
                <td class="tdName"><span>20点-21点</span></td>
                <td class="tdName"><span>21点-22点</span></td>
                <td class="tdName"><span>22点-23点</span></td>
                <td class="tdName"><span>23点-24点</span></td>
              </tr>
              <tr>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
                <td><span>0</span></td>
              </tr>
            </tbody>
          </table>
        </el-col>
        <el-col :span="6">
          <div class="wrapOkstyle">
            <div class="wrapOkbtn"><el-button type="success">OK</el-button></div>
            <el-table
              :data="stationData"
              style="width: 100%"
              height="300"
            >
              <el-table-column
                :sortable="true"
                prop="work_number"
                width="80"
                label="工位号"
              />
              <el-table-column
                :sortable="true"
                prop="work_description"
                label="工位描述"
              />
              <el-table-column
                :sortable="true"
                label="追溯"
              >
                <template>
                  <el-button class="zhuisubtn" type="primary">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <table class="table3 workpiece">
              <tbody>
                <tr>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>工件到位</span></p>
                    </div>
                  </td>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>p 传递机型</span></p>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>允许工作</span></p>
                    </div>
                  </td>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>M 传递机型</span></p>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>申请上线</span></p>
                    </div>
                  </td>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>p 申请下线</span></p>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>允许保存</span></p>
                    </div>
                  </td>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>保存完成</span></p>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>OK放行</span></p>
                    </div>
                  </td>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>NG放行</span></p>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelinenormal" /><span>PLC心跳</span></p>
                    </div>
                  </td>
                  <td>
                    <div class="wrappstyle">
                      <p><span class="wholeline wholelineerror" /><span>设备故障</span></p>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'mesContainer',
  // 数据模型
  data() {
    return {
      tableData: [],
      stationData: [
        { work_number: 'OP010', work_description: 'xxxxxx' },
        { work_number: 'OP020', work_description: 'xxxxxx' }
      ]
    }
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
table.table3{
    text-align:center;
    border-collapse:collapse;
    width: 100%;
    margin: 5px 0;
}
.table3 tbody td{
    font-size: 14px;
    font-weight: bold;
    border: 1px solid #e4e4e4;
    height: 32px;
    background-color: #ffffff;
}
.tdName{
  width: 112px;
  white-space: nowrap;
  background-color: #e7e7e7 !important;
  color: #777777 !important;
}
.loginStyle{
  width: 150px;
  button{
    width: 162px;
    height: 40px;
    font-weight: 700;
    font-size: 16px;
    background-color: #229f99;
    border: 0;
    border-radius: 0px;
  }
  button:active{
    background-color: #0a8c86;
  }
}
.normal{
  color: #67C23A;
  font-weight: 700;
}

.showImg{
    background: #e6fbfe !important;
    color: #333333;
    width: 40%;
    height: 314px !important;
    text-align: center;
    img{
      height: 314px !important;
    }
}

.qianzhi{
  button{
  background: #3d98cf;
    color: #ffffff;
    font-size: 18px;
    width: 150px;
    height: 127px;
    border-radius: 0;
    border:0;
  }
  button:active{
      background-color: #096ca8 ;
    }

}
.wraptable{
  width: 60%;
}
.jianju{
    width: 45px;
    display: inline-block;
}
::v-deep .el-table{
      border: 1px solid #e7dfdf;
}
::v-deep .el-table th {
    font-size: 14px;
    color: #777777 !important;
    font-weight: bold;
    height: 32px;
    padding: 0;
    background-color: #e7e7e7 !important;
}
::v-deep .el-table .cell{
  text-align: center;
}
::v-deep .el-table td.el-table__cell div{
  font-size: 14px;
  font-weight: 700;
}
.wrappstyle{
  display: flex;
  justify-content: space-around;
  p{
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    span{
      margin: 0 5px;
    }
  }
}
.wholeline{
  width:20px;
  height: 20px;
  border-radius: 50%;
}
.wholelinenormal{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.wholelineerror{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #f00;
  box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.wholelinegray{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0,0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.scan{
  width: 120px;
  border: 0 !important;
  button{
    background: #3d98cf;
    color: #ffffff;
    font-size: 14px;
    height: 60px;
    width: 120px;
    border-radius: 0;
    border: 0;
    box-shadow: inset 2px 2px 2px 0px rgba(255, 255 ,255,.5), inset -7px -7px 10px 0px rgba(0 ,0 ,0,.1), 7px 7px 20px 0px rgba(0 ,0 ,0,.1), 4px 4px 5px 0px rgba(0 ,0 ,0,.1);
  }
  button:active{
    box-shadow: inset 2px 2px 2px 0px rgba(42, 170, 244, 0.8), inset -7px -7px 10px 0px rgba(0 ,0 ,0,.1), 7px 7px 20px 0px rgba(0 ,0 ,0,.1), 4px 4px 5px 0px rgba(0 ,0 ,0,.1);
  }
}
.zhuisubtn{
  background: #3d98cf;
    color: #ffffff;
    font-size: 12px;
    border-radius: 25px;
    border: 0;
    box-shadow: inset 2px 2px 2px 0px rgba(255, 255 ,255,.5), inset -7px -7px 10px 0px rgba(0 ,0 ,0,.1), 7px 7px 20px 0px rgba(0 ,0 ,0,.1), 4px 4px 5px 0px rgba(0 ,0 ,0,.1);
}
.zhuisubtn:active{
  box-shadow: inset 2px 2px 2px 0px rgba(42, 170, 244, 0.8), inset -7px -7px 10px 0px rgba(0 ,0 ,0,.1), 7px 7px 20px 0px rgba(0 ,0 ,0,.1), 4px 4px 5px 0px rgba(0 ,0 ,0,.1);
}
.wholetd{
  width: 32.8%;
}
.tableImg{
  display: flex;
}
.wrapOkbtn{
  button{
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0;
    font-size: 16px;
    font-weight: 700;
    box-shadow: inset 2px 2px 2px 0px rgba(255, 255 ,255,.5), inset -7px -7px 10px 0px rgba(0 ,0 ,0,.1), 7px 7px 20px 0px rgba(0 ,0 ,0,.1), 4px 4px 5px 0px rgba(0 ,0 ,0,.1);
  }
}
.workpiece{
  border: 1px solid #e7dfdf;
  .wrappstyle{
    justify-content: center;
    p{
      width: 112px;
      white-space: nowrap;
    }
  }
}
.workpiece tr td{
  height: 48.3px;
    border: 0;
}
.orderwd{
  border: 0 !important;
}
.riyueactive{
  color: #ffffff !important;
  background-color: #3d97ce !important;
}
.daynoborder tr td{
  border: 0;
}
.nomargin{
  margin: 0 !important;
}
.w150{
  width: 150px;
}
</style>
