import request from '@/utils/request'

// 查询服务信息
export function queryCoreServerAndCellTree(data) {
  return request({
    url: 'aisEsbWeb/SysCoreServerAndCellTreeSelect',
    method: 'post',
    data
  })
}
export function queryAllCoreServer(data) {
  return request({
    url: 'aisEsbWeb/SysCoreServerSelect',
    method: 'post',
    data
  })
}
// 查询Cell信息
export function queryAllCoreCell(data) {
  return request({
    url: 'aisEsbWeb/SysCoreCellSelect',
    method: 'post',
    data
  })
}
// 查询Hmi菜单信息
export function queryAllHmiMenu(data) {
  return request({
    url: 'aisEsbWeb/SysHmiMenuSelect',
    method: 'post',
    data
  })
}

export default { queryAllCoreServer, queryAllCoreCell, queryAllHmiMenu, queryCoreServerAndCellTree }
