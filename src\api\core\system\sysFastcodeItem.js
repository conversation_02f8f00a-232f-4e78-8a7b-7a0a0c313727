import request from '@/utils/request'

//查询快速编码子
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeSel',
    method: 'post',
    data
  })
}
//新增快速编码子
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeIns',
    method: 'post',
    data
  })
}
//修改快速编码子
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeUpd',
    method: 'post',
    data
  })
}
//删除快速编码子
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }
