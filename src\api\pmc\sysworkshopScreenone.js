import request from '@/utils/request'

// 查询安灯显示
export function getAndonReport(data) {
  return request({
    url: 'aisEsbApi/pmc/core/andon/PmcCoreAndonCardReport',
    method: 'post',
    data
  })
}
// 查询安灯事件
export function getAndonEvent(data) {
  return request({
    url: 'aisEsbApi/pmc/core/andon/PmcCoreAndonEventReport',
    method: 'post',
    data
  })
}
// 查询今日计划
export function getCrueentPlay(data) {
  return request({
    url: 'aisEsbOra/pmc/project/bq/PmcBqStationScreenOrderNum',
    method: 'post',
    data
  })
}
export default { getAndonReport, getAndonEvent, getCrueentPlay }
