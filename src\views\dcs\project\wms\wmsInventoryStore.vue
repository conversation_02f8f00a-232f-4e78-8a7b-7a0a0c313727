<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="日期:">
                <div class="block">
                  <el-date-picker
                    v-model="query.start_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-time="['00:00:00', '23:59:59']"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :picker-options="pickerOptions1"
                    :clearable="false"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="项目编号：">
                <el-input v-model="query.project_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="零件号：">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="end_date" width="160" label="入库时间" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_code" width="160" label="入库库位号" />
            <el-table-column :show-overflow-tooltip="true" prop="stock_group_code" label="库位区域" />
            <el-table-column :show-overflow-tooltip="true" prop="project_code" label="项目编号" />
            <el-table-column :show-overflow-tooltip="true" prop="material_code" label="零件号" />
            <el-table-column :show-overflow-tooltip="true" prop="serial_num" width="160" label="钢板自编号" />
            <el-table-column :show-overflow-tooltip="true" prop="kit_material_type_display" width="100" label="零件类型">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.KIT_MATERIAL_TYPE[scope.row.kit_material_type_display] || '其他' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="material_length" label="零件长" />
            <el-table-column :show-overflow-tooltip="true" prop="material_width" label="零件宽" />
            <el-table-column :show-overflow-tooltip="true" prop="material_thickness" label="零件厚" />
            <el-table-column :show-overflow-tooltip="true" prop="weight" label="重" />
            <el-table-column :show-overflow-tooltip="true" prop="total_ton" label="吨" />
            <el-table-column :show-overflow-tooltip="true" prop="piece_count" label="片数" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsInventoryStore from '@/api/dcs/project/wms/wmsInventoryStore'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_INVENTORY_STORE',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '入库统计',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'fj_task_id',
      // 排序
      sort: ['fj_task_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsInventoryStore },
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        search: false,
        refresh: false,
        add: false,
        edit: false,
        del: false,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_me_stock:add'],
        edit: ['admin', 'b_dcs_wms_map_me_stock:edit'],
        del: ['admin', 'b_dcs_wms_map_me_stock:del'],
        down: ['admin', 'b_dcs_wms_map_me_stock:down']
      },
      pickerMinDate: null,
      day31: 31 * 24 * 3600 * 1000,
      pickerOptions1: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            return (time.getTime() > (this.pickerMinDate + this.day31)) || (time.getTime() < (this.pickerMinDate - this.day31))
          }
          return false
        }
      }
    }
  },
  dicts: ['KIT_MATERIAL_TYPE'],
  computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime =
          start.getFullYear() +
          '-' +
          (start.getMonth() + 1 >= 10
            ? start.getMonth() + 1
            : '0' + (start.getMonth() + 1)) +
          '-' +
          (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) +
          ' 00:00:00'
      const defalutEndTime =
          end.getFullYear() +
          '-' +
          (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : '0' + (end.getMonth() + 1)) +
          '-' +
          (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) +
          ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  created() {
    this.crud.query.start_date = this.timeDefault
    this.crud.toQuery()
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  methods: {
  }
}
</script>
