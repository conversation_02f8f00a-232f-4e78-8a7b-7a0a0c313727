import request from '@/utils/request'

// 查询订单信息
export function sel(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoSelSH',
        method: 'post',
        data
    })
}
// 新增订单信息
export function add(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoInsSH',
        method: 'post',
        data
    })
}
// 修改订单信息
export function edit(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoUpdSH',
        method: 'post',
        data
    })
}
// 修改订单信息--修改有效标识
export function editEnableFlag(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoEnableFlagUpd',
        method: 'post',
        data
    })
}
// 修改订单信息--修改订单状态
export function editMoStatus(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoStatusUpd',
        method: 'post',
        data
    })
}
// 删除订单信息
export function del(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoDel',
        method: 'post',
        data
    })
}
// 查询订单配方信息
export function selRecipe(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoRecipeSel',
        method: 'post',
        data
    })
}
// 订单发布
export function moRelease(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesApsPlanMoRelease',
        method: 'post',
        data
    })
}

export default { sel, add, edit, del, editEnableFlag, selRecipe, moRelease, editMoStatus }