<template>
  <codeEditor ref="codeEditor" :code_content="code_content" :readonly="readonly" @change="handleCodeChange" />
</template>

<script>
import codeEditor from '@/components/CodeEditor/CodeEditor'
import { sel as selM } from '@/api/core/flow/rcsFlowModFunctionM'
import { sel, add, edit } from '@/api/core/flow/rcsFlowModFunctionI'
import Cookies from 'js-cookie'
import Vue from 'vue'
const defaultForm = {
  user_name: Cookies.get('userName'),
  function_i_id: 0,
  function_m_code: '',
  function_i_code: '',
  function_i_name: '',
  function_i_des: '',
  function_i_content: '',
  function_i_status: ''
}
export default {
  name: 'RCS_FLOW_MOD_FUNCTIONI',
  components: { codeEditor },
  props: {
    step_mod_id: {
      type: [String, Number],
      default: -1
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  // 数据模型
  data() {
    return {
      form: {},
      code_content: ''
    }
  },
  watch: {
    step_mod_id: {
      immediate: true,
      deep: true,
      handler() {
        this.code_content = ''
        // const query = {
        //   step_mod_id: this.step_mod_id
        // }
        // sel(query)
        //   .then(res => {
        //     const defaultQuery = JSON.parse(JSON.stringify(res))
        //     if (defaultQuery.code === 0) {
        //       if (defaultQuery.data.length > 0) {
        //         this.code_content = defaultQuery.data[0].function_i_content
        //       }
        //     }
        //   })
        //   .catch(() => {
        //     this.$message({
        //       message: '查询异常',
        //       type: 'error'
        //     })
        //   })
      }
    }
  },

  mounted: function() {},
  created: function() {},
  methods: {
    handleCodeChange(content) {}
  }
}
</script>
