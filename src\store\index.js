// import { createStore } from "vuex";

// export default createStore({
//  state: {},
//  mutations: {},
//  actions: {},
//  modules: {}
// });

import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'

// import designer from './modules/designer'
// import release from './modules/release'

// require.context是webpack中，用来创建自己的（模块）上下文
// 要搜索的文件夹目录
// 是否还应该搜索它的子目录
// 以及一个匹配文件的正则表达式

// https://blog.csdn.net/weixin_43582611/article/details/103457389 vue中filter()和map()和reduce()高阶函数使用
// reduce() 对数组的结果进行汇总

// https://blog.csdn.net/zeternityyt/article/details/80041526 vue.js的状态管理vuex中store的使用
// Vuex.Store：是专为vue.js应用程序开发的状态管理模式
// 状态管理有5个核心，分别是state、getter、mutation、action以及module

Vue.use(Vuex)

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)

// 您不需要“import app from”。/modules/app'`
// 它将自动要求模块文件中的所有vuex模块
// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // console.log("【store.index.modules】")

  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = new Vuex.Store({
  modules,
  getters
  // designer: designer,
  // release: release
})

export default store
