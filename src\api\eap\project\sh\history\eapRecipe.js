import request from '@/utils/request'

// 查询配方维护信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/history/EapRecipeSel',
    method: 'post',
    data
  })
}
// 新增配方维护信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/history/EapRecipeIns',
    method: 'post',
    data
  })
}
// 修改配方维护信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/history/EapRecipeUpd',
    method: 'post',
    data
  })
}
// 删除配方维护信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/history/EapRecipeDel',
    method: 'post',
    data
  })
}
// 设备自检
export function equipStatusReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/history/equipStatusReport',
    method: 'post',
    data
  })
}
// 设备自检浮窗数据
export function selectEquipStatusInfo(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sh/recipe/history/selectEquipStatusInfo',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, equipStatusReport, selectEquipStatusInfo }

