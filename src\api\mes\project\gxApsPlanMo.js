import request from '@/utils/request'

// 查询订单信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesApsPlanMoSel',
    method: 'post',
    data
  })
}
// 新增订单信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxApsPlanMoIns',
    method: 'post',
    data
  })
}
// 修改订单信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxApsPlanMoUpd',
    method: 'post',
    data
  })
}
// 修改订单信息--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesApsPlanMoEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除订单信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesApsPlanMoDel',
    method: 'post',
    data
  })
}
// 查询订单配方信息
export function selRecipe(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesApsPlanMoRecipeSel',
    method: 'post',
    data
  })
}
// 订单发布
export function moRelease(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesApsPlanMoRelease',
    method: 'post',
    data
  })
}
// 同步AI工控
export function uploadMes(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesApsPlanMoUploadMes',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, editEnableFlag, selRecipe, moRelease,uploadMes }

