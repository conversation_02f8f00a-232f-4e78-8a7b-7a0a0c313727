<template>
  <el-row :gutter="20" class="el-row">
    <el-col :span="24">
      <!--逻辑属性组-->
      <el-drawer append-to-body :wrapper-closable="false" :title="dialogTitleFrom" :visible.sync="dialogVisbleSyncFrom" size="450px" @closed="drawerClose">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <!--快速编码：TERMINAL_TYPE-->
          <el-form-item :label="$t('lang_pack.logicattr.menuType')" prop="menu_type_code">
            <!-- 菜单类型 -->
            <el-select v-model="form.menu_type_code" clearable filterable placeholder="请输入驱动编号/名称">
              <el-option v-for="item in dict.TERMINAL_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.groupCode')" prop="logic_attr_group_code">
            <!-- 组编码 -->
            <el-input v-model="form.logic_attr_group_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.logicattr.groupDescription')" prop="logic_attr_group_des">
            <!-- 组描述 -->
            <el-input v-model="form.logic_attr_group_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center;">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <!--表格渲染-->
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" size="small" :data="tableDataTable" style="width: 100%" :height="height" highlight-current-row @sort-change="sortChage">
        <el-table-column  :show-overflow-tooltip="true" prop="menu_type_code" :label="$t('lang_pack.logicattr.menuType')" sortable="custom">
          <!-- 菜单类型 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.TERMINAL_TYPE[scope.row.menu_type_code] }}
          </template>
        </el-table-column>

        <el-table-column  :show-overflow-tooltip="true" prop="logic_attr_group_code" :label="$t('lang_pack.logicattr.groupCode')" sortable="custom" />
        <!-- 组编码 -->
        <el-table-column  :show-overflow-tooltip="true" prop="logic_attr_group_des" :label="$t('lang_pack.logicattr.groupDescription')" sortable="custom" />
        <!-- 组描述 -->

        <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100" sortable="custom">
          <!-- 有效标识 -->
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
          </template>
        </el-table-column>

        <!-- Table单条操作-->
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
          <!-- 操作 -->
          <template slot-scope="scope">
            <el-link class="linkItem" type="primary" @click="toTableButEdit(scope.row)">编辑</el-link>
            <el-link class="linkItem" type="primary" @click="toTableButDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>

      <!--分页组件-->
      <el-pagination
        :page-size.sync="pageTable.size"
        :total="pageTable.total"
        :current-page.sync="pageTable.page"
        :page-sizes="[20, 30, 40, 50, 100]"
        style="margin-top: 8px;float:right;"
        layout="total, prev, pager, next, sizes"
        @size-change="toSizeTableChangeHandler($event)"
        @current-change="toPageTableChangeHandler"
      />
    </el-col>
  </el-row>
</template>

<script>
import Cookies from 'js-cookie'
import { selLogicAttrGroup, insLogicAttrGroup, updLogicAttrGroup, delLogicAttrGroup } from '@/api/core/logic/logicAttrGroup'

export default {
  name: 'ATTRGROUP',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      // 查询条件
      query: {
        logicAttrGroupCodeDes: '',
        enable_flag: '',
        tableOrder: 'asc', // 排序方式asc,desc
        tableOrderField: 'logic_attr_group_id', // 排序字段
        tablePage: 1, // 当前页
        tableSize: 20 // 每页数据条数
      },
      // 删除按钮(是否加载)
      delLoadingBut: false,

      // FROM:弹窗(新增、修改明细)
      dialogVisbleSyncFrom: false,
      dialogTitleFrom: '',

      // 新增&修改(初始化数据)
      form: {
        logic_attr_group_id: '',
        menu_type_code: '',
        logic_attr_group_code: '',
        logic_attr_group_des: '',
        enable_flag: 'Y'
      },
      rules: {
        // 提交验证规则
        logic_attr_group_code: [{ required: true, message: '请输入逻辑属性组编码', trigger: 'blur' }]
      },

      // Table
      listLoadingTable: false,
      popTableBut: false,

      // 动态数据(如果写入值，就会是固定的值)
      tableDataTable: [],
      selectedTable: [], // 已选择项

      pageTable: {
        // 页码
        page: 0,
        // 每页数据条数
        size: 20,
        // 总数据条数
        total: 0
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'TERMINAL_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // 查询
    this.toButQuery()
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    // 按钮
    toButQuery(queryContent) {
      // 查询
      if (queryContent !== undefined) {
        this.query.logicAttrGroupCodeDes = queryContent
      }
      this.listLoadingTable = true
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName'),
        logicAttrGroupCodeDes: this.query.logicAttrGroupCodeDes,
        enable_flag: this.query.enable_flag,
        tableOrder: this.query.tableOrder, // 排序方式asc,desc
        tableOrderField: this.query.tableOrderField, // 排序字段
        tablePage: this.query.tablePage, // 当前页
        tableSize: this.query.tableSize // 每页数据条数
      }
      selLogicAttrGroup(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
            this.pageTable.total = defaultQuery.count
            this.listLoadingTable = false
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toButAdd() {
      // 新增
      this.form.logic_attr_group_id = ''
      this.form.menu_type_code = ''
      this.form.logic_attr_group_code = ''
      this.form.logic_attr_group_des = ''
      this.form.enable_flag = 'Y'
      this.dialogVisbleSyncFrom = true // 新增弹出框
      this.dialogTitleFrom = '新增逻辑属性组'
    },
    toButEdit() {
      // 编辑(Table 编辑)
      this.form.logic_attr_group_id = this.selectedTable[0].logic_attr_group_id
      this.form.menu_type_code = this.selectedTable[0].menu_type_code
      this.form.logic_attr_group_code = this.selectedTable[0].logic_attr_group_code
      this.form.logic_attr_group_des = this.selectedTable[0].logic_attr_group_des
      this.form.enable_flag = this.selectedTable[0].enable_flag
      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改逻辑属性组'
    },

    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 格式化查询条件
          const save = {
            user_name: Cookies.get('userName'),
            logic_attr_group_id: this.form.logic_attr_group_id,
            menu_type_code: this.form.menu_type_code,
            logic_attr_group_code: this.form.logic_attr_group_code,
            logic_attr_group_des: this.form.logic_attr_group_des,
            enable_flag: this.form.enable_flag
          }

          const that = this
          // 新增
          if (this.form.logic_attr_group_id === undefined || this.form.logic_attr_group_id.length <= 0) {
            insLogicAttrGroup(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  that.$emit('AddTreeNode', defaultQuery.result, this.form.logic_attr_group_code, this.form.logic_attr_group_des)
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '新增异常',
                  type: 'error'
                })
              })
          } else {
            // 修改
            updLogicAttrGroup(save)
              .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                  this.dialogVisbleSyncFrom = false // 弹出框隐藏
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  that.$emit('EditTreeNode', this.form.logic_attr_group_id, this.form.logic_attr_group_code, this.form.logic_attr_group_des)
                  // 查询
                  this.toButQuery()
                } else {
                  this.$message({
                    message: defaultQuery.msg,
                    type: 'info'
                  })
                }
              })
              .catch(() => {
                this.$message({
                  message: '修改异常',
                  type: 'error'
                })
              })
          }
        }
      })
    },

    toTableButEdit(data) {
      // Table编辑(单笔)
      this.form.logic_attr_group_id = data.logic_attr_group_id
      this.form.menu_type_code = data.menu_type_code
      this.form.logic_attr_group_code = data.logic_attr_group_code
      this.form.logic_attr_group_des = data.logic_attr_group_des
      this.form.enable_flag = data.enable_flag
      this.dialogVisbleSyncFrom = true // 修改弹出框
      this.dialogTitleFrom = '修改逻辑属性组'
    },
    toTableButDelete(data) {
      // Table删除(单笔)
      this.$confirm(`此操作将永久删除逻辑属性组${data.logic_attr_group_des}是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 格式化删除条件
          const del = {
            user_name: Cookies.get('userName'),
            logic_attr_group_id: data.logic_attr_group_id
          }
          const that = this
          delLogicAttrGroup(del)
            .then(res => {
              const defaultDel = JSON.parse(JSON.stringify(res))
              if (defaultDel.code === 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
                that.$emit('DelTreeNode', data.logic_attr_group_id)
                // 查询
                this.toButQuery()
              } else {
                this.$message({
                  message: defaultDel.msg,
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '删除异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },

    // Page:分页
    toSizeTableChangeHandler(val) {
      // 页大小改变
      this.pageTable.size = val
      // 查询
      this.toButQuery()
    },
    toPageTableChangeHandler(val) {
      // 页数改变
      this.pageTable.page = val
      // 查询
      this.toButQuery()
    },
    sortChage(obj) {
      if (obj.order == null) {
        this.query.tableOrder = 'asc' // asc,desc
        this.query.tableOrderField = 'logic_attr_group_id'
      } else {
        this.query.tableOrder = obj.order === 'ascending' ? 'asc' : 'desc' // asc,desc
        this.query.tableOrderField = obj.prop
      }
      // 查询
      this.toButQuery()
    }
  }
}
</script>

<style lang="scss">
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-drawer {
  overflow-y: scroll;
}
</style>
