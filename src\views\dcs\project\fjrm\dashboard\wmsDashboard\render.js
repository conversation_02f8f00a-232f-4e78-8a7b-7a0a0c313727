
import { getIconPathData } from '@/components/wmsDashboard/icon'
function render(g, node, isSelected, that, data) {
  const body = g.append('rect').attr('class', 'body').attr('rx', 2)
  if (typeof (data) !== 'string') {
    body
      .style('width', '68px')
      .style('stroke-width', '1px')
      .attr('x', (data.i * 78))
      .attr('y', data.item.type === '1' ? ((data.k * 20) + (data.node.y - 30)) : (data.k * 20) + data.node.y)
      .style('height', '20px')
      .attr('stroke', '#000000')
      .style('fill', node.value ? node.backGround : '#ffffff')
    g.append('text')
      .attr('x', (data.i * 78))
      .attr('y', data.item.type === '1' ? ((data.k * 20) + (data.node.y - 15)) : (data.k * 20) + data.node.y + 15)
      .attr('class', 'unselectable')
      .attr('fill', '#ffffff')
      .text(() => node.value)
      .style('font-size', '12px')
      .style('font-weight', 'bold')
  }
  if (data === 'bigCar') {
    const iconInfo = getIconPathData().filter((item) => item.id === 'bigCar')[0]
    g.append('image')
      .attr('x', node.x)
      .attr('preserveAspectRatio', 'none')
      .attr('y', node.y)
      .style('height', node.height + 'px')
      .style('width', node.width + 'px')
      .attr('href', iconInfo.path)
  } else if (data === 'smallCar') {
    const iconInfo = getIconPathData().filter((item) => item.id === 'smallCar')[0]
    g.append('image')
      .attr('x', node.x)
      .attr('y', node.y)
      .style('height', node.height + 'px')
      .style('width', node.width + 'px')
      .attr('href', iconInfo.path)
  }
  //     .style('filter', 'drop-shadow(2px 2px 3px rgba(0, 0, 0, 0.5))')
  // if (node.type === 'kw') {
  //   const body = g.append('rect').attr('class', 'body').attr('rx', 2)
  //   body
  //     .style('width', node.width + 'px')
  //     .style('stroke-width', '0px')
  //     .attr('x', node.x - (node.width / 2))
  //     .attr('y', node.y - (node.height / 2))
  //   // .style("fill", node.color)
  //     .style('height', node.height + 'px')
  //     .attr('stroke', node.borderColor)
  //   const iconInfo = getIconPathData().filter((item) => item.id === 'kuwei')[0]
  //   g.append('image')
  //     .attr('x', node.x - (node.width / 2))
  //     .attr('preserveAspectRatio', 'none')
  //     .attr('y', node.y - (node.height / 2))
  //     .style('height', node.height + 'px')
  //     .style('width', node.width + 'px')
  //     .attr('href', iconInfo.path)
  //   if (node.width >= 98 && node.height >= 16) {
  //     g.append('text')
  //       .attr('x', node.x - (node.width / 2) + 5)
  //       .attr('y', node.y + (node.height / 4))
  //       .attr('class', 'unselectable')
  //       .attr('fill', '#FFFFFF')
  //       .text(() => node.stock_code)
  //       .style('font-size', '12px')
  //       .style('font-weight', 'bold')
  //   }
  // } else if (node.type === 'rgkw') {
  //   const body = g.append('rect').attr('class', 'body').attr('rx', 2)
  //   body
  //     .style('width', node.width + 'px')
  //     .style('stroke-width', node.strokeWidth + 'px')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //     .style('height', node.height + 'px')
  //     .attr('stroke', node.borderColor)
  //   const iconInfo = getIconPathData().filter((item) => item.id === 'kuwei')[0]
  //   g.append('image')
  //     .attr('x', node.x + 2)
  //     .attr('preserveAspectRatio', 'none')
  //     .attr('y', node.y + 2)
  //     .style('height', node.height - 4 + 'px')
  //     .style('width', node.width - 4 + 'px')
  //     .attr('href', iconInfo.path)
  // } else if (node.type === 'rkgd') {
  //   const body = g.append('rect').attr('class', 'body').attr('rx', 2)
  //   body
  //     .style('width', node.width + 'px')
  //     .style('stroke-width', node.strokeWidth + 'px')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //     .style('fill', '#7F756B')
  //     .style('height', node.height + 'px')
  //     .attr('stroke', node.borderColor)
  //   const iconInfo = getIconPathData().filter((item) => item.id === 'gundao')[0]
  //   g.append('image')
  //     .attr('x', node.x)
  //     .attr('preserveAspectRatio', 'none')
  //     .attr('y', node.y - 15)
  //     .style('height', node.height + 30 + 'px')
  //     .style('width', node.width + 'px')
  //     .attr('href', iconInfo.path)
  // } else if (node.type === 'bigCar') {
  //   const iconInfo = getIconPathData().filter((item) => item.id === 'bigCar')[0]
  //   g.append('image')
  //     .attr('x', node.x)
  //     .attr('preserveAspectRatio', 'none')
  //     .attr('y', node.y)
  //     .style('height', node.height + 'px')
  //     .style('width', node.width + 'px')
  //     .attr('href', iconInfo.path)
  // } else if (node.type === 'smallCar') {
  //   const iconInfo = getIconPathData().filter((item) => item.id === 'smallCar')[0]
  //   g.append('image')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //     .style('height', node.height + 'px')
  //     .style('width', node.width + 'px')
  //     .attr('href', iconInfo.path)
  // } else if (node.type === 'rxd') {
  //   const body = g.append('rect').attr('class', 'rxhd').attr('rx', 2)
  //   body
  //     .style('width', node.width + 'px')
  //     .style('stroke-width', '2px')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //   // .style("fill", node.color)
  //     .style('height', node.height + 'px')
  //     .attr('stroke', '#3b3128')
  //     .style('fill', node.borderColor)
  //     .style('filter', 'drop-shadow(2px 2px 3px rgba(0, 0, 0, 0.5))')
  // } else if (node.type === 'A' || node.type === 'B') {
  //   const body = g.append('rect').attr('class', 'rxhd').attr('rx', 2)
  //   body
  //     .style('width', node.width + 'px')
  //     .style('stroke-width', '5px')
  //     .attr('x', node.x)
  //     .attr('y', node.y)
  //     .style('height', node.height + 'px')
  //     .attr('stroke', '#3b3128')
  //     .style('fill', 'transparent')
  //     .style('filter', 'drop-shadow(2px 2px 3px rgba(0, 0, 0, 0.5))')
  //   const text = g.append('text')
  //     .attr('x', node.x + node.width / 2) // 中心位置的 x 坐标
  //     .attr('y', node.y + node.height / 2) // 中心位置的 y 坐标
  //     .attr('text-anchor', 'middle') // 文本居中对齐
  //     .attr('dominant-baseline', 'middle') // 垂直居中
  //     .style('font-size', '30px') // 设置字体大小
  //     .style('fill', node.color) // 文本颜色
  //   text.text(node.describe) // 设置文本内容
  //   g.lower()
  // }
  if (node.type === 'kw') {
    g.append('title')
      .text(() => `板长:${node.m_length ? node.m_length : '暂无数据'}\n板宽:${node.m_width ? node.m_width : '暂无数据'}\n板厚:${node.m_thickness ? node.m_thickness : '暂无数据'}\n零件编号:${node.material_code ? node.material_code : '暂无数据'}\n项目编码:${node.lot_num ? node.lot_num : '暂无数据'}`)
    g.on('click', () => {
      that.open(node)
    })
  }
}
export default render
