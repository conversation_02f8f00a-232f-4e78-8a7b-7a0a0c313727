<template>
  <div>
    <!-- <div v-if="greenbgShow" class="greenStyle" :style="'background-image:url(' + greenbg + ');'">
            <div class="drop drop1">
                <h1 style="text-shadow: 0 5px rgb(0 0 0 / 30%);">{{ $t('lang_pack.header.greenNewEnergy') }}</h1>
                <div class="drop drop2" />
                <div class="drop drop3" />
                <div class="drop drop4" />
            </div>
      </div> -->
    <!-- <elFrame ref="fIframe" v-if="elementFlag" :name="pathData.name" :src="pathData.function_path + '?prod_line_id=' + currentStation.prod_line_id + '&station_id=' + currentStation.station_id + '&station_code=' + currentStation.station_code +'&cell_id='+currentStation.cell_id"  /> -->
    <FloatingButton :current-station="currentStation" :style-attr="styleAttr" :current-menu="currentMenu" @ok="handleMenu" @attrKey="handleAttr" />
  </div>
</template>
<script>
import FloatingButton from '@/components/FloatingButton/index'
import Cookies from 'js-cookie'
export default {
  name: 'HMI_MAIN',
  components: { FloatingButton },
  data() {
    return {
      elementFlag: false,
      currentMenu: [],
      attrKey: '',
      styleAttr: ''
    }
  },
  mounted() {
  },
  created() {
    window.addEventListener('message', this.sendMessage, false)
  },
  destroyed() {
    window.removeEventListener('message', this.sendMessage)
  },
  methods: {
    handleMenu(json) {
      this.elementFlag = false
      this.$nextTick(() => {
        this.greenbgShow = false
        this.pathData.name = json.menu_item_des
        this.pathData.function_path = json.function_path
        this.elementFlag = true
      })
    },
    handleAttr(item) {
      const menuData = ['open'] // 这里的值代表直接在当前页面调用，不用传给iframe里面去调用
      if (menuData.includes(item.key)) {
        this[item.key]()
        return
      }
      this.$refs.fIframe.$refs.iframeId.contentWindow.postMessage(item, '*')
    },
    // 处理接收的消息
    sendMessage(event) {
      const data = event.data
      if (typeof data !== 'string') {
        return
      }
      this.styleAttr = data
    },
    open() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.logout()
      })
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.greenStyle{
  background-repeat: no-repeat;
  background-size: cover;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.drop {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    border: 1px solid rgba(255,255,255,0.2);
    position: absolute;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: block;
    margin: 0 auto;
    h1 {
        text-align: center;
        font-family: Arial;
        color: #FFF;
        font-size: 90px;
        padding: 20px 30px;
        text-transform: uppercase;
    }
}
.drop1 {
    width: 47%;
    height: 150px;
    top: 56px;
    left: 0;
    right: 0;
    z-index: 2;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}
.drop2 {
    width: 40px;
    height: 40px;
    top: -60px;
    left: -80%;
    right: 0;
    z-index: 4;
}
.drop3 {
    width: 60px;
    height: 60px;
    bottom: -80px;
    right: 30px;
    z-index: 3;
}
.drop4 {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    top: -55px;
    right: 20px;
}
body{
  padding: 0 !important;
}
</style>
