import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModFunctionISel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModFunctionIIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModFunctionIUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModFunctionIDel',
    method: 'post',
    data
  })
}
// 另存为模板
export function saveAs(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModFunctionISaveAs',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, saveAs }

