export const view = {
  title: {
    boardPractice: 'Board Practice',
    cimMessageReport: 'CIM Message Report',
    interfaceLog: 'Interface Log',
    threeRateReport: 'Three Rate Report',
    communicationDiagnosis: 'Communication Diagnosis',
    pointChangeTracking: 'Point Change Tracking',
    pointWriteTracking: 'Point Write Tracking',
    setting: 'Setting',
    dcsAlarm: 'Alarm',
    parkSortRule: 'Sorting Rule Setting',
    parkSortSplitRule: 'Sorting Extraction Comparison Rule Setting',
    parkXNPSort: 'X Number/Position Sorting Mode Record',
    parkExtra: 'Extra Parameter Settings'
  },
  button: {
    export: 'Export',
    search: 'Search',
    reset: 'Reset',
    view: 'View',
    close: 'Close',
    confirm: 'Confirm',
    cancel: 'Cancel',
    disable: 'Disable',
    unbind: 'Unbind',
    setting: 'Setting',
    addPosition: 'Add Position'
  },
  enum: {
    placeholder: {
      pleaseChosen: 'Please select',
      pleaseEnter: 'Please Enter'
    },
    boardErrorCode: {
      normal: 'Normal',
      mixedBoard: 'Mixed Board',
      readFailureBoard: 'Read Failure Board',
      duplicateCode: 'Duplicate Code',
      forcedPass: 'Forced Pass',
      forcedFinal: 'Forced Final',
      otherDefined: 'Other Defined'
    },
    manualProcessingBoard: {
      manualInputMode: 'Manual Input Mode',
      reread: 'Reread',
      forcedPass: 'Forced Pass'
    },
    communication: {
      normal: 'Communication Normal',
      abnormal: 'Communication Abnormal'
    },
    writeStatus: {
      normal: 'Write Normal',
      abnormal: 'Write Exception'
    }
  },
  field: {
    plan: {
      lotNum: 'Lot Num',
      lotNo: 'Lot No',
      plantOrderNum: 'Plant Order Num',
      tailCount: 'Tail Count'
    },
    jobOrder: {
      batchNo: 'Lot No.(TVB)',
      laserBatchNo: 'Lot No.(CARD)',
      originalLotNo: 'originalLot',
      certifyLotNo: 'certifyLot',
      typesettingNumber: 'QTY pcs',
      customerMaterialNo: 'Customer P/N',
      lot: 'Lot',
      model: 'Model',
      ul: 'UL mark',
      bdBarcodes: 'PCS Barcode Set',
      xoutPositions: 'XOUT Position Set',
      bdLevels: 'PCS Level Set',
      bdLevelJudgments: 'PCS Level Judgement Set',
      shipAddress: 'shipAddress'
    },
    setRecord: {
      created_time: 'Creation Time',
      lot_num: 'Order Number',
      array_index: 'Order SET Sequence',
      board_sn: 'Line Scan Serial Number',
      pile_barcode: 'Packaging Barcode',
      array_barcode: 'SET Barcode',
      array_status: 'SET Status',
      array_ng_code: 'SET Sorting NG Code',
      array_ng_msg: 'SET Sorting NG Description',
      array_level: 'Line Scan SET Level',
      array_mark: 'Line Scan Optical Inspection Result',
      array_bd_count: 'Number of PCS under Line Scan SET',
      board_result: 'Board Judgement Result',
      board_turn: 'Rotation Direction',
      deposit_position: 'Stacking Position',
      xout_flag: 'Is XOUT Sorting',
      xout_set_num: 'XOUT Set Quantity',
      xout_act_num: 'XOUT Actual Quantity',
      array_front_info: 'SET Front Line Scan Data',
      array_back_info: 'SET Back Line Scan Data',
      task_type: 'Task Type',
      model_type: 'Model Number (Model)',
      model_version: 'Model Number (Model) Version',
      array_type: 'SET Type',
      bd_type: 'PCS Type',
      m_length: 'Length, in millimeters',
      m_width: 'Width, in millimeters',
      m_tickness: 'Thickness, in millimeters',
      m_weight: 'Weight, in grams',
      batch_no: 'Batch Number',
      cycle_period: 'Cycle Period',
      laser_batch_no: 'Laser Batch Number',
      typesetting_no: 'Typesetting Number',
      bd_barcodes: 'PCS Barcode Set',
      xout_positions: 'XOUT Position Set',
      bd_levels: 'PCS Level Set',
      bd_level_judgments: 'PCS Level Judgement Set',
      customer_mn: 'Customer Material Number',
      ul_code: 'UL Code',
      pile_use_flag: 'Is Packaged for Use',
      enable_flag: 'Valid Flag',
      unbind_flag: 'Is Unbound',
      unbind_time: 'Unbinding Time',
      unbind_way: 'Unbinding Method Description',
      part_num: 'materialNum',
      lot: 'lot',
      DC: 'Dc',
      cus_2D1: 'cusQRC1',
      cus_2D2: 'cusQRC2',
      inner_2D: 'SETnum',
      xout_mapping_result: 'Mapping'
    },
    pdSetRecord: {
      created_time: 'Creation Time',
      lot_num: 'Order Number',
      array_index: 'Order SET Sequence',
      board_sn: 'Line Scan Serial Number',
      pile_barcode: 'Packaging Barcode',
      array_barcode: 'Inner Code',
      array_status: 'SET Status',
      array_ng_code: 'SET Sorting NG Code',
      array_ng_msg: 'SET Sorting NG Description',
      array_level: 'Line Scan SET Level',
      array_mark: 'Line Scan Optical Inspection Result',
      array_bd_count: 'Number of PCS under Line Scan SET',
      board_result: 'Board Judgement Result',
      board_turn: 'Rotation Direction',
      deposit_position: 'Stacking Position',
      xout_flag: 'Is XOUT Sorting',
      xout_set_num: 'XOUT Set Quantity',
      xout_act_num: 'XOUT Actual Quantity',
      array_front_info: 'SET Front Line Scan Data',
      array_back_info: 'SET Back Line Scan Data',
      task_type: 'Task Type',
      model_type: 'Model Number (Model)',
      model_version: 'Model Number (Model) Version',
      array_type: 'SET Type',
      bd_type: 'PCS Type',
      m_length: 'Length, in millimeters',
      m_width: 'Width, in millimeters',
      m_tickness: 'Thickness, in millimeters',
      m_weight: 'Weight, in grams',
      batch_no: 'Batch Number',
      cycle_period: 'Cycle Period',
      laser_batch_no: 'Laser Batch Number',
      typesetting_no: 'Typesetting Number',
      bd_barcodes: 'PCS Barcode Set',
      xout_positions: 'XOUT Position Set',
      bd_levels: 'PCS Level Set',
      bd_level_judgments: 'PCS Level Judgement Set',
      customer_mn: 'Customer Material Number',
      ul_code: 'UL Code',
      pile_use_flag: 'Is Packaged for Use',
      enable_flag: 'Valid Flag',
      unbind_flag: 'Is Unbound',
      unbind_time: 'Unbinding Time',
      unbind_way: 'Unbinding Method Description',
      part_num: 'materialNum',
      lot: 'lot',
      DC: 'Dc',
      cus_2D1: 'cusQRC1',
      cus_2D2: 'cusQRC2',
      inner_2D: 'SETnum',
      xout_mapping_result: 'Mapping'
    },
    pileRecord: {
      intef_return_code: 'Interface Return Code',
      intef_return_msg: 'Interface Return Message',
      apply_return_code: 'Application Return Code',
      apply_return_msg: 'Application Return Message',
      print_return_code: 'Print Return Code',
      print_return_msg: 'Print Return Message',
      inspect_result_code: 'Inspection Result Code',
      inspect_result_msg: 'Inspection Result Message'
    },
    splitRules: {
      order_lot: 'Order Split Batch Rule',
      custom_model: 'Custom Split Model Rule',
      array_set: 'WS NUM',
      array_inner: 'WS NUM',
      array_lot: 'Lot Num on QR code/frame',
      array_innerLot: 'Lot Num on QR code/frame',
      array_model: 'SET Split Model Rule',
      array_cycle: '2D date code on frame',
      array_innerCycle: '2D date code on frame',
      array_dc: ' Date code:',
      array_ln: ' Lot Num:',
      array_innerDc: ' Date code:',
      array_innerIn: 'Lot num on card by Marking',
      bd_set: 'WS Num on 2D code',
      bd_lot: 'Lot Num on QR code/pcs',
      bd_model: 'PCS Split Model Rule',
      bd_cycle: ' Date code on 2D code/ pcs',
      bd_inner: 'WS Num on 2D code',
      bd_innerCycle: ' Date code on 2D code/ pcs',
      bd_dc: 'Date code marking on pcs',
      bd_ln: 'Lot num by laser',
      bd_index: 'Ordinal num on 2D code'
    },
    recipe: {
      weight_error: 'Weight Error(g)',
      inkjet_tpl: 'Inkjet Template'
    },
    stationFlow: {
      station_code: 'Station Code',
      station_desc: 'Station Description',
      serial_num: 'Tag ID',
      recipe_paras: 'Recipe Parameters',
      arrive_date: 'Arrival Time',
      leave_date: 'Departure Time',
      cost_time: 'Time Consuming(ms)'
    },
    stationQuality: {
      station_flow_id: 'Flow ID',
      station_code: 'Station Code',
      station_desc: 'Station Description',
      serial_num: 'Tag ID',
      quality_for: 'Quality Source',
      tag_code: 'Tag Code',
      tag_value: 'Tag Value',
      quality_d_sign: 'Quality Sign',
      trace_d_time: 'Trace Time'
    },
    ccdValidationResult: {
      station_code: 'Station Code',
      station_desc: 'Station Description',
      serial_num: 'Tag ID',
      validate_result: 'Validation Result',
      validate_msg: 'Validation Message',
      validate_time: 'Validation Time'
    },
    workpiece: {
      prod_line_code: 'Production Line',
      serial_num: 'Assembly Number',
      quality_sign: 'Quality Mark',
      make_order: 'Order Number',
      records: 'Station Records-list',
      arrive_date: 'Arrival Time-datetime',
      leave_date: 'Departure Time-datetime',
      cost_time: 'Time Cost(ms)'
    },
    mappingResultRecord: {
      enable_flag: 'Valid Flag',
      item_date_val: 'Download Time',
      upload_date_val: 'Upload Time',
      product_no: 'Product Number',
      product_rev: 'Product Version',
      lot_no: 'Lot Number',
      barcode: 'Internal Code',
      bad_mark: 'Mapping Result',
      data_from: 'Data Source',
      process_no: 'Process Code',
      twodlot_no: '2D Mapping Lot Number',
      parentlot_no: 'Original Parent Lot',
      reworknum: 'Rework Count',
      angle: 'Rotation Angle',
      flip_type: 'X,Y (Flip)',
      stripeachpiecex: 'Each Piece (Strip) X',
      stripeachpiecey: 'Each Piece (Strip) Y',
      err_type: 'Bad Data Result'
    },
    pile: {
      item_date_val: 'Time',
      lot_num: 'Order Number',
      finish_lable_count: 'Packaging Order',
      pile_barcode: 'Packaging Barcode',
      custom_barcode: 'EAP Packaging Barcode',
      pile_index: 'Packaging Sequence in Order',
      array_count: 'Total Number of SETs in Packaging',
      pile_status: 'Packaging Status',
      pile_ng_code: 'Packaging Validation NG Code',
      pile_ng_msg: 'Packaging Validation NG Description',
      pc_array_list: 'PC Plan SET Collection',
      plc_array_list: 'PLC Feedback SET Collection',
      trunk_flag: 'Is Tail Box',
      pile_weight: 'Packaging Weight',
      pile_user: 'Packaging Person',
      enable_flag: 'Is Valid',
      unbind_flag: 'Is Unbound',
      unbind_user: 'Unbinding Person',
      unbind_time: 'Unbinding Time'
    }
  },
  form: {
    batchNo: 'Batch NO',
    cardWorkOrder: 'card Work Order',
    subBatchNo: 'Sub Batch NO',
    subSimplifiedCode: 'Sub Simplified Code',
    materialNo: 'Material No',
    carrierBarcode: 'Carrier Barcode',
    taskSource: 'Task Source',
    station: 'Station',
    portNo: 'Port',
    boardBarcode: 'Borad Barcode',
    boardStatus: 'Board Status',
    boardErrorCode: 'Board Error Code',
    isFirstCheck: 'Is First Check',
    isDummy: 'Is Dummy',
    manualProcessingBoard: 'Manual Work Board',
    hasPanelMode: 'Panel Mode',
    operator: 'Operator',
    rooftopBarcode: 'Rooftop Barcode',
    orientation: 'Orientation',
    orientationToFront: 'Front',
    orientationToBack: 'Back',
    isOffline: 'Is Offline',
    time: 'Time',
    timePeriod: 'Time Period',
    timePeriodStart: 'Start Time',
    timePeriodEnd: 'End Time',
    messageSource: 'Message Source',
    displayMode: 'Display Mode',
    completionFlag: 'Completion Flag',
    interfaceName: 'Interface Name',
    interfaceDescription: 'Interface Description',
    sourceSystem: 'Source System',
    targetSystem: 'Target System',
    requestParameters: 'Request Parameters',
    responseParameters: 'Response Parameters',
    isSuccess: 'Is Success',
    interfaceMethod: 'Interface Method',
    instance: 'Instance',
    communicationStatus: 'Communication Status',
    tagGroup: 'Tag Group',
    tag: 'Tag',
    processWarning: 'Process Warning',
    shiftCode: 'Shift Code',
    uploadFlag: 'Upload Flag',
    writeStatus: 'Write Status',
    isReversed: 'Is Reversed',
    reversedFlag: 'Reversed Flag'
  },
  table: {
    name: 'Name',
    time: 'Time',
    subTaskNo: 'Subtask Number',
    portNo: 'Port Number',
    carrierBarcode: 'Carrier Barcode',
    rooftopBarcode: 'Rooftop Barcode',
    boardBarcode: 'Board Barcode',
    boardStatus: 'Board Status',
    ngBoardErrorCode: 'NG Board Error Code',
    ngBoardErrorDescription: 'NG Board Error Description',
    isFirstCheckBoard: 'Is First Check Board',
    taskSource: 'Task Source',
    motherBatch: 'Mother Batch',
    subTaskSimplifiedCode: 'Subtask Simplified Code',
    subTaskSorting: 'Subtask Sorting',
    materialNo: 'Material Number',
    carrierType: 'Carrier Type',
    layer: 'Layer',
    subDiskSorting: 'Sub-disk Sorting',
    boardLength: 'Board Length',
    boardWidth: 'Board Width',
    boardThickness: 'Board Thickness',
    boardSorting: 'Board Sorting',
    isDummyBoard: 'Is Dummy Board',
    manualProcessingBoard: 'Manual Processing Board',
    isPanelMode: 'Is Panel Mode',
    operator: 'Operator',
    isEAP: 'Is EAP',
    orientation: 'Orientation',
    messageSource: 'Message Source',
    displayMode: 'Display Mode',
    completionFlag: 'Completion Flag',
    message: 'Message',
    code: 'Code',
    serialNumber: 'Serial Number',
    esbInterfaceName: 'ESB Interface Name',
    esbInterfaceDescription: 'ESB Interface Description',
    interfaceMethod: 'Interface Method',
    esbInterfaceAddress: 'ESB Interface Address',
    sourceSystem: 'Source System',
    targetSystem: 'Target System',
    startTime: 'Start Time',
    endTime: 'End Time',
    requestDuration: 'Request Duration',
    isSuccess: 'Is Success',
    interfaceMessage: 'Interface Message',
    requestParameters: 'Request Parameters',
    responseParameters: 'Response Parameters',
    customizedParameters: 'Customized Parameters',
    requestMessage: 'Request Message',
    remark: 'Remark',
    shift: 'Shift',
    shiftCode: 'Shift Code',
    shiftStart: 'Shift Start',
    shiftEnd: 'Shift End',
    uploadFlag: 'Upload Flag',
    uploadTimes: 'Upload Times',
    uploadMessage: 'Upload Message',
    dropBoardQuantity: 'Drop Board Quantity',
    totalQuantity: 'Total Quantity',
    dropBoardRate: 'Drop Board Rate',
    boardOkQuantity: 'Board OK Quantity',
    boardNgQuantity: 'Board NG Quantity',
    boardFailureRate: 'Board Failure Rate',
    carrierOkQuantity: 'Carrier OK Quantity',
    carrierNgQuantity: 'Carrier NG Quantity',
    carrierFailureRate: 'Carrier Failure Rate',
    workingTime: 'Working Time',
    shiftTime: 'Shift Time',
    oee: 'OEE',
    tableForm: 'Table Form',
    curveForm: 'Curve Form',
    exceptionStatistics: 'Exception Statistics',
    communicationInfo: 'Communication Info',
    simulationFlag: 'Simulation Flag',
    sampleQuantity: 'Sample Quantity',
    instanceCode: 'Instance Code',
    instanceDescription: 'Instance Description',
    communicationStatus: 'Communication Status',
    messageDescription: 'Message Description',
    communicationStatusScatterPlotAnalysis: 'Communication Status Scatter Plot Analysis',
    statusValue: 'Status Value',
    communicationExceptionFrequencyStatistics: 'Communication Exception Frequency Statistics',
    networkOutageFrequency: 'Network Outage Frequency',
    tagID: 'Tag ID',
    tagDescription: 'Tag Description',
    collectionValue: 'Collection Value',
    isWarningSet: 'Is Warning Set',
    processWarning: 'Process Warning',
    lowerLimitValue: 'Lower Limit Value',
    upperLimitValue: 'Upper Limit Value',
    oldValue: 'Old Value',
    tagUniqueKey: 'Tag Unique Key',
    dataAlarm: 'Data Alarm',
    dataNormal: 'Data Normal',
    tagAttribute: 'Tag Attribute',
    dataType: 'Data Type',
    area: 'Area',
    areaAddress: 'Area Address',
    startAddress: 'Start Address',
    dataLength: 'Data Length',
    bit: 'Bit',
    OPCAddress: 'OPC Address',
    tagDataScatterPlotAnalysis: 'Tag Data Scatter Plot Analysis',
    processWarningSign: 'Process Warning Sign',
    normal: 'Normal',
    alarm: 'Alarm',
    writeActivityScatterPlotAnalysis: 'Write Activity Scatter Plot Analysis',
    writeFrequencyStatistics: 'Write Frequency Statistics',
    statisticsFromWriterRatio: 'Statistics from Writer Ratio',
    writeNormal: 'Write Normal',
    writeException: 'Write Exception',
    writeValue: 'Write Value',
    writer: 'Writer',
    writeStatus: 'Write Status',
    writeTagQuantity: 'Write Tag Quantity',
    consumeTime: 'Consume Time (ms)',
    frequencyStatistics: 'Frequency Statistics',
    pieChartAnalysis: 'Pie Chart Analysis',
    writeDetail: 'Write Detail'
  },
  pagination: {
    total: 'Total',
    current: 'Current',
    previous: 'Previous',
    next: 'Next',
    unit: 'Page'
  },
  dialog: {
    top: 'Top',
    bottom: 'Bottom',
    queryException: 'Query Exception',
    selectStation: 'Please select a station',
    exportFailed: 'Export Failed',
    selectInstance: 'Please select an instance',
    pleaseSelectTagGroup: 'Please select tag group',
    pleaseSelectTag: 'Please select tag',
    noData: 'No data currently',
    scadaCommunicationReportQueryFailed: 'Scada communication report query failed',
    scadaCommunicationReportQueryTimeoutOrSQLError: 'Scada communication report query timeout or SQL error',
    querySelCellIPException: 'Query selCellIP exception',
    noUnitIPAndPortNumberObtained: 'No unit IP and port number obtained',
    scadaInstanceBaseDataQueryFailed: 'Scada instance base data query failed',
    scadaInstanceBaseDataQueryTimeoutOrSQLError: 'Scada instance base data query timeout or SQL error',
    scadaInstanceTagGroupDataQueryFailed: 'Scada instance tag group data query failed',
    scadaInstanceTagGroupDataQueryTimeoutOrSQLError: 'Scada instance tag group data query timeout or SQL error',
    scadaInstanceTagDataQueryFailed: 'Scada instance tag data query failed',
    scadaInstanceTagDataQueryTimeoutOrSQLError: 'Scada instance tag data query timeout or SQL error',
    scadaReadDataChangeReportQueryFailed: 'Scada read data change report query failed',
    scadaReadDataChangeReportQueryTimeoutOrSQLError: 'Scada read data change report query timeout or SQL error',
    scadaWriteReportQueryFailed: 'Scada write report query failed',
    scadaWriteReportQueryTimeoutOrSQLError: 'Scada write report query timeout or SQL error',
    reconfirmedToClose: 'Please confirm again whether to close',
    hint: 'Hint',
    operationSucceed: 'Operation succeeded',
    operationFailed: 'Operation failed',
    operationCanceled: 'Operation canceled',
    confirmToUnbindThisPCSRecord: 'Confirm to unbind this PCS record',
    confirmToDisableThisSETRecord: 'Confirm to disable this SET record',
    confirmedToSend: 'Confirmed to send',
    formatError: 'Format error'
  }
}

// 志圣- 珠海超毅
export const zhcy = {
  // Operation Modes
  offlineMode: 'Offline Mode',
  onlineRemote: 'Online/Remote',
  onlineLocal: 'Online/Local',

  // Production Status
  productionAllowed: 'Production Allowed',
  productionNotAllowed: 'Production Not Allowed',
  recipeUpdateAllowed: 'Recipe Update Allowed',
  recipeUpdateNotAllowed: 'Recipe Update Forbidden',

  // Indicator Lights
  triColorLight: 'Alarm Light',
  fourColorLight: 'Light',
  greenLight: 'Green Light',
  yellowLight: 'Yellow Light',
  redLight: 'Red Light',
  blueLight: 'Blue Light',

  // Heartbeat Status
  plcHeartbeat: 'PLC',
  eapStatus: 'EAP',
  ccdStatus: 'CCD',
  online: 'Online',
  offline: 'Offline',

  // Login/Logout
  login: 'Login',
  logout: 'Logout',
  employeeLogin: 'Employee Login',
  employeeId: 'Employee ID',
  name: 'Name',
  userName: 'Name',
  permission: 'Permission',
  pleaseEnterEmployeeId: 'Please enter employee ID',
  cancel: 'Cancel',
  confirm: 'Confirm',
  employee: 'Employee',
  engineer: 'Engineer',
  admin: 'Administrator',

  // Other Buttons
  syncAccountInfo: 'Sync Account Info',
  scanAndLoad: 'Load',
  start: 'Start',
  end: 'End',
  loading: 'Loading...',
  loadSuccess: 'Load Successful',
  loadFail: 'Load Failed',
  loadFailCheckContent: 'Load failed, please check scan content',
  pleaseLoginFirst: 'Please login before loading operation',
  offlineModeCannotLoad: 'Cannot perform loading operation in offline mode',

  // Other Text
  pleaseSelectStation: 'No station selected or station information incomplete',
  materialNumber: 'Material Number',
  dryFilmCode: 'Dry Film Code',
  pleaseSelectStatus: 'Please select at least one status for query',

  // Message Prompts
  mqttConnSuccess: 'MQTT connection successful',
  mqttConnFailed: 'MQTT connection failed',
  mqttReconnecting: 'MQTT connection lost, reconnecting...',
  plcCommError: 'PLC communication interrupted',
  eapCommError: 'EAP communication interrupted',
  ccdCommError: 'CCD communication interrupted',
  syncingAccountInfo: 'Requesting account information sync...',
  syncAccountSuccess: 'Successfully synchronized {0} permission accounts',
  syncAccountFailed: 'Failed to request permission account information',
  syncAccountError: 'Error requesting permission account information',
  enterEmployeeId: 'Please enter employee ID',
  loggingIn: 'Logging in...',
  loginSuccess: 'Login successful',
  loginFailed: 'Login failed',
  loginError: 'Login error',
  noUserLoggedIn: 'No user currently logged in',
  confirmLogout: 'Are you sure you want to log out the current employee?',
  logoutSuccess: 'Logout successful',
  logoutFailed: 'Logout failed',
  logoutError: 'Logout error',
  enterOrScanMaterial: 'Please enter or scan material number',
  startingTask: 'Starting task...',
  taskStartSuccess: 'Task started successfully',
  taskStartFailed: 'Task start failed, please check device status',
  endingTask: 'Ending task...',
  taskEndSuccess: 'End request sent successfully, waiting for device to finish remaining materials',
  taskEndFailed: 'Task end failed, please check device status',
  noRecipeData: 'No recipe data available to deploy',
  recipeDeploySuccess: 'Recipe deployed successfully',
  queryException: 'Query exception',
  queryExceptionWithError: 'Query exception: {0}',
  switchingToMode: 'Switching to {0}...',
  rescanRequired: 'Please scan again to load',
  confirm: 'Confirm',
  cancel: 'Cancel',
  warning: 'Warning',
  confirmCancelTask: 'Are you sure you want to cancel task {0}?',
  taskCancelSuccess: 'Task cancelled successfully',
  taskCancelFailed: 'Task cancellation failed',
  taskCancelError: 'Task cancellation error',
  confirmDeleteTask: 'Are you sure you want to delete task {0}? This action cannot be undone!',
  confirmDeleteButton: 'Confirm Delete',
  taskDeleteSuccess: 'Task deleted successfully',
  taskDeleteFailed: 'Task deletion failed',
  taskDeleteError: 'Task deletion error',
  confirmStartTask: 'Are you sure you want to start task {0}?',
  confirmEndTask: 'Are you sure you want to end task {0}?',
  requestingSyncAccountInfo: 'Requesting account information sync...',
  successfullySynced: 'Successfully synchronized ',
  permissionAccountInfo: ' permission accounts',
  requestPermissionAccountInfoFailed: 'Failed to request permission account information',
  requestPermissionAccountInfoException: 'Error requesting permission account information',
  noStationSelectedOrIncompleteInfo: 'No station selected or station information incomplete',
  selectAtLeastOneStatusForQuery: 'Please select at least one status for query',
  handleReloadMachine: 'Confirm reload machine',

  // Task Status
  planning: 'Planning',
  inProduction: 'In Production',
  completed: 'Completed',
  cancelled: 'Cancelled',

  // Other Prompts
  pleaseLoginFirst: 'Please login first',
  offlineModeCannotLoad: 'Cannot load in offline mode',
  loading: 'Loading',
  loadSuccess: 'Load successful',
  loadFail: 'Load failed',
  loadFailCheckContent: 'Load failed, please check device status',
  rescanRequired: 'Please scan again to load',
  confirm: 'Confirm',
  queryFailed: 'Failed to get task list',
  queryException: 'Exception when getting task list',
  taskList: 'Task List',
  alarmInfo: 'Alarm Information',
  taskName: 'Task Name',
  status: 'Status',
  productNumber: 'Product Number',
  taskQuantity: 'Task Quantity',
  createTime: 'Create Time',
  startTime: 'Start Time',
  endTime: 'End Time',
  operations: 'Operations',
  start: 'Start',
  end: 'End',
  cancel: 'Cancel',
  delete: 'Delete',
  query: 'Query',
  planning: 'Planning',
  inProduction: 'In Production',
  completed: 'Completed',
  cancelled: 'Cancelled',
  station: 'Station',
  instanceCode: 'Instance Code',
  instanceDesc: 'Instance Description',
  alarmCode: 'Alarm Code',
  alarmLevel: 'Alarm Level',
  alarmDesc: 'Alarm Description',
  alarmTime: 'Alarm Time',
  resetTime: 'Reset Time',
  isReset: 'Is Reset',
  isSimulated: 'Is Simulated',
  resetYes: 'Reset',
  resetNo: 'Not Reset',
  yes: 'Yes',
  no: 'No',
  capacity: 'Capacity',
  dailyProduction: 'Daily Production',
  readRate: 'Read Rate',
  oee: 'OEE',
  projectName: 'Project Name',
  currentValue: 'Current Value',
  unit: 'Unit',
  upperLimit: 'Upper Limit',
  lowerLimit: 'Lower Limit',
  statusText: 'Status',
  modifyRecipe: 'Modify Recipe',
  isModifyParameters: 'Modify Parameters',
  parameterCode: 'Parameter Code',
  parameterDesc: 'Parameter Description',
  parameterValue: 'Parameter Value',
  validFlag: 'Valid Flag',
  valid: 'Valid',
  invalid: 'Invalid',
  parameterCodeLabel: 'Parameter Code',
  parameterDescLabel: 'Parameter Description',
  parameterValueLabel: 'Parameter Value',
  validFlagLabel: 'Valid Flag',
  confirmAndIssue: 'Confirm and Issue',
  cimMessage: 'CIM Message',
  code: 'Code',
  message: 'Message',
  reloadMachineRequired: 'Reload Machine Required',
  reloadMachineToContinue: 'Please reload machine to continue operation',
  offDuty: 'Off Duty',
  onDuty: 'On Duty',
  enterMaterialCode: 'Enter Material Code',
  enterMaterialDesc: 'Enter Material Description',
  productionMode: 'Production Mode',
  firstPieceMode: 'First Piece Mode',
  dummyMode: 'Dummy Mode',
  recipeMaintenance: 'Recipe Maintenance',
  materialNumber: 'Material Number',
  pagination: {
    total: 'Total',
    current: 'Current Page',
    unit: '',
    previous: 'Previous',
    next: 'Next'
  }
}

export const lang_pack = {
  locale: 'English',
  SystemName: 'WORK',
  SystemNameOne: 'ORDWE INFO SYSTEM',
  UserName: 'Username',
  Password: 'Password',
  Role: 'Role',
  Code: 'Code',
  SignOn: 'SIGN ON',
  Prompt: 'Prompt',
  codeError: "code doesn't  exist or has expired！",
  LoggingIn: 'LoggingIn...',

  // Date time picker shortcuts
  dateTimePicker: {
    last10Minutes: 'Last 10 Minutes',
    last30Minutes: 'Last 30 Minutes',
    lastHour: 'Last 1 Hour',
    lastDay: 'Last 1 Day',
    lastWeek: 'Last 7 Days',
    last30Days: 'Last 30 Days',
    last90Days: 'Last 90 Days',
    lastYear: 'Last 1 Year'
  },
  /** ************************************************************/
  /* Core定义*/
  commonPage: { /* 公共 */
    add: 'Add',
    edit: 'Edit',
    remove: 'Remove',
    search: 'Search',
    reset: 'Reset',
    operate: 'Operate',
    cancel: 'Cancel',
    close: 'Close',
    confirm: 'Confirm',
    checkAll: 'CheckAll',
    back: 'back',
    upload: 'upload',
    download: 'download',
    validIdentification: 'Valid Identification：',
    validIdentificationt: 'Valid Identification',
    detail: 'detail',
    required: 'required',
    submitSuccesful: 'submit Succesful',
    addSuccesful: 'add Succesful',
    editSuccessful: 'edit Successful',
    deleteSuccesful: 'delete Succesful',
    operationSuccessful: 'operation Successful',
    operationfailure: 'Operation failure!',
    pingSuccessful: 'ping Successful',
    pingfailure: 'ping failure',
    scan: 'scan',
    check: 'check'
  },
  maintenanceMenu: { /* 菜单维护 */
    menuEncodingdescription: 'Menu Encoding/Description：',
    menuGroupCoding: 'Menu Group Coding',
    menuGroupDescription: 'menu Group Description',
    order: 'Order',
    menuType: 'Menu Type',
    icon: 'Icon',
    submenuCoding: 'Submenu Coding',
    submenuCodingDescription: 'Submenu Coding Description',
    addSubmenu: 'AddSubmenu',
    procedure: 'Procedure'
  },
  maintenancePeople: { /* 人员维护 */
    nameEmployeeID: 'Name EmployeeID：',
    emailPhone: 'Email/Phone：',
    EmployeeID: 'EmployeeID',
    name: 'Name',
    username: 'Username',
    password: 'Password',
    email: 'Email',
    phone: 'Phone',
    phonenumber: 'Cell-phone Number',
    department: 'Department',
    position: 'Position',
    role: 'Role',
    photo: 'Photo'
  },
  maintenanceErrorMsg: { /* 国际化配置 */
    errorType: 'Type',
    errorModule: 'Module',
    errorFunctionCode: 'FunctionCode',
    errorChinese: 'Chinese',
    errorEnglish: 'English',
    errorOther: 'Other',
    errorKeyword: 'Keyword'
  },
  maintenanceRole: { /* 角色维护 */
    roleNumberDescription: 'Role Number Description：',
    roleNumber: 'Role Number',
    roleDescription: 'Role Description',
    productionLine: 'Production Line',
    process: 'Process',
    locationCoding: 'Location Coding',
    locationDescription: 'Location Description',
    roleEncoding: 'Role Encoding',
    permission: 'Permission',
    menu: 'Menu',
    station: 'Station'
  },
  applicationMenu: { /* 程序菜单 */
    programCodingDescription: 'Program Coding Description：',
    programCode: 'Program Code',
    programDescription: 'Program Description',
    programType: 'Program Type',
    programPath: 'Program Path',
    programDependence: 'Program Dependence',
    programAttribute: 'Program Attribute'
  },
  fastCode: { /* 快速编码 */
    groupCodeDescription: 'Group Code Description：',
    groupCode: 'Group Code',
    groupDescription: 'Group Description',
    isEnabled: 'IsEnabled',
    childCode: 'Child Code',
    childDescription: 'Child Description',
    order: 'Order'
  },
  systemParameter: { /* 系统参数 */
    parametricDescription: 'Parametric / Description：',
    cell: 'Cell：',
    parameterCode: 'Parameter Code',
    parameterDescription: 'Parameter Description',
    parameterValue: 'parameter Value',
    parameterNumber: 'parameter Number',
    parameterdescription: 'Parameter Description'
  },
  errorMessage: { /* 错误消息定义 */
    errorMessageIDDescription: 'ErrorMessageID / Description：',
    errorMessageDescription: 'Error Message Description',
    errorMessageID: 'ErrorMessage ID'
  },
  taskScheduling: { /* 任务调度 */
    taskName: 'Task Name：',
    cellId: 'Cell Id',
    BeanName: 'Bean Name',
    cronExpression: 'Cron Expression',
    mannerExecution: 'Manner Execution',
    failureSuspend: 'Failure Suspend',
    parameter: 'Parameter',
    successSuspendRemove: 'Success Suspend / Remove',
    remark: 'Remark',
    taskStatus: 'Task Status',
    status: 'Status',
    pauseAfterFailure: 'Pause After Failure',
    pauseAfterSuccessRemove: 'Pause After Success / Remove'
  },
  maintenancePline: { /* 产线维护 */
    lineCodeDescription: 'Line Code / Description：',
    lineType: 'Line Type：',
    productionScheduleType: 'Production Schedule Type：',
    lineCode: 'Line Code',
    lineDescription: 'Line Description',
    factory: 'Factory',
    workCenter: 'Work Center',
    activation: 'Activation',
    lockNumber: 'Lock Number',
    sort: 'Sort'
  },
  maintenanceStation: { /* 工位维护 */
    stationCodeDescription: 'Station Code / Description：',
    productionLine: 'Production Line：',
    productionLinet: 'Production Line',
    stationCode: 'Station Code',
    stationDescription: 'Station Description',
    shortNumber: 'Short Number',
    productionLineSegmentCode: 'Production Line Segment Code',
    divisionNumber: 'Division Number',
    stationRelatedAttributes: 'Station Related Attributes',
    stationCombinationCode: 'Station Combination Code',
    ipAddress: 'Ip Address',
    reportWorkingProcedureNumber: 'Report Working Procedure Number',
    workDescription: 'Work Description',
    downTime: 'Down Time',
    serviceTime: 'Service Time',
    runTime: 'Run Time',
    taktImage: 'TaktImage',
    scrollingMessage: 'Scrolling Message',
    methodLis: 'Method Lis',
    productType: 'Product Type',
    cellId: 'Cell Id',
    scadaExample: 'Scada Example',
    exampleFlowChart: 'Example Flow Chart',
    automatedLogicalInstance: 'Auto mated Logical Instance',
    interfaceConfigurationEnvironment: 'Interface Configuration Environment',
    sort: 'Sort',
    OnlineStation: 'Online Station',
    offStation: 'Off Station',
    repairStation: 'Repair Station',
    stationDisplayedIndependently: 'Station Displayed Independently',
    CombinatorialCode: 'Combinatorial Code',
    optionPeriod: 'Option Period',
    orderSelectionAuthority: 'Order Selection Authority',
    segmentedEncoding: 'Segmented Encode',
    onlineStationCode: 'Sys Station',
    reportWorkingProcedureDescription: 'Report Working Procedure Description',
    attribute1: 'Attribute1',
    attribute2: 'Attribute2',
    attribute3: 'Attribute3',
    attribute4: 'Attribute4',
    attribute5: 'Attribute5'
  },
  maintenanceDrive: { /* 驱动维护 */
    driverNameSeries: 'Driver Name / Series：',
    driverName: 'Driver Name',
    area: 'Area'
  },
  maintenanceType: { /* 型号维护 */
    driverNameType: 'Driver Name / Type：',
    driverName: 'Driver Name',
    type: 'Type'
  },
  tagsDefined: { /* 标签定义 */
    productionLineId: 'Production Line Id',
    exampleCode: 'Example Code',
    exampleDescription: 'Example Description',
    exampleGroupCode: 'Example Group Code',
    exampleGroupDes: 'example Group Des',
    driveProgram: 'Driver Program',
    type: 'Type',
    exampleAttribute: 'Example Attribute',
    stationCode: 'Station Code',
    simpleDb: 'Simple Db',
    simulation: 'Simulation'
  },
  monitor: { /* 实例监控 */
    emptyMonitoring: 'empty Monitoring',
    stopMonitoring: 'stop Monitoring',
    startupMonitoring: 'startup Monitoring',
    addTag: 'add Tag',
    startupKafkaMonitoring: 'startup Kafka Monitoring',
    exampleCode: 'example Code',
    exampleDescription: 'Example Description',
    driveProgram: 'Driver Program',
    simulation: 'Simulation',
    timeDuration: 'Time Duration',
    status: 'Status',
    heartbeat: 'Heart beat',
    addWatch: 'Add Watch',
    labelCode: 'Label Code',
    labelDescription: 'Label Description',
    currentValue: 'Current Value',
    labelGroups: 'Label Groups',
    time: 'Time',
    feature: 'Feature',
    message: 'Message',
    labelValue: 'Label Value',
    labelAttr: 'label Attribute',
    groupCode: 'Group Code',
    groupDescription: 'Group Description',
    regionName: 'Region Name',
    areaNumber: 'Area Number',
    regionPosition: 'Region Position',
    dataType: 'Data Type',
    initialAddress: 'Initial Address',
    dataLength: 'Data Length',
    length: 'Length',
    bit: 'Bit',
    opcRealAddress: ' Opc Real Address',
    opcVirtualAddress: 'Opc Virtual Address',
    readWriteAccessPermission: 'ReadWrite Access Permission',
    dataPermissions: 'Data Permissions',
    changePush: 'change Push',
    snapshotStorage: 'Snapshot Storage',
    otherAttributes: 'Other Attributes',
    convertFormat: 'Convert Format',
    saveOnChange: 'Save On Change',
    warningProcessing: 'Warning Processing',
    warningUpperLimit: 'Warning Upper Limit',
    lowerWarningLimit: 'Lower Warning Limit',
    disDataTypes: 'Distinguish data types',
    openOPCUA: 'open OPCUA',
    push: 'Push',
    dataConversion: 'Data Conversion',
    messageDriven: 'Message Driven',
    emptyMessage: 'Empty Message',
    refresh: 'refresh',
    write: 'write',
    attr: 'attr',
    selectProduction: 'please select productionLine',
    selectCell: 'please select cell',
    codeOrDes: 'Instance code or description',
    addMon: 'Add monitoring tags',
    queryInstance: 'Please query the instance first before starting monitoring',
    serviceCell: 'Please maintain service and unit information first',
    selectTAG: 'Please select a TAG',
    refreshException: 'refresh Exception'
  },
  logicattr: { /* 逻辑属性 */
    menuType: 'Menu Type',
    groupCode: 'Group Code',
    groupDescription: 'Group Description',
    childCode: 'Child Code',
    childDescription: 'Child Description',
    attribute1: 'Attribute 1',
    attribute2: 'Attribute 2',
    attribute3: 'Attribute 3',
    attribute4: 'Attribute 4',
    attribute5: 'Attribute 5',
    logicalPropertyMaintenance: 'Logical Property Maintenance'
  },
  logicfunc: { /* 自动化逻辑属性 */
    automatedLogicalPropertyConfiguration: 'Automated Logical Property Configuration',
    logicalProgram: 'Logical Program',
    schedulerCode: 'Scheduler Code',
    schedulerDescription: 'Scheduler Description',
    setControllers: 'SetControllers',
    subproject: 'Subproject',
    value: 'Value',
    attribute1: 'Attribute 1',
    attribute2: 'Attribute 2',
    attribute3: 'Attribute 3',
    attribute14: 'Attribute 4',
    attribute5: 'Attribute 5',
    isEnabled: 'IsEnabled',
    attributeGroup: 'Attribute Group',
    groupCode: 'Group Code',
    groupDescription: 'Group Description',
    subprojectDescription: 'Subproject Description'
  },
  modmain: { /* 主流程图模板维护 */
    templateCode: 'Template Code：',
    templateDescription: 'Template Description：',
    templateCodet: 'Template Code',
    templateDescriptiont: 'Template Description',
    programDriven: 'Program Driven'
  },
  modfunctionm: { /* 函数维护 */
    functionCode: 'Function Code：',
    functionCodet: 'Function Code',
    funcname: 'funcname：',
    funcnamet: 'funcname',
    functionDescription: 'Function Description',
    csprojCode: 'csproj Code:',
    csprojCodet: 'csprojCode',
    functionversion: 'functionversion',
    functionversiondes: 'functionversiondes'
  },
  modcsproj: { /* 工程维护 */
    csprojCode: 'csproj Code：',
    csprojCodet: 'csproj Code',
    csprojdes: 'csprojdes：',
    csprojdest: 'csprojdes',
    csprojtargetframework: 'csprojtargetframework',
    csprojreference: 'csprojreference',
    csprojversion: 'csprojversion',
    csprojversiondes: 'csprojversiondes',
    csprojpathWin: 'csprojpathWin',
    csprojpathLinux: 'csprojpathLinux'
  },
  mainmain: { /* 主流程图维护 */
    mainProcessCode: 'Main Process Code：',
    mainProcessDescription: 'Main Process Description：',
    basicAttribute: 'Basic Attribute',
    processTemplate: 'Process Template',
    station: 'Station',
    mainProcessCodet: 'Main Process Code',
    mainProcessDescriptiont: 'Main Process Description',
    taskNumberPrefix: 'Task Number Prefix',
    programProperties: 'Program Properties',
    processFlow: 'Process Flow',
    examplesCollections: 'Examples Collections',
    triggerPoint: 'Trigger Point',
    triggerPointValue: 'Trigger Point Value',
    taskTriggeringMode: 'Task Triggering Mode',
    pollingTime: 'Polling Time',
    conditionsSet: 'Conditions Set',
    conditionGroupDescription: 'Condition Group Description',
    conditionsDescribed: 'Conditions Described',
    monitoringPoint: 'Monitoring Point',
    setUpInstructions: 'SetUp Instructions',
    processCodet: 'Process Code',
    processDescriptiont: 'Process Des',
    taskInfo: 'Task Info',
    view: 'view',
    dataException: 'Data Exception',
    selectProduction: 'Please select the production line and workstation',
    taskNumber: 'taskNumber',
    creationDate: 'creationDate',
    detailInfo: 'Detailed information',
    pendEvents: 'Pending events',
    strDrawFlow: 'Struggling to draw a flowchart',
    attrMomitor: 'Attribute monitoring',
    abnormalData: 'Abnormal initialization mode data',
    amplify: 'amplify',
    reduce: 'reduce',
    sureDelete: 'Are you sure you want to delete the current selection',
    step: 'step?',
    cancelledDeletion: 'Cancelled deletion',

    log: 'log',
    attr: 'attr',
    inputParams: 'input Params',
    outParams: 'out Params',
    subprocess: 'subprocess',
    stepDes: 'step Des',
    sort: 'sort',
    stepType: 'step Type',
    startStep: 'start Step',
    endStep: 'end Step',
    generalSteps: 'general Steps',
    methodName: 'method Name',
    nextOK: 'Next step when OK',
    stepToNg: 'Steps to NG',
    afterLoop: 'Next step loop ends',
    selectNext1: 'Select 1 Next Steps',
    selectNext2: 'Select 2 Next Steps',
    selectNext3: 'Select 3 Next Steps',
    selectNext4: 'Select 4 Next Steps',
    selectNext5: 'Select 5 Next Steps',
    cancelNext: 'Cancel next step',
    retryCount: 'retry Count',
    limiTime: 'limi Time(ms)',
    colorStateOK: 'Color in OK state',
    colorStateNG: 'Color in NG state',
    colorStateCancel: 'Color in NG cancel',
    colorStateRetry: 'Color in NG retry',
    initColor: 'init Color',
    code: 'code',
    conditionGroup: 'condition Group',
    notHave: 'not have',
    endAll: 'End All',
    logQuery: 'log Query',
    logType: 'log Type',
    logCode: 'log Code',
    date: 'date',
    logInfo: 'log Info',

    stepJump: 'step Jump',
    radio: 'radio',
    index: 'index',
    stepName: 'step Name',
    selectFlow: 'Please select the process first',
    jumpSuccessful: 'jump Successful',

    subDes: 'Sub description',
    processType: 'Process Type',
    startSub: 'Start sub',
    endSub: 'End sub',
    regularSub: 'Regular sub',
    nextSub: 'Next sub',
    subEnd: 'End of sub',
    afterEnd: 'End the entire process after the current sub process ends',
    subFunctionDll: 'sub FunctionDll',
    subAttr: 'Sub Program Properties',
    controlIcon: 'control Icon',
    controlWidth: 'control Width',
    controlHeight: 'control Height',
    colorStateWait: 'colorStateWait',
    cannotBeEmpty: 'cannot Be Empty',
    enterNumber: 'Please enter a numerical value',
    enterSub: 'Please enter the sub process code',
    enterDes: 'Please enter a sub process description',
    nextSelect: 'Please select the next sub process',
    enterFunction: 'Please enter the sub process FunctionDll',
    addSub: 'Successfully added sub process',
    addError: 'Sub process addition exception',
    editSuccess: 'Subprocess modification successful',
    editError: 'Sub process modification exception'

  },
  taskList: { /* 任务列表*/
    TaskSource: 'Task Source',
    taskNumber: 'task Number',
    ScheduleDate: 'Schedule Date',
    NumberOfTasks: 'Number Of Tasks',
    UrgentOrder: 'Urgent Order',
    TargetCuttingMachine: 'Target Cutting Machine',
    CutType: 'Cut Type',
    TaskCreationTime: 'Task Creation Time',
    CrownBlockAutomatic: 'Crown Block Automatic',
    CrownBlockStatus: 'Crown Block Status',
    CuttingStatus: 'Cutting Status',
    WhetherToCutOrNot: 'Whether To Cut Or Not',
    SortOrNot: 'Sort Or Not',
    ValidOrNot: 'Valid Or Not',
    SteelPlateModel: 'Steel Plate Model',
    DXFcode: 'DXF Code',
    DXFFiles: 'DXF Files',
    NCcode: 'NC Code',
    NCFiles: 'NC Files',
    TaskStatus: 'Task Status',
    validIdentification: 'Valid Identification：'
  },
  cuttingZone: { // 切割区
    CacheBit: 'Cache Bit',
    TaskNumber: 'Task Number',
    SteelPlateModel: 'Steel Plate Model',
    SteelPlateSource: 'SteelPlate Source',
    CacheStatus: 'Cache Status',
    DelayTime: 'Delay Time',
    ManualOperation: 'Manual Operation',
    CuttingPosition: 'Cutting Position',
    CuttingMachineStatus: 'Cutting Machine Status',
    cutNCName: 'Cut file name',
    PROGRAMNO: 'PROGRAM NO',
    schedule: 'schedule',
    DeviceName: 'Device Name',
    AlarmClassification: 'Alarm Classification',
    AlarmLevel: 'Alarm Level',
    AlarmMessage: 'Alarm Message',
    AlarmOccurrenceTime: 'Alarm Occurrence Time',
    startTime: 'start Time',
    endTime: 'end Time',
    PlannedCuttingTime: 'Planned Cutting Time',
    ProgramUploadTime: 'Program Upload Time',
    CurrentWorkstation: 'Current Workstation',
    TargetWorkstation: 'Target Workstation',
    StockCode: 'Stock Code',
    StockCount: 'Stock Count',
    MaterialModel: 'Material Model',
    ExecutionOrder: 'Execution Order',
    AdjustTheOrder: 'AdjustThe Order',
    WarehousingTaskNumber: 'Warehousing Task Number',
    WarehousingTime: 'Warehousing Time',
    LocationSorting: 'Location Sorting',
    ZAxisCoordinates: 'Z Axis Coordinates',
    IsItLocked: 'Is It Locked',
    LockTaskNumber: 'Lock Task Number'
  },
  sortingArea: { // 分拣区
    SortingStation: 'Sorting Station',
    SortType: 'Sort Type',
    PlannedQuantity: 'Planned Quantity',
    ActualQuantity: 'Actual Quantity',
    SheetName: 'Sheet Name',
    MaterialFrameNumber: 'Material Frame Number',
    MaterialFrameParts: 'Material Frame Parts',
    Number: 'Number',
    Status: 'Status',
    MaterialFrameDetails: 'Material Frame Details',
    AGVStatus: 'AGV Status',
    StationInspectionStatus: 'Station Inspection Status',
    WorkReportingStatus: 'Work Reporting Status',
    AbnormalWorkReporting: 'Abnormal Work Reporting',
    TransitDetails: 'Transit Details',
    ProductionTaskNumber: 'Production Task Number',
    CurrentStationNumber: 'Current Station Number',
    SourceStaus: 'Source Staus',
    Cutterbar: 'Cutterbar',
    arriveDate: 'arrive Date',
    leaveDate: 'leave Date',
    StationConsumptionTime: 'Station Consumption Time',
    PalletNumber: 'Pallet Number',
    ReportWorkOrNot: 'Report Work OrNot',
    MaterialFrameTaskNumber: 'Material Frame Task Number',
    PartLength: 'Part Length',
    PartWidth: 'Part Width',
    PartType: 'Part Type',
    PartThickness: 'Part Thickness',
    PartWeight: 'Part Weight',
    PlannedSortingTime: 'Planned Sorting Time',
    SortingStationNumber: 'Sorting Station Number',
    PartNumber: 'Part Number',
    PartBarcodeNumber: 'Part Barcode Number',
    PartDrawingNumber: 'Part Drawing Number',
    partMaterialNumber: 'part material number',
    PlannedTimeSpent: 'Planned Time Spent /s',
    CurrentSortingStation: 'Current Sorting Station',
    TargetTransmissionStation: 'Target Transmission Station',
    CompulsoryRelease: 'Compulsory Release',
    MandatoryReportingInterface: 'Mandatory Reporting Interface',
    IsAutomaticSortingCompleted: 'IsAutomatic Sorting Completed',
    CurrentSortingDXFDrawing: 'Current Sorting DXF Drawing',
    CurrentSortingSteelPlateModel: 'Current Sorting Steel Plate Model',
    ForceComplete: 'Force Complete',
    WorkReportingInterfaceAddress: 'Wor kReporting Interfac eAddress',
    DXFFileName: 'DXF File Name',
    GNCFileName: 'GNC File Name',
    PersonInChargeOfWorkReporting: 'PersonIn Charge Of Work Reporting',
    ClickToReportWork: 'Click To Report Work'
  },
  equipment: {
    stationCode: 'station Code',
    deviceCode: 'device Code',
    DeviceName: 'Device Name',
    PartNumber: 'Part Number',
    PartName: 'Part Name',
    TheoreticalLifespan: 'Theoretical Lifespan',
    ServiceLife: 'Service Life',
    Status: 'Status',
    PartDescription: 'Part Description',
    OriginalPartNumber: 'Original Part Number',
    OriginalPartName: 'Original Part Name',
    PartNumberAfterReplacement: 'Part NumberAfter Replacement',
    PartNameAfterReplacement: 'Part Name After Replacement',
    ReplacementTime: 'Replacement Time'
  },
  mfTable: { // 机型基础
    creator: 'creator',
    creationTime: 'creation time ',
    ModifiedBy: 'modifier ',
    time: 'time ',
    link: 'link ',
    mainMaterialCode: 'Material code ',
    mainMaterialDescription: 'MaterialDescription ',
    partDrawingNumber: 'Part drawing number ',
    partMaterialNumber: 'part material number',
    model_type: 'Model ',
    length: 'length ',
    width: 'width ',
    thick: '厚',
    height: 'height ',
    weight: 'heavy ',
    materialQuality: 'Material ',
    cuttingMachine: 'Material (cutting machine)',
    dataSources: 'Data source ',
    cuttingXCoordinate: 'Cut the x coordinate ',
    cuttingyCoordinate: 'Cut y coordinate ',
    enableFlag: 'Valid Identification: '
  },
  smaterialbox: {// 分拣箱基础
    creator: 'creator',
    creationTime: 'creation Time',
    modifiedBy: 'modifier',
    time: 'time',
    materialFrameCoding: 'material Frame Coding',
    materialFrameName: 'material Frame Name',
    materialFrameType: 'material Frame Type',
    AGVLocationCode: 'AGV LocationCode'
  },
  qualityData: {// 质量数据采集
    creator: 'creator',
    creationTime: 'creation Time',
    modifiedBy: 'modifier',
    time: 'time',
    stationId: 'station Id',
    sourceOfQualityData: 'source Of QualityData',
    collectionItems: 'collectio Items',
    markOfConformity: 'mark Of Conformity',
    groupNumber: 'group Number',
    groupDescription: 'group Description',
    positionOfColumn: 'position Of Column',
    columnSorting: 'column Sorting',
    measurementObject: 'measurement Object',
    entryName: 'entry Name',
    collectionProjectUnit: 'collection Project Unit',
    standardValue: 'standard Value',
    lowerLimitingValue: 'lower Limiting Value',
    upperLimitValue: 'upper Limit Value',
    saveOrNot: 'save Or Not'
  },
  taskTable: {
    taskNumber: 'task Number',
    taskSource: 'task Source',
    serialNumber: 'serial Number',
    batchNumber: 'batc hNumber',
    productionMode: 'production mode',
    taskType: 'task Type',
    materialCode: 'material Code',
    materialDescription: 'material Description',
    partDrawingNumber: 'part drawing number',
    partMaterialNumber: 'part material number',
    modelY: 'model',
    length: 'length',
    width: 'width',
    thick: 'thick',
    height: 'height',
    weight: 'weight',
    materialQuality: 'material Quality',
    cuttingMachine: 'cutting Machine',
    xCoordinate: 'x Coordinate',
    yCoordinate: 'y Coordinate',
    DXFFileName: 'DXF File Name',
    DXFFileURLPath: 'DXF File URL Path',
    JSONFileName: 'JSON File Name',
    JSONFileURLPath: 'JSON File URL Path',
    JSONFileURLTxt: 'JSON File URL Txt',
    JSONData: 'JSON Data',
    NCFileName: 'NC File Name',
    NCFileURLPath: 'NC File URL Path',
    scheduledTime: 'scheduled Time',
    sort: 'sort',
    taskStatus: 'task Status',
    taskMessage: 'task Message',
    cutterbar: 'cutterbar',
    cutterbarType: 'cutterbar Type',
    cuttingPlanTime: 'cutting Plan Time',
    shotBlastingOrNot: 'shot Blasting Or Not',
    automaticCrownBlockOrNot: 'automatic CrownBlock Or Not',
    whetherToAutotheboard: 'whether To Autotheboard',
    whetherToSprayCode: 'whether To Spray Code',
    whetherToAutomaticallyCut: 'whether To Automatically Cut',
    automaticSortingOrNot: 'automatic Sorting Or Not',
    isTheCarCentered: 'is Th eCar Centered',
    onSiteSiteMaterials: 'on Site Site Materials',
    stationPosition: 'station Position',
    reportWorkOrNot: 'report Work Or Not',
    area: 'area', // 面次
    NoBowPlate: 'No Bow Plate'// 是否首板
  },
  surplusMaterialTable: {// 生产任务余料表
    description: 'description',
    mainTaskID: 'mainTask ID',
    plusTaskNumber: 'plusTaskNumber',
    plusRemnantMat: 'plusRemnantMat',
    plusRemnantThick: 'plusRemnantThick',
    plusRemnantLength: 'plusRemnantLength',
    plusRemnantWidth: 'plusRemnantWidth',
    plusRemnantWeight: 'plusRemnantWeight'
  },
  eventRecordForm: { // 事件记录表
    mainTaskID: 'main task ID',
    user: 'user ',
    eventType: 'event type ',
    eventName: 'Event name ',
    sendContent: 'Send content ',
    acceptContent: 'Acceptcontent',
    messageCODE: 'Message CODE',
    messageContent: 'Message content ',
    eventStatus: 'event status ',
    startTime: 'Start time ',
    endTime: 'The end time',
    timeConsuming: 'Consumed time (seconds)',
    remake: 'Remarks',
    condition: 'condition',
    timeoutLimit: 'timeout Limit',
    lastUpdateTime: 'last Update Time'
  },
  sortingResults: {// Sorting results
    mainTaskID: 'main task ID',
    stationCode: 'Station number ',
    partBarcodeNumber: 'Barcode for Parts ',
    robotCoding: 'Robot coding',
    partCode: 'Part code ',
    partDrawingNumber: 'Part drawing number ',
    sortSequenceNumber: 'Sorting sequence number ',
    partMaterialNumber: 'part material number',
    nedCuttingTime: 'cut time ',
    routingCode: 'Process Code ',
    sprayCodeXCoordinate: 'code vertex X coordinate ',
    sprayCodeYCoordinate: 'Code vertex Y coordinate ',
    PartType: 'Part type ',
    sortResultCode: 'Sorting result code ',
    exceptionInformation: 'Message/exception information ',
    length: 'length',
    width: 'width ',
    thick: '厚',
    NCFileName: 'nc file name',
    weight: 'heavy ',
    nextRouting: 'Parts processing',
    reservedPartProperties: 'Parts distribution point',
    timeRequiredForSorting: 'time Required For Sorting',
    startTime: 'Start time ',
    endTime: 'The end time',
    sortStartTime: 'Sorting start time',
    sortEndTime: 'Sorting end time'
  },
  sortingBoxResults: { // 料框结果
    materialFramePosition: 'Material frame position ',
    materialFrameBarcode: 'Material frame barcode ',
    taskNumber: 'task number ',
    partBarcodeNumber: 'Barcode for Parts ',
    partCode: 'Part code ',
    partType: 'Part type ',
    resultCode: 'Result code ',
    stackingMessage: 'Palletizing message/exception ',
    stackingTime: 'Stackingtime ',
    stackingStartTime: 'Stacking start time ',
    stackingEndTime: 'End of stacking time'
  },
  realTimeTable: { // 工位MIS实时状态表
    username: 'User name ',
    arriveDate: 'arrival time ',
    leaveDate: 'Leave time ',
    stationCode: 'Station number ',
    stationDescription: 'Station description ',
    stationProperties: 'Station properties ',
    stationStatus: 'Station status ',
    taskNumber: 'task number ',
    taskSource: 'task source ',
    serialNumber: 'serial number ',
    batchNumber: 'batch number ',
    palletNumber: 'tray number ',
    palletUsageTimes: 'number of times used ',
    maximumPalletUsage: 'maximum number of times used ',
    trayAlarmIdentification: 'Pallet AlarmIdentification ',
    mainMaterialCode: 'Material code ',
    mainMaterialDescription: 'MaterialDescription ',
    partDrawingNumber: 'Part drawing number ',
    partMaterialNumber: 'part material number',
    model_type: 'Model ',
    length: 'length ',
    width: 'width',
    thick: 'thick',
    weight: 'weight',
    materialQuality: 'Material ',
    cuttingMachine: 'Material',
    uptime: 'online time ',
    workpieceInProcessTime: 'Time in production',
    idealBeat: 'Ideal beat',
    actualBeat: 'Actual beat',
    shifCode: 'shift code ',
    shifDes: 'Shift Description ',
    sourceStationNumber: 'Source station number ',
    stationErrorMessage: 'Station error message ',
    DXFName: 'DXF name ',
    JSONName: 'JSON name ',
    NCName: 'NC name '
  },
  wmsCbTable: {// WMS Crane base table
    libraryArea: 'library area ',
    crownBlockCode: 'Crownblock Code ',
    processName: 'Process name ',
    subProcess: 'subprocess ',
    crownBlockDescription: 'CrownblockDescription ',
    crownBlockNumber: 'crownblock number ',
    crownBlockAttributes: 'Crownblockattributes ',
    crownBlockType: 'Crane type ',
    groupCollection: 'Manage library bit group collection ',
    originXCoordinate: 'originX coordinate ',
    originYCoordinate: 'originY coordinate ',
    originZCoordinate: 'originZ coordinate ',
    farPointXCoordinate: 'far-point X coordinate ',
    farPointYCoordinate: 'far-point Y coordinate ',
    farPointZCoordinate: 'far point Z coordinate ',
    safeDistance: 'safe distance ',
    WarehouseLocationGroupCode: 'location codes',
    LocationGroupDescription: 'location to describe',
    warehouseLocationNumber: 'warehouse location number ',
    WarehouseLocationDescription: 'location description',
    InventoryLocationSorting: 'location sorting',
    manageInventoryOrNot: 'Manage inventory or not ',
    isTheModelFixed: 'Whether the model is fixed ',
    modelID: 'Model ID',
    xCoordinate: 'X coordinate ',
    YCoordinate: 'Y coordinate ',
    ZCoordinate: 'Z coordinate ',
    isTheZAxisDefinedValue: 'Whether the Z axis is a defined value ',
    ZAxisDynamicCalculationMethod: 'Z axis dynamic calculation method',
    inventory: 'inventory',
    minimumInventory: 'minimum inventory ',
    maximumInventory: 'maximum inventory ',
    loopCode: 'loopcode ',
    MaximumLimitExpirationTime: 'biggest limit extended time',
    warehouseLocationStatus: 'Repository location status ',
    locationType: 'library location type ',
    taskNumber: 'task number ',
    taskSource: 'task source ',
    serialNumber: 'serial number ',
    batchNumber: 'batch number ',
    ZAxisCoordinates: 'Z axis coordinates ',
    isInventoryLocked: 'Inventory is locked ',
    warehousingMethod: 'warehousingmethod',
    taskType: 'task type ',
    findTheYCoordinate: 'Find the Y coordinate ',
    isTheSearchCompleted: 'Whether search is completed ',
    warehousingTime: 'days in storage ',
    isInventoryAlarm: 'Alarm if inventory is out of date ',
    mainTaskID: 'TaskID ',
    model_type: 'Model ',
    locationID: 'library locationID ',
    taskExecutionStatus: 'Task execution status',
    TaskExecutionErrorMessage: 'task execution error message',
    startingStorageLocation: 'start repository location ',
    targetInventoryLocation: 'Target repository ',
    taskStartTime: 'Task start time ',
    taskEndTime: 'task end time ',
    timeConsuming: 'Consumed time (seconds)',
    isTheModelChecked: 'Is the model checked ',
    checkTheRollerTableTime: 'Check roller table time ',
    checkModelTime: 'Check model time ',
    checkModelResults: 'Check model results ',
    crownBlockTaskType: 'CrownBlockTaskType ',
    stepCode: 'Step code',
    stepName: 'Step name ',
    stepStatus: 'Step status ',
    restrictionStepID: 'restriction step ID',
    restrictionStepName: 'Restriction step name ',
    restrictionStepStatus: 'Restriction step status ',
    isTheRollerTableChecked: 'Whether to check the roller table'
  },
  meStationQuality: {
    stationCode: 'Station number ',
    taskNumber: 'task number ',
    serialNumber: 'serial number ',
    groupNumber: 'group number ',
    groupDescription: 'Group description ',
    positionOfColumn: 'Column position ',
    collectionItems: 'Collection items ',
    columnSorting: 'Column sorting ',
    measurementObject: 'measure object ',
    entryName: 'Capture item name ',
    collectionProjectUnit: 'Collection item unit ',
    standardValue: 'standard value ',
    lowerLimitingValue: 'lower limit ',
    upperLimitValue: 'upper Limit Value',
    collectValues: 'sub-qualification mark ',
    retrospectiveTime: 'Retrospectivetime'

  },
  center: { /* 中心维护 */
    centreNumberDescription: 'Centre Number / Description：',
    centreCode: 'Centre Code',
    centreDescription: 'Centre Description',
    mainCard: 'Main Card',
    attachCard1: 'AttachCard1',
    attachCard2: 'AttachCard2',
    attachCard3: 'AttachCard3'
  },
  server: { /* 服务维护 */
    centre: 'Centre',
    serviceTagDescription: 'Service Tag / Description：',
    serviceCode: 'Service Code',
    serviceDescription: 'Service Description',
    mainCard: 'Main Card',
    attachCard1: 'AttachCard1',
    attachCard2: 'AttachCard2',
    attachCard3: 'AttachCard3',
    esbServer: 'Esb Server',
    system: 'system'
  },
  cell: { /* 单元维护 */
    cellNameDescription: 'Cell Name / Description：',
    serve: 'Serve',
    cellName: 'Cell Name',
    cellDescription: 'Cell Description',
    mirrorName: 'Mirror Name',
    webPort: 'Web Port',
    mqttPort: 'Mqtt Port',
    opcUaPort: 'OPC UA Port',
    cellPort: 'Cell Port',
    startOpcUa: 'Start OPC UA'
  },
  interf: { /* 接口维护 */
    interfaceCodeDescription: 'Interface Code / Description：',
    interfaceCode: 'Interface Code',
    interfaceDescription: 'Interface Description',
    sourceSystem: 'Source System',
    targetSystem: 'Target System',
    interfaceMeans: 'Interface Means',
    testAddress: 'Test Address',
    officialAddress: 'Official Address',
    testReturnValue: 'Test Return Value',
    interfaceNumber: 'Interface Number',
    productionAddress: 'Production Address',
    remark: 'Remark',
    cell: 'Cell',
    parameterValue: 'Parameter Value'
  },
  file: { /* 文件管理 */
    autoDesk: 'Auto Desk',
    fileName: 'File Name',
    fileSize: 'File Size'
  },
  celldeploy: { /* 单元部署 */
    serve: 'Serve：',
    uploadMirroring: 'Upload Mirroring',
    installAll: 'Install All',
    installSelected: 'Install Selected',
    unitName: 'Unit Name',
    unitDescription: 'Unit Description',
    cellMirrorName: 'Cell Mirror Name',
    installMirrorName: 'Install Mirror Name',
    serviceMirroringInstall: 'Service Mirroring Install'
  },
  cellprogupd: { /* 单元程序更新 */
    serve: 'Serve：',
    massUpdate: 'Mass Update',
    selectiveupdate: 'Selective Update',
    updateObject: 'Update Object',
    updateMode: 'Update Mode',
    file: 'File',
    serialNumber: 'Serial Number',
    cell: 'Cell',
    procedure: 'Procedure',
    fileName: 'File Name',
    fileType: 'File Type',
    fileSize: 'File Size'
  },
  progbatchupd: { /* 单元程序更新批量 */
    serve: 'Serve：',
    massUpdate: 'Mass Update',
    selectiveupdate: 'Selective Update',
    updateObject: 'Update Object',
    updateMode: 'Update Mode',
    file: 'File',
    serialNumber: 'Serial Number',
    cell: 'Cell',
    procedure: 'Procedure',
    fileName: 'File Name',
    fileType: 'File Type',
    fileSize: 'File Size'
  },
  /** ************************************************************/
  /* MES定义*/

  /** ************************************************************/
  /* PMC定义*/
  pmcquery: { /* 查询条件 */
    creationDate: 'Creation Date：',
    workCenterCode: 'Work Center Code：',
    prodLineCode: 'Prod Line Code：',
    stationCode: 'Station Code：',
    makeOrder: 'Make Order：',
    vin: 'Vin：',
    vinFlag: 'Vin Flag：',
    workStatus: 'Work Status：',
    setSign: 'Set Sign：',
    zkName: 'Zk Name：',
    lastMakeOrder: 'Last Make Order：',
    stationDeviceType: 'Station Device Type：',
    deviceSectionCode: 'Device Section Code：',
    qualityFrom: 'Quality From：',
    screenSetType: 'Screen Set Type：',
    andonType: 'Andon Type：',
    andonCardStatus: 'Andon Card Status：',
    andonControlName: 'Andon Control Name：',
    happenDate: 'happen Date：',
    resetFlag: 'reset Flag：',
    andonVoiceSerial: 'Andon Voice Serial：',
    andonVoiceDes: 'Andon Voice Des：',
    andonMusicDes: 'Andon Music Des：',
    enableFlag: 'Enable Flag：'
  },
  flowonline: { /* 线首 */
    creationDate: 'Creation Date',
    flowOnlineId: 'Flow Online Id',
    workCenterCode: 'Work Center Code',
    prodLineCode: 'Prod Line Code',
    stationCode: 'Station Code',
    makeOrder: 'Make Order',
    serialNum: 'Serial Num',
    dms: 'Dms',
    itemProject: 'Item Project',
    vin: 'Vin',
    smallModelType: 'Small Model Type',
    mainMaterialCode: 'Main Material Code',
    materialColor: 'Material Color',
    materialSize: 'Material Size',
    shaftProcNum: 'Shaft Proc Num',
    staffId: 'Staff Id',
    palletNum: 'Pallet Num',
    engineNum: 'Engine Num',
    driverWay: 'Driver Way',
    publishNumber: 'Publish Number',
    vinFlag: 'Vin Flag',
    repairFlag: 'Repair Flag',
    enableFlag: 'Enable Flag'
  },
  stationmo: { /* 工位生产订单 */
    creationDate: 'Creation Date',
    stationMoId: 'Station Mo Id',
    prodLineCode: 'Prod Line Code',
    stationCode: 'Station Code',
    makeOrder: 'Make Order',
    targetMakeOrder: 'Target Make Order',
    dms: 'Dms',
    itemProject: 'Item Project',
    moWorkOrder: 'Mo Work Order',
    workStatus: 'Work Status',
    setSign: 'Set Sign',
    setDate: 'Set Date',
    setMarks: 'Set Marks',
    preSetOutBut: 'Pre Set Out',
    cancelPreSetOutBut: 'Cancel Pre Set Out',
    setOutBut: 'Set In',
    preSetInBut: 'Pre Set In',
    cancelPreSetInBut: 'Cancel Pre Set In',
    setInBut: 'Set Out'
  },
  modownstatus: { /* 下发订单状态 */
    creationDate: 'Creation Date',
    lastUpdateDate: 'Last Update Date',
    zkName: 'Zk Name',
    lastMakeOrder: 'Last Make Order'
  },
  stationflow: { /* 过站 */
    creationDate: 'Creation Date',
    stationFlowId: 'Station Flow Id',
    workCenterCode: 'Work Center Code',
    prodLineCode: 'Prod Line Code',
    stationCode: 'Station Code',
    stationDes: 'Station Des',
    proceduceCode: 'Proceduce Code',
    proceduceDes: 'Proceduce Des',
    serialType: 'Serial Type',
    serialNum: 'Serial Num',
    palletNum: 'Pallet Num',
    staffId: 'Staff Id',
    makeOrder: 'Make Order',
    dms: 'Dms',
    itemProject: 'Item Project',
    vin: 'Vin',
    smallModelType: 'Small Model Type',
    mainMaterialCode: 'Main Material Code',
    materialColor: 'Material Color',
    materialSize: 'Material Size',
    shaftProcNum: 'Shaft Proc Num',
    repair_flag: 'Repair Flag',
    qualitySign: 'Quality Sign',
    dataCollectWay: 'Data Collect Way',
    arriveDate: 'Arrive Date',
    leaveDate: 'Leave Date',
    costTime: 'Cost Time',
    shifCode: 'Shif Code',
    shifDes: 'Shif Des',
    setSign: 'Set Sign',
    checkStatus: 'Check Status',
    checkCode: 'Check Code',
    checkMsg: 'Check Msg',
    flowStautsCode: 'Flow Stauts Code',
    assemblyFinishFlag: 'Assembly Finish Flag',
    mesAviFlag: 'Mes Avi Flag',
    upFlag: 'Up Flag',
    upNgCode: 'Up Ng Code',
    upNgMsg: 'Up Ng Msg',
    vinAviFlag: 'Vin Avi Flag',
    downVinFlag: 'Down Vin Flag',
    downVinNgCode: 'Down Vin Ng Code',
    downVinNgMsg: 'Down Vin Ng Msg',
    jzAviFlag: 'Jz Avi Flag',
    downJzFlag: 'Down Jz Flag',
    downJzNgCode: 'Down Jz Ng Code',
    downJzNgMsg: 'Down Jz Ng Msg',
    lesAviFlag: 'Les Avi Flag',
    downLesFlag: 'Down Les Flag',
    downLesNgCode: 'Down Les Ng Code',
    downLesNgMsg: 'Down Les Ng Msg',
    downAgvFlag: 'Down Agv Flag',
    downAgvNgCode: 'Down Agv Ng Code',
    downAgvNgMsg: 'Down Agv Ng Msg',
    enableFlag: 'Enable Flag'
  },
  meStationFlow: {
    stationCode: 'Station number ',
    stationDes: 'Station Description ',
    taskNumber: 'task number ',
    taskSource: 'task source ',
    serialNumber: 'serial number ',
    batchNumber: 'batch number ',
    palletNum: 'tray number ',
    mainMaterialCode: 'Material code ',
    mainMaterialDescription: 'MaterialDescription ',
    partDrawingNumber: 'Part drawing number ',
    partMaterialNumber: 'part material number',
    model_type: 'Model ',
    length: 'length ',
    width: 'wide ',
    thick: 'thick',
    weight: 'weight',
    materialQuality: 'Material ',
    cuttingMachine: 'Material (cutting machine)',
    releaseMethod: 'Release mode ',
    releaseUser: 'release person ',
    releaseInstructions: 'Release Instructions ',
    releaseTime: 'release time ',
    arriveDate: 'arrival time ',
    leaveDate: 'Leave time ',
    costTime: 'Time consumed (in seconds)',
    shifCode: 'shift code ',
    shifDes: 'Shift Description ',
    OnlineWorkstationIdentification: 'online location identification',
    OfflineWorkstationIdentification: 'offline location identification',
    tagQualitySignId: 'conformity mark',
    reportWorkOrNot: 'Did you complete the report?',
    WorkReportInspectionInformation: 'work check news',
    upFlag: 'upFlag ',
    upNgCode: 'upNgCode',
    upNgMsg: 'upNgMsg'
  },
  stationstatus: { /* 当前工位实时工件信息 */
    creationDate: 'Creation Date',
    stationStatusId: 'Station Status Id',
    workCenterCode: 'Work Center Code',
    prodLineCode: 'Prod Line Code',
    stationCode: 'Station Code',
    stationDes: 'Station Des',
    stationStatus: 'Station Status',
    emptyBanFlag: 'Empty Ban Flag',
    serialNum: 'Serial Num',
    palletNum: 'Pallet Num',
    staffId: 'Staff Id',
    makeOrder: 'Make Order',
    dms: 'Dms',
    itemProject: 'Item Project',
    vin: 'Vin',
    smallModelType: 'Small Model Type',
    mainMaterialCode: 'Main Material Code',
    materialColor: 'Material Color',
    materialSize: 'Material Size',
    shaftProcNum: 'Shaft Proc Num',
    qualitySign: 'Quality Sign',
    setSign: 'Set Sign',
    checkStatus: 'Check Status',
    checkCode: 'Check Code',
    checkMsg: 'Check Msg',
    engineNum: 'Engine Num',
    driverWay: 'Driver Way',
    statusWay: 'Status Way',
    lineSectionCode: 'Line Section Code',
    allowWay: 'Allow Way'
  },
  setinout: { /* 拉入拉出履历 */
    creationDate: 'Creation Date',
    setReportId: 'Set Report Id',
    workCenterCode: 'Work Center Code',
    prodLineCode: 'Prod Line Code',
    stationCode: 'Station Code',
    stationDes: 'Station Des',
    setType: 'Set Type',
    serialNum: 'Serial Num',
    palletNum: 'Pallet Num',
    staffId: 'Staff Id',
    makeOrder: 'Make Order',
    dms: 'Dms',
    itemProject: 'Item Project',
    vin: 'Vin',
    smallModelType: 'Small Model Type',
    mainMaterialCode: 'Main Material Code',
    materialColor: 'Material Color',
    materialSize: 'Material Size',
    shaftProcNum: 'Shaft Proc Num',
    setDate: 'Set Date',
    setMarks: 'Set Marks',
    preSetInBut: 'Pre Set Out',
    setInBut: 'Set Out'
  },
  stationdevice: { /* 工位设备对应关系 */
    creationDate: 'Creation Date',
    stationDeviceId: 'Station Device Id',
    workCenterCode: 'Work Center Code',
    prodLineCode: 'Prod Line Code',
    stationCode: 'Station Code',
    stationDeviceType: 'Station Device Type',
    deviceCode: 'Device Code',
    deviceDes: 'Device Des',
    deviceSectionCode: 'Device Section Code',
    enableFlag: 'Enable Flag'
  },
  recipequality: { /* 工位数据采集定义 */
    creationDate: 'Creation Date',
    qualityId: 'Quality Id',
    stationId: 'Station Id',
    qualityFrom: 'Quality From',
    tagId: 'Tag Id',
    tagQualitySignId: 'Tag Quality Sign Id',
    groupOrder: 'Group Order',
    groupName: 'Group Name',
    tagColOrder: 'Tag Col Order',
    tagColInnerOrder: 'Tag Col Inner Order',
    qualityFor: 'Quality For',
    tagDes: 'Tag Des',
    tagUom: 'Tag Uom',
    theoryValue: 'Theory Value',
    downLimit: 'Down Limit',
    upperLimit: 'Upper Limit',
    saveOrNot: 'saveOrNot',
    enableFlag: 'Enable Flag'
  },
  screenset: { /* 大屏基础 */
    creationDate: 'Creation Date',
    screenSetId: 'Screen Set Id',
    screenSetType: 'Screen Set Type',
    screenSetCode: 'Screen Set Code',
    screenSetDes: 'Screen Set Des',
    screenSetUrl: 'Screen Set Url',
    screenSetUrlDes: 'Screen Set Url Des',
    orderNum: 'Order Num',
    switchTime: 'Switch Time',
    enableFlag: 'Enable Flag'
  },
  screensetcontent: { /* 大屏内容 */
    creationDate: 'Creation Date',
    prodLineCode: 'Prod Line Code',
    todayPlan: 'Today Plan',
    currentOffline: 'Current Offline',
    finishRate: 'Finish Rate',
    jphActual: 'Jph Actual',
    deviceMobility: 'Device Mobility',
    todayStop: 'Today Stop',
    aboveShow: 'Above Show',
    belowShow: 'Below Show',
    backgroundImage: 'Background Image',
    enableFlag: 'Enable Flag'
  },
  andonbtn: { /* 安灯按钮基础 */
    creationDate: 'Creation Date',
    andonBtnId: 'Andon Btn Id',
    stationId: 'Station Id',
    andonType: 'Andon Type',
    andonDes: 'Andon Des',
    andonColor: 'Andon Color',
    btnLevel: 'Btn Level',
    action: 'action',
    btnTagId: 'Btn Tag Id',
    lineStopTagId: 'Line Stop Tag Id',
    lineStopApi: 'Line Stop Api',
    andonMusicId: 'Andon Music Id',
    andonVoiceIdList: 'Andon Voice Id List',
    andonLimitTime: 'Andon Limit Time',
    andonBtnStatus: 'Andon Btn Status',
    attribute1: 'Attribute1',
    attribute2: 'Attribute2',
    attribute3: 'Attribute3',
    enableFlag: 'Enable Flag'
  },
  andoncard: { /* 安灯工位牌基础 */
    creationDate: 'Creation Date',
    andonCardId: 'Andon Card Id',
    stationId: 'Station Id',
    andonCardType: 'Andon Card Type',
    andonCardColor: 'Andon Card Color',
    cardTagId: 'Card Tag Id',
    andonCardStatus: 'Andon Card Status',
    andonCardCode: 'Andon Card Code',
    enableFlag: 'Enable Flag'
  },
  andoncontrol: { /* 安灯工位状态控制基础 */
    creationDate: 'Creation Date',
    andonControlId: 'Andon Control Id',
    stationId: 'Station Id',
    andonControlName: 'Andon Control Name',
    andonCardValue: 'Andon Card Value',
    andonCardTagId: 'Andon Card Tag Id',
    andonMusicId: 'Andon Music Id',
    lineStopTagId: 'Line Stop Tag Id',
    lineStopApi: 'Line Stop Api',
    attribute1: 'Attribute1',
    attribute2: 'Attribute2',
    attribute3: 'Attribute3',
    enableFlag: 'Enable Flag'
  },
  andonevent: { /* 安灯事件 */
    creationDate: 'Creation Date',
    andonEventId: 'Andon Event Id',
    workCenterCode: 'Work Center Code',
    prodLineCode: 'Prod Line Code',
    stationName: 'station Name',
    stationCode: 'Station Code',
    stationDes: 'Station Des',
    lineSectionCode: 'Line Section Code',
    andonBtnId: 'Andon Btn Id',
    andonType: 'Andon Type',
    andonDes: 'Andon Des',
    andonLimitTime: 'Andon Limit Time',
    happenDate: 'Happen Date',
    resetDate: 'Reset Date',
    resetFlag: 'Reset Flag',
    costTime: 'Cost Time',
    overTimeFlag: 'Over Time Flag',
    enableFlag: 'Enable Flag'
  },
  andonvoice: { /* 安灯音响基础 */
    creationDate: 'Creation Date',
    andonVoiceId: 'Andon Voice Id',
    andonVoiceSerial: 'Andon Voice Serial',
    andonVoiceDes: 'Andon Voice Des',
    andonVoiceAttr: 'Andon Voice Attr',
    btnTagId: 'Btn Tag Id',
    btnLevel: 'Btn Level',
    enableFlag: 'Enable Flag'
  },
  andonmusic: { /* 安灯音响音乐 */
    creationDate: 'Creation Date',
    andonMusicId: 'Andon Music Id',
    andonMusicDes: 'Andon Music Des',
    andonMusicPath: 'Andon Music Path',
    andonMusicAttr: 'Andon Music Attr',
    enableFlag: 'Enable Flag'
  },
  haCarTypeRoute: { /* 焊装车型工艺路线维护 */
    haCarTypeDes: 'Car Type：',
    productionLine: 'Production Line：',
    productionLinet: 'Production Line',
    carType: 'Car Type',
    onlineStation: 'Online Station',
    routeStation: 'Route Station',
    attribute1: 'Attribute1',
    attribute2: 'Attribute2',
    attribute3: 'Attribute3'
  },

  /** ************************************************************/
  /* PCB定义*/

  /** ************************************************************/
  /* DCS定义*/

  // 越南包装项目定义
  vie: {
    // 配方维护
    partNum: 'TVB P/N',
    version: 'version',
    setType: 'SET Type',
    innerType: 'Inner Type',
    pcsType: 'PCS Type',
    plateLen: 'plateLen(mm)',
    plateWid: 'plateWid(mm)',
    plateThi: 'plateThi(mm)',
    plateWei: 'plateWei(g)',
    pcsCodeLen: 'PCS CodeLen',
    setCodeLen: 'SET CodeLen',
    innerCodeLen: 'Inner CodeLen',
    setCase: 'SET Case',
    innerCase: 'Inner Case',
    pcsCase: 'PCS Case',
    index: 'Index',

    ordnumTruBatchRule: 'OrdnumTruBatchRule',
    cusPartNumIntRule: 'CusPartNumIntRule',
    setTruBatchRule: 'SET TruBatchRule',
    setPartNumIntRule: 'SET PartNumIntRule',
    setIntCycRule: 'SET IntCycRule',
    pcsTruBatchRule: 'PCS TruBatchRule',
    pcsPartNumIntRule: 'PCS PartNumIntRule',
    pcsIntSeriaRule: 'PCS IntSeriaRule',
    pcsNumStartNum: 'PCS NumStartNum',
    pcsNumValadd: 'PCS NumValadd',
    // 工单管理
    OriginLotNum: 'OriginLotNum',
    taskSource: 'taskSource',
    taskStatus: 'taskStatus',
    orderNum: 'PO NO.',
    task_type: 'taskType',
    task_status: 'taskStatus',
    cusPartNum: 'cusPartNum',
    planQuantity: 'planQuantity',
    singlePackQua: 'singlePackQua',
    planTime: 'planTime',
    orderStatus: 'orderStatus',
    customCode1: 'customCode',
    orderType: 'orderType',
    okFinishe: 'okFinishe',
    ngFinishe: 'ngFinishe',
    cycle: 'Date code',
    cutBatchNum: 'cutBatchNum',
    intPartNumb: 'intPartNumb',
    taskStarTime: 'taskStarTime',
    taskEndTime: 'taskEndTime',
    taskUserTime: 'taskUserTime',
    operator: 'operator',
    confirmIssuance: 'confirm',
    LWHTHK: 'L/W/H/THK',
    plantCode: 'plant Code',
    salesOrder: 'sales Order',
    salesItem: 'sales Item',
    salesOrg: 'sales Org',
    salesType: 'sales Type',
    customPn: 'custom Pn',
    customPo: 'custom Po',
    customCode: 'custom Code',
    customName: 'custom Name',
    splitLot: 'split Lot',
    splitModel: 'split Model',
    attribute1: 'attribute1',
    attribute2: 'attribute2',
    attribute3: 'attribute3',

    xoutNum: 'XOUTNum',
    xoutType: 'XOUTType',
    quaOfOkBoard: 'quaOfOkBoard',
    quaOfNgBoard: 'quaOfNgBoard',
    passRate: 'passRate',
    numOfComBoards: 'numOfComBoards',
    packComplete: 'packComplete',
    orderStarTime: 'orderStarTime',
    orderUseTime: 'orderUseTime',

    setOrder: 'setOrder',
    judgingTheResult: 'jud Res',
    setCode: 'SETCode',
    setInnerCode: 'Inner Code',
    rotationDirection: 'rot Dir',
    setLevel: 'SET Level',
    stackingPosition: 'stac Pos',
    setFront: 'SET Front',
    setOpposite: 'SET Oppo',
    sortingError: 'sorting Error',
    packOrder: 'pack Order',
    packCode: 'pack Code',
    setNum: 'set Num',
    IsItATrunk: 'Is It A Trunk',
    packError: 'pack Error',
    cus2D1: 'cusQRC1',
    cus2D2: 'cusQRC2',
    inner2D: 'SETnum',
    xout_mapping_result: 'Mapping',
    InnerOrder: 'Inner Order',
    InnerLevel: 'Inner Level',
    InnerFront: 'Inner Front',
    InnerOpposite: 'Inner Oppo',

    equAllPLCIss: 'equ All PLC Iss',
    triColorLight: 'tri Color Light',
    lineScan: 'line Scan',
    printer: 'printer',
    employeeID: 'employee Name',
    name: 'name',
    department: 'department',
    classes: 'classes',
    login: 'login',
    logOut: 'logOut',
    viewDetails: 'view Details',
    NGmulCod: 'NG-mulCod',
    OKPosition: 'OKPosition',
    NGPosition: 'NGPosition',
    rotate: 'rotate',
    NORotate: 'NO Rotate',
    Yes: 'Yes',
    NO: 'NO',
    cancel: 'cancel',
    close: 'close',
    employeeLogin: 'User Login',
    messageAlert: 'message Alert',
    workOrder: 'work Order',
    run: 'run',
    alarm: 'alarm',
    stop: 'stop',
    preserve: 'preserve',
    mecFai: 'mec Fai',
    repair: 'repair',
    queryException: 'query Exception',
    loginSuccess: 'login Success',
    operationException: 'operation Exception',
    AreYouSureToLogOut: 'Are You Sure To LogOut？',
    AreYouSureDelete: 'Are you sure to delete this data?',
    prompt: 'prompt',
    determine: 'determine',
    qualifiedQuantity: 'qualified Quantity',
    unqualifiedQuantity: 'unqualified Quantity',
    quantity: 'quantity',
    cnneSuccess: 'cnne Success',
    cnneFailed: 'cnne Failed',
    connDisconRecon: 'conn Discon Recon。。。',
    PLCCommunInter: 'PLC CommunInter',
    lineScanCommunInter: 'line Scan CommunInter',
    printerCommunInter: 'printer CommunInter',
    MESCommunInterMES: 'MES CommunInterMES',
    pleaseStartMonitor: 'please Start Monitor',
    noIDFound: 'no ID Found',
    save: 'save',
    TotalNumberOfPackages: 'Total number of packages',
    TotalNumberOfPieces: 'Total number of pieces',
    MaximumNumberOfFullPackages: ' Maximum number of full packages',
    TrayMaterialCode: 'Tray material code',
    TrayDiskCapacity: 'Tray disk capacity ',
    SortingComparisonRuleSetting: 'Sorting comparison rule setting ',
    // 包装set表
    pileBarcode: 'pile Barcode',
    arrayIndex: 'array Index',
    boardSn: 'board Sn',
    arrayLevel: 'array Level',
    arrayFrontLevel: 'array Front Level',
    arrayBackLevel: 'array Back Level',
    setFrontReadingCodeTime: 'Front Code Time',
    setReverseCodeReadingTime: 'Reverse Code Time',
    arrayMark: 'array Mark',
    arrayBdCount: 'array BdCount',
    boardResult: 'board Result',
    depositPosition: 'deposit Position',
    xoutFlag: 'xout Flag',
    xoutSetNum: 'xout Set Num',
    xoutActNum: 'xout Act Num',
    arrayNgCode: 'array Ng Code',
    arrayNgMsg: 'array Ng Msg',
    arrayFrontInfo: 'array Front Info',
    arrayBackInfo: 'array Back Info',
    arrayInnerLevel: 'array Level',
    arrayInnerBdCount: 'array BdCount',
    arrayInnerNgCode: 'array Ng Code',
    arrayInnerNgMsg: 'array Ng Msg',
    arrayInnerFrontInfo: 'array Front Info',
    arrayInnerBackInfo: 'array Back Info',
    upFlag: 'up Flag',
    upNgCode: 'up Ng Code',
    pileUseFlag: 'pile Use Flag',
    unbindFlag: 'unbind Flag',
    unbindUser: 'unbind User',
    unbindTime: 'unbind Time',
    unbindWay: 'unbind Way',
    pileIndex: 'pile Index',
    arrayCount: 'array Count',
    pileWeight: 'pile Weight',
    pileUser: 'pile User',
    pcArraylist: 'pc Array list',
    plcArraylist: 'plc Array list',
    pileStatus: 'pile Status',
    pileFrontLevel: 'pile Front Level',
    pileBackLevel: 'pile Back Level',
    pileFrontReadingCodeTime: 'pile Front Reading Code Time',
    pileReverseCodeReadingTime: 'pile Reverse Code Reading Time',
    pilengCode: 'pileng Code',
    pile_ng_msg: 'pile_ng_msg',
    dailyPassRate: 'dailyPassRate',
    dailyOKQuantity: 'dailyOKQuantity',
    dailyNGQuantity: 'dailyNGQuantity',
    dailyHourProduct: 'dailyHourProduct',
    dailyHourProductVal: 'dailyHourProductVal',
    dailyHourProductAve: 'dailyHourProductAve',
    arrayInnerCount: 'array Count',
    pcArrayInnerlist: 'pc Array list',
    plcArrayInnerlist: 'plc Array list',
    form: 'form',
    start: 'start',
    locat: 'locat',
    cut: 'cut',
    charac: 'charac',
    formulaMainten: 'formula Mainten',
    cannotBeEmptyOrAPositiveInteger: 'cannot BeEmpty Or A Positive Integer',
    otherValuesCannotBeEmpty: 'other Values Cannot BeEmpty',
    editSuccess: 'edit Success',
    effective: 'effective',
    invalid: 'invalid',
    what: 'what？',
    changeTo: 'change To',
    Cancellation: 'Cancellation',
    TaskInit: 'TaskInit',
    scan: 'scan',
    scanOrdNumMESTask: 'scanLot NoMESTask：',
    select: 'select',
    success: 'success',
    PleaseSelect: 'Please Select At Least One Item!',
    conSel: 'Confirm Selected',
    Piece: 'Piece of data?',
    SuccessIss: 'Successfully issued',

    pcsCode: 'PCS Code',
    PCSReverse: 'PCS Reverse',
    PCSFront: 'PCS Front',
    PCSOrder: 'PCS Order',
    PCSLevel: 'PCS Level',
    LineScanPCSResults: 'Line Scan PCS Results',
    PCSSortingNGCode: 'PCS Sorting NG Code',
    PCSSortingNGMsg: 'PCS Sorting NG Msg',
    PCSStatus: 'PCS Status',
    whetherXOUT: 'whether XOUT',
    time: 'time',
    setStatus: 'set Status',
    InnerStatus: 'Inner Status',
    export: 'export',
    setOutside: 'set Outside',
    packInformation: 'Packaging formula maintenance information',

    unbind: 'Unbind',
    manual: 'Manual',
    masterLot: 'master Lot Status', // 母批状态
    targetQuantity: 'target Quantity',
    completed: 'completed', // 完成数量
    NGQuantity: 'NG Quantity', // NG数量
    checkplans: 'check plans', // 首检计划数量
    checksCompleted: 'checks Completed', // 首检完成数量
    platingplates: 'plating plates', // 陪镀板数量
    plates: 'plates', // 分盘数量
    area: 'area', // 面次
    MaxNumber: 'Max Number', // Tray盘最大使用次数
    orderParameters: 'order Parameters', // 工单参数集合
    selectedTaskData: 'Confirm to delete the selected task data ?', // 确认删除选中的任务数据?
    confirmUnbindAllSetTips: 'Are you sure you want to unbind all set of this pile?',
    confirmUnbindAllBdTips: 'Are you sure you want to unbind all board of this set?',
    BoardNumber: 'Board Number', // 板子序号
    BarCode: 'Bar Code', // 板子条码
    Features: 'Comparison of features' // 功能对比
  },
  interfaceLogs: {
    time: ' time',
    interName: 'inter Name',
    interDes: 'inter Des',
    sourceSystem: 'source System',
    targetSystem: 'target System',
    requestParams: 'request Params',
    responseParams: 'response Params',
    successFlag: 'whether Success',
    ESBinterName: 'ESBinter Name',
    ESBinterDes: 'ESBinter Des',
    ESBinterAddress: 'ESBinter Address',
    interMeans: 'inter Means',
    startTime: 'startTime',
    endTime: 'endTime',
    costTime: 'request Duration',
    InterMessages: 'Inter Messages',
    parasList: 'cust Params',
    requestInfo: 'request Messages',
    remark: 'remark',
    total: 'total',
    pre: 'pre',
    next: 'next',
    current: 'current',
    page: 'page',
    paramsDetails: 'params Details',
    pleaseEnter: 'please Enter',
    Topped: 'Topped',
    bottomSet: 'bottomSet',
    ExportFailed: 'ExportFailed',
    serialNumber: 'serial Number'
  },
  scadaAlarmReport: {
    title: 'SCADA Alarm Report',
    prodLine: 'Production Line',
    station: 'Station',
    instanceCode: 'Instance Code',
    alarmCode: 'Alarm Code',
    alarmDesc: 'Alarm Description',
    resetFlag: 'Reset Status',
    resetYes: 'Reset',
    resetNo: 'Not Reset',
    time: 'Time',
    startTime: 'Start Time',
    endTime: 'End Time',
    instanceDesc: 'Instance Description',
    alarmLevel: 'Alarm Level',
    alarmTime: 'Alarm Time',
    resetTime: 'Reset Time',
    isSimulated: 'Simulated',
    simulatedYes: 'Yes',
    simulatedNo: 'No',
    tag: 'Tag',
    export: 'Export',
    search: 'Search',
    reset: 'Reset',
    totalCount: 'Total Count',
    currentPage: 'Current Page',
    previousPage: 'Previous Page',
    nextPage: 'Next Page',
    page: 'Page',
    top: 'Top',
    bottom: 'Bottom',
    selectProdLineAndStation: 'Please select production line and station',
    queryException: 'Query Exception',
    exportFailed: 'Export Failed',
    serialNumber: 'serialNumber'
  },
  messageReport: {
    cim_from: 'cim from',
    finish_flag: 'finish flag',
    screen_control: 'screen control',
    screen_code: 'screen code',
    cim_msg: 'cim msg',
    selectStation: 'Please select a workstation'
  },
  diagnosis: {
    client_code: 'client code',
    link_status: 'link status',
    link_message: 'link message',
    creationDate: 'creationDate',
    table: 'table',
    simulated_flag: 'simulated flag',
    curved: 'curved',
    abnormal: 'abnormal',
    link_normal: 'link normal',
    link_abnormal: 'link abnormal',
    scadaFaild: 'Scada instance basic data query failed:',
    scadaError: 'Scada instance basic data query timeout or SQL error ',
    link_analysis: 'link analysis',
    sampleSize: 'sample Size',
    statusValue: 'status Value',
    abnormalStatistics: 'abnormal Statistics',
    frequency: 'Network outage frequency',
    exception: 'Query selCellIP exception',
    instance: 'Please select an instance',
    UnableNumber: 'Unable to obtain unit IP and port number',
    NoData: 'Reached the top with no data',
    scadaQueryFailed: 'Scada communication report query failed',
    scadaSqlError: 'Scada communication report query timeout or SQL error',
    serialNumber: 'serial Number:',
    time: 'time:',
    messageDes: 'messageDes:',
    NoDataAvailable: 'No Data Available'
  },
  proMonitor: {
    processTasks: 'process Tasks:',
    total: 'total:',
    dayQuantity: 'dayQuantity:',
    dayNormal: 'dayNormal:',
    dayAbnormal: 'dayAbnormal',
    cancel: 'cancel',
    view: 'view',
    time: 'time：',
    info: 'info：',
    step: 'step：',
    log: 'log：',
    queryException: 'queryException',
    cancelSucess: 'cancelSucess:',
    abnormal: 'abnormal:'
  },
  header: {
    user: 'user：',
    onlineDuration: 'online Duration：',
    min: 'min',
    signOut: 'sign Out',
    lockScreen: 'lock Screen',
    screen: 'screen',
    stationSelect: 'station Select',
    greenNewEnergy: 'GREEN NEW ENERGY',
    loginPassword: 'Please enter your login password',
    unlock: 'unlock',
    exitSystem: 'Are you sure to log out and exit the system?',
    passwordUnlock: 'Please enter the login password to unlock',
    passwordError: 'Password error, please re-enter',
    unlockingSuccessful: 'unlocking Successful',
    error: 'error',
    usernameRequire: 'The username cannot be empty',
    passwordRequire: 'Password cannot be empty',
    roleRequire: 'role cannot be empty',
    codeRequire: 'The verification code cannot be empty'
  },
  hmiMain: {
    onLine: 'onLine', // 在线
    offline: 'offline', // 脱机
    prot1: 'prot1', // 端口1
    prot2: 'prot2', // 端口2
    protAgv1: 'AGV(1)', // AGV1
    protAgv2: 'AGV(2)', // AGV2
    enable: 'enable', // 启用
    disabled: 'disabled', // 禁用
    manual: 'manual', // 手动画面
    eapPing: 'eapPing', // Ping-EAP
    reOnline: 'reOnline', // 收板机在线
    CCD: 'Plate CCD',
    PlateCCD2: 'Plate CCD2',
    CCD1: 'Vehicle CCD1',
    CCD2: 'Vehicle CCD2',
    DownStatus: 'DownStatus',
    upStatus: 'upStatus',
    petName: 'petName',
    processQuantity: 'processQuantity',
    importTask: 'importTask',
    productionTask: 'productionTask',
    vehicleCode: 'vehicleCode',
    plannedQuantity: 'plannedQuantity',
    okQuantity: 'OKQuantity',
    noReadQuantity: 'NoReadQuantity',
    shortQuantity: 'ShortQuantity',
    leftNoReadCount: 'LeftNoReadQuantity',
    wipManualReport: 'ScanBarCode',
    cancelWip: 'NoRead Wip',
    endWip: 'Finish Wip',
    entranNum: 'entranNum',
    exportNum: 'exportNum',
    productionTime: 'productionTime',
    occurrenTime: 'occurrenTime',
    alarmLevel: 'alarmLevel',
    alarmMsg: 'alarmMsg',
    parameterName: 'parameterName',
    company: 'company',
    setValue: 'setValue',
    actualValue: 'actualValue',
    employee: 'employee', // 员工号
    name: 'name', // 姓名
    department: 'department', // 部门
    status: 'status', // 机台状态
    EAP: 'EAP', // EAP远程
    AIS: 'AIS', // AIS本地
    production: 'production', // 生产模式
    transport: 'transport', // 搬运模式
    plate: 'plate', // 板件模式
    panel: 'With Panel', // 有Panel
    NoPanel: 'No Panel', // 无Panel
    more: 'more', // 更多
    work: 'Job port', // 作业端口
    Login: 'Login', // 登入
    LogOut: 'LogOut', // 登出
    prot1Step: 'prot 1 Step', // 端口1步序
    prot1info: 'prot 1 info', // 端口1消息
    forced: 'forced', // 强制退载具
    prot2Step: 'prot 2 Step', // 端口2步序
    prot2info: 'prot 2 info', // 端口2消息
    portStatus: 'port Status', // 端口状态
    carrierStatus: 'carrier Status', // 载具状态
    firstEdition: 'first Edition', // 是否首板
    complete: 'finish/plan', // 完工/计划
    firstPlan: 'inspect finish/plan', // 首板完工/计划
    workMode: 'work Mode', // 作业模式
    LWH: 'LWH', // 长宽高
    vehicle: 'Vehicle/canopy', // 载具/天盖
    Tray: 'Tray tray code', // Tray盘码
    plateCode: 'plate Code', // 板件码
    fmale: 'Female', // 母批号
    blowOff: 'blow-off', // 放口,
    plan: 'plan', // 计划
    put: 'put', // 已放
    putStatus: 'Discharge', // 放状态
    panelStatus: 'Status', // 板件状态
    received: 'received', // 已收,
    close: 'close', // 收口
    receivingState: 'Receiving', // 收状态
    SupportBrowning: 'Support Browning', // 配套棕化
    SupportPunching: 'Support Punching', // 配套冲孔
    serial: 'index', // 序号
    firstPiece: 'firstPiece', // 首件
    abnormal: 'abnormal', // 异常
    time: 'time', // 时间
    sideboard: 'sideboard', // 陪板
    vehicleInput: 'Vehicle input', // 载具输入
    manualBatch: 'Manual batch report', // 手工批次上报
    localTask: 'Local task increment', // 本地任务增加
    firstCheck: 'First check decision', // 首检判定
    panelInput: 'Panel input', // Panel输入
    panelJudge: 'Panel Judge', // Panel判定
    confirmation: 'Batch confirmation', // 切批确认
    CIMMessage: 'CIM message', // CIM消息
    dailyOnline: 'Daily online', // 日在线生产
    dailyOffLine: 'Daily off-line', // 日脱机生产
    procedure: 'The current login information cannot be obtained. Procedure', // 获取当前登录信息异常
    side: 'The right side', // 正面
    reverse: 'Reverse side', // 反面
    productionBoard: 'Production board', // 生产板
    dummy: 'Dummy',
    have: 'There are', // 有
    isNo: 'There is no', // 无
    logoutSuccess: 'Logout success', // 登出成功
    workOrderEntry: 'Work order entry', // 工单输入
    clickOK: 'Batch needs to switch the main process formula, please confirm the switch is completed and click OK', // 批次需要切换主制程配方,请确认切换完成后点击确定
    palletInput: 'Pallet/Tray input', // Pallet/Tray输入
    mixedBatch: 'Mixed batch confirmation', // 混批确认
    forValidation: 'Manually scan the task and submit it to EAP for validation', // 人工扫描任务并提交到EAP验证
    forFinish: 'Manual batch reporting', // 手动结批上报
    forFinish2: 'Manual WipFinish', // 手动结批上报
    Identify: 'Identify port', // 确定对端口
    mandatory: 'Are vehicle unloader operations mandatory?', // 进行强制退载具操作吗？
    machineProduction: 'Status modification is not allowed during the current machine production', // 当前机台生产中,不允许状态修改
    cancelFlowchart: 'Cancel flowchart successfully', // 取消流程图成功
    cancelException: 'Cancel flowchart exception', // 取消流程图异常
    abnormalError: 'Query point data exception:', // 查询点位数据异常
    EAPCommunication: 'EAP communication failure', // EAP通讯中断
    PLCCommunication: 'PLC communication failure', // PLC通讯中断
    CCDinterrupted: 'Board reading CCD communication is interrupted', // 板件读码CCD通讯中断
    CCD1Interrupted: 'Vehicle 1 read CCD communication is down', // 载具1读码CCD通讯中断
    CCD2Interrupted: 'Vehicle 2 read CCD communication is down', // 载具2读码CCD通讯中断
    startTime: 'star tTime', // 开始时间
    endTime: 'end Time', // 结束时间,
    implement: 'implement ',
    implementUnLoad: 'actualRecv',
    scanConfirmation: 'work order confirmation ',
    inSeconds: 'Interface times out after seconds ',
    BOXInput: 'BOX input ',
    deviceInit: 'initialization ',
    deviceRun: 'Run ',
    deviceStop: 'Stop ',
    deviceIdle: 'standby ',
    deviceDown: 'Stop ',
    devicePm: 'Maintenance'

  },
  workOrder: {
    workOrderNumber: 'workOrdNum',
    processCode: 'process Code',
    productUse: 'product Use',
    batchNumber: 'batch Num',
    materialNumber: 'mater Num',
    NumbeOfPlates: 'Num Of Plates',
    platSe: 'plateSe',
    flip: 'flip',
    parameterInfo: 'parameter Info'
  },
  productionTasks: {
    masterStatus: 'master Status',
    subbatchState: 'subbatch State',
    methodCompletion: 'method Comple',
    normalFinish: 'normal Finish',
    lessBoard: 'less Board',
    boardsFinish: 'boards Finish',
    ForcedFinish: 'Forced Finish',
    viewPNL: 'PNL',
    PNLlist: 'PNL list',
    deleteTask: 'delete Task',
    FinishedCondition: 'Finished Condition'
  },
  stationParameter: {
    stationConfiguration: 'Station parameter configuration',
    theSettings: 'If no corresponding IP address is found, check the Settings',
    theReceiver: 'The IP address of the receiver is not found. Set the IP address of the receiver',
    notEnabled: 'Get trigger retracting monitor is not enabled',
    interfaceCode: 'Interface Code',
    interDes: 'inter Des',
    NGDegree: 'NG Degree',
    synchronization: 'synchronization',
    continuation: 'continuation',
    enable: 'enable',
    cutOnline: 'The board placement machine is offline and cannot be cut online',
    onlineMode: 'Not operable in online mode'
  },
  // 广合
  guanghe: {
    parmas: 'Parameters', // 参数
    finish: 'finish', // 完成
    plateNG: 'plateNG', // 板件NG数量
    trayNG: 'trayNG', // 托盘NG数量
    trayOK: 'trayOK', // 托盘OK数量
    panelNG: 'panelNG', // 面板NG数量
    panelOK: 'panelOK', // 面板OK数量
    deviceOee: 'deviceOee', // 设备OEE
    OkQuantity: 'OkQuantity', // OK数量
    NGQuantity: 'NGQuantity', // NG数量
    total: 'total', // 总量
    NGPass: 'NGPass', // NG_PASS数量
    OfflineCodeReading: 'Offline Read', // 离线读码量
    OnlineCodeReading: 'Online Read', // 在线读码量
    readBitRate: 'read Rate', // 读码率
    port1Return: 'port1Return', // 端口1退载具
    port2Return: 'port2Return', // 端口2退载具
    port3Return: 'port3Return', // 端口3退载具
    port4Return: 'port4Return', // 端口4退载具
    exitSystem: 'exit', // 退出系统
    logout: 'logout', // 员工登出
    energyDetail: 'energy Detail',
    timeTaskData: 'time Task Data'
  },
  // 定颖
  dy: {
    auto: 'auto',
    offLine: 'offLine',
    local: 'Local',
    semiAuto: 'semiAuto',
    model: 'model',
    Semi_Auto: 'Semi-Auto',
    Auto: 'Auto'
  },
  // 维信
  wx: {
    recipeName: 'Recipe Name',
    downRecipe: 'Scan to Download Recipe:',
    editParameters: 'Modify Parameters:',
    import: 'Import',
    export: 'Export',
    equipSelfTest: 'Device self-test',
    recipeType: 'Recipe type',
    recipeDesc: 'Recipe Desc',
    lotNo: 'Batch barcode',
    version: 'Version',
    down: 'Download',
    upload: 'Upload',
    distributed: 'Distribute',
    equipSelfTestInfo: 'Equipment self-test',
    waitUpload: 'There is already an upload task, please wait for the upload to complete',
    uploadFormula: ' Trigger the upload recipe process',
    confirmDelete: ' Confirm to delete the selected ',
    articleData: 'Data',
    SuccessfullyMes: ' Upload MES successfully ',
    selected: ' Confirm to upload the selected ',
    uploadTask: 'There is already an upload task, please wait for the upload to complete',
    trigger: 'Trigger the process of uploading formulas',
    confirmRecipe: 'Confirm uploading the selected recipe',
    alreadyUploadTask: 'There is already an upload task, please wait for the upload to complete',
    confirmDownload: 'Confirm downloading the recipe from the upper system',
    pleaseWait: 'There is already a download task, please wait for the download to complete',
    triggerDownload: 'Trigger the download recipe process',
    IssuedEquipment: 'Confirm Distribute Recipe to Equipment?',
    alreadyExistsTask: 'There is already a task to be issued, please wait for it to be completed',
    SuccessfullyIssued: 'Successfully issued formula to equipment',
    selectOne: 'Please select a piece of data!',
    exportName: 'Confirm the export formula name as',
    recipeDownload: 'recipe Download',

    comfirmReset: 'Are you sure you want to reset the process?',
    resetSuccess: 'Process reset successful',
    currently: 'The material has not been obtained yet. Please maintain the material first',
    queryFailed: 'Process reset failed',
    failedmaterial: 'Failed to match the current material number',
    FoundRecipe: 'Relevant recipe information found',
    pleaseScan: 'Please scan or enter the work order number first',
    reset: 'Reset',
    lotNum: 'lotNum',
    batchCheck: 'Batch Check',
    employeeID: 'Employee',
    productInfo: 'Production Info',
    alarmMsg: 'Alarm Message',
    alarmTime: 'Alarm Time',
    alarmLevel: 'Alarm Level',
    alarmCode: 'Alarm Code',
    alarmDesc: 'Alarm Description',
    feedbackInfo: 'Feedback Info',
    userInfo: 'User Login',
    equipmentInfo: 'Equipment Self-Test',
    productResult: 'Product Login',
    otherInfo: 'Other Info',
    panelInfo: 'panel Info',
    paramInfo: 'param Info',
    statusInfo: 'status Info',
    producrParameter: 'Product Params',
    projectName: 'Project Name',
    currentValue: 'current value',
    state: 'Status',
    unit: 'Unit',
    upperLimit: 'Upper Limit',
    lowerLimit: 'Lower Limit',
    modifyFormula: 'Modify Formula',
    thickness: 'Plug hole machine plate thickness',
    modifiyPrams: 'Do you want to modify parameters',
    parameterCode: 'Parameter Code',
    parameterDesc: 'Parameter Description',
    parameterValue: 'Parameter Value',
    cancel: 'Cancel',
    confirm: 'Confirm Distribution',
    plcHeartbeat: 'PLC Heartbeat',
    eapHeartbeat: 'EAP Heartbeat',
    upstream: 'Upstream',
    downstream: 'Downstream',
    electricity: 'Electricity',
    productionMode: 'Mass Production Mode',
    firstArticleMode: 'First Article Mode',
    dummyMode: 'Dummy Mode',
    stationCode: 'Station Code',
    capacity: 'Capacity',
    readBitRate: 'Barcode Reading Rate',
    onDuty: 'On Duty',
    leave: 'Off Duty',
    enterCode: 'Please Input Material Code',
    enterDesc: 'Please Input Material Description',
    hole: 'Select Hole Filling +',
    selectSupface: 'Select Surface +',
    materialMaintenance: 'Material Maintenance',
    equipment: 'Equipment No.',
    scanDownload: 'Scan to Download Recipe:',
    addParams: 'Add Parameters',
    scadaRequest: 'Initiate SCADA Recipe Request, Recipe Downloading',
    scadaError: 'Initiate SCADA Recipe Request, Recipe Download Exception',
    uploadSuccessful: 'Upload Successfully',
    downLoadRecipe: 'Confirm to Distribute This Recipe?',
    subDetail: 'Sub-Recipe Maintenance Details',
    modificationFailed: 'Modification Failed'
  },
  // 福建瑞闽
  fjrm: {
    wasteBoxCode: 'waste box',
    wasteBoxDes: 'des',
    height: 'height',
    rgvCode: 'rgv code',
    rgvDes: 'rgv des',
    wharfCode: 'wharf code',
    wharfDes: 'wharf des',
    wharfType: 'wharf type',
    locationX: 'location x',
    locationY: 'location y',
    locationZ: 'location z',
    wharfOrder: 'wharf order',
    wharfTag: 'wharf tag',
    lockFlag: 'lock flag',
    lotNo: 'lot no',
    si: 'si',
    fe: 'fe',
    cu: 'cu',
    mn: 'mn',
    mg: 'mg',
    ni: 'ni',
    zn: 'zn',
    ti: 'ti',
    cr: 'cr',
    na: 'na',
    ca: 'ca',
    taskNum: 'task num',
    taskFrom: 'task from',
    taskWay: 'task way',
    taskType: 'task type',
    wareHouse: 'ware house',
    fromStockCode: 'from stock code',
    toStockCode: 'to stock code',
    lotNum: 'lot num',
    materialCode: 'material code',
    width: 'width',
    errorMin: 'error min',
    errorMax: 'error max',
    executeWidth: 'execute width',
    taskOrder: 'task order',
    taskStatus: 'task status',
    enableFlag: 'enable flag'
  }
}
