<template>
  <div class="mesContainer">
    <div style="height: 65px">
      <table style="width: 100%; border: 0px">
        <tr v-for="item in andonTypeList" :key="item.andon_station_type_id">
          <td class="tdFirst">
            <el-button
              class="buttonItem buttonFirst"
              size="medium"
              type="primary"
              plain
            >
              {{ item.andon_type_des }}
            </el-button>
          </td>
          <td style="width: 20px">
            <el-button
              class="buttonLeft"
              size="medium"
              :style="'background-image:url(' + leftArrow + ');'"
              @click="moveLeft(item, $event)"
            />
          </td>
          <td class="tdScrollbar">
            <el-scrollbar ref="scrollbar" style="width: 100%">
              <div class="leftStyle" :style="'width: 400%;'">
                <el-checkbox-group v-model="checkboxGroup[item.andon_station_type_id]" size="medium" :border="true">
                  <el-checkbox-button v-for="btn in item.details" :key="btn.andon_station_type_i_id" :class="btn.andon_i_status === 'CALL'?'yellowActive':'grayActive'" :label="btn.andon_station_type_i_id">{{ btn.andon_station_type_i_des }}</el-checkbox-button>
                </el-checkbox-group>
                <!-- <el-button
                  v-for="(btn,index) in item.details"
                  :key="btn.andon_station_type_i_id"
                  class="buttonItem"
                  :class="btn.andon_i_status === 'CALL'?'yellowActive':'grayActive'"
                  size="medium"
                  type="primary"
                  plain
                  @click="selectedAndonType(btn,index)"
                >
                  {{ btn.andon_station_type_i_des }}
                </el-button> -->
              </div>
            </el-scrollbar>
          </td>
          <td style="width: 20px">
            <el-button
              class="buttonRight"
              size="medium"
              :style="'background-image:url(' + rightArrow + ');'"
              @click="moveRight(item, $event)"
            />
          </td>
          <td class="tdLast">
            <el-button
              class="buttonOne buttonItem"
              size="medium"
              type="primary"
              :disabled="false"
              plain
              @click="clickCallBtn(item,'CALL')"
            >
              呼叫
            </el-button>
            <el-button
              class="buttonTwo buttonItem"
              size="medium"
              type="primary"
              plain
              @click="clickCallBtn(item,'RESET')"
            >
              复位
            </el-button>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import { mesHmiStationAndonTypeSel, mesHmiStationAndonTypeUpd } from '@/api/mes/core/hmi/stationAndon'
import Cookies from 'js-cookie'
import leftArrow from '@/assets/images/leftArrow.png'
import rightArrow from '@/assets/images/rightArrow.png'
export default {
  name: 'MES_STATION_ANDON',
  // 数据字典
  // dicts: ['PLAN_MO_STATUS'],
  // 数据模型
  data() {
    return {
      leftArrow: leftArrow,
      rightArrow: rightArrow,
      left: 0,
      andonTypeList: [],
      currentStation: {},
      checkboxGroup: {}
    }
  },
  mounted: function() {

  },
  created: function() {
    this.currentStation = {
      prod_line_id: this.$route.query.prod_line_id,
      prod_line_code: '',
      prod_line_des: '',
      station_id: this.$route.query.station_id,
      station_code: this.$route.query.station_code,
      station_des: ''
    }
    this.getAndonType()
  },
  methods: {
    moveLeft(row, e) {
      row.left = row.left === 0 ? 0 : (row.left += 100)
      e.target.parentNode.nextElementSibling.querySelector('.leftStyle').style.marginLeft = row.left + 'px'
      // if()
    },
    moveRight(row, e) {
      row.left -= 100
      e.target.parentNode.previousElementSibling.querySelector('.leftStyle').style.marginLeft = row.left + 'px'
    },
    // 查询当前工位作业信息
    getAndonType() {
      if (this.currentStation.station_code === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      mesHmiStationAndonTypeSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.andonTypeList = defaultQuery.data
            for (let i = 0; i < this.andonTypeList.length; i++) {
              this.andonTypeList[i].left = 0
              this.$set(this.checkboxGroup, this.andonTypeList[i].andon_station_type_id, [])
              for (let j = 0; j < this.andonTypeList[i].details.length; j++) {
                if (!this.andonTypeList[i].details[j].andon_i_status) {
                  this.andonTypeList[i].details[j].andon_i_status = 'RESET'
                }
              }
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询mesHmiStationAndonTypeSel异常', type: 'error' })
        })
    },
    clickCallBtn(data, btnType) {
      const checked = this.checkboxGroup[data.andon_station_type_id]
      if (!checked || checked.length <= 0) {
        this.$message({ message: '请选择安灯类型后进行呼叫或复位操作', type: 'info' })
        return
      }

      const andonTypeList = []

      const andonType = this.andonTypeList.find(item => item.andon_station_type_id === data.andon_station_type_id)
      for (const item of andonType.details) {
        if (checked.indexOf(item.andon_station_type_i_id) > -1 && item.andon_i_status !== btnType) {
          const d = {
            andon_station_type_i_id: item.andon_station_type_i_id,
            andon_type_code: data.andon_type_code,
            andon_type_des: data.andon_type_des,
            andon_station_type_i_code: item.andon_station_type_i_code,
            andon_station_type_i_des: item.andon_station_type_i_des,
            andon_i_type: item.andon_i_type,
            material_code: item.material_code,
            material_des: item.material_des
          }
          andonTypeList.push(d)
        }
      }

      if (andonTypeList.length <= 0) {
        return
      }

      const params = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        prod_line_id: this.currentStation.prod_line_id,
        station_code: this.currentStation.station_code,
        andonTypeList: andonTypeList,
        btnType: btnType
      }

      console.info(params)
      mesHmiStationAndonTypeUpd(params)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '操作成功', type: 'success' })
            this.checkboxGroup[data.andon_station_type_id] = []
            for (const item of andonType.details) {
              if (checked.indexOf(item.andon_station_type_i_id) > -1 && item.andon_i_status !== btnType) {
                item.andon_i_status = btnType
              }
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '呼叫或复位异常', type: 'error' })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.buttonRight{
    height: 50px;
    margin-top: 5px;
    font-weight: bold;
    margin-left: 0px;
    padding: 0px;
    width: 20px;
    border: 1px solid #b7b7b7;
    background: #ffffff;
    background-size: 60%;
    background-repeat: no-repeat;
    background-position: center;
}
  .buttonItem{
    height: 50px;
    margin-top: 5px;
    font-weight: bold;
    margin-left: 5px;
  }
  .buttonLeft{
    height: 50px;
    margin-top: 5px;
    font-weight: bold;
    margin-left: 0px;
    padding: 0px;
    width: 20px;
    border: 1px solid #b7b7b7;
    background: #ffffff;
    background-size: 60%;
    background-repeat: no-repeat;
    background-position: center;
  }
  .tdScrollbar{
    width: 100%;
  }
  .tdFirst,.tdLast{
    display: flex;
  }
  .buttonFirst{
    background: #ff8001;
    color: #ffffff;
  }
  .buttonFirst:hover{
    background: #e77001;
    border-color: #e77001;
    color: #ffffff;
  }
  .buttonOne{
    background: #fd0100;
    color: #ffffff;
  }
  .buttonOne:hover,.buttonOne:focus{
    background: #da0000;
    border-color: #da0000;
    color: #ffffff;
  }
  .buttonTwo{
    background: #2ecb32;
    color: #ffffff;
  }
  .buttonTwo:hover,.buttonTwo:focus{
    background: #02a107;
    border-color: #02a107;
    color: #ffffff;
  }

  :deep(.el-checkbox-button__inner){
    background: #cccccc;
    margin: 0 5px;
    border-radius: 4px !important;
    border: 1px solid #607d8b;
    padding: 15px 20px !important;
    margin-top: 4px
  }
  :deep(.el-checkbox-button:first-child .el-checkbox-button__inner){
    border: 1px solid #607d8b;
  }
  :deep(.yellowActive){
    .el-checkbox-button__inner{
      color: #ffffff;
      background: #ffc107;
      border-color: #9d7600;
    }
  }
  :deep(.grayActive){
    .el-checkbox-button__inner{
      color: #ffffff;
      background: #b6b6b6;
      border-color: #4c4c4c;
    }
  }
  :deep(.el-checkbox-button.is-checked .el-checkbox-button__inner) {
    color: #000000;
    border: 3px solid #ff0000;
}
  :deep(.el-checkbox-button.is-checked:first-child .el-checkbox-button__inner){
    color: #000000;
    border: 3px solid #ff0000;
  }
</style>
