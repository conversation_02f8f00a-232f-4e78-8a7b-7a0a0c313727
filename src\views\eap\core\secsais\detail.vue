<template>
  <!--模组子维护-->
  <el-card shadow="never">
    <div v-if="recipe_detail_id > 0">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <div v-if="client_des && model_name" class="selected-info">
          <span class="selected-text">{{ client_des }} - {{ model_name }}</span>
        </div>
        <div class="modify-switch">
          <span>{{ $t('core.secsais.modifyParameter') }}</span>
          <el-switch v-model="localDisabled" active-color="#13ce66" inactive-color="#ff4949" @change="handleSwitchChange" />
        </div>
      </div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table
            ref="table"
            v-loading="loading"
            :data="recipeData"
            :height="height"
            style="width: 100%;"
          >
            <el-table-column :show-overflow-tooltip="true" align="center" prop="tag_code" :label="$t('core.secsais.parameterCode')" />
            <el-table-column :show-overflow-tooltip="true" align="center" prop="tag_des" :label="$t('core.secsais.parameterDes')" />
            <el-table-column :show-overflow-tooltip="true" align="center" :label="$t('core.secsais.parameterVal')">
              <template slot-scope="scope">
                <span v-if="scope.row.tag_value !== undefined">{{ scope.row.tag_value }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column v-if="localDisabled" :show-overflow-tooltip="true" align="center" :label="$t('core.secsais.parameterInput')">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.input_value"
                  :placeholder="scope.row.tag_value !== undefined ? scope.row.tag_value : ''"
                  @blur="handleInputBlur(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" :label="$t('core.secsais.parameterUnit')">
              <template slot-scope="scope">
                <span>{{ scope.row.opc_addr || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" :label="$t('core.secsais.parameterLimit')">
              <template slot-scope="scope">
                <span v-if="scope.row.down_limit !== undefined && scope.row.down_limit !== -1.0">{{ scope.row.down_limit }}</span>
                <span v-else>-</span>
                <span> ~ </span>
                <span v-if="scope.row.upper_limit !== undefined && scope.row.upper_limit !== -1.0">{{ scope.row.upper_limit }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <el-pagination
            :current-page.sync="currentPage"
            :page-sizes="[20, 50, 100, 200]"
            :page-size.sync="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </el-row>
    </div>
    <div v-else class="no-recipe-selected">
      <p>{{ $t('core.secsais.noRecipeSelected') }}</p>
    </div>
  </el-card>
</template>

<script>
import recipeApi from '@/api/eap/core/secsais/recipeApi'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import Cookies from 'js-cookie'
import { selCellIP } from '@/api/core/center/cell'

export default {
  name: 'EAP_CORE_SECSAIS_RECIPE_DETAIL',
  props: {
    recipe_detail_id: {
      type: [String, Number],
      default: -1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    tag_code_prefix: {
      type: String,
      default: ''
    },
    client_id: {
      type: [String, Number],
      default: ''
    },
    client_code: {
      type: String,
      default: ''
    },
    tag_group_code: {
      type: String,
      default: ''
    },
    client_des: {
      type: String,
      default: ''
    },
    model_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      loading: false,
      recipeData: [],
      currentPage: 1,
      pageSize: 100,
      total: 0,
      sort: 'tag_code asc',
      // 本地修改状态，避免直接修改prop
      localDisabled: false,
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mesClientTopicList: [], // 已订阅的主题列表
      optionsMqtt: {
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        cleansession: false
      },
      cellIp: '', // 单元IP
      mqttPort: '' // MQTT端口号
    }
  },
  watch: {
    // Use a combined watcher to prevent duplicate API calls
    '$props': {
      handler(newProps, oldProps) {
        if (newProps.recipe_detail_id > 0) {
          this.loadRecipeDetails()

          // 如果client_code或tag_group_code变化，重新订阅MQTT主题
          if (this.mqttConnStatus &&
              (newProps.client_code !== oldProps.client_code ||
               newProps.tag_group_code !== oldProps.tag_group_code)) {
            console.log('配方变化，重新订阅MQTT主题')
            this.resubscribeMqttTopics()
          }
        }
      },
      immediate: true,
      deep: true
    },
    // 监听disabled prop的变化
    disabled: {
      handler(newVal) {
        this.localDisabled = newVal
      },
      immediate: true
    }
  },
  created() {
    // Initialize MQTT connection
    this.initMqttConnection()

    // 初始化本地修改状态
    this.localDisabled = this.disabled

    // 添加调试信息
    console.log('detail.vue created, props:', {
      recipe_detail_id: this.recipe_detail_id,
      client_code: this.client_code,
      tag_group_code: this.tag_group_code,
      client_des: this.client_des,
      model_name: this.model_name,
      disabled: this.disabled
    })
  },

  // Clean up MQTT connection when component is destroyed
  beforeDestroy() {
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    // 加载配方详情
    loadRecipeDetails() {
      if (this.recipe_detail_id <= 0) {
        return
      }

      // 如果MQTT已连接，先清空监控
      if (this.mqttConnStatus) {
        console.log('加载新配方，清空监控')
        this.clearMonitoring()
      }

      this.loading = true

      const params = {
        tag_group_id: this.recipe_detail_id,
        tag_code_prefix: this.tag_code_prefix,
        client_id: this.client_id,
        client_code: this.client_code,
        tag_group_code: this.tag_group_code,
        station_code: this.$route.query.station_code || '',
        page: this.currentPage,
        size: this.pageSize,
        sort: this.sort
      }

      recipeApi.queryRecipeDetails(params).then(res => {
        this.loading = false
        if (res.code === 0) {
          // 为每个参数添加input_value字段，初始值为null（表示未修改）
          // 不设置默认值，保持原始数据
          const data = (res.data || []).map(item => {
            return {
              ...item,
              input_value: null // 初始为null，表示未修改
            }
          })
          this.recipeData = data
          this.total = res.count || 0

          // 如果MQTT已连接，重新订阅主题
          if (this.mqttConnStatus && this.client_code && this.tag_group_code) {
            console.log('数据加载完成，重新订阅MQTT主题')
            // 订阅新的主题
            const topic = `SCADA_CHANGE/${this.client_code}/${this.tag_group_code}`
            if (!this.mesClientTopicList.includes(topic)) {
              console.log('订阅新主题:', topic)
              this.topicSubscribe(topic)
              this.mesClientTopicList.push(topic)
            }
          }
        } else {
          this.$message({
            type: 'error',
            message: res.msg || this.$t('core.secsais.fetchRecipeDataFailed')
          })
        }
      }).catch(error => {
        this.loading = false
        console.error('获取配方详情失败:', error)
        this.$message({
          type: 'error',
          message: this.$t('core.secsais.fetchRecipeDataFailed')
        })
      })
    },

    // 处理输入框失去焦点事件
    handleInputBlur(row) {
      console.log('handleInputBlur called with row:', row)

      // 如果输入值为null或空，则不发起修改
      if (row.input_value === null || row.input_value === undefined || row.input_value === '') {
        console.log('输入值为空，不发起修改')
        row.input_value = null // 重置为null
        return
      }

      // 如果输入值与当前值相同，则不发起修改
      if (row.tag_value !== undefined && row.input_value === row.tag_value) {
        console.log('输入值与当前值相同，不发起修改')
        row.input_value = null // 重置为null
        return
      }

      // 检查上下限
      // 尝试将输入值转换为数字
      const inputValue = parseFloat(row.input_value)

      // 如果输入值可以转换为数字，并且有上下限设置，则进行校验
      // 不再限制特定数据类型，只要能转换为数字且有上下限设置就校验
      if (!isNaN(inputValue)) {
        // 检查下限
        if (row.down_limit !== undefined && row.down_limit !== -1.0 && inputValue < row.down_limit) {
          console.log('输入值低于下限:', inputValue, '<', row.down_limit)
          this.$message({
            type: 'warning',
            message: this.$t('core.secsais.valueTooLow') + ': ' + inputValue + ' < ' + row.down_limit
          })
          row.input_value = null // 重置为null
          return
        }

        // 检查上限
        if (row.upper_limit !== undefined && row.upper_limit !== -1.0 && inputValue > row.upper_limit) {
          console.log('输入值高于上限:', inputValue, '>', row.upper_limit)
          this.$message({
            type: 'warning',
            message: this.$t('core.secsais.valueTooHigh') + ': ' + inputValue + ' > ' + row.upper_limit
          })
          row.input_value = null // 重置为null
          return
        }
      }

      // 检查MQTT连接状态
      if (!this.mqttConnStatus) {
        console.log('MQTT未连接，无法修改参数')
        this.$message({
          type: 'error',
          message: this.$t('core.secsais.mqttNotConnected')
        })
        return
      }

      try {
        // 保存原始输入值，以便在收到MQTT响应后进行比较
        const inputValue = row.input_value
        console.log('参数修改 - 当前值:', row.tag_value, '输入值:', inputValue, '标签:', row.tag_code)

        // 构建MQTT消息
        const tagKey = `${this.client_code}/${this.tag_group_code}/${row.tag_code}`

        // 构建MQTT消息数据
        const sendJson = {
          Data: [
            {
              TagKey: tagKey,
              TagValue: inputValue,
              TagId: row.tag_id,
              TagCode: row.tag_code,
              TagGroupCode: this.tag_group_code,
              ClientCode: this.client_code
            }
          ],
          ClientName: 'SCADA_WEB'
        }

        // 转换为JSON字符串
        const sendStr = JSON.stringify(sendJson)

        // 构建MQTT主题
        const topic = `SCADA_WRITE/${this.client_code}`

        // 确保已订阅标签变化主题
        const changeTopic = `SCADA_CHANGE/${this.client_code}/${this.tag_group_code}`
        if (!this.mesClientTopicList.includes(changeTopic)) {
          console.log('订阅标签变化主题:', changeTopic)
          this.topicSubscribe(changeTopic)
          this.mesClientTopicList.push(changeTopic)
        }

        console.log('发送MQTT消息:', topic, sendStr)

        // 发送MQTT消息
        this.clientMqtt.publish(topic, sendStr, { qos: 0 }, (error) => {
          if (error) {
            console.error('MQTT发送失败:', error)
            this.$message({
              type: 'error',
              message: this.$t('core.secsais.modifyFailed')
            })
          } else {
            console.log('MQTT发送成功，等待返回值更新')

            // 显示成功消息
            this.$message({
              type: 'success',
              message: this.$t('core.secsais.modifySuccess')
            })
          }
        })
      } catch (error) {
        console.error('发送MQTT消息失败:', error)
        this.$message({
          type: 'error',
          message: this.$t('core.secsais.modifyFailed')
        })
        // 清空输入值，因为更新失败
        row.input_value = null
      }
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadRecipeDetails()
    },

    // 处理每页显示数量变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadRecipeDetails()
    },

    // 处理开关状态变化
    handleSwitchChange(val) {
      console.log('修改参数开关状态变化:', val)
      // 可以在这里添加其他逻辑，例如通知父组件状态变化
      this.$emit('update:disabled', val)
    },

    // 清空监控
    clearMonitoring() {
      console.log('清空监控')

      // 取消订阅所有已订阅的主题
      this.mesClientTopicList.forEach(topic => {
        this.topicUnsubscribe(topic)
      })

      // 清空已订阅主题列表
      this.mesClientTopicList = []

      // 清空标签值
      this.recipeData.forEach(item => {
        // 只清空tag_value，保留其他属性
        item.tag_value = undefined
        item.input_value = null
      })
    },

    // 重新订阅MQTT主题
    resubscribeMqttTopics() {
      if (!this.mqttConnStatus) {
        console.log('MQTT未连接，无法订阅主题')
        return
      }

      // 先清空监控
      this.clearMonitoring()

      // 订阅新的主题
      if (this.client_code && this.tag_group_code) {
        const topic = `SCADA_CHANGE/${this.client_code}/${this.tag_group_code}`
        console.log('订阅新主题:', topic)
        this.topicSubscribe(topic)
        this.mesClientTopicList.push(topic)
      }
    },

    // 初始化MQTT连接
    initMqttConnection() {
      console.log('MQTT连接启动中...')

      // 使用selCellIP获取单元IP和端口信息
      const query = {
        userName: Cookies.get('userName'),
        cell_id: this.$route.query.cell_id || '',
        current_ip: window.location.hostname
      }

      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)

          if (!result || !result.ip || !result.mqtt_port) {
            this.$message({
              message: this.$t('core.secsais.serviceCell'),
              type: 'error'
            })
            return
          }

          // 获取连接地址
          this.cellIp = result.ip
          this.mqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port

          const connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
          console.log('MQTT连接URL：' + connectUrl)

          // 创建MQTT连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt)

          // 连接成功事件
          this.clientMqtt.on('connect', () => {
            console.log('MQTT连接成功')
            this.mqttConnStatus = true

            // 订阅相关主题
            if (this.client_code && this.tag_group_code) {
              // 使用resubscribeMqttTopics方法订阅主题
              this.resubscribeMqttTopics()

              this.$message({
                message: this.$t('core.secsais.mqttConnectSuccess'),
                type: 'success'
              })
            }
          })

          // 连接错误事件
          this.clientMqtt.on('error', (error) => {
            console.error('MQTT连接失败:', error)
            this.$message({
              message: this.$t('core.secsais.mqttConnectionFailed'),
              type: 'error'
            })
            this.clientMqtt.end()
            this.mqttConnStatus = false
          })

          // 重连事件
          this.clientMqtt.on('reconnect', () => {
            console.warn('MQTT正在重连...')
          })

          // 断开连接事件
          this.clientMqtt.on('disconnect', () => {
            console.warn('MQTT连接断开')
            this.mqttConnStatus = false
          })

          // 关闭连接事件
          this.clientMqtt.on('close', () => {
            console.warn('MQTT连接关闭')
            this.mqttConnStatus = false
          })

          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            console.log('MQTT收到来自', topic, '的消息', message.toString())
            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch((error) => {
          console.error('获取单元IP失败:', error)
          this.$message({
            message: this.$t('core.secsais.getCellIPFailed'),
            type: 'error'
          })
        })
    },

    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('core.secsais.mqttNotConnected'),
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输
      // level 2：只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('core.secsais.mqttNotConnected'),
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:', topic)
        } else {
          console.log('MQTT订阅失败:', topic)
        }
      })
    },

    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('core.secsais.mqttNotConnected'),
          type: 'error'
        })
        return
      }

      // 取消订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:', topic)
        } else {
          console.log('MQTT取消订阅失败:', topic)
        }
      })
    },

    // 发送MQTT消息
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('core.secsais.mqttNotConnected'),
          type: 'error'
        })
        return false
      }

      // 发布消息
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (error) {
          console.error('MQTT发送失败:', error)
          return false
        } else {
          console.log('MQTT发送成功:', topic, msg)
          return true
        }
      })
    },

    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      // console.log('MQTT收到来自', channel, '的消息', message.toString())

      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },

    // 刷新标签信息
    reflashTagInfo(channel, message) {
      try {
        console.log('收到MQTT消息，准备更新标签值:', channel, message.toString())

        const jsonData = JSON.parse(message)
        if (!jsonData) return

        // 根据消息格式处理不同类型的消息
        if (jsonData.TagKey && jsonData.TagCode && jsonData.TagNewValue !== undefined) {
          // 标准格式消息
          const tagKey = jsonData.TagKey
          const tagCode = jsonData.TagCode
          const tagNewValue = jsonData.TagNewValue

          console.log('解析到标签更新:', tagCode, tagNewValue)

          // 更新对应的标签值
          const tagItem = this.recipeData.find(item => item.tag_code === tagCode)
          if (tagItem) {
            console.log('找到匹配的标签项，更新前值:', tagItem.tag_value, '更新后值:', tagNewValue)
            // 更新标签值
            tagItem.tag_value = tagNewValue
            // 清空输入值，因为已经更新成功
            if (tagItem.input_value !== null) {
              tagItem.input_value = null
            }
          } else {
            console.log('未找到匹配的标签项:', tagCode)
            // 尝试通过TagKey查找
            const parts = tagKey.split('/')
            if (parts.length >= 3) {
              const codeFromKey = parts[2]
              const tagItemByKey = this.recipeData.find(item => item.tag_code === codeFromKey)
              if (tagItemByKey) {
                console.log('通过TagKey找到匹配项，更新前值:', tagItemByKey.tag_value, '更新后值:', tagNewValue)
                // 更新标签值
                tagItemByKey.tag_value = tagNewValue
                // 清空输入值，因为已经更新成功
                if (tagItemByKey.input_value !== null) {
                  tagItemByKey.input_value = null
                }
              }
            }
          }
        } else if (Array.isArray(jsonData.Data)) {
          // 批量更新格式
          console.log('收到批量更新数据:', jsonData.Data)

          jsonData.Data.forEach(item => {
            if (item.TagKey && item.TagValue !== undefined) {
              const parts = item.TagKey.split('/')
              if (parts.length >= 3) {
                const tagCode = parts[2]
                const tagNewValue = item.TagValue

                console.log('处理批量更新项:', tagCode, tagNewValue)

                const tagItem = this.recipeData.find(i => i.tag_code === tagCode)
                if (tagItem) {
                  console.log('找到匹配的标签项，更新前值:', tagItem.tag_value, '更新后值:', tagNewValue)
                  // 更新标签值
                  tagItem.tag_value = tagNewValue
                  // 清空输入值，因为已经更新成功
                  if (tagItem.input_value !== null) {
                    tagItem.input_value = null
                  }
                }
              }
            }
          })
        } else {
          console.log('未知格式的MQTT消息:', jsonData)
        }
      } catch (error) {
        console.error('解析MQTT消息失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.no-recipe-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #909399;
}

.selected-info {
  display: flex;
  align-items: center;
}

.selected-text {
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
}

.modify-switch {
  display: flex;
  align-items: center;
}

.modify-switch span {
  margin-right: 10px;
}
</style>
