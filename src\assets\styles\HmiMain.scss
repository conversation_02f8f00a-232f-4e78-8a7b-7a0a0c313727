 .el-container{
  background-color: var(--el-container-bgColor);
  ::v-deep .el-card{
    border: none;
    background-color: transparent;
  }
  ::v-deep .el-card__body{
    padding: 8px !important;
    background-color: var(--el-card__body-bgColor);
  }
    .header{
        background: var(--el-header--bgcolor);
        height: 80px !important;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title{
            font-family:' Microsoft YaHei';
            letter-spacing: 2px;
            font-size: 38px;
            color: var(--el-header--bgTextColor);
        }
        .headerLogo{
          display: flex;
          align-items: center;
          .headerStaion{
            span{
            color: #ffffff;
            font-size: 12px;
            padding: 8px 10px;
            border-radius: 25px;
            background: rgba(0,0,0,.1);
            font-weight: normal;
            white-space: nowrap;
            cursor: pointer;
              .svg-icon{
                margin-right: 5px;
              }
            }
            .spanFisrt{
              margin-right: 6px;
              margin-left: 15px;
            }
          }
          .hmi-logo {
            width: auto;
            height: 60px;
          }
        }
        .wrappstyle {
            display: flex;
            p {
              display: flex;
              align-items: center;
              span {
                font-size: 16px;
                font-weight: 600;
                margin: 0 5px;
                color: #fff;
              }
              .wholeline {
                width: 20px;
                height: 20px;
                border-radius: 50%;
              }
              .wholelinenormal {
                -webkit-animation: heart 1s linear infinite;
                animation: heart 1s linear infinite;
                background-color: #0d0;
                box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
                background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
              }
              .wholelineerror {
                -webkit-animation: heart 1s linear infinite;
                animation: heart 1s linear infinite;
                background-color: #f00;
                box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
                background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
              }
              .wholelinegray {
                -webkit-animation: heart 1s linear infinite;
                animation: heart 1s linear infinite;
                background-color: #606266;
                box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
                background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
              }
            }
            p:last-child {
                margin-right: 0 !important;
            }
          }
    }
    .elRowStyle{
        margin: 10px 0 0 0 !important;
        .tableHeader,.tableHeaderRight{
          width: 100%;
          tr{
            th{
              width: 33.3%;
              height: 40px;
              font-size: 18px;
              color: var(--el-table--textColor);
              border-right: 1px solid #f5f5e1;
            }
          }
          tr:nth-of-type(1){
            background: var( --el-table--bgcolor);
            th{
              font-family: var(--el-font-family);
              color: #fff;
            }
          }
          tr:nth-of-type(2){
            font-family: var(--el-font-family);
            background: var(--el-table--bgTextColor);
          }
        }
        .tableHeaderRight{
          tr{
            th{
              width: 25%;
              height: 30px;
            }
          }
        }
        #readRateDom{
          border: 1px solid #cbcdd1;
          width: 100%;
          height: 230px;
        }
        #deviceOeeDom{
          margin-top: 5px;
          border: 1px solid #cbcdd1;
          width: 100%;
          height: 230px;
          position: relative;
        }
        #straSquareDom{
          margin-top: 5px;
          border: 1px solid #cbcdd1;
          width: 100%;
          height: 230px;
          color: #473904;
        }
    }
    .mar-10{
        margin-top: 5px;
    }
    ::v-deep .iframeCard{
      .el-card__body{
        padding: 0 !important; 
      }
    }
    ::v-deep .el-table{
      border: 1px solid  #c2bfbf;
      th{
        font-size: 16px;
        font-weight: bold;
        color: var(--el-table--bgTextColor);
        background-color: var(--el-table--bgcolor) !important;
        font-family: var(--el-font-family);
      }
      td{
        color: var(--el-table2--bgTextColor);
        border-right: 1px solid #D9DCDB !important;
        font-family: var(--el-font-family);
      }
      tr{
        background-color:var(--el-table2--bgTextSingleColor);
      }
      tr:nth-child(even){
        background-color:var(--el-table2--bgTextDoubleColor) !important;
      }
      .el-table__cell{
        .cell{
          font-size: 16px;
          font-weight: bold;
        }
      }
      .el-table__body-wrapper{
        background: var(--el-table2--bottombgcolor);
      }
    }
    .tableBorder{
      position: relative;
    }
    .more{
      position: absolute;
      margin: auto;
      right: 0;
      top: 0;
      bottom: 0;
      width: 25px;
      height: 70px;
      text-align: center;
      background-color: var(--el-table--bgcolor);
      opacity: 0.9;
      border-radius: 5px;
      color: #fff;
      padding: 0 5px;
      writing-mode: vertical-rl;
      text-orientation: upright;
      cursor: pointer;
    }
}
body{
  padding: 0 !important;
}