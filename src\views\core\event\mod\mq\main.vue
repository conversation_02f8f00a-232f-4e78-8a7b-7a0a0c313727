<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.modmain.templateCode')">
                <!-- 模板代码： -->
                <el-input
                  v-model="query.event_mod_mq_code"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item
                :label="$t('lang_pack.modmain.templateDescription')"
              >
                <!-- 模板描述： -->
                <el-input
                  v-model="query.event_mod_mq_des"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentification')"
              >
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="450px"
      >
        <el-form
          ref="form"
          class="el-form-wrap el-form-column"
          :model="form"
          :rules="rules"
          size="small"
          label-width="100px"
          :inline="true"
        >
          <el-form-item
            :label="$t('lang_pack.modmain.templateCodet')"
            prop="event_mod_mq_code"
          >
            <!-- 模板代码 -->
            <el-input v-model="form.event_mod_mq_code" />
          </el-form-item>
          <el-form-item
            :label="$t('lang_pack.modmain.templateDescriptiont')"
            prop="event_mod_mq_des"
          >
            <!-- 模板描述 -->
            <el-input v-model="form.event_mod_mq_des" />
          </el-form-item>
          <el-form-item
            :label="$t('lang_pack.modmain.programDriven')"
            prop="event_mod_mq_dll"
          >
            <!-- 程序驱动 -->
            <el-input v-model="form.event_mod_mq_dll" />
          </el-form-item>
          <el-form-item
            :label="$t('lang_pack.commonPage.validIdentificationt')"
            prop="enable_flag"
          >
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio
                v-for="item in dict.ENABLE_FLAG"
                :key="item.id"
                :label="item.value"
              >{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button
            size="small"
            icon="el-icon-close"
            plain
            @click="crud.cancelCU"
          >{{ $t("lang_pack.commonPage.cancel") }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              :height="height"
              :highlight-current-row="true"
              @header-dragend="crud.tableHeaderDragend()"
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column
                v-if="1 == 0"
                width="10"
                prop="event_mod_mq_id"
                label="id"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="event_mod_mq_code"
                :label="$t('lang_pack.modmain.templateCodet')"
                width="100"
              />
              <!-- 模板代码 -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="event_mod_mq_des"
                :label="$t('lang_pack.modmain.templateDescriptiont')"
                width="150"
              />
              <!-- 模板描述 -->
              <el-table-column
                :show-overflow-tooltip="true"
                prop="event_mod_mq_dll"
                :label="$t('lang_pack.modmain.programDriven')"
                width="200"
              />
              <!-- 程序驱动 -->
              <el-table-column
                :label="$t('lang_pack.commonPage.validIdentificationt')"
                prop="enable_flag"
              >
                <!-- 有效标识 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('lang_pack.commonPage.operate')"
                align="center"
                fixed="right"
              >
                <!-- 操作 -->
                <template slot-scope="scope">
                  <udOperation
                    :data="scope.row"
                    :permission="permission"
                    :disabled-dle="false"
                  >
                    <template slot="right">
                      <el-button
                        slot="reference"
                        type="text"
                        size="small"
                        @click="$refs.subItem && $refs.subItem.crud.toAdd()"
                      >{{
                        $t("lang_pack.maintenanceMenu.addSubmenu")
                      }}</el-button>
                      <!-- 新增子菜单 -->
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>

        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <subItem
              ref="subItem"
              class="taIbleFirst box-card1"
              :event_mod_mq_id="this.form.event_mod_mq_id" 
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudEventMqModMain from '@/api/core/event/eventMqModMain'
import subItem from './subItem'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  event_mod_mq_id: 0,
  event_mod_mq_code: '',
  event_mod_mq_des: '',
  event_mod_mq_dll: '',
  enable_flag: 'Y'
}
export default {
  name: 'EVENT_MQ_MOD_MAIN',
  components: { crudOperation, rrOperation, udOperation, pagination, subItem },
  props: {},
  cruds() {
    return CRUD({
      title: 'MQ事件模板',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'event_mod_mq_id',
      // 排序
      sort: ['event_mod_mq_id asc'],
      // CRUD Method
      crudMethod: { ...crudEventMqModMain },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'rcs_event_mod_mq:add'],
        edit: ['admin', 'rcs_event_mod_mq:edit'],
        del: ['admin', 'rcs_event_mod_mq:del'],
        down: ['admin', 'rcs_event_mod_mq:down']
      },
      rules: {
        event_mod_mq_code: [
          { required: true, message: '请输入模板代码', trigger: 'blur' }
        ],
        event_mod_mq_des: [
          { required: true, message: '请输入模板描述', trigger: 'blur' }
        ],
        event_mod_mq_dll: [
          { required: true, message: '请输入程序驱动', trigger: 'blur' }
        ]
      },
      customPopover: false
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  methods: {
    handleRowClick(row, column, event) {
      this.form.event_mod_mq_id = row.event_mod_mq_id
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>
