<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="loading" border size="small" :data="tableData" style="width: 100%" cell-style="border:0px;border-bottom:1px solid #dfe6ec" height="500px" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column :show-overflow-tooltip="true" prop="panel_index" :label="$t('lang_pack.vie.BoardNumber')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_barcode" :label="$t('lang_pack.vie.BarCode')" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { eapApsPlanDReportSelect } from '@/api/eap/eapApsPlanReport'
export default {
  name: 'PlanDReport',
  props: {
    plan_id: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      loading: true,
      tableData: []
    }
  },
  created() {
    eapApsPlanDReportSelect({ 'plan_id': this.plan_id }).then(res => {
      this.loading = false
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data.length > 0) {
          this.tableData = defaultQuery.data
        }
      }
    })
      .catch(() => {
        this.$message({
          message: this.$t('view.dialog.queryException'),
          type: 'error'
        })
      })
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 280
    }
  },
  methods: {

  }
}
</script>
<style lang="less" scoped>
::v-deep .wrapElFormFirst {
  .el-form-item__label{
    width: 110px !important;
  }
}
::v-deep .el-descriptions-item__label.is-bordered-label{
  width:130px;
}
</style>
