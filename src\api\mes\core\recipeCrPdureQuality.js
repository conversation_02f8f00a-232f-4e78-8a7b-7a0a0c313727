import request from '@/utils/request'

// 查询工艺路线-工位-工序-质量信息
export function sel(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureQualitySel',
        method: 'post',
        data
    })
}
// 查询工艺路线-工位-工序-质量信息
export function selTagColOrder(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureTagColOrderSel',
        method: 'post',
        data
    })
}

// 新增工艺路线-工位-工序-质量信息
export function add(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureQualityIns',
        method: 'post',
        data
    })
}
// 修改工艺路线-工位-工序-质量信息
export function edit(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureQualityUpd',
        method: 'post',
        data
    })
}
// 修改工艺路线-工位-工序-质量信息--修改有效标识
export function editEnableFlag(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureQualityEnableFlagUpd',
        method: 'post',
        data
    })
}
// 删除工艺路线-工位-工序-质量信息
export function del(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureQualityDel',
        method: 'post',
        data
    })
}

// 批量新增工艺路线-工位-工序-质量信息
export function batchAdd(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeCrPdureQualityBatchIns',
        method: 'post',
        data
    })
}

export default { sel, selTagColOrder, add, edit, del, editEnableFlag, batchAdd }