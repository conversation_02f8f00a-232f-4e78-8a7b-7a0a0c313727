<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>总装生产队列</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDate }}</p>
            </div>
          </div>
        </el-col>

      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24" class="elnopadding">
          <div class="carInfoTable">
            <el-table
              :data="queueresult"
              style="width: 100%"
            >
              <el-table-column 
                prop="dateNumber"
                label="日期编号"
              />
              <el-table-column 
                prop="signal"
                label="生产号"
              />
              <el-table-column 
                prop="type"
                label="类型"
                width="100"
              />
              <el-table-column 
                prop="playTime"
                label="计划开始日期"
              />
              <el-table-column
                prop="status"
                label="状态"
                width="100"
              />
              <el-table-column 
                prop="material"
                label="物料号"
              />
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'queue',
  data() {
    return {
      nowDate: '', // 当前日期
      queue: [{
        dateNumber: '2111000011111',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000022222',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000033333',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000044444',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000055555',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000066666',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000077777',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000088888',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000099999',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000010101010',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000011111111',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '211100001212121212',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '211100001313131313',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '211100001414141414',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000015151515',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '211100001616161616',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '211100001717171717',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '2111000018181818',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '211100001919191919',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }, {
        dateNumber: '21110000020202020',
        signal: 'me11111111',
        type: '卡车',
        playTime: '2022-10-05',
        status: '已上线',
        material: 'QEERDDD54545454544'
      }],
      queueresult: [],
      queuetimer: null,
      queueindex: 0
    }
  },
  created() {
    this.formatDate()
    this.initcheckstationdata()
  },
  mounted() {
    this.currentTime()
  },
  // 销毁定时器
  beforeDestroy() {
    if (this.formatDate) {
      clearInterval(this.formatDate) // 在Vue实例销毁前，清除时间定时器
    }
  },
  methods: {
    initcheckstationdata() {
      this.queueresult = this.queue.slice(this.queueindex * 10, this.queueindex * 10 + 10)
      if (this.queue.length === 0) {
        clearInterval(this.queuetimer)
      } else {
        this.queuetimer = setInterval(() => {
          this.queueindex++
          this.queueresult = this.queue.slice(this.queueindex * 10, this.queueindex * 10 + 10)
          if (this.queueresult.length === 0) {
            this.queueindex = 0
            this.queueresult = this.queue.slice(this.queueindex * 10, this.queueindex * 10 + 10)
          }
        }, 5000)
      }
    },
    currentTime() {
      setInterval(this.formatDate, 1000)
    },
    formatDate() {
      const date = new Date()
      const year = date.getFullYear() // 年
      const month = date.getMonth() + 1 // 月
      const day = date.getDate() // 日
      const week = date.getDay() // 星期
      const weekArr = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      let hour = date.getHours() // 时
      hour = hour < 10 ? '0' + hour : hour // 如果只有一位，则前面补零
      let minute = date.getMinutes() // 分
      minute = minute < 10 ? '0' + minute : minute // 如果只有一位，则前面补零
      let second = date.getSeconds() // 秒
      second = second < 10 ? '0' + second : second // 如果只有一位，则前面补零
      this.nowDate = `${year}/${month}/${day} ${hour}:${minute}:${second} ${weekArr[week]}`
    }
  }
}
</script>

<style lang="less" scoped>
body{
  background: #1d3a6a !important;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.wraptext p::before{
    content: '';
    width: 10px;
    height: 10px;
    background-color: #ff0000;
    display: block;
    border-radius: 50%;
    margin-right: 10px;
}
.cardheadbg{
  background-color: #031c45;
      // background: -webkit-gradient( linear, left bottom, left top, color-stop(0.02, #031c45), color-stop(0.51, #194998), color-stop(0.87, #031c45) );
}
.elnopadding,::v-deep .el-card__body{
  padding: 0 !important;
}
.cardStyle{
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    img{
      width: 110px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex-direction: column;
    width: 20%;
    p{
      margin: 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 36px;
    color: #ffffff;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
::v-deep .el-card{
  border-radius: 0;
}
::v-deep .el-table th{
    background-color: #6b88b8 !important;
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px #000;
}
::v-deep .el-table--enable-row-transition .el-table__body td{
  background-color: #1d3a6a !important;
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px #000;
    text-align: center;
}
::v-deep .el-table th.el-table__cell>.cell{
  text-align: center;
}
::v-deep .el-table__body{
  min-height: calc(100vh - 70px) !important;
  padding-bottom: 20px;
    background: #1d3a6a;
}
</style>
