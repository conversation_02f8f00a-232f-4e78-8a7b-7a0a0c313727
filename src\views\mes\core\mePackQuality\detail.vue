<template>
  <div>
    <el-card ref="queryCard" shadow="never">
      <el-descriptions class="margin-top" :column="5" size="medium" border>

        <el-descriptions-item>
          <template slot="label">
            模组数量
          </template>
          {{ mzCount }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            容量差
          </template>
          {{ data.pack_capacity_diff }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            最大容量
          </template>
          {{ data.pack_max_capacity }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            最小容量
          </template>
          {{ data.pack_min_capacity }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <el-dialog :fullscreen="false" append-to-body="true" top="10px" :show-close="true" :close-on-click-modal="false" title="电芯列表" custom-class="step-attr-dialog" width="90%" :visible.sync="detailDialogVisible">
        <detail2 v-if="detailDialogVisible" ref="detail2" :data="currentRowData" />
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-dialog append-to-body :close-on-click-modal="false" title="替换模组" :visible.sync="updDialogVisbleSync" width="520px">
            <el-input v-model="mz_barcode" placeholder="请输入模组条码" />
            <div style="text-align: center; margin: 20px 0 0 0">
              <el-button size="small" icon="el-icon-close" plain @click="updDialogVisbleSync=false">取消</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :disabled="mz_barcode === ''" @click="saveMzBarcode()">确认</el-button>
              <!-- 确认 -->
            </div>
          </el-dialog>
          <!--表格渲染-->
          <el-table ref="table" border size="small" :data="detailData" style="width: 100%" height="450px" highlight-current-row @header-dragend="crud.tableHeaderDragend()">

            <el-table-column width="100" label="替换模组" align="center" fixed="left">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="updateMz(scope.row)">替换模组</el-tag>
              </template>
            </el-table-column>
            <el-table-column width="100" label="电芯列表" align="center" fixed="left">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row)">电芯列表</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="item_date" min-width="150" label="时间" />
            <el-table-column :show-overflow-tooltip="true" prop="station_code" min-width="100" label="工位号" />
            <el-table-column :show-overflow-tooltip="true" prop="make_order" min-width="100" label="订单号" />
            <el-table-column :show-overflow-tooltip="true" prop="small_model_type" min-width="100" label="机型" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_barcode" min-width="220" label="模组码" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_index" min-width="100" label="模组排序" />
            <el-table-column :show-overflow-tooltip="true" prop="ng_rack" min-width="100" label="排废曹号" />
            <el-table-column :show-overflow-tooltip="true" prop="ng_code" min-width="100" label="排废原因代码" />
            <el-table-column :show-overflow-tooltip="true" prop="ng_msg" min-width="450" label="排废原因描述" />
            <el-table-column :show-overflow-tooltip="true" prop="col_num" min-width="150" label="单列/双列" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_capacity_plus" min-width="150" label="容量和" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_capacity_diff" min-width="150" label="容量差" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_max_capacity" min-width="150" label="最大容量" />
            <el-table-column :show-overflow-tooltip="true" prop="mz_min_capacity" min-width="150" label="最小容量" />
            <el-table-column :show-overflow-tooltip="true" prop="up_flag" min-width="100" label="上传标识">
              <template slot-scope="scope">
                <el-tag :type="scope.row.up_flag==='Y'?'success':'warning'" effect="dark" style="cursor: pointer;" size="medium">{{ scope.row.up_flag }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="up_ng_code" min-width="100" label="上传错误代码" />
            <el-table-column :show-overflow-tooltip="true" prop="up_ng_msg" min-width="300" label="上传错误说明" />
            <el-table-column :show-overflow-tooltip="true" prop="enable_flag" min-width="100" label="有效标识">
              <template slot-scope="scope">
                {{ scope.row.enable_flag==="Y"?"有效":"无效" }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { selDetail, edit } from '@/api/mes/core/mePackQuality'
import detail2 from '@/views/mes/core/meMzQuality/detail'
export default {
  name: 'MES_ME_PACK_QUALITY',
  components: { detail2 },
  // 数据字典
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      detailData: [],
      updDialogVisbleSync: false,
      currentRowData: {},
      detailDialogVisible: false,
      mz_barcode: '',
      mzCount: 0
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.toQuery()
  },
  methods: {
    toQuery() {
      selDetail({
        pack_quality_id: this.data.pack_quality_id
      })
        .then(res => {
          this.mzCount = res.count
          if (res.code === 0 && res.count > 0) {
            this.detailData = res.data
          } else {
            this.detailData = []
          }
        })
        .catch(ex => {
          this.$message({ message: '操作失败：' + ex, type: 'error' })
          this.detailData = []
        })
    },
    updateMz(row) {
      this.currentRowData = row
      this.mz_barcode = ''
      this.updDialogVisbleSync = true
    },
    saveMzBarcode() {
      if (this.mz_barcode === '') {
        this.$message({ message: '请输入模组条码', type: 'info' })
        return
      }
      this.$confirm(`确认将模组条码${this.currentRowData.mz_barcode}替换成${this.mz_barcode}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          edit({
            pack_mz_rel_id: this.currentRowData.pack_mz_rel_id,
            mz_barcode: this.mz_barcode
          })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.updDialogVisbleSync = false
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    },
    opendDetail(row) {
      this.currentRowData = row
      this.detailDialogVisible = true
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
