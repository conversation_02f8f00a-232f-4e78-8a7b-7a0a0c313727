import request from '@/utils/request'

// 工位安灯明细查询
export function queryAllMesAndonStationTypeI(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeISelect',
    method: 'post',
    data
  })
}

// 工位安灯明细增加
export function insMesAndonStationTypeI(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeIInsert',
    method: 'post',
    data
  })
}

// 工位安灯明细修改
export function updMesAndonStationTypeI(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeIUpdate',
    method: 'post',
    data
  })
}

// 工位安灯明细删除
export function delMesAndonStationTypeI(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesAndonStationTypeIDelete',
    method: 'post',
    data
  })
}

export default { queryAllMesAndonStationTypeI, insMesAndonStationTypeI, updMesAndonStationTypeI, delMesAndonStationTypeI }
