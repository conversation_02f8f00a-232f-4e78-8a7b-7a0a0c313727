import request from '@/utils/request'

// 查询大屏配置信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigSel',
    method: 'post',
    data
  })
}
// 新增大屏配置信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigIns',
    method: 'post',
    data
  })
}
// 修改大屏配置信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigUpd',
    method: 'post',
    data
  })
}
// 删除大屏配置信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigDel',
    method: 'post',
    data
  })
}
// 查询对应工位的大屏配置信息
export function selStationConfig(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapScreenConfigStationSel',
    method: 'post',
    data
  })
}
// 查询对应工位的状态颜色
export function selGroupCodeFast(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeGroupSel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, selStationConfig ,selGroupCodeFast}

