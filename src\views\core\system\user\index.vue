<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.maintenancePeople.nameEmployeeID')">
                <!-- 姓名/员工号： -->
                <el-input v-model="query.staffIdName" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.maintenancePeople.emailPhone')">
                <!-- 邮箱/手机号： -->
                <el-input v-model="query.emailPhone" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <div v-if="crud.status.edit > 0" style="text-align:center;margin-bottom:20px;">
          <img :src="form.user_img_path ? 'data:image/png;base64,' + form.user_img_path : Avatar" title="点击上传头像" style="width:100px;border-radius: 50%;cursor: pointer;" @click="toggleShow" />
          <myUpload ref="myUpload" v-model="show" :url="updateAvatarApi" :params="uploadParams" :no-rotate="false" @crop-upload-success="cropUploadSuccess" @crop-upload-fail="cropUploadFail" />
        </div>
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.maintenancePeople.EmployeeID')" prop="staff_id">
            <!-- 员工号 -->
            <el-input v-model="form.staff_id" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenancePeople.name')" prop="staff_name">
            <!-- 姓名 -->
            <el-input v-model="form.staff_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenancePeople.username')" prop="user_code">
            <!-- 用户名 -->
            <el-input v-model="form.user_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenancePeople.password')" prop="user_pwd">
            <!-- 密码 -->
            <el-input v-model="form.user_pwd" type="password" auto-complete="off" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenancePeople.email')" prop="staff_email">
            <!-- 邮箱 -->
            <el-input v-model="form.staff_email" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenancePeople.phone')" prop="staff_phone">
            <!-- 电话 -->
            <el-input v-model="form.staff_phone" />
          </el-form-item>

          <!--快速编码：DEPARTMENT-->
          <el-form-item :label="$t('lang_pack.maintenancePeople.department')" prop="depart_code">
            <!-- 部门 -->

            <el-select v-model="form.depart_code" clearable filterable>
              <el-option v-for="item in dict.DEPARTMENT" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!--快速编码：DUTY-->
          <el-form-item :label="$t('lang_pack.maintenancePeople.position')" prop="duty_code">
            <!-- 职位 -->
            <el-select v-model="form.duty_code" clearable filterable>
              <el-option v-for="item in dict.DUTY" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!--表：sys_role-->
          <el-form-item :label="$t('lang_pack.maintenancePeople.role')">
            <el-select v-model="form.role_id">
              <!-- 角色 -->
              <el-option v-for="item in roleData" :key="item.role_code" :label="item.role_des" :value="item.role_id" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="user_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="staff_id" :label="$t('lang_pack.maintenancePeople.EmployeeID')" />
            <!-- 员工号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="staff_name" :label="$t('lang_pack.maintenancePeople.name')" />
            <!-- 姓名 -->
            <el-table-column  :show-overflow-tooltip="true" prop="user_code" :label="$t('lang_pack.maintenancePeople.username')" />
            <!-- 用户名 -->
            <el-table-column  :show-overflow-tooltip="true" prop="staff_email" :label="$t('lang_pack.maintenancePeople.email')" />
            <!-- 邮箱 -->
            <el-table-column  :show-overflow-tooltip="true" prop="staff_phone" :label="$t('lang_pack.maintenancePeople.phonenumber')" />
            <!-- 手机号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="role_id" :label="$t('lang_pack.maintenancePeople.role')">
              <!-- 角色 -->
              <template slot-scope="scope">
                {{ getRoleDes(scope.row.role_id) }}
              </template>
            </el-table-column>
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="user_img_path" :label="$t('lang_pack.maintenancePeople.photo')" /> -->
            <!-- 头像 -->

            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import store from '@/store'
import myUpload from 'vue-image-crop-upload'
import Avatar from '@/assets/images/avatar.png'
import crudUser from '@/api/core/system/sysUser'
import { sel } from '@/api/core/system/sysRole'
import { isvalidPhone } from '@/utils/validate' // 数据验证
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  user_id: '',
  staff_id: '',
  staff_name: '',
  user_code: '',
  user_pwd: '',
  staff_email: '',
  staff_phone: '',
  depart_code: '',
  duty_code: '',
  role_id: '',
  user_img_path: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_USER',
  components: { crudOperation, rrOperation, udOperation, pagination, myUpload },
  cruds() {
    return CRUD({
      title: '用户',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'user_id',
      // 排序
      sort: ['user_id asc'],
      // CRUD Method
      crudMethod: { ...crudUser },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入电话号码'))
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_user:add'],
        edit: ['admin', 'sys_user:edit'],
        del: ['admin', 'sys_user:del'],
        down: ['admin', 'sys_user:down']
      },
      rules: {
        staff_id: [{ required: true, message: '请输入员工号', trigger: 'blur' }],
        staff_name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
        user_code: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        user_pwd: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        staff_email: [{ required: true, message: '请输入邮箱地址', trigger: 'blur' }, { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
        staff_phone: [{ required: true, trigger: 'blur', validator: validPhone }]
      },
      // 角色数据
      roleData: [],
      show: false,
      Avatar: Avatar,
      uploadParams: { user_name: '', user_id: '' }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'DEPARTMENT', 'DUTY'],
  computed: {
    ...mapGetters(['user', 'updateAvatarApi'])
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    sel(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.roleData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 获取角色的中文描述
    getRoleDes(role_id) {
      var item = this.roleData.find(item => item.role_id === role_id)
      if (item !== undefined) {
        return item.role_des
      }
      return role_id
    },
    toggleShow() {
      this.show = !this.show
    },
    cropUploadSuccess(jsonData, field) {
      this.form.user_img_path = jsonData.result
      if (this.user.id.toString() === this.uploadParams.user_id.toString()) {
        store.dispatch('GetInfo').then(() => {})
      }
      this.show = !this.show
      this.$refs.myUpload.off()
      this.crud.refresh()
    },
    cropUploadFail(jsonData, field) {
      this.show = !this.show
      this.$refs.myUpload.off()
    },

    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      this.uploadParams.user_id = crud.form.user_id
      return true
    }
  }
}
</script>
