<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workCenterCode')">
                <!-- 车间： -->
                <el-select v-model="query.work_center_code" clearable filterable @change="changeWorkCenterCode">
                  <el-option v-for="item in dict.WORK_CENTER_CODE" :key="item.id" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.prodLineCode')">
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_code">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des"
                    :value="item.prod_line_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item label="查询日期" prop="select_date">
                <el-date-picker v-model="query.select_date" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                  popper-class="tpc" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation>
                <template slot="right">
                  <download-excel style="margin-left: 10px;" :fields="exportDataStandard" :data="crud.data" type="xls"
                    :name="exportName" :header="exportHeader" :fetch="createExportData" :before-generate="startDownload"
                    :before-finish="finishDownload" worksheet="停线记录">
                    <el-button icon="el-icon-download">导出</el-button>
                  </download-excel>
                </template>
              </rrOperation>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" @cell-click="tableDbEdit" :row-class-name="tableRowClassName"
            :cell-class-name="tableCellClassName" v-loading="crud.loading" size="small" :data="crud.data"
            style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row>
            <el-table-column v-if="false" prop="andon_event_id" />
            <el-table-column :show-overflow-tooltip="true" prop="index" min-width="20" label="序号" />
            <el-table-column :show-overflow-tooltip="true" prop="happen_date" min-width="80" label="时间段" />
            <el-table-column :show-overflow-tooltip="true" prop="part_code" min-width="80" label="零件号" />
            <el-table-column :show-overflow-tooltip="true" prop="part_name" min-width="80" label="制件名称" />
            <el-table-column prop="stop_reason" label="停线主要内容" min-width="200" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-if="scope.row.index === clickRow && scope.column.index === clickCell">
                  <el-input ref="editInput" v-model="scope.row.stop_reason" size="mini" @blur="inputBlur(scope.row)" />
                </div>
                <div v-else>{{ scope.row.stop_reason }}</div>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="stop_time" min-width="50" label="停线时间" />
            <el-table-column prop="stop_attr" label="停线属性" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-if="scope.row.index === clickRow && scope.column.index === clickCell">
                  <el-select ref="select" v-model="scope.row.stop_attr" filterable multiple clearable size="mini"
                    @visible-change="handleBlur(scope.row, $event)">
                    <el-option ref="option" v-for="item in dict.STOP_REASON" :key="item.id" :label="item.label"
                      :value="item.label + ''" />
                  </el-select>
                </div>
                <div v-else>{{ scope.row.stop_attr }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="remarks" label="备注" min-width="50" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-if="scope.row.index === clickRow && scope.column.index === clickCell">
                  <el-input ref="editInput" v-model="scope.row.remarks" size="mini" @blur="inputBlur(scope.row)" />
                </div>
                <div v-else>{{ scope.row.remarks }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import crudCyStopReport from '@/api/pmc/andon/cystopreport'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
export default {
  name: 'CyStopReport',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '冲压停线报表',
      // 登录用户
      userName: Cookies.get('userName'),
      // CRUD Method
      crudMethod: { ...crudCyStopReport },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), crud()],
  // 数据模型
  data() {
    return {
      key: Math.random(),
      height: document.documentElement.clientHeight - 270,
      permission: {},
      prodLineData: [],
      clickRow: null, // 当前点击的行
      clickCell: null, // 当前点击的列
      tabClickLabel: "", // 当前点击的列名
      exportDataStandard: {
        "序号": "index",
        "时间段": "happen_date",
        "零件号": "part_code",
        "制件名称": "part_name",
        "停线主要内容": "stop_reason",
        "停线时间": "stop_time",
        "停线属性": "stop_attr",
        "备注": "remarks"
      },
      exportName: "冲压部A线生产过程停线记录",
      exportHeader: "冲压部" + new Date().getFullYear() + "年" + (new Date().getMonth() + 1) + "月" + new Date().getDate() + "日" + "A线生产过程停线记录",
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WORK_CENTER_CODE', 'ANDON_TYPE', 'RESET_FLAG', 'OVER_TIME_FLAG', 'STOP_REASON'],
  mounted: function () {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function () { },
  methods: {
    // 更改车间
    changeWorkCenterCode(val) {
      this.currentWorkCenterCode = val // 当前车间
      // 加载 产线LOV
      this.queryProdLine()
    },
    // 产线LOV
    queryProdLine() {
      const query = {
        work_center_code: this.currentWorkCenterCode,
        userID: Cookies.get('userName')
      }
      lovProdLine(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.prodLineData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    // 控制input显示 row 当前行 column 当前列
    tableDbEdit(row, column, cell, event) {
      var that = this
      switch (column.property) {
        case "stop_reason":
        case "remarks":
          that.clickRow = row.index;
          that.clickCell = column.index;
          that.tabClickLabel = column.label;
          break;
        case "stop_attr":
          that.clickRow = row.index;
          that.clickCell = column.index;
          that.tabClickLabel = column.label;
          row.stop_attr = row.stop_attr.split(',')
          break;
        default:
          that.clickRow = null;
          that.clickCell = null;
          that.tabClickLabel = "";
          return;
      }
    },
    // 把每一行的索引放进row
    tableRowClassName({ row, rowIndex }) {
      row.index = rowIndex;
    },

    // 把每一列的索引放进column
    tableCellClassName({ column, columnIndex }) {
      column.index = columnIndex;
    },

    // 失去焦点初始化
    inputBlur(data) {
      this.updateStopReason(data)
    },
    handleBlur(data, e) {
      if (!e) {
        this.updateStopReason(data)
      }
    },
    updateStopReason(data) {
      var stopAttr = ""
      if (data.stop_attr != null && data.stop_attr.length > 0) {
        data.stop_attr.forEach(element => {
          stopAttr += element + ","
        });
      }
      const query = {
        andon_event_id: data.andon_event_id,
        stop_reason: data.stop_reason,
        stop_attr: stopAttr.substring(0, stopAttr.length - 1),
        remarks: data.remarks
      }
      crudCyStopReport.update(query)
        .then(res => {

        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })

    },
    createExportData() {
      debugger
      return this.$refs.table.data
    },
    startDownload() {
      console.log("数据开始")
    },
    finishDownload() {
      console.log("数据下载完成")
    }
  }
}
</script>
