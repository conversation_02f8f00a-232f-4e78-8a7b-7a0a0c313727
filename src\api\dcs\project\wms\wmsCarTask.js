import request from '@/utils/request'
// 查询中控出库任务
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsCarTaskSelect',
    method: 'post',
    data
  })
}
// 删除中控出库任务
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsCarTaskDelete',
    method: 'post',
    data
  })
}
export function taskFinish(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsCarTaskForceFinish',
    method: 'post',
    data
  })
}
// 获取主页面流程管理list
export function flowTask(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/flowTaskListSelect',
    method: 'post',
    data: data
  })
}
export function flowUpdate(address, data) {
  return request({
    url: `http://${address}/cell/core/flow/CoreFlowUpdate`,
    method: 'post',
    data: data
  })
}
export default { sel, del, taskFinish, flowTask, flowUpdate }
