<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="日期:">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-time="['00:00:00', '23:59:59']"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :picker-options="pickerOptions1"
                    :clearable="false"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="项目编号：">
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="物料编码：">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="task_num" label="任务号" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_num" label="项目编号" />
            <el-table-column :show-overflow-tooltip="true" prop="serial_num" width="160" label="钢板自编号" />
            <el-table-column :show-overflow-tooltip="true" prop="material_code" label="零件号" />
            <el-table-column :show-overflow-tooltip="true" prop="from_stock_code" label="起始位置" />
            <el-table-column :show-overflow-tooltip="true" prop="to_stock_code" label="目标位置" />
            <el-table-column :show-overflow-tooltip="true" prop="m_length" width="160" label="长" />
            <el-table-column :show-overflow-tooltip="true" prop="m_width" width="130" label="宽" />
            <el-table-column :show-overflow-tooltip="true" prop="m_height" width="130" label="厚" />
            <el-table-column :show-overflow-tooltip="true" prop="m_weight" label="重" />
            <el-table-column :show-overflow-tooltip="true" prop="weight_in_tons" label="总吨数" />
            <el-table-column :show-overflow-tooltip="true" prop="piece_count" label="片数" />
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudWmsInventoryOut from '@/api/dcs/project/wms/wmsInventoryOut'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {

}
export default {
  name: 'WMS_INVENTORY_STORE',
  components: { crudOperation, rrOperation, pagination },
  cruds() {
    return CRUD({
      title: '出库统计',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'stock_event_id',
      // 排序
      sort: ['stock_event_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsInventoryOut },
      queryOnPresenterCreated: false,
      query: {
        task_type: 'KW_XL', // 固定查询任务类型
        stock_way: 'OUT' // 固定查询库存操作方式
      },
      // 按钮显示
      optShow: {
        search: false,
        refresh: false,
        add: false,
        edit: false,
        del: false,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_me_stock:add'],
        edit: ['admin', 'b_dcs_wms_map_me_stock:edit'],
        del: ['admin', 'b_dcs_wms_map_me_stock:del'],
        down: ['admin', 'b_dcs_wms_map_me_stock:down']
      },
      pickerMinDate: null,
      day31: 31 * 24 * 3600 * 1000,
      pickerOptions1: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            return (time.getTime() > (this.pickerMinDate + this.day31)) || (time.getTime() < (this.pickerMinDate - this.day31))
          }
          return false
        }
      }
    }
  },
  computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime =
            start.getFullYear() +
            '-' +
            (start.getMonth() + 1 >= 10
              ? start.getMonth() + 1
              : '0' + (start.getMonth() + 1)) +
            '-' +
            (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) +
            ' 00:00:00'
      const defalutEndTime =
            end.getFullYear() +
            '-' +
            (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : '0' + (end.getMonth() + 1)) +
            '-' +
            (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) +
            ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  created() {
    this.crud.query.item_date = this.timeDefault
    this.crud.toQuery()
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  methods: {
  }
}
</script>
