<template>
  <div class="workstationScreen">
    <el-card class="cardStyle">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="logoStyle"><img src="@/assets/images/qyc1.png" alt=""><span>{{ this.$route.query.station_code }}</span></div>
            <div class="wrapmarquee">
              <marquee loop="infinite">
                <div class="wraptext">
                  <p v-for="(item,index) in emergency" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                </div>
              </marquee>
            </div>
            <div class="headerL">
              <p class="pImg"><img src="@/assets/images/log.png" alt=""></p>
              <p class="pTime">{{ nowDateTime }} {{ nowDateWeek }}</p>
            </div>
          </div>
        </el-col>

      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="12">
          <el-card style="padding:16px !important" shadow="never">
            <div class="carInfo">
              <div class="carInfoText">
                <el-descriptions :column="2">
                  <el-descriptions-item label="VIN"><span class="vinstyle">{{ carorderinfo.vin }}</span></el-descriptions-item>
                  <el-descriptions-item label="DMS"><span class="vinstyle">{{ carorderinfo.dms }}</span></el-descriptions-item>
                  <el-descriptions-item label="车型">{{ carorderinfo.small_model_type }}</el-descriptions-item>
                  <el-descriptions-item label="驾驶室颜色">{{ carorderinfo.material_color }}</el-descriptions-item>
                  <el-descriptions-item label="高低宽窄">{{ carorderinfo.material_size }}</el-descriptions-item>
                  <el-descriptions-item label="发动机">{{ carorderinfo.engine_num }}</el-descriptions-item>
                  <el-descriptions-item label="驱动形式">{{ carorderinfo.driver_way }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never">
            <div class="carInfoTable">
              <el-table
                :data="carPlayData"
                style="width: 100%"
                height="147"
              >
                <el-table-column 
                  prop="rownum"
                  label="序号"
                  width="60"
                />
                <el-table-column 
                  prop="small_model_type"
                  label="车型代码"
                  width="180"
                />
                <el-table-column 
                  prop="vin"
                  label="VIN"
                />
                <el-table-column
                  prop="status"
                  label="状态"
                  width="80"
                />
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="12">
          <el-card shadow="never">
            <div class="carInfoTable">
              <el-table
                :data="carInfoDataresult"
                style="width: 100%"
                class="partstyle"
                height="264"
              >
                <el-table-column 
                  prop="ROWNUM"
                  label="序号"
                  width="60"
                />
                <el-table-column 
                  prop="MATERIAL_CODE"
                  label="图号"
                  width="200"
                />
                <el-table-column 
                  prop="MATERIAL_DESCRIPTION"
                  label="零件名称"
                />
                <el-table-column 
                  prop="DEMAND_QUANTITY"
                  label="用量"
                  width="80"
                />
              </el-table>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never">
            <div class="carInfoVideo" :style="'background-image:url(' + Background + ');'">
              <div v-if="playerOptions.sources[0].src===''" />
              <video-player
                v-else
                v-show="playerOptions.sources[0].src !== ''"
                ref="videoPlayer"
                class="video-player vjs-custom-skin"
                :playsinline="true"
                :options="playerOptions"
                @ended="onPlayerEnded($event)"
              />
              <img v-show="imgListv[0].IMAGE_URL !== '' && playerOptions.sources[0].src === ''" :src="imgListv[imgindex].IMAGE_URL">
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="3">
          <div class="carInfoQuestion">
            <div class="carInfoText">
              <el-descriptions title="计划/完成数" :column="1" :colon="false">
                <el-descriptions-item>计划数：{{ today_plan }}</el-descriptions-item>
                <el-descriptions-item>完成数：{{ current_offline }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-col>
        <el-col :span="9">
          <div class="carInfoQuestion">
            <div class="carInfoText">
              <el-descriptions title="控制特性" :column="1">
                <el-descriptions-item label="控制特性">{{ controlresult[controlindex].CONTROL_CHARACTERISTIC }}</el-descriptions-item>
                <el-descriptions-item label="特性要求" class="overtext">{{ controlresult[controlindex].FEATURE_REQUIRES }}</el-descriptions-item>
                <el-descriptions-item label="重要度">{{ controlresult[controlindex].IMPORTANCE }}</el-descriptions-item>
                <el-descriptions-item label="检查频次">{{ controlresult[controlindex].CHECKS_FREQUENCY }}</el-descriptions-item>
                <el-descriptions-item label="检查手段">{{ controlresult[controlindex].CHECK_MEANS }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="carInfoQuestion">
            <div class="carInfoText">
              <div>
                <el-descriptions title="工艺质量" :column="1">
                  <template v-if="jzresult[jzzindex].quality_des !== '' || jzresult[jzzindex].quality_rule !== '' || jzresult[jzzindex].quality_actual !== ''">
                    <el-descriptions-item label="工艺描述">{{ jzresult[jzzindex].quality_des }}</el-descriptions-item>
                    <el-descriptions-item label="加注量">{{ jzresult[jzzindex].quality_rule }}</el-descriptions-item>
                    <el-descriptions-item label="实际值"><span :class="jzresult[jzzindex].judge_flag === '1' ? 'greenactive':'redactive'">{{ jzresult[jzzindex].quality_actual }}</span></el-descriptions-item>
                  </template>
                  <template v-else-if="njresult[njzindex].quality_des !== '' || njresult[njzindex].quality_rule !== '' || njresult[njzindex].quality_actual !== ''">
                    <el-descriptions-item label="工艺描述">{{ njresult[njzindex].quality_des }}</el-descriptions-item>
                    <el-descriptions-item label="力矩要求">{{ njresult[njzindex].quality_rule }}</el-descriptions-item>
                    <el-descriptions-item label="实际值"><span :class="njresult[njzindex].judge_flag === 'OK' ? 'greenactive':'redactive'">{{ njresult[njzindex].quality_actual }}</span></el-descriptions-item>
                  </template>
                  <template v-else-if="tjresult[tjzindex].quality_des !== '' || tjresult[tjzindex].quality_rule !== '' || tjresult[tjzindex].quality_actual !== ''">
                    <el-descriptions-item label="工艺描述">{{ tjresult[tjzindex].quality_des }}</el-descriptions-item>
                    <el-descriptions-item label="合格标志">{{ tjresult[tjzindex].quality_rule }}</el-descriptions-item>
                    <el-descriptions-item label="实际值"><span :class="tjresult[tjzindex].judge_flag === '1' ? 'greenactive':'redactive'">{{ tjresult[tjzindex].quality_actual }}</span></el-descriptions-item>
                  </template>
                </el-descriptions>
              </div>
              <div v-show="jz_quality_list.length === 0 && nj_quality_list.length === 0 && tj_quality_list.length === 0">
                <p class="nogydata">暂无工艺质量</p>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="carInfoQuestion">
            <div class="carInfoText timeTop">
              <el-descriptions title="实时缺陷" :column="1" :colon="false">
                <el-descriptions-item v-if="flawresult.length <= 0"><span class="nodata">暂无实时缺陷</span></el-descriptions-item>
                <el-descriptions-item v-for="(item,index) in flawresult" v-else :key="index"><span class="topred">{{ item.FAULT_DESC }}</span></el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { getAndonContent } from '@/api/pmc/sysAndonContent'
import { getStationTime, getStationOrder, getStationOrderQueueZZ, getStationBZG, getStationEmergency, getStationQuality, getStationTop } from '@/api/pmc/sysworkstationScreen'
import Background from '@/assets/images/qyc1.png'
import posterimg from '@/assets/images/posterimg.jpg'
export default {
  name: 'workstationScreen',
  components: {
  },
  data() {
    return {
      // showtarget: true,
      playerOptions: {
        playbackRates: [0.5, 1.0, 1.5, 2.0], // 可选的播放速度
        autoplay: true, // 如果为true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 是否视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        // aspectRatio: '569:320', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [{
          type: 'video/mp4', // 类型
          src: '' // url地址
        }],
        poster: posterimg, // 封面地址
        notSupportedMessage: '此视频正在加载中，请稍等', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: true, // 当前时间和持续时间的分隔符
          durationDisplay: true, // 显示持续时间
          remainingTimeDisplay: false, // 是否显示剩余时间功能
          fullscreenToggle: true // 是否显示全屏按钮
        }
      },
      carorderinfo: {
        make_order: '',
        vin: '',
        dms: '',
        small_model_type: '',
        material_color: '',
        material_size: '',
        engine_num: '',
        driver_way: ''
      },
      emergency: [],
      emergencyresult: [],
      flaw: [],
      flawresult: [],
      flawnumber: 3,
      emergencynumber: 4,
      carInfoDatanumber: 9,
      carPlayDatanumber: 5,
      control: [],
      controlresult: [{ CONTROL_CHARACTERISTIC: '', FEATURE_REQUIRES: '', IMPORTANCE: '', CHECKS_FREQUENCY: '', CHECK_MEANS: '' }],
      controlindex: 0,
      flawindex: 0,
      emergencyindex: 0,
      flawtimer: null,
      controltimer: null,
      emergencytimer: null,
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      switchImg: false,
      switchVideo: false,
      vList: [],
      vListv: [],
      vListmp: [],
      vListvtwo: [{ url: 'http://***********:9080/video/YZA/ZADP02-1.mp4' }, { url: 'http://***********:9080/video/YZA/ZADP03-2.mp4' }],
      vListvtwoindex: 0,
      imgListv: [{ IMAGE_URL: '' }],
      imgListvtimer: null,
      dialogVList: [{ url: 'http://localhost:8080/wanjiang/4.mp4', type: 1 }, { url: 'http://*************:8080/wanjiang/i1.png', type: 2 }, { url: 'http://*************:8080/wanjiang/i2.png', type: 2 }],
      vindex: 0,
      imgindex: 0,
      dialogVindex: 0,
      station_code: '',
      Background: Background,
      posterimg: posterimg,
      today_plan: '',
      current_offline: '',
      carPlayData: [],
      carPlayDataresult: [],
      carPlayDatatimer: null,
      carPlayDataindex: 0,
      carInfoResult: [],
      num: 0,
      carInfoData: [],
      carInfoDataresult: [],
      carInfoDataindex: 0,
      carInfoDatatimer: null,
      jz_quality_list: [],
      jztimer: null,
      jzzindex: 0,
      jzresult: [{ judge_flag: '', quality_actual: '', quality_des: '', quality_rule: '' }],
      nj_quality_list: [],
      njtimer: null,
      njzindex: 0,
      njresult: [{ judge_flag: '', quality_actual: '', quality_des: '', quality_rule: '' }],
      tj_quality_list: [],
      tjtimer: null,
      tjzindex: 0,
      tjresult: [{ judge_flag: '', quality_actual: '', quality_des: '', quality_rule: '' }],
      work_center_code: 'ZA',
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: '',
      timer5: '',
      timer6: '',
      timer7: ''
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.timer1 = setInterval(() => {
      this.initstationTime()
    }, 1000)
    // 加载工位屏当前订单号
    this.initStationOrder()
    this.timer2 = setInterval(() => {
      this.initStationOrder()
    }, 5000)
    // 加载工位屏订单队列
    this.initStationOrderQueue()
    this.timer3 = setInterval(() => {
      this.initStationOrderQueue()
    }, 5000)
    this.getQueue()
    // 加载工位屏紧急事件
    this.initStationEmergency()
    this.timer4 = setInterval(() => {
      this.initStationEmergency()
    }, 2000)
    // this.getEmergencytimer()
    // 加载工位屏工艺质量
    this.initStationQuality()
    this.timer5 = setInterval(() => {
      this.initStationQuality()
    }, 2000)
    this.getQualitytimer()
    // 加载工位屏BOM作业三单关键特性
    this.getBZG()
    // 加载工位屏实时缺陷
    this.initStationTop()
    this.timer6 = setInterval(() => {
      this.initStationTop()
    }, 10 * 60 * 1000)
    this.getToptimer()
    // 计划数/完成数
    this.initAndonContent()
    this.timer7 = setInterval(() => {
      this.initAndonContent()
    }, 30000)
  },
  mounted() {
    // 加载工位屏订单队列
    // this.setQueue()
    // 加载工位屏工艺质量
    this.setQualitytimer()
    // 加载工位屏BOM作业三单关键特性
    this.setBZG()
    // 加载工位屏实时缺陷
    this.setToptimer()
  },
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
    clearInterval(this.timer5)
    clearInterval(this.timer6)
    clearInterval(this.timer7)
  },
  methods: {
    // 视频播完回调
    onPlayerEnded($event) {
      this.vindex++
      if (this.vindex >= this.vListv.length) {
        this.vindex = 0
      }
      this.playerOptions.sources[0].src = this.vListv[this.vindex].url
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏当前订单号
    initStationOrder() {
      var query = {
        station_code: this.$route.query.station_code,
        make_order: this.carorderinfo.make_order,
        work_center_code: this.work_center_code
      }
      getStationOrder(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (res.data[0].make_order_list === null || res.data[0].make_order_list.length === 0) {
            this.carorderinfo.make_order = ''
            this.carorderinfo.vin = ''
            this.carorderinfo.dms = ''
            this.carorderinfo.small_model_type = ''
            this.carorderinfo.material_color = ''
            this.carorderinfo.material_size = ''
            this.carorderinfo.engine_num = ''
            this.carorderinfo.driver_way = ''
          } else {
            this.carorderinfo.make_order = res.data[0].make_order_list[0].make_order
            this.carorderinfo.vin = res.data[0].make_order_list[0].vin
            this.carorderinfo.dms = res.data[0].make_order_list[0].dms
            this.carorderinfo.small_model_type = res.data[0].make_order_list[0].small_model_type
            this.carorderinfo.material_color = res.data[0].make_order_list[0].material_color
            this.carorderinfo.material_size = res.data[0].make_order_list[0].material_size
            this.carorderinfo.engine_num = res.data[0].make_order_list[0].engine_num
            this.carorderinfo.driver_way = res.data[0].make_order_list[0].driver_way
          }
          if (this.carorderinfo.make_order === '') {
            this.carInfoData = []
            this.vList = []
            this.control = []
            console.log(111)
          } else {
            var query = {
              station_code: this.$route.query.station_code,
              make_order: this.carorderinfo.make_order,
              work_center_code: this.work_center_code
            }
            getStationBZG(query)
              .then(res => {
                // console.log(res)
                if (res.code !== 0 && res.count < 0) {
                  console.log('请求数据异常')
                  return
                }
                // BOM
                this.carInfoData = res.data[0].bom_list
                console.log(this.carInfoData)
                // BOM
                // 视频图片轮播
                this.vList = res.data[0].three_list
                console.log(this.vList)
                // 视频图片轮播
                // 控制特性
                this.control = res.data[0].element_list
                console.log(this.control)
                // 控制特性
              })
              .catch(() => {
                console.log('请求数据为空')
              })
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    setBZG() {
      setInterval(this.getBZG, 5000)
    },
    getBZG() {
      this.carInfoDataresult = this.carInfoData.slice(this.carInfoDataindex * this.carInfoDatanumber, this.carInfoDataindex * this.carInfoDatanumber + this.carInfoDatanumber)
      if (this.carInfoData === null || this.carInfoData.length === 0) {
        this.carInfoDataresult = []
      } else {
        this.carInfoDataresult = this.carInfoData.slice(this.carInfoDataindex * this.carInfoDatanumber, this.carInfoDataindex * this.carInfoDatanumber + this.carInfoDatanumber)
        this.carInfoDataindex++
        // console.log(this.flawresult)
        if (this.carInfoDataresult.length === 0) {
          this.carInfoDataindex = 0
          this.carInfoDataresult = this.carInfoData.slice(this.carInfoDataindex * this.carInfoDatanumber, this.carInfoDataindex * this.carInfoDatanumber + this.carInfoDatanumber)
        }
      }
      this.vListmp = this.vList.filter(value => {
        return value.REVERSED1 === 'mp4'
      })
      this.vListv = this.vListmp
      if (this.vListv.length === 0) {
        this.playerOptions.sources[0].src = ''
      } else if (this.vListv.length === 1) {
        this.playerOptions.loop = true
        this.playerOptions.sources[0].src = this.vListv[this.vindex].IMAGE_URL
      } else {
        this.playerOptions.loop = false
        this.playerOptions.sources[0].src = this.vListv[this.vindex].IMAGE_URL
      }
      var imglists = this.vList.filter(value => {
        return value.REVERSED1 !== 'mp4'
      })
      if (imglists.length === 0) {
        this.imgListv.push({ IMAGE_URL: '' })
      } else {
        this.imgListv = imglists
        this.imgindex++
        if (this.imgindex >= this.imgListv.length) {
          this.imgindex = 0
        }
      }
      if (this.control === null || this.control.length === 0) {
        this.controlresult[this.controlindex].CONTROL_CHARACTERISTIC = ''
        this.controlresult[this.controlindex].FEATURE_REQUIRES = ''
        this.controlresult[this.controlindex].IMPORTANCE = ''
        this.controlresult[this.controlindex].CHECKS_FREQUENCY = ''
        this.controlresult[this.controlindex].CHECK_MEANS = ''
      } else {
        this.controlresult = this.control
        this.controlindex++
        if (this.controlindex >= this.control.length) {
          this.controlindex = 0
        }
      }
    },
    // 加载工位屏订单队列
    initStationOrderQueue() {
      var query = {
        station_code: this.$route.query.station_code,
        work_center_code: this.work_center_code
      }
      getStationOrderQueueZZ(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.carPlayData = res.data[0].station_mo_list
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // setQueue() {
    //   setInterval(this.getQueue, 2000)
    // },
    // getQueue() {
    //   this.carPlayDataresult = this.carPlayData.slice(this.carPlayDataindex * this.carPlayDatanumber, this.carPlayDataindex * this.carPlayDatanumber + this.carPlayDatanumber)
    //   if (this.carPlayData === null || this.carPlayData.length === 0) {
    //     this.carPlayDataresult = []
    //   } else {
    //     this.carPlayDataresult = this.carPlayData.slice(this.carPlayDataindex * this.carPlayDatanumber, this.carPlayDataindex * this.carPlayDatanumber + this.carPlayDatanumber)
    //     this.carPlayDataindex++
    //     if (this.carPlayDataresult.length === 0) {
    //       this.carPlayDataindex = 0
    //       this.carPlayDataresult = this.carPlayData.slice(this.carPlayDataindex * this.carPlayDatanumber, this.carPlayDataindex * this.carPlayDatanumber + this.carPlayDatanumber)
    //     }
    //   }
    // },
    // 加载工位屏紧急事件
    initStationEmergency() {
      var query = {
        station_code: this.$route.query.station_code,
        work_center_code: this.work_center_code
      }
      getStationEmergency(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          // 紧急事件
          this.emergency = res.data[0].andon_list
          if (this.emergency === null || this.emergency.length === 0) {
            this.emergency = []
          }
          // 紧急事件
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏工艺质量
    initStationQuality() {
      var query = {
        station_code: this.$route.query.station_code,
        make_order: this.carorderinfo.make_order,
        vin: this.carorderinfo.vin,
        work_center_code: this.work_center_code
      }
      getStationQuality(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          // 工艺质量(加注)
          this.jz_quality_list = res.data[0].jz_quality_list
          // 工艺质量(拧紧)
          this.nj_quality_list = res.data[0].nj_quality_list
          // 工艺质量(涂胶)
          this.tj_quality_list = res.data[0].jz_quality_list
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    setQualitytimer() {
      setInterval(this.getQualitytimer, 5000)
    },
    getQualitytimer() {
      if (this.jz_quality_list === null || this.jz_quality_list.length === 0) {
        this.jzresult[this.jzzindex].judge_flag = ''
        this.jzresult[this.jzzindex].quality_des = ''
        this.jzresult[this.jzzindex].quality_rule = ''
        this.jzresult[this.jzzindex].quality_actual = ''
      } else {
        this.jzresult = this.jz_quality_list
        this.jzzindex++
        if (this.jzzindex >= this.jz_quality_list.length) {
          this.jzzindex = 0
        }
      }
      if (this.nj_quality_list === null || this.nj_quality_list.length === 0) {
        this.njresult[this.njzindex].judge_flag = ''
        this.njresult[this.njzindex].quality_des = ''
        this.njresult[this.njzindex].quality_rule = ''
        this.njresult[this.njzindex].quality_actual = ''
      } else {
        this.njresult = this.nj_quality_list
        this.njzindex++
        if (this.njzindex >= this.nj_quality_list.length) {
          this.njzindex = 0
        }
      }
      if (this.tj_quality_list === null || this.tj_quality_list.length === 0) {
        this.tjresult[this.tjzindex].judge_flag = ''
        this.tjresult[this.tjzindex].quality_des = ''
        this.tjresult[this.tjzindex].quality_rule = ''
        this.tjresult[this.tjzindex].quality_actual = ''
      } else {
        this.tjresult = this.tj_quality_list
        this.tjzindex++
        if (this.tjzindex >= this.tj_quality_list.length) {
          this.tjzindex = 0
        }
      }
    },
    // 加载工位屏实时缺陷
    initStationTop() {
      var query = {
        station_code: this.$route.query.station_code,
        work_center_code: this.work_center_code
      }
      getStationTop(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          // 实时缺陷
          this.flaw = res.data[0].top_list
          // 实时缺陷
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    setToptimer() {
      setInterval(this.getToptimer, 2000)
    },
    getToptimer() {
      this.flawresult = this.flaw.slice(this.flawindex * this.flawnumber, this.flawindex * this.flawnumber + this.flawnumber)
      if (this.flaw === null || this.flaw.length === 0) {
        this.flawresult = []
      } else {
        this.flawresult = this.flaw.slice(this.flawindex * this.flawnumber, this.flawindex * this.flawnumber + this.flawnumber)
        this.flawindex++
        if (this.flawresult.length === 0) {
          this.flawindex = 0
          this.flawresult = this.flaw.slice(this.flawindex * this.flawnumber, this.flawindex * this.flawnumber + this.flawnumber)
        }
      }
    },
    // 加载工位屏计划数/完成数据
    initAndonContent() {
      var query = {
        station_code: this.$route.query.station_code,
        prod_line_code: this.$route.query.prod_line_code,
        fastcode_group_code: this.$route.query.prod_line_code + '_UP_DOWN_LINE_STATION'
      }
      getAndonContent(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.today_plan = res.today_plan
          this.current_offline = res.current_offline
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .m-slide{
  background: none !important;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ffba00;
  font-size: 40px;
  font-weight: 700;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.cardStyle{
  margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
      padding: 10px;
  .logoStyle{
    display: flex;
    align-items: center;
    img{
      width: 60px;
      margin-right: 10px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    p{
      margin: 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
      margin-top: 5px !important;
      font-weight: 700;
    }
  }
}
.carInfo{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .carInfoText{
    flex: 1;
  }
  .carInfoImg{
    width: 120px;
    img{
      width: 100%;
    }
  }
}
::v-deep .carInfoTable{
  .el-table--small .el-table__cell {
    padding: 1px 0;
}
}
.carInfoVideo{
height: 264px;
    background-color: #ffffff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
  img{
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.dialogVideo{
    position: fixed;
    z-index: 999;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
.carInfodialogVideo{
  // height: 265px;
  background-color: #333333;
      background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}
::v-deep .el-descriptions__title::before{
  content: '';
  width: 6px;
  height: 16px;
  background-color: #6b9ef9;
  display: inline-block;
  margin-right: 8px;
  border-radius: 25px;
}
::v-deep .el-descriptions__title{
  display: flex;
    align-items: center;
}
::v-deep .el-table th {
    background-color: #333333 !important;
    color: #ffffff;
    font-size: 19px;
}
::v-deep .el-table__row td,::v-deep .el-table__row td{
      font-size: 19px;
    font-weight: 700;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background:#6b9ef9 !important;
  color: #ffffff !important;

}
::v-deep .el-descriptions-item__container .el-descriptions-item__label,::v-deep .el-descriptions-item__container .el-descriptions-item__content{
  font-size: 19px;
    color: #333333;
    font-weight: 700;
}
::v-deep .el-descriptions__title{
  font-size: 19px;
}
::v-deep .el-descriptions-item{
  padding-bottom: 0 !important;
}
::v-deep .el-descriptions__header{
  margin-bottom: 10px !important;
}
::v-deep .my-label,.labelNum{
  color: #ff0000 !important;
}
::v-deep .el-card__body {
    padding: 0px !important;
}
.carInfoQuestion{
  padding: 17px 10px;
  padding-top: 10px;
}
::v-deep .video-player{
      height: 100% !important;
}
::v-deep .video-js.vjs-fluid{
  min-height: 100% !important;
      padding-top: initial !important;
}
::v-deep .video-js .vjs-tech {
  width: 100%;
  height: 100%;
    object-fit: fill !important;
}
::v-deep .vjs_video_433-dimensions.vjs-fluid {
    padding-top: initial !important;
}
::v-deep .video-js{
  background-color: #ffffff !important;
}
::v-deep .vjs-poster{
  background-color: #ffffff !important;
}
.greenactive{
  color: green !important;
}
.redactive{
  color: red !important;
}
.timeTop{
  .topred{
  color: #ff0000;
  white-space: wrap !important;
}
}
.nodata,.nogydata{
  color: red;
  font-weight: 700;
  font-size: 19px;
}
.nogydata{
  margin: 0;
  margin-left: 12px;
}
::v-deep .el-descriptions-item__content{
overflow: hidden !important;
white-space: nowrap;
text-overflow: ellipsis !important;
display: block !important;
}
::v-deep .vinstyle{
  color: #ff0000 !important;
  font-size: 22px;
}
// img[src=""] {
//     opacity: 0;
//   }
</style>
