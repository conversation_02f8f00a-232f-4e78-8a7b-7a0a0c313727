<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenancePline.lineCodeDescription')">
                <!-- 产线编码/描述： -->
                <el-input v-model="query.prodLineCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenancePline.lineType')">
                <!-- 产线类型： -->
                <el-select v-model="query.prod_line_type" clearable filterable>
                  <el-option v-for="item in dict.PROD_LINE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.maintenancePline.productionScheduleType')">
                <!-- 排产类型： -->
                <el-select v-model="query.plan_region_code" clearable filterable>
                  <el-option v-for="item in dict.PLAN_REGION_CODE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
              <el-form-item :label="$t('lang_pack.maintenancePline.lineCode')" prop="prod_line_code">
                <!-- 产线编码 -->
                <el-input v-model="form.prod_line_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenancePline.lineDescription')" prop="prod_line_des">
                <!-- 产线描述 -->
                <el-input v-model="form.prod_line_des" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenancePline.lineType')" prop="prod_line_type">
                <!-- 产线类型 -->
                <el-select v-model="form.prod_line_type" clearable filterable>
                  <el-option v-for="item in dict.PROD_LINE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenancePline.factory')" prop="factory_code">
                <!-- 工厂 -->
                <el-input v-model="form.factory_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenancePline.workCenter')" prop="work_center_code">
                <!-- 工作中心 -->
                <el-input v-model="form.work_center_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenancePline.productionScheduleType')" prop="plan_region_code">
                <!-- 排产类型 -->
                <el-select v-model="form.plan_region_code" clearable filterable>
                  <el-option v-for="item in dict.PLAN_REGION_CODE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.maintenancePline.activation')" prop="utilization_rate">
                <!-- 稼动率 -->
                <el-input v-model="form.utilization_rate" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenancePline.lockNumber')" prop="plan_lock_count">
                <!-- 锁定数量 -->
                <el-input v-model="form.plan_lock_count" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.maintenancePline.sort')" prop="prod_line_order">
                <!-- 排序 -->
                <el-input v-model="form.prod_line_order" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="prod_line_code" :label="$t('lang_pack.maintenancePline.lineCode')" width="160"/>
            <!-- 产线编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="prod_line_des" :label="$t('lang_pack.maintenancePline.lineCodeDescription')" width="120" />
            <!-- 产线描述 -->
            <el-table-column  :label="$t('lang_pack.maintenancePline.lineType')" align="center" prop="prod_line_type" width="100">
              <!-- 产线类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_LINE_TYPE[scope.row.prod_line_type] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="factory_code" :label="$t('lang_pack.maintenancePline.factory')" />
            <!-- 工厂 -->
            <el-table-column  :show-overflow-tooltip="true" prop="work_center_code" :label="$t('lang_pack.maintenancePline.workCenter')" />
            <!-- 工作中心 -->
            <el-table-column  :label="$t('lang_pack.maintenancePline.productionScheduleType')" align="center" prop="plan_region_code" width="100">
              <!-- 排产类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PLAN_REGION_CODE[scope.row.plan_region_code] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="utilization_rate" :label="$t('lang_pack.maintenancePline.activation')" />
            <!-- 稼动率 -->
            <el-table-column  :show-overflow-tooltip="true" prop="plan_lock_count" :label="$t('lang_pack.maintenancePline.lockNumber')" />
            <!-- 锁定数量 -->
            <el-table-column  :show-overflow-tooltip="true" prop="prod_line_order" :label="$t('lang_pack.maintenancePline.sort')" />
            <!-- 排序 -->
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudProdLine from '@/api/core/factory/sysProdLine'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  prod_line_id: '',
  prod_line_code: '',
  prod_line_des: '',
  prod_line_type: '',
  factory_code: '',
  work_center_code: '',
  plan_region_code: '',
  utilization_rate: '0',
  plan_lock_count: '0',
  prod_line_order: '1',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_PROD_LINE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '产线维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'prod_line_id',
      // 排序
      sort: ['prod_line_id asc'],
      // CRUD Method
      crudMethod: { ...crudProdLine },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_prod_line:add'],
        edit: ['admin', 'sys_prod_line:edit'],
        del: ['admin', 'sys_prod_line:del']
      },
      rules: {
        prod_line_code: [{ required: true, message: '请输入产线编码', trigger: 'blur' }],
        prod_line_type: [{ required: true, message: '请输入产线类型', trigger: 'blur' }],
        plan_region_code: [{ required: true, message: '请输入排产类型', trigger: 'blur' }]
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'PROD_LINE_TYPE', 'PLAN_REGION_CODE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {}
}
</script>
