<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <!--查询条件-->
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
          <el-form ref="query" :inline="true" size="mini" style="margin: 0; padding: 0">
            <el-form-item label="实例" style="margin: 0px 0px 5px 0px">
              <el-select v-model="query.client_code" filterable size="mini" style="width: 300px" @change="getCellIp()">
                <el-option v-for="(item,index) in scadaClientLov" :key="index" :label="item.client_des" :value="item.client_code">
                  <span style="float: left;color: #8492a6; font-size: 13px">{{ item.client_code }}</span>
                  <span style="float: right">{{ item.client_des }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="读取状态" style="margin: 0px 0px 5px 0px">
              <el-select v-model="query.read_status" clearable size="mini" style="width: 120px">
                <el-option v-for="item in readStatusLov" :key="item.read_status" :label="item.read_status_des" :value="item.read_status" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围" style="margin: 0px 0px 5px 0px">
              <el-date-picker
                ref="timepicker"
                v-model="query.create_time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 350px"
                align="right"
                :picker-options="pickerOptions"
              />
            </el-form-item>
            <el-form-item style="margin: 0;float:right">
              <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="btnQuery('search','','')">搜索
              </el-button>
              <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="btnQuery('reset','','')">重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!--查询结果-->
        <el-card shadow="always" style="margin-top: 10px">
          <el-tabs v-model="tabValue" :tab-position="tabPosition" style="height:500px;" @tab-click="tabSelect">
            <el-tab-pane name="tableTab"><span slot="label"><i class="el-icon-s-grid" /> 表格形式</span>
              <el-card>
                <el-table
                  ref="tableReport"
                  v-loading="loadingReport"
                  :data="dataReport"
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F4F7', color: '#757575' }"
                  border
                  height="450px"
                  :highlight-current-row="true"
                >
                  <el-table-column  v-if="1 == 1" width="200" prop="id" label="id" />
                  <el-table-column  :show-overflow-tooltip="true" width="200" prop="create_time" label="时间" />
                  <el-table-column  :show-overflow-tooltip="true" width="150" prop="device_code" label="实例编码" />
                  <el-table-column  :show-overflow-tooltip="true" width="200" prop="device_des" label="实例描述" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="read_status" label="读取状态">
                    <template slot-scope="scope">
                      <el-tag :type="scope.row.read_status === 'OK' ? 'success' : 'warning'" disable-transitions>
                        {{ scope.row.read_status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column  :show-overflow-tooltip="true" prop="read_message" label="读取信息" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="read_tag_count" label="读取数量" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="read_multy_bytes" label="读取字节" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="120" prop="cost_time" label="消耗时间(ms)" />
                  <el-table-column  :show-overflow-tooltip="true" align="center" width="100" prop="simulated_flag" label="模拟标识" />
                </el-table>
                <el-row :gutter="20">
                  <el-col :span="4" :offset="10">
                    <div style="margin-top: 5px">
                      <el-button-group>
                        <el-button type="primary" icon="el-icon-arrow-left" @click="pageQuery('pre')">上一页</el-button>
                        <el-button type="primary" @click="pageQuery('next')">下一页<i class="el-icon-arrow-right el-icon--right" /></el-button>
                      </el-button-group>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-tab-pane>
            <el-tab-pane name="chartTab"><span slot="label"><i class="el-icon-s-data" /> 曲线形式</span>
              <el-card>
                <ECharts ref="chartReport" v-loading="loadingChartReport" :options="chartOptions" style="width: 1200px;height:450px;" />
              </el-card>
            </el-tab-pane>
            <el-tab-pane name="ngChartTab"><span slot="label"><i class="el-icon-coin" /> 异常统计</span>
              <el-card>
                <ECharts ref="ngChartReport" v-loading="loadingChartReport" :options="ngChartOptions" style="width: 1200px;height:450px;" />
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<!--JS-->
<script>
// 导入
import { selCellIP } from '@/api/core/center/cell'
import { InitTimePickRange, ScadaClientSelect } from '@/api/core/scada/report'
import ECharts from 'vue-echarts'
import Cookies from 'js-cookie'
import axios from 'axios'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'

// 执行
export default {
  name: 'REPORT_SCADA_TAGREAD',
  // 1.初始化组件
  components: {
    ECharts
  },
  // 2.初始化参数设置
  data() {
    return {
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      clientInfo: {},
      query: {
        client_code: '',
        read_status: '',
        create_time: null
      },
      scadaClientLov: [],
      readStatusLov: [
        { read_status: 'OK', read_status_des: '读取正常' },
        { read_status: 'NG', read_status_des: '读取异常' }
      ],
      tabPosition: 'left', // Tab方向
      tabValue: 'tableTab', // 当前Tab选择
      dataReport: [], // 表格数据
      loadingReport: false, // 表格是否正在加载
      loadingChartReport: false,
      tableSize: 100, // 默认表格单次查询100行数据
      chartOptions: {},
      ngChartOptions: {},
      pickerOptions: {}
    }
  },
  // 3.页面创建时加载
  created: function() {
    // 初始化日期选择器快捷选项
    this.pickerOptions = createDatePickerShortcuts(this.$i18n)

    // Scada实例查询
    ScadaClientSelect().then((res) => {
      var result = JSON.parse(JSON.stringify(res))
      if (result.hasOwnProperty('error')) {
        this.$message({
          message: 'Scada实例基础数据查询失败:' + result.error,
          type: 'error'
        })
        this.scadaClientLov = []
        return
      }
      this.scadaClientLov = result.data
    }).catch(() => {
      this.$message({
        message: 'Scada实例基础数据查询超时或者SQL错误',
        type: 'error'
      })
    })
    // 自动加载开始时间结束时间(默认1天以内)
    this.query.create_time = InitTimePickRange(1)
  },
  // 5.页面渲染
  mounted() {
    this.chartOptions = {
      title: {
        text: '读取活动散点图分析',
        left: 'center'
      },
      legend: {
        data: ['读取正常', '读取异常'],
        left: 'right'
      },
      dataZoom: [{
        type: 'inside'
      }, {
        type: 'slider'
      }],
      xAxis: {
        name: '样本数量'
      },
      yAxis: {
        name: '耗时(ms)'
      },
      series: [{
        name: '读取正常',
        type: 'scatter',
        itemStyle: {
          normal: {
            color: 'green'
          }
        }
      }, {
        name: '读取异常',
        type: 'scatter',
        itemStyle: {
          normal: {
            color: 'red'
          }
        }
      }]
    }
    // NG统计
    this.ngChartOptions = {
      title: {
        text: '读取异常频次统计',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      dataZoom: [{
        type: 'inside'
      }, {
        type: 'slider'
      }],
      xAxis: [{
        type: 'category',
        axisTick: {
          alignWithLabel: true
        }
      }],
      yAxis: [{
        type: 'value'
      }],
      series: [{
        name: '失败频次',
        type: 'bar',
        barWidth: '60%'
      }]
    }
  },
  // 4.页面执行方法事件
  methods: {

    getCellIp() {
      const cell_id = this.scadaClientLov.filter(a => a.client_code === this.query.client_code)[0].cell_id
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询selCellIP异常', type: 'error' })
        })
    },
    // 4.1 查询方法
    btnQuery(code, pageDirct, pageId) {
      if (code === 'reset') { // 重置
        this.query.client_code = ''
        this.query.read_status = ''
        this.query.create_time = InitTimePickRange(1)
      } else {
        if (this.query.client_code === '') {
          this.$message({
            message: '请选择实例',
            type: 'info'
          })
          return
        }
        var start_time = this.query.create_time === null ? '' : this.query.create_time[0]
        var end_time = this.query.create_time === null ? '' : this.query.create_time[1]
        var isKT = true
        if (start_time !== '' && end_time !== '') {
          var start_time2 = start_time.replace(/\-/g, '/')
          var end_time2 = end_time.replace(/\-/g, '/')
          var sTime = new Date(start_time2) // 开始时间
          var eTime = new Date(end_time2) // 结束时间
          var diff = parseInt((eTime.getTime() - sTime.getTime()) / (1000 * 3600))
          if (diff <= 24) {
            isKT = false
          }
        }
        this.loadingReport = true
        if (this.cellIp === '' || this.webapiPort === '') {
          this.$message({
            message: '未获取到单元IP与端口号',
            type: 'info'
          })
          return
        }
        var method = '/cell/core/scada/CoreScadaTagReadReportSelect'
        var path = ''
        if (process.env.NODE_ENV === 'development') {
          path = 'http://localhost:' + this.webapiPort + method
        } else {
          path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        }

        // A:表格刷新
        const data = {
          device_code: this.query.client_code,
          read_status: this.query.read_status,
          start_time: start_time,
          end_time: end_time,
          tableSize: this.tableSize,
          page_dirct: pageDirct,
          page_id: pageId
        }
        axios
          .post(path, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.data.code === 0) {
              if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                if (pageId !== '') {
                  this.$message({
                    message: '已到顶无数据',
                    type: 'info'
                  })
                } else {
                  this.dataReport = []
                }
                this.loadingReport = false
                return
              }
              this.dataReport = defaultQuery.data.data
              this.loadingReport = false
            } else {
              this.$message({
                message: 'Scada读取活动报表查询失败:' + defaultQuery.data.msg,
                type: 'error'
              })
              this.dataReport = []
              this.loadingReport = false
            }
          })
          .catch(ex => {
            this.loadingReport = false
            this.$message({
              message: 'Scada读取活动报表查询超时或者SQL错误',
              type: 'error'
            })
          })
        // B:曲线刷新
        if (code === 'search') {
          this.loadingChartReport = true
          const data1 = {
            device_code: this.query.client_code,
            read_status: this.query.read_status,
            start_time: start_time,
            end_time: end_time,
            tableSize: '',
            page_dirct: '',
            page_id: ''
          }
          axios
            .post(path, data1, {
              headers: {
                'Content-Type': 'application/json'
              }
            })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.data.code === 0) {
                if (defaultQuery.data.data === undefined || defaultQuery.data.data.length <= 0) {
                  this.chartOptions = {}
                  this.ngChartOptions = {}
                  this.loadingChartReport = false
                  return
                }
                var arrayData = defaultQuery.data.data
                var okList = []
                var ngList = []
                var ngDateList = [] // 用于NG统计X
                var ngValueList = [] // 用于NG统计Y
                var oldDateOrHour = ''
                for (var i = 0; i < arrayData.length; i++) {
                  var nowDateOrHour = ''
                  var read_status = arrayData[i].read_status
                  var read_message = arrayData[i].read_message
                  var read_tag_count = arrayData[i].read_tag_count
                  var read_multy_bytes = arrayData[i].read_multy_bytes
                  var cost_time = arrayData[i].cost_time
                  var create_time = arrayData[i].create_time
                  var create_time_val = arrayData[i].create_time_val.toString()
                  var item = []
                  item.push(i + 1)
                  item.push(cost_time)
                  item.push(create_time)
                  item.push(read_status)
                  item.push(read_message)
                  item.push(read_tag_count)
                  item.push(read_multy_bytes)
                  if (read_status === 'OK') okList.push(item)
                  else ngList.push(item)
                  // NG统计
                  if (read_status === 'NG') {
                    if (isKT) {
                      nowDateOrHour = create_time.substring(0, 10)
                    } else {
                      nowDateOrHour = create_time_val.substring(8, 10)
                    }
                    if (oldDateOrHour !== nowDateOrHour) {
                      oldDateOrHour = nowDateOrHour
                      ngDateList.push(nowDateOrHour)
                      ngValueList.push(1)
                    } else {
                      var newValue = ngValueList[ngValueList.length - 1]
                      newValue = newValue + 1
                      ngValueList.splice((ngValueList.length - 1), 1, newValue)
                    }
                  }
                }
                // 散点图
                this.chartOptions = {
                  title: {
                    text: '读取活动散点图分析',
                    left: 'center'
                  },
                  legend: {
                    data: ['读取正常', '读取异常'],
                    left: 'right'
                  },
                  dataZoom: [{
                    type: 'inside'
                  }, {
                    type: 'slider'
                  }],
                  tooltip: {
                    showDelay: 0,
                    formatter: function(params) {
                      if (params.value.length >= 5) {
                        return '序号:' + params.value[0] + '<br/>' +
                                            '时间:' + params.value[2] + '<br/>' +
                                            '读取消耗(ms):' + params.value[1] + '<br/>' +
                                            '读取状态:' + params.value[3] + '<br/>' +
                                            '消息描述:' + params.value[4] + '<br/>' +
                                            '读取数量:' + params.value[5] + '<br/>' +
                                            '读取字节数:' + params.value[6]
                      }
                    },
                    axisPointer: {
                      show: true,
                      type: 'cross',
                      lineStyle: {
                        type: 'dashed',
                        width: 1
                      }
                    }
                  },
                  xAxis: {
                    name: '样本数量'
                  },
                  yAxis: {
                    name: '耗时(ms)'
                  },
                  series: [{
                    name: '读取正常',
                    type: 'scatter',
                    symbolSize: 10,
                    data: okList,
                    itemStyle: {
                      normal: {
                        color: 'green'
                      }
                    }
                  },
                  {
                    name: '读取异常',
                    type: 'scatter',
                    symbolSize: 10,
                    data: ngList,
                    itemStyle: {
                      normal: {
                        color: 'red'
                      }
                    }
                  }
                  ]
                }
                // 读取异常统计
                this.ngChartOptions = {
                  title: {
                    text: '读取异常频次统计',
                    left: 'center'
                  },
                  tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                      type: 'shadow'
                    }
                  },
                  dataZoom: [{
                    type: 'inside'
                  }, {
                    type: 'slider'
                  }],
                  xAxis: [{
                    type: 'category',
                    data: ngDateList,
                    axisTick: {
                      alignWithLabel: true
                    }
                  }],
                  yAxis: [{
                    type: 'value'
                  }],
                  series: [{
                    name: '失败频次',
                    data: ngValueList,
                    type: 'bar',
                    barWidth: '60%'
                  }]
                }
                this.loadingChartReport = false
              } else {
                this.$message({
                  message: 'Scada读取活动报表查询失败:' + defaultQuery.data.msg,
                  type: 'error'
                })
                this.chartOptions = {}
                this.ngChartOptions = {}
                this.loadingChartReport = false
              }
            })
            .catch(ex => {
              this.loadingChartReport = false
              this.$message({
                message: 'Scada读取活动报表查询超时或者SQL错误',
                type: 'error'
              })
            })
        }
      }
    },
    // 4.2 Tab切换
    tabSelect(tab, event) {},
    // 4.3 上一页或者下一页翻页
    pageQuery(pageDirct) {
      if (this.dataReport == null || this.dataReport.length <= 0) {
        this.$message({
          message: '当前无数据',
          type: 'info'
        })
        return
      }
      if (pageDirct === 'pre') {
        this.btnQuery('tablePage', pageDirct, this.dataReport[0].id)
      } else {
        this.btnQuery('tablePage', pageDirct, this.dataReport[this.dataReport.length - 1].id)
      }
    }
  }
}
</script>
<!--CSS-->
<style lang="scss" >
.box-card {min-height: calc(100vh);padding: 10px;}
:focus {outline: 0;}
.el-card__body {padding: 10px;}
</style>
