import request from '@/utils/request'

// 查询大屏基础
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetSel',
    method: 'post',
    data
  })
}
// 新增大屏基础
export function add(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetIns',
    method: 'post',
    data
  })
}
// 修改大屏基础
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetUpd',
    method: 'post',
    data
  })
}
// 删除大屏基础
export function del(data) {
  return request({
    url: 'aisEsbWeb/pmc/core/PmcScreenSetDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }

