<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="任务号:">
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="任务类型:">
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.ROLLER_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="到货单号:">
                <el-input v-model="query.list_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="任务状态：">
                <el-select v-model="query.task_status" clearable filterable>
                  <el-option
                    v-for="item in dict.PLAN_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="700px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="180px"
              :inline="true"
            >
              <el-form-item label="任务号" prop="task_num">
                <el-input v-model="form.task_num" />
              </el-form-item>
              <el-form-item label="任务来源" prop="task_from">
                <el-input v-model="form.task_from" />
              </el-form-item>
              <el-form-item label="任务类型" prop="task_type">
                <el-select v-model="form.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.ROLLER_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="到货单号" prop="list_num">
                <el-input v-model="form.list_num" />
              </el-form-item>
              <el-form-item label="任务状态" prop="task_status">
                <el-select v-model="form.task_status" clearable filterable>
                  <el-option
                    v-for="item in dict.PLAN_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column
              label=""
              align="center"
              width="150"
              fixed="left"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" style="color: #00c2fd;" @click="viewDetail(scope)">查看明细</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  :disabled="scope.row.task_status != 'PLAN'"
                  @click="taskStatusExecute(scope.row.roller_task_id,'WORK')"
                >执行</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  :disabled="scope.row.task_status != 'PLAN' "
                  @click="taskStatusExecute(scope.row.roller_task_id,'CANCEL')"
                >取消</el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="creation_date" label="创建时间" />
            <el-table-column :show-overflow-tooltip="true" prop="task_num" label="任务号" />
            <el-table-column :show-overflow-tooltip="true" prop="task_from" label="任务来源">
              <template slot-scope="scope">
                {{ dict.label.PLAN_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_type" label="任务类型">
              <template slot-scope="scope">
                {{ dict.label.ROLLER_TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="list_num" label="到货单号" />
            <el-table-column :show-overflow-tooltip="true" prop="task_status" label="任务状态">
              <template slot-scope="scope">
                {{ dict.label.PLAN_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>

            <!--
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="240"
              fixed="right"
            >
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>-->
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <selectModal ref="selectModal" :footer="false" />
  </div>
</template>

<script>
import selectModal from '@/components/selectModal'
import crudWmsRollerTask from '@/api/dcs/project/whzsj/wmsRollerTask'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  plan_from: 'AIS',
  enable_flag: 'Y'
}
export default {
  name: 'WMS_ROLLER_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination, selectModal },
  cruds() {
    return CRUD({
      title: '辊道任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'roller_task_id',
      // 排序
      sort: ['roller_task_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsRollerTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      rules: {
        task_num: [{ required: true, message: '请输入任务号', trigger: 'blur' }],
        task_type: [{ required: true, message: '请输入任务类型', trigger: 'blur' }],
        list_num: [{ required: true, message: '请输入到货单号', trigger: 'blur' }],
        task_status: [{ required: true, message: '请输入任务状态', trigger: 'blur' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG', 'PLAN_FROM', 'PLAN_STATUS', 'ROLLER_TASK_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    viewDetail(scope) {
      this.$refs.selectModal.open({
        type: 'gdrw', // 辊道任务
        checkType: '',
        search: {
          roller_task_id: scope.row.roller_task_id,
          sort: 'roller_task_d_id desc',
          user_name: Cookies.get('userName')
        }
      })
    },

    // 执行
    taskStatusExecute(rollerTaskId, taskStatus) {
      this.$confirm(`确定要执行当前任务吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            roller_task_id: rollerTaskId,
            task_status: taskStatus,
            user_name: Cookies.get('userName')
          }
          crudWmsRollerTask.taskStatusUpd(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('执行成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '执行失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '执行失败',
              type: 'error'
            })
          })
        })
        .catch(() => {})
    }
  }
}
</script>

          <style>
          .table-descriptions-label {
          width: 150px;
          }
          .table-descriptions-content {
          width: 150px;
          color: #b1be26;
          }

          </style>
