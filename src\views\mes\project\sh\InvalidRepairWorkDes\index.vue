<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width: 100%"
                    :picker-options="pickerOptions1"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="返修策略名称：">
                <el-input v-model="query.repair_work_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="返修策略：">
                <el-input v-model="query.repair_work_station_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料码：">
                <el-input v-model="query.exact_barcode" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <!-- <rrOperation /> -->
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <!-- <template slot="group_left">
          <el-button v-permission="permission.down" :loading="crud.downloadLoading" :disabled="!crud.data.length" size="small" icon="el-icon-download" @click="doExport" />
        </template> -->
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column :show-overflow-tooltip="true" prop="item_date" min-width="150" label="解绑时间" />
            <el-table-column :show-overflow-tooltip="true" prop="repair_work_des" min-width="100" label="返修策略名称" />
            <el-table-column :show-overflow-tooltip="true" prop="repair_work_station_code" min-width="100" label="返修策略" />
            <el-table-column :show-overflow-tooltip="true" prop="exact_barcode" min-width="100" label="物料码" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >总数量：{{ page.total }}</el-button>
              <el-button
                type="primary"
              >当前第{{ nowPageIndex }}页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >&lt;&nbsp;上一页</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >下一页&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getParamsSel } from '@/utils/index'
import crudStationSel from '@/api/mes/project/sh/shMaterialWork3'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import { mesExportSelOne } from '@/api/mes/core/meExport'
import { downloadFile } from '@/utils/index'
import { fileDownload } from '@/api/core/file/file'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
const defaultForm = {

}
export default {
  name: 'MES_ME_Invalid_RelSel',
  components: { rrOperation, crudOperation },
  cruds() {
    return CRUD({
      title: '解绑数据查询',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mz_quality_id',
      // 排序
      sort: ['mz_quality_id desc'],
      // CRUD Method
      crudMethod: { ...crudStationSel },
      listData: [],
      // 打开页面不查询
      queryOnPresenterCreated: false,
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      },
      query: {
        tableSize: 20
      }
    })
  },
  // 数据字典
  dicts: ['QUALIFIED_FLAG', 'DX_NG_CODE'],
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 310,
      permission: {
        add: ['admin', 'mes_me_mz_unb_quality:add'],
        edit: ['admin', 'mes_me_mz_unb_quality:edit'],
        del: ['admin', 'mes_me_mz_unb_quality:del'],
        down: ['admin', 'mes_me_mz_unb_quality:down']
      },
      stationData: [],
      smallModelTypeData: [],
      nowPageIndex: 1, // 当前页数
      pageList: [],
      exportId: '',
      timer: '',
      pickerMinDate: null,
      pickerMaxDate: null,
      day15: 31 * 24 * 3600 * 1000,
      pickerOptions1: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            return (time.getTime() > (this.pickerMinDate + this.day15)) || (time.getTime() < (this.pickerMinDate - this.day15))
          }
          return false
        }
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 310
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y',
      station_group_code: 'R1'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })

    selSmallModel({
      user_name: Cookies.get('userName'),
      enable_flag: 'Y',
      prod_line_type: 'MZ'
    })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.smallModelTypeData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '初始化模式数据异常',
          type: 'error'
        })
      })
  },
  methods: {
    doExport() {
      if (!this.query.item_date) {
        this.$message({ message: '一次最多只能导出31天数据，请重选时间后再导出！', type: 'info' })
        return
      }
      let dateStr1 = this.query.item_date[0]
      dateStr1 = dateStr1.replace(/-/g, '/')
      let dateStr2 = this.query.item_date[1]
      dateStr2 = dateStr2.replace(/-/g, '/')
      var date1 = new Date(dateStr1)
      var date2 = new Date(dateStr2)
      var Difference_In_Time = date2.getTime() - date1.getTime()
      var Difference_In_Days = parseInt(Difference_In_Time / (1000 * 3600 * 24))
      if (Difference_In_Days > 31) {
        this.$message({ message: '一次最多只能导出31天数据，请重选时间后再导出！', type: 'info' })
        return
      }

      this.crud.downloadLoading = true
      crudStationSel.exportEventInsert(this.query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.exportId = defaultQuery.result
            this.timer = setInterval(this.getFileStatus, 2000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
            this.crud.downloadLoading = false
          }
        })
        .catch(() => {
          this.$message({ message: '导出数据异常', type: 'error' })
          this.crud.downloadLoading = false
        })
    },
    getFileStatus() {
      // 获取文件下载状态
      mesExportSelOne({ export_id: this.exportId })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0 && defaultQuery.data) {
            if (defaultQuery.data.finish_status === 'OK') {
              clearInterval(this.timer)
              // 文件已生成，则下载该文件
              fileDownload({ file_path: defaultQuery.data.down_url }).then(result => {
                downloadFile(result, defaultQuery.data.export_name + '数据', 'csv')
                this.crud.downloadLoading = false
              }).catch(() => {
                this.crud.downloadLoading = false
              })
            } else if (defaultQuery.data.finish_status === 'NG') {
              this.$message({ message: defaultQuery.data.error_msg, type: 'error' })
              clearInterval(this.timer)
              this.crud.downloadLoading = false
            }
          }
        })
    },
    toQuery() {
      const Arr = ['item_date', 'repair_work_des', 'repair_work_station_code', 'exact_barcode']
      if (!getParamsSel(this, Arr)) {
        this.$message({
          type: 'warning',
          message: '请至少选择一个查询条件'
        })
        return
      }
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      Object.keys(this.query).forEach(key => {
        this.query[key] = this.crud.defaultQuery[key]
      })
      // this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: '已置顶',
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: '已置底',
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    }
  }
}
</script>

  <style>
  .table-descriptions-label {
    width: 150px;
  }
  .table-descriptions-content {
    width: 150px;
  }
  </style>
