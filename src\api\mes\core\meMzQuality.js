import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualitySel',
    method: 'post',
    data
  })
}
// 查询最大值和最小值
export function MesMeMzDxQualitySel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzDxQualitySel2',
    method: 'post',
    data
  })
}
// 查询
export function selDetail(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzDxQualitySel',
    method: 'post',
    data
  })
}
// 导出
export function down(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualityDown',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 替换电芯条码
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualityUpd',
    method: 'post',
    data
  })
}
// 模组中段替换电芯条码
export function mzQualityUpd(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualityUpd2',
    method: 'post',
    data
  })
}
// 解除绑定
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualityDel',
    method: 'post',
    data
  })
}
// 重新上传
export function reupload(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualityReupload',
    method: 'post',
    data
  })
}
// 电芯信息确认
export function MesMeMzCheck(data) {
  return request({
    url: 'aisEsbWeb//mes/core/MesMeMzCheck',
    method: 'post',
    data
  })
}
// 插入导出事件
export function exportEventInsert(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesMeMzQualityExportEventInsert',
    method: 'post',
    data
  })
}

export default { sel,MesMeMzDxQualitySel, selDetail, edit, mzQualityUpd,del, down, reupload,MesMeMzCheck, exportEventInsert }

