<template>
  <el-radio-group v-if="control_type === 'radio'" v-model="value" :size="size" :disabled="disabled" @change="handleChange">
    <el-radio v-for="item in data" :key="item.fastcode_code" :label="item.fastcode_code">{{ item.fastcode_des }}</el-radio>
  </el-radio-group>
  <el-select v-else v-model="value" :clearable="clearable" :multiple="multiple" :filterable="filterable" :size="size" :disabled="disabled" :style="{ width: isNaN(width) ? width : width + 'px' }" @change="handleChange">
    <el-option v-for="item in data" :key="item.fastcode_code" :label="item.fastcode_des" :value="item.fastcode_code" :disabled="optionDisabled" />
  </el-select>
</template>
<script>
import { lovFastcode } from '@/api/core/system/sysFastcode'
export default {
  props: {
    fastcode_group_code: {
      type: String,
      required: true
    },
    fastcode_code: {
      type: String,
      required: true,
      default: ''
    },
    // 控件类型：radio=单选按钮 select=下拉框
    control_type: {
      type: String,
      required: true
    },
    width: {
      type: [String, Number],
      default: '200'
    },
    size: {
      type: String,
      required: true,
      default: 'small'
    },
    // 多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 筛选
    filterable: {
      type: Boolean,
      default: false
    },
    // 清除
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否可用
    disabled: {
      type: Boolean,
      default: false
    },
    optionDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      data: [],
      value: null
    }
  },
  watch: {
    fastcode_group_code: {
      immediate: true,
      deep: true,
      handler() {
        const query = {
          fastcode_group_code: this.fastcode_group_code
        }
        lovFastcode(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.data = defaultQuery.data
              }
            }
          })
          .catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
      }
    },
    fastcode_code: {
      immediate: true,
      deep: true,
      handler() {
        // 多选赋值是数组，需要将字符串处理成数组
        if (this.multiple) {
          if (this.fastcode_code === '') {
            this.value = null
          } else {
            this.value = this.fastcode_code.split(',')
          }
        } else {
          this.value = this.fastcode_code
        }
      }
    }
  },
  created: function() {},
  methods: {
    handleChange(val) {
      // 多选结果是数组，需要处理成逗号分隔的字符串
      if (this.multiple) {
        this.$emit('update:fastcode_code', val.join(','))
      } else {
        this.$emit('update:fastcode_code', val)
        for (const item of this.data) {
          if (item.fastcode_code === val) {
            this.$emit('update:fastcode_des', item.fastcode_des)
            break
          }
        }
      }
      this.$emit('Change', val)
    }
  }
}
</script>
