import request from '@/utils/request'

// 查询Server下Cell安装信息
export function cellInstallSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreCellInstallSel',
    method: 'post',
    data
  })
}

// Server上传文件成功后处理
export function ServerInstallIns(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreServerInstallIns',
    method: 'post',
    data
  })
}
// Cell上传文件成功后处理
export function CellInstallIns(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreCellInstallIns',
    method: 'post',
    data
  })
}

// 查询Server和Cell信息
export function ServerAndCellInstallSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreServerAndCellInstallSel',
    method: 'post',
    data
  })
}
// 根据Server安装成功后清除相关数据
export function InstallSuccessDel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreInstallSuccessDel',
    method: 'post',
    data
  })
}
// 根据Cell安装成功后清除相关数据
export function CellFileUpdateDel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreCellFileUpdateDel',
    method: 'post',
    data
  })
}

// 根据Server安装
export function ServerInstall(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}
// 根据Cell安装
export function CellInstall(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}
export default { cellInstallSel,
  ServerInstallIns, CellInstallIns,
  ServerAndCellInstallSel, InstallSuccessDel, CellFileUpdateDel,
  ServerInstall, CellInstall }

