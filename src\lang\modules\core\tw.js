// 核心模块的繁體中文翻譯

export default {
  // 這裡可以添加核心模塊特定的翻譯
  core: {
    // SECS/AIS配方管理
    secsais: {
      // 頁面標題和通用文本
      maintenance: 'SECS/AIS配方管理',
      detail: '配方詳情',
      noRecipeSelected: '未選擇配方或配方詳情加載中...',
      clientList: '實例列表',
      modelList: '配方列表',

      // 表單字段
      clientDes: '客戶端描述',
      tagGroupCode: '標籤組編碼',
      modelName: '模型名稱',
      tagGroupId: '標籤組ID',
      tagCodePrefix: '標籤編碼前綴',
      clientId: '客戶端ID',
      clientCode: '客戶端編碼',
      parameterCode: '參數編碼',
      parameterDes: '參數描述',
      parameterVal: '參數值',
      enableFlag: '有效標識',
      valid: '有效',
      invalid: '無效',

      // 操作相關
      modifyParameter: '是否修改參數',
      addParameter: '新增參數',
      confirmDelete: '確認刪除選中的{0}條數據?',
      confirmDistribute: '確認下發該配方嗎?',
      confirmChangeEnableFlag: '確定要將有效標識修改為【{value}】嗎？',
      deleteSuccess: '刪除成功',
      deleteFailed: '刪除失敗',
      modifySuccess: '修改成功',
      modifyFailed: '修改失敗',
      emptyValueSet: '空值已設置',
      distributeSuccess: '下發成功',
      distributeFailed: '下發失敗',
      mqttConnectionFailed: 'MQTT連接失敗',
      mqttNotConnected: '請先連接MQTT服務',
      mqttConnectSuccess: 'MQTT連接成功',
      mqttUpdateTimeout: 'MQTT更新超時，請檢查連接',
      serviceCell: '無法獲取單元服務器信息',
      getCellIPFailed: '獲取單元IP失敗',

      // 參數相關
      parameterCode: '參數編碼',
      parameterDes: '參數描述',
      parameterVal: '當前值',
      parameterUnit: '單位',
      parameterLimit: '上下限',
      parameterInput: '輸入值',
      modifyParameter: '是否修改參數',
      noRecipeSelected: '請選擇配方',
      fetchRecipeDataFailed: '獲取配方數據失敗',
      valueTooLow: '輸入值低於下限',
      valueTooHigh: '輸入值高於上限',

      // 批量操作相關
      selectBatchParameters: '請選擇要批量修改的參數',
      confirmBatchModify: '確認要批量修改選中的 {0} 個參數嗎?',
      batchModify: '批量修改',
      batchModifying: '正在批量修改參數...',
      batchModifyComplete: '批量修改完成，成功: {0}，失敗: {1}',
      batchModifySuccess: '批量修改成功，共修改 {0} 個參數',
      batchModifyFailed: '批量修改參數失敗',
      batchModifyCanceled: '已取消批量修改',

      // 搜索和提示
      search: '搜索',
      reset: '重置',
      confirm: '確認',
      cancel: '取消',
      prompt: '提示',
      required: '必填項',
      fetchModelDataFailed: '獲取配方模型數據失敗',
      fetchRecipeDataFailed: '獲取配方詳情失敗',
      maintenance: 'SECS/AIS配方維護',
      confirmDelete: '確認刪除{0}條數據?',

      // 狀態和操作提示
      refreshInstanceList: '刷新實例列表',
      refreshingInstanceList: '正在刷新實例列表...',
      online: '在線',
      offline: '離線',
      stationCodeMissing: '工站編碼缺失，無法查詢配方模型數據',
      stationCodeMissingMqtt: '工站編碼缺失，無法連接MQTT',
      cancelDelete: '取消刪除',
      unknownMqttFormat: '未知格式的MQTT消息',

      // 配方參數相關
      recipeList: '配方列表',
      recipeDetails: '配方詳情',
      modelList: '配方列表',
      modifyParameter: '修改參數',
      parameterCode: '參數代碼',
      parameterDes: '參數描述',
      parameterVal: '參數值',
      parameterInput: '輸入值',
      parameterUnit: '單位',
      parameterLimit: '參數範圍',
      noRecipeSelected: '請選擇一個配方',
      valueTooLow: '輸入值低於下限',
      valueTooHigh: '輸入值高於上限',
      modifySuccess: '修改成功',
      modifyFailed: '修改失敗',

      // MQTT相關
      mqttNotConnected: 'MQTT未連接',
      mqttConnectSuccess: 'MQTT連接成功',
      mqttConnectionFailed: 'MQTT連接失敗',
      serviceCell: '獲取服務單元信息失敗',
      getCellIPFailed: '獲取單元IP失敗'
    }
  }
}
