<template>
    <el-card shadow="always" style="margin-top: 10px">
        <el-row :gutter="20">
            <el-col :span="24">
                <el-dialog title="抛丸钢板库位详情"  width="60%" :before-close="handleClose" :visible.sync="dialogVisible">
                    <el-form ref="ruleForm" class="el-form-wrap" :model="form" :rules="rules" size="small"
                        label-width="130px" :inline="true">
                        <el-col :span="8">
                            <el-form-item label="抛丸出库库位" prop="from_stock_code">
                                <el-select v-model="form.from_stock_code" :clearable="true" filterable @change="houseSelect">
                                    <el-option v-for="item in currentData" :key="item.stock_code" :label="item.stock_code" :value="item.stock_code" >
                                        <span style="float: left">{{ item.stock_des }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="钢板型号" prop="model_type">
                                <el-input  disabled v-model="form.model_type" clearable size="small" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="库存量" prop="from_stock_count">
                                <el-input v-model="form.from_stock_count" disabled clearable size="small" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                        <el-form-item label="抛丸传输库位" prop="to_stock_code">
                            <el-select v-model="form.to_stock_code" clearable filterable >
                                <el-option v-for="item in targetData" :key="item.station_code" :label="item.station_code" :value="item.station_code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="抛丸数量" prop="pw_count">
                            <el-input type="number" v-model.number="form.pw_count" @blur="BlurText($event)" clearable size="small" />
                        </el-form-item>
                    </el-col>
                    <el-divider />
                    <div style="text-align: center;width: 100%;">
                        <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
                        <!-- 取消 -->
                        <el-button type="primary" size="small" icon="el-icon-check" :loading="dialogLoading" @click="handleOk">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                        <!-- 确认 -->
                    </div>
                    </el-form>
                </el-dialog>
            </el-col>
        </el-row>
    </el-card>
</template>
<script>
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import crudCarTask from '@/api/dcs/core/wms/carTask'
import Cookies from 'js-cookie'
export default {
    name: 'BLASTMODAL',
    data() {
        return {
            modelList:[],
            form:{
                from_stock_code:'',
                model_type:'',
                from_stock_count:'',
                to_stock_code:'',
                pw_count:'',
                from_stock_id:'',
                to_stock_id:'',
                to_stock_count:'',
            },
            rules:{
                from_stock_code: [{ required: true, message: '请选择当前库位', trigger: 'blur' }],
                model_type: [{ required: true, message: '请选择钢板型号', trigger: 'blur' }],
                from_stock_count: [{ required: true, message: '请选择库存量', trigger: 'blur' }],
                to_stock_code: [{ required: true, message: '请选择目标库位', trigger: 'blur' }],
                pw_count: [{ required: true, message: '请选择抛丸数量', trigger: 'blur' }],
                // car_code:[{ required: true, message: '请选择天车', trigger: 'blur' }],
            },
            dialogLoading:false,
            currentData:[],
            targetData:[],
            dialogVisible:false
        }
    },
    created(){
        //获取抛丸出库库位
        crudCarTask.wmsStockCode({}).then(res=>{
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
                if(defaultQuery.count >0){
                    this.currentData = defaultQuery.data
                }
            } else {
                this.currentData = []
                this.$message({ message: defaultQuery.msg, type: 'error' })
            }
        })
        .catch(() => {
            this.currentData = []
            this.$message({ message: '查询异常', type: 'error' })
        })
        this.getReposData()
    },
    methods:{
        BlurText(e) {
            let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value);
            if (!boolean) {
                this.$message.warning("数量不能为空或正整数");
                e.target.value = "";
            };
        },
        houseSelect(e){
            let obj = {}
            obj = this.currentData.filter(item => {return item.stock_code == e})[0] || {}
            if(Object.keys(obj).length >0){  
                this.form.model_type = obj.model_type
                this.form.from_stock_count = obj.stock_count
                this.form.from_stock_id = obj.stock_id
            }
            this.form.pw_count = ''
        },
        getReposData(){
            // 获取抛丸传输库位
            crudCarTask.PwStockCode({}).then(res=>{
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if(defaultQuery.count >0){
                        this.form.to_stock_code = defaultQuery.data[0].station_code
                        this.targetData = defaultQuery.data
                    }
                } else {
                    this.form.to_stock_code = ''
                    this.targetData = []
                    this.$message({ message: defaultQuery.msg, type: 'error' })
                }
            })
            .catch(() => {
                this.form.to_stock_code = ''
                this.targetData = []
                this.$message({ message: '查询异常', type: 'error' })
            })
        },
        toHouseSelect(e){
            // let obj = {}
            // obj = this.targetData.filter(item => {return item.stock_code == e})[0] || {}
            // console.log(obj)
            // if(Object.keys(obj).length >0){  
            //     this.form.to_stock_id = obj.stock_id
            //     this.form.to_stock_count = obj.stock_count
            // }
            // console.log(this.form.to_stock_count)
        },
        handleClose(){
            this.dialogVisible = false
            this.$emit('ok')
        },
        handleOk(){
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.dialogLoading = true
                    if(this.form.pw_count <= 0 || this.form.pw_count > this.form.from_stock_count){
                        this.$message.error('抛丸数量必须大于0且不能超过库存量')
                        this.dialogLoading = false
                        return false
                    }
                    const query = {
                        pw_count:this.form.pw_count,
                        model_type:this.form.model_type,
                        from_stock_code:this.form.from_stock_code,
                        to_stock_code:this.form.to_stock_code,
                    }
                    crudLoadAreaOper.wmsPwTask(query).then(res=>{
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.code === 0) {
                            this.handleClose()
                            this.$emit('handleOk')
                            this.$message({ message: defaultQuery.msg || '新增抛丸任务成功', type: 'success' })
                        } else {
                            this.$message({ message: defaultQuery.msg, type: 'error' })
                        }
                    })
                    .catch(() => {
                        this.$message({ message: '查询异常', type: 'error' })
                    })
                    .finally(()=>{
                        this.dialogLoading = false
                    })
                }else {
                    return false;
                }
            })
        }
    }
}
</script>
<style scoped lang="less">
.el-pagination{
    float:none !important;
    text-align:right;
}
.el-form-wrap .el-form-item{
    width: 100% !important;
}
</style>