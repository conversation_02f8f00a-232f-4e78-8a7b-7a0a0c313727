<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-3 col-12">
                            <el-form-item label="项目名称:">
                                <el-select v-model="query.pm_project_name" clearable filterable>
                                    <el-option v-for="item in dict.PM_PROJECT_NAME" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item label="需求/问题分类:">
                                <el-select v-model="query.pm_classify" clearable filterable>
                                    <el-option v-for="item in dict.PM_CLASSIFY" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item label="紧急程度:">
                                <el-select v-model="query.pm_urgency_level" clearable filterable>
                                    <el-option v-for="item in dict.PM_URGENCY" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item label="整体状态:">
                                <el-select v-model="query.pm_overall_status" clearable filterable>
                                    <el-option v-for="item in dict.PM_OVERALL_STATE" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-3 col-12">
                            <el-form-item label="有效标识：">
                                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" >
                <template slot="right">
                    <el-button size="small" type="warning" icon="el-icon-upload2" plain round @click="importDialogVisible = true">
                        导入
                    </el-button>
                    <el-button size="small" type="warning" icon="el-icon-download" plain round @click="crud.doExport">
                        导出
                    </el-button>
                </template>
            </crudOperation>
            <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="导入" width="400px" :visible.sync="importDialogVisible">
                <div class="uploadStyleone">
                <el-upload
                    ref="upload"
                    :multiple="false"
                    class="upload-demo"
                    action=""
                    drag=""
                    :limit="uploadLimit1"
                    :accept="uploadAccept1"
                    :auto-upload="false"
                    :on-change="handleImport"
                    :http-request="uploadFile"
                    :on-progress="progressA"
                    :file-list="fileList"
                    name="file"
                >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                    将文件拖到此处，或
                    <em>点击上传</em>
                    </div>
                    <div class="el-upload__text">只能上传{{ uploadAccept1 }}文件</div>
                </el-upload>
                <el-input
                    v-if="isUpLoadError"
                    v-model="errorMsg"
                    type="textarea"
                    :rows="5"
                />
                <div style="text-align: center;margin-top:10px">
                    <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" @click="toButDrawerUpload">导入</el-button>
                </div>
                </div>
            </el-dialog>
            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="180px" :inline="true">
                            <el-form-item label="项目名称" prop="pm_project_name">
                                <!-- <fastCode fastcode_group_code="PM_PROJECT_NAME" :fastcode_code.sync="form.pm_project_name" control_type="select" size="small" /> -->
                                <el-select v-model="form.pm_project_name" clearable filterable>
                                    <el-option v-for="item in dict.PM_PROJECT_NAME" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="需求/问题分类" prop="pm_classify">
                                <el-select v-model="form.pm_classify" clearable filterable>
                                    <el-option v-for="item in dict.PM_CLASSIFY" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="工序" prop="pm_process">
                                <el-input v-model="form.pm_process" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="产线" prop="pm_line">
                                <el-input v-model="form.pm_line" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="设备编号" prop="pm_device_code">
                                <el-input v-model="form.pm_device_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="设备分类" prop="pm_device_calssify">
                                <el-input v-model="form.pm_device_calssify" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="设备拓展属性" prop="pm_device_extend">
                                <el-input v-model="form.pm_device_extend" type="textarea":rows="2"/>
                            </el-form-item>
                            <el-form-item label="需求/问题名称" prop="pm_demand_name">
                                <el-input v-model="form.pm_demand_name" type="textarea":rows="2"/>
                            </el-form-item>
                            <el-form-item label="需求/问题描述" prop="pm_demand_des">
                                <el-input v-model="form.pm_demand_des" type="textarea":rows="2"/>
                            </el-form-item>
                            <el-form-item label="是否待讨论" prop="pm_is_discuss">
                                <el-radio-group v-model="form.pm_is_discuss">
                                    <el-radio v-for="item in [{id:0,label:'是',value:'Y'},{id:1,label:'否',value:'N'}]" :key="item.id" :label="item.label" >{{
                                        item.label
                                    }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="是否必须" prop="pm_is_must">
                                <el-radio-group v-model="form.pm_is_must">
                                    <el-radio v-for="item in [{id:0,label:'是',value:'Y'},{id:1,label:'否',value:'N'}]" :key="item.id" :label="item.label" >{{
                                        item.label
                                    }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="紧急程度" prop="pm_urgency_level">
                                <el-select v-model="form.pm_urgency_level" clearable filterable>
                                    <el-option v-for="item in dict.PM_URGENCY" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="整体状态" prop="pm_overall_status">
                                <el-select v-model="form.pm_overall_status" clearable filterable>
                                    <el-option v-for="item in dict.PM_OVERALL_STATE" :key="item.id" :label="item.label" :value="item.label" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="整体剩余时间(天)" prop="pm_overall_time">
                                <el-input  v-model="form.pm_overall_time" disabled clearable size="small"  :class="form.pm_overall_time === 1 ? 'earlyWarning' : (form.pm_overall_time!=='' && form.pm_overall_time < 1) ? 'alarmWarning' :'' "/>
                            </el-form-item>
                            <el-form-item label="提出时间" prop="pm_proposed_time">
                                <el-date-picker v-model="form.pm_proposed_time" type="date" value-format="yyyy-MM-dd" size="small" align="right" unlink-panels placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="预期开发完成时间" prop="pm_exploit_expect_time">
                                <el-date-picker v-model="form.pm_exploit_expect_time" type="date" value-format="yyyy-MM-dd" size="small" align="right" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="预期上线时间" prop="pm_launch_time">
                                <el-date-picker v-model="form.pm_launch_time" type="date" value-format="yyyy-MM-dd" size="small" align="right" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="提出人" prop="pm_proposer">
                                <el-input v-model="form.pm_proposer" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="业务评审时间" prop="pm_review_time">
                                <el-date-picker v-model="form.pm_review_time" type="date" value-format="yyyy-MM-dd" size="small" align="right" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="业务评审人" prop="pm_review_by">
                                <el-input v-model="form.pm_review_by" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="业务评审方案" prop="pm_review_programme">
                                <el-input v-model="form.pm_review_programme" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="开发责任人" prop="pm_development_by">
                                <el-input v-model="form.pm_development_by" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="开发计划完成时间" prop="pm_exploit_plan_time">
                                <el-date-picker v-model="form.pm_exploit_plan_time" type="date" value-format="yyyy-MM-dd" size="small" align="right" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="实际开发完成时间" prop="pm_exploit_actual_time">
                                <el-date-picker v-model="form.pm_exploit_actual_time" type="date" value-format="yyyy-MM-dd" size="small" align="right" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="是否需要延期沟通" prop="pm_is_postpone">
                                <el-radio-group v-model="form.pm_is_postpone">
                                    <el-radio v-for="item in [{id:0,label:'是',value:'Y'},{id:1,label:'否',value:'N'}]" :key="item.id" :label="item.label" >{{
                                        item.label
                                    }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="开发完成剩余时间" prop="pm_exploit_remain_time">
                                <el-input v-model="form.pm_exploit_remain_time" disabled clearable size="small" :class="form.pm_exploit_remain_time === 1 ? 'earlyWarning' : (form.pm_exploit_remain_time!=='' && form.pm_exploit_remain_time < 1 )? 'alarmWarning' : '' " />
                            </el-form-item>
                            <el-form-item label="开发解释" prop="pm_exploit_doc">
                                <el-input v-model="form.pm_exploit_doc" type="textarea":rows="2"/>
                            </el-form-item>
                            <el-form-item label="交付责任人" prop="pm_delivery_by">
                                <el-input v-model="form.pm_delivery_by" clearable size="small" />
                            </el-form-item>
                            <el-form-item label="交付计划完成时间" prop="pm_delivery_plan_time">
                                <el-date-picker v-model="form.pm_delivery_plan_time" type="date" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="交付时间完成时间" prop="pm_delivery_actual_time">
                                <el-date-picker v-model="form.pm_delivery_actual_time" type="date" placeholder="请选择日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="交付解释" prop="pm_delivery_doc">
                                <el-input v-model="form.pm_delivery_doc" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                                <!-- 有效标识 -->
                                <el-radio-group v-model="form.enable_flag">
                                    <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                                        item.label
                                    }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" fixed/>
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_project_name"
                            label="项目名称"  width="120" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_classify"
                            label="需求/问题分类" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_process"
                            label="工序" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_line"
                            label="产线" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_device_code"
                            label="设备编号" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_device_calssify"
                            label="设备分类" width="100" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_device_extend"
                            label="设备拓展属性" width="180" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_demand_name"
                            label="需求/问题名称" width="180" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_demand_des"
                            label="需求/问题描述" width="180" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_is_discuss"
                            label="是否待讨论" width="100" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_is_must"
                            label="是否必须" width="100" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_urgency_level"
                            label="紧急程度" width="100" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_overall_status"
                            label="整体状态" width="100" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_overall_time"
                            label="整体剩余时间（天）" width="130" align='center'>
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.pm_overall_time > 1" type="" effect="dark">{{scope.row.pm_overall_time}}</el-tag>
                                <el-tag v-else-if="scope.row.pm_overall_time <= 1"  effect="dark" 
                                :type="scope.row.pm_overall_time === 1 ? 'warning' : (scope.row.pm_overall_time!=='' && scope.row.pm_overall_time < 1 ) ? 'danger' : '' "
                                >{{scope.row.pm_overall_time}}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_proposed_time"
                            label="提出时间" width="100" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_exploit_expect_time"
                            label="预计开发完成时间" width="130" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_launch_time"
                            label="预计上线时间" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_proposer"
                            label="提出人" width="80" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_review_time"
                            label="业务评审时间" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_review_by"
                            label="业务评审人" width="100" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_review_programme"
                            label="业务评审方案" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_development_by"
                            label="开发责任人" width="120" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_exploit_plan_time"
                            label="开发计划完成时间" width="140" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_exploit_actual_time"
                            label="实际开发完成时间" width="140" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_is_postpone"
                            label="是否需要延期沟通" width="130" align='center' />
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_exploit_remain_time"
                            label="开发完成剩余时间" width="140" align='center'>
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.pm_exploit_remain_time > 1" type="" effect="dark">{{scope.row.pm_exploit_remain_time}}</el-tag>
                                <el-tag v-else-if="scope.row.pm_exploit_remain_time <= 1"  effect="dark" 
                                :type="scope.row.pm_exploit_remain_time === 1 ? 'warning' : (scope.row.pm_exploit_remain_time!=='' && scope.row.pm_exploit_remain_time < 1 ) ? 'danger' : '' "
                                >{{scope.row.pm_exploit_remain_time}}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_exploit_doc"
                            label="开发解释" width="140" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_delivery_by"
                            label="交付责任人" width="140" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_delivery_plan_time"
                            label="交付计划完成时间" width="140" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_delivery_actual_time"
                            label="交付时间完成时间" width="140" align='center'/>
                        <el-table-column  :show-overflow-tooltip="true" prop="pm_delivery_doc"
                            label="交付解释" width="140" align='center'/>
                            <el-table-column  :label="$t('lang_pack.recipequality.enableFlag')"
                            prop="enable_flag" width="80" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
                            </template>
                        </el-table-column>
                        <!-- Table单条操作-->
                        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center"  fixed="right">
                            <!-- 操作 -->
                            <template slot-scope="scope">
                                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudPmEvent from '@/api/pmManage/project/pmEvent'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    pm_id:'',
    pm_project_name:'',
    pm_classify:'',
    pm_process:'',
    pm_line:'',
    pm_device_code:'',
    pm_device_calssify:'',
    pm_device_extend:'',
    pm_demand_name:'',
    pm_demand_des:'',
    pm_is_discuss:'否',
    pm_is_must:'否',
    pm_urgency_level:'',
    pm_overall_status:'',
    pm_overall_time:'',
    pm_proposed_time:'',
    pm_exploit_expect_time:'',
    pm_launch_time:'',
    pm_proposer:'',
    pm_review_time:'',
    pm_review_by:'',
    pm_review_programme:'',
    pm_development_by:'',
    pm_exploit_plan_time:'',
    pm_exploit_actual_time:'',
    pm_is_postpone:'否',
    pm_exploit_remain_time:'',
    pm_exploit_doc:'',
    pm_delivery_by:'',
    pm_delivery_plan_time:'',
    pm_delivery_actual_time:'',
    pm_delivery_doc:'',
    enable_flag:'Y',
}
export default {
    name: 'WEB_PROJECT',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '项目管理',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'pm_id',
            // 排序
            sort: ['pm_id asc'],
            // CRUD Method
            crudMethod: { ...crudPmEvent },
            // 按钮显示
            optShow: {
                add: true,
                edit: true,
                del: true,
                reset: true,
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                pm_project_name: [{ required: true, message: '请选择项目', trigger: 'blur' }],
                pm_is_must: [{ required: true, message: '请选择是否必须', trigger: 'blur' }],
                pm_urgency_level: [{ required: true, message: '请选择紧急程度', trigger: 'blur' }],
                pm_overall_status: [{ required: true, message: '请选择整体状态', trigger: 'blur' }],
                pm_proposed_time: [{ required: true, message: '请选择提出时间', trigger: 'blur' }],
                pm_launch_time: [{ required: true, message: '请选择预计上线时间', trigger: 'blur' }],
            },
            fileList: [],
            importDialogVisible: false,
            uploadLimit1: 1,
            uploadAccept1: '.xls,.xlsx',
            fileData: new FormData(),
            upLoading: false,
            isUpLoadError: false,
            errorMsg: ''
        }
    },
    dicts: ['PM_MODULES_TYPE','PM_DEVICE_TYPE','PM_OVERALL_STATE','PM_URGENCY','ENABLE_FLAG','PM_PROJECT_NAME','PM_CLASSIFY'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
        // 导入文件时将文件存入数组中
        handleImport(file, fileList) {
            this.isUpLoadError = false
            this.fileList = fileList
        },
        // 点击上传时覆盖默认上传事件
        uploadFile: function(file) {
            this.fileData.set('file', file.file)
        },
        /** 文件正在上传时的钩子 **/
        progressA(event, file) {},
            // 上传按钮事件
        toButDrawerUpload() {
            if (this.fileList.length === 0) {
                this.$message({
                    message: '请选择文件',
                    type: 'info'
                })
                return
            }
            const userName = Cookies.get('userName')
            if (userName) {
                this.fileData.set('userName', userName)
            }
            this.$refs.upload.submit()

            // 配置路径
            var method = 'pm/core/SysPmImport'
            var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
            // var path = 'http://*************:9089/' + method
            this.upLoading = true
            axios
                .post(path, this.fileData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: progressEvent => {
                    const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
                    this.$refs.upload.onProgress({ percent: num }) // 进度条
                }
                })
                .then(response => {
                const defaultQuery = JSON.parse(JSON.stringify(response))
                if (defaultQuery.data.code === 0) {
                    this.$message({
                    type: 'success',
                    message: '导入成功!'
                    })
                    this.fileList = []
                    this.isUpLoadError = false
                    this.upLoading = false
                    this.importDialogVisible = false
                    this.crud.toQuery()
                } else {
                    this.upLoading = false
                    // 失败后清除文件，不然改完再次上传会报错
                    this.fileList = []
                    this.errorMsg = defaultQuery.data.msg
                    this.isUpLoadError = true
                    this.$message({
                    type: 'error',
                    message: '导入失败!'
                    })
                }
                }).catch((ex) => {
                this.upLoading = false
                // 失败后清除文件，不然改完再次上传会报错
                this.fileList = []
                this.$message({
                    message: '导入异常' + ex,
                    type: 'error'
                })
            })
        },
        changeEnabled(data, val) {
            this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            })
            .then(() => {
                crudPmEvent
                .editEnableFlag({
                user_name: Cookies.get('userName'),
                pm_id: data.pm_id,
                enable_flag: val
                })
                .then(res => {
                if (res.code === 0) {
                    this.$message({ message: '修改成功', type: 'success' })
                } else {
                    this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                    data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                }
                })
                .catch(ex => {
                this.$message({ message: '操作失败：' + ex, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
                })
            })
            .catch(() => {
            data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        },
    }
}
</script>
<style scoped lang="less">
    .earlyWarning{
        ::v-deep .el-input__inner{
            color:#ffba00 !important;
            border-color:#ffba00;
        }
    }
    .alarmWarning{
        ::v-deep .el-input__inner{
            color:#ff0000 !important;
            border-color:#ff0000;
        }
    }
</style>
  