<template>
  <div class="mainInterface">
    <el-card>
      <div class="wrapbtn">
        <el-button class="btnone" type="success">在线模式</el-button>
        <el-button class="btnone" type="success">端口1启用</el-button>
        <el-button class="btnone" type="success">端口2启用</el-button>
        <el-button class="btntwo" type="primary">手工画面</el-button>
      </div>
    </el-card>
    <el-card>
      <div>
        <table class="table3">
          <tbody>
            <tr class="trtitle">
              <td class="tdName"><span>员工号</span></td>
              <td><span>XXXXXXXX</span></td>
              <td class="tdName"><span>姓名</span></td>
              <td><span>XXXXXXXX</span></td>
              <td class="tdName"><span>部门</span></td>
              <td><span>XXXXXXXX</span></td>
              <td class="loginStyle"><el-button type="warning">登入</el-button></td>
              <td class="loginStyle"><el-button type="warning">登出</el-button></td>
            </tr>
            <tr class="trtitle">
              <td class="tdName"><span>机台状态</span></td>
              <td><span class="normal">在线</span></td>
              <td class="tdName"><span>生产模式</span></td>
              <td><span>EAP</span></td>
              <td class="tdName"><span>板件模式</span></td>
              <td><span>读码</span></td>
              <td class="tdName"><span>作业端口</span></td>
              <td><span>1</span></td>
            </tr>
          </tbody>
        </table>
        <table class="table3">
          <tbody>
            <tr class="trtitle">
              <td rowspan="4" class="showImg">
                <span class="spanIMg">
                  <img src="@/assets/images/hmiLoginLeft.gif" alt="">
                </span>

              </td>
              <td class="tdName"><span>端口1步序</span></td>
              <td colspan="2"><span>子流程名称</span></td>
              <td colspan="2"><span>步骤名称</span></td>
              <td rowspan="2" class="qianzhi w150"><el-button type="warning">强制退载具</el-button></td>
            </tr>
            <tr class="trtitle">
              <td class="tdName"><span>端口1消息</span></td>
              <td colspan="4"><span>XXXXXXXX</span></td>
            </tr>
            <tr class="trtitle">
              <td class="tdName"><span>端口2步序</span></td>
              <td colspan="2"><span>子流程名称</span></td>
              <td colspan="2"><span>步骤名称</span></td>
              <td rowspan="2" class="qianzhi w150"><el-button type="warning">强制退载具</el-button></td>
            </tr>
            <tr class="trtitle">
              <td class="tdName"><span>端口2消息</span></td>
              <td colspan="4"><span>XXXXXXXX</span></td>
            </tr>
          </tbody>
        </table>
        <div class="wraptable">
          <el-table
            :data="tableData"
            style="width: 100%"
            height="216"
          >
            <el-table-column 
              prop="serial"
              label="序号"
              width="60"
            />
            <el-table-column 
              prop="barcode"
              label="模板条码"
            />
            <el-table-column 
              prop="tray"
              label="Tray条码"
            />
            <el-table-column 
              prop="first"
              label="首件"
            />
            <el-table-column 
              prop="status"
              label="状态"
              width="60"
            />
            <el-table-column 
              prop="time"
              label="时间"
            />
            <el-table-column 
              prop="dummy"
              label="Dummy"
            />
          </el-table>
          <table class="table3" style="width:52.5%">
            <tbody>
              <tr class="trtitle">
                <td class="tdName"><span>批次号</span></td>
                <td><span>XXXXXXXX</span></td>
                <td class="tdName"><span>面次</span></td>
                <td><span class="jianju">1</span></td>
                <td class="tdName"><span>是否首板</span></td>
                <td><span>dummy</span></td>
                <td class="tdName"><span>作业模式</span></td>
                <td><span>dummy</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName"><span>完工/计划</span></td>
                <td><span>0</span>/<span>30</span></td>
                <td class="tdName"><span>NG</span></td>
                <td><span class="jianju">0</span></td>
                <td class="tdName"><span>首板完工/计划</span></td>
                <td><span>0</span>/<span>1</span></td>
                <td class="tdName"><span>长/宽/高</span></td>
                <td><span>0/0/0</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName"><span>载具/天盖</span></td>
                <td colspan="8"><span>XXXXXXXX</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName"><span>Tray盘码</span></td>
                <td colspan="8"><span>XXXXXXXX</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName"><span>板件码</span></td>
                <td colspan="8"><span>XXXXXXXX</span></td>
              </tr>
            </tbody>
          </table>

        </div>
      </div>
    </el-card>
  </div>
</template>
<script>
export default {
  name: 'mainInterface',
  data() {
    return {
      tableData: [{
        serial: '0/0/0',
        barcode: '0/0/0',
        tray: '0/0/0',
        first: '0/0/0',
        status: '0/0/0',
        dummy: '0/0/0',
        time: '0/0/0'
      }, {
        serial: '0/0/0',
        barcode: '0/0/0',
        tray: '0/0/0',
        first: '0/0/0',
        status: '0/0/0',
        dummy: '0/0/0',
        time: '0/0/0'
      }, {
        serial: '0/0/0',
        barcode: '0/0/0',
        tray: '0/0/0',
        first: '0/0/0',
        status: '0/0/0',
        dummy: '0/0/0',
        time: '0/0/0'
      }, {
        serial: '0/0/0',
        barcode: '0/0/0',
        tray: '0/0/0',
        first: '0/0/0',
        status: '0/0/0',
        dummy: '0/0/0',
        time: '0/0/0'
      }, {
        serial: '0/0/0',
        barcode: '0/0/0',
        tray: '0/0/0',
        first: '0/0/0',
        status: '0/0/0',
        dummy: '0/0/0',
        time: '0/0/0'
      }]
    }
  },
  methods: {

  }
}
</script>
<style lang="less" scoped>
/* Table 3 Style */
table.table3{
    text-align:center;
    border-collapse:collapse;
    width: 100%;
    margin: 5px 0;
}
.table3 tbody td{
    font-size: 16px;
    font-weight: bold;
    border: 1px solid #e4e4e4;
    height: 43px;
    background-color: #ffffff;
}
.tdName{
  background-color: #f7f7f7 !important;
      width: 160px;
      color: #333333;
      box-shadow: inset 2px 2px 2px 0px rgb(255 255 255 / 50%), inset -7px -7px 10px 0px rgb(0 0 0 / 10%), 7px 7px 20px 0px rgb(0 0 0 / 10%), 4px 4px 5px 0px rgb(0 0 0 / 10%);
    text-shadow: 2px 2px 3px rgb(255 255 255 / 50%), -4px -4px 6px rgb(116 125 136 / 20%);
}
.showImg{
    background: #e6fbfe !important;
    color: #333333;
        width: 47.5%;
    height: 260px !important;
    .spanIMg{
      height: 260px !important;
      img{
      object-fit: fill;
    }
    }

}
.loginStyle{
  width: 150px;
  button{
    width: 162px;
    height: 40px;
    border-radius: 0;
    font-weight: 700;
    font-size: 16px;
    background-color: #229f99;
    border: 0;
  }
  button:active{
    background-color: #0a8c86;
  }
}
.qianzhi{
  button{
  background: #3d98cf;
    color: #ffffff;
    font-size: 18px;
    width: 150px;
    height: 127px;
    border-radius: 0;
    border:0
  }
  button:active{
      background-color: #096ca8 ;
    }

}
.normal{
  color: #67C23A;
  font-weight: 700;
}
.wrapbtn{
  .btnone{
    background: #259f92;
    border-color: #259f92;
  }
  .btnone:active{
    background: #13887c;
  }
  .btntwo{
    background: #3d98cf;
  }
  .btntwo:active{
    background: #096ca8;
  }
  button{
    border-radius: 6px;
    width: 150px;
    height: 45px;
    font-size: 16px;
    font-weight: 700;
  }
}
.wraptable{
  display: flex;
}
.jianju{
    width: 45px;
    display: inline-block;
}
::v-deep .el-table{
  margin-top: 5px;
}
::v-deep .el-table th {
    font-size: 16px;
    font-weight: bold;
    background-color: #f7f7f7 !important;
    border: 1px solid #e4e4e4 !important;
    // box-shadow: 0px 20px 5px rgba(0,0,0,0.7), 5px -5px 0 rgba(0,0,0,1);
    // box-shadow: inset 2px 2px 2px 0px rgba(255,255,255,0.5), inset -7px -7px 10px 0px rgba(0,0,0,0.1), 7px 7px 20px 0px rgba(0,0,0,0.1), 4px 4px 5px 0px rgba(0, 0, 0,0.1);
    outline: none;
}
::v-deep .el-table .cell{
  text-align: center;
}
::v-deep .el-table td.el-table__cell div{
  font-size: 14px;
  font-weight: 700;
}
.w150{
  width: 150px;
}
</style>
