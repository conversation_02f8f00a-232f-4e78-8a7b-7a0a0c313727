<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logoThn" class="sidebar-logo">
        <!-- <h1 v-else class="sidebar-title">{{ title }} </h1> -->
      </router-link>
      <!-- to="/" -->
      <img v-else :src="logo" class="sidebar-logo logo1" @click="showDialog = true">
      <!-- <h1 class="sidebar-title" >{{ title }}</h1> -->
    </transition>
    <div v-show="showDialog" class="el-dialog__wrapper dialogStyle" style="z-index: 2001;">
      <div class="el-dialog dialogradius" style="margin-top: 15vh; width: 45%;">
        <div class="el-dialog__header">
          <span class="el-dialog__title">系统图片</span><button type="button" class="el-dialog__headerbtn" @click="showDialog = false"><i class="el-dialog__close el-icon el-icon-close" /></button>
        </div>
        <div class="el-dialog__body">
          <div class="wrapUplod">
            <div class="wrapUploadOne">
              <el-upload ref="uploadIco" class="avatar-uploader" action="" :multiple="false" :auto-upload="false" :limit="uploadLimitIco" :accept="uploadAcceptIco" :file-list="fileListIco" :on-change="handleUploadOnChangeIco" :http-request="handleUploadHttpRequestIco">
                <img v-if="imageUrlIco" :src="imageUrlIco" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <div class="logoStyle">Ico图(建议上传：20*20像素)</div>
            </div>

            <div class="wrapUploadOne">
              <el-upload ref="uploadLogoThn" class="avatar-uploader" action="" :multiple="false" :auto-upload="false" :limit="uploadLimitLogoThn" :accept="uploadAcceptLogoThn" :file-list="fileListLogoThn" :on-change="handleUploadOnChangeLogoThn" :http-request="handleUploadHttpRequestLogoThn">
                <img v-if="imageUrlLogoThn" :src="imageUrlLogoThn" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <div class="logoStyle">缩略图(建议上传：32*32像素)</div>
            </div>

            <div class="wrapUploadOne">
              <el-upload ref="uploadLogo" class="avatar-uploader" action="" :multiple="false" :auto-upload="false" :limit="uploadLimitLogo" :accept="uploadAcceptLogo" :file-list="fileListLogo" :on-change="handleUploadOnChangeLogo" :http-request="handleUploadHttpRequestLogo">
                <img v-if="imageUrlLogo" :src="imageUrlLogo" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <div class="logoStyle">Logo图(建议上传：70*70像素)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Cookies from 'js-cookie'
import Logo from '@/assets/images/xg1.png'
import LogoThn from '@/assets/images/xg2.png'
import { sel } from '@/api/core/system/sysLogo'
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      // title: 'JHONG',
      logo: Logo,
      logoThn: LogoThn,
      showDialog: false,

      // 文件上传Ico
      uploadLimitIco: 1,
      uploadAcceptIco: '*.ico',
      fileListIco: [],
      imageUrlIco: '',

      // 文件上传LogoThn(缩略图)
      uploadLimitLogoThn: 1,
      uploadAcceptLogoThn: '*.png',
      fileListLogoThn: [],
      imageUrlLogoThn: '',

      // 文件上传Logo
      uploadLimitLogo: 1,
      uploadAcceptLogo: '*.png',
      fileListLogo: [],
      imageUrlLogo: ''
    }
  },
  created: function() {
    this.toButQuery()
  },
  methods: {
    // 查询LOGO图片信息按钮
    toButQuery() {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName')
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.logo = 'data:image/png;base64,' + defaultQuery.data[0].logo
              this.logoThn = 'data:image/png;base64,' + defaultQuery.data[0].logo_thn

              this.imageUrlIco = 'data:image/png;base64,' + defaultQuery.data[0].ico
              this.imageUrlLogoThn = 'data:image/png;base64,' + defaultQuery.data[0].logo_thn
              this.imageUrlLogo = 'data:image/png;base64,' + defaultQuery.data[0].logo
            }
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: '查询数据失败'
          })
        })
    },

    // ----------------------------------------------------------------
    // Ico
    // 导入文件时将文件存入数组中
    handleUploadOnChangeIco(file, fileList) {
      this.fileListIco = fileList
      this.imageUrlIco = URL.createObjectURL(file.raw)
      // 手动提交
      this.$refs.uploadIco.submit()
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequestIco(file) {
      if (this.fileListIco.length === 0) {
        this.$message({
          message: '请选择ICO图片',
          type: 'info'
        })
        return
      }
      this.fileDataIco = new FormData()
      this.fileDataIco.append('type', 'ICO')
      this.fileDataIco.append('user_name', Cookies.get('userName'))
      // 上传文件对象
      this.fileDataIco.append('file', file.file)
      // 配置路径
      var method = '/core/system/CoreSysLogoIns'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      axios
        .post(path, this.fileDataIco, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.uploadIco.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: 'ICO图片上传成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
    },

    // ----------------------------------------------------------------
    // LogoThn(缩略图)
    // 导入文件时将文件存入数组中
    handleUploadOnChangeLogoThn(file, fileList) {
      this.fileListLogoThn = fileList
      this.imageUrlLogoThn = URL.createObjectURL(file.raw)
      // 手动提交
      this.$refs.uploadLogoThn.submit()
    },
    // 处理上传图片
    handleUploadHttpRequestLogoThn(file) {
      if (this.fileListLogoThn.length === 0) {
        this.$message({
          message: '请选择缩略图',
          type: 'info'
        })
        return
      }
      this.fileDataLogoThn = new FormData()
      this.fileDataLogoThn.append('type', 'LOGO_THN')
      this.fileDataLogoThn.append('user_name', Cookies.get('userName'))
      // 上传文件对象
      this.fileDataLogoThn.append('file', file.file)
      // 配置路径
      var method = '/core/system/CoreSysLogoIns'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      axios
        .post(path, this.fileDataLogoThn, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.uploadLogoThn.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.logoThn = 'data:image/png;base64,' + defaultQuery.data.result
            this.$message({
              type: 'success',
              message: '缩略图上传成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
    },

    // ----------------------------------------------------------------
    // Logo
    // 导入文件时将文件存入数组中
    handleUploadOnChangeLogo(file, fileList) {
      this.fileListLogo = fileList
      this.imageUrlLogo = URL.createObjectURL(file.raw)
      // 手动提交
      this.$refs.uploadLogo.submit()
    },
    // 处理上传图片
    handleUploadHttpRequestLogo(file) {
      if (this.fileListLogo.length === 0) {
        this.$message({
          message: '请选择Logo图片',
          type: 'info'
        })
        return
      }
      this.fileDataLogo = new FormData()
      this.fileDataLogo.append('type', 'LOGO')
      this.fileDataLogo.append('user_name', Cookies.get('userName'))
      // 上传文件对象
      this.fileDataLogo.append('file', file.file)
      // 配置路径
      var method = '/core/system/CoreSysLogoIns'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      axios
        .post(path, this.fileDataLogo, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.uploadLogo.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.logo = 'data:image/png;base64,' + defaultQuery.data.result
            this.$message({
              type: 'success',
              message: '图片上传成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 55px;
  text-align: center;
  overflow: hidden;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  .logo1 {
    cursor: pointer;
    height: 55px;
  }

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 6px;
    }

    & .sidebar-title {
      padding: 0px 15px 0px 15px;
      border-radius: 50%;
      display: inline-block;

      margin-top: 10px;
      color: #fff;
      font-weight: 600;
      font-size: 30px;
      font-family: YouYuan, Helvetica Neue, Arial, Helvetica, sans-serif;

      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
.dialogStyle {
  background: rgba(0, 0, 0, 0.3);
  transition: 0.4s;
}
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 140px;
  height: 140px;
  line-height: 140px;
  text-align: center;
}
::v-deep .avatar {
  width: 140px;
  height: 140px;
  display: block;
}
.logoStyle {
  margin-top: 20px;
  font-size: 12px;
}
.wrapUplod {
  display: flex;
  justify-content: space-around;
}
.dialogradius {
  border-radius: 10px;
}
.upbtn {
  margin-top: 10px;
}
.el-dialog__header {
  background: linear-gradient(45deg, #2e5bff, #92daff);
  padding: 10px;
  border-radius: 0 0 25px 25px;
}
.el-dialog__title {
  font-size: 16px;
  color: #ffffff;
}
.el-dialog__headerbtn {
  top: 13px;
}
.el-dialog__close {
  color: #ffffff;
}
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #d2e0ed;
}
.el-dialog__body {
  padding: 40px 20px;
}
</style>
