import request from '@/utils/request'

// 根据array_barcode解绑set
export function unbindSet(data) {
  return request({
    url: 'aisEsbWeb/pack/project/tripod/op/UnbindSetByArrayBarcodeForHB',
    method: 'post',
    data
  })
}

// 根据pile_barcode解绑包装pile下的所有set
export function unbindPile(data) {
  return request({
    url: 'aisEsbWeb/pack/project/tripod/op/UnbindPileByPileBarcodeForHB',
    method: 'post',
    data
  })
}

export default { unbindSet, unbindPile }
