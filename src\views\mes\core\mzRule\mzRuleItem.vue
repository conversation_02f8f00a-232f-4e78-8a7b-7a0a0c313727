<template>
  <!--模组电芯-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
        <el-form-item label="电芯序号" prop="dx_num">
          <el-input v-model.number="form.dx_num" />
        </el-form-item>

        <!--快速编码：DX_TYPE-->
        <el-form-item label="电芯类型" prop="dx_type">
          <fastCode fastcode_group_code="DX_TYPE" :fastcode_code.sync="form.dx_type" control_type="select" size="small" />
        </el-form-item>
        <!--快速编码：LINE_NUM-->
        <el-form-item label="线体" prop="line_num">
          <fastCode fastcode_group_code="LINE_NUM" :fastcode_code.sync="form.line_num" control_type="select" size="small" />
        </el-form-item>
        <!--快速编码：DX_DIRECT-->
        <el-form-item label="方向" prop="dx_direct">
          <fastCode fastcode_group_code="DX_DIRECT" :fastcode_code.sync="form.dx_direct" control_type="select" size="small" />
        </el-form-item>
        <!--快速编码：DX_GEAR-->
        <el-form-item label="档位" prop="dx_gear">
          <fastCode fastcode_group_code="DX_GEAR" :fastcode_code.sync="form.dx_gear" control_type="select" size="small" />
        </el-form-item>
        <!--快速编码：DX_SHEET_TPYE-->
        <el-form-item label="片材类型" prop="sheet_type">
          <fastCode fastcode_group_code="DX_SHEET_TPYE" :fastcode_code.sync="form.sheet_type" control_type="select" size="small" />
        </el-form-item>
        <el-form-item label="正极剪裁量" prop="positive_cut">
          <el-input v-model="form.positive_cut" />
        </el-form-item>
        <el-form-item label="负极剪裁量" prop="negative_cut">
          <el-input v-model="form.negative_cut" />
        </el-form-item>
        <el-form-item label="备注说明" prop="dx_notes">
          <el-input v-model="form.dx_notes" />
        </el-form-item>
        <el-form-item label="属性1" prop="attribute1">
          <el-input v-model="form.attribute1" />
        </el-form-item>
        <el-form-item label="属性2" prop="attribute2">
          <el-input v-model="form.attribute2" />
        </el-form-item>
        <el-form-item label="属性3" prop="attribute3">
          <el-input v-model="form.attribute3" />
        </el-form-item>

        <el-form-item label="有效标识">
          <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
        </el-form-item>
      </el-form>

      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="478" highlight-current-row @selection-change="crud.selectionChangeHandler">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="mz_dx_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="dx_num" label="电芯序号" />
          <el-table-column  :show-overflow-tooltip="true" prop="line_num" label="线体" />
          <el-table-column  :show-overflow-tooltip="true" prop="dx_type" label="电芯类型" />
          <el-table-column  :show-overflow-tooltip="true" prop="positive_cut" label="正极剪裁量" />
          <el-table-column  :show-overflow-tooltip="true" prop="negative_cut" label="负极剪裁量" />
          <el-table-column  :show-overflow-tooltip="true" prop="sheet_type" label="片材类型" />
          <el-table-column  :show-overflow-tooltip="true" prop="dx_direct" label="方向" />
          <el-table-column  :show-overflow-tooltip="true" prop="dx_gear" label="档位" />
          <el-table-column  :show-overflow-tooltip="true" prop="dx_notes" label="备注说明" />

          <el-table-column  label="有效标识" align="center" prop="enable_flag" width="100">
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>

          <el-table-column  label="操作" width="115" align="center" fixed="right">
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import crudMzDxRule from '@/api/mes/core/mzDxRule'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  mz_dx_id: '',
  mz_id: '',
  dx_num: '',
  line_num: '',
  dx_direct: '',
  dx_gear: '',
  dx_notes: '',
  sheet_type: '',
  dx_type: '',
  positive_cut: '0',
  negative_cut: '0',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MzDxRule',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    mz_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '模组电芯',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'mz_dx_id',
      // 排序
      sort: ['mz_dx_id asc'],
      // CRUD Method
      crudMethod: { ...crudMzDxRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      queryOnPresenterCreated: false
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_mz_dx:add'],
        edit: ['admin', 'c_mes_fmod_recipe_mz_dx:edit'],
        del: ['admin', 'c_mes_fmod_recipe_mz_dx:del'],
        down: ['admin', 'c_mes_fmod_recipe_mz_dx:down']
      },
      rules: {
        // 提交验证规则
        dx_num: [{ required: true, message: '请输入电芯序号', trigger: 'blur' }]
      }
    }
  },
  watch: {
    mz_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.mz_id = this.mz_id
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {},
  methods: {
    // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.mz_id = this.mz_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.mz_id = this.mz_id
      return true
    }
  }
}
</script>
