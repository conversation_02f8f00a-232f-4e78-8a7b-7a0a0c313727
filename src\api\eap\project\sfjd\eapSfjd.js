import request from '@/utils/request'

// 查询配方
export function recipeSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/EapProjectSfjdRecipeSelect',
    method: 'post',
    data
  })
}

// 查询安灯
export function andonSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/EapProjectSfjdAndonSelect',
    method: 'post',
    data
  })
}

// 写入配方
export function EapProjectSfjdOpeLogIns(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/EapProjectSfjdOpeLogIns',
    method: 'post',
    data
  })
}
// 查询
export function EapProjectSfjdOpeLogSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/project/sfjd/EapProjectSfjdOpeLogSelect',
    method: 'post',
    data
  })
}

export default { recipeSelect, andonSelect, EapProjectSfjdOpeLogIns, EapProjectSfjdOpeLogSelect }
