<template>
  <el-container :style="'height:' + height + 'px'">
    <el-header>
      <img :src="logo" class="hmi-logo">
      <div style="float:right;height:60px;line-height:60px;color:#FFFFFF;font-weight:600;">
        <span style="text-decoration:underline;cursor: pointer;" @click="openStationChoose">{{ currentStation.prod_line_code + ' ' + currentStation.prod_line_des }}</span>&nbsp;&nbsp;&nbsp;&nbsp; <span style="text-decoration:underline;cursor: pointer;" @click="openStationChoose">工位：{{ currentStation.station_code + ' ' + currentStation.station_des }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
        <span>当前用户：{{ user.nickName }}&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span>在线时长：60分钟&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span style="text-decoration:underline;cursor: pointer;"><i class="el-icon-switch-button" />&nbsp;登出</span>
      </div>
      <el-dialog title="工位选择" :visible.sync="dialogVisible" width="80%" top="65px">
        <div style="width:100%">
          <div v-for="(item, index) in stationData" :key="index" style="margin-bottom:20px;">
            <el-tag :key="index" type="info" :disable-transitions="false" style="line-height:40px;height:40px;">
              {{ item.prod_line_code + ' ' + item.prod_line_des }}
            </el-tag>
            <div v-if="item.station_list.length > 0" style="margin-top:10px;">
              <el-tag v-for="(item1, index1) in item.station_list" :key="index1" :disable-transitions="false" style="margin-right:10px;line-height:50px;height:50px;cursor: pointer;" @click="handleStationChoose(item, item1)">
                {{ item1.station_code + ' ' + item1.station_des }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-dialog>
    </el-header>
    <el-main style="background-color: #e6e8ee">
      <el-tabs id="hmiTabs" v-model="editableTabsValue1" :closable="true" @tab-remove="handleRemove">
        <el-tab-pane v-for="(item, index) in editableTabs1" :key="index" :label="item.title" :name="item.name" style="padding: 0px; background-color: #e6e8ee">
          <!-- <elFrame :src="item.path" /> -->
          <elFrame :src="item.path + '?line_code=' + currentStation.prod_line_code + '&station_code=' + currentStation.station_code" />
        </el-tab-pane>
      </el-tabs>
    </el-main>
    <el-footer style="height: 65px">
      <table style="width: 100%; border: 0px">
        <tr>
          <td style="width: 20px">
            <el-button
              v-show="hmiMenuData.length !== 0"
              class="filter-item"
              size="medium"
              type="primary"
              icon="el-icon-arrow-left"
              style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
              plain
              @click="marginLeft !== 0 ? (marginLeft += 100) : 0"
            />
          </td>
          <td>
            <el-scrollbar ref="scrollbar" style="width: 100%">
              <div :style="'width: 400%;margin-left:' + marginLeft + 'px;'">
                <template v-for="(item, index) in hmiMenuData">
                  &nbsp;

                  <el-button
                    :key="item.id"
                    class="filter-item"
                    size="medium"
                    type="primary"
                    :icon="item.menu_icon"
                    style="
                            height: 50px;
                            margin-top: 5px;
                            font-weight: bold;
                            margin-left: 0px;
                          "
                    plain
                    @click="handlesSelect(item, index, item)"
                  >
                    {{ item.current_menu_des === '' ? item.menu_des : item.current_menu_des }}
                  </el-button>
                </template>
              </div>
            </el-scrollbar>
          </td>
          <td style="width: 20px">
            <el-button
              v-show="hmiMenuData.length !== 0"
              class="filter-item"
              size="medium"
              type="primary"
              icon="el-icon-arrow-right"
              style="
                    height: 50px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
              plain
              @click="marginLeft -= 100"
            />
          </td>
        </tr>
      </table>
    </el-footer>
  </el-container>
</template>

<script>
import { sel } from '@/api/core/system/sysLogo'
import elFrame from '@/views/hmi/core/iframe'
import { sel as selHmiInfo } from '@/api/hmi/main'
import Cookies from 'js-cookie'
import { mapGetters } from 'vuex'

export default {
  name: 'layout',
  components: {
    elFrame
  },
  data() {
    return {
      logo: '',
      dialogVisible: false,
      height: document.documentElement.clientHeight,
      hmiMenuData: [],
      editableTabsValue1: '0',
      editableTabs1: [],
      marginLeft: 0,
      marginLeft1: 0,
      stationData: [],
      currentStation: {
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: ''
      }
    }
  },
  computed: {
    ...mapGetters(['user'])
  },
  watch: {},
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight
    }
  },
  created: function() {
    this.getLogo()
    this.handleRefresh('127.0.0.1', '8090')
  },
  methods: {
    getLogo() {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName')
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.logo = 'data:image/png;base64,' + defaultQuery.data[0].logo
            }
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: '查询数据失败'
          })
        })
    },
    openStationChoose() {
      this.dialogVisible = true
    },
    handleStationChoose(item, item1) {
      this.editableTabs1 = []
      this.currentStation = {
        prod_line_code: item.prod_line_code,
        prod_line_des: item.prod_line_des,
        station_id: item1.station_id,
        station_code: item1.station_code,
        station_des: item1.station_des
      }
      this.dialogVisible = false
    },
    handleRefresh(line_code, station_code) {
      this.editableTabs1 = []
      this.hmiMenuData = []
      const query = {
        user_name: Cookies.get('userName'),
        userID: Cookies.get('userId')
      }
      selHmiInfo(query)
        .then(res => {
          this.stationData = res.station
          if (res.station.length > 0) {
            this.currentStation = {
              prod_line_code: this.stationData[0].prod_line_code,
              prod_line_des: this.stationData[0].prod_line_des,
              station_id: this.stationData[0].station_list[0].station_id,
              station_code: this.stationData[0].station_list[0].station_code,
              station_des: this.stationData[0].station_list[0].station_des
            }
          }
          const menuData = res.menu

          // this.hmiMenuData = defaultQuery.data;
          var dd = []
          for (let i = 0; i < menuData.length; i++) {
            const element = menuData[i]
            const id = element.menu_item_id
            const menu_des = element.menu_item_des
            const path = element.function_path
            const hmi_menu_ico = element.hmi_menu_ico

            var aa = {}
            aa.id = '1-' + id
            aa.menu_des = menu_des
            aa.button_type = 'primary'
            aa.current_menu_des = ''
            aa.menu_icon = hmi_menu_ico === '' ? 'el-icon-s-platform' : hmi_menu_ico
            aa.path = path // 'http://' + server_host + ':' + cell_port + path // http://localhost:8013/hmi
            dd.push(aa)
          }
          this.hmiMenuData = dd
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: '查询数据失败'
          })
        })
    },
    handlesSelect(item, index, parent) {
      console.log(item)
      let obj = {}
      const TabName = 'Tab_' + item.id
      obj = this.editableTabs1.find(item => {
        return item.name === TabName
      })
      const currentMenu1 = this.editableTabs1.filter(item => item.button_type === 'warning')
      if (currentMenu1.length > 0) {
        currentMenu1[0].button_type = 'primary'
      }
      if (typeof obj !== 'undefined') {
        obj.button_type = 'warning'
        this.editableTabsValue1 = TabName
      } else {
        this.editableTabs1.push({
          title: item.menu_des,
          name: TabName,
          path: item.path,
          icon: item.menu_icon,
          button_type: 'warning'
        })
        this.editableTabsValue1 = TabName
      }

      const currentMenu = this.hmiMenuData.filter(item => item.button_type === 'warning')
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
        currentMenu[0].current_menu_des = ''
      }
      parent.current_menu_des = item.menu_des
      parent.button_type = 'warning'
      this.$emit('showCellNavbar', item.menu_des)
    },
    handleRemove(tabName) {
      for (var i = 0; i < this.editableTabs1.length; i++) {
        if (this.editableTabs1[i].name === tabName) {
          this.editableTabs1.splice(i, 1)
        }
      }
    },
    handlesOpenedMenu(item, index) {
      const currentMenu = this.editableTabs1.filter(item => item.button_type === 'warning')
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
      }
      item.button_type = 'warning'
      this.editableTabsValue1 = item.name
      this.$emit('showCellNavbar', item.title)
    }
  }
}
</script>
<style lang="less" scoped>
.el-header {
  background-color: #79a0f1;
}
.el-footer {
  background-color: #79a0f1;
  color: #333;
  line-height: 60px;
  padding: 0px;
  overflow: hidden;
}
.el-main {
  background-color: #ffffff;
  color: #333;
  padding: 0px;
}
body > .el-container {
  margin-bottom: 40px;
}
.hmi-logo {
  width: 120px;
  height: 32px;
  margin-right: 6px;
  margin-top: 15px;
}
</style>
<style lang="less" scoped>
#hmiTabs .el-tabs__header {
  margin: 0 0 0px;
  display: none;
}
</style>
