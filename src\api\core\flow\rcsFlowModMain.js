import request from '@/utils/request'

// 查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainSel',
    method: 'post',
    data
  })
}
// 新增
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainIns',
    method: 'post',
    data
  })
}
// 修改
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainUpd',
    method: 'post',
    data
  })
}
// 删除
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainDel',
    method: 'post',
    data
  })
}
// 保存流程图
export function saveChart(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainChartSave',
    method: 'post',
    data
  })
}
// 查询可以复制的流程图
export function selChart(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainListSel',
    method: 'post',
    data
  })
}
// 复制流程
export function copyChart(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainCopy',
    method: 'post',
    data
  })
}
// 查询流程图子流程与步骤的树形结构
export function selSubStepTree(data) {
  return request({
    url: 'aisEsbWeb/core/flow/CoreFlowModMainSubStepTree',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, saveChart, selChart, copyChart, selSubStepTree }

