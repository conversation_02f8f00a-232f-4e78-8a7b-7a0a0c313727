import request from '@/utils/request'

// 查询机型基础
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelSelect',
    method: 'post',
    data
  })
}
// 新增机型基础
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelInsert',
    method: 'post',
    data
  })
}
// 修改机型基础
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelUpdate',
    method: 'post',
    data
  })
}
// 删除机型基础
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelDelete',
    method: 'post',
    data
  })
}
// 修改机型基础--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelEnableFlagUpdate',
    method: 'post',
    data
  })
}
// 导出机型基础模板
export function down(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelExportTemplate',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 修改机型基础--修改当前型号
export function FlagUpdate(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelSelectedFlagUpdate',
    method: 'post',
    data
  })
}
export function FlagUpdate2(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFmodModelSelectedFlagUpdate2',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, editEnableFlag, down, FlagUpdate, FlagUpdate2 }

