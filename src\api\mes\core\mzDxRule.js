import request from '@/utils/request'

// 配方模组电芯电芯查询
export function sel(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeMZDXSel',
        method: 'post',
        data
    })
}
// 配方模组电芯电芯增加
export function add(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeMZDXIns',
        method: 'post',
        data
    })
}
// 配方模组电芯电芯修改
export function edit(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeMZDXUpd',
        method: 'post',
        data
    })
}
// 配方模组电芯电芯删除
export function del(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeMZDXDel',
        method: 'post',
        data
    })
}

// 配方模组电芯批量编辑
export function batchEdit(data) {
    return request({
        url: 'aisEsbWeb/mes/core/MesRecipeMZDXBatchEdit',
        method: 'post',
        data
    })
}

export default { sel, add, edit, del, batchEdit }