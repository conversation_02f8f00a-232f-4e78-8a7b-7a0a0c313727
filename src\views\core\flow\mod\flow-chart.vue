<template>
  <el-container>
    <el-aside width="70px" style="background-color: #faf8f9; padding: 0px">
      <div class="tool tool_return" @click="deleteFun">
        <div class="label">返回</div>
      </div>
      <div class="tool tool_save" @click="$refs.chart.save()">
        <div class="label">保存</div>
      </div>
      <div class="tool tool_copy" @click="handleCopyChart">
        <div class="label">复制</div>
      </div>
      <div class="tool tool_sub" draggable @dragend="handleToolDragEnd($event, 'sub')">
        <div class="label">流程</div>
      </div>
      <div class="tool tool_step" draggable @dragend="handleToolDragEnd($event, 'step')">
        <div class="label">步骤</div>
      </div>
      <div class="tool tool_judge" draggable @dragend="handleToolDragEnd($event, 'judge')">
        <div class="label">条件</div>
      </div>
      <div class="tool tool_label" draggable @dragend="handleToolDragEnd($event, 'label')">
        <div class="label">标签</div>
      </div>
      <div :class="'tool ' + linkClass" @click="handleLink">
        <div style="height:17px;font-size:20px;font-weight: 600;"><i class="el-icon-share" /></div>
        <div style="font-size:12px;font-weight: 600;">连线</div>
      </div>
      <div class="tool tool_move" @click="$refs.chart.selectedReadonly()">
        <div class="label">{{ readonly ? '已选中' :'未选中' }}</div>
      </div>
      <div class="tool">
        <el-popover
          placement="right"
          width="100"
          trigger="click"
        >
          <div class="itemAlign" @click="handleAlign('top')">顶对齐</div>
          <div class="itemAlign" @click="handleAlign('right')">右对齐</div>
          <div class="itemAlign" @click="handleAlign('bottom')">低对齐</div>
          <div class="itemAlign" @click="handleAlign('left')">右对齐</div>
          <div slot="reference" class="label">对齐方式</div>
        </el-popover>
      </div>
    </el-aside>
    <el-container style="border-left: 2px solid #e7e7e7">
      <el-header>
        {{ flow_mod_main_des }}
      </el-header>
      <el-main>
        <el-dialog title="复制流程图" width="450px" top="10px" :visible.sync="dialogCopyChart" append-to-body>
          <el-select v-model="CopyFlowMainId" filterable style="width: 100%">
            <el-option
              v-for="item in FlowChartList"
              :key="item.flow_mod_main_id"
              :label="item.flow_mod_main_des"
              :value="item.flow_mod_main_id"
            />
          </el-select>
          <el-divider />
          <div style="text-align: center; margin-bottom: 10px">
            <el-button size="small" icon="el-icon-close" plain @click="handleCancelCopyChart">取消</el-button>
            <el-button type="primary" size="small" icon="el-icon-check" @click="handleSaveCopyChart">确认</el-button>
          </div>
        </el-dialog>
        <el-dialog title="标签" width="450px" top="10px" :visible.sync="dialogLabel" append-to-body>
          <el-input v-model="flow_label" :placeholder="flowLabelPlaceholder" clearable size="mini" style="width: 100%" />
          <el-divider />
          <div style="text-align: center; margin-bottom: 10px">
            <el-button size="small" icon="el-icon-close" plain @click="handleCancelLabelSave">取消</el-button>
            <el-button type="primary" size="small" icon="el-icon-check" @click="handleLabelSave">确认</el-button>
          </div>
        </el-dialog>
        <el-scrollbar ref="scrollbar" :width="'100%'" :height="height">
          <Flowchart
            ref="chart"
            v-loading="loading"
            :nodes="nodes"
            :connections="connections"
            :width="'100%'"
            :height="height"
            :deleteflag="deleteflag"
            :readonly="readonly"
            :fastlink="fastLink"
            element-loading-text="拼命绘制流程图中"
            @editnode="handleEditNode"
            @dblclick="handleDblClick"
            @editconnection="handleEditConnection"
            @save="handleChartSave"
            @delnode="handleDelNode"
            @selectChart="handleSelectChart"
            @eventValue="handleEventValue"
          />
          <!-- <div class="mini-map-container" @mousemove="mindMapMouseMove" ref="miniMapContainerRef">
            <div class="move"></div>
            <img id="imgUrl" src="" style="width: 100%; height: 100%;" alt="">
          </div> -->
        </el-scrollbar>
        <div style="position: absolute;height:100%;top:0;right:0" :style="{width:rcsFlowSubVisible ? '650px':''}">
          <RcsFlowSub
            ref="rcsFlowSub"
            :readonly="false"
            :visible.sync="rcsFlowSubVisible"
            :node-form.sync="nodeForm.target"
            @update:visible="func"
            @refreshChart="handleRefreshChart"
          />
        </div>
        <div style="position: fixed;width:650px;height:100%;top:0;right:0" :style="{width:rcsFlowSubStepVisible? '650px':''}">
          <RcsFlowStep
            ref="rcsFlowStep"
            flow_main_id="0"
            :flow_mod_main_id="flow_mod_main_id"
            :flow_mod_main_des="flow_mod_main_des"
            :readonly="false"
            :visible.sync="rcsFlowSubStepVisible"
            :node-form.sync="nodeForm.target"
            @update:visible="func"
            @refreshChart="handleRefreshChart"
          />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
/* eslint-disable no-unused-vars */

import Cookies from 'js-cookie'
import Flowchart from '@/components/FlowChart/index'
import RcsFlowSub from '@/views/core/flow/mod/sub'
import RcsFlowStep from '@/views/core/flow/mod/step'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
import { sel, saveChart, selChart, copyChart } from '@/api/core/flow/rcsFlowModMain'
import { del as delSub } from '@/api/core/flow/rcsFlowModSub'
import { del as delStep } from '@/api/core/flow/rcsFlowModSubStep'
// import html2canvas from "html2canvas"
export default {
  components: {
    Flowchart,
    RcsFlowSub,
    RcsFlowStep
  },
  props: {
    flow_mod_main_id: {
      type: [String, Number],
      default: -1
    },
    flow_mod_main_des: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      widthAll: '100%',
      height: document.documentElement.clientHeight - 60,
      nodes: [],
      connections: [],
      nodeForm: { target: null },
      connectionForm: { target: null, operation: null },
      loading: false,
      dialogLabel: false,
      flow_label: '',
      flowLabelPlaceholder: '',
      rcsFlowSubVisible: false,
      rcsFlowSubStepVisible: false,
      scrollLeft: 0,
      scrollTop: 0,
      dialogCopyChart: false,
      FlowChartList: [],
      CopyFlowMainId: '',
      fastLink: false,
      linkClass: 'tool_link',
      readonly: false,
      dataEvent: { pageX: 0, pageY: 0 },
      deleteflag: true // 这个变量是因为在流程图编辑操作的时候按delete键会弹框删除子流程，为true才可以删除子流程，为false不可删除
    }
  },
  watch: {
    flow_mod_main_id: {
      immediate: true,
      deep: true,
      handler() { }
    },
    flow_mod_main_des: {
      immediate: true,
      deep: true,
      handler() { }
    }
  },
  mounted() {
    const that = this
    // setTimeout(() => {
    //   html2canvas(document.getElementById('chart'), { useCORS: true }).then(img => {
    //     document.getElementById('imgUrl').src = img.toDataURL("image/png");
    //   })
    // }, 1000)
    this.handleScroll()
    window.onresize = () => {
      return (() => {
        that.height = document.documentElement.clientHeight - 60
      })()
    }
  },
  created() {
    this.LoadFlowChart()
    this.FlowChartList = []
    selChart({ user_name: Cookies.get('userName') })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.FlowChartList = defaultQuery.data
            const index = this.FlowChartList.findIndex(item => item.flow_mod_main_id === this.flow_mod_main_id)
            if (index !== -1) {
              this.FlowChartList.splice(index, 1)
            }
          }
        }
      })
      .catch(() => {
        this.FlowChartList = []
      })
  },
  methods: {
    handleScroll() {
      const scrollbarEl = this.$refs.scrollbar.wrap
      scrollbarEl.onscroll = () => {
        this.scrollLeft = scrollbarEl.scrollLeft
        this.scrollTop = scrollbarEl.scrollTop
      }
    },
    deleteFun() {
      this.deleteflag = false
      this.$nextTick(() => {
        this.$emit('closeDrawer')
      })
    },
    func(val) {
      this.deleteflag = true
      this.widthAll = val ? '65%' : '100%'
    },
    handleAlign(val) {
      console.log(val)
    },
    async mindMapMouseMove(event) {
      // 获取mini-map-container元素
      const thumbElem = document.getElementsByClassName('mini-map-container')[0]
      const thumbPosX = Math.round(thumbElem.getBoundingClientRect().left + document.documentElement.scrollLeft)
      const thumbPosY = Math.round(thumbElem.getBoundingClientRect().top + document.documentElement.scrollTop)
      const moveElem = document.getElementsByClassName('move')[0]
      const moveElemW = moveElem.offsetWidth
      const moveElemH = moveElem.offsetHeight
      const x = event.pageX - thumbPosX
      const y = event.pageY - thumbPosY
      moveElem.style.left = x - moveElemW / 2 + 'px'
      moveElem.style.top = y - moveElemH / 2 + 'px'
      // 判断是否要出mini-map-container范围
      if (parseInt(moveElem.style.left) < 0) {
        moveElem.style.left = 0
      } else if (parseInt(moveElem.style.left) > (thumbElem.offsetWidth - moveElemW)) {
        moveElem.style.left = thumbElem.offsetWidth - moveElemW + 'px'
      }
      if (parseInt(moveElem.style.top) < 0) {
        moveElem.style.top = 0
      } else if (parseInt(moveElem.style.top) > (thumbElem.offsetHeight - moveElemH)) {
        moveElem.style.top = thumbElem.offsetHeight - moveElemH + 'px'
      }
      // 放大的倍数
      const sca = thumbElem.offsetWidth / moveElemW
      const svg = document.getElementById('chart')
      svg.scrollTop = sca * parseInt(moveElem.style.top) * 9
      svg.scrollLeft = sca * parseInt(moveElem.style.left) * 6.8
    },
    handleDblClick(position) { },
    handleDelNode(type, id) {
      if (type === 'sub' && id !== 0) {
        delSub({
          flow_mod_sub_id: id
        })
          .then(res => {
            const defaultDel = JSON.parse(JSON.stringify(res))
            if (defaultDel.code === 0) {
              this.$message({
                message: '删除子流程成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: defaultDel.msg,
                type: 'error'
              })
            }
          })
          .catch(() => {
            this.$message({
              message: '删除子流程异常',
              type: 'error'
            })
          })
      } else if ((type === 'step' || type === 'judge') && id !== 0) {
        delStep({ step_mod_id: id })
          .then(res => {
            const defaultDel = JSON.parse(JSON.stringify(res))
            if (defaultDel.code === 0) {
              this.$message({
                message: '删除流程步骤成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: defaultDel.msg,
                type: 'error'
              })
            }
          })
          .catch(() => {
            this.$message({
              message: '删除流程步骤异常',
              type: 'error'
            })
          })
      }
    },
    async handleChartSave(nodes, connections) {
      const save = {
        user_name: Cookies.get('userName'),
        flow_mod_main_id: this.flow_mod_main_id,
        flow_mod_chart: '{"nodes":' + JSON.stringify(nodes) + ',"connections":' + JSON.stringify(connections) + '}'
      }
      // 修改
      saveChart(save)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '流程图保存成功', type: 'success' })
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'warning' })
          }
        })
        .catch(() => {
          this.$message({
            message: '保存异常',
            type: 'error'
          })
        })
    },
    handleRefreshChart(node, thisH) {
      this.$refs.chart.init(node, thisH)
      this.$refs.chart.save()
    },
    handleSelectChart(val) {
      this.readonly = val
    },
    handleEditNode(node, thisH) {
      this.rcsFlowSubStepVisible = false
      this.rcsFlowSubVisible = false
      this.nodeForm.target = node
      this.deleteflag = false
      if (node.type === 'sub') {
        this.rcsFlowSubVisible = true
        this.$nextTick(() => {
          this.$refs.rcsFlowSub.editNode(node, thisH, this.flow_mod_main_id)
        })
      } else if (node.type === 'step' || node.type === 'judge') {
        this.rcsFlowSubStepVisible = true
        this.$nextTick(() => {
          this.$refs.rcsFlowStep.editNode(node, thisH, this.flow_mod_main_id)
        })
      } else if (node.type === 'label') {
        this.flow_label = node.describe === '标签' ? '' : node.describe
        this.flowLabelPlaceholder = node.desdialogLabelcribe
        this.$nextTick(() => {
          this.dialogLabel = true
        })
      }
      // this.widthAll = (this.rcsFlowSubVisible || this.rcsFlowSubStepVisible) ? '65%' :'100%'
    },
    handleCancelLabelSave() {
      this.deleteflag = true
      this.dialogLabel = false
    },
    handleLabelSave() {
      this.nodeForm.target.describe = this.flow_label === '' ? '标签' : this.flow_label
      this.dialogLabel = false
      this.$refs.chart.save()
      this.deleteflag = true
    },
    handleEditConnection(connection) {
      // this.connectionForm.target = connection;
      // this.connectionDialogVisible = true;
    },
    LoadFlowChart() {
      this.loading = true
      sel({
        user_name: Cookies.get('userName'),
        flow_mod_main_id: this.flow_mod_main_id
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              if (defaultQuery.data[0].flow_mod_chart !== '') {
                const flow_chart = JSON.parse(defaultQuery.data[0].flow_mod_chart)
                this.nodes = flow_chart.nodes
                this.connections = flow_chart.connections
              } else {
                this.nodes = []
                this.connections = []
              }
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
          this.nodes = []
          this.connections = []
          this.loading = false
        })
    },
    handleEventValue(val) {
      this.dataEvent = val
    },
    async handleToolDragEnd(event, type) {
      // setTimeout(()=>{
      //   console.log(this.dataEvent,'1111')
      // },1000)
      await new Promise(resolve => {
        setTimeout(resolve, 200)
      })
      if (type === 'sub') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          // x: event.clientX - 100 + this.scrollLeft,
          // y: event.clientY - 90 + this.scrollTop,
          x: this.dataEvent.offsetX,
          y: this.dataEvent.offsetY,
          name: '1',
          type: 'sub',
          width: 170,
          height: 85,
          imgId: '108',
          color: '#FFFFFF',
          describe: '子流程',
          strokeWidth: 1
        })
      } else if (type === 'step') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          // x: event.clientX - 100,
          // y: event.clientY - 90,
          x: this.dataEvent.offsetX,
          y: this.dataEvent.offsetY,
          name: '1-1',
          type: 'step',
          width: 150,
          height: 70,
          imgId: '105',
          color: '#FFFFFF',
          describe: '步骤',
          strokeWidth: 1
        })
      } else if (type === 'judge') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          // x: event.clientX - 100,
          // y: event.clientY - 60,
          x: this.dataEvent.offsetX,
          y: this.dataEvent.offsetY,
          name: '1-1',
          type: 'judge',
          width: 150,
          height: 70,
          imgId: '105',
          color: '#FFFFFF',
          describe: '条件',
          strokeWidth: 1
        })
      } else if (type === 'label') {
        this.$refs.chart.add({
          id: +new Date(),
          subId_stepId: 0,
          x: this.dataEvent.offsetX,
          y: this.dataEvent.offsetY,
          name: '1',
          type: 'label',
          width: 10,
          height: 10,
          imgId: '105',
          color: '#FFFFFF',
          describe: '标签',
          strokeWidth: 1
        })
      }
    },
    handleCopyChart() {
      this.deleteflag = false
      this.dialogCopyChart = true
    },
    handleCancelCopyChart() {
      this.deleteflag = true
      this.dialogCopyChart = false
    },
    handleSaveCopyChart() {
      this.$confirm(`该操作会清除当前流程的所有配置，是否确定复制?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          copyChart({
            user_name: Cookies.get('userName'),
            from_flow_mod_main_id: this.CopyFlowMainId,
            to_flow_mod_main_id: this.flow_mod_main_id
          })
            .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              this.loading = false
              if (defaultQuery.code === 0) {
                this.$message({ message: '流程图复制成功', type: 'success' })
                this.LoadFlowChart(this.flow_mod_main_id)
              } else {
                this.$message({ message: defaultQuery.msg, type: 'warning' })
              }
            })
            .catch(() => {
              this.loading = false
              this.$message({
                message: '流程图复制异常',
                type: 'error'
              })
            })
          this.CopyFlowMainId = ''
          this.deleteflag = true
          this.dialogCopyChart = false
        })
        .catch(() => {
          this.deleteflag = true
          this.dialogCopyChart = false
        })
    },
    handleLink() {
      this.fastLink = !this.fastLink
      if (this.fastLink) {
        this.linkClass = 'tool_link_hover'
      } else {
        this.linkClass = 'tool_link'
      }
    }
  }
}
</script>
<style scoped>
.el-drawer__wrapper {
  position: absolute !important;
}
.tool {
  width: 70px;
  height: 50px;
  text-align: center;
  margin-top: 5px;
  border: 1px solid #ffffff;
  color: #7c7c7c;
  border-radius: 5px;
  cursor: pointer;
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: 23px 5px;
  -moz-user-select: none;
  /*火狐*/

  -webkit-user-select: none;
  /*webkit浏览器*/

  -ms-user-select: none;
  /*IE10*/

  -khtml-user-select: none;
  /*早期浏览器*/

  user-select: none;
}

.tool .label {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-top: 25px;
}

.tool_return {
  background-image: url('~@/assets/images/flow/return_grey.png');
}

.tool_return:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/return_white.png');
}

.tool_save {
  background-image: url('~@/assets/images/flow/save_grey.png');
}

.tool_save:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/save_white.png');
}

.tool_copy {
  background-size: 18px;
  background-position: 25px 5px;
  background-image: url('~@/assets/images/flow/copy_grey.png');
}

.tool_copy:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/copy_white.png');
}

.tool_sub {
  background-image: url('~@/assets/images/flow/sub_grey.png');
}

.tool_sub:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/sub_white.png');
}

.tool_step {
  background-image: url('~@/assets/images/flow/step_grey.png');
}

.tool_step:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/step_white.png');
}

.tool_judge {
  background-size: 15px;
  background-position: 25px 3px;
  background-image: url('~@/assets/images/flow/judge_grey.png');
}

.tool_judge:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/judge_white.png');
}

.tool_label {
  background-image: url('~@/assets/images/flow/label_grey.png');
}

.tool_label:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/label_white.png');
}
.tool_move{
  background-image: url('~@/assets/images/flow/move_grey.png');
}
.tool_move:hover {
  background-color: #445ddb;
  color: #ffffff;
  background-image: url('~@/assets/images/flow/move_white.png');
}
.tool_link_hover {
  background-color: #445ddb;
  color: #ffffff;
}

.title {
  margin-top: 10px;
  margin-bottom: 0;
}

.subtitle {
  margin-bottom: 10px;
}

.el-header {
  background-color: #f6f6f6;
  color: #333;
  font-weight: 600;
  font-size: 30px;
  text-align: center;
  line-height: 60px;
}

.el-main {
  background-color: #f1f1f1;
  color: #333;
  padding: 0px;
}
</style>
