<template>
  <div class="app-container">
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-descriptions :column="6" size="small" border>
          <el-descriptions-item label="允许扫码"><p><span :class="monitorData.PlcS_RequestSignal.value === '1' ? 'commonsatu statuOne' : 'commonsatu statuZero'" /></p></el-descriptions-item>
          <el-descriptions-item label="已扫描模组数量"><span style="font-size:20px;font-weight:700">{{ mz_Data.length }} / {{ monitorData.MNQ_Num.value }}</span></el-descriptions-item>
          <el-descriptions-item label="是否返修模式"><el-switch v-model="monitorData.MesS_ModuleBlockStatus.value" active-value="1" inactive-value="0" active-color="#13ce66" inactive-color="#ff4949" @change="handleblack" /></el-descriptions-item>
          <el-descriptions-item label="上线模组数量">
            <el-select v-model="monitorData.MNQ_Num.value" filterable @change="handleMzQuantuity">
              <el-option
                v-for="item in dict.ONLINE_MZ_QUANTITY"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item v-if="monitorData.MNQ_Num.value && mz_Data.length === Number(monitorData.MNQ_Num.value)" label="模组码扫码情况"><span style="font-size:20px;font-weight:700;color: #12dc12;">{{ '模组码扫描完成' }}</span></el-descriptions-item>
          <el-descriptions-item label="确认"><el-button class="mzScanBtn" size="mini" type="primary" @click="handleOk">确认</el-button></el-descriptions-item>
        </el-descriptions>
      </el-row>
    </el-card>
    <el-card class="wrapCard CardTwo" style="margin-top: 10px;">
      <div class="scanStyle">
        <span>模组码：</span>
        <div class="wrapimin">
          <el-input v-model="monitorData.BarS_GetBarCodeResult.value" type="text" size="large" placeholder="请输入内容" />
          <img :src="keyboard" @click="showKeyboard">
        </div>
        <el-button class="scanBtn" size="large" type="primary" @click="ManualDxScan">扫描</el-button>
      </div>
      <el-table border :data="mz_Data" style="width: 100%" :height="height" :highlight-current-row="true">
        <el-table-column label="模组码" prop="mz_barcode" />
        <el-table-column label="是否OK" prop="mz_status">
          <template slot-scope="scope">
            {{ 'OK' }}
          </template>
        </el-table-column>
        <el-table-column label="模组顺序" width="200" prop="sort">
          <template slot-scope="scope">
            <el-select v-model="scope.row.sort" filterable @change="handleMzSort(scope.row)">
              <el-option
                v-for="item in [{id:1,label:'1',value:'1'},{id:2,label:'2',value:'2'},{id:3,label:'3',value:'3'},{id:4,label:'4',value:'4'}]"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="模组状态" width="200" prop="repair_status">
          <template slot-scope="scope">
            <el-select v-model="scope.row.repair_status" filterable @change="handleMzRepairStatus(scope.row)">
              <el-option
                v-for="item in dict.MZ_STATUS"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
          <template slot-scope="scope">
            <!-- <el-button slot="reference" type="text" size="small" @click="handleChange(scope.row)">替换</el-button> -->
            <el-button slot="reference" type="text" size="small" @click="handleDelete(scope.row)">{{ $t('lang_pack.commonPage.remove') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>
<script>
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
import Cookies from 'js-cookie'
import { MesGxScanMzCheck, MesGxScanMzSelect, MesGxScanMzDelete, MesGxScanMzSubmit,MesGxMzRepairStatus} from '@/api/mes/project/manualModule.js'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  name: 'manualModule',
  components: {
    SimpleKeyboard
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 210,
      keyboard: keyboard,
      allowScan: '',
      currentInputDom: '',
      input: '',
      mz_Data: [],
      isShow: false,
      plcMzIndex: '',
      monitorData: {
        // PLC请求写入信号
        PlcS_RequestSignal: { client_code: 'OP2000', group_code: 'PlcStatus', tag_code: 'PlcS_RequestSignal', value: '' },
        // Bar扫描条码结果
        BarS_GetBarCodeResult: { client_code: 'OP2001', group_code: 'BarStatus', tag_code: 'BarS_GetBarCodeResult', value: '' },
        // Bar获取扫描需求指令
        BarS_ScanBarCodeOrde: { client_code: 'OP2001', group_code: 'BarStatus', tag_code: 'BarS_ScanBarCodeOrde', value: '' },
        // 返修标准
        MesS_ModuleBlockStatus: { client_code: 'OP2000', group_code: 'MesRecipe', tag_code: 'MesS_ModuleBlockStatus', value: '0' },
        // 扫描数量
        MNQ_Num: { client_code: 'OP2002', group_code: 'BarStatus', tag_code: 'MNQ_Num', value: '4' }
      },
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      scanBarFlag: true,
      timer: null
    }
  },
  dicts: ['ONLINE_MZ_QUANTITY', 'MZ_STATUS'],
  mounted: function() {
    // 启动监控
    this.toStartWatch()
    this.mzDataSelect()
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 210
    }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  methods: {
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },
    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // 删除键的相应操作
      }
    },
    showKeyboard() {
      this.isShow = true
    },
    ManualDxScan() {
      // 判断是否可以扫描模组
      if (this.mz_Data.length >= Number(this.monitorData.MNQ_Num.value) && this.plcMzIndex === '') {
        this.$message({
          message: `最多扫描${this.mz_Data.length}组模组码`,
          type: 'warning'
        })
        return
      }
      if (this.monitorData.PlcS_RequestSignal.value !== '1') {
        this.$message({
          message: 'PLC请求信号未接入不可扫描模组码',
          type: 'warning'
        })
        return
      }
      if (this.mz_Data.some(e => e.tag_value === this.monitorData.BarS_GetBarCodeResult.value)) {
        this.$message({
          message: '该模组码已扫描，请重新扫描',
          type: 'warning'
        })
        return
      }
      if (this.monitorData.BarS_GetBarCodeResult.value === '') {
        this.$message({
          message: '输入模组码不能为空',
          type: 'warning'
        })
        return
      }
      this.toGxDxOnlineCheck()
    },
    handleMzSort(data) {
      // this.plcMzIndex = data.sort
      this.toGxDxOnlineCheck(data)
    },
    handleMzRepairStatus(data){
      data.repair_flag=this.monitorData.MesS_ModuleBlockStatus.value;
      MesGxMzRepairStatus(data).then(res=>{
        if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            setTimeout(() => {
              this.mzDataSelect()
            }, 1000)
          }
      });
    },
    toGxDxOnlineCheck(data) {
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: this.$route.query.prod_line_id,
        station_code: this.$route.query.station_code,
        mz_barcode: (data && data.mz_barcode) ? data.mz_barcode : this.monitorData.BarS_GetBarCodeResult.value,
        mz_index: (data && data.sort) ? data.sort : '',
        bar_num: Number(this.monitorData.MNQ_Num.value)
      }
      MesGxScanMzCheck(query)
        .then(res => {
          this.plcMzIndex = ''
          if (res.code !== 0) {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              center: true,
              showCancelButton: false
            })
          } else {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            setTimeout(() => {
              this.mzDataSelect()
            }, 1000)
          }
        })
        .catch(() => {
          this.$message({
            message: '模组上线校验异常',
            type: 'error'
          })
        })
    },
    handleChange(data) {
      this.$confirm('确认要替换这条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!this.monitorData.BarS_GetBarCodeResult.value) {
          this.$message({
            message: '请先输入模组码',
            type: 'error'
          })
          return
        }
        this.plcMzIndex = data.sort
        this.ManualDxScan()
      })
    },
    handleOk() {
      if (this.monitorData.MNQ_Num.value && this.mz_Data.length === Number(this.monitorData.MNQ_Num.value)) {
        const query = {
          station_code: this.$route.query.station_code,
          bar_num: Number(this.monitorData.MNQ_Num.value)
        }
        MesGxScanMzSubmit(query).then(res => {
          if (res.code === 0) {
            this.$message({ type: 'success', message: '操作成功' })
            this.mzDataSelect()
            this.handleRequestCodeOrde('OP2000/MesStatus/MesS_DataReady', '1', 'OP2000')
          }
        }).catch(() => {
          this.$message({
            message: '模组上线校验异常',
            type: 'error'
          })
        })
        return
      }
      this.$message({
        type: 'warning',
        message: '请先扫描完所有模组码'
      })
    },
    handleMzQuantuity(e) {
      if (!e) {
        return
      }
      this.handleRequestCodeOrde('OP2002/BarStatus/MNQ_Num', e, 'OP2002')
    },
    handleblack(e) {
      this.handleRequestCodeOrde('OP2000/MesRecipe/MesS_ModuleBlockStatus', e, 'OP2000')
    },
    handleMzTagValue(data) {
      this.handleRequestCodeOrde(data.tag_key, data.mz_repair_status, data.tag_key.split('/')[0])
    },
    handleDelete(data) {
      const query = {
        station_code: this.$route.query.station_code,
        mz_barcode: data.mz_barcode,
        mz_index: data.sort,
        userName: Cookies.get('userName'),
        bar_num: Number(this.monitorData.MNQ_Num.value)
      }
      this.$confirm('确认要删除这条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        MesGxScanMzDelete(query).then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.mzDataSelect()
          } else {
            this.$message({
              message: '操作失败',
              type: 'error'
            })
          }
        }).catch(() => {
          this.$message({
            message: '操作失败',
            type: 'error'
          })
        })
      })
    },
    mzDataSelect() {
      this.mz_Data = []
      var query = {
        station_code: this.$route.query.station_code,
        repair_flag: true // 返修模式
      }
      MesGxScanMzSelect(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.mz_Data = defaultQuery.data
          }
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
          }
        }
      }
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')
      // 获取cell信息
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          // var connectUrl = 'ws://*************:8083' + '/mqtt'
          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // 获取Tag值
            this.GetTagValue()
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            var jsonData = JSON.parse(message)
            if (jsonData == null) return
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
              // 第一先判断PLC是否可以扫入，第二判断指令是否允许下发
              if (this.monitorData.PlcS_RequestSignal.value === '1' && this.monitorData.BarS_ScanBarCodeOrde.value === '1') {
                this.ManualDxScan()
                this.handleRequestCodeOrde('OP2001/BarStatus/BarS_ScanBarCodeOrde', '0', 'OP2001')
              }
            }
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleRequestCodeOrde(tagKey, value, clientCode) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = `SCADA_WRITE/${clientCode}`
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 从后台REDIS获取数据
    GetTagValue() {
      // this.mz_Data = []
      // 读取Tag集合(Key)
      var readTagArray = []
      if (this.scanBarFlag) {
        Object.keys(this.monitorData).forEach(key => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          var readTag = {}
          readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
          readTagArray.push(readTag)
        })
      }
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                if (this.scanBarFlag) {
                  Object.keys(this.monitorData).forEach(key => {
                    var client_code = this.monitorData[key].client_code
                    var group_code = this.monitorData[key].group_code
                    var tag_code = this.monitorData[key].tag_code
                    var tag_key = client_code + '/' + group_code + '/' + tag_code
                    const item = result.filter(item => item.tag_key === tag_key)
                    if (item.length > 0) {
                      this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                    }
                    if (!this.monitorData.MNQ_Num.value || this.monitorData.MNQ_Num.value === '0') {
                      this.monitorData.MNQ_Num.value = '4'
                    }
                  })
                }
                if (this.monitorData.PlcS_RequestSignal.value === '1' && this.monitorData.BarS_ScanBarCodeOrde.value === '1' && this.scanBarFlag) {
                  this.scanBarFlag = false
                  this.ManualDxScan()
                  this.handleRequestCodeOrde('OP2001/BarStatus/BarS_ScanBarCodeOrde', '0', 'OP2001')
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .elRowStyle {
    justify-content: space-around;
    .el-descriptions{
    p{
    display: flex;
    align-items: center;
    color: #333333;
    margin: 0;
    margin-bottom: 10px;
    span {
        display: flex;
        align-items: center;
    }
    .commonsatu {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
        display: block;
    }
    .statuOne {
        background-color: #13ce66;
    }
    .statuZero {
        background-color: #cccccc;
    }
    .statuTwo {
        background-color: #ff4949;
    }
    .statuSecond {
        background-color: #cccccc;
    }
    }
   }
}
.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  margin-bottom: 20px;
  span {
    white-space: nowrap;
    font-weight: 700;
    color: #79a0f1;
  }
  button {
    margin-left: 10px;
  }
  .scanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .scanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
  .wrapimin{
    position: relative;
    width: 100%;
    img{
        position: absolute;
        right: 7px;
        top: -3px;
        width: 45px;
        z-index: 2;
    }
    }
}
.mzScanBtn {
    width: 90px;
    height: 30px;
    margin-bottom: 5px !important;
    position: relative;
    color: #ffffff;
    background: linear-gradient(50deg, #bad1ff, #79a0f1);
    border-radius: 25px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0.5px -0.5px 1px 1px #fff, -1px 1px 1px 1px #bad1ff, -1px 3px 18px 0px #bad1ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    user-select: none;
    border: 0;
  }
  .mzScanBtn:active {
    font-size: 14px;
    box-shadow: 0.5px -0.5px 1px 0.5px #fff, -1px 1px 1px 0.5px #79a0f1, -1px 4px 10px 1px #79a0f1;
  }
</style>
