<template>
    <div>
        <el-table border @header-dragend="crud.tableHeaderDragend()" v-loading="loading" :data="tableData" :row-key="row => row.id"
            :highlight-current-row="highlightCurrentRow" @selection-change="handleSelectionChange">
            <el-table-column  v-if="isMultiple" type="selection" width="55"></el-table-column>
            <el-table-column  v-if="isRadio" width="55" label="单选">
                <template slot-scope="scope">
                    <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
                    <el-radio @change.native.stop.prevent="rowChange(scope.row)" v-model="templateSelection"
                        :label="scope.row.id">&nbsp;
                    </el-radio>
                </template>
            </el-table-column>
            <el-table-column  v-for="(column, index) in tableColumns" :key="index" :prop="column.prop" :label="column.label"
                :min-width="column.width">
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    name: 'CommonTable',
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        highlightCurrentRow: {
            type: Bo<PERSON>an,
            default: true,
        },
        columns: { //表头
            type: Array,
            required: true,
        },
        data: { //表格数据
            type: Array,
            default: () => [],
        },
        isMultiple: { //是否多选
            type: Boolean,
            default: false,
        },
        isRadio: {
            type: Boolean,//是否单选
            default: false,
        },
        onRowClick: {
            type: Function,
            default: () => { },
        },
        onSelectionChange: {
            type: Function,
            default: () => { },
        },
    },
    data() {
        return {
            tableData: [], // 表格数据
            tableColumns: [], // 表格列
            isMultipleSelect: false, // 是否多选
            selectedRows: [], // 已选中的行
            templateSelection: '',
        };
    },
    computed: {
        // 是否显示选择列
        showSelectionColumn() {
            return this.isMultipleSelect || this.onSelectionChange;
        },
    },
    created() {
        this.getTableList()
    },
    watch: {
        columns: {
            immediate: true,
            handler(columns) {
                this.tableColumns = columns;
                this.isMultipleSelect = this.showSelectionColumn;
            },
        },
        data: {
            immediate: true,
            handler(data) {
                this.tableData = data;
            },
        },
    },
    methods: {
        // 点击行事件
        handleRowClick(row) {
            this.onRowClick(row);
        },
        rowChange(row) {
            this.templateSelection = row.id
        },
        getTableList() {
            const url = '/cell/core/flow/CoreFlowSelectStep';
            const data = {
                station_id: this.stationId,
            }
            axios.post(url, data, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then((res) => {

            }).catch(err => {
                this.$message({ message: '查询异常：' + err, type: 'error' })
            })
        },
        // 选择行事件
        handleSelectionChange(rows) {
            this.selectedRows = rows;
            this.onSelectionChange(rows);
        },
    },
};
</script>