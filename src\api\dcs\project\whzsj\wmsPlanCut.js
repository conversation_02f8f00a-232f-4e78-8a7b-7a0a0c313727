import request from '@/utils/request'
// 查询切割计划
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanCutSelect',
    method: 'post',
    data
  })
}
// 删除切割计划
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanCutDelete',
    method: 'post',
    data
  })
}

// 计划状态更新
export function planStatusUpd(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanCutStatusUpd',
    method: 'post',
    data
  })
}

// 切割上料
export function planManual(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanCutManual',
    method: 'post',
    data
  })
}

// 库存同步
export function planInventorySend(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/whzsj/DcsWmsPlanCutInventorySend',
    method: 'post',
    data
  })
}
export default { sel, del, planStatusUpd, planManual, planInventorySend }
