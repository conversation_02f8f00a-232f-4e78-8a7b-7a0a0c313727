<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="24">
                <MainChart
                    ref="chart"
                    v-loading="loading"
                    :nodes="nodes"
                    :connections="connections"
                    :width="'1856'"
                    :height="'290'"
                    :readonly="false"
                    element-loading-text="拼命绘制流程图中"
                    @editnode="handleEditNode"
                />
            </el-col>
        </el-row>
    </div>
</template>
<script>
/* eslint-disable no-unused-vars */
import MainChart from '@/components/MainArea/index'
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
import axios from 'axios'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
export default {
  components: {
    MainChart
  },
  data() {
    return {
      diameter: 15,
      nodes: [],
      connections: [],
      thisH: null,
      loading: false,
      nodeForm:{},
      offsetArr:[
        [{x:10,y:0,type:'bigCar'},{x:10,y:20,type:'smallCar'}],
        [{x:11,y:0,type:'bigCar'},{x:11,y:21,type:'smallCar'}],
        [{x:12,y:0,type:'bigCar'},{x:12,y:22,type:'smallCar'}],
        [{x:13,y:0,type:'bigCar'},{x:13,y:23,type:'smallCar'}],
        [{x:14,y:0,type:'bigCar'},{x:14,y:24,type:'smallCar'}],
        [{x:15,y:0,type:'bigCar'},{x:15,y:25,type:'smallCar'}],
        [{x:16,y:0,type:'bigCar'},{x:16,y:26,type:'smallCar'}],
        [{x:17,y:0,type:'bigCar'},{x:17,y:27,type:'smallCar'}],
        [{x:18,y:0,type:'bigCar'},{x:18,y:28,type:'smallCar'}],
        [{x:19,y:0,type:'bigCar'},{x:19,y:29,type:'smallCar'}],
        [{x:20,y:0,type:'bigCar'},{x:20,y:30,type:'smallCar'}],
        [{x:21,y:0,type:'bigCar'},{x:21,y:70,type:'smallCar'}],
        [{x:30,y:0,type:'bigCar'},{x:30,y:50,type:'smallCar'}],
        [{x:40,y:0,type:'bigCar'},{x:40,y:25,type:'smallCar'}],
        [{x:50,y:0,type:'bigCar'},{x:50,y:40,type:'smallCar'}],
        [{x:60,y:0,type:'bigCar'},{x:60,y:10,type:'smallCar'}],
        [{x:300,y:0,type:'bigCar'},{x:300,y:0,type:'smallCar'}],
        [{x:310,y:0,type:'bigCar'},{x:310,y:90,type:'smallCar'}],
        [{x:320,y:0,type:'bigCar'},{x:320,y:40,type:'smallCar'}],
        [{x:330,y:0,type:'bigCar'},{x:330,y:60,type:'smallCar'}],
      ],
      count:0
    }
  },
  mounted() {
    this.timer = setInterval(()=>{
      this.count ++ 
      if(this.count === this.offsetArr.length){
        this.count = 0
      }
      this.offsetArr[this.count].map(item=>{
        this.nodes.map(e=>{
          if(e.type && item.type === e.type){
            e = Object.assign(e,item)
          }
        })
      })
    },500)
  },
  beforeDestroy(){
    if(this.timer){
      clearInterval(this.timer)
    }
  },
  created() {
      const arr = [
        {stock_code:'L01',x:0,y:61},{stock_code:'L02',x:0,y:89},{stock_code:'L03',x:0,y:117},{stock_code:'L04',x:0,y:145},
        {stock_code:'P33',x:310,y:5},{stock_code:'P05',x:310,y:33},{stock_code:'P04',x:310,y:61},{stock_code:'P03',x:310,y:89},{stock_code:'P02',x:310,y:117},{stock_code:'P01',x:310,y:145},
        {stock_code:'P31',x:620,y:5},{stock_code:'P32',x:620,y:33},{stock_code:'P34',x:620,y:61},{stock_code:'P12',x:620,y:89},{stock_code:'P11',x:620,y:117},{stock_code:'P10',x:620,y:145},{stock_code:'P09',x:620,y:173},{stock_code:'P08',x:620,y:201},{stock_code:'P07',x:620,y:229},{stock_code:'P06',x:620,y:257},
        {stock_code:'P30',x:930,y:5},{stock_code:'P18',x:930,y:117},{stock_code:'P17',x:930,y:145},{stock_code:'P16',x:930,y:173},{stock_code:'P15',x:930,y:201},{stock_code:'P14',x:930,y:229},{stock_code:'P13',x:930,y:257},
        {stock_code:'P24',x:1240,y:117},{stock_code:'P23',x:1240,y:145},{stock_code:'P22',x:1240,y:173},{stock_code:'P21',x:1240,y:201},{stock_code:'P20',x:1240,y:229},{stock_code:'P19',x:1240,y:257},
        {stock_code:'P29',x:1550,y:145},{stock_code:'P28',x:1550,y:173},{stock_code:'P27',x:1550,y:201},{stock_code:'P26',x:1550,y:229},{stock_code:'P25',x:1550,y:257},
        {x:0,y:0,type:'bigCar'},
        {x:0,y:0,type:'smallCar'},
      ]
      crudMainPage.allInventory({}).then(res=>{//所有库存
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.nodes = arr.map(item=>{
            const item2 = defaultQuery.data.find(item2=>item2.stock_code === item.stock_code);
            return Object.assign(item,item2)
          })
        }
      })
      .catch(() => {
          this.$message({
              message: '获取库位异常',
              type: 'error'
          })
        })
  },
  methods: {
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
  },
}
</script>

<style lange="less" scoped>
.app-container{
    width: 1856px;
    height: 290px;
    padding: 0;
}
</style>