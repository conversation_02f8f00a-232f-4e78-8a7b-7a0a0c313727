<template>
  <div class="app-container">
    <el-row :gutter="20" class="el-row">
      <el-col :span="24" style="margin-bottom: 10px">
        <el-card class="box-card1" shadow="always">
          <!--查询条件-->
          <div slot="header" class="clearfix">
            <!--表：sys_fmod_prod_line-->
            <el-select v-model="query.prod_line_id" size="mini" placeholder="请选择产线" multiple @change="changeProdLine">
              <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
            <!--表：scada_client-->
            <el-select v-model="query.client_id" size="mini" placeholder="请选择实例" multiple @change="changeClient">
              <el-option v-for="item in clientData" :key="item.client_code" :label="item.client_des" :value="item.client_id" />
            </el-select>

            <el-input v-model="query.clientCodeDes" clearable size="small" placeholder="实例编码或描述" style="width: 150px" class="filter-item" />
            <el-button class="filter-item" size="mini" type="primary" icon="el-icon-search" style="margin-left: 10px" @click="toButQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
            <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-refresh-left" @click="toButResetQuery">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->
            <el-button v-if="true" class="filter-item" size="mini" type="primary" icon="el-icon-delete" :disabled="tableDataGroupTable.length == 0" style="float: right" plain @click="toGroupButClear()">{{ $t('lang_pack.monitor.emptyMonitoring') }}</el-button>  <!-- 清空监控 -->
            <el-button v-if="true" class="filter-item" size="mini" type="primary" icon="el-icon-video-pause" :disabled="!mqttConnStatus" style="float: right" plain @click="toStopWatch">{{ $t('lang_pack.monitor.stopMonitoring') }}</el-button>  <!-- 停止监控 -->
            <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-video-play" type="primary" :disabled="mqttConnStatus" style="float: right" plain @click="toStartWatch">{{ $t('lang_pack.monitor.startupMonitoring') }}</el-button>  <!-- 启动监控 -->
            <el-button v-if="true" class="filter-item" size="mini" icon="el-icon-video-play" type="primary" :disabled="mqttConnStatus" style="float: right" plain @click="toStartKafkaWatch">{{ $t('lang_pack.monitor.startupKafkaMonitoring') }}</el-button>  <!-- 启动KAFKA监控 -->
          </div>

          <el-row :gutter="20">
            <!--实例-->
            <el-col :span="12">
              <el-table ref="table" v-loading="false" border :data="tableDataTable" style="width: 100%" :stripe="true" height="400px" max-height="400px" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
                <el-table-column :show-overflow-tooltip="true" prop="client_code" width="100" :label="$t('lang_pack.monitor.exampleCode')" />  <!-- 实例编码 -->
                <el-table-column :show-overflow-tooltip="true" prop="client_des" width="150" :label="$t('lang_pack.monitor.exampleDescription')" />  <!-- 实例描述 -->
                <el-table-column :show-overflow-tooltip="true" prop="client_driver" :label="$t('lang_pack.monitor.driveProgram')" />  <!-- 驱动程序 -->

                <el-table-column :label="$t('lang_pack.monitor.simulation')" align="center" prop="simulated_flag">  <!-- 是否模拟 -->
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === 'Y' ? $t('lang_pack.vie.Yes') : $t('lang_pack.vie.NO') }}
                  </template>
                </el-table-column>
                <!--自定义列-->
                <el-table-column :show-overflow-tooltip="true" prop="tdOkTime" width="100" :label="$t('lang_pack.monitor.timeDuration')" />  <!-- 持续时间(分) -->
                <el-table-column :label="$t('lang_pack.monitor.status')" align="center" prop="tdstatus" width="55" fixed="right">  <!-- 状态 -->
                  <template slot-scope="scope">
                    <span v-show="scope.row.tdstatus == '-1'" class="statuStyle noNormalStyle" />
                    <span v-show="scope.row.tdstatus == '0'" class="statuStyle normalStyle" />
                    <span v-show="scope.row.tdstatus == '1'" class="statuStyle abnormalStyle" />

                    <!-- -1 灰色 （无状态）
                    0 绿色 （正常）
                    1 红色 （异常）
                     -->
                    <!-- <el-image v-if="scope.row.tdstatus === -1" :src="normalsingle" lazy class="el-avatar" />
                    <el-image v-if="scope.row.tdstatus == 0" :src="redsingle" lazy class="el-avatar" />
                    <el-image v-if="scope.row.tdstatus == 1" :src="greensingle" lazy class="el-avatar" /> -->
                  </template>
                </el-table-column>

                <el-table-column :label="$t('lang_pack.monitor.heartbeat')" align="center" prop="tdbeat" width="55" fixed="right">  <!-- 心跳 -->
                  <template slot-scope="scope">
                    <span v-show="scope.row.tdbeat == '-1'" class="statuStyle noNormalStyle" />
                    <span v-show="scope.row.tdbeat == '0'" class="statuStyle noNormalStyle" />
                    <span v-show="scope.row.tdbeat == '1'" class="statuStyle normalStyle" />

                    <!-- <el-image v-if="scope.row.tdbeat == -1" :src="normalsingle" fit="contain" lazy class="el-avatar" />
                    <el-image v-if="scope.row.tdbeat == 0" :src="normalsingle" fit="contain" lazy class="el-avatar" />
                    <el-image v-if="scope.row.tdbeat == 1" :src="greensingle" fit="contain" lazy class="el-avatar" /> -->
                  </template>
                </el-table-column>

                <!-- Table单条操作-->
                <el-table-column :label="$t('lang_pack.monitor.addWatch')" width="80" align="center" fixed="right">  <!-- 添加监控 -->
                  <template slot-scope="scope">
                    <el-link class="linkItem" type="primary" @click="toControlClientTagList(scope.row)">{{ $t('lang_pack.monitor.addTag') }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <!--Tag监控-->
            <el-col :span="12">
              <el-table ref="tableGroup" v-loading="false" border :data="tableDataGroupTable" style="width: 100%" :stripe="true" height="400px" max-height="400px" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
                <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                <el-table-column v-if="1 == 0" width="10" prop="tag_group_id" label="tag_group_id" />
                <el-table-column v-if="1 == 0" width="10" prop="tag_id" label="tag_id" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_code" width="70" :label="$t('lang_pack.monitor.labelCode')" /> <!-- 标签代码 -->
                <el-table-column :show-overflow-tooltip="true" prop="tag_des" :label="$t('lang_pack.monitor.labelDescription')" />  <!-- 标签描述 -->
                <el-table-column :show-overflow-tooltip="true" prop="tdtagvalue" width="70" :label="$t('lang_pack.monitor.currentValue')" />  <!-- 当前值 -->
                <el-table-column :show-overflow-tooltip="true" prop="tag_group_code" width="100" :label="$t('lang_pack.monitor.labelGroups')" />  <!-- 标签组 -->
                <el-table-column v-if="1 == 0" width="10" prop="tagOnlyKey" label="tagOnlyKey" />
                <!-- Table单条操作-->
                <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-link class="linkItem" type="primary" @click="toTagRefresh(scope.row)">{{ $t('lang_pack.monitor.refresh') }}</el-link>
                    <el-link class="linkItem" type="primary" @click="toTagWrite(scope.row)">{{ $t('lang_pack.monitor.write') }}</el-link>
                    <el-link class="linkItem" type="primary" @click="toTagAttr(scope.row)">{{ $t('lang_pack.monitor.attr') }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <!--驱动消息-->
              <el-card class="box-card1" shadow="never" style="margin-top: 10px">
                <div slot="header" class="clearfix">
                  <span>驱动消息</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="tableDataItemTable = []">清空消息</el-button>
                </div>
                <el-table ref="tableItemTable" v-loading="false" border :data="tableDataItemTable" style="width: 100%" max-height="250px" :highlight-current-row="true" @header-dragend="crud.tableHeaderDragend()">
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_TIME" width="150" :label="$t('lang_pack.monitor.time')" />  <!-- 时间 -->
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_STATUS" width="100" :label="$t('lang_pack.monitor.status')" />  <!-- 状态 -->
                  <el-table-column :show-overflow-tooltip="true" prop="MSG_FUNC" width="120" :label="$t('lang_pack.monitor.feature')" />  <!-- 功能 -->
                  <el-table-column prop="MSG_INFO" :label="$t('lang_pack.monitor.message')" />  <!-- 消息 -->
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>

      <!--弹框：添加监控标签-->
      <el-drawer append-to-body title="添加监控标签" :visible.sync="dialogVisbleSyncFrom" :before-close="toBeforeCloseFrom" size="40%">
        <el-form ref="form" :model="form" size="small">
          <el-form-item>
            <el-tree ref="tree" :data="treeData" :props="treeProps" show-checkbox />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="toFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toFormFromSubmit('form')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确认 -->
        </div>
      </el-drawer>

      <!--弹框：标签写入-->
      <el-dialog append-to-body :close-on-click-modal="false" :title="wirteTagTitle" :visible.sync="writeDialogVisbleSyncFrom" :before-close="toWriteBeforeCloseFrom" width="520px">
        <el-form ref="formWrite" :model="formWrite" size="small" label-width="60px">
          <el-form-item v-if="1 == 0" label="id" prop="write_client_code" display:none> <el-input v-model="formWrite.write_client_code" />id </el-form-item>
          <el-form-item v-if="1 == 0" label="id" prop="write_tagOnlyKey" display:none> <el-input v-model="formWrite.write_tagOnlyKey" />id </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.labelValue')" prop="write_tag_value" display:none>  <!-- 标签值 -->
            <el-input ref="writeTagValue" v-model="formWrite.write_tag_value" />
          </el-form-item>
        </el-form>
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="toWriteFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="toWrigeFormFromSubmit('formWrite')">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确认 -->
        </div>
      </el-dialog>

      <!--弹框：标签属性-->
      <el-drawer append-to-body title="标签属性" :visible.sync="attrDialogVisbleSyncFrom" :before-close="toAttrBeforeCloseFrom" size="50%">
        <el-form ref="formAttr" :model="formAttr" :inline="true" size="small" label-width="100px">
          <el-form-item :label="$t('lang_pack.monitor.exampleCode')" prop="client_code" display:none>  <!-- 实例编码 -->
            <el-input v-model="formAttr.client_code" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.exampleDescription')" prop="client_des">  <!-- 实例描述 -->
            <el-input v-model="formAttr.client_des" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.groupCode')" prop="tag_group_code">  <!-- 组编码 -->
            <el-input v-model="formAttr.tag_group_code" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.groupDescription')" prop="tag_group_des">  <!-- 组描述 -->
            <el-input v-model="formAttr.tag_group_des" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.labelCode')" prop="tag_code">  <!-- 标签编码 -->
            <el-input v-model="formAttr.tag_code" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.labelDescription')" prop="tag_des">  <!-- 标签描述 -->
            <el-input v-model="formAttr.tag_des" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.regionName')" prop="block_name">  <!-- 区域名称 -->
            <el-input v-model="formAttr.block_name" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.regionPosition')" prop="block_addr">  <!-- 区域位置 -->
            <el-input v-model="formAttr.block_addr" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.dataType')" prop="data_type">  <!-- 数据类型 -->
            <el-input v-model="formAttr.data_type" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.initialAddress')" prop="start_addr">  <!-- 起始地址 -->
            <el-input v-model="formAttr.start_addr" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.length')" prop="data_length">  <!-- 长度 -->
            <el-input v-model="formAttr.data_length" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.bit')" prop="data_bit">  <!-- 位 -->
            <el-input v-model="formAttr.data_bit" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.opcRealAddress')" prop="opc_addr">  <!-- OPC 真实地址 -->
            <el-input v-model="formAttr.opc_addr" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.opcVirtualAddress')" prop="opc_demo_addr">  <!-- OPC 模拟地址 -->
            <el-input v-model="formAttr.opc_demo_addr" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.readWriteAccessPermission')" prop="data_access">  <!-- 读写权限 -->
            <el-input v-model="formAttr.data_access" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.push')" prop="pub_flag">  <!-- 是否推送 -->
            <el-input v-model="formAttr.pub_flag" :disabled="true" style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.monitor.dataConversion')" prop="data_format">  <!-- 数据转换 -->
            <el-input v-model="formAttr.data_format" :disabled="true" style="width: 200px" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center; margin-bottom: 10px">
          <el-button size="small" icon="el-icon-close" plain @click="toAttrFromCancel">{{ $t('lang_pack.commonPage.close') }}</el-button>  <!-- 关闭 -->
        </div>
      </el-drawer>
    </el-row>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import { selScadaClient } from '@/api/core/scada/client'
import { scadaTagGroupTree, scadaTagInfo } from '@/api/core/scada/tag'
import { selCoreServerCell } from '@/api/core/center/server'
import { scadaReadTagValue } from '@/api/core/scada/monitor'

// import Normalsingle from '@/assets/status/normalsingle.png'
// import Greensingle from '@/assets/status/greensingle.png'
// import Redsingle from '@/assets/status/redsingle.png'

import mqtt from 'mqtt'
import { MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'

export default {
  name: 'SCADA_KAFKA_MONITOR',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,

      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId: 'ScadaWeb_' + Cookies.get('userName') + '_' + Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },

      // 查询条件
      query: {
        prod_line_id: '',
        clientCodeDes: ''
      },
      // 产线集合
      currentProdLineIds: '',
      prodLineData: [],
      // 实例集合
      currentClientIds: '',
      clientData: [],

      // 实例表
      tableDataTable: [],
      // 图片
      // normalsingle: Normalsingle,
      // greensingle: Greensingle,
      // redsingle: Redsingle,

      // 组、Tab表
      tableDataGroupTable: [],

      // 驱动消息
      tableDataItemTable: [],

      // 弹框：添加监控标签
      dialogVisbleSyncFrom: false,
      treeData: [], // Tag 树
      treeProps: {
        children: 'children',
        label: 'label'
      },
      treeCheckedList: [], // 树选择值

      // 弹框：标签写入
      writeDialogVisbleSyncFrom: false,
      form: {},
      formWrite: {
        write_client_code: '',
        write_tagOnlyKey: '',
        write_tag_value: ''
      },
      // 弹框：标签属性
      attrDialogVisbleSyncFrom: false,
      formAttr: {
        client_code: '',
        client_des: '',
        tag_group_code: '',
        tag_group_des: '',
        tag_code: '',
        tag_des: '',
        block_name: '',
        block_addr: '',
        data_type: '',
        start_addr: '',
        data_length: '',
        data_bit: '',
        opc_addr: '',
        opc_demo_addr: '',
        data_access: '',
        pub_flag: '',
        data_format: ''
      },

      // Tag监控集合
      mesTagWatchList: [],
      mesClientTopicList: [],
      currentClientCode: '',
      wirteTagTitle: '标签值'
    }
  },

  created() {
    // MQTT连接
    // this.initMqtt('myTopic')

    // 加载 产线LOV
    const query = {
      userID: Cookies.get('userName')
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },

  methods: {
    // ----------------------------------【KAFKA】----------------------------------
    // 启动KAFKA监控
    toStartKafkaWatch() {
      console.log('KAFKA连接启动中。。。')
    },

    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      console.log('MQTT连接启动中。。。')

      if (this.tableDataTable.length === 0) {
        this.$message({
          message: '请先查询实例，再启动监控',
          type: 'error'
        })
      }

      if (this.tableDataTable.length > 0) {
        // 获取连接地址
        // 'ws://192.168.167.136:8090/mqtt'
        var connectUrl = ''

        var query = {
          userName: Cookies.get('userName'),
          esb_server_flag: 'Y'
        }
        selCoreServerCell(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.count === 0) {
              this.$message({
                message: '请先维护Server、Cell信息',
                type: 'error'
              })
              return
            }

            connectUrl = 'ws://' + defaultQuery.data[0].server_host_1 + ':' + defaultQuery.data[0].cell_webapi_port + '/mqtt'

            // connectUrl=MQTT_SERVICE;
            console.log('拼接URL：' + connectUrl)

            // mqtt连接
            // this.clientMqtt = mqtt.connect(MQTT_SERVICE, this.optionsMqtt); //开启连接
            this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
            this.clientMqtt.on('connect', e => {
              this.mqttConnStatus = true

              // 当MQTT连接成功后，注册CLIENT相关TOPIC
              for (var i = 0; i < this.tableDataTable.length; i++) {
                var clientCode = this.tableDataTable[i].client_code

                var topic_clientStatus = 'SCADA_STATUS/' + clientCode
                var topic_clientBeat = 'SCADA_BEAT/' + clientCode
                var topic_clientMsg = 'SCADA_MSG/' + clientCode
                this.topicSubscribe(topic_clientStatus)
                this.topicSubscribe(topic_clientBeat)
                this.topicSubscribe(topic_clientMsg)
              }

              this.$message({
                message: '连接成功',
                type: 'success'
              })
            })

            // MQTT连接失败
            this.clientMqtt.on('error', () => {
              this.$message({
                message: '连接失败',
                type: 'error'
              })

              this.clientMqtt.end()
            })
            // 断开发起重连(异常)
            this.clientMqtt.on('reconnect', () => {
              this.$message({
                message: '连接断开，正在重连。。。',
                type: 'error'
              })
            })
            this.clientMqtt.on('disconnect', () => {
              this.$message({
                message: '服务连接断开',
                type: 'error'
              })
            })
            this.clientMqtt.on('close', () => {
              this.clientMqtt.end()

              this.$message({
                message: '服务连接断开',
                type: 'error'
              })
            })
            // 接收消息处理
            this.clientMqtt.on('message', (topic, message) => {
              // console.log('MQTT收到来自', topic, '的消息', message.toString())

              // const res = JSON.parse(message.toString())
              // 解析传过来的数据
              this.mqttUpdateTable(topic, message)
            })
          })
          .catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
      }
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      this.clientMqtt.unsubscribe(topic, error => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 停止监控(断开连接)
    toStopWatch() {
      console.warn('MQTT停止连接')

      // this.clientMqtt.disconnect()
      this.clientMqtt.end()
      // this.clientMqtt = null
      this.mqttConnStatus = false
    },
    // 离开此页面时销毁mqtt链接
    beforeDestroy() {
      console.log('MQTT销毁链接')

      if (this.mqttClient.end) this.mqttClient.end()
    },

    // ----------------------------------【实例】----------------------------------
    // 更改产线
    changeProdLine(val) {
      console.log('changeProdLine:' + val)

      this.currentProdLineIds = val

      // 加载 实例LOV
      this.clientData = []
      const query = {
        userID: Cookies.get('userName'),
        sort: 'client_id',
        prodLineIds: this.currentProdLineIds
      }
      selScadaClient(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.clientData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 更改实例
    changeClient(val) {
      console.log('changeClient:' + val)

      this.currentClientIds = val
    },
    // 按钮
    toButQuery() {
      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        prodLineIds: this.currentProdLineIds,
        sort: 'client_id',
        clientIds: this.currentClientIds,
        clientCodeDes: this.query.clientCodeDes
      }
      selScadaClient(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          // 清空
          this.tableDataTable = []
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              defaultQuery.data.forEach(item => {
                var tableColumn = {} // 新增Tag监控
                tableColumn.client_id = item.client_id
                tableColumn.client_code = item.client_code
                tableColumn.client_des = item.client_des
                tableColumn.client_driver = item.client_driver
                tableColumn.simulated_flag = item.simulated_flag
                tableColumn.tdOkTime = ''
                tableColumn.tdstatus = '-1'
                tableColumn.tdbeat = '-1'
                this.tableDataTable.push(tableColumn)
              })
            } else {
              this.tableDataTable = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toButResetQuery() {
      // 重置
      this.query.clientCodeDes = ''
      this.query.enable_flag = ''

      // 测试发送消息
      const sendObjec = {
        title: '测试',
        detail: 'Hello mqtt'
      }
      this.sendMessage('myTopic', sendObjec)
    },

    // Table: Tag列表
    toControlClientTagList(data) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      this.currentClientCode = data.client_code
      this.dialogVisbleSyncFrom = true

      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        client_id: data.client_id
      }
      scadaTagGroupTree(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          this.dialogVisbleSyncFrom = true
          if (defaultQuery.data.length > 0) {
            this.treeData = defaultQuery.data
          }
        })
        .catch(() => {})
    },

    // Tree:
    toBeforeCloseFrom(done) {
      // 新增弹出框(关闭前的回调)
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toFromCancel() {
      // 取消
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toFormFromSubmit() {
      // 确定(修改)
      // 新增Tag监控
      this.toGroupButAdd()
    },

    // ----------------------------------【TAG监控】----------------------------------
    // 组Group
    toGroupButAdd() {
      // 查询Tag监控
      if (this.$refs.tree.getCheckedNodes().length <= 0) {
        this.$message({ message: '请选择一个TAG', type: 'warning' })
        return
      }
      // 循环选中Tag
      var newClientTagGroupList = []
      var newTagList = []
      this.$refs.tree.getCheckedNodes().forEach(item => {
        if (item.level === 2) {
          var tableColumn = {} // 新增Tag监控
          tableColumn.client_code = this.currentClientCode
          tableColumn.tag_group_id = item.tag_group_id
          tableColumn.tag_id = item.tag_id
          tableColumn.tag_group_code = item.tag_group_code
          tableColumn.tag_code = item.tag_code
          tableColumn.tag_des = item.tag_des
          tableColumn.data_type = item.data_type
          tableColumn.data_access = item.data_access
          tableColumn.simulated_flag = item.simulated_flag
          tableColumn.tdtagvalue = '-1'

          // 监控Tag
          var tagOnlyKey = tableColumn.client_code + '/' + tableColumn.tag_group_code + '/' + tableColumn.tag_code

          tableColumn.tagOnlyKey = tagOnlyKey
          if (this.tableDataGroupTable.filter(item => item.tag_id === tableColumn.tag_id).length === 0) {
            this.tableDataGroupTable.push(tableColumn)
          }
          // Tag组
          var clientGroupMultyKey = 'SCADA_CHANGE/' + tableColumn.client_code + '/' + tableColumn.tag_group_code
          if (this.mesClientTopicList.indexOf(clientGroupMultyKey) < 0) {
            this.mesClientTopicList.push(clientGroupMultyKey)
            newClientTagGroupList.push(clientGroupMultyKey)
          }
          if (this.mesTagWatchList.indexOf(tagOnlyKey) < 0) {
            newTagList.push(tagOnlyKey)
            this.mesTagWatchList.push(tagOnlyKey)
          }
        }
      })

      // 获取Tag值
      if (newTagList.length > 0) {
        this.GetTagValue(newTagList)
        // MesSleep(100);
      }
      // 订阅Tag组
      if (newClientTagGroupList.length > 0) {
        for (var i = 0; i < newClientTagGroupList.length; i++) {
          this.topicSubscribe(newClientTagGroupList[i].toString())
        }
      }
      this.dialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toGroupButClear() {
      // 清空Tag监控
      for (var i = 0; i < this.mesClientTopicList.length; i++) {
        this.topicUnsubscribe(this.mesClientTopicList[i])
      }
      this.mesClientTopicList = []
      this.mesTagWatchList = []
      this.tableDataGroupTable = []
      this.tableDataItemTable = [] // 日志
    },

    // ----------------------------------【Tag明细】----------------------------------
    toTagRefresh(data) {
      // 刷新
      try {
        var lst = []
        lst.push(data.tagOnlyKey)
        this.GetTagValue(lst)
      } catch {
        this.$message({
          message: '刷新异常',
          type: 'error'
        })
      }
    },
    toTagWrite(data) {
      // 写入
      this.formWrite.write_client_code = data.client_code
      this.formWrite.write_tagOnlyKey = data.tagOnlyKey
      this.formWrite.write_tag_value = ''
      this.wirteTagTitle = data.tag_des

      this.$nextTick(x => {
        // 正确写法
        this.$refs.writeTagValue.focus()
      })
      this.writeDialogVisbleSyncFrom = true // 弹出框
    },
    toWriteBeforeCloseFrom() {
      this.writeDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toWriteFromCancel() {
      this.writeDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toWrigeFormFromSubmit() {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.formWrite.write_tagOnlyKey,
        TagValue: this.formWrite.write_tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.formWrite.write_client_code
      this.sendMessage(topic, sendStr)

      this.writeDialogVisbleSyncFrom = false
    },

    toTagAttr(data) {
      // 属性
      this.attrDialogVisbleSyncFrom = true

      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        tag_id: data.tag_id
      }
      this.listLoadingTable = true
      scadaTagInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.formAttr.client_code = defaultQuery.data[0].client_code
              this.formAttr.client_des = defaultQuery.data[0].client_des
              this.formAttr.tag_group_code = defaultQuery.data[0].tag_group_code
              this.formAttr.tag_group_des = defaultQuery.data[0].tag_group_des
              this.formAttr.tag_code = defaultQuery.data[0].tag_code
              this.formAttr.tag_des = defaultQuery.data[0].tag_des
              this.formAttr.block_name = defaultQuery.data[0].block_name
              this.formAttr.block_addr = defaultQuery.data[0].block_addr
              this.formAttr.data_type = defaultQuery.data[0].data_type
              this.formAttr.start_addr = defaultQuery.data[0].start_addr
              this.formAttr.data_length = defaultQuery.data[0].data_length
              this.formAttr.data_bit = defaultQuery.data[0].data_bit
              this.formAttr.opc_addr = defaultQuery.data[0].opc_addr
              this.formAttr.opc_demo_addr = defaultQuery.data[0].opc_demo_addr
              this.formAttr.data_access = defaultQuery.data[0].data_access
              this.formAttr.pub_flag = defaultQuery.data[0].pub_flag
              this.formAttr.data_format = defaultQuery.data[0].data_format
            } else {
              this.formAttr.client_code = ''
              this.formAttr.client_des = ''
              this.formAttr.tag_group_code = ''
              this.formAttr.tag_group_des = ''
              this.formAttr.tag_code = ''
              this.formAttr.tag_des = ''
              this.formAttr.block_name = ''
              this.formAttr.block_addr = ''
              this.formAttr.data_type = ''
              this.formAttr.start_addr = ''
              this.formAttr.data_length = ''
              this.formAttr.data_bit = ''
              this.formAttr.opc_addr = ''
              this.formAttr.opc_demo_addr = ''
              this.formAttr.data_access = ''
              this.formAttr.pub_flag = ''
              this.formAttr.data_format = ''
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    toAttrBeforeCloseFrom() {
      this.attrDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    toAttrFromCancel() {
      this.attrDialogVisbleSyncFrom = false // 弹出框隐藏
    },

    // ----------------------------------【MQTT消息触发】----------------------------------
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_STATUS/') >= 0 || channel.indexOf('SCADA_BEAT/') >= 0 || channel.indexOf('SCADA_MSG/') >= 0) {
        this.reflashClientInfo(channel, message)
      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    // 接受到CLIENT对应的消息时，进行界面更新
    reflashClientInfo(channel, message) {
      var jsonData = JSON.parse(message)
      var clientCode = jsonData.ClientCode

      if (channel.indexOf('SCADA_STATUS/') >= 0) {
        var Status = jsonData.Status
        var OkTime = jsonData.OkTime

        this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdstatus = Status
        this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdOkTime = OkTime
      } else if (channel.indexOf('SCADA_BEAT/') >= 0) {
        var Beat = jsonData.Beat
        this.tableDataTable.filter(item => item.client_code === clientCode)[0].tdbeat = Beat
      } else if (channel.indexOf('SCADA_MSG/') >= 0) {
        // 明细
        var FuncCode = jsonData.FuncCode
        var Status = jsonData.Status
        var MsgInfo = jsonData.Message
        var Time = jsonData.Time

        var newRow = {
          MSG_TIME: Time,
          MSG_STATUS: Status,
          MSG_FUNC: FuncCode,
          MSG_INFO: MsgInfo
        }
        if (this.tableDataItemTable.length >= 100) {
          this.tableDataItemTable.splice(this.tableDataItemTable.length - 1, 1)
        }
        this.tableDataItemTable.unshift(newRow)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      // var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue
      if (this.mesTagWatchList.indexOf(TagKey) < 0) return
      this.tableDataGroupTable.filter(item => item.tag_code === TagCode)[0].tdtagvalue = TagNewValue
    },
    // 从后台REDIS获取数据
    GetTagValue(newTagList) {
      var readTagStrs = ''
      for (var i = 0; i < newTagList.length; i++) {
        if (i === 0) {
          readTagStrs = newTagList[i].toString()
        } else {
          readTagStrs = readTagStrs + ',' + newTagList[i].toString()
        }
      }

      // 格式化查询条件
      const query = {
        userName: Cookies.get('userName'),
        readTagStrs: readTagStrs
      }
      scadaReadTagValue(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))

          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // var children = JSON.stringify(result, null, '\t');

              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()

                  if (this.mesTagWatchList.indexOf(tagKey) >= 0) {
                    this.tableDataGroupTable.filter(item => item.tagOnlyKey == tagKey)[0].tdtagvalue = tagValue
                  }
                }
              }
            }
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-card__header {
  padding: 10px;
}
.box-card {
  min-height: calc(100vh);
  padding: 10px;
  .el-card__body {
    padding: 5px;
  }
}
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-drawer {
  overflow-y: scroll;
}
.el-avatar {
  background-color: #ffffff;
}
.statuStyle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
}
.noNormalStyle {
  background-color: #cccccc;
}
.abnormalStyle {
  background-color: red;
}
.normalStyle {
  background-color: green;
}
</style>
