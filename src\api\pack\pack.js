import request from '@/utils/request'

// 查询分选条件设定
export function sortSel(data) {
  return request({
    url: 'aisEsbWeb/pack/core/SortSel',
    method: 'post',
    data
  })
}
// 分选条件设定修改
export function SortUpd(data) {
  return request({
    url: 'aisEsbWeb/pack/core/SortUpd',
    method: 'post',
    data
  })
}
// 新增配方
export function add(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodRecipeIns',
    method: 'post',
    data
  })
}
// 查询配方
export function sel(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodRecipeSel',
    method: 'post',
    data
  })
}
// 修改配方
export function edit(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodRecipeUpd',
    method: 'post',
    data
  })
}
// 删除配方
export function del(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackFmodRecipeDel',
    method: 'post',
    data
  })
}

export default { sortSel, SortUpd, sel, add, edit, del }

