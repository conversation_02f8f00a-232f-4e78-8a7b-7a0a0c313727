<template>
  <SopChart
    ref="chart"
    v-loading="loading"
    :nodes="nodes"
    :connections="connections"
    :width="600"
    :height="360"
    :readonly="true"
    :gx-code="gxCode"
    element-loading-text="拼命绘制流程图中"
    @editnode="handleEditNode"
    @dblclick="handleDblClick"
    @editconnection="handleEditConnection"
    @save="handleChartSave"
    @delnode="handleDelNode"
  />
</template>
<script>
/* eslint-disable no-unused-vars */

import stationMonitor from '@/api/mes/core/hmi/stationMonitor'
import crudrecipeCrPdure from '@/api/mes/core/recipeCrPdure'
import Cookies from 'js-cookie'
import SopChart from '@/components/SopChart/index'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
export default {
  components: {
    SopChart
  },
  props: {
    proceduce_id: {
      type: [String, Number],
      default: 0
    },
    quality_data: {
      type: Array,
      default: null
    },
    gxCode: {
      type: String,
      default: ''
    }

  },
  data() {
    return {
      timer1: '',
      nodes: [],
      connections: [],
      nodeForm: { target: null },
      connectionForm: { target: null, operation: null },
      loading: false,
      dialogLabel: false,
      flow_label: '',
      flowLabelPlaceholder: '',
      rcsFlowSubVisible: false,
      rcsFlowSubStepVisible: false,
      scrollLeft: 0,
      scrollTop: 0,
      currentPdure: {}
    }
  },
  watch: {
    proceduce_id: {
      immediate: true,
      deep: true,
      handler() {
        this.loadSopChart()
      }
    }
  },
  mounted: function() {
    this.timer1 = setInterval(this.loadSopInfo, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer1)
  },
  created() {
    this.loadSopChart()
  },
  methods: {
    handleDblClick(position) {},
    handleDelNode(type, id) {

    },
    async handleChartSave(nodes, connections) {

    },
    handleRefreshChart(node, thisH) {
      this.$refs.chart.init(node, thisH)
      this.$refs.chart.save()
    },
    handleEditNode(node, thisH) {

    },
    handleEditConnection(connection) {
      // this.connectionForm.target = connection;
      // this.connectionDialogVisible = true;
    },
    loadSopChart() {
      this.loading = true
      crudrecipeCrPdure.sel({
        user_name: Cookies.get('userName'),
        proceduce_id: this.proceduce_id
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.currentPdure = defaultQuery.data[0]
              if (defaultQuery.data[0].pdure_chart !== '') {
                const flow_chart = JSON.parse(defaultQuery.data[0].pdure_chart)
                this.nodes = flow_chart.nodes
                const image = this.nodes.filter(item => item.type === 'image')[0]
                image.id = +new Date()

                console.log(this.nodes)
                this.connections = flow_chart.connections
              } else {
                this.nodes = [{
                  id: +new Date(),
                  x: 0,
                  y: 0,
                  type: 'image',
                  width: 600,
                  height: 400,
                  describe: '作业指导图片',
                  strokeWidth: 1,
                  href: this.proceduce_id
                }]
                this.connections = []
              }
            } else {
              this.nodes = [{
                id: +new Date(),
                x: 0,
                y: 0,
                type: 'image',
                width: 600,
                height: 400,
                describe: '作业指导图片',
                strokeWidth: 1,
                href: this.proceduce_id
              }]
              this.connections = []
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
          this.nodes = [{
            id: +new Date(),
            x: 0,
            y: 0,
            type: 'image',
            width: 600,
            height: 400,
            describe: '作业指导图片',
            strokeWidth: 1,
            href: this.proceduce_id
          }]
          this.connections = []
          this.loading = false
        })
    },
    loadSopInfo() {
      if (this.currentPdure.proceduce_type === 'SHAFT_QUALITY') {
        this.quality_data.forEach(a => {
          const circleInfo = this.nodes.filter(b => b.index + '' === a.tag_col_order.toString())
          if (circleInfo.length > 0) {
            circleInfo[0].status = a.quality_status
          }
        })
      }
    }
  }
}
</script>
