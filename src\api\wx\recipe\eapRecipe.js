import request from '@/utils/request'

// 查询配方维护信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapRecipeSel',
    method: 'post',
    data
  })
}
// 新增配方维护信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapRecipeIns',
    method: 'post',
    data
  })
}
// 修改配方维护信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapRecipeUpd',
    method: 'post',
    data
  })
}
// 删除配方维护信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapRecipeDel',
    method: 'post',
    data
  })
}
// 设备自检
export function equipStatusReport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/equipStatusReport',
    method: 'post',
    data
  })
}
// 设备自检浮窗数据
export function selectEquipStatusInfo(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/selectEquipStatusInfo',
    method: 'post',
    data
  })
}

// 导出配方维护信息
export function EapRecipeExport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapRecipeExport',
    method: 'post',
    responseType: 'blob',
    data
  })
}
// 导入配方维护信息
export function EapRecipeImport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapRecipeImport',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获取异常日志
export function nodeExceptionLogs(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/exceptionLog/nodeExceptionLogs',
    method: 'post',
    data
  })
}

// 获取接口日志
export function getLatestInterfLog(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/exceptionLog/getLatestInterfLog',
    method: 'post',
    data
  })
}

// 批次校验
export function eapUploadBatchCheck(data) {
  return request({
    url: 'aisEsbWeb/eap/project/mflex/recipe/EapUploadBatchCheck',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, equipStatusReport, selectEquipStatusInfo,
  EapRecipeExport, EapRecipeImport, getLatestInterfLog, nodeExceptionLogs, eapUploadBatchCheck }

