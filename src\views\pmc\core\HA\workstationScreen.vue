<template>
  <div class="workstationScreen">
    <el-card class="cardStyle">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="logoStyle"><img src="@/assets/images/qyc1.png" alt=""><span>{{ this.$route.query.station_code }}</span></div>
            <div class="wrapmarquee">
              <marquee loop="infinite">
                <div class="wraptext">
                  <p v-for="(item,index) in emergency" :key="index"><span :class="item.andon_type=='6'?'redactive':''">{{ item.andon_des }}</span></p>
                </div>
              </marquee>
            </div>
            <div class="headerL">
              <p class="pImg"><img src="@/assets/images/log.png" alt=""></p>
              <p class="pTime">{{ nowDateTime }} {{ nowDateWeek }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <div class="wrapitem">
        <div class="itemone">
          <el-row :gutter="24" class="elRowStyle elRowStyleFlex">
            <el-col :span="24">
              <el-card shadow="never">
                <div class="carInfoTable">
                  <el-table
                    :data="haStationQueue"
                    style="width: 100%"
                    height="320"
                    :row-class-name="tableRowClassName"
                  >
                    <el-table-column 
                      prop="rownum"
                      label="序号"
                      width="60"
                    />
                    <el-table-column 
                      prop="white_car_adjust"
                      label="白车身调整总成"
                      width="160"
                    />
                    <el-table-column 
                      prop="make_order"
                      label="订单号"
                      width="120"
                    />
                    <el-table-column  label="描述">
                      <template slot-scope="scope">
                        <div v-if="$route.query.prod_line_type === 'UB'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbf_s }}</span> /
                          <span>{{ scope.row.y26 }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'MB'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbr_s }}</span> /
                          <span>{{ scope.row.bbf_s }}</span> /
                          <span>{{ scope.row.bok_s }}</span> /
                          <span>{{ scope.row.y26 }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'MF'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbf_s }}</span> /
                          <span>{{ scope.row.y26 }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'FE'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbr_s }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'RE'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'BS'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbr_s }}</span> /
                          <span>{{ scope.row.bok_s }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'RF'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbr_s }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'FD'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bed_s }}</span>
                        </div>
                        <div v-if="$route.query.prod_line_type === 'FL'">
                          <span>{{ scope.row.vpp_s }}</span> /
                          <span>{{ scope.row.bbl_s }}</span> /
                          <span>{{ scope.row.bbr_s }}</span> /
                          <span>{{ scope.row.bbf_s }}</span> /
                          <span>{{ scope.row.bok_s }}</span> /
                          <span>{{ scope.row.y26 }}</span> /
                          <span>{{ scope.row.bed_s }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column 
                      prop="status"
                      label="状态"
                      width="80"
                    />
                  </el-table>
                </div>
              </el-card>
            </el-col>
            <el-col :span="24">
              <el-card shadow="never">
                <div class="carInfoTable">
                  <el-table
                    :data="carInfoDataresult"
                    style="width: 100%"
                    class="partstyle"
                    height="305"
                  >
                    <el-table-column
                      prop="ROWNUM"
                      label="序号"
                      width="60"
                    />
                    <el-table-column
                      prop="MATERIAL_CODE"
                      label="图号"
                      width="200"
                    />
                    <el-table-column
                      prop="MATERIAL_DESCRIPTION"
                      label="零件名称"
                    />
                    <el-table-column
                      prop="DEMAND_QUANTITY"
                      label="用量"
                      width="80"
                    />
                  </el-table>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <div class="itemtwo">
          <el-row :gutter="24" class="elRowStyle elRowStyleFlex">
            <el-col :span="24">
              <el-card shadow="never" style="margin-left:5px;">
                <div class="carInfoQuestion">
                  <div class="carInfoText">
                    <el-descriptions title="实时缺陷" :column="1" :colon="false">
                      <el-descriptions-item v-if="flawresult.length <= 0"><span class="nodata">暂无实时缺陷</span></el-descriptions-item>
                      <el-descriptions-item v-for="(item,index) in flawresult" v-else :key="index"><span class="topred">{{ item.FAULT_DESC }}</span></el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="24">
              <el-card shadow="never" style="margin-left:5px;">
                <!-- :style="'background-image:url(' + Background + ');'" -->
                <div class="carInfoVideo">
                  <div v-if="playerOptions.sources[0].src===''" />
                  <video-player
                    v-else
                    v-show="playerOptions.sources[0].src !== ''"
                    ref="videoPlayer"
                    class="video-player vjs-custom-skin"
                    :playsinline="true"
                    :options="playerOptions"
                    @ended="onPlayerEnded($event)"
                  />
                  <img v-show="imgListv[0].IMAGE_URL !== '' && playerOptions.sources[0].src === ''" :src="imgListv[imgindex].IMAGE_URL">
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getStationTime, getStationOrder, getStationOrderQueue, getStationTop, getStationBZG, getHaStationScreenQueue } from '@/api/pmc/sysworkstationScreen'
import posterimg from '@/assets/images/posterimg.jpg'
export default {
  name: 'workstationScreen',
  components: {
  },
  data() {
    return {
      // showtarget: true,
      playerOptions: {
        playbackRates: [0.5, 1.0, 1.5, 2.0], // 可选的播放速度
        autoplay: true, // 如果为true,浏览器准备好时开始回放。
        muted: true, // 默认情况下将会消除任何音频。
        loop: false, // 是否视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        // aspectRatio: '569:320', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [{
          type: 'video/mp4', // 类型
          src: '' // url地址
        }],
        poster: posterimg, // 封面地址
        notSupportedMessage: '此视频正在加载中，请稍等', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: true, // 当前时间和持续时间的分隔符
          durationDisplay: true, // 显示持续时间
          remainingTimeDisplay: false, // 是否显示剩余时间功能
          fullscreenToggle: true // 是否显示全屏按钮
        }
      },
      emergency: [],
      emergencyresult: [],
      flaw: [],
      flawresult: [],
      flawnumber: 3,
      emergencynumber: 4,
      carInfoDatanumber: 9,
      carPlayDatanumber: 7,
      flawindex: 0,
      emergencyindex: 0,
      flawtimer: null,
      emergencytimer: null,
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      switchImg: false,
      switchVideo: false,
      vList: [],
      vListv: [],
      vListmp: [],
      imgListv: [{ IMAGE_URL: '' }],
      imgListvtimer: null,
      vindex: 0,
      imgindex: 0,
      station_code: '',
      // Background: Background,
      posterimg: posterimg,
      sliderData: [
        {
          title: '计划数：',
          num: ''
        },
        {
          title: '完成数：',
          num: ''
        }
      ],
      carPlayData: [],
      carPlayDataresult: [],
      carPlayDatatimer: null,
      carPlayDataindex: 0,
      // carInfoResult: [],
      num: 0,
      carInfoData: [],
      carInfoDataresult: [],
      carInfoDataindex: 0,
      carInfoDatatimer: null,
      work_center_code: 'HA',
      haStationQueue: [],
      make_order_one: '',
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: '',
      timer5: '',
      timer6: '',
      timer7: ''
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.timer1 = setInterval(() => {
      this.initstationTime()
    }, 1000)
    // 加载工位屏当前订单号
    this.initStationOrder()
    this.timer2 = setInterval(() => {
      this.initStationOrder()
    }, 5000)
    // 加载工位屏订单队列
    this.initStationOrderQueue()
    this.timer3 = setInterval(() => {
      this.initStationOrderQueue()
    }, 5000)
    this.getQueue()
    // // 加载工位屏BOM作业三单关键特性
    this.getBZG()
    // // 加载工位屏缺陷TOP
    this.initStationTop()
    this.timer6 = setInterval(() => {
      this.initStationTop()
    }, 10 * 60 * 1000)
    this.getToptimer()
    // 加载工位生成订单状态
    this.initHaStationScreenQueue()
    this.timer7 = setInterval(() => {
      this.initHaStationScreenQueue()
    }, 15000)
  },
  mounted() {
    // // 加载工位屏订单队列
    this.setQueue()
    // // 加载工位屏BOM作业三单关键特性
    this.setBZG()
    // // 加载工位屏缺陷TOP
    this.setToptimer()
  },
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
    clearInterval(this.timer5)
    clearInterval(this.timer6)
    clearInterval(this.timer7)
  },
  methods: {
    tableRowClassName({ row }) {
      // console.log(row)
      if (row.status === '已完成') {
        this.make_order_one = ''
        return 'row-success'
      }
      if (row.status === '进行中') {
        this.make_order_one = row.make_order
        return 'row-warning'
      }
    },
    // 视频播完回调
    onPlayerEnded($event) {
      this.vindex++
      if (this.vindex >= this.vListv.length) {
        this.vindex = 0
      }
      this.playerOptions.sources[0].src = this.vListv[this.vindex].url
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位生成订单状态
    initHaStationScreenQueue() {
      var query = {
        work_center_code: this.work_center_code,
        station_code: this.$route.query.station_code,
        prod_line_code: this.$route.query.prod_line_code,
        prod_line_type: this.$route.query.prod_line_type
      }
      getHaStationScreenQueue(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.haStationQueue = res.data
          // this.nowDateTime = res.data[0].sysdate
          // this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载工位屏当前订单号
    initStationOrder() {
      var query = {
        station_code: this.$route.query.station_code,
        make_order: this.make_order_one,
        work_center_code: this.work_center_code
      }
      getStationOrder(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          if (this.make_order_one === '') {
            this.carInfoData = []
            this.vList = []
          } else {
            var query = {
              station_code: this.$route.query.station_code,
              bom_station_code: this.$route.query.bom_station_code,
              make_order: this.make_order_one,
              work_center_code: this.work_center_code
            }
            getStationBZG(query)
              .then(res => {
                // console.log(res)
                if (res.code !== 0 && res.count < 0) {
                  console.log('请求数据异常')
                  return
                }
                // BOM
                this.carInfoData = res.data[0].bom_list
                console.log(this.carInfoData)
                // BOM
                // 视频图片轮播
                this.vList = res.data[0].three_list
                console.log(this.vList)
                // 视频图片轮播
              })
              .catch(() => {
                console.log('请求数据为空')
              })
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    setBZG() {
      setInterval(this.getBZG, 5000)
    },
    getBZG() {
      this.carInfoDataresult = this.carInfoData.slice(this.carInfoDataindex * this.carInfoDatanumber, this.carInfoDataindex * this.carInfoDatanumber + this.carInfoDatanumber)
      if (this.carInfoData === null || this.carInfoData.length === 0) {
        this.carInfoDataresult = []
      } else {
        this.carInfoDataresult = this.carInfoData.slice(this.carInfoDataindex * this.carInfoDatanumber, this.carInfoDataindex * this.carInfoDatanumber + this.carInfoDatanumber)
        this.carInfoDataindex++
        // console.log(this.flawresult)
        if (this.carInfoDataresult.length === 0) {
          this.carInfoDataindex = 0
          this.carInfoDataresult = this.carInfoData.slice(this.carInfoDataindex * this.carInfoDatanumber, this.carInfoDataindex * this.carInfoDatanumber + this.carInfoDatanumber)
        }
      }
      this.vListmp = this.vList.filter(value => {
        return value.REVERSED1 === 'mp4'
      })
      this.vListv = this.vListmp
      if (this.vListv.length === 0) {
        this.playerOptions.sources[0].src = ''
      } else if (this.vListv.length === 1) {
        this.playerOptions.loop = true
        this.playerOptions.sources[0].src = this.vListv[this.vindex].IMAGE_URL
      } else {
        this.playerOptions.loop = false
        this.playerOptions.sources[0].src = this.vListv[this.vindex].IMAGE_URL
      }
      var imglists = this.vList.filter(value => {
        return value.REVERSED1 !== 'mp4'
      })
      if (imglists.length === 0) {
        this.imgListv.push({ IMAGE_URL: '' })
      } else {
        this.imgListv = imglists
        this.imgindex++
        if (this.imgindex >= this.imgListv.length) {
          this.imgindex = 0
        }
      }
    },
    // 加载工位屏订单队列
    initStationOrderQueue() {
      var query = {
        station_code: this.$route.query.station_code,
        work_center_code: this.work_center_code
      }
      getStationOrderQueue(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.carPlayData = res.data[0].station_mo_list
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    setQueue() {
      setInterval(this.getQueue, 2000)
    },
    getQueue() {
      this.carPlayDataresult = this.carPlayData.slice(this.carPlayDataindex * this.carPlayDatanumber, this.carPlayDataindex * this.carPlayDatanumber + this.carPlayDatanumber)
      if (this.carPlayData === null || this.carPlayData.length === 0) {
        this.carPlayDataresult = []
      } else {
        this.carPlayDataresult = this.carPlayData.slice(this.carPlayDataindex * this.carPlayDatanumber, this.carPlayDataindex * this.carPlayDatanumber + this.carPlayDatanumber)
        this.carPlayDataindex++
        if (this.carPlayDataresult.length === 0) {
          this.carPlayDataindex = 0
          this.carPlayDataresult = this.carPlayData.slice(this.carPlayDataindex * this.carPlayDatanumber, this.carPlayDataindex * this.carPlayDatanumber + this.carPlayDatanumber)
        }
      }
    },
    // 加载工位屏缺陷TOP
    initStationTop() {
      var query = {
        station_code: this.$route.query.station_code,
        work_center_code: this.work_center_code
      }
      getStationTop(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          // 缺陷TOP
          this.flaw = res.data[0].top_list
          // 缺陷TOP
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    setToptimer() {
      setInterval(this.getToptimer, 2000)
    },
    getToptimer() {
      this.flawresult = this.flaw.slice(this.flawindex * this.flawnumber, this.flawindex * this.flawnumber + this.flawnumber)
      if (this.flaw === null || this.flaw.length === 0) {
        this.flawresult = []
      } else {
        this.flawresult = this.flaw.slice(this.flawindex * this.flawnumber, this.flawindex * this.flawnumber + this.flawnumber)
        this.flawindex++
        if (this.flawresult.length === 0) {
          this.flawindex = 0
          this.flawresult = this.flaw.slice(this.flawindex * this.flawnumber, this.flawindex * this.flawnumber + this.flawnumber)
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.elRowStyle{
  display: flex;
  flex-direction: column;
  width: 100%;
}
::v-deep .el-table__header{
  width: 100%;
}
::v-deep .m-slide{
  background: none !important;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ffba00;
  font-size: 40px;
  font-weight: 700;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.cardStyle{
  margin: 10px;
  padding: 0;
  border: 0;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
      padding: 10px;
  .logoStyle{
    display: flex;
    align-items: center;
    img{
      width: 60px;
      margin-right: 10px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    p{
      margin: 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
      margin-top: 5px !important;
      font-weight: 700;
    }
  }
}
.carInfo{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .carInfoText{
    flex: 1;
  }
  .carInfoImg{
    width: 120px;
    img{
      width: 100%;
    }
  }
}
::v-deep .carInfoTable{
  .el-table--small .el-table__cell {
    padding: 1px 0;
}
}
::v-deep .elRowStyle{
 .el-col{
  margin-bottom: 5px;
 }
}
.carInfoVideo{
  width: 100%;
height: 418px;
    background-color: #ffffff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
  img{
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.dialogVideo{
    position: fixed;
    z-index: 999;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
.carInfodialogVideo{
  // height: 265px;
  background-color: #333333;
      background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}
::v-deep .el-descriptions__title::before{
  content: '';
  width: 6px;
  height: 16px;
  background-color: #6b9ef9;
  display: inline-block;
  margin-right: 8px;
  border-radius: 25px;
}
::v-deep .el-descriptions__title{
  display: flex;
    align-items: center;
}
::v-deep .el-table th {
    background-color: #333333 !important;
    color: #ffffff;
    font-size: 19px;
}
::v-deep .el-table__row td,::v-deep .el-table__row td{
    font-size: 12px;
    font-weight: 700;
    height: 25px;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background:#6b9ef9 !important;
  color: #ffffff !important;

}
::v-deep .el-descriptions-item__container .el-descriptions-item__label,::v-deep .el-descriptions-item__container .el-descriptions-item__content{
  font-size: 19px;
    color: #333333;
    font-weight: 700;
}
::v-deep .el-descriptions__title{
  font-size: 19px;
}
::v-deep .el-descriptions-item{
  padding-bottom: 0 !important;
}
::v-deep .el-descriptions__header{
  margin-bottom: 10px !important;
}
::v-deep .my-label,.labelNum{
  color: #ff0000 !important;
}
::v-deep .el-card__body {
  display: flex;
    padding: 0px !important;
}
::v-deep .el-table .cell{
  line-height: 20px;
}
.carInfoQuestion{
  width: 100%;
  height: 208px;
}
::v-deep .video-player{
      height: 100% !important;
}
::v-deep .video-js.vjs-fluid{
  min-height: 100% !important;
      padding-top: initial !important;
}
::v-deep .video-js .vjs-tech {
  width: 100%;
  height: 100%;
    object-fit: fill !important;
}
::v-deep .vjs_video_433-dimensions.vjs-fluid {
    padding-top: initial !important;
}
::v-deep .video-js{
  background-color: #ffffff !important;
}
::v-deep .vjs-poster{
  background-color: #ffffff !important;
}
.greenactive{
  color: green !important;
}
.redactive{
  color: red !important;
}
.topred{
  color: #ff0000;
}
.nodata,.nogydata{
  color: red;
  font-weight: 700;
  font-size: 19px;
}
.nogydata{
  margin: 0;
  text-align: center;
  height: 180px;
  line-height: 180px;
  color: #909399;
  font-size: 12px;
  font-weight: normal;
  border-radius: 4px;
}
::v-deep .el-descriptions-item__content{
overflow: hidden !important;
white-space: nowrap !important;
text-overflow: ellipsis !important;
display: block !important;
}
::v-deep .vinstyle{
  color: #ff0000 !important;
  font-size: 22px;
}
.gyzlstyle{
  background-color: #333333 !important;
    color: #ffffff;
    font-size: 19px;
    margin: 0;
    padding: 3px;
    padding-left: 10px;
}
.workstationScreen{
  overflow: hidden;
}
::v-deep .row-success{
    color: #fff !important;
    background-color: #67c23a !important;
}
::v-deep .row-warning{
    color: #fff !important;
    background-color: #e6a23c !important;
}
.wrapitem{
  display: flex;
  width: 100%;
  .itemone{
    width: 50%;
    .elRowStyleFlex{
      margin: 0;
      padding: 0;
      .el-col{
        padding-right: 0 !important;
      }
    }
  }
  .itemtwo{
    width: 50%;
    .elRowStyleFlex{
      margin: 0 !important;
      padding: 0 !important;
      .el-col{
        padding-right: 0 !important;
        padding-left: 0 !important;
      }
    }
  }
}
// img[src=""] {
//     opacity: 0;::
//   }
</style>
