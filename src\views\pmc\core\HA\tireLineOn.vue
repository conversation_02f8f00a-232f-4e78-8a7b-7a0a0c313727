<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardOne">
      <div class="headerStyle">
        <div class="logoStyle"><img src="@/assets/images/log.png" alt=""><div class="currentPlay">今日计划：<span>{{ today_plan }}</span></div></div>
        <div class="headerC">
          <h2>轮胎线上线</h2>
        </div>
        <div class="headerL">
          {{ nowDateTime }} {{ nowDateWeek }}
        </div>
      </div>
    </el-card>
    <el-card class="cardStyle" shadow="never">
      <div class="wrapitem">
        <el-row :gutter="24" class="elRowStyle">
          <el-col :span="6" style="padding-right:0px;">
            <el-card shadow="never">
              <div class="carInfoTable">
                <el-table
                  :data="orderList"
                  style="width: 100%"
                  height="620"
                  :row-class-name="tableRowClassName"
                >
                  <el-table-column 
                    prop="seq"
                    label="序号"
                    width="70"
                  />
                  <el-table-column 
                    prop="make_order"
                    label="订单号"
                    width="150"
                  />
                  <el-table-column 
                    prop="work_status_name"
                    label="状态"
                  />
                </el-table>
              </div>
            </el-card>
          </el-col>
          <el-col :span="18">
            <el-card shadow="never">
              <div class="carInfoTable">
                <el-table
                  :data="newtyreList"
                  style="width: 100%"
                  class="partstyle"
                  height="620"
                  :row-class-name="gettableRowClassName"
                >
                  <el-table-column 
                    prop="seq"
                    label="序号"
                    width="70"
                  />
                  <el-table-column 
                    prop="tyre_name"
                    label="上线轮胎名称"
                    width="200"
                  />
                  <el-table-column 
                    prop="tyre_material_code"
                    label="上线轮胎图号"
                  />
                  <el-table-column 
                    prop="feature_value_desc"
                    label="规格"
                    width="160"
                  />
                  <el-table-column 
                    prop="tyre_number"
                    label="数量"
                    width="120"
                  />
                </el-table>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getStationTime, getPmcBqTyreStationLeft, getPmcBqTyreStationRight } from '@/api/pmc/sysworkstationScreen'
import { getCrueentPlay } from '@/api/pmc/sysworkshopScreenone'

export default {
  name: 'tireLineOn',
  data() {
    return {
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      orderList: [],
      tyreList: [],
      newtyreList: [],
      today_plan: '',
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: ''
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.timer1 = setInterval(() => {
      this.initstationTime()
    }, 1000)
    this.initCrueentPlay()
    this.timer2 = setInterval(() => {
      this.initCrueentPlay()
    }, 5000)
    this.initPmcBqTyreStationLeft()
    this.timer3 = setInterval(() => {
      this.initPmcBqTyreStationLeft()
    }, 10000)
    this.initPmcBqTyreStationRight()
    this.timer3 = setInterval(() => {
      this.initPmcBqTyreStationRight()
    }, 10000)
  },
  mounted() {

  },
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
  },
  methods: {
    tableRowClassName({ row }) {
      if (row.work_status_name === '已完成') {
        return 'row-success'
      }
      if (row.work_status_name === '进行中') {
        return 'row-warning'
      }
    },
    gettableRowClassName({ row, rowIndex }) {
      console.log(row, rowIndex)
      if (row.checked_result === 'Y') {
        return 'row-success'
      }
      const firstUncheckedIndex = this.newtyreList.findIndex(
        (item) => item.checked_flag === 'N'
      )
      console.log(firstUncheckedIndex)
      if (rowIndex === firstUncheckedIndex) {
        return 'row-warning'
      }
      return ''
    },
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 加载今日计划
    initCrueentPlay() {
      var query = {}
      getCrueentPlay(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.today_plan = res.orderNum
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 轮胎线左
    initPmcBqTyreStationLeft() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getPmcBqTyreStationLeft(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.orderList = res.orderList.slice(0, 9)
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 轮胎线右
    initPmcBqTyreStationRight() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getPmcBqTyreStationRight(query)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tyreList = res.tyreList
          let startIndex = -1
          this.newtyreList = []
          // 查找第一个 checked_flag 为 N 的元素的索引
          for (let i = 0; i < this.tyreList.length; i++) {
            if (this.tyreList[i].checked_flag === 'N') {
              startIndex = i
              break
            }
          }

          if (startIndex !== -1) {
            // 往上截取三条数据
            for (let i = startIndex - 3; i < startIndex; i++) {
              if (i >= 0) {
                this.newtyreList.push(this.tyreList[i])
              }
            }

            // 添加第一个 checked_flag 为 N 的元素
            this.newtyreList.push(this.tyreList[startIndex])

            // 往下截取四条数据
            for (let i = startIndex + 1; i <= startIndex + 5; i++) {
              if (i < this.tyreList.length) {
                this.newtyreList.push(this.tyreList[i])
              }
            }
          } else {
            console.log('未找到 checked_flag 为 N 的元素')
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
.elRowStyle{
  display: flex;
}
::v-deep .el-table__header{
  width: 100%;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ffba00;
  font-size: 40px;
  font-weight: 700;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.cardStyle{
  margin: 0;
  padding: 0;
  border: 0;
}
.cardOne{
  margin-bottom: 12px;
}
.headerStyle{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 15px;
  .logoStyle{
    width: 30%;
    display: flex;
    align-items: center;
    img{
      width: 130px;
    }
    .currentPlay{
      font-size: 28px;
    font-weight: 700;
    margin-left: 30px;
    color: #ff1100;
    }
  }
  .headerC{
    h2{
      font-size: 56px;
      margin: 0;
    }
  }
  .headerL{
    width: 30%;
    font-size: 26px;
    text-align: right;
    font-weight: 700;
  }
}
::v-deep .el-table th {
    background-color: #ffffff !important;
    color: #000000;
    font-size: 20px;
    border-bottom: 1px solid #dfe6ec !important;
}
::v-deep .el-table__row td,::v-deep .el-table__row td{
    font-size: 20px;
    font-weight: 700;
    height: 25px;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background:#6b9ef9 !important;
  color: #ffffff !important;

}
::v-deep .el-card__body {
  display: flex;
    padding: 0px !important;
}
::v-deep .el-table .cell{
  line-height: 45px;
}
.greenactive{
  color: green !important;
}
::v-deep .row-success{
    color: #fff !important;
    background-color: #67c23a !important;
}
::v-deep .row-warning{
    color: #fff !important;
    background-color: #e6a23c !important;
}
.wrapitem{
  width: 100%;
  .itemone{
    width: 50%;
    .elRowStyleFlex{
      margin: 0;
      padding: 0;
      .el-col{
        padding-right: 0 !important;
      }
    }
  }
  .itemtwo{
    width: 50%;
    .elRowStyleFlex{
      margin: 0 !important;
      padding: 0 !important;
      .el-col{
        padding-right: 0 !important;
        padding-left: 0 !important;
      }
    }
  }
}
</style>
