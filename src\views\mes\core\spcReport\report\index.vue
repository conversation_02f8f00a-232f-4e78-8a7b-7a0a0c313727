<template>
    <div class="box-card">
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
            <el-form ref="query" :inline="true" size="mini" style="margin-top: 15px; padding: 0" label-width="70px">
                <el-row>
                    <el-form-item label="产线" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.prod_line_code" clearable style="width: 150px" value-key="prod_line_id"
                            @change="handleProdLineChange">
                            <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_des"
                                :value="item">
                                <span style="float: left">{{ item.prod_line_des }}</span>
                                <span style="
                                    float: right;
                                    margin-left: 20px;
                                    color: #8492a6;
                                    font-size: 13px;
                                ">{{ item.prod_line_code }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="机型" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.small_model_type" filterable clearable style="width: 130px">
                            <el-option v-for="item in smallModelTypeData" :key="item.small_type_id"
                                :label="item.small_model_type" :value="item.small_model_type">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="工位号" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.station_code" filterable clearable style="width: 150px"
                            value-key="station_code" @change="getQulityforData">
                            <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_code"
                                :value="item">
                                <span style="float: left">{{ item.station_code }}</span>
                                <span style="
                            float: right;
                            margin-left: 20px;
                            color: #8492a6;
                            font-size: 13px;
                        ">{{ item.station_des }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="控制项目" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.tag_des" filterable clearable style="width: 220px">
                            <el-option v-for="item in tagList" :key="item.tag_des" :label="item.tag_des"
                                :value="item.tag_des" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="查询时间" style="margin: 0px 0px 5px 0px">
                        <el-date-picker v-model="query.leave_date" type="datetimerange" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" range-separator="~" start-placeholder="开始日期"
                            end-placeholder="结束日期" style="width: 350px" align="right" />
                    </el-form-item>
                    <el-button style="margin: 0px 0px 5px 0px" class="filter-item" size="mini" type="primary"
                        icon="el-icon-search" @click="handleQuery()">查询</el-button>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <el-table ref="table" v-loading="tableLoading" :data="tableData" style="width: 100%"
                :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" :height="height"
                :highlight-current-row="true">
                <el-table-column :show-overflow-tooltip="true" prop="prod_line_code" width="120" label="产线编码" />
                <el-table-column :show-overflow-tooltip="true" prop="prod_line_des" width="240" label="产线描述" />
                <el-table-column :show-overflow-tooltip="true" prop="station_code" label="工位编号" />
                <el-table-column :show-overflow-tooltip="true" prop="station_des" width="300" label="工位描述" />
                <el-table-column :show-overflow-tooltip="true" prop="small_model_type" label="机型" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_des" width="130" label="控制项目" />
                <el-table-column :show-overflow-tooltip="true" prop="tag_value" label="采集值" />
                <el-table-column :show-overflow-tooltip="true" prop="upper_limit" label="上限值" />
                <el-table-column :show-overflow-tooltip="true" prop="down_limit" label="下限值" />
                <el-table-column :show-overflow-tooltip="true" prop="trace_d_time" width="130" label="采集时间" />
            </el-table>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <el-tabs v-model="tabValue" style="height: calc(100vh - 520px)" @tab-click="handleClick">
                <el-tab-pane v-for="(item, index) in tablePane" :name="item.name">
                    <span slot="label"><i :class="item.icon" /> {{ item.name_desc }}</span>
                    <div style="display:flex;">
                        <div>
                            <template v-if="item.name === 'SampleTrend'">
                                <el-button @click="getSampleTrend('0')">小时</el-button>
                                <el-button @click="getSampleTrend('1')">日</el-button>
                                <el-button @click="getSampleTrend('2')">周</el-button>
                                <el-button @click="getSampleTrend('3')">月</el-button>
                            </template>
                            <template v-else-if="item.name === 'ProcessTrend'">
                                <el-date-picker v-model="process_date" type="datetimerange" size="small" align="right"
                                    unlink-panels range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss" />
                                <el-button @click="getProcessTrend()">查询</el-button>
                            </template>
                            <template v-else-if="item.name === 'YieldTrend'">
                                <el-date-picker v-model="yield_date" type="datetimerange" size="small" align="right"
                                    unlink-panels range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss" />
                                <el-button @click="getYieldTrend()">查询</el-button>
                                <el-button @click="getYieldTrend('1')">日</el-button>
                                <el-button @click="getYieldTrend('2')">周</el-button>
                                <el-button @click="getYieldTrend('3')">月</el-button>
                            </template>
                            <ECharts :id="item.id" :ref="'cpk_chart' + (index + 1)" :options="dynamicOrgOptions(index + 1)"
                                style="width: 1200px;height: calc(100vh - 590px)" />
                        </div>
                        <div class="sampleStyle" v-if="item.name === 'SampleTrend'">
                            <div class="sampleOperate">
                                <el-checkbox ref="option1" v-model="option1"
                                    @change="handleCheckedChange('option1')">取样间隔时间设定</el-checkbox>
                                <div>
                                    <span>间隔分钟</span>
                                    <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                                <div>
                                    <span>共取次数</span>
                                    <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                            </div>
                            <div class="sampleOperate">
                                <el-checkbox ref="option2" v-model="option2"
                                    @change="handleCheckedChange('option2')">取样间隔数量设定</el-checkbox>
                                <div>
                                    <span>间隔条数</span>
                                    <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                                <div>
                                    <span>共取次数</span>
                                    <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                            </div>
                            <div class="sampleOperate">
                                <el-checkbox ref="option3" v-model="option3"
                                    @change="handleCheckedChange('option3')">取样前N条设定</el-checkbox>
                                <div>
                                    <span>&nbsp;&nbsp;开始条数</span>
                                    <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                                <div>
                                    <span>前N条数据</span>
                                    <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                            </div>
                            <div class="sampleOperate">
                                <el-checkbox ref="option3" v-model="option3"
                                    @change="handleCheckedChange('option4')">取样后N条设定</el-checkbox>
                                <div>
                                    <span>&nbsp;&nbsp;开始条数</span>
                                    <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                                <div>
                                    <span>后N条数据</span>
                                    <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                            </div>
                            <div class="sampleOperate">
                                <el-checkbox ref="option3" v-model="option3"
                                    @change="handleCheckedChange('option5')">取样开始位置设定</el-checkbox>
                                <div>
                                    <span>开始条数</span>
                                    <el-input-number v-model="query.attr1" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                                <div>
                                    <span>间隔数量</span>
                                    <el-input-number v-model="query.attr2" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                                <div>
                                    <span>共取次数</span>
                                    <el-input-number v-model="query.attr3" clearable size="mini" :min="1" :max="99999"
                                        style="width: 100px" />
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>
</template>
<script>
import {
    mesQualitySpcAnalyze, mesQualitySpcTagList, sampleTrend, basicTrend, histogram, meanVariance, meanStandardDeviation
    , processCapability, yieldData, oee
} from '@/api/mes/core/spcReport'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import ECharts from 'vue-echarts'
export default {
    name: 'MES_SPC_DATA_REPORT',
    components: {
        ECharts
    },
    data() {
        return {
            height: document.documentElement.clientHeight - 610,
            tabValue: 'SampleTrend',
            orgOptions1: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                        label: {
                            show: true,
                        },
                    },
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: [150, 230, 224, 218, 135, 147, 260],
                        type: 'line',
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true, //开启显示
                                    position: 'top', //顶部显示
                                    textStyle: {
                                        //数值样式
                                        color: 'black',
                                        fontSize: 16,
                                    },
                                },
                                color: '#4472C4',
                            },
                        },
                    }
                ]
            },
            orgOptions2: {
                backgroundColor: 'white',
                grid: {
                    top: '20%',
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    borderWidth: 1,
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: 'z-index:2'

                },
                legend: [{
                    top: 'top',
                    left: 'center',
                    orient: 'horizontal',
                    data: ['下限', '平均值', '上限'],
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 15,
                    borderRadius: 4,
                    textStyle: {
                        color: '#000',
                        fontFamily: 'Alibaba PuHuiTi',
                        fontSize: 14,
                        fontWeight: 400
                    }
                }],
                xAxis: {
                    type: 'category',
                    data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#393939' //X轴文字颜色
                        }
                    }
                },
                yAxis: [

                    {
                        type: 'value',
                        name: '',
                        nameTextStyle: {
                            color: '#000',
                            fontFamily: 'Alibaba PuHuiTi',
                            fontSize: 14,
                            fontWeight: 600
                            // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                        },
                        nameGap: 30,  // 表现为上下位置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: '#393939',
                            fontSize: 14
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        }

                    }

                ],
                series: [
                    {
                        name: '下限',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5470c6',
                            normal: {
                                color: '#5470c6'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5470c6'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                    {
                        name: '平均值',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#91cc75',
                            normal: {
                                color: '#91cc75'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#91cc75'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                    {
                        name: '上限',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5fc79e',
                            normal: {
                                color: '#5fc79e'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5fc79e'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                ]
            },
            orgOptions3: {
                backgroundColor: 'white',
                tooltip: {
                    trigger: 'axis',
                },
                calculable: true,
                xAxis: [
                    {
                        type: 'category',
                        data: ['2012年', '2013年', '2014年', '2015年', '2016年', '2017年', '2018年', '2019年'],
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#000',
                            },
                        },
                    },
                ],
                yAxis: [
                    {
                        interval: 10,
                        name: '频率',
                        type: 'value',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#000',
                            },
                        },
                        splitLine: {
                            show: false, // 取消网格线
                            lineStyle: {
                                color: '#000',
                            },
                        },
                        axisLabel: {
                            color: '#000', // 设置刻度标签颜色
                        },
                    },
                ],
                series: [
                    {
                        type: 'bar',
                        barWidth: 60,
                        data: [9, 8, 10, 7, 2, 2, 5, 10],
                        color: '#C0C0C0',
                        barGap: 0,
                        itemStyle: {
                            borderWidth: 1, // 设置边框宽度
                            borderColor: '#000', // 设置边框颜色
                        },
                    },
                    {
                        type: 'bar',
                        barWidth: 60,
                        data: [8, 4, 9, 7, 10, 9, 6, 4, 3],
                        color: '#C0C0C0',
                        barGap: 0,
                        itemStyle: {
                            borderWidth: 1, // 设置边框宽度
                            borderColor: '#000', // 设置边框颜色
                        },
                    },
                ],
                legend: {
                    textStyle: {
                        // 图例文字的样式
                        color: '#fff',
                        fontSize: 18,
                        padding: [2, 0, 0, 2],
                        fontFamily: '微软雅黑',
                        fontWeight: 'normal',
                    },
                },
            },
            orgOptions4: {
                backgroundColor: 'white',
                grid: {
                    top: '20%',
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    borderWidth: 1,
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: 'z-index:2'

                },
                legend: [{
                    top: 'top',
                    left: 'center',
                    orient: 'horizontal',
                    data: ['均值'],
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 15,
                    borderRadius: 4,
                    textStyle: {
                        color: '#000',
                        fontFamily: 'Alibaba PuHuiTi',
                        fontSize: 14,
                        fontWeight: 400
                    }
                }],
                xAxis: {
                    type: 'category',
                    data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#393939' //X轴文字颜色
                        }
                    }
                },
                yAxis: [

                    {
                        type: 'value',
                        name: '',
                        nameTextStyle: {
                            color: '#000',
                            fontFamily: 'Alibaba PuHuiTi',
                            fontSize: 14,
                            fontWeight: 600
                            // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                        },
                        nameGap: 30,  // 表现为上下位置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: '#393939',
                            fontSize: 14
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        }

                    }

                ],
                series: [
                    {
                        name: '均值',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5470c6',
                            normal: {
                                color: '#5470c6'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5470c6'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                ]
            },
            orgOptions5: {
                backgroundColor: 'white',
                grid: {
                    top: '20%',
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    borderWidth: 1,
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: 'z-index:2'

                },
                legend: [{
                    top: 'top',
                    left: 'center',
                    orient: 'horizontal',
                    data: ['均值'],
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 15,
                    borderRadius: 4,
                    textStyle: {
                        color: '#000',
                        fontFamily: 'Alibaba PuHuiTi',
                        fontSize: 14,
                        fontWeight: 400
                    }
                }],
                xAxis: {
                    type: 'category',
                    data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#393939' //X轴文字颜色
                        }
                    }
                },
                yAxis: [

                    {
                        type: 'value',
                        name: '',
                        nameTextStyle: {
                            color: '#000',
                            fontFamily: 'Alibaba PuHuiTi',
                            fontSize: 14,
                            fontWeight: 600
                            // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                        },
                        nameGap: 30,  // 表现为上下位置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: '#393939',
                            fontSize: 14
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        }

                    }

                ],
                series: [
                    {
                        name: '均值',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5470c6',
                            normal: {
                                color: '#5470c6'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5470c6'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                ]
            },
            orgOptions6: {
                backgroundColor: 'white',
                grid: {
                    top: '20%',
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    borderWidth: 1,
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: 'z-index:2'

                },
                legend: [{
                    top: 'top',
                    left: 'center',
                    orient: 'horizontal',
                    data: ['cp', 'cpk'],
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 15,
                    borderRadius: 4,
                    textStyle: {
                        color: '#000',
                        fontFamily: 'Alibaba PuHuiTi',
                        fontSize: 14,
                        fontWeight: 400
                    }
                }],
                xAxis: {
                    type: 'category',
                    data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#393939' //X轴文字颜色
                        }
                    }
                },
                yAxis: [

                    {
                        type: 'value',
                        name: '',
                        nameTextStyle: {
                            color: '#000',
                            fontFamily: 'Alibaba PuHuiTi',
                            fontSize: 14,
                            fontWeight: 600
                            // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                        },
                        nameGap: 30,  // 表现为上下位置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: '#393939',
                            fontSize: 14
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        }

                    }

                ],
                series: [
                    {
                        name: 'cp',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5470c6',
                            normal: {
                                color: '#5470c6'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5470c6'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                    {
                        name: 'cpk',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#91cc75',
                            normal: {
                                color: '#91cc75'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#91cc75'
                        },
                        data: [0, 0, 0, 0, 0]
                    }
                ]
            },
            orgOptions7: {
                backgroundColor: 'white',
                grid: {
                    top: '20%',
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    borderWidth: 1,
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: 'z-index:2'

                },
                legend: [{
                    top: 'top',
                    left: 'center',
                    orient: 'horizontal',
                    data: ['良品率'],
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 15,
                    borderRadius: 4,
                    textStyle: {
                        color: '#000',
                        fontFamily: 'Alibaba PuHuiTi',
                        fontSize: 14,
                        fontWeight: 400
                    }
                }],
                xAxis: {
                    type: 'category',
                    data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#393939' //X轴文字颜色
                        }
                    }
                },
                yAxis: [

                    {
                        type: 'value',
                        name: '',
                        nameTextStyle: {
                            color: '#000',
                            fontFamily: 'Alibaba PuHuiTi',
                            fontSize: 14,
                            fontWeight: 600
                            // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                        },
                        nameGap: 30,  // 表现为上下位置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: '#393939',
                            fontSize: 14
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        }

                    }

                ],
                series: [
                    {
                        name: '良品率',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5470c6',
                            normal: {
                                color: '#5470c6'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5470c6'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                ]
            },
            orgOptions8: {
                backgroundColor: 'white',
                grid: {
                    top: '20%',
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    borderWidth: 1,
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: 'z-index:2'

                },
                legend: [{
                    top: 'top',
                    left: 'center',
                    orient: 'horizontal',
                    data: ['性能开动率', '合格率', '时间开动率'],
                    itemWidth: 15,
                    itemHeight: 10,
                    itemGap: 15,
                    borderRadius: 4,
                    textStyle: {
                        color: '#000',
                        fontFamily: 'Alibaba PuHuiTi',
                        fontSize: 14,
                        fontWeight: 400
                    }
                }],
                xAxis: {
                    type: 'category',
                    data: ['13:00', '14:00', '15:00', '16:00', '17:00'],
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#393939' //X轴文字颜色
                        }
                    }
                },
                yAxis: [

                    {
                        type: 'value',
                        name: '',
                        nameTextStyle: {
                            color: '#000',
                            fontFamily: 'Alibaba PuHuiTi',
                            fontSize: 14,
                            fontWeight: 600
                            // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                        },
                        nameGap: 30,  // 表现为上下位置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            color: '#393939',
                            fontSize: 14
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#eeeeee'
                            }
                        }

                    }

                ],
                series: [
                    {
                        name: '性能开动率',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        //标记的图形为实心圆
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5470c6',
                            normal: {
                                color: '#5470c6'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5470c6'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                    {
                        name: '合格率',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#91cc75',
                            normal: {
                                color: '#91cc75'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#91cc75'
                        },
                        data: [0, 0, 0, 0, 0]
                    },
                    {
                        name: '时间开动率',
                        type: 'line',
                        showAllSymbol: true, //显示所有图形。
                        symbolSize: 8, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: 'white',
                            borderWidth: '2',
                            borderColor: '#5fc79e',
                            normal: {
                                color: '#5fc79e'//拐点颜色
                            }
                        },
                        lineStyle: {
                            color: '#5fc79e'
                        },
                        data: [0, 0, 0, 0, 0]
                    }
                ]
            },
            // 样本个数
            samples: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            // 样本取值
            values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            // 上限值
            upper_limit: 0,
            // 下限值
            down_limit: 0,
            tag_uom: '',
            prodLineData: [],
            smallModelTypeData: [],
            stationData: [],
            tableData: [],
            tableDataCatch: [],
            tagList: [],
            tableLoading: false,
            option1: true,
            option2: false,
            option3: false,
            option4: false,
            option5: false,
            customPopover1: false,
            query: {
                station_code: '',
                small_model_type: '',
                quality_for: '',
                selectedoption: 'option1',
                attr1: 1,
                attr2: 1,
                attr3: 1,
                leave_date: null
            },
            result: {
                C: 0.0,
                X: 0.0,
                T: 0.0,
                δ: 0.0,
                Ca: 0.0,
                Cp: 0.0,
                Cpk: 0.0,
                USL: 0.0,
                LSL: 0.0
            },
            // 时间选择器
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            tablePane: [
                {
                    name: 'SampleTrend',//样本趋势图
                    icon: 'el-icon-s-data',
                    id: 'sampleChart',
                    name_desc: '样本趋势图',
                },
                {
                    name: 'BasicTrend',//基本趋势图
                    icon: 'el-icon-s-data',
                    id: 'basicChart',
                    name_desc: '基本趋势图',
                },
                {
                    name: 'HistogramTrend',//直方图
                    icon: 'el-icon-s-data',
                    id: 'histogramChart',
                    name_desc: '直方图',
                },
                {
                    name: 'MeanTrend',//均值方差图
                    icon: 'el-icon-s-data',
                    id: 'meanChart',
                    name_desc: '均值方差图',
                },
                {
                    name: 'MeanStandardTrend',//均值标准差图
                    icon: 'el-icon-s-data',
                    id: 'meanStandardChart',
                    name_desc: '均值标准差图',
                },
                {
                    name: 'ProcessTrend',//工序能力分析
                    icon: 'el-icon-s-data',
                    id: 'processChart',
                    name_desc: '工序能力分析',
                },
                {
                    name: 'YieldTrend',//良品率
                    icon: 'el-icon-s-data',
                    id: 'YIELDChart',
                    name_desc: '良品率',
                },
                {
                    name: 'OeeTrend',//oee分析图
                    icon: 'el-icon-s-data',
                    id: 'oeeChart',
                    name_desc: 'oee分析图',
                },
            ],
            timer: null,
            sampleVal: '',
            process_date: null,
            yield_date: null,
            yieldVal: '',
        }
    },
    mounted() {
        // 样本趋势图
        this.getSampleTrend()
        this.timer = setInterval(() => {
            this.getSampleTrend()
        }, 10000)
        // 基本趋势图
        // this.getBasicTrend()
        // 直方图
        // this.getHistogramTrend()
        // 均值方差图
        // this.getMeanTrend()
        // 均值标准差图
        // this.getMeanStandardTrend()
        // 工序能力分析
        // this.getProcessTrend()
        // 良品率
        // this.getYieldTrend()
        // oee分析图
        // this.getOeeTrend()
    },
    beforeDestory() {
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    methods: {
        dynamicOrgOptions(index) {
            return this['orgOptions' + index];
        },
        handleProdLineChange(data) {
            const query = {
                userID: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            }
            selStation(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.stationData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                })

            selSmallModel({
                user_name: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            })
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.smallModelTypeData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '初始化模式数据异常',
                        type: 'error'
                    })
                })
        },
        getQulityforData(data) {
            this.tagList = []
            mesQualitySpcTagList({
                enable_flag: 'Y',
                station_id: data.station_id
            })
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.tagList = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询采集项目异常',
                        type: 'error'
                    })
                })
        },
        handleClick(tab, event) {
            if (tab.name) {
                // 判断，是否存在name，存在后清楚定时器，在生成新的定时器
                clearInterval(this.timer)
                // 根据拿到的name匹配上动态调用接口，这样就不用一次调用所有的接口了
                this[`get${tab.name}`]()
                this.timer = setInterval(() => {
                    this[`get${tab.name}`]()
                }, 10000)
            }
        },
        getSampleTrend(val) {
            if (val) {
                this.sampleVal = val
            }
            sampleTrend({ timeDimension: this.sampleVal }).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.stationQualityInfo)
                        const xAxisData = []
                        const seriesData = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key].length)
                        }
                        this.orgOptions1.xAxis.data = xAxisData
                        this.orgOptions1.series[0].data = seriesData
                    }
                }
            })
        },
        getBasicTrend() {
            basicTrend({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.stationQualityInfo)
                        const xAxisData = []
                        const seriesData = []
                        const seriesData1 = []
                        const seriesData2 = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key].downLimit)
                            seriesData1.push(data[key].avg)
                            seriesData2.push(data[key].upperLimit)
                        }
                        this.orgOptions2.xAxis.data = xAxisData
                        this.orgOptions2.series[0].data = seriesData
                        this.orgOptions2.series[1].data = seriesData1
                        this.orgOptions2.series[2].data = seriesData2
                    }
                }
            })
        },
        getHistogramTrend() {
            histogram({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        console.log(defaultQuery)
                        this.orgOptions3.xAxis.data = defaultQuery.data.xCoordinate
                        // {
                        //     type: 'bar',
                        //     barWidth: 60,
                        //     data: [9, 8, 10, 7, 2, 2, 5, 10],
                        //     color: '#C0C0C0',
                        //     barGap: 0,
                        //     itemStyle: {
                        //         borderWidth: 1, // 设置边框宽度
                        //         borderColor: '#000', // 设置边框颜色
                        //     },
                        // },
                    }
                }
            })
        },
        getMeanTrend() {
            meanVariance({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (Object.keys(defaultQuery.data).length > 0) {
                    const data = this.sortObjectByTimestamp(defaultQuery.data.meanVariance)
                    const xAxisData = []
                    const seriesData = []
                    const seriesData1 = []
                    for (let key in data) {
                        xAxisData.push(key)
                        seriesData.push(data[key])
                    }
                    this.orgOptions4.xAxis.data = xAxisData
                    this.orgOptions4.series[0].data = seriesData
                }
            })
        },
        getMeanStandardTrend() {
            meanStandardDeviation({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.meanStandardDeviation)
                        const xAxisData = []
                        const seriesData = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key])
                        }
                        this.orgOptions5.xAxis.data = xAxisData
                        this.orgOptions5.series[0].data = seriesData
                    }
                }
            })
        },
        getProcessTrend() {
            const query = {
                itemDate: this.process_date
            }
            processCapability(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.meanStandardDeviation)
                        const xAxisData = []
                        const seriesData = []
                        const seriesData1 = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key].cp)
                            seriesData1.push(data[key].cpk)
                        }
                        this.orgOptions6.xAxis.data = xAxisData
                        this.orgOptions6.series[0].data = seriesData
                        this.orgOptions6.series[1].data = seriesData1
                    }
                }
            })
        },
        getYieldTrend(val) {
            if (val) {
                this.yieldVal = val
            }
            const query = {
                itemDate: this.yield_date,
                timeDimension: this.yieldVal
            }
            yieldData(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.yields)
                        const xAxisData = []
                        const seriesData = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key])
                        }
                        this.orgOptions7.xAxis.data = xAxisData
                        this.orgOptions7.series[0].data = seriesData
                    }
                }
            })
        },
        getOeeTrend() {
            oee({}).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code == '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        const data = this.sortObjectByTimestamp(defaultQuery.data.oeeInfo[0])
                        const xAxisData = []
                        const seriesData = []
                        const seriesData1 = []
                        const seriesData2 = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key].performanceEfficiency)
                            seriesData1.push(data[key].qualificationRate)
                            seriesData2.push(data[key].timeActivationRate)
                        }
                        this.orgOptions8.xAxis.data = xAxisData
                        this.orgOptions8.series[0].data = seriesData
                        this.orgOptions8.series[1].data = seriesData1
                        this.orgOptions8.series[2].data = seriesData2
                    }
                }
            })
        },
        handleCheckedChange(option) {
            this.query.selectedOption = option;
            ['option1', 'option2', 'option3', 'option4', 'option5'].forEach(item => this[item] = option === item);
        },
        // 字符串转时间戳并且支持排序的方法
        sortObjectByTimestamp(obj) {
            var sortedArray = Object.entries(obj).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            });
            var sortedObj = {};
            for (var i = 0; i < sortedArray.length; i++) {
                var key = sortedArray[i][0];
                var value = sortedArray[i][1];
                sortedObj[key] = value;
            }
            return sortedObj;
        }
    }
}
</script>
<style scoped lang="less">
.sampleStyle {
    display: flex;
    overflow-x: hidden; //超出隐藏
    flex-wrap: wrap; //超出自动换行

    .sampleOperate {
        width: 50%;

        div {
            margin-top: 5px;
            text-align: right;

            span {
                margin-right: 5px;
            }
        }
    }
}
</style>
