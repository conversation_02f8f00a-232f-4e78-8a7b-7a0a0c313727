<template>
  <div id="loadMonitor" class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.version') + ':'">
                <!-- 版本 -->
                <el-input v-model="query.model_version" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <!-- 料号 -->
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.taskSource') + ':'">
                <!-- 任务来源 -->
                <el-select v-model="query.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_FROM"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.task_status') + ':'">
                <!-- 任务状态 -->
                <el-select v-model="query.lot_status" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('view.field.plan.lotNum') + ':'">
                <!-- 订单号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card ref="queryCard" shadow="never" class="wrapCard" style="margin-top: 10px;">
      <div style="display:flex;align-items: center;justify-content: space-between;">
        <div class="orderInfo">
          <span>{{ $t('lang_pack.vie.scanOrdNumMESTask') }}</span>
          <div />
          <el-button size="small" type="primary">
            <svg-icon icon-class="scan" style="margin-right:5px" />{{ $t('lang_pack.vie.scan') }}
          </el-button>
        </div>
        <el-button
          size="medium"
          type="primary"
          :icon="controlValue === '1' ? 'el-icon-video-play' : 'el-icon-video-pause'"
          class="playBtn"
          :class="{ 'playStart': controlValue === '1', 'playEnd': controlValue != '1' }"
          @click="handleTagWrite('SortPlc/PlcBase/AppStart')"
        >{{ controlValue === '1' ? 'Start' : 'Stop'
        }}</el-button>
      </div>
    </el-card>
    <el-row :gutter="20">
      <el-col :span="18">
        <el-card shadow="always" style="margin-top: 10px">
          <!--工具栏-->
          <crudOperation show="" :permission="permission">
            <template slot="right">
              <el-button
                size="small"
                type="primary"
                @click="taskCancelOrInit($t('lang_pack.vie.Cancellation'), { lot_status: 'CANCEL' }, 'cancel')"
              >{{ $t('lang_pack.vie.Cancellation') }}</el-button>
              <el-button
                size="small"
                type="primary"
                @click="taskCancelOrInit($t('lang_pack.vie.TaskInit'), { lot_status: 'PLAN', finish_ok_count: 0, finish_ng_count: 0 }, 'init')"
              >{{ $t('lang_pack.vie.TaskInit') }}</el-button>
            </template>
          </crudOperation>
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.vie.taskSource') + '：'" prop="m_length">
                <!-- 任务状态 -->
                <el-select v-model="form.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_FROM"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.task_type') + '：'" prop="m_length">
                <!-- 任务类型 -->
                <el-select v-model="form.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plantCode') + '：'" prop="plant_code">
                <!-- 工厂编码 -->
                <el-input v-model="form.plant_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('view.field.plan.lotNum') + '：'" prop="lot_num">
                <!-- 订单号 -->
                <el-input v-model="form.lot_num" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.partNum') + '：'" prop="model_type">
                <!-- 料号 -->
                <el-input v-model="form.model_type" readonly="readonly">
                  <div slot="append">
                    <el-button slot="reference" @click="handleSelect">{{ $t('lang_pack.vie.select') }}</el-button>
                  </div>
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.version') + '：'" prop="model_version">
                <!-- 版本 -->
                <el-input v-model="form.model_version" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.setType') + '：'" prop="array_type">
                <!-- SET类型 -->
                <el-select v-model="form.array_type" clearable filterable disabled>
                  <el-option
                    v-for="item in dict.QR_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.pcsType') + '：'" prop="bd_type">
                <!-- PCS类型 -->
                <el-select v-model="form.bd_type" clearable filterable disabled>
                  <el-option
                    v-for="item in dict.QR_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateLen')" prop="m_length">
                <!-- 长 -->
                <el-input v-model="form.m_length" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateWid')" prop="m_width">
                <!-- 宽 -->
                <el-input v-model="form.m_width" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateThi')" prop="m_tickness">
                <!-- 厚 -->
                <el-input v-model="form.m_tickness" disabled clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateWei')" prop="m_weight">
                <!-- 重 -->
                <el-input v-model="form.m_weight" disabled clearable size="small" />
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.vie.cusPartNum') + '：'" prop="part_barcode">
                                    客户料号
                                    <el-input v-model="form.part_barcode" clearable size="small" />
                                </el-form-item> -->
              <el-form-item :label="$t('lang_pack.vie.planQuantity') + '：'" prop="plan_lot_count">
                <!-- 计划数量 -->
                <el-input
                  v-model.number="form.plan_lot_count"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.singlePackQua') + '：'" prop="unit_count">
                <!-- 单包数量 -->
                <el-input
                  v-model.number="form.unit_count"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.cycle') + '：'" prop="cycle_period">
                <!-- 周期 -->
                <el-input
                  v-model.number="form.cycle_period"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.salesOrder') + '：'" prop="sales_order">
                <!-- 销售订单号 -->
                <el-input v-model="form.sales_order" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.salesItem') + '：'" prop="sales_item">
                <!-- 销售订单行 -->
                <el-input v-model="form.sales_item" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.salesOrg') + '：'" prop="sales_org">
                <!-- 销售组织 -->
                <el-input v-model="form.sales_org" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.salesType') + '：'" prop="sales_type">
                <!-- 销售类型 -->
                <el-input v-model="form.sales_type" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.customPn') + '：'" prop="custom_pn">
                <!-- 终端客户PN -->
                <el-input v-model="form.custom_pn" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.customPo') + '：'" prop="custom_po">
                <!-- 终端客户订单 -->
                <el-input v-model="form.custom_po" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.customCode') + '：'" prop="custom_code">
                <!-- 终端客户编码 -->
                <el-input v-model="form.custom_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.customName') + '：'" prop="custom_name">
                <!-- 终端客户名称 -->
                <el-input v-model="form.custom_name" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.splitLot') + '：'" prop="split_lot">
                <!-- 截取批次号 -->
                <el-input v-model="form.split_lot" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.splitModel') + '：'" prop="split_model">
                <!-- 截取料号 -->
                <el-input v-model="form.split_model" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.orderStatus') + '：'" prop="lot_status">
                <!-- 订单状态 -->
                <el-select v-model="form.lot_status" clearable filterable disabled>
                  <el-option
                    v-for="item in dict.TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.attribute1') + '：'" prop="attribute1">
                <!-- 预留属性1 -->
                <el-input v-model="form.attribute1" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.attribute2') + '：'" prop="attribute2">
                <!-- 预留属性2 -->
                <el-input v-model="form.attribute2" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.attribute3') + '：'" prop="attribute3">
                <!-- 预留属性3 -->
                <el-input v-model="form.attribute3" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskSource')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ dict.label.TASK_FROM[props.row.task_from] }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('view.field.plan.lotNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.lot_num
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.task_type')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  > {{
                    dict.label.TASK_TYPE[props.row.task_type] }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.plantCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.plant_code
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.partNum')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.model_type
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.version')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.model_version
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.orderStatus')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.TASK_STATUS[props.row.lot_status] }}</el-descriptions-item>
                  <!-- <el-descriptions-item :label="$t('lang_pack.vie.cusPartNum')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.from_stock_code }}</el-descriptions-item> -->
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.planQuantity')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.plan_lot_count
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.singlePackQua')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    props.row.unit_count }}</el-descriptions-item>
                  <!-- <el-descriptions-item :label="$t('lang_pack.vie.planTime')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.need_check_model_flag == 'Y' ? '是' : '否'}}</el-descriptions-item> -->
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.setType')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.QR_TYPE[props.row.array_type] }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.pcsType')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.QR_TYPE[props.row.bd_type] }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.plateLen')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_length
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.plateWid')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_width
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.plateThi')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_tickness
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.plateWei')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.m_weight
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.cycle')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.cycle_period
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.salesOrder')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.sales_order
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.salesItem')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.sales_item
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.salesOrg')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.sales_org
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.salesType')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.sales_type
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.customPn')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.custom_pn
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.customPo')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.custom_po
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.customCode')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.custom_code
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.customName')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.custom_name
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.splitLot')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.split_lot
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.splitLot')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.split_model
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.okFinishe')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.finish_ok_count
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.ngFinishe')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.finish_ng_count
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskStarTime')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_start_time
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskEndTime')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_end_time
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.taskUserTime')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.task_cost_time
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.attribute1')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.attribute1
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.attribute2')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.attribute2
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.vie.attribute3')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{ props.row.attribute3
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :label="$t('lang_pack.commonPage.validIdentificationt')"
                    label-class-name="table-descriptions-label"
                    content-class-name="table-descriptions-content"
                  >{{
                    dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.vie.taskSource')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 订单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('view.field.plan.lotNum')"
              width="120"
              align="center"
            />
            <!-- 类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              :label="$t('lang_pack.vie.task_type')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 工厂编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plant_code"
              :label="$t('lang_pack.vie.plantCode')"
              width="120"
              align="center"
            />
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="120"
              align="center"
            />
            <!-- 版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_version"
              :label="$t('lang_pack.vie.version')"
              width="130"
              align="center"
            />
            <!-- 订单状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_status"
              :label="$t('lang_pack.vie.orderStatus')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <!-- <el-button class="statusBtn" :type="scope.row.lot_status === 250 ? '' : scope.row.lot_status === 251 ? 'warning' :  scope.row.lot_status === 252 ? 'success' : scope.row.lot_status === 253 ? 'info' : ''"> -->
                <el-button
                  class="statusBtn"
                  :type="scope.row.lot_status === 'PLAN' ? '' : scope.row.lot_status === 'WORK' ? 'warning' : scope.row.lot_status === 'FINISH' ? 'success' : scope.row.lot_status === 'CANCEL' ? 'info' : ''"
                >
                  <!-- {{ getLotStatus(scope.row.lot_status)}} -->
                  {{ dict.label.TASK_STATUS[scope.row.lot_status] }}
                </el-button>
              </template>
            </el-table-column>
            <!-- 客户料号 -->
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="task_num"
                            :label="$t('lang_pack.vie.cusPartNum')" width="130" align='center'/> -->
            <!-- 计划数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_lot_count"
              :label="$t('lang_pack.vie.planQuantity')"
              width="130"
              align="center"
            />
            <!-- 单包数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="unit_count"
              :label="$t('lang_pack.vie.singlePackQua')"
              width="130"
              align="center"
            />
            <!-- OK完工 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_ok_count"
              :label="$t('lang_pack.vie.okFinishe')"
              width="130"
              align="center"
            />
            <!-- NG完工 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_ng_count"
              :label="$t('lang_pack.vie.ngFinishe')"
              width="130"
              align="center"
            />
            <!-- SET类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_type"
              :label="$t('lang_pack.vie.setType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.array_type] }}
              </template>
            </el-table-column>
            <!-- PCS类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_type"
              :label="$t('lang_pack.vie.pcsType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.bd_type] }}
              </template>
            </el-table-column>
            <!-- 计划时间 -->
            <!-- <el-table-column  :show-overflow-tooltip="true" prop="lot_status"
                            :label="$t('lang_pack.vie.planTime')" width="130" align='center'/> -->

            <!-- 板长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.vie.plateLen')"
              width="130"
              align="center"
            />
            <!-- 板宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.vie.plateWid')"
              width="130"
              align="center"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_tickness"
              :label="$t('lang_pack.vie.plateThi')"
              width="130"
              align="center"
            />
            <!-- 板重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.vie.plateWei')"
              width="130"
              align="center"
            />
            <!-- 周期 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cycle_period"
              :label="$t('lang_pack.vie.cycle')"
              width="130"
              align="center"
            />
            <!-- 销售订单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="sales_order"
              :label="$t('lang_pack.vie.salesOrder')"
              width="130"
              align="center"
            />
            <!-- 销售订单行 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="sales_item"
              :label="$t('lang_pack.vie.salesItem')"
              width="130"
              align="center"
            />
            <!-- 销售组织 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="sales_org"
              :label="$t('lang_pack.vie.salesOrg')"
              width="130"
              align="center"
            />
            <!-- 销售类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="sales_type"
              :label="$t('lang_pack.vie.salesType')"
              width="130"
              align="center"
            />
            <!-- 终端客户PN -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="custom_pn"
              :label="$t('lang_pack.vie.customPn')"
              width="130"
              align="center"
            />
            <!-- 终端客户订单 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="custom_po"
              :label="$t('lang_pack.vie.customPo')"
              width="130"
              align="center"
            />
            <!-- 终端客户编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="custom_code"
              :label="$t('lang_pack.vie.customCode')"
              width="130"
              align="center"
            />
            <!-- 终端客户名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="custom_name"
              :label="$t('lang_pack.vie.customName')"
              width="130"
              align="center"
            />
            <!-- 截取批号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_lot"
              :label="$t('lang_pack.vie.splitLot')"
              width="130"
              align="center"
            />
            <!-- 截取料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_model"
              :label="$t('lang_pack.vie.intPartNumb')"
              width="130"
              align="center"
            />
            <!-- 任务开始时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_start_time"
              :label="$t('lang_pack.vie.taskStarTime')"
              width="140"
              align="center"
            />
            <!-- 任务结束时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_end_time"
              :label="$t('lang_pack.vie.taskEndTime')"
              width="130"
              align="center"
            />
            <!-- 任务消耗时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_cost_time"
              :label="$t('lang_pack.vie.taskUserTime')"
              width="150"
              align="center"
            />
            <!-- 预留属性1 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="attribute1"
              :label="$t('lang_pack.vie.attribute1')"
              width="130"
              align="center"
            />
            <!-- 预留属性2 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="attribute2"
              :label="$t('lang_pack.vie.attribute2')"
              width="130"
              align="center"
            />
            <!-- 预留属性3 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="attribute3"
              :label="$t('lang_pack.vie.attribute3')"
              width="130"
              align="center"
            />
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <!-- <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" /> -->
                {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.Yes') : $t('lang_pack.vie.NO') }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="120"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                  <template
                    v-if="scope.row.lot_status === 'WORK'"
                    slot="right"
                  >
                    <el-button
                      v-permission="permission.del"
                      :loading="crud.status.cu === 2"
                      :disabled="false"
                      size="small"
                      type="text"
                      @click="handleCloseJobOrder(scope.row)"
                    >
                      {{ $t('view.button.close') }}
                    </el-button>  <!-- 关闭 -->
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="always" style="margin-top: 10px;" :style="{ 'height': height + 130 + 'px' }">
          <el-form
            ref="form"
            class="el-form-wrap el-form-column"
            :model="form"
            :rules="rules"
            size="small"
            label-width="145px"
            :inline="true"
          >
            <el-form-item :label="$t('view.field.plan.lotNum') + '：'">
              <!-- 订单号 -->
              <el-input v-model="form.lot_num" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.partNum') + '：'">
              <!-- 料号 -->
              <el-input v-model="form.model_type" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.version') + '：'">
              <!-- 版本 -->
              <el-input v-model="form.model_version" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.plateLen') + '：'">
              <!-- 板长 -->
              <el-input v-model="form.m_length" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.plateWid') + '：'">
              <!-- 板宽 -->
              <el-input v-model="form.m_width" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.plateThi') + '：'">
              <!-- 板厚 -->
              <el-input v-model="form.m_tickness" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.plateWei') + '：'">
              <!-- 板重 -->
              <el-input v-model="form.m_weight" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.planQuantity') + '：'">
              <!-- 计划数量 -->
              <el-input v-model="form.plan_lot_count" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.singlePackQua') + '：'">
              <!-- 单包数量 -->
              <el-input v-model="form.unit_count" disabled clearable size="small" />
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.task_type') + '：'">
              <!-- 类型 -->
              <el-select v-model="form.task_type" clearable filterable disabled>
                <el-option
                  v-for="item in dict.TASK_TYPE"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('lang_pack.vie.cycle') + '：'">
              <!-- 周期 -->
              <el-input v-model="form.cycle_period" disabled clearable size="small" />
            </el-form-item>
          </el-form>
          <!-- <el-divider /> -->
          <div style="text-align: center;">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-check"
              style="position: absolute; bottom: 10px;margin-left:-70px;width:150px;height:45px;font-size:24px;"
              @click="handleOk"
            >{{ $t('lang_pack.vie.confirmIssuance') }}</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <detail v-if="orderFlag" ref="orderFlag" @ok="orderDetail" />
    <!-- <transition name="el-zoom-in-center">
          <span v-show="messageShow" :class="'message message-' + MessageLevel"><i
            :class="MessageLevel === 'warning' ? 'el-icon-warning' : MessageLevel === 'error' ? 'el-icon-error' : 'el-icon-info'"
          />&nbsp;{{
            messageContent }}</span> -->
    <!-- </transition> -->
    <el-dialog :title="$t('lang_pack.vie.messageAlert')" :visible.sync="contentMessage.dialogVisible" width="650px" top="65px">
      <span>{{ contentMessage.content }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">{{ $t('lang_pack.vie.close') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import crudTask from '@/api/pack/task'
import { closeJobOrder } from '@/api/pack/project/tripod/jobOrder'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import detail from './detail.vue'
import { selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
  plan_id: '',
  task_from: 'AIS',
  task_type: 'Normal',
  plant_code: '',
  lot_num: '',
  model_type: '',
  model_version: '',
  plan_lot_count: '',
  unit_count: '',
  array_type: '',
  bd_type: '',
  m_length: '',
  m_width: '',
  m_tickness: '',
  m_weight: '',
  cycle_period: '',
  sales_order: '',
  sales_item: '',
  sales_org: '',
  sales_type: '',
  custom_pn: '',
  custom_po: '',
  custom_code: '',
  custom_name: '',
  split_lot: '',
  split_model: '',
  finish_ok_count: '0',
  finish_ng_count: '0',
  lot_status: 'PLAN',
  task_start_time: '',
  task_end_time: '',
  task_cost_time: '',
  recipe_paras: '',
  sort_paras: '',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'PACKTASK',
  components: { crudOperation, rrOperation, udOperation, pagination, detail },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.workOrder'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'plan_id',
      // 排序
      sort: ['plan_id asc'],
      // CRUD Method
      crudMethod: { ...crudTask },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 302,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        task_from: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        task_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        lot_num: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        model_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        plan_lot_count: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        unit_count: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        cycle_period: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      orderFlag: false,
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: 1
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
                    'ScadaEsb_' +
                    Cookies.get('userName') +
                    '_' +
                    Math.random()
                      .toString(16)
                      .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },

      // 监控点位
      controlValue: '',
      messageList: [],
      timer: null,
      messageContent: '',
      MessageLevel: 'info',
      messageShow: false,
      contentMessage: {
        content: '',
        level: 'info',
        dialogVisible: false
      },
      timerVisible: null
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'TASK_STATUS', 'TASK_TYPE', 'TASK_FROM', 'QR_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 302
    }
    this.getStationData()
  },
  created: function() {
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearTimeout(this.timerVisible)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        // cell_id: this.$route.query.cell_id
        cell_id: 1
      }
      this.getCellIp()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    getTagValue() {
      const readTagArray = [
        { tag_key: 'SortPlc/PlcBase/AppStart' }
      ]
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  if (tagKey === 'SortPlc/PlcBase/AppStart') {
                    this.controlValue = tagValue
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    BlurText(e) {
      const boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger'))
        e.target.value = ''
      }
    },
    getLotStatus(id) {
      var item = this.dict.TASK_STATUS.find(item => item.id === id)
      if (item !== undefined) {
        return item.label
      }
      return ''
    },
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudTask
            .edit({
              user_name: Cookies.get('userName'),
              plan_id: data.plan_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    handleRowClick(row) {
      this.form = row
    },
    orderDetail(row) {
      this.form.model_version = row.model_version
      this.form.model_type = row.model_type
      this.form.m_length = row.m_length
      this.form.m_width = row.m_width
      this.form.m_tickness = row.m_tickness
      this.form.m_weight = row.m_weight
      this.form.array_type = row.array_type
      this.form.bd_type = row.bd_type
      this.orderFlag = false
    },
    handleSelect() {
      this.orderFlag = true
      this.$nextTick(() => {
        this.$refs.orderFlag.dialogVisible = true
      })
    },
    handleOk() {
      const query = {
        user_name: Cookies.get('userName'),
        plan_id: this.form.plan_id
      }
      crudTask.taskDown(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success(this.$t('lang_pack.vie.SuccessIss'))
        } else {
          this.$message.error(res.msg)
        }
      }).catch(err => {
        this.$message.error(err.msg)
      })
    },
    [CRUD.HOOK.beforeToCU](crud) {
      this.form = {}
      this.form.task_from = 'AIS'
      this.form.task_type = 'Normal'
      this.form.finish_ok_count = '0'
      this.form.finish_ng_count = '0'
      this.form.lot_status = 'PLAN'
      this.form.enable_flag = 'Y'
    },
    // 刷新之后
    [CRUD.HOOK.afterRefresh](crud) {
      // 判断订单状态是不是work中，如果不是默认不赋值
      if (crud.data.length > 0 && crud.data[0].lot_status === 'WORK') {
        this.form = crud.data[0]
      }
    },
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form = this.form
    },
    // 任务取消 或者任务初始化
    taskCancelOrInit(msg, val, type) {
      if (!this.crud.selections.length) {
        this.$message({ message: this.$t('lang_pack.vie.PleaseSelect'), type: 'warning' })
        return
      }
      this.$confirm(`${this.$t('lang_pack.vie.conSel')} ${this.crud.selections.length} ${this.$t('lang_pack.vie.Piece')}`, this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          const ids = []
          this.crud.selections.forEach(e => {
            // 判断是不是任务取消按钮  并且勾选中有一项为cancel 或者 PLAN 状态都不能取消
            if ((e.plan_id && e.lot_status !== 'CANCEL') && type === 'cancel') {
              ids.push(e.plan_id)
            }
            if ((e.plan_id && e.lot_status !== 'PLAN') && type === 'init') {
              ids.push(e.plan_id)
            }
          })
          if (!ids.length) {
            this.$message({ message: `${msg}` + this.$t('lang_pack.vie.operationException'), type: 'warning' })
            return
          }
          const query = {
            ids: ids.join(','),
            user_name: Cookies.get('userName')
          }
          for (const key in val) {
            query[key] = val[key]
          }
          crudTask.edit(query).then(res => {
            if (res.code === 0) {
              this.$message({ message: `${msg}` + this.$t('lang_pack.vie.success'), type: 'success' })
              this.crud.toQuery()
            } else {
              this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
            }
          }).catch(ex => {
            this.$message({ message: this.$t('lang_pack.vie.operationException') + ex, type: 'error' })
          })
        })
        .catch(() => { })
    },
    // ----------------------------------【MQTT】----------------------------------
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://*************:8083'  + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        this.topicSubscribe('AISWEB_MSG/BzSort')
        this.topicSubscribe('SCADA_CHANGE/SortPlc/PlcBase/AppStart')
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          var TagKey = jsonData.TagKey
          var TagNewValue = jsonData.TagNewValue
          if (TagKey === 'SortPlc/PlcBase/AppStart') {
            this.controlValue = TagNewValue
          } else if (topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0) {
            this.handleMessage(jsonData)
          }
        } catch (e) { console.log(e) }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    handleTagWrite(tagKey) {
      // 鼠标按下写入值为1，抬起写入值为0
      this.controlValue = this.controlValue === '1' ? '2' : '1'
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: this.controlValue
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/SortPlc'
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        }
      })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'pack_task') return
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      }
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      this.contentMessage.content = json.msg
      this.contentMessage.level = 'info'
      if (json.msg_level === 'INFO') {
        this.contentMessage.level = 'info'
      } else if (json.msg_level === 'ERROR') {
        this.contentMessage.level = 'error'
      } else if (json.msg_level === 'WARN') {
        this.contentMessage.level = 'warning'
      }
      this.contentMessage.dialogVisible = true
      if (json.dlg_second > 0) {
        this.timerVisible = setTimeout(() => {
          clearTimeout(this.timerVisible)
          this.contentMessage.dialogVisible = false
        }, json.dlg_second)
      }
    },
    handleClose() {
      this.timerVisible && clearTimeout(this.timerVisible)
      this.contentMessage.dialogVisible = false
    },
    handleCloseJobOrder(data) {
      this.$confirm(this.$t('view.dialog.reconfirmedToClose'), this.$t('view.dialog.hint'), {
        confirmButtonText: this.$t('view.button.confirm'),
        cancelButtonText: this.$t('view.button.cancel'),
        type: 'warning'
      })
        .then(() => {
          closeJobOrder({
            'lot_num': data.lot_num
          })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('view.dialog.operationSucceed'), type: 'success' })
                // 向SortPlc/PlcPile/FinishToPlc写入值1
                this.sendMessage('SCADA_WRITE/SortPlc', JSON.stringify({ Data: [{ TagKey: 'SortPlc/PlcPile/FinishToPlc', TagValue: '1' }], ClientName: 'SCADA_WEB' }))
              } else {
                this.$message({ message: this.$t('view.dialog.operationFailed') + ': ' + res.msg, type: 'error' })
              }
              this.crud.toQuery()
            })
            .catch(ex => {
              this.$message({ message: this.$t('view.dialog.operationFailed') + ': ' + ex, type: 'error' })
            })
        })
        .catch(() => {
          this.$message({ message: this.$t('view.dialog.operationCanceled') })
        })
    }
  }
}
</script>
<style scope lang="less">.el-pagination {
    text-align: right;
    float: none !important;
}

.statusBtn {
    width: 70px !important;
}

.orderInfo {
    margin-left: 35px;
    font-size: 13px;
    display: flex;
    align-items: center;

    div {
        width: 960px;
        height: 30px;
        background-color: #FFFF00;
        border: 1px solid #cdcccc;
        margin: 0 5px;
    }
}

#loadMonitor .message {
    color: #000;
    font-weight: 600;
    padding: 5px 10px 5px 10px;
    border-radius: 10px;
}

#loadMonitor .message-info {
    background-color: #7AA1EF;
}

#loadMonitor .message-warning {
    background-color: #FBB85A;
}

#loadMonitor .message-error {
    background-color: #F56C6C;
}

#loadMonitor {
    .playBtn {
        font-size: 24px;
        border: none;
        width: 150px;
        height: 45px;
        margin-top: -10px;
    }

    .playStart {
        background-color: #0cd80c;
    }

    .playEnd {
        background-color: #ff0000;
    }
}</style>
