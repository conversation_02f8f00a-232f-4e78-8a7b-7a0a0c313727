<template>
  <div>
    <!--工具栏-->
    <el-button v-if="!readonly" class="filter-item" size="small" type="primary" icon="el-icon-plus" style="margin-bottom: 10px" :disabled="step_mod_attr_group_id == 0" plain round @click="crud.toAdd">
      新增
    </el-button>
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px" :inline="false" :disabled="readonly">
        <el-form-item label="属性代码" prop="step_mod_attr_item_code">
          <el-input v-model="form.step_mod_attr_item_code" />
        </el-form-item>
        <el-form-item label="属性描述" prop="step_mod_attr_item_des">
          <el-input v-model="form.step_mod_attr_item_des" />
        </el-form-item>
        <el-form-item label="数据类型" prop="step_mod_attr_value_type">
          <el-select v-model="form.step_mod_attr_value_type" style="width:100%">
            <el-option v-for="item in [{ label: 'TAG_ID', id: 'TAG_ID' }, { label: 'NORMAL', id: 'NORMAL' }]" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="默认参数值" prop="step_mod_attr_n_value">
          <el-input v-model="form.step_mod_attr_n_value" />
        </el-form-item>
        <el-form-item label="关键字" prop="step_mod_tag_code_list">
          <el-input v-model="form.step_mod_tag_code_list" />
        </el-form-item>
        <el-form-item label="条件成立说明" prop="ok_remarks">
          <el-input v-model="form.ok_remarks" />
        </el-form-item>
        <el-form-item label="有效标识" prop="enable_flag">
          <el-radio-group v-model="form.enable_flag">
            <el-radio label="Y">有效</el-radio>
            <el-radio label="N">失效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form v-if="readonly" ref="form1" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
        <el-form-item label="参数值" prop="step_attr_n_value">
          <el-input v-model="form.step_attr_n_value" />
        </el-form-item>
        <el-form-item label="点位" prop="link_tag_id_list">
          <el-input v-model.number="form.link_tag_id_list">
            <div slot="append">
              <el-popover v-model="customPopover" placement="left" width="650">
                <tagSelect ref="tagSelect" :client-id-list="client_id_list" :tag-id="form.link_tag_id_list" @chooseTag="handleChooseTag" />
                <el-button slot="reference">选择</el-button>
              </el-popover>
            </div>
          </el-input>
        </el-form-item>
        <el-form-item label="错误标识" prop="error_flag">
          <el-input v-model="form.error_flag" :disabled="true" />
        </el-form-item>
        <el-form-item label="错误信息" prop="error_msg">
          <el-input v-model="form.error_msg" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-divider />
      <div v-if="!readonly" style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
      </div>
      <div v-else style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="btnLoading" @click="handleSaveAttr">确认</el-button>
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          highlight-current-row
          height="250px"
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
        >
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="step_mod_attr_group_id" label="id" />
          <el-table-column :show-overflow-tooltip="true" prop="step_mod_attr_item_code" label="属性代码" width="110" />
          <el-table-column :show-overflow-tooltip="true" prop="step_mod_attr_item_des" label="属性描述" width="120" />
          <el-table-column :show-overflow-tooltip="true" prop="step_mod_attr_value_type" label="数据类型" width="100" />
          <el-table-column :show-overflow-tooltip="true" prop="step_mod_attr_n_value" label="值" width="40" />
          <el-table-column v-if="readonly" :show-overflow-tooltip="true" prop="tag_group_des" label="标签组" width="70" />
          <el-table-column v-if="readonly" :show-overflow-tooltip="true" prop="tag_code" label="标签代码" width="70" />
          <el-table-column v-if="readonly" :show-overflow-tooltip="true" prop="tag_des" label="标签描述" width="70" />
          <el-table-column label="有效标识" prop="enable_flag" width="70">
            <template slot-scope="scope">
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <udOperation v-if="!readonly" :data="scope.row" :permission="permission" :disabled-dle="false" />
              <el-button v-if="readonly" :loading="crud.status.cu === 2" size="small" type="text" @click="crud.toEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudFlowModAttrItem from '@/api/core/flow/rcsFlowModAttrItem'
import { add, edit } from '@/api/core/flow/rcsFlowMainStepAttr'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
// import crudOperation from "@crud/CRUD.operation";
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  step_mod_attr_item_id: 0,
  step_mod_attr_group_id: 0,
  step_mod_attr_item_code: '',
  step_mod_attr_item_des: '',
  step_mod_attr_value_type: '',
  step_mod_attr_n_value: '',
  step_mod_tag_code_list: '',
  ok_remarks: '',
  enable_flag: 'Y',
  step_attr_item_id: '',
  flow_main_id: 0,
  client_id_list: '',
  step_attr_n_value: '',
  link_tag_id_list: '',
  error_flag: '',
  error_msg: ''
}
export default {
  name: 'RCSFLOWMODATTRITEM',
  components: { udOperation, pagination },
  props: {
    step_mod_attr_group_id: {
      type: [String, Number],
      default: -1
    },
    client_id_list: {
      type: [String, Number],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    },
    flow_main_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '属性',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'step_mod_attr_item_id',
      // 排序
      sort: ['step_mod_attr_item_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowModAttrItem },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      btnLoading: false,
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'rcs_flow_mod_attr_item:add'],
        edit: ['admin', 'rcs_flow_mod_attr_item:edit'],
        del: ['admin', 'rcs_flow_mod_attr_item:del']
      },
      rules: {
        step_mod_attr_item_code: [{ required: true, message: '请输入属性代码', trigger: 'blur' }],
        step_mod_attr_item_des: [{ required: true, message: '请输入属性描述', trigger: 'blur' }]
      },
      customPopover: false
    }
  },
  watch: {
    step_mod_attr_group_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.step_mod_attr_group_id = this.step_mod_attr_group_id
        this.query.flow_main_id = this.flow_main_id
        this.query.client_id_list = this.client_id_list
        this.crud.toQuery()
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    handleChooseTag(tagId) {
      this.form.link_tag_id_list = tagId
      this.customPopover = false
    },
    handleSaveAttr() {
      const save = {
        user_name: Cookies.get('userName'),
        ...this.form
      }
      save.flow_main_id = this.flow_main_id
      this.btnLoading = true
      if (this.form.step_attr_item_id === 0) {
        add(save)
          .then(res => {
            this.btnLoading = false
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.crud.status.edit = CRUD.STATUS.NORMAL
              this.$message({ message: '保存成功', type: 'success' })
              this.crud.toQuery()
            } else if (defaultQuery.code === -1) {
              this.crud.status.add = 1
              this.$message({ message: defaultQuery.msg, type: 'info' })
            }
          })
          .catch(() => {
            this.btnLoading = false
            this.$message({ message: '保存异常', type: 'error' })
          })
      } else {
        edit(save)
          .then(res => {
            this.btnLoading = false
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.crud.status.edit = CRUD.STATUS.NORMAL
              this.$message({ message: '保存成功', type: 'success' })
              this.crud.toQuery()
            } else if (defaultQuery.code === -1) {
              this.$message({ message: defaultQuery.msg, type: 'info' })
            }
          })
          .catch(() => {
            this.btnLoading = false
            this.$message({
              message: '保存异常',
              type: 'error'
            })
          })
      }
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.step_mod_attr_group_id = this.step_mod_attr_group_id
      return true
    }
  }
}
</script>
