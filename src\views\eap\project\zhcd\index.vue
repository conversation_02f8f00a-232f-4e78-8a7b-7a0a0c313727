<template>
  <div id="loadMonitor" class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard"
    >
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('view.field.plan.lotNum') + ':'">
                <!-- 订单号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <!-- 料号 -->
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.taskSource') + ':'">
                <!-- 任务来源 -->
                <el-select v-model="query.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_FROM"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item :label="$t('lang_pack.vie.task_status') + ':'">
                <!-- 任务状态 -->
                <el-select v-model="query.lot_status" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card
      ref="queryCard"
      shadow="never"
      class="wrapCard"
      style="margin-top: 10px"
    >
      <div
        style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
      >
        <div class="orderInfo">
          <div style="width: 220px;">{{ $t("lang_pack.vie.scanOrdNumMESTask") }}</div>
          <el-input v-model="scanValue" clearable size="small" @keyup.enter.native="handleScan" />
          <el-button size="small" type="primary" @click="handleScan">
            <svg-icon icon-class="scan" style="margin-right: 5px" />{{
              $t("lang_pack.vie.scan")
            }}
          </el-button>
        </div>
      </div>
    </el-card>
    <el-row :gutter="20">
      <el-col :span="18">
        <el-card shadow="always" style="margin-top: 10px">
          <!--工具栏-->
          <crudOperation show="" :permission="permission" />
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item
                :label="$t('lang_pack.vie.taskSource') + '：'"
                prop="task_from"
              >
                <!-- 任务状态 -->
                <el-select v-model="form.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_FROM"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.task_type') + '：'"
                prop="task_type"
              >
                <!-- 任务类型 -->
                <el-select v-model="form.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.plantCode') + '：'"
                prop="plant_code"
              >
                <!-- 工厂编码 -->
                <el-input v-model="form.plant_code" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('view.field.plan.lotNum') + '：'"
                prop="lot_num"
              >
                <!-- 订单号 -->
                <el-input v-model="form.lot_num" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.partNum') + '：'"
                prop="model_type"
              >
                <!-- 料号 -->
                <el-input v-model="form.model_type" readonly="readonly">
                  <div slot="append">
                    <el-button slot="reference" @click="handleSelect">{{
                      $t("lang_pack.vie.select")
                    }}</el-button>
                  </div>
                </el-input>
              </el-form-item>
              <!-- <el-form-item
                  :label="$t('lang_pack.vie.version') + '：'"
                  prop="model_version"
                > -->
              <!-- 版本 -->
              <!-- <el-input
                    v-model="form.model_version"
                    disabled
                    clearable
                    size="small"
                  />
                </el-form-item> -->
              <el-form-item
                :label="$t('lang_pack.vie.innerType') + '：'"
                prop="array_type"
              >
                <!-- SET类型 -->
                <el-select
                  v-model="form.array_type"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in dict.QR_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.pcsType') + '：'"
                prop="bd_type"
              >
                <!-- PCS类型 -->
                <el-select v-model="form.bd_type" clearable filterable disabled>
                  <el-option
                    v-for="item in dict.QR_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.plateLen')"
                prop="m_length"
              >
                <!-- 长 -->
                <el-input
                  v-model="form.m_length"
                  disabled
                  clearable
                  size="small"
                />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.plateWid')"
                prop="m_width"
              >
                <!-- 宽 -->
                <el-input
                  v-model="form.m_width"
                  disabled
                  clearable
                  size="small"
                />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.plateThi')"
                prop="m_tickness"
              >
                <!-- 厚 -->
                <el-input
                  v-model="form.m_tickness"
                  disabled
                  clearable
                  size="small"
                />
              </el-form-item>
              <!-- <el-form-item
                :label="$t('lang_pack.vie.plateWei')"
                prop="m_weight"
              >
                重
                <el-input
                  v-model="form.m_weight"
                  disabled
                  clearable
                  size="small"
                />
              </el-form-item> -->
              <el-form-item
                :label="$t('lang_pack.vie.planQuantity') + '：'"
                prop="plan_lot_count"
              >
                <!-- 计划数量 -->
                <el-input
                  v-model.number="form.plan_lot_count"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.singlePackQua') + '：'"
                prop="unit_count"
              >
                <!-- 单包数量 -->
                <el-input
                  v-model.number="form.unit_count"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.cycle') + '：'"
                prop="cycle_period"
              >
                <!-- 周期 -->
                <el-input
                  v-model.number="form.cycle_period"
                  type="number"
                  clearable
                  size="small"
                  @blur="BlurText($event)"
                />
              </el-form-item>
              <el-form-item
                :label="$t('view.field.jobOrder.originalLotNo') + '：'"
                prop="original_lot"
              >
                <el-input v-model="form.original_lot" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('view.field.jobOrder.certifyLotNo') + '：'"
                prop="certify_lot"
              >
                <el-input
                  v-model="form.certify_lot"
                  clearable
                  size="small"
                />
              </el-form-item>
              <!-- 排版数 -->
              <el-form-item
                :label="$t('view.field.jobOrder.typesettingNumber') + '：'"
                prop="typesetting_no"
              >
                <el-input
                  v-model="form.typesetting_no"
                  clearable
                  size="small"
                />
              </el-form-item>
              <!-- 客户料号 -->
              <el-form-item
                :label="$t('view.field.jobOrder.customerMaterialNo') + '：'"
                prop="customer_mn"
              >
                <el-input v-model="form.customer_mn" clearable size="small" />
              </el-form-item>
              <!-- UL -->
              <el-form-item
                :label="$t('view.field.jobOrder.ul') + '：'"
                prop="ul_code"
              >
                <el-input v-model="form.ul_code" clearable size="small" />
              </el-form-item>
              <!-- 出货地 -->
              <el-form-item
                :label="$t('view.field.jobOrder.shipAddress') + '：'"
                prop="ship_address"
              >
                <el-input v-model="form.ship_address" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.salesOrder') + '：'"
                prop="sales_order"
              >
                <!-- 销售订单号 -->
                <el-input v-model="form.sales_order" clearable size="small" />
              </el-form-item>
              <!-- 销售订单行 -->
              <el-form-item
                :label="$t('lang_pack.vie.salesItem') + '：'"
                prop="sales_item"
              >
                <el-input v-model="form.sales_item" clearable size="small" />
              </el-form-item>
              <!-- 销售组织 -->
              <el-form-item
                :label="$t('lang_pack.vie.salesOrg') + '：'"
                prop="sales_org"
              >
                <el-input v-model="form.sales_org" clearable size="small" />
              </el-form-item>
              <!-- 销售类型 -->
              <el-form-item
                :label="$t('lang_pack.vie.salesType') + '：'"
                prop="sales_type"
              >
                <el-input v-model="form.sales_type" clearable size="small" />
              </el-form-item>
              <!-- 终端客户PN -->
              <el-form-item
                :label="$t('lang_pack.vie.customPn') + '：'"
                prop="custom_pn"
              >
                <el-input v-model="form.custom_pn" clearable size="small" />
              </el-form-item>
              <!-- 终端客户订单 -->
              <el-form-item
                :label="$t('lang_pack.vie.customPo') + '：'"
                prop="custom_po"
              >
                <el-input v-model="form.custom_po" clearable size="small" />
              </el-form-item>
              <!-- 终端客户编码 -->
              <el-form-item
                :label="$t('lang_pack.vie.customCode') + '：'"
                prop="custom_code"
              >
                <el-input v-model="form.custom_code" clearable size="small" />
              </el-form-item>
              <!-- 终端客户名称 -->
              <el-form-item
                :label="$t('lang_pack.vie.customName') + '：'"
                prop="custom_name"
              >
                <el-input v-model="form.custom_name" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.splitLot') + '：'"
                prop="split_lot"
              >
                <!-- 截取批次号 -->
                <el-input v-model="form.split_lot" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.splitModel') + '：'"
                prop="split_model"
              >
                <!-- 截取料号 -->
                <el-input v-model="form.split_model" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.orderStatus') + '：'"
                prop="lot_status"
              >
                <!-- 订单状态 -->
                <el-select
                  v-model="form.lot_status"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in dict.TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                    >{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.commonPage.validIdentification')"
              >
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.attribute1') + '：'"
                prop="attribute1"
              >
                <!-- 预留属性1 -->
                <el-input v-model="form.attribute1" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.attribute2') + '：'"
                prop="attribute2"
              >
                <!-- 预留属性2 -->
                <el-input v-model="form.attribute2" clearable size="small" />
              </el-form-item>
              <el-form-item
                :label="$t('lang_pack.vie.attribute3') + '：'"
                prop="attribute3"
              >
                <!-- 预留属性3 -->
                <el-input v-model="form.attribute3" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button
                size="small"
                icon="el-icon-close"
                plain
                @click="crud.cancelCU"
              >{{ $t("lang_pack.commonPage.cancel") }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
            @row-click="handleRowClick"
          >
            <el-table-column type="selection" width="55" />

            <!-- 来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.vie.taskSource')"
              width=""
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
              label="排序"
              type="index"
              prop="lot_index"
              width="60"
            />
            <!-- 订单号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('view.field.plan.lotNum')"
              width="200"
              align="center"
            />
            <!-- 型号 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.maintenanceType.type')"
              width="120"
              align="center"
            /> -->
            <!-- 计划数量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="plan_lot_count"
              :label="$t('lang_pack.sortingArea.PlannedQuantity')"
              width=""
              align="center"
            />
            <!-- OK完工 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_ok_count"
              :label="$t('lang_pack.vie.okFinishe')"
              width=""
              align="center"
            />
            <!-- 订单状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_status"
              :label="$t('lang_pack.vie.orderStatus')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-button
                  class="statusBtn"
                  :type="scope.row.lot_status === 'PLAN' ? 'primary' : scope.row.lot_status === 'WORK' ? 'warning'
                    : scope.row.lot_status === 'FINISH'
                      ? 'success'
                      : scope.row.lot_status === 'CANCEL'
                        ? 'info'
                        : ''
                  "
                >
                  <!-- {{ getLotStatus(scope.row.lot_status)}} -->
                  {{ dict.label.TASK_STATUS[scope.row.lot_status] }}
                </el-button>
              </template>
            </el-table-column>
            <!-- 总层数 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_level"
              :label="$t('lang_pack.vie.total')"
              width=""
              align="center"
            />
            <!-- 已扫层数 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="finish_scan_count"
              label="已扫层数"
              width=""
              align="center"
            />
            <!-- 半成品型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="temp_product_no"
              label="半成品型号"
              width="200"
              align="center"
            />
            <!-- Top层 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="temp_top_level"
              label="Top层"
              width=""
              align="center"
            />
            <!-- Bot层 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="temp_bot_level"
              label="Bot层"
              width=""
              align="center"
            />
            <!-- 板长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.vie.plateLen')"
              width=""
              align="center"
            />
            <!-- 板宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.vie.plateWid')"
              width=""
              align="center"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_tickness"
              :label="$t('lang_pack.vie.plateThi')"
              width=""
              align="center"
            />
            <!-- 板重 -->
            <!-- <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.vie.plateWei')"
              width="130"
              align="center"
            /> -->
            <!-- 左下发 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="left_on_flag"
              label="左下发"
              width=""
              align="center"
            />
            <!-- 右下发 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="right_on_flag"
              label="右下发"
              width=""
              align="center"
            />
            <!-- 左完成 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="left_finish_ok_count"
              label="左完成"
              width=""
              align="center"
            />
            <!-- 右完成 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="right_finish_ok_count"
              label="右完成"
              width=""
              align="center"
            />
            <!-- 任务开始时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_start_time"
              :label="$t('lang_pack.vie.taskStarTime')"
              width="150"
              align="center"
            />
            <!-- 任务结束时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_end_time"
              :label="$t('lang_pack.vie.taskEndTime')"
              width="150"
              align="center"
            />
            <!-- 任务消耗时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_cost_time"
              :label="$t('lang_pack.vie.taskUserTime')"
              width="150"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="user_id"
              label="操作者"
              width="130"
              align="center"
            />

            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <!-- <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" /> -->
                {{
                  scope.row.enable_flag === "Y"
                    ? $t("lang_pack.vie.Yes")
                    : $t("lang_pack.vie.NO")
                }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="200"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :delete-edit="false"
                >
                  <template slot="right">
                    <el-button
                      :loading="crud.status.cu === 2"
                      :disabled="false"
                      size="small"
                      type="text"
                      @click="handleCancel(scope.row)"
                    >
                      取消任务
                    </el-button>
                    <el-button
                      :loading="crud.status.cu === 2"
                      :disabled="false"
                      size="small"
                      type="text"
                      @click="showDraw('layerInfo','cbxx',scope.row)"
                    >
                      层别信息
                    </el-button>
                    <el-button
                      :loading="crud.status.cu === 2"
                      :disabled="false"
                      size="small"
                      type="text"
                      @click="showDraw('boardRecord','pbjl',scope.row)"
                    >
                      配板记录
                    </el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页组件
            <pagination /> -->
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          shadow="always"
          style="margin-top: 10px"
          :style="{ height: height + 90 + 'px' }"
        >
          <el-form
            ref="form"
            class="el-form-column workOrder"
            :model="form"
            :rules="rules"
            size="small"
            label-width="145px"
            :inline="true"
            :style="{
              'max-height': height + 'px',
              'overflow-x': 'auto',
              'overflow-x': 'hidden',
            }"
          >
            <!-- 订单号 -->
            <el-form-item :label="$t('view.field.plan.lotNum') + '：'">
              <el-input
                v-model="form.lot_num"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 型号 -->
            <el-form-item :label="$t('lang_pack.maintenanceType.type') + ':'">
              <el-input
                v-model="form.model_type"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 板长 -->
            <el-form-item :label="$t('lang_pack.vie.plateLen') + '：'">
              <el-input
                v-model="form.m_length"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 板宽 -->
            <el-form-item :label="$t('lang_pack.vie.plateWid') + '：'">
              <el-input
                v-model="form.m_width"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 板厚 -->
            <el-form-item :label="$t('lang_pack.vie.plateThi') + '：'">
              <el-input
                v-model="form.m_tickness"
                disabled
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 板重 -->
            <!-- <el-form-item :label="$t('lang_pack.vie.plateWei') + '：'">
              <el-input
                v-model="form.m_weight"
                disabled
                clearable
                size="small"
              />
            </el-form-item> -->
            <!-- 数量 -->
            <el-form-item label="数量：">
              <el-input
                v-model="form.plan_lot_count"
                clearable
                size="small"
              />
            </el-form-item>
            <!-- 总层数 -->
            <el-form-item label="总层数：">
              <el-input
                v-model="form.lot_level"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item label="左配机使用：">
              <el-checkbox-group v-model="form.left_on_flag">
                <el-checkbox />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="右配机使用：">
              <el-checkbox-group v-model="form.right_on_flag" :disabled="form.left_on_flag > 6">
                <el-checkbox />
              </el-checkbox-group>
            </el-form-item>
          </el-form>
          <!-- <el-divider /> -->
          <div style="text-align: center">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-check"
              style="
                  position: absolute;
                  bottom: 10px;
                  margin-left: -70px;
                  width: 150px;
                  height: 45px;
                  font-size: 24px;
                "
              @click="handleOk"
            >确定下发</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <detail v-if="orderFlag" ref="orderFlag" @ok="orderDetail" />
    <roleCheck
      v-if="roleCheckShow"
      ref="roleCheck"
      :params-code="'Pack_TaskCancelOrInit_Password'"
      @roleCheck="roleCheck"
    />
    <selectDraw ref="layerInfo" :footer="false" :dict="dict" />
    <selectDraw ref="boardRecord" :footer="false" :dict="dict" />
  </div>
</template>

<script>
import api from '@/api/eap/project/zhcd/eapZhcd'
import Cookies from 'js-cookie'
import selectDraw from '@/components/selectDraw'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import detail from '@/views/pack/project/tripod/task/detail.vue'
import roleCheck from '@/views/core/hmi/roleCheck'
const defaultForm = {
  plan_id: '',
  task_from: 'AIS',
  task_type: 'Normal',
  lot_num: '',
  lot_index: '',
  model_type: '',
  pallet_num: '',
  plan_lot_count: '',
  lot_level: '',
  m_length: '',
  m_width: '',
  m_tickness: '',
  // m_weight: '',
  finish_ok_count: '0',
  lot_status: 'UNFINISH',
  task_start_time: '',
  task_end_time: '',
  task_cost_time: '',
  left_on_flag: true,
  right_on_flag: false,
  left_finish_ok_count: '0',
  right_finish_ok_count: '0',
  user_id: Cookies.get('userName'),
  enable_flag: 'Y',
  temp_product_no: '',
  temp_top_level: '',
  temp_bot_level: '',
  finish_scan_count: '',
  userName: Cookies.get('userName')
}
export default {
  name: 'PACKTASK',
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    detail,
    roleCheck,
    selectDraw
  },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.workOrder'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'plan_id',
      // 排序
      sort: ['plan_id asc'],
      // CRUD Method
      crudMethod: { ...api },
      query: { size: 1, lot_status: 'UNFINISH' },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      roleCheckShow: false,
      height: document.documentElement.clientHeight - 410,
      permission: {
        add: ['admin', 'plan:add'],
        edit: ['admin', 'plan:edit'],
        del: ['admin', 'plan:del']
      },
      rules: {
        task_from: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        task_type: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        lot_num: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        model_type: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        // plan_lot_count: [
        //   {
        //     required: true,
        //     message: this.$t('lang_pack.commonPage.required'),
        //     trigger: 'blur'
        //   }
        // ],
        unit_count: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        cycle_period: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        batch_no: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        laser_batch_no: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ],
        ship_address: [
          {
            required: true,
            message: this.$t('lang_pack.commonPage.required'),
            trigger: 'blur'
          }
        ]
      },
      orderFlag: false,
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: 1
      },
      // 监控点位
      controlValue: '',
      messageList: [],
      timer: null,
      messageContent: '',
      MessageLevel: 'info',
      messageShow: false,
      rockObj: {
        msg: '',
        val: '',
        type: ''
      },
      scanValue: ''
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'TASK_STATUS', 'TASK_TYPE', 'TASK_FROM', 'QR_TYPE'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 410
    }
    this.getStationData()
  },
  created: function() {
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
  },
  methods: {
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        // cell_id: this.$route.query.cell_id
        cell_id: 1
      }
    },
    BlurText(e) {
      const boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(
          this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger')
        )
        e.target.value = ''
      }
    },
    getLotStatus(id) {
      var item = this.dict.TASK_STATUS.find((item) => item.id === id)
      if (item !== undefined) {
        return item.label
      }
      return ''
    },
    changeEnabled(data, val) {
      this.$confirm(
        this.$t('lang_pack.vie.changeTo') +
            '【' +
            (data.enable_flag === 'Y'
              ? this.$t('lang_pack.vie.effective')
              : this.$t('lang_pack.vie.invalid')) +
            '】' +
            this.$t('lang_pack.vie.what'),
        this.$t('lang_pack.vie.prompt'),
        {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          api
            .edit({
              user_name: Cookies.get('userName'),
              plan_id: data.plan_id,
              enable_flag: val
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: this.$t('lang_pack.vie.editSuccess'),
                  type: 'success'
                })
              } else {
                this.$message({
                  message:
                      this.$t('lang_pack.commonPage.operationfailure') + res.msg,
                  type: 'error'
                })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch((ex) => {
              this.$message({
                message: this.$t('lang_pack.commonPage.operationfailure') + ex,
                type: 'error'
              })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    handleRowClick(row) {
      this.form = row
      // 确认下发逻辑：
      // 1.只有PLAN状态下的任务才允许下发，其他的不允许；
      // 2.当lot_level>6时，默认勾选上左配板，右配板不允许勾选，禁用；
      // 3.当lot_level<=6时，默认勾选上左配板和右配板。
      this.form.left_on_flag = true
      if (Number(this.form.lot_level) > 6) {
        this.form.right_on_flag = false
      }
      if (Number(this.form.lot_level) <= 6) {
        this.form.right_on_flag = true
      }
    },
    orderDetail(row) {
      this.form.model_version = row.model_version
      this.form.model_type = row.model_type
      this.form.m_length = row.m_length
      this.form.m_width = row.m_width
      this.form.m_tickness = row.m_tickness
      this.form.m_weight = row.m_weight
      this.form.array_type = row.array_type
      this.form.bd_type = row.bd_type
      this.orderFlag = false
    },
    handleSelect() {
      this.orderFlag = true
      this.$nextTick(() => {
        this.$refs.orderFlag.dialogVisible = true
      })
    },
    handleOk() {
      if (!this.form.plan_id) {
        this.$message({
          type: 'warning',
          message: '请先选中一行数据'
        })
        return
      }
      if (this.form.lot_status !== 'PLAN') {
        this.$message({
          type: 'warning',
          message: 'PLAN状态才可下发'
        })
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        plan_id: this.form.plan_id,
        left_on_flag: this.form.left_on_flag,
        right_on_flag: this.form.right_on_flag
      }
      api.EapPbjApsPlanDownTask(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message.success(this.$t('lang_pack.vie.SuccessIss'))
            this.crud.toQuery()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.msg)
        })
    },
    // 刷新之后
    [CRUD.HOOK.afterRefresh](crud) {
    },
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form = this.form
    },
    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      this.roleCheckShow = false
      status === 'OK' && this.taskCancelOrInit()
    },
    // 任务取消 或者任务初始化
    taskCancelOrInit() {
      if (!this.crud.selections.length) {
        this.$message({
          message: this.$t('lang_pack.vie.PleaseSelect'),
          type: 'warning'
        })
        return
      }
      this.$confirm(
        `${this.$t('lang_pack.vie.conSel')} ${
          this.crud.selections.length
        } ${this.$t('lang_pack.vie.Piece')}`,
        this.$t('lang_pack.vie.prompt'),
        {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          cancelButtonText: this.$t('lang_pack.vie.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          const ids = []
          this.crud.selections.forEach((e) => {
            // 判断是不是任务取消按钮  并且勾选中有一项为cancel 或者 PLAN 状态都不能取消
            if (
              e.plan_id &&
                e.lot_status !== 'CANCEL' &&
                this.rockObj.type === 'cancel'
            ) {
              ids.push(e.plan_id)
            }
            if (
              e.plan_id &&
                e.lot_status !== 'PLAN' &&
                this.rockObj.type === 'init'
            ) {
              ids.push(e.plan_id)
            }
          })
          if (!ids.length) {
            this.$message({
              message:
                  `${this.rockObj.msg}` +
                  this.$t('lang_pack.vie.operationException'),
              type: 'warning'
            })
            return
          }
          const query = {
            ids: ids.join(','),
            user_name: Cookies.get('userName')
          }
          for (const key in this.rockObj.val) {
            query[key] = this.rockObj.val[key]
          }
          api
            .edit(query)
            .then((res) => {
              if (res.code === 0) {
                this.$message({
                  message:
                      `${this.rockObj.msg}` + this.$t('lang_pack.vie.success'),
                  type: 'success'
                })
                this.crud.toQuery()
              } else {
                this.$message({
                  message:
                      this.$t('lang_pack.vie.operationException') + res.msg,
                  type: 'error'
                })
              }
            })
            .catch((ex) => {
              this.$message({
                message: this.$t('lang_pack.vie.operationException') + ex,
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    handleScan() {
      if (!this.scanValue) {
        this.$message({
          type: 'warning',
          message: '请先扫描订单号'
        })
        return
      }
      const query = {
        station_code: this.$route.query.station_code,
        lot_num: this.scanValue
      }
      api.EapPbjApsPlanAddByScan(query).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '扫描成功'
          })
          console.log(this)
          this.crud.refresh()
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      }).catch((ex) => {
        this.$message({
          type: 'error',
          message: ex.msg
        })
      })
    },
    handleCancel(data) {
      const query = {
        plan_id: data.plan_id,
        lot_status: 'CANCEL'
      }
      this.$confirm('确定取消当前任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.EapPbjApsPlanUpdLotStatus(query).then(res => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.crud.toQuery()
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            })
          }
        }).catch((ex) => {
          this.$message({
            type: 'error',
            message: ex.msg
          })
        })
      })
    },
    showDraw(attr, type, data) {
      this.$refs[attr].open({
        type,
        checkType: '',
        search: {
          // 页码
          page: 1,
          // 每页数据条数
          size: 9999,
          plan_id: data.plan_id,
          user_name: Cookies.get('userName')
        }
      })
    }
  }
}
</script>
  <style scope lang="less">
  .el-pagination {
    text-align: right;
    float: none !important;
  }

  .statusBtn {
    width: 70px !important;
  }
  .orderInfo {
    margin-left: 30px;
    font-size: 14px;
    display: flex;
    align-items: center;

    .el-input__inner {
      width: 960px;
      height: 30px;
      background-color: #ffff00;
      border: 1px solid #cdcccc;
      margin: 0 5px;
    }
  }

  #loadMonitor .message {
    color: #000;
    font-weight: 600;
    padding: 5px 10px 5px 10px;
    border-radius: 10px;
  }

  #loadMonitor .message-info {
    background-color: #7aa1ef;
  }

  #loadMonitor .message-warning {
    background-color: #fbb85a;
  }

  #loadMonitor .message-error {
    background-color: #f56c6c;
  }

  #loadMonitor {
    .playBtn {
      font-size: 24px;
      border: none;
      width: 150px;
      height: 45px;
      margin-top: -10px;
    }

    .playStart {
      background-color: #0cd80c;
    }

    .playEnd {
      background-color: #ff0000;
    }
  }
  </style>
