<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="145px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号:">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.fjrm.lotNo')" prop="lot_num">
                <!-- 批次号 -->
                <el-input v-model="form.lot_num" clearable size="small" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.si')" prop="si">
                <el-input v-model="form.si" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.fe')" prop="fe">
                <el-input v-model="form.fe" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.cu')" prop="cu">
                <el-input v-model="form.cu" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.mn')" prop="mn">
                <el-input v-model="form.mn" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.mg')" prop="mg">
                <el-input v-model="form.mg" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.ni')" prop="ni">
                <el-input v-model="form.ni" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.zn')" prop="zn">
                <el-input v-model="form.zn" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.ti')" prop="ti">
                <el-input v-model="form.ti" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.cr')" prop="cr">
                <el-input v-model="form.cr" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.na')" prop="na">
                <el-input v-model="form.na" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.ca')" prop="ca">
                <el-input v-model="form.ca" clearable size="small" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.enableFlag')" prop="enable_flag">
                <!-- 有效标识 -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" prop="chemistry_id" fixed />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->

            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('lang_pack.fjrm.lotNo')"
              width="140"
              align="center"
            />

            <el-table-column
              :show-overflow-tooltip="true"
              prop="si"
              :label="$t('lang_pack.fjrm.si')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="fe"
              :label="$t('lang_pack.fjrm.fe')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cu"
              :label="$t('lang_pack.fjrm.cu')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mn"
              :label="$t('lang_pack.fjrm.mn')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mg"
              :label="$t('lang_pack.fjrm.mg')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ni"
              :label="$t('lang_pack.fjrm.ni')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="zn"
              :label="$t('lang_pack.fjrm.zn')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ti"
              :label="$t('lang_pack.fjrm.ti')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cr"
              :label="$t('lang_pack.fjrm.cr')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="na"
              :label="$t('lang_pack.fjrm.na')"
              width="140"
              align="center"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ca"
              :label="$t('lang_pack.fjrm.ca')"
              width="140"
              align="center"
            />

            <el-table-column
              :label="$t('lang_pack.fjrm.enableFlag')"
              align="center"
              width="120"
              prop="enable_flag"
            >
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column
              prop="button"
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudChemistry from '@/api/dcs/project/fjrm/chemistry/chemistry'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  chemistry_id: '',
  lot_num: '',
  si: '',
  fe: '',
  cu: '',
  mn: '',
  mg: '',
  ni: '',
  zn: '',
  ti: '',
  cr: '',
  na: '',
  ca: '',
  enable_flag: 'Y'
}
export default {
  name: 'CHEMISTRY',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '化学成分基础表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'chemistry_id',
      // 排序
      sort: ['chemistry_id asc'],
      // CRUD Method
      crudMethod: { ...crudChemistry },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'dcs_wms_chemistry:add'],
        edit: ['admin', 'dcs_wms_chemistry:edit'],
        del: ['admin', 'dcs_wms_chemistry:del']
      },
      rules: {
        lot_num: [{ required: true, message: '请选择批次号', trigger: 'blur' }],
        enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
      }
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
    changeEnabled(data, val) {
      this.$confirm('确定要将有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudChemistry
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              chemistry_id: data.chemistry_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    }
  }
}
</script>
