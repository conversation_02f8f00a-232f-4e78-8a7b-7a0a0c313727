<template>
  <div class="app-container">
    <el-card shadow="never" style="margin-top: 10px">
      <el-row :gutter="20">
        <el-col :span="attrElCol">
          <!--表格渲染-->
          <el-table
            :key="tableKey"
            ref="attrTable"
            v-loading="attrTableLoading"
            :cell-style="tableRowClassName"
            size="small"
            :data="attrData"
            style="width: 100%"
            height="560px"
          >
            <el-table-column :show-overflow-tooltip="true" prop="flow_mod_sub_des" label="子流程" />
            <el-table-column :show-overflow-tooltip="true" prop="step_mod_des" label="步骤" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="step_mod_attr_group_code" label="属性组编码" />
            <el-table-column :show-overflow-tooltip="true" prop="step_mod_attr_group_des" label="属性组描述" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="step_mod_attr_item_code" label="属性编码" />
            <el-table-column :show-overflow-tooltip="true" prop="step_mod_attr_item_des" label="属性描述" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="step_mod_attr_value_type" label="属性类型" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="step_mod_attr_n_value" label="默认值" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="step_attr_n_value" width="200" label="设定值">
              <template slot-scope="scope">
                <el-input v-model="scope.row.step_attr_n_value" style="width:100%">
                  <div slot="append">
                    <el-button slot="reference" @click="handleDefaultValue(scope.row)">默认</el-button>
                  </div>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="link_tag_id_list" width="220" label="标签ID">
              <template slot-scope="scope">
                <el-input v-if="scope.row.step_mod_attr_value_type === 'TAG_ID'" v-model="scope.row.link_tag_id_list" style="width:200px">
                  <div slot="append">
                    <el-button slot="reference" @click="chooseTag(scope.row)">选择&nbsp;<i v-if="scope.row.step_mod_attr_item_id === currentRow.step_mod_attr_item_id" class="el-icon-d-arrow-right" /></el-button>
                  </div>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="tag_group_des" label="标签组" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="tag_code" label="标签代码" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="tag_des" label="标签描述" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="error_flag" label="错误代码" />
            <el-table-column v-if="attrElCol === 24" :show-overflow-tooltip="true" prop="error_msg" label="错误描述" />
          </el-table>
        </el-col>
        <el-col :span="tagElCol">
          <tagSelect ref="tagSelect" :client_id_list="client_id_list" :tag_code="currentRow.step_mod_tag_code_list" @chooseTag="handleChooseTag" @closeChooseTag="handleCloseChooseTag" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { selAll, saveAttr, autoMatchAttr } from '@/api/core/flow/rcsFlowMainStepAttr'
import tagSelect from '@/views/core/flow/main/tagSel'
import Cookies from 'js-cookie'
export default {
  name: 'RCS_FLOW_MAIN_STEP_ATTR',
  components: { tagSelect },
  props: {
    flow_main_id: {
      type: [String, Number],
      default: -1
    },
    flow_mod_main_id: {
      type: [String, Number],
      default: -1
    },
    client_id_list: {
      type: [String, Array],
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      attrElCol: 24,
      tagElCol: 0,
      attrTableLoading: false,
      attrData: [],
      tableKey: 0,
      currentRow: {}
    }
  },
  mounted: function() {},
  created: function() {
    this.toQuery()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (row.step_mod_attr_item_id === this.currentRow.step_mod_attr_item_id) {
        return { background: '#D6E3FE', color: '#000', fontWeight: 700 }
      } else {
        return { }
      }
    },
    toQuery() {
      const query = {
        user_name: Cookies.get('userName'),
        flow_main_id: this.flow_main_id,
        flow_mod_main_id: this.flow_mod_main_id
      }
      selAll(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.attrData = defaultQuery.data
            } else {
              this.attrData = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleDefaultValue(row) {
      row.step_attr_n_value = row.step_mod_attr_n_value
      this.tableKey++
    },
    chooseTag(row) {
      this.currentRow = row
      if (this.attrElCol !== 12) {
        this.tableKey++
      }
      this.attrElCol = 12
      this.tagElCol = 12
    },
    handleChooseTag(data) {
      const row = this.attrData.filter(item => item.step_mod_attr_item_id === this.currentRow.step_mod_attr_item_id)[0]
      row.link_tag_id_list = data.tag_id
      row.tag_group_des = data.tag_group_des
      row.tag_code = data.tag_code
      row.tag_des = data.tag_des
      // this.tableKey++
    },
    handleCloseChooseTag() {
      this.currentRow = {}
      this.attrElCol = 24
      this.tagElCol = 0
      this.tableKey++
    },
    saveAttrData() {
      var arr = []
      this.attrData.forEach(item => {
        arr.push({
          flow_main_id: this.flow_main_id,
          step_mod_attr_item_id: item.step_mod_attr_item_id,
          step_attr_n_value: item.step_attr_n_value,
          link_tag_id_list: item.link_tag_id_list,
          step_attr_item_id: item.step_attr_item_id
        })
      })
      if (arr.length === 0) {
        this.$message({ message: '没有可保存的数据', type: 'info' })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '数据处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const save = {
        user_name: Cookies.get('userName'),
        data: arr
      }
      saveAttr(save)
        .then(res => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '保存成功', type: 'success' })
            this.toQuery()
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          loading.close()
          this.$message({ message: '保存异常', type: 'error' })
        })
    },
    autoMatchAttr() {
      const loading = this.$loading({
        lock: true,
        text: '数据处理中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const save = {
        user_name: Cookies.get('userName'),
        flow_main_id: this.flow_main_id,
        flow_mod_main_id: this.flow_mod_main_id,
        client_id_list: this.client_id_list
      }
      autoMatchAttr(save)
        .then(res => {
          loading.close()
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '操作成功', type: 'success' })
            this.toQuery()
          } else if (defaultQuery.code === -1) {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          loading.close()
          this.$message({ message: '操作异常', type: 'error' })
        })
    }
  }
}
</script>
