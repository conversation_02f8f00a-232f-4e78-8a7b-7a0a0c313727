<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="余料编号:">
                                <!-- 余料编号 -->
                                <el-input v-model="query.plus_task_num" clearable size="small" />
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="余料材质:">
                                <!-- 余料材质 -->
                                <el-input v-model="query.plus_remnant_mat" clearable size="small" />
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />
            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.mainTaskID')" prop="mo_id">
                                <!-- 主任务ID -->
                                <el-input v-model="form.mo_id" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.plusTaskNumber')" prop="plus_task_num">
                                <!-- 余料编号 -->
                                <el-input v-model="form.plus_task_num" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.plusRemnantMat')" prop="plus_serial_num">
                                <!-- 余料材质 -->
                                <el-input v-model="form.plus_remnant_mat" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.plusRemnantThick')" prop="plus_lot_num">
                                <!-- 余料厚度 -->
                                <el-input v-model="form.plus_remnant_thick" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.plusRemnantLength')" prop="plus_lot_num">
                                <!-- 余料长度 -->
                                <el-input v-model="form.plus_remnant_length" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.plusRemnantWidth')" prop="plus_lot_num">
                                <!-- 余料宽度 -->
                                <el-input v-model="form.plus_remnant_width" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.surplusMaterialTable.plusRemnantWeight')" prop="plus_lot_num">
                                <!-- 余料重量 -->
                                <el-input v-model="form.plus_remnant_weight" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" fixed/>
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="5" size="small" border>
                                <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_plus_id }}</el-descriptions-item>
                                <el-descriptions-item label="主任务ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_id }}</el-descriptions-item>
                                <el-descriptions-item label="余料编号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plus_task_num }}</el-descriptions-item>
                                <el-descriptions-item label="余料材质" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plus_remnant_mat }}</el-descriptions-item>
                                <el-descriptions-item label="余料厚度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plus_remnant_thick }}</el-descriptions-item>
                                <el-descriptions-item label="余料长度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plus_remnant_length }}</el-descriptions-item>
                                <el-descriptions-item label="余料宽度" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plus_remnant_width }}</el-descriptions-item>
                                <el-descriptions-item label="余料重量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.plus_remnant_weight }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <!-- 主任务ID -->
                        <el-table-column  :show-overflow-tooltip="true" prop="mo_id"
                            :label="$t('lang_pack.surplusMaterialTable.mainTaskID')" width="220" align='center'/>
                        <!-- 余料任务号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="plus_task_num"
                            :label="$t('lang_pack.surplusMaterialTable.plusTaskNumber')" width="220" align='center'/>
                        <!-- 余料序列号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="plus_remnant_mat"
                            :label="$t('lang_pack.surplusMaterialTable.plusRemnantMat')" width="220" align='center'/>
                        <!-- 余料批次号 -->
                        <el-table-column :show-overflow-tooltip="true" prop="plus_remnant_thick"
                            :label="$t('lang_pack.surplusMaterialTable.plusRemnantThick')" width="220" align='center' />
                         <!-- 余料批次号 -->
                         <el-table-column :show-overflow-tooltip="true" prop="plus_remnant_length"
                            :label="$t('lang_pack.surplusMaterialTable.plusRemnantLength')" width="220" align='center' />
                          <!-- 余料批次号 -->
                          <el-table-column :show-overflow-tooltip="true" prop="plus_remnant_width"
                            :label="$t('lang_pack.surplusMaterialTable.plusRemnantWidth')" width="220" align='center' />
                          <!-- 余料批次号 -->
                          <el-table-column :show-overflow-tooltip="true" prop="plus_remnant_weight"
                            :label="$t('lang_pack.surplusMaterialTable.plusRemnantWeight')" width="220" align='center' />
                         <!-- Table单条操作-->
                        <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center"  fixed="right">
                            <!-- 操作 -->
                            <template slot-scope="scope">
                                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudTaskPlus from '@/api/dcs/core/aps/taskPlus'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    item_date:'',
    item_date_val:'',
    mo_plus_id:'',
    mo_id:'',
    plus_task_num:'',
    plus_serial_num:'',
    plus_lot_num:'',

}
export default {
    name: 'WEB_TASK_PLUS',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '生产任务余料',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'mo_plus_id',
            // 排序
            sort: ['mo_plus_id asc'],
            // CRUD Method
            crudMethod: { ...crudTaskPlus },
            // 按钮显示
            optShow: {
                add: true,
                edit: true,
                del: true,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                plus_task_num: [{ required: true, message: '请选择余料编号', trigger: 'blur' }],
                plus_remnant_mat: [{ required: true, message: '请选择余料材质', trigger: 'blur' }],
                plus_remnant_thick: [{ required: true, message: '请选择余料批次号', trigger: 'blur' }],
            },
        }
    },
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
    }
}
</script>
  