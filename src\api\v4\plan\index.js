import request from '@/utils/request'

const context = 'api/v4/plans'

export function _get(params) {
  return request({
    url: context,
    method: 'get',
    params
  })
}

export function _patch(data, params) {
  return request({
    url: context + '/' + data.id,
    method: 'patch',
    data,
    params
  })
}

export function _post(data, params) {
  return request({
    url: context,
    method: 'post',
    data,
    params
  })
}

export function _delete(data, params) {
  return request({
    url: context + '/' + (data.id || data.ids),
    method: 'delete',
    data,
    params
  })
}

export default { _delete, _get, _patch, _post }
