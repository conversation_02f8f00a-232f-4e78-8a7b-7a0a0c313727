<!--
<template>
  <div id="nav">
    <router-link to="/">Home</router-link> |
    <router-link to="/about">About</router-link>
  </div>
  <router-view />
</template>

<style lang="less">
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

#nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
</style>
-->

<!--<template>标签上使用 v-for来循环-->
<!--export：用于导出常量、函数、文件、模块等；都可以使用import导入-->
<!--export default：-->

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
// 大屏使用
// import InitDbRoutes from './mixin/InitDbRoutes'
// import InitGlobalConfigData from './mixin/InitGlobalConfigData'
import Cookies from 'js-cookie'
import { sel } from '@/api/core/system/sysLogo'

export default {
  name: 'App',
  // mixins: [InitDbRoutes, InitGlobalConfigData],
  mixins: [],
  data() {
    return { iconImage: '' }
  },
  created() {
    this.toIconQuery()
  },
  beforeMount() {},
  mounted() {},
  methods: {
    // 查询ICO图片信息按钮
    toIconQuery() {
      const query = {
        user_name: Cookies.get('userName')
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.iconImage = 'data:image/png;base64,' + defaultQuery.data[0].ico
              var link = document.querySelector("link[rel*='icon']") || document.createElement('link')
              link.type = 'image/x-icon'
              link.rel = 'shortcut icon'
              link.href = this.iconImage
              document.getElementsByTagName('head')[0].appendChild(link)
              this.$store.state.settings.footerTxt = defaultQuery.data[0].footer_txt
            }
          }
        })
        .catch(() => {
          // this.$message({
          //   type: 'error',
          //   message: '查询ICON数据失败'
          // })
          console.log('查询ICON数据失败')
        })
    }
  }
}
</script>
<!-- 大屏
<style lang="less">
  html, body {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
  }

  #app {
    width: 100%;
    height: 100%;
  }
</style>
-->
<style>
body .el-table th.gutter{
    display: table-cell!important;
}
</style>
