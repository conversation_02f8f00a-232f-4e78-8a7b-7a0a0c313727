<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          料盒1
        </template>
        <el-input ref="webBoxBarCode1" v-model="webBoxBarCode1" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          料盒2
        </template>
        <el-input ref="webBoxBarCode2" v-model="webBoxBarCode2" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          料盒3
        </template>
        <el-input ref="webBoxBarCode3" v-model="webBoxBarCode3" clearable size="mini" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          料盒4
        </template>
        <el-input ref="webBoxBarCode4" v-model="webBoxBarCode4" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo('confirm')">人工输入</el-button>
      <el-button type="primary" @click="handleSendInfo('retry')">重 读</el-button>
      <el-button type="primary" @click="handleSendInfo('pass')">越 过</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      webBoxBarCode1: '',
      webBoxBarCode2: '',
      webBoxBarCode3: '',
      webBoxBarCode4: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webBoxBarCode1.focus()
    })
    var boxBarCodeList=this.tag_key_list.BoxBarCodeList
    if(boxBarCodeList!==''){
      var tempList=boxBarCodeList.split('&')
      if(tempList[0]!=='NoRead'){
         this.webBoxBarCode1=tempList[0]
      }
      if(tempList[1]!=='NoRead'){
         this.webBoxBarCode2=tempList[1]
      }
      if(tempList[2]!=='NoRead'){
         this.webBoxBarCode3=tempList[2]
      }
      if(tempList[3]!=='NoRead'){
         this.webBoxBarCode4=tempList[3]
      }
    }
  },
  methods: {
    handleSendInfo(type) {
      var webPanelletConfirmModel = '0'
      if(type === 'confirm' || type === 'pass'){
        if (this.webBoxBarCode1 === '' || this.webBoxBarCode1 === 'NoRead') {
          this.$message({ message: '请输入BOX1', type: 'info' })
          return
        }
        if (this.webBoxBarCode2 === '' || this.webBoxBarCode2 === 'NoRead') {
          this.$message({ message: '请输入BOX2', type: 'info' })
          return
        }
        if (this.webBoxBarCode3 === '' || this.webBoxBarCode3 === 'NoRead') {
          this.$message({ message: '请输入BOX3', type: 'info' })
          return
        }
        if (this.webBoxBarCode4 === '' || this.webBoxBarCode4 === 'NoRead') {
          this.$message({ message: '请输入BOX4', type: 'info' })
          return
        }
      }
      if (type === 'confirm') {
        webPanelletConfirmModel = '1'
      } else if (type === 'retry') {
        webPanelletConfirmModel = '2'
      } else if (type === 'pass') {
        webPanelletConfirmModel = '3'
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebPalletNum,
        TagValue: this.webBoxBarCode1+'&'+this.webBoxBarCode2+'&'+this.webBoxBarCode3+'&'+this.webBoxBarCode4
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPalletConfirmModel,
        TagValue: webPanelletConfirmModel
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPalletInfoRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebPalletNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
