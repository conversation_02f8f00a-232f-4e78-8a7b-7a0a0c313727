<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="工位：">
                <el-select v-model="query.station_id" filterable clearable size="small">
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="机型：">
                <el-select v-model="query.small_type_id" filterable clearable size="small">
                  <el-option v-for="item in smallModelData" :key="item.small_type_id" :label="item.small_model_type + ' ' + item.model_code" :value="item.small_type_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="物料编码：">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="物料描述：">
                <el-input v-model="query.material_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button size="small" type="primary" icon="el-icon-upload2" plain round @click="importDialogVisible = true">
            导入
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="260px" :inline="true">
          <el-form-item label="工位" prop="station_id">
            <el-select v-model="form.station_id" filterable clearable>
              <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="机型" prop="small_type_id">
            <el-select v-model="form.small_type_id" filterable clearable>
              <el-option v-for="item in smallModelData" :key="item.small_type_id" :label="item.small_model_type + ' ' + item.model_code" :value="item.small_type_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="物料编码" prop="material_code">
            <el-input v-model="form.material_code" />
          </el-form-item>
          <el-form-item label="物料描述" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item>
          <el-form-item label="零件图号" prop="draw_code">
            <el-input v-model="form.draw_code" />
          </el-form-item>
          <el-form-item label="配套用量" prop="usage">
            <el-input v-model.number="form.usage" />
          </el-form-item>
          <el-form-item label="单位" prop="material_uom">
            <el-input v-model="form.material_uom" />
          </el-form-item>
          <el-form-item label="物料属性" prop="material_attr">
            <el-select v-model="form.material_attr">
              <el-option v-for="item in dict.MATERIAL_ATTR" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="物料单位成本" prop="material_cost">
            <el-input v-model="form.material_cost" />
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="form.remarks" />
          </el-form-item>
          <el-form-item label="最大数量" prop="max_count">
            <el-input v-model.number="form.max_count" />
          </el-form-item>
          <el-form-item label="安全库存" prop="min_count">
            <el-input v-model.number="form.min_count" />
          </el-form-item>
          <el-form-item label="料架编码" prop="rack_code">
            <el-input v-model="form.rack_code" />
          </el-form-item>
          <el-form-item label="料架最大承载容器数" prop="rack_max_count">
            <el-input v-model.number="form.rack_max_count" />
          </el-form-item>
          <el-form-item label="料架安全承载容器数" prop="rack_min_count">
            <el-input v-model.number="form.rack_min_count" />
          </el-form-item>
          <el-form-item label="标准容器规格" prop="box_type">
            <el-input v-model="form.box_type" />
          </el-form-item>
          <el-form-item label="标准容器承载数量" prop="box_set">
            <el-input v-model.number="form.box_set" />
          </el-form-item>
          <el-form-item label="零件标号" prop="material_index">
            <el-input v-model.number="form.material_index" />
          </el-form-item>
          <el-form-item label="零件规格" prop="material_gg">
            <el-input v-model="form.material_gg" />
          </el-form-item>
          <el-form-item label="是否主物料" prop="main_material_flag">
            <el-radio-group v-model="form.main_material_flag">
              <el-radio v-for="item in dictsData" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否精准追溯验证" prop="verify_flag">
            <el-radio-group v-model="form.verify_flag">
              <el-radio v-for="item in dictsData" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否批次验证" prop="batch_flag">
            <el-radio-group v-model="form.batch_flag">
              <el-radio v-for="item in dictsData" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="精准追溯物料验证规则" prop="material_rule_exact">
            <el-input v-model="form.material_rule_exact" />
          </el-form-item>
          <el-form-item label="批次追溯物料验证规则" prop="material_rule_card">
            <el-input v-model="form.material_rule_card" />
          </el-form-item>
          <el-form-item label="是否库存管理" prop="stock_flag">
            <el-radio-group v-model="form.stock_flag">
              <el-radio v-for="item in dictsData" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="物料版本号" prop="bom_version">
            <el-input v-model="form.bom_version" />
          </el-form-item>
          <el-form-item label="是否防重" prop="fchong_flag">
            <el-radio-group v-model="form.fchong_flag">
              <el-radio v-for="item in dictsData" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="属性1" prop="attribute1">
            <el-input v-model="form.attribute1" />
          </el-form-item>
          <el-form-item label="属性2" prop="attribute2">
            <el-input v-model="form.attribute2" />
          </el-form-item>
          <el-form-item label="属性3" prop="attribute3">
            <el-input v-model="form.attribute3" />
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <!--导入BOM-->
      <el-dialog :fullscreen="false" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" :modal-append-to-body="false" title="导入BOM" width="400px" :visible.sync="importDialogVisible">
        <div class="uploadStyleone">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :auto-upload="false"
            :on-change="handleImport"
            :http-request="uploadFile"
            :on-progress="progressA"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
          <el-input
            v-if="isUpLoadError"
            v-model="errorMsg"
            type="textarea"
            :rows="5"
          />
          <div style="text-align: center;margin-top:10px">
            <el-link
              href="../static/bomTemplate.xlsx"
              target="_blank"
              type="primary"
              icon="el-icon-download"
              download="BOM导入模板.xlsx"
            >下载导入模板</el-link>
            <el-button type="primary" size="small" icon="el-icon-check" :loading="upLoading" style="margin-left:10px" @click="toButDrawerUpload">上传</el-button>
          </div>
        </div>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            height="450px"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_id }}</el-descriptions-item>
                  <el-descriptions-item label="工位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_code + ' ' + props.row.station_des }}</el-descriptions-item>
                  <el-descriptions-item label="机型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.small_model_type }}</el-descriptions-item>
                  <el-descriptions-item label="物料编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                  <el-descriptions-item label="物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_des }}</el-descriptions-item>
                  <el-descriptions-item label="零件图号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.draw_code }}</el-descriptions-item>
                  <el-descriptions-item label="配套用量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.usage }}</el-descriptions-item>
                  <el-descriptions-item label="单位" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_uom }}</el-descriptions-item>
                  <el-descriptions-item label="物料属性" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.MATERIAL_ATTR[props.row.material_attr] }}</el-descriptions-item>
                  <el-descriptions-item label="物料单位成本" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_cost }}</el-descriptions-item>
                  <el-descriptions-item label="备注" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.remarks }}</el-descriptions-item>
                  <el-descriptions-item label="最大数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.max_count }}</el-descriptions-item>
                  <el-descriptions-item label="安全库存" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.min_count }}</el-descriptions-item>
                  <el-descriptions-item label="料架编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rack_code }}</el-descriptions-item>
                  <el-descriptions-item label="料架最大承载容器数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rack_max_count }}</el-descriptions-item>
                  <el-descriptions-item label="料架安全承载容器数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.rack_min_count }}</el-descriptions-item>
                  <el-descriptions-item label="标准容器规格" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_type }}</el-descriptions-item>
                  <el-descriptions-item label="标准容器承载数量" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.box_set }}</el-descriptions-item>
                  <el-descriptions-item label="零件标号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_index }}</el-descriptions-item>
                  <el-descriptions-item label="零件规格" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_gg }}</el-descriptions-item>
                  <el-descriptions-item label="是否主物料" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.main_material_flag === 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="是否精准追溯验证" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.verify_flag === 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="是否批次验证" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.batch_flag === 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="精准追溯物料验证规则" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_exact }}</el-descriptions-item>
                  <el-descriptions-item label="批次追溯物料验证规则" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_rule_card }}</el-descriptions-item>
                  <el-descriptions-item label="是否库存管理" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.stock_flag === 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="物料版本号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bom_version }}</el-descriptions-item>
                  <el-descriptions-item label="是否防重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.fchong_flag === 'Y' ? '是' : '否' }}</el-descriptions-item>
                  <el-descriptions-item label="属性1" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute1 }}</el-descriptions-item>
                  <el-descriptions-item label="属性2" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute2 }}</el-descriptions-item>
                  <el-descriptions-item label="属性3" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.attribute3 }}</el-descriptions-item>
                  <el-descriptions-item label="有效标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                  <el-descriptions-item label="创建人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.created_by }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.creation_date }}</el-descriptions-item>
                  <el-descriptions-item label="修改人" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_updated_by }}</el-descriptions-item>
                  <el-descriptions-item label="修改时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.last_update_date }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="station_id" width="150" label="工位">
              <template slot-scope="scope">
                {{ scope.row.station_code + ' ' + scope.row.station_des }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="small_type_id" width="150" label="机型">
              <template slot-scope="scope">
                {{ scope.row.small_model_type }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="material_code" width="150" label="物料编码" />
            <el-table-column :show-overflow-tooltip="true" prop="material_des" width="150" label="物料描述" />
            <el-table-column :show-overflow-tooltip="true" prop="draw_code" width="100" label="零件图号" />
            <el-table-column :show-overflow-tooltip="true" prop="usage" width="100" label="配套用量" />
            <el-table-column :show-overflow-tooltip="true" prop="material_uom" width="100" label="单位" />
            <el-table-column :show-overflow-tooltip="true" prop="material_attr" width="100" label="物料属性">
              <template slot-scope="scope">
                {{ dict.label.MATERIAL_ATTR[scope.row.material_attr] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="rack_code" width="100" label="料架编码" />
            <el-table-column :show-overflow-tooltip="true" prop="material_index" width="100" label="零件标号" />
            <el-table-column :show-overflow-tooltip="true" prop="material_gg" width="100" label="零件规格" />
            <el-table-column :show-overflow-tooltip="true" prop="main_material_flag" width="100" label="主物料">
              <template slot-scope="scope">
                {{ scope.row.main_material_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="verify_flag" width="130" label="精准追溯验证">
              <template slot-scope="scope">
                {{ scope.row.verify_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="batch_flag" width="100" label="批次验证">
              <template slot-scope="scope">
                {{ scope.row.batch_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="stock_flag" width="100" label="库存管理">
              <template slot-scope="scope">
                {{ scope.row.stock_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="bom_version" width="100" label="版本号" />
            <el-table-column :show-overflow-tooltip="true" prop="fchong_flag" width="100" label="是否防重">
              <template slot-scope="scope">
                {{ scope.row.fchong_flag === 'Y' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="creation_date" width="150" label="创建时间" />
            <el-table-column label="有效标识" prop="enable_flag" width="70">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudSmallModelBom from '@/api/mes/core/smallModelBom'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  material_id: '',
  station_id: '',
  small_type_id: '',
  material_code: '',
  material_des: '',
  draw_code: '',
  usage: '',
  material_uom: '',
  material_attr: '',
  material_cost: '',
  remarks: '',
  max_count: '',
  min_count: '',
  rack_code: '',
  rack_max_count: '',
  rack_min_count: '',
  box_type: '',
  box_set: '',
  material_index: '',
  material_gg: '',
  main_material_flag: 'N',
  verify_flag: 'N',
  batch_flag: 'N',
  material_rule_exact: '',
  material_rule_card: '',
  stock_flag: 'N',
  bom_version: '',
  fchong_flag: 'N',
  enable_flag: 'Y',
  attribute1: '',
  attribute2: '',
  attribute3: ''
}
export default {
  name: 'MES_SMALL_MODEL_BOM',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '机型BOM',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'material_id',
      // 排序
      sort: ['material_id asc'],
      // CRUD Method
      crudMethod: { ...crudSmallModelBom },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'MATERIAL_ATTR'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback()
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    var checkUsage = (rule, value, callback) => {
      if (value.length === 0) {
        return callback(new Error('请输入配套用量'))
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'mes_small_model_bom:add'],
        edit: ['admin', 'mes_small_model_bom:edit'],
        del: ['admin', 'mes_small_model_bom:del'],
        down: ['admin', 'mes_small_model_bom:down']
      },
      rules: {
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        small_type_id: [{ required: true, message: '请选择机型', trigger: 'blur' }],
        material_code: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
        material_des: [{ required: true, message: '请输入物料描述', trigger: 'blur' }],
        usage: [{ required: true, validator: checkUsage, trigger: 'blur' }],
        material_cost: [
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,7})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
            message: '请输入正确格式,可保留七位小数'
          }
        ],
        max_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        min_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        rack_max_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        rack_min_count: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        box_set: [{ required: true, validator: checkNumber, trigger: 'blur' }],
        material_index: [{ required: true, validator: checkNumber, trigger: 'blur' }]
      },
      dictsData: [{ value: 'Y', label: '是' }, { value: 'N', label: '否' }],
      stationData: [],
      smallModelData: [],
      uploadLimit: 1,
      uploadAccept: '.xlsx',
      fileList: [],
      importDialogVisible: false,
      fileData: new FormData(),
      upLoading: false,
      isUpLoadError: false,
      errorMsg: ''
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 300
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })

    const query1 = {
      userID: Cookies.get('userName'),
      sort: '',
      page: '',
      size: '',
      enable_flag: 'Y'
    }
    selSmallModel(query1)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.smallModelData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定要将物料为【' + data.material_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudSmallModelBom
            .editEnableFlag({
              user_name: Cookies.get('userName'),
              material_id: data.material_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '修改成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {},
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = '/mes/core/MesSmallModelBomImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .el-form-wrap {
  height: 85%;
  overflow: auto;
}
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
