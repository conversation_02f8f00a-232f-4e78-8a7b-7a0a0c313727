<template>
  <div class="screen_container">
    <div class="screen_head">
      <h2>装三生产车间设备状态</h2>
      <div class="headLogo">
        <img class="logo" src="@/assets/images/qcLogo.png">
        <div class="imgtime"><img src="@/assets/images/time.png"><span class="showTime">{{ gettime }}</span><span class="showTime timeMargin">{{ week }}</span></div>
      </div>
    </div>
    <div class="mainbox">
      <div class="column">
        <div class="columnFirst">
          <div class="wrappstyle">
            <p><span class="wholeline default" /><span>默认状态</span></p>
            <p><span class="wholeline normal" /><span>设备正常</span></p>
            <p><span class="wholeline secure" /><span>设备安全报警</span></p>
            <p><span class="wholeline hardware" /><span>设备硬件报警</span></p>
            <p><span class="wholeline message" /><span>设备通讯报警</span></p>
            <p><span class="wholeline overtime" /><span>设备超时报警</span></p>
            <p><span class="wholeline warn" /><span>设备警告报警</span></p>
          </div>
        </div>
        <div class="columnFirst around">
          <div class="neishi">
            <div class="wrappstyleOne">
              <p><span>OP3020<br>缸套压装</span><span ref="OP3020" class="wholeline default" /></p>
              <p><span>0P3030<br>缸套测量</span><span ref="OP3030" class="wholeline default" /></p>
              <p><span>0P3040<br>打标上线</span><span ref="OP3040" class="wholeline default" /></p>
              <p><span>0P3050A<br>主轴盖旋松</span><span ref="OP3050A" class="wholeline default" /></p>

              <p><span>OP3050B<br>主轴盖抓取</span><span ref="OP3050B" class="wholeline default" /></p>
              <p><span>0P3080<br>固紧活塞冷却</span><span ref="OP3080" class="wholeline default" /></p>
              <p><span>OP3090<br>曲轴上线</span><span ref="OP3090" class="wholeline default" /></p>
              <p><span>0P3110<br>主轴间隙测量</span><span ref="OP3110" class="wholeline default" /></p>
              <p><span>0P3130<br>连杆转运</span><span ref="OP3130" class="wholeline default" /></p>

              <p><span>OP3145A<br>缸体翻转</span><span ref="OP3145A" class="wholeline default" /></p>
              <p><span>OP3160<br>连杆力矩测量</span><span ref="OP3160" class="wholeline default" /></p>
            </div>
            <h2>内饰线</h2>
            <div class="wrappstyleOne">
              <p><span>OP3330<br>油底壳固紧</span><span ref="OP3330" class="wholeline default" /></p>
              <p><span>OP3310<br>油底壳上料</span><span ref="OP3310" class="wholeline default" /></p>

              <p><span>OP3290B<br>油底壳装垫片</span><span ref="OP3290B" class="wholeline default" /></p>
              <p><span>OP3290A<br>油底壳涂胶</span><span ref="OP3290A" class="wholeline default" /></p>
              <p><span>OP3270<br>飞轮壳拧紧</span><span ref="OP3270" class="wholeline default" /></p>

              <p><span>OP3190A<br>齿轮室垫片安装</span><span ref="OP3190A" class="wholeline default" /></p>
            </div>
          </div>
          <div class="neishi">
            <div class="wrappstyleOne">
              <p><span>OP1010<br>连杆上线</span><span ref="OP1010" class="wholeline default" /></p>
            </div>
            <h2>活连线</h2>
            <div class="wrappstyleOne">
              <p><span>OP1020<br>活塞装配</span><span ref="OP1020" class="wholeline default" /></p>
            </div>
          </div>
          <div class="neishi">
            <div class="wrappstyleOne">
              <p><span>OP2040A<br>装气门</span><span ref="OP2040A" class="wholeline default" /></p>
              <p><span>OP2040B<br>下沉测量</span><span ref="OP2040B" class="wholeline default" /></p>
            </div>
            <h2>缸盖线</h2>
            <div class="wrappstyleOne">
              <p><span>OP2070<br>锁夹压装</span><span ref="OP2070" class="wholeline default" /></p>
            </div>
          </div>
        </div>
        <div class="columnFirst around">
          <div class="neishi neishifull">
            <div class="wrappstyleOne">
              <p><span>OP4040<br>飞轮螺栓拧紧</span><span ref="OP4040" class="wholeline default" /></p>
              <p><span>OP4120<br>齿轮室拧紧</span><span ref="OP4120" class="wholeline default" /></p>
              <p><span>OP4140<br>活塞凸出测量</span><span ref="OP4140" class="wholeline default" /></p>
              <p><span>OP4160<br>皮带轮拧紧</span><span ref="OP4160" class="wholeline default" /></p>
              <p><span>OP4180<br>固紧机油冷却</span><span ref="OP4180" class="wholeline default" /></p>
            </div>
            <h2>外装线</h2>
            <div class="wrappstyleOne">
              <p><span>OP4430<br>气缸盖罩拧紧</span><span ref="OP4430" class="wholeline default" /></p>
              <p><span>OP4400<br>固紧摇臂螺栓</span><span ref="OP4400" class="wholeline default" /></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { selStatusNienSix, selStatusNienEight, selStatusZeroZero, selStatusZeroOne, selStatusEightNine, selStatusNienZero, selStatusNineOne, selStatusNineTwo, selStatusNineThree, selStatusNineFour, selStatusNineFive } from '@/api/mes/project/qcEquipStatus'
export default {
  name: 'MES_QC_EQUIP_STATUSCREEN',
  data() {
    return {
      timer1: '',
      timer2: '',
      timer3: '',
      timer4: '',
      timer5: '',
      timer6: '',
      timer7: '',
      timer8: '',
      timer9: '',
      timer10: '',
      timer11: '',
      timer12: '',
      gettime: '',
      week: '',
      tagWzArrNienSix: [],
      tagWzArrNienEight: [],
      tagWzArrZeroZero: [],
      tagWzArrZeroOne: [],
      tagWzggArrEightNine: [],
      tagggArrNienZero: [],
      tagNzArrNineOne: [],
      tagNzArrNineTwo: [],
      tagNzArrNineThree: [],
      tagNzArrNineFour: [],
      tagNzArrNineFive: []
    }
  },
  created: function() {
    this.getTime()
    this.timer1 = setInterval(() => {
      this.getTime()
    }, 1000)
    // 外装线8096
    this.initStatusNienSix()
    this.timer2 = setInterval(() => {
      this.initStatusNienSix()
    }, 3000)
    // 外装线8098
    this.initStatusNienEight()
    this.timer3 = setInterval(() => {
      this.initStatusNienEight()
    }, 3000)
    // 外装线8100
    this.initStatusZeroZero()
    this.timer4 = setInterval(() => {
      this.initStatusZeroZero()
    }, 3000)
    // 外装线8101
    this.initStatusZeroOne()
    this.timer5 = setInterval(() => {
      this.initStatusZeroOne()
    }, 3000)
    // 活连线/缸盖线 8189
    this.initStatusEightNine()
    this.timer6 = setInterval(() => {
      this.initStatusEightNine()
    }, 3000)
    // 缸盖线 8090
    this.initStatusNienZero()
    this.timer7 = setInterval(() => {
      this.initStatusNienZero()
    }, 3000)
    // 缸盖线 8091
    this.initStatusNineOne()
    this.timer8 = setInterval(() => {
      this.initStatusNineOne()
    }, 3000)
    // 缸盖线 8092
    this.initStatusNineTwo()
    this.timer9 = setInterval(() => {
      this.initStatusNineTwo()
    }, 3000)
    // 缸盖线 8093
    this.initStatusNineThree()
    this.timer10 = setInterval(() => {
      this.initStatusNineThree()
    }, 3000)
    // 缸盖线 8094
    this.initStatusNineFour()
    this.timer11 = setInterval(() => {
      this.initStatusNineFour()
    }, 3000)
    // 缸盖线 8095
    this.initStatusNineFive()
    this.timer12 = setInterval(() => {
      this.initStatusNineFive()
    }, 3000)
  },
  mounted() {
  },
  // 离开此页面时销毁定时器
  beforeDestroy() {
    clearInterval(this.timer1)
    clearInterval(this.timer2)
    clearInterval(this.timer3)
    clearInterval(this.timer4)
    clearInterval(this.timer5)
    clearInterval(this.timer6)
    clearInterval(this.timer7)
    clearInterval(this.timer8)
    clearInterval(this.timer9)
    clearInterval(this.timer10)
    clearInterval(this.timer11)
    clearInterval(this.timer12)
  },
  methods: {
    // 外装线8096
    initStatusNienSix() {
      const queryArryNienSix = [
        {
          tag_key: 'OP4040/PlcAlarm/PlcA_MentStaus'
        }, {
          tag_key: 'OP4120/PlcAlarm/PlcA_MentStaus'
        }, {
          tag_key: 'OP4140/PlcAlarm/PlcA_MentStaus'
        }, {
          tag_key: 'OP4160/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNienSix(queryArryNienSix)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagWzArrNienSix = res.data
          for (var i = 0; i < this.tagWzArrNienSix.length; i++) {
            var station_code = this.tagWzArrNienSix[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagWzArrNienSix[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP4040') {
              if (tag_value === '0') {
                this.$refs.OP4040.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4040.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4040.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4040.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4040.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4040.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP4120') {
              if (tag_value === '0') {
                this.$refs.OP4120.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4120.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4120.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4120.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4120.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4120.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP4140') {
              if (tag_value === '0') {
                this.$refs.OP4140.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4140.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4140.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4140.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4140.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4140.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP4160') {
              if (tag_value === '0') {
                this.$refs.OP4160.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4160.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4160.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4160.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4160.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4160.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 外装线8098
    initStatusNienEight() {
      const queryArryNienEight = [
        {
          tag_key: 'OP4180/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNienEight(queryArryNienEight)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagWzArrNienEight = res.data
          for (var i = 0; i < this.tagWzArrNienEight.length; i++) {
            var station_code = this.tagWzArrNienEight[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagWzArrNienEight[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP4180') {
              if (tag_value === '0') {
                this.$refs.OP4180.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4180.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4180.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4180.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4180.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4180.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 外装线8100
    initStatusZeroZero() {
      const queryArryZeroZero = [
        {
          tag_key: 'OP4400/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusZeroZero(queryArryZeroZero)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagWzArrZeroZero = res.data
          for (var i = 0; i < this.tagWzArrZeroZero.length; i++) {
            var station_code = this.tagWzArrZeroZero[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagWzArrZeroZero[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP4400') {
              if (tag_value === '0') {
                this.$refs.OP4400.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4400.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4400.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4400.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4400.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4400.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 外装线8101
    initStatusZeroOne() {
      const queryArryZeroOne = [
        {
          tag_key: 'OP4430/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusZeroOne(queryArryZeroOne)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagWzArrZeroOne = res.data
          for (var i = 0; i < this.tagWzArrZeroOne.length; i++) {
            var station_code = this.tagWzArrZeroOne[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagWzArrZeroOne[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP4430') {
              if (tag_value === '0') {
                this.$refs.OP4430.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP4430.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP4430.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP4430.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP4430.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP4430.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 活连线/缸盖线 8189
    initStatusEightNine() {
      const queryArryEightNine = [
        {
          tag_key: 'OP1010/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP1020/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP2040A/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP2040B/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusEightNine(queryArryEightNine)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagWzggArrEightNine = res.data
          for (var i = 0; i < this.tagWzggArrEightNine.length; i++) {
            var station_code = this.tagWzggArrEightNine[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagWzggArrEightNine[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP1010') {
              if (tag_value === '0') {
                this.$refs.OP1010.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP1010.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP1010.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP1010.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP1010.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP1010.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP1020') {
              if (tag_value === '0') {
                this.$refs.OP1020.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP1020.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP1020.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP1020.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP1020.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP1020.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP2040A') {
              if (tag_value === '0') {
                this.$refs.OP2040A.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP2040A.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP2040A.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP2040A.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP2040A.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP2040A.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP2040B') {
              if (tag_value === '0') {
                this.$refs.OP2040B.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP2040B.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP2040B.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP2040B.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP2040B.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP2040B.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 缸盖线8090
    initStatusNienZero() {
      const queryArryNienZero = [
        {
          tag_key: 'OP2070/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNienZero(queryArryNienZero)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagggArrNienZero = res.data
          for (var i = 0; i < this.tagggArrNienZero.length; i++) {
            var station_code = this.tagggArrNienZero[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagggArrNienZero[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP2070') {
              if (tag_value === '0') {
                this.$refs.OP2070.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP2070.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP2070.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP2070.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP2070.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP2070.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 内装线 8091
    initStatusNineOne() {
      const queryArryNineOne = [
        {
          tag_key: 'OP3020/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3030/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3040/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3050A/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNineOne(queryArryNineOne)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagNzArrNineOne = res.data
          for (var i = 0; i < this.tagNzArrNineOne.length; i++) {
            var station_code = this.tagNzArrNineOne[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagNzArrNineOne[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP3020') {
              if (tag_value === '0') {
                this.$refs.OP3020.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3020.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3020.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3020.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3020.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3020.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3030') {
              if (tag_value === '0') {
                this.$refs.OP3030.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3030.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3030.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3030.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3030.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3030.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3040') {
              if (tag_value === '0') {
                this.$refs.OP3040.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3040.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3040.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3040.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3040.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3040.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3050A') {
              if (tag_value === '0') {
                this.$refs.OP3050A.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3050A.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3050A.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3050A.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3050A.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3050A.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 内装线 8092
    initStatusNineTwo() {
      const queryArryNineTwo = [
        {
          tag_key: 'OP3050B/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3080/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3090/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3110/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3130/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNineTwo(queryArryNineTwo)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagNzArrNineTwo = res.data
          for (var i = 0; i < this.tagNzArrNineTwo.length; i++) {
            var station_code = this.tagNzArrNineTwo[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagNzArrNineTwo[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP3050B') {
              if (tag_value === '0') {
                this.$refs.OP3050B.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3050B.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3050B.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3050B.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3050B.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3050B.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3080') {
              if (tag_value === '0') {
                this.$refs.OP3080.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3080.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3080.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3080.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3080.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3080.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3090') {
              if (tag_value === '0') {
                this.$refs.OP3090.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3090.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3090.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3090.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3090.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3090.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3110') {
              if (tag_value === '0') {
                this.$refs.OP3110.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3110.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3110.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3110.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3110.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3110.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3130') {
              if (tag_value === '0') {
                this.$refs.OP3130.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3130.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3130.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3130.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3130.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3130.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 内装线 8093
    initStatusNineThree() {
      const queryArryNineThree = [
        {
          tag_key: 'OP3145A/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3160/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3190A/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNineThree(queryArryNineThree)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagNzArrNineThree = res.data
          for (var i = 0; i < this.tagNzArrNineThree.length; i++) {
            var station_code = this.tagNzArrNineThree[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagNzArrNineThree[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP3145A') {
              if (tag_value === '0') {
                this.$refs.OP3145A.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3145A.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3145A.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3145A.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3145A.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3145A.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3160') {
              if (tag_value === '0') {
                this.$refs.OP3160.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3160.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3160.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3160.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3160.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3160.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3190A') {
              if (tag_value === '0') {
                this.$refs.OP3190A.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3190A.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3190A.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3190A.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3190A.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3190A.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 内装线 8094
    initStatusNineFour() {
      const queryArryNineFour = [
        {
          tag_key: 'OP3270/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3290A/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3290B/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNineFour(queryArryNineFour)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagNzArrNineFour = res.data
          for (var i = 0; i < this.tagNzArrNineFour.length; i++) {
            var station_code = this.tagNzArrNineFour[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagNzArrNineFour[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP3270') {
              if (tag_value === '0') {
                this.$refs.OP3270.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3270.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3270.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3270.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3270.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3270.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3290A') {
              if (tag_value === '0') {
                this.$refs.OP3290A.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3290A.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3290A.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3290A.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3290A.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3290A.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3290B') {
              if (tag_value === '0') {
                this.$refs.OP3290B.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3290B.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3290B.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3290B.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3290B.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3290B.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 内装线 8095
    initStatusNineFive() {
      const queryArryNineFive = [
        {
          tag_key: 'OP3310/PlcAlarm/PlcA_MentStaus'
        },
        {
          tag_key: 'OP3330/PlcAlarm/PlcA_MentStaus'
        }
      ]
      selStatusNineFive(queryArryNineFive)
        .then(res => {
          console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.tagNzArrNineFive = res.data
          for (var i = 0; i < this.tagNzArrNineFive.length; i++) {
            var station_code = this.tagNzArrNineFive[i].tag_key
            var stationTag = station_code.split('/')[0]
            var tag_value = this.tagNzArrNineFive[i].tag_value
            // 0设备正常、1设备安全报警、2设备硬件报警、3设备通讯报警、4设备超时报警、5设备警告报警
            if (stationTag === 'OP3310') {
              if (tag_value === '0') {
                this.$refs.OP3310.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3310.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3310.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3310.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3310.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3310.style.backgroundColor = '#ff9900'
              }
            }
            if (stationTag === 'OP3330') {
              if (tag_value === '0') {
                this.$refs.OP3330.style.backgroundColor = '#00ff00'
              }
              if (tag_value === '1') {
                this.$refs.OP3330.style.backgroundColor = '#ff0000'
              }
              if (tag_value === '2') {
                this.$refs.OP3330.style.backgroundColor = '#d400ff'
              }
              if (tag_value === '3') {
                this.$refs.OP3330.style.backgroundColor = '#ffe600'
              }
              if (tag_value === '4') {
                this.$refs.OP3330.style.backgroundColor = '#007cff'
              }
              if (tag_value === '5') {
                this.$refs.OP3330.style.backgroundColor = '#ff9900'
              }
            }
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    }
  }
}
</script>

<style lang="less" scoped>
.screen_container{
  background: url('~@/assets/images/wcbg.jpg') no-repeat #000;
  background-size: cover;
  height:calc(100vh);
  .screen_head{
    position: relative;
    background: url('~@/assets/images/headBg.png') no-repeat;
    background-size: 100% 100%;
    height: 140px;
    h2{
        margin: 0;
        height: 140px;
        line-height: 100px;
        text-align: center;
        color: #ffffff;
        font-weight: 700;
        font-size: 40px;
    }
    .headLogo{
      width: 100%;
      position: absolute;
      top: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      .logo{
       width: 100px;
       margin-left: 152px;
      }
      .imgtime{
        display: flex;
        align-items: center;
        img{
          width: 20px;
          margin-right: 5px;
        }
        .showTime{
          color: #00e2fd;
          font-size: 30px;
          font-weight: 700;
        }
        .timeMargin{
          margin-left: 10px;
        }
      }
    }
  }
  .mainbox{
    // min-width: 1024px;
    // max-width: 1920px;
    padding: 0.125rem 0.125rem 0;
    display: flex;
    .column{
      width: 100%;
      .columnFirst{
        display: flex;
        .panel{
          padding: 0 0.1875rem 0.1875rem;
          margin-bottom: 0.1875rem;
        }
        .panelOne{
          width: 30%;
          margin-right: 0.1875rem;
          .panelHeight{
            height: 220px;
            margin-bottom: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
          .panelbgone{
            background: url('~@/assets/images/bordercenter.png') no-repeat;
            background-size: 100% 100%;
              p{
              font-size: 30px;
              color: #ffffff;
              font-weight: 700;
              margin: 10px 0;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-wrap: wrap;
                span{
                  color: #00ed1f;
                  font-size: 60px;
                }
            }
          }
          .indicatorrone{
            width: 100%;
            height: 600px;
          }
        }
        .panelTwo{
          width: 70%;
        }
        .panelbgtwo{
          background: url('~@/assets/images/bordercenter.png') no-repeat;
            background-size: 100% 100%;
        }
      }
    }
  }
}
// 细化滚动条
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 0 !important;
  height: 0 !important;
}
:deep(.el-table){
  background: none !important;
  margin-top: 30px !important;
  th{
    background: #1daad6 !important;
    color: #fff !important;
    font-size: 16px !important;
  }
  tr{
    background: none;
    color: #fff !important;
    font-size: 28px !important;
  }
  .cell{
    line-height: 26px;
  }
}
:deep(.el-table__body tr.current-row > td){
    background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table__row:hover){
     background-color: #000c4e !important;
    color: #ffffff;
  }
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background: none !important;
}
.around{
  justify-content: space-between;
  margin-top: 15px;
}
.neishifull{
  width: 100%;
}
.wrappstyle{
  display: flex;
  justify-content: space-around;
  border: 1px solid #3a137f;
  padding: 10px;
  p{
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    span{
        margin: 0 5px;
        color: #ffffff;
        font-weight: 700;
        font-size: 26px;
    }
  }
}
.neishi{
  .wrappstyleOne{
  display: flex;
  justify-content: space-around;
  border: 1px solid #3a137f;
  padding: 20px 10px;
  p{
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    flex-direction: column;
    span{
        margin: 5px;
        color: #ffffff;
        font-weight: 700;
        font-size: 26px;
        text-align: center;
    }
  }
}
h2{
    color: #ffffff;
    font-size: 30px;
    text-align: center;
    margin: 40px 0;
}
}
.wholeline{
  width:25px;
  height: 25px;
  border-radius: 50%;
  display: block;
}
.default{
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #c3c3c3;
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.normal{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #00ff00;
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.secure{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #ff0000;
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.hardware{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #d400ff;
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.message{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #ffe600;
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.overtime{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #007cff;
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
.warn{
  -webkit-animation: heart 1s linear infinite;
  animation: heart 1s linear infinite;
  background-color: #ff9900;
  background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255,255,255) 25%, rgba(255,255,255,0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255,255,255,0.5), rgba(255,255,255,0)), radial-gradient(100% 100%, rgba(255,255,255,0) 30%, rgba(255,255,255,0.3) 40%, rgba(0,0,0,0.5) 50%);
}
</style>
