import request from '@/utils/request'

// 查询码头基础表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWharfSelect',
    method: 'post',
    data
  })
}
// 新增码头基础表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWharfInsert',
    method: 'post',
    data
  })
}
// 修改码头基础表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWharfUpdate',
    method: 'post',
    data
  })
}
// 删除码头基础表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWharfDelete',
    method: 'post',
    data
  })
}

// 修改码头基础表--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsFmodWharfEnableFlagUpdate',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

