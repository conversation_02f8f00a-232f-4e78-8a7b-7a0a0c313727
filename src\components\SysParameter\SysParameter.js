import Vue from 'vue'
import { sel as getSysParameterDetail } from '@/api/core/system/sysParameter'

export default class SysParameter {
  constructor(sysParameter) {
    this.sysParameter = sysParameter
  }

  async init(names, completeCallback) {
    if (names === undefined || name === null) {
      throw new Error('need SysParameter names')
    }
    const ps = []
    names.forEach(n => {
      Vue.set(this.sysParameter, n, {})
      Vue.set(this.sysParameter.label, n, {})
      Vue.set(this.sysParameter, n, [])
      ps.push(
        getSysParameterDetail({ parameter_code: n, enable_flag: 'Y' }).then(data => {
          if (data.code === 0 && data.count > 0) {
            this.sysParameter[n].splice(0, 0, ...data.data)
          }
        })
      )
    })
    await Promise.all(ps)
    completeCallback()
  }
}
