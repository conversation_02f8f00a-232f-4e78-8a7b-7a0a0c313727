<template>
  <el-container style="height: 100%; border: 0px solid red">
    <el-aside width="350px" style="background-color: rgb(238, 241, 246)">
      <el-form ref="query" label-width="80px" :inline="false" :model="form" :rules="rules" size="small" :disabled="readonly" style="margin: 0; padding: 0;">
        <el-form-item label="函数模板" prop="function_m_code">
          <el-select v-model="form.function_m_code" clearable style="width:90%" @change="handleFunctionMCodeChange">
            <el-option v-for="item in functionMData" :key="item.function_m_code" :label="item.function_m_name + '/' + item.function_m_des" :value="item.function_m_code" />
          </el-select>
        </el-form-item>
        <el-form-item label="函数编码" prop="function_i_code">
          <el-input v-model="form.function_i_code" style="width:90%" />
        </el-form-item>
        <el-form-item label="函数名称" prop="function_i_name">
          <el-input v-model="form.function_i_name" style="width:90%" />
        </el-form-item>
        <el-form-item label="函数描述" prop="function_i_des">
          <el-input v-model="form.function_i_des" style="width:90%" />
        </el-form-item>
      </el-form>
      <div v-if="!readonly" style="text-align:center">
        <el-button class="filter-item" size="mini" type="primary" icon="el-icon-aim" style="margin-left: 10px" @click="handleCheck">代码检查</el-button>
        <el-button v-if="true" class="filter-item" size="mini" type="primary" icon="el-icon-circle-check" style="margin-left: 10px" @click="handleSave">保存</el-button>
        <el-button v-if="true" class="filter-item" size="mini" type="primary" icon="el-icon-s-promotion" style="margin-left: 10px" @click="handleRelease">发布</el-button>
      </div>
    </el-aside>
    <el-main>
      <codeEditor ref="codeEditor" :code_content="form.function_i_content" :readonly="readonly" @change="handleCodeChange" />
    </el-main>
  </el-container>
</template>

<script>
import codeEditor from '@/components/CodeEditor/CodeEditor'
import { sel as selM } from '@/api/core/flow/rcsFlowModFunctionM'
import { sel, add, edit } from '@/api/core/flow/rcsFlowModFunctionI'
import Cookies from 'js-cookie'
import Vue from 'vue'
const defaultForm = {
  user_name: Cookies.get('userName'),
  function_i_id: 0,
  function_m_code: '',
  function_i_code: '',
  function_i_name: '',
  function_i_des: '',
  function_i_content: '',
  function_i_status: ''
}
export default {
  name: 'RCSFLOWMODFUNCTIONI',
  components: { codeEditor },
  props: {
    step_mod_id: {
      type: [String, Number],
      default: -1
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 70,
      form: {},
      rules: {
        // 提交验证规则
        function_i_code: [{ required: true, message: '请输入函数编码', trigger: 'blur' }],
        function_i_name: [{ required: true, message: '请输入函数名称', trigger: 'blur' }],
        function_i_des: [{ required: true, message: '请输入函数描述', trigger: 'blur' }]
      },
      code_content: '',
      functionMData: [],
      functionMCodeOld: ''
    }
  },
  watch: {
    step_mod_id: {
      immediate: true,
      deep: true,
      handler() {
        for (const key in defaultForm) {
          if (this.form.hasOwnProperty(key)) {
            this.form[key] = defaultForm[key]
          } else {
            Vue.set(this.form, key, defaultForm[key])
          }
        }
        const query = {
          step_mod_id: this.step_mod_id
        }
        sel(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.data.length > 0) {
                this.form.function_i_id = defaultQuery.data[0].function_i_id
                this.form.function_m_code = defaultQuery.data[0].function_m_code
                this.form.function_i_code = defaultQuery.data[0].function_i_code
                this.form.function_i_name = defaultQuery.data[0].function_i_name
                this.form.function_i_des = defaultQuery.data[0].function_i_des
                this.form.function_i_content = defaultQuery.data[0].function_i_content
                this.form.function_i_status = defaultQuery.data[0].function_i_status
              }
            }
          })
          .catch(() => {
            this.$message({
              message: '查询异常',
              type: 'error'
            })
          })
      }
    },
    'form.function_m_code': {
      handler(val, oldval) {
        this.functionMCodeOld = oldval
      }
    }
  },
  mounted() {
    window.addEventListener('resize', () => (this.height = document.documentElement.clientHeight - 70), false)
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    selM(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.functionMData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    handleCodeChange(content) {
      this.code_content = content
    },
    handleFunctionMCodeChange(function_m_code) {
      this.$confirm(`更换函数模板会覆盖当前代码，是否继续?`, '提示', {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const function_m_content = this.functionMData.filter(item => item.function_m_code === function_m_code)[0].function_m_content
          this.form.function_i_content = function_m_content
        })
        .catch(() => {
          this.form.function_m_code = this.functionMCodeOld
        })
    },
    handleCheck() {},
    handleSave() {
      const saveData = {}
      for (const key in this.form) {
        Vue.set(saveData, key, this.form[key])
      }
      Vue.set(saveData, 'step_mod_id', this.step_mod_id)
      saveData.function_i_content = this.code_content
      if (this.form.function_i_id === 0) {
        add(saveData)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message({ message: '保存成功', type: 'success' })
            } else if (defaultQuery.code === -1) {
              this.$message({ message: defaultQuery.msg, type: 'warning' })
            }
          })
          .catch(() => {
            this.$message({ message: '保存异常', type: 'error' })
          })
      } else {
        edit(saveData)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message({ message: '保存成功', type: 'success' })
            } else if (defaultQuery.code === -1) {
              this.$message({ message: defaultQuery.msg, type: 'warning' })
            }
          })
          .catch(() => {
            this.$message({ message: '保存异常', type: 'error' })
          })
      }
    },
    handleRelease() {}
  }
}
</script>

<style scoped>
.el-main{
    padding: 0px;
}
.el-aside {
    margin-bottom: 0px;
    padding: 10px;
}
</style>
