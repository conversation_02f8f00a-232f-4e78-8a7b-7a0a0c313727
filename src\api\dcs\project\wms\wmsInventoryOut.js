import request from '@/utils/request'
// 查询库存量
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsInventoryOutSelect',
    method: 'post',
    data
  })
}
// 导出库存量
export function down(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/wms/DcsWmsInventoryOutExport',
    method: 'post',
    responseType: 'blob',
    data
  })
}
export default { sel, down }
