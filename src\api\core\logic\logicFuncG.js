import request from '@/utils/request'

// 查询自动化逻辑属性组
export function selLogicFuncGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncGSel',
    method: 'post',
    data
  })
}
// 新增自动化逻辑属性组
export function insLogicFuncGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncGIns',
    method: 'post',
    data
  })
}
// 修改自动化逻辑属性组
export function updLogicFuncGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncGUpd',
    method: 'post',
    data
  })
}
// 删除自动化逻辑属性组
export function delLogicFuncGroup(data) {
  return request({
    url: 'aisEsbWeb/core/logic/CoreLogicFuncGDel',
    method: 'post',
    data
  })
}
export default { selLogicFuncGroup, insLogicFuncGroup, updLogicFuncGroup, delLogicFuncGroup }
