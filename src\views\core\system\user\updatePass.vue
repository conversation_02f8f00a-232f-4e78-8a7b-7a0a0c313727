<template>
  <div style="display: inline-block">
    <el-dialog :visible.sync="dialog" :close-on-click-modal="false" :before-close="cancel" :title="title" append-to-body width="500px" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="88px">
        <el-form-item label="旧密码：" prop="oldPass" style="padding-bottom:15px;">
          <el-input v-model="form.oldPass" type="password" auto-complete="on" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="新密码：" prop="newPass" style="padding-bottom:15px;">
          <el-input v-model="form.newPass" type="password" auto-complete="on" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="确认密码：" prop="confirmPass" style="padding-bottom:15px;">
          <el-input v-model="form.confirmPass" type="password" auto-complete="on" style="width: 370px;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import store from '@/store'
import { updatePass } from '@/api/core/system/sysUser'
export default {
  data() {
    const confirmPass = (rule, value, callback) => {
      if (value) {
        if (this.form.newPass !== value) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请再次输入密码'))
      }
    }
    return {
      loading: false,
      dialog: false,
      title: '修改密码',
      form: { user_name: Cookies.get('userName'), oldPass: '', newPass: '', confirmPass: '' },
      rules: {
        oldPass: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        newPass: [{ required: true, message: '请输入新密码', trigger: 'blur' }, { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }],
        confirmPass: [{ required: true, validator: confirmPass, trigger: 'blur' }]
      }
    }
  },
  created() {
    this.$nextTick(() => {
      // 密码过期提示
      this.passwordHasExpiredTips()
    })
  },
  methods: {
    cancel() {
      this.resetForm()
    },
    doSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true
          updatePass(this.form)
            .then(res => {
              this.loading = false
              if (res.code === 0) {
                Cookies.remove('sys.passwordHasExpired')
                this.resetForm()
                this.$notify({
                  title: '密码修改成功，请重新登录',
                  type: 'success',
                  duration: 2000
                })
                // 判断当前路径不包含LOGIN时，退出登录
                if (this.$route.path.toUpperCase().indexOf('LOGIN') === -1) {
                  setTimeout(() => {
                    store.dispatch('LogOut').then(() => {
                      location.reload() // 为了重新实例化vue-router对象 避免bug
                    })
                  }, 1500)
                }
              } else {
                this.$notify({
                  title: '密码修改失败，' + res.msg,
                  type: 'error',
                  duration: 1500
                })
              }
            })
            .catch(err => {
              this.loading = false
              console.log(err.response.data.message)
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      this.dialog = false
      this.$refs['form'].resetFields()
      this.form = { oldPass: '', newPass: '', confirmPass: '' }
    },
    passwordHasExpiredTips() {
      // 密码过期提示
      const isTrue = Cookies.get('sys.passwordHasExpired')
      if (isTrue) {
        this.$notify({
          title: '提示',
          message: '当前登录密码已过期，请修改密码后重新登陆！',
          type: 'warning',
          duration: 5000
        })
        this.dialog = true
      }
    }
  }
}
</script>

<style scoped></style>
