<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="到货清单编号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="合同号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="批次号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="供应商名称:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-6 col-12">
              <el-form-item label="创建时间:">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:100%"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="状态：">
                <el-select v-model="query.lock_flag" clearable filterable>
                  <el-option
                    v-for="item in [{id:'1',label:'是',value:'Y'},{id:'2',label:'否',value:'N'}]"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-download"
            @click="importDialogVisible = true"
          >
            清单导入
          </el-button>
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="tableData"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="dhqdh" label="到货清单编号" />
            <el-table-column :show-overflow-tooltip="true" prop="hth" label="合同号" />
            <el-table-column :show-overflow-tooltip="true" prop="pch" label="批次号" />
            <el-table-column :show-overflow-tooltip="true" prop="drfs" label="导入方式" />
            <el-table-column :show-overflow-tooltip="true" prop="ly" label="来源" />
            <el-table-column :show-overflow-tooltip="true" prop="sl" label="数量" />
            <el-table-column :show-overflow-tooltip="true" prop="sj" label="创建时间" />
            <el-table-column :show-overflow-tooltip="true" prop="zt" width="120" label="状态" />
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  style="color: #22C55E;"
                >入库</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >编辑</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >删除</el-button>
                <!-- <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :disabled-dle="false"
                >
                  <template slot="right">
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                    >{{
                      $t("lang_pack.maintenanceMenu.addSubmenu")
                    }}</el-button>
                    新增子菜单
                  </template>
                </udOperation> -->
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-dialog
      :fullscreen="false"
      :show-close="true"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      title="电子到货清单导入"
      width="400px"
      :visible.sync="importDialogVisible"
    >
      <div class="uploadStyleone">
        <el-upload
          ref="upload"
          :multiple="false"
          class="upload-demo"
          action=""
          drag=""
          :limit="uploadLimit1"
          :accept="uploadAccept1"
          :auto-upload="false"
          :on-change="handleImport"
          :http-request="uploadFile"
          :on-progress="progressA"
          :file-list="fileList"
          name="file"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__text">文件大小不能超过50M,只能上传{{ uploadAccept1 }}文件</div>
        </el-upload>
        <el-input v-if="isUpLoadError" v-model="errorMsg" type="textarea" :rows="5" />
        <div style="text-align: center;margin-top:10px">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="upLoading"
            @click="toButDrawerUpload"
          >导入</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import crudWmsMapStockCell from '@/api/dcs/project/wms/wmsMapStockCell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
const defaultForm = {

}
export default {
  name: 'WMS_INVENTORY_LIST',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '入库到货清单',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapStockCell },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 320,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      tableData: [
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '入库中' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '排队中' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' },
        { dhqdh: '20250514001', hth: '232062', pch: 'BAO-SH2025-036', drfs: '一体化导入', ly: '宝钢', sl: '1000', sj: '2025-05-14 10:00:00', zt: '已完成' }
      ],
      uploadLimit: 1, // 注意：加密程序需要传2个文件
      uploadAccept: '*',
      uploadDrawerTitle: '文件上传',
      currentUploadType: '',
      currentFilePath: '',
      uploadDrawerVisbleSync: false,
      fileList: [],
      modelList: [],
      importDialogVisible: false,
      uploadLimit1: 1,
      uploadAccept1: '.xls,.xlsx',
      fileData: new FormData()
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 320
    }
  },
  created: function() {
  },
  methods: {
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.isUpLoadError = false
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.set('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) { },
    // 上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      const userName = Cookies.get('userName')
      if (userName) {
        this.fileData.set('userName', userName)
      }
      this.$refs.upload.submit()

      // 配置路径
      var method = 'dcs/core/DcsApsIntefProducTaskImport'
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method
      // var path = '/gxAisEsbWeb' + method
      this.upLoading = true
      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.fileList = []
            this.isUpLoadError = false
            this.upLoading = false
            this.importDialogVisible = false
            this.crud.toQuery()
          } else {
            this.upLoading = false
            // 失败后清除文件，不然改完再次上传会报错
            this.fileList = []
            this.errorMsg = defaultQuery.data.msg
            this.isUpLoadError = true
            this.$message({
              type: 'error',
              message: '导入失败!'
            })
          }
        }).catch((ex) => {
          this.upLoading = false
          // 失败后清除文件，不然改完再次上传会报错
          this.fileList = []
          this.$message({
            message: '导入异常' + ex,
            type: 'error'
          })
        })
    }
  }
}
</script>

    <style>
    .table-descriptions-label {
    width: 150px;
    }
    .table-descriptions-content {
    width: 150px;
    }
    </style>
