import request from '@/utils/request'

// 外装线8096
export function selStatusNienSix(data) {
  return request({
    url: 'http://10.2.101.2:8096/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 外装线8098
export function selStatusNienEight(data) {
  return request({
    url: 'http://10.2.101.2:8098/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 外装线8100
export function selStatusZeroZero(data) {
  return request({
    url: 'http://10.2.101.2:8100/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 外装线8101
export function selStatusZeroOne(data) {
  return request({
    url: 'http://10.2.101.2:8101/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 活连线/缸盖线 8189
export function selStatusEightNine(data) {
  return request({
    url: 'http://10.2.101.3:8089/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 缸盖线 8090
export function selStatusNienZero(data) {
  return request({
    url: 'http://10.2.101.3:8090/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 缸盖线 8091
export function selStatusNineOne(data) {
  return request({
    url: 'ttp://10.2.101.3:8091/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 缸盖线 8092
export function selStatusNineTwo(data) {
  return request({
    url: 'http://10.2.101.3:8092/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 缸盖线 8093
export function selStatusNineThree(data) {
  return request({
    url: 'http://10.2.101.3:8093/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 缸盖线 8094
export function selStatusNineFour(data) {
  return request({
    url: 'http://10.2.101.3:8094/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}
// 缸盖线 8095
export function selStatusNineFive(data) {
  return request({
    url: 'http://10.2.101.3:8095/cell/core/scada/CoreScadaReadTag',
    method: 'post',
    data
  })
}

export default { selStatusNienSix, selStatusNienEight, selStatusZeroZero, 
  selStatusZeroOne, selStatusEightNine, selStatusNienZero, 
  selStatusNineOne, selStatusNineTwo, selStatusNineThree, 
  selStatusNineFour, selStatusNineFive }

