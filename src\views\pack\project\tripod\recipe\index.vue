<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                <!-- 料号 -->
                <el-input v-model="query.model_type" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.vie.version') + ':'">
                <!-- 版本 -->
                <el-input v-model="query.model_version" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title "
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap scrollFormWarp"
              :model="form"
              :rules="rules"
              size="small"
              label-width="220px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.vie.partNum')" prop="model_type">
                <!-- 料号 -->
                <el-input v-model="form.model_type" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.version')" prop="model_version">
                <!-- 版本 -->
                <el-input v-model="form.model_version" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.setType')" prop="array_type">
                <!-- SET类型 -->
                <el-select v-model="form.array_type" clearable filterable>
                  <el-option v-for="item in dict.QR_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.pcsType')" prop="bd_type">
                <!-- PCS类型 -->
                <el-select v-model="form.bd_type" clearable filterable>
                  <el-option v-for="item in dict.QR_TYPE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateLen')" prop="m_length">
                <!-- 长 -->
                <el-input v-model.number="form.m_length" type="number" clearable size="small" @input="handleInput('m_length')" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateWid')" prop="m_width">
                <!-- 宽 -->
                <el-input v-model.number="form.m_width" type="number" clearable size="small" @input="handleInput('m_width')" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateThi')" prop="m_tickness">
                <!-- 厚 -->
                <el-input v-model.number="form.m_tickness" type="number" clearable size="small" @input="handleInput('m_tickness')" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.plateWei')" prop="m_weight">
                <!-- 重 -->
                <el-input v-model.number="form.m_weight" type="number" clearable size="small" @input="handleInput('m_weight')" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.setCodeLen')" prop="array_length">
                <!-- SET条码长度 -->
                <el-input v-model.number="form.array_length" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.pcsCodeLen')" prop="bd_length">
                <!-- PCS条码长度 -->
                <el-input v-model.number="form.bd_length" type="number" clearable size="small" @blur="BlurText($event)" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.setCase')" prop="array_case">
                <!-- SET大小写 -->
                <el-select v-model="form.array_case" clearable filterable>
                  <el-option v-for="item in dict.QR_CASE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.pcsCase')" prop="bd_case">
                <!-- PCS大小写 -->
                <el-select v-model="form.bd_case" clearable filterable>
                  <el-option v-for="item in dict.QR_CASE" :key="item.id" :label="item.label" :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.pcsNumStartNum')" prop="bd_index_start">
                <!-- PCS序号开始数字 -->
                <el-input v-model="form.bd_index_start" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.vie.pcsNumValadd')" prop="bd_index_incre">
                <!-- PCS序号递增值 -->
                <el-input v-model="form.bd_index_incre" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-for="(rule, i) in splitRuleNames"
                :key="rule + '_el-form-item_' + i"
                :label="$t('view.field.splitRules.' + rule)"
                :prop="rule + '_split_rule'"
                class="el-form-item-type"
              >
                <span>{{ $t('lang_pack.vie.form') }}</span>
                <el-select v-model="form[rule + '_split_rule' + '1']" clearable filterable class="subInput">
                  <el-option
                    v-for="item in dict.QR_DIRECT"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <span>{{ $t('lang_pack.vie.start') }}</span><el-input v-model.number="form[rule + '_split_rule' + '2']" type="number" clearable size="small" class="subInput" @blur="BlurText($event)" />
                <span>{{ $t('lang_pack.vie.locat') + ',' + $t('lang_pack.vie.cut') }}</span><el-input v-model.number="form[rule + '_split_rule' + '3']" type="number" clearable size="small" class="subInput" @blur="BlurText($event)" />
                <span>{{ $t('lang_pack.vie.charac') }}</span>
              </el-form-item>
              <el-form-item
                v-show="false"
                v-model="form.split_rules"
              />
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item :label="$t('lang_pack.vie.partNum')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.version')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_version }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.setType')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.QR_TYPE[props.row.array_type] }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.pcsType')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content"> {{ dict.label.QR_TYPE[props.row.bd_type] }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateLen')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_length }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateWid')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_width }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateThi')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_tickness }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.plateWei')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_weight }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.setCodeLen')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.array_length }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.pcsCodeLen')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bd_length }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.setCase')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.QR_TYPE[props.row.array_case] }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.pcsCase')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.QR_TYPE[props.row.bd_case] }}</el-descriptions-item>
                  <el-descriptions-item v-for="(rule, i) in splitRuleNames" :key="rule + '_el-descriptions-item_' +i" :label="$t('view.field.splitRules' + rule)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row[rule + '_split_rule'] }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.pcsNumStartNum')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bd_index_start }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.vie.pcsNumValadd')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.bd_index_incre }}</el-descriptions-item>
                  <el-descriptions-item :label="$t('lang_pack.commonPage.validIdentificationt')" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.ENABLE_FLAG[props.row.enable_flag] }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              type="index"
              :index="indexMethods"
              :label="$t('lang_pack.vie.index')"
              width="60"
              align="center"
            />
            <!-- 料号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.vie.partNum')"
              width="130"
              align="center"
            />
            <!-- 版本 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_version"
              :label="$t('lang_pack.vie.version')"
              width="120"
              align="center"
            />
            <!-- SET类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_type"
              :label="$t('lang_pack.vie.setType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.array_type] }}
              </template>
            </el-table-column>
            <!-- PCS类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_type"
              :label="$t('lang_pack.vie.pcsType')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_TYPE[scope.row.bd_type] }}
              </template>
            </el-table-column>
            <!-- 板长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.vie.plateLen')"
              width="130"
              align="center"
            />
            <!-- 板宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.vie.plateWid')"
              width="130"
              align="center"
            />
            <!-- 板厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_tickness"
              :label="$t('lang_pack.vie.plateThi')"
              width="130"
              align="center"
            />
            <!-- 板重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.vie.plateWei')"
              width="130"
              align="center"
            />
            <!-- SET条码长度 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_length"
              :label="$t('lang_pack.vie.setCodeLen')"
              width="130"
              align="center"
            />
            <!-- PCS条码长度 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_length"
              :label="$t('lang_pack.vie.pcsCodeLen')"
              width="130"
              align="center"
            />
            <!-- SET大小写 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="array_case"
              :label="$t('lang_pack.vie.setCase')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_CASE[scope.row.array_case] }}
              </template>
            </el-table-column>
            <!-- PCS大小写 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_case"
              :label="$t('lang_pack.vie.pcsCase')"
              width="140"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.QR_CASE[scope.row.array_case] }}
              </template>
            </el-table-column>
            <!-- 遍历截取规则 -->
            <el-table-column
              v-for="(rule, i) in splitRuleNames"
              :key="rule + '_el-table-column_' + i"
              :show-overflow-tooltip="true"
              :prop="rule + '_split_rule'"
              :label="$t('view.field.splitRules.' + rule)"
              width="190"
              align="center"
            />
            <!-- PCS序号开始数字 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_index_start"
              :label="$t('lang_pack.vie.pcsNumStartNum')"
              width="130"
              align="center"
            />
            <!-- PCS序号递增值 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="bd_index_incre"
              :label="$t('lang_pack.vie.pcsNumValadd')"
              width="130"
              align="center"
            />
            <!-- 有效标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="enable_flag"
              :label="$t('lang_pack.commonPage.validIdentificationt')"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudPack from '@/api/pack/pack'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  created_by: '',
  creation_date: '',
  last_updated_by: '',
  last_update_date: '',
  recipe_id: '',
  model_type: '',
  model_version: '',
  array_type: '',
  bd_type: '',
  m_length: '',
  m_width: '',
  m_tickness: '',
  m_weight: '',
  array_length: 0,
  bd_length: 0,
  array_case: '',
  bd_case: '',
  split_rules: '',
  order_lot_split_rule: '',
  custom_model_split_rule: '',
  array_lot_split_rule: '',
  array_model_split_rule: '',
  array_cycle_split_rule: '',
  bd_lot_split_rule: '',
  bd_model_split_rule: '',
  bd_cycle_split_rule: '',
  bd_index_split_rule: '',
  bd_index_start: '',
  bd_index_incre: '',
  enable_flag: 'Y',
  order_lot_split_rule1: '',
  order_lot_split_rule2: '',
  order_lot_split_rule3: '',
  custom_model_split_rule1: '',
  custom_model_split_rule2: '',
  custom_model_split_rule3: '',
  array_lot_split_rule1: '',
  array_lot_split_rule2: '',
  array_lot_split_rule3: '',
  array_model_split_rule1: '',
  array_model_split_rule2: '',
  array_model_split_rule3: '',
  array_cycle_split_rule1: '',
  array_cycle_split_rule2: '',
  array_cycle_split_rule3: '',
  bd_lot_split_rule1: '',
  bd_lot_split_rule2: '',
  bd_lot_split_rule3: '',
  bd_model_split_rule1: '',
  bd_model_split_rule2: '',
  bd_model_split_rule3: '',
  bd_cycle_split_rule1: '',
  bd_cycle_split_rule2: '',
  bd_cycle_split_rule3: '',
  bd_index_split_rule1: '',
  bd_index_split_rule2: '',
  bd_index_split_rule3: ''
}
export default {
  name: 'CARTASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.formulaMainten'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['creation_date desc'],
      // CRUD Method
      crudMethod: { ...crudPack },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['ENABLE_FLAG', 'QR_TYPE', 'QR_CASE', 'QR_DIRECT'],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 235,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      splitRuleNames: [
        // 'order_lot', // 订单号截取批次规则
        // 'custom_model', // 客户料号截取料号规则
        'array_set', // SET截取SET板件号规则
        'array_lot', // SET截取批次规则
        // 'array_model', // SET截取料号规则
        'array_cycle', // SET截取周期规则
        'array_dc', // SET截取DateCode规则
        'array_ln', // SET截取LotNum规则
        'bd_set', // PCS截取SET板件号规则
        'bd_lot', // PCS截取批次规则
        // 'bd_model', // PCS截取料号规则
        'bd_cycle', // PCS截取周期规则
        'bd_dc', // PCS截取DateCode规则
        'bd_ln', // SET截取LotNum规则
        'bd_index' // PCS截取序号规则
      ],
      rules: {
        model_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        model_version: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        array_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        bd_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        m_length: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        m_width: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        m_tickness: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        m_weight: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        array_length: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        bd_length: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        array_case: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        bd_case: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      }
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 235
    }
  },
  created: function() {
    // for (let i = 0; i < this.splitRuleNames.length; i++) {
    //   this.rules[this.splitRuleNames[i] + '_split_rule'] = [{ validator: this.validateRule, trigger: 'blur' }]
    // }
  },
  methods: {
    indexMethods(index) {
      return (this.crud.page.page - 1) * this.crud.page.size + index + 1
    },
    handleInput(value) {
      const reg = /^\d+(\.\d{0,1})?$/ // 验证数字及一位小数的正则表达式
      if (!reg.test(this.form[value])) {
        // 输入不符合要求，清空输入框
        this.form[value] = ''
      }
    },
    BlurText(e) {
      const boolean = new RegExp('^[0-9][0-9]*$').test(e.target.value)
      if (!boolean) {
        this.$message.warning(this.$t('lang_pack.vie.cannotBeEmptyOrAPositiveInteger'))
        e.target.value = ''
      }
    },
    validateRule(rule, value, callback) {
      // const { order_lot_split_rule1, order_lot_split_rule2, order_lot_split_rule3,
      // } = this.form;

      const { [rule.field + '1']: val1, [rule.field + '2']: val2, [rule.field + '3']: val3 } = this.form
      if (!(new RegExp('^[0-9][0-9]*$').test(val2))) {
        this.form[rule.field + '2'] = ''
      }
      if (!(new RegExp('^[0-9][0-9]*$').test(val3))) {
        this.form[rule.field + '3'] = ''
      }
      if (val1 || val2 || val3) {
        if ((val2 || val2 === 0) && (val3 || val3 === 0) && val1) {
          if (val2 >= 0 && val3 >= 0) {
            callback()
          } else {
            callback(new Error(this.$t('lang_pack.vie.otherValuesCannotBeEmpty')))
          }
        } else {
          callback(new Error(this.$t('lang_pack.vie.otherValuesCannotBeEmpty')))
        }
      } else {
        callback()
      }
    },
    validateRule2(rule, value, callback) {
      const {
        custom_model_split_rule1, custom_model_split_rule2, custom_model_split_rule3
      } = this.form
      if (custom_model_split_rule1 || custom_model_split_rule2 || custom_model_split_rule3) {
        if ((custom_model_split_rule2 || custom_model_split_rule2 === 0) && (custom_model_split_rule3 || custom_model_split_rule3 === 0) && custom_model_split_rule1) {
          if (custom_model_split_rule2 >= 0 && custom_model_split_rule3 >= 0) {
            callback()
          } else {
            callback(new Error(this.$t('lang_pack.vie.otherValuesCannotBeEmpty')))
          }
        } else {
          callback(new Error(this.$t('lang_pack.vie.otherValuesCannotBeEmpty')))
        }
      } else {
        callback()
      }
    },
    changeEnabled(data, val) {
      this.$confirm(this.$t('lang_pack.vie.changeTo') + '【' + (data.enable_flag === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          crudPack
            .edit({
              user_name: Cookies.get('userName'),
              recipe_id: data.recipe_id,
              enable_flag: val
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
              } else {
                this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch(ex => {
              this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    // 查询成功后的回调
    [CRUD.HOOK.afterRefresh](crud) {
      for (let i = 0; i < crud.data.length; i++) {
        const item = crud.data[i]
        if (item.split_rules) {
          const splitRules = JSON.parse(item.split_rules)
          for (const key in splitRules) {
            if (splitRules[key]) {
              const key1 = key + '1'
              const key2 = key + '2'
              const key3 = key + '3'
              this.$set(item, key, splitRules[key])
              this.$set(item, key1, splitRules[key].split('|')[0])
              this.$set(item, key2, splitRules[key].split('|')[1])
              this.$set(item, key3, splitRules[key].split('|')[2])
            }
          }
        }
      }
    },
    // 开始 "编辑" - 之前
    [CRUD.HOOK.beforeToEdit](crud) {
      const arr = this.splitRuleNames
      for (const field of arr) {
        for (let i = 1; i <= 3; i++) {
          const key = `${field}${i}`
          this.form[key] = crud.form[field] ? crud.form[field].split('|')[i - 1] : ''
        }
      }
      return true
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      const splitRules = {}
      for (let i = 0; i < this.splitRuleNames.length; i++) {
        const key = this.splitRuleNames[i] + '_split_rule'
        const key1 = key + '1'
        const key2 = key + '2'
        const key3 = key + '3'
        splitRules[key] = (this.form[key1] &&
        (this.form[key2] || parseInt(this.form[key2]) === 0) &&
        (this.form[key3] || parseInt(this.form[key3]) === 0))
          ? (this.form[key1] + '|' + this.form[key2] + '|' + this.form[key3])
          : ''
        crud.form[key] = splitRules[key]
      }
      this.form.split_rules = JSON.stringify(splitRules)
      crud.form.split_rules = this.form.split_rules
      return true
    }
  }
}
</script>
<style scoped lang="less">
.subInput{
    width: 90px !important;
    margin:0 10px;
}
.el-form-item-type{
    width:100%;
    span{
        font-size: 12px;
        color: #5f5f5f;
    }
}
</style>
