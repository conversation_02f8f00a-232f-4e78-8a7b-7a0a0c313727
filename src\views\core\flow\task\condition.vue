<template>
  <div>
    <el-form ref="query" :inline="false" size="small">
      <el-form-item :label="$t('lang_pack.mainmain.conditionsSet')+'：'">
        <el-select v-model="currentGroupId" filterable clearable style="width:300px;" @change="groupChange">
          <el-option v-for="item in conditionGroupData" :key="item.step_condition_g_id" :label="item.step_condition_g_des" :value="item.step_condition_g_id" />
        </el-select>
      </el-form-item>
    </el-form>

    <el-row :gutter="20" style="margin-top:10px;">
      <el-col :span="24">
        <!--表格渲染-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="250px" @selection-change="crud.selectionChangeHandler">
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="step_condition_i_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="step_condition_i_des" :label="$t('lang_pack.mainmain.conditionsDescribed')" />
          <el-table-column  :show-overflow-tooltip="true" prop="tag_id" :label="$t('lang_pack.mainmain.monitoringPoint')" />
          <el-table-column  :show-overflow-tooltip="true" prop="ok_remarks" :label="$t('lang_pack.mainmain.setUpInstructions')" />
          <el-table-column  :label="$t('lang_pack.commonPage.validIdentification')" prop="enable_flag">
            <template slot-scope="scope">
              {{ scope.row.enable_flag === 'Y' ? $t('lang_pack.vie.effective') : $t('lang_pack.vie.invalid') }}
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudFlowMainConditionG from '@/api/core/flow/rcsFlowMainConditionG'
import crudFlowMainConditionI from '@/api/core/flow/rcsFlowMainConditionI'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
// import crudOperation from "@crud/CRUD.operation";
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  step_condition_i_id: 0,
  step_condition_g_id: 0,
  step_condition_i_des: '',
  tag_id: '',
  ok_remarks: '',
  enable_flag: 'Y'
}
export default {
  name: 'RCS_FLOW_MAIN_CONDITION_I',
  components: { udOperation, pagination },
  props: {
    flow_main_id: {
      type: [String, Number],
      default: -1
    },
    step_mod_id: {
      type: [String, Number],
      default: -1
    }
  },
  cruds() {
    return CRUD({
      title: '属性',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'step_condition_i_id',
      // 排序
      sort: ['step_condition_i_id asc'],
      // CRUD Method
      crudMethod: { ...crudFlowMainConditionI },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: false,
        reset: false,
        buttonGroup: false
      },
      query: {
        step_condition_g_id: 0
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'rcs_flow_main_condition_i:add'],
        edit: ['admin', 'rcs_flow_main_condition_i:edit'],
        del: ['admin', 'rcs_flow_main_condition_i:del']
      },
      currentGroupId: '',
      conditionGroupData: []
    }
  },
  watch: {},

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    crudFlowMainConditionG
      .sel({
        user_name: Cookies.get('userName'),
        flow_main_id: this.flow_main_id,
        step_mod_id: this.step_mod_id
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.conditionGroupData = defaultQuery.data
          }
        }
        this.loading = false
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.mainmain.abnormalData'),
          type: 'error'
        })
      })
  },
  methods: {
    groupChange(val) {
      if (val === '') {
        val = '0'
      }
      this.query.step_condition_g_id = val
      this.crud.toQuery()
    }
  }
}
</script>
