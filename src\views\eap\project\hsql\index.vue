<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard header">
      <el-header>
        <div class="statuHead">
          <div>
            <el-popover placement="right" width="170" trigger="click">
              <el-button
                v-for="(item, index) in [
                  {
                    tag_key: 'PmPlc/PlcStatus/ControlMode',
                    tag_value: '1',
                    label: '离线模式',
                  },
                  {
                    tag_key: 'PmPlc/PlcStatus/ControlMode',
                    tag_value: '2',
                    label: '在线/本地',
                  },
                  {
                    tag_key: 'PmPlc/PlcStatus/ControlMode',
                    tag_value: '3',
                    label: '在线/远程',
                  },
                ]"
                :key="index"
                size="medium"
                type="primary"
                style="font-size: 20px"
                :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                @click="handleWrite(item.tag_key, item.tag_value)"
                >{{ item.label }}</el-button
              ><br />
              <el-button
                slot="reference"
                :class="
                  monitorData.ControlMode.value === '2' ||
                  monitorData.ControlMode.value === '3'
                    ? 'btnone'
                    : 'btnone0'
                "
                >{{
                  controlData[monitorData.ControlMode.value] || "离线模式"
                }}</el-button
              >
            </el-popover>
            <el-popover placement="right" width="200" trigger="click">
              <el-button
                v-for="(item, index) in [
                  {
                    tag_key: 'PmPlc/PlcStatus/ProductMode',
                    tag_value: '0',
                    label: '量产模式',
                  },
                  {
                    tag_key: 'PmPlc/PlcStatus/ProductMode',
                    tag_value: '1',
                    label: '首件模式',
                  },
                  {
                    tag_key: 'PmPlc/PlcStatus/ProductMode',
                    tag_value: '2',
                    label: 'Dummy模式',
                  },
                ]"
                :key="index"
                size="medium"
                type="primary"
                style="font-size: 20px"
                :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                @click="handleWrite(item.tag_key, item.tag_value)"
                >{{ item.label }}</el-button
              ><br />
              <el-button
                slot="reference"
                :class="
                  monitorData.ProductMode.value === '1' ||
                  monitorData.ProductMode.value === '2'
                    ? 'btnone'
                    : 'btnone0'
                "
                >{{
                  controlStatusData[monitorData.ProductMode.value].label ||
                  "量产模式"
                }}</el-button
              >
            </el-popover>
          </div>
          <div>
            <div class="wrappstyle">
              <p>
                <span
                  :class="
                    monitorData.LightGreen.value === '1'
                      ? 'wholeline1 wholelinenormal1'
                      : monitorData.LightYellow.value === '1'
                      ? 'wholeline1 wholelineerror1'
                      : monitorData.LightRed.value === '1'
                      ? 'wholeline1 deviceRed'
                      : 'wholeline1 wholelinegray1'
                  "
                />
                <span class="statuText">{{
                  $t("lang_pack.vie.triColorLight")
                }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.PlcHeartBeat.value === '1'
                      ? 'wholeline wholelinenormal'
                      : monitorData.PlcHeartBeat.value === '0'
                      ? 'wholeline wholelineerror'
                      : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">PLC心跳</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.UpLink.value === '0'
                      ? 'wholeline wholelinenormal'
                      : monitorData.UpLink.value === '1'
                      ? 'wholeline wholelineerror'
                      : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">上游</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.DownLink.value === '0'
                      ? 'wholeline wholelinenormal'
                      : monitorData.DownLink.value === '1'
                      ? 'wholeline wholelineerror'
                      : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">下游</span>
              </p>
            </div>
          </div>
        </div>
      </el-header>
    </el-card>
    <el-row
      :gutter="20"
      style="margin-right: 0px; padding: 0px; margin-top: 10px"
    >
      <el-col :span="16" style="padding-right: 0">
        <el-col :span="24" style="padding: 0">
          <!-- <el-col :span="12" style="padding: 0 5px 0 0;"> -->
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="80px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12 barcode">
                    <el-form-item label="工单：">
                      <el-input
                        ref="lot_no"
                        v-model="lot_no"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                    <el-button
                      type="primary"
                      style="width: 80px"
                      @click="manInput()"
                      >扫描</el-button
                    >
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="数量：">
                      <el-input
                        ref="lot_count"
                        v-model="lot_count"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="料号：">
                      <el-select
                        v-model="query.client_code"
                        filterable
                        size="mini"
                        style="width: 200px"
                        :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                        @change="clientCodeSelect"
                      >
                        <el-option
                          v-for="(item, index) in materialData"
                          :key="index"
                          :label="item.material_code"
                          :value="item.material_code"
                        >
                          <span
                            style="float: left; color: #8492a6; font-size: 13px"
                            >{{ item.material_code }}</span
                          >
                          <span style="float: right">{{
                            item.material_des
                          }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="员工号：">
                      <el-input
                        ref="user.nickName"
                        v-model="user.nickName"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>生产信息</span>
            </div>
            <el-table ref="table" border :data="productData" :height="height">
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="LotID"
                label="lot号"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="PartNo"
                width="130"
                label="料号"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="PanelID"
                label="板号"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="RecipeCode"
                label="配方名称"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras150"
                label="板长"
              />

              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras151"
                label="板宽"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras1"
                width="150"
                label="塞孔1加热开关"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras2"
                width="130"
                label="塞孔2加热开关"
              />

              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras3"
                width="130"
                label="内外置供墨"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras4"
                width="130"
                label="大气真空模式"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras5"
                label="自动涂墨"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras6"
                width="120"
                label="单双面涂墨"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras7"
                label="联机"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras8"
                label="EIP启用"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras8"
                label="照明开关"
              />

              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras10"
                label="多段压力"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras11"
                label="油墨检查"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras28"
                width="130"
                label="外部急停中"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras29"
                width="130"
                label="上下料急停中"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras31"
                width="180"
                label="外围移栽光幕遮挡中"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="Paras32"
                label="检板功能"
              />
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <el-card shadow="never" class="wrapCard">
              <div slot="header" class="wrapTextSelect">
                <span>报警信息</span>
              </div>
              <el-table
                ref="table"
                border
                :data="alarmData"
                :row-key="(row) => row.id"
                height="200"
              >
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="station_code"
                  label="工位"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_code"
                  label="实例编号"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_des"
                  label="实例描述"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_code"
                  label="报警代码"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_level"
                  label="报警级别"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_des"
                  label="报警描述"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  label="报警时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_date"
                  label="复位时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_flag"
                  label="是否复位"
                >
                  <template slot-scope="scope">
                    {{ scope.row.reset_flag === "Y" ? "已复位" : "待复位" }}
                  </template>
                </el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="simulated_flag"
                  label="是否模拟"
                >
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === "Y" ? "是" : "否" }}
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-card></el-col
        >
      </el-col>
      <el-col :span="8" style="padding-right: 0">
        <el-card shadow="never" class="wrapCard">
          <div class="pieChart">
            <div id="capacityDom" />
            <!-- 产能 -->
            <div id="oeeDom" />
            <!-- oee -->
            <div id="readbitRateDom" />
            <!-- 读码率 -->
          </div>
          <el-table
            ref="table"
            border
            :data="plcCraftData"
            :row-key="(row) => row.id"
            :height="gatherHeight"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              label="项目名称"
              prop="tag_des"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="tag_value"
              label="当前值"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="unit"
              label="单位"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="upper_limit"
              label="上限"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="down_limit"
              label="下限"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="status"
              label="状态"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :show-close="true"
      title="修改配方"
      width="80%"
      :visible.sync="dialogVisible"
      class="dialogTable"
    >
      <div
        style="
          margin-bottom: 10px;
          display: flex;
          justify-content: right;
          align-items: center;
        "
      >
        <span>是否修改参数：</span>
        <el-switch
          v-model="disabled"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </div>
      <el-table
        ref="table"
        v-loading="crud.loading"
        border
        size="small"
        :data="crud.data"
        style="width: 100%"
        :cell-style="crud.cellStyle"
        height="478"
        max-height="478"
        highlight-current-row
        @header-dragend="crud.tableHeaderDragend()"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="45" align="center" />
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column
          v-if="1 == 0"
          width="10"
          prop="recipe_detail_id"
          label="id"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="parameter_code"
          label="参数编码"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_des"
          label="参数描述"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_val"
          label="参数值"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.parameter_val"
              :disabled="handleDisabled(scope.row.parameter_val)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="有效标识"
          align="center"
          prop="enable_flag"
          width="100"
        >
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleOk">确定下发</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { sel as eapRecipeSel, getWorkOrderInfo as selOrderInfo} from "@/api/eap/project/sfjd/eapRecipe";
import { selScadaTag } from "@/api/core/scada/tag";
import crudEapRecipeDetail from "@/api/eap/project/sfjd/eapRecipeDetail";
import { scadaTagGroupTree } from "@/api/core/scada/tagGroup";
import { sel as selMaterial } from "@/api/eap/project/sfjd/eapMaterial";
import CRUD, { presenter, header, form, crud } from "@crud/crud";
import axios from "axios";
import { selCellIP } from "@/api/core/center/cell";
import Cookies from "js-cookie";
import mqtt from "mqtt";
import { MQTT_USERNAME, MQTT_PASSWORD } from "@/utils/emq.js";
import { mapGetters } from 'vuex'
const defaultForm = {};
export default {
  name: "shRecipeMain",
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 608,
      gatherHeight: document.documentElement.clientHeight - 424,
      lot_no: "",
      material_code: "",
      lot_count: "0",
      current_user: "",
      recipeData: [],
      capacityDom: null,
      oeeDom: null,
      readbitRateDom: null,
      capacityOption: {
        title: {
          show: true,
          text: "100%",
          itemGap: 10,
          x: "center",
          y: "30%",
          subtext: "产能",
          textStyle: {
            fontSize: 24,
            color: "#999999",
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: "bold",
            color: "#333333",
          },
        },
        color: ["#6df320", "#d2e312"],
        tooltip: {
          backgroundColor: "#fff",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);",
          textStyle: {
            color: "#000",
          },
        },
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "90%"],
            center: ["50%", "40%"],
            label: {
              // 鼠标悬浮具体数据显示
              show: false,
            },
            data: [
              { value: 335, name: "脱岗" },
              { value: 234, name: "在岗" },
            ],
          },
        ],
      },
      oeeOption: {
        title: {
          show: true,
          text: "100%",
          itemGap: 10,
          x: "center",
          y: "30%",
          subtext: "OEE",
          textStyle: {
            fontSize: 24,
            color: "#999999",
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: "bold",
            color: "#333333",
          },
        },
        color: ["#409EFF", "#40e2ff"],
        tooltip: {
          backgroundColor: "#fff",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);",
          textStyle: {
            color: "#000",
          },
        },
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "90%"],
            center: ["50%", "40%"],
            label: {
              // 鼠标悬浮具体数据显示
              show: false,
            },
            data: [
              { value: 335, name: "脱岗" },
              { value: 234, name: "在岗" },
            ],
          },
        ],
      },
      readbitRateOption: {
        title: {
          show: true,
          text: "100%",
          itemGap: 10,
          x: "center",
          y: "30%",
          subtext: "读码率",
          textStyle: {
            fontSize: 24,
            color: "#999999",
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: "bold",
            color: "#333333",
          },
        },
        color: ["#9d9727", "#c25b1f"],
        tooltip: {
          backgroundColor: "#fff",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);",
          textStyle: {
            color: "#000",
          },
        },
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "90%"],
            center: ["50%", "40%"],
            label: {
              // 鼠标悬浮具体数据显示
              show: false,
            },
            data: [
              { value: 335, name: "脱岗" },
              { value: 234, name: "在岗" },
            ],
          },
        ],
      },
      dialogVisible: false,
      rules: {
        material_code: [
          { required: true, message: "请输入物料编码", trigger: "blur" },
        ],
        material_des: [
          { required: true, message: "请输入物料描述", trigger: "blur" },
        ],
      },
      disabled: false,
      controlStatus: {
        light_status: "",
        device_plc_status: "",
        ais_status: "0",
        plc_status: "0",
        eap_status: "0",
      },
      controlData: {
        1: "离线模式",
        2: "在线/本地",
        3: "在线/远程",
      },
      controlStatusData: [
        { id: "0", label: "量产模式" },
        { id: "1", label: "首件模式" },
        { id: "2", label: "Dummy模式" },
      ],
      monitorData: {
        UpLink: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "UpLink",
          tag_des: "	[PlcStatus]上游设备连接",
          value: "0",
        },
        DownLink: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "DownLink",
          tag_des: "[PlcStatus]下游设备连接",
          value: "0",
        },
        PlcHeartBeat: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "PlcHeartBeat",
          tag_des: "[PlcStatus]PLC心跳",
          value: "0",
        },
        ElecConSum: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "ElecConSum",
          tag_des: "[PlcStatus]设备用电量",
          value: "0",
        },
        LightGreen: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "LightGreen",
          tag_des: "[PlcStatus]三色灯绿",
          value: "0",
        },
        LightYellow: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "LightYellow",
          tag_des: "[PlcStatus]三色灯红",
          value: "0",
        },
        LightRed: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "LightRed",
          tag_des: "[PlcStatus]设备用电量",
          value: "0",
        },
        ControlMode: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "ControlMode",
          tag_des: "[PlcStatus]设备控制状态(1:离线,2:在线/本地,3:在线/远程)",
          value: "0",
        },
        ProductMode: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "ProductMode",
          tag_des: "[PlcStatus]设备生产模式(0:量产,1:首件,2:Dummy)",
          value: "0",
        },
        RecipeUpd: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "RecipeUpd",
          tag_des: "[PlcStatus]配方修改",
          value: "0",
        },
        RecipeSelFinish: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "RecipeSelFinish",
          tag_des: "[PlcStatus]配方选择完成",
          value: "0",
        },
      },
      cellIp: "", // 单元IP
      webapiPort: "", // 单元API端口号
      mqttPort: "", // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: "/mqtt",
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          "ScadaEsb_" +
          Cookies.get("userName") +
          "_" +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false,
      },
      alarmData: [],
      materialData: [],
      timer: null,
      groupData: [],
      productObj: {},
      productData: [],
      ArrTag: [
        "LotID",
        "PartNo",
        "PanelID",
        "RecipeCode",
        "Paras1",
        "Paras2",
        "Paras3",
        "Paras4",
        "Paras5",
        "Paras6",
        "Paras7",
        "Paras8",
        "Paras9",
        "Paras10",
        "Paras11",
        "Paras28",
        "Paras29",
        "Paras31",
        "Paras32",
        "Paras150",
        "Paras151",
      ],
      // ArrTag2: ['RecipeCode', 'Paras191', 'Paras192', 'Paras197', 'Paras198', 'Paras200'],
      // plcCraftObj: {},
      plcCraftData: [],
    };
  },
  cruds() {
    return CRUD({
      title: "配方维护",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "recipe_detail_id ",
      // 排序
      sort: ["recipe_detail_id asc"],
      // CRUD Method
      crudMethod: { ...crudEapRecipeDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true,
      }
    });
  },
  dicts: ["PROJECT_PARAMS_WARNING"],
  computed: {
    ...mapGetters(['user'])
  },
  mounted: function () {
    const query = {
      client_id: 1010,
      enable_flag: "Y",
      sort: "tag_group_id",
      user_name: Cookies.get("userName"),
    };
    scadaTagGroupTree(query).then((res) => {
      if (res.data.length > 0) {
        const Arr = ["101003", "101004"];
        Arr.forEach((temp) => {
          const data = res.data.find((item) => item.tag_group_id === temp);
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `PmPlc/${e.tag_group_code}/${e.tag_code}`,
                value: "",
              });
            });
          }
        });
      }
    });
    const params = {
      tableOrder: "asc",
      tableOrderField: "tag_id",
      tablePage: 1,
      tableSize: 1000,
      tag_group_id: 101003,
      user_name: Cookies.get("userName"),
    };
    selScadaTag(params).then((res) => {
      if (res.code === 0) {
        res.data.forEach((item) => {
          this.dict.PROJECT_PARAMS_WARNING.forEach((e) => {
            if (`PmPlc/${item.tag_attr}/${item.tag_code}` === e.value) {
              const result = {
                tag_des: item.tag_des,
                tag_key: `PmPlc/${item.tag_attr}/${item.tag_code}`,
                tag_value: "",
                unit: "",
                down_limit: item.down_limit,
                upper_limit: item.upper_limit,
                status: "",
              };
              this.plcCraftData.push(result);
            }
          });
        });
      }
    });
    const that = this;
    this.getMaterialInfo();
    this.timer = setInterval(this.getAlarmData, 15000);
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 608;
      that.gatherHeight = document.documentElement.clientHeight - 424;
    };
    this.$nextTick(() => {
      this.getCapacity();
      this.getOee();
      this.getreadbitRate();
    });
 },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  created() {
    this.getCellIp();
  },
  methods: {
    getMaterialInfo() {
      const query = {
        page: 1,
        size: 1000,
        sort: "material_id desc",
        user_name: Cookies.get("userName"),
      };
      selMaterial(query)
        .then((res) => {
          if (res.code === 0) {
            if (res.data.length > 0) {
              this.materialData = res.data;
              return;
            }
            this.materialData = [];
            this.$message({
              type: "warning",
              message: "当前未获取到物料，请先去维护物料",
            });
          }
        })
        .catch((error) => {
          this.materialData = [];
          this.$message({ type: "error", message: "查询失败" + error.msg });
        });
    },
    clientCodeSelect(e) {
      const query = {
        page: 1,
        size: 10,
        sort: "recipe_id asc",
        enable_flag: "Y",
        material_code: e,
      };
      eapRecipeSel(query)
        .then((res) => {
          if (res.code === 0) {
            if (res.data.length > 0) {
              this.query.recipe_id = res.data[0].recipe_id;
              this.query.size = 1000;
              this.$nextTick(() => {
                this.crud.toQuery();
                this.dialogVisible = true;
              });
              return;
            }
            this.$message({
              type: "error",
              message: `未能根据当前物料号【${e}】查询到相关配方信息`,
            });
          }
        })
        .catch((error) => {
          this.$message({ type: "error", message: "查询失败" + error.msg });
        });
    },
    manInput() {
      if (!this.lot_no) {
        this.$message({ type: "warning", message: "请先扫描或输入工单" });
        return;
      }
      var queryParameter = {
        user_name: Cookies.get("userName"),
        station_code: this.$route.query.station_code,
        lot_no: this.lot_no,
        enable_flag: "Y"
      };
      selOrderInfo(queryParameter)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== "") {
              this.crud.data = defaultQuery.data;
            }
            this.dialogVisible = true;
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t("lang_pack.vie.queryException"),
            type: "error",
          });
        });
    },
    choiceLotNum() {
      this.dialogVisible = true;
    },
    handleDisabled(value) {
      if (this.disabled) return false;
      const Arr = ["null", "", "undefined", "0"];
      if (!value || Arr.includes(value)) return true;
    },
    handleWrite(key, value) {
      var sendJson = {};
      var rowJson = [];
      var newRow = {
        TagKey: key,
        TagValue: value,
      };
      rowJson.push(newRow);
      sendJson.Data = rowJson;
      sendJson.ClientName = "SCADA_WEB";
      var sendStr = JSON.stringify(sendJson);
      var topic = "SCADA_WRITE/" + key.split("/")[0];
      this.sendMessage(topic, sendStr);
    },
    handleOk() {
      //1.请求下发配方
      var sendJson = {};
      var rowJson = [];
      var newRow = {
        TagKey: "PmPlc/PcStatus/RecipeDownReq",
        TagValue: "1",
      };
      rowJson.push(newRow);
      sendJson.Data = rowJson;
      sendJson.ClientName = "SCADA_WEB";
      var sendStr = JSON.stringify(sendJson);
      var topic = "SCADA_WRITE/PmPlc";
      this.sendMessage(topic, sendStr);
      //2.读取plc修改配方，判断是否允许下发配方
      if (this.monitorData.RecipeUpd.value === "0") {
        this.dialogVisible = false;
        this.$message({ type: "info", message: "设备运行中，不允许下发配方" });
        var rowJson = [];
        var newRow = {
          TagKey: "PmPlc/PcStatus/RecipeDownReq",
          TagValue: "0",
        };
        rowJson.push(newRow);
        sendJson.Data = rowJson;
        sendJson.ClientName = "SCADA_WEB";
        var sendStr = JSON.stringify(sendJson);
        var topic = "SCADA_WRITE/PmPlc";
        this.sendMessage(topic, sendStr);
        return;
      }

      //3.下发配方信息
      this.crud.data.forEach((item) => {
        var sendJson = {};
        var rowJson = [];
        var newRow = {
          TagKey: item.tag_key,
          TagValue: item.parameter_val,
        };
        rowJson.push(newRow);
        sendJson.Data = rowJson;
        sendJson.ClientName = "SCADA_WEB";
        var sendStr = JSON.stringify(sendJson);
        var topic = "SCADA_WRITE/" + item.client_code;
        this.sendMessage(topic, sendStr);
      });
      //4.下发完成复位请求下发信号
      var rowJson = [];
      var newRow = {
        TagKey: "PmPlc/PcStatus/RecipeDownReq",
        TagValue: "0",
      };
      rowJson.push(newRow);
      sendJson.Data = rowJson;
      sendJson.ClientName = "SCADA_WEB";
      var sendStr = JSON.stringify(sendJson);
      var topic = "SCADA_WRITE/PmPlc";
      this.sendMessage(topic, sendStr);
      setTimeout(() => {
        this.dialogVisible = false;
        this.$message({ type: "success", message: "下发配方成功" });
      }, 500);
    },
    getCapacity() {
      this.capacityDom = this.$echarts.init(
        document.getElementById("capacityDom")
      );
      var that = this;
      this.capacityDom.setOption(this.capacityOption);
      window.addEventListener("resize", function () {
        that.capacityDom.resize();
      });
    },
    getOee() {
      this.oeeDom = this.$echarts.init(document.getElementById("oeeDom"));
      var that = this;
      this.oeeDom.setOption(this.oeeOption);
      window.addEventListener("resize", function () {
        that.oeeDom.resize();
      });
    },
    getreadbitRate() {
      this.readbitRateDom = this.$echarts.init(
        document.getElementById("readbitRateDom")
      );
      var that = this;
      this.readbitRateDom.setOption(this.readbitRateOption);
      window.addEventListener("resize", function () {
        that.readbitRateDom.resize();
      });
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get("userName"),
        cell_id: 1,
        current_ip: window.location.hostname,
      };
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result);
            this.cellIp = ipInfo.ip;
            this.webapiPort = ipInfo.webapi_port;
            this.mqttPort = ipInfo.mqtt_port;
            setTimeout(() => {
              this.toStartWatch();
              this.getTagValue();
              this.getAlarmData();
            }, 1000);
          } else {
            this.$message({ message: defaultQuery.msg, type: "error" });
          }
        })
        .catch(() => {
          this.$message({ message: "查询异常", type: "error" });
        });
    },
    getAlarmData() {
      var method = "/cell/core/scada/CoreScadaAlarmSelect";
      var path = "";
      if (process.env.NODE_ENV === "development") {
        path = "http://localhost:" + this.webapiPort + method;
      } else {
        path = "http://" + this.cellIp + ":" + this.webapiPort + method;
      }
      var queryData = {
        tablePage: 1,
        tableSize: 10,
      };
      axios
        .post(path, queryData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data;
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: "查询异常：" + ex, type: "error" });
        });
    },
    getTagValue() {
      var readTagArray = [];
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code;
        var group_code = this.monitorData[key].group_code;
        var tag_code = this.monitorData[key].tag_code;
        if (this.aisMonitorMode === "AIS-SERVER") {
          client_code = client_code + "_" + this.$route.query.station_code;
        }
        var readTag = {};
        readTag.tag_key = client_code + "/" + group_code + "/" + tag_code;
        readTagArray.push(readTag);
      });
      this.ArrTag.forEach((item) => {
        readTagArray.push({
          tag_key: "PmPlc/PlcCraft" + "/" + item,
        });
      });
      this.plcCraftData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key,
        });
      });
      var method = "/cell/core/scada/CoreScadaReadTag";
      var path = "";
      if (process.env.NODE_ENV === "development") {
        path = "http://localhost:" + this.webapiPort + method;
      } else {
        path = "http://" + this.cellIp + ":" + this.webapiPort + method;
      }
      axios
        .post(path, readTagArray, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data;
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code;
                var group_code = this.monitorData[key].group_code;
                var tag_code = this.monitorData[key].tag_code;
                if (this.aisMonitorMode === "AIS-SERVER") {
                  client_code =
                    client_code + "_" + this.$route.query.station_code;
                }
                var tag_key = client_code + "/" + group_code + "/" + tag_code;
                const item = result.filter((item) => item.tag_key === tag_key);
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? "" : item[0].tag_value;
                }
              });
              result.forEach((e) => {
                this.ArrTag.forEach((item) => {
                  if ("PmPlc/PlcCraft" + "/" + item === e.tag_key) {
                    this.productObj[item] = e.tag_value;
                  }
                });
                this.plcCraftData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.tag_value = e.tag_value;
                    if (
                      typeof +e.tag_value === "number" &&
                      +e.tag_value >= item.down_limit &&
                      +e.tag_value <= item.upper_limit
                    ) {
                      item.status = "OK";
                    } else {
                      item.status = "NG";
                    }
                  }
                });
              });
              this.productData = [this.productObj];
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: "查询异常：" + ex, type: "error" });
        });
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end();
        this.mqttConnStatus = false;
      }
      var connectUrl = "ws://" + this.cellIp + ":" + this.mqttPort + "/mqtt";
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt); // 开启连接
      this.clientMqtt.on("connect", (e) => {
        this.mqttConnStatus = true;
        var aisClientCode = "PmAis";
        var plcClientCode = "PmPlc";
        var eapClientCode = "PmEap";
        this.topicSubscribe("SCADA_STATUS/" + aisClientCode);
        this.topicSubscribe("SCADA_BEAT/" + aisClientCode);
        this.topicSubscribe("SCADA_STATUS/" + plcClientCode);
        this.topicSubscribe("SCADA_BEAT/" + plcClientCode);
        this.topicSubscribe("SCADA_STATUS/" + eapClientCode);
        this.topicSubscribe("SCADA_BEAT/" + eapClientCode);
        this.groupData.forEach((item) => {
          this.topicSubscribe("SCADA_CHANGE/" + item.tag_key);
        });
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code;
          var group_code = this.monitorData[key].group_code;
          var tag_code = this.monitorData[key].tag_code;
          if (this.aisMonitorMode === "AIS-SERVER") {
            client_code = client_code + "_" + this.$route.query.station_code;
          }
          this.topicSubscribe(
            "SCADA_CHANGE/" + client_code + "/" + group_code + "/" + tag_code
          );
        });
        this.$message({
          message: "连接成功",
          type: "success",
        });
      //记录当前登录账户
      this.handleWrite("PmPlc/PlcCraft/Spare1", this.user.nickName);
      });

      // MQTT连接失败
      this.clientMqtt.on("error", () => {
        this.$message({
          message: "连接失败",
          type: "error",
        });
        this.clientMqtt.end();
      });
      // 断开发起重连(异常)
      this.clientMqtt.on("reconnect", () => {
        this.$message({
          message: "连接断开，正在重连。。。",
          type: "error",
        });
      });
      this.clientMqtt.on("disconnect", () => {});
      this.clientMqtt.on("close", () => {});
      // 接收消息处理
      this.clientMqtt.on("message", (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message);
          if (jsonData == null) return;
          // if (topic.indexOf('SCADA_BEAT/') >= 0) {
          //   var heartBeatValue = jsonData.Beat
          //   if (topic.indexOf('SCADA_BEAT/PmEap') >= 0) {
          //     if (this.controlStatus.eap_status !== '2') {
          //       this.controlStatus.eap_status = heartBeatValue
          //     }
          //   } else if (topic.indexOf('SCADA_BEAT/PmPlc') >= 0) {
          //     if (this.controlStatus.plc_status !== '2') {
          //       this.controlStatus.plc_status = heartBeatValue
          //     }
          //   } else if (topic.indexOf('SCADA_BEAT/PmAis') >= 0) {
          //     if (this.controlStatus.ais_status !== '2') {
          //       this.controlStatus.ais_status = heartBeatValue
          //     }
          //   }
          // } else
          if (topic.indexOf("SCADA_CHANGE/") >= 0) {
            const group_code = "PmPlc/PlcCraft";
            this.ArrTag.forEach((item) => {
              if (group_code + "/" + item === jsonData.TagKey) {
                // 强制更新数组
                this.productObj = {
                  ...this.productObj,
                  [item]: jsonData.TagNewValue,
                };
                this.productData = [this.productObj];
              }
            });
            this.plcCraftData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.tag_value = jsonData.TagNewValue;
                if (
                  typeof +jsonData.TagNewValue === "number" &&
                  +jsonData.TagNewValue >= item.down_limit &&
                  +jsonData.TagNewValue <= item.upper_limit
                ) {
                  item.status = "OK";
                } else {
                  item.status = "NG";
                }
              }
            });
            Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code;
              var group_code = this.monitorData[key].group_code;
              var tag_code = this.monitorData[key].tag_code;
              if (this.aisMonitorMode === "AIS-SERVER") {
                client_code =
                  client_code + "_" + this.$route.query.station_code;
              }
              var tag_key = client_code + "/" + group_code + "/" + tag_code;
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue;
              }
            });
          }
        } catch (e) {
          console.log(e);
        }
      });
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: "请启动监控",
          type: "error",
        });
        return;
      }
      console.log(topic, msg);
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn("MQTT发送成功：" + topic);
          // this.$message({ message: '写入成功', type: 'success' })
        } else {
          this.$message({ message: "操作失败！", type: "error" });
        }
      });
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: "请启动监控",
          type: "error",
        });
        return;
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: "请启动监控",
            type: "error",
          });
          return;
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          console.log("MQTT订阅失败:" + topic);
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.app-container {
  padding: 10px;
  ::v-deep .el-header {
    padding: 0;
  }
  .header {
    ::v-deep .el-card__body {
      padding: 10px 15px 0 !important;
    }
  }
  .wrapTextSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrapElForm {
    display: flex;
    ::v-deep .barcode {
      display: flex;
      .el-form-item--small {
        width: 90%;
        margin-right: 10px;
      }
    }
  }
  .pieChart {
    width: 100%;
    display: flex;
    div {
      width: 33%;
    }
    #capacityDom {
      height: 300px;
    }
    #capacityDom {
      height: 300px;
    }
    #readbitRateDom {
      height: 300px;
    }
  }
  .active {
    ::v-deep .el-input__inner {
      background-color: #ffff00;
    }
  }
  .dialog-footer {
    text-align: center;
  }
  .statuHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrappstyle {
    display: flex;
    align-items: center;
    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 12px;
        font-weight: 700;
      }
      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }

    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }
  .btnone {
    background: #50d475;
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
    font-size: 18px;
  }

  .btnone:active {
    background: #13887c;
  }
  .wholeline {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }
  .wholeline1 {
    width: 20px;
    height: 20px;
  }
  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .dialogTable {
    ::v-deep .el-dialog {
      margin-top: 5vh !important;
    }
  }
}
</style>
