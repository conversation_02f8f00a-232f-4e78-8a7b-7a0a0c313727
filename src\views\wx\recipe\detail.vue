<template>
    <!--模组子维护-->
    <el-card shadow="never">
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap form-colum" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item label="参数编码" prop="parameter_code">
            <el-input v-model="form.parameter_code" />
          </el-form-item>
          <el-form-item label="参数描述" prop="parameter_des">
            <el-input v-model="form.parameter_des" />
          </el-form-item>
          <el-form-item label="参数值" prop="parameter_val">
            <el-input v-model="form.parameter_val" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.triggerPoint')" prop="tag_id">
            <!-- 触发点位 -->
            <el-input v-model.number="form.tag_id" readonly="readonly">
              <div slot="append">
                <el-popover v-model="customPopover" placement="left" width="650">
                  <tagSelect ref="tagSelect" :client-id-list="form.client_id_list" :tag-id="form.tag_id" @chooseTag="handleChooseTag" />
                  <el-button slot="reference">选择</el-button>
                </el-popover>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="有效标识" prop="enable_flag">
            <el-select v-model="form.enable_flag" clearable>
              <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
  
      <el-row :gutter="20">
        <el-col :span="24" class="elTableItem">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="478" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="recipe_detail_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_code" label="参数编码" />
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_des" label="参数描述" />
            <el-table-column  :show-overflow-tooltip="true" prop="parameter_val" label="参数值" />
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" label="创建时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="tag_id" label="对应Tag" />
            <el-table-column  label="有效标识" align="center" prop="enable_flag" width="100">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>
  
            <el-table-column  label="操作" width="115" align="center" fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission"  :disabled-dle="true" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </template>
  
  <script>
  import eapRecipeDetail from '@/api/wx/recipe/eapRecipeDetail'
  import Cookies from 'js-cookie'
  import CRUD, { presenter, header, form, crud } from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  const defaultForm = {
    recipe_detail_id: '',
    parameter_code: '',
    parameter_des: '',
    parameter_val: '',
    enable_flag: 'Y',
    client_id_list: '',
    tag_id: ''
  }
  export default {
    name: 'EAP_RECIPE_DETAIL',
    components: { crudOperation, rrOperation, udOperation, pagination },
    props: {
      recipe_detail_id: {
        type: [String, Number],
        default: -1
      }
    },
    cruds() {
      return CRUD({
        title: '子配方维护详情',
        // 登录用户
        userName: Cookies.get('userName'),
        // 唯一字段
        idField: 'recipe_detail_id',
        // 排序
        sort: ['recipe_detail_id asc'],
        // CRUD Method
        crudMethod: { ...eapRecipeDetail },
        // 按钮显示
        optShow: {
          add: true,
          edit: true,
          del: true,
          reset: true
        }
      })
    },
    // 数据字典
    dicts: ['ENABLE_FLAG'],
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
      return {
        height: document.documentElement.clientHeight - 200,
        permission: {
          add: ['admin', 'c_eap_fmod_recipe_pack_d:add'],
          edit: ['admin', 'c_eap_fmod_recipe_pack_d:edit'],
          del: ['admin', 'c_eap_fmod_recipe_pack_d:del'],
          down: ['admin', 'c_eap_fmod_recipe_pack_d:down']
        },
        rules: {
          tag_id: [{ required: true, message: '请选择采集项目标签', trigger: 'blur' }],
          enable_flag: [{ required: true, message: '请选择有效标识', trigger: 'blur' }]
        },
        customPopover: false
      }
    },
    watch: {
      recipe_detail_id: {
        immediate: true,
        deep: true,
        handler() {
          this.query.recipe_id = this.recipe_detail_id
          this.crud.toQuery()
        }
      }
    },
  
    mounted: function() {
      const that = this
      window.onresize = function temp() {
        that.height = document.documentElement.clientHeight - 200
      }
    },
    created: function() {},
    methods: {
      handleChooseTag(tagId) {
        this.form.tag_id = tagId
        this.customPopover = false
      },
      // 针对子页面需要在查询之前再赋值，因为重置按钮会清空值
      [CRUD.HOOK.beforeRefresh](crud) {
        crud.query.recipe_id = this.recipe_detail_id
      },
      // 提交前做的操作
      [CRUD.HOOK.beforeSubmit](crud) {
        crud.form.recipe_id = this.recipe_detail_id
        return true
      }
    }
  }
  </script>
  <style lang="less" scoped>
  .form-colum{
    flex-direction: column;
    .el-form-item{
      width: 100%;
    }
  }
  </style>
  