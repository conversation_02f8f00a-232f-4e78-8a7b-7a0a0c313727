<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="版本号：">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>

            <div class="formChild col-md-4 col-12">
              <el-form-item label="有效标识：">
                <fastCode
                  fastcode_group_code="ENABLE_FLAG"
                  :fastcode_code.sync="query.enable_flag"
                  control_type="select"
                  size="small"
                />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button
            class="filter-item"
            size="small"
            type="success"
            icon="el-icon-document"
            plain
            round
            @click="recipeDrawerVisible = true"
          >
            配方
          </el-button>
        </template>
      </crudOperation>
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="750px"
      >
        <el-form
          ref="form"
          class="el-form-wrap"
          :model="form"
          :rules="rules"
          size="small"
          label-width="250px"
          :inline="true"
        >
          <el-form-item label="配方" prop="recipe_id">
            <el-select v-model="form.recipe_id" filterable clearable>
              <el-option
                v-for="item in recipeData"
                :key="item.recipe_id"
                :label="item.recipe_name + ' ' + item.recipe_version"
                :value="item.recipe_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="配方名称" prop="pack_name">
            <el-input v-model="form.pack_name" />
          </el-form-item>
          <el-form-item label="模组数量" prop="mz_count">
            <el-input v-model="form.mz_count" />
          </el-form-item>
          <el-form-item label="有效标识">
            <fastCode
              fastcode_group_code="ENABLE_FLAG"
              :fastcode_code.sync="form.enable_flag"
              control_type="radio"
              size="mini"
            />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >确认</el-button>
        </div>
      </el-drawer>
      <!--条码NG规则-->
      <el-drawer append-to-body :wrapper-closable="false" title="条码NG规则" :visible.sync="dxBarDrawerVisible" size="60%">
        <packDxBarRuleItem v-if="dxBarDrawerVisible" ref="packDxBarRuleItem" :pack_id="currentPackId" />
      </el-drawer>
      <!--模组范围约束-->
      <el-drawer append-to-body :wrapper-closable="false" title="模组范围约束" :visible.sync="limitDrawerVisible" size="60%">
        <packLimitRuleItem v-if="limitDrawerVisible" ref="packLimitRuleItem" :pack_id="currentPackId" />
      </el-drawer>
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        title="配方信息"
        :visible.sync="recipeDrawerVisible"
        size="100%"
        @closed="getRecipeData()"
      >
        <recipe v-if="recipeDrawerVisible" ref="recipe" />
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              border
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @header-dragend="crud.tableHeaderDragend()"
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-descriptions class="mzthWidth" style="margin-right:150px" :column="4" size="small" border>
                    <el-descriptions-item
                      label="pack规则ID"
                      label-class-name="table-descriptions-label"
                      content-class-name="table-descriptions-content"
                    >{{ props.row.pack_id }}</el-descriptions-item>
                    <el-descriptions-item
                      label="配方名称"
                      label-class-name="table-descriptions-label"
                      content-class-name="table-descriptions-content"
                    >{{ props.row.pack_name }}</el-descriptions-item>
                    <el-descriptions-item
                      label="模组数量"
                      label-class-name="table-descriptions-label"
                      content-class-name="table-descriptions-content"
                    >{{ props.row.mz_count }}</el-descriptions-item>
                    <el-descriptions-item
                      label="有效标识"
                      label-class-name="table-descriptions-label"
                      content-class-name="table-descriptions-content"
                    >{{ props.row.enable_flag == 'N' ? '无效' : '有效'
                    }}</el-descriptions-item>
                  </el-descriptions>
                </template>
              </el-table-column>
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="pack_id" label="id" />

              <el-table-column :show-overflow-tooltip="true" prop="recipe_type" label="配方类型" />
              <el-table-column :show-overflow-tooltip="true" prop="recipe_name" label="配方描述" />
              <el-table-column :show-overflow-tooltip="true" prop="recipe_version" label="版本号" />
              <el-table-column :show-overflow-tooltip="true" prop="liable_person" label="责任人" />
              <el-table-column :show-overflow-tooltip="true" prop="update_remark" label="更新说明" />
              <el-table-column :show-overflow-tooltip="true" prop="pack_name" label="配方名称" />
              <el-table-column :show-overflow-tooltip="true" prop="mz_count" label="模组数量" />
              <el-table-column label="是否有效" align="center" prop="enable_flag">
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="295" align="center" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button
                        slot="reference"
                        type="text"
                        size="small"
                        @click="$refs.packRuleItem && $refs.packRuleItem.crud.toAdd()"
                      >新增模组</el-button>
                      <el-button slot="reference" type="text" size="small" @click="dxBarDrawerVisible = true">条码NG规则</el-button>
                      <el-button slot="reference" type="text" size="small" @click="limitDrawerVisible = true">模组范围约束</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <packRuleItem ref="packRuleItem" class="tableFirst" :pack_id="currentPackId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudPackRule from '@/api/mes/core/packRule'
import packRuleItem from './packRuleItem'
import packDxBarRuleItem from './packDxBarRuleItem'
import packLimitRuleItem from './packLimitRuleItem'
import crudRecipe from '@/api/mes/core/recipe'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import recipe from '@/views/mes/core/recipe/recipe'
const defaultForm = {
  pack_id: '',
  recipe_id: '',
  pack_name: '',
  mz_count: '0',
  enable_flag: 'Y'
}
export default {
  name: 'PackRule',
  components: { crudOperation, rrOperation, udOperation, pagination, packRuleItem, recipe, packDxBarRuleItem, packLimitRuleItem },
  props: {},
  cruds() {
    return CRUD({
      title: 'PACK规则',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'pack_id',
      // 排序
      sort: ['pack_id asc'],
      // CRUD Method
      crudMethod: { ...crudPackRule },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_pack:add'],
        edit: ['admin', 'c_mes_fmod_recipe_pack:edit'],
        del: ['admin', 'c_mes_fmod_recipe_pack:del'],
        down: ['admin', 'c_mes_fmod_recipe_pack:down']
      },
      rules: {
        recipe_id: [{ required: true, message: '请选择配方', trigger: 'blur' }],
        mz_count: [{ required: true, message: '请填写模组数量', trigger: 'blur' }]
      },
      currentPackId: 0,
      recipeDrawerVisible: false,
      dxBarDrawerVisible: false,
      limitDrawerVisible: false
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.getRecipeData()
  },
  methods: {
    handleRowClick(row, column, event) {
      this.currentPackId = row.pack_id
    },

    getRecipeData() {
      this.recipeData = []
      crudRecipe
        .sel({
          user_name: Cookies.get('userName'),
          recipe_type: 'MODELPACK',
          enable_flag: 'Y'
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.recipeData = defaultQuery.data
            }
          }
          this.loading = false
        })
        .catch(() => {
          this.$message({
            message: '初始化模式数据异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;

  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }

  .el-row {
    width: 50%;
  }
}
</style>
