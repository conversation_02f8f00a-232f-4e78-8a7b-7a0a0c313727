import request from '@/utils/request'

// 查询模组信息
export function mesGxPackManualPackInfoSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxPackManualPackInfoSelect',
    method: 'post',
    data
  })
}
// 查询扫描模组数据
export function mesGxPackManualSelect(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxPackManualSelect',
    method: 'post',
    data
  })
}
// PACK模组绑定校验
export function mesGxPackManualMzCheck(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxPackManualMzCheck',
    method: 'post',
    data
  })
}

// 开始扫模组码时清空PACK配组临时表
export function mesGxPackManualOpClear(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxPackManualOpClear',
    method: 'post',
    data
  })
}

// 针对PACK配组数据进行存储
export function mesGxPackManualSave(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/MesGxPackManualSave',
    method: 'post',
    data
  })
}
export function timeoutCheck(data) {
  return request({
    url: 'aisEsbWeb/mes/project/gx/timeoutCheck',
    method: 'post',
    data
  })
}

export default { mesGxPackManualPackInfoSelect, mesGxPackManualSelect, mesGxPackManualMzCheck,
  mesGxPackManualOpClear, mesGxPackManualSave, timeoutCheck }
