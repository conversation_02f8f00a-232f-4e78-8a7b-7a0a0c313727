import variables from '@/assets/styles/element-variables.scss'
import defaultSettings from '@/settings'
const { tagsView, fixedHeader, sidebarLogo, uniqueOpened, showFooter, footerTxt, caseNumber,sidebarStyle,headerStyle,hmiBgStyle } = defaultSettings

const state = {
  theme: variables.theme,
  showSettings: false,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  uniqueOpened: uniqueOpened,
  userValue:'0',
  warningValue:true,
  showFooter: showFooter,
  footerTxt: footerTxt,
  caseNumber: caseNumber,
  themeStyle:false,
  sidebarStyle:sidebarStyle,
  headerStyle,headerStyle,
  hmiBgStyle:'0',
  systemData:{},
  systemArr:[],
  systemLanguageData:{},
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

