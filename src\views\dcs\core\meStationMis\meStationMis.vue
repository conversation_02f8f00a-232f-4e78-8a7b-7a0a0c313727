<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位号:">
                <!-- 工位号 -->
                <el-select v-model="query.station_code" clearable filterable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code">
                    <span style="float: left">{{ item.station_code }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="工位描述:">
                <!-- 工位描述 -->
                <el-input v-model="query.station_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号:">
                <!-- 任务号 -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="批次号:">
                <!-- 批次号 -->
                <el-input v-model="query.lot_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码:">
                <!-- 物料编码 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料描述:">
                <!-- 物料描述 -->
                <el-input v-model="query.material_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.realTimeTable.stationCode')" prop="station_code">
                <!-- 工位号 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.stationDescription')" prop="station_des">
                <!-- 工位描述 -->
                <el-input v-model="form.station_des" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.stationProperties')" prop="station_attr">
                <!-- 工位属性 -->
                <el-input v-model="form.station_attr" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.stationStatus')" prop="station_status">
                <!-- 工位状态 -->
                <el-input v-model="form.station_status" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.taskNumber')" prop="task_num">
                <!-- 任务号 -->
                <el-input v-model="form.task_num" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.serialNumber')" prop="serial_num">
                <!-- 序列号 -->
                <el-input v-model="form.serial_num" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.batchNumber')" prop="lot_num">
                <!-- 批次号 -->
                <el-input v-model="form.lot_num" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.palletNumber')" prop="pallet_num">
                <!-- 托盘号 -->
                <el-input v-model="form.pallet_num" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.mainMaterialCode')" prop="station_code">
                <!-- 物料编码 -->
                <el-input v-model="form.station_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.mainMaterialDescription')" prop="robot_id">
                <!-- 物料描述 -->
                <el-input v-model="form.robot_id" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.partDrawingNumber')" prop="part_draw">
                <!-- 零件图号 -->
                <el-input v-model="form.part_draw" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.model_type')" prop="model_type">
                <!-- 型号 -->
                <el-input v-model="form.model_type" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.materialQuality')" prop="m_texture">
                <!-- 材质 -->
                <el-input v-model="form.m_texture" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.cuttingMachine')" prop="cut_texture">
                <!-- 材质(切割机) -->
                <el-input v-model="form.cut_texture" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.shifCode')" prop="shift_code">
                <!-- 班次代码 -->
                <el-input v-model="form.shift_code" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.shifDes')" prop="shift_des">
                <!-- 班次描述 -->
                <el-input v-model="form.shift_des" clearable size="small" />
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.realTimeTable.DXFName')" prop="dxf_name">
                                DXF名称
                                <el-input v-model="form.dxf_name" clearable size="small" />
                            </el-form-item> -->
              <el-form-item :label="$t('lang_pack.realTimeTable.JSONName')" prop="json_name">
                <!-- JSON名称 -->
                <el-input v-model="form.json_name" clearable size="small" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.realTimeTable.NCName')" prop="nc_name">
                <!-- NC名称 -->
                <el-input v-model="form.nc_name" clearable size="small" />
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                  <el-descriptions-item label="用户名" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.user_name }}</el-descriptions-item>
                  <el-descriptions-item label="工件到达时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.arrive_date }}</el-descriptions-item>
                  <el-descriptions-item label="工件离开时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.leave_date }}</el-descriptions-item>
                  <el-descriptions-item label="工位号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_code }}</el-descriptions-item>
                  <el-descriptions-item label="工位属性" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.STATION_ATTR[props.row.station_attr] }}</el-descriptions-item>
                  <el-descriptions-item label="工位状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.STATION_STATUS[props.row.station_status] }}</el-descriptions-item>
                  <el-descriptions-item label="任务号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_num }}</el-descriptions-item>
                  <el-descriptions-item label="任务来源" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.task_from }}</el-descriptions-item>
                  <el-descriptions-item label="序列号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_num }}</el-descriptions-item>
                  <el-descriptions-item label="批次号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.lot_num }}</el-descriptions-item>
                  <el-descriptions-item label="托盘号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_num }}</el-descriptions-item>
                  <el-descriptions-item label="托盘使用次数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_use_count }}</el-descriptions-item>
                  <el-descriptions-item label="托盘最高使用次数" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_max_count }}</el-descriptions-item>
                  <el-descriptions-item label="托盘报警标识" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.pallet_alarm_flag }}</el-descriptions-item>
                  <el-descriptions-item label="物料编码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_code }}</el-descriptions-item>
                  <el-descriptions-item label="物料描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_des }}</el-descriptions-item>
                  <el-descriptions-item label="零件图号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.material_draw }}</el-descriptions-item>
                  <el-descriptions-item label="型号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.model_type }}</el-descriptions-item>
                  <el-descriptions-item label="长" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_length }}</el-descriptions-item>
                  <el-descriptions-item label="宽" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_width }}</el-descriptions-item>
                  <el-descriptions-item label="厚" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_height }}</el-descriptions-item>
                  <el-descriptions-item label="重" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_weight }}</el-descriptions-item>
                  <el-descriptions-item label="材质" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.m_texture }}</el-descriptions-item>
                  <el-descriptions-item label="材质(切割)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ dict.label.CUTTERBAR_TEXTURE[props.row.cut_texture] }}</el-descriptions-item>
                  <el-descriptions-item label="上线时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.online_time }}</el-descriptions-item>
                  <el-descriptions-item label="工件在制时间(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.serial_stay_time }}</el-descriptions-item>
                  <el-descriptions-item label="理想节拍(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.ideal_beats }}</el-descriptions-item>
                  <el-descriptions-item label="实际节拍(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.actual_beats }}</el-descriptions-item>
                  <el-descriptions-item label="班次代码" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.shift_code }}</el-descriptions-item>
                  <el-descriptions-item label="班次描述" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.shift_des }}</el-descriptions-item>
                  <el-descriptions-item label="来源工位号" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.from_station_code }}</el-descriptions-item>
                  <el-descriptions-item label="工位错误信息" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.station_msg }}</el-descriptions-item>
                  <!-- <el-descriptions-item label="DXF名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.dxf_name }}</el-descriptions-item> -->
                  <el-descriptions-item label="JSON名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.json_name }}</el-descriptions-item>
                  <el-descriptions-item label="NC名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ [props.row.nc_name] }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <!-- 用户名 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="user_name"
              :label="$t('lang_pack.realTimeTable.username')"
              min-width="100"
              align="center"
            />
            <!-- 到达时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="arrive_date"
              :label="$t('lang_pack.realTimeTable.arriveDate')"
              min-width="130"
              align="center"
            />
            <!-- 离开时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="leave_date"
              :label="$t('lang_pack.realTimeTable.leaveDate')"
              min-width="130"
              align="center"
            />
            <!-- 工位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_code"
              :label="$t('lang_pack.realTimeTable.stationCode')"
              min-width="100"
              align="center"
            />
            <!-- 工位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_des"
              :label="$t('lang_pack.realTimeTable.stationDescription')"
              min-width="100"
              align="center"
            />
            <!-- 工位属性 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_attr"
              :label="$t('lang_pack.realTimeTable.stationProperties')"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.STATION_ATTR[scope.row.station_attr] }}
              </template>
            </el-table-column>
            <!-- 工位状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_status"
              :label="$t('lang_pack.realTimeTable.stationStatus')"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.STATION_STATUS[scope.row.station_status] }}
              </template>
            </el-table-column>
            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              :label="$t('lang_pack.realTimeTable.taskNumber')"
              min-width="100"
              align="center"
            />
            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.realTimeTable.taskSource')"
              min-width="100"
              align="center"
            />
            <!-- 序列号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_num"
              :label="$t('lang_pack.realTimeTable.serialNumber')"
              min-width="100"
              align="center"
            />
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('lang_pack.realTimeTable.batchNumber')"
              min-width="100"
              align="center"
            />
            <!-- 托盘号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pallet_num"
              :label="$t('lang_pack.realTimeTable.palletNumber')"
              min-width="100"
              align="center"
            />
            <!-- 使用次数 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pallet_use_count"
              :label="$t('lang_pack.realTimeTable.palletUsageTimes')"
              min-width="100"
              align="center"
            />
            <!-- 最高使用次数 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pallet_max_count"
              :label="$t('lang_pack.realTimeTable.maximumPalletUsage')"
              min-width="100"
              align="center"
            />
            <!-- 托盘报警标识 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="pallet_alarm_flag"
              :label="$t('lang_pack.realTimeTable.trayAlarmIdentification')"
              min-width="100"
              align="center"
            />
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.realTimeTable.mainMaterialCode')"
              min-width="100"
              align="center"
            />
            <!-- 物料描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="robot_id"
              :label="$t('lang_pack.realTimeTable.mainMaterialDescription')"
              min-width="100"
              align="center"
            />
            <!-- 零件图号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="part_draw"
              :label="$t('lang_pack.realTimeTable.partDrawingNumber')"
              min-width="100"
              align="center"
            />
            <!-- 型号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="model_type"
              :label="$t('lang_pack.mfTable.model_type')"
              min-width="100"
              align="center"
            />
            <!-- 长 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_length"
              :label="$t('lang_pack.mfTable.length')"
              min-width="100"
              align="center"
            />
            <!-- 宽 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.mfTable.width')"
              min-width="100"
              align="center"
            />
            <!-- 厚 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_height"
              :label="$t('lang_pack.mfTable.thick')"
              min-width="100"
              align="center"
            />
            <!-- 重 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_weight"
              :label="$t('lang_pack.mfTable.weight')"
              min-width="100"
              align="center"
            />
            <!-- 材质 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_texture"
              :label="$t('lang_pack.mfTable.materialQuality')"
              min-width="100"
              align="center"
            />
            <!-- 材质(切割机) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cut_texture"
              :label="$t('lang_pack.mfTable.cuttingMachine')"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.CUTTERBAR_TEXTURE[scope.row.cut_texture] }}
              </template>
            </el-table-column>
            <!-- 上线时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="online_time"
              :label="$t('lang_pack.realTimeTable.uptime')"
              min-width="100"
              align="center"
            />
            <!-- 工件在制时间 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="serial_stay_time"
              :label="$t('lang_pack.realTimeTable.workpieceInProcessTime')"
              min-width="130"
              align="center"
            />
            <!-- 理想节拍(秒) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ideal_beats"
              :label="$t('lang_pack.realTimeTable.idealBeat')"
              min-width="100"
              align="center"
            />
            <!-- 实际节拍(秒) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="actual_beats"
              :label="$t('lang_pack.realTimeTable.actualBeat')"
              min-width="100"
              align="center"
            />
            <!-- 班次代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="shift_code"
              :label="$t('lang_pack.realTimeTable.shifCode')"
              min-width="100"
              align="center"
            />
            <!-- 班次描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="shift_des"
              :label="$t('lang_pack.realTimeTable.shifDes')"
              min-width="100"
              align="center"
            />
            <!-- 来源工位号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_station_code"
              :label="$t('lang_pack.realTimeTable.sourceStationNumber')"
              min-width="100"
              align="center"
            />
            <!-- 工位错误信息 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="station_msg"
              :label="$t('lang_pack.realTimeTable.stationErrorMessage')"
              min-width="100"
              align="center"
            />
            <!-- DXF名称
                        <el-table-column  :show-overflow-tooltip="true" prop="dxf_name"
                            :label="$t('lang_pack.realTimeTable.DXFName')" min-width="100" align='center'/> -->
            <!-- JSON名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="json_name"
              :label="$t('lang_pack.realTimeTable.JSONName')"
              min-width="100"
              align="center"
            />
            <!-- NC名称 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="nc_name"
              :label="$t('lang_pack.realTimeTable.NCName')"
              min-width="100"
              align="center"
            />
            <!-- Table单条操作-->
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudMeStationMis from '@/api/dcs/core/meStationMis/meStationMis'
import crudStation from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  user_name: '',
  arrive_date: '',
  leave_date: '',
  station_code: '',
  station_des: '',
  station_attr: '',
  station_status: '',
  task_num: '',
  task_from: '',
  serial_num: '',
  lot_num: '',
  pallet_num: '',
  pallet_use_count: '',
  pallet_max_count: '',
  material_code: '',
  material_des: '',
  material_draw: '',
  model_type: '',
  m_length: '',
  m_width: '',
  m_height: '',
  m_weight: '',
  m_texture: '',
  cut_texture: '',
  online_time: '',
  serial_stay_time: '',
  ideal_beats: '',
  actual_beats: '',
  shift_code: '',
  shift_des: '',
  from_station_code: '',
  station_msg: '',
  dxf_name: '',
  json_name: '',
  nc_name: ''
}
export default {
  name: 'WEB_ME_STATION_MIS',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '工位MIS实时状态表',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'station_code',
      // 排序
      sort: ['station_code asc'],
      // CRUD Method
      crudMethod: { ...crudMeStationMis },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 310,
      permission: {
        add: ['admin', 'fmod_recipe_quality:add'],
        edit: ['admin', 'fmod_recipe_quality:edit'],
        del: ['admin', 'fmod_recipe_quality:del']
      },
      rules: {
        arrive_date: [{ required: true, message: '请选择工件到达时间', trigger: 'blur' }],
        station_code: [{ required: true, message: '请选择工位号', trigger: 'blur' }],
        station_des: [{ required: true, message: '请选择工位描述', trigger: 'blur' }],
        station_attr: [{ required: true, message: '请选择工位属性', trigger: 'blur' }],
        station_status: [{ required: true, message: '请选择工位状态', trigger: 'blur' }]
      },
      // 工位数据
      stationData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'DATA_SOURCES', 'CUTTERBAR_TEXTURE', 'STATION_ATTR', 'STATION_STATUS'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 310
    }
  },
  created: function() {
    const query = {
      userID: Cookies.get('userName')
    }
    crudStation.sel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data.length > 0) {
          this.stationData = defaultQuery.data || []
        }
      } else {
        this.stationData = []
        this.$message({
          message: '工位查询异常',
          type: 'error'
        })
      }
    })
      .catch(() => {
        this.stationData = []
        this.$message({
          message: '工位查询异常',
          type: 'error'
        })
      })
  },
  methods: {
  }
}
</script>
