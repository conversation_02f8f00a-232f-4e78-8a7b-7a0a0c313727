<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          请输入密钥
        </template>
        <el-input
          ref="keyCode"
          v-model="keyCode"
          clearable
          size="mini"
        />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top: 10px; text-align: right">
      <el-button type="primary" @click="handleSendInfo">确 定</el-button>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  components: {},
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      keyCode: ''
    }
  },
  mounted: function() {
  },
  created: function() {},
  methods: {
    handleSendInfo() {
      if (this.keyCode !== 'Jh@2024#20250101') {
        this.$message({ message: '校验失败，请重新输入', type: 'error' })
        return
      }
      this.$emit('sendMessage', this.keyCode)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
