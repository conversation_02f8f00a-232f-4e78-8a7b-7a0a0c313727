import request from '@/utils/request'

// 查询机型BOM
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelBomSel',
    method: 'post',
    data
  })
}
// 新增机型BOM
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelBomIns',
    method: 'post',
    data
  })
}
// 修改机型BOM
export function edit(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelBomUpd',
    method: 'post',
    data
  })
}
// 修改机型BOM--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelBomEnableFlagUpd',
    method: 'post',
    data
  })
}
// 删除机型BOM
export function del(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesSmallModelBomDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, editEnableFlag }

