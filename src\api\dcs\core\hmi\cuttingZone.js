import request from '@/utils/request'

// 查询切割区缓存
export function cacheArea(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsApsMeStationMisCache',
    method: 'post',
    data: data
  })
}

// 到切割区/分拣区
export function toCuttingArea(data,url) {
    // 注: url = Cutting 为切割区   url = SortingArea 为分拣区 
    return request({
      url: `aisEsbWeb/dcs/hmi/DcsApsMeStationMis${url}`,
      method: 'post',
      data: data
    })
  }

// 查询切割管理list
export function cuttingList(data) {
    return request({
      url: 'aisEsbWeb/dcs/hmi/DcsApsMeStationMisCuttingList',
      method: 'post',
      data: data
    })
  }

// 获取下拉工位的数据
export function cuttingCode(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/GetStationCode',
    method: 'post',
    data: data
  })
}

// 获取手动控制信息
export function sprayInfo(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsApsManualControl',
    method: 'post',
    data: data
  })
}

// 重新下发任务
export function issueTask(data) {
  return request({
    url: 'aisEsbWeb/dcs/interface/cut/issue-task',
    method: 'post',
    data: data
  })
}
// 更换切割机
export function ChangeCut(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/DcsApsChangeCut',
    method: 'post',
    data: data
  })
}
// 更换切割机
export function forBiddenCut(data) {
  return request({
    url: 'aisEsbWeb/dcs/hmi/ForbiddenCutting',
    method: 'post',
    data: data
  })
}

export default { cacheArea,toCuttingArea,cuttingList ,cuttingCode,sprayInfo,issueTask,ChangeCut,forBiddenCut}

