<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="库区编码:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="库位编码:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="钢板编号:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="库位类型:">
                <el-input v-model="query.stock_group_code" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            size="small"
            type="primary"
            @click="dialogVisible = true"
          >
            半自动倒垛
          </el-button>
        </template>
      </crudOperation>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="tableData"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="kqbm" label="库区编码" />
            <el-table-column :show-overflow-tooltip="true" prop="kqms" label="库区描述" />
            <el-table-column :show-overflow-tooltip="true" prop="kwbm" label="库位编码" />
            <el-table-column :show-overflow-tooltip="true" prop="kwzt" label="库位状态" />
            <el-table-column :show-overflow-tooltip="true" prop="gbbm" label="钢板编码" />
            <el-table-column :show-overflow-tooltip="true" prop="kcsl" label="库存数量" />
            <el-table-column :show-overflow-tooltip="true" prop="kwlx" label="库位类型" />
            <el-table-column :show-overflow-tooltip="true" prop="zdkc" width="120" label="最大库存" />
            <el-table-column :show-overflow-tooltip="true" prop="zxkc" width="120" label="最小库存" />
            <el-table-column
              label="人工库存操作"
              align="center"
              fixed="right"
              width="270"
            >
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  style="color: #22C55E;"
                  @click="manualDialogVisible = true"
                >入库</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  style="color: #22C55E;"
                >出库</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  style="color: #22C55E;"
                  @click="viewDetail"
                >查看库存明细</el-button>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >编辑</el-button>
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                >删除</el-button>
                <!-- <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :disabled-dle="false"
                >
                  <template slot="right">
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                    >{{
                      $t("lang_pack.maintenanceMenu.addSubmenu")
                    }}</el-button>
                    新增子菜单
                  </template>
                </udOperation> -->
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
    <el-dialog
      :fullscreen="false"
      :show-close="true"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      title="半自动倒垛"
      width="70%"
      :visible.sync="dialogVisible"
    >
      <el-form
        ref="ruleForm"
        class="el-form-wrap"
        :model="formRules"
        :rules="rules"
        size="small"
        label-width="180px"
        :inline="true"
      >
        <div style="display: flex;">
          <el-form-item label="库区编码">
            <el-input v-model="formRules.kqbm" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="当前库位编码">
            <el-input v-model.number="formRules.dqkwbm" type="number" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="目标库位编码" prop="max_count">
            <el-input v-model="formRules.mbkwbm" disabled clearable size="small" />
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item label="当前库位库存数量">
            <el-input v-model="formRules.kqbm" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="目标库位库存数量">
            <el-input v-model.number="formRules.dqkwbm" type="number" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="倒库数量" prop="max_count">
            <el-input v-model="formRules.mbkwbm" disabled clearable size="small" />
          </el-form-item>
        </div>
        <el-divider />
        <div style="display: flex;justify-content: center;width: 100%;">
          <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="dialogLoading" @click="handleOk">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-form>
    </el-dialog>

    <el-dialog
      :fullscreen="false"
      :show-close="true"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
      title="手动入库"
      width="70%"
      :visible.sync="manualDialogVisible"
    >
      <el-form
        ref="ruleForm"
        class="el-form-wrap"
        :model="formRules"
        :rules="rules"
        size="small"
        label-width="180px"
        :inline="true"
      >
        <div style="display: flex;">
          <el-form-item label="库位号">
            <el-input v-model="formRules.kqbm" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="库存量">
            <el-input v-model.number="formRules.dqkwbm" type="number" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="最大库存" prop="max_count">
            <el-input v-model="formRules.mbkwbm" disabled clearable size="small" />
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item label="钢板编号">
            <el-input v-model="formRules.kqbm" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="数量">
            <el-input v-model.number="formRules.dqkwbm" type="number" disabled clearable size="small" />
          </el-form-item>
          <el-form-item label="批次号" prop="max_count">
            <el-input v-model="formRules.mbkwbm" disabled clearable size="small" />
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item label="项目号">
            <el-input v-model="formRules.kqbm" disabled clearable size="small" />
          </el-form-item>
          <el-form-item style="visibility: hidden;" label="项目号">
            <el-input v-model="formRules.kqbm" disabled clearable size="small" />
          </el-form-item>
        </div>
        <el-divider />
        <div style="display: flex;justify-content: center;width: 100%;">
          <el-button size="small" icon="el-icon-close" plain @click="handleClose">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="dialogLoading" @click="handleOk">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-form>
    </el-dialog>
    <selectModal ref="taskInfo" :footer="false" />
  </div>
</template>

<script>
import crudWmsMapStockCell from '@/api/dcs/project/wms/wmsMapStockCell'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import selectModal from '@/components/selectModal'
import axios from 'axios'
const defaultForm = {

}
export default {
  name: 'WMS_INVENTORY_LIST',
  components: { crudOperation, rrOperation, udOperation, pagination, selectModal },
  cruds() {
    return CRUD({
      title: '入库到货清单',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'cell_id',
      // 排序
      sort: ['cell_id asc'],
      // CRUD Method
      crudMethod: { ...crudWmsMapStockCell },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 320,
      permission: {
        add: ['admin', 'b_dcs_wms_map_stock_cell:add'],
        edit: ['admin', 'b_dcs_wms_map_stock_cell:edit'],
        del: ['admin', 'b_dcs_wms_map_stock_cell:del']
      },
      tableData: [
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' },
        { kqbm: 'A', kqms: 'A库位', kwbm: 'A-01', kwzt: '可用', gbbm: 'Q235BGT700', kcsl: '800', kwlx: '正常版规垛位', zdkc: '100', zxkc: '10' }
      ],
      dialogVisible: false,
      manualDialogVisible: false,
      formRules: {
        kqbm: 'A-01',
        dqkwbm: 100,
        mbkwbm: 1000
      }
    }
  },
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 320
    }
  },
  created: function() {
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.manualDialogVisible = false
    },
    viewDetail() {
      this.$refs.taskInfo.open({
        type: 'hskcmx',
        checkType: '',
        search: {}
      })
    }
  }
}
</script>

    <style>
    .table-descriptions-label {
    width: 150px;
    }
    .table-descriptions-content {
    width: 150px;
    }
    </style>
