import request from '@/utils/request'

const context = 'aisEsbWeb/pack/project/avary'

// 新增
export function add(data) {
  return request({
    url: context + '/PackPlanTaskInsert',
    method: 'post',
    data
  })
}

// 查询
export function sel(data) {
  return request({
    url: context + '/PackPlanTaskSelect',
    method: 'post',
    data
  })
}

// 修改
export function edit(data) {
  return request({
    url: context + '/PackPlanTaskUpdate',
    method: 'post',
    data
  })
}

// 删除
export function del(data) {
  return request({
    url: context + '/PackPlanTaskDelete',
    method: 'post',
    data
  })
}

// 关闭
export function close(data) {
  return request({
    url: context + '/PackPlanTaskClose',
    method: 'post',
    data
  })
}

// 任务下发
export function taskDown(data) {
  return request({
    url: context + '/PackPlanTaskDown',
    method: 'post',
    data
  })
}

// 获取订单类型，数量，合格率
export function taskInfoSelect(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackWorkInfoSelect',
    method: 'post',
    data
  })
}

// 获取主页面曲线图
export function taskPassRate(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackHourlyPassRate',
    method: 'post',
    data
  })
}

// 获取主页面曲查看明细
export function InfoSelect(data) {
  return request({
    url: 'aisEsbWeb/pack/core/PackArrayInfoSelect',
    method: 'post',
    data
  })
}

// 从mes下载mapping结果
export function GetStripInspectData(data) {
  return request({
    url: 'aisEsbApi/pack/project/avary/op/mes/interf/GetStripInspectData',
    method: 'post',
    data
  })
}
// 获取生产信息
export function PackProductInfoSelect(data) {
  return request({
    url: 'aisEsbWeb/pack/project/tripod/PackProductInfoSelect',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del, taskDown, taskInfoSelect, taskPassRate, InfoSelect, GetStripInspectData, close, PackProductInfoSelect }

