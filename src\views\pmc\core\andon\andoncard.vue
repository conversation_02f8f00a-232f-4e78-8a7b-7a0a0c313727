<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.workCenterCode')">
                <!-- 车间： -->
                <el-select v-model="query.work_center_code" clearable filterable @change="changeWorkCenterCode">
                  <el-option v-for="item in dict.WORK_CENTER_CODE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.prodLineCode')">
                <!-- 产线： -->
                <!--表：sys_fmod_prod_line-->
                <el-select v-model="query.prod_line_code" @change="changeProdLine">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.stationCode')">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_id">
                  <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.andonCardStatus')">
                <!-- 工位牌状态： -->
                <el-select v-model="query.andon_card_status" clearable filterable>
                  <el-option v-for="item in dict.ANDON_CARD_STATUS" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.enableFlag')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
              <el-form-item :label="$t('lang_pack.stationdevice.workCenterCode')" prop="work_center_code">
                <!-- 车间 -->
                <el-select v-model="form.work_center_code" clearable filterable @change="changeWorkCenterCode">
                  <el-option v-for="item in dict.WORK_CENTER_CODE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <!--表：sys_fmod_prod_line-->
              <el-form-item :label="$t('lang_pack.stationdevice.prodLineCode')" prop="prod_line_code">
                <!-- 产线 -->
                <el-select v-model="form.prod_line_code" size="small" placeholder="请选择产线" @change="changeProdLine">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_code" />
                </el-select>
              </el-form-item>
              <!--表：sys_fmod_station-->
              <el-form-item :label="$t('lang_pack.andoncard.stationId')" prop="station_id">
                <!-- 工位号 -->
                <el-select v-model="form.station_id" size="small" placeholder="请选择工位">
                  <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.andoncard.andonCardType')">
                <!-- 工位牌类型 -->
                <el-select v-model="form.andon_card_type" clearable filterable>
                  <el-option v-for="item in dict.ANDON_CARD_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.andoncard.andonCardColor')">
                <!-- 颜色 -->
                <el-select v-model="form.andon_card_color" clearable filterable>
                  <el-option v-for="item in dict.ANDON_CARD_COLOR" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.andoncard.andonCardCode')" prop="andon_card_code">
                <!-- 工位牌编码 -->
                <el-input v-model="form.andon_card_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.andoncard.cardTagId')" prop="card_tag_id">
                <!-- 工位牌状态TAG -->
                <el-input v-model="form.card_tag_id" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.andoncard.andonCardStatus')" prop="andon_card_status">
                <!-- 工位牌状态 -->
                <el-input v-model="form.andon_card_status" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.andoncard.enableFlag')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="55" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" :label="$t('lang_pack.andonbtn.creationDate')" />
            <!-- 创建时间 -->

            <el-table-column  :label="$t('lang_pack.andoncard.stationId')" align="center" prop="station_id" width="100">
              <!-- 工位号 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStationDes(scope.row.station_id) }}
              </template>
            </el-table-column>
            <el-table-column  :label="$t('lang_pack.andoncard.andonCardType')" align="center" prop="andon_card_type">
              <!-- 工位牌类型 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ANDON_CARD_TYPE[scope.row.andon_card_type] }}
              </template>
            </el-table-column>
            <el-table-column  :label="$t('lang_pack.andoncard.andonCardColor')" align="center" prop="andon_card_color">
              <!-- 颜色 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ANDON_CARD_COLOR[scope.row.andon_card_color] }}
              </template>
            </el-table-column>

            <el-table-column  :show-overflow-tooltip="true" prop="andon_card_code" :label="$t('lang_pack.andoncard.andonCardCode')" />
            <!-- 工位牌编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="card_tag_id" :label="$t('lang_pack.andoncard.cardTagId')" />
            <!-- 工位牌状态TAG -->
            <el-table-column  :show-overflow-tooltip="true" prop="andon_card_status" :label="$t('lang_pack.andoncard.andonCardStatus')" />
            <!-- 工位牌状态 -->

            <el-table-column  :label="$t('lang_pack.andoncard.enableFlag')" align="center" prop="enable_flag" width="100">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import crudStation from '@/api/core/factory/sysStation'
import crudAndonCard from '@/api/pmc/andon/andoncard'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  andon_card_id: '',
  station_id: '',
  andon_card_type: '',
  andon_card_color: '',
  andon_card_code: '',
  card_tag_id: '0',
  andon_card_status: '',
  enable_flag: 'Y'
}
export default {
  name: 'ANDONCARD',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '安灯工位牌基础',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'andon_card_id',
      // 排序
      sort: ['andon_card_id asc'],
      // CRUD Method
      crudMethod: { ...crudAndonCard },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'fmod_andon_card:add'],
        edit: ['admin', 'fmod_andon_card:edit'],
        del: ['admin', 'fmod_andon_card:del']
      },
      rules: {
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        andon_card_type: [{ required: true, message: '请选择工位牌类型', trigger: 'blur' }]
      },
      // 车间数据
      currentWorkCenterCode: '', // 当前车间(单笔)
      // 产线数据
      currentProdLineCode: '', // 当前产线(单笔)
      prodLineData: [],
      // 工位数据
      stationData: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'WORK_CENTER_CODE', 'ANDON_CARD_TYPE', 'ANDON_CARD_COLOR', 'ANDON_CARD_STATUS'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {},
  methods: {
    // 获取产线的中文描述
    getProdLineDes(prod_line_code) {
      var item = this.prodLineData.find(item => item.prod_line_code === prod_line_code)
      if (item !== undefined) {
        return item.prod_line_des
      }
      return prod_line_code
    },
    // 获取工位的中文描述
    getStationDes(station_id) {
      var item = this.stationData.find(item => item.station_id === station_id)
      if (item !== undefined) {
        return item.station_des
      }
      return station_id
    },

    // 更改车间
    changeWorkCenterCode(val) {
      this.currentWorkCenterCode = val // 当前车间
      // 加载 产线LOV
      this.queryProdLine()
    },
    // 产线LOV
    queryProdLine() {
      const query = {
        work_center_code: this.currentWorkCenterCode,
        userID: Cookies.get('userName')
      }
      lovProdLine(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.prodLineData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    // 更改产线
    changeProdLine(val) {
      this.currentProdLineCode = val // 当前产线
      // 加载 工位LOV
      this.queryStation()
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get('userName'),
        prod_line_code: this.currentProdLineCode,
        enable_flag: 'Y'
      }
      crudStation
        .lovStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>
