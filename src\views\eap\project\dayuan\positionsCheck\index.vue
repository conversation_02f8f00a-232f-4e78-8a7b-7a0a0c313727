<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="扫描料号：">
                <el-input v-model="query.material_no" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="批次号：">
                <el-input v-model="query.batch_no" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="操作时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer
        title="工单详情"
        :visible.sync="postionsDrawer"
        size="650px"
      >
        <detail ref="detail" class="tableFirst" :work_order_d_id="currentPackId" />
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            height="478"
            max-height="478"
            highlight-current-row
            @selection-change="crud.selectionChangeHandler"
          >
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="work_order_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="material_no" label="扫描料号" />
            <el-table-column  :show-overflow-tooltip="true" prop="batch_no" label="批次号" />
            <el-table-column  :show-overflow-tooltip="true" prop="creation_date" label="操作时间" />
            <el-table-column  label="操作" width="175" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button slot="reference" type="text" size="small" @click="checkDetails(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import eapDyWorkOrder from '@/api/eap/project/dayuan/eapDyWorkOrder'
import detail from './detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  work_order_id: '',
  material_no: '',
  batch_no: '',
  creation_date: ''
}
export default {
  name: 'EAP_DY_WORK_ORDER',
  components: { crudOperation, rrOperation, udOperation, pagination, detail },
  props: {},
  cruds() {
    return CRUD({
      title: '工单查询记录',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'work_order_id',
      // 排序
      sort: ['work_order_id asc'],
      // CRUD Method
      crudMethod: { ...eapDyWorkOrder },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EAP_RECIPE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'eap_dy_work_order:add'],
        down: ['admin', 'eap_dy_work_order:down']
      },
      rules: {
      },
      currentPackId: 0,
      postionsDrawer: false
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
  },
  methods: {
    checkDetails(row) {
      this.currentPackId = row.work_order_id
      this.postionsDrawer = true
    }
  }
}
</script>
<style lang="less" scoped>

</style>
