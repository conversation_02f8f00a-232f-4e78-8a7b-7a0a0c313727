{"heartbeat": [{"scadaClient": "SCADA_BEAT", "tags": {"des": "EAP心跳", "tag_des": "EAP", "tag_key": "LoadEap", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_BEAT", "tags": {"des": "PLC心跳", "tag_des": "PLC", "tag_key": "LoadPlc0", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_BEAT", "tags": {"des": "板件CCD1心跳", "tag_des": "lang_pack.hmiMain.CCD", "tag_key": "LoadPanelCcd1", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_BEAT", "tags": {"des": "板件CCD2心跳", "tag_des": "lang_pack.hmiMain.PlateCCD2", "tag_key": "LoadPanelCcd2", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}], "scadaPoint": [{"scadaClient": "SCADA_CHANGE", "tags": {"tag_des": "lang_pack.dy.offLine", "tag_key": "LoadPlc0/PlcConfig/SysModel", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_des": "AGV", "tag_key": "LoadPlc0/PlcConfig/PortStatus", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_des": "status", "tag_key": "LoadPlc0/PlcStatus/DeviceStatus", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_des": "Panel", "tag_key": "LoadPlc0/PlcConfig/PanelModel", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_des": "work", "tag_key": "LoadPlc0/PlcStatus/PlcWorkPortIndex", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_des": "AisConfig", "tag_key": "LoadAis/AisConfig/EapApiTimeOutSeconds", "tag_value": "0"}, "ports": {"mqtt": 8083, "cell": 8089}}], "scadaPlcRequestVal": [{"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc0/PlcStatus/TaskDirection", "tag_value": "0", "code": ["1", "2"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc1/PlcStatus/TaskDirection", "tag_value": "0", "code": ["3", "4"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc2/PlcStatus/TaskDirection", "tag_value": "0", "code": ["5", "6"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc3/PlcStatus/TaskDirection", "tag_value": "0", "code": ["7", "8"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc4/PlcStatus/TaskDirection", "tag_value": "0", "code": ["9", "10"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc5/PlcStatus/TaskDirection", "tag_value": "0", "code": ["11", "12"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc6/PlcStatus/TaskDirection", "tag_value": "0", "code": ["13", "14"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc7/PlcStatus/TaskDirection", "tag_value": "0", "code": ["15", "16"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc8/PlcStatus/TaskDirection", "tag_value": "0", "code": ["17", "18"]}, "ports": {"mqtt": 8083, "cell": 8089}}, {"scadaClient": "SCADA_CHANGE", "tags": {"tag_key": "LoadPlc9/PlcStatus/TaskDirection", "tag_value": "0", "code": ["19", "20"]}, "ports": {"mqtt": 8083, "cell": 8089}}]}