<template>
  <el-row :gutter="20">
    <!-- <el-col :span="24" style="min-height: 120px"> -->
    <!-- <div id="homeQuickLinks">
        <div v-for="(item, index) in treeData" :key="index" class="grid-content bg-purple" @click="handleOpenPage(item.path)">
          <div :class="'el-avatar el-avatar-bg' + index">
            <svg-icon :icon-class="item.menu_item_ico" />
          </div>
          <div class="menu-title">{{ item.menu_item_des }}</div>
        </div>
        <div class="grid-content bg-purple" @click="handleAdd">
          <div class="el-avatar-add">
            <i class="el-icon-plus" />
          </div> -->
    <!-- <el-avatar
          size="large"
          icon="el-icon-plus"
          style="background: #ffffff; color: #cfd7e1; font-size: 35px"
        ></el-avatar> -->
    <!-- <div class="menu-title">添加快捷菜单</div>
        </div>
      </div> -->
    <!-- <div id="drawerDiv">
        <el-drawer title="添加快捷菜单" :visible.sync="dialogVisbleSyncFrom" size="65%" @closed="drawerClose">
          <addShortcutMenu v-if="addShortcutMenuShow" ref="addShortcutMenu" @refresh="handleRefresh" @cancel="handleCancel" />
        </el-drawer>
      </div> -->
    <!-- </el-col> -->
    <el-col id="homeMain" :span="24">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never" style="height: 250px">
            <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 600">
              <div
                style="
                  height: 33px;
                  line-height: 33px;
                  font-size: 15px;
                  color: #333333;
                "
              >
                &nbsp;&nbsp;&nbsp;服务器监控
              </div>
            </div>
            <div class="wrapProgress">
              <el-progress type="circle" :percentage="percentage" :format="handleFormat" :color="colors" :width="130" :stroke-width="24" />
              <el-progress type="circle" :percentage="percentage1" :format="handleFormat1" :color="colors" :width="130" :stroke-width="24" />
              <el-progress type="circle" :percentage="percentage2" :format="handleFormat2" :color="colors" :width="130" :stroke-width="24" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never" style="height: 250px">
            <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
              <div
                style="
                  height: 33px;
                  line-height: 33px;
                  font-size: 15px;
                  color: #333333;
                "
              >
                指标监控
              </div>
            </div>
            <report />
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="12" style="padding-left: 10px; padding-right: 10px">
          <el-card shadow="never" style="height: 360px;position:relative">
            <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 600">
              <div
                style="
                  height: 33px;
                  line-height: 33px;
                  font-size: 15px;
                  color: #333333;
                "
              >
                &nbsp;&nbsp;&nbsp;可视化系统
                <div style="float: right">
                  <span class="wrapSpan"><i class="el-icon-plus" /></span>
                  <span class="wrapSpan"><i class="el-icon-minus" /></span>
                  <span class="wrapSpan"><i class="el-icon-full-screen" /></span>
                </div>
              </div>
            </div>
            <div class="wrapImg">
              <img :src="factoryLayout">
            </div>
          </el-card>
        </el-col> -->
        <el-col :span="12" style="padding-left: 10px; padding-right: 10px">
          <el-card shadow="never" style="height: 360px">
            <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
              <div
                style="
                  height: 33px;
                  line-height: 33px;
                  font-size: 15px;
                  color: #333333;
                "
              >
                &nbsp;&nbsp;&nbsp;设备消息
              </div>
            </div>
            <div class="InfoRoll">
              <deviceScroll :list="dataList"></deviceScroll>
              <!-- <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg0"> <i class="el-icon-bell" /></span>
                  &nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg0"> <i class="el-icon-bell" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg1"> <i class="el-icon-discount" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg1"> <i class="el-icon-discount" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg2"> <i class="el-icon-warning-outline" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg2"> <i class="el-icon-warning-outline" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg3"> <i class="el-icon-warning-outline" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div> -->
            </div>
          </el-card>
        </el-col>
        <el-col :span="12" style="padding-left: 10px; padding-right: 10px">
          <el-card shadow="never" style="height: 360px">
            <div slot="header" class="clearfix" style="font-size: 14px; color: #757575; font-weight: 700">
              <div
                style="
                  height: 33px;
                  line-height: 33px;
                  font-size: 15px;
                  color: #333333;
                "
              >
                &nbsp;&nbsp;&nbsp;消息
              </div>
            </div>
            <div class="InfoRoll">
              <deviceScroll :list="flowChatList"></deviceScroll>
              <!-- <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg0"> <i class="el-icon-bell" /></span>
                  &nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg0"> <i class="el-icon-bell" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg1"> <i class="el-icon-discount" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg1"> <i class="el-icon-discount" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg2"> <i class="el-icon-warning-outline" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg2"> <i class="el-icon-warning-outline" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div>
              <div class="home-message">
                <div class="home-message-item">
                  <span class="home-message-item-icon item-icon-bg3"> <i class="el-icon-warning-outline" /></span>&nbsp;
                  <span>消息通知！消息通知！消息通知！消息通知！消息通知！</span>
                </div>
                <div class="wrapTime">
                  2022/04/26 15:15:00
                </div>
              </div> -->

              <!-- <el-col :span="24">
                <el-row :gutter="20">
                  <el-card shadow="never" :style="{height:height + 'px'}" class="iframeBox">
                    <div >
                      <iframe id="iFrame" src="http://*************:3000/d/b8214c74-a543-4ae4-847f-d588e542f32a/onlinecount?orgId=1" frameborder="0" scrolling="no" :style="{width:'100%',height:height + 'px'}"></iframe>
                    </div>
                  </el-card>
                </el-row>
              </el-col> -->
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import addShortcutMenu from '@/views/core/system/menu/add_shortcut_menu'
import report from '@/views/report/index'
import { queryShortcutMenuSelect } from '@/api/core/system/sysMenu'
import FactoryLayout from '@/assets/images/boss.gif'
import deviceScroll from '../components/homeModules/index.vue'
export default {
  components: {
    addShortcutMenu,
    report,
    deviceScroll
  },
  data() {
    return {
      factoryLayout: FactoryLayout,
      dialogVisbleSyncFrom: false,
      addShortcutMenuShow: false,
      treeData: [],
      percentage: 10,
      percentage1: 30,
      percentage2: 30,
      colors: [{ color: '#5cb87a', percentage: 20 }, { color: '#6f7ad3', percentage: 40 }, { color: '#1989fa', percentage: 60 }, { color: '#e6a23c', percentage: 80 }, { color: '#f56c6c', percentage: 100 }],
      dataList:[
        {id:1,messageInfo:'报警通知！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:15:00'},
        {id:2,messageInfo:'报警通知2！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:16:00'},
        {id:3,messageInfo:'报警通知3！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:17:00'},
        {id:4,messageInfo:'报警通知4！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:18:00'},
        {id:5,messageInfo:'报警通知5！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:19:00'},
        {id:6,messageInfo:'报警通知6！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:20:00'},
        {id:7,messageInfo:'报警通知7！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:21:00'},
        {id:8,messageInfo:'报警通知8！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:22:00'},
        {id:9,messageInfo:'报警通知9！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:23:00'},
        {id:10,messageInfo:'报警通知10！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:24:00'},
        {id:11,messageInfo:'报警通知11！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:25:00'},
        {id:12,messageInfo:'报警通知12！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:26:00'},
        {id:13,messageInfo:'报警通知13！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:27:00'},
        {id:14,messageInfo:'报警通知14！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:28:00'},
        {id:15,messageInfo:'报警通知15！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:29:00'},
        {id:16,messageInfo:'报警通知16！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:30:00'},
      ],
      flowChatList:[
        {id:1,messageInfo:'流程通知！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:15:00'},
        {id:2,messageInfo:'流程通知2！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:16:00'},
        {id:3,messageInfo:'流程通知3！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:17:00'},
        {id:4,messageInfo:'流程通知4！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:18:00'},
        {id:5,messageInfo:'流程通知5！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:19:00'},
        {id:6,messageInfo:'流程通知6！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:20:00'},
        {id:7,messageInfo:'流程通知7！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:21:00'},
        {id:8,messageInfo:'流程通知8！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:22:00'},
        {id:9,messageInfo:'流程通知9！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:23:00'},
        {id:10,messageInfo:'流程通知10！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:24:00'},
        {id:11,messageInfo:'流程通知11！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:25:00'},
        {id:12,messageInfo:'流程通知12！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:26:00'},
        {id:13,messageInfo:'流程通知13！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:27:00'},
        {id:14,messageInfo:'流程通知14！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:28:00'},
        {id:15,messageInfo:'流程通知15！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:29:00'},
        {id:16,messageInfo:'流程通知16！报警通知！报警通知！报警通知！报警通知！',createTime:'2023/04/24 10:30:00'},
      ],
    }
  },
  created() {
    this.initTree()
  },
  mounted(){
    // window.onresize = function () { //窗口尺寸变化时，重新计算和缩放
    //   document.querySelector('.el-progress-circle').style.width = Math.floor(window.innerHeight / 5) + 'px'
    // }
    var that = this
    this.height = document.documentElement.clientHeight - 50
    window.onresize = function() {
      that.height = document.documentElement.clientHeight - 50
    }
  },
  methods: {
    drawerClose() {
      this.addShortcutMenuShow = false
    },
    handleOpenPage(path) {
      this.$router.push({ path: path })
    },
    handleAdd() {
      this.addShortcutMenuShow = true
      this.dialogVisbleSyncFrom = true
    },
    handleCancel() {
      this.addShortcutMenuShow = false
      this.dialogVisbleSyncFrom = false
    },
    handleRefresh() {
      this.addShortcutMenuShow = false
      this.dialogVisbleSyncFrom = false
      this.initTree()
    },
    initTree() {
      const query = {
        userID: Cookies.get('userId')
      }
      queryShortcutMenuSelect(query)
        .then(resSel => {
          const defaultQuerySel = JSON.parse(JSON.stringify(resSel))
          console.log(defaultQuerySel)
          if (defaultQuerySel.data.length > 0) {
            this.treeData = defaultQuerySel.data
          }
        })
        .catch(() => {})
    },
    handleFormat(percentage) {
      return 'CPU ' + percentage + '%'
    },
    handleFormat1(percentage) {
      return '内存 ' + percentage + '%'
    },
    handleFormat2(percentage) {
      return '硬盘 ' + percentage + '%'
    }
  }
}
</script>

<style lang="less" >
#homeQuickLinks {
  .bg-purple {
    background: #ffffff;
    text-align: center;
  }
  .bg-purple:hover {
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
    cursor: pointer;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #b4c0cf;
  }
  .grid-content {
    border-radius: 15px;
    padding-bottom: 15px;
    height: 75px;
    max-width: 150px;
    float: left;
    margin-left: 10px;
    margin-top: 40px;
  }
  .el-avatar {
    border-radius: 20px;
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #0b4ab1;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    margin-top: -15px;
  }
  .el-avatar-bg0 {
    background: #495ce4;
  }
  .el-avatar-bg1 {
    background: #f3c227;
  }
  .el-avatar-bg2 {
    background: #fb9161;
  }
  .el-avatar-bg3 {
    background: #3fc7e4;
  }
  .el-avatar-bg4 {
    background: #58d879;
  }
  .el-avatar-add {
    border: 2px dashed #464a97;
    border-radius: 20px;
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #464a97;
    background: #f1eff1;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 25px;
    margin-top: -15px;
    // font-weight: 600;
  }
  .menu-title {
    width: 150px;
    color: #000;
    font-size: 14px;
    // font-weight: 600;
    padding-top: 10px;
  }
}

#homeMain {
  padding: 30px 0px 5px 10px;
  .el-card__header {
    padding: 15px 10px;
    border-bottom: 0;
  }
  .InfoRoll{
    overflow: hidden;
    height: 320px;
  }
  .home-message {
    width: 100%;
    height: 35px;
    line-height: 35px;
    color: #333333;
    border-radius: 5px;
    margin-bottom: 5px;
    font-size: 13px;
    display: flex;
  }
  .home-message-item {
    display: flex;
    align-items: center;
    width: 80%;
    float: left;
  }
  .home-message-item-icon {
    color: #79a0f1;
    font-size: 20px;
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    margin-left: 5px;
  }
  // .item-icon-bg0 {
  //   background-color: #495ce4;
  // }
  // .item-icon-bg1 {
  //   background-color: #f3c227;
  // }
  // .item-icon-bg2 {
  //   background-color: #fb9161;
  // }
  // .item-icon-bg3 {
  //   background-color: #3fc7e4;
  // }
  // .item-icon-bg4 {
  //   background-color: #58d879;
  // }
}
.el-card {
  border: none;
  border-radius: 0.5rem;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  margin-bottom: 15px;
}
.app-main {
  background-color: #f5f5f5 !important;
}
.el-row {
  margin: 0 !important;
}
.wrapTime {
  text-align: right;
}
.el-card__body {
  padding: 0 15px;
}
.wrapProgress {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}
.wrapImg {
  display: flex;
  justify-content: center;
  img {
    position: absolute;
    bottom: -15%;
    width: 64%;
  }
}
.wrapSpan {
  color: #333;
  padding: 2px;
  font-size: 14px;
  margin: 0 5px;
  cursor: pointer;
  i {
    font-weight: bold;
  }
}
.wrapTime {
  white-space: nowrap;
}
::v-deep .el-card__header{
  padding: 8px;
}
</style>
