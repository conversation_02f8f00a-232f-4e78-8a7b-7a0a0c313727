<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.pmcquery.stationCode')">
                <!-- 工位： -->
                <!--表：sys_fmod_station-->
                <el-select v-model="query.station_code" filterable>
                  <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_code" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="设备名称：">
                <el-input v-model="query.equip_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="设备编号：">
                <el-input v-model="query.equip_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="设备类型：">
                <!-- 设备类型： -->
                <el-select v-model="query.equip_type" clearable filterable>
                  <el-option v-for="item in [{value: '手动',label: '手动'},{value: '自动',label: '自动'}]" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="780px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="240px" :inline="true">
          <el-form-item label="设备名称" prop="equip_des">
            <el-input v-model.number="form.equip_des" />
          </el-form-item>
          <el-form-item label="设备编号" prop="equip_code">
            <el-input v-model="form.equip_code" />
          </el-form-item>
          <el-form-item label="设备制造商" prop="equip_brand">
            <el-input v-model="form.equip_brand" />
          </el-form-item>
          <el-form-item label="设备型号" prop="equip_model">
            <el-input v-model="form.equip_model" />
          </el-form-item>
          <el-form-item label="设备序列号" prop="equip_no">
            <el-input v-model="form.equip_no" />
          </el-form-item>
          <el-form-item label="设备出厂时间" prop="equip_leave_date">
            <el-date-picker v-model="form.equip_leave_date" placeholder="选择时间" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
          </el-form-item>
          <el-form-item label="设备入厂时间" prop="equip_arrive_date">
            <el-date-picker v-model="form.equip_arrive_date" placeholder="选择时间" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
          </el-form-item>
          <el-form-item label="设备启用时间" prop="equip_start_date">
            <el-date-picker v-model="form.equip_start_date" placeholder="选择时间" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
          </el-form-item>
          <el-form-item label="设备类型" prop="equip_type">
            <el-select v-model="form.equip_type" clearable filterable>
              <el-option v-for="item in [{value: '手动',label: '手动'},{value: '自动',label: '自动'}]" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属工位" prop="station_code">
            <el-select v-model="form.station_code">
              <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_des" :value="item.station_code" />
            </el-select>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="450px" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_des" width="120" label="设备名称" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_code" width="120" label="设备编号" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_brand" width="120" label="设备制造商" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_model" width="120" label="设备型号" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_no" width="120" label="设备序列号" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_leave_date" width="120" label="设备出厂时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_arrive_date" width="120" label="设备入厂时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_start_date" width="120" label="设备启用时间" />
            <el-table-column  :show-overflow-tooltip="true" prop="equip_type" width="120" label="设备类型" />
            <el-table-column  label="所属工位" align="center" prop="station_code">
              <!-- 工位号 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStationDes(scope.row.station_code) }}
              </template>
            </el-table-column>
            <!-- Table单条操作-->
            <el-table-column  label="操作" width="115" align="center" fixed="right">  <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudStation from '@/api/core/factory/sysStation'
import crudMesQcEquip from '@/api/mes/project/mesQcEquip'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  equip_id: '',
  equip_des: '',
  equip_code: '',
  equip_brand: '',
  equip_model: '',
  equip_no: '',
  equip_leave_date: '',
  equip_arrive_date: '',
  equip_start_date: '',
  equip_type: '',
  station_code: '',
  equip_image_url: ''

}
export default {
  name: 'MES_QC_EQUIP',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '设备台账',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段:'
      idField: 'equip_id',
      // 排序
      sort: ['equip_id desc'],
      // CRUD Method
      crudMethod: { ...crudMesQcEquip },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'mes_qc_equip:add'],
        edit: ['admin', 'mes_qc_equip:edit'],
        del: ['admin', 'mes_qc_equip:del'],
        down: ['admin', 'mes_qc_equip:down']
      },
      rules: {
        equip_des: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
        equip_code: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
        equip_brand: [{ required: true, message: '请输入设备制造商', trigger: 'blur' }],
        equip_model: [{ required: true, message: '请输入设备型号', trigger: 'blur' }],
        equip_no: [{ required: true, message: '请输入设备序列号', trigger: 'blur' }],
        equip_leave_date: [{ required: true, message: '请输入设备出厂时间', trigger: 'blur' }],
        equip_arrive_date: [{ required: true, message: '请输入设备入厂时间', trigger: 'blur' }],
        equip_start_date: [{ required: true, message: '请输入设备启用时间', trigger: 'blur' }],
        station_code: [{ required: true, message: '请选择所属工位', trigger: 'blur' }],
        equip_type: [{ required: true, message: '请选择设备类型', trigger: 'blur' }]
      },
      // 工位数据
      stationData: [],
      dialogImageUrl: '',
      dialogVisible: false,
      // 文件上传
      currentUploadType: '',
      currentFilePath: '',
      uploadDrawerTitle: '文件上传',
      uploadDrawerVisbleSync: false,
      uploadLimit: 1,
      uploadAccept: '*.*',
      fileList: []
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.queryStation()
  },
  methods: {
    // 获取工位的中文描述
    getStationDes(station_code) {
      var item = this.stationData.find(item => item.station_code === station_code)
      if (item !== undefined) {
        return item.station_des
      }
      return station_code
    },
    // 工位LOV
    queryStation() {
      const query = {
        userID: Cookies.get('userName')
      }
      crudStation
        .lovStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
