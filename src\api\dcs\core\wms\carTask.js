import request from '@/utils/request'

// 查询WMS天车任务
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsCarTaskSelect',
    method: 'post',
    data
  })
}
// 删除WMS天车任务
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsCarTaskDelete',
    method: 'post',
    data
  })
}

// 修改WMS天车任务--修改有效标识
export function editEnableFlag(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsCarTaskEnableFlag',
    method: 'post',
    data
  })
}
// 新增抛丸任务
export function wmsPwTask(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsPwTask',
    method: 'post',
    data: data
  })
}
// 抛丸出库库位
export function wmsStockCode(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsFromStockCode',
    method: 'post',
    data: data
  })
}
// 查询抛丸传输工位
export function PwStockCode(data) {
  return request({
    url: 'aisEsbWeb/dcs/core/DcsApsWmsPwStockCode',
    method: 'post',
    data
  })
}
export default { sel,del, editEnableFlag, wmsStockCode, wmsPwTask, PwStockCode }

