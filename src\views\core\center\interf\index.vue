<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.interf.interfaceCodeDescription')">
                <!-- 接口编码/ 描述： -->
                <el-input v-model="query.esbInterfCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="120px" :inline="true">
          <el-form-item :label="$t('lang_pack.interf.interfaceCode')" prop="esb_interf_code">
            <!-- 接口代码 -->
            <el-input v-model="form.esb_interf_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.interf.interfaceDescription')" prop="esb_interf_des">
            <!-- 接口描述 -->
            <el-input v-model="form.esb_interf_des" />
          </el-form-item>

          <!--快速编码：INTERF_SYS-->
          <el-form-item :label="$t('lang_pack.interf.sourceSystem')" prop="data_from_sys">
            <!-- 来源系统 -->
            <el-select v-model="form.data_from_sys" clearable filterable>
              <el-option v-for="item in dict.INTERF_SYS" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <!--快速编码：INTERF_SYS-->
          <el-form-item :label="$t('lang_pack.interf.targetSystem')" prop="data_to_sys">
            <!-- 目标系统 -->
            <el-select v-model="form.data_to_sys" clearable filterable>
              <el-option v-for="item in dict.INTERF_SYS" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <!--快速编码：INTERF_WAY-->
          <el-form-item :label="$t('lang_pack.interf.interfaceMeans')" prop="esb_interf_way">
            <!-- 接口方式 -->
            <el-select v-model="form.esb_interf_way" clearable filterable>
              <el-option v-for="item in dict.INTERF_WAY" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.interf.testAddress')" prop="esb_dev_intef_url">
            <!-- 测试地址 -->
            <el-input v-model="form.esb_dev_intef_url" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.interf.officialAddress')" prop="esb_prod_intef_url">
            <!-- 正式地址 -->
            <el-input v-model="form.esb_prod_intef_url" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.interf.testReturnValue')" prop="esb_test_result">
            <!-- 测试返回值 -->
            <el-input v-model="form.esb_test_result" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <el-radio-group v-model="form.enable_flag">
              <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"  highlight-current-row @selection-change="crud.selectionChangeHandler" @row-click="handleRowClick">
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  v-if="1 == 0" width="10" prop="esb_interf_id" label="id" />
              <el-table-column  :show-overflow-tooltip="true" prop="esb_interf_code" :label="$t('lang_pack.interf.interfaceNumber')" width="140" />
              <!-- 接口编号 -->
              <el-table-column  :show-overflow-tooltip="true" prop="esb_interf_des" :label="$t('lang_pack.interf.interfaceDescription')" width="100" />
              <!-- 接口描述 -->

              <el-table-column  :label="$t('lang_pack.interf.sourceSystem')" align="center" prop="data_from_sys" width="100">
                <!-- 来源系统 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.INTERF_SYS[scope.row.data_from_sys] }}
                </template>
              </el-table-column>
              <el-table-column  :label="$t('lang_pack.interf.targetSystem')" align="center" prop="data_to_sys" width="100">
                <!-- 目标系统 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.INTERF_SYS[scope.row.data_to_sys] }}
                </template>
              </el-table-column>
              <el-table-column  :label="$t('lang_pack.interf.interfaceMeans')" align="center" prop="esb_interf_way" width="100">
                <!-- 接口方式 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.INTERF_WAY[scope.row.esb_interf_way] }}
                </template>
              </el-table-column>

              <el-table-column  :show-overflow-tooltip="true" prop="esb_dev_intef_url" :label="$t('lang_pack.interf.testAddress')" />
              <!-- 测试地址 -->
              <el-table-column  :show-overflow-tooltip="true" prop="esb_prod_intef_url" :label="$t('lang_pack.interf.productionAddress')" width="100"/>
              <!-- 生产地址 -->
              <el-table-column  :show-overflow-tooltip="true" prop="esb_test_result" :label="$t('lang_pack.interf.testReturnValue')" width="130"/>
              <!-- 测试返回值 -->
              <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
                <!-- 有效标识 -->
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
                </template>
              </el-table-column>
              <el-table-column  prop="button" :label="$t('lang_pack.commonPage.operate')" align="center" width="160" fixed="right">
                <template slot-scope="scope">
                  <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                    <template slot="right">
                      <el-button slot="reference" type="text" size="small" @click="$refs.interfItem && $refs.interfItem.crud.toAdd()">新增接口维护</el-button>
                    </template>
                  </udOperation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <interfItem ref="interfItem" class="tableFirst" :esb_interf_id="currentEsbInterfId" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import crudInterf from '@/api/core/center/interf'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import interfItem from '@/views/core/center/interf/interfItem'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  esb_interf_id: '',
  esb_interf_code: '',
  esb_interf_des: '',
  data_from_sys: '',
  data_to_sys: '',
  esb_interf_way: '',
  esb_dev_intef_url: '',
  esb_prod_intef_url: '',
  esb_test_result: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_INTERF',
  components: { crudOperation, rrOperation, udOperation, pagination, interfItem },
  props: {},
  cruds() {
    return CRUD({
      title: '接口维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'esb_interf_id',
      // 排序
      sort: ['esb_interf_id asc'],
      // CRUD Method
      crudMethod: { ...crudInterf },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: false,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      interfItemShow: false,
      currentEsbInterfId: 0,
      permission: {
        add: ['admin', 'sys_interf:add'],
        edit: ['admin', 'sys_interf:edit'],
        del: ['admin', 'sys_interf:del']
      },
      rules: {
        esb_interf_code: [{ required: true, message: '请输入接口编码', trigger: 'blur' }],
        data_from_sys: [{ required: true, message: '请输入来源系统', trigger: 'blur' }],
        data_to_sys: [{ required: true, message: '请输入目标系统', trigger: 'blur' }],
        esb_interf_way: [{ required: true, message: '请输入接口方式', trigger: 'blur' }]
      }
    }
  },
  watch: {},
  // 数据字典
  dicts: ['ENABLE_FLAG', 'INTERF_SYS', 'INTERF_WAY'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  created: function() {},
  methods: {
    handleRowClick(row, column, event) {
      this.currentEsbInterfId = row.esb_interf_id
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
</style>
