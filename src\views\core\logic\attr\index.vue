<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect wrapFile">
            <span>{{ $t('lang_pack.logicattr.logicalPropertyMaintenance') }}</span>  <!-- 逻辑属性维护 -->
          </div>
          <el-tree :data="treeData" :props="defaultProps" :highlight-current="true" @node-click="handleNodeClick" />
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-card class="box-card1" shadow="always" :logicattrgroupid="currentLogicAttrGroupId">
          <div slot="header" class="wrapTextSelect">
            <span>{{ rightHeaderTitle }}</span>
            <div class="wrapSearch">
              <div class="inputSearch">
                <el-input v-model="queryHeader.content" :placeholder="contentPlaceholder" class="filter-item inputItem" size="small" />
                <el-button class="filter-item buttonItem" size="small" type="primary" @click="handleQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
              </div>
              <el-button v-if="true" class="filter-item" size="small" type="primary" icon="el-icon-plus" plain @click="handleAdd">{{ $t('lang_pack.commonPage.add') }}</el-button>  <!-- 新增 -->
            </div>
          </div>
          <logicAttrGroup v-if="logicAttrGroupShow" ref="logicAttrGroup" @AddTreeNode="handleLogicAttrGroupAddTreeNode" @EditTreeNode="handleLogicAttrGroupEditTreeNode" @DelTreeNode="handleLogicAttrGroupDelTreeNode" />
          <logicAttrItem v-if="logicAttrItemShow" ref="logicAttrItem" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import logicAttrGroup from '@/views/core/logic/attr/logicAttrGroup'
import logicAttrItem from '@/views/core/logic/attr/logicAttrItem'
import { selLogicAttrGroup } from '@/api/core/logic/logicAttrGroup'

export default {
  name: 'RCS_LOGIC_ATTR',
  components: { logicAttrGroup, logicAttrItem },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',
      contentPlaceholder: '属性组编码/描述',
      rightHeaderTitle: '属性组列表',
      queryHeader: {
        content: ''
      },

      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'logic_attr_group_des'
      },
      currentNode: [],

      logicAttrGroupShow: true,
      logicAttrItemShow: false,
      currentLogicAttrGroupId: '0'
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },
  created: function() {
    // 树
    selLogicAttrGroup({})
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.length > 0) {
          this.treeData = []
          var a = {}
          a.level = 1
          a.logic_attr_group_id = 0
          a.logic_attr_group_code = ''
          a.logic_attr_group_des = '属性组'
          a.children = []
          for (let index = 0; index < defaultQuery.data.length; index++) {
            const item = defaultQuery.data[index]
            var b = {}
            b.level = 2
            b.logic_attr_group_id = item.logic_attr_group_id
            b.logic_attr_group_code = item.logic_attr_group_code
            b.logic_attr_group_des = item.logic_attr_group_des
            a.children.push(b)
          }
          this.treeData.push(a)
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    drawerClose() {
      // 关闭drawer时清理表单验证
      this.$refs['form'].resetFields()
    },
    handleNodeClick(data) {
      this.currentNode = data
      this.queryHeader.content = ''
      if (data.level === 1) {
        this.logicAttrItemShow = false
        if (this.logicAttrGroupShow) {
          this.$refs['logicAttrGroup'].toButQuery('')
        } else {
          this.logicAttrGroupShow = true
        }
        this.rightHeaderTitle = '属性组列表'
        this.contentPlaceholder = '属性组编码/描述'
      } else if (data.level === 2) {
        this.logicAttrGroupShow = false
        this.currentLogicAttrGroupId = data.logic_attr_group_id
        if (this.logicAttrItemShow) {
          this.$refs['logicAttrItem'].toButQuery(data.logic_attr_group_id, '')
        } else {
          this.logicAttrItemShow = true
        }
        this.rightHeaderTitle = '属性列表'
        this.contentPlaceholder = '属性编码/描述'
      }
    },

    handleLogicAttrGroupAddTreeNode(logic_attr_group_id, logic_attr_group_code, logic_attr_group_des) {
      const item = this.treeData.filter(item => item.level === 1)[0]
      if (!item.children) {
        this.$set(item, 'children', [])
      }
      item.children.push({
        level: 2,
        logic_attr_group_id: logic_attr_group_id,
        logic_attr_group_code: logic_attr_group_code,
        logic_attr_group_des: logic_attr_group_des
      })
    },
    handleLogicAttrGroupEditTreeNode(logic_attr_group_id, logic_attr_group_code, logic_attr_group_des) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const items = children.filter(item => item.logic_attr_group_id + '' === logic_attr_group_id + '')[0]
      items.logic_attr_group_code = logic_attr_group_code
      items.logic_attr_group_des = logic_attr_group_des
    },
    handleLogicAttrGroupDelTreeNode(logic_attr_group_id) {
      const children = this.treeData.filter(item => item.level === 1)[0].children
      const index = children.findIndex(d => d.logic_attr_group_id + '' === logic_attr_group_id + '')
      children.splice(index, 1)
    },

    handleQuery() {
      if (this.logicAttrGroupShow) {
        this.$refs['logicAttrGroup'].toButQuery(this.queryHeader.content)
      } else if (this.logicAttrItemShow) {
        this.$refs['logicAttrItem'].toButQuery(this.currentLogicAttrGroupId, this.queryHeader.content)
      }
    },
    handleAdd() {
      if (this.logicAttrGroupShow) {
        this.$refs['logicAttrGroup'].toButAdd()
      } else if (this.logicAttrItemShow) {
        this.$refs['logicAttrItem'].toButAdd()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.box-card1 {
  min-height: calc(100vh - 60px);
  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    font-weight: bold;
    color: #79a0f1;
  }
  .el-tree-node__expand-icon.is-leaf {
    color: #79a0f1;
  }
  .el-dialog__body {
    padding: 0px;
  }
}
::v-deep .el-card__header {
    padding: 8px;
    height: auto;
  }
.wrapTextSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    white-space: nowrap;
    font-size: 14px;
    color: rgb(117, 117, 117);
    font-weight: bold;
    margin-right: 10px;
  }
}
.wrapSearch {
  display: flex;
  align-items: center;
  .inputSearch {
    display: flex;
    align-items: center;
    margin-right: 15px;
    .inputItem {
      .el-input__inner {
        width: 150px;
        border-radius: 4px 0 0 4px !important;
      }
    }
    .buttonItem {
      border-radius: 0 0.25rem 0.25rem 0;
      margin-left: -5px;
      z-index: 9;
    }
  }
}
.wrapFile {
  height: 33px;
  line-height: 33px;
}
.el-tree--highlight-current {
  .el-tree-node__content {
    padding: 10px;
    height: auto;
  }
}
.el-tree-node__expand-icon {
  color: #79a0f1;
}
.el-tree-node__label {
  font-size: 12px;
}
.el-tree-node__content:hover {
  background-color: #e8efff;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e8efff;
}
</style>
