<template>
  <div class="app-container">
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          循环任务次数
        </template>
        <table>
          <tr>
            <td>
              <el-input ref="cycleTaskCount" v-model="cycleTaskCount" clearable size="mini" />
            </td>
            <td>
              <el-button type="primary" @click="handleCycleTask">写 入</el-button>
            </td>
          </tr>
        </table>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>

import { eapKrsCycleTaskWrite } from '@/api/eap/project/krs/eapKrsApsPlan'
import Cookies from 'js-cookie'
export default {
  name: 'cycleTask',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      cycleTaskCount: '1'
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
  },
  methods: {
    handleCycleTask() {
      this.$confirm('确定循环任务次数'+this.cycleTaskCount , this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      }).then(() =>{
        const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        cycle_task: this.cycleTaskCount
      }
      eapKrsCycleTaskWrite(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({
              message: this.$t('lang_pack.vie.cnneSuccess'),
              type: 'success'
            })
          } else {
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
      }).catch(() => {
      })
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>
