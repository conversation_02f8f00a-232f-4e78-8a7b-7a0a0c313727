<template>
  <div>
    <MainChart
      ref="chart"
      v-loading="loading"
      :nodes="nodes"
      :connections="connections"
      :width="'100%'"
      :height="'240'"
      :readonly="false"
      element-loading-text="拼命绘制中"
      @editnode="handleEditNode"
      @ok="handleCutNode"
    />
  </div>
</template>
<script>
import MainChart from '@/components/hmiImage/index'
export default {
  name: 'HMIIMAGE',
  components: { MainChart },
  data() {
    return {
      connections: [],
      loading: false,
      nodes: [
        {
          type: 'img',
          x: 0,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/1.jpeg')
        },
        {
          type: 'img',
          x: 30,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/2.jpeg')
        },
        {
          type: 'img',
          x: 60,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/3.jpeg')
        },
        {
          type: 'img',
          x: 90,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/4.jpeg')
        },
        {
          type: 'img',
          x: 120,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/5.jpg')
        },
        {
          type: 'img',
          x: 150,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/6.jpeg')
        },
        {
          type: 'img',
          x: 30,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/7.jpeg')
        },
        {
          type: 'img',
          x: 210,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/8.jpeg')
        },
        {
          type: 'img',
          x: 240,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/9.jpeg')
        },
        {
          type: 'img',
          x: 270,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/10.jpg')
        },
        {
          type: 'img',
          x: 300,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/11.jpeg')
        },
        {
          type: 'img',
          x: 330,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/12.jpeg')
        },
        {
          type: 'img',
          x: 360,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/13.jpg')
        },
        {
          type: 'img',
          x: 390,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/14.jpeg')
        },
        {
          type: 'img',
          x: 420,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/15.jpeg')
        },
        {
          type: 'img',
          x: 450,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/16.jpeg')
        },
        {
          type: 'img',
          x: 480,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/17.jpeg')
        },
        {
          type: 'img',
          x: 510,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/18.jpeg')
        },
        {
          type: 'img',
          x: 540,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/19.jpeg')
        },
        {
          type: 'img',
          x: 570,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/20.jpeg')
        },
        {
          type: 'img',
          x: 600,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/21.jpeg')
        },
        {
          type: 'img',
          x: 630,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/22.jpeg')
        },
        {
          type: 'img',
          x: 660,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/23.jpeg')
        },
        {
          type: 'img',
          x: 690,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/24.jpeg')
        },
        {
          type: 'img',
          x: 720,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/25.jpg')
        },
        {
          type: 'img',
          x: 750,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/26.jpeg')
        },
        {
          type: 'img',
          x: 780,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/27.jpeg')
        },
        {
          type: 'img',
          x: 810,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/28.jpeg')
        },
        {
          type: 'img',
          x: 840,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/29.jpeg')
        },
        {
          type: 'img',
          x: 870,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/30.jpeg')
        },
        {
          type: 'img',
          x: 900,
          y: 0,
          width: 30,
          height: 30,
          href: require('@/assets/images/hmiMain/31.jpg')
        }
      ]
    }
  },
  created() {
    // this.$refs.chart.add({
    //     id: +new Date(),
    //     x: 20,
    //     y: 20,
    //     type: 'circle',
    //     index: 1,
    //     width: 25,
    //     height: 25,
    //     color: '#C8CACC',
    //     describe: '圆圈',
    //     stroke: '#FFFFFF',
    //     strokeWidth: '2px',
    //     status: 'WAIT'
    // })
    for (let i = 1; i < 5; i++) {
      this.nodes.push(
        {
          id: +new Date(),
          x: (i * 10),
          y: 10,
          type: 'img',
          index: 1,
          width: 25,
          height: 25,
          color: '#C8CACC',
          describe: '圆圈',
          stroke: '#FFFFFF',
          strokeWidth: '2px',
          status: 'WAIT',
          href: require(`@/assets/images/hmiMain/${i}.jpeg`)
        }
      )
    }
  },
  methods: {
    handleEditNode() {},
    handleCutNode() {}
  }
}
</script>
