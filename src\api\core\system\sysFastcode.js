import request from '@/utils/request'

// 查询快速编码组
export function sel(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeGroupSel',
    method: 'post',
    data
  })
}
// 新增快速编码组
export function add(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeGroupIns',
    method: 'post',
    data
  })
}
// 修改快速编码组
export function edit(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeGroupUpd',
    method: 'post',
    data
  })
}
// 删除快速编码组
export function del(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeGroupDel',
    method: 'post',
    data
  })
}

// 快速编码Lov
export function lovFastcode(data) {
  return request({
    url: 'aisEsbWeb/core/system/CoreSysFastCodeLov',
    method: 'post',
    data
  })
}
export function getDictDetail(dictName) {
  const params = {
    dictName
  }
  return request({
    url: 'aisEsbWeb/core/system/CoreSysGetDictDetail',
    method: 'get',
    params
  })
}
export default { sel, add, edit, del, lovFastcode }
