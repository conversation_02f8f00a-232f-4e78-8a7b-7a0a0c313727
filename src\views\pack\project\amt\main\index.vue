<template>
  <div id="loadMonitor">
    <el-container>
      <el-header style="background-color: #d2dae7">
        <div class="statuHead">
          <div>
            <!-- statr -->
            <el-button
              size="medium"
              type="primary"
              :icon="
                controlValue === '1'
                  ? 'el-icon-video-play'
                  : 'el-icon-video-pause'
              "
              class="playBtn"
              :class="{
                playStart: controlValue === '1',
                playEnd: controlValue != '1',
              }"
              @click="handleTagWrite('BzPlc01/PlcBase/AppStart')"
            >{{ controlValue === "1" ? "Start" : "Stop" }}</el-button>
          </div>
          <div>
            <div class="wrappstyle">
              <p><span
                   :class="controlStatus.device_plc_status === '1' ? 'wholeline1 wholelinenormal1' : 'wholeline1 wholelinegray1'"
                 />
                <span class="statuText">{{ $t('lang_pack.vie.equAllPLCIss') }}</span>
              </p>
              <p>
                <span
                  :class="
                    controlStatus.light_status === '1'
                      ? 'wholeline1 wholelinenormal1'
                      : controlStatus.light_status === '2'
                        ? 'wholeline1 wholelineerror1'
                        : controlStatus.light_status === '3'
                          ? 'wholeline1 deviceRed'
                          : 'wholeline1 wholelinegray1'
                  "
                />
                <span class="statuText">{{
                  $t("lang_pack.vie.triColorLight")
                }}</span>
              </p>
              <p v-if="controlStatus.dev_status">
                <span
                  :class="`wholeline1 ${
                    deviceStatus[controlStatus.dev_status].type
                  }`"
                />
                <span class="statuText">{{
                  deviceStatus[controlStatus.dev_status].status
                }}</span>
              </p>
              <p v-else>
                <span :class="`wholeline1 ${deviceStatus[0].type}`" />
                <span class="statuText">{{ deviceStatus[0].status }}</span>
              </p>
              <el-divider direction="vertical" style="width: 2px" />
              <p>
                <span
                  :class="
                    controlStatus.BzPlc01Status === '1'
                      ? 'wholeline wholelinenormal'
                      : controlStatus.BzPlc01Status === '2'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">包装机01</span>
              </p>
              <p>
                <span
                  :class="
                    controlStatus.BzPlc02Status === '1'
                      ? 'wholeline wholelinenormal'
                      : controlStatus.BzPlc02Status === '2'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">包装机02</span>
              </p>
              <p>
                <span
                  :class="
                    controlStatus.PmCcdStatus === '1'
                      ? 'wholeline wholelinenormal'
                      : controlStatus.PmCcdStatus === '2'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">喷墨CCD</span>
              </p>
              <p>
                <span
                  :class="
                    controlStatus.PmPrintStatus === '1'
                      ? 'wholeline wholelinenormal'
                      : controlStatus.PmPrintStatus === '2'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">喷墨打印机</span>
              </p>
            </div>
          </div>
        </div>
      </el-header>
      <el-main>
        <div class="peopleInfo">
          <div class="peopleDetail">
            <div class="peopleMsg" style="margin-left: 20px">
              {{ $t("lang_pack.vie.employeeID") }}
              <div class="dataInfo">{{ loginInfo.user_name }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.vie.name") }}
              <div class="dataInfo">{{ loginInfo.nick_name }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.vie.department") }}
              <div class="dataInfo">{{ loginInfo.dept_id }}</div>
            </div>
            <div class="peopleMsg">
              {{ $t("lang_pack.vie.classes") }}
              <div class="dataInfo">{{ loginInfo.shift_id }}</div>
            </div>
          </div>
          <div>
            <el-button type="primary" @click="openUserLogin">{{
              $t("lang_pack.vie.login")
            }}</el-button>
            <el-button type="danger" @click="handleUserLogout">{{
              $t("lang_pack.vie.logOut")
            }}</el-button>
          </div>
        </div>
        <div class="wraptable">
          <!-- lot no -->
          <el-table :data="orderData" border style="width: 100%" height="85">
            <!-- <el-table-column prop="lot_num" label="单号" /> -->
            <!-- 批号 -->
            <el-table-column
              prop="lot_no"
              :label="$t('view.field.plan.lotNum')"
            />
            <!-- 来源 -->
            <el-table-column
              prop="task_from"
              :label="$t('lang_pack.vie.taskSource')"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_FROM[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 料号 -->
            <el-table-column :show-overflow-tooltip="true" prop="model_type" :label="$t('lang_pack.vie.partNum')" align="center" />
            <el-table-column :show-overflow-tooltip="true" prop="model_version" :label="$t('lang_pack.vie.version')" align="center" />
            <el-table-column prop="unit_count" label="单包uint数量" />
            <!-- <el-table-column :show-overflow-tooltip="true" prop="tail_count" label="尾包uint数量" align="center" /> -->
            <!-- <el-table-column :show-overflow-tooltip="true" prop="tray_material_code" label="Tray物料编码" align="center" /> -->
            <el-table-column :show-overflow-tooltip="true" prop="item_date_val" label="订单开始时间" align="center">
              <template slot-scope="scope">
                {{ scope.row.item_date | dateFormat }}
              </template>
            </el-table-column>
            <!-- 订单耗时 -->
            <!-- <el-table-column :show-overflow-tooltip="true" prop="task_cost_time" label="耗时" align="center" /> -->
          </el-table>
        </div>
        <!-- <div class="wraptable">
          <el-table :data="XoutData" border style="width: 100%" height="85">
            <el-table-column
              prop="tray_height"
              label="Tray高度(um)"
            />
            <el-table-column
              prop="tray_weight"
              label="Tray重量(g)"
            >
              <template slot-scope="scope">
                {{ scope.row.tray_weight / 1000 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="tray_disk_capacity"
              label="Tray容积"
            />
            <el-table-column prop="overlay_height" label="Overlay高度(um)" />
            <el-table-column prop="overlay_weight" label="Overlay重量(g)">
              <template slot-scope="scope">
                {{ scope.row.overlay_weight / 1000 }}
              </template>
            </el-table-column>
            <el-table-column prop="weight_error" label="重量误差(g)">
              <template slot-scope="scope">
                {{ scope.row.weight_error / 1000 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="bd_way1"
              label="束带方式(十字)"
            />
            <el-table-column
              prop="bd_way3"
              label="束带方式(一字)"
            />
          </el-table>
        </div> -->
        <iframeView :height="'500'" :src="'http://127.0.0.1:5885/#/monitor?id=1'" />
        <!-- <el-row :gutter="20">
        </el-row> -->
      </el-main>
    </el-container>
    <el-dialog
      :title="$t('lang_pack.vie.employeeLogin')"
      :visible.sync="userDialogVisible"
      width="650px"
      top="65px"
    >
      <el-input
        ref="userId"
        v-model="userId"
        clearable
        size="mini"
        style="width: 100%"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">{{
          $t("lang_pack.vie.cancel")
        }}</el-button>
        <el-button type="primary" @click="handleUserLogin">{{
          $t("lang_pack.vie.login")
        }}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="$t('lang_pack.vie.messageAlert')"
      :visible.sync="contentMessage.dialogVisible"
      top="65px"
    >
      <span>{{ contentMessage.content }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">{{
          $t("lang_pack.vie.close")
        }}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="jsonTitle" width="50%" :visible.sync="jsonDialogVisible">
      <el-input
        v-model="jsonStr"
        autosize
        type="textarea"
        :rows="20"
        :placeholder="$t('lang_pack.interfaceLogs.pleaseEnter')"
        style="max-height: 450px; overflow: auto"
      />
      <span style="color: red">{{ jsonErrorMsg }}</span>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { selCellIP } from '@/api/core/center/cell'
import {
  selLoginInfo,
  userLogin,
  userLogout
} from '@/api/eap/eapMeStationUser'
import {
  InfoSelect
} from '@/api/pack/task'
import { sel as planSel } from '@/api/pack/core/plan'
import { sel as traySel } from '@/api/pack/project/kinsus/tray-recipe'
import { sel as ReicpeSel } from '@/api/pack/core/meReicpe'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import { MesReturnMsgSel } from '@/api/pack/monitorIndex'
import iframeView from '@/views/core/hmiMain/iframe.vue'
// 全局变量获取存储在浏览器中语言方式
// const _LANGUAGE = localStorage.getItem('language').split('-')[1] || 'zh-CN'
export default {
  name: 'EAP_UNLOAD_MONITOR_HMI',
  components: { iframeView },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 715,
      setValueData: [
        { title: 'SET01', val: '', back: '0' },
        { title: 'SET02', val: '', back: '0' },
        { title: 'SET03', val: '', back: '0' },
        { title: 'SET04', val: '', back: '0' },
        { title: 'SET05', val: '', back: '0' },
        { title: 'SET06', val: '', back: '0' },
        { title: 'SET07', val: '', back: '0' },
        { title: 'SET08', val: '', back: '0' },
        { title: 'SET09', val: '', back: '0' },
        { title: 'SET10', val: '', back: '0' },
        { title: 'SET11', val: '', back: '0' },
        { title: 'SET12', val: '', back: '0' },
        { title: 'SET13', val: '', back: '0' },
        { title: 'SET14', val: '', back: '0' },
        { title: 'SET15', val: '', back: '0' },
        { title: 'SET16', val: '', back: '0' },
        { title: 'SET17', val: '', back: '0' },
        { title: 'SET18', val: '', back: '0' },
        { title: 'SET19', val: '', back: '0' },
        { title: 'SET20', val: '', back: '0' },
        { title: 'SET21', val: '', back: '0' },
        { title: 'SET22', val: '', back: '0' },
        { title: 'SET23', val: '', back: '0' },
        { title: 'SET24', val: '', back: '0' },
        { title: 'SET25', val: '', back: '0' }
      ],
      setValueData01: [
        {
          title: this.$t('lang_pack.vie.orderUseTime'),
          val: '',
          key: 'cost_time'
        },
        {
          title: this.$t('lang_pack.vie.xoutNum'),
          val: '',
          key: 'xout_act_num'
        },
        {
          title: this.$t('lang_pack.vie.InnerOrder'),
          val: '',
          key: 'array_index'
        },
        {
          title: this.$t('lang_pack.vie.judgingTheResult'),
          val: '',
          key: 'board_result'
        },
        {
          title: this.$t('lang_pack.vie.setInnerCode'),
          val: '',
          key: 'array_barcode'
        },
        {
          title: this.$t('lang_pack.vie.rotationDirection'),
          val: '',
          key: 'board_turn'
        },
        {
          title: this.$t('lang_pack.vie.InnerLevel'),
          val: '',
          key: 'array_level'
        },
        {
          title: this.$t('lang_pack.vie.stackingPosition'),
          val: '',
          key: 'deposit_position'
        },
        {
          title: this.$t('lang_pack.vie.InnerFront'),
          val: '',
          key: 'array_front_info'
        },
        {
          title: this.$t('lang_pack.vie.InnerOpposite'),
          val: '',
          key: 'array_back_info'
        },
        {
          title: this.$t('lang_pack.vie.sortingError'),
          val: '',
          key: 'array_ng_msg'
        }
      ],
      setValueData02: [
        {
          title: this.$t('lang_pack.vie.packOrder'),
          val: '',
          key: 'finish_lable_count'
        },
        {
          title: this.$t('lang_pack.vie.packCode'),
          val: '',
          key: 'pile_barcode'
        },
        { title: this.$t('lang_pack.vie.setNum'), val: '', key: 'array_count' },
        {
          title: this.$t('lang_pack.vie.IsItATrunk'),
          val: '',
          key: 'trunk_flag'
        },
        {
          title: this.$t('lang_pack.vie.packError'),
          val: '',
          key: 'pile_ng_msg'
        }
      ],
      indicatorr: null,
      controlValue: '',
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 控制器状态与心跳
      controlStatus: {
        device_plc_status: '',
        light_status: '',
        dev_status: '',
        BzPlc01Status: '0',
        BzPlc02Status: '0',
        PmCcdStatus: '0',
        PmPrintStatus: '0'
      },
      // 设备状态
      deviceStatus: {
        0: { val: '0', status: this.$t('lang_pack.vie.run'), type: 'wholelinegray1' },
        1: { val: '1', status: this.$t('lang_pack.vie.run'), type: 'deviceGreen' },
        2: { val: '2', status: this.$t('lang_pack.vie.alarm'), type: 'deviceRed' },
        3: { val: '3', status: this.$t('lang_pack.vie.stop'), type: 'deviceRed' },
        4: { val: '4', status: this.$t('lang_pack.vie.preserve'), type: 'deviceYellow' },
        5: { val: '5', status: this.$t('lang_pack.vie.mecFai'), type: 'deviceRed' },
        6: { val: '6', status: this.$t('lang_pack.vie.repair'), type: 'deviceYellow' }
      },
      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      userDialogVisible: false,
      userId: '',
      orderData: [],
      // 启用监听数据模式 AIS-PC=电脑模式,AIS-SERVER=服务器模式
      // AIS-SERVER模式，监听的Client Code需要拼接上工位编码,例如：LoadPlc_OP1010
      aisMonitorMode: 'AIS-PC',
      XoutData: [],
      mes_return_msg: '',
      messageList: [],
      messageShow: false,
      contentMessage: {
        content: '',
        level: 'info',
        dialogVisible: false
      },
      timer: null,
      timerVisible: null,
      resultObj: {},
      jsonStr: '',
      jsonErrorMsg: '',
      jsonDialogVisible: false,
      jsonTitle: '',
      pileObj: {},
      XOUTFlag: true,
      ccdImage: '',
      isResponse: true
    }
  },
  dicts: [
    'ENABLE_FLAG',
    'TASK_STATUS',
    'TASK_TYPE',
    'TASK_FROM',
    'QR_TYPE',
    'XOUT_TYPE',
    'DEPOSIT_POSITION'
  ],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 40
    }
    this.getStationData()
    // 获取表格订单
    this.getOrderData()
    // this.getMesMsg()
  },
  created: function() {
    this.timer = setInterval(() => {
      if (!this.isResponse) {
        return
      }
      // 获取表格订单
      this.getOrderData()
      // this.getMesMsg()
    }, 1000 * 5)
    var queryParameter = {
      userName: Cookies.get('userName'),
      cell_id: '0',
      parameter_code: 'AIS_MONITOR_MODE',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.aisMonitorMode = defaultQuery.data[0].parameter_val
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        cell_id: this.$route.query.cell_id
      }
      this.getCellIp()
      this.getLoginInfo()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    getTagValue() {
      const readTagArray = [
        { tag_key: 'BzPlc01/PlcBase/AppStart' },
        { tag_key: 'BzPlc01/Plc01Status/DeviceAllows' },
        { tag_key: 'BzPlc01/PlcBase/TriColorLamp' },
        { tag_key: 'BzPlc01/PlcBase/Status' }
      ]
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: { 'Content-Type': 'application/json' }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  if (tagKey === 'BzPlc01/PlcBase/AppStart') {
                    this.controlValue = tagValue
                  } else if (tagKey === 'BzPlc01/Plc01Status/DeviceAllows') {
                    this.controlStatus.device_plc_status = tagValue
                  } else if (tagKey === 'BzPlc01/PlcBase/TriColorLamp') {
                    this.controlStatus.light_status = tagValue
                  } else if (tagKey === 'BzPlc01/PlcBase/Status') {
                    this.controlStatus.dev_status = tagValue
                  }
                }
              }
            }
          }
        })
        .catch((ex) => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    getOrderData() {
      const query = {
        page: 1,
        size: 1,
        sort: 'plan_id desc',
        user_name: Cookies.get('userName'),
        lot_status: 'WORK'
      }
      planSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.orderData = defaultQuery.data || []
            const objectTrayData = defaultQuery.data.length > 0 && defaultQuery.data[0]
            // 如果没有数据,则清空
            // 调用tray配方维护数据
            this.getTrayRecipeData(objectTrayData)
          } else {
            this.XoutData = []
            this.orderData = []
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch((err) => {
          this.orderData = []
          this.XoutData = []
          this.$message({ message: err.msg, type: 'error' })
        })
    },
    getTrayRecipeData(objectTrayData) {
      const query = { tray_material_code: objectTrayData.tray_material_code }
      traySel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0 && defaultQuery.data.length > 0) {
          this.getXoutData(defaultQuery.data[0], objectTrayData)
        }
      })
    },
    getMesMsg() {
      const query = {}
      MesReturnMsgSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.mes_return_msg = defaultQuery.result || ''
          } else {
            this.mes_return_msg = ''
            this.$message({
              message: defaultQuery.msg,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          this.mes_return_msg = ''
          this.$message({
            message: err.msg,
            type: 'error'
          })
        })
    },
    getXoutData(trayRecipeData, objectTrayData) {
      console.log(objectTrayData)
      if (!objectTrayData.model_type && !objectTrayData.model_version) {
        this.XoutData = []
        return
      }
      const query = { model_type: objectTrayData.model_type, model_version: objectTrayData.model_version }
      ReicpeSel(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              const obj = defaultQuery.data[0]
              obj.tray_weight = trayRecipeData.tray_weight
              obj.tray_height = trayRecipeData.tray_height
              obj.tray_disk_capacity = objectTrayData.tray_disk_capacity
              this.XoutData = [obj]
            }
          } else {
            this.XoutData = []
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch((err) => {
          this.XoutData = []
          this.$message({ message: err.msg, type: 'error' })
        })
    },
    getLotStatus(id) {
      var item = this.dict.TASK_STATUS.find((item) => item.id === id)
      if (item !== undefined) {
        return item.label
      }
      return ''
    },
    // 获取当前登录信息
    getLoginInfo() {
      this.loginInfo.user_name = '---'
      this.loginInfo.nick_name = '---'
      this.loginInfo.dept_id = '---'
      this.loginInfo.shift_id = '---'
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    // 处理登录
    handleUserLogin() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        user_code: this.userId
      }
      userLogin(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const loginInfo = defaultQuery.data[0]
            this.loginInfo.user_name = loginInfo.user_name
            this.loginInfo.nick_name = loginInfo.nick_name
            this.loginInfo.dept_id = loginInfo.dept_id
            this.loginInfo.shift_id = loginInfo.shift_id
            this.userDialogVisible = false
            this.$message({
              message: this.$t('lang_pack.vie.loginSuccess'),
              type: 'success'
            })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    // 打开登录操作
    openUserLogin() {
      this.userId = ''
      this.userDialogVisible = true
      this.$nextTick((x) => {
        // 正确写法
        this.$refs.userId.focus()
      })
    },
    // 处理登出
    handleUserLogout() {
      this.$confirm(
        this.$t('lang_pack.vie.AreYouSureToLogOut'),
        this.$t('lang_pack.vie.prompt'),
        {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          type: 'warning'
        }
      )
        .then(() => {
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.currentStation.station_id
          }
          userLogout(query)
            .then((res) => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.loginInfo.user_name = ''
                this.loginInfo.nick_name = ''
                this.loginInfo.dept_id = ''
                this.loginInfo.shift_id = ''
                this.$message({
                  message: this.$t('lang_pack.vie.loginSuccess'),
                  type: 'success'
                })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              this.$message({
                message: this.$t('lang_pack.vie.operationException'),
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    // ----------------------------------【MQTT】----------------------------------
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://************:8083' + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        this.topicSubscribe('SCADA_CHANGE/BzPlc01/PlcBase/AppStart')
        this.topicSubscribe('SCADA_CHANGE/BzPlc01/Plc01Status/DeviceAllows')
        this.topicSubscribe('SCADA_CHANGE/BzPlc01/PlcBase/TriColorLamp')
        this.topicSubscribe('SCADA_CHANGE/BzPlc01/PlcBase/Status')
        // 监控线扫照片
        this.topicSubscribe('SCADA_CHANGE/Ccd/CcdStatus/PanelPic')
        // 监控通讯状态
        var BzPlc01 = 'BzPlc01'
        var BzPlc02 = 'BzPlc02'
        var PmCcd = 'PmCcd'
        var PmPrint = 'PmPrint'
        if (this.aisMonitorMode === 'AIS-SERVER') {
          BzPlc01 = BzPlc01 + '_' + this.currentStation.station_code
          BzPlc02 = BzPlc02 + '_' + this.currentStation.station_code
          PmCcd = PmCcd + '_' + this.currentStation.station_code
          PmPrint = PmPrint + '_' + this.currentStation.station_code
        }
        this.topicSubscribe('SCADA_STATUS/' + BzPlc01)
        this.topicSubscribe('SCADA_BEAT/' + BzPlc01)
        this.topicSubscribe('SCADA_STATUS/' + BzPlc02)
        this.topicSubscribe('SCADA_BEAT/' + BzPlc02)
        this.topicSubscribe('SCADA_STATUS/' + PmCcd)
        this.topicSubscribe('SCADA_BEAT/' + PmCcd)
        this.topicSubscribe('SCADA_STATUS/' + PmPrint)
        this.topicSubscribe('SCADA_BEAT/' + PmPrint)
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          var TagKey = jsonData.TagKey
          var TagNewValue = jsonData.TagNewValue
          if (topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0) {
            this.handleMessage(jsonData)
          } else if (TagKey === 'BzPlc01/PlcBase/AppStart') {
            // statr
            this.controlValue = TagNewValue
          } else if (TagKey === 'BzPlc01/Plc01Status/DeviceAllows') {
            // 设备允许plc下发
            this.controlStatus.device_plc_status = TagNewValue
          } else if (TagKey === 'BzPlc01/PlcBase/TriColorLamp') {
            // 三色灯
            this.controlStatus.light_status = TagNewValue
          } else if (TagKey === 'BzPlc01/PlcBase/Status') {
            // 设备状态
            this.controlStatus.dev_status = TagNewValue
          } else if (topic.indexOf('SCADA_BEAT/') >= 0) {
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf('SCADA_BEAT/BzPlc01') >= 0) {
              // 心跳
              if (this.controlStatus.BzPlc01Status !== '2') {
                this.controlStatus.BzPlc01Status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/BzPlc02') >= 0) {
              if (this.controlStatus.BzPlc02Status !== '2') {
                this.controlStatus.BzPlc02Status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/PmCcd') >= 0) {
              if (this.controlStatus.PmCcdStatus !== '2') {
                this.controlStatus.PmCcdStatus = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/PmPrint') >= 0) {
              if (this.controlStatus.PmPrintStatus !== '2') {
                this.controlStatus.PmPrintStatus = heartBeatValue
              }
            }
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            if (topic === 'SCADA_CHANGE/Ccd/CcdStatus/PanelPic') {
              this.ccdImage = 'data:image/png;base64,' + jsonData.TagNewValue
            }
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    handleTagWrite(tagKey) {
      this.controlValue = this.controlValue === '1' ? '2' : '1'
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: this.controlValue
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      console.log(newRow)
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/BzPlc01'
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        }
      })
    },
    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'pack_monitor') return
      // 若是员工登入
      if (json.func_code === 'user_login_in') {
        if (!this.userDialogVisible) {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.openUserLogin()
        }
        return
      }
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      }
      if (json.func_code === 'array_result') {
        this.resultObj = json.func_paras
        // 判断从 mqtt中读取到ng 和ok数量要返回到 表格中去
        if (this.resultObj.finish_ok_count >= 0) {
          this.XoutData[0].finish_ok_count = this.resultObj.finish_ok_count
        }
        if (this.resultObj.finish_ng_count >= 0) {
          this.XoutData[0].finish_ng_count = this.resultObj.finish_ng_count
        }
        for (const k in this.resultObj) {
          for (let i = 0; i < this.setValueData01.length; i++) {
            if (this.setValueData01[i].key === k) {
              this.setValueData01[i].val = this.resultObj[k]
              break
            }
          }
        }
      }
      if (json.func_code === 'pile_result') {
        this.pileObj = json.func_paras
        const plcArrayList = this.pileObj.plc_array_list.split(',')
        const pcArrayList = this.pileObj.pc_array_list.split(',')
        for (const k in this.pileObj) {
          for (let i = 0; i < this.setValueData02.length; i++) {
            if (this.setValueData02[i].key === k) {
              this.setValueData02[i].val = this.pileObj[k]
              break
            }
          }
        }
        // 更新setValueData数组
        var updatedSetValueData = this.setValueData.map(function(item, index) {
          var val = ''
          var back = '0'
          // 如果plcArrayList和pcArrayList相应索引的值相等
          if (plcArrayList[index] === pcArrayList[index]) {
            val = plcArrayList[index]
          } else {
            back = '1'
            val = plcArrayList[index]
          }
          return { title: item.title, val: val, back: back }
        })

        this.setValueData = updatedSetValueData
      }
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      this.contentMessage.content = json.msg
      this.contentMessage.level = 'info'
      if (json.msg_level === 'INFO') {
        this.contentMessage.level = 'info'
      } else if (json.msg_level === 'ERROR') {
        this.contentMessage.level = 'error'
      } else if (json.msg_level === 'WARN') {
        this.contentMessage.level = 'warning'
      }
      this.contentMessage.dialogVisible = false
      if (json.dlg_second > 0) {
        this.timerVisible = setTimeout(() => {
          clearTimeout(this.timerVisible)
          this.contentMessage.dialogVisible = false
        }, json.dlg_second)
      }
    },
    handleClose() {
      this.timerVisible && clearTimeout(this.timerVisible)
      this.contentMessage.dialogVisible = false
    },
    viewsDetails(title, dataKey) {
      if (!this.resultObj.array_id) {
        this.$message({
          type: 'warning',
          message: this.$t('lang_pack.vie.noIDFound')
        })
        return
      }
      const query = {
        array_id: this.resultObj.array_id
      }
      this.jsonTitle = title
      this.jsonStr = '{}'
      this.jsonErrorMsg = ''
      InfoSelect(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // var _json = ''
            // if (title === this.$t(this.frontLangKey)) {
            //   _json = JSON.parse(defaultQuery.data[0].array_front_info)
            //   this.jsonStr = JSON.stringify(_json, null, 4)
            // } else {
            //   _json = JSON.parse(defaultQuery.data[0].array_back_info)
            //   this.jsonStr = JSON.stringify(_json, null, 4)
            // }
            // const json = JSON.parse(defaultQuery.data[0].array_front_info)
            this.jsonStr = JSON.stringify(JSON.parse(defaultQuery.data[0][dataKey]), null, 4)
            this.jsonDialogVisible = true
          } else {
            this.jsonStr = ''
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch((ex) => {
          this.jsonStr = ''
          this.jsonErrorMsg = ex
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    }
  }
}
</script>

  <style>
#loadMonitor {
  padding: 5px 5px 5px 5px;
}

#loadMonitor .el-header {
  /* background-image: linear-gradient(to right, #D0DEFA,#D0DEFA,#9EBAF4); */
  color: #333;
  height: 50px !important;
  line-height: 50px;
  padding-left: 5px !important;
}

#loadMonitor .el-main {
  background-color: #fff;
  color: #333;
  padding: 5px;
  overflow: hidden;
}

#loadMonitor .el-descriptions {
  background-color: #9ebaf4;
}

#loadMonitor .table-descriptions-label {
  width: 150px;
}

#loadMonitor .table-descriptions-content {
  width: 150px;
  color: #333;
  font-weight: 600;
}

#loadMonitor .port-title {
  background-image: linear-gradient(to right, #d0defa, #d0defa, #9ebaf4);
  color: #333;
  line-height: 45px;
  font-weight: 600;
  padding-left: 15px;
}

#loadMonitor .port-table-descriptions-label {
  width: 150px;
}

#loadMonitor .port-table-descriptions-content {
  color: #333;
  font-weight: 600;
}

#loadMonitor .message {
  color: #000;
  font-weight: 600;
  padding: 5px 10px 5px 10px;
  border-radius: 10px;
}

#loadMonitor .message-info {
  background-color: #7aa1ef;
}

#loadMonitor .message-warning {
  background-color: #fbb85a;
}

#loadMonitor .message-error {
  background-color: #f56c6c;
}

.warningMsg {
  height: 135px !important;
  white-space: wrap !important;
  word-wrap: break-word;
  /* 超出宽度自动换行 */
}

.warninArrMsg {
  height: 50px !important;
  white-space: wrap !important;
  word-wrap: break-word;
  /* 超出宽度自动换行 */
}
</style>
  <style lang="less" scoped>
#loadMonitor .el-header {
  .btnone {
    background: #50d475;
    border-color: #50d475;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
  }

  .btnone:active {
    background: #13887c;
  }

  .btntwo {
    background: #5a6671;
    border: 1px solid #5a6671;
  }

  .btntwo:active {
    background: #096ca8;
  }

  // button {
  //   border-radius: 0.25rem;
  //   width: 104px;
  //   height: 36px;
  //   font-size: 15px;
  // }
  .playBtn {
    border-radius: 0.25rem;
    font-size: 24px;
    border: none;
    width: 120px;
    height: 40px;
    margin-top: 5px;
  }

  .playStart {
    background-color: #0cd80c;
  }

  .playEnd {
    background-color: #ff0000;
  }
}

table.table3 {
  text-align: center;
  border-collapse: collapse;
  width: 100%;
  margin: 5px 0;
}

.table3 tbody td {
  color: #000000;
  font-size: 13px;
  font-weight: normal;
  border: 1px solid #9596a5;
  height: 30px;
  background-color: #ffffff;
  white-space: nowrap;
}

.tdName {
  background-color: #f2f2f2 !important;
  width: 160px;
  color: #5c717d;
  box-shadow: inset 2px 2px 2px 0px rgb(255 255 255 / 50%),
    inset -7px -7px 10px 0px rgb(0 0 0 / 10%), 7px 7px 20px 0px rgb(0 0 0 / 10%),
    4px 4px 5px 0px rgb(0 0 0 / 10%);
  text-shadow: 2px 2px 3px rgb(255 255 255 / 50%),
    -4px -4px 6px rgb(116 125 136 / 20%);
}

.loginStyle {
  width: 150px;

  button {
    width: 162px;
    height: 40px;
    font-weight: 700;
    font-size: 16px;
    background-color: #229f99;
    border: 0;
    border-radius: 0px;
  }

  button:active {
    background-color: #0a8c86;
  }
}

.normal {
  color: #67c23a;
  font-weight: 700;
}

.zaixian {
  color: #81ff42 !important;
}

.operatingEMode {
  color: #67c23a !important;
}

.lixian {
  color: #000000 !important;
}

.showImg {
  background: #f2f6ff !important;
  color: #333333;
  width: 29%;
  height: 220px !important;
  border: 1px solid #9596a5;
  margin-top: 5px;
  margin-right: 5px;
  text-align: center;

  img {
    height: 213px !important;
  }
}

.qianzhi {
  button {
    color: #ffffff;
    font-size: 16px;
    height: 36px;
    width: 129px;
    border-radius: 0.25rem;
    border: 0;
    margin: 10px 0;
    word-spacing: -2px;
    background: #3398cb;
  }

  button:active {
    background-color: #096ca8;
  }
}

.wraptable {
  display: flex;
}

.jianju {
  width: 45px;
  display: inline-block;
}

::v-deep .el-table {
  margin-top: 5px;
  border: 1px solid #9596a5 !important;
}

::v-deep .el-table th {
  color: #fff;
  font-size: 16px;
  font-weight: normal;
  background-color: #4777db !important;
  outline: none;
  height: 35px !important;
  padding: 0;
  border-bottom: 1px solid #9596a5 !important;
}

::v-deep .el-table_1_column_1,
::v-deep .el-table_2_column_7 {
  border-left: 0 !important;
}

::v-deep .el-table .cell {
  text-align: center;
}

::v-deep .el-table td.el-table__cell div {
  font-size: 14px;
  font-weight: 700;
}

.peopleInfo {
  margin-bottom: 5px;
  box-shadow: 0 2px 10px #d1d1d1;
  border-radius: 0;
  background: #fff;
  padding: 10px 5px;
  display: flex;
  justify-content: space-between;

  .peopleDetail {
    display: flex;

    .peopleMsg {
      display: flex;
      align-items: center;
      margin-left: 50px;

      .dataInfo {
        width: 150px;
        height: 25px;
        background-color: #d2dae7;
        text-align: center;
        margin-left: 10px;
      }
    }
  }

  ul {
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: space-around;

    //   li {
    //     // width: 7.5%;
    //     display: flex;
    //     flex-direction: column;
    //     justify-content: center;
    //     padding: 0;
    //     margin: 0;
    //     list-style: none;
    //     position: relative;

    //     // ::after{
    //     //   content: '';
    //     //   position: absolute;
    //     //   right: 0;
    //     //   top: 10px;
    //     //   width: 1px;
    //     //   height: 26px;
    //     //   background-color: #d8e4fd;
    //     // }
    //     span{
    //       font-size: 16px;
    //       color: #d8e4fd;
    //       margin-bottom: 4px;
    //     }
    //     button:nth-of-type(1) {
    //       background: #66cbfe;
    //       width: 90px;
    //       word-spacing: 5px;
    //       font-size: 16px;
    //       padding: 7px 0;
    //     }

    //     button:nth-of-type(2) {
    //       background: #ff6157;
    //       margin-left: 18px;
    //       border-radius: 0.25rem;
    //       width: 90px;
    //       word-spacing: 5px;
    //       font-size: 16px;
    //       padding: 7px 0;
    //     }
    //   }

    .longString {
      width: 1px;
      height: 26px;
      background-color: #d8e4fd;
      margin-top: 5px;
    }

    .lastLi {
      ::after {
        content: "";
        position: absolute;
        right: 0;
        top: 10px;
        width: 0;
        height: 0;
        background-color: #9a9a9a;
      }
    }

    .lastLi {
      display: flex;
      flex-direction: inherit;
      align-items: center;
      width: 15%;
    }

    list-style: none;
  }
}

::v-deep .wd {
  width: 100px;
}

::v-deep .wdban {
  width: 60px;
}

.marginR {
  margin-right: 5px !important;
}

.noborderR {
  border-right: 0 !important;
}

.indicatorlstyle {
  width: 20.46%;
  border: 1px solid #9596a5;
  margin-top: 5px;
  margin-right: 5px;

  .indicatorrone {
    width: 100%;
    height: 218px;
  }
}

.w150 {
  width: 150px;
}

.eapStatus {
  background: #0d0;
  color: #fff;
}

.statuHead {
  display: flex;
  justify-content: space-between;

  .wrappstyle {
    display: flex;
    align-items: center;

    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;

      span {
        font-size: 12px;
        font-weight: 700;
      }

      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }

    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }

  .wholeline {
    width: 20px;
    height: 20px;
    margin-top: 5px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholeline1 {
    width: 20px;
    height: 20px;
    margin-top: 5px;
  }

  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
}

.el-descriptions-row {
  .btnGreen {
    background-color: #0d0 !important;
    border: none !important;
  }

  .btnRed {
    background-color: #ee1216 !important;
    border: none !important;
  }

  .btnYellow {
    background-color: #daea05 !important;
    border: none !important;
  }
}

::v-deep .el-descriptions--small.is-bordered .el-descriptions-item__cell {
  padding: 3px !important;
}

::v-deep .el-table__body-wrapper {
  height: 50px !important;
  overflow: hidden;
}
.lineScanImage {
  height: calc(100vh - 590px);
  text-align: center;
  img {
    height: 100%;
  }
}
</style>
