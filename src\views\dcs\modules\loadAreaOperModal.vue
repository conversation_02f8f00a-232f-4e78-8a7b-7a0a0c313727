<template>
    <el-dialog :title="title" :visible.sync="dialogVisible" :width="width" :before-close="handleClose">
        <el-descriptions class="margin-top"  :column="2" :size="size" border style="margin: 10px 0;" v-if="type ==='3'">
            <el-descriptions-item>
                <template slot="label">
                    任务号
                </template>
                {{ dataObj.rwh }}
            </el-descriptions-item>
            <el-descriptions-item>
                <template slot="label">
                    图纸名称
                </template>
                {{ dataObj.drawName }}
            </el-descriptions-item>
        </el-descriptions>
        <el-table border ref="table" v-loading="loading" :data="tableData" :row-key="row => row.id"
            :highlight-current-row="highlightCurrentRow" :height="height" v-if="type === '2'">
            <!-- 钢板型号 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="station_code"
              :label="$t('lang_pack.cuttingZone.SteelPlateModel')" />
              <!-- 入库任务号 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="stock_code"
              :label="$t('lang_pack.cuttingZone.WarehousingTaskNumber')" />
              <!-- 入库时间 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="stock_d_time"
              :label="$t('lang_pack.cuttingZone.WarehousingTime')" />
              <!-- 库位排序 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="stock_order"
              :label="$t('lang_pack.cuttingZone.LocationSorting')" />
              <!-- Z轴坐标 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="location_z"
              :label="$t('lang_pack.cuttingZone.ZAxisCoordinates')" />
              <!-- 是否锁定 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="lock_flag"
              :label="$t('lang_pack.cuttingZone.IsItLocked')" />
               <!-- 锁定任务号 (字段未确认)-->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="cost_time"
              :label="$t('lang_pack.cuttingZone.LockTaskNumber')" />
        </el-table>
        <el-table border ref="table" v-loading="loading" :data="taskDrawData" :row-key="row => row.id"
            :highlight-current-row="highlightCurrentRow" :height="height" v-if="type === '3'">
            <!-- 分拣工位 -->
            <el-table-column  :show-overflow-tooltip="true" align="center" prop="station_code"
              :label="$t('lang_pack.sortingArea.SortingStationNumber')" />
              <!-- 零件编号 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="part_code"
              :label="$t('lang_pack.sortingArea.PartNumber')" />
              <!-- 零件类型 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="stock_d_time"
              :label="$t('lang_pack.sortingArea.PartType')" />
              <!-- 零件长 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="m_length"
              :label="$t('lang_pack.sortingArea.PartLength')" />
              <!-- 零件宽 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="m_width"
              :label="$t('lang_pack.sortingArea.PartWidth')" />
              <!-- 零件厚 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="m_height"
              :label="$t('lang_pack.sortingArea.PartThickness')" />
               <!-- 零件重 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="m_weight"
              :label="$t('lang_pack.sortingArea.PartWeight')" />
              <!-- 计划分拣时间 -->
            <el-table-column :show-overflow-tooltip="true" align="center" prop="fj_start_time"
              :label="$t('lang_pack.sortingArea.PlannedSortingTime')" />
        </el-table>
    </el-dialog>
</template>
<script>
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
export default {
    name: 'loadAreaOperModal',
    data() {
        return {
            title: '',
            size:'small',
            dialogVisible: false,
            width: '60%',
            height: document.documentElement.clientHeight - 400,
            loading: false,
            highlightCurrentRow: false,
            tableData: [],
            dataObj: {},
            type:'',
            taskDrawData:[]
        }
    },
    props:{
      mo_id:{
        type:String,
      }
    },
    created(){
      this.getTaskDraw()
    },
    methods: {
        getTaskDraw(){
          crudLoadAreaOper.taskDraw({mo_id:this.mo_id}).then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if(defaultQuery.data.length > 0){
                        this.taskDrawData = defaultQuery.data
                    }
                }
            })
            .catch(() => {
              this.taskDrawData = []
                this.$message({
                    message: '型号查询异常',
                    type: 'error'
                })
              })
        },
        handleClose() {
            this.tableData = []
            this.dataObj = {}
            this.dialogVisible = false
            this.$emit('ok')
        }
    }
}
</script>