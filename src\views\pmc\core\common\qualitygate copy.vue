<template>
  <div class="workstationScreen">
    <el-card class="cardStyle cardheadbg">
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="24">
          <div class="headerStyle">
            <div class="headbg" :style="'background-image:url(' + headlbg + ');'" />
            <div class="logoStyle"><img src="@/assets/images/logw.png" alt=""></div>
            <div class="headTitle">
              <h2>{{ prod_line_des }}</h2>
            </div>
            <div class="headerL">
              <p class="pTime">{{ nowDateWeek }}</p>
              <p class="pTime">{{ nowDateTime }}</p>
            </div>
          </div>
        </el-col>

      </el-row>
    </el-card>
    <el-card class="cardStyle" shadow>
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="16">
          <el-card class="wraptop" shadow="never">
            <div class="carInfoQuestion">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-descriptions__title grade">今日 TOP</h4></div>
                <el-table
                  :data="todayTopData"
                  style="width: 100%"
                  height="600px"
                >
                  <el-table-column 
                    prop="id"
                    label="#"
                    width="40"
                  />
                  <el-table-column 
                    prop="carType"
                    label="车型"
                    width="140"
                  />
                  <el-table-column 
                    prop="description"
                    label="问题描述"
                  />
                  <el-table-column 
                    prop="grade"
                    label="等级"
                    width="60"
                  />
                  <el-table-column 
                    prop="frequency"
                    label="频次"
                    width="60"
                  />
                </el-table>
              </div>
            </div>
            <div class="carInfoQuestion">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-descriptions__title grade">本周 TOP</h4></div>
                <el-table
                  :data="thisWeekData"
                  style="width: 100%"
                  height="600px"
                >
                  <el-table-column 
                    prop="id"
                    label="#"
                    width="40"
                  />
                  <el-table-column 
                    prop="carType"
                    label="车型"
                    width="140"
                  />
                  <el-table-column 
                    prop="description"
                    label="问题描述"
                  />
                  <el-table-column 
                    prop="grade"
                    label="等级"
                    width="60"
                  />
                  <el-table-column 
                    prop="frequency"
                    label="频次"
                    width="60"
                  />
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <div class="carInfoQuestion fullwdith">
              <div class="titleTip"><h4 class="el-descriptions__title grade">TOP 对比</h4></div>
              <div id="topcomparison" style="width: auto;height: 280px" />
            </div>
          </el-card>
          <el-card shadow="never">
            <div class="carInfoQuestion fullwdith">
              <div class="titleTip"><h4 class="el-descriptions__title grade">当月指标 展示</h4></div>
              <div class="indicatorlstyle">
                <div id="indicatorl" class="indicatorlone" />
                <div id="indicatorr" class="indicatorrone" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
    <!-- <el-card class="cardStyle" shadow>
      <el-row :gutter="24" class="elRowStyle">
        <el-col :span="16">
          <el-card class="wraptop" shadow="never">
            <div class="carInfoQuestion">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-descriptions__title grade">本月 TOP</h4></div>
                <el-table
                  :data="thisMonthData"
                  style="width: 100%"
                >
                  <el-table-column 
                    prop="id"
                    label="#"
                    width="40"
                  />
                  <el-table-column 
                    prop="carType"
                    label="车型"
                    width="60"
                  />
                  <el-table-column 
                    prop="description"
                    label="问题描述"
                  />
                  <el-table-column 
                    prop="grade"
                    label="等级"
                    width="60"
                  />
                  <el-table-column 
                    prop="frequency"
                    label="频次"
                    width="60"
                  />
                </el-table>
              </div>
            </div>
            <div class="carInfoQuestion">
              <div class="carInfoText carInfomargin">
                <div class="titleTip"><h4 class="el-descriptions__title grade">上周TOP</h4></div>
                <el-table
                  :data="lastWeekData"
                  style="width: 100%"
                >
                  <el-table-column 
                    prop="id"
                    label="#"
                    width="40"
                  />
                  <el-table-column 
                    prop="carType"
                    label="车型"
                    width="60"
                  />
                  <el-table-column 
                    prop="description"
                    label="问题描述"
                  />
                  <el-table-column 
                    prop="grade"
                    label="等级"
                    width="60"
                  />
                  <el-table-column 
                    prop="frequency"
                    label="频次"
                    width="60"
                  />
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8" />
      </el-row>
    </el-card> -->
  </div>
</template>

<script>
import { getStationTime } from '@/api/pmc/sysTime'
import headlbg from '@/assets/images/headlbg.png'
export default {
  name: 'qualitygate',
  components: {

  },
  data() {
    return {
      nowDateTime: '', // 当前日期
      nowDateWeek: '', // 周末
      prod_line_des: '质量门',
      headlbg: headlbg,
      todayTopData: [{
        id: '1',
        carType: '牵引7',
        description: '放水阀>装配缺陷>装配不当',
        grade: 'C',
        frequency: '16'
      }, {
        id: '2',
        carType: '牵引7',
        description: '左前组合灯总成>左下>装配缺陷>松动',
        grade: 'C',
        frequency: '14'
      }, {
        id: '3',
        carType: '牵引7',
        description: '后围下部加强横梁左段>左上>连接缺陷>错边',
        grade: 'B',
        frequency: '11'
      }, {
        id: '4',
        carType: '牵引7',
        description: '柴油机总成>标准件缺陷>螺栓/螺母滑牙',
        grade: 'C',
        frequency: '10'
      }],
      thisWeekData: [{
        id: '1',
        carType: '牵引7',
        description: '侧标志灯总成>装配缺陷>卡扣脱落',
        grade: 'B',
        frequency: '35'
      }, {
        id: '2',
        carType: '牵引10',
        description: '前顶盖板>右下>外观缺陷>凹坑',
        grade: 'C',
        frequency: '33'
      }, {
        id: '3',
        carType: '牵引7',
        description: '右侧围外板焊接总成>右上>外观缺陷>划伤',
        grade: 'B',
        frequency: '32'
      }, {
        id: '4',
        carType: '牵引7',
        description: '左后动力悬置主动侧支架>装配缺陷>调整不当',
        grade: 'C',
        frequency: '30'
      }],
      thisMonthData: [{
        id: '1',
        carType: '牵引10',
        description: '左上工具箱盖板总成>外观缺陷>划伤',
        grade: 'B',
        frequency: '67'
      }, {
        id: '2',
        carType: '牵引7',
        description: '侧标志灯总成>装配缺陷>卡扣脱落',
        grade: 'B',
        frequency: '65'
      }, {
        id: '3',
        carType: '牵引7',
        description: '左下护板总成>外观缺陷>脏污',
        grade: 'B',
        frequency: '55'
      }, {
        id: '4',
        carType: '牵引10',
        description: '左后动力悬置主动侧支架>装配缺陷>调整不当',
        grade: 'C',
        frequency: '52'
      }, {
        id: '5',
        carType: '牵引7',
        description: '顶置文件柜总成>外观缺陷>脏污',
        grade: 'B',
        frequency: '48'
      }],
      lastWeekData: [{
        id: '1',
        carType: '牵引7',
        description: '左后动力悬置主动侧支架>装配缺陷>调整不当',
        grade: 'C',
        frequency: '27'
      }, {
        id: '2',
        carType: '牵引7',
        description: '前顶盖板>右下>外观缺陷>凹坑',
        grade: 'C',
        frequency: '24'
      }, {
        id: '3',
        carType: '牵引7',
        description: '左下护板总成>外观缺陷>脏污',
        grade: 'B',
        frequency: '20'
      }, {
        id: '4',
        carType: '牵引7',
        description: '顶置文件柜总成>外观缺陷>脏污',
        grade: 'C',
        frequency: '17'
      }, {
        id: '5',
        carType: '牵引7',
        description: '制动管束>装配缺陷>松动',
        grade: 'B',
        frequency: '15'
      }],
      orgOptions1: {},
      emergency: [],
      emergencyresult: [],
      flaw: [],
      flawresult: [],
      flawnumber: 4,
      emergencynumber: 4,
      flawindex: 0,
      emergencyindex: 0,
      flawtimer: null,
      emergencytimer: null,
      nowDate: '',
      station_code: '',
      sliderData: [
        {
          title: '计划数：',
          num: ''
        },
        {
          title: '完成数：',
          num: ''
        }
      ],
      carInfoResult: [],
      num: 0,
      topcomparisonOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '12%',
          top: '10%',
          right: '5%',
          bottom: '10%',
          // width: '88%',
          height: '80%'
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0]
        },
        yAxis: {
          type: 'category',
          data: ['问题1', '问题2', '问题3', '问题4', '问题5'],
          axisLabel: {
            textStyle: {
              fontSize: '14',
              fontWeight: '700'
            }
          }
        },
        series: [
          {
            itemStyle: {
              color: '#546fc6'
            },
            name: '本周',
            type: 'bar',
            data: [],
            barGap: '0%'
          },
          {
            itemStyle: {
              color: '#93cc74'
            },
            name: '上周',
            type: 'bar',
            data: [],
            barGap: '0%'
          }
        ]
      },
      indicatorlOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['DRL', 'FTQ']
        },
        grid: {
          left: '14%',
          top: '10%',
          right: '22%',
          bottom: '10%'
          // width: '76%',
          // height: '78%'
        },
        xAxis: [
          {
            type: 'category',
            data: ['车型1', '车型2', '车型3', '车型4', '车型5', '车型6'],
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            min: 0,
            max: 250,
            interval: 50,
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                fontSize: '14',
                fontWeight: '700'
              }
            }
          },
          {
            type: 'value',
            min: 0,
            max: 100,
            interval: 20,
            axisLabel: {
              formatter: '{value} %',
              textStyle: {
                fontSize: '14',
                fontWeight: '700'
              }
            }
          }
        ],
        series: [
          {
            itemStyle: {
              color: '#5471c9'
            },
            name: 'DRL',
            type: 'bar',
            tooltip: {
              valueFormatter: function(value) {
                return value
              }
            },
            data: []
          },
          {
            itemStyle: {
              color: '#ec6762'
            },
            name: 'FTQ',
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function(value) {
                return value + ' %'
              }
            },
            data: []
          }
        ]
      },
      indicatorrOption: {
        tooltip: {
          trigger: 'item'
        },
        legend: {
        },
        color: ['#546fc6', '#fac958', '#91cd76'],
        series: [
          {
            label: {
              show: true,
              position: 'inside',
              formatter(ar) { return ar.value + ' ' + ar.percent + '%' }
            },
            type: 'pie',
            radius: '65%',
            // 饼图位置参数
            center: ['50%', '55%'],
            data: [
              { value: '', name: '设计' },
              { value: '', name: '零部件' },
              { value: '', name: '制造' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },
      topcomparison: null,
      indicatorl: null,
      indicatorr: null,
      timettt: null
    }
  },
  created() {
    // 加载工位屏时间
    this.initstationTime()
    this.stationTimeTimer = setInterval(() => {
      this.initstationTime()
    }, 1000)
    this.initcheckstationdata()

    setInterval(() => {
      this.initcheckstationdata()
    }, 1000)
  },
  mounted() {
    // 在通过mounted调用即可
    this.echartsInit()
  },
  // 销毁定时器
  beforeDestroy() {
    if (this.formatDate) {
      clearInterval(this.formatDate) // 在Vue实例销毁前，清除时间定时器
    }
  },
  methods: {
    // 加载工位屏时间
    initstationTime() {
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          // console.log(res)
          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据异常')
            return
          }
          this.nowDateTime = res.data[0].sysdate
          this.nowDateWeek = res.data[0].sysweek
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    },
    // 初始化echarts
    echartsInit() {
      var that = this
      this.topcomparison = this.$echarts.init(document.getElementById('topcomparison'))
      this.indicatorl = this.$echarts.init(document.getElementById('indicatorl'))
      this.indicatorr = this.$echarts.init(document.getElementById('indicatorr'))
      window.addEventListener('resize', function() {
        that.topcomparison.resize()
        that.indicatorl.resize()
        that.indicatorr.resize()
      })
    },
    initcheckstationdata() {
      // this.echartsInit()
      var query = {
        station_code: this.$route.query.station_code
      }
      getStationTime(query)
        .then(res => {
          console.log(res)
          this.topcomparisonOption.series[0].data = [25, 23, 18, 17, 13]
          this.topcomparisonOption.series[1].data = [27, 24, 20, 17, 15]
          this.indicatorlOption.series[0].data = [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3]
          this.indicatorlOption.series[1].data = [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
          this.indicatorrOption.series[0].data[0].value = 1048
          this.indicatorrOption.series[0].data[1].value = 735
          this.indicatorrOption.series[0].data[2].value = 580
          this.topcomparison.setOption(this.topcomparisonOption)
          this.indicatorl.setOption(this.indicatorlOption)
          this.indicatorr.setOption(this.indicatorrOption)

          if (res.code !== 0 && res.count < 0) {
            console.log('请求数据为空')
            return
          }
        })
        .catch(() => {
          console.log('请求数据为空')
        })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .m-slide{
  background: none !important;
}
.wrapmarquee{
  width: 100%;
    flex: 1;
}
.wraptext{
  display: flex;
  align-items: center;
  p{
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  margin-right: 25px;
  display: flex;
  align-items: center;
  }
}
.wraptext p::before{
    content: '';
    width: 10px;
    height: 10px;
    background-color: #ff0000;
    display: block;
    border-radius: 50%;
    margin-right: 10px;
}
.cardStyle{
    margin: 0;
    padding: 0;
    border-radius: 0;
}
.cardheadbg{
  background-color: #031c45;
}
.headerStyle{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  .headbg{
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  .logoStyle{
    display: flex;
    align-items: center;
    width: 20%;
    z-index: 2;
    padding-left: 4%;
    img{
      width: 180px;
    }
    span{
      font-weight: 700;
      font-size: 24px;
    }
  }
  .headerL{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 15%;
    margin-left: 5%;
    p{
      margin: 5px 0;
    }
    .pImg{
    width: 60px;
    img{
          width: 100%;
        }
    }
    .pTime{
          font-weight: 700;
    color: #ffffff;
    font-size: 18px;
    }
  }
  .headTitle{
    h2{
    margin: 0;
    font-size: 55px;
    color: #fdfd21;
    text-shadow: 3px 3px 3px #000000;
    }
  }
}
.carInfo{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .carInfoText{
    flex: 1;
  }
  .carInfoImg{
    width: 120px;
    img{
      width: 100%;
    }
  }
}
::v-deep .carInfoTable{
  .el-table--small .el-table__cell {
    padding: 1px 0;
}
}
.carInfoVideo{
height: 264px;
    background-color: #333333;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
  img{
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.dialogVideo{
    position: fixed;
    z-index: 999;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
.carInfodialogVideo{
  // height: 265px;
  background-color: #333333;
      background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
  video{
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}
::v-deep .el-descriptions__title::before{
  content: '';
  width: 10px;
  height: 10px;
  background-color: #ffffff;
  display: inline-block;
  margin-right: 8px;
  border-radius: 25px;
      margin-left: 8px;
}
::v-deep .el-descriptions__title{
  display: flex;
    align-items: center;
    font-size: 20px;
    color: #ffffff;
}
::v-deep .el-table th {
    background-color: #0070c0 !important;
    color: #ffffff;
    font-weight: 700;
    font-size: 19px;
}
::v-deep .el-table__row td,::v-deep .el-table__row td{
      font-size: 12px;
    font-weight: 700;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background:#6b9ef9 !important;
  color: #ffffff !important;

}
::v-deep .el-descriptions-item__container .el-descriptions-item__label,::v-deep .el-descriptions-item__container .el-descriptions-item__content{
  font-size: 16px;
    color: #333333;
    font-weight: 700;
}
::v-deep .my-label,.labelNum{
  color: #ff0000 !important;
}
::v-deep .el-card__body {
    padding: 0px !important;
}
.carInfoQuestion{
  width: 50%;
}
.fullwdith{
  width: 100% !important;
}
img[src=""] {
    opacity: 0;
  }
.wraptop{
  ::v-deep .el-card__body{
    display: flex;
    justify-content: space-between;
  }
}
.titleTip{
  display: flex;
  justify-content: center;
  margin: 10px 0;
  .grade{
    margin: 0;
    padding: 10px;
    padding-left: 0;
    background-color: #0070c0;
    border-radius: 6px;
    width: 180px;
    display: flex;
    justify-content: center;
}
}

::v-deep .el-card{
  border: 0;
}
::v-deep .el-descriptions__header{
  background-color: #6b9ef9;
  padding: 10px 0;
  border-radius: 0 25px 25px 0 !important;
}
.indicatorlstyle{
  display: flex;
  align-items: center;
  .indicatorlone{
    width: 50%;
    height: 240px;
  }
  .indicatorrone{
    width: 50%;
    height: 240px;
  }
}
.carInfomargin{
  margin: 0 5px;
}
:deep(.el-table td.el-table__cell div){
  font-size: 28px;
  line-height: 34px;
}
</style>
