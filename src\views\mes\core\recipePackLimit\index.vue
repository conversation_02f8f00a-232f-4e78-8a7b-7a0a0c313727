<template>
  <!--电芯排废-->
  <el-card shadow="never">
    <div style="margin-bottom:10px;">
      <!--<el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>-->
      <!-- 取消 -->
      <el-button type="primary" size="small" icon="el-icon-check" :loading="btnLoading" @click="saveData">保存</el-button>
      <!-- 确认 -->
    </div>
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <!--<el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="678" highlight-current-row @selection-change="crud.selectionChangeHandler">-->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" size="small" :data="tableDataTable" style="width: 100%" :header-cell-style="{ background: '#eef1f6', color: '#545559' }" height="478" max-height="678" highlight-current-row @selection-change="handleSelectionChange">
          <el-table-column  type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column  v-if="1 == 0" width="10" prop="pack_limit_id" label="id" />
          <el-table-column  :show-overflow-tooltip="true" prop="pack_limit_code" label="范围值约束编码" />
          <el-table-column  :show-overflow-tooltip="true" prop="pack_limit_des" label="范围值约束描述" />
          <el-table-column  :show-overflow-tooltip="true" prop="down_limit" label="下限">
            <template slot-scope="scope">
              <el-input v-model="scope.row.down_limit" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="upper_limit" label="上限">
            <template slot-scope="scope">
              <el-input v-model="scope.row.upper_limit" />
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="pack_limit_index" label="序号">
            <template slot-scope="scope">
              <el-input v-model="scope.row.pack_limit_index" />
            </template>
          </el-table-column>
          <el-table-column  label="有效标识" align="center" prop="enable_flag">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.enable_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" @change="changeEnabled(scope.row, scope.row.enable_flag)" />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import { mesrecipePackLimitMustSel, mesrecipePackLimitMustUpd, mesrecipePackLimitMustEnableFlagUpd } from '@/api/mes/core/recipePackLimit'
import Cookies from 'js-cookie'
export default {
  name: 'recipePackLimit',
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'c_mes_fmod_recipe_pack_limit:add'],
        edit: ['admin', 'c_mes_fmod_recipe_pack_limit:edit'],
        del: ['admin', 'c_mes_fmod_recipe_pack_limit:del'],
        down: ['admin', 'c_mes_fmod_recipe_pack_limit:down']
      },
      rules: {},
      listLoadingTable: false,
      tableDataTable: [],
      multipleSelection: [],
      btnLoading: false
    }
  },
  watch: {
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.toQuery()
  },
  methods: {
    toQuery() {
      const query = {
        user_name: Cookies.get('userName')
      }
      mesrecipePackLimitMustSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          console.log('查询')
          console.log(defaultQuery)

          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            } else {
              this.tableDataTable = []
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('确定范围值约束编码【' + data.pack_limit_des + '】的有效标识修改为【' + (data.enable_flag === 'Y' ? '有效' : '无效') + '】吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const save = {
            user_name: Cookies.get('userName'),
            pack_limit_code: data.pack_limit_code,
            enable_flag: data.enable_flag
          }

          mesrecipePackLimitMustEnableFlagUpd(save)
            .then(res => {
              this.btnLoading = false
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: '操作成功！', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + defaultQuery.msg, type: 'error' })
                data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
              }
            })
            .catch((ex) => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
              data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
            })
        })
        .catch(() => {
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
    },
    // 修改下/上限、排废槽号
    saveData() {
      if (this.multipleSelection.length === 0) {
        this.$message({ message: '请选中数据操作！', type: 'info' })
        return
      }
      console.log('保存')
      console.log(this.multipleSelection)
      this.btnLoading = true

      const save = {
        user_name: Cookies.get('userName'),
        packLimitList: this.multipleSelection
      }

      mesrecipePackLimitMustUpd(save)
        .then(res => {
          this.btnLoading = false
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: '保存成功！', type: 'success' })
            this.toQuery()
          } else {
            this.$message({ message: '保存失败：' + defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.btnLoading = false
          this.$message({
            message: '修改异常',
            type: 'error'
          })
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
