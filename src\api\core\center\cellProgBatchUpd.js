import request from '@/utils/request'

// 查询Cell服务
export function querySysCoreCell(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysServerCellSel',
    method: 'post',
    data
  })
}

export function SysCoreCellUpdateProcSave(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreSysCellUpdateProcSaveBatch',
    method: 'post',
    data
  })
}
export function UpdateProcDSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreUpdateProcDSel',
    method: 'post',
    data
  })
}
export function ServerCellProcInfoSel(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreServerCellProcInfoSel',
    method: 'post',
    data
  })
}
// 根据Server安装
export function ServerUpdate(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}
// 根据Cell安装
export function CellUpdate(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}
export function ClearProcAndProcD(data) {
  return request({
    url: 'aisEsbWeb/core/center/CoreClearProcAndProcD',
    method: 'post',
    data
  })
}

export default { querySysCoreCell, SysCoreCellUpdateProcSave, UpdateProcDSel, ServerCellProcInfoSel, ServerUpdate, CellUpdate, ClearProcAndProcD }

