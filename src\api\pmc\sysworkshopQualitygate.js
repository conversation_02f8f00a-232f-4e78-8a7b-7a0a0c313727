import request from '@/utils/request'

// 查询工位屏时间
export function getStationTime(data) {
    return request({
        url: 'aisEsbApi/pmc/project/bq/PmcBqSysDataSel',
        method: 'post',
        data
    })
}
// 获取本周 今日 本月
export function getTopMonth(data) {
    return request({
        url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getTop?type=${data.type}&station=${data.station}`,
        method: 'get',
    })
}
// 获取上周质量数据
export function getLastWeek() {
    return request({
        url: 'http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getLastWeek',
        method: 'get',
    })
}
// 获取DPU质量问题
export function getDpuProData(data) {
    return request({
        url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getThisMontBarChart?station=${data.station}`,
        method: 'get',
    })
}
//获取总装下线质量
export function getfinAssProData(data) {
    return request({
        url: `http://10.140.4.11:9999/baic-mom-qm-defect/api/v1/bigScreen/getThisMonthPieChart?station=${data.station}`,
        method: 'get',
    })
}

//获取标题
export function getStationDes(data) {
    return request({
        url: `http://10.140.6.64:8500/api/v1/station?queryMode=list&stationCode=${data.station}`,
        method: 'get',
    })
}