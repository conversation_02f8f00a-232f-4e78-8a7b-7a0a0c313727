// cover some element-ui styles
:root {
  --el-button--color: #79a0f1;
  --el-color--primary: #409eff;
  --el-menuBg--color: #79a0f1;
  --el-header--color: #fff;
  --el-headerTitle-color: #fff;
  // 新hmi的公用
  --el-font-family:'NSimSun';
  // --el-container-bgColor:'';
  --el-container-bgColor:#D3DBE8;
  // --el-card__body-bgColor:'';
  --el-card__body-bgColor:#FFFFFD;
  // --el-header--bgcolor: #003561;
  --el-header--bgcolor: #3D4FB5;
  --el-header--bgTextColor: #ffffff;
  // --el-table--bgcolor: #12C1A2;
  --el-table--bgcolor: #3D75E2;
  --el-table--bgTextColor: #F2F2F2;
  --el-table--textColor: #474948;
  --el-table2--bgcolor: #D8DDE3;
  --el-table2--bottombgcolor: '';
  --el-table2--bgTextSingleColor:'';
  --el-table2--bgTextColor:'';
  --el-table2--bgTextDoubleColor:#F2F2F2;
  --el-table2--textColor:#4C4C4B;
  --el-dialog-bgColor:#D7DEE8;
  --el-dialog-textColor:#012A56;
  --el-suspend-bgColor:#3D75E2;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

.el-drawer__body {
  overflow: hidden !important;

  .scrollFormWarp {
    max-height: calc(100vh - 200px);
    overflow: auto;
  }

  .scrollFormWarp::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
  }

  .scrollFormWarp::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f6f9ff;
    cursor: pointer !important;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-table th {
  font-size: 12px;
  font-weight: bold;
  background-color: #e8efff !important;
}

.el-table tr:nth-child(even) {
  background-color: #f6f9ff;
}

.el-table--enable-row-transition .el-table__body td {
  border: 0 !important;
}

.el-table th.is-leaf,
.el-table td {
  border-bottom: 0 !important;
}

.el-button--text {
  color: var(--el-button--color);
}

.el-pager li.active,
.el-pager li:hover {
  color: var(--el-button--color);
}

.el-select-dropdown__item.selected {
  color: var(--el-button--color);
  font-weight: bold;
}

.el-select-dropdown__item {
  font-size: 12px;
}

.el-button--primary {
  background-color: var(--el-button--color);
  border-color: var(--el-button--color);
  border-radius: 0.25rem;
}

.el-button--default {
  border-radius: 0.25rem;
}

.el-table__body tr.current-row>td {
  background-color: var(--el-button--color) !important;
  color: #ffffff;
}

.el-table__body tr.current-row .el-button--text span {
  color: #ffffff !important;
}

.el-table__body tr.hover-row>td {
  background-color: var(--el-button--color) !important;
  color: #ffffff;
}

.el-table__body tr.hover-row .el-button--text span {
  color: #ffffff !important;
}

.el-table__body tr.current-row .linkItem,
.el-table__body tr.hover-row .linkItem {
  color: #ffffff !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  // background-color: #79a0f1;
  // border-color: #79a0f1;
  background-color: var(--el-button--color);
  border-color: var(--el-button--color);
}

.el-checkbox__inner:hover {
  border-color: #79a0f1;
}

.el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #79a0f1;
}

.el-button--warning {
  background-color: #ff9f43;
  border: 1px solid #ff9f43;
  color: #fff;
  border-radius: 0.25rem;
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  // background-color: #79a0f1;
  // border-color: #79a0f1;
  background-color: var(--el-button--color);
  border-color: var(--el-button--color);
}

.el-form-item--small .el-form-item__label {
  font-size: 12px;
  color: #5f5f5f;
}

.el-input__inner:focus {
  border-color: #79a0f1;
}

.el-input__inner {
  border: 1px solid #d9d9d9;
  color: #5f5f5f;
  font-size: 12px;
}

.el-select .el-input .el-select__caret.is-reverse {
  color: #79a0f1;
  font-size: 12px;
}

.el-card__body {
  padding: 15px !important;
}

.crud-opts-right .el-button {
  border: 0 !important;
}

.el-pagination span:not([class*='suffix']),
.el-pagination__sizes .el-input .el-input__inner {
  font-size: 12px;
}

.el-icon-search,
.el-icon-refresh,
.el-icon-download {
  font-weight: bold;
  font-size: 12px;
}

.crud-opts-right .el-button {
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 20px;
}

.crud-opts-right .el-button:hover,
.crud-opts-right .el-button:focus {
  background-color: #e8efff;
  color: #578eff;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 20px;
  border-radius: 40px !important;
}

// .el-form-item__label {
//   padding-right: 0 !important;
// }
.el-table::before {
  height: 0;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
  color: #79a0f1;
  font-size: 12px;
}

.el-checkbox__label {
  font-size: 12px;
}

.el-form-wrap {
  display: flex;
  flex-wrap: wrap;
  padding: 0;

  .el-form-item--small .el-form-item__content {
    width: 100%;
  }

  .el-form-item {
    width: 50%;
    padding-right: 0 !important;
    margin-right: 0 !important;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .el-select {
    width: 100% !important;
  }

  .el-form-item--small .el-form-item__label {
    margin-right: 10px;
  }
}

.el-form-column {
  flex-direction: column;

  .el-form-item {
    width: 100%;
  }
}

.el-radio__label {
  font-size: 12px;
}

.el-radio__input.is-checked+.el-radio__label {
  color: var(--el-button--color);
  font-size: 12px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: var(--el-button--color);
  background: var(--el-button--color);
}

.el-scrollbar__view {
  .tags-view-item {
    font-size: 12px !important;
    background-color: #f6f9ff !important;
    border-color: #79a0f1 !important;

    .el-icon-close:before {
      vertical-align: -2px;
    }
  }

  .tags-view-item::before {
    background: #79a0f1 !important;
  }
}

.el-drawer__header {
  padding: 0 !important;
}

.el-drawer__header span::before {
  content: '';
  background-color: #79a0f1;
  padding-right: 6px;
  margin-right: 5px;
  border-radius: 10px;
}

.el-drawer__open .el-drawer.rtl {
  padding: 15px 15px 0 15px;
}

.el-drawer__close-btn {
  padding: 0;
}

.el-icon-close:before {
  font-weight: bold;
  font-size: 14px;
}

.el-drawer__close-btn {
  color: #606266;
}

.el-dialog__header div::before {
  content: '';
  background-color: #79a0f1;
  padding-right: 6px;
  margin-right: 5px;
  border-radius: 10px;
}

// .el-button.is-plain:hover, .el-button.is-plain:focus {
//   border-color: #79a0f1;
//   color: #79a0f1;
// }
#icon_scrollbar .el-avatar {
  background: #79a0f1 !important;
}

.el-submenu .el-menu-item {
  display: flex;
  align-items: center;
}

#app .sidebar-container .svg-icon {
  margin-right: 6px !important;
}

.el-link--primary {
  color: var(--el-button--color) !important;
}

#app .sidebar-container .el-menu-item.is-active,
#app .sidebar-container .el-submenu .el-menu-item:hover {
  padding-left: 50px !important;
  -webkit-transition: padding 0.35s ease 0s !important;
  -o-transition: padding 0.35s ease 0s !important;
  -moz-transition: padding 0.35s ease 0s !important;
  transition: padding 0.35s ease 0s !important;
  background: -webkit-linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  background: -moz-linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  background: -o-linear-gradient(118deg, var(--el-button--color)1, #bfd4ff);
  background: linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  -webkit-box-shadow: 0 0 10px 1px rgb(115 103 240 / 70%);
  box-shadow: 0 0 10px 1px rgb(121 160 241);
  background-color: var(--el-button--color) !important;
}

#app .sidebar-container .el-menu-item.is-active {
  background: -webkit-linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  background: -moz-linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  background: -o-linear-gradient(118deg, var(--el-button--color)1, #bfd4ff);
  background: linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  -webkit-box-shadow: 0 0 10px 1px rgb(115 103 240 / 70%);
  box-shadow: 0 0 10px 1px rgb(121 160 241);
  background-color: var(--el-button--color) !important;
}

.el-menu-item:hover {
  padding-left: 30px !important;
  -webkit-transition: padding 0.35s ease 0s !important;
  -o-transition: padding 0.35s ease 0s !important;
  -moz-transition: padding 0.35s ease 0s !important;
  transition: padding 0.35s ease 0s !important;
  background: -webkit-linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  background: -moz-linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  background: -o-linear-gradient(118deg, var(--el-button--color)1, #bfd4ff);
  background: linear-gradient(118deg, var(--el-button--color), #bfd4ff);
  -webkit-box-shadow: 0 0 10px 1px rgb(115 103 240 / 70%);
  box-shadow: 0 0 10px 1px rgb(121 160 241);
  background-color: var(--el-button--color) !important;
}

.wrapCard {
  .el-card__body {
    padding: 15px 15px 5px 15px !important;
  }
}

.el-switch.is-checked .el-switch__core {
  border-color: var(--el-button--color) !important;
  background-color: var(--el-button--color) !important;
}

.el-pagination {
  margin: 15px 0 0 0 !important;
  padding: 0;
}

.wrapElForm {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .formChild {
    padding: 0;
  }
}

.wrapElFormFirst {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 0 !important;

  .el-form-item {
    display: flex;
    align-items: center;
    margin: 0;
  }

  .el-form-item__label {
    white-space: nowrap;
    margin: 0;
  }

  .el-form-item--small .el-form-item__content {
    width: 100%;
  }

  .el-select {
    width: 100% !important;
  }

  .formChild {
    padding-right: 10px;
  }

  .formChild:first-child {
    padding-left: 0;
  }
}

.wrapElFormSecond {
  display: flex;
  justify-content: flex-end;

  .el-form-item {
    margin: 0;
  }
}

.col-12 {
  margin-bottom: 10px;
}

.el-button--text span {
  border: 0;
}

.sysSearch .el-input--small .el-input__inner {
  border-radius: 15px;
  background-color: var(--el-menuBg--color);
  border: 1px solid #cfdeff;
  font-size: 12px;
  color: #ffffff;
}

.el-textarea__inner,
.el-input__inner {
  $placeholder: #cccccc;

  &::placeholder {
    color: $placeholder;
  }

  &::-webkit-input-placeholder {
    /* WebKit browsers 适配谷歌 */
    color: $placeholder;
  }

  &:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 适配火狐 */
    color: $placeholder;
  }

  &::-moz-placeholder {
    /* Mozilla Firefox 19+ 适配火狐 */
    color: $placeholder;
  }

  &:-ms-input-placeholder {
    /* Internet Explorer 10+  适配ie*/
    color: $placeholder;
  }
}

.el-menu--vertical>.el-menu .svg-icon {
  margin-right: 8px !important;
}

.el-dropdown-menu--medium .el-dropdown-menu__item {
  font-size: 12px;
}

.sidebar-logo-container {
  margin-bottom: 0 !important;
  height: 70px !important;
}

#app .sidebar-container a {
  display: flex !important;
  overflow: hidden !important;
  justify-content: center !important;
  align-items: center;

  h1 {
    width: 100px;
    padding: 0;
    height: 40px;
    font-size: 28px;
    line-height: 32px;
    text-align: center;
    border: 3px solid #ffffff;
    margin: 0;
  }
}

.crud-opts {
  padding: 0 !important;
  margin-bottom: 15px !important;
}

.tags-view-container {
  border-bottom: 0 !important;
}

// .el-table__row {
//   -webkit-transition: all 0.25s ease;
//   -o-transition: all 0.25s ease;
//   -moz-transition: all 0.25s ease;
//   transition: all 0.25s ease;
// }
// .el-table__row:hover {
//   -webkit-transform: translateY(-4px);
//   -moz-transform: translateY(-4px);
//   -ms-transform: translateY(-4px);
//   -o-transform: translateY(-4px);
//   transform: translateY(-4px);
// }
.el-scrollbar__bar.is-vertical {
  width: 0 !important;
}

.tableFirst {
  .el-card__body {
    padding: 0 !important;
  }
}

.wrapRowItem,
.elTableItem {
  .el-table {
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
}

.scrollbar-wrapper::-webkit-scrollbar {
  width: 1;
}

.scrollbar-wrapper::-webkit-scrollbar {
  width: 0;
}

.el-submenu__icon-arrow {
  right: 32px;
}

// 细化滚动条
.el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #ebeef5;
  cursor: pointer !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f6f9ff;
  cursor: pointer !important;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
  cursor: pointer !important;
}

.el-table__fixed-right-patch {
  width: 0 !important;
}

.el-table--fluid-height .el-table__fixed-right {
  right: 0 !important;
  bottom: 0 !important;
}

#app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 180px) !important;
}

#app .sidebar-container.sidebar-logo .el-scrollbar {
  height: calc(100% - 115px) !important;
}

.el-table .sort-caret {
  border: solid 4px transparent;
}

.el-table .sort-caret.ascending {
  top: 6px;
}

.el-table .sort-caret.descending {
  bottom: 9px;
}

.el-table .descending .sort-caret.descending {
  border-top-color: #79a0f1;
}

.el-table .ascending .sort-caret.ascending {
  border-bottom-color: #79a0f1;
}

.linkItem {
  color: #0072d6;
  font-size: 12px;
}

.el-link.is-underline:hover:after {
  border-bottom: 0;
  color: #46a6ff;
  font-weight: normal;
}

.el-tree__empty-text {
  font-size: 12px;
}

.el-autocomplete {
  width: 100%;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: var(--el-button--color);
}

.iconItem {
  span:first-child {
    width: 100%;
  }
}

:deep(.el-notification.right) {
  display: none !important;
}

.fromP {
  .el-form-item__label {
    width: 130px !important
  }
}
.workOrder{
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
  }

  &::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f6f9ff;
    cursor: pointer !important;
  }
}