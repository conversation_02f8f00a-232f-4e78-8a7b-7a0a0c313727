<template>
  <div id="loadMonitor" />
</template>
<script>
import Cookies from 'js-cookie'
import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { selCellIP } from '@/api/core/center/cell'
export default {
  name: 'MQTT_PAGE',
  props: {
    // 注意：格式为
    // monitorData :{
    // key值为 tag_code，client_code为实例监控的实力编码，group_code为实例监控的标签组，tag_code为实例监控的OnOffLine
    //     OnOffLine: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'OnOffLine', tag_des: '[PlcConfig]在线/离线模式', value: '' },
    // }
    monitorData: {
      type: Object,
      default: () => ({})
    },
    // 这个是监听页面点位的心跳和状态的
    controlStatus: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      }
    }
  },
  mounted() {
    this.getStationData()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        cell_id: this.$route.query.cell_id
      }
      this.getCellIp()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        // this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        // 注册监控CCD照片
        // this.topicSubscribe('SCADA_CHANGE/Ccd/CcdStatus/PanelPic')
        // 其他标准点位监控
        Object.keys(this.monitorData).forEach(key => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
        })
        Object.keys(this.controlStatus).forEach(key => {
          var client_code = this.controlStatus[key].client_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          this.topicSubscribe('SCADA_STATUS/' + client_code)
          this.topicSubscribe('SCADA_BEAT/' + client_code)
        })
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })
      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
      })
      this.clientMqtt.on('close', () => {
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('SCADA_BEAT/') >= 0) { // 心跳
            var heartBeatValue = jsonData.Beat
            Object.keys(this.controlStatus).forEach(item => {
              var client_code = this.controlStatus[item].client_code
              if (topic.indexOf('SCADA_BEAT/' + client_code) >= 0) {
                if (this.controlStatus[client_code].value !== '2') {
                  this.controlStatus[client_code].value = heartBeatValue
                }
              }
            })
          } else if (topic.indexOf('SCADA_STATUS/') >= 0) { // 通讯结果状态
            var statusValue = jsonData.Status
            var OkTime = jsonData.OkTime
            Object.keys(this.controlStatus).forEach(item => {
              var client_code = this.controlStatus[item].client_code
              if (topic.indexOf('SCADA_STATUS/' + client_code) >= 0) {
                if (statusValue === '0') {
                  this.controlStatus[client_code].value = '2'
                } else {
                  if (this.controlStatus[client_code].value === '2') {
                    this.controlStatus.eap_status = '1'
                  }
                }
              }
            })
            Object.keys(this.monitorData).forEach(item => {
              var client_code = this.monitorData[item].client_code
              if (topic.indexOf('SCADA_STATUS/' + client_code) >= 0) {
                this.monitorData[item].OkTime = OkTime
              }
            })
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              if (this.aisMonitorMode === 'AIS-SERVER') {
                client_code = client_code + '_' + this.currentStation.station_code
              }
              var tag_key = client_code + '/' + group_code + '/' + tag_code
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue
              }
            })
          }
        } catch (e) { console.log(e) }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 写入值
    handleWrite(data) {
      // 判断是不是数组
      if (!Array.isArray(data)) {
        this.$message({
          type: 'warning',
          message: '请输入数组格式'
        })
        return
      }
      data.forEach(e => {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: e.key,
          TagValue: e.val
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/' + e.key.split('/')[0]
        this.sendMessage(topic, sendStr)
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
        } else {
          this.$message({ message: this.$t('lang_pack.commonPage.operationfailure'), type: 'error' })
        }
      })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      if (Object.keys(this.monitorData).length === 0) {
        this.$message({
          type: 'error',
          message: '请设置tag_key值'
        })
        return
      }
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code = client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter(item => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    }
  }
}
</script>
