<template>
  <div class="app-container">
    <el-card
      v-if="crud.props.searchToggle"
      ref="queryCard"
      shadow="never"
      class="wrapCard headerCard"
    >
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方描述：">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="版本号：">
                <el-input
                  v-model="query.recipe_version"
                  clearable
                  size="small"
                />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="配方类型：">
                <el-select v-model="query.recipe_type" clearable>
                  <el-option
                    v-for="item in dict.EAP_RECIPE_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item label="有效标识：">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option
                    v-for="item in dict.ENABLE_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 5px">
      <!-- 扫码栏 -->
      <div class="recipeInfo">
        <div>
          <span>扫码下载配方：</span>
          <el-input
            ref="recipeInfoScan"
            v-model="recipeInfoScan"
            style="width: 500px"
            type="text"
            @keyup.native="handleKeyup"
            @keydown.native="handleKeydown"
          />
          <el-button
            slot="reference"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="recipeInfoScan === ''"
            @click="batchDownload()"
          >
            {{ $t("lang_pack.commonPage.download") }}
          </el-button>
        </div>
        <div>
          <el-button
            :class="monitorData.OnOffLine.value === '1' ? 'btnone' : 'btnone0'"
            type="success"
            @click="handleOnOffLineSwitch"
          >{{
            monitorData.OnOffLine.value === "1" ? "在线模式" : "离线模式"
          }}</el-button>
          <el-popover
            placement="bottom"
            title="设备自检"
            trigger="hover"
            @show="handleMouseEnter"
          >
            <div v-html="popoverContent" />
            <el-button
              slot="reference"
              @click.prevent="handleDevice"
            >设备自检</el-button>
          </el-popover>
        </div>
      </div>

      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            slot="reference"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="batchDelete()"
          >
            {{ $t("lang_pack.commonPage.remove") }}
          </el-button>

          <!-- <el-button
              slot="reference"
              class="filter-item"
              type="primary"
              icon="el-icon-upload"
              plain
              round
              size="small"
              :loading="crud.delAllLoading"
              :disabled="crud.selections.length === 0"
              @click="batchUplaod()"
            >
              {{ $t('lang_pack.commonPage.upload') }}
            </el-button> -->
        </template>
      </crudOperation>
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="650px"
      >
        <el-form
          ref="form"
          class="el-form-wrap"
          :model="form"
          :rules="rules"
          size="small"
          label-width="100px"
          :inline="true"
        >
          <el-form-item label="配方类型" prop="recipe_type">
            <el-select
              v-model="form.recipe_type"
              clearable
              @change="chooseRecipeType"
            >
              <el-option
                v-for="item in dict.EAP_RECIPE_TYPE"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="配方描述" prop="recipe_name">
            <el-input v-model="form.recipe_name" />
          </el-form-item>
          <el-form-item label="批次号" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item label="版本号" prop="recipe_version">
            <el-input v-model="form.recipe_version" />
          </el-form-item>
          <!-- <el-form-item label="设备编码" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item label="设备描述" prop="device_des">
            <el-input v-model="form.device_des" />
          </el-form-item> -->
          <!-- <el-form-item label="物料编码" prop="material_code">
            <el-input v-model="form.material_code" />
          </el-form-item>
          <el-form-item label="物料描述" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item> -->
          <el-form-item label="有效标识" prop="enable_flag">
            <el-select v-model="form.enable_flag" clearable>
              <el-option
                v-for="item in dict.ENABLE_FLAG"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button
            size="small"
            icon="el-icon-close"
            plain
            @click="crud.cancelCU"
          >取消</el-button>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >确认</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column
                v-if="1 == 0"
                width="10"
                prop="recipe_id"
                label="id"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="recipe_type"
                label="配方类型"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="recipe_name"
                label="配方描述"
                width="210"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="device_code"
                label="批次号"
                width="170"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="recipe_version"
                label="版本号"
                width="120"
              />
              <!-- <el-table-column
                :show-overflow-tooltip="true"
                prop="device_code"
                label="设备编码"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="device_des"
                label="设备描述"
              /> -->
              <!-- <el-table-column
                :show-overflow-tooltip="true"
                prop="material_code"
                label="物料编码"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="material_des"
                label="物料描述"
              /> -->
              <el-table-column
                label="是否有效"
                align="center"
                prop="enable_flag"
              >
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                width="200"
                align="center"
                fixed="right"
              >
                <template slot-scope="scope">
                  <!-- <udOperation :data="scope.row" :permission="permission" :disabled-dle="false">
                      <template slot="right">

                      </template>
                    </udOperation> -->
                  <operation
                    :data="scope.row"
                    :permission="permission"
                    :disabled-dle="false"
                    @ok="doDelete"
                  >
                    <template slot="right">
                      <!-- <el-button
                        slot="reference"
                        type="text"
                        size="small"
                        @click="$refs.detail && $refs.detail.crud.toAdd()"
                        >新增参数</el-button
                      > -->
                      <el-button
                        slot="reference"
                        type="text"
                        size="small"
                        style="margin-left: 0px"
                        @click="upload(scope.row)"
                      >上传</el-button>
                      <el-button
                        slot="reference"
                        type="text"
                        size="small"
                        style="margin-left: 0px"
                        @click="downLoad(scope.row)"
                      >下载</el-button>
                      <el-button
                        slot="reference"
                        type="text"
                        size="small"
                        style="margin-left: 0px"
                        @click="down(scope.row)"
                      >下发</el-button>
                    </template>
                  </operation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <detail
              ref="detail"
              class="tableFirst"
              :recipe_detail_id="currentPackId"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 5px">
      <span>设备自检信息：{{ popoverContent }}</span>
    </el-card>
  </div>
</template>

<script>
import eapRecipe from '@/api/wx/recipe/eapRecipe'
import {
  selLoginInfo,
  userLogin,
  userLogout
} from '@/api/eap/eapMeStationUser'
import detail from './detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import operation from '@/views/core/system/errorMsg/operation.vue'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
  recipe_id: '',
  recipe_name: '',
  recipe_version: '',
  device_code: '',
  device_des: '',
  material_code: '',
  material_des: '',
  recipe_type: '',
  enable_flag: 'Y',
  recipeFileNames: ''
}
export default {
  name: 'EAP_RECIPE',
  components: {
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    detail,
    operation
  },
  props: {},
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id asc'],
      // CRUD Method
      crudMethod: { ...eapRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EAP_RECIPE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      recipeInfoScan: '',
      permission: {
        add: ['admin', 'c_eap_fmod_recipe_maintain:add'],
        edit: ['admin', 'c_eap_fmod_recipe_maintain:edit'],
        del: ['admin', 'c_eap_fmod_recipe_maintain:del']
      },
      rules: {
        recipe_type: [
          { required: true, message: '请选择配方类型', trigger: 'blur' }
        ],
        recipe_version: [
          { required: true, message: '请填写版本号', trigger: 'blur' }
        ],
        recipe_name: [
          { required: true, message: '请填写配方描述', trigger: 'blur' }
        ],
        material_code: [
          { required: false, message: '请填写物料编码', trigger: 'blur' }
        ],
        material_des: [
          { required: false, message: '请填写物料描述', trigger: 'blur' }
        ],
        device_code: [
          { required: false, message: '请填写批次号', trigger: 'blur' }
        ],
        device_des: [
          { required: false, message: '请填写设备描述', trigger: 'blur' }
        ],
        enable_flag: [
          { required: true, message: '请选择有效标识', trigger: 'blur' }
        ]
      },
      currentPackId: 0,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userId') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 监听数据
      monitorData: {
        ApplyUploadRecipe: {
          client_code: 'DesAis',
          group_code: 'AisStatus',
          tag_code: 'ApplyUploadRecipe',
          tag_des: '[AisStatus]申请上传配方',
          value: ''
        },
        ApplyDownLoadRecipe: {
          client_code: 'DesAis',
          group_code: 'AisStatus',
          tag_code: 'ApplyDownLoadRecipe',
          tag_des: '[AisStatus]申请下载配方',
          value: ''
        },
        ApplyDistributeRecipe: {
          client_code: 'DesAis',
          group_code: 'AisStatus',
          tag_code: 'ApplyDistributeRecipe',
          tag_des: '[AisStatus]申请下发配方',
          value: ''
        },
        OnOffLine: {
          client_code: 'DesAis',
          group_code: 'AisStatus',
          tag_code: 'OnOffLine',
          tag_des: '[AisStatus]在线/离线模式',
          value: ''
        }
      },
      lockTime: 10,

      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      tagKeyList: [
        'DesAis/AisStatus/ApplyUploadRecipe',
        'DesAis/AisStatus/ApplyDownLoadRecipe',
        'DesAis/AisStatus/ApplyDistributeRecipe'
      ],
      applyObj: {
        uploadValue: '',
        downloadValue: '',
        downValue: ''
      },
      timearr: [0, 0],
      popoverContent: '',
      onOffLine: 0,
      deviceStatus: -1,
      recipeTimer: null,
      tempTime: 0
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
    this.$refs.recipeInfoScan.focus()
  },
  created: function() {
    this.getStationData()
    this.handleMouseEnter()
    this.recipeTimer = setInterval(() => {
      this.crud.toQuery()
    }, 5000)
  },
  methods: {
    handleKeydown() {
      this.timearr[0] = new Date().getTime()
    },
    handleKeyup() {
      this.timearr[1] = new Date().getTime()
      if ((this.timearr[1] - this.timearr[0]) > 80) {
        this.recipeInfoScan = ''
      } else if (this.tempTime !== 0 && (this.timearr[1] - this.tempTime) > 80) {
        this.recipeInfoScan = ''
        this.tempTime = this.timearr[0]
      }
    },
    // 多个删除
    batchDelete() {
      this.$confirm(
        `确认删除选中的${this.crud.selections.length}条数据?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          const ids = this.crud.selections
            .map((item) => item[this.crud.idField])
            .join(',')
          const recipeFileNames = this.crud.selections
            .map((item) => item.recipe_name + '-' + item.recipe_version)
            .join(',')
          const query = {
            ids,
            recipeFileNames,
            user_name: Cookies.get('userName')
          }
          this.delete(query)
        })
        .catch(() => {})
    },
    // 单个删除
    doDelete(data) {
      const query = {
        ids: data.recipe_id,
        recipeFileNames: data.recipe_name + '-' + data.recipe_version,
        user_name: Cookies.get('userName')
      }
      this.delete(query)
    },
    delete(data) {
      eapRecipe
        .del(data)
        .then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.crud.toQuery()
          } else {
            this.$message({
              message: res.msg || '删除失败',
              type: 'error'
            })
          }
        })
        .catch((e) => {
          this.$message({
            message: '删除失败',
            type: 'error'
          })
        })
    },
    chooseRecipeType(val) {
      console.log(val)
      if (val === 'MATERIAL') {
        this.rules.material_code[0].required = true
        this.rules.material_des[0].required = true
        this.rules.device_code[0].required = false
        this.rules.device_des[0].required = false
      } else {
        this.rules.material_code[0].required = false
        this.rules.material_des[0].required = false
        this.rules.device_code[0].required = true
        this.rules.device_des[0].required = true
      }
    },
    handleRowClick(row, column, event) {
      this.currentPackId = row.recipe_id
      console.log(row.recipe_id)
    },
    async handleMouseEnter() {
      await this.fetchContent()
    },
    handleMouseLeave() {},
    async fetchContent() {
      try {
        const res = await eapRecipe.selectEquipStatusInfo({})
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.result !== '') {
            this.popoverContent = defaultQuery.result.replace(
              /\\r\\n/g,
              '<br>'
            )
          }
        }
      } catch (error) {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      }
      // eapRecipe.selectEquipStatusInfo({}).then(res => {
      //   const defaultQuery = JSON.parse(JSON.stringify(res))
      //   if (defaultQuery.code === 0) {
      //     if (defaultQuery.result !== '') {
      //       this.popoverContent = defaultQuery.result.replace(/\\r\\n/g, '<br>')
      //     }
      //   }
      // }).catch(() => {
      //   this.$message({
      //     message: '查询异常',
      //     type: 'error'
      //   })
      // })
    },
    handleDevice() {
      this.onOffLine = this.monitorData.OnOffLine.value
      eapRecipe
        .equipStatusReport({ station_code: this.$route.query.station_code })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          this.deviceStatus = defaultQuery.code
          this.handleStopDeviceSwitch()
          if (defaultQuery.code === 0) {
            this.$message({
              type: 'success',
              message: '上传mes成功'
            })
          }
        })
        .catch(() => {
          this.$message({
            message: '操作异常',
            type: 'error'
          })
        })
    },
    // 批量上传
    batchUplaod() {
      this.$confirm(
        `确认上传选中的${this.crud.selections.length}条数据?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          if (this.monitorData.ApplyUploadRecipe.value === '1') {
            this.$message({
              type: 'warning',
              message: '已存在上传任务，请等待上传完成'
            })
            return
          }
          var sendJson = {}
          var rowJson = []
          var newRow1 = {
            TagKey: 'DesAis/AisStatus/ApplyUploadRecipe',
            TagValue: '1'
          }
          rowJson.push(newRow1)
          var newRow2 = {
            TagKey: 'DesAis/AisStatus/UploadRecipeName',
            TagValue: this.crud.selections
              .map((item) => item.recipe_name)
              .join(',')
          }
          rowJson.push(newRow2)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/DesAis'
          this.sendMessage(topic, sendStr)
          this.$message({
            type: 'success',
            message: '触发上传配方流程'
          })
        })
        .catch(() => {})
    },
    // 单个上传
    upload(row) {
      this.$confirm(`确认上传选中的配方?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          debugger
          if (this.monitorData.ApplyUploadRecipe.value === '1') {
            this.$message({
              type: 'warning',
              message: '已存在上传任务，请等待上传完成'
            })
            return
          }
          var sendJson = {}
          var rowJson = []
          var newRow1 = {
            TagKey: 'DesAis/AisStatus/ApplyUploadRecipe',
            TagValue: '1'
          }
          rowJson.push(newRow1)
          var newRow2 = {
            TagKey: 'DesAis/AisStatus/UploadRecipeName',
            TagValue: row.recipe_name
          }
          rowJson.push(newRow2)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/DesAis'
          this.sendMessage(topic, sendStr)
          this.$message({
            type: 'success',
            message: '触发上传配方流程'
          })
        })
        .catch(() => {})
    },
    // 批量下载
    batchDownload() {
      this.$confirm(`确认从上层系统下载配方?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.monitorData.ApplyDownLoadRecipe.value === '1') {
            this.$message({
              type: 'warning',
              message: '已存在下载任务，请等下载完成'
            })
            return
          }
          var sendJson = {}
          var rowJson = []
          var newRow1 = {
            TagKey: 'DesAis/AisStatus/ApplyDownLoadRecipe',
            TagValue: '1'
          }
          rowJson.push(newRow1)
          var newRow2 = {
            TagKey: 'DesAis/AisStatus/DownLoadRecipeName',
            TagValue: this.recipeInfoScan
          }
          rowJson.push(newRow2)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/DesAis'
          this.sendMessage(topic, sendStr)
          this.$message({
            type: 'success',
            message: '触发下载配方流程'
          })
        })
        .catch(() => {})
    },
    // 单个下载
    downLoad(row) {
      this.$confirm(`确认从上层系统下载该配方?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.monitorData.ApplyDownLoadRecipe.value === '1') {
            this.$message({
              type: 'warning',
              message: '已存在下载任务，请等下载完成'
            })
            return
          }
          var sendJson = {}
          var rowJson = []
          var newRow1 = {
            TagKey: 'DesAis/AisStatus/ApplyDownLoadRecipe',
            TagValue: '1'
          }
          rowJson.push(newRow1)
          var newRow2 = {
            TagKey: 'DesAis/AisStatus/DownLoadRecipeName',
            TagValue: row.recipe_name
          }
          rowJson.push(newRow2)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/DesAis'
          this.sendMessage(topic, sendStr)
          this.$message({
            type: 'success',
            message: '触发下载配方流程'
          })
        })
        .catch(() => {})
    },
    // 单个下发
    down(row) {
      this.$confirm(`确认下发配方到设备?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.monitorData.ApplyDistributeRecipe.value === '1') {
            this.$message({
              type: 'warning',
              message: '已存在下发任务，请等下发完成'
            })
            return
          }
          var sendJson = {}
          var rowJson = []
          var newRow1 = {
            TagKey: 'DesAis/AisStatus/ApplyDistributeRecipe',
            TagValue: '1'
          }
          rowJson.push(newRow1)
          var newRow2 = {
            TagKey: 'DesAis/AisStatus/DistributeRecipeName',
            TagValue: row.recipe_name
          }
          rowJson.push(newRow2)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/DesAis'
          this.sendMessage(topic, sendStr)

          this.$message({
            type: 'success',
            message: '下发配方到设备成功'
          })
        })
        .catch(() => {})
    },
    scadaPoint(dataKey) {
      for (let i = 0; i < dataKey.length; i++) {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: dataKey[i].TagKey,
          TagValue: dataKey[i].TagValue
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/DesAis'
        this.sendMessage(topic, sendStr)
      }
    },
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        cell_id: this.$route.query.cell_id
      }
      this.getCellIp()
      this.getLoginInfo()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    // 获取当前登录信息
    getLoginInfo() {
      this.loginInfo.user_name = '---'
      this.loginInfo.nick_name = '---'
      this.loginInfo.dept_id = '---'
      this.loginInfo.shift_id = '---'
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '获取当前登录信息异常', type: 'error' })
        })
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code =
                    client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter((item) => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },

    // 处理在线离线模式切换
    handleOnOffLineSwitch() {
      var tag_value = this.monitorData.OnOffLine.value === '1' ? '0' : '1'
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'DesAis/AisStatus/OnOffLine',
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/DesAis'
      this.sendMessage(topic, sendStr)
      this.onOffLine = tag_value
      this.handleStopDeviceSwitch()
    },
    // 给plc设备停止状态，(0为离线 1在线并认证状态成功 2在线且认证失败)
    handleStopDeviceSwitch() {
      var stopflag = '0'
      if (this.onOffLine === '0') {
        stopflag = '0'
      } else if (this.onOffLine === '1') {
        if (this.deviceStatus === 0) {
          stopflag = '1'
        } else {
          stopflag = '2'
        }
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'DesPlc/PlcStatus/StopDeviceTag',
        TagValue: stopflag
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/DesPlc'
      this.sendMessage(topic, sendStr)
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // var connectUrl = 'ws://*************:8083'  + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        // 注册监控CCD照片
        this.topicSubscribe('SCADA_CHANGE/Ccd/CcdStatus/PanelPic')
        // 监控通讯状态
        var eapClientCode = 'LoadEap'
        var plcClientCode = 'LoadPlc'
        var panelCcdClientCode = 'LoadPanelCcd'
        var panelCcdClientCode2 = 'LoadPanelCcd2'
        var palletCcd1ClientCode = 'LoadPalletCcd1'
        var palletCcd2ClientCode = 'LoadPalletCcd2'
        if (this.aisMonitorMode === 'AIS-SERVER') {
          eapClientCode =
            eapClientCode + '_' + this.currentStation.station_code
          plcClientCode =
            plcClientCode + '_' + this.currentStation.station_code
          panelCcdClientCode =
            panelCcdClientCode + '_' + this.currentStation.station_code
          panelCcdClientCode2 =
            panelCcdClientCode2 + '_' + this.currentStation.station_code
          palletCcd1ClientCode =
            palletCcd1ClientCode + '_' + this.currentStation.station_code
          palletCcd2ClientCode =
            palletCcd2ClientCode + '_' + this.currentStation.station_code
        }
        this.topicSubscribe('SCADA_STATUS/' + eapClientCode)
        this.topicSubscribe('SCADA_BEAT/' + eapClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        this.topicSubscribe('SCADA_STATUS/' + panelCcdClientCode)
        this.topicSubscribe('SCADA_BEAT/' + panelCcdClientCode)
        this.topicSubscribe('SCADA_STATUS/' + panelCcdClientCode2)
        this.topicSubscribe('SCADA_BEAT/' + panelCcdClientCode2)
        this.topicSubscribe('SCADA_STATUS/' + palletCcd1ClientCode)
        this.topicSubscribe('SCADA_BEAT/' + palletCcd1ClientCode)
        this.topicSubscribe('SCADA_STATUS/' + palletCcd2ClientCode)
        this.topicSubscribe('SCADA_BEAT/' + palletCcd2ClientCode)
        // 其他标准点位监控
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          this.topicSubscribe(
            'SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code
          )
        })
        this.$message({
          message: '连接成功',
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return

          if (
            topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0
          ) {
            this.handleMessage(jsonData)
          } else if (topic.indexOf('SCADA_BEAT/') >= 0) {
            // 心跳
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf('SCADA_BEAT/LoadEap') >= 0) {
              if (this.controlStatus.eap_status !== '2') {
                this.controlStatus.eap_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPlc') >= 0) {
              if (this.controlStatus.plc_status !== '2') {
                this.controlStatus.plc_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPanelCcd') >= 0) {
              if (this.controlStatus.panel_status !== '2') {
                this.controlStatus.panel_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPalletCcd1') >= 0) {
              if (this.controlStatus.pallet1_status !== '2') {
                this.controlStatus.pallet1_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPalletCcd2') >= 0) {
              if (this.controlStatus.pallet2_status !== '2') {
                this.controlStatus.pallet2_status = heartBeatValue
              }
            }
          } else if (topic.indexOf('SCADA_STATUS/') >= 0) {
            // 通讯结果状态
            var statusValue = jsonData.Status
            if (topic.indexOf('SCADA_STATUS/LoadEap') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.eap_status = '2'
                this.$message({ message: 'EAP通讯中断', type: 'error' })
              } else {
                if (this.controlStatus.eap_status === '2') {
                  this.controlStatus.eap_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPlc') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.plc_status = '2'
                this.$message({ message: 'PLC通讯中断', type: 'error' })
              } else {
                if (this.controlStatus.plc_status === '2') {
                  this.controlStatus.plc_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPanelCcd') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.panel_status = '2'
                this.$message({
                  message: '板件读码CCD通讯中断',
                  type: 'error'
                })
              } else {
                if (this.controlStatus.panel_status === '2') {
                  this.controlStatus.panel_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPalletCcd1') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.pallet1_status = '2'
                this.$message({
                  message: '载具1读码CCD通讯中断',
                  type: 'error'
                })
              } else {
                if (this.controlStatus.pallet1_status === '2') {
                  this.controlStatus.pallet1_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPalletCcd2') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.pallet2_status = '2'
                this.$message({
                  message: '载具2读码CCD通讯中断',
                  type: 'error'
                })
              } else {
                if (this.controlStatus.pallet2_status === '2') {
                  this.controlStatus.pallet2_status = '1'
                }
              }
            }
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            if (topic === 'SCADA_CHANGE/Ccd/CcdStatus/PanelPic') {
              this.ccdImage = 'data:image/png;base64,' + jsonData.TagNewValue
            } else {
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code =
                    client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
            }
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 订阅主题函数
    topicSubscribeChange(topic) {
      if (!this.mqttChangeStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientChangeMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttChangeStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          if (this.showMsg) {
            this.$message({ message: '操作成功！', type: 'success' })
          }
          // 执行完成后都复位为true
          this.showMsg = true
        } else {
          // 执行完成后都复位为true
          this.showMsg = true
          this.$message({ message: '操作失败！', type: 'error' })
        }
      })
    },

    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.old_recipe_name = crud.form.recipe_name
      crud.form.old_recipe_version = crud.form.recipe_version
      return true
    }
  }
}
</script>
  <style lang="less" scoped>
  .app-container{
    padding: 10px;
  }
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.recipeInfo {
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btnone {
  background: #50d475;
  border-color: #50d475;
}

.btnone0 {
  background: #959595;
  border-color: #e8efff;
  color: #ffffff;
}
.headerCard{
  ::v-deep .el-card__body{
    padding: 10px 15px  5px !important;
  }
}
</style>
