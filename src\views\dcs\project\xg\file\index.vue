<template>
  <div class="app-container">
    <!--文件上传-->
    <el-drawer append-to-body :wrapper-closable="false" :title="uploadDrawerTitle" :visible.sync="uploadDrawerVisbleSync" size="550px" @closed="handleUploadDrawerClose">
      <el-form ref="formUpload" class="wrapUpload" :inline="true" size="small" style="margin: 0; padding: 0" label-width="80px">
        <el-form-item label=" ">
          <el-upload
            ref="upload"
            :multiple="false"
            class="upload-demo"
            action=""
            drag=""
            :limit="uploadLimit"
            :accept="uploadAccept"
            :on-change="handleUploadOnChange"
            :auto-upload="false"
            :http-request="handleUploadHttpRequest"
            :on-progress="handleUploadProgress"
            :file-list="fileList"
            name="file"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="handleUploadDrawerCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" @click="handleUploadFile">{{ $t('lang_pack.commonPage.upload') }}</el-button>
        <!-- 上传 -->
      </div>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect wrapFile">
            <span>{{ $t('lang_pack.file.autoDesk') }}</span>
            <!-- 文件夹目录 -->
          </div>

          <el-tree :data="treeData" :props="defaultProps" :highlight-current="true" @node-click="handleNodeClick" />
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect">
            <span>{{ rightHeaderTitle }}</span>
            <div class="wrapSearch">
              <div class="inputSearch">
                <el-input v-model="queryHeader.content" :placeholder="contentPlaceholder" class="filter-item inputItem" size="small" />
                <el-button class="filter-item buttonItem" size="small" type="primary" @click="handleQuery">查询</el-button>
              </div>
              <el-button v-if="true" class="filter-item" size="small" type="primary" icon="el-icon-upload2" plain @click="handleAddTag">上传</el-button>
            </div>
          </div>

          <fileItem ref="fileItem" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'
import fileItem from '@/views/dcs/project/xg/file/fileItem'
import { dirFileTree } from '@/api/dcs/project/xg/file'

export default {
  name: 'FILE_MANAGE',
  components: { fileItem },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      contentPlaceholder: '文件列表',
      // 查询条件
      queryHeader: {
        content: ''
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentNode: [],

      currentFileName: '',
      currentFilePath: '',
      rightHeaderTitle: '文件列表',
      clientModelData: [],

      // 文件上传
      uploadDrawerTitle: '文件上传',
      uploadDrawerVisbleSync: false,
      uploadLimit: 1,
      uploadAccept: '*.*',
      fileList: []
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
  },

  created: function() {
    const query = {
      type: 'directory',
      file_name: '',
      file_path: ''
    }
    dirFileTree(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.length > 0) {
          this.treeData = []
          var a = {}
          a.label = '根目录'
          a.path = ''
          a.children = defaultQuery.data
          this.treeData.push(a)
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    // 树点击事件
    handleNodeClick(data) {
      this.currentNode = data
      this.queryHeader.content = ''

      this.currentFileName = data.label
      this.currentFilePath = data.path
      this.$refs['fileItem'].toButQuery('', this.currentFilePath)
    },

    // 查询按钮
    handleQuery() {
      this.$refs['fileItem'].toButQuery(this.queryHeader.content, this.currentFilePath)
    },

    // 上传按钮
    handleAddTag() {
      this.uploadDrawerVisbleSync = true
    },
    handleUploadDrawerClose() {
      this.fileList = []
    },
    // 导入文件时将文件存入数组中
    handleUploadOnChange(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    handleUploadHttpRequest(file) {
      this.fileData.append('file', file.file)
    },
    // 文件正在上传时的钩子
    handleUploadProgress(event, file) {},
    // 关闭上传文件抽屉
    handleUploadDrawerCancel() {
      this.uploadDrawerVisbleSync = false
    },
    // 处理上传文件
    handleUploadFile() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }
      this.fileData = new FormData()
      this.fileData.append('file_path', this.currentFilePath)
      this.$refs.upload.submit()

      // 配置路径
      var method = 'dcs/project/xg/file/CoreFileUpload'
      // var path = "http://localhost:8089/" + method;
      var path = (process.env.NODE_ENV === 'development' ? process.env.VUE_APP_BASE_API + '/' : '/aisEsbWeb/') + method

      console.log('path: ' + path)

      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))
          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })

            this.uploadDrawerVisbleSync = false
            this.$refs['fileItem'].toButQuery(this.queryHeader.content, this.currentFilePath)
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
    }
  }
}
</script>

<style scoped lang="less">
::v-deep .el-card__header {
  padding: 8px;
}
.box-card1 {
  min-height: calc(100vh - 60px);

  .el-card__body {
    padding: 5px;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    font-weight: bold;
  }
  .el-dialog__body {
    padding: 0px;
  }
}
.wrapTextSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    white-space: nowrap;
    font-size: 14px;
    color: rgb(117, 117, 117);
    font-weight: bold;
    margin-right: 10px;
  }
}
.wrapSearch {
  display: flex;
  align-items: center;
  .inputSearch {
    display: flex;
    align-items: center;
    margin-right: 15px;
    .inputItem {
      .el-input__inner {
        width: 150px;
        border-radius: 4px 0 0 4px !important;
      }
    }
    .buttonItem {
      border-radius: 0 0.25rem 0.25rem 0;
      margin-left: -5px;
      z-index: 9;
    }
  }
}
.wrapFile {
  height: 33px;
  line-height: 33px;
}
.wrapUpload {
  text-align: center;
  .el-form-item {
    margin-right: 0;
  }
}
</style>
