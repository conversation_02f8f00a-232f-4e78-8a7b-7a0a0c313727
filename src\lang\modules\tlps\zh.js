// TLPS项目的中文翻译

export default {
  // 这里可以添加TLPS项目特定的翻译
  tlps: {
    // 配方管理
    recipe: {
      // 页面标题和通用文本
      maintenance: '配方维护',
      detail: '子配方维护详情',
      recipeDetail: '配方详情',
      noRecipeSelected: '未选择配方或配方详情加载中...',

      // 表单字段
      name: '配方描述',
      version: '版本号',
      type: '配方类型',
      deviceCode: '设备编码',
      deviceDes: '设备描述',
      materialCode: '物料编码',
      materialDes: '物料描述',
      parameterCode: '参数编码',
      parameterDes: '参数描述',
      parameterVal: '参数值',
      enableFlag: '有效标识',
      valid: '有效',
      invalid: '无效',

      // 操作相关
      scanDownload: '扫码下载配方',
      modifyParameter: '是否修改参数',
      addParameter: '新增参数',
      confirmDelete: '确认删除选中的{0}条数据?',
      confirmDistribute: '确认下发该配方吗?',
      confirmChangeEnableFlag: '确定要将有效标识修改为【{value}】吗？',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败',
      modifySuccess: '修改成功',
      modifyFailed: '修改失败',
      distributeSuccess: '下发成功',

      // 按钮和交互元素
      triggerPoint: '触发点位',
      selectTriggerPoint: '选择',
      cancel: '取消',
      confirm: '确认',
      download: '下载',
      upload: '上传',
      distribute: '下发',
      delete: '删除',
      downloadSuccess: '配方下载中',
      downloadFailed: '配方下载出现异常',

      // 提示信息
      startMonitoring: '请启动监控',
      connectionFailed: '连接失败',
      reconnecting: '连接断开，正在重连...',
      connectionDisconnected: '服务连接断开',
      connectionClosed: '服务连接断开',
      subscribeSuccess: 'MQTT订阅成功: {0}',
      subscribeFailed: 'MQTT订阅失败: {0}',
      unsubscribeSuccess: 'MQTT取消订阅成功: {0}',
      unsubscribeFailed: 'MQTT取消订阅失败: {0}',
      operationException: '操作异常',
      operationFailed: '操作失败',
      queryException: '查询异常',
      uploadMesSuccess: '上传MES成功',
      uploadSuccess: '上传成功',
      uploadTaskExists: '已存在上传任务，请等待上传完成',
      recipeDownloading: '发起Scada请求配方,配方下载中',
      recipeDownloadException: '发起Scada请求配方,配方下载出现异常',

      // 验证规则提示
      selectRecipeType: '请选择配方类型',
      fillVersion: '请填写版本号',
      fillRecipeName: '请填写配方描述',
      fillMaterialCode: '请填写物料编码',
      fillMaterialDes: '请填写物料描述',
      fillDeviceCode: '请填写设备编码',
      fillDeviceDes: '请填写设备描述',
      selectEnableFlag: '请选择有效标识',
      selectTriggerTag: '请选择采集项目标签'
    },

    // 主页面
    index: {
      title: 'TLPS系统',
      recipeManagement: '配方管理',
      deviceStatus: '设备状态',
      alarmInfo: '报警信息',

      // 操作模式
      offlineMode: '离线模式',
      onlineLocal: '在线/本地',
      onlineRemote: '在线/远程',

      // 设备状态
      deviceStatusLabel: '设备状态',
      deviceStatus0: '未知',
      deviceStatus1: '初始化',
      deviceStatus2: '待机',
      deviceStatus3: '报警',
      deviceStatus4: '停止',
      deviceStatus5: '运行',
      deviceStatus6: 'PM',

      // 状态指示
      alarmLight: '告警灯',
      plcHeartbeat: 'PLC心跳',

      // 表单字段
      batchNumber: '批次号',
      materialCode: '料号',
      recipe: '配方',
      selectRecipe: '请选择配方',
      processingQuantity: '加工数量',
      distributePreview: '下发预览',

      // 生产信息
      productionInfo: '生产信息',

      // 报警信息
      alarmInfo: '报警信息',
      station: '工位',
      instanceCode: '实例编号',
      instanceDescription: '实例描述',
      alarmCode: '报警代码',
      alarmLevel: '报警级别',
      alarmDescription: '报警描述',
      alarmTime: '报警时间',
      resetTime: '复位时间',
      isReset: '是否复位',
      resetYes: '已复位',
      resetNo: '待复位',
      isSimulated: '是否模拟',
      yes: '是',
      no: '否',

      // 图表
      capacity: '产能',
      oee: 'OEE',
      readRate: '读码率',

      // 表格列
      projectName: '项目名称',
      currentValue: '当前值',
      unit: '单位',
      upperLimit: '上限',
      lowerLimit: '下限',
      status: '状态',

      // 参数字段
      parameterCode: '参数编码',
      parameterDes: '参数描述',
      parameterVal: '参数值',
      enableFlag: '有效标识',
      valid: '有效',
      invalid: '无效',

      // 配方对话框
      modifyRecipe: '修改配方',
      modifyParameter: '是否修改参数',
      cancel: '取消',
      confirmDistribute: '确定下发',

      // CIM消息对话框
      cimMessage: 'CIM消息',
      code: '代码',
      message: '消息',
      confirm: '确认',

      // 配方确认对话框
      recipeConfirmTitle: '下发配方确认',
      recipeInfo: '配方信息',
      recipeName: '配方名称',
      stationCode: '工站编码',
      batchNumber: '批次号',
      materialCode: '料号',
      quantity: '数量',
      employeeInfo: '员工信息',
      recipeDetails: '配方详情',

      // 验证消息
      pleaseSelectRecipe: '请先选择配方',
      pleaseEnterBatchNumber: '请输入批次号',
      pleaseEnterMaterialCode: '请输入料号',
      deviceMustBeStandby: '设备必须处于待机状态才能下发配方',
      deviceRunning: '设备运行中，不允许下发配方',
      quantityMustBePositive: '数量必须为大于等于0的整数',
      recipeConfigIncomplete: '配方参数配置不完整，无法下发配方，请联系管理员补全scada_tag表的block_addr配置',
      getRecipeDetailFailed: '获取配方详情失败，无法下发配方',
      getRecipeDetailFailedWithMsg: '获取配方详情失败: {msg}',
      recipeBaseInfoNotFound: '未找到选中的配方基本信息',
      missingBlockAddrConfig: '以下参数缺少正确的block_addr配置，无法下发配方，请联系工程师补全：',
      noMaterialFound: '当前未获取到物料，请先去维护物料',
      queryFailed: '查询失败',
      queryException: '查询异常',
      queryExceptionWithMsg: '查询异常：{msg}',
      duplicateRecipeCode: '发现重复的配方编码: {codes}，请联系管理员修改配方配置',
      duplicateRecipeCodeMark: '重复配方编码!',
      recipeListRefreshSuccess: '配方列表刷新成功，共{count}条数据',
      noRecipeData: '未找到配方数据',
      refreshRecipeListFailed: '刷新配方列表失败: {msg}',
      refreshingRecipeList: '正在刷新配方列表...',
      pleaseStartMonitoring: '请启动监控',
      operationFailed: '操作失败！',

      // 成功消息
      distributeSuccess: '下发配方成功',
      recipeDistributeSuccess: '下发配方成功',
      connectionSuccess: '连接成功',
      connectionFailed: '连接失败',
      connectionDisconnected: '连接断开，正在重连...',

      // 配方详情对话框
      recipeInfo: '配方信息',
      recipeDetails: '配方详情',
      recipeName: '配方名称',
      stationCode: '工站编码',
      batchNumber: '批次号',
      materialCode: '料号',
      quantity: '数量',
      employeeInfo: '员工信息',
      parameterCode: '参数编码',
      parameterDescription: '参数描述',
      parameterValue: '参数值',
      enableFlag: '有效标识',
      valid: '有效',
      invalid: '无效',
      noRecipeSelectedOrLoading: '未选择配方或配方详情加载中...',
      loadingRecipeDetails: '正在加载配方详情...',
      cancel: '取消',
      confirmDistribute: '确认下发',

      // 报警信息
      alarmCode: '报警代码',
      alarmLevel: '报警级别',
      alarmDescription: '报警描述',
      alarmTime: '报警时间',
      resetTime: '复位时间',
      isReset: '是否复位',
      resetted: '已复位',
      waitingReset: '待复位',
      isSimulated: '是否模拟',
      yes: '是',
      no: '否',

      // 生产信息
      itemName: '项目名称',
      currentValue: '当前值',
      unit: '单位',
      upperLimit: '上限',
      lowerLimit: '下限',
      status: '状态',

      // CIM消息
      cimMessage: 'CIM消息',
      code: '代码',
      message: '消息',
      confirm: '确认',

      // 配方下发确认
      recipeDistributeConfirm: '下发配方确认',

      // 设备状态
      deviceStatusLabel: '设备状态',
      deviceStatus0: '未知',
      deviceStatus1: '初始化',
      deviceStatus2: '待机',
      deviceStatus3: '报警',
      deviceStatus4: '停止',
      deviceStatus5: '运行',
      deviceStatus6: 'PM',

      // 其他UI元素
      alarmLight: '三色灯',
      plcHeartbeat: 'PLC心跳',
      recipe: '配方',
      selectRecipe: '请选择配方',
      refreshRecipeList: '刷新配方列表',
      processingQuantity: '加工数量',
      distributePreview: '下发预览',
      productionInfo: '生产信息',
      alarmInfo: '报警信息',
      station: '工站',
      instanceCode: '实例编码',
      instanceDescription: '实例描述',
      modifyRecipe: '修改配方',
      modifyParameter: '修改参数',
      parameterDes: '参数描述',
      parameterVal: '参数值',

      // 图表标题
      productionCapacity: '产能',
      readRate: '读码率'
    },

    // 确认对话框
    confirm: {
      title: '提示',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }
  }
}
