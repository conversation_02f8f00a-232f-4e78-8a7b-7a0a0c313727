{"name": "aisweb", "version": "2.0.0", "description": "智能下料车间生产管理系统", "author": "Hong Jing", "license": "Apache-2.0", "scripts": {"tauri": "tauri", "dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --progress=false", "build:prod": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build", "build:local": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode local", "build:stage": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "svgo": "svgo -f src/assets/icons/svg --config=src/assets/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["git add"]}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "@tauri-apps/api": "^1.5.2", "@tauri-apps/cli": "^1.5.8", "ace-builds": "^1.4.12", "aisweb": "file:", "animate.css": "^3.7.2", "autofit.js": "^3.1.0", "axios": "^0.19.0", "clipboard": "^2.0.4", "codemirror": "^5.49.2", "connect": "3.6.6", "core-js": "^2.6.12", "d3": "^5.15.0", "dayjs": "^1.11.5", "echarts": "^4.9.0", "echarts-gl": "^1.1.1", "echarts-liquidfill": "^2.0.5", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.15.8", "exceljs": "^4.4.0", "file-saver": "^1.3.8", "fuse.js": "3.4.4", "html2canvas": "^1.4.1", "iview": "^3.5.4", "jquery": "^3.6.0", "js-beautify": "^1.10.2", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "jsencrypt": "^3.0.0-rc.1", "jsoneditor": "^6.3.0", "jszip": "3.1.5", "lodash": "^4.17.15", "mavon-editor": "^2.9.0", "mqtt": "^4.2.6", "mui-player": "^1.7.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "paho-mqtt": "^1.1.0", "path-to-regexp": "2.4.0", "qs": "^6.9.1", "roc-tooltip-show": "^0.1.1", "screenfull": "4.2.0", "simple-keyboard": "^3.4.145", "sortablejs": "1.8.4", "spark-md5": "^3.0.1", "swiper": "^8.4.3", "v-contextmenu": "^2.8.1", "v-playback": "^1.0.2", "vue": "^2.6.10", "vue-async-computed": "^3.9.0", "vue-awesome-swiper": "^3.1.3", "vue-codemirror-lite": "^1.0.4", "vue-count-to": "^1.0.13", "vue-cropper": "0.4.9", "vue-draggable-resizable": "^2.0.0-rc2", "vue-echarts": "^5.0.0-beta.0", "vue-grid-layout": "^2.3.6", "vue-highlightjs": "^1.3.3", "vue-i18n": "^8.27.2", "vue-image-crop-upload": "^2.5.0", "vue-json-editor": "^1.3.2", "vue-json-excel": "^0.3.0", "vue-pdf": "^4.3.0", "vue-radial-progress": "^0.2.10", "vue-router": "3.0.2", "vue-runtime-helpers": "^1.1.2", "vue-seamless-scroll": "^1.1.17", "vue-simple-uploader": "^0.7.4", "vue-splitpane": "1.0.4", "vue-ueditor-wrap": "^2.4.4", "vue-video-player": "^5.0.2", "vue2-ace-editor": "^0.0.13", "vuedraggable": "^2.20.0", "vuex": "^3.0.1", "vuex-map-fields": "^1.3.4", "vuex-persistedstate": "^2.5.4", "wangeditor": "^3.1.1", "webpack-jquery-ui": "^2.0.1", "xlsx": "^0.14.1"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/parser": "^7.7.4", "@babel/register": "7.0.0", "@vue/babel-plugin-transform-vue-jsx": "^1.2.1", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.0", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "3.2.0", "http-proxy-middleware": "^0.19.1", "husky": "1.3.1", "iview-loader": "^1.2.2", "less": "^3.0.4", "less-loader": "^4.1.0", "lint-staged": "8.1.5", "plop": "2.3.0", "runjs": "^4.3.2", "sass": "^1.32.13", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "uglifyjs-webpack-plugin": "^2.1.3", "vue-template-compiler": "2.6.10", "webpack-cli": "^4.10.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}