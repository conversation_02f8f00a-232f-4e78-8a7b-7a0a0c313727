<template>
  <div class="box-card">
    <el-card shadow="always" class="wrapCard">
      <el-form ref="formServer" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.cellprogupd.serve')">
                <!-- 服务： -->
                <el-select v-model="formServer.server_id" clearable size="small" @change="serverChange">
                  <el-option v-for="item in serverData" :key="item.server_id" :label="item.server_des" :value="item.server_id" />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>

    <!--弹框：文件上传-->
    <el-drawer append-to-body title="文件上传" :visible.sync="dialogVisbleSyncFrom" size="40%" @closed="drawerClose">
      <el-form ref="formUpload" :inline="true" size="small" style="margin: 0; padding: 0" label-width="80px">
        <el-form-item :label="$t('lang_pack.cellprogupd.updateMode')">
          <!-- 更新方式 -->
          <el-select v-model="formUpload.proc_update_way" @change="procUpdateWayChange">
            <el-option v-for="item in updateWayData" :key="item.fastcode_code" :label="item.fastcode_des" :value="item.fastcode_code" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.cellprogupd.file')">
          <!-- 文件 -->
          <el-upload ref="upload" :multiple="true" class="upload-demo" action="" drag="" :limit="uploadLimit" :accept="uploadAccept" :on-change="handleImport" :auto-upload="false" :http-request="uploadFile" :on-progress="progressA" :file-list="fileList" name="file">
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__text">只能上传{{ uploadAccept }}文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="toButDrawerCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
        <!-- 取消 -->
        <el-button type="primary" size="small" icon="el-icon-check" @click="toButDrawerUpload">{{ $t('lang_pack.commonPage.upload') }}</el-button>
        <!-- 上传 -->
      </div>
    </el-drawer>

    <!--弹框：文件列表-->
    <el-drawer append-to-body title="文件列表" :visible.sync="dialogVisbleSyncFileList" size="40%" @closed="drawerFileListClose">
      <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" :data="procDDate" style="width: 100%" :cell-style="getCellStyle" max-height="350px">
        <el-table-column  :label="$t('lang_pack.cellprogupd.serialNumber')" type="index" width="50" />
        <!-- 序号 -->
        <el-table-column  :show-overflow-tooltip="true" prop="cell_container_name" :label="$t('lang_pack.cellprogupd.cell')" />
        <!-- 单元 -->
        <el-table-column  :show-overflow-tooltip="true" prop="proc_code" :label="$t('lang_pack.cellprogupd.procedure')" />
        <!-- 程序 -->
        <el-table-column  :show-overflow-tooltip="true" prop="file_name" :label="$t('lang_pack.cellprogupd.fileName')" />
        <!-- 文件名称 -->
        <el-table-column  :show-overflow-tooltip="true" prop="file_type" :label="$t('lang_pack.cellprogupd.fileType')" />
        <!-- 文件类型 -->
        <el-table-column  :show-overflow-tooltip="true" prop="file_size" :label="$t('lang_pack.cellprogupd.fileSize')" />
        <!-- 文件大小 -->
      </el-table>
    </el-drawer>

    <!--单元-->
    <el-row :gutter="20" style="margin-top:10px;">
      <el-col :span="6">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect">
            <span>{{ $t('lang_pack.cellprogupd.cell') }}</span>
            <!-- 单元 -->
          </div>
          <el-tree ref="cellTree" show-checkbox :check-on-click-node="true" :data="cellTreeData" :props="cellTreeProps" node-key="cell_id" @check-change="handleCheckChange" />
        </el-card>
      </el-col>

      <!--单元服务-->
      <el-col :span="18">
        <el-card class="box-card1">
          <div slot="header" class="wrapTextSelect">
            <span>{{ cellTitle }}</span>
          </div>
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="listLoadingTable" :data="tableDataTable" style="width: 100%" :stripe="true" max-height="445px" :highlight-current-row="true" :row-class-name="tableRowClassName">
            <el-table-column  :label="$t('lang_pack.cellprogupd.serialNumber')" type="index" width="120" />
            <!-- 序号 -->
            <el-table-column  :show-overflow-tooltip="true" prop="fastcode_des" :label="$t('lang_pack.cellprogupd.updateObject')" />
            <!-- 更新对象 -->
            <el-table-column  :show-overflow-tooltip="true" prop="proc_update_way" :label="$t('lang_pack.cellprogupd.updateMode')" width="150" />
            <!-- 更新方式 -->

            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-link class="linkItem" type="primary" @click="toTableButUpload(scope.row)">上传</el-link>
                <el-link class="linkItem" type="primary" @click="toTableButFileList(scope.row)">查看文件</el-link>
                <el-link class="linkItem" :disabled="scope.row.update_cell_proc_id === 0" type="primary" @click="toTableButInstall(scope.row)">更新</el-link>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import axios from 'axios'
import { lovFastcode } from '@/api/core/system/sysFastcode'
import { lovServer } from '@/api/core/center/server'
import { lovCell } from '@/api/core/center/cell'

import { CellUpdateProcSel, CellUpdateProcSave, UpdateProcDSel, ServerCellProcInfoSel, ServerUpdate, ClearProcAndProcD } from '@/api/core/center/cellProgUpd'

export default {
  name: 'CELL_PROG_UPD',
  // 数据模型
  data() {
    return {
      // 查询条件
      formServer: {
        server_id: ''
      },

      // 弹框：文件上传
      formUpload: {
        proc_update_way: 'package'
      },
      dialogVisbleSyncFrom: false,
      uploadLimit: 2, // 注意：加密程序需要传2个文件
      uploadAccept: '.jar,.tar.gz',
      // 上传
      fileList: [],
      fileData: '',

      // 弹框：文件列表
      dialogVisbleSyncFileList: false,
      procDDate: [],

      // 单元
      // Cell树结构数据
      cellTreeData: [],
      cellTreeProps: {
        children: 'children',
        label: 'cell_container_name_des'
      },
      cellTitle: '当前选择单元',

      // 单元服务Table
      listLoadingTable: false,
      tableDataTable: [],

      serverData: [], // 服务Lov
      updateWayData: [], // 更新方式Lov
      currentCell: [],
      currentProc: [],

      currentServerHost: '' // 当前服务IP
    }
  },

  mounted: function() {},
  created: function() {
    // 服务Lov
    this.serverData = []
    var query1 = {
      userName: Cookies.get('userName'),
      enable_flag: 'Y'
    }
    lovServer(query1)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.count > 0) {
          this.serverData = defaultQuery.data
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })

    // 更新方式Lov
    const query2 = {
      userName: Cookies.get('userName'),
      fastcode_group_code: 'PROC_UPDATE_WAY'
    }
    // 从后台获取到对象数组
    lovFastcode(query2)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.updateWayData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },

  methods: {
    // ===============================================================================
    // Server值改变时
    serverChange() {
      this.cellTreeData = []
      this.tableDataTable = []
      this.cellTitle = '当前选择单元:'
      this.currentCell = []
      this.currentProc = []
      this.$refs.cellTree.setCheckedKeys([])
      if (this.formServer.server_id === '') {
        return
      }

      // 当前服务IP
      const serverInfo = this.serverData.filter(item => item.server_id === this.formServer.server_id)[0]
      if (serverInfo.server_host_1 !== '') {
        this.currentServerHost = serverInfo.server_host_1
      } else if (serverInfo.server_host_2 !== '') {
        this.currentServerHost = serverInfo.server_host_2
      } else if (serverInfo.server_host_3 !== '') {
        this.currentServerHost = serverInfo.server_host_3
      } else if (serverInfo.server_host_4 !== '') {
        this.currentServerHost = serverInfo.server_host_4
      }

      console.log('获取currentServerHost：' + this.currentServerHost)

      var query = {
        userName: Cookies.get('userName'),
        server_id: this.formServer.server_id
      }
      this.listLoadingTable = true
      lovCell(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.cellTreeData = defaultQuery.data
            }
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
    },

    // ===============================================================================
    // 树
    handleCheckChange(data, checked, indeterminate) {
      if (checked) {
        this.$refs.cellTree.setCheckedKeys([])
        this.$refs.cellTree.setCheckedKeys([data.cell_id])
        this.cellTitle = '当前选择单元:' + data.cell_container_name_des
        this.currentCell = data
        this.loadTable()
      } else {
        if (this.$refs.cellTree.getCheckedNodes().length === 0) {
          this.cellTitle = '当前选择单元:'
          this.currentCell = []
          this.tableDataTable = []
        }
      }
    },

    // 单元更新信息 查询
    loadTable() {
      this.tableDataTable = []
      var query = {
        userName: Cookies.get('userName'),
        cell_id: this.currentCell.cell_id
      }
      this.listLoadingTable = true
      CellUpdateProcSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.tableDataTable = defaultQuery.data
            }
          }
          this.listLoadingTable = false
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
    },

    // ===============================================================================
    // 单元服务
    tableRowClassName({ row, rowIndex }) {
      if (row.update_cell_proc_id !== 0) {
        return 'success-row'
      }
      return ''
    },

    // 上传
    toTableButUpload(row) {
      this.currentProc = row
      this.dialogVisbleSyncFrom = true
    },
    // 查看文件
    toTableButFileList(row) {
      this.procDDate = []
      var query = {
        userName: Cookies.get('userName'),
        update_cell_proc_id: row.update_cell_proc_id
      }
      UpdateProcDSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.procDDate = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
      this.dialogVisbleSyncFileList = true
    },
    // 更新
    toTableButInstall(row) {
      var cell_names = []
      this.$refs.cellTree.getCheckedNodes().forEach(item => {
        cell_names.push(item.cell_container_name)
      })
      this.$confirm('确定要更新' + cell_names + '的' + row.fastcode_code + '吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          ServerCellProcInfoSel({
            userName: Cookies.get('userName'),
            server_id: this.formServer.server_id,
            cell_ids: row.update_cell_id,
            update_cell_proc_id: row.update_cell_proc_id
          })
            .then(res => {
              const defaultQuery = JSON.parse(res.result)

              console.log('toTableButInstall更新查询数据：')
              console.log(defaultQuery)

              if (defaultQuery.deploy_info.length > 0 && defaultQuery.cellList.length > 0) {
                // 配置路径
                var method = ':7089/SysContainerFileDeploy'
                var path = ''
                if (process.env.NODE_ENV === 'development') {
                  path = 'http://localhost' + method
                } else {
                  path = 'http://' + this.currentServerHost + method
                }

                console.log('toTableButInstall接口地址：' + path)

                ServerUpdate(path, defaultQuery)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                      this.$message({
                        message: '更新成功',
                        type: 'success'
                      })
                      this.updateSuccessDelete({ update_cell_proc_id: row.update_cell_proc_id }, 2)
                    }
                  })
                  .catch(() => {
                    this.$message({
                      message: '更新异常',
                      type: 'error'
                    })
                  })
              } else {
                this.$message({
                  message: '未发现可更新信息',
                  type: 'info'
                })
              }
            })
            .catch(() => {
              this.$message({
                message: '初始化数据异常',
                type: 'error'
              })
            })
        })
        .catch(() => {})
    },
    // 删除更新信息
    updateSuccessDelete(parameter, type) {
      ClearProcAndProcD(parameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({
              message: '相关数据清理成功',
              type: 'success'
            })
            if (type === 1) {
              // 查询
              this.serverChange()
            } else {
              this.loadTable()
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '相关数据清理异常',
            type: 'error'
          })
        })
    },

    // ===============================================================================
    // 弹框：文件上传
    // 关闭抽屉时
    drawerClose() {
      this.fileList = []
    },
    drawerFileListClose() {
      this.procDDate = []
    },
    getCellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec;padding:5px 0px 5px 0px'
    },
    // 更新方式切换
    procUpdateWayChange() {
      if (this.formUpload.proc_update_way === 'package') {
        this.uploadLimit = 1
        this.uploadAccept = '.jar,.tar.gz'
      } else {
        this.uploadLimit = 20
        this.uploadAccept = '.dll,.exe,.config,.json'
      }
    },

    // 抽屉取消按钮事件
    toButDrawerCancel() {
      this.dialogVisbleSyncFrom = false
    },
    // 抽屉上传按钮事件
    toButDrawerUpload() {
      if (this.fileList.length === 0) {
        this.$message({
          message: '请选择文件',
          type: 'info'
        })
        return
      }

      // 格式化 参数
      this.fileData = new FormData()
      this.fileData.append('cell_container_name', this.currentCell.cell_container_name)
      this.fileData.append('proc_code', this.currentProc.fastcode_code)
      this.$refs.upload.submit()

      // 配置路径
      var method = ':7089/SysContainerFileUpload'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost' + method
      } else {
        path = 'http://' + this.currentServerHost + method
      }

      console.log('toButDrawerUpload接口地址：' + path)

      axios
        .post(path, this.fileData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: progressEvent => {
            const num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
            this.$refs.upload.onProgress({ percent: num }) // 进度条
          }
        })
        .then(response => {
          const defaultQuery = JSON.parse(JSON.stringify(response))

          console.log('toButDrawerUpload文件上传返回值')
          console.log(defaultQuery)

          if (defaultQuery.data.code === 0) {
            this.$message({
              type: 'success',
              message: '文件上传成功!'
            })
            this.initCellFilieUpdateProc()
            this.currentProc = []
            this.dialogVisbleSyncFrom = false
          } else {
            this.$message({
              type: 'error',
              message: defaultQuery.data.msg
            })
          }
        })
    },
    initCellFilieUpdateProc() {
      var procDetailList = []
      this.fileList.forEach(item => {
        var procDetai = {}
        procDetai.file_name = item.name
        procDetai.file_type = item.raw.type
        procDetai.file_size = item.size
        procDetailList.push(procDetai)
      })
      var data = {
        userName: Cookies.get('userName'),
        update_cell_proc_id: this.currentProc.update_cell_proc_id,
        update_cell_id: this.currentProc.update_cell_id,
        proc_code: this.currentProc.fastcode_code,
        proc_update_way: this.formUpload.proc_update_way,
        proc_detail_list: procDetailList
      }
      CellUpdateProcSave(data)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.loadTable()
          }
        })
        .catch(() => {
          this.$message({
            message: '初始化数据异常',
            type: 'error'
          })
        })
    },
    // 文件
    // 导入文件时将文件存入数组中
    handleImport(file, fileList) {
      this.fileList = fileList
    },
    // 点击上传时覆盖默认上传事件
    uploadFile: function(file) {
      this.fileData.append('file', file.file)
    },
    /** 文件正在上传时的钩子 **/
    progressA(event, file) {}
  }
}
</script>

<style scoped lang="scss">
.box-card {
  height: calc(100vh - 55px);
  padding: 10px;
  padding-top: 20px;
  .box-card1 {
    height: calc(100vh - 140px);
    .el-checkbox__label {
      font-size: 12px;
      color: #757575;
    }
  }
}
// 去掉弹窗出现时的出现蓝框
:focus {
  outline: 0;
}
.el-card__body {
  padding: 10px;
}
.el-table .success-row {
  background: #6cca9e;
  font-weight: 600;
}
.wrapTextSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    white-space: nowrap;
    font-size: 14px;
    color: rgb(117, 117, 117);
    font-weight: bold;
    margin-right: 10px;
  }
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: #e8efff;
}
::v-deep .el-tree-node__content {
  padding: 10px;
  height: auto;
}
::v-deep .el-tree-node__content:hover {
  background-color: #e8efff;
}
</style>
