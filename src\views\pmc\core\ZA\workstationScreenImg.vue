<template>
    <el-carousel  indicator-position="none" arrow="never" height="calc(100vh)">
      <el-carousel-item v-for="(item,index) in imgArr" :key="index">
        <img :src="item.IMAGE_URL" class="imgUrl" :onerror="defaultImage"  />
      </el-carousel-item>
    </el-carousel>
  </template>
 <script>
 import { PmcCoreMesCaProcessDocSel } from '@/api/pmc/sysworkstationScreenImg'
 export default {
    name: 'workstationScreenImg',
    data(){
        return {
            imgArr:[],
            defaultImage: 'this.src="' + require('@/assets/images/qyc1.png') + '"' ,
            timer:null,
        }
    },
    mounted(){
        this.getScreenImgUrl()
        this.timer = setInterval(() => {
            this.getScreenImgUrl()
        }, 1000 * 60);
    },
    beforeDestroy(){
        clearInterval(this.timer)
    },
    methods:{
        getScreenImgUrl(){
            const query = {}
            PmcCoreMesCaProcessDocSel(query).then(res=>{
                if (res.code !== 0 && res.count < 0) {
                    this.imgArr = []
                    console.log('请求数据异常')
                    return
                }
                this.imgArr = res.data
            }).catch(err=>{
                this.imgArr = []
                console.log('请求数据位空')
            })
        }
    }
  }
</script>
  <style scoped lang="less">
    .imgUrl{
        width:100%;
        height:100%
    }
  </style>