<template>
    <div class="box-card">
        <el-card shadow="always" style="border: 0px solid #d2dbe4">
            <el-form ref="query" :inline="true" size="mini" style="margin-top: 15px; padding: 0" label-width="70px">
                <el-row>
                    <el-form-item label="产线" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.prod_line_code" clearable style="width: 150px" value-key="prod_line_id"
                            @change="handleProdLineChange">
                            <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_des"
                                :value="item">
                                <span style="float: left">{{ item.prod_line_des }}</span>
                                <span style="
                                    float: right;
                                    margin-left: 20px;
                                    color: #8492a6;
                                    font-size: 13px;
                                ">{{ item.prod_line_code }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="工位号" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.station_code" filterable clearable style="width: 150px"
                            value-key="station_code" @change="getQulityforData">
                            <el-option v-for="item in stationData" :key="item.station_code" :label="item.station_code"
                                :value="item">
                                <span style="float: left">{{ item.station_code }}</span>
                                <span style="
                            float: right;
                            margin-left: 20px;
                            color: #8492a6;
                            font-size: 13px;
                        ">{{ item.station_des }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="测量项目" style="margin: 0px 0px 5px 0px">
                        <el-select v-model="query.quality_for" filterable clearable style="width: 220px">
                            <el-option v-for="item in tagList" :key="item.tag_des" :label="item.tag_des"
                                :value="item.tag_des" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="查询时间" style="margin: 0px 0px 5px 0px">
                        <el-date-picker v-model="query.date" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
                            :default-time="['00:00:00', '23:59:59']" range-separator="~" start-placeholder="开始日期"
                            end-placeholder="结束日期" style="width: 350px" align="right" @input="$forceUpdate()" />
                    </el-form-item>
                    <el-button style="margin: 0px 0px 5px 0px" class="filter-item" size="mini" type="primary"
                        icon="el-icon-search" @click="handleQuery()">查询</el-button>
                    <el-button style="margin: 0px 0px 5px 0px" class="filter-item" size="mini" type="primary"
                        icon="el-icon-upload2" :disabled="!tableData.length" :loading="downloadLoading"
                        @click="exportExcel()">导出</el-button>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <el-table ref="table" v-loading="tableLoading" :data="tableData" style="width: 100%"
                :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" :height="height"
                :highlight-current-row="true">
                <el-table-column :show-overflow-tooltip="true" prop="prod_line_code" width="120" label="产线编码" />
                <el-table-column :show-overflow-tooltip="true" prop="date" label="日期" />
                <el-table-column :show-overflow-tooltip="true" prop="oee" width="130" label="OEE" />
                <el-table-column :show-overflow-tooltip="true" prop="timeActivationRate" width="130" label="时间开动率(%)" />
                <el-table-column :show-overflow-tooltip="true" prop="performanceEfficiency" width="130" label="性能开动率(%)" />
                <el-table-column :show-overflow-tooltip="true" prop="qualificationRate" width="120" label="合格率(%)" />
                <el-table-column :show-overflow-tooltip="true" prop="start_time" label="开动时间(m)" />
                <el-table-column :show-overflow-tooltip="true" prop="plan_time" label="计划时间(m)" />
            </el-table>
        </el-card>
        <el-card shadow="always" style="margin-top: 5px">
            <div id="cpk_chart" style="width: 100%;height: calc(100vh - 620px)"></div>
        </el-card>
    </div>
</template>
<script>
import { downloadFile } from '@/utils/index'
import { mesQualitySpcAnalyze, mesQualitySpcTagList, oee, oeeExport } from '@/api/mes/core/spcReport'
import { sel as selProdLine } from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { sel as selSmallModel } from '@/api/mes/core/smallModel'
import Cookies from 'js-cookie'
import ECharts from 'vue-echarts'
export default {
    name: 'MES_REPORT_OEE',
    components: {
        ECharts
    },
    data() {
        return {
            height: document.documentElement.clientHeight - 485,
            prodLineData: [],
            smallModelTypeData: [],
            stationData: [],
            tableData: [],
            tableDataCatch: [],
            tagList: [],
            tableLoading: false,
            query: {
                station_code: '',
                small_model_type: '',
                quality_for: '',
                date: null
            },
            // 时间选择器
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            oeeDom: null,
            downloadLoading: false,
        }
    },
    computed: {
        // 默认时间
        timeDefault() {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            // 月，日 不够10补0
            const defalutStartTime =
                start.getFullYear() +
                "-" +
                (start.getMonth() + 1 >= 10
                    ? start.getMonth() + 1
                    : "0" + (start.getMonth() + 1)) +
                "-" +
                (start.getDate() >= 10 ? start.getDate() : "0" + start.getDate()) +
                " 00:00:00";
            const defalutEndTime =
                end.getFullYear() +
                "-" +
                (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : "0" + (end.getMonth() + 1)) +
                "-" +
                (end.getDate() >= 10 ? end.getDate() : "0" + end.getDate()) +
                " 23:59:59";
            return [defalutStartTime, defalutEndTime];
        },
    },
    created: function () {
        this.query.date = this.timeDefault;
        selProdLine({
            user_name: Cookies.get('userName'),
            enable_flag: 'Y'
        })
            .then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === 0) {
                    if (defaultQuery.data.length > 0) {
                        this.prodLineData = defaultQuery.data
                    }
                }
            })
            .catch(() => {
                this.$message({
                    message: '初始化模式数据异常',
                    type: 'error'
                })
            })
    },
    mounted() {
        window.addEventListener('resize', function () {
            that.oeeDom.resize()
        })
    },
    methods: {
        handleProdLineChange(data) {
            const query = {
                userID: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            }
            selStation(query)
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.stationData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询异常',
                        type: 'error'
                    })
                })

                return
            selSmallModel({
                user_name: Cookies.get('userName'),
                enable_flag: 'Y',
                prod_line_id: data.prod_line_id
            })
                .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.smallModelTypeData = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '初始化模式数据异常',
                        type: 'error'
                    })
                })
        },
        handleQuery() {
            if (this.query.prod_line_code === '') {
                this.$message({
                    message: '请选择产线',
                    type: 'info'
                })
                return
            }
            if (this.query.station_code === '') {
                this.$message({
                    message: '请选择工位号',
                    type: 'info'
                })
                return
            }
            if (this.query.quality_for === '') {
                this.$message({
                    message: '请选择测量项目',
                    type: 'info'
                })
                return
            }
            if (!this.query.date) {
                this.$message({
                    message: '请选择时间',
                    type: 'info'
                })
                return
            }
            this.tableData = []
            this.tableLoading = true
            this.getOeeTrend()
        },
        getQulityforData(data) {
            this.tagList = []
            this.query.quality_for = ''
            mesQualitySpcTagList({
                enable_flag: 'Y',
                station_id: data.station_id
            })
                .then(res => {
                    console.log(res)
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                        if (defaultQuery.data.length > 0) {
                            this.tagList = defaultQuery.data
                        }
                    }
                })
                .catch(() => {
                    this.$message({
                        message: '查询采集项目异常',
                        type: 'error'
                    })
                })
        },
        getOeeTrend() {
            this.oeeDom = this.$echarts.init(document.getElementById('cpk_chart'))
            const query = {
                prod_line_id: this.query.prod_line_code.prod_line_id,
                prod_line_code: this.query.prod_line_code.prod_line_code,
                station_code: this.query.station_code.station_code,
                quality_for:this.query.quality_for,
                item_date: this.query.date
            }
            const colors = ['#5470c6', '#91cc75', '#5fc79e','#2F8FFF']
            oee(query).then(res => {
                const defaultQuery = JSON.parse(JSON.stringify(res))
                if (defaultQuery.code === '200') {
                    if (Object.keys(defaultQuery.data).length > 0) {
                        this.tableData = defaultQuery.data.oeeInfos
                        const data = this.sortObjectByTimestamp(defaultQuery.data.oeeReportData)
                        const xAxisData = []
                        const seriesData = []
                        const seriesData1 = []
                        const seriesData2 = []
                        const seriesData3 = []
                        for (let key in data) {
                            xAxisData.push(key)
                            seriesData.push(data[key].performanceEfficiency)
                            seriesData1.push(data[key].qualificationRate)
                            seriesData2.push(data[key].timeActivationRate)
                            seriesData3.push(data[key].oee)
                        }
                        const option = {
                            backgroundColor: 'white',
                            grid: {
                                top: '20%',
                                left: '5%',
                                right: '5%',
                                bottom: '8%',
                                containLabel: true
                            },
                            tooltip: {
                                trigger: 'axis',
                                borderWidth: 1,
                                axisPointer: {
                                    type: 'shadow'
                                },
                                extraCssText: 'z-index:2',
                                formatter: (params) => {
                                    let arr = [...params];
                                    let str = '';
                                    arr.forEach((item, index) => {
                                        str += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
                                        colors[index] +
                                            ';"></span>' + item.seriesName + '  ' + item.data + '%<br />';
                                    });
                                    str = arr[0].name + '<br />' + str;
                                    return str;
                                },
                            },
                            legend: [{
                                top: 'top',
                                left: 'center',
                                orient: 'horizontal',
                                data: ['性能开动率', '合格率', '时间开动率','OEE'],
                                itemWidth: 15,
                                itemHeight: 10,
                                itemGap: 15,
                                borderRadius: 4,
                                textStyle: {
                                    color: '#000',
                                    fontFamily: 'Alibaba PuHuiTi',
                                    fontSize: 14,
                                    fontWeight: 400
                                }
                            }],
                            xAxis: {
                                type: 'category',
                                data: xAxisData,
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: '#393939' //X轴文字颜色
                                    }
                                }
                            },
                            yAxis: [

                                {
                                    type: 'value',
                                    name: '',
                                    nameTextStyle: {
                                        color: '#000',
                                        fontFamily: 'Alibaba PuHuiTi',
                                        fontSize: 14,
                                        fontWeight: 600
                                        // padding: [0, 0, 0, 40], // 四个数字分别为上右下左与原位置距离
                                    },
                                    nameGap: 30,  // 表现为上下位置
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#eeeeee'
                                        }
                                    },
                                    axisTick: {
                                        show: false
                                    },
                                    axisLabel: {
                                        color: '#393939',
                                        fontSize: 14,
                                        formatter: function (value) {
                                            return value + '%';
                                        }
                                    },
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: '#eeeeee'
                                        }
                                    }

                                }

                            ],
                            series: [
                                {
                                    name: '性能开动率',
                                    type: 'line',
                                    showAllSymbol: true, //显示所有图形。
                                    //标记的图形为实心圆
                                    symbolSize: 8, //标记的大小
                                    itemStyle: {
                                        //折线拐点标志的样式
                                        color: 'white',
                                        borderWidth: '2',
                                        borderColor: '#5470c6',
                                        normal: {
                                            color: '#5470c6',//拐点颜色
                                        }
                                    },
                                    lineStyle: {
                                        color: '#5470c6'
                                    },
                                    data: seriesData
                                },
                                {
                                    name: '合格率',
                                    type: 'line',
                                    showAllSymbol: true, //显示所有图形。
                                    symbolSize: 8, //标记的大小
                                    itemStyle: {
                                        //折线拐点标志的样式
                                        color: 'white',
                                        borderWidth: '2',
                                        borderColor: '#91cc75',
                                        normal: {
                                            color: '#91cc75',//拐点颜色

                                        },

                                    },
                                    lineStyle: {
                                        color: '#91cc75'
                                    },
                                    data: seriesData1
                                },
                                {
                                    name: '时间开动率',
                                    type: 'line',
                                    showAllSymbol: true, //显示所有图形。
                                    symbolSize: 8, //标记的大小
                                    itemStyle: {
                                        //折线拐点标志的样式
                                        color: 'white',
                                        borderWidth: '2',
                                        borderColor: '#5fc79e',
                                        normal: {
                                            color: '#5fc79e',//拐点颜色
                                        },

                                    },
                                    lineStyle: {
                                        color: '#5fc79e'
                                    },
                                    data: seriesData2
                                },
                                {
                                    name: 'OEE',
                                    type: 'line',
                                    showAllSymbol: true, //显示所有图形。
                                    symbolSize: 8, //标记的大小
                                    itemStyle: {
                                        //折线拐点标志的样式
                                        color: 'white',
                                        borderWidth: '2',
                                        borderColor: '#2F8FFF',
                                        normal: {
                                            color: '#2F8FFF',//拐点颜色
                                        },

                                    },
                                    lineStyle: {
                                        color: '#2F8FFF'
                                    },
                                    data: seriesData3
                                }
                            ]
                        }
                        this.oeeDom.setOption(option)
                    }
                } else {
                    this.oeeDom = null
                    this.$message({
                        type: 'warning',
                        message: defaultQuery.msg
                    })
                }
            }).catch((ex) => {
                this.oeeDom = null
                this.$message({
                    type: 'warning',
                    message: ex.msg
                })
            })
            this.tableLoading = false
        },
        exportExcel() {
            const chart = this.$echarts.init(document.getElementById('cpk_chart'))
            const base64 = chart.getDataURL({
                pixelRatio: 4, // 可选，设置像素比例
                backgroundColor: '#fff' // 可选，设置背景颜色
            });
            // 这句代码是因为入参不需要"data:image/png;base64"
            const base = base64.split(',')[1]
            this.downloadLoading = true
            if (!base) {
                this.downloadLoading = false
                return
            }
            const query = {
                file: base,
                prod_line_id: this.query.prod_line_code.prod_line_id,
                prod_line_code: this.query.prod_line_code.prod_line_code,
                station_code: this.query.station_code.station_code,
                quality_for:this.query.quality_for,
                item_date: this.query.date
             }
            oeeExport(query).then(res => {
                downloadFile(res, 'oee导出', 'xlsx')
                this.downloadLoading = false
            }).catch((ex) => {
                this.downloadLoading = false
                this.$message({ type: 'error', msg: ex.msg })
            })

        },
        // 字符串转时间戳并且支持排序的方法
        sortObjectByTimestamp(obj) {
            var sortedArray = Object.entries(obj).sort((a, b) => {
                return new Date(a[0]) - new Date(b[0]);
            });
            var sortedObj = {};
            for (var i = 0; i < sortedArray.length; i++) {
                var key = sortedArray[i][0];
                var value = sortedArray[i][1];
                sortedObj[key] = value;
            }
            return sortedObj;
        }
    }
}
</script>
