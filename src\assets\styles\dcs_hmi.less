@media screen and (max-height: 768px) {
    .mainPage-container{
        .crownBlock{
            .el-card__body{
                height: 465px;
                font-size: 12px;
                .Inb-out-bound{
                    height: 18vh !important;
                }
            }
            ::v-deep .el-divider--horizontal{
                margin: 0 !important;
            }
        }
    }
    .app-container{
        .warehouse{
            ::v-deep .el-descriptions-item__cell {
                .el-descriptions-item__label,
                .el-descriptions-item__content {
                    font-size: 16px !important;
                }
            }
        }
    }
}
@media screen and (min-height: 950px) {
    .mainPage-container{
        .crownBlock{
            .Inb-out-bound{
                height: 14vh !important;
            }
        }
        .sorting{
            height: 32vh !important;
            ::v-deep .el-divider--horizontal{
                margin: 24px 0 !important;
            }
        }
    }
    
}