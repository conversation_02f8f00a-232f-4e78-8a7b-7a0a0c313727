<template>
    <div class="app-container">
        <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="100px">
                <div class="wrapElForm">
                    <div class="wrapElFormFirst col-md-10 col-12">
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="事件类型:">
                                <!-- 事件类型 -->
                                <el-select v-model="query.event_type" clearable filterable>
                                    <el-option v-for="item in dict.EVENT_TYPE" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="formChild col-md-4 col-12">
                            <el-form-item label="事件状态:">
                                <!-- 事件状态 -->
                                <el-select v-model="query.event_status" clearable filterable>
                                    <el-option v-for="item in dict.EVENT_STATUS" :key="item.id" :label="item.label" :value="item.value" >
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="wrapElFormSecond formChild col-md-2 col-12">
                        <el-form-item>
                            <rrOperation />
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </el-card>

        <el-card shadow="always" style="margin-top: 10px">
            <!--工具栏-->
            <crudOperation show="" :permission="permission" />

            <el-row :gutter="20">
                <el-col :span="24">
                    <!--表单渲染-->
                    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title"
                        :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="750px">
                        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small"
                            label-width="145px" :inline="true">
                            <el-form-item :label="$t('lang_pack.eventRecordForm.eventType')" prop="event_type">
                                <!-- 事件类型 -->
                                <el-input v-model="form.mo_id" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.eventRecordForm.eventName')" prop="event_name">
                                <!-- 事件名称 -->
                                <el-input v-model="form.event_name" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.eventRecordForm.messageCODE')" prop="event_code">
                                <!-- 消息CODE -->
                                <el-input v-model="form.event_code" clearable size="small" />
                            </el-form-item>
                            <el-form-item :label="$t('lang_pack.eventRecordForm.eventStatus')" prop="event_status">
                                <!-- 事件状态 -->
                                <el-input v-model="form.event_status" clearable size="small" />
                            </el-form-item>
                        </el-form>
                        <el-divider />
                        <div style="text-align: center">
                            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                                $t('lang_pack.commonPage.cancel') }}</el-button>
                            <!-- 取消 -->
                            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2"
                                @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
                            <!-- 确认 -->
                        </div>
                    </el-drawer>

                    <!--表格渲染-->
                    <el-table border  ref="table" v-loading="crud.loading"
                        size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height"
                        :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
                        <el-table-column type="selection" width="55" />
                        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
                        <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-descriptions style="margin-right:150px" :column="4" size="small" border>
                                    <el-descriptions-item label="ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_event_id }}</el-descriptions-item>
                                    <el-descriptions-item label="主任务ID" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.mo_id }}</el-descriptions-item>
                                    <el-descriptions-item label="用户" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.user_name }}</el-descriptions-item>
                                    <el-descriptions-item label="事件类型" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{dict.label.EVENT_TYPE[props.row.event_type]}}</el-descriptions-item>
                                    <el-descriptions-item label="事件状态" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{dict.label.EVENT_STATUS[props.row.event_status]}}</el-descriptions-item>
                                    <el-descriptions-item label="事件名称" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.event_name }}</el-descriptions-item>
                                    <el-descriptions-item label="发送内容" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.send_data }}</el-descriptions-item>
                                    <el-descriptions-item label="接受内容" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.recv_data }}</el-descriptions-item>
                                    <el-descriptions-item label="消息CODE" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.event_code }}</el-descriptions-item>
                                    <el-descriptions-item label="消息内容" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.event_msg }}</el-descriptions-item>
                                    <el-descriptions-item label="开始时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.start_date }}</el-descriptions-item>
                                    <el-descriptions-item label="结束时间" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.end_date }}</el-descriptions-item>
                                    <el-descriptions-item label="消耗时间(秒)" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.cost_times }}</el-descriptions-item>
                                    <el-descriptions-item label="备注" label-class-name="table-descriptions-label" content-class-name="table-descriptions-content">{{ props.row.event_remarks }}</el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-table-column>
                        <!-- 事件类型 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="event_type"
                            :label="$t('lang_pack.eventRecordForm.eventType')" min-width="100" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.EVENT_TYPE[scope.row.event_type] }}
                            </template>
                        </el-table-column>
                        <!-- 事件状态 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="event_status"
                            :label="$t('lang_pack.eventRecordForm.eventStatus')" min-width="100" align='center'>
                            <template slot-scope="scope">
                                <!--取到当前单元格-->
                                {{ dict.label.EVENT_STATUS[scope.row.event_status] }}
                            </template>
                        </el-table-column>
                        <!-- 事件名称 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="event_name"
                            :label="$t('lang_pack.eventRecordForm.eventName')" min-width="100" align='center'/>
                        <!-- 发送内容 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="send_data"
                            :label="$t('lang_pack.eventRecordForm.sendContent')" min-width="100" align='center'/>
                        <!-- 接受内容 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="recv_data"
                            :label="$t('lang_pack.eventRecordForm.acceptContent')" min-width="100" align='center'/>
                        <!-- 消息CODE -->
                        <el-table-column  :show-overflow-tooltip="true" prop="event_code"
                            :label="$t('lang_pack.eventRecordForm.messageCODE')" min-width="100" align='center'/>
                        <!-- 消息内容 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="event_msg"
                            :label="$t('lang_pack.eventRecordForm.messageContent')" min-width="100" align='center'/>
                        <!-- 开始时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="start_date"
                            :label="$t('lang_pack.eventRecordForm.startTime')" min-width="100" align='center'/>
                        <!-- 结束时间 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="end_date"
                            :label="$t('lang_pack.eventRecordForm.endTime')" min-width="100" align='center'/>
                        <!-- 消耗时间(秒) -->
                        <el-table-column  :show-overflow-tooltip="true" prop="cost_times"
                            :label="$t('lang_pack.eventRecordForm.timeConsuming')" min-width="100" align='center'/>
                        <!-- 备注 -->
                        <el-table-column  :show-overflow-tooltip="true" prop="event_remarks"
                            :label="$t('lang_pack.eventRecordForm.remake')" min-width="100" align='center'/>
                    </el-table>
                    <!--分页组件-->
                    <pagination />
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
  
<script>
import crudTaskEvent from '@/api/dcs/core/aps/taskEvent'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
    mo_event_id:'',
    mo_id:'',
    user_name:'',
    event_type:'',
    event_name:'',
    send_data:'',
    recv_data:'',
    event_code:'',
    event_msg:'',
    event_status:'',
    start_date:'',
    end_date:'',
    cost_times:'',
    event_remarks:'',
}
export default {
    name: 'WEB_APS_TASK_EVENT',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({
            title: '生产任务事件记录',
            // 登录用户
            userName: Cookies.get('userName'),
            // 唯一字段
            idField: 'mo_event_id',
            // 排序
            sort: ['mo_event_id asc'],
            // CRUD Method
            crudMethod: { ...crudTaskEvent },
            // 按钮显示
            optShow: {
                add: false,
                edit: false,
                del: false,
                reset: true
            }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据模型
    data() {
        return {
            height: document.documentElement.clientHeight - 270,
            permission: {
                add: ['admin', 'fmod_recipe_quality:add'],
                edit: ['admin', 'fmod_recipe_quality:edit'],
                del: ['admin', 'fmod_recipe_quality:del']
            },
            rules: {
                quality_from: [{ required: true, message: '请选择工位', trigger: 'blur' }]
            },
        }
    },
    // 数据字典
    dicts: ['EVENT_TYPE','EVENT_STATUS'],
    mounted: function () {
        const that = this
        window.onresize = function temp() {
            that.height = document.documentElement.clientHeight - 270
        }
    },
    created: function () {
    },
    methods: {
    }
}
</script>
  