<template>
  <div>
    <el-card shadow="never" style="margin-top: 10px">
      <el-dialog :fullscreen="false" :append-to-body="true" top="10px" :show-close="true" :close-on-click-modal="false" title="模组列表" custom-class="step-attr-dialog" width="90%" :visible.sync="detailDialogVisible">
        <detail2 v-if="detailDialogVisible" ref="detail2" :data="currentRowData" />
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="24">

          <!--表格渲染-->
          <el-table ref="table" border size="small" :data="detailData" style="width: 100%" height="450px" highlight-current-row @header-dragend="crud.tableHeaderDragend()">

            <el-table-column width="100" label="解绑" align="center" fixed="left">
              <template slot-scope="scope">
                <el-button type="primary" size="small" icon="el-icon-check" @click="unbind(scope.row)">解绑</el-button>
              </template>
            </el-table-column>
            <el-table-column width="100" label="模组列表" align="center" fixed="left">
              <template slot-scope="scope">
                <el-tag type="" effect="plain" style="cursor: pointer;" size="medium" @click="opendDetail(scope.row)">模组列表</el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="item_date" min-width="150" label="时间" />
            <el-table-column :show-overflow-tooltip="true" prop="make_order" min-width="100" label="订单号" />
            <el-table-column :show-overflow-tooltip="true" prop="small_model_type" min-width="100" label="机型" />
            <el-table-column :show-overflow-tooltip="true" prop="pack_barcode" min-width="220" label="PACK码" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { selDetail, edit, sel } from '@/api/mes/core/mePackQuality'
import crudMeCarQuality from '@/api/mes/core/meCarQuality'
import detail2 from '@/views/mes/core/mePackQuality/detail'
export default {
  name: 'MES_ME_PACK_QUALITY',
  components: { detail2 },
  // 数据字典
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      detailData: [],
      updDialogVisbleSync: false,
      currentRowData: {},
      detailDialogVisible: false,
      mz_barcode: '',
      mzCount: 0
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    this.toQuery()
  },
  methods: {
    toQuery() {
      console.log(this.data)
      sel({
        car_code: this.data.car_code
      })
        .then(res => {
          this.mzCount = res.count
          if (res.code === 0 && res.count > 0) {
            this.detailData = res.data
          } else {
            this.detailData = []
          }
        })
        .catch(ex => {
          this.$message({ message: '操作失败：' + ex, type: 'error' })
          this.detailData = []
        })
    },
    updateMz(row) {
      this.currentRowData = row
      this.mz_barcode = ''
      this.updDialogVisbleSync = true
    }, unbind(row) {
      console.log(row)
      this.$confirm(`确认解绑选中的${row.pack_barcode}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var idList = [row.barcode_rel_id]
        console.log(idList)
        crudMeCarQuality.delRel({
          idList: idList
        })
          .then(res => {
            if (res.code === 0) {
              this.$message({ message: '操作成功', type: 'success' })
              this.toQuery()
            } else {
              this.$message({ message: '操作失败：' + res.msg, type: 'error' })
            }
          })
          .catch(ex => {
            this.$message({ message: '操作失败：' + ex, type: 'error' })
          })
      })
        .catch(() => {})
    },
    saveMzBarcode() {
      if (this.mz_barcode === '') {
        this.$message({ message: '请输入模组条码', type: 'info' })
        return
      }
      this.$confirm(`确认将模组条码${this.currentRowData.mz_barcode}替换成${this.mz_barcode}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          edit({
            pack_mz_rel_id: this.currentRowData.pack_mz_rel_id,
            mz_barcode: this.mz_barcode
          })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.updDialogVisbleSync = false
                this.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    },
    opendDetail(row) {
      this.currentRowData = row
      this.detailDialogVisible = true
    }
  }
}
</script>

<style>
.table-descriptions-label {
  width: 150px;
}
.table-descriptions-content {
  width: 150px;
}
</style>
