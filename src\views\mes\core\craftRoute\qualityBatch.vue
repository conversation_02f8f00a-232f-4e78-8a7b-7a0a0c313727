<template>
  <!--电芯排废-->
  <el-card shadow="never">
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <el-form ref="form" class="el-form-wrap" :model="model" :rules="model.rules" size="small">
          <!--表格渲染-->
          <!--<el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" height="478" max-height="678" highlight-current-row @selection-change="crud.selectionChangeHandler">-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" size="small" :data="model.tableDataTable" style="width: 100%" :header-cell-style="{ background: '#eef1f6', color: '#545559' }" height="478" max-height="678" highlight-current-row @row-click="handleRowClick">
            <!-- Table单条操作-->
            <el-table-column  label="操作" align="center" fixed="left" width="70">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="toTableButDelete(scope.row)">删除</el-button>
                <!-- <el-button type="text" size="small">上移</el-button>
                <el-button type="text" size="small">下移</el-button> -->
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="数据来源" width="120px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.quality_from'" :rules="model.rules.quality_from" style="width:100%">
                  <el-select v-model="scope.row.quality_from" clearable>
                    <el-option v-for="item in [{ label: 'SCADA', id: 'SCADA' }, { label: 'WEBSERVICE', id: 'WEBSERVICE' }]" :key="item.id" :label="item.label" :value="item.id" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="采集项目" width="180px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_id'" :rules="model.rules.tag_id" style="width:100%">
                  <el-input v-model.number="scope.row.tag_id" readonly="readonly" width="120px">
                    <el-button slot="append" @click="openSelectTag">选择</el-button>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="合格标志" width="180px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_quality_sign_id'" :rules="model.rules.tag_quality_sign_id" style="width:100%">
                  <el-input v-model.number="scope.row.tag_quality_sign_id" readonly="readonly" width="120px">
                    <el-button slot="append" @click="openSelectTag2">选择</el-button>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="组号" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.group_order'" :rules="model.rules.group_order" style="width:100%">
                  <el-input v-model.number="scope.row.group_order" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="组描述" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.group_name'" style="width:100%">
                  <el-input v-model="scope.row.group_name" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="列位置" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_col_order'" :rules="model.rules.tag_col_order" style="width:100%">
                  <el-input v-model.number="scope.row.tag_col_order" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="列位置内排序" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_col_inner_order'" :rules="model.rules.tag_col_inner_order" style="width:100%">
                  <el-input v-model.number="scope.row.tag_col_inner_order" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="X坐标位置" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.location_x'" :rules="model.rules.location_x" style="width:100%">
                  <el-input v-model.number="scope.row.location_x" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="Y坐标位置" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.location_y'" :rules="model.rules.location_y" style="width:100%">
                  <el-input v-model.number="scope.row.location_y" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="套筒号" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.bolt_code'" :rules="model.rules.bolt_code" style="width:100%">
                  <el-input v-model.number="scope.row.bolt_code" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="扭矩值" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.torque'" style="width:100%">
                  <el-input v-model.number="scope.row.torque" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="程序号" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.progm_num'" :rules="model.rules.progm_num" style="width:100%">
                  <el-input v-model.number="scope.row.progm_num" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="控件类型" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.control_type'" style="width:100%">
                  <el-select v-model="scope.row.control_type" clearable>
                    <el-option v-for="item in dict.CONTROL_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="控件长度" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.control_width'" :rules="model.rules.control_width" style="width:100%">
                  <el-input v-model.number="scope.row.control_width" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="控件宽度" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.control_height'" :rules="model.rules.control_height" style="width:100%">
                  <el-input v-model.number="scope.row.control_height" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="测量对象" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.quality_for'" style="width:100%">
                  <el-input v-model="scope.row.quality_for" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="采集项目名称" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_des'" style="width:100%">
                  <el-input v-model="scope.row.tag_des" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="采集项目单位" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.tag_uom'" style="width:100%">
                  <el-input v-model="scope.row.tag_uom" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="标准值" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.theory_value'" style="width:100%">
                  <el-input v-model="scope.row.theory_value" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="下限值" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.down_limit'" style="width:100%">
                  <el-input v-model="scope.row.down_limit" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="上限值" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.upper_limit'" style="width:100%">
                  <el-input v-model="scope.row.upper_limit" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="属性1" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.attribute1'" style="width:100%">
                  <el-input v-model="scope.row.attribute1" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="属性2" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.attribute2'" style="width:100%">
                  <el-input v-model="scope.row.attribute2" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="属性3" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.attribute3'" style="width:100%">
                  <el-input v-model="scope.row.attribute3" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column  :show-overflow-tooltip="true" label="有效标识" width="100px">
              <template slot-scope="scope">
                <el-form-item :prop="'tableDataTable.' + scope.$index + '.enable_flag'" style="width:100%">
                  <el-select v-model="scope.row.enable_flag" clearable>
                    <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-col>
    </el-row>
    <el-dialog :fullscreen="false" top="10px" :modal-append-to-body="true" :append-to-body="true" title="采集项目" custom-class="step-attr-dialog" :visible.sync="customPopover1">
      <tagSelect ref="tagSelect" client-id-list="" :tag-id="currentRow.tag_id" @chooseTag="handleChooseTag1" />
    </el-dialog>
    <el-dialog :fullscreen="false" top="10px" :modal-append-to-body="true" :append-to-body="true" title="采集项目" custom-class="step-attr-dialog" :visible.sync="customPopover2">
      <tagSelect ref="tagSelect" client-id-list="" :tag-id="currentRow.tag_quality_sign_id" @chooseTag="handleChooseTag2" />
    </el-dialog>

    <div style="text-align: center;margin-top:10px">
      <!--<el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>-->
      <el-button type="primary" size="small" icon="el-icon-plus" @click="toButAdd">新增行</el-button>
      <!-- 取消 -->
      <el-button type="primary" size="small" icon="el-icon-check" :loading="saveBtnLoading" :disabled="model.tableDataTable.length === 0" @click="saveData">保存</el-button>
      <!-- 确认 -->
    </div>
  </el-card>
</template>

<script>
import { batchAdd } from '@/api/mes/core/recipeCrPdureQuality'
import Cookies from 'js-cookie'
export default {
  name: 'qualityBatch',
  props: {
    proceduce_id: {
      type: [String, Number],
      default: -1
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  // 数据模型
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value.length === 0) {
        return callback()
      }
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 200,
      saveBtnLoading: false,
      model: {
        rules: {
          quality_from: [{ required: true, message: '请选择数据来源', trigger: 'blur' }],
          tag_id: [{ required: true, message: '请选择采集项目标签', trigger: 'blur' }],
          tag_quality_sign_id: [{ required: true, message: '请选择合格标志标签', trigger: 'blur' }],
          group_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          tag_col_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          tag_col_inner_order: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          location_x: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          location_y: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          bolt_code: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          progm_num: [{ required: true, message: '请填写程序号', trigger: 'blur' }],
          control_width: [{ required: true, validator: checkNumber, trigger: 'blur' }],
          control_height: [{ required: true, validator: checkNumber, trigger: 'blur' }]
        },
        tableDataTable: []
      },
      customPopover1: false,
      customPopover2: false,
      currentRow: {
        tag_id: ''
      }
    }
  },
  // 数据字典
  dicts: ['CONTROL_TYPE', 'ENABLE_FLAG'],
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created: function() {
    this.toButAdd()
  },
  methods: {
    // 保存
    saveData() {
      this.$refs['form'].validate((valid, model) => {
        if (valid) {
          const save = {
            user_name: Cookies.get('userName'),
            qualityList: this.model.tableDataTable
          }
          this.saveBtnLoading = true
          batchAdd(save)
            .then(res => {
              this.saveBtnLoading = false
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: '保存成功！', type: 'success' })
                this.$emit('updateVisible', false)
              } else {
                this.$message({ message: '保存失败：' + defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              this.saveBtnLoading = false
              this.$message({
                message: '保存异常',
                type: 'error'
              })
            })
        }
      })
    },
    toTableButDelete(row) {
      console.info(row.seq)
      this.model.tableDataTable.splice(row.seq, 1)
      this.refreshSeq()
    },
    toButAdd() {
      this.model.tableDataTable.push({
        seq: this.model.tableDataTable.length,
        quality_id: '',
        proceduce_id: this.proceduce_id,
        quality_from: '',
        tag_id: '',
        tag_quality_sign_id: '',
        group_order: '',
        group_name: '',
        tag_col_order: '',
        tag_col_inner_order: '',
        location_x: '',
        location_y: '',
        bolt_code: '',
        torque: '',
        progm_num: '',
        control_type: '',
        control_width: '',
        control_height: '',
        quality_for: '',
        tag_des: '',
        tag_uom: '',
        theory_value: '',
        down_limit: '',
        upper_limit: '',
        enable_flag: 'Y',
        attribute1: '',
        attribute2: '',
        attribute3: ''
      })
    },
    refreshSeq() {
      for (let i = 0; i < this.model.tableDataTable.length; i++) {
        this.model.tableDataTable[i].seq = i
      }
    },
    handleRowClick(row, column, event) {
      this.currentRow = row
    },
    handleChooseTag1(tagId) {
      this.currentRow.tag_id = tagId
      this.customPopover1 = false
    },
    openSelectTag() {
      this.customPopover1 = true
    },
    openSelectTag2() {
      this.customPopover2 = true
    },
    handleChooseTag2(tagId) {
      this.currentRow.tag_quality_sign_id = tagId
      this.customPopover2 = false
    }
  }
}
</script>
