import request from '@/utils/request'

// 查询任务基础表
export function sel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsIntefTaskSelect',
    method: 'post',
    data
  })
}
// 新增任务基础表
export function add(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsIntefTaskInsert',
    method: 'post',
    data
  })
}
// 修改任务基础表
export function edit(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsIntefTaskUpdate',
    method: 'post',
    data
  })
}
// 删除任务基础表
export function del(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsIntefTaskDelete',
    method: 'post',
    data
  })
}

// 任务状态更新
export function taskStatusUpd(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsIntefTaskTaskStatusUpd',
    method: 'post',
    data
  })
}

// 查询库位
export function stockSel(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsStockSel',
    method: 'post',
    data
  })
}
export default { sel, add, edit, del, taskStatusUpd, stockSel }

