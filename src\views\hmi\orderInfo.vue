<template>
  <div class="orderinfo-container">
    <!-- <el-card shadow="always" class="wrapCard CardOne">
      <div class="wrapElForm">
        <div class="wrapElFormFirst col-md-2 col-12">
          <div class="formChild col-md-2 col-12">logo</div>
        </div>
        <div class="wrapElFormSecond formChild col-md-10 col-12">
          <div class="formChild col-md-2 col-12">产线：</div>
          <div class="formChild col-md-2 col-12">工位号：</div>
          <div class="formChild col-md-2 col-12">工位描述：</div>
          <div class="formChild col-md-2 col-12">员工姓名：</div>
          <div class="formChild col-md-2 col-12">在线时间：</div>
        </div>
      </div>
    </el-card> -->
    <el-card>
      <div>
        <h4>模组配方表格</h4>
        <el-table border @header-dragend="crud.tableHeaderDragend()" :data="recipeTableData" style="width: 100%" :highlight-current-row="true">
          <el-table-column  label="模组编号" prop="mz_num" />
          <el-table-column  label="模组名称" prop="mz_name" />
          <el-table-column  label="模组电芯单双列" prop="dx_column" />
          <el-table-column  label="模组电芯层数" prop="dx_layer_num" />
          <el-table-column  label="模组电芯数量" prop="dx_count" />
          <el-table-column  label="模组P数" prop="mz_p_count" />
          <el-table-column  label="模组S数" prop="mz_s_count" />
          <el-table-column  label="模组类型" prop="mz_type" />
          <el-table-column  label="高档位电芯电压下限v" prop="dx_pressure_high_down_limit" />
          <el-table-column  label="高档位电芯电压上限v" prop="dx_pressure_high_upper_limit" />
          <el-table-column  label="低档位电芯电压下限v" prop="dx_pressure_low_down_limit" />
          <el-table-column  label="低档位电芯电压上限v" prop="dx_pressure_low_upper_limit" />
          <el-table-column  label="电芯内阻下限m" prop="dx_neizu_down_limit" />
          <el-table-column  label="电芯内阻上限m" prop="dx_neizu_upper_limit" />
          <el-table-column  label="电芯K值下限" prop="dx_kvalue_down_limit" />
          <el-table-column  label="电芯K值上限" prop="dx_kvalue_upper_limit" />
          <el-table-column  label="电芯电容下限" prop="dx_container_down_limit" />
          <el-table-column  label="电芯电容上限" prop="dx_container_upper_limit" />
          <el-table-column  label="模组双电芯电容下限" prop="mz_dd_container_down_limit" />
          <el-table-column  label="模组双电芯电容上限" prop="mz_dd_container_upper_limit" />
        </el-table>
      </div>
    </el-card>
    <el-card class="wrapCard CardTwo">
      <el-row :gutter="20" class="elRowStyle">
        <el-col :span="8">
          <el-card class="box-card1">
            <div class="orderButton">
              <el-button size="large" type="primary" @click="getOrder">选择订单</el-button>
              <el-button size="large" type="primary" @click="orderDetail = true">查看订单明细</el-button>
            </div>
            <div class="wrapDes">
              <el-descriptions :column="2" title="当前订单信息">
                <el-descriptions-item label="订单号">{{ currentOrderInfo[0].make_order }}</el-descriptions-item>
                <el-descriptions-item label="计划数量">{{ currentOrderInfo[0].mo_plan_count }}</el-descriptions-item>
                <el-descriptions-item label="机型">{{ currentOrderInfo[0].small_model_type }}</el-descriptions-item>
                <el-descriptions-item label="完成数量">{{ currentOrderInfo[0].mo_finish_count }}</el-descriptions-item>
                <el-descriptions-item label="模组配方">{{ currentOrderInfo[0].recipe }}</el-descriptions-item>
                <el-descriptions-item label="电芯排废率">{{ currentOrderInfo[0].mo_scrap_rate }}</el-descriptions-item>
                <el-descriptions-item label="开始时间">{{ currentOrderInfo[0].plan_start_time }}</el-descriptions-item>
                <el-descriptions-item label="结束时间">{{ currentOrderInfo[0].plan_start_time }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div>
              <h4>模组电芯配方表格</h4>
              <el-table border @header-dragend="crud.tableHeaderDragend()" height="100" :data="batteriesTableData" style="width: 100%" :highlight-current-row="true">
                <el-table-column  label="电芯序号" prop="dx_num" />
                <el-table-column  label="线体选择" prop="line_num" />
                <el-table-column  label="电芯方向" prop="dx_direct" />
                <el-table-column  label="电芯档位" prop="dx_gear" />
              </el-table>
            </div>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card class="box-card1">
            <div class="stepStyle">当前作业步骤：xxxxxxxxxxxxxxxxxxx</div>
            <div class="scanStyle">
              <span>料框码：</span>
              <el-input v-model="scanone" size="large" placeholder="请输入内容" :disabled="true" />
              <el-button size="large" type="primary">扫描</el-button>
            </div>
            <div class="scanStyle">
              <span>电芯码：</span>
              <el-input v-model="scantwo" size="large" placeholder="请输入内容" />
              <el-button size="large" type="primary">扫描</el-button>
            </div>
            <el-table border @header-dragend="crud.tableHeaderDragend()" :data="orderTableData" style="width: 100%" :highlight-current-row="true">
              <el-table-column  label="日期" prop="date" />
              <el-table-column  label="姓名" prop="name" />
              <el-table-column  label="地址" prop="address" />
              <!-- Table单条操作-->
              <el-table-column  label="操作" align="right" width="460px" fixed="right">
                <template>
                  <el-link type="primary" @click="logVisible = true">查看日志</el-link>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <!-- <el-card class="box-card1" /> -->
        </el-col>
      </el-row>
    </el-card>
    <el-drawer title="选择订单" :visible.sync="chooseOrder" direction="rtl" size="80%">
      <el-radio-group v-model="orderRadio">
        <el-radio v-for="(item,index) in radioArr" :key="index" :class="{flagActive:item.mo_flag === 0?false:true}" :label="item.make_order" @change="radioChange()">
          <span>订单来源：{{ item.mo_from }}</span>
          <span>订单号：{{ item.make_order }}</span>
          <span>订单批号：{{ item.product_batch }}</span>
          <span>产品型号：{{ item.small_model_type }}</span>
          <span>订单计划数量：{{ item.mo_plan_count }}</span>
          <span>实际完成数量：{{ item.mo_finish_count }}</span>
          <span>计划开始时间：{{ item.plan_start_time | dateFormat }}</span>
          <span>计划结束时间：{{ item.plan_end_time | dateFormat }}</span>
          <span>订单排序：{{ item.mo_order_by }}</span>
          <span>订单启动时间：{{ item.start_date | dateFormat }}</span>
          <span>订单完成时间：{{ item.finish_date | dateFormat }}</span>
          <span>订单来源客户描述：{{ item.mo_custom_des }}</span>
        </el-radio>
      </el-radio-group>
    </el-drawer>
    <el-drawer title="订单明细" :visible.sync="orderDetail" direction="rtl" size="50%">
      <!-- <el-table border @header-dragend="crud.tableHeaderDragend()" :data="recipeTableData" style="width: 100%" :highlight-current-row="true">
        <el-table-column  label="日期" prop="date" />
        <el-table-column  label="姓名" prop="name" />
      </el-table> -->
    </el-drawer>

    <!-- <div class="line" />
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      background-color="#79a0f1"
      text-color="#fff"
      active-text-color="#000000"
    >
      <el-menu-item index="1">处理中心</el-menu-item>
      <el-menu-item index="2">消息中心</el-menu-item>
      <el-menu-item index="3">消息中心</el-menu-item>
      <el-menu-item index="4">消息中心</el-menu-item>
      <el-menu-item index="5">消息中心</el-menu-item>
      <el-menu-item index="6">消息中心</el-menu-item>
      <el-menu-item index="7">消息中心</el-menu-item>
      <el-menu-item index="8">消息中心</el-menu-item>
    </el-menu> -->
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { chooseOrderInfo, getOrderInfo, showOrderInfo } from '@/api/hmi/orderinfo.js'
export default {
  name: 'orderInfo',
  data() {
    return {
      scanone: '',
      scantwo: '',
      chooseOrder: false,
      orderDetail: false,
      activeIndex: '1',
      orderRadio: '',
      radioArr: [
        // {
        //   name: '精确试算',
        //   id: '1'
        // },
        // {
        //   name: '模糊试算',
        //   id: '2'
        // }
      ],
      currentOrderInfo: [{
        make_order: '------',
        mo_plan_count: '------',
        small_model_type: '------',
        mo_finish_count: '------',
        recipe: '------',
        mo_scrap_rate: '------',
        plan_start_time: '------'
      }],
      recipeTableData: [],
      batteriesTableData: [],
      orderTableData: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }]
    }
  },
  created() {

  },
  methods: {
    getOrder() {
      this.chooseOrder = true
      var query = {
        userName: Cookies.get('userName'),
        prod_line_id: 4,
        station_id: 1
      }
      chooseOrderInfo(query).then(res => {
        console.log(res)
        if (res.code === 0) {
          this.radioArr = res.data
        }
      })
        .catch(() => {
          this.$message({
            message: '选择订单异常',
            type: 'error'
          })
        })
    },
    radioChange() {
      this.chooseOrder = false
      var getQuery = {
        userName: Cookies.get('userName'),
        station_id: 2,
        make_order: 22
      }
      getOrderInfo(getQuery).then(() => {
        var showQuery = {
          userName: Cookies.get('userName'),
          station_id: 2
        }
        showOrderInfo(showQuery).then(res => {
          const result = JSON.parse(res.result)
          this.recipeTableData = result.mz_list
          this.currentOrderInfo = result.mo_list
          this.batteriesTableData = result.mzd_list
          console.log(result)
          // if (res.code === 0) {
          //   this.radioArr = res.data
          // }
        })
          .catch(() => {
            this.$message({
              message: '展示信息查询异常',
              type: 'error'
            })
          })
      })
        .catch(() => {
          this.$message({
            message: '获取信息查询异常',
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.box-card1 {
  height: calc(100vh - 80px);
}
.orderButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.wrapDes {
  margin-top: 30px;
}
.stepStyle {
  padding: 10px 0;
  padding-bottom: 30px;
}
.scanStyle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  span {
    white-space: nowrap;
  }
  button {
    margin-left: 10px;
  }
}
.el-menu {
  display: flex;
  justify-content: space-between;
  overflow: auto;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
  background-color: #ffffff !important;
}
::v-deep .el-menu--horizontal > .el-menu-item {
  flex: 1;
  text-align: center;
  margin: 0 5px;
}
.el-radio-group,
.el-radio {
  width: 100%;
  margin-right: 0;
}
.el-radio-group {
  height: calc(100vh - 80px);
  overflow: auto;
}
.el-radio {
  margin: 10px 0;
  padding: 10px 0;
  display: flex;
    align-items: center;
    border-bottom: 1px dotted #acacac;
}
::v-deep .el-radio__label {
  font-size: 14px !important;
  display: flex;
    flex-wrap: wrap;
    span{
      margin: 5px 10px;
    }
}
.flagActive{
  background-color: #e8efff;
}
</style>
