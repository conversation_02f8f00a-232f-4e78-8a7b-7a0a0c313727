<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="设备编号：">
                <!-- 设备编号： -->
                <el-input v-model="query.equip_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="设备名称：">
                <!-- 设备名称： -->
                <el-input v-model="query.equip_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="设备型号：">
                <!-- 设备型号： -->
                <el-input v-model="query.equip_model" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="设备状态：">
                <!-- 设备状态： -->
                <fastCode fastcode_group_code="DEVICE_STATUS" :fastcode_code.sync="query.equip_status" control_type="select" size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="所属工位：">
                <!-- 所属工位： -->
                <el-select v-model="query.station_code">
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code">
                    <span style="float: left">{{ item.station_code }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <crudOperation show="" :permission="permission" />
      <el-drawer
        append-to-body
        :wrapper-closable="false"
        :title="crud.status.title"
        :visible="crud.status.cu > 0"
        :before-close="crud.cancelCU"
        size="750px"
      >
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="145px" :inline="true">
          <el-form-item label="设备编号" prop="equip_code">
            <!-- 设备编号 -->
            <el-input v-model="form.equip_code" />
          </el-form-item>
          <el-form-item label="设备名称" prop="equip_des">
            <!-- 设备名称 -->
            <el-input v-model="form.equip_des" />
          </el-form-item>
          <el-form-item label="设备厂商" prop="equip_brand">
            <!-- 设备厂商 -->
            <el-input v-model="form.equip_brand" />
          </el-form-item>
          <el-form-item label="设备型号" prop="equip_model">
            <!-- 设备型号 -->
            <el-input v-model="form.equip_model" />
          </el-form-item>
          <el-form-item label="设备状态" prop="equip_status">
            <!-- 设备状态 -->
            <fastCode fastcode_group_code="DEVICE_STATUS" :fastcode_code.sync="form.equip_status" control_type="select" size="small" />
          </el-form-item>
          <el-form-item label="所属工位" prop="station_code">
            <!-- 所属工位 -->
            <el-select v-model="form.station_code">
              <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code" :value="item.station_code">
                <span style="float: left">{{ item.station_code }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.station_des }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备图片" prop="equip_image_url" class="equipImg">
            <!-- 新增图片 -->
            <el-upload
              ref="updload_avatar"
              action=""
              list-type="picture-card"
              class="avatar-uploader"
              :auto-upload="false"
              :limit="1"
              :on-change="getFile"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
            <img v-if="form.equip_image_url" ref="img" class="avatar-img" :src="form.equip_image_url">
          </el-form-item>
          <el-form-item :label="$t('lang_pack.mainmain.triggerPoint')" prop="station_communication_status">
            <!-- 设备通讯状态 -->
            <el-input v-model="form.station_communication_status" readonly="readonly">
              <div slot="append">
                <el-popover v-model="customPopover" placement="left" width="650">
                  <tagSelect ref="tagSelect" :client-id-list="clientIdList" :tag-id="Number(form.station_communication_status)" @handleChooseTag="handleChooseTag(arguments)" />
                  <el-button slot="reference">选择</el-button>
                </el-popover>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
            $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            :loading="crud.status.cu === 2"
            @click="crud.submitCU"
          >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            highlight-current-row
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              prop=""
              :label="$t('lang_pack.mfTable.length')"
              align="center"
              width="180"
            >
              <template slot-scope="scope">
                <img :src="scope.row.equip_image_url" style="width: 120px;height: 60px;">
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop="m_width"
              :label="$t('lang_pack.mfTable.width')"
              align="center"
            >
              <template slot-scope="scope">
                <div class="deviceInfo">
                  <p>设备编号：<span>{{ scope.row.equip_code }}</span></p>
                  <p>设备名称：<span>{{ scope.row.equip_des }}</span></p>
                  <p>设备型号：<span>{{ scope.row.equip_model }}</span></p>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop=""
              :label="$t('lang_pack.mfTable.thick')"
              align="center"
            >
              <template slot-scope="scope">
                <div class="deviceInfo">
                  <p>&emsp;&emsp;设备厂商：<span>{{ scope.row.equip_brand }}</span></p>
                  <p>设备所属工位：<span>{{ scope.row.station_code }}</span></p>
                  <p>设备运行时间：<span>{{ scope.row.equip_start_date }}</span></p>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop=""
              :label="$t('lang_pack.mfTable.materialQuality')"
              align="center"
            >
              <template slot-scope="scope">
                <div class="device_status">
                  <span class="status">设备状态：</span>
                  <span :class="scope.row.equip_status == 'N' ? 'status_Red' : 'status_green'">{{ dict.label.DEVICE_STATUS[scope.row.equip_status] }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :show-overflow-tooltip="true"
              prop=""
              :label="$t('lang_pack.mfTable.mainMaterialCode')"
              align="center"
            >
              <template slot-scope="scope">
                <div class="device_status">
                  <span class="status">设备通讯状态：</span>
                  <span :class="(scope.row.tag_value === '0' || scope.row.tag_value === '1') ? 'status_green' : 'status_Red'">{{ (scope.row.tag_value === '0' || scope.row.tag_value === '1') ? '成功' :'失败' }}</span>
                </div>
              </template></el-table-column>
            <!-- Table单条操作-->
            <el-table-column prop="button" :label="$t('lang_pack.commonPage.operate')" width="120" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <!-- <pagination /> -->
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { sel } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import crudLedger from '@/api/dcs/core/devideLedger/ledger'
import axios from 'axios'
import Cookies from 'js-cookie'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  equip_code: '',
  equip_des: '',
  equip_brand: '',
  equip_model: '',
  equip_no: '',
  equip_leave_date: '',
  equip_start_date: '',
  equip_type: '',
  equip_image_url: '',
  equip_arrive_date: '',
  station_code: '',
  station_status: '',
  station_communication_status: '',
  attribute1: ''
}
export default {
  name: 'WEB_LEDGER',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '设备台账',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'equip_id',
      // 排序
      sort: ['equip_id asc'],
      // CRUD Method
      crudMethod: { ...crudLedger },
      // 按钮显示
      optShow: {
        add: true,
        edit: false,
        del: true,
        reset: true
      },
      query: {
        size: 100
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 280,
      permission: {
        add: ['admin', 'ledger:add'],
        edit: ['admin', 'ledger:edit'],
        del: ['admin', 'ledger:del']
      },
      rules: {
        equip_code: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        equip_des: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        equip_model: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        equip_status: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        station_code: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        equip_image_url: [{ required: true, message: '该项为必填项', trigger: 'blur' }],
        attribute1: [{ required: true, message: '该项为必填项', trigger: 'blur' }]
      },
      stationData: [],
      uploadAccept: '.jpeg,jpg,.png',
      imageUrl: '',
      customPopover: false,
      clientIdList: '',
      tagId: '',
      cellIp: '',
      webapiPort: '',
      timer: null,
      optionsMqtt8083: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8) + '8083', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt8084: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8) + '8084', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      optionsMqtt8085: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8) + '8085', // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      mqttConnStatus8083: false, // MQTT连接状态(true、false)
      mqttConnStatus8084: false, // MQTT连接状态(true、false)
      mqttConnStatus8085: false, // MQTT连接状态(true、false)
      clientMqtt8083: null,
      clientMqtt8084: null,
      clientMqtt8085: null,
      statrWatchFlag: true,
      callMqttFlag: false
    }
  },
  mounted() {
    const query = {
      enable_flag: 'Y',
      userName: Cookies.get('userName')
    }
    sel(query).then(res => {
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        this.stationData = defaultQuery.data || []
      }
    }).catch(() => {
      this.stationData = []
      this.$message({
        message: '工位查询异常',
        type: 'error'
      })
    })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 280
    }
  },
  created() {
    const ArrCellId = ['1', '2', '3']
    // const ArrCellId = ['1']
    ArrCellId.forEach((e, index) => {
      this.getTagValue(e, true)
    })
    setTimeout(() => {
      this.callMqttFlag = true
    }, 1000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  dicts: ['DEVICE_STATUS', 'COMMUNICATION_STATUS'],
  methods: {
    getFile(file, fileList) {
      // 清除上一次选择的文件 避免后面不在触发此事件
      this.$refs['updload_avatar'].clearFiles()

      // 文件格式及大小校验、获取base64
      this.verifyFile(file.raw).then(resp => {
        return this.getBase64(file.raw)
      }).then(resp => {
        // this.userInfo.accountImg = resp
        // vue 数据监测不到对象深处的数据更改，所以需要使用$set通知一下vue
        // 不这样写会导致页面中的图片展示不生效
        this.$set(this.form, 'equip_image_url', resp)
      }).catch(err => {
        this.$message(err)
      })
    },
    // 文件校验
    verifyFile(file) {
      return new Promise((resolve, reject) => {
        const isLt2M = file.size / 1024 / 1024 < 2
        if (!isLt2M) {
          reject('上传头像图片大小不能超过 2MB!')
        }
        resolve(isLt2M)
      })
    },
    // 用于获取base64字符
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function() {
          imgResult = reader.result
        }
        reader.onerror = function(error) {
          reject(error)
        }
        reader.onloadend = function() {
          resolve(imgResult)
        }
      })
    },
    handleChooseTag(data) {
      this.form.attribute1 = data[0]
      this.form.attribute2 = data[1]
      this.form.attribute3 = data[2]
      this.form.station_communication_status = data[3]
      this.customPopover = false
    },
    // 刷新之后
    [CRUD.HOOK.afterRefresh](crud) {
      // 获取mqtt的cell_id,徐工的有三个cellid 分别是1 2 3，我是默认一次获取全部的  本地调试的时候需要把 2 3关掉，没有2 3的服务器
      if (this.callMqttFlag) {
        const ArrCellId = ['1', '2', '3']
        ArrCellId.forEach(e => {
          this.getTagValue(e, false)
        })
      }
      // if (crud.data.length > 0) {
      // attribute2是 每个redis里面的cell_id
      // const ArrCellId = new Set(crud.data.map(item => item.attribute2))

      // const ArrCellId = crud.data.reduce((acc, current) => {
      //   // 使用findIndex检查当前元素是否已经存在于结果数组中
      //   if (acc.findIndex(item => item.attribute2 === current.attribute2 && item.attribute3 === current.attribute3) === -1) {
      //     acc.push(current) // 如果不存在，则将当前元素添加到结果数组中
      //   }
      //   return acc
      // }, [])

      // }
    },
    // async handleData(ArrCellId, flag) {
    //   for (const e of ArrCellId) {
    //     try {
    //       await this.fetchData(e, flag)
    //     } catch (error) {
    //       console.error(error)
    //     }
    //   }
    // },
    // fetchData(e, flag) {
    //   return new Promise((resolve, reject) => {
    //     this.getTagValue(e, flag)
    //       .then(data => {
    //         console.log(data)
    //         resolve(data)
    //       })
    //       .catch(error => {
    //         console.error(error)
    //         reject(error)
    //       })
    //   })
    // },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      // 查不到啥原因,图片默认赋值为'\x'
      crud.form.equip_image_url = crud.form.equip_image_url.length > 10 ? crud.form.equip_image_url : ''
      return true
    },
    // 提交之后
    [CRUD.HOOK.afterSubmit](crud) {
      for (var i = 0; i < this.crud.data.length; i++) {
        const ipInfo = { 'mqtt_port': this.crud.data[i].attribute3 }
        var clientCode = this.crud.data[i].attribute1.split('/')[0]
        // 订阅主题
        var topic_clientStatus = 'SCADA_STATUS/' + clientCode
        this.topicSubscribe(topic_clientStatus, ipInfo)
        this.topicSubscribe('SCADA_CHANGE/' + this.crud.data[i].attribute1, ipInfo)
      }
      // setTimeout(() => {
      //   this.getTagValue(crud.attribute2, false)
      // }, 800)
      return true
    },
    getTagValue(cell_id, flag) {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            // 这里用个延时器，不然拿不到this.crud.data的值
            setTimeout(() => {
              flag && this.toStartWatch(ipInfo)
              this.getTagKeyValue(ipInfo, cell_id)
            }, 800)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getTagKeyValue(ipInfo, cell_id) {
      if (!this.crud.data.length) return
      var readTagArray = []
      this.crud.data.forEach(item => {
        if (item.attribute1 && item.attribute1 !== '') {
          if (cell_id === item.attribute2) {
            var readTag = {}
            readTag.tag_key = item.attribute1
            readTagArray.push(readTag)
          }
        }
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + ipInfo.webapi_port + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + ipInfo.ip + ':' + ipInfo.webapi_port + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              this.crud.data.forEach(item => {
                result.forEach(e => {
                  if (item.attribute1 === e.tag_key) {
                    this.$set(item, 'tag_value', e.tag_value)
                  }
                })
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('view.dialog.queryException') + '：' + ex, type: 'error' })
        })
    },
    toStartWatch(ipInfo) {
      // var connectUrl = 'ws://*************:8083' + '/mqtt'
      var connectUrl = 'ws://' + ipInfo.ip + ':' + ipInfo.mqtt_port + '/mqtt'
      this[`clientMqtt${ipInfo.mqtt_port}`] = mqtt.connect(connectUrl, this[`optionsMqtt${ipInfo.mqtt_port}`]) // 开启连接
      this[`clientMqtt${ipInfo.mqtt_port}`].on('connect', e => {
        this[`mqttConnStatus${ipInfo.mqtt_port}`] = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        for (var i = 0; i < this.crud.data.length; i++) {
          if (this.crud.data[i].attribute3 == ipInfo.mqtt_port) {
            var clientCode = this.crud.data[i].attribute1.split('/')[0]
            // 订阅主题
            var topic_clientStatus = 'SCADA_STATUS/' + clientCode
            this.topicSubscribe(topic_clientStatus, ipInfo)
            this.topicSubscribe('SCADA_CHANGE/' + this.crud.data[i].attribute1, ipInfo)
          }
        }
        this.$message({
          message: `端口${ipInfo.mqtt_port}` + this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })

      // MQTT连接失败
      this[`clientMqtt${ipInfo.mqtt_port}`].on('error', () => {
        this.$message({
          message: `端口${ipInfo.mqtt_port}` + this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this[`clientMqtt${ipInfo.mqtt_port}`].end()
      })
      // 断开发起重连(异常)
      this[`clientMqtt${ipInfo.mqtt_port}`].on('reconnect', () => {
        this.$message({
          message: `端口${ipInfo.mqtt_port}` + this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this[`clientMqtt${ipInfo.mqtt_port}`].on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this[`clientMqtt${ipInfo.mqtt_port}`].on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this[`clientMqtt${ipInfo.mqtt_port}`].on('message', (topic, message) => {
        // console.log('MQTT收到来自', topic, '的消息', message.toString())
        // const res = JSON.parse(message.toString())
        // 解析传过来的数据
        this.mqttUpdateTable(topic, message)
      })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      // console.log('MQTT收到来自', channel, '的消息', message.toString())

      if (channel.indexOf('SCADA_STATUS/') >= 0) {
        this.reflashClientInfo(channel, message)
      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagNewValue = jsonData.TagNewValue
      this.crud.data.forEach(item => {
        if (item.attribute1 === TagKey) {
          this.$set(item, 'tag_value', TagNewValue)
        }
      })
    },
    // 接受到CLIENT对应的消息时，进行界面更新
    reflashClientInfo(channel, message) {
      var jsonData = JSON.parse(message)
      // console.log(jsonData)
      var clientCode = jsonData.ClientCode
      if (channel.indexOf('SCADA_STATUS/') >= 0) {
        var OkTime = jsonData.OkTime
        this.crud.data.forEach(item => {
          if (item.attribute1.split('/')[0] === clientCode) {
            this.$set(item, 'equip_start_date', this.formatSeconds(OkTime))
          }
        })
      }
    },
    // 订阅主题函数
    topicSubscribe(topic, ipInfo) {
      if (!this[`mqttConnStatus${ipInfo.mqtt_port}`]) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this[`clientMqtt${ipInfo.mqtt_port}`].subscribe(topic, { qos: 0 }, error => {
        if (!this[`mqttConnStatus${ipInfo.mqtt_port}`]) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 把分转换成 xx天xx小时xx分
    formatSeconds(second) {
      var days = Math.floor(second / 1440)
      var hours = Math.floor((second % 1440) / 60)
      var remainingMinutes = second % 60
      return days + '天' + hours + '小时' + remainingMinutes + '分'
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-table thead{
    display: none;
}
.deviceInfo{
    margin: 0 auto;
    width: 60%;
    text-align: left;
    p{
        font-weight: 600;
        span{
            font-weight: normal;
        }
    }
}

.device_status{
    display: flex;
    align-items: center;
    height: 30px;
    .status{
        font-size: 16px;
    }
    span{
        margin-left: 10px;
        display: block;
        text-align: center;
    }
    .status_Red{
        background: red;
        width: 50px;
        height: 30px;
        color: #fff;
        border-radius: 3px;
        line-height: 30px;
    }
    .status_green{
        background: green;
        width: 50px;
        height: 30px;
        color: #fff;
        border-radius: 3px;
        line-height: 30px;
    }
}
.el-form-item__content{
    display: flex;
}
::v-deep .el-upload--picture-card{
    width: 50px;
    height: 50px;
    position: relative;
    .el-icon-plus{
        position: absolute;
        top: 10px;
        left: 10px;
    }
}
.equipImg{
    ::v-deep .el-form-item__content{
        display: flex;
    }
}
.avatar-img{
    margin-left: 20px;
    width: 50px;
    height: 50px;
}

</style>
