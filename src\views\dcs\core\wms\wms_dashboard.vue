<template>
  <el-container style="height: 100%">
    <el-header
      :style="'background-image:url(' +
        headerBackground +
        ');background-size:100% 100%;width:100%;height:80px'
      "
    >
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7">
          <img
            :src="headerLogo"
            style="width: 150px; height: 65px; float: left; margin-left: 180px;margin-top: 10px;"
          >
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 25px">
          <span class="title">库区智能调度可视化看板</span>
        </el-col>
        <el-col :span="7" style="text-align: center; padding-top: 10px; padding-right: 150px"><span class="time"> {{
          nowDate }}{{ time }}{{ week }} </span></el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20">
        <el-col :span="18" style="width: 77.1666666667%;">
          <MainChart
            ref="chart"
            v-loading="loading"
            :nodes="nodes"
            :connections="connections"
            :width="'1450'"
            :height="'570'"
            :readonly="false"
            element-loading-text="拼命绘制流程图中"
            @editnode="handleEditNode"
            @ok="handleCutNode"
          />
          <el-table
            ref="table"
            v-loading="listLoadingTable"
            :data="tableDataTable"
            style="width: 100%; margin-top: 5px"
            :header-cell-style="{ background: '#00479d', color: '#000' }"
            border
            :stripe="true"
            height="250px"
            :highlight-current-row="true"
          >
            <el-table-column :show-overflow-tooltip="true" prop="task_type" label="任务类型">
              <template slot-scope="scope"><!--取到当前单元格-->
                {{ scope.row.task_type === "KW_XL" ? "出料" : "上料" }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_status" label="任务状态" width="140" align="center">
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="task_num" width="170" label="任务号" />
            <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="lot_num"
                  width="100"
                  label="批次号"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="serial_num"
                  width="100"
                  label="钢板号"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="material_code"
                  width="120"
                  label="物料编码"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="material_des"
                  width="100"
                  label="物料描述"
                /> -->
            <el-table-column :show-overflow-tooltip="true" prop="model_type" width="100" label="型号" />
            <el-table-column :show-overflow-tooltip="true" prop="m_length" width="60" label="长" />
            <el-table-column :show-overflow-tooltip="true" prop="m_width" width="60" label="宽" />
            <el-table-column :show-overflow-tooltip="true" prop="m_height" width="60" label="厚" />
            <el-table-column :show-overflow-tooltip="true" prop="m_weight" width="60" label="重" />
            <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="m_uom"
                  width="60"
                  label="单位"
                /> -->
            <el-table-column :show-overflow-tooltip="true" prop="from_stock_code" width="100" label="来源库位" />
            <el-table-column :show-overflow-tooltip="true" prop="to_stock_code" width="100" label="目标库位" />

            <el-table-column label="是否检查辊道" align="center" prop="need_check_gd_flag" width="100">
              <template slot-scope="scope"><!--取到当前单元格-->
                {{ scope.row.need_check_gd_flag === "Y" ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column label="是否检查型号" align="center" prop="need_check_model_flag" width="100">
              <template slot-scope="scope"><!--取到当前单元格-->
                {{ scope.row.need_check_model_flag === "Y" ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column label="启动是否告之" align="center" prop="need_tell_start_flag" width="100">
              <template slot-scope="scope"><!--取到当前单元格-->
                {{ scope.row.need_tell_start_flag === "Y" ? "是" : "否" }}
              </template>
            </el-table-column>

            <el-table-column :show-overflow-tooltip="true" prop="tell_start_date" width="100" label="告之启动时间" />
            <el-table-column :show-overflow-tooltip="true" prop="tell_cancel_date" width="100" label="告之取消时间" />
            <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="tell_stop_date"
                  width="100"
                  label="告之暂停时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="tell_start_by"
                  width="100"
                  label="告之启动者"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="tell_cancel_by"
                  width="100"
                  label="告之取消者"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="tell_stop_by"
                  width="100"
                  label="告之暂停者"
                /> -->
            <el-table-column label="是否锁定" align="center" prop="lock_flag" width="100">
              <template slot-scope="scope"><!--取到当前单元格-->
                {{ scope.row.lock_flag === "Y" ? "锁定" : "未锁定" }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="6" style="width: 22.8333333333%;">
          <div
            style="
                border: 2px solid #2c88b3;
                box-shadow: #2c88b3 0px 0px 10px inset;
                width: 100%;
                padding: 5px;
              "
          >
            <div class="dataInfo">任务编号：{{ rcsWriteTask }}</div>
            <div class="dataInfo">钢板型号：{{ rcsWriteModelType }}</div>
            <div class="dataInfo">钢板长度：{{ rcsWriteLength }}</div>
            <div class="dataInfo">钢板宽度：{{ rcsWriteWidth }}</div>
            <div class="dataInfo">钢板厚度：{{ rcsWriteHeight }}</div>
            <div class="dataInfo">钢板重量：{{ rcsWriteWeight }}</div>
            <div class="dataInfo">大车激光：{{ x }}</div>
            <div class="dataInfo">小车激光：{{ y }}</div>
            <div class="dataInfo">起升激光：{{ h }}</div>
            <!-- <div class="dataInfo">抓取位置：{{ stockCodeFromLabel }}</div> -->
            <!-- <div class="dataInfo">放置位置：{{ stockCodeToLabel }}</div> -->
          </div>
          <div
            style="
                border: 2px solid #2c88b3;
                box-shadow: #2c88b3 0px 0px 10px inset;
                width: 100%;
                height: 510px;
                margin-top: 5px;
                padding: 5px;
              "
          >
            <div class="dataInfo">接收任务</div>
            <table style="width: 100%">
              <tr>
                <td style="width: 50%">
                  <div class="dataInfo">X：{{ recvX }}</div>
                </td>
                <td style="width: 50%">
                  <div class="dataInfo">Y：{{ recvY }}</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="dataInfo">Z：{{ recvZ }}</div>
                </td>
                <td>
                  <div class="dataInfo">R：{{ recvR }}</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="dataInfo">A：{{ recvA }}</div>
                </td>
                <td>
                  <div class="dataInfo">
                    Start：{{ recvStart }}
                    <!-- <el-button
                        class="lu-btn-3d"
                        style="line-height: 10px; margin-left: 10px"
                        @click="handleStartWrite('recv')"
                        >设置</el-button
                      > -->
                  </div>
                </td>
              </tr>
            </table>
            <div class="dataInfo">执行任务</div>
            <table style="width: 100%">
              <tr>
                <td style="width: 50%">
                  <div class="dataInfo">X：{{ execX }}</div>
                </td>
                <td style="width: 50%">
                  <div class="dataInfo">Y：{{ execY }}</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="dataInfo">Z：{{ execZ }}</div>
                </td>
                <td>
                  <div class="dataInfo">R：{{ execR }}</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="dataInfo">A：{{ execA }}</div>
                </td>
                <td>
                  <div class="dataInfo">
                    Start：{{ execStart }}
                    <!-- <el-button
                        class="lu-btn-3d"
                        style="line-height: 10px; margin-left: 10px"
                        @click="handleStartWrite('exec')"
                        >设置</el-button
                      > -->
                  </div>
                </td>
              </tr>
            </table>
            <!-- <table style="width: 100%">
                <tr>
                  <td style="width: 30px">
                    <img
                      id="img_signal_sdyx"
                      :src="monitorInit"
                      style="width: 30px"
                    />
                  </td>
                  <td>
                    <div class="dataInfo" style="margin-bottom: 5px">
                      手动运行
                    </div>
                  </td>
                  <td style="width: 30px">
                    <img
                      id="img_signal_zdyx"
                      :src="monitorInit"
                      style="width: 30px"
                    />
                  </td>
                  <td>
                    <div class="dataInfo" style="margin-bottom: 5px">
                      自动运行
                    </div>
                  </td>
                </tr>
                <tr>
                  <td style="width: 30px">
                    <img
                      id="img_signal_gzbj"
                      :src="monitorInit"
                      style="width: 30px"
                    />
                  </td>
                  <td>
                    <div class="dataInfo" style="margin-bottom: 5px">
                      故障报警
                    </div>
                  </td>
                  <td style="width: 30px">
                    <img
                      id="img_signal_xcdyzs"
                      :src="monitorInit"
                      style="width: 30px"
                    />
                  </td>
                  <td>
                    <div class="dataInfo" style="margin-bottom: 5px">
                      行车电源指示
                    </div>
                  </td>
                </tr>
              </table> -->
            <div class="btn-crown-block">
              <span
                class="lu-btn-3d "
                @mouseup="handleTagWrite('SlCarPlc01/DcsStatus/RcsTag001', '0')"
                @mousedown="handleTagWrite('SlCarPlc01/DcsStatus/RcsTag001', '1')"
              >重新启用自动</span>
              <span
                style="margin-left:15px"
                class="lu-btn-3d"
                @mouseup="handleTagWrite('SlCarPlc01/DcsStatus/FaultReset', '0')"
                @mousedown="handleTagWrite('SlCarPlc01/DcsStatus/FaultReset', '1')"
              >故障复位</span>
              <span
                class="lu-btn-3d"
                @mouseup="handleTagWrite('SlCarPlc01/DcsStatus/DeviceSelfTest', '0')"
                @mousedown="handleTagWrite('SlCarPlc01/DcsStatus/DeviceSelfTest', '1')"
              >设备自检</span>
              <span
                style="margin-left:15px"
                class="lu-btn-3d"
                @mouseup="handleTagWrite('SlCarPlc01/DcsStatus/RcsTag002', '0')"
                @mousedown="handleTagWrite('SlCarPlc01/DcsStatus/RcsTag002', '1')"
              >大车激光值确认</span>
              <span
                class="lu-btn-3d"
                @mouseup="handleTagWrite('SlCarPlc01/DcsStatus/RcsTag003', '0')"
                @mousedown="handleTagWrite('SlCarPlc01/DcsStatus/RcsTag003', '1')"
              >小车激光值确认</span>
              <span style="margin-left:15px" class="lu-btn-3d" @click="reversePark">库位钢板倒库</span>
              <span class="lu-btn-3d" @click="taskCannel('SlAisSimPlc/WmsStatus/CarCreate01','1')">行车任务取消</span>
              <span
                style="margin-left:15px"
                class="lu-btn-3d"
                @mouseup="handleTagWrite('SlCarPlc01/DcsStatus/RcsWritePwStart', '')"
                @mousedown="handleTagWrite2('SlBlastPlc/DcsStatus/RcsWritePwStart', '0')"
              >清除抛丸</span>
            </div>
          </div>
          <el-dialog
            append-to-body
            :close-on-click-modal="false"
            :title="wirteTagTitle"
            :visible.sync="writeDialogVisbleSyncFrom"
            width="520px"
          >
            <el-form ref="formWrite" :model="formWrite" size="small" label-width="60px">
              <el-form-item v-if="1 == 0" label="id" prop="write_client_code" display:none>
                <el-input v-model="formWrite.write_client_code" />id
              </el-form-item>
              <el-form-item v-if="1 == 0" label="id" prop="write_tagOnlyKey" display:none>
                <el-input v-model="formWrite.write_tagOnlyKey" />id
              </el-form-item>
              <el-form-item label="标签值" prop="write_tag_value" display:none>
                <el-input ref="writeTagValue" v-model="formWrite.write_tag_value" clearable />
              </el-form-item>
            </el-form>
            <div style="text-align: center; margin-bottom: 10px">
              <el-button size="small" icon="el-icon-close" plain @click="toWriteFromCancel">取消</el-button>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                @click="toWrigeFormFromSubmit('formWrite')"
              >确认</el-button>
            </div>
          </el-dialog>
        </el-col>
      </el-row>
    </el-main>
    <reverseModal v-if="reverseModal" ref="reverseModalFlag" @ok="reverseModal = false, getStockSel()" />
  </el-container>
</template>
<script>
import { sel as selTask } from '@/api/dcs/core/wms/carTask'
import { getIconPathData } from '@/components/wmsDashboard/icon'
import crudLoadAreaOper from '@/api/dcs/core/hmi/loadAreaOper'
import headerBackground from '@/assets/images/dcs/header1.png'
import headerLogo from '@/assets/images/dcs/xugongLogo.png'
import monitorInit from '@/assets/images/dcs/Init.png'
import MainChart from '@/components/wmsDashboard/index'
import crudMainPage from '@/api/dcs/core/hmi/mainPage'
import reverseModal from '../../modules/reverseModal'// 倒库
import axios from 'axios'
import Cookies from 'js-cookie'
import * as d3 from 'd3'
import { roundTo20 } from '@/utils/math'
import { selCellIP } from '@/api/core/center/cell'
import mqtt from 'mqtt'
import { MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
export default {
  components: {
    MainChart, reverseModal
  },
  props: {
    task_num: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      reverseModal: false,
      imgData: getIconPathData(),
      headerBackground: headerBackground,
      headerLogo: headerLogo,
      monitorInit: monitorInit,
      diameter: 15,
      nodes: [],
      connections: [],
      thisH: null,
      loading: false,
      nodeForm: {},
      nowDate: '',
      time: '',
      week: '',
      tableDataTable: [],
      listLoadingTable: false,
      timer: null,
      timer1: null,
      timer2: null,
      timer3: null,
      timer4: null,
      timerLoad: null,
      px: 0.2325, // 1mm=0.2325px
      carVrituWorkRegionWidth: 830, // 天车网页活动范围X长度:1230是实际活动范围,减去距离400(天车开始的位置)
      carVrituWorkRegionHeight: 495, // 天车网页活动范围Y长度
      carAuctalWorkRegionWidth: 52800, // 天车实际活动范围X长度:4500~57300
      carAuctalWorkRegionHeight: 23000, // 小车实际活动范围Y长度:1150~24150
      x: '',
      y: '',
      h: '',
      tagKeyList: [
        'SlCarPlc01/PlcStatus/X_real-time', // 大车激光X
        'SlCarPlc01/PlcStatus/Y_real-time', // 小车激光Y
        'SlCarPlc01/PlcStatus/Z_real-time', // 起升激光
        'SlCarPlc01/PlcStatus/AcceptTaskX', // 接收任务X
        'SlCarPlc01/PlcStatus/AcceptTaskY', // 接收任务Y
        'SlCarPlc01/PlcStatus/AcceptTaskZ', // 接收任务Z
        'SlCarPlc01/PlcStatus/AcceptTaskR', // 接收任务R
        'SlCarPlc01/PlcStatus/AcceptTaskA', // 接收任务A
        'SlCarPlc01/PlcStatus/AcceptTaskStart', // 接收任务Start
        'SlCarPlc01/PlcStatus/PlcTimeX', // 执行任务X
        'SlCarPlc01/PlcStatus/PlcTimeY', // 执行任务Y
        'SlCarPlc01/PlcStatus/PlcTimeZ', // 执行任务Z
        'SlCarPlc01/PlcStatus/PlcTimeR', // 执行任务R
        'SlCarPlc01/PlcStatus/PlcTimeA', // 执行任务A
        'SlCarPlc01/PlcStatus/PlcReadStartTask', // 执行任务Start
        'SlCarPlc01/DcsStatus/RcsWriteModelType', // 钢板型号
        'SlCarPlc01/DcsStatus/RcsWriteLength', // RCS写入钢板长
        'SlCarPlc01/DcsStatus/RcsWriteWidth', // RCS写入钢板宽
        'SlCarPlc01/DcsStatus/RcsWriteHeight', // RCS写入钢板厚
        'SlCarPlc01/DcsStatus/RcsWriteWeight', // RCS写入钢板重
        'SlCarPlc01/DcsStatus/RcsWriteTask', // RCS写入任务号
        'SlCarPlc01/DcsStatus/RcsTag001', // 重新启用自动
        'SlCarPlc01/DcsStatus/FaultReset', // 故障复位
        'SlCarPlc01/DcsStatus/DeviceSelfTest', // 设备自检
        'SlCarPlc01/DcsStatus/RcsTag002', // 大车激光值确认
        'SlCarPlc01/DcsStatus/RcsTag003' // 小车激光值确认
        // "CarPlc01/RcsGrroupRead/Tag164", //手动运行
        // "CarPlc01/RcsGrroupRead/Tag163", //自动运行
        // "CarPlc01/RcsGrroupRead/Tag165", //故障报警
        // "CarPlc01/RcsGrroupRead/Tag07", //行车电源指示"
      ],
      recvX: '',
      recvY: '',
      recvZ: '',
      recvR: '',
      recvA: '',
      recvStart: '',
      execX: '',
      execY: '',
      execZ: '',
      execR: '',
      execA: '',
      execStart: '',
      rcsWriteModelType: '',
      rcsWriteLength: '',
      rcsWriteWidth: '',
      rcsWriteHeight: '',
      rcsWriteWeight: '',
      rcsWriteTask: '',
      stockCodeFrom: '',
      // stockCodeFromLabel: "",
      stockCodeTo: '',
      // stockCodeToLabel: "",
      wirteTagTitle: '',
      writeDialogVisbleSyncFrom: false,
      formWrite: {
        write_client_code: '',
        write_tagOnlyKey: '',
        write_tag_value: ''
      },
      // MQTT
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaWeb_' +
          Cookies.get('userId') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      cell_ip: '',
      webapi_port: ''
    }
  },
  watch: {
    task_num: {
      immediate: true,
      deep: true,
      handler() {
        this.nodes.map(item => {
          this.getHighlight(item)
        })
      }
    }
  },
  dicts: ['PROD_TASK_STATUS'],
  mounted() {
    this.currentTime()
    this.timer1 = setInterval(this.workLocation, 1000)
    this.timerLoad = setInterval(() => {
      location.reload()
    }, 1000 * 60 * 10)
  },
  beforeDestory() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.timerLoad) {
      clearInterval(this.timerLoad)
    }
    // 离开此页面时销毁mqtt链接
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  created() {
    this.getPointData()
    this.getTableData()
    this.getStockSel() // 查询上料区库位List
    // this.timer = setInterval(()=>{
    //   this.getPointData()
    // },1000)
  },
  methods: {
    currentTime() {
      setInterval(this.formatDate, 1000)
    },
    formatDate() {
      const date = new Date()
      const hours = date.getHours()
      const minuter =
        date.getMinutes() > 9 ? date.getMinutes() : '0' + date.getMinutes()
      const seconds =
        date.getSeconds() > 9 ? date.getSeconds() : '0' + date.getSeconds()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const d = date.getDate()
      const day = date.getDay()
      const week = [
        '星期日',
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六'
      ]

      this.nowDate = year + '-' + month + '-' + d + ' '
      this.time = hours + ':' + minuter + ':' + seconds + ' '
      this.week = week[day]
    },
    getPointData() {
      crudMainPage.allInventory({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            const arr = []
            arr.push(
              {
                borderColor: '#FFFFFF',
                color: '#083388',
                describe: '人工库位',
                height: 50,
                stock_code: 'L01',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rgkw',
                width: 200,
                x: 205,
                y: 120
              },
              {
                borderColor: '#FFFFFF',
                color: '#083388',
                describe: '人工库位',
                height: 50,
                stock_code: 'L02',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rgkw',
                width: 200,
                x: 205,
                y: 175
              },
              {
                borderColor: '#FFFFFF',
                color: '#083388',
                describe: '人工库位',
                height: 50,
                stock_code: 'L03',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rgkw',
                width: 200,
                x: 205,
                y: 230
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '人工库位',
                height: 50,
                stock_code: 'L04',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rgkw',
                width: 200,
                x: 205,
                y: 285
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P06',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 410,
                y: 285
                // x: 410,
                // y: 10,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P05',
                stock: '10',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 410,
                y: 230
                // x: 410,
                // y: 65,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P04',
                stock: '10',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 410,
                y: 175
                // x: 410,
                // y: 120,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P03',
                stock: '10',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 410,
                y: 120
                // x: 410,
                // y: 175,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P02',
                stock: '10',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 410,
                y: 65
                // x: 410,
                // y: 230,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P01',
                stock: '10',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 410,
                y: 10
                // x: 410,
                // y: 285,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P16',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 505
                // x: 615,
                // y: 10,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P15',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 450
                // x: 615,
                // y: 65,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P14',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 395
                // x: 615,
                // y: 120,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P13',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 340
                // x: 615,
                // y: 175,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P12',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 285
                // x: 615,
                // y: 230,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P11',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 230
                // x: 615,
                // y: 285,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P10',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 175
                // x: 615,
                // y: 340,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P09',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 120
                // x: 615,
                // y: 395,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P08',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 65
                // x: 615,
                // y: 450,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P07',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 615,
                y: 10
                // x: 615,
                // y: 505,
              },
              // {
              //   borderColor: "#FF0000",
              //   color: "#083388",
              //   describe: "库位",
              //   height: 50,
              //   stock_code: "P23",
              //   stock_count:"",
              //   model_type:"",
              //   strokeWidth: 2,
              //   type: "kw",
              //   width: 200,
              //   x: 820,
              //   y: 10,
              // },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P22',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 820,
                y: 505
                // x: 820,
                // y: 230,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P21',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 820,
                y: 450
                // x: 820,
                // y: 285,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P20',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 820,
                y: 395
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P19',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 820,
                y: 340
                // x: 820,
                // y: 395,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P18',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 820,
                y: 285
                // x: 820,
                // y: 450,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P17',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 820,
                y: 230
                // x: 820,
                // y: 505,
              },
              // {
              //   borderColor: "#FF0000",
              //   color: "#083388",
              //   describe: "库位",
              //   height: 50,
              //   stock_code: "P30",
              //   stock_count:"",
              //   model_type:"",
              //   strokeWidth: 2,
              //   type: "kw",
              //   width: 200,
              //   x: 1025,
              //   y: 10,
              // },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P23',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1025,
                y: 230
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P28',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1025,
                y: 505
                // x: 1025,
                // y: 285,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P27',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1025,
                y: 450
                // x: 1025,
                // y: 340,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P26',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1025,
                y: 395
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P25',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1025,
                y: 340
                // x: 1025,
                // y: 450,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P24',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1025,
                y: 285
                // x: 1025,
                // y: 505,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P29',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1230,
                y: 230
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P30',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1230,
                y: 285
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P34',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1230,
                y: 505
                // x: 1230,
                // y: 340,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P33',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1230,
                y: 450
                // x: 1230,
                // y: 395,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P32',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1230,
                y: 395
                // x: 1230,
                // y: 450,
              },
              {
                borderColor: '#FF0000',
                color: '#083388',
                describe: '库位',
                height: 50,
                stock_code: 'P31',
                stock_count: '',
                model_type: '',
                strokeWidth: 2,
                type: 'kw',
                width: 200,
                x: 1230,
                y: 340
                // x: 1230,
                // y: 505,
              },
              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: 'L01',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 410,
                y: 340
              },
              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: 'L02',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 205,
                y: 340
              },

              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: 'L03',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 410,
                y: 440
              },
              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: '',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 10,
                y: 440
              },
              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: '',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 820,
                y: 135
              },
              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: '',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 1025,
                y: 135
              },
              {
                borderColor: '#083388',
                color: '#A9C6F9',
                describe: '入库辊道',
                height: 80,
                stock_code: '',
                stock_count: '',
                model_type: '',
                strokeWidth: 0,
                type: 'rkgd',
                width: 200,
                x: 1230,
                y: 135
              },
              {
                color: '',
                describe: '抛丸机',
                height: 175,
                stock_code: '',
                stock_count: '',
                strokeWidth: 0,
                type: 'paowanji',
                width: '',
                x: 200,
                y: 410
              },
              {
                color: '',
                describe: '大车',
                height: 610,
                stock_code: '',
                stock_count: '',
                strokeWidth: 0,
                type: 'bigCar',
                width: 230,
                x: 400, // 右侧极限：1230,左侧侧极限：205
                y: -20
              },
              {
                color: '',
                describe: '小车',
                height: 60,
                stock_code: '',
                stock_count: '',
                strokeWidth: 0,
                type: 'smallCar',
                width: 230,
                x: 400, // 右侧极限：1230,左侧侧极限：205
                y: 5
              },
            )
            this.nodes = arr.map(item => {
              const obj = defaultQuery.data.find(e => e.stock_code === item.stock_code)
              return { ...item, ...obj }
            })
            console.log(this.nodes, '4545454545')
            this.connections = []
            this.toStartWatch()
          }
        }
      })
        .catch(() => {
          this.$message({
            message: '获取点位异常',
            type: 'error'
          })
        })
    },
    getTableData() {
      this.listLoadingTable = true
      const query = {
        page: 1,
        size: 100,
        sort: 'task_id asc',
        user_name: Cookies.get('userName')
      }
      selTask(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.tableDataTable = defaultQuery.data
          } else {
            this.tableDataTable = []
          }
          this.listLoadingTable = false
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    handleStartWrite(type) {
      if (type === 'recv') {
        this.wirteTagTitle = '接收任务-Start'
        // 写入
        this.formWrite.write_client_code = 'CarPlc01'
        this.formWrite.write_tagOnlyKey = 'CarPlc01/RcsGrroupRead/Tag417'
        this.formWrite.write_tag_value = ''

        this.$nextTick((x) => {
          // 正确写法
          this.$refs.writeTagValue.focus()
        })
        this.writeDialogVisbleSyncFrom = true
      } else if (type === 'exec') {
        this.wirteTagTitle = '执行任务-Start'
        // 写入
        this.formWrite.write_client_code = 'CarPlc01'
        this.formWrite.write_tagOnlyKey = 'CarPlc01/RcsGrroupRead/Tag423'
        this.formWrite.write_tag_value = ''

        this.$nextTick((x) => {
          // 正确写法
          this.$refs.writeTagValue.focus()
        })
        this.writeDialogVisbleSyncFrom = true
      }
    },
    toWriteFromCancel() {
      this.writeDialogVisbleSyncFrom = false // 弹出框隐藏
    },
    handleCutNode(data) {
      this.$emit('ok', data)
    },
    handleEditNode(node, thisH) {
      this.nodeForm = node
      this.thisH = thisH
    },
    taskCannel(tagKey, val) {
      const query = {
        station_code: 'P01',
        flow_main_code: 'CarMainSchedulingOverheadCranes,CarRouteExecution',
        flow_task_status: 'WORKING'
      }
      var method = '/dcs/interf/core/cancelFlow'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cell_ip + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cell_ip + ':' + this.webapi_port + method
        // path = 'http://*************:8089' + method
      }
      axios.post(path, query, { headers: { 'Content-Type': 'application/json' }}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res.data))
        if (defaultQuery.code === 0) {
          this.$message({ type: 'success', message: defaultQuery.msg || '任务取消成功' })
          this.handleTagWrite(tagKey, val, '1')
        }
      }).catch(ex => {
        this.$message({
          message: ex.msg || '操作异常',
          type: 'error'
        })
      })
    },
    getHighlight(item) {
      if (this.task_num && this.task_num === item.task_num) {
        item.status = 'LOCKSTATUS'
        this.$refs.chart.renderNode(item, false)
      } else {
        item.status = ''
        this.$refs.chart.renderNode(item, false)
      }
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 获取连接地址
      // 'ws://***************:8090/mqtt'
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.cell_ip = result.ip
          this.webapi_port = result.webapi_port
          this.GetTagValue(result.ip, result.webapi_port)
          //  var connectUrl ="ws://*************:8083/mqtt";
          // connectUrl=MQTT_SERVICE;
          console.log('拼接URL：' + connectUrl)
          // mqtt连接
          // this.clientMqtt = mqtt.connect(MQTT_SERVICE, this.optionsMqtt); //开启连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', (e) => {
            this.mqttConnStatus = true
            for (let index = 0; index < this.tagKeyList.length; index++) {
              this.topicSubscribe('SCADA_CHANGE/' + this.tagKeyList[index])
              // this.topicSubscribe("SCADA_CHANGE/CarPlc01/RcsGrroupRead/Tag407");
            }
            this.topicSubscribe('SCADA_CHANGE/SlAisSimPlc/WmsStatus/CarCreate01')
          })

          // MQTT连接失败
          this.clientMqtt.on('error', (error) => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })

            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', (error) => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', (error) => {
            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          this.clientMqtt.on('close', () => {
            this.clientMqtt.end()

            this.$message({
              message: '服务连接断开',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // console.log('MQTT收到来自', topic, '的消息', message.toString())

            const res = JSON.parse(message.toString())

            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    GetTagValue(ip, port) {
      var readTagArray = []
      for (var i = 0; i < this.tagKeyList.length; i++) {
        var readTag = {}
        readTag.tag_key = this.tagKeyList[i].toString()
        readTagArray.push(readTag)
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
      } else {
        path = 'http://' + ip + ':' + port + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var tagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value.toString()
                  if (tagKey === 'SlCarPlc01/PlcStatus/X_real-time') { // 大车激光
                    this.x = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/Y_real-time') { // 小车激光
                    this.y = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/Z_real-time') { // 小车激光
                    this.h = tagValue
                  } else if (tagKey === 'SlCarPlc01/DcsStatus/RcsWriteModelType') { // 钢板型号
                    this.rcsWriteModelType = tagValue
                  } else if (tagKey === 'SlCarPlc01/DcsStatus/RcsWriteLength') { // RCS写入钢板长
                    this.rcsWriteLength = tagValue
                  } else if (tagKey === 'SlCarPlc01/DcsStatus/RcsWriteWidth') { // RCS写入钢板宽
                    this.rcsWriteWidth = tagValue
                  } else if (tagKey === 'SlCarPlc01/DcsStatus/RcsWriteHeight') { // RCS写入钢板厚
                    this.rcsWriteHeight = tagValue
                  } else if (tagKey === 'SlCarPlc01/DcsStatus/RcsWriteWeight') { // RCS写入钢板重
                    this.rcsWriteWeight = tagValue
                  } else if (tagKey === 'SlCarPlc01/DcsStatus/RcsWriteTask') { // RCS写入任务号
                    this.rcsWriteTask = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/AcceptTaskX') { // 接收任务X
                    this.recvX = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/AcceptTaskY') { // 接收任务y
                    this.recvY = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/AcceptTaskZ') { // 接收任务Z
                    this.recvZ = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/AcceptTaskR') { // 接收任务R
                    this.recvR = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/AcceptTaskA') { // 接收任务A
                    this.recvA = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/AcceptTaskStart') { // 接收任务Start
                    this.recvStart = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/PlcTimeX') { // 执行任务X
                    this.execX = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/PlcTimeY') { // 执行任务Y
                    this.execY = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/PlcTimeZ') { // 执行任务Z
                    this.execZ = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/PlcTimeR') { // 执行任务R
                    this.execR = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/PlcTimeA') { // 执行任务A
                    this.execA = tagValue
                  } else if (tagKey === 'SlCarPlc01/PlcStatus/PlcReadStartTask') { // 执行任务Start
                    this.execStart = tagValue
                  }
                }
              }
            }
          }
          this.updateCarLocation()
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
          console.log(ex)
        })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },

    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }

      // 订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    handleTagWrite(tagKey, val, type) {
      // 鼠标按下写入值为1，抬起写入值为0
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: val
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + tagKey.split('/')[0]
      this.sendMessage(topic, sendStr)
      const clickedSpan = event.target
      if (type === '1') return
      if (val === '1') {
        clickedSpan.classList.add('lu-btn-3d-active')
      } else {
        clickedSpan.classList.remove('lu-btn-3d-active')
      }
    },
    handleTagWrite2(tagKey, val, type) {
      const clickedSpan = event.target
      // 鼠标按下写入值为1，抬起写入值为0
      if (val === '0') {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: tagKey,
          TagValue: val
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/' + tagKey.split('/')[0]
        this.sendMessage(topic, sendStr)
        clickedSpan.classList.add('lu-btn-3d-active')
      } else {
        clickedSpan.classList.remove('lu-btn-3d-active')
      }
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_STATUS/') >= 0 || channel.indexOf('SCADA_BEAT/') >= 0 || channel.indexOf('SCADA_MSG/') >= 0) {

      }
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagCode = jsonData.TagCode
      var TagOldValue = jsonData.TagOldValue
      var TagNewValue = jsonData.TagNewValue
      if (TagKey === 'SlCarPlc01/PlcStatus/X_real-time') { // 大车激光
        this.x = TagNewValue
        if (this.x !== '' && this.y !== '') {
          this.updateCarLocation()
        }
      } else if (TagKey === 'SlCarPlc01/PlcStatus/Y_real-time') { // 小车激光
        this.y = TagNewValue
        console.log(TagNewValue)
        if (this.x !== '' && this.y !== '') {
          this.updateCarLocation()
        }
      } else if (TagKey === 'SlCarPlc01/PlcStatus/Z_real-time') { // 小车激光
        this.h = TagNewValue
      } else if (TagCode === 'RcsWriteModelType') { // 钢板型号
        this.rcsWriteModelType = TagNewValue
      } else if (TagCode === 'RcsWriteLength') { // RCS写入钢板长
        this.rcsWriteLength = TagNewValue
      } else if (TagCode === 'RcsWriteWidth') { // RCS写入钢板宽
        this.rcsWriteWidth = TagNewValue
      } else if (TagCode === 'RcsWriteHeight') { // RCS写入钢板厚
        this.rcsWriteHeight = TagNewValue
      } else if (TagCode === 'RcsWriteWeight') { // RCS写入钢板重
        this.rcsWriteWeight = TagNewValue
      } else if (TagCode === 'RcsWriteTask') { // RCS写入任务号
        this.rcsWriteTask = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/AcceptTaskX') { // 接收任务X
        this.recvX = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/AcceptTaskY') { // 接收任务y
        this.recvY = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/AcceptTaskZ') { // 接收任务Z
        this.recvZ = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/AcceptTaskR') { // 接收任务R
        this.recvR = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/AcceptTaskA') { // 接收任务A
        this.recvA = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/AcceptTaskStart') { // 接收任务Start
        this.recvStart = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/PlcTimeX') { // 执行任务X
        this.execX = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/PlcTimeY') { // 执行任务Y
        this.execY = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/PlcTimeZ') { // 执行任务Z
        this.execZ = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/PlcTimeR') { // 执行任务R
        this.execR = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/PlcTimeA') { // 执行任务A
        this.execA = TagNewValue
      } else if (TagKey === 'SlCarPlc01/PlcStatus/PlcReadStartTask') { // 执行任务Start
        this.execStart = TagNewValue
      }
    },
    updateCarLocation() {
      if (this.x === '' || this.y === '') {
        return
      }
      var car1LocationX = (parseFloat(this.x) - 4500) * this.px // 大天车的可活动范围是0-57300，实际活动范围是4500-57300
      var car1LocationY = (parseFloat(this.y) - 1150) * this.px // 小天车的可活动范围是0-24150，实际活动范围是1150-24150
      var rate1 = +(
        (this.carAuctalWorkRegionWidth * this.px) /
        this.carVrituWorkRegionWidth
      ).toFixed(2)
      var rate2 = +(
        (this.carAuctalWorkRegionHeight * this.px) /
        this.carVrituWorkRegionHeight
      ).toFixed(2)
      var x = +(car1LocationX / rate1).toFixed(2) + 400
      var y = +(car1LocationY / rate2).toFixed(2)

      const bigCarInfo = this.nodes.filter((item) => item.type === 'bigCar')[0]
      const smallCarInfo = this.nodes.filter((item) => item.type === 'smallCar')[0]
      if (x < 400) {
        bigCarInfo.x = 400
        smallCarInfo.x = 400
      } else if (x > 1220) {
        bigCarInfo.x = 1220
        smallCarInfo.x = 1220
      } else {
        bigCarInfo.x = x
        smallCarInfo.x = x
      }
      if (y > 500) {
        smallCarInfo.y = 500
      } else {
        smallCarInfo.y = y
      }
      console.log(x, y, rate1, rate2, smallCarInfo)
    },
    getStockSel() {
      crudLoadAreaOper.stockSel({}).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.tableData = defaultQuery.data || []
          }
        } else {
          this.tableData = []
          this.$message({
            message: '库位查询异常',
            type: 'error'
          })
        }
      })
        .catch(() => {
          this.tableData = []
          this.$message({
            message: '库位查询异常',
            type: 'error'
          })
        })
    },
    reversePark() {
      this.reverseModal = true
      this.$nextTick(() => {
        this.$refs.reverseModalFlag.dialogVisible = true
      })
    }
  }
}
</script>

<style lange="less" scoped>
.el-header {
  background-color: #161522;
  text-align: center;
}

.el-header .title {
  font-size: 25px;
  letter-spacing: 5px;
  background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.el-header .time {
  font-size: 20px;
  background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.el-main {
  background-color: #161522;
  padding-top: 5px;
  overflow: hidden;
}

.el-main .dataInfo {
  font-size: 20px;
  background: linear-gradient(to top, #31baff, #d7edf9, #fdf7ee);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
  margin-bottom: 10px;
}

.btn-crown-block {
  display: flex;
  overflow-x: hidden;
  flex-wrap: wrap;
}

.lu-btn-3d {
  display: block;
  width: 180px;
  height: 42px;
  line-height: 42px;
  background: linear-gradient(to bottom, #005b89 0%, #005b89 100%);
  border: none;
  border-radius: 5px;
  color: #d6dce5;
  font-weight: 600;
  font-family: "Open Sans", sans-serif;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.4);
  font-size: 20px;
  text-align: center;
  box-shadow: 0px 3px 0px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  margin: 5px 0;
  letter-spacing: 2px;
}

.lu-btn-3d-active {
  color: #fff;
  background: linear-gradient(to bottom, #13e913 0%, #13e913 100%);
}

/* .lu-btn-3d:hover {
  background: linear-gradient(to bottom, #005b89 0%, #005b89 100%);
  box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.2);
  color: #fbfbfb;
  top: 1px;
} */
</style>
